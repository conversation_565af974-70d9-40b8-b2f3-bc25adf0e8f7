<template>
<view style="width:100%">
	<view class="dp-product-zhaopin">
		<view class="box_6 flex-col" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/zhaopin/partdetails?id='+item[zwid]">
			<view class="box_7 flex-row justify-between">
				<view class="title-wrapper flex-row">
					<text class="text_7">{{item.title}}</text>
					<view class="person-count" v-if="item.numbers">
						<text>招{{item.numbers}}人</text>
					</view>
				</view>
				<view class="collect-btn">
					<image
						class="thumbnail_2"
						referrerpolicy="no-referrer" 
						:src="item.is_favorite ? '/static/img/star2.png' : '/static/img/star.png'"
					/>
				</view>
			</view>
			<view class="salary-wrapper flex-row">
				<text class="salary-text" :style="{color: t('color1')}">{{item.salary}}</text>
				<!-- <text class="salary-unit" :style="{color: t('color1')}">·{{item.salary_unit}}</text> -->
			</view>
			<view class="company-info flex-row">
				
				<text class="company-name" v-if="item.company_name">{{item.company_name}}</text>
				
				<text class="company-address" v-if="item.work_address">{{item.work_address}}</text>
				<text class="dot">·</text>
				<text class="company-address" v-if="item.juli">{{item.juli}}</text>
			</view>
			<view class="welfare-list flex-row" v-if="item.welfare && item.welfare.length>0">
				<view class="welfare-item" v-for="(wf,wk) in item.welfare" :key="wk">
					<text>{{wf}}</text>
				</view>
			</view>
			<view class="tag-list flex-row" v-if="item.age_requirement || item.gender_requirement !== undefined || item.work_mode || item.work_time_type || item.payment">
				<view class="left-tags">
					<view class="tag-item" v-if="item.age_requirement">{{item.age_requirement}}</view>
					<view class="tag-item" v-if="item.gender_requirement !== undefined">{{item.gender_requirement === 1 ? '男' : item.gender_requirement === 2 ? '女' : '男女不限'}}</view>
					<view class="tag-item" v-if="item.work_mode">{{item.work_mode}}</view>
					<view class="tag-item" v-if="item.work_time_type">{{item.work_time_type}}</view>
					<view class="tag-item" v-if="item.payment">{{item.payment}}</view>
				</view>
				<view class="share-earn flex-col" v-if="item.commission_text && showcommission == '1'" :style="{background: `linear-gradient(135deg, ${t('color1')} 0%, ${t('color1')} 100%)`}">
					<text class="share-text">分享赚</text>
					<text class="earn-amount">{{item.commission_text}}元</text>
				</view>
			</view>
		</view>
	</view>
</view>
</template>

<script>
export default {
	data(){
		return {
			buydialogShow:false,
			proid:0,
		}
	},
	props: {
		menuindex:{default:-1},
		namecolor:{default:'#333'},
		showaddress:{default:'1'},
		data:{},
		idfield:{default:'id'},
		zwid:{default:'zwid'},
		showcommission:{default:'1'}
	},
	methods: {
		goto(e) {
			const url = e.currentTarget.dataset.url;
			if(url) {
				uni.navigateTo({
					url: url
				});
			}
		}
	}
}
</script>

<style lang="scss">

.dp-product-zhaopin {
	padding: 16rpx;
	height: auto;
	position: relative;
	overflow: hidden;
}

.box_6 {
	box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.05);
	background-color: #FFFFFF;
	justify-content: flex-start;
	margin-bottom: 24rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
		box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.03);
	}
}

.box_7 {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.title-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.text_7 {
	color: #1f2937;
	font-size: 32rpx;
	font-weight: 600;
	line-height: 44rpx;
	font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif;
}

.person-count {
	background-color: #F3F4F6;
	padding: 4rpx 12rpx;
	border-radius: 4rpx;
	
	text {
		color: #6B7280;
		font-size: 22rpx;
		line-height: 30rpx;
	}
}

.collect-btn {
	padding: 8rpx;
}

.thumbnail_2 {
	width: 32rpx;
	height: 32rpx;
}

.salary-wrapper {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
	gap: 8rpx;
}

.salary-text {
	font-size: 32rpx;
	font-weight: 500;
	line-height: 44rpx;
}

.salary-unit {
	font-size: 24rpx;
	line-height: 34rpx;
	opacity: 0.8;
}

.company-info {
	margin: 20rpx 0;
	.company-logo {
		width: 36rpx;
		height: 36rpx;
		border-radius: 6rpx;
	}
}

.company-name, .company-address, .company-exp {
	color: #6B7280;
	font-size: 22rpx;
	line-height: 30rpx;
	font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif;
}

.dot {
	color: #D1D5DB;
	font-size: 22rpx;
	line-height: 30rpx;
}

.welfare-list {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
	margin-bottom: 12rpx;
}

.welfare-item {
	background-color: #F5F7FA;
	padding: 6rpx 16rpx;
	border-radius: 24rpx;
	
	text {
		color: #666666;
		font-size: 22rpx;
		line-height: 30rpx;
	}
}

.tag-list {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	margin-top: -4rpx;
}

.left-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	padding-top: 4rpx;
}

.tag-item {
	padding: 4rpx 14rpx;
    background-color: #F5F7FA;
    border-radius: 24rpx;
    color: #666666;
    font-size: 22rpx;
    line-height: 30rpx;
}

.share-earn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-left: 16rpx;
	margin-top: -38rpx;
	padding: 8rpx 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.2);
	min-width: 120rpx;
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.95);
	}
}

.share-text {
	color: #FFFFFF;
	font-size: 22rpx;
	line-height: 28rpx;
	font-weight: 600;
	margin-bottom: 4rpx;
	position: relative;
	
	&::after {
		content: '';
		position: absolute;
		bottom: -4rpx;
		left: -12rpx;
		right: -12rpx;
		height: 1px;
		background: rgba(255, 255, 255, 0.2);
	}
}

.earn-amount {
	color: #FFFFFF;
	font-size: 22rpx;
	font-weight: 500;
	line-height: 30rpx;
	margin-top: 4rpx;
}
</style>