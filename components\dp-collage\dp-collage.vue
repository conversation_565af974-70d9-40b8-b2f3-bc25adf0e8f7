<template>
<view class="dp-collage" :style="{
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+params.margin_x*2.2+'rpx 0',
	padding:(params.padding_y*2.2)+'rpx '+params.padding_x*2.2+'rpx'
}">
	<view v-show="!params.shopstyle||params.shopstyle==1">
		<view class="dp-collage-item" v-if="params.style=='1' || params.style=='2' || params.style=='3'">
			<view class="item" v-for="(item,index) in data" :style="params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')" :key="item.id" @click="goto" :data-url="'/activity/collage/product?id='+item.proid">
				<view class="product-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
					<image class="saleimg" :src="params.saleimg" v-if="params.saleimg!=''" mode="widthFix"/>
				</view>
				<view class="product-info">
					<view class="p1" v-if="params.showname == 1">{{item.name}}</view>
					<view class="p2">
						<view class="p2-1" v-if="params.showprice != '0'">
							<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">￥</text>{{item.sell_price}}</text>
							<text class="t2" v-if="params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
						</view>
					</view>
					<view class="p3">
						<view class="p3-1" :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}">{{item.teamnum}}人团</view>
						<view class="p3-2" v-if="params.showsales=='1' && item.sales>0"><text style="overflow:hidden">已拼成{{item.sales}}件</text></view>
					</view>
				</view>
			</view>
		</view>
		<view class="dp-collage-itemlist" v-if="params.style=='list'">
			<view class="item" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/activity/collage/product?id='+item.proid">
				<view class="product-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
					<image class="saleimg" :src="params.saleimg" v-if="params.saleimg!=''" mode="widthFix"/>
				</view>
				<view class="product-info">
					<view class="p1" v-if="params.showname == 1">{{item.name}}</view>
					<view class="p2" v-if="params.showprice != '0'">
						<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx;padding-right:1px">￥</text>{{item.sell_price}}</text>
						<text class="t2" v-if="params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
					</view>
					<view class="p3">
						<view class="p3-1" :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}">{{item.teamnum}}人团</view>
						<view class="p3-2" v-if="params.showsales=='1' && item.sales>0"><text style="overflow:hidden">已拼成{{item.sales}}件</text></view>
					</view>
				</view>
			</view>
		</view>
		<view class="dp-collage-itemline" v-if="params.style=='line'">
			<view class="item" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/activity/collage/product?id='+item.proid">
				<view class="product-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
					<image class="saleimg" :src="params.saleimg" v-if="params.saleimg!=''" mode="widthFix"/>
				</view>
				<view class="product-info">
					<view class="p1" v-if="params.showname == 1">{{item.name}}</view>
					<view class="p2">
						<view class="p2-1" v-if="params.showprice != '0'">
							<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">￥</text>{{item.sell_price}}</text>
							<text class="t2" v-if="params.showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
						</view>
					</view>
					<view class="p3">
						<view class="p3-1" :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}">{{item.teamnum}}人团</view>
						<view class="p3-2" v-if="params.showsales=='1' && item.sales>0"><text style="overflow:hidden">已拼成{{item.sales}}件</text></view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<view v-show="params.shopstyle==2">
		<view class="dp-collage-item" v-if="params.style=='2'">
			<view class="item" v-for="(item,index) in data" :style="params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')" :key="item.id" @click="goto" :data-url="'/activity/collage/product?id='+item.proid">
				<view class="product-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
					<image class="saleimg" :src="params.saleimg" v-if="params.saleimg!=''" mode="widthFix"/>
					<view :style="{background:'rgba('+t('color1rgb')+',1)'}" class="tag">拼团</view>
				</view>
				<view class="product-info">
					<view class="p1" v-if="params.showname == 1">{{item.name}}</view>
					<view class="flex">
						<view :style="{'border-color':t('color1')}" class="total flex">
							<view :style="{background:'rgba('+t('color1rgb')+',1)'}" class="num">{{item.teamnum}}人团</view>
							<view :style="{color:t('color1'),background:'rgba('+t('color1rgb')+',0.1)'}" class="sales">已团{{item.sales}}件</view>
						</view>
					</view>
					<view class="price flex-bt flex-y-center">
						<text :style="{color:t('color1')}" class="text">￥{{item.sell_price}}</text>
						<view :style="{background:'rgba('+t('color1rgb')+',1)'}" class="add flex-xy-center">
							<image :src="pre_url+'/static/imgsrc/decoration_add.png'"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="dp-collage-itemlist" v-if="params.style=='list'">
			<view class="item" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/activity/collage/product?id='+item.proid">
				<view class="product-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
					<image class="saleimg" :src="params.saleimg" v-if="params.saleimg!=''" mode="widthFix"/>
					<view :style="{background:'rgba('+t('color1rgb')+',1)'}" class="tag">拼团</view>
				</view>
				<view class="product-info">
					<view class="p1" v-if="params.showname == 1">{{item.name}}</view>
					<view :style="{color:t('color1')}" class="text">已团{{item.sales}}件</view>
					<view class="total flex-y-center">
						<view :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}" class="tag">{{item.teamnum}}人团</view>
						<view class="flex-y-bottom">
							<view :style="{color:t('color1')}" class="unit">￥</view>
							<view :style="{color:t('color1')}" class="price">{{item.sell_price}}</view>
						</view>
					</view>
					<view :style="{background:'rgba('+t('color1rgb')+',1)'}" class="btn flex-xy-center">去拼团</view>
				</view>
			</view>
		</view>
	</view>
</view>
</template>
<script>
	export default {
		data(){
			return {
				pre_url:getApp().globalData.pre_url
			}
		},
		props: {
			params:{},
			data:{}
		}
	}
</script>
<style>
.dp-collage{height: auto; position: relative;overflow: hidden; padding: 0px; background: #fff;}
.dp-collage-item{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}
.dp-collage-item .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden}
.dp-collage-item .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}
.dp-collage-item .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-collage-item .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}
.dp-collage-item .product-pic .tag{padding: 0 15rpx;line-height: 35rpx;display: inline-block;font-size: 24rpx;color: #fff;background: linear-gradient(to bottom right,#ff88c0,#ec3eda);position: absolute;left: 0;top: 0;border-radius: 0 0 10rpx 0}
.dp-collage-item .product-info {padding:20rpx 20rpx;position: relative;}
.dp-collage-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
.dp-collage-item .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}
.dp-collage-item .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}
.dp-collage-item .product-info .p2-1 .t1{font-size:36rpx;}
.dp-collage-item .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-collage-item .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}
.dp-collage-item .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}
.dp-collage-item .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}
.dp-collage-item .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}
.dp-collage-item .product-info .total{border-radius: 8rpx;border: 1rpx solid #FF3143;font-size: 24rpx;background: #ffeded;overflow: hidden;}
.dp-collage-item .product-info .total .num{color: #fff;background: #FF3143;padding: 3rpx 8rpx;}
.dp-collage-item .product-info .total .sales{color: #FF3143;padding: 3rpx 8rpx;}
.dp-collage-item .product-info .price{position: relative;margin-top: 15rpx;}
.dp-collage-item .product-info .price .text{color: #FF3143;font-weight: bold;font-size: 30rpx;}
.dp-collage-item .product-info .price .add{height: 50rpx;width: 50rpx;border-radius: 100rpx;background: #FF3143;}
.dp-collage-item .product-info .price .add image{height: 30rpx;width: 30rpx;display: block;}

.dp-collage-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}
.dp-collage-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:20rpx;border-radius:10rpx}
.dp-collage-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}
.dp-collage-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-collage-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}
.dp-collage-itemlist .product-pic .tag{padding: 0 15rpx;line-height: 35rpx;display: inline-block;font-size: 24rpx;color: #fff;background: linear-gradient(to bottom right,#ff88c0,#ec3eda);position: absolute;left: 0;top: 0;border-radius: 0 0 10rpx 0}
.dp-collage-itemlist .product-info {width: 70%;padding:6rpx 10rpx 5rpx 20rpx;position: relative;}
.dp-collage-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
.dp-collage-itemlist .product-info .p2{margin-top:20rpx;height:56rpx;line-height:56rpx;overflow:hidden;}
.dp-collage-itemlist .product-info .p2 .t1{font-size:36rpx;}
.dp-collage-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-collage-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}
.dp-collage-itemlist .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}
.dp-collage-itemlist .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}
.dp-collage-itemlist .product-info .text{font-size:24rpx;margin-top: 30rpx;}
.dp-collage-itemlist .product-info .total{margin-top: 20rpx;}
.dp-collage-itemlist .product-info .total .tag{width: auto;background: #ffeded;color: #fff;line-height: 35rpx;padding: 0 8rpx;border-radius: 8rpx;font-size: 24rpx;margin-right: 5rpx;}
.dp-collage-itemlist .product-info .total .unit{font-size: 24rpx;font-weight: bold;line-height: 28rpx;}
.dp-collage-itemlist .product-info .total .price{font-size: 35rpx;font-weight: bold;line-height: 35rpx;}
.dp-collage-itemlist .product-info .btn{width: 140rpx;height: 60rpx;color: #fff;border-radius: 100rpx;background: rgb(253, 70, 62);font-size: 26rpx;position: absolute;right: 0;bottom: 0;font-weight: bold;}

.dp-collage-itemline{width:100%;display:flex;overflow-x:scroll;overflow-y:hidden}
.dp-collage-itemline .item{width: 220rpx;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;margin-right:4px}
.dp-collage-itemline .product-pic {width:220rpx;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}
.dp-collage-itemline .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-collage-itemline .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}
.dp-collage-itemline .product-info {padding:20rpx 20rpx;position: relative;}
.dp-collage-itemline .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
.dp-collage-itemline .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}
.dp-collage-itemline .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}
.dp-collage-itemline .product-info .p2-1 .t1{font-size:36rpx;}
.dp-collage-itemline .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-collage-itemline .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}
.dp-collage-itemline .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}
.dp-collage-itemline .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}
.dp-collage-itemline .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}
</style>