<template>
<view class="container">
	<block v-if="isload">

		<!-- <dd-tab :itemdata="[t('一级')+'('+userinfo.myteamCount1+')',t('二级')+'('+userinfo.myteamCount2+')']" :itemst="['1','2','3']" :st="st" :isfixed="false" @changetab="changetab" v-if="userlevel && userlevel.can_agent==2"></dd-tab>
		<dd-tab :itemdata="[t('一级')+'('+userinfo.myteamCount1+')',t('二级')+'('+userinfo.myteamCount2+')',t('三级')+'('+userinfo.myteamCount3+')']" :itemst="['1','2','3']" :st="st" :isfixed="false" @changetab="changetab" v-if="userlevel && userlevel.can_agent==3"></dd-tab>
		 -->
		
		<dd-tab :itemdata="[t('一级'),t('二级')]" :itemst="['1','2','3']" :st="st" :isfixed="false" @changetab="changetab" v-if="userlevel && userlevel.can_agent==2"></dd-tab>
		<dd-tab :itemdata="[t('一级'),t('二级'),t('三级')]" :itemst="['1','2','3']" :st="st" :isfixed="false" @changetab="changetab" v-if="userlevel && userlevel.can_agent==3"></dd-tab>
		
		<!-- 时间筛选区域 -->
		<view class="time-filter" v-if="team_settings && team_settings.show_statistics == 1">
			<view class="filter-item">
				<text class="filter-label">开始：</text>
				<picker mode="date" @change="onStartDateChange" :value="start_time">
					<view class="date-picker">{{start_time || '请选择'}}</view>
				</picker>
			</view>
			<view class="filter-item">
				<text class="filter-label">结束：</text>
				<picker mode="date" @change="onEndDateChange" :value="end_time">
					<view class="date-picker">{{end_time || '请选择'}}</view>
				</picker>
			</view>
			<view class="filter-btn" :style="{background:t('color1'),color:'#fff'}" @tap="applyTimeFilter">筛选</view>
			<view class="filter-btn reset-btn" @tap="resetTimeFilter">重置</view>
		</view>
		
		<!-- 统计信息显示区域 -->
		<view class="statistics-bar" v-if="team_settings && team_settings.show_statistics == 1 && statistics" :style="{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
			<view class="stat-item">
				<text class="stat-value">{{statistics.total_count || 0}}</text>
				<text class="stat-label">总人数</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">¥{{statistics.total_amount || '0.00'}}</text>
				<text class="stat-label">总下单金额</text>
			</view>
		</view>
		
		<view class="topsearch flex-y-center">
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="输入昵称/姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm" @input="searchChange"></input>
			</view>
		</view>

		<!-- Skeleton Loader -->
		<view class="content" v-if="loading && pagenum === 1">
			<view class="member-list">
				<view class="skeleton-item" v-for="i in 3" :key="i">
					<view class="skeleton-header">
						<view class="skeleton-avatar"></view>
						<view class="skeleton-info">
							<view class="skeleton-line" style="width: 40%;"></view>
							<view class="skeleton-line" style="width: 60%; margin-top: 12rpx;"></view>
						</view>
					</view>
					<view class="skeleton-body">
						<view class="skeleton-line" style="width: 90%;"></view>
						<view class="skeleton-line" style="width: 70%;"></view>
					</view>
				</view>
			</view>
		</view>

		<view class="content" v-if="!loading && datalist.length > 0">
			<view class="member-list">
				<view class="member-item" v-for="(item, index) in datalist" :key="index" @tap="toggleExpand(index)" :style="{animationDelay: (index * 0.05) + 's'}">
					<view class="item-header" :style="{background: 'linear-gradient(135deg, ' + t('color1') + ' 0%, ' + t('color2') + ' 100%)'}">
						<view class="user-info">
							<image class="avatar" :src="item.headimg"></image>
							<view class="info-text">
								<view class="nickname-line">
									<text class="nickname">{{ item.nickname }}</text>
									<view class="relation-chart-btn" v-if="team_settings && team_settings.show_relation_chart == 1" @tap.stop="goToRelationChart" :data-id="item.id">关系图</view>
								</view>
								<view class="join-time">{{ item.createtime }} 加入</view>
							</view>
						</view>
						<view class="right-info">
							<view class="user-level-badge">{{ item.levelname }}</view>
							<image class="arrow-icon" :class="{expanded: expandedIndex === index}" src="/static/img/arrow-down.png"></image>
						</view>
					</view>

					<view class="expandable-content" :class="{expanded: expandedIndex === index}">
						<view class="item-body">
							<view class="info-grid">
								<view class="info-cell" v-if="team_settings && team_settings.show_other_commission == 1">
									<text class="info-label">贡献{{ t('佣金') }}</text>
									<text class="info-value highlight" :style="{ color: t('color1') }">+{{ item.commission }}</text>
								</view>
								<view class="info-cell" v-if="team_settings && team_settings.show_consume_amount == 1">
									<text class="info-label">消费金额</text>
									<text class="info-value highlight" :style="{ color: t('color1') }">¥{{ item.consume_amount || '0.00' }}</text>
								</view>
								<view class="info-cell" v-if="team_settings && team_settings.show_team_performance == 1">
									<text class="info-label">团队业绩</text>
									<text class="info-value">{{ item.tuanduiyeji }}</text>
								</view>
								<view class="info-cell" v-if="team_settings && team_settings.show_direct_count == 1">
									<text class="info-label">直推人数</text>
									<text class="info-value">{{ item.direct_count || item.downcount }}</text>
								</view>
								<view class="info-cell" v-if="team_settings && team_settings.show_member_count == 1">
									<text class="info-label">团队人数</text>
									<text class="info-value">{{ item.downcount }}</text>
								</view>
								<view class="info-cell" v-if="item.tel">
									<text class="info-label">手机号</text>
									<text class="info-value">{{ item.tel }}</text>
								</view>
								<view class="info-cell" v-if="item.has_original_recommender == 1 && item.original_recommender">
									<text class="info-label">原推荐人</text>
									<text class="info-value">{{ item.original_recommender.nickname }}</text>
								</view>
							</view>
						</view>

						<view class="item-footer" v-if="userlevel && (userlevel.team_givemoney == 1 || userlevel.team_givescore == 1 || userlevel.team_levelup == 1 || userlevel.team_daikexiadan == 1)">
							<view class="action-btn" v-if="userlevel && userlevel.team_givemoney == 1" :style="{color:t('color1'), borderColor:t('color1')}" @tap.stop="givemoneyshow" :data-id="item.id">转{{ t('余额') }}</view>
							<view class="action-btn" v-if="userlevel && userlevel.team_givescore == 1" :style="{color:t('color1'), borderColor:t('color1')}" @tap.stop="givescoreshow" :data-id="item.id">转{{ t('积分') }}</view>
							<view class="action-btn" v-if="userlevel && userlevel.team_levelup == 1" :style="{color:t('color1'), borderColor:t('color1')}" @tap.stop="showDialog" :data-id="item.id" :data-levelid="item.levelid" :data-levelsort="item.levelsort">升级</view>
							<view class="action-btn" v-if="userlevel && userlevel.team_daikexiadan == 1" :style="{color:t('color1'), borderColor:t('color1')}" @tap.stop="goDaigoupick" :data-id="item.id">帮他下单</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- Empty State -->
		<view class="empty-state" v-if="nodata">
			<image class="empty-icon" src="/static/img/no-data.png"></image>
			<text class="empty-text">{{ keyword ? '未找到相关成员' : '暂无团队成员' }}</text>
		</view>

		<uni-popup id="dialogmoneyInput" ref="dialogmoneyInput" type="dialog">
			<uni-popup-dialog mode="input" title="转账金额" value="" placeholder="请输入转账金额" @confirm="givemoney"></uni-popup-dialog>
		</uni-popup>
		<uni-popup id="dialogscoreInput" ref="dialogscoreInput" type="dialog">
			<uni-popup-dialog mode="input" title="转账数量" value="" placeholder="请输入转账数量" @confirm="givescore"></uni-popup-dialog>
		</uni-popup>
		
		<view v-if="dialogShow" class="popup__container">
			<view class="popup__overlay" @tap.stop="showDialog"></view>
			<view class="popup__modal">
				<view class="popup__title">
					<text class="popup__title-text">升级</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="showDialog"/>
				</view>
				<view class="popup__content">
					<view class="sheet-item" v-for="(item, index) in levelList" :key="index">
						<text class="item-text flex-item">{{item.name}}</text>
						<view class="flex1"></view><view @tap="changeLevel" :data-id="item.id" :data-name="item.name" v-if="item.id != tempLevelid && item.sort > tempLevelsort" :style="{'color':t('color1')}">选择</view><view v-else style="color: #ccc;">选择</view>
					</view>
				</view>
			</view>
		</view>
	</block>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading && pagenum > 1"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      st: 1,
      datalist: [],
      pagenum: 1,
			userlevel:{},
			userinfo:{},
			textset:{},
			levelList:{},
			team_settings: {},
			keyword:'',
			tomid:'',
			tomoney:0,
			toscore:0,
      nodata: false,
      nomore: false,
			dialogShow: false,
			tempMid: '',
			tempLevelid: '',
			tempLevelsort: '',
			start_time: '', // 开始时间
			end_time: '',   // 结束时间
			statistics: {}, // 统计数据
			expandedIndex: -1, // -1表示所有卡片都是折叠的
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1
      this.getdata(true);
    }
  },
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var st = that.st;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
      var start_time = that.start_time;
      var end_time = that.end_time;
			that.loading = true;
			that.nodata = false;
      that.nomore = false;
      app.post('ApiAgent/team', {st: st,pagenum: pagenum,keyword:keyword,start_time:start_time,end_time:end_time}, function (res) {
				that.loading = false;
        var data = res.datalist;
        if (pagenum == 1) {
					that.userinfo = res.userinfo;
					that.userlevel = res.userlevel;
					that.textset = app.globalData.textset;
          that.datalist = data;
					that.levelList = res.levelList;
					that.team_settings = res.team_settings || {};
					that.statistics = res.statistics || {}; // 获取统计数据
          if (data.length == 0) {
            that.nodata = true;
          }
					uni.setNavigationBarTitle({
						title: that.t('我的团队')
					});
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
	  goDaigoupick: function(e) {
	        var mid = e.currentTarget.dataset.id;
	        uni.navigateTo({
	            url: '/pagesExa/daike/daigoupick?mid=' + mid
	        });
	    },
    changetab: function (st) {
			this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
		givemoneyshow:function(e){
			var that = this;
			var id = e.currentTarget.dataset.id;
			that.tomid = id;
			that.$refs.dialogmoneyInput.open();
		},
		givescoreshow:function(e){
			var that = this;
			var id = e.currentTarget.dataset.id;
			that.tomid = id;
			that.$refs.dialogscoreInput.open();
		},
		givemoney:function(done, money){
			var that = this;
			var id = that.tomid;
			app.showLoading('提交中');
			app.post('ApiAgent/givemoney', {id:id,money:money}, function (res) {
				app.showLoading(false);
				if (res.status == 0) {
          app.error(res.msg);
        } else {
          app.success(res.msg);
					that.getdata();
					that.$refs.dialogmoneyInput.close();
				}
			})
		},
		givescore:function(done, score){
			var that = this;
			var id = that.tomid;
			app.showLoading('提交中');
			app.post('ApiAgent/givescore', {id:id,score:score}, function (res) {
				app.showLoading(false);
				if (res.status == 0) {
          app.error(res.msg);
        } else {
          app.success(res.msg);
					that.getdata();
					that.$refs.dialogscoreInput.close();
				}
			})
		},
    searchChange: function (e) {
      this.keyword = e.detail.value;
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
      that.getdata();
    },
		showDialog:function(e){
			let that = this;
			that.tempMid = e.currentTarget.dataset.id;
			that.tempLevelid = e.currentTarget.dataset.levelid;
			that.tempLevelsort = e.currentTarget.dataset.levelsort;
			this.dialogShow = !this.dialogShow
		},
		changeLevel: function (e) {
			var that = this;
			var mid = that.tempMid;
			var levelId = e.currentTarget.dataset.id;
			var levelName = e.currentTarget.dataset.name;
			app.confirm('确定要升级为'+levelName+'吗?', function () {
				app.showLoading('提交中');
			  app.post('ApiAgent/levelUp', {mid: mid,levelId:levelId}, function (res) {
					app.showLoading(false);
					if (res.status == 0) {
					  app.error(res.msg);
					} else {
						app.success(res.msg);
						that.dialogShow = false;
						that.getdata();
					}
			  });
			});
    },
		goToRelationChart: function(e) {
			var mid = e.currentTarget.dataset.id;
			uni.navigateTo({
				url: '/activity/commission/teamchart?mid=' + mid
			});
		},
		toggleExpand: function(index) {
			if (this.expandedIndex === index) {
				this.expandedIndex = -1;
			} else {
				this.expandedIndex = index;
			}
		},
    // 时间筛选相关方法
    onStartDateChange: function(e) {
    	this.start_time = e.detail.value;
    },
    onEndDateChange: function(e) {
    	this.end_time = e.detail.value;
    },
    applyTimeFilter: function() {
    	if (this.start_time && this.end_time) {
    		if (this.start_time > this.end_time) {
    			uni.showToast({
    				title: '开始时间不能大于结束时间',
    				icon: 'none'
    			});
    			return;
    		}
    	}
    	this.getdata();
    },
    resetTimeFilter: function() {
    	this.start_time = '';
    	this.end_time = '';
    	this.getdata();
    },
  }
};
</script>
<style>
.container {
	background-color: #f7f8fa;
	min-height: 100vh;
	padding-bottom: 20rpx;
}

.topsearch{width:94%;margin:16rpx 3%;background: #fff; border-radius: 8rpx;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f5f5f5;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:20rpx; margin-right: 10rpx;}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.content {
	width: 94%;
	margin: 0 3%;
	margin-top: 20rpx;
}

.member-list {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.member-item {
	background: #fff;
	border-radius: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
	overflow: hidden;
	display: flex;
	flex-direction: column;
	transition: all 0.3s ease;
	animation: fadeInUp 0.5s ease-out forwards;
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	color: #fff;
	cursor: pointer;
}

.user-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.avatar {
	width: 90rpx;
	height: 90rpx;
	border-radius: 50%;
	border: 4rpx solid rgba(255, 255, 255, 0.5);
}

.nickname-line {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.info-text .nickname {
	font-size: 32rpx;
	font-weight: bold;
}

.relation-chart-btn {
	background: rgba(255, 255, 255, 0.2);
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	font-weight: 500;
	transition: background 0.3s;
}
.relation-chart-btn:hover {
	background: rgba(255, 255, 255, 0.4);
}

.info-text .join-time {
	font-size: 22rpx;
	opacity: 0.9;
	margin-top: 8rpx;
}

.right-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.user-level-badge {
	font-size: 22rpx;
	background: rgba(255, 255, 255, 0.2);
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-weight: 500;
}

.arrow-icon {
	width: 28rpx;
	height: 28rpx;
	transition: transform 0.3s ease-in-out;
}

.arrow-icon.expanded {
	transform: rotate(180deg);
}

.expandable-content {
	max-height: 0;
	overflow: hidden;
	transition: max-height 0.4s ease-in-out, padding 0.4s ease-in-out;
}

.expandable-content.expanded {
	max-height: 1000rpx; /* Set a sufficiently large value */
}

.item-body {
	padding: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.info-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
	gap: 20rpx;
}

.info-cell {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	background: #f7f8fa;
	padding: 16rpx;
	border-radius: 12rpx;
}

.info-label {
	font-size: 24rpx;
	color: #999;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.info-value.highlight {
	font-size: 32rpx;
	font-weight: bold;
}

.item-footer {
	padding: 0 24rpx 24rpx;
	display: flex;
	justify-content: flex-end;
	flex-wrap: wrap;
	gap: 16rpx;
}

.action-btn {
	height: 60rpx;
	line-height: 60rpx;
	padding: 0 28rpx;
	border: 1rpx solid #ddd;
	border-radius: 30rpx;
	font-size: 24rpx;
	font-weight: 500;
	color: #666;
	background: #fff;
	transition: all 0.2s ease;
}
.action-btn:hover {
	opacity: 0.8;
}

/* Skeleton Styles */
@keyframes shimmer {
  0% { background-position: -468px 0; }
  100% { background-position: 468px 0; }
}

.skeleton-item {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
}

.skeleton-line {
  height: 28rpx;
  border-radius: 8rpx;
  background: #e0e0e0;
  background-image: linear-gradient(to right, #e0e0e0 0%, #f0f0f0 20%, #e0e0e0 40%, #e0e0e0 100%);
  background-repeat: no-repeat;
  background-size: 800px 104px;
  animation: shimmer 1s linear infinite;
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.skeleton-avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: #e0e0e0;
}

.skeleton-info {
  flex: 1;
}

.skeleton-body {
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* Empty State Styles */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 24rpx;
  opacity: 0.7;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 时间筛选样式 */
.time-filter {
  width: 94%;
  margin: 16rpx 3%;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.filter-label {
  font-size: 26rpx;
  color: #333;
  margin-right: 8rpx;
  white-space: nowrap;
}

.date-picker {
  padding: 8rpx 16rpx;
  border: 1px solid #ddd;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #333;
  min-width: 140rpx;
  text-align: center;
}

.filter-btn {
  padding: 8rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-left: 8rpx;
  white-space: nowrap;
}

.reset-btn {
  background: #f0f0f0;
  color: #333;
}

/* 统计信息样式 */
.statistics-bar {
  width: 94%;
  margin: 0 3%;
  padding: 20rpx;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  margin-top: 8rpx;
  opacity: 0.9;
}

/* 弹窗样式 */
.sheet-item {display: flex;align-items: center;padding:20rpx 30rpx;}
.sheet-item .item-img {width: 44rpx;height: 44rpx;}
.sheet-item .item-text {display: block;color: #333;height: 100%;padding: 20rpx;font-size: 32rpx;position: relative; width: 90%;}
.sheet-item .item-text:after {position: absolute;content: '';height: 1rpx;width: 100%;bottom: 0;left: 0;border-bottom: 1rpx solid #eee;}
.man-btn {
  line-height: 100rpx;
  text-align: center;
  background: #FFFFFF;
  font-size: 30rpx;
  color: #FF4015;
}

</style>