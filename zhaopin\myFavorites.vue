<template>
  <view>
    <block v-if="isload">
      <view class="favorites-page">
        <!-- 顶部筛选栏 -->
        <view class="filter-bar">
          <view 
            class="filter-item"
            :class="{ active: currentTab === 'all' }"
            :style="{
              backgroundColor: currentTab === 'all' 
                ? `rgba(${t('color1rgb')}, 0.1)`
                : '#f8f8f8',
              color: currentTab === 'all' 
                ? t('color1')
                : '#666',
              boxShadow: currentTab === 'all' 
                ? `0 4rpx 12rpx rgba(${t('color1rgb')}, 0.15)`
                : '0 2rpx 8rpx rgba(0, 0, 0, 0.02)'
            }"
            @tap="switchTab('all')"
          >全部</view>
          <view 
            class="filter-item"
            :class="{ active: currentTab === 'fulltime' }"
            :style="{
              backgroundColor: currentTab === 'fulltime' 
                ? `rgba(${t('color1rgb')}, 0.1)`
                : '#f8f8f8',
              color: currentTab === 'fulltime' 
                ? t('color1')
                : '#666',
              boxShadow: currentTab === 'fulltime' 
                ? `0 4rpx 12rpx rgba(${t('color1rgb')}, 0.15)`
                : '0 2rpx 8rpx rgba(0, 0, 0, 0.02)'
            }"
            @tap="switchTab('fulltime')"
          >全职</view>
          <view 
            class="filter-item"
            :class="{ active: currentTab === 'parttime' }"
            :style="{
              backgroundColor: currentTab === 'parttime' 
                ? `rgba(${t('color1rgb')}, 0.1)`
                : '#f8f8f8',
              color: currentTab === 'parttime' 
                ? t('color1')
                : '#666',
              boxShadow: currentTab === 'parttime' 
                ? `0 4rpx 12rpx rgba(${t('color1rgb')}, 0.15)`
                : '0 2rpx 8rpx rgba(0, 0, 0, 0.02)'
            }"
            @tap="switchTab('parttime')"
          >兼职</view>
        </view>

        <!-- 职位列表 -->
        <scroll-view 
          scroll-y 
          class="job-list"
          @scrolltolower="loadMore"
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
        >
          <!-- 列表内容 -->
          <block v-if="favoriteJobs.length > 0">
            <view 
              v-for="(job, index) in favoriteJobs" 
              :key="index"
              class="job-card"
              @tap="viewJobDetail(job.id)"
            >
              <!-- 职位信息 -->
              <view class="job-info">
                <view class="job-header">
                  <text class="job-title">{{ job.title }}</text>
                  <text class="job-salary">{{ job.salary }}</text>
                </view>
                <view class="company-info">
                  <image class="company-logo" :src="job.companyLogo" mode="aspectFit"></image>
                  <text class="company-name">{{ job.companyName }}</text>
                  <text class="collect-time">{{ job.collectTime }}</text>
                </view>
              </view>
              
              <!-- 底部标签和操作 -->
              <view class="job-footer">
                <view class="job-tags">
                  <text class="tag" v-for="(tag, tagIndex) in job.tags" :key="tagIndex">
                    {{ tag }}
                  </text>
                </view>
                <view class="action-buttons">
                  <button 
                    class="action-btn delete"
                    @tap.stop="removeFromFavorites(job.id)"
                  >取消收藏</button>
                </view>
              </view>
            </view>
          </block>

          <!-- 空状态 -->
          <view v-else class="empty-state">
            <image class="empty-icon" src="/static/icons/empty-favorites.png" mode="aspectFit"></image>
            <text class="empty-text">暂无收藏的职位</text>
            <button 
              class="browse-btn" 
              @tap="goToJobList"
              :style="{
                background: `linear-gradient(135deg, ${t('color1')}, ${t('color1')}dd)`,
                boxShadow: `0 8rpx 20rpx rgba(${t('color1rgb')}, 0.25)`
              }"
            >去浏览职位</button>
          </view>

          <!-- 加载状态 -->
          <view v-if="isLoading && favoriteJobs.length > 0" class="loading-more">
            正在加载更多...
          </view>
          <view v-if="noMoreData && favoriteJobs.length > 0" class="no-more-data">
            没有更多数据了
          </view>
        </scroll-view>
      </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      currentTab: 'all',
      favoriteJobs: [],
      isLoading: false,
      isRefreshing: false,
      noMoreData: false,
      page: 1,
      pageSize: 10,
      pre_url: app.globalData.pre_url
    }
  },

  onLoad(opt) {
    this.opt = app.getopts(opt);
    this.loadFavoriteJobs();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  onShareAppMessage() {
    return this._sharewx({
      title: '我的收藏',
      desc: '查看收藏的职位',
      pic: ''
    });
  },

  onShareTimeline() {
    var sharewxdata = this._sharewx({
      title: '我的收藏',
      desc: '查看收藏的职位',
      pic: ''
    });
    var query = (sharewxdata.path).split('?')[1];
    return {
      title: sharewxdata.title,
      imageUrl: sharewxdata.imageUrl,
      query: query
    }
  },

  onReachBottom() {
    if (!this.noMoreData && !this.isLoading) {
      this.loadMore();
    }
  },

  methods: {
    // 切换标签
    switchTab(tab) {
      if (this.currentTab === tab) return;
      this.currentTab = tab;
      this.page = 1;
      this.noMoreData = false;
      this.favoriteJobs = [];
      this.loadFavoriteJobs();
    },

    // 加载收藏的职位
    async loadFavoriteJobs() {
      if (this.isLoading || this.noMoreData) return;
      this.isLoading = true;
      this.loading = true;

      const params = {
        page: this.page,
        limit: this.pageSize
      };
      
      // 根据当前标签筛选类型
      if (this.currentTab !== 'all') {
        params.type = this.currentTab === 'fulltime' ? 1 : 2;
      }

      app.get('/apiZhaopin/getFavoriteList', params, (res) => {
        this.loading = false;
        this.isLoading = false;
        this.isRefreshing = false;
        uni.stopPullDownRefresh();

        if (res.status === 1) {
          const jobList = res.data.list.map(item => ({
            id: item.id,
            title: item.title,
            salary: item.salary,
            companyName: item.company_name,
            companyLogo: item.company_logo,
            tags: [
              item.education,
              item.experience,
              item.work_address
            ].filter(Boolean),
            collectTime: item.create_time
          }));

          if (this.page === 1) {
            this.favoriteJobs = jobList;
          } else {
            this.favoriteJobs = [...this.favoriteJobs, ...jobList];
          }

          this.noMoreData = jobList.length < this.pageSize || this.favoriteJobs.length >= res.data.total;
          this.page++;
          this.isload = true;
        } else {
          uni.showToast({
            title: res.msg || '加载失败，请重试',
            icon: 'none'
          });
        }
      });
    },

    // 下拉刷新
    async onRefresh() {
      this.isRefreshing = true;
      this.page = 1;
      this.noMoreData = false;
      await this.loadFavoriteJobs();
    },

    // 加载更多
    loadMore() {
      this.loadFavoriteJobs();
    },

    // 查看职位详情
    viewJobDetail(jobId) {
      uni.navigateTo({
        url: `/zhaopin/partdetails?id=${jobId}`
      });
    },

    // 移除收藏
    removeFromFavorites(jobId) {
      uni.showModal({
        title: '提示',
        content: '确定要取消收藏该职位吗？',
        success: (res) => {
          if (res.confirm) {
            this.toggleFavorite(jobId);
          }
        }
      });
    },

    // 切换收藏状态
    toggleFavorite(jobId) {
      app.post('/apiZhaopin/favoritePosition', {
        position_id: jobId
      }, (res) => {
        if (res.status === 1) {
          // 如果是取消收藏，从列表中移除
          if (res.data.is_favorite === 0) {
            this.favoriteJobs = this.favoriteJobs.filter(job => job.id !== jobId);
          }
          uni.showToast({
            title: res.msg,
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.msg || '操作失败',
            icon: 'none'
          });
        }
      });
    },

    // 跳转到职位列表
    goToJobList() {
      uni.navigateTo({
        url: '/zhaopin/index'
      });
    }
  }
}
</script>

<style lang="scss">
.favorites-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: env(safe-area-inset-bottom);
  
  .filter-bar {
    display: flex;
    background-color: rgba(255, 255, 255, 0.98);
    padding: 24rpx 30rpx;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    backdrop-filter: blur(10px);
    
    .filter-item {
      padding: 16rpx 40rpx;
      font-size: 28rpx;
      color: #666;
      background-color: #f8f8f8;
      border-radius: 36rpx;
      margin-right: 24rpx;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
      
      &.active {
        background-color: v-bind('`rgba(${t("color1rgb")}, 0.1)`');
        color: v-bind('t("color1")');
        font-weight: 500;
        box-shadow: v-bind('`0 4rpx 12rpx rgba(${t("color1rgb")}, 0.15)`');
      }
    }
  }
  
  .job-list {
    height: calc(100vh - 88rpx);
    padding: 16rpx;
    
    .job-card {
      margin: 20rpx 12rpx;
      padding: 28rpx;
      background-color: #fff;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1rpx solid rgba(0, 0, 0, 0.02);
      
      &:active {
        transform: scale(0.985);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
      }
      
      .job-info {
        .job-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20rpx;
          
          .job-title {
            font-size: 32rpx;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.4;
          }
          
          .job-salary {
            font-size: 30rpx;
            font-weight: 600;
            color: #ff4d4f;
            background: rgba(255, 77, 79, 0.08);
            padding: 4rpx 16rpx;
            border-radius: 6rpx;
          }
        }
        
        .company-info {
          display: flex;
          align-items: center;
          margin-bottom: 24rpx;
          
          .company-logo {
            width: 56rpx;
            height: 56rpx;
            border-radius: 12rpx;
            margin-right: 16rpx;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
            border: 1rpx solid rgba(0, 0, 0, 0.04);
          }
          
          .company-name {
            font-size: 26rpx;
            color: #666;
            margin-right: 16rpx;
          }
          
          .collect-time {
            font-size: 24rpx;
            color: #999;
            background: #f8f9fa;
            padding: 2rpx 12rpx;
            border-radius: 4rpx;
          }
        }
      }
      
      .job-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20rpx;
        
        .job-tags {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;
          margin-right: 16rpx;
          
          .tag {
            padding: 4rpx 16rpx;
            background-color: #f8f9fa;
            border-radius: 4rpx;
            font-size: 24rpx;
            color: #666;
            border: 1rpx solid #eee;
          }
        }
        
        .action-buttons {
          .action-btn {
            padding: 6rpx 16rpx;
            font-size: 24rpx;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            position: relative;
            overflow: visible;
            font-weight: 400;
            min-width: 80rpx;
            text-align: center;
            height: 32rpx;
            line-height: 32rpx;
            
            &::before {
              content: '';
              position: absolute;
              top: 0;
              right: -16rpx;
              width: 32rpx;
              height: 100%;
              background: inherit;
              transform: skewX(-30deg);
              transform-origin: left bottom;
              z-index: -1;
            }
            
            &::after {
              content: '';
              position: absolute;
              bottom: -4rpx;
              right: -4rpx;
              width: 8rpx;
              height: 8rpx;
              background: rgba(0, 0, 0, 0.1);
              transform: rotate(45deg);
              z-index: -2;
            }
            
            &.delete {
              background: #f5f5f5;
              color: #999;
              
              &:active {
                transform: translateY(1rpx);
                color: #666;
                background: #f0f0f0;
              }
              
              &::before {
                box-shadow: 1rpx 0 3rpx rgba(0, 0, 0, 0.03);
              }
            }
          }
        }
      }
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 240rpx;
    
    .empty-icon {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 40rpx;
      opacity: 0.8;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
      margin-bottom: 48rpx;
    }
    
    .browse-btn {
      padding: 24rpx 64rpx;
      background: v-bind('`linear-gradient(135deg, ${t("color1")}, ${t("color1")}dd)`');
      color: #fff;
      font-size: 28rpx;
      border-radius: 44rpx;
      box-shadow: v-bind('`0 8rpx 20rpx rgba(${t("color1rgb")}, 0.25)`');
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: none;
      font-weight: 500;
      letter-spacing: 2rpx;
      
      &:active {
        transform: translateY(2rpx) scale(0.98);
        box-shadow: v-bind('`0 4rpx 12rpx rgba(${t("color1rgb")}, 0.2)`');
        background: v-bind('`linear-gradient(135deg, ${t("color1")}, ${t("color1")}dd)`');
      }
    }
  }
  
  .loading-more,
  .no-more-data {
    text-align: center;
    padding: 30rpx 0;
    font-size: 26rpx;
    color: #999;
    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.02));
  }
}
</style> 