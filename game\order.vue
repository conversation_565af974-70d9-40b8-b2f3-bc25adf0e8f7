<template>
	<view class="container">
		<block v-if="isload">
			<dd-tab
				:itemdata="['抽奖订单', '中奖订单', '兑换订单']"
				:itemst="['0', '1', '3']"
				:st="st"
				:isfixed="true"
				@changetab="changetab"
			></dd-tab>
			<view style="width: 100%; height: 100rpx"></view>
			<!-- #ifndef H5 || APP-PLUS -->
			<view class="topsearch flex-y-center">
				<view class="f1 flex-y-center">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input
						:value="keyword"
						placeholder="输入关键字搜索"
						placeholder-style="font-size:24rpx;color:#C2C2C2"
						@confirm="searchConfirm"
					/>
				</view>
			</view>
			<!--  #endif -->
			<view class="order-content">
				<block v-for="(item, index) in datalist" :key="item.id">
					<view class="order-box">
						<view class="head">
							<view class="f1">
								<image :src="item.sweepstakes_img" class="logo-row"></image>
								{{ item.sweepstakes_name }}
							</view>
							<view class="flex1"></view>
							<text class="st2">{{ statusEnum[item.status] }}</text>
						</view>
						<view class="content">
							<view class="detail">
								<text class="t2">订单号: {{ item.ordernum }}</text>
							</view>
						</view>
						<block v-for="subItem in item.product_list" :key="subItem.id">
							<view class="content">
								<view>
									<image :src="subItem.pic"></image>
								</view>
								<view class="detail">
									<text class="t1">{{ subItem.name }}</text>
									<text class="t2"></text>
									<view class="t3">
										<text class="x1 flex1">￥{{ subItem.price }}</text>
										<text class="x2">×1</text>
									</view>
								</view>
							</view>
						</block>
						<view class="bottom">
							<text>共计{{item.product_list.length}}件商品 实付:￥{{ item.totalprice }}</text>
						</view>

						<view class="op">
							<!-- <view @tap.stop="goDetail(item)" class="btn2">详情</view> -->
							<view
								v-if="item.status === 2"
								@tap.stop="goCharge(item)"
								class="btn1"
								:style="{ background: t('color1') }"
							>
								兑换
							</view>
						</view>
					</view>
				</block>
			</view>
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>

			<uni-popup ref="popupCharge" type="dialog">
				<view class="pri_box">
					<view class="pri_box_header">
						兑换
						<view class="close_wrap" @tap="onCloseModal">
							<uni-icons type="closeempty" size="24" color="#fff"></uni-icons>
						</view>
					</view>
					<view class="pri_box_content">
						<input class="input-class" type="text" :value="dlivery_code" @input="inputCode" />
						<view class="btn-confirm" @tap="onConfirmCharge">确认兑换</view>
					</view>
				</view>
			</uni-popup>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			menuindex: -1,

			st: '1',
			datalist: [],
			pagenum: 1,
			nomore: false,
			nodata: false,
			codtxt: '',
			canrefund: 1,
			express_content: '',
			selectExpressShow: false,
			hexiao_qr: '',
			keyword: '',
			currentInfo: {},
			dlivery_code: '',
			statusEnum: {
				0: '未支付',
				1: '未中奖',
				2: '已中奖',
				3: '已兑奖'
			}
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		if (this.opt && this.opt.st) {
			this.st = this.opt.st;
		}
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	onReachBottom: function () {
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getdata(true);
		}
	},
	onNavigationBarSearchInputConfirmed: function (e) {
		this.searchConfirm({ detail: { value: e.text } });
	},
	methods: {
		getdata: function (loadmore) {
			if (!loadmore) {
				this.pagenum = 1;
				this.datalist = [];
			}
			var that = this;
			var pagenum = that.pagenum;
			var st = that.st;
			that.nodata = false;
			that.nomore = false;
			that.loading = true;
			app.post('ApiSweepstakes/orderList', { status: st, page: pagenum, keyword: that.keyword }, function (res) {
				that.loading = false;
				var data = res.list;
				if (pagenum == 1) {
					that.codtxt = res.codtxt;
					that.canrefund = res.canrefund;
					that.datalist = data;
					if (data.length == 0) {
						that.nodata = true;
					}
					that.loaded();
				} else {
					if (data.length == 0) {
						that.nomore = true;
					} else {
						var datalist = that.datalist;
						var newdata = datalist.concat(data);
						that.datalist = newdata;
					}
				}
			});
		},
		changetab: function (st) {
			this.st = st;
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0
			});
			this.getdata();
		},
		searchConfirm: function (e) {
			this.keyword = e.detail.value;
			this.getdata(false);
		},
		/**
		 * 详情
		 * @param {Object} item
		 */
		goDetail(item) {},
		/**
		 * 兑换
		 * @param {Object} item
		 */
		goCharge(item) {
			this.currentInfo = Object.assign({}, item);
			this.$refs.popupCharge.open();
		},
		onCloseModal() {
			this.$refs.popupCharge.close();
		},
		inputCode(e) {
			this.dlivery_code = e.detail.value;
		},
		onConfirmCharge() {
			let that = this;
			that.loading = true;
			const params = {
				dlivery_code: that.dlivery_code,
				oid: that.currentInfo.id
			};
			app.post('ApiSweepstakes/getDlivery', params, function (res) {
				that.loading = false;
				if (res && res.status == 1) {
					app.alert('兑换成功');
					that.$refs.popupCharge.close();
					that.changetab();
				} else {
					app.alert(res.msg || '出错了');
				}
			});
		}
	}
};
</script>
<style>
.container {
	width: 100%;
}
.topsearch {
	width: 94%;
	margin: 10rpx 3%;
}
.topsearch .f1 {
	height: 60rpx;
	border-radius: 30rpx;
	border: 0;
	background-color: #fff;
	flex: 1;
}
.topsearch .f1 .img {
	width: 24rpx;
	height: 24rpx;
	margin-left: 10px;
}
.topsearch .f1 input {
	height: 100%;
	flex: 1;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
}
.order-content {
	display: flex;
	flex-direction: column;
}
.order-box {
	width: 94%;
	margin: 10rpx 3%;
	padding: 6rpx 3%;
	background: #fff;
	border-radius: 8px;
}
.order-box .head {
	display: flex;
	width: 100%;
	border-bottom: 1px #f4f4f4 solid;
	height: 70rpx;
	line-height: 70rpx;
	overflow: hidden;
	color: #999;
}
.order-box .head .f1 {
	display: flex;
	align-items: center;
	color: #333;
}
.order-box .head image {
	width: 34rpx;
	height: 34rpx;
	margin-right: 4px;
}
.order-box .head .st0 {
	width: 140rpx;
	color: #ff8758;
	text-align: right;
}
.order-box .head .st1 {
	width: 140rpx;
	color: #ffc702;
	text-align: right;
}
.order-box .head .st2 {
	width: 140rpx;
	color: #ff4246;
	text-align: right;
}
.order-box .head .st3 {
	width: 140rpx;
	color: #999;
	text-align: right;
}
.order-box .head .st4 {
	width: 140rpx;
	color: #bbb;
	text-align: right;
}

.order-box .content {
	display: flex;
	width: 100%;
	padding: 16rpx 0px;
	border-bottom: 1px #f4f4f4 dashed;
	position: relative;
}
.order-box .content:last-child {
	border-bottom: 0;
}
.order-box .content image {
	width: 140rpx;
	height: 140rpx;
}
.order-box .content .detail {
	display: flex;
	flex-direction: column;
	margin-left: 14rpx;
	flex: 1;
}
.order-box .content .detail .t1 {
	font-size: 26rpx;
	line-height: 36rpx;
	margin-bottom: 10rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.order-box .content .detail .t2 {
	height: 46rpx;
	line-height: 46rpx;
	color: #999;
	overflow: hidden;
	font-size: 26rpx;
}
.order-box .content .detail .t3 {
	display: flex;
	height: 40rpx;
	line-height: 40rpx;
	color: #ff4246;
}
.order-box .content .detail .x1 {
	flex: 1;
}
.order-box .content .detail .x2 {
	width: 100rpx;
	font-size: 32rpx;
	text-align: right;
	margin-right: 8rpx;
}

.order-box .bottom {
	width: 100%;
	padding: 10rpx 0px;
	border-top: 1px #f4f4f4 solid;
	color: #555;
}
.order-box .op {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-end;
	align-items: center;
	width: 100%;
	padding: 10rpx 0px;
	border-top: 1px #f4f4f4 solid;
	color: #555;
}

.btn1 {
	margin-left: 20rpx;
	margin-top: 10rpx;
	width: 160rpx;
	height: 60rpx;
	line-height: 60rpx;
	color: #fff;
	border-radius: 3px;
	text-align: center;
}
.btn2 {
	margin-left: 20rpx;
	margin-top: 10rpx;
	width: 160rpx;
	height: 60rpx;
	line-height: 60rpx;
	color: #333;
	background: #fff;
	border: 1px solid #cdcdcd;
	border-radius: 3px;
	text-align: center;
}

.hxqrbox {
	background: #fff;
	padding: 50rpx;
	position: relative;
	border-radius: 20rpx;
}
.hxqrbox .img {
	width: 400rpx;
	height: 400rpx;
}
.hxqrbox .txt {
	color: #666;
	margin-top: 20rpx;
	font-size: 26rpx;
	text-align: center;
}
.hxqrbox .close {
	width: 50rpx;
	height: 50rpx;
	position: absolute;
	bottom: -100rpx;
	left: 50%;
	margin-left: -25rpx;
	border: 1px solid rgba(255, 255, 255, 0.5);
	border-radius: 50%;
	padding: 8rpx;
}
/* 兑换 */
.charge-wrap {
	background: #fff;
}
.charge-content {
	display: flex;
	justify-content: flex-start;
}

/* 弹窗 */
.pri_box {
	width: 240px;
	height: 250px;
	margin: 180px auto 0;
	background-color: #fff;
	text-align: center;
}

.pri_box_header {
	height: 60px;
	background: url(../game/static/game/happy/box_header_bg.png) no-repeat;
	background-size: 100% 100%;
	line-height: 60px;
	color: #fff;
	font-size: 20px;
	font-weight: 700;
	position: relative;
}

.btn-confirm {
	width: 150px;
	height: 32px;
	text-align: center;
	margin: 36px auto 0;
	background: url(../game/static/game/happy/btn-confirm.png) no-repeat;
	background-size: 100% 100%;
	color: #fff;
	line-height: 32px;
	font-size: 14px;
}

.pri_box_content {
	padding: 20px;
	display: flex;
	flex-direction: column;
}

.input-class {
	background: #efefef;
	height: 32px;
	border-radius: 3px;
	border: none;
	outline: none;
	padding-left: 5px;
	color: #f85e06;
	font-weight: 700;
	text-align: left;
}

.lucky_text {
	text-align: left;
	margin-bottom: 10px;
	font-size: 13px;
}

.lucky_tips {
	margin-top: 3px;
	text-align: center;
	font-size: 12px;
	color: #aaa5a5;
}

.close_wrap {
	position: absolute;
	top: 15px;
	right: 5px;
	line-height: 0;
}
</style>
