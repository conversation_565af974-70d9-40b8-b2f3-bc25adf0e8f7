<template>
	<view class="container">
		<block v-if="isload">
			<form @submit="topay">
				<view v-for="(buydata, index) in allbuydata" :key="index" class="buydata">
					<view class="btitle">
						<image class="img" src="/static/img/ico-shop.png" />{{buydata.business.name}}
					</view>
					<view class="bcontent">
						<view class="product">
							<view v-for="(item, index2) in buydata.prodata" :key="index2">
								<view class="item flex">
									<view class="img" @tap="goto" :data-url="'/shopPackage/shop/product?id=' + item.product.id">
										<image v-if="item.guige.pic" :src="item.guige.pic"></image>
										<image v-else :src="item.product.pic"></image>
									</view>
									<view class="info flex1">
										<view class="f1">{{item.product.name}}</view>
										<view class="f2">规格：{{item.guige.name}}</view>
										<view class="f3">
											<block v-if="order_change_price"><input type="number" :value="item.guige.sell_price" :data-price="item.guige.sell_price" :data-index="index" :data-index2="index2" class="inputPrice" @input="inputPrice"></block>
											<block v-else><text style="font-weight:bold;">￥{{buydata.product_price}}</text></block>
											<text style="padding-left:20rpx"> × {{item.num}}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view class="storeitem" v-if="buydata.freightList[buydata.freightkey].pstype==1">
							<block v-for="(item, idx) in buydata.freightList[buydata.freightkey].storedata" :key="idx">
								<view class="radio-item" @tap.stop="choosestore" :data-bid="buydata.bid" :data-index="idx" v-if="idx<5 || storeshowall==true">
									<view class="f1">{{item.name}} </view>
									<text style="color:#f50;">{{item.juli}}</text>
									<view class="radio"
										:style="buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''">
										<image class="radio-img" src="/static/img/checkd.png" />
									</view>
								</view>
							</block>
							<view v-if="storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5" class="storeviewmore" @tap="doStoreShowAll">- 查看更多 - </view>
						</view>
						

                        <!-- view class="price">
							<text class="f1">商品涨幅</text>
							<text class="f2">{{miaosha.zhangfu}}%</text>
						</view> -->
						<!-- <view class="price">
							<text class="f1">商品编号</text>
							<text class="f2">{{buydata.procode}}</text>
						</view> -->
						<view class="price">
							<text class="f1">涨幅比例</text>
							<text class="f2">{{adminset.miaosha_zhangfu}}%</text>
						</view>
						<view class="price">
							<text class="f1">委托价格</text>
							<!-- <text class="f2">¥{{buydata.product_price}}</text> -->
							<text class="f2">¥{{weituo_price}}</text>
						</view>
						<view class="price">
							<text class="f1">手续费比例</text>
							<text class="f2">{{adminset.miaosha_sxf}}%</text>
						</view>
						<view class="price">
							<text class="f1">手续费</text>
							<text class="f2">¥{{shouxufei}}</text>
						</view>
						<view style="display:none">{{test}}</view>
						<view class="form-item" v-for="(item,idx) in buydata.freightList[buydata.freightkey].formdata" :key="item.id">
							<view class="label">{{item.val1}}<text v-if="item.val3==1" style="color:red"> *</text></view>
							<block v-if="item.key=='input'">
								<input type="text" :name="'form'+buydata.bid+'_'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx"/>
							</block>
							<block v-if="item.key=='textarea'">
								<textarea :name="'form'+buydata.bid+'_'+idx" class='textarea' :placeholder="item.val2" placeholder-style="font-size:28rpx"/>
							</block>
							<block v-if="item.key=='radio'">
								<radio-group class="radio-group" :name="'form'+buydata.bid+'_'+idx">
									<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
										<radio class="radio" :value="item1"/>{{item1}}
									</label>
								</radio-group>
							</block>
							<block v-if="item.key=='checkbox'">
								<checkbox-group :name="'form'+buydata.bid+'_'+idx" class="checkbox-group">
									<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
										<checkbox class="checkbox" :value="item1"/>{{item1}}
									</label>
								</checkbox-group>
							</block>
							<block v-if="item.key=='selector'">
								<picker class="picker" mode="selector" :name="'form'+buydata.bid+'_'+idx" value="" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx] || buydata.editorFormdata[idx]===0"> {{item.val2[buydata.editorFormdata[idx]]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='time'">
								<picker class="picker" mode="time" :name="'form'+buydata.bid+'_'+idx" value="" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx]">{{buydata.editorFormdata[idx]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='date'">
								<picker class="picker" mode="date" :name="'form'+buydata.bid+'_'+idx" value="" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx]">{{buydata.editorFormdata[idx]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='upload'">
								<input type="text" style="display:none" :name="'form'+buydata.bid+'_'+idx" :value="buydata.editorFormdata[idx]"/>
								<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
									<view class="form-imgbox" v-if="buydata.editorFormdata[idx]">
										<view class="form-imgbox-img"><image class="image" :src="buydata.editorFormdata[idx]" @click="previewImage" :data-url="buydata.editorFormdata[idx]" mode="widthFix"/></view>
									</view>
									<view class="form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-bid="buydata.bid" :data-idx="idx"></view>
								</view>
							</block>
						</view>
					</view>
				</view>
				
				<view style="width: 100%; height:110rpx;"></view>
				<view class="footer flex notabbarbot">
					<view class="text1 flex1">总计：
						<text style="font-weight:bold;font-size:36rpx">￥{{shouxufei}}</text>
					</view>
					<button class="op" form-type="submit" :style="{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" :disabled="submitDisabled">
						委托上架</button>
				</view>
			</form>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				pre_url:app.globalData.pre_url,
				test:'test',
				havetongcheng: 0,
				address: [],
				memberList: [],
				checkMem:{},
				usescore: 0,
				scoredk_money: 0,
				totalprice: '0.00',
				couponvisible: false,
				cuxiaovisible: false,
				membervisible: false,
				memberinfovisible:false,
				selectmemberinfo:{},
				bid: 0,
				nowbid: 0,
				needaddress: 1,
				linkman: '',
				tel: '',
				userinfo: {},
				miaosha:{},
				outmember:{},
				pstimeDialogShow: false,
				pstimeIndex: -1,
				manjian_money: 0,
				cxid: 0,
				cxids: [],
				latitude: "",
				longitude: "",
				allbuydata: {},
				allbuydatawww: {},
				alltotalprice: "",
				cuxiaoinfo: false,
				cuxiaoList: {},
				type11visible: false,
				type11key: -1,
				regiondata: '',
				items: [],
				editorFormdata:[],
				buy_selectmember:false,
				multi_promotion:0,
				storeshowall:false,
				order_change_price:false,
				invoiceShow:false,
				invoice:{},
				invoice_type:[],
				invoice_type_select:1,
				name_type_select:1,
				name_type_personal_disabled:false,
				inputDisabled:false,
				submitDisabled:false,
				pstype3needAddress:false,
				isshowglass:false,
				glassrecordlist:[],
				grid:0,
				curindex:-1,
				curindex2:-1,
				ccid:0,
				adminset:[],
				weituo_price:0,
				shouxufei:0,
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.ccid= this.opt.ccid ? this.opt.ccid  : 1;
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiMiaosha/buyweituo', {
					prodata: this.opt.id
				}, function(res) {
					that.loading = false;
					//登录/注册
					if(res.status == 'denglu')
					{
						app.alert(res.msg)
						setTimeout(function () {
						  app.goto('/pagesB/login/login');
						}, 1500);
						return;
					}
					//卡号
					if(res.status == 'kahao')
					{
						app.alert(res.msg)
						setTimeout(function () {
						  app.goto('/pages/my/setshoukuan');
						}, 1500);
						return;
					}
					//地址
					if(res.status == 'address')
					{
						app.alert(res.msg)
						setTimeout(function () {
						  app.goto('/pages/address/addressadd');
						}, 1500);
						return;
					}
					if (res.status == 0) {
						if (res.msg) {
							app.alert(res.msg, function() {
								if (res.url) {
									app.goto(res.url);
								} else {
									app.goback();
								}
							});
						} else if (res.url) {
							app.goto(res.url);
						} else {
							app.alert('您没有权限购买该商品');
						}
						return;
					}
					that.havetongcheng = res.havetongcheng;
					that.address = res.address;
					that.linkman = res.linkman;
					that.tel = res.tel;
					that.userinfo = res.userinfo;
				    that.miaosha = res.miaosha;
					that.adminset = res.adminset;
					that.weituo_price = res.allbuydata[0]['product_price']+res.allbuydata[0]['product_price']*res.adminset.miaosha_zhangfu*0.01;
					that.weituo_price = that.weituo_price.toFixed(2);
					that.shouxufei = res.allbuydata[0]['product_price']*that.adminset.miaosha_sxf*0.01;
					that.shouxufei  = that.shouxufei.toFixed(2);
					that.outmember = res.outmember
					that.buy_selectmember = res.buy_selectmember;
					that.order_change_price = res.order_change_price;
					that.pstype3needAddress = res.pstype3needAddress;
					if(that.buy_selectmember){
						uni.request({
							url: app.globalData.pre_url+'/static/area2.json',
							data: {},
							method: 'GET',
							header: { 'content-type': 'application/json' },
							success: function(res2) {
								that.items = res2.data
							}
						})
					}
					that.allbuydata = res.allbuydata;
					that.allbuydatawww = JSON.parse(JSON.stringify(res.allbuydata));
					that.needLocation = res.needLocation;
					that.scorebdkyf = res.scorebdkyf;
					that.multi_promotion = res.multi_promotion;
					that.calculatePrice();
					that.loaded();
					
					// var allbuydata = that.allbuydata;
					// for (var i in allbuydata) {
					// 	allbuydata[i].tempInvoice = uni.getStorageSync('temp_invoice_' + allbuydata[i].bid);
					// }
					// that.allbuydata = allbuydata;
					

					if (res.needLocation == 1) {
						app.getLocation(function(res) {
							var latitude = res.latitude;
							var longitude = res.longitude;
							that.latitude = latitude;
							that.longitude = longitude;
							var allbuydata = that.allbuydata;
							for (var i in allbuydata) {
								var freightList = allbuydata[i].freightList;
								for (var j in freightList) {
									if (freightList[j].pstype == 1 || freightList[j].pstype == 5) {
										var storedata = freightList[j].storedata;
										if (storedata) {
											for (var x in storedata) {
												if (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {
													var juli = that.getDistance(latitude, longitude,storedata[x].latitude, storedata[x].longitude);
													storedata[x].juli = juli;
												}
											}
											storedata.sort(function(a, b) {
												return a["juli"] - b["juli"];
											});
											for (var x in storedata) {
												if (storedata[x].juli) {
													storedata[x].juli = storedata[x].juli + '千米';
												}
											}
											console.log(storedata);
											allbuydata[i].freightList[j].storedata = storedata;
										}
									}
								}
							}
							that.allbuydata = allbuydata;
							
						});
					}
				});
			},
			//积分抵扣
			scoredk: function(e) {
				var usescore = e.detail.value[0];
				if (!usescore) usescore = 0;
				this.usescore = usescore;
				this.calculatePrice();
			},
			inputLinkman: function(e) {
				this.linkman = e.detail.value;
			},
			inputTel: function(e) {
				this.tel = e.detail.value;
			},
			inputfield: function(e) {
				var bid = e.currentTarget.dataset.bid;
				var field = e.currentTarget.dataset.field;
				allbuydata2[bid][field] = e.detail.value;
				this.allbuydata2 = allbuydata2;
			},
			//选择收货地址
			chooseAddress: function() {
				app.goto('/pages/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));
			},
			inputPrice: function(e) {
				var that = this;
				var index = e.currentTarget.dataset.index;
				var index2 = e.currentTarget.dataset.index2;
				var allbuydata = that.allbuydata;
				var allbuydatawww = that.allbuydatawww;
				var oldprice = allbuydatawww[index]['prodata'][index2].guige.sell_price;
				if(e.detail.value == '' || parseFloat(e.detail.value) < parseFloat(oldprice)) {
					that.submitDisabled = true;
					app.error('不能小于原价:'+oldprice);
					return;
				}
				that.submitDisabled = false;
				allbuydata[index]['prodata'][index2].guige.sell_price = e.detail.value;
				allbuydata[index]['product_price'] = (e.detail.value * allbuydata[index]['prodata'][index2].num).toFixed(2);
				// allbuydata[index].prodatastr = allbuydata[index].prodatastr
				that.allbuydata = allbuydata;
				console.log(allbuydata[index]);
				that.calculatePrice();
			},
			//计算价格
			calculatePrice: function() {
				var that = this;
				var address = that.address;
				var allbuydata = that.allbuydata;
				var alltotalprice = 0;
				var allfreight_price = 0;
				var needaddress = 0;
				// console.log(allbuydata)
				for (var k in allbuydata) {
					var product_price = parseFloat(allbuydata[k].product_price);
					var leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣
					var manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动
					var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 
					var cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动 
					var invoice_money = parseFloat(allbuydata[k].invoice_money); //+发票 
					//算运费
					var freightdata = allbuydata[k].freightList[allbuydata[k].freightkey];
					var freight_price = freightdata['freight_price'];
					if (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {
						needaddress = 1;
					}
					if(that.pstype3needAddress && (freightdata.pstype == 3 || freightdata.pstype == 4 || freightdata.pstype == 5)) {
						needaddress = 1;
					}
					if (allbuydata[k].coupontype == 4) {
						freight_price = 0;
					}
					var totalprice = product_price - leveldk_money - manjian_money - coupon_money + cuxiao_money;
					if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费

					totalprice = totalprice + freight_price;
					allbuydata[k].freight_price = freight_price.toFixed(2);
					if(allbuydata[k].business.invoice && allbuydata[k].business.invoice_rate > 0 && allbuydata[k].tempInvoice){
						var invoice_money = totalprice * parseFloat(allbuydata[k].business.invoice_rate) / 100;
						allbuydata[k].invoice_money = invoice_money.toFixed(2);
						totalprice = totalprice + invoice_money;
					}
					console.log('invoice_money');
					console.log(invoice_money);
					
					allbuydata[k].totalprice = totalprice.toFixed(2);
					alltotalprice += totalprice;
					allfreight_price += freight_price;
				}
				that.needaddress = needaddress;
				if (that.usescore) {
					var scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣
				} else {
					var scoredk_money = 0;
				}

				var oldalltotalprice = alltotalprice;
				alltotalprice = alltotalprice - scoredk_money;
				if (alltotalprice < 0) alltotalprice = 0;

				if (that.scorebdkyf == '1' && scoredk_money > 0 && alltotalprice < allfreight_price) {
					//积分不抵扣运费
					alltotalprice = allfreight_price;
					scoredk_money = oldalltotalprice - allfreight_price;
				}
				var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例
				var scoremaxtype = parseInt(that.userinfo.scoremaxtype);
				var scoredkmaxmoney = parseFloat(that.userinfo.scoredkmaxmoney);

				if (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 &&
					scoredk_money > oldalltotalprice * scoredkmaxpercent * 0.01) {
					scoredk_money = oldalltotalprice * scoredkmaxpercent * 0.01;
					alltotalprice = oldalltotalprice - scoredk_money;
				} else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {
					scoredk_money = scoredkmaxmoney;
					alltotalprice = oldalltotalprice - scoredk_money;
				}

				if (alltotalprice < 0) alltotalprice = 0;
				alltotalprice = alltotalprice.toFixed(2);
				that.alltotalprice = alltotalprice;
				that.allbuydata = allbuydata;
			},
			changeFreight: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = e.currentTarget.dataset.bid;
				var index = e.currentTarget.dataset.index;
				var freightList = allbuydata[bid].freightList;
				if(freightList[index].pstype==1 && freightList[index].storedata.length < 1) {
					app.error('无可自提门店');return;
				}
				if(freightList[index].pstype==5 && freightList[index].storedata.length < 1) {
					app.error('无可配送门店');return;
				}
				allbuydata[bid].freightkey = index;
				that.allbuydata = allbuydata;
				that.calculatePrice();
				that.allbuydata[bid].editorFormdata = [];
			},
			chooseFreight: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = e.currentTarget.dataset.bid;
				// console.log(bid);
				// console.log(allbuydata);
				var freightList = allbuydata[bid].freightList;
				var itemlist = [];

				for (var i = 0; i < freightList.length; i++) {
					itemlist.push(freightList[i].name);
				}

				uni.showActionSheet({
					itemList: itemlist,
					success: function(res) {
						if (res.tapIndex >= 0) {
							allbuydata[bid].freightkey = res.tapIndex;
							that.allbuydata = allbuydata;
							that.calculatePrice();
						}
					}
				});
			},
			choosePstime: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = e.currentTarget.dataset.bid;
				var freightkey = allbuydata[bid].freightkey;
				var freightList = allbuydata[bid].freightList;
				var freight = freightList[freightkey];
				var pstimeArr = freightList[freightkey].pstimeArr;
				var itemlist = [];
				for (var i = 0; i < pstimeArr.length; i++) {
					itemlist.push(pstimeArr[i].title);
				}
				if (itemlist.length == 0) {
					app.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');
					return;
				}
				that.nowbid = bid;
				that.pstimeDialogShow = true;
				that.pstimeIndex = -1;
			},
			pstimeRadioChange: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var pstimeIndex = e.currentTarget.dataset.index;
				// console.log(pstimeIndex)
				var nowbid = that.nowbid;
				var freightkey = allbuydata[nowbid].freightkey;
				var freightList = allbuydata[nowbid].freightList;
				var freight = freightList[freightkey];
				var pstimeArr = freightList[freightkey].pstimeArr;
				var choosepstime = pstimeArr[pstimeIndex];
				allbuydata[nowbid].pstimetext = choosepstime.title;
				allbuydata[nowbid].freight_time = choosepstime.value;
				that.allbuydata = allbuydata
				that.pstimeDialogShow = false;
			},
			hidePstimeDialog: function() {
				this.pstimeDialogShow = false;
			},
			chooseCoupon: function(e) {
				var allbuydata = this.allbuydata;
				var bid = e.bid;
				var couponrid = e.rid;
				var couponkey = e.key;
				var oldcoupons = allbuydata[bid].coupons;
				var oldcouponrids = allbuydata[bid].couponrids;
				var couponList = allbuydata[bid].couponList;
				if (app.inArray(couponrid,oldcouponrids)) {
					var coupons = [];
					var couponrids = [];
					for(var i in oldcoupons){
						if(oldcoupons[i].id != couponrid){
							coupons.push(oldcoupons[i]);
							couponrids.push(oldcoupons[i].id);
						}
					}
				} else {
					coupons = oldcoupons;
					couponrids = oldcouponrids;
					console.log(allbuydata[bid].coupon_peruselimit + '---' + oldcouponrids.length);
					if(allbuydata[bid].coupon_peruselimit > oldcouponrids.length){
						console.log('xxxx');
						coupons.push(couponList[couponkey]);
						couponrids.push(couponList[couponkey].id);
					}else{
						if(allbuydata[bid].coupon_peruselimit > 1){
							app.error('最多只能选用'+allbuydata[bid].coupon_peruselimit+'张');
							return;
						}else{
							coupons = [couponList[couponkey]];
							couponrids = [couponrid];
						}
					}
				}
				console.log(coupons);
				console.log(couponrids);
				allbuydata[bid].coupons = coupons;
				allbuydata[bid].couponrids = couponrids;
				var coupon_money = 0;
				var coupontype = 1;
				for(var i in coupons){
					if(coupons[i]['type'] == 4){
						coupontype = 4;
					}else if(coupons[i]['type'] == 10){
						coupon_money += coupons[i]['thistotalprice'] * (100-coupons[i]['discount']) * 0.01;
					}else{
						coupon_money += coupons[i]['money']
					}
				}
				allbuydata[bid].coupontype = coupontype;
				allbuydata[bid].coupon_money = coupon_money;
				this.allbuydata = allbuydata;
				this.couponvisible = false;
				this.calculatePrice();
			},
			choosestore: function(e) {
				var bid = e.currentTarget.dataset.bid;
				var storekey = e.currentTarget.dataset.index;
				var allbuydata = this.allbuydata;
				var buydata = allbuydata[bid];
				var freightkey = buydata.freightkey
				allbuydata[bid].freightList[freightkey].storekey = storekey
				this.allbuydata = allbuydata;
			},
			//提交并支付
			topay: function(e) {
				var that = this;
				
				app.showLoading('提交中');
				app.post('ApiMiaosha/createsxf', {
					miaoshaid:this.opt.id,
				}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}else if(res.payorderid){
						setTimeout(function () {
							app.goto('/pages/pay/pay?id=' + res.payorderid);
						}, 1500);
					}
				});
			},
			showCouponList: function(e) {
				this.couponvisible = true;
				this.bid = e.currentTarget.dataset.bid;
			},
			showInvoice: function(e) {
				this.invoiceShow = true;
				this.bid = e.currentTarget.dataset.bid;
				let index = e.currentTarget.dataset.index;
				this.invoice_type = this.allbuydata[index].business.invoice_type;
				this.invoice = this.allbuydata[index].tempInvoice;
			},
			changeOrderType: function(e) {
				var that = this;
				var value = e.detail.value;
				if(value == 2) {
					that.name_type_select = 2;
					that.name_type_personal_disabled = true;
				} else {
					that.name_type_personal_disabled = false;
				}
				that.invoice_type_select = value;
			},
			changeNameType: function(e) {
				var that = this;
				var value = e.detail.value;
				that.name_type_select = value;
			},
			invoiceFormSubmit: function (e) {
			  var that = this;
				var formdata = e.detail.value;
				if(formdata.invoice_name == '') {
					app.error('请填写抬头名称');
					return;
				}
				if((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {
					///^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/
					app.error('请填写公司税号');
					return;
				}
				if(formdata.invoice_type == 2) {
					if(formdata.address == '') {
						app.error('请填写注册地址');
						return;
					}
					if(formdata.tel == '') {
						app.error('请填写注册电话');
						return;
					}
					if(formdata.bank_name == '') {
						app.error('请填写开户银行');
						return;
					}
					if(formdata.bank_account == '') {
						app.error('请填写银行账号');
						return;
					}
				}
				if (formdata.mobile != '') {
					if(!/^1[3456789]\d{9}$/.test(formdata.mobile)){
						app.error("手机号码有误，请重填");
						return;
					}
				}
				if (formdata.email != '') {
					if(!/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/.test(formdata.email)){
						app.error("邮箱有误，请重填");
						return;
					}
				}
				if(formdata.mobile == '' && formdata.email == '') {
					app.error("手机号和邮箱请填写其中一个");
					return;
				}
				// console.log(formdata);
				var allbuydata = that.allbuydata;
				for (var i in allbuydata) {
					if(allbuydata[i].bid == that.bid)
						allbuydata[i].tempInvoice = formdata;
				}
				that.allbuydata = allbuydata;
				that.invoiceShow = false;
					// that.loading = true;
					// uni.setStorageSync('temp_invoice_' + that.opt.bid, formdata);
				that.calculatePrice();
			},
			handleClickMask: function() {
				this.couponvisible = false;
				this.cuxiaovisible = false;
				this.type11visible = false;
				this.membervisible = false;
				this.invoiceShow = false;
			},
			showCuxiaoList: function(e) {
				this.cuxiaovisible = true;
				this.bid = e.currentTarget.dataset.bid;
			},
			changecx: function(e) {
				var that = this;
				var cxid = e.currentTarget.dataset.id;
				var cxindex = e.currentTarget.dataset.index;
				console.log(cxid);
				that.cxid = cxid;
				if (cxid == 0) {
					that.cuxiaoinfo = false;
					return;
				}
				var cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];
				console.log(cuxiaoinfo.cuxiaomoney)
				app.post("ApiShop/getcuxiaoinfo", {
					id: cxid
				}, function(res) {
					if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {
						res.cuxiaomoney = cuxiaoinfo.cuxiaomoney
					}
					that.cuxiaoinfo = res;
				});
			},
			changecxMulti: function(e) {
				var that = this;
				var cxid = e.currentTarget.dataset.id;
				var cxindex = e.currentTarget.dataset.index;
				that.cuxiaoList.length = 0;
				console.log('cxid:'+cxid);
				if (cxid == 0) {
					that.cuxiaoinfo = false;
					that.cxids.length = 0;
					that.cxid = 0;
					return;
				}
				var index = that.cxids.indexOf(cxid);
				if(index === -1){
					that.cxids.push(cxid);
				} else {
					that.cxids.splice(index);
				}
				if(that.cxids.length == 0) {
					that.cxid = 0;
					that.cuxiaoinfo = false;
					return;
				}
				that.cxid = '';
				var cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];
				console.log(cuxiaoinfo.cuxiaomoney)
				app.showLoading();
				app.post("ApiShop/getcuxiaoinfo", {
					id: that.cxids
				}, function(res) {
					// if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {
					// 	res.cuxiaomoney = cuxiaoinfo.cuxiaomoney
					// }
					app.showLoading(false);
					that.cuxiaoList = res;
				});
			},
			chooseCuxiao: function() {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = that.bid;
				var cxid = that.cxid;
				var cxids = that.cxids;
				console.log(cxid == 0)
				if (cxid == 0 || cxid == '') {
					allbuydata[bid].cuxiaoid = '';
					allbuydata[bid].cuxiao_money = 0;
					allbuydata[bid].cuxiaoname = '不使用促销';
					allbuydata[bid].cuxiaonameArr = [];
				} else {
					allbuydata[bid].cuxiaoid = [];
					allbuydata[bid].cuxiao_money = 0;
					allbuydata[bid].cuxiaotype = [];
					allbuydata[bid].cuxiaonameArr = [];
					console.log(that.cuxiaoList.info)
					if(that.cuxiaoList.info && that.cuxiaoList.info.length > 0) {
						for (var i in that.cuxiaoList.info) {
							var cxtype = that.cuxiaoList.info[i].type;
							console.log(cxtype);
							if (cxtype == 1 || cxtype == 6) {
								//满额立减 满件立减
								allbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'] * -1;
							} else if (cxtype == 2) {
								//满额赠送
								allbuydata[bid].cuxiao_money += 0;
							} else if (cxtype == 3) {
								//加价换购  27.8+15.964+41.4
								allbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'];
							} else if (cxtype == 4 || cxtype == 5) {
								//满额打折 满件打折
								var cuxiaoMoney = 0;
								for ( var y in that.allbuydata[bid].cuxiaolist) {
									if(that.cuxiaoList.info[i].id == that.allbuydata[bid].cuxiaolist[y].id) {
										cuxiaoMoney = that.allbuydata[bid].cuxiaolist[y].cuxiaomoney;
									}
								}
								console.log('cuxiaoMoney');
								console.log(cuxiaoMoney);
								allbuydata[bid].cuxiao_money += cuxiaoMoney * -1
							}
							allbuydata[bid].cuxiaoid.push(that.cuxiaoList.info[i].id);
							allbuydata[bid].cuxiaotype.push(cxtype);
							allbuydata[bid].cuxiaonameArr.push(that.cuxiaoList.info[i]['name']);
						}
						console.log('allbuydata[bid]');
						console.log(allbuydata[bid]);
					} else {
						var cxtype = that.cuxiaoinfo.info.type;
						console.log(cxtype);
						if (cxtype == 1 || cxtype == 6) {
							//满额立减 满件立减
							allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'] * -1;
						} else if (cxtype == 2) {
							//满额赠送
							allbuydata[bid].cuxiao_money = 0;
						} else if (cxtype == 3) {
							//加价换购  27.8+15.964+41.4
							allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'];
						} else if (cxtype == 4 || cxtype == 5) {
							//var product_price = parseFloat(allbuydata[bid].product_price);
							//var leveldk_money = parseFloat(allbuydata[bid].leveldk_money); //会员折扣
							//var manjian_money = parseFloat(allbuydata[bid].manjian_money); //满减活动
							//满额打折 满件打折
							//allbuydata[bid].cuxiao_money = (1 - that.cuxiaoinfo.info['zhekou'] * 0.1) * (product_price - leveldk_money - manjian_money) * -1;
							allbuydata[bid].cuxiao_money = that.cuxiaoinfo.cuxiaomoney * -1
						}
						allbuydata[bid].cuxiaoid = cxid;
						allbuydata[bid].cuxiaotype = cxtype;
						allbuydata[bid].cuxiaoname = that.cuxiaoinfo.info['name'];
					}
				}
				this.allbuydata = allbuydata;
				this.cuxiaovisible = false;
				this.calculatePrice();
			},
			showType11List: function(e) {
				this.type11visible = true;
				this.bid = e.currentTarget.dataset.bid;
			},
			changetype11: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = that.bid;
				that.type11key = e.currentTarget.dataset.index;
				// console.log(that.type11key)
			},
			chooseType11: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = that.bid;
				var type11key = that.type11key;
				if (type11key == -1) {
					app.error('请选择物流');
					return;
				}
				allbuydata[bid].type11key = type11key + 1;
				// console.log(allbuydata[bid].type11key)
				var freightkey = allbuydata[bid].freightkey;
				var freightList = allbuydata[bid].freightList;
				var freight_price = parseFloat(freightList[freightkey].type11pricedata[type11key].price);
				var product_price = parseFloat(allbuydata[bid].product_price);
				// console.log(freightList[freightkey].freeset);
				// console.log(parseFloat(freightList[freightkey].free_price));
				// console.log(product_price);
				if (freightList[freightkey].freeset == 1 && parseFloat(freightList[freightkey].free_price) <=
					product_price) {
					freight_price = 0;
				}
				allbuydata[bid].freightList[freightkey].freight_price = freight_price;

				this.allbuydata = allbuydata;
				this.type11visible = false;
				this.calculatePrice();
			},
			openMendian: function(e) {
				var allbuydata = this.allbuydata
				var bid = e.currentTarget.dataset.bid;
				var freightkey = e.currentTarget.dataset.freightkey;
				var storekey = e.currentTarget.dataset.storekey;
				var frightinfo = allbuydata[bid].freightList[freightkey]
				var storeinfo = frightinfo.storedata[storekey];
				// console.log(storeinfo)
				app.goto('mendian?id=' + storeinfo.id);
			},
			openLocation: function(e) {
				var allbuydata = this.allbuydata
				var bid = e.currentTarget.dataset.bid;
				var freightkey = e.currentTarget.dataset.freightkey;
				var storekey = e.currentTarget.dataset.storekey;
				var frightinfo = allbuydata[bid].freightList[freightkey]
				var storeinfo = frightinfo.storedata[storekey];
				// console.log(storeinfo)
				var latitude = parseFloat(storeinfo.latitude);
				var longitude = parseFloat(storeinfo.longitude);
				var address = storeinfo.name;
				uni.openLocation({
					latitude: latitude,
					longitude: longitude,
					name: address,
					scale: 13
				})
			},
			editorChooseImage: function (e) {
				var that = this;
				var bid = e.currentTarget.dataset.bid;
				var idx = e.currentTarget.dataset.idx;
				var editorFormdata = that.allbuydata[bid].editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				app.chooseImage(function(data){
					editorFormdata[idx] = data[0];
					// console.log(editorFormdata)
					that.allbuydata[bid].editorFormdata = editorFormdata
					that.test = Math.random();
				})
			},
			editorBindPickerChange:function(e){
				var that = this;
				var bid = e.currentTarget.dataset.bid;
				var idx = e.currentTarget.dataset.idx;
				var val = e.detail.value;
				var editorFormdata = that.allbuydata[bid].editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = val;
				// console.log(editorFormdata)
				that.allbuydata[bid].editorFormdata = editorFormdata;
				that.test = Math.random();
			},

			showMemberList: function(e) {
				this.membervisible = true;
			},
			regionchange2: function(e) {
				const value = e.detail.value
				// console.log(value[0].text + ',' + value[1].text + ',' + value[2].text);
				this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text
			},
			memberSearch: function() {
				var that = this;
				// console.log(that.regiondata)
				app.post('ApiShop/memberSearch', {
					diqu: that.regiondata
				}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					var data = res.memberList;
					that.memberList = data;
				});
			},
			checkMember: function(e) {
				var that = this;
				that.checkMem = e.currentTarget.dataset.info;
				this.membervisible = false;
			},
			showmemberinfo:function(e){
				var that = this;
				var mid = e.currentTarget.dataset.mid;
				app.showLoading('提交中');
				app.post('ApiShop/getmemberuplvinfo',{mid:mid}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					that.selectmemberinfo = res.info;
					that.memberinfovisible = true;
				});
			},
			memberinfoClickMask:function(){
				this.memberinfovisible = false;
			},
			doStoreShowAll:function(){
				this.storeshowall = true;
			},
			showglass:function(e){
				var that = this
				var grid = e.currentTarget.dataset.grid;
				var index = e.currentTarget.dataset.index;
				var index2 = e.currentTarget.dataset.index2;
				console.log(grid)
				console.log(index)
				console.log(index2)
				// console.log(that.glassrecordlist)
				if(that.glassrecordlist.length<1){
					//没有数据 就重新请求
					that.loading;
					app.post('ApiGlass/myrecord', {pagenum:1,listrow:100}, function (res) {
						that.loading = false;
					  var datalist = res.data;
						console.log(datalist)
						that.glassrecordlist = datalist;
						// console.log(that.glassrecordlist);
						// if(datalist.length>0){
						// 	// 
						// 	that.isshowglass = true
						// }else{
						// 	app.error('无可用的视力档案')
						// }
						that.isshowglass = true
					});
					
				}else{
					that.isshowglass = true
				}
				that.curindex = index
				that.curindex2 = index2
				that.grid = grid
			},
			hideglass:function(e){
				var that = this
				that.isshowglass = false;
			},
			chooseglass:function(e){
				var that = this;
				var gindex = e.detail.value;
				var allbuydata = that.allbuydata;
				var grid = that.grid;
				var index = that.curindex;
				var index2 = that.curindex2;
				console.log(gindex+'-'+that.curindex+'-'+that.curindex2)
				var glassrecordlist = that.glassrecordlist;
				var product = allbuydata[index]['prodata'][index2].product
				var sid = glassrecordlist[gindex].id
				if(grid==sid){
					product.glassrecord = {};
					that.grid = 0;
				}else{
					product.glassrecord = glassrecordlist[gindex]
					that.grid = glassrecordlist[gindex].id
				}
				
				that.allbuydata[index]['prodata'][index2]['product'] = product;
				that.isshowglass = false;
			}
		}
	}
</script>

<style>
	.redBg{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx; width: auto; display: inline-block; margin-top: 4rpx;}
.address-add {width: 94%;margin: 20rpx 3%;background: #fff;border-radius: 20rpx;padding: 20rpx 3%;min-height: 140rpx;}
.address-add .f1 {margin-right: 20rpx}
.address-add .f1 .img {width: 66rpx;height: 66rpx;}
.address-add .f2 {color: #666;}
.address-add .f3 {width: 26rpx;height: 26rpx;}
.linkitem {width: 100%;padding: 1px 0;background: #fff;display: flex;align-items: center}.cf3 {width: 200rpx;height: 26rpx;display: block;
    text-align: right;}
.linkitem .f1 {width: 160rpx;color: #111111}
.linkitem .input {height: 50rpx;padding-left: 10rpx;color: #222222;font-weight: bold;font-size: 28rpx;flex: 1}
.buydata {width: 94%;margin: 0 3%;background: #fff;margin-bottom: 20rpx;border-radius: 20rpx;}
.btitle {width: 100%;padding: 20rpx 20rpx;display: flex;align-items: center;color: #111111;font-weight: bold;font-size: 30rpx}
.btitle .img {width: 34rpx;height: 34rpx;margin-right: 10rpx}
.bcontent {width: 100%;padding: 0 20rpx}
.product {width: 100%;border-bottom: 1px solid #f4f4f4}
.product .item {width: 100%;padding: 20rpx 0;background: #fff;border-bottom: 1px #ededed dashed;}
.product .item:last-child {border: none}
.product .info {padding-left: 20rpx;}
.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
.product .info .f2 {color: #999999;font-size: 24rpx}
.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}
.product image {width: 140rpx;height: 140rpx}
.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}
.freight .f1 {color: #333;margin-bottom: 10rpx}
.freight .f2 {color: #111111;text-align: right;flex: 1}
.freight .f3 {width: 24rpx;height: 28rpx;}
.freighttips {color: red;font-size: 24rpx;}
.freight-ul {width: 100%;display: flex;}
.freight-li {flex-shrink: 0;display: flex;background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;text-align: center;height: 48rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0}
.inputPrice {border: 1px solid #ddd; width: 200rpx; height: 40rpx; border-radius: 10rpx; padding: 0 4rpx;}

.price {width: 100%;padding: 20rpx 0;background: #fff;display: flex;align-items: center}
.price .f1 {color: #333}
.price .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}
.price .f3 {width: 24rpx;height: 24rpx;}
.price .couponname{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;display:inline-block;margin:2rpx 0 2rpx 10rpx}
.scoredk {width: 94%;margin: 0 3%;margin-bottom: 20rpx;border-radius: 20rpx;padding: 24rpx 20rpx;background: #fff;display: flex;align-items: center}
.scoredk .f1 {color: #333333}
.scoredk .f2 {color: #999999;text-align: right;flex: 1}
.remark {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center}
.remark .f1 {color: #333;width: 200rpx}
.remark input {border: 0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right}
.footer {width: 96%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 2%;display: flex;align-items: center;z-index: 8;box-sizing:content-box}
.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}
.footer .text1 text {color: #e94745;font-size: 32rpx;}
.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}
.footer .op[disabled] { background: #aaa !important; color: #666;}
.storeitem {width: 100%;padding: 20rpx 0;display: flex;flex-direction: column;color: #333}
.storeitem .panel {width: 100%;height: 60rpx;line-height: 60rpx;font-size: 28rpx;color: #333;margin-bottom: 10rpx;display: flex}
.storeitem .panel .f1 {color: #333}
.storeitem .panel .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}
.storeitem .radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;border-bottom: 0 solid #eee;padding: 8rpx 20rpx;}
.storeitem .radio-item:last-child {border: 0}
.storeitem .radio-item .f1 {color: #666;flex: 1}
.storeitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left: 30rpx}
.storeitem .radio .radio-img {width: 100%;height: 100%}
.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}
.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.pstime-item .radio .radio-img {width: 100%;height: 100%}
.cuxiao-desc {width: 100%}
.cuxiao-item {display: flex;padding: 0 40rpx 20rpx 40rpx;}
.cuxiao-item .type-name {font-size: 28rpx;color: #49aa34;margin-bottom: 10rpx;flex: 1}
.cuxiao-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.cuxiao-item .radio .radio-img {width: 100%;height: 100%}

.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}
.form-item .label {color: #333;width: 200rpx;flex-shrink:0}
.form-item .radio{transform:scale(.7);}
.form-item .checkbox{transform:scale(.7);}
.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}
.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}
.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}
.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .radio2{display:flex;align-items:center;}
.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}
.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}

.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.form-imgbox-close .image{width:100%;height:100%}
.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.form-imgbox-img>.image{max-width:100%;}
.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.form-uploadbtn{position:relative;height:180rpx;width:180rpx}

.member_search{width:100%;padding:0 40rpx;display:flex;align-items:center}
.searchMemberButton{height:60rpx;background-color: #007AFF;border-radius: 10rpx;width: 160rpx;line-height: 60rpx;color: #fff;text-align: center;font-size: 28rpx;display: block;}
.memberlist{width:100%;padding:0 40rpx;height: auto;margin:20rpx auto;}
.memberitem{display:flex;align-items:center;border-bottom:1px solid #f5f5f5;padding:20rpx 0}
.memberitem image{display: block;height:100rpx;width:100rpx;margin-right:20rpx;}
.memberitem .t1{color:#333;font-weight:bold}
.memberitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.memberitem .radio .radio-img {width: 100%;height: 100%}

.checkMem{ display: inline-block; }
.checkMem p{ height: 30px; width: 100%; display: inline-block; }
.placeholder{  font-size: 26rpx;line-height: 80rpx;}
.selected-item span{ font-size: 26rpx !important;}
.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;flex-shrink:0}
.orderinfo .item .t2{flex:1;text-align:right}
.orderinfo .item .red{color:red}

.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}

.btn{ height:80rpx;line-height: 80rpx;width:90%;margin:0 auto;border-radius:40rpx;margin-top:40rpx;color: #fff;font-size: 28rpx;font-weight:bold}
.invoiceBox .radio radio{transform: scale(0.8);}
.invoiceBox .radio:nth-child(2) { margin-left: 30rpx;}
.glassinfo{color: #333; padding:10rpx; border-radius: 10rpx;display: flex;justify-content: space-between;align-items: center;background: #f4f4f4;margin-top: 10rpx;}
.glassinfo .f2{display: flex;justify-content: flex-end;}
.glassinfo .f2 image{width: 32rpx;height: 36rpx;padding-top: 4rpx;}
.glassinfo .f1{font-weight: bold;}

.glass_popup{
	
}
.glass_popup .popup__title{padding: 30rpx 0 0 0;}
.glassitem{background:#eeeeee;border-radius: 10rpx;width: 94%;margin: 20rpx 3%;display: flex;align-items: center;padding: 20rpx 0;}
.glassitem.on{background: #ffe6c8;}
.glassitem .radio{width: 80rpx;flex-shrink: 0;text-align: center;}
.glassitem .gcontent{flex:1;padding: 0 20rpx;}
.glassrow{display: flex;padding: 10rpx 0;align-items: center;}
.glassrow .glasscol{min-width: 25%;text-align: center;}
.glassitem .bt{border-top:1px solid #e3e3e3}
</style>
