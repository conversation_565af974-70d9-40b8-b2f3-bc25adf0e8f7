<template>
<view>
	<view v-if="isload">
		<view class="buydialog-mask" @tap="buydialogChange"></view>
		<view class="buydialog" :class="menuindex>-1?'tabbarbot':'notabbarbot'">
			<view class="close" @tap="buydialogChange">
				<image src="/static/img/close.png" class="image"/>
			</view>
			<view class="title">
				<image :src="product.pic" class="img" @tap="previewImage" :data-url="product.pic"/>
				<view class="price" :style="{color:t('color1')}">￥{{product.sell_price}} <text v-if="product.market_price*1 > product.sell_price*1" class="t2">￥{{product.market_price}}</text></view>
				<view class="stock">库存：{{product.stock}}</view>
			</view>
			<view class="buynum flex flex-y-center">
				<view class="flex1">购买数量：</view>
				<view class="addnum">
					<view class="minus" @tap="gwcminus"><image class="img" src="/static/img/cart-minus.png"/></view>
					<input class="input" type="number" :value="gwcnum" @input="gwcinput"></input>
					<view class="plus" @tap="gwcplus"><image class="img" src="/static/img/cart-plus.png"/></view>
				</view>
			</view>
			<view class="op">
				<block  v-if="product.stock <= 0">
					<button class="nostock">库存不足</button>
				</block>
				<block  v-else>
					<button class="tobuy" :style="{backgroundColor:t('color1')}" @tap="tobuy" v-if="btntype==0">立即购买</button>
					<button class="tobuy" :style="{backgroundColor:t('color1')}" @tap="tobuy" v-if="btntype==2">确 定</button>
				</block>
			</view>
		</view>
	</view>
	<loading v-if="loading"></loading>
</view>
</template>
<script>
	var app = getApp();
	export default {
		data() {
			return {
				ks:'',
				product:{},
				gwcnum:1,
				isload:false,
				loading:false,
				canaddcart:true,
			}
		},
		props: {
			btntype:{default:0},
			menuindex:{default:-1},
			controller:{default:'ApiTuangou'},
			needaddcart:{default:true},
			proid:{}
		},
		mounted:function(){
			this.getdata();
		},
		methods:{
			getdata:function(){
				var that = this;
				that.loading = true;
				app.post(this.controller+'/getproductdetail',{id:that.proid},function(res){
					that.loading = false;
					that.product = res.product;
					if(!that.product.limit_start){
						that.product.limit_start = 1;
					}
					that.gwcnum = that.product.limit_start;
					that.isload = true;
					if(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车
						that.canaddcart = false;
					}
					if(that.product.istg == 1){
						that.canaddcart = false;
					}
					if(that.controller == 'ApiTuangou'){
						that.canaddcart = false;
					}
				});
			},
			buydialogChange:function(){
				this.$emit('buydialogChange');
			},
			tobuy: function (e) {
				var that = this;
				var ks = that.ks;
				var proid = that.product.id;
				var num = that.gwcnum;
				if (num < 1) num = 1;
				if (this.product.stock < num) {
					app.error('库存不足');
					return;
				}
				var prodata = proid + ',' + num;
				this.$emit('buydialogChange');
				if(this.controller == 'ApiTuangou'){
					app.goto('/activity/tuangou/buy?prodata=' + prodata);
				}
			},
			//加
			gwcplus: function (e) {
				var gwcnum = this.gwcnum + 1;
				var ks = this.ks;
				if (gwcnum > this.product.stock) {
					app.error('库存不足');
					return 1;
				}
				this.gwcnum = this.gwcnum + 1;
			},
			//减
			gwcminus: function (e) {
				var gwcnum = this.gwcnum - 1;
				var ks = this.ks;
				if (gwcnum <= this.product.limit_start - 1) {
					if(this.product.limit_start > 1){
						app.error('该商品' + this.product.limit_start + '件起售');
					}
					return;
				}
				this.gwcnum = this.gwcnum - 1;
			},
			//输入
			gwcinput: function (e) {
				var ks = this.ks;
				var gwcnum = parseInt(e.detail.value);
				if (gwcnum < 1) return 1;
				if (gwcnum > this.product.stock) {
					return this.product.stock > 0 ? this.product.stock : 1;
				}
				this.gwcnum = gwcnum;
			},
		}
	}
</script>
<style>

.buydialog-mask{ position: fixed; top: 0px; left: 0px; width: 100%; background: rgba(0,0,0,0.5); bottom: 0px;z-index:10}
.buydialog{ position: fixed; width: 100%; left: 0px; bottom: 0px; background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px}
.buydialog .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:12}
.buydialog .close .image{ width: 30rpx; height:30rpx; }
.buydialog .title{ width: 94%;position: relative; margin: 0 3%; padding:20rpx 0px; border-bottom:0; height: 190rpx;}
.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}
.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}
.buydialog .title .price .t1{ font-size:26rpx}
.buydialog .title .price .t2{ font-size:26rpx;text-decoration:line-through;color:#aaa}
.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}
.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}

.buydialog .guigelist{ width: 94%; position: relative; margin: 0 3%; padding:0px 0px 10px 0px; border-bottom: 0; }
.buydialog .guigelist .name{ height:70rpx; line-height: 70rpx;}
.buydialog .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}
.buydialog .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:4rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4}
.buydialog .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}
.buydialog .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }
.buydialog .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}
.buydialog .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}
.buydialog .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}
.buydialog .addnum .img{width:24rpx;height:24rpx}
.buydialog .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:28rpx;margin: 0 15rpx;}

.buydialog .op{width:90%;margin:20rpx 5%;border-radius:36rpx;overflow:hidden;display:flex;margin-top:100rpx;}
.buydialog .addcart{flex:1;height:72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}
.buydialog .tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}
.buydialog .nostock{flex:1;height: 72rpx; line-height: 72rpx; background:#aaa; color: #fff; border-radius: 0px; border: none;}
</style>