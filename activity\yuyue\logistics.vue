<template>
<view class="container">
	<block v-if="isload">
		<map v-if="worker_order.status!=0 && worker_order.status!=4" class="map" :longitude="binfo.longitude" :latitude="binfo.latitude" scale="14" :markers="[{
			id:0,
			latitude:binfo.latitude,
			longitude:binfo.longitude,
			iconPath: '/static/peisong/marker_business.png',
			width:'44',
			height:'54'
		},{
			id:0,
			latitude:orderinfo.latitude,
			longitude:orderinfo.longitude,
			iconPath: '/static/peisong/marker_kehu.png',
			width:'44',
			height:'54'
		},{
			id:0,
			latitude:worker.latitude,
			longitude:worker.longitude,
			iconPath: '/static/peisong/marker_qishou.png',
			width:'44',
			height:'54'
		}]"></map>
		<map v-else class="map" :longitude="binfo.longitude" :latitude="binfo.latitude" scale="14" :markers="[{
			id:0,
			latitude:binfo.latitude,
			longitude:binfo.longitude,
			iconPath: '/static/peisong/marker_business.png',
			width:'44',
			height:'54'
		},{
			id:0,
			latitude:orderinfo.latitude,
			longitude:orderinfo.longitude,
			iconPath: '/static/peisong/marker_kehu.png',
			width:'44',
			height:'54'
		}]"></map>

		<view class="order-box">
			<view class="head">
				<view class="f1" v-if="worker_order.status==3"><image src="/static/peisong/ps_time.png" class="img"/>已完成</view>
				<view class="f1" v-if="worker_order.status==1"><image src="/static/peisong/ps_time.png" class="img"/>已接单</view>
				<view class="f1" v-if="worker_order.status==2"><image src="/static/peisong/ps_time.png" class="img"/>服务中</view>
				<view class="flex1"></view>
			</view>
			<view class="content" style="border-bottom:0">
				<view class="f1">
					<view class="t1"><text class="x1">{{worker_order.juli}}</text><text class="x2">{{worker_order.juli_unit}}</text></view>
					<view class="t2"><image src="/static/peisong/ps_juli.png" class="img"/></view>
					<view class="t3"><text class="x1">{{worker_order.juli2}}</text><text class="x2">{{worker_order.juli2_unit}}</text></view>
				</view>
				<view class="f2">
					<view class="t1">{{binfo.name}}</view>
					<view class="t2">{{binfo.address}}</view>
					<view class="t3">{{orderinfo.address}}</view>
				</view>
				<view class="f3" @tap.stop="daohang"><image :src="pre_url+'/static/img/peisong/ps_daohang.png'" class="img"/></view>
			</view>
		</view>

		<!-- 服务进度时间轴 -->
		<view class="service-timeline" v-if="worker_order.status!=0 && service_progress && service_progress.length>0">
			<view class="timeline-title">服务进度</view>
			<view class="timeline-container">
				<view v-for="(item, index) in service_progress" :key="index" class="timeline-item" :class="item.status === 'completed' ? 'completed' : 'waiting'">
					<view class="timeline-icon">
						<image :src="'/static/peisong/' + item.icon + '.png'" class="icon-img"/>
						<view class="timeline-line" v-if="index < service_progress.length - 1"></view>
					</view>
					<view class="timeline-content">
						<view class="timeline-header">
							<text class="timeline-title-text">{{item.title}}</text>
							<text class="timeline-time" v-if="item.time">{{item.time}}</text>
						</view>
						<view class="timeline-desc">{{item.desc}}</view>
						
						<!-- 到达照片 -->
						<view class="timeline-photo" v-if="item.arrival_photo && need_arrival_photo == 1">
							<image :src="item.arrival_photo" mode="aspectFill" @tap="previewImage" :data-src="item.arrival_photo" class="photo-img"/>
							<text class="photo-desc">到达现场照片</text>
						</view>
						
						<!-- 完成照片 -->
						<view class="timeline-photo" v-if="item.complete_photo && need_complete_photo == 1">
							<image :src="item.complete_photo" mode="aspectFill" @tap="previewImage" :data-src="item.complete_photo" class="photo-img"/>
							<text class="photo-desc">服务完成照片</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 技师位置信息 -->
		<view class="technician-location" v-if="need_location == 1 && arrival_info && arrival_info.arrival_distance">
			<view class="location-title">
				<text class="icon-location">📍</text>
				<text>技师到达位置</text>
			</view>
			
			<view class="location-content">
				<view class="distance-info">
					<text class="label">距离您:</text>
					<text class="value">{{arrival_info.arrival_distance_text}}</text>
				</view>
				
				<view class="arrival-time">
					<text class="label">到达时间:</text>
					<text class="value">{{arrival_info.time}}</text>
				</view>
			</view>
			
			<view class="location-photo" v-if="arrival_info.arrival_photo && need_arrival_photo == 1">
				<image :src="arrival_info.arrival_photo" mode="aspectFill" @tap="previewImage" :data-src="arrival_info.arrival_photo" class="photo-img"/>
				<text class="photo-desc">到达现场照片</text>
			</view>
		</view>

		<view class="orderinfo">
			<view class="box-title">购买清单({{orderinfo.procount}})</view>
			<view v-for="(item, idx) in prolist" :key="idx" class="item">
				<text class="t1 flex1">{{item.name}} {{item.ggname}}</text>
				<text class="t2 flex0">￥{{item.sell_price}} ×{{item.num}} </text>
			</view>
		</view>
		
		<view class="orderinfo" v-if="worker_order.status!=0">
			<view class="box-title">服务信息</view>
			<view class="item" v-if="worker.realname">
				<text class="t1">服务人员</text>
				<text class="t2"><text style="font-weight:bold">{{worker.realname}}</text>({{worker.tel}})</text>
			</view>
			<view class="item">
				<text class="t1">接单时间</text>
				<text class="t2">{{dateFormat(worker_order.starttime)}}</text>
			</view>
			<view class="item" v-if="worker_order.daodiantime">
				<text class="t1">{{yuyue_sign?'出发时间':'到店时间'}}</text>
				<text class="t2">{{dateFormat(worker_order.daodiantime)}}</text>
			</view>
			<view class="item" v-if="worker_order.sign_time">
				<text class="t1">开始时间</text>
				<text class="t2">{{dateFormat(worker_order.sign_time)}}</text>
			</view>
			<view class="item" v-if="worker_order.endtime">
				<text class="t1">完成时间</text>
				<text class="t2">{{dateFormat(worker_order.endtime)}}</text>
			</view>
			<view class="item" v-if="worker_order.arrival_distance && need_location == 1">
				<text class="t1">到达距离</text>
				<text class="t2">{{worker_order.arrival_distance}} 米</text>
			</view>
		</view>

		<view class="orderinfo">
			<view class="box-title">订单信息</view>
			<view class="item">
				<text class="t1">订单编号</text>
				<text class="t2" user-select="true" selectable="true">{{orderinfo.ordernum}}</text>
			</view>
			<view class="item">
				<text class="t1">下单时间</text>
				<text class="t2">{{dateFormat(orderinfo.createtime)}}</text>
			</view>
			<view class="item">
				<text class="t1">支付时间</text>
				<text class="t2">{{dateFormat(orderinfo.paytime)}}</text>
			</view>
			<view class="item">
				<text class="t1">支付方式</text>
				<text class="t2">{{orderinfo.paytype}}</text>
			</view>
			<view class="item">
				<text class="t1">商品金额</text>
				<text class="t2 red">¥{{orderinfo.product_price}}</text>
			</view>
			<view class="item">
				<text class="t1">实付款</text>
				<text class="t2 red">¥{{orderinfo.totalprice}}</text>
			</view>
			<view class="item">
				<text class="t1">备注</text>
				<text class="t2 red">{{orderinfo.message ? orderinfo.message : '无'}}</text>
			</view>
		</view>
		<view style="width:100%;height:120rpx"></view>
		<view class="bottom">
			<view class="f1" v-if="worker_order.status!=0 && worker.tel" @tap="call" :data-tel="worker.tel"><image src="/static/peisong/tel1.png" class="img"/>联系服务人员</view>
			<view class="f2" v-if="worker_order.status!=0" @tap="call" :data-tel="binfo.tel"><image src="/static/peisong/tel2.png" class="img"/>联系商家</view>
			<view class="btn1" @tap="goto" :data-url="'commentps?id='+worker_order.id" v-if="mid==orderinfo.mid && worker_order.worker_id>0 && worker_order.status==3">评价服务人员</view>
		</view>
	
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			pre_url:app.globalData.pre_url,
			nodata:false,
      express_com: '',
      express_no: '',
      datalist: [],

      orderinfo: {},
      prolist: [],
      binfo: {},
      worker: {},
      worker_order: {},
			mid:'',
			
			// 新增字段
			service_progress: [],
			arrival_info: {},
			completion_info: {},
			need_location: 1,
			need_arrival_photo: 1,
			need_complete_photo: 1,
			yuyue_sign: false
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},

  methods: {
		getdata: function () {
			var that = this;
			that.express_no = that.opt.express_no;
			that.loading = true;
			app.get('ApiYuyue/logistics', { express_no: that.express_no}, function (res) {
				that.loading = false;
				that.orderinfo = res.orderinfo;
				that.prolist = res.prolist;
				that.binfo = res.binfo;
				that.worker = res.worker;
				that.worker_order = res.worker_order;
				that.set = res.set;
				that.mid = res.mid;
				
				// 新增服务进度数据处理
				that.service_progress = res.service_progress || [];
				that.arrival_info = res.arrival_info || {};
				that.completion_info = res.completion_info || {};
				that.need_location = res.need_location || 0;
				that.need_arrival_photo = res.need_arrival_photo || 0;
				that.need_complete_photo = res.need_complete_photo || 0;
				that.yuyue_sign = res.yuyue_sign || false;
				
				that.loaded();
				console.log('物流数据加载完成', res);
			});
		},
		
		call: function(e) {
			var tel = e.currentTarget.dataset.tel;
			uni.makePhoneCall({
				phoneNumber: tel
			});
		},
		
		loaded: function() {
			this.isload = true;
		},
		
		// 地图导航功能
		daohang: function() {
			var that = this;
			if (!that.binfo.latitude || !that.binfo.longitude) {
				uni.showToast({
					title: '暂无位置信息',
					icon: 'none'
				});
				return;
			}
			
			uni.openLocation({
				latitude: parseFloat(that.binfo.latitude),
				longitude: parseFloat(that.binfo.longitude),
				name: that.binfo.name,
				address: that.binfo.address
			});
		},
		
		// 图片预览功能
		previewImage: function(e) {
			var src = e.currentTarget.dataset.src;
			if (!src) return;
			
			var imgList = [];
			
			// 收集所有可预览的照片
			if (this.arrival_info && this.arrival_info.arrival_photo) {
				imgList.push(this.arrival_info.arrival_photo);
			}
			
			if (this.completion_info && this.completion_info.complete_photo) {
				imgList.push(this.completion_info.complete_photo);
			}
			
			// 从服务进度中收集照片
			if (this.service_progress && this.service_progress.length > 0) {
				this.service_progress.forEach(function(item) {
					if (item.arrival_photo) {
						imgList.push(item.arrival_photo);
					}
					if (item.complete_photo) {
						imgList.push(item.complete_photo);
					}
				});
			}
			
			// 如果没有照片，使用当前照片
			if (imgList.length === 0) {
				imgList.push(src);
			}
			
			uni.previewImage({
				current: src,
				urls: imgList
			});
		},
		
		// 日期格式化
		dateFormat: function(timestamp) {
			if (!timestamp) return '';
			var date = new Date(timestamp * 1000);
			var year = date.getFullYear();
			var month = date.getMonth() + 1;
			var day = date.getDate();
			var hour = date.getHours();
			var minute = date.getMinutes();
			
			month = month < 10 ? '0' + month : month;
			day = day < 10 ? '0' + day : day;
			hour = hour < 10 ? '0' + hour : hour;
			minute = minute < 10 ? '0' + minute : minute;
			
			return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
		},
		
		// 页面跳转
		goto: function(e) {
			var url = e.currentTarget.dataset.url;
			uni.navigateTo({
				url: url
			});
		}
  }
}
</script>
<style>
.expressinfo .head { width:100%;background: #fff; margin:20rpx 0;padding: 20rpx 20rpx;display:flex;align-items:center}
.expressinfo .head .f1{ width:120rpx;height:120rpx;margin-right:20rpx}
.expressinfo .head .f1 image{width:100%;height:100%}
.expressinfo .head .f2{display:flex;flex-direction:column;flex:auto;font-size:30rpx;color:#999999}
.expressinfo .head .f2 .t1{margin-bottom:8rpx}
.expressinfo .content{ width: 100%;  background: #fff;display:flex;flex-direction:column;color: #979797;padding:20rpx 40rpx}
.expressinfo .content .on{color: #23aa5e;}
.expressinfo .content .item{display:flex;width: 96%;  margin: 0 2%;border-left: 1px #dadada solid;padding:10rpx 0}
.expressinfo .content .item .f1{ width:40rpx;flex-shrink:0;position:relative}
.expressinfo .content image{width: 30rpx; height: 30rpx; position: absolute; left: -16rpx; top: 22rpx;}
/*.content .on image{ top:-1rpx}*/
.expressinfo .content .item .f1 image{ width: 30rpx; height: 30rpx;}

.expressinfo .content .item .f2{display:flex;flex-direction:column;flex:auto;}
.expressinfo .content .item .f2 .t1{font-size: 30rpx;}
.expressinfo .content .item .f2 .t1{font-size: 26rpx;}


.map{width:100%;height:500rpx;overflow:hidden}
.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}
.ordertop .f1{color:#fff}
.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}
.ordertop .f1 .t2{font-size:24rpx}

.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}
.order-box .head .f1{display:flex;align-items:center;color:#222222}
.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}
.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}
.order-box .head .f2{color:#FF6F30}
.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}

.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}
.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}
.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}

.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}
.order-box .content .f2{}
.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t2{font-size:24rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f3 .img{width:72rpx;height:168rpx}

.orderinfo{width: 94%;margin:20rpx 3%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;border-radius:8px}
.orderinfo .box-title{color:#161616;font-size:30rpx;height:80rpx;line-height:80rpx;font-weight:bold}
.orderinfo .item{display:flex;width:100%;padding:10rpx 0;}
.orderinfo .item .t1{width:200rpx;color:#161616}
.orderinfo .item .t2{flex:1;text-align:right;color:#222222}
.orderinfo .item .red{color:red}

.bottom{ width: 100%;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;align-items:center;height:100rpx;}
.bottom .f1{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55;border-right:1px solid #EAEEED}
.bottom .f1 .img{width:44rpx;height:44rpx}
.bottom .f2{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55}
.bottom .f2 .img{width:44rpx;height:44rpx}
.bottom .btn1{flex:1;background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:100rpx;line-height:100rpx;color:#fff;text-align:center;font-size:32rpx}

/* 服务进度时间轴样式 */
.service-timeline {
  margin: 20rpx 3%;
  width: 94%;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.timeline-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
}

.timeline-container {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 40rpx;
  position: relative;
}

.timeline-icon {
  width: 80rpx;
  position: relative;
}

.timeline-icon .icon-img {
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border-radius: 50%;
  z-index: 2;
  position: relative;
}

.timeline-line {
  position: absolute;
  top: 40rpx;
  left: 20rpx;
  width: 2rpx;
  height: calc(100% + 40rpx);
  background: #e0e0e0;
  z-index: 1;
}

.timeline-content {
  flex: 1;
  padding-left: 20rpx;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.timeline-title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.timeline-photo {
  margin-top: 20rpx;
}

.timeline-photo .photo-img {
  width: 100%;
  height: 360rpx;
  border-radius: 8rpx;
}

.photo-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.completed .timeline-icon .icon-img {
  border: 2rpx solid #07c160;
}

.waiting .timeline-icon .icon-img {
  border: 2rpx solid #ccc;
}

/* 技师位置信息样式 */
.technician-location {
  margin: 20rpx 3%;
  width: 94%;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.location-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.icon-location {
  margin-right: 10rpx;
  color: #07c160;
}

.location-content {
  margin-top: 20rpx;
}

.distance-info, .arrival-time {
  display: flex;
  margin-bottom: 16rpx;
}

.label {
  width: 160rpx;
  color: #666;
  font-size: 28rpx;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.location-photo {
  margin-top: 20rpx;
}

.location-photo .photo-img {
  width: 100%;
  height: 360rpx;
  border-radius: 8rpx;
}
</style>