<template>
<view class="container">
	<view class="header">
		<image src="/static/img/agent-banner.png" class="banner-img"></image>
		<view class="header-content">
			<text class="title">申请成为代理商</text>
			<text class="subtitle">享受专属代理权益，获得丰厚佣金回报</text>
		</view>
	</view>
	
	<view class="content">
		<!-- 申请表单 -->
		<view class="form-container" v-if="apply_status === 'none'">
			<view class="form-title">
				<image src="/static/img/icon-form.png" class="form-icon"></image>
				<text>代理申请信息</text>
			</view>
			
			<view class="form-item">
				<text class="label">代理商名称</text>
				<input class="input" type="text" v-model="formData.name" placeholder="请输入代理商名称" />
			</view>
			
			<view class="form-item">
				<text class="label">联系电话</text>
				<input class="input" type="text" v-model="formData.tel" placeholder="请输入联系电话" />
			</view>
			
			<view class="form-item">
				<text class="label">代理级别</text>
				<picker mode="selector" :range="agentLevels" range-key="name" @change="onLevelChange">
					<view class="picker-display">
						<text>{{selectedLevel.name}}</text>
						<image src="/static/img/arrow-down.png" class="arrow-icon"></image>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="label">备注说明</text>
				<textarea class="textarea" v-model="formData.remark" placeholder="请输入申请理由或备注信息"></textarea>
			</view>
			
			<button class="submit-btn" @tap="submitApply" :disabled="submitting">
				<text v-if="submitting">提交中...</text>
				<text v-else>提交申请</text>
			</button>
		</view>
		
		<!-- 申请状态显示 -->
		<view class="status-container" v-if="apply_status !== 'none'">
			<view class="status-card">
				<image :src="statusIcon" class="status-icon"></image>
				<text class="status-text">{{statusText}}</text>
				<text class="status-desc">{{statusDesc}}</text>
				
				<view class="agent-info" v-if="agentInfo">
					<text class="info-title">申请信息</text>
					<view class="info-item">
						<text class="info-label">代理商名称：</text>
						<text class="info-value">{{agentInfo.name}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">联系电话：</text>
						<text class="info-value">{{agentInfo.tel}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">代理级别：</text>
						<text class="info-value">{{getAgentLevelName(agentInfo.agent_level)}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">申请时间：</text>
						<text class="info-value">{{formatTime(agentInfo.createtime)}}</text>
					</view>
				</view>
				
				<button class="back-btn" @tap="goBack" v-if="apply_status === 1">
					进入代理中心
				</button>
			</view>
		</view>
		
		<!-- 代理权益说明 -->
		<view class="benefits-container">
			<view class="benefits-title">
				<image src="/static/img/icon-benefits.png" class="benefits-icon"></image>
				<text>代理权益</text>
			</view>
			
			<view class="benefit-item">
				<image src="/static/img/icon-commission.png" class="benefit-icon"></image>
				<view class="benefit-content">
					<text class="benefit-title">高额佣金</text>
					<text class="benefit-desc">享受订单高额佣金分成，收益丰厚</text>
				</view>
			</view>
			
			<view class="benefit-item">
				<image src="/static/img/icon-area.png" class="benefit-icon"></image>
				<view class="benefit-content">
					<text class="benefit-title">区域独享</text>
					<text class="benefit-desc">独享覆盖区域代理权，无竞争压力</text>
				</view>
			</view>
			
			<view class="benefit-item">
				<image src="/static/img/icon-support.png" class="benefit-icon"></image>
				<view class="benefit-content">
					<text class="benefit-title">全程支持</text>
					<text class="benefit-desc">专业团队全程指导，助您成功</text>
				</view>
			</view>
		</view>
	</view>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			apply_status: 'none', // none: 未申请, 0: 待审核, 1: 已通过, 2: 已拒绝
			agentInfo: null,
			submitting: false,
			formData: {
				name: '',
				tel: '',
				agent_level: 1,
				remark: ''
			},
			agentLevels: [
				{id: 1, name: '市级代理'},
				{id: 2, name: '省级代理'},
				{id: 3, name: '区县代理'},
				{id: 4, name: '街道代理'}
			],
			selectedLevel: {id: 1, name: '市级代理'}
		};
	},
	
	computed: {
		statusIcon() {
			const icons = {
				0: '/static/img/status-pending.png',
				1: '/static/img/status-approved.png',
				2: '/static/img/status-rejected.png'
			};
			return icons[this.apply_status] || '/static/img/status-pending.png';
		},
		
		statusText() {
			const texts = {
				0: '审核中',
				1: '申请通过',
				2: '申请被拒绝'
			};
			return texts[this.apply_status] || '未知状态';
		},
		
		statusDesc() {
			const descs = {
				0: '您的代理申请正在审核中，请耐心等待...',
				1: '恭喜您！代理申请已通过，您可以开始享受代理权益',
				2: '很抱歉，您的代理申请被拒绝，请联系客服了解详情'
			};
			return descs[this.apply_status] || '';
		}
	},
	
	onLoad() {
		this.checkApplyStatus();
	},
	
	methods: {
		// 安全获取颜色值
		getColor: function(colorKey) {
			try {
				if (typeof this.t === 'function') {
					return this.t(colorKey);
				}
				return null;
			} catch (e) {
				console.log('获取颜色失败:', e);
				return null;
			}
		},
		
		// 安全获取RGB颜色值
		getColorRgb: function(colorKey) {
			try {
				if (typeof this.t === 'function') {
					return this.t(colorKey);
				}
				return null;
			} catch (e) {
				console.log('获取RGB颜色失败:', e);
				return null;
			}
		},
		
		// 检查申请状态
		checkApplyStatus() {
			var that = this;
			// 使用公开接口，通过手机号查询状态
			if (uni.getStorageSync('userTel')) {
				app.get('ApiCityAgentPublic/getApplyStatusByTel', {tel: uni.getStorageSync('userTel')}, function(res) {
					if (res.status === 1) {
						that.apply_status = res.apply_status;
						that.agentInfo = res.agent;
					} else {
						that.apply_status = 'none';
					}
				});
			} else {
				// 如果没有存储的手机号，尝试获取登录用户的申请状态
				app.get('ApiCityAgent/getApplyStatus', {}, function(res) {
					if (res.status === 1) {
						that.apply_status = res.apply_status;
						that.agentInfo = res.agent;
					} else {
						that.apply_status = 'none';
					}
				}, function(error) {
					// 接口调用失败，说明用户未登录或不是代理，显示申请表单
					that.apply_status = 'none';
				});
			}
		},
		
		// 代理级别选择
		onLevelChange(e) {
			const index = e.detail.value;
			this.selectedLevel = this.agentLevels[index];
			this.formData.agent_level = this.selectedLevel.id;
		},
		
		// 提交申请
		submitApply() {
			var that = this;
			
			// 表单验证
			if (!this.formData.name) {
				app.alert('请输入代理商名称');
				return;
			}
			
			if (!this.formData.tel) {
				app.alert('请输入联系电话');
				return;
			}
			
			// 手机号格式验证
			var telReg = /^1[3-9]\d{9}$/;
			if (!telReg.test(this.formData.tel)) {
				app.alert('请输入正确的手机号');
				return;
			}
			
			this.submitting = true;
			
			// 使用公开接口提交申请
			app.post('ApiCityAgentPublic/applyAgent', this.formData, function(res) {
				that.submitting = false;
				
				if (res.status === 1) {
					app.success(res.msg);
					// 存储手机号用于后续查询状态
					uni.setStorageSync('userTel', that.formData.tel);
					// 刷新申请状态
					setTimeout(() => {
						that.checkApplyStatus();
					}, 1000);
				} else {
					app.error(res.msg);
				}
			});
		},
		
		// 返回或进入代理中心
		goBack() {
			if (this.apply_status === 1) {
				// 申请通过，进入代理中心
				app.goto('/pagesExt/cityagent/index');
			} else {
				// 返回上一页
				uni.navigateBack();
			}
		},
		
		// 获取代理级别名称
		getAgentLevelName(level) {
			const levels = {
				1: '市级代理',
				2: '省级代理',
				3: '区县代理',
				4: '街道代理'
			};
			return levels[level] || '未知';
		},
		
		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp * 1000);
			return date.toLocaleString();
		}
	}
};
</script>

<style>
.container {
	min-height: 100vh;
	background: #f8f8f8;
}

.header {
	position: relative;
	height: 300rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
}

.banner-img {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0.3;
}

.header-content {
	text-align: center;
	z-index: 1;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 28rpx;
	opacity: 0.9;
}

.content {
	padding: 30rpx;
}

/* 表单样式 */
.form-container {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.form-title {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.form-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 15rpx;
}

.form-title text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.form-item {
	margin-bottom: 30rpx;
}

.label {
	display: block;
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	background: #fafafa;
}

.textarea {
	width: 100%;
	min-height: 120rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 15rpx 20rpx;
	font-size: 28rpx;
	background: #fafafa;
}

.picker-display {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 0 20rpx;
	background: #fafafa;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
}

.submit-btn {
	width: 100%;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	font-size: 32rpx;
	font-weight: bold;
	border-radius: 40rpx;
	border: none;
	margin-top: 30rpx;
}

.submit-btn[disabled] {
	opacity: 0.6;
}

/* 状态样式 */
.status-container {
	margin-bottom: 30rpx;
}

.status-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.status-icon {
	width: 100rpx;
	height: 100rpx;
	margin-bottom: 20rpx;
}

.status-text {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.status-desc {
	display: block;
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 30rpx;
}

.agent-info {
	background: #f8f8f8;
	border-radius: 15rpx;
	padding: 25rpx;
	margin: 30rpx 0;
	text-align: left;
}

.info-title {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.info-item {
	display: flex;
	margin-bottom: 15rpx;
}

.info-label {
	font-size: 26rpx;
	color: #666;
	width: 140rpx;
}

.info-value {
	font-size: 26rpx;
	color: #333;
	flex: 1;
}

.back-btn {
	width: 300rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	font-size: 28rpx;
	border-radius: 30rpx;
	border: none;
	margin-top: 20rpx;
}

/* 权益说明样式 */
.benefits-container {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.benefits-title {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.benefits-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 15rpx;
}

.benefits-title text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.benefit-item {
	display: flex;
	align-items: center;
	margin-bottom: 25rpx;
}

.benefit-icon {
	width: 50rpx;
	height: 50rpx;
	margin-right: 20rpx;
}

.benefit-content {
	flex: 1;
}

.benefit-title {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.benefit-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}
</style> 