<template>
	<view class="photo-upload-container">
		<!-- 顶部标题区域 -->
		<view class="header-section">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<view class="header-content">
				<text class="main-title">舌诊拍摄</text>
			</view>
			<view class="help-btn" @click="showHelp">
				<text class="help-icon">❓</text>
			</view>
		</view>

		<!-- 拍摄指导区域 -->
		<view class="guide-section">
			<view class="guide-list">
				<view class="guide-item" v-for="(item, index) in guideItems" :key="index">
					<view class="guide-check">{{ index + 1 }}</view>
					<text class="guide-text">{{ item }}</text>
				</view>
			</view>
		</view>

		<!-- 舌头示例图像区域 -->
		<view class="example-section">
			<view class="example-container">
				<image 
					class="tongue-example" 
					src="/static/shezhen/tongue-example.png" 
					mode="aspectFit"
					@error="onExampleImageError"
				></image>
				<view class="example-overlay">
					<view class="scan-frame"></view>
					<text class="example-text">请将舌头放置在框内</text>
				</view>
			</view>
		</view>

		<!-- 底部相机控制区域 -->
		<view class="camera-controls">
			<view class="control-item" @click="openGallery">
				<text class="control-icon">📱</text>
				<text class="control-label">相册</text>
			</view>
			
			<view class="control-item main-control" @click="takePhoto">
				<view class="capture-btn" :class="{ 'capturing': isCapturing }">
					<view class="capture-circle"></view>
				</view>
				<text class="control-label">拍摄</text>
			</view>
			
			<view class="control-item" @click="switchCamera">
				<text class="control-icon">🔄</text>
				<text class="control-label">切换</text>
			</view>
		</view>

		<!-- 拍摄成功遮罩 -->
		<view v-if="showCaptureSuccess" class="capture-success-overlay">
			<view class="success-content">
				<text class="success-icon">✓</text>
				<text class="success-text">拍摄成功</text>
			</view>
		</view>

		<!-- 加载遮罩 -->
		<view v-if="isLoading" class="loading-overlay">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PhotoUpload',
	data() {
		return {
			// 拍摄指导信息数据
			guideItems: [
				'将舌头完整放置在拍摄区域内',
				'光线充足，不逆光、不曝光、不反光', 
				'确保舌头无异物、无异色，舌面平展'
			],
			
			// 拍摄状态控制
			isCapturing: false,          // 是否正在拍摄
			isLoading: false,            // 是否显示加载状态
			loadingText: '正在处理...',  // 加载提示文本
			showCaptureSuccess: false,   // 是否显示拍摄成功提示
			
			// 相机配置
			cameraPosition: 'back',      // 相机位置：front前置，back后置
			flashMode: 'off'             // 闪光灯模式：off关闭，on开启，auto自动
		}
	},
	
	onLoad() {
		console.log('2025-01-26 11:35:00,001-INFO-[photo-upload][onLoad_001] 舌苔拍摄上传页面加载完成');
		
		// 初始化相机权限检查
		this.initCameraPermission();
	},
	
	onReady() {
		console.log('2025-01-26 11:35:00,002-INFO-[photo-upload][onReady_001] 页面渲染完成');
	},
	
	onUnload() {
		console.log('2025-01-26 11:35:00,003-INFO-[photo-upload][onUnload_001] 页面卸载');
	},
	
	methods: {
		/**
		 * 初始化相机权限检查
		 * 检查并申请相机使用权限
		 */
		initCameraPermission() {
			console.log('2025-01-26 11:35:00,004-INFO-[photo-upload][initCameraPermission_001] 开始检查相机权限');
			
			uni.authorize({
				scope: 'scope.camera',
				success: () => {
					console.log('2025-01-26 11:35:00,005-INFO-[photo-upload][initCameraPermission_002] 相机权限获取成功');
					this.checkWritePhotosAlbumPermission();
				},
				fail: (err) => {
					console.error('2025-01-26 11:35:00,006-ERROR-[photo-upload][initCameraPermission_003] 相机权限获取失败:', err);
					
					uni.showModal({
						title: '权限申请',
						content: '需要相机权限才能进行舌诊拍摄，请允许权限申请',
						success: (res) => {
							if (res.confirm) {
								console.log('2025-01-26 11:35:00,007-INFO-[photo-upload][initCameraPermission_004] 用户同意跳转设置页面');
								uni.openSetting({
									success: (settingRes) => {
										console.log('2025-01-26 11:35:00,008-INFO-[photo-upload][initCameraPermission_005] 设置页面返回结果:', settingRes);
									}
								});
							} else {
								console.log('2025-01-26 11:35:00,009-INFO-[photo-upload][initCameraPermission_006] 用户拒绝权限申请');
								this.goBack();
							}
						}
					});
				}
			});
		},
		
		/**
		 * 检查相册写入权限
		 * 用于保存拍摄的照片到相册
		 */
		checkWritePhotosAlbumPermission() {
			console.log('2025-01-26 11:35:00,010-INFO-[photo-upload][checkWritePhotosAlbumPermission_001] 检查相册写入权限');
			
			uni.authorize({
				scope: 'scope.writePhotosAlbum',
				success: () => {
					console.log('2025-01-26 11:35:00,011-INFO-[photo-upload][checkWritePhotosAlbumPermission_002] 相册写入权限获取成功');
				},
				fail: (err) => {
					console.log('2025-01-26 11:35:00,012-WARN-[photo-upload][checkWritePhotosAlbumPermission_003] 相册写入权限获取失败，但不影响拍摄功能:', err);
				}
			});
		},
		
		/**
		 * 拍摄照片
		 * 调用相机拍摄舌头照片
		 */
		takePhoto() {
			if (this.isCapturing || this.isLoading) {
				console.log('2025-01-26 11:35:00,013-INFO-[photo-upload][takePhoto_001] 正在处理中，忽略重复点击');
				return;
			}
			
			console.log('2025-01-26 11:35:00,014-INFO-[photo-upload][takePhoto_002] 开始拍摄舌头照片');
			
			this.isCapturing = true;
			this.isLoading = true;
			this.loadingText = '正在拍摄...';
			
			// 使用uni.chooseImage进行拍摄
			uni.chooseImage({
				count: 1,                    // 最多选择1张图片
				sizeType: ['original'],      // 选择原图
				sourceType: ['camera'],      // 只允许拍摄，不允许从相册选择
				success: (res) => {
					console.log('2025-01-26 11:35:00,015-INFO-[photo-upload][takePhoto_003] 拍摄成功，图片路径:', res.tempFilePaths[0]);
					
					this.isCapturing = false;
					this.isLoading = false;
					this.showCaptureSuccess = true;
					
					// 1秒后隐藏成功提示并跳转
					setTimeout(() => {
						this.showCaptureSuccess = false;
						this.navigateToAnalysis(res.tempFilePaths[0]);
					}, 1000);
				},
				fail: (err) => {
					console.error('2025-01-26 11:35:00,016-ERROR-[photo-upload][takePhoto_004] 拍摄失败:', err);
					
					this.isCapturing = false;
					this.isLoading = false;
					
					// 判断是否用户取消
					if (err.errMsg && err.errMsg.includes('cancel')) {
						console.log('2025-01-26 11:35:00,017-INFO-[photo-upload][takePhoto_005] 用户取消拍摄');
						return;
					}
					
					uni.showToast({
						title: '拍摄失败，请重试',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		/**
		 * 打开相册选择图片
		 * 允许用户从相册中选择舌头照片
		 */
		openGallery() {
			console.log('2025-01-26 11:35:00,018-INFO-[photo-upload][openGallery_001] 打开相册选择图片');
			
			uni.chooseImage({
				count: 1,                         // 最多选择1张图片
				sizeType: ['original'],           // 选择原图
				sourceType: ['album'],            // 只允许从相册选择
				success: (res) => {
					console.log('2025-01-26 11:35:00,019-INFO-[photo-upload][openGallery_002] 从相册选择成功，图片路径:', res.tempFilePaths[0]);
					
					this.navigateToAnalysis(res.tempFilePaths[0]);
				},
				fail: (err) => {
					console.error('2025-01-26 11:35:00,020-ERROR-[photo-upload][openGallery_003] 相册选择失败:', err);
					
					// 判断是否用户取消
					if (err.errMsg && err.errMsg.includes('cancel')) {
						console.log('2025-01-26 11:35:00,021-INFO-[photo-upload][openGallery_004] 用户取消相册选择');
						return;
					}
					
					uni.showToast({
						title: '图片选择失败',
						icon: 'none'
					});
				}
			});
		},
		
		/**
		 * 切换摄像头
		 * 在前置和后置摄像头之间切换
		 */
		switchCamera() {
			console.log('2025-01-26 11:35:00,022-INFO-[photo-upload][switchCamera_001] 切换摄像头');
			
			this.cameraPosition = this.cameraPosition === 'front' ? 'back' : 'front';
			
			uni.showToast({
				title: this.cameraPosition === 'front' ? '前置摄像头' : '后置摄像头',
				icon: 'none',
				duration: 1000
			});
			
			console.log('2025-01-26 11:35:00,023-INFO-[photo-upload][switchCamera_002] 摄像头切换至:', this.cameraPosition);
		},
		
		/**
		 * 跳转到分析页面
		 * @param {string} imagePath - 图片路径
		 */
		navigateToAnalysis(imagePath) {
			console.log('2025-01-26 11:35:00,024-INFO-[photo-upload][navigateToAnalysis_001] 跳转到分析页面，图片路径:', imagePath);
			
			uni.navigateTo({
				url: `/pagesB/shezhen/analysis?image=${encodeURIComponent(imagePath)}`,
				success: () => {
					console.log('2025-01-26 11:35:00,025-INFO-[photo-upload][navigateToAnalysis_002] 成功跳转到分析页面');
				},
				fail: (err) => {
					console.error('2025-01-26 11:35:00,026-ERROR-[photo-upload][navigateToAnalysis_003] 跳转分析页面失败:', err);
					
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
		},
		
		/**
		 * 返回上一页
		 * 返回到舌诊引导页面
		 */
		goBack() {
			console.log('2025-01-26 11:35:00,027-INFO-[photo-upload][goBack_001] 返回上一页');
			
			uni.navigateBack({
				delta: 1,
				success: () => {
					console.log('2025-01-26 11:35:00,028-INFO-[photo-upload][goBack_002] 成功返回上一页');
				},
				fail: (err) => {
					console.error('2025-01-26 11:35:00,029-ERROR-[photo-upload][goBack_003] 返回上一页失败:', err);
					
					// 如果返回失败，尝试跳转到引导页面
					uni.redirectTo({
						url: '/pagesB/shezhen/guide'
					});
				}
			});
		},
		
		/**
		 * 显示帮助信息
		 * 显示更详细的拍摄说明
		 */
		showHelp() {
			console.log('2025-01-26 11:35:00,032-INFO-[photo-upload][showHelp_001] 显示帮助信息');
			
			uni.showModal({
				title: '拍摄帮助',
				content: '1. 选择光线充足的环境\n2. 张大嘴巴，充分伸出舌头\n3. 保持舌面平整，无食物残留\n4. 手机保持稳定，对准舌头中央',
				success: (res) => {
					console.log('2025-01-26 11:35:00,033-INFO-[photo-upload][showHelp_002] 帮助信息确认');
				}
			});
		},
		
		/**
		 * 示例图片加载错误处理
		 * 当舌头示例图片加载失败时的处理
		 */
		onExampleImageError() {
			console.error('2025-01-26 11:35:00,034-ERROR-[photo-upload][onExampleImageError_001] 示例图片加载失败');
			
			// 不显示错误提示，静默处理
		}
	}
}
</script>

<style scoped>
/* 主容器样式 */
.photo-upload-container {
	width: 100vw;
	height: 100vh;
	background: #ffffff;
	display: flex;
	flex-direction: column;
}

/* 顶部标题区域样式 */
.header-section {
	background: #ffffff;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f0f0f0;
}

.back-btn,
.help-btn {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: #f8f9fa;
}

.back-icon,
.help-icon {
	font-size: 32rpx;
	color: #666;
}

.header-content {
	flex: 1;
	text-align: center;
}

.main-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

/* 拍摄指导区域样式 */
.guide-section {
	padding: 30rpx;
}

.guide-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.guide-item:last-child {
	margin-bottom: 0;
}

.guide-check {
	width: 40rpx;
	height: 40rpx;
	background: #007AFF;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.guide-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.4;
	flex: 1;
}

/* 舌头示例图像区域样式 */
.example-section {
	flex: 1;
	padding: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.example-container {
	position: relative;
	width: 520rpx;
	height: 400rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	overflow: hidden;
}

.tongue-example {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.example-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.scan-frame {
	width: 240rpx;
	height: 320rpx;
	border: 3rpx solid #007AFF;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
}

.example-text {
	font-size: 26rpx;
	color: #666;
	background: rgba(255, 255, 255, 0.9);
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
}

/* 底部相机控制区域样式 */
.camera-controls {
	background: #ffffff;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-around;
	border-top: 1rpx solid #f0f0f0;
}

.control-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.control-icon {
	font-size: 40rpx;
	margin-bottom: 10rpx;
}

.control-label {
	font-size: 24rpx;
	color: #666;
}

.main-control {
	/* 主拍摄按钮容器 */
}

.capture-btn {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: #007AFF;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 10rpx;
	transition: all 0.2s ease;
}

.capture-btn.capturing {
	transform: scale(0.95);
	background: #ff3b30;
}

.capture-circle {
	width: 80rpx;
	height: 80rpx;
	background: white;
	border-radius: 50%;
}

/* 拍摄成功遮罩样式 */
.capture-success-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.7);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.success-content {
	background: white;
	padding: 60rpx;
	border-radius: 16rpx;
	text-align: center;
	min-width: 280rpx;
}

.success-icon {
	font-size: 60rpx;
	color: #34c759;
	margin-bottom: 20rpx;
	display: block;
	font-weight: bold;
}

.success-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

/* 加载遮罩样式 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.loading-content {
	background: white;
	padding: 60rpx;
	border-radius: 16rpx;
	text-align: center;
	min-width: 280rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin: 0 auto 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 响应式适配 */
@media screen and (max-width: 400px) {
	.header-section {
		padding: 15rpx 20rpx;
	}
	
	.main-title {
		font-size: 32rpx;
	}
	
	.guide-section {
		padding: 20rpx;
	}
	
	.example-container {
		width: 450rpx;
		height: 350rpx;
	}
	
	.camera-controls {
		padding: 30rpx;
	}
}
</style> 