<template>
<view>
    <block v-if="isload">
        <view class="container nodiydata">
            <view class="topcontent">
                <view class="logo">
                    <view class="level-icon" :style="{background:t('color1')}">{{rewardRule.name ? rewardRule.name.substr(0,1) : 'R'}}</view>
                </view>
                <view class="title">{{rewardRule.name}}</view>
                <view class="desc">
                    <view class="status-tag" :class="rewardRule.status == 1 ? 'active' : 'inactive'">
                        {{rewardRule.status == 1 ? '启用中' : '已禁用'}}
                    </view>
                </view>
            </view>

            <view class="contentbox">
                <view class="shop_tab">
                    <view :class="'cptab_text ' + (st==0?'cptab_current':'')" @tap="changetab" data-st="0">规则详情<view class="after" :style="{background:t('color1')}"></view></view>
                    <view :class="'cptab_text ' + (st==1?'cptab_current':'')" @tap="changetab" data-st="1">奖励记录<view class="after" :style="{background:t('color1')}"></view></view>
                    <view :class="'cptab_text ' + (st==2?'cptab_current':'')" @tap="changetab" data-st="2">统计数据<view class="after" :style="{background:t('color1')}"></view></view>
                </view>

                <!-- 规则详情 -->
                <view class="cp_detail" v-if="st==0">
                    <view class="detail-section">
                        <view class="section-title">推荐人等级</view>
                        <view class="section-content">
                            <view class="level-tag" v-if="rewardRule.from_level_names && rewardRule.from_level_names.length > 0" 
                                  v-for="(levelName, idx) in rewardRule.from_level_names" :key="'from_'+idx">
                                {{levelName}}
                            </view>
                            <view class="level-tag" v-else>{{rewardRule.from_level_name}}</view>
                        </view>
                    </view>

                    <view class="detail-section">
                        <view class="section-title">被推荐人等级</view>
                        <view class="section-content">
                            <view class="level-tag" v-if="rewardRule.to_level_names && rewardRule.to_level_names.length > 0" 
                                  v-for="(levelName, idx) in rewardRule.to_level_names" :key="'to_'+idx">
                                {{levelName}}
                            </view>
                            <view class="level-tag" v-else>{{rewardRule.to_level_name}}</view>
                        </view>
                    </view>
                    
                    <view class="detail-section">
                        <view class="section-title">奖励模式</view>
                        <view class="section-content">
                            <view class="reward-mode-tag" :class="rewardRule.reward_mode">
                                <text class="reward-mode-icon">
                                    <text class="iconfont" :class="rewardRule.reward_mode === 'regular' ? 'icon-gift' : 'icon-chart-line'"></text>
                                </text>
                                <text class="reward-mode-text">
                                    {{rewardRule.reward_mode === 'regular' ? '常规奖励' : '累计创客奖励'}}
                                </text>
                                <view class="reward-mode-desc">
                                    {{rewardRule.reward_mode === 'regular' ? '每推荐一个人获得一次奖励' : '累计达到一定人数时发放奖励'}}
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="detail-section">
                        <view class="section-title">奖励规则</view>
                        <view class="section-content">
                            <view class="reward-rule-item" v-for="(rule, idx) in rewardRule.reward_rules" :key="idx">
                                <view class="rule-icon" :style="{background:t('color1')}">{{idx+1}}</view>
                                <view class="rule-content">
                                    <view class="rule-text">
                                        <text v-if="rewardRule.reward_mode === 'regular'">每推荐 <text class="highlight">{{rule.recommend_count}}</text> 个人</text>
                                        <text v-else>累计达到 <text class="highlight">{{rule.recommend_count}}</text> 人</text>
                                    </view>
                                    <view class="rule-text">奖励 <text class="highlight">{{rule.reward_amount}}</text> 
                                        {{rule.reward_type === 'balance' ? t('余额') : 
                                          rule.reward_type === 'points' ? t('积分') : 
                                          rule.reward_type === 'commission' ? t('佣金') : 
                                          rule.reward_type === 'xianjinquan' ? t('现金券') : t('贡献值')}}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="detail-section">
                        <view class="section-title">创建时间</view>
                        <view class="section-content">
                            <view class="time-text">{{formatTime(rewardRule.createtime)}}</view>
                        </view>
                    </view>
                </view>

                <!-- 奖励记录 -->
                <view class="cp_detail" v-if="st==1">
                    <view class="rewards-list">
                        <block v-if="datalist.length > 0">
                            <view class="reward-record" v-for="(item, index) in datalist" :key="index">
                                <view class="record-header">
                                    <view class="user-info">
                                        <image class="avatar" :src="item.to_headimg || '/static/img/default_avatar.png'"></image>
                                        <view class="user-details">
                                            <view class="nickname">{{item.to_nickname}}</view>
                                            <view class="tel">{{item.to_tel}}</view>
                                        </view>
                                    </view>
                                    <view class="reward-amount" :style="{color:t('color1')}">
                                        +{{item.reward_amount}} {{item.reward_type_name || 
                                          (item.reward_type === 'balance' ? t('余额') : 
                                           item.reward_type === 'points' ? t('积分') : 
                                           item.reward_type === 'commission' ? t('佣金') : 
                                           item.reward_type === 'xianjinquan' ? t('现金券') : t('贡献值'))}}
                                    </view>
                                </view>
                                <view class="record-content">
                                    <view class="level-info">
                                        <text>推荐人等级: {{item.from_level_name}}</text>
                                        <text class="separator">|</text>
                                        <text>被推荐人等级: {{item.to_level_name}}</text>
                                    </view>
                                    <view class="record-time">{{item.createtime_format}}</view>
                                </view>
                            </view>
                        </block>
                        <nomore v-if="nomore"></nomore>
                        <nodata v-if="nodata"></nodata>
                    </view>
                </view>

                <!-- 统计数据 -->
                <view class="cp_detail" v-if="st==2">
                    <view class="stats-cards">
                        <view class="stats-card">
                            <view class="stats-value">{{statsData.total_recommend || 0}}</view>
                            <view class="stats-label">总推荐人数</view>
                        </view>
                        <view class="stats-card">
                            <view class="stats-value">{{statsData.today_recommend || 0}}</view>
                            <view class="stats-label">今日推荐</view>
                        </view>
                        <view class="stats-card">
                            <view class="stats-value">{{statsData.total_reward || 0}}</view>
                            <view class="stats-label">总奖励金额</view>
                        </view>
                    </view>
                    
                    <view class="my-referrer" v-if="myReferrer">
                        <view class="section-title">我的推荐人</view>
                        <view class="referrer-card">
                            <image class="referrer-avatar" :src="myReferrer.headimg || '/static/img/default_avatar.png'"></image>
                            <view class="referrer-info">
                                <view class="referrer-name">{{myReferrer.nickname}}</view>
                                <view class="referrer-tel">{{myReferrer.tel}}</view>
                                <view class="referrer-level">等级: {{myReferrer.level_name}}</view>
                            </view>
                        </view>
                    </view>
                    
                    <view class="my-referrals">
                        <view class="section-title">我推荐的会员 ({{referralsCount || 0}})</view>
                        <view class="referrals-list" v-if="myReferrals.length > 0">
                            <view class="referral-item" v-for="(item, index) in myReferrals" :key="index">
                                <image class="referral-avatar" :src="item.headimg || '/static/img/default_avatar.png'"></image>
                                <view class="referral-info">
                                    <view class="referral-name">{{item.nickname}}</view>
                                    <view class="referral-level">{{item.level_name}}</view>
                                </view>
                                <view class="referral-time">{{item.createtime_format}}</view>
                            </view>
                        </view>
                        <nomore v-if="referralsNomore"></nomore>
                        <nodata v-if="referralsNodata"></nodata>
                    </view>
                </view>
            </view>
            
            <view class="covermy" style="top:75vh;background:rgba(0,0,0,0.7)" @tap="goBack">
                <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">返回</text>
                <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">上一页</text>
            </view>
        </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
    data() {
        return {
            opt:{},
            loading:false,
            isload: false,
            menuindex:-1,
            pre_url:app.globalData.pre_url,

            st: 0,
            rewardRule:{},
            pagenum: 1,
            datalist: [],
            topbackhide: false,
            nomore: false,
            nodata:false,
            
            statsData: {},
            myReferrer: null,
            myReferrals: [],
            referralsCount: 0,
            referralsPagenum: 1,
            referralsNomore: false,
            referralsNodata: false
        }
    },
    onLoad: function (opt) {
        this.opt = app.getopts(opt);
        this.st = this.opt.st || 0;
        this.getdata();
    },
    onPullDownRefresh: function () {
        this.pagenum = 1;
        this.referralsPagenum = 1;
        this.getdata();
    },
    onReachBottom: function () {
        if (this.st == 1 && !this.nodata && !this.nomore) {
            this.pagenum = this.pagenum + 1;
            this.getDataList(true);
        } else if (this.st == 2 && !this.referralsNodata && !this.referralsNomore) {
            this.referralsPagenum = this.referralsPagenum + 1;
            this.getReferralsList(true);
        }
    },
    onPageScroll: function (e) {
        uni.$emit('onPageScroll',e);
    },
    onShareAppMessage:function(){
        return this._sharewx({title:this.rewardRule.name || '等级推荐奖励'});
    },
    onPageScroll: function (e) {
        var that = this;
        var scrollY = e.scrollTop;
        if (scrollY > 200 && !that.topbackhide) {
            that.topbackhide = true;
        }
        if (scrollY < 150 && that.topbackhide) {
            that.topbackhide = false;
        }
    },
    methods: {
        getdata: function () {
            var that = this;
            var id = that.opt.id || 0;
            that.loading = true;
            app.get('ApiLevelreward/getlist', {id: id}, function (res) {
                that.loading = false;
                if(res.data && res.data.length > 0) {
                    that.rewardRule = res.data[0]; // 获取规则详情
                    that.loaded({title:that.rewardRule.name,pic:''});
                    uni.setNavigationBarTitle({
                        title: that.rewardRule.name
                    });
                    that.getDataList();
                } else {
                    app.alert('未找到奖励规则');
                }
            });
        },
        changetab: function (e) {
            var st = e.currentTarget.dataset.st;
            this.st = st;
            if (st == 1) {
                this.pagenum = 1;
                this.datalist = [];
                this.nomore = false;
                this.nodata = false;
            } else if (st == 2) {
                this.referralsPagenum = 1;
                this.myReferrals = [];
                this.referralsNomore = false;
                this.referralsNodata = false;
            }
            uni.pageScrollTo({
                scrollTop: 0,
                duration: 0
            });
            this.getDataList();
        },
        getDataList: function (loadmore) {
            if(!loadmore){
                this.pagenum = 1;
                this.datalist = [];
            }
            var that = this;
            var pagenum = that.pagenum;
            var st = that.st;
            that.loading = true;
            that.nodata = false;
            that.nomore = false;
            
            if(st == 0) {
                that.loading = false;
                that.nodata = false;
            } else if(st == 1) {
                app.post('ApiLevelreward/getrecord', {pagenum: pagenum, limit: 20}, function (res) {
                    that.loading = false;
                    uni.stopPullDownRefresh();
                    var data = res.data;
                    if (pagenum == 1) {
                        that.datalist = data;
                        if (data.length == 0) {
                            that.nodata = true;
                        }
                    } else {
                        if (data.length == 0) {
                            that.nomore = true;
                        } else {
                            var datalist = that.datalist;
                            var newdata = datalist.concat(data);
                            that.datalist = newdata;
                        }
                    }
                });
            } else if(st == 2) {
                app.post('ApiLevelreward/getstats', {}, function (res) {
                    that.statsData = res.data;
                    
                    app.post('ApiLevelreward/getmyreferrer', {}, function (refRes) {
                        that.myReferrer = refRes.data;
                        
                        if (!loadmore) {
                            that.referralsPagenum = 1;
                            that.getReferralsList(false);
                        } else {
                            that.loading = false;
                            uni.stopPullDownRefresh();
                        }
                    });
                });
            }
        },
        getReferralsList: function(loadmore) {
            var that = this;
            var pagenum = that.referralsPagenum;
            that.loading = true;
            
            app.post('ApiLevelreward/getmyreferrals', {pagenum: pagenum, limit: 10}, function (refsRes) {
                that.loading = false;
                uni.stopPullDownRefresh();
                var data = refsRes.data;
                that.referralsCount = refsRes.count;
                
                if (pagenum == 1) {
                    that.myReferrals = data;
                    if (data.length == 0) {
                        that.referralsNodata = true;
                    }
                } else {
                    if (data.length == 0) {
                        that.referralsNomore = true;
                    } else {
                        var currentReferrals = that.myReferrals;
                        var newReferrals = currentReferrals.concat(data);
                        that.myReferrals = newReferrals;
                    }
                }
            });
        },
        formatTime: function(timestamp) {
            if (!timestamp) return '';
            var date = new Date(timestamp * 1000);
            var year = date.getFullYear();
            var month = ('0' + (date.getMonth() + 1)).slice(-2);
            var day = ('0' + date.getDate()).slice(-2);
            var hours = ('0' + date.getHours()).slice(-2);
            var minutes = ('0' + date.getMinutes()).slice(-2);
            
            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
        },
        goBack() {
            uni.navigateBack({
                delta: 1
            });
        }
    }
}
</script>
<style>
.container{position:relative}
.nodiydata{display:flex;flex-direction:column}

/* 顶部内容区域改进 */
.nodiydata .topcontent{
    width:94%;
    margin-left:3%;
    padding: 40rpx 24rpx; 
    border-bottom:none;
    margin-bottom:30rpx; 
    background: linear-gradient(to right, #f7f9ff, #eef2ff);
    display:flex;
    flex-direction:column;
    align-items:center;
    border-radius:24rpx;
    position:relative;
    z-index:2;
    box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.05);
}

.nodiydata .topcontent .logo{
    width:140rpx;
    height:140rpx;
    margin-top:20rpx;
    border:none;
    border-radius:70rpx;
    overflow:hidden;
    box-shadow: 0 10rpx 20rpx rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.nodiydata .topcontent .logo:active {
    transform: scale(0.96);
}

.nodiydata .topcontent .logo .level-icon{
    width:100%;
    height:100%;
    display:flex;
    align-items:center;
    justify-content:center;
    color:#fff;
    font-size:56rpx;
    font-weight:bold;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.nodiydata .topcontent .title {
    color:#222222;
    font-size:40rpx;
    font-weight:700;
    margin-top:24rpx;
    text-align:center;
    padding:0 10rpx;
    letter-spacing: 1rpx;
}

.nodiydata .topcontent .desc {
    display:flex;
    align-items:center;
    margin-top:20rpx;
}

.nodiydata .topcontent .status-tag {
    padding:8rpx 24rpx;
    border-radius:30rpx;
    font-size:24rpx;
    margin-bottom:20rpx;
    font-weight:500;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10rpx); }
    to { opacity: 1; transform: translateY(0); }
}

.nodiydata .topcontent .active {
    background:linear-gradient(to right, #E6F7E6, #d7f5d7);
    color:#07C160;
}

.nodiydata .topcontent .inactive {
    background:linear-gradient(to right, #F5F5F5, #ebebeb);
    color:#999;
}

/* 内容区域改进 */
.nodiydata .contentbox{
    width:94%;
    margin-left:3%;
    background: #fff;
    border-radius:24rpx;
    margin-bottom:32rpx;
    overflow:hidden;
    box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.05);
}

/* 标签页改进 */
.nodiydata .shop_tab{
    display:flex;
    width: 100%;
    height:100rpx;
    border-bottom:1px solid #f0f0f0;
    background: #fff;
}

.nodiydata .shop_tab .cptab_text{
    flex:1;
    text-align:center;
    color:#646566;
    height:100rpx;
    line-height:100rpx;
    position:relative;
    font-size: 28rpx;
    transition: color 0.3s;
}

.nodiydata .shop_tab .cptab_current{
    color: #323233;
    font-weight: 600;
}

.nodiydata .shop_tab .after{
    display:none;
    position:absolute;
    left:50%;
    margin-left:-20rpx;
    bottom:10rpx;
    height:4rpx;
    border-radius:2rpx;
    width:40rpx;
    transition: width 0.3s;
}

.nodiydata .shop_tab .cptab_current .after{
    display:block;
    animation: tabIndicator 0.3s ease-out;
}

@keyframes tabIndicator {
    from { width: 0; margin-left: 0; }
    to { width: 40rpx; margin-left: -20rpx; }
}

.nodiydata .cp_detail{
    min-height:500rpx;
    padding:40rpx 30rpx;
}

/* 规则详情样式改进 */
.detail-section {
    margin-bottom:40rpx;
    animation: sectionFadeIn 0.5s ease-in-out;
}

@keyframes sectionFadeIn {
    from { opacity: 0; transform: translateY(20rpx); }
    to { opacity: 1; transform: translateY(0); }
}

.section-title {
    font-size:32rpx;
    font-weight:700;
    color:#333;
    margin-bottom:24rpx;
    position:relative;
    padding-left:24rpx;
    display: flex;
    align-items: center;
}

.section-title:before {
    content:'';
    width:6rpx;
    height:32rpx;
    background:linear-gradient(to bottom, #007AFF, #0063cc);
    position:absolute;
    left:0;
    top:6rpx;
    border-radius:3rpx;
}

.section-content {
    padding:0 10rpx;
}

.level-tag {
    display:inline-block;
    background:linear-gradient(to right, #F5F6F8, #eaebee);
    color:#333;
    font-size:26rpx;
    padding:12rpx 24rpx;
    border-radius:8rpx;
    margin-right:16rpx;
    margin-bottom:16rpx;
    box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.03);
    transition: transform 0.2s;
}

.level-tag:active {
    transform: scale(0.96);
}

.reward-rule-item {
    display:flex;
    align-items:center;
    margin-bottom:24rpx;
    background:linear-gradient(to right, #F9F9F9, #f2f2f2);
    border-radius:16rpx;
    padding:30rpx 24rpx;
    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.03);
    transition: transform 0.3s;
}

.reward-rule-item:active {
    transform: translateY(2rpx);
}

.rule-icon {
    width:60rpx;
    height:60rpx;
    border-radius:30rpx;
    color:#fff;
    display:flex;
    align-items:center;
    justify-content:center;
    font-weight:bold;
    font-size:28rpx;
    margin-right:24rpx;
    box-shadow: 0 6rpx 12rpx rgba(0,0,0,0.1);
}

.rule-content {
    flex:1;
}

.rule-text {
    font-size:28rpx;
    color:#333;
    line-height:44rpx;
}

.highlight {
    color:#FF6B00;
    font-weight:700;
    padding: 0 4rpx;
}

.time-text {
    font-size:28rpx;
    color:#666;
    padding: 12rpx 20rpx;
    background: #f5f5f5;
    border-radius: 8rpx;
    display: inline-block;
}

/* 奖励记录样式改进 */
.rewards-list {
    padding:0 10rpx;
}

.reward-record {
    background:linear-gradient(to right, #F9F9F9, #f2f2f2);
    border-radius:16rpx;
    padding:24rpx;
    margin-bottom:30rpx;
    box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.03);
    transition: transform 0.3s;
    animation: cardFadeIn 0.5s ease-in-out;
}

@keyframes cardFadeIn {
    from { opacity: 0; transform: translateY(20rpx); }
    to { opacity: 1; transform: translateY(0); }
}

.reward-record:active {
    transform: translateY(2rpx);
}

.record-header {
    display:flex;
    justify-content:space-between;
    align-items:center;
    margin-bottom:20rpx;
}

.user-info {
    display:flex;
    align-items:center;
}

.avatar {
    width:80rpx;
    height:80rpx;
    border-radius:40rpx;
    margin-right:20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
    border: 2rpx solid #fff;
}

.user-details {
    display:flex;
    flex-direction:column;
}

.nickname {
    font-size:30rpx;
    font-weight:600;
    color:#333;
}

.tel {
    font-size:24rpx;
    color:#999;
    margin-top: 4rpx;
}

.reward-amount {
    font-size:36rpx;
    font-weight:700;
    padding: 8rpx 16rpx;
    background: rgba(255,255,255,0.6);
    border-radius: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.record-content {
    border-top:1px solid rgba(238,238,238,0.8);
    padding-top:16rpx;
}

.level-info {
    font-size:26rpx;
    color:#666;
    margin-bottom:12rpx;
    background: rgba(255,255,255,0.6);
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
}

.separator {
    margin:0 10rpx;
    color:#DDDDDD;
}

.record-time {
    font-size:24rpx;
    color:#999;
}

/* 统计数据样式改进 */
.stats-cards {
    display:flex;
    justify-content:space-between;
    margin-bottom:50rpx;
}

.stats-card {
    flex:1;
    background:linear-gradient(to bottom right, #F9F9F9, #f2f2f2);
    border-radius:16rpx;
    padding:30rpx 20rpx;
    margin:0 10rpx;
    text-align:center;
    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.05);
    transition: transform 0.3s;
    animation: statCardFadeIn 0.5s ease-in-out;
}

@keyframes statCardFadeIn {
    from { opacity: 0; transform: translateY(20rpx); }
    to { opacity: 1; transform: translateY(0); }
    0% { opacity: 0; transform: translateY(20rpx); }
    100% { opacity: 1; transform: translateY(0); }
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }

.stats-card:active {
    transform: translateY(2rpx);
}

.stats-value {
    font-size:40rpx;
    font-weight:700;
    color:#333;
    margin-bottom:12rpx;
    text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.05);
}

.stats-label {
    font-size:26rpx;
    color:#999;
    position: relative;
}

.stats-label:after {
    content: '';
    display: block;
    width: 40rpx;
    height: 4rpx;
    background: #eee;
    margin: 12rpx auto 0;
    border-radius: 2rpx;
}

.my-referrer, .my-referrals {
    margin-bottom:50rpx;
    animation: sectionFadeIn 0.5s ease-in-out;
    animation-delay: 0.3s;
}

.referrer-card {
    background:linear-gradient(to right, #F9F9F9, #f2f2f2);
    border-radius:16rpx;
    padding:30rpx 24rpx;
    display:flex;
    align-items:center;
    box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.03);
    transition: transform 0.3s;
}

.referrer-card:active {
    transform: translateY(2rpx);
}

.referrer-avatar {
    width:90rpx;
    height:90rpx;
    border-radius:45rpx;
    margin-right:24rpx;
    border: 2rpx solid #fff;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.referrer-info {
    flex:1;
}

.referrer-name {
    font-size:30rpx;
    font-weight:600;
    color:#333;
}

.referrer-tel, .referrer-level {
    font-size:24rpx;
    color:#999;
    margin-top:8rpx;
}

.referrals-list {
    margin-top:24rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.03);
}

.referral-item {
    display:flex;
    align-items:center;
    padding:24rpx 20rpx;
    border-bottom:1px solid rgba(238,238,238,0.6);
    transition: background-color 0.3s;
}

.referral-item:active {
    background-color: #f9f9f9;
}

.referral-item:last-child {
    border-bottom: none;
}

.referral-avatar {
    width:70rpx;
    height:70rpx;
    border-radius:35rpx;
    margin-right:20rpx;
    border: 2rpx solid #fff;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
}

.referral-info {
    flex:1;
}

.referral-name {
    font-size:28rpx;
    font-weight: 500;
    color:#333;
}

.referral-level {
    font-size:24rpx;
    color:#999;
    margin-top:6rpx;
    background: #f5f5f5;
    display: inline-block;
    padding: 4rpx 12rpx;
    border-radius: 6rpx;
    margin-top: 8rpx;
}

.referral-time {
    font-size:24rpx;
    color:#999;
    background: rgba(245,245,245,0.6);
    padding: 6rpx 12rpx;
    border-radius: 6rpx;
}

.nodiydata .covermy{
    position:fixed;
    z-index:99999;
    cursor:pointer;
    display:flex;
    flex-direction:column;
    align-items:center;
    justify-content:center;
    overflow:hidden;
    z-index:9999;
    top:81vh;
    left:82vw;
    color:#fff;
    background-color:rgba(0,122,255,0.85);
    width:120rpx;
    height:120rpx;
    font-size:26rpx;
    border-radius:60rpx;
    box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
    transition: transform 0.3s, background-color 0.3s;
}

.nodiydata .covermy:active {
    transform: scale(0.95);
    background-color: rgba(0,100,220,0.9);
}

/* 奖励模式样式 */
.reward-mode-tag {
    background: linear-gradient(to right, #F9F9F9, #f2f2f2);
    border-radius: 16rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: column;
    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.03);
    transition: transform 0.3s;
    animation: fadeIn 0.5s ease-in-out;
    position: relative;
    overflow: hidden;
}

.reward-mode-tag:active {
    transform: translateY(2rpx);
}

.reward-mode-tag.regular {
    border-left: 6rpx solid #36BFFA;
}

.reward-mode-tag.regular:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100rpx;
    height: 100rpx;
    background: rgba(54, 191, 250, 0.05);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.reward-mode-tag.cumulative {
    border-left: 6rpx solid #FF6B00;
}

.reward-mode-tag.cumulative:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100rpx;
    height: 100rpx;
    background: rgba(255, 107, 0, 0.05);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.reward-mode-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    border-radius: 30rpx;
    margin-bottom: 16rpx;
    color: #fff;
    font-size: 28rpx;
}

.reward-mode-tag.regular .reward-mode-icon {
    background: linear-gradient(to bottom right, #36BFFA, #0093D9);
    box-shadow: 0 6rpx 12rpx rgba(54, 191, 250, 0.2);
}

.reward-mode-tag.cumulative .reward-mode-icon {
    background: linear-gradient(to bottom right, #FF6B00, #E85600);
    box-shadow: 0 6rpx 12rpx rgba(255, 107, 0, 0.2);
}

.reward-mode-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx;
}

.reward-mode-desc {
    font-size: 26rpx;
    color: #999;
    line-height: 36rpx;
}
</style> 