<template>
<view class="container">
	<!-- 顶部导航栏 -->
	<view class="nav-header">
		<view class="nav-title">消息通知</view>
		<view class="nav-actions">
			<view class="unread-count" v-if="unread_count > 0">{{unread_count}}</view>
			<view class="action-btn" @click="markAllAsRead" v-if="unread_count > 0">全部已读</view>
		</view>
	</view>
	
	<!-- 搜索栏 -->
	<view class="search-container">
		<view class="search-box">
			<image src="/static/icon/search.png" class="search-icon"></image>
			<input type="text" v-model="keyword" placeholder="搜索消息" @confirm="searchMessages" />
		</view>
	</view>
	
	<!-- 筛选标签 -->
	<view class="filter-tabs">
		<view class="tab-item" :class="{active: read_status === 'all'}" @click="filterByReadStatus('all')">
			全部
		</view>
		<view class="tab-item" :class="{active: read_status === 'unread'}" @click="filterByReadStatus('unread')">
			未读 <text v-if="unread_count > 0" class="tab-count">({{unread_count}})</text>
		</view>
		<view class="tab-item" :class="{active: read_status === 'read'}" @click="filterByReadStatus('read')">
			已读 <text v-if="read_count > 0" class="tab-count">({{read_count}})</text>
		</view>
	</view>
	
	<!-- 消息列表 - 微信聊天样式 -->
	<view class="message-list">
		<view v-for="(item, index) in list" :key="index" class="chat-item" @click="goDetail(item)">
			<!-- 左侧头像区域 -->
			<view class="avatar-container">
				<image :src="item.pic || '/static/img/default-message.png'" class="message-avatar" mode="aspectFill"></image>
				<!-- 未读红点 -->
				<view v-if="item.is_read == 0" class="red-dot"></view>
			</view>
			
			<!-- 右侧内容区域 -->
			<view class="content-container">
				<view class="content-header">
					<view class="message-title">{{item.title}}</view>
					<view class="message-time">{{item.createtime_text}}</view>
				</view>
				<view class="content-body">
					<view class="message-preview">{{item.subtitle || item.content}}</view>
					<view class="message-status">
						<text v-if="item.is_read == 0" class="unread-indicator">●</text>
						<text class="read-count">{{item.readcount}}人已读</text>
					</view>
				</view>
			</view>
		</view>
	</view>
	
	<!-- 无数据提示 -->
	<view v-if="!loading && list.length === 0" class="empty-state">
		<image src="/static/img/empty-message.png" class="empty-icon"></image>
		<text class="empty-text">暂无消息通知</text>
	</view>
	
	<!-- 加载状态 -->
	<view v-if="loading && list.length === 0" class="loading-state">
		<text>加载中...</text>
	</view>
	
	<!-- 加载更多 -->
	<view v-if="!nomore && list.length > 0" class="load-more">
		<text>上拉加载更多</text>
	</view>
	
	<!-- 没有更多 -->
	<view v-if="nomore && list.length > 0" class="no-more">
		<text>没有更多消息了</text>
	</view>
	
	<!-- 底部导航 -->
	<dp-tabbar :current="0"></dp-tabbar>
	
	<!-- 弹窗消息 -->
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:true,
      isload: false,
			menuindex:-1,
			nodata:false,
			nomore:false,
			keyword:'',
      list: [],
      pagenum: 1,
			clist:[],
			cnamelist:[],
			cidlist:[],
      cid: 0,
			bid: 0,
			listtype:0,
            set:'',
            look_type:false,
			read_status: 'all',
			total_count: 0,
			read_count: 0,
			unread_count: 0,
			current: 0,
			tablist: [
				{name: '全部', id: 0},
				{name: '系统通知', id: 1},
				{name: '活动消息', id: 2},
				{name: '订单消息', id: 3}
			]
    };
  },
  onLoad: function (opt) {
		console.log('2025-01-03 22:55:53,565-INFO-[msglist][onLoad_001] 页面加载开始');
		this.opt = app.getopts(opt);
		this.cid = this.opt.cid || 0;	
		this.bid = this.opt.bid || 0;
        this.look_type = this.opt.look_type || false;
        if(this.opt.keyword) {
        	this.keyword = this.opt.keyword;
        }
		console.log('2025-01-03 22:55:53,566-INFO-[msglist][onLoad_002] 参数解析完成', this.opt);
		this.getdata(true);
		this.getReadStatistics();
  },
	onPullDownRefresh: function () {
		console.log('2025-01-03 22:55:53,567-INFO-[msglist][onPullDownRefresh_001] 下拉刷新开始');
		this.getdata(true);
		this.getReadStatistics();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
  },
  onReachBottom: function () {
		console.log('2025-01-03 22:55:53,568-INFO-[msglist][onReachBottom_001] 触底加载更多');
    if (!this.nomore && !this.nodata) {
      this.pagenum = this.pagenum + 1;
      this.getdata(false);
    }
  },
  methods: {
    getdata: function (refresh = false) {
			console.log('2025-01-03 22:55:53,569-INFO-[msglist][getdata_001] 开始获取数据', {refresh, pagenum: this.pagenum});
			if (refresh) {
				this.pagenum = 1;
				this.list = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
      var cid = that.cid;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
      app.post('ApiMessageNotify/getmsglist', {bid:that.bid,cid: cid,pagenum: pagenum,keyword:keyword, read_status: that.read_status}, function (res) {
				that.loading = false;
        var data = res.data;
        if (pagenum == 1) {
					that.listtype = res.listtype || 0;
					that.clist    = res.clist;
                    that.set      = res.set;
					if((res.clist).length > 0){
						var cnamelist = [];
						var cidlist = [];
						cnamelist.push('全部');
						cidlist.push('0');
						for(var i in that.clist){
							cnamelist.push(that.clist[i].name);
							cidlist.push(that.clist[i].id);
						}
						that.cnamelist = cnamelist;
						that.cidlist = cidlist;
					}

					uni.setNavigationBarTitle({
						title: res.title
					});
          that.list = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.list;
            var newdata = datalist.concat(data);
            that.list = newdata;
          }
        }
      });
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword
      that.getdata();
    },
    changetab: function (cid) {
      this.cid = cid;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    getReadStatistics() {
      console.log('2025-01-03 23:15:01,008-INFO-[msglist][getReadStatistics_001] 开始获取阅读统计');
      
      uni.request({
        url: this.$config.apiurl + '/api/message_notify/getReadStatistics',
        method: 'POST',
        success: (res) => {
          console.log('2025-01-03 23:15:01,009-INFO-[msglist][getReadStatistics_002] 统计响应：' + JSON.stringify(res.data));
          
          if (res.data.code === 1) {
            const stats = res.data.data;
            this.total_count = stats.total_count;
            this.read_count = stats.read_count;
            this.unread_count = stats.unread_count;
          }
        },
        fail: (err) => {
          console.log('2025-01-03 23:15:01,010-ERROR-[msglist][getReadStatistics_003] 统计请求失败：' + JSON.stringify(err));
        }
      });
    },
    filterByReadStatus(status) {
      console.log('2025-01-03 23:15:01,011-INFO-[msglist][filterByReadStatus_001] 筛选状态：' + status);
      
      if (this.read_status !== status) {
        this.read_status = status;
        this.getdata(true);
      }
    },
    markAllAsRead() {
      console.log('2025-01-03 23:15:01,012-INFO-[msglist][markAllAsRead_001] 开始批量标记已读');
      
      uni.showModal({
        title: '确认操作',
        content: '确定要将所有未读消息标记为已读吗？',
        success: (res) => {
          if (res.confirm) {
            uni.request({
              url: this.$config.apiurl + '/api/message_notify/markAllAsRead',
              method: 'POST',
              success: (res) => {
                console.log('2025-01-03 23:15:01,013-INFO-[msglist][markAllAsRead_002] 批量标记响应：' + JSON.stringify(res.data));
                
                if (res.data.code === 1) {
                  this.$refs.popmsg.show('标记成功');
                  this.getdata(true);
                  this.getReadStatistics();
                } else {
                  this.$refs.popmsg.show(res.data.msg || '标记失败');
                }
              },
              fail: (err) => {
                console.log('2025-01-03 23:15:01,014-ERROR-[msglist][markAllAsRead_003] 批量标记失败：' + JSON.stringify(err));
                this.$refs.popmsg.show('网络请求失败');
              }
            });
          }
        }
      });
    },
    searchMessages() {
      console.log('2025-01-03 23:15:01,015-INFO-[msglist][searchMessages_001] 搜索关键词：' + this.keyword);
      this.getdata(true);
    },
    tabchange(index) {
      console.log('2025-01-03 23:15:01,016-INFO-[msglist][tabchange_001] 切换分类：' + index);
      this.current = index;
      this.getdata(true);
    },
    goDetail(item) {
      console.log('2025-01-03 23:15:01,017-INFO-[msglist][goDetail_001] 跳转详情，id=' + item.id);
      uni.navigateTo({
        url: '/pagesExb/message/detail?id=' + item.id
      });
    }
  }
};
</script>
<style>
page {
	background: #f6f6f7;
}

/* 顶部导航栏 */
.nav-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: #fff;
	border-bottom: 1rpx solid #e5e5e5;
}

.nav-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.nav-actions {
	display: flex;
	align-items: center;
}

.unread-count {
	background: #ff4757;
	color: #fff;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	margin-right: 20rpx;
	min-width: 40rpx;
	text-align: center;
}

.action-btn {
	padding: 10rpx 20rpx;
	background: #007aff;
	color: #fff;
	border-radius: 20rpx;
	font-size: 26rpx;
}

/* 搜索容器 */
.search-container {
	padding: 20rpx 30rpx;
	background: #fff;
	border-bottom: 1rpx solid #e5e5e5;
}

.search-box {
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 30rpx;
	padding: 15rpx 20rpx;
}

.search-icon {
	width: 30rpx;
	height: 30rpx;
	margin-right: 15rpx;
}

.search-box input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	background: transparent;
	border: none;
}

/* 筛选标签 */
.filter-tabs {
	display: flex;
	padding: 20rpx 30rpx;
	background: #fff;
	border-bottom: 1rpx solid #e5e5e5;
}

.tab-item {
	padding: 12rpx 24rpx;
	margin-right: 20rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	color: #666;
	background: #f0f0f0;
	position: relative;
}

.tab-item.active {
	background: #007aff;
	color: #fff;
}

.tab-count {
	font-size: 24rpx;
	opacity: 0.8;
}

/* 消息列表 - 微信聊天样式 */
.message-list {
	background: #fff;
}

.chat-item {
	display: flex;
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;
	background: #fff;
}

.chat-item:active {
	background: #f5f5f5;
}

/* 左侧头像区域 */
.avatar-container {
	position: relative;
	margin-right: 25rpx;
	flex-shrink: 0;
}

.message-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 12rpx;
	background: #f0f0f0;
}

/* 未读红点 */
.red-dot {
	position: absolute;
	top: -5rpx;
	right: -5rpx;
	width: 20rpx;
	height: 20rpx;
	background: #ff4757;
	border-radius: 50%;
	border: 2rpx solid #fff;
}

/* 右侧内容区域 */
.content-container {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-height: 100rpx;
}

.content-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 10rpx;
}

.message-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	flex: 1;
	margin-right: 20rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.message-time {
	font-size: 24rpx;
	color: #999;
	flex-shrink: 0;
}

.content-body {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}

.message-preview {
	font-size: 28rpx;
	color: #666;
	flex: 1;
	margin-right: 20rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	line-height: 1.4;
}

.message-status {
	display: flex;
	align-items: center;
	flex-shrink: 0;
}

.unread-indicator {
	color: #ff4757;
	font-size: 20rpx;
	margin-right: 10rpx;
}

.read-count {
	font-size: 24rpx;
	color: #999;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 60rpx;
	background: #fff;
}

.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 40rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 加载状态 */
.loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 80rpx;
	background: #fff;
	font-size: 28rpx;
	color: #999;
}

/* 加载更多 */
.load-more {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx;
	background: #fff;
	font-size: 26rpx;
	color: #999;
}

/* 没有更多 */
.no-more {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx;
	background: #fff;
	font-size: 26rpx;
	color: #ccc;
}

/* 兼容旧样式 */
.topsearch{width:100%;padding:20rpx 20rpx;background:#fff}
.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1;overflow:hidden}
.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;background-color:#f5f5f5;}

.message_list{padding:10rpx 16rpx;background:#f6f6f7;margin-top:6rpx;}
.message_list .message-item1 {width:100%;display: inline-block;position: relative;margin-bottom:16rpx;background: #fff;border-radius:12rpx;overflow:hidden}
.message_list .message-item1 .message-pic {width:100%;height:auto;overflow:hidden;background: #ffffff;}
.message_list .message-item1 .message-pic .image{width: 100%;height:auto}
.message_list .message-item1 .message-info {padding:10rpx 20rpx 20rpx 20rpx;}
.message_list .message-item1 .message-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.message_list .message-item1 .message-info .p2{flex-grow:0;flex-shrink:0;display:flex;padding:10rpx 0;font-size:24rpx;color:#a88;overflow:hidden}

.message_list .message-item2 {width: 49%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}
.message_list .message-item2 .message-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}
.message_list .message-item2 .message-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.message_list .message-item2 .message-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}
.message_list .message-item2 .message-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.message_list .message-item2 .message-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}

.message_list .message-itemlist {width:100%;display: inline-block;position: relative;margin-bottom:12rpx;padding:12rpx;background: #fff;display:flex;border-radius:8rpx;}
.message_list .message-itemlist .message-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 25%;position: relative;}
.message_list .message-itemlist .message-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.message_list .message-itemlist .message-info {width: 65%;height:auto;overflow:hidden;padding:0 20rpx;display:flex;flex-direction:column;justify-content:space-between}
.message_list .message-itemlist .message-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:92rpx}
.message_list .message-itemlist .message-info .p2{display:flex;flex-grow:0;flex-shrink:0;font-size:24rpx;color:#a88;overflow:hidden;padding-bottom:6rpx}

.message_list .message-item3 {width: 32%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}
.message_list .message-item3 .message-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}
.message_list .message-item3 .message-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.message_list .message-item3 .message-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}
.message_list .message-item3 .message-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.message_list .message-item3 .message-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}

.p3{color:#8c8c8c;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
</style> 