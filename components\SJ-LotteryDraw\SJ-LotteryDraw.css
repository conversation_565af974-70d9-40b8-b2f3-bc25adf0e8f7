@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
li {
	list-style: none;
}

ul,
li {
	margin: 0;
	padding: 0;
}

.grid_wrap {
	width: 580upx;
	height: 580upx;
	position: relative;
	border-radius: 12upx;
	/* box-shadow: 0upx 12upx 0upx 0upx #89bbf7; */
}

.grid_wrap .lottery_wrap_border {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.grid_wrap .lottery_wrap_border ul {
	position: absolute;
	display: flex;
	align-items: center;
	justify-content: center;
}

.grid_wrap .lottery_wrap_border ul li {
	border-radius: 50%;
	width: 20upx;
	height: 20upx;
	background-color: #bce0e9;
}

.grid_wrap .lottery_wrap_border ul li:nth-child(even) {
	width: 24upx;
	height: 24upx;
	background-color: #f5fbc8;
}

.grid_wrap .lottery_wrap_border ul:nth-child(odd) {
	width: 100%;
	height: 35upx;
	left: 0;
	right: 0;
	flex-direction: row;
}

.grid_wrap .lottery_wrap_border ul:nth-child(odd) li {
	margin: 0 10upx;
}

.grid_wrap .lottery_wrap_border ul:nth-child(even) {
	width: 35upx;
	height: 100%;
	top: 0;
	bottom: 0;
	flex-direction: column;
}

.grid_wrap .lottery_wrap_border ul:nth-child(even) li {
	margin: 10upx 0;
}

.grid_wrap .lottery_wrap_border ul:nth-child(3) {
	bottom: 0;
}

.grid_wrap .lottery_wrap_border ul:nth-child(4) {
	right: 0;
}

.grid_wrap .lottery_wrap_border ul:nth-child(1) li:nth-child(odd),
.grid_wrap .lottery_wrap_border ul:nth-child(4) li:nth-child(odd) {
	animation: blink_large 1s linear infinite;
}

.grid_wrap .lottery_wrap_border ul:nth-child(1) li:nth-child(even),
.grid_wrap .lottery_wrap_border ul:nth-child(4) li:nth-child(even) {
	animation: blink_small 1s linear infinite;
}

.grid_wrap .lottery_wrap_border ul:nth-child(3) li:nth-child(even),
.grid_wrap .lottery_wrap_border ul:nth-child(2) li:nth-child(even) {
	width: 17upx;
	height: 17upx;
	background-color: #bce0e9;
	animation: blink_large 1s linear infinite;
}

.grid_wrap .lottery_wrap_border ul:nth-child(3) li:nth-child(odd),
.grid_wrap .lottery_wrap_border ul:nth-child(2) li:nth-child(odd) {
	width: 24upx;
	height: 24upx;
	background-color: #f5fbc8;
	animation: blink_small 1s linear infinite;
}

.grid_wrap .lottery_wrap {
	width: 580upx;
	height: 580upx;
	font-size: 14upx;
	background-color: #61a2fc;
	border-radius: 12upx;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 1;
}

.grid_wrap .lottery_wrap .lottery_grid {
	width: 100%;
	height: 100%;
	position: relative;
}

.grid_wrap .lottery_wrap .lottery_grid li {
	width: 146.6666666667upx;
	height: 146.6666666667upx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	float: left;
	position: absolute;
	background-color: #ffffff;
	border-radius: 12upx;
	box-shadow: 0upx 8upx 0upx 0upx #9cd2ff;
	color: #708abf;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-size: 20upx;
}

.grid_wrap .lottery_wrap .lottery_grid li .grid_img {
	width: 80upx;
	height: 80upx;
}

.grid_wrap .lottery_wrap .lottery_grid .active {
	background: #ff3a59;
	box-shadow: 0upx 8upx 0upx 0upx #ea0125;
	color: #ffffff;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(1) {
	left: 20upx;
	top: 20upx;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(2) {
	left: 166.6666666667upx;
	top: 20upx;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(3) {
	left: 313.3333333333upx;
	top: 20upx;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(4) {
	left: 313.3333333333upx;
	top: 166.6666666667upx;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(5) {
	left: 313.3333333333upx;
	top: 313.3333333333upx;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(6) {
	left: 166.6666666667upx;
	top: 313.3333333333upx;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(7) {
	left: 20upx;
	top: 313.3333333333upx;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(8) {
	left: 20upx;
	top: 166.6666666667upx;
}

.grid_wrap .lottery_wrap .lottery_grid li:nth-of-type(9) {
	left: 166.6666666667upx;
	top: 166.6666666667upx;
	cursor: pointer;
	background: #ff3a59;
	box-shadow: 0upx 8upx 0upx 0upx #ea0125;
	color: #ffffff;
	font-size: 40upx;
	font-weight: bolder;
}

@keyframes blink_large {
	to {
		width: 24upx;
		height: 24upx;
		background-color: #f5fbc8;
	}
}

@keyframes blink_small {
	to {
		width: 17upx;
		height: 17upx;
		background-color: #bce0e9;
	}
}

/*# sourceMappingURL=SJ-LotteryDraw.css.map */