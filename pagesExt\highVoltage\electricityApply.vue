<template>
	<view class="content">
		<view class="header" style="height: 260rpx">
			<view class="top">
				<view class="title-box">
					<view class="title">高压办电</view>
					<view>帮代办、少跑腿</view>
				</view>
				<image src="@/static/highVoltage/icon1.png" class="logo"></image>
			</view>
		</view>
		<view class="form">
			<view class="form-item box" v-for="(value, key) in formdata" :key="key">
				<view class="title">{{ value[0] }}</view>
				<view class="value">{{ value[1] }}</view>
			</view>
			<view class="form-item box">
				<view class="title">施工单位</view>
				<input class="input" placeholder="请输入" v-model="formData.shigongdanwei" />
			</view>
			<view class="form-item box">
				<view class="title">报验内容</view>
				<textarea class="textarea input" v-model="formData.baoyanneirong" placeholder="请输入" />
			</view>
		</view>
		<view class="btn" @click="commitSend">申请送电</view>
		<view class="btn back-i" @click="back">返回</view>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			formdata: [],
			// 申请送电表单
			formData: {},
			delta: 1,
			id: null
		};
	},
	onLoad({ id }) {
		this.id = id;
		this.getDetail();
	},
	methods: {
		getDetail() {
			/**
			 * @method  获取详情
			 */
			const _this = this;
			app.post(
				'ApiShenqingbandian/orderdetail',
				{
					id: this.id
				},
				({ detail }) => {
					_this.formdata = detail.formdata;
					uni.stopPullDownRefresh();
				}
			);
		},
		back() {
			/**
			 * @method 返回上一页
			 */
			uni.navigateBack();
		},
		commitSend() {
			/**
			 * @method 申请送电
			 */
			const _this = this;
			app.post('ApiShenqingbandian/orderCollect', this.formData, ({ msg }) => {
				uni.showToast({
					title: msg,
					icon: 'none'
				});
				_this.getDetail();
			});
		}
	}
};
</script>

<style lang="scss">
@import 'styled.scss';
</style>
