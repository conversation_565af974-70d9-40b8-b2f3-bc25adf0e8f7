<template>
<view class="dp-article" :style="{
	backgroundColor:params.bgcolor,margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
}">

<!-- <view :style="{fontSize: 30}">带货笔记</view> -->
	<dp-daihuobiji-waterfall :list="data" ref="waterfall" :showtime="params.showtime" :showreadcount="params.showreadcount" idKey="id" imageSrcKey="coverimg"></dp-daihuobiji-waterfall>
	<!-- <view class="article-item2 daihuobiji-item" v-for="(item,index) in data" :style="{marginRight:index%2==0?'2%':'0'}" :key="item.id" @click="goto" :data-url="'/pages/article/detail?id='+item.artid">
			<image class="cover-img" v-if="item.coverimg"  :src="item.coverimg" mode="widthFix"/>
		<view class="article-info daihuobiji-info">
			<rich-text class="item-content" :nodes="item.content"></rich-text>
			<view class="item-user-info">
				<view class="user-info">
					<view class="user-avatar"></view>
					<text class="user-name">{{item.nickname}}</text>
				</view>
				<view class="read-info">
					<view class="read-num">{{item.readcount}}</view>
				</view>
			</view>
			
		</view>
	</view> -->
	
    <!--单排三图e-->
	
</view>
</template>
<script>
	export default {
		props: {
			params:{},
			data:{
				type: Array,
				default: () => []
			}
		}
	}
</script>
<style>
.dp-article{height: auto; position: relative;overflow: hidden; padding:10rpx 0px; background: #fff;}
.dp-article .article-item1 {width: 100%;display: inline-block;position: relative;margin-bottom:12rpx;background: #fff;border-radius:12rpx;overflow:hidden}
.dp-article .article-item1 .article-pic {width:100%;height:auto;overflow:hidden;background: #ffffff;}
.dp-article .article-item1 .article-pic .image{width: 100%;height:auto}
.dp-article .article-item1 .article-info {padding:10rpx 20rpx;}
.dp-article .article-item1 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.dp-article .article-item1 .article-info .t1{word-break: break-all;text-overflow: ellipsis;overflow: hidden;display: block;font-size: 32rpx;}
.dp-article .article-item1 .article-info .t2{word-break: break-all;text-overflow: ellipsis;padding-top:4rpx;overflow:hidden;}
.dp-article .article-item1 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;padding:10rpx 0;font-size:24rpx;color:#a88;overflow:hidden}
.dp-article .article-item1 .article-info .p3{color:#8c8c8c;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;max-height:92rpx}

.dp-article .article-item2 {width: 49%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}
/*.article-item2:nth-child(even){margin-right:2%}*/
.dp-article .article-item2 .article-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}
.dp-article .article-item2 .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-article .article-item2 .article-info {padding:10rpx 20rpx;display:flex;flex-direction:column;}
.dp-article .article-item2 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.dp-article .article-item2 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}

.dp-article .article-itemlist {width:100%;display: inline-block;position: relative;margin-bottom:12rpx;padding:12rpx;background: #fff;display:flex;border-radius:8rpx;}
.dp-article .article-itemlist .article-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 25%;position: relative;}
.dp-article .article-itemlist .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-article .article-itemlist .article-info {width: 65%;height:auto;overflow:hidden;padding:0 20rpx;display:flex;flex-direction:column;justify-content:space-between}
.dp-article .article-itemlist .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:92rpx}
.dp-article .article-itemlist .article-info .p2{display:flex;flex-grow:0;flex-shrink:0;font-size:24rpx;color:#a88;overflow:hidden}


.dp-article .article-waterfall-info{padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}
.dp-article .article-waterfall-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.dp-article .article-waterfall-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}

.dp-article .article-item3 {width: 32%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}
/*.article-item3:nth-child(even){margin-right:2%}*/
.dp-article .article-item3 .article-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}
.dp-article .article-item3 .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-article .article-item3 .article-info {padding:10rpx 20rpx;display:flex;flex-direction:column;}
.dp-article .article-item3 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.dp-article .article-item3 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}

.p3{color:#8c8c8c;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
</style>

<style lang="scss" scoped>
	.daihuobiji-item{
		.cover-img {
			width: 100%;
		}
		
		.daihuobiji-info {
			.item-content{
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
				line-height: 33rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin-top: 20rpx;
				margin-bottom: 22rpx;
			}
			.item-user-info {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				
				.user-info {
					display: flex;
					flex-direction: row;
					justify-content: flex-start;
					align-items: center;
						
					.user-avatar {
						width: 32rpx;
						height: 32rpx;
						background: #D9D9D9;
						border-radius: 100%;
						overflow: hidden;
						margin-right: 6rpx;
					}
					.user-name{
						font-family: MiSans VF, MiSans VF;
						font-weight: 400;
						font-size: 22rpx;
						color: #333333;
						line-height: 26rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
				.read-info{
					display: flex;
					flex-direction: row;
					justify-content: flex-end;
					align-items: center;
					.read-num {
						font-family: MiSans VF, MiSans VF;
						font-weight: 400;
						font-size: 22rpx;
						color: #333333;
						line-height: 26rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
			}
		}
		
	}

</style>