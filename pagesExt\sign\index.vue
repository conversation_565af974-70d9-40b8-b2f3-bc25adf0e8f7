<template> 
  <view class="container">
    <block v-if="isload">
      <view class="qd_head" v-if="style == 1">
        <block v-if="signset.bgpic">
          <image :src="signset.bgpic" class="qdbg"></image>
        </block>
        <block v-else>
          <image :src="pre_url + '/static/img/sign-bg.png'" class="qdbg"></image>
        </block>

        <view class="myscore">
          <!-- <view class="f2">{{userinfo.score}}{{t('积分')}}</view> -->
          <view class="f2">
            <view class="f2">今日签到分红:{{userinfo.qiandaofh}}</view>
          </view>
          <view class="f2">
            <view class="f2">签到分红总计:{{userinfo.qiandaocount}}</view>
          </view>
        </view>
        <view class="signlog" @tap="goto" data-url="signrecord">签到记录</view>

        <view class="signbtn" v-if="!hassign">
          <button class="btn" :style="{background:t('color1')}" @tap="signin">立即签到</button>
        </view>
        <view class="signbtn" v-else>
          <button class="btn2">今日已签到</button>
          <view class="signtip">已连续签到{{userinfo.signtimeslx}}天</view>
        </view>
      </view>
      <view class="qd_head qd_head2" v-if="style == 2" :style="'background-image:url(' + signset.bgpic + ');'">
        <!-- <view class="myscore"><view class="f1">{{userinfo.score}}</view><view class="f2">{{t('积分')}}</view></view> -->
        <view class="signlog" @tap="goto" data-url="signrecord">签到记录</view>

        <view class="signbtn" v-if="!hassign">
          <button class="btn" :style="{background:t('color1')}" @tap="signin">立即签到</button>
          <view class="signtip">当前共{{userinfo.score}}{{t('积分')}}</view>
        </view>
        <view class="signbtn" v-else>
          <button class="btn2">今日已签到</button>
          <view class="signtip">已连续签到{{userinfo.signtimeslx}}天，共{{userinfo.score}}{{t('积分')}}</view>
        </view>
        <view class="calendar">
          <uni-calendar 
            :insert="true"
            :lunar="false" 
            :start-date="start_date"
            :end-date="end_date"
            :selected='selectedDate'
            :showMonth="false"
            :backColor= "t('color1')"
            :fontColor= "'#fff'"
          />
        </view>
        <!-- [{date: '2021-11-02', info: '已签'}] -->
      </view>
      
      <view class="qd_guize">
        <view class="gztitle"> — 签到排名 — </view>
        <view class="paiming">
          <view v-for="(item, index) in list" :key="index" class="item flex">
            <view class="f1">
              <text class="t1">{{item.nickname}}</text>
            </view>
            <view class="f2">
              <text class="t2">连续签到</text>
              <text class="t1">{{item.signtimeslx}}</text>
              <text class="t2">天</text>
            </view>
          </view>
        </view>
        <view class="btn-a" @tap="getPaiming" v-if="!nomore && list && list.length >=10" :style="{color:t('color1')}">查看更多</view>
        <nomore v-if="nomore"></nomore>
      </view>
      
      <view class="qd_guize">
        <view class="gztitle"> — 签到规则 — </view>
        <view class="guize_txt">
          <parse :content="signset.guize" />
        </view>
      </view>
      
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
let videoAd = null;
var app = getApp();
import uniCalendar from './uni-calendar/uni-calendar.vue';
export default {
  components: {
    uniCalendar
  },
  data() {
    return {
      opt:{},
      loading:false,
      isload: false,
      menuindex:-1,
      pre_url:app.globalData.pre_url,
      hassign:false,
      signset:{},
      userinfo:{},
      list: [],
      display:false,
      style:1,
      start_date:'',
      end_date:'',
      selectedDate:[], //{date: '2022-08-02', info: '已签'}
      nomore:false,
      nodata:false,
      pagenum:1,
      isdoing:false,
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  methods: {
    getdata: function () {
      var that = this;
      that.loading = true;
      app.get('ApiSign/index', {}, function (res) {
        that.loading = false;
        that.hassign = res.hassign;
        that.signset = res.signset;
        that.userinfo = res.userinfo;
        that.list = res.list || []; // 确保 list 为数组
        that.display = res.signset.display;
        that.style = res.signset.style;
        that.end_date = res.today;
        that.selectedDate = res.selectedDate;
        that.loaded();
        if (app.globalData.platform == 'wx' && res.signset.rewardedvideoad && !videoAd && wx.createRewardedVideoAd) {
          videoAd = wx.createRewardedVideoAd({
            adUnitId: res.signset.rewardedvideoad
          });
          videoAd.onLoad(() => {})
          videoAd.onError((err) => {})
          videoAd.onClose(res2 => {
            that.isdoing = false;
            if (res2 && res2.isEnded) {
              that.confirmsign();
            } else {
              // 用户关闭了广告
            }
          });
        }
      });
    },
    signin: function () {
      var that = this;
      if (that.isdoing) return;
      that.isdoing = true;
      if (app.globalData.platform == 'wx' && that.signset.rewardedvideoad && videoAd) {
        videoAd.show().catch(() => {
          videoAd.load()
          .then(() => videoAd.show())
          .catch(err => {
            that.confirmsign();
          });
        });
      } else {
        that.confirmsign();
      }
    },
    confirmsign() {
      var that = this;
      app.post('ApiSign/signin', {}, function (data) {
        if (data.status == 1) {
          app.success('+' + data.scoreadd + that.t('积分'));
          that.getdata();
        } else {
          app.alert(data.msg);
        }
        that.isdoing = false;
      });
    },
    getPaiming: function () {
      var that = this;
      if (!this.nodata && !this.nomore) {
        this.pagenum = this.pagenum + 1;
        this.loading = true;
        app.post('ApiSign/getPaiming', { pagenum: this.pagenum }, function (res) {
          that.loading = false;
          var datalist = res.datalist || []; // 确保 datalist 为数组
          if (that.pagenum == 1) {
            that.list = datalist;
            if (datalist.length == 0) {
              that.nodata = true;
            }
          } else {
            if (datalist.length == 0) {
              that.nomore = true;
            } else {
              var list = that.list || [];
              var newdata = list.concat(datalist);
              that.list = newdata;
            }
          }
        });
      }
    },
  }
};
</script>

<style>
page{background:#f4f4f4}
.qd_head{width: 100%;/* height:940rpx; */position: relative;}
.qdbg{width: 100%;height:940rpx;}
.myscore{position:absolute;top:60rpx;width:100%;display:flex;color:#fff;flex-direction:column;align-items:center;z-index:2}
.myscore .f1{font-size:56rpx;font-weight:bold}
.myscore .f2{font-size:32rpx}
.signlog{position:absolute;top:50rpx;right:20rpx;color:#fff;font-size:28rpx;z-index:2}
.qd_head .signbtn{position:absolute;top:760rpx;width:100%;display:flex;flex-direction:column;align-items:center;z-index:2}
.qd_head .signbtn .btn{width:440rpx;height:80rpx;border-radius:40rpx;font-size:32rpx;font-weight:bold;color:#fff}
.qd_head .signbtn .btn2{width:440rpx;height:80rpx;background:#FCB0B0;border-radius:40rpx;font-size:32rpx;font-weight:bold;color:#fff}
.qd_head .signbtn .signtip{color:#999999;margin-top:16rpx}
.btn-a { text-align: center;margin-top: 18rpx;}

.qd_head2 { padding-bottom: 20rpx;background-position: center;background-repeat: no-repeat;background-size:cover;}
.qd_head2 .calendar { padding-top: 300rpx;width: 96%;margin: 0 2%;}
.qd_head2 .signbtn {/* position:initial;*/top: 136rpx;}
.qd_head2 .signbtn .signtip {color: #fff;}

.qd_guize{width:100%;margin:0;padding-bottom:20rpx}
.qd_guize .gztitle{width:100%;text-align:center;font-size:32rpx;color:#656565;font-weight:bold;height:100rpx;line-height:100rpx}
.guize_txt{box-sizing: border-box;padding:0 30rpx;line-height:42rpx;}
.paiming{ width:94%;margin:0 3%;background:#fff;border-radius:10px;padding:20rpx 20rpx;}
.paiming .item{ line-height: 80rpx;border-bottom: 1px dashed #eee;}
.paiming .item:last-child{border:0}
.paiming .item .f1{flex:1;display:flex;flex-direction:column}
.paiming .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}
.paiming .item .f1 .t2{color:#666666}
.paiming .item .f1 .t3{color:#666666}
.paiming .item .f2{ flex:1;text-align:right;font-size:30rpx;}
.paiming .item .f2 .t1{color:#03bc01}
.paiming .item .f2 .t2{color:#000000}
</style>
