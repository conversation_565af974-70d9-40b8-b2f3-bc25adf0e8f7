<template>
  <view class="container">
    <block v-if="isload">
     <!-- <dd-tab 
        :itemdata="['我的利润']" 
        :itemst="['all']" 
        :st="st" 
        :isfixed="true" 
        @changetab="changetab">
      </dd-tab> -->

      <!-- 显示总利润 -->
      <view class="total-profit">
        <text>总利润：￥{{ lirun }} 元</text>
      </view>

      <!-- 订单列表展示 -->
      <view class="order-content">
        <block v-for="(item, index) in datalist" :key="index">
          <view class="order-box">
            <view class="order-head">
              <view class="profit-info">
                <text class="profit-text">商品名称：{{ item.name }}</text>
              </view>
            </view>

            <!-- 订单详情 -->
            <view class="order-content-details">
              <image :src="item.pic" class="product-image"></image>
              <view class="detail-info">
                <text class="detail-name">{{ item.name }}</text>
                <text class="detail-profit">订单利润：￥{{ item.lirun }}</text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="order-operation">
              <view v-if="item.status2 == 0" @tap.stop="goto" :data-url="'buyweituo?id=' + item.id" class="btn-entrust">委托上架</view>
            </view>
          </view>
        </block>
      </view>

      <nomore v-if="nomore"></nomore>
      <nodata v-if="nodata"></nodata>
    </block>

    <!-- 底部加载状态 -->
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>
<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false, // 页面加载标志
      menuindex: -1,
      st: 'all',
      datalist: [],
      pagenum: 1,
      nomore: false,
      nodata: false,
      codtxt: "",
      canrefund: 1,
      express_content: '',
      selectExpressShow: false,
      hexiao_qr: '',
      keyword: '',
      lirun: 0, // 总利润
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    if (this.opt && this.opt.st) {
      this.st = this.opt.st;
    }
    this.getdata(); // 加载数据
  },

  onPullDownRefresh: function () {
    this.getdata(); // 下拉刷新
  },

  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true); // 加载更多数据
    }
  },

  methods: {
    // 获取数据
    getdata: function (loadmore) {
      var that = this;

      // 重置或加载更多数据
      if (!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
      }
      var pagenum = that.pagenum;
      var st = that.st;

      // 重置状态
      that.nodata = false;
      that.nomore = false;
      that.loading = true;

      // 调用 API
      app.post('ApiMiaosha/mylirun', { st: st, pagenum: pagenum, keyword: that.keyword }, function (res) {
        that.loading = false;

        // 检查数据是否返回成功
        if (res && res.datalist) {
          var data = res.datalist;
          that.lirun = res.total_lirun || 0; // 获取总利润，默认值为0

          if (pagenum == 1) {
            that.datalist = data;

            // 如果数据为空，显示“无数据”提示
            if (data.length == 0) {
              that.nodata = true;
            }

            // 页面已加载，设置 isload 为 true
            that.isload = true;
          } else {
            // 如果没有更多数据
            if (data.length == 0) {
              that.nomore = true;
            } else {
              // 合并更多数据
              that.datalist = that.datalist.concat(data);
            }
          }
        } else {
          // 处理返回数据为空的情况
          that.nodata = true;
        }
      });
    },

    // 切换 Tab
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata(); // 切换 Tab 后重新加载数据
    },

    // 搜索确认
    searchConfirm: function (e) {
      this.keyword = e.detail.value;
      this.getdata(false); // 执行搜索
    }
  }
};
</script>


<style>
.container {
  width: 100%;
  background-color: #f8f8f8;
}
.total-profit {
  background-color: #ffffff;
  padding: 20rpx;
  margin: 20rpx 0;
  text-align: center;
  font-size: 36rpx !important;
  color: #ff4246;
  font-weight: bold;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  z-index: 100;  /* 增加层级，确保不被遮挡 */
  margin-top: 60rpx; /* 为其增加足够的上边距 */
}


.order-content {
  display: flex;
  flex-direction: column;
}

.order-box {
  width: 94%;
  margin: 10rpx 3%;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.order-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.profit-info {
  font-size: 26rpx;
  color: #666;
}

.order-content-details {
  display: flex;
  padding: 16rpx 0;
  border-top: 1px solid #f4f4f4;
}

.product-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  margin-right: 14rpx;
}

.detail-info {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  flex: 1;
}

.detail-name {
  font-size: 28rpx;
  color: #333;
}

.detail-profit {
  font-size: 26rpx;
  color: #ff4246;
}

.order-operation {
  display: flex;
  justify-content: flex-end;
  padding-top: 10rpx;
  border-top: 1px solid #f4f4f4;
}

.btn-entrust {
  background-color: #ff8758;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 5rpx;
  font-size: 26rpx;
  text-align: center;
}
.total-profit {
  background-color: #ffffff;  /* 白色背景 */
  padding: 20rpx;             /* 内边距 */
  margin: 20rpx 0;            /* 上下外边距 */
  text-align: center;         /* 居中对齐 */
  font-size: 36rpx;           /* 字体大小 */
  color: #ff4246;             /* 红色字体 */
  font-weight: bold;          /* 字体加粗 */
  border-radius: 10rpx;       /* 圆角效果 */
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 阴影效果 */
}

</style>