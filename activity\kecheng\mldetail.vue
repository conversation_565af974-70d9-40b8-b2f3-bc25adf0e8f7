<template>
<view class="wrap">
	<block v-if="isload">
		<block v-if="detail.kctype==1">
			<view class="title">{{detail.name}}</view>
			<dp :pagecontent="pagecontent"></dp>
			<view style="margin-bottom: 40rpx;"></view>
		</block>
		<view class="audo-video" v-if="detail.kctype==2">
			<view class="audoimg"><image :src="detail.pic"/></view>
			<view class="play">
				<view class="play-left">
					<image src="/static/img/video_icon.png" v-show="playshow" @tap="play"></image>   
			    <image src="/static/img/play.png" v-show="!playshow" @tap="pauseaudio"></image> 
			    <text>{{nowtime}}</text>
			  </view>
			  <view class="play-right">
					<slider @change="sliderChange"  @changing="sliderChanging" class="slider" block-size="16"  :min="0" :max="time"  :value="currentTime" activeColor="#595959"  />
			  </view>
				<view class="play-end"><text>{{duration}}</text></view>
				<view class="audio-speed-btn" @tap="showSpeedMenu = true">
					<text class="speed-text">{{currentSpeed}}x</text>
				</view>
			</view>
		</view>
		<view class="videobox" v-if="detail.kctype==3" :class="{'video-sticky': isVideoSticky}">
			<video  
				class="video" 
				id="video" 
				:autoplay="true" 
				:src="detail.video_url"   
				:initial-time="detail.startTime" 
				@pause="pause" 
				@timeupdate="timeupdate" 
				@ended="ended">
			</video>
			<view class="speed-control-btn" @tap="showSpeedMenu = true">
				<text class="speed-text">{{currentSpeed}}x</text>
			</view>
		</view>
		<view class="video-placeholder" v-if="detail.kctype==3 && isVideoSticky" :style="{height: videoHeight + 'px'}"></view>
		<view style=" height: 30rpx; width: 100%; background-color: #f5f5f5;"></view>
		
		<!-- 添加笔记功能区域 -->
		<view v-if="kechengset && kechengset.enable_notes == 1 && (detail.ispay == 1 || (kechengset.notes_need_buy == 0))" class="note-section">
			<!-- 添加记笔记浮动按钮 -->
			<view class="note-button" @tap="showNoteDialog" :style="{background:t('color1')}">
				<text>记笔记</text>
			</view>
		</view>
		
		<view class="content_box">
			<view class="title flex">
				<view class="t1">课程目录</view>
				<view class="t2" v-if="detail.isdt==1 && detail.count>=detail.kccount && iskaoshi!=1" @tap.stop="goto" :data-url="'tiku?id=' + detail.kcid" data-opentype="redirect">去答题</view>
				<view class="t2" v-if="iskaoshi==1" @tap.stop="goto" :data-url="'recordlog?kcid=' + detail.kcid">答题记录</view>
			</view>
			<view class="current-learning-card" v-if="currentChapter">
				<view class="card-header">
					<text class="card-title">正在学习</text>
					<text class="progress-text">学习进度: {{currentChapter.jindu || 0}}{{currentChapter.kctype==1 && (currentChapter.jindu||0)!='100'?'':'%'}}</text>
				</view>
				<view class="card-content" @tap="scrollToCurrentChapter">
					<view class="chapter-info">
						<image v-if="currentChapter.kctype==1" src="/static/img/tw_icon.png" class="chapter-icon" /> 
						<image v-if="currentChapter.kctype==2" src="/static/img/mp3_icon.png" class="chapter-icon" />
						<image v-if="currentChapter.kctype==3" src="/static/img/video_icon.png" class="chapter-icon" /> 
						<view class="chapter-text">
							<view class="chapter-name">{{currentChapter.name}}</view>
							<view class="chapter-type">
								<text v-if="currentChapter.kctype==1" class="type-tag">图文课程</text>
								<text v-if="currentChapter.kctype==2" class="type-tag">音频课程</text>
								<text v-if="currentChapter.kctype==3" class="type-tag">视频课程</text>
								<text v-if="currentChapter.video_duration>0" class="duration-tag">时长: {{currentChapter.duration}}</text>
							</view>
						</view>
					</view>
					<view class="continue-btn">继续学习</view>
				</view>
			</view>
			<view class="mulubox flex" v-for="(item, index) in datalist" :key="index" :id="'chapter-' + item.id">
				<view class="left_box">
					<image v-if="item.kctype==1" src="/static/img/tw_icon.png" /> 
					<image v-if="item.kctype==2" src="/static/img/mp3_icon.png" />
					<image v-if="item.kctype==3" src="/static/img/video_icon.png" /> 
				</view>
				<view class="right_box flex">
					<view :class="'title_box '+ (item.id==detail.id?'current-chapter':'') + (item.jindu=='100'?' completed-chapter':'')"   @tap="todetail" :data-key='item.key' :data-mianfei='item.ismianfei' :data-url="'mldetail?id='+item.id+'&kcid='+item.kcid" :data-opentype="item.kctype==1 ? 'redirect' : 'redirect'">
						<view class="t1"> 
							<text v-if="item.id==detail.id" class="current-indicator">正在学习</text>
							{{item.name}}
						</view>
						<view> 
							<text  v-if="item.kctype==1"  class="t2">图文课程 </text>
							<text v-if="item.kctype==2"  class="t2">音频课程 </text>
							<text v-if="item.kctype==3"  class="t2">视频课程 </text>
							<text  class="t2" v-if="item.video_duration>0"> 时长: {{item.duration}}</text>
						</view>
					</view>
					<view class="jindu" v-if="item.jindu">{{item.jindu}}{{item.kctype==1 && item.jindu!='100'?'':'%'}}</view>
					<view class="skbtn" v-if="item.ismianfei && !item.jindu">试看</view>
				</view>		
			</view>
		</view>
	</block>
	<nomore text="没有更多课程了" v-if="nomore"></nomore>
	<nodata text="没有查找到相关课程" v-if="nodata"></nodata>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
	
	<!-- 笔记功能弹窗 -->
	<view v-if="noteDialogVisible" class="popup__container">
		<view class="popup__overlay" @tap.stop="hideNoteDialog"></view>
		<view class="popup__modal note-modal">
			<view class="popup__title">
				<text class="popup__title-text">我的课程笔记</text>
				<image :src="pre_url + '/static/img/close.png'" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hideNoteDialog"/>
			</view>
			<view class="popup__content">
				<view class="note-tabs">
					<view :class="'tab-item ' + (noteTabIndex == 1 ? 'active' : '')" @tap="switchNoteTab" :data-index="1">
						<text>添加笔记</text>
					</view>
					<view :class="'tab-item ' + (noteTabIndex == 2 ? 'active' : '')" @tap="switchNoteTab" :data-index="2">
						<text>我的笔记</text>
					</view>
				</view>
				
				<!-- 添加笔记页面 -->
				<view v-if="noteTabIndex == 1" class="add-note-content">
					<view class="form-group">
						<view class="form-label">笔记内容</view>
						<textarea 
							class="note-textarea" 
							v-model="noteForm.content" 
							placeholder="请输入您的学习笔记..."
							maxlength="1000"
							:show-count="true"
						></textarea>
					</view>
					<view class="form-group">
						<view class="form-label">时间点 (秒)</view>
						<input 
							class="note-input" 
							type="number" 
							v-model="noteForm.time_point" 
							placeholder="当前学习时间点"
						/>
					</view>
					<view class="form-group" v-if="kechengset && kechengset.notes_need_progress == 1">
						<view class="form-label">学习进度</view>
						<view class="progress-input">
							<slider 
								:value="noteForm.study_progress" 
								@change="onProgressChange"
								:min="0" 
								:max="100" 
								:step="1"
								:show-value="true"
								activeColor="#FF5347"
							/>
							<text class="progress-text">{{noteForm.study_progress}}%</text>
						</view>
					</view>
					<view class="form-actions">
						<button class="btn-cancel" @tap="resetNoteForm">重置</button>
						<button class="btn-submit" @tap="submitNote" :style="{background:t('color1')}">
							{{editingNoteId ? '更新笔记' : '保存笔记'}}
						</button>
					</view>
				</view>
				
				<!-- 我的笔记列表 -->
				<view v-if="noteTabIndex == 2" class="my-notes-content">
					<view class="notes-filter">
						<view class="filter-item">
							<text class="filter-label">筛选：</text>
							<picker @change="onFilterChange" :value="filterIndex" :range="filterOptions">
								<view class="picker-text">{{filterOptions[filterIndex]}}</view>
							</picker>
						</view>
					</view>
					
					<view class="notes-list" v-if="myNotesList.length > 0">
						<view class="note-item" v-for="(note, index) in myNotesList" :key="note.id">
							<view class="note-header">
								<view class="note-time">{{note.createtime_format}}</view>
								<view class="note-actions">
									<text class="action-btn edit-btn" @tap="editNote" :data-note="JSON.stringify(note)">编辑</text>
									<text class="action-btn delete-btn" @tap="deleteNote" :data-id="note.id">删除</text>
								</view>
							</view>
							<view class="note-content">{{note.content}}</view>
							<view class="note-info">
								<text class="note-progress" v-if="kechengset && kechengset.notes_need_progress == 1">学习进度: {{note.study_progress}}%</text>
								<text class="note-time-point" v-if="note.note_time">时间点: {{note.note_time}}秒</text>
								<text class="note-chapter" v-if="note.chapter_name">章节: {{note.chapter_name}}</text>
							</view>
						</view>
					</view>
					
					<view class="no-notes" v-else>
						<image :src="pre_url + '/static/img/empty.png'" class="empty-icon"/>
						<text class="empty-text">暂无笔记记录</text>
						<text class="empty-tip">开始学习并记录您的想法吧！</text>
					</view>
					
					<view class="load-more-notes" v-if="!noMoreNotes && myNotesList.length > 0" @tap="loadMoreNotes">
						<text>{{loadingNotes ? '加载中...' : '加载更多'}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
	
	<view class="speed-menu-mask" v-if="showSpeedMenu" @tap="showSpeedMenu = false">
		<view class="speed-menu" @tap.stop="">
			<view class="speed-menu-header">
				<text class="speed-title">播放倍速</text>
				<view class="close-btn" @tap="showSpeedMenu = false">×</view>
			</view>
			<view class="speed-options">
				<view 
					class="speed-option" 
					:class="{'active': speed === currentSpeed}" 
					v-for="speed in speedOptions" 
					:key="speed"
					@tap="selectSpeed(speed)"
				>
					<text class="speed-label">{{speed}}x</text>
					<view class="speed-desc">{{getSpeedDesc(speed)}}</view>
				</view>
			</view>
		</view>
	</view>
</view>
</template>

<script>
var app = getApp();
var interval = null;

export default {
	data() {
		return {
			loading:false,
			isload: false,
			isplay: 0,
			detail: [],
			datalist: [],
			pagecontent: "",
			playshow:true, //播放的图片
			stipshow:false, //暂停的图片
			lock: false, // 锁
			status: 1, // 1暂停 2播放
			currentTime: 0,  //当前进度
			duration: '', // 总进度
			videoContext: '',
			iskaoshi:'',
			pagenum:1,
			studlog:[],
			innerAudioContext: '',
			startTime:'',
			seek: false ,//是否处于拖动状态
			time:'',
			playJd:0,
			nowtime:'',
			isauto:false,
			isVideoSticky: false, // 视频是否吸顶
			videoHeight: 0, // 视频原始高度
			scrollTop: 0, // 当前滚动位置
			currentChapter: null, // 当前正在学习的章节信息
			showSpeedMenu: false,
			currentSpeed: 1,
			speedOptions: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
			noteDialogVisible: false,
			noteTabIndex: 1,
			noteForm: {
				content: '',
				time_point: '',
				study_progress: 0
			},
			filterIndex: 0,
			filterOptions: ['全部', '学习进度', '时间点', '章节'],
			myNotesList: [],
			loadingNotes: false,
			noMoreNotes: false,
			editingNoteId: null, // 正在编辑的笔记ID，为null表示新建笔记
			notesPageNum: 1, // 笔记列表当前页码
			// 课程配置信息
			kechengset: {
				enable_notes: 1, // 默认启用笔记功能，1为启用，0为禁用
				notes_need_progress: 1 // 默认启用学习进度，1为启用，0为禁用
			},
			pre_url: '' // 添加 pre_url 变量，用于图片路径
		};
	},
	computed: {
		getCurrentChapter() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][getCurrentChapter_001] 计算当前章节信息');
			if (this.datalist && this.detail.id) {
				return this.datalist.find(item => item.id === this.detail.id);
			}
			return null;
		}
	},
	onLoad: function (opt) {
		console.log('2025-01-14 22:55:53,565-INFO-[mldetail][onLoad_001] 页面加载开始，参数:', opt);
		this.opt = app.getopts(opt);
		
		// 重置倍速显示为默认值，确保每个章节都从正确的状态开始
		this.currentSpeed = 1;
		console.log('2025-01-14 22:55:53,565-INFO-[mldetail][onLoad_002] 重置倍速显示为默认值');
		
		this.getdata();
		this.getdatalist(); 
		this.innerAudioContext = uni.createInnerAudioContext();
		console.log('2025-01-14 22:55:53,565-INFO-[mldetail][onLoad_003] 音频上下文创建完成');
	},
	onShow:function(){
		console.log('2025-01-03 22:55:53,565-INFO-[mldetail][onShow_001] 页面显示');
		var that=this
		clearInterval(interval);
		this.innerAudioContext.stop();
	},
	onUnload: function () {
		console.log('2025-01-14 22:55:53,565-INFO-[mldetail][onUnload_001] 页面卸载，清理资源');
		clearInterval(interval);
		var that=this
		this.innerAudioContext.stop();
		// 移除滚动监听 - 小程序环境兼容性处理
		// #ifndef MP-WEIXIN
		if (typeof uni.offPageScroll === 'function') {
			uni.offPageScroll(this.onPageScroll);
		}
		// #endif
		// #ifdef MP-WEIXIN
		// 小程序环境不支持 uni.offPageScroll，无需调用
		console.log('2025-01-14 22:55:53,566-INFO-[mldetail][onUnload_002] 小程序环境，跳过 offPageScroll 调用');
		// #endif
	},
	onHide(){
		console.log('2025-01-03 22:55:53,565-INFO-[mldetail][onHide_001] 页面隐藏');
		this.playshow = false
	},
	onReady() {
		console.log('2025-01-03 22:55:53,565-INFO-[mldetail][onReady_001] 页面准备完成');
		this.initVideoStickyListener();
		// 初始化视频上下文和倍速设置
		this.initVideoContext();
		console.log('2025-01-03 22:55:53,565-INFO-[mldetail][onReady_002] 视频上下文初始化完成');
	},
	onPageScroll(e) {
		console.log('2025-01-03 22:55:53,565-INFO-[mldetail][onPageScroll_001] 页面滚动，scrollTop:', e.scrollTop);
		this.scrollTop = e.scrollTop;
		this.handleVideoSticky(e.scrollTop);
	},
	onReachBottom: function () {
		console.log('2025-01-03 22:55:53,565-INFO-[mldetail][onReachBottom_001] 触底加载更多');
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getdatalist(true);
		}
	},
	methods: {
		scrollToCurrentChapter() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][scrollToCurrentChapter_001] 滚动到当前章节');
			if (this.detail.id) {
				uni.pageScrollTo({
					selector: '#chapter-' + this.detail.id,
					duration: 300
				});
			}
		},
		updateCurrentChapter() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][updateCurrentChapter_001] 更新当前章节信息');
			if (this.datalist && this.detail.id) {
				this.currentChapter = this.datalist.find(item => item.id === this.detail.id);
				console.log('2025-01-03 22:55:53,565-INFO-[mldetail][updateCurrentChapter_002] 当前章节信息:', this.currentChapter);
			}
		},
		initVideoStickyListener() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][initVideoStickyListener_001] 初始化视频吸顶监听');
			if (this.detail.kctype === 3) {
				// 获取视频元素的位置和高度
				const query = uni.createSelectorQuery().in(this);
				query.select('.videobox').boundingClientRect((rect) => {
					if (rect) {
						this.videoHeight = rect.height;
						console.log('2025-01-03 22:55:53,565-INFO-[mldetail][initVideoStickyListener_002] 视频高度获取成功:', this.videoHeight);
					}
				}).exec();
			}
		},
		handleVideoSticky(scrollTop) {
			if (this.detail.kctype !== 3) return;
			
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][handleVideoSticky_001] 处理视频吸顶，scrollTop:', scrollTop);
			
			// 获取视频容器的位置
			const query = uni.createSelectorQuery().in(this);
			query.select('.videobox').boundingClientRect((rect) => {
				if (rect) {
					console.log('2025-01-03 22:55:53,565-INFO-[mldetail][handleVideoSticky_002] 视频容器位置:', rect.top);
					// 当视频容器滚动到顶部时，启用吸顶
					if (rect.top <= 0 && !this.isVideoSticky) {
						this.isVideoSticky = true;
						console.log('2025-01-03 22:55:53,565-INFO-[mldetail][handleVideoSticky_003] 启用视频吸顶');
					} else if (scrollTop <= 0 && this.isVideoSticky) {
						// 滚动到顶部时取消吸顶
						this.isVideoSticky = false;
						console.log('2025-01-03 22:55:53,565-INFO-[mldetail][handleVideoSticky_004] 取消视频吸顶');
					}
				}
			}).exec();
		},
		getdata:function(){
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][getdata_001] 开始获取课程详情数据，参数:', this.opt);
			var that = this;
			var id = this.opt.id || 0;
			that.id = id;
			var kcid = this.opt.kcid || 0;
			that.loading = true;
			
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][getdata_002] 调用ApiKecheng/mldetail接口，id:', id, 'kcid:', kcid);
			
			app.get('ApiKecheng/mldetail', {id: id,kcid:kcid}, function (res) {
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][getdata_003] ApiKecheng/mldetail接口返回结果:', res);
				that.loading = false;
				var detail = res.detail;
				that.detail = detail;
				that.iskaoshi = res.iskaoshi;
				that.isauto = res.isauto;
				that.currentTime = detail.startTime
				uni.setNavigationBarTitle({
					title: detail.name
				});
				if(detail.jumpurl){
					app.goto(detail.jumpurl);return;
				}
				that.studylog = res.studylog;
				
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][getdata_004] 学习记录信息:', res.studylog);
				
				// 2025-01-14 22:55:53,565-INFO-[mldetail][getdata_007] 获取课程设置信息
				if(res.kechengset) {
					that.kechengset = res.kechengset;
					console.log('2025-01-14 22:55:53,565-INFO-[mldetail][getdata_008] 课程设置信息:', res.kechengset);
				}
				
				var pagecontent = JSON.parse(detail.detail);
				that.pagecontent = pagecontent;
				that.loaded({title:detail.name,pic:detail.pic});
				
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][getdata_005] 准备调用addstudy方法');
				that.addstudy();
				if(detail.kctype>1){
					interval = setInterval(function () {
						that.addstudy();
					}, 10000);
				}
				that.play();
				
				// 课程数据加载完成后，初始化视频吸顶和恢复倍速设置
				that.$nextTick(() => {
					that.initVideoStickyListener();
					// 更新当前章节信息
					that.updateCurrentChapter();
					
					// 延迟恢复倍速设置，确保音频/视频已完全加载
					setTimeout(() => {
						that.restoreSpeedSetting();
					}, 2000);
				});
			});
		},
		todetail:function(e){
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][todetail_001] 点击章节跳转，事件数据:', e.currentTarget.dataset);
			var that = this;
			var url = e.currentTarget.dataset.url;
			var ismf = e.currentTarget.dataset.mianfei;
			var opentype = e.currentTarget.dataset.opentype;
			var key = e.currentTarget.dataset.key;
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][todetail_002] 跳转参数 - URL:', url, '免费试看:', ismf, '打开方式:', opentype, 'key:', key);
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][todetail_003] 当前课程状态 - 是否购买:', that.detail.ispay, '课程价格:', that.detail.price);
			
			if(ismf==1 || that.detail.ispay==1 || that.detail.price==0){
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][todetail_004] 有权限访问章节，准备跳转:', url);
				app.goto(url,opentype);
			}else{
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][todetail_005] 无权限访问章节，需要购买课程');
				app.alert('请先购买课程',function(){
					app.goto('product?id='+that.opt.kcid);
				});
			}
		},
		getdatalist: function(loadmore){
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][getdatalist_001] 获取课程目录列表，loadmore:', loadmore);
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
			var that = this;
			var pagenum = that.pagenum;
			var kcid = that.opt.kcid ? that.opt.kcid : '';
			var order = that.order;
			var field = that.field; 
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			app.post('ApiKecheng/getmululist', {pagenum: pagenum,field: field,order: order,id:kcid}, function (res) { 
				console.log('2025-01-03 22:55:53,565-INFO-[mldetail][getdatalist_002] 课程目录数据获取成功:', res);
				that.loading = false;
				uni.stopPullDownRefresh();
				var data = res.data;
				if (pagenum == 1) {
				  that.datalist = data;
				  if (data.length == 0) {
				    that.nodata = true;
				  }
					
				}else{
				  if (data.length == 0) {
				    that.nomore = true;
				  } else {
				    var datalist = that.datalist;
				    var newdata = datalist.concat(data);
				    that.datalist = newdata;
				  }
				}
				
				// 数据加载完成后更新当前章节信息
				that.$nextTick(() => {
					that.updateCurrentChapter();
				});
			});
		},
		scrolltolower: function () {	     
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][scrolltolower_001] 滚动到底部');
			if (!this.nomore) {
				this.pagenum = this.pagenum + 1;    
				this.getdatalist(true);
			}
		},
		payvideo: function () {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][payvideo_001] 播放视频');
			this.isplay = 1;
			uni.createVideoContext('video').play();
		},
		parsevideo: function () {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][parsevideo_001] 暂停视频');
			this.isplay = 0;
			uni.createVideoContext('video').stop();
		},
		pause:function(){
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][pause_001] 视频暂停，记录学习进度');
			//将暂停播放时间请求
			var that = this
			var id = that.opt.id ? that.opt.id : '';
			that.addstudy();
		},
		addstudy:function(){
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_001] 开始记录学习进度，当前进度:', this.currentTime, '学习进度:', this.playJd);
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_002] 学习记录信息:', this.studylog);
			
			var that = this
			var logid = that.studylog.id; 
			var id= that.detail.id;
			
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_003] 准备调用ApiKecheng/addstudy接口，参数:', {
				logid: logid,
				currentTime: that.currentTime,
				playJd: that.playJd
			});
			
			app.post('ApiKecheng/addstudy', {logid:logid,currentTime:that.currentTime,playJd:that.playJd}, function (res) {
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_004] ApiKecheng/addstudy接口返回结果:', res);
				
				if(res.status == 1) {
					console.log('2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_005] 学习记录保存成功，进度:', res.jindu);
					that.datalist[that.detail.key].jindu = res.jindu
					that.detail.startTime = that.currentTime
				} else {
					console.log('2025-01-14 22:55:53,565-ERROR-[mldetail][addstudy_006] 学习记录保存失败:', res.msg);
				}
				
				//if(res.playJd>='100' &&  that.isauto && that.detail.isdt==1 && that.detail.count>=that.detail.kccount && that.iskaoshi!=1){
					//app.goto('tiku?id=' + that.detail.kcid);
				//}
				/*if(that.playJd>=100){
					  app.confirm('本节已学完，是否学习下一节', function (res) {
								app.post('ApiKecheng/nextsection', {id: id,kcid:that.detail.kcid}, function (res) {
											app.goto('/activity/kecheng/mldetail?id='+res.id+'&kcid='+that.detail.kcid);
								});
						},
						function (res) {
									that.stipshow=false;
									that.playshow=true;
									that.currentTime = 0;
									that.nowtime = '00:00';
									that.playJd = 0;
						})
				}*/
			})
		},
		timeupdate:function(e){
			//跳转到指定播放位置 initial-time 时间为秒
			let that = this;
			//播放的总时长
			var duration = e.detail.duration;
			//实时播放进度 秒数
			var currentTime = e.detail.currentTime;
			//当前视频进度
			// console.log("视频播放到第" + currentTime + "秒")//查看正在播放时间，以秒为单位
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][timeupdate_001] 视频时间更新，当前时间:', currentTime, '总时长:', duration);
			var jump_time = that.currentTime   //上次结束时间
			if (that.detail.isjinzhi == 1) {
				if (currentTime > jump_time && currentTime - jump_time > 1 && that.datalist[that.detail.key].jindu!='100' ) {
						let videoContext = wx.createVideoContext('video');
						videoContext.seek(that.currentTime);
						wx.showToast({
							title: '未完整看完该视频，不能快进',
							icon: 'none',
							duration: 2000
						});
				}
			}
			
			that.currentTime  = currentTime; //实时播放进度
			var min = Math.floor(currentTime/60)
			var second = currentTime%60
			that.nowtime = (min>=10?min:'0'+min)+':'+(second>=10?second:'0'+second)
			  //计算进度
			if(that.playJd < 100){
				that.playJd = (that.currentTime/(duration-1)).toFixed(2)*100;
				if(that.playJd>100) that.playJd=100
			}
			that.datalist[that.detail.key].jindu = that.playJd.toFixed(1)
		
		},
		ended(){
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][ended_001] 视频播放结束');
			var that=this;
			if(that.iskaoshi!=1 && that.playJd==100 &&  that.isauto && that.detail.isdt==1  ){
				app.goto('tiku?id=' + that.detail.kcid);
				return;
			
			}
		},
		// 播放
		play() {
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][play_001] 开始播放音频');
			var that=this
			this.playshow=true;
			this.innerAudioContext.autoplay = true;
			this.innerAudioContext.src = that.detail.voice_url;
			
			this.innerAudioContext.play();
			this.innerAudioContext.onCanplay(()=> {
				this.innerAudioContext.duration;
				setTimeout(() => {
					that.time = this.innerAudioContext.duration.toFixed(0);
					var min = Math.floor(that.time/60);
					var second = that.time%60
					this.duration = (min>10?min:'0'+min)+':'+(second>10?second:'0'+second);	
					console.log('2025-01-14 22:55:53,565-INFO-[mldetail][play_002] 音频时长获取成功:', this.duration);
					
					// 音频加载完成后，延迟应用倍速设置
					setTimeout(() => {
						that.restoreAndApplySpeedSetting();
					}, 1000);
				}, 1000)
			})  
			that.startTime =  that.detail.startTime

			if(that.detail.startTime >=that.detail.video_duration){
				that.startTime =  0
			}

			this.innerAudioContext.seek(that.startTime)
			this.innerAudioContext.onPlay(() => {
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][play_003] 音频开始播放');
				that.playshow=false;
				
				// 播放开始时再次确保倍速设置生效
				setTimeout(() => {
					var savedSpeed = uni.getStorageSync('playbackSpeed') || 1;
					if (savedSpeed !== 1) {
						that.applyAudioSpeedSetting(savedSpeed);
					}
				}, 500);
			});
			this.innerAudioContext.onPause(() => {
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][play_004] 音频暂停播放');
				//that.addstudy();
				that.playshow=true;
			});
			this.innerAudioContext.onEnded(() => {
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][play_005] 音频播放结束');
				that.playJd = 100;
				clearInterval(interval);
				that.addstudy();
				that.playshow=true;
			});
			this.innerAudioContext.onTimeUpdate(() => {
				var nowtime = this.innerAudioContext.currentTime.toFixed(0)
				var min = Math.floor(nowtime/60)
				var second = nowtime%60
				that.nowtime = (min>=10?min:'0'+min)+':'+(second>=10?second:'0'+second)
				  //计算进度
				if(that.playJd < 100 && that.innerAudioContext.duration > 0){
					that.playJd = ((nowtime/that.innerAudioContext.duration).toFixed(2))*100;
					if(that.playJd>100) that.playJd=100
				}
				that.currentTime = this.innerAudioContext.currentTime;
			//	console.log(that.currentTime);
				//console.log('播放进度',that.innerAudioContext.currentTime,)			
			});
		 
	
		},
		// 暂停
		pauseaudio() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][pauseaudio_001] 暂停音频播放');
			var that=this
			this.innerAudioContext.pause();
			that.addstudy();
		},
		// 拖动进度条
		sliderChange(data) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][sliderChange_001] 拖动进度条到:', data.detail.value);
			var that=this;
			if(that.detail.isjinzhi == 1 && data.detail.value>that.detail.startTime && that.datalist[that.detail.key].jindu!='100'){
				app.error('未完整听完该音频，不能快进');return;
			}else{
				that.currentTime = data.detail.value;
				this.innerAudioContext.seek(data.detail.value)
			}	
		},
		//拖动中
		sliderChanging(data) {	
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][sliderChanging_001] 正在拖动进度条:', data.detail.value);
			this.currentTime = data.detail.value	
		},
		showSpeedMenu: function (speed) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][showSpeedMenu_001] 显示倍速选择菜单');
			this.currentSpeed = speed;
			this.showSpeedMenu = true;
		},
		selectSpeed: function (speed) {
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][selectSpeed_001] 选择倍速:', speed);
			this.currentSpeed = speed;
			this.showSpeedMenu = false;
			
			// 应用倍速设置
			this.applySpeedSetting(speed);
			
			// 保存倍速设置到本地存储
			uni.setStorageSync('playbackSpeed', speed);
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][selectSpeed_002] 倍速设置已保存到本地存储:', speed);
			
			// 显示用户反馈
			uni.showToast({
				title: `倍速已设置为${speed}x`,
				icon: 'none',
				duration: 1500
			});
		},
		getSpeedDesc: function (speed) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][getSpeedDesc_001] 获取倍速描述:', speed);
			const descriptions = {
				0.5: '慢速',
				0.75: '较慢',
				1.0: '正常',
				1.25: '较快',
				1.5: '快速',
				1.75: '很快',
				2.0: '超快'
			};
			return descriptions[speed] || '正常';
		},
		applySpeedSetting: function (speed) {
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_001] 应用倍速设置:', speed);
			var that = this;
			
			if (this.detail.kctype == 3) {
				// 视频倍速控制
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_002] 设置视频倍速:', speed);
				
				// 小程序端和其他端都通过VideoContext设置
				setTimeout(() => {
					try {
						that.videoContext = uni.createVideoContext('video', that);
						if (that.videoContext && typeof that.videoContext.playbackRate === 'function') {
							that.videoContext.playbackRate(speed);
							console.log('2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_003] 视频倍速设置成功:', speed);
						} else {
							console.log('2025-01-14 22:55:53,565-WARN-[mldetail][applySpeedSetting_004] 视频上下文不支持倍速功能');
							// 对于不支持的情况，显示提示但不报错
							if (speed !== 1.0) {
								uni.showToast({
									title: '当前设备不支持视频倍速',
									icon: 'none',
									duration: 2000
								});
							}
						}
					} catch (error) {
						console.log('2025-01-14 22:55:53,565-ERROR-[mldetail][applySpeedSetting_005] 视频倍速设置失败:', error);
						if (speed !== 1.0) {
							uni.showToast({
								title: '倍速设置失败，请重试',
								icon: 'none',
								duration: 2000
							});
						}
					}
				}, 500); // 延迟执行确保视频已加载
				
			} else if (this.detail.kctype == 2) {
				// 音频倍速控制
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_006] 设置音频倍速:', speed);
				
				setTimeout(() => {
					try {
						// 检查音频上下文是否支持倍速
						if (that.innerAudioContext && typeof that.innerAudioContext.playbackRate !== 'undefined') {
							// 尝试设置倍速
							that.innerAudioContext.playbackRate = speed;
							console.log('2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_007] 音频倍速设置成功:', speed);
							
							// 验证设置是否生效
							setTimeout(() => {
								if (Math.abs(that.innerAudioContext.playbackRate - speed) > 0.01) {
									console.log('2025-01-14 22:55:53,565-WARN-[mldetail][applySpeedSetting_008] 音频倍速设置未生效');
									if (speed !== 1.0) {
										uni.showToast({
											title: '当前设备音频倍速功能不稳定',
											icon: 'none',
											duration: 2000
										});
									}
								}
							}, 100);
						} else {
							console.log('2025-01-14 22:55:53,565-WARN-[mldetail][applySpeedSetting_009] 音频上下文不支持倍速功能');
							if (speed !== 1.0) {
								uni.showToast({
									title: '当前设备不支持音频倍速',
									icon: 'none',
									duration: 2000
								});
							}
						}
					} catch (error) {
						console.log('2025-01-14 22:55:53,565-ERROR-[mldetail][applySpeedSetting_010] 音频倍速设置失败:', error);
						if (speed !== 1.0) {
							uni.showToast({
								title: '倍速设置失败，请重试',
								icon: 'none',
								duration: 2000
							});
						}
					}
				}, 300); // 延迟执行确保音频已加载
			}
		},
		initVideoContext() {
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][initVideoContext_001] 初始化视频上下文');
			var that = this;
			
			if (this.detail.kctype == 3) {
				// 延迟初始化，确保video组件已渲染
				setTimeout(() => {
					that.videoContext = uni.createVideoContext('video', that);
					console.log('2025-01-14 22:55:53,565-INFO-[mldetail][initVideoContext_002] 视频上下文创建完成');
					
					// 视频加载完成后，延迟恢复倍速设置
					setTimeout(() => {
						that.restoreAndApplySpeedSetting();
					}, 1000);
				}, 1000);
			}
		},
		showNoteDialog() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][showNoteDialog_001] 显示笔记弹窗');
			this.noteDialogVisible = true;
			this.noteTabIndex = 1;
			this.resetNoteForm();
			this.getMyNotes();
		},
		hideNoteDialog() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][hideNoteDialog_001] 隐藏笔记弹窗');
			this.noteDialogVisible = false;
			this.editingNoteId = null;
			this.resetNoteForm();
		},
		resetNoteForm() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][resetNoteForm_001] 重置笔记表单');
			this.noteForm = {
				content: '',
				time_point: this.currentTime || '',
				study_progress: (this.kechengset && this.kechengset.notes_need_progress == 1) ? 0 : 0
			};
			this.editingNoteId = null;
		},
		switchNoteTab(e) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][switchNoteTab_001] 切换笔记标签页');
			const index = e.currentTarget.dataset.index;
			this.noteTabIndex = index;
			if (index == 2) {
				this.getMyNotes();
			}
		},
		onProgressChange(e) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][onProgressChange_001] 学习进度变化');
			this.noteForm.study_progress = e.detail.value;
		},
		submitNote() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][submitNote_001] 开始提交笔记');
			var that = this;
			
			if (!that.noteForm.content.trim()) {
				app.error('请输入笔记内容');
				return;
			}
			
			var apiUrl = that.editingNoteId ? 'ApiKechengNotes/updateNote' : 'ApiKechengNotes/addNote';
			var params = {
				kcid: that.opt.kcid,
				chapter_id: that.detail.id || 0,
				content: that.noteForm.content.trim(),
				note_time: that.noteForm.time_point || that.currentTime || 0
			};
			
			// 只有当课程设置需要进度时才提交进度数据
			if(that.kechengset && that.kechengset.notes_need_progress == 1) {
				params.study_progress = that.noteForm.study_progress;
			}
			
			if (that.editingNoteId) {
				params.id = that.editingNoteId;
			}
			
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][submitNote_002] 提交参数:', params);
			
			app.showLoading('保存中...');
			app.post(apiUrl, params, function(res) {
				app.showLoading(false);
				console.log('2025-01-03 22:55:53,565-INFO-[mldetail][submitNote_003] 笔记保存结果:', res);
				
				if (res.status == 1) {
					app.success(res.msg);
					that.resetNoteForm();
					that.noteTabIndex = 2;
					that.getMyNotes();
				} else {
					app.error(res.msg);
				}
			});
		},
		getMyNotes(loadMore = false) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][getMyNotes_001] 获取我的笔记列表，loadMore:', loadMore);
			var that = this;
			
			if (!loadMore) {
				that.notesPageNum = 1;
				that.myNotesList = [];
			}
			
			that.loadingNotes = true;
			that.noMoreNotes = false;
			
			var params = {
				kcid: that.opt.kcid,
				chapter_id: that.detail.id || 0,
				pagenum: that.notesPageNum,
				pernum: 10
			};
			
			app.post('ApiKechengNotes/getMyNotes', params, function(res) {
				that.loadingNotes = false;
				console.log('2025-01-03 22:55:53,565-INFO-[mldetail][getMyNotes_002] 笔记列表获取结果:', res);
				
				if (res.status == 1) {
					var data = res.data || [];
					if (that.notesPageNum == 1) {
						that.myNotesList = data;
					} else {
						that.myNotesList = that.myNotesList.concat(data);
					}
					
					if (data.length < 10) {
						that.noMoreNotes = true;
					}
				} else {
					app.error(res.msg);
				}
			});
		},
		editNote(e) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][editNote_001] 编辑笔记');
			const noteData = JSON.parse(e.currentTarget.dataset.note);
			this.editingNoteId = noteData.id;
			this.noteForm = {
				content: noteData.content,
				study_progress: noteData.study_progress || 0,
				time_point: noteData.note_time || ''
			};
			this.noteTabIndex = 1;
		},
		deleteNote(e) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][deleteNote_001] 删除笔记');
			var that = this;
			const noteId = e.currentTarget.dataset.id;
			
			app.confirm('确定要删除这条笔记吗？', function() {
				app.showLoading('删除中...');
				app.post('ApiKechengNotes/deleteNote', {id: noteId}, function(res) {
					app.showLoading(false);
					console.log('2025-01-03 22:55:53,565-INFO-[mldetail][deleteNote_002] 删除结果:', res);
					
					if (res.status == 1) {
						app.success(res.msg);
						that.getMyNotes();
					} else {
						app.error(res.msg);
					}
				});
			});
		},
		onFilterChange(e) {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][onFilterChange_001] 筛选变化:', e.detail.value);
			this.filterIndex = e.detail.value;
			this.getMyNotes();
		},
		loadMoreNotes() {
			console.log('2025-01-03 22:55:53,565-INFO-[mldetail][loadMoreNotes_001] 加载更多笔记');
			if (!this.loadingNotes && !this.noMoreNotes) {
				this.notesPageNum++;
				this.getMyNotes(true);
			}
		},
		// 专门处理音频倍速设置的方法
		applyAudioSpeedSetting: function(speed) {
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][applyAudioSpeedSetting_001] 设置音频倍速:', speed);
			var that = this;
			
			try {
				if (that.innerAudioContext && typeof that.innerAudioContext.playbackRate !== 'undefined') {
					// 多次尝试设置，提高成功率
					var setSpeedAttempts = 0;
					var maxAttempts = 3;
					
					function attemptSetSpeed() {
						try {
							that.innerAudioContext.playbackRate = speed;
							console.log('2025-01-14 22:55:53,565-INFO-[mldetail][applyAudioSpeedSetting_002] 音频倍速设置尝试:', setSpeedAttempts + 1, '倍速:', speed);
							
							// 验证设置
							setTimeout(() => {
								if (that.innerAudioContext && Math.abs(that.innerAudioContext.playbackRate - speed) < 0.01) {
									console.log('2025-01-14 22:55:53,565-INFO-[mldetail][applyAudioSpeedSetting_003] 音频倍速设置成功验证:', that.innerAudioContext.playbackRate);
								} else if (setSpeedAttempts < maxAttempts - 1) {
									setSpeedAttempts++;
									setTimeout(attemptSetSpeed, 200);
								} else {
									console.log('2025-01-14 22:55:53,565-WARN-[mldetail][applyAudioSpeedSetting_004] 音频倍速设置多次尝试后仍未生效');
								}
							}, 100);
							
						} catch (error) {
							console.log('2025-01-14 22:55:53,565-ERROR-[mldetail][applyAudioSpeedSetting_005] 音频倍速设置失败:', error);
						}
					}
					
					attemptSetSpeed();
				} else {
					console.log('2025-01-14 22:55:53,565-WARN-[mldetail][applyAudioSpeedSetting_006] 音频上下文不支持倍速或未初始化');
				}
			} catch (error) {
				console.log('2025-01-14 22:55:53,565-ERROR-[mldetail][applyAudioSpeedSetting_007] 音频倍速设置异常:', error);
			}
		},
		
		// 初始化倍速设置
		initSpeedSetting: function() {
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][initSpeedSetting_001] 初始化倍速设置');
			
			// 页面初始化时，始终从默认倍速1开始显示
			this.currentSpeed = 1;
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][initSpeedSetting_002] 设置默认倍速显示为1');
		},
		
		// 恢复倍速设置
		restoreSpeedSetting: function() {
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][restoreSpeedSetting_001] 恢复倍速设置');
			
			// 从本地存储获取倍速设置
			const savedSpeed = uni.getStorageSync('playbackSpeed');
			if (savedSpeed && savedSpeed !== 1) {
				this.currentSpeed = savedSpeed;
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][restoreSpeedSetting_002] 从本地存储恢复倍速设置:', savedSpeed);
			}
		},
		
		// 恢复并应用倍速设置
		restoreAndApplySpeedSetting: function() {
			console.log('2025-01-14 22:55:53,565-INFO-[mldetail][restoreAndApplySpeedSetting_001] 恢复并应用倍速设置');
			
			// 从本地存储获取倍速设置
			const savedSpeed = uni.getStorageSync('playbackSpeed');
			if (savedSpeed && savedSpeed !== 1) {
				this.currentSpeed = savedSpeed;
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][restoreAndApplySpeedSetting_002] 恢复倍速设置:', savedSpeed);
				
				// 应用倍速设置
				this.applySpeedSetting(savedSpeed);
			} else {
				console.log('2025-01-14 22:55:53,565-INFO-[mldetail][restoreAndApplySpeedSetting_003] 使用默认倍速1');
			}
		},
	}
};
</script>
<style>
.wrap{ background: #fff;}
.wrap .title{ padding: 30rpx; font-size: 42rpx; color: #111111; font-weight: bold; justify-content: space-between;}
	
.hide{ display: none;}
.provideo{border-radius:27rpx;width:750rpx;position:absolute;z-index:1000;align-items:center;justify-content:space-between}
.provideo image{ width: 100%;}
.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}

/* 视频吸顶样式 */
.videobox{width:100%;text-align:center;background:#000; position: relative; }
.videobox.video-sticky {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	width: 100%;
}
.video-placeholder {
	width: 100%;
	background: #000;
}
.videobox .video{width:100%;}
.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx}

.content_box{ background: #fff;}
.content_box .title{ line-height: 60rpx; margin-left: 30rpx; padding:20rpx 0rpx;border-bottom: 1px solid #F7F7F7;}
.content_box .title .t1{ font-size: 32rpx; font-weight: bold;  }
.content_box .title .t2{ font-size: 24rpx; background:#fff;border:1px solid #cdcdcd;border-radius:3px; margin-right: 20rpx; padding: 0rpx 20rpx; border-radius: 10rpx;}

/* 当前学习章节卡片样式 */
.current-learning-card {
	margin: 20rpx 30rpx;
	background: linear-gradient(135deg, #FF5347 0%, #FF6B47 100%);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 20rpx rgba(255, 83, 71, 0.3);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.card-title {
	color: #fff;
	font-size: 28rpx;
	font-weight: bold;
}

.progress-text {
	color: rgba(255, 255, 255, 0.9);
	font-size: 24rpx;
}

.card-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.chapter-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.chapter-icon {
	width: 44rpx;
	height: 44rpx;
	margin-right: 20rpx;
}

.chapter-text {
	flex: 1;
}

.chapter-name {
	color: #fff;
	font-size: 30rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.chapter-type {
	display: flex;
	align-items: center;
}

.type-tag, .duration-tag {
	color: rgba(255, 255, 255, 0.8);
	font-size: 22rpx;
	margin-right: 15rpx;
}

.continue-btn {
	background: rgba(255, 255, 255, 0.2);
	color: #fff;
	padding: 16rpx 30rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.mulubox{ padding-top: 35rpx; padding-left: 30rpx;}
.left_box{ display: flex;}
.left_box image{ width: 44rpx; height:44rpx; margin-right: 40rpx; margin-top: 26rpx; }
.right_box{ border-bottom: 1px solid #F6F6F6; padding-bottom: 30rpx; width: 100%; justify-content: space-between;}
.title_box{ width: 80%;}
.title_box .t1{ color: #1E252F; font-size: 28rpx; font-weight: bold;}
.title_box .t2{ color: #B8B8B8;font-size: 24rpx;line-height: 60rpx; margin-right: 15rpx;}

/* 当前章节高亮样式 */
.right_box .current-chapter .t1{ color:#FF5347; font-weight: bold;}
.right_box .current-chapter .t2{ color:#FF5347;}
.current-indicator {
	background: #FF5347;
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	margin-right: 10rpx;
}

/* 已完成章节样式 */
.right_box .completed-chapter .t1{ color:#999; }
.right_box .completed-chapter .t2{ color:#999;}

.right_box .on text{ color:#FF5347}
.right_box .on .t1{  color:#FF5347}
.skbtn{  background-color: #FFEEEC; padding: 6rpx 20rpx; margin-right: 10px; height: 44rpx; width: 90rpx; color: #FC6D65; font-size: 24rpx; border-radius: 22rpx; margin-top: 20rpx;}
.right_box .jindu{ color:#FF5347; margin-right: 20rpx; font-size: 24rpx;}
.baner{ width:100%; overflow: hidden; box-sizing: border-box; position: relative;}
.audioBg{display: block; width:100%; height:370rpx;}
.transmit{ position: absolute; left: 0;  right: 0; top: 0; bottom:0; margin: auto; display: block; width:80rpx; height:80rpx;}

.content {	padding: 20upx;}
.list {font-size: 28upx;line-height: 88upx;padding-left: 30upx;background: #fff;border-radius: 10upx;margin-top: 20upx;color: #333;}

/*音频播放器样式*/
.audoimg{ width: 100%; }
.audoimg image{ width: 100%; height: 600rpx; }
/* 修复CSS语法错误 - 使用::v-deep替代/deep/ */
::v-deep .uni-slider-handle-wrapper{
    background: black !important;
}
::v-deep .uni-slider-thumb{
    background: black !important;
}
.play{ background-color:rgba(255,255,255,0.5);width: 100%; height: 124rpx;position: absolute; bottom:0%;  }
.play-left text{ margin-top: 1px; color: black;  font-size: 13px; line-height: 120rpx;  position: absolute; left: 13%;    }
.play-end text{ margin-top: 1px; color: black;  font-size: 13px; line-height: 120rpx; right: 8%;  position: absolute;      }
.slider{  width: 366rpx; position: relative; margin-top: 42rpx;  color: black; float: left;}
.musions{  width: 26px; height: 26px; margin: 17px 4px 0 5px; float: left; }
.play image{   width: 26px; height: 26px; margin: 34rpx 4px 0 5px;float: left;  }
.play-left{width: 170rpx;height: 116upx;    float: left;  border-radius: 38px;  }
.play-right{ width: 66%;  float: left; height: 58px; position: relative; }
.audo-video {  width: 100%;   position: relative; top: -18px; }
.slider-box {  display: flex; align-items: center;justify-content: center;font-size: 26upx; color: #999; }
button {  display: inline-block; width: 100upx; background-color: #fff;  font-size: 24upx;    color: #000;   padding: 0; }
.hidden {position: fixed;  z-index: -1;   width: 1upx;height: 1upx;}
.speed-control-btn, .audio-speed-btn {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	background: rgba(0, 0, 0, 0.7);
	border-radius: 40rpx;
	padding: 10rpx 20rpx;
	z-index: 10;
}
.audio-speed-btn {
	position: relative;
	top: 0;
	right: 0;
	margin-left: 20rpx;
	background: rgba(0, 0, 0, 0.1);
	border: 2rpx solid #ddd;
}
.speed-text {
	color: white;
	font-size: 24rpx;
	font-weight: bold;
}
.audio-speed-btn .speed-text {
	color: #333;
}
.speed-menu-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}
.speed-menu {
	background: white;
	border-radius: 20rpx;
	margin: 40rpx;
	max-width: 600rpx;
	width: 90%;
	animation: slideUp 0.3s ease-out;
}
@keyframes slideUp {
	from {
		transform: translateY(100rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}
.speed-menu-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx 20rpx;
	border-bottom: 2rpx solid #f5f5f5;
}
.speed-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	color: #666;
	border-radius: 50%;
	background: #f5f5f5;
}
.speed-options {
	padding: 20rpx 0;
}
.speed-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	transition: background-color 0.2s;
}
.speed-option:hover {
	background: #f8f8f8;
}
.speed-option.active {
	background: #e8f4fd;
	border-left: 6rpx solid #1890ff;
}
.speed-label {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}
.speed-option.active .speed-label {
	color: #1890ff;
	font-weight: bold;
}
.speed-desc {
	font-size: 26rpx;
	color: #999;
}
.speed-option.active .speed-desc {
	color: #1890ff;
}


/* 笔记功能样式 */
.note-modal {
	min-height: 800rpx;
	max-height: 1200rpx;
}

.note-tabs {
	display: flex;
	border-bottom: 2rpx solid #f5f5f5;
	margin-bottom: 30rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 0;
	font-size: 28rpx;
	color: #666;
	position: relative;
}

.tab-item.active {
	color: #FF5347;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 4rpx;
	background: #FF5347;
	border-radius: 2rpx;
}

.add-note-content {
	padding: 0 20rpx;
}

.form-group {
	margin-bottom: 40rpx;
	background: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
}

.form-label::before {
	content: '';
	width: 6rpx;
	height: 24rpx;
	background: #FF5347;
	margin-right: 12rpx;
	border-radius: 3rpx;
}

.note-input {
	width: 100%;
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 12rpx;
	font-size: 26rpx;
	background: #fafafa;
	box-sizing: border-box;
}

.note-input:focus {
	border-color: #FF5347;
	background: #fff;
}

.progress-input {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 12rpx;
	background: #fafafa;
	position: relative;
}

.progress-input slider {
	flex: 1;
	margin-right: 20rpx;
}

.progress-input .uni-slider-handle {
	width: 32rpx;
	height: 32rpx;
	background: #FF5347;
	box-shadow: 0 2rpx 6rpx rgba(255, 83, 71, 0.3);
}

.progress-input .uni-slider-track {
	background: #FF5347;
}

.progress-text {
	font-size: 26rpx;
	color: #FF5347;
	font-weight: bold;
	min-width: 60rpx;
	text-align: right;
	position: absolute;
	right: 20rpx;
}

/* 笔记内容部分样式优化 */
.note-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 12rpx;
	font-size: 26rpx;
	line-height: 1.6;
	background: #fafafa;
	box-sizing: border-box;
	resize: none;
}

.note-textarea:focus {
	border-color: #FF5347;
	background: #fff;
	box-shadow: 0 2rpx 8rpx rgba(255, 83, 71, 0.1);
}

/* 按钮样式优化 */
.form-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 40rpx;
	padding: 0 20rpx;
}

.btn-cancel, .btn-submit {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
	font-weight: bold;
	transition: all 0.3s ease;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
}

.btn-cancel:active {
	background: #eee;
}

.btn-submit {
	color: #fff;
	box-shadow: 0 4rpx 12rpx rgba(255, 83, 71, 0.3);
}

.btn-submit:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 6rpx rgba(255, 83, 71, 0.2);
}

.my-notes-content {
	padding: 0 20rpx;
}

.notes-filter {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f8f8f8;
	border-radius: 12rpx;
}

.filter-item {
	display: flex;
	align-items: center;
}

.filter-label {
	font-size: 26rpx;
	color: #666;
	margin-right: 20rpx;
}

.picker-text {
	font-size: 26rpx;
	color: #333;
	padding: 10rpx 20rpx;
	background: #fff;
	border-radius: 8rpx;
	border: 1rpx solid #ddd;
}

.notes-list {
	max-height: 600rpx;
	overflow-y: auto;
}

.note-item {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	border-left: 6rpx solid #FF5347;
}

.note-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.note-time {
	font-size: 24rpx;
	color: #999;
}

.note-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	border: 1rpx solid;
}

.edit-btn {
	color: #FF5347;
	border-color: #FF5347;
	background: rgba(255, 83, 71, 0.1);
}

.delete-btn {
	color: #ff4757;
	border-color: #ff4757;
	background: rgba(255, 71, 87, 0.1);
}

.note-content {
	font-size: 28rpx;
	line-height: 1.6;
	color: #333;
	margin-bottom: 20rpx;
	word-break: break-all;
}

.note-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 24rpx;
	color: #666;
}

.note-progress {
	color: #FF5347;
	font-weight: bold;
}

.note-time-point {
	color: #999;
}

.note-chapter {
	color: #999;
}

.no-notes {
	text-align: center;
	padding: 80rpx 20rpx;
}

.empty-icon {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}

.empty-text {
	display: block;
	font-size: 28rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.empty-tip {
	display: block;
	font-size: 24rpx;
	color: #ccc;
}

.load-more-notes {
	text-align: center;
	padding: 30rpx;
	font-size: 26rpx;
	color: #666;
	border-top: 1rpx solid #f0f0f0;
	margin-top: 20rpx;
}

.load-more-notes:active {
	background: #f8f8f8;
}

/* 记笔记按钮样式 */
.note-button {
	position: fixed;
	right: 30rpx;
	bottom: 280rpx;
	z-index: 99;
	border-radius: 40rpx;
	padding: 16rpx 36rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	color: #fff;
	transform: scale(1);
	transition: all 0.3s ease;
}
.note-button:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.note-button image {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
	filter: brightness(10);
}
.note-button text {
	font-size: 28rpx;
	color: #fff;
	font-weight: 500;
}
</style>