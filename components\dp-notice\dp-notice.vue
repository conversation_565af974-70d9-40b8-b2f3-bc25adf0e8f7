<template>
<view class="dp-notice" :style="{
	color:params.color,
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
}">
	<view class="left" v-if="params.showimg==1"><image class="image" :src="params.img" mode="heightFix"/></view>
	<view class="right">
		<image v-if="params.showicon==1" class="ico" :src="params.icon"/>
		<!-- 上下滚动 -->
		<swiper v-if="params.scrollType === 'vertical'" 
			style="position:relative;height:40rpx;" 
			:autoplay="true" 
			:interval="params.scroll*1000" 
			:vertical="true" 
			class="itemlist">
			<swiper-item class="item" v-for="item in data" :key="item.id" 
				:style="{fontSize:(params.fontsize*2.2)+'rpx'}" 
				@click="goto" 
				:data-url="item.hrefurl">{{item.title}}</swiper-item>
		</swiper>
		<!-- 左右滚动 -->
		<swiper v-else-if="params.scrollType === 'horizontal'" 
			style="position:relative;height:40rpx;" 
			:autoplay="true" 
			:interval="params.scroll*1000" 
			:vertical="false" 
			class="itemlist">
			<swiper-item class="item" v-for="item in data" :key="item.id" 
				:style="{fontSize:(params.fontsize*2.2)+'rpx'}" 
				@click="goto" 
				:data-url="item.hrefurl">{{item.title}}</swiper-item>
		</swiper>
		<!-- 跑马灯效果 -->
		<view v-else-if="params.scrollType === 'marquee'" 
			class="marquee-container">
			<view class="marquee-text" :style="{
				fontSize:(params.fontsize*2.2)+'rpx',
				animationDuration: marqueeTime + 's'
			}">
				<text class="marquee-content">
					<text v-for="(item, index) in data" :key="item.id" 
						class="marquee-item"
						@click="goto" 
						:data-url="item.hrefurl">
						{{item.title}}
						<text v-if="index < data.length - 1">&nbsp;&nbsp;|&nbsp;&nbsp;</text>
					</text>
				</text>
				<!-- 复制一份内容，实现无缝滚动 -->
				<text class="marquee-content">
					<text v-for="(item, index) in data" :key="'copy_' + index"
						class="marquee-item"
						@click="goto" 
						:data-url="item.hrefurl">
						{{item.title}}
						<text v-if="index < data.length - 1">&nbsp;&nbsp;|&nbsp;&nbsp;</text>
					</text>
				</text>
			</view>
		</view>
	</view>
</view>
</template>

<script>
export default {
	props: {
		params:{
			type: Object,
			default: () => ({
				scrollType: 'vertical', // 默认上下滚动
				scroll: 3 // 默认滚动间隔
			})
		},
		data:{
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			marqueeTime: 15 // 默认动画时长
		}
	},
	mounted() {
		// 根据内容长度动态计算滚动时间
		if(this.params.scrollType === 'marquee' && this.data.length > 0) {
			const totalLength = this.data.reduce((acc, curr) => acc + curr.title.length, 0);
			this.marqueeTime = Math.max(totalLength * 0.5, 10); // 根据内容长度计算动画时间，最少10秒
		}
	},
	methods: {
		goto(e) {
			const url = e.currentTarget.dataset.url
			if (url) {
				uni.navigateTo({
					url: url
				})
			}
		}
	}
}
</script>

<style>
.dp-notice{height: auto;background: #fff; font-size: 28rpx; color: #666666;overflow: hidden; white-space:nowrap; position: relative;display:flex;align-items:center;padding:2px 4px}
.dp-notice .left{position:relative;padding-right:20rpx;margin-right:20rpx;height:40rpx;display:flex;align-items: center;}
.dp-notice .left:before { content: " "; position: absolute; width: 0; top: 2px; right: 0; bottom: 2px; border-right: 1px solid #e2e2e2; }
.dp-notice .image{position:relative;height:36rpx;width:auto}
.dp-notice .right{flex-grow:1;display:flex;align-items: center;overflow:hidden}
.dp-notice .right .ico{width:36rpx;height:36rpx;margin-right:10rpx}
.dp-notice .itemlist{width:100%;height:100%;line-height:40rpx;font-size:28rpx;}

/* 跑马灯样式 */
.marquee-container {
	width: 100%;
	height: 40rpx;
	overflow: hidden;
	position: relative;
}
.marquee-text {
	position: absolute;
	white-space: nowrap;
	animation-name: marquee;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
	animation-delay: 0s;
	display: flex;
}
.marquee-content {
	display: inline-block;
	padding-right: 50rpx;
}
.marquee-item {
	display: inline-block;
}
@keyframes marquee {
	0% {
		transform: translateX(0);
	}
	100% {
		transform: translateX(-50%);
	}
}
</style>