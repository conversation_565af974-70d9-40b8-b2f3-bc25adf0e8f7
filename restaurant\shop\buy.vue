<template>
<view class="container">
	<block v-if="isload">
		<view class="address-add flex-y-center">
			<view class="f1">桌台信息</view>
			<view class="f2 flex1" v-if="tableinfo.id">
				<view style="font-weight:bold;color:#111111;font-size:30rpx">{{tableinfo.name}}<text style="font-size:24rpx;font-weight:normal;color:#666;margin-left:10rpx">{{tableinfo.seat}}人桌</text></view>
			</view>
			<view v-else class="f2 flex1">请扫描桌台二维码</view>
			<image src="/static/img/arrowright.png" class="f3"></image>
		</view>
		<view v-for="(buydata, index) in allbuydata" :key="index" class="buydata">
			<view class="btitle"><image class="img" src="/static/img/ico-shop.png"/>{{buydata.business.name}}</view>
			<view class="bcontent">
				<view class="product">
					<view v-for="(item, index2) in buydata.prodata" :key="index2" class="item flex">
						<view class="img" @tap="goto" :data-url="'product?id=' + item.product.id"><image :src="item.product.pic"></image></view>
						<view class="info flex1">
							<view class="f1">{{item.product.name}}</view>
							<view class="f2">规格：{{item.guige.name}}{{item.jldata.jltitle}}</view>
							<view class="f3">
								<text style="font-weight:bold;">￥{{parseFloat(parseFloat(item.guige.sell_price)+parseFloat(item.jldata.jlprice)).toFixed(2)}}</text>								
								<text style="padding-left:20rpx"> × {{item.num}}</text></view>
						</view>
					</view>
				</view>
				<view class="price" v-if="ordertype == 'create_order'">
					<text class="f1">就餐人数</text>
					<text class="f2" @tap="showRenshuSelect" :data-bid="buydata.bid">{{buydata.renshu>0 ? buydata.renshu+'人' : '请选择人数'}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text></text>
				</view>

				<view class="price">
					<text class="f1">菜品金额</text>
					<text class="f2">¥{{buydata.product_price}}</text>
				</view>
				<view class="price" v-if="buydata.leveldk_money>0">
					<text class="f1">{{t('会员')}}折扣({{userinfo.discount}}折)</text>
					<text class="f2">-¥{{buydata.leveldk_money}}</text>
				</view>
				<view class="price" v-if="buydata.manjian_money>0">
					<text class="f1">满减活动</text>
					<text class="f2">-¥{{buydata.manjian_money}}</text>
				</view>
				<view class="price" v-if="ordertype == 'create_order'">
					<text class="f1">{{buydata.tea_fee_text}}</text>
					<text class="f2">+¥{{buydata.tea_fee * (buydata.renshu>0 ? buydata.renshu : 1)}}</text>
				</view>
				<view class="price" v-if="ordertype == 'create_order'">
					<view class="f1">{{t('优惠券')}}</view>
					<view v-if="buydata.couponCount > 0" class="f2" @tap="showCouponList" :data-bid="buydata.bid"><text style="color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx" :style="{background:t('color1')}">{{buydata.couponrid!=0?buydata.couponList[buydata.couponkey].couponname:buydata.couponCount+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text></view>
					<text class="f2" v-else style="color:#999">无可用{{t('优惠券')}}</text>
				</view>
				<view class="price" v-if="ordertype == 'create_order' && buydata.cuxiaoCount > 0">
					<view class="f1">促销活动</view>
					<view class="f2" @tap="showCuxiaoList" :data-bid="buydata.bid"><text style="color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx" :style="{background:t('color1')}">{{buydata.cuxiaoname?buydata.cuxiaoname:buydata.cuxiaoCount+'个可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text></view>
				</view>
				<view class="remark">
					<text class="f1">备注</text>
					<input type="text" class="flex1" placeholder="请输入您的口味或要求" @input="inputfield" data-field="message" :data-bid="buydata.bid" placeholder-style="color:#cdcdcd;font-size:28rpx"></input>
				</view>
			</view>
		</view>
		
		<view class="scoredk" v-if="ordertype == 'create_order' && userinfo.score2money>0 && (userinfo.scoremaxtype==0 || (userinfo.scoremaxtype==1 && userinfo.scoredkmaxmoney>0))">
			<checkbox-group @change="scoredk" class="flex" style="width:100%">
				<view class="f1">
					<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text style="color:#e94745">{{userinfo.scoredk_money*1}}</text> 元</view>
					<view style="font-size:22rpx;color:#999" v-if="userinfo.scoremaxtype==0 && userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100">最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>
					<view style="font-size:22rpx;color:#999" v-else-if="userinfo.scoremaxtype==1">最多可抵扣{{userinfo.scoredkmaxmoney}}元</view>
				</view>
				<view class="f2">使用{{t('积分')}}抵扣
					<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
				</view>
			</checkbox-group>
		</view>
		<view style="width: 100%; height:110rpx;"></view>
		<view class="footer flex">
			<view class="text1 flex1">总计：
				<text style="font-weight:bold;font-size:36rpx">￥{{alltotalprice}}</text>
			</view>
			<view class="op" @tap="topay" :style="{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">提交订单</view>
		</view>

		<view v-if="couponvisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal">
				<view class="popup__title">
					<text class="popup__title-text">请选择{{t('优惠券')}}</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="handleClickMask"/>
				</view>
				<view class="popup__content">
					<couponlist :couponlist="allbuydata[bid].couponList" :choosecoupon="true" :selectedrid="allbuydata[bid].couponrid" :bid="bid" @chooseCoupon="chooseCoupon"></couponlist>
				</view>
			</view>
		</view>

		<view v-if="cuxiaovisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal">
				<view class="popup__title">
					<text class="popup__title-text">优惠促销</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="handleClickMask"/>
				</view>
				<view class="popup__content">
					<view class="cuxiao-desc">
						<view class="cuxiao-item" @tap="changecx" data-id="0">
							<view class="type-name"><text style="color:#333">不使用促销</text></view>
							<view class="radio" :style="cxid==0 ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
						</view>
						<view v-for="(item, index) in allbuydata[bid].cuxiaolist" :key="index" class="cuxiao-item" @tap="changecx" :data-id="item.id">
							<view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px">{{item.tip}}</text> <text style="color:#333;padding-left:20rpx">{{item.name}}</text></view>
							<view class="radio" :style="cxid==item.id ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
						</view>
					</view>
					<view id="cxproinfo" v-if="cuxiaoinfo.product" style="padding:0 40rpx">
						<view class="product">
							<view class="item flex" style="background:#f5f5f5">
								<view class="img" @tap="goto" :data-url="'product?id=' + cuxiaoinfo.product.id"><image :src="cuxiaoinfo.product.pic"></image></view>
								<view class="info flex1">
									<view class="f1">{{cuxiaoinfo.product.name}}</view>
									<view class="f2">规格：{{cuxiaoinfo.guige.name}}</view>
									<view class="f3"><text style="font-weight:bold;">￥{{cuxiaoinfo.guige.sell_price}}</text><text style="padding-left:20rpx"> × 1</text></view>
								</view>
							</view>
						</view>
					</view>
					<view style="width:100%; height:120rpx;"></view>
					<view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff">
						<view style="width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;" :style="{background:t('color1')}" @tap="chooseCuxiao">确 定</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="renshuvisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal">
				<view class="popup__title">
					<text class="popup__title-text">请选择就餐人数</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="handleClickMask"/>
				</view>
				<view class="popup__content">
					<view class="cuxiao-desc">
						<view class="cuxiao-item" @tap="changerenshu" :data-id="item" v-for="item in [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]" :key="index">
							<view class="type-name"><text style="color:#333">{{item}}人</text></view>
							<view class="radio" :style="renshu==item ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
						</view>
					</view>
					
					<view style="width:100%; height:120rpx;"></view>
					<view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff">
						<view style="width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;" :style="{background:t('color1')}" @tap="chooseRenshu">确 定</view>
					</view>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
      address: [],
      usescore: 0,
      scoredk_money: 0,
      totalprice: '0.00',
      couponvisible: false,
      cuxiaovisible: false,
			renshuvisible:false,
			renshu:1,
      bid: 0,
      nowbid: 0,
      needaddress: 1,
      linkman: '',
      tel: '',
			userinfo:{},
      manjian_money: 0,
      cxid: 0,
      latitude: "",
      longitude: "",
      allbuydata: "",
      alltotalprice: "",
      cuxiaoinfo: false,
			tableId:'',
			tableinfo:{},
			ordertype:'create_order'
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.tableId = this.opt.tableId || '';
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiRestaurantShop/buy', {prodata: that.opt.prodata,tableId:that.tableId,frompage:that.opt.frompage}, function (res) {
				that.loading = false;
				if (res.status == 0) {
					if (res.msg) {
						app.alert(res.msg, function () {
							if (res.url) {
								app.goto(res.url);
							} else {
								app.goback();
							}
						});
					} else if (res.url) {
						app.goto(res.url);
					} else {
						app.alert('您没有权限购买该商品');
					}
					return;
				}
				that.tableinfo = res.tableinfo;
				that.userinfo = res.userinfo;
				that.allbuydata = res.allbuydata;
				that.scorebdkyf = res.scorebdkyf;
				that.ordertype = res.ordertype;
				that.calculatePrice();
				that.loaded();
			});
		},
    //积分抵扣
    scoredk: function (e) {
      var usescore = e.detail.value[0];
      if (!usescore) usescore = 0;
      this.usescore = usescore;
      this.calculatePrice();
    },
    inputLinkman: function (e) {
      this.linkman = e.detail.value;
    },
    inputTel: function (e) {
      this.tel = e.detail.value;
    },
    inputfield: function (e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = e.currentTarget.dataset.bid;
			var field = e.currentTarget.dataset.field;
      allbuydata[bid][field] = e.detail.value;
      this.allbuydata = allbuydata;
    },
    //计算价格
    calculatePrice: function () {
      var that = this;
      var allbuydata = that.allbuydata;
      var alltotalprice = 0;
      for (var k in allbuydata) {
        var product_price = parseFloat(allbuydata[k].product_price);
        var leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣
        var manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动
        var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 
        var cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动  
        var tea_fee = parseFloat(allbuydata[k].tea_fee) * (allbuydata[k].renshu ? allbuydata[k].renshu : 1); //+茶位费
       
        var totalprice = product_price + tea_fee - leveldk_money - manjian_money - coupon_money + cuxiao_money;
        if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费

        allbuydata[k].totalprice = totalprice.toFixed(2);
        alltotalprice += totalprice;
      }

      if (that.usescore) {
        var scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣
      } else {
        var scoredk_money = 0;
      }

      var oldalltotalprice = alltotalprice;
      alltotalprice = alltotalprice - scoredk_money;
      if (alltotalprice < 0) alltotalprice = 0;

      var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例
      var scoremaxtype = parseInt(that.userinfo.scoremaxtype);
      var scoredkmaxmoney = parseFloat(that.userinfo.scoredkmaxmoney);

      if (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > oldalltotalprice * scoredkmaxpercent * 0.01) {
        scoredk_money = oldalltotalprice * scoredkmaxpercent * 0.01;
        alltotalprice = oldalltotalprice - scoredk_money;
      } else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {
        scoredk_money = scoredkmaxmoney;
        alltotalprice = oldalltotalprice - scoredk_money;
      }

      if (alltotalprice < 0) alltotalprice = 0;
      alltotalprice = alltotalprice.toFixed(2);
      that.alltotalprice = alltotalprice;
      that.allbuydata = allbuydata;
    },
		chooseCoupon:function(e){
      var allbuydata = this.allbuydata;
			var bid = e.bid;
			var couponrid = e.rid;
      var couponkey = e.key;
			if (couponrid == allbuydata[bid].couponrid) {
        allbuydata[bid].couponkey = 0;
        allbuydata[bid].couponrid = 0;
        allbuydata[bid].coupontype = 1;
        allbuydata[bid].coupon_money = 0;
        this.allbuydata = allbuydata;
        this.couponvisible = false;
      } else {
        var couponList = allbuydata[bid].couponList;
        var coupon_money = couponList[couponkey]['money'];
        var coupontype = couponList[couponkey]['type'];
        allbuydata[bid].couponkey = couponkey;
        allbuydata[bid].couponrid = couponrid;
        allbuydata[bid].coupontype = coupontype;
        allbuydata[bid].coupon_money = coupon_money;
        this.allbuydata = allbuydata;
        this.couponvisible = false;
      }
      this.calculatePrice();
		},
    //提交并支付
    topay: function () {
      var that = this;
      var usescore = this.usescore;
      var frompage = that.opt.frompage ? that.opt.frompage : '';
      var allbuydata = that.allbuydata;
      var buydata = [];
			
			for (var i in allbuydata) {
				if(that.ordertype == 'create_order'){
					if(allbuydata[i].tea_fee > 0 && allbuydata[i].renshu==0){
						app.error('请选择就餐人数');
					 return;
					}
				}
				buydata.push({
					bid: allbuydata[i].bid,
					prodata: allbuydata[i].prodatastr,
					cuxiaoid: allbuydata[i].cuxiaoid,
					couponrid: allbuydata[i].couponrid,
					renshu: allbuydata[i].renshu,
					message: allbuydata[i].message,
					field1: allbuydata[i].field1,
					field2: allbuydata[i].field2,
					field3: allbuydata[i].field3,
					field4: allbuydata[i].field4,
					field5: allbuydata[i].field5
				});
			}
      
			app.showLoading('提交中');
			var apiurl = 'ApiRestaurantShop/createOrder';
			if(that.ordertype == 'edit_order'){
				apiurl = 'ApiRestaurantShop/editOrder';
			}
      app.post(apiurl, {frompage: frompage,buydata: buydata,tableid:that.tableId,usescore: usescore}, function (res) {
				app.showLoading(false);
        if (res.status == 0) {
          //that.showsuccess(res.data.msg);
          app.error(res.msg);
          return;
        }
				if(frompage == 'admin')
					app.goto('/admin/restaurant/tableWaiterDetail?id=' + that.tableId, 'redirect');
				else {
					if(res.pay_after && res.pay_after != 1)
						app.goto('/pages/pay/pay?id=' + res.payorderid, 'redirect');
					else
						app.goto('/restaurant/shop/orderlist', 'redirect');
						
				}
      });
    },
		showRenshuSelect:function(e){
			this.renshuvisible = true;
      this.bid = e.currentTarget.dataset.bid;
		},
		changerenshu: function (e) {
      var that = this;
      that.renshu = e.currentTarget.dataset.id;
		},
    chooseRenshu: function () {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = that.bid;
			that.allbuydata[bid].renshu = that.renshu
			this.renshuvisible = false;
			that.calculatePrice();
		},
    showCouponList: function (e) {
      this.couponvisible = true;
      this.bid = e.currentTarget.dataset.bid;
    },
    handleClickMask: function () {
      this.couponvisible = false;
      this.cuxiaovisible = false;
      this.renshuvisible = false;
    },
    showCuxiaoList: function (e) {
      this.cuxiaovisible = true;
      this.bid = e.currentTarget.dataset.bid;
    },
    changecx: function (e) {
      var that = this;
      var cxid = e.currentTarget.dataset.id;
      console.log(cxid);
      that.cxid = cxid;
      if (cxid == 0) {
        that.cuxiaoinfo = false;
        return;
      }
      app.post("ApiRestaurantShop/getcuxiaoinfo", {id: cxid}, function (res) {
        that.cuxiaoinfo = res;
      });
    },
    chooseCuxiao: function () {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = that.bid;
      var cxid = that.cxid;
      if (cxid == 0) {
        allbuydata[bid].cuxiaoid = '';
        allbuydata[bid].cuxiao_money = 0;
        allbuydata[bid].cuxiaoname = '不使用促销';
      } else {
        var cxtype = that.cuxiaoinfo.info.type;
				console.log(cxtype);
        if (cxtype == 1 || cxtype == 6) {
          //满额立减 满件立减
          allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'] * -1;
        } else if (cxtype == 2) {
          //满额赠送
          allbuydata[bid].cuxiao_money = 0;
        } else if (cxtype == 3) {
          //加价换购
          allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'];
        } else if (cxtype == 4 || cxtype == 5) {
					var product_price = parseFloat(allbuydata[bid].product_price);
					var leveldk_money = parseFloat(allbuydata[bid].leveldk_money); //会员折扣
					var manjian_money = parseFloat(allbuydata[bid].manjian_money); //满减活动
          //满额打折 满件打折
          allbuydata[bid].cuxiao_money = (1 - that.cuxiaoinfo.info['zhekou'] * 0.1) * (product_price - leveldk_money - manjian_money) * -1;
        }
        allbuydata[bid].cuxiaoid = cxid;
        allbuydata[bid].cuxiaotype = cxtype;
        allbuydata[bid].cuxiaoname = that.cuxiaoinfo.info['name'];
      }
      this.allbuydata = allbuydata;
      this.cuxiaovisible = false;
      this.calculatePrice();
    },
  }
}
</script>
<style>
.address-add{ width:94%;margin:20rpx 3%;background:#fff;border-radius:20rpx;padding: 20rpx 3%;min-height:140rpx;}
.address-add .f1{margin-right:20rpx}
.address-add .f1 .img{ width: 66rpx; height: 66rpx; }
.address-add .f2{ color: #666; }
.address-add .f3{ width: 26rpx; height: 26rpx;}

.linkitem{width: 100%;padding:1px 0;background: #fff;display:flex;align-items:center}
.linkitem .f1{width:160rpx;color:#111111}
.linkitem .input{height:50rpx;padding-left:10rpx;color:#222222;font-weight:bold;font-size:28rpx;flex:1}

.buydata{width:94%;margin:0 3%;background:#fff;margin-bottom:20rpx;border-radius:20rpx;}

.btitle{width:100%;padding:20rpx 20rpx;display:flex;align-items:center;color:#111111;font-weight:bold;font-size:30rpx}
.btitle .img{width:34rpx;height:34rpx;margin-right:10rpx}

.bcontent{width:100%;padding:0 20rpx}

.product{width:100%;border-bottom:1px solid #f4f4f4} 
.product .item{width:100%; padding:20rpx 0;background:#fff;border-bottom:1px #ededed dashed;}
.product .item:last-child{border:none}
.product .info{padding-left:20rpx;}
.product .info .f1{color: #222222;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.product .info .f2{color: #999999; font-size:24rpx}
.product .info .f3{color: #FF4C4C; font-size:28rpx;display:flex;align-items:center;margin-top:10rpx}
.product image{ width:140rpx;height:140rpx}

.freight{width:100%;padding:20rpx 0;background:#fff;display:flex;flex-direction:column;}
.freight .f1{color:#333;margin-bottom:10rpx}
.freight .f2{color: #111111;text-align:right;flex:1}
.freight .f3{width: 24rpx;height:28rpx;}
.freighttips{color:red;font-size:24rpx;}

.freight-ul{width:100%;display:flex;}
.freight-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:24rpx;color:#6C737F;font-size:24rpx;text-align: center;height:48rpx; line-height:48rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}


.price{width:100%;padding:20rpx 0;background:#fff;display:flex;align-items:center}
.price .f1{color:#333}
.price .f2{ color:#111;font-weight:bold;text-align:right;flex:1}
.price .f3{width: 24rpx;height:24rpx;}

.scoredk{width:94%;margin:0 3%;margin-bottom:20rpx;border-radius:20rpx;padding:24rpx 20rpx; background: #fff;display:flex;align-items:center}
.scoredk .f1{color:#333333}
.scoredk .f2{ color: #999999;text-align:right;flex:1}

.remark{width: 100%;padding:16rpx 0;background: #fff;display:flex;align-items:center}
.remark .f1{color:#333;width:200rpx}
.remark input{ border:0px solid #eee;height:70rpx;padding-left:10rpx;text-align:right}

.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding:0 20rpx;display:flex;align-items:center;z-index:8}
.footer .text1 {height:110rpx;line-height:110rpx;color: #2a2a2a;font-size: 30rpx;}
.footer .text1  text{color: #e94745;font-size: 32rpx;}
.footer .op{width: 200rpx;height:80rpx;line-height:80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius:44rpx}

.storeitem{width: 100%;padding:20rpx 0;display:flex;flex-direction:column;color:#333}
.storeitem .panel{width: 100%;height:60rpx;line-height:60rpx;font-size:28rpx;color:#333;margin-bottom:10rpx;display:flex}
.storeitem .panel .f1{color:#333}
.storeitem .panel .f2{ color:#111;font-weight:bold;text-align:right;flex:1}
.storeitem .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;border-bottom:0 solid #eee;padding:8rpx 20rpx;}
.storeitem .radio-item:last-child{border:0}
.storeitem .radio-item .f1{color:#666;flex:1}
.storeitem .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left:30rpx}
.storeitem .radio .radio-img{width:100%;height:100%}

.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}
.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}
.pstime-item .radio .radio-img{width:100%;height:100%}

.cuxiao-desc{width:100%}
.cuxiao-item{display: flex;padding:0 40rpx 20rpx 40rpx;}
.cuxiao-item .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;flex:1}
.cuxiao-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}
.cuxiao-item .radio .radio-img{width:100%;height:100%}
</style>