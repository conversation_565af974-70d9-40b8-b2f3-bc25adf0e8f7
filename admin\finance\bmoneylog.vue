<template>
<view class="container">
	<block v-if="isload">
		<view class="content">
			<view v-for="(item, index) in datalist" :key="index" class="item">
				<view class="header">
					<view class="left">
						<text class="remark">{{item.remark}}</text>
						<view class="sub-info">
							<text>订单号:{{item.orderid}}</text>
							<text class="dot">·</text>
							<text>{{dateFormat(item.createtime)}}</text>
						</view>
					</view>
					<view class="right">
						<text class="money" :class="item.money>0?'income':'expense'">
							{{item.money>0?'+':''}}{{item.money}}
						</text>
						<text class="balance">余额:{{item.after}}</text>
					</view>
				</view>
				<view class="info" v-if="item.deduct_money!='0' || item.afterusertotal!='0'">
					<text v-if="item.deduct_money!='0'">
						扣除:{{item.deduct_money}} <text class="reason">({{item.deduct_reason}})</text>
					</text>
					<text v-if="item.afterusertotal!='0'">用户总额:{{item.afterusertotal}}</text>
				</view>
			</view>
		</view>
		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			canwithdraw:false,
			textset:{},
      st: 0,
      datalist: [],
      pagenum: 1,
			nodata:false,
      nomore: false
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st || 0;
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata(true);
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var st = that.st;
			that.nodata = false;
			that.nomore = false;
			that.loading = true;
      app.post('ApiAdminFinance/bmoneylog', {pagenum: pagenum}, function (res) {
				that.loading = false;
        var data = res.data;
        if (pagenum == 1) {
					that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
  }
};
</script>
<style>
.container{ width:100%;display:flex;flex-direction:column}
.content{ width:94%;margin:0 3% 20rpx 3%;}
.content .item{
    width:100%;
    background:#fff;
    margin:10rpx 0;
    padding:16rpx;
    border-radius:6rpx;
}
.content .item .header{
    display:flex;
    justify-content:space-between;
}
.content .item .header .left{
    flex:1;
    margin-right:20rpx;
}
.content .item .header .remark{
    display:block;
    color:#333;
    font-size:28rpx;
    margin-bottom:6rpx;
}
.content .item .header .sub-info{
    font-size:22rpx;
    color:#999;
}
.content .item .header .sub-info .dot{
    margin:0 6rpx;
}
.content .item .header .right{
    text-align:right;
}
.content .item .header .money{
    display:block;
    font-size:32rpx;
    font-weight:500;
    margin-bottom:4rpx;
}
.content .item .header .money.income{
    color:#03bc01;
}
.content .item .header .money.expense{
    color:#ff4d4f;
}
.content .item .header .balance{
    font-size:22rpx;
    color:#999;
}
.content .item .info{
    margin-top:10rpx;
    padding-top:10rpx;
    border-top:1rpx solid #f5f5f5;
    font-size:22rpx;
    color:#666;
}
.content .item .info .reason{
    color:#999;
}

.data-empty{background:#fff}
</style>