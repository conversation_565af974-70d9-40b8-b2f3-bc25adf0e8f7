<template>
<view class="page">
	<!-- 自定义导航栏 -->
	<view class="custom-navbar">
		<view class="navbar-left" @tap="goBack">
			<text class="back-arrow">‹</text>
		</view>
		<view class="navbar-center">
			<text class="navbar-title">AI在线舌诊</text>
		</view>
		<view class="navbar-right">
			<!-- 测试按钮 -->
			<text class="test-btn" @tap="showTipModal">测试</text>
			<text class="test-btn" @tap="openQuestionModal">测试2</text>
		</view>
	</view>
	
	<!-- 主要内容区域 -->
	<view class="content-container">
		<!-- 上半部分：舌头图片显示 -->
		<view class="image-section">
			<view class="tongue-display">
				<image class="tongue-image" :src="tongueImageUrl || pre_url+'/static/img/shezhen-analysis-frame.png'"/>
				<!-- 扫描特效 -->
				<view class="scan-effect" v-if="currentStep <= 6">
					<view class="scan-line"></view>
					<view class="scan-border"></view>
				</view>
			</view>
		</view>
		
		<!-- 下半部分：分析进度 -->
		<view class="analysis-section">
			<view class="analysis-header">
				<text class="analysis-title">智能分析中</text>
			</view>
			
			<view class="progress-list">
				<view
					v-for="step in displayedSteps"
					:key="step.id"
					class="progress-item"
					:class="{'completed': step.completed, 'active': currentStep === step.id}"
				>
					<text class="progress-text">{{step.text}}</text>
					<view class="progress-indicator">
						<!-- 未开始：空圆圈 -->
						<view v-if="currentStep < step.id" class="status-circle"></view>
						<!-- 进行中：转圈动画 -->
						<view v-else-if="currentStep === step.id && !step.completed" class="status-loading"></view>
						<!-- 已完成：对号图标 -->
						<image v-else-if="step.completed" class="status-check" :src="pre_url+'/static/img/shezhen-duihao.png'"/>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 温馨提示弹窗 -->
	<view class="modal-overlay" v-if="showModal" @tap="hideModal">
		<view class="modal-container" @tap.stop>
			<view class="modal-header">
				<text class="modal-title">AI在线舌诊</text>
			</view>

			<view class="modal-tip-header">
				<image class="tip-warning-icon" :src="pre_url+'/static/img/shezhen-warning-icon.png'"/>
				<view class="tip-title-row">
					<text class="tip-title">温馨提示</text>
					<image class="tip-close-icon" :src="pre_url+'/static/img/shezhen-close-icon.png'" @tap="hideModal"/>
				</view>
			</view>

			<view class="modal-content">
				<text class="tip-content">
					请拍摄带有舌头，清晰的彩色照片，或使用后置摄像头拍摄
				</text>
			</view>

			<view class="modal-actions">
				<view class="action-btn primary" @tap="retakePhoto">
					<text class="btn-text">重新拍摄</text>
				</view>
				<view class="action-btn secondary" @tap="switchToBackCamera">
					<text class="btn-text secondary">切换后置摄像头拍摄</text>
				</view>
			</view>
		</view>
	</view>

	<!-- 问答弹窗 -->
	<view class="question-modal-overlay" v-if="showQuestionModal" @tap="hideQuestionModal">
		<view class="question-modal-container" @tap.stop>
			<!-- 背景分析进度 -->
			<view class="background-analysis">
				<view class="bg-image-section">
					<image class="bg-tongue-image" :src="tongueImageUrl || pre_url+'/static/img/shezhen-analysis-frame.png'"/>
				</view>
				<view class="bg-analysis-section">
					<view class="bg-analysis-header">
						<text class="bg-analysis-title">智能分析中</text>
					</view>
					<view class="bg-progress-list">
						<view class="bg-progress-item">
							<text class="bg-progress-text">解析舌色...</text>
							<view class="bg-status-loading"></view>
						</view>
						<view class="bg-progress-item">
							<text class="bg-progress-text">解析舌苔...</text>
							<image class="bg-status-check" :src="pre_url+'/static/img/shezhen-duihao.png'"/>
						</view>
						<view class="bg-progress-item">
							<text class="bg-progress-text">解析舌型...</text>
							<image class="bg-status-check" :src="pre_url+'/static/img/shezhen-duihao.png'"/>
						</view>
						<view class="bg-progress-item">
							<text class="bg-progress-text">分析体质...</text>
							<image class="bg-status-check" :src="pre_url+'/static/img/shezhen-duihao.png'"/>
						</view>
					</view>
				</view>
			</view>

			<!-- 前景问答卡片 -->
			<view class="question-card">
				<view class="question-header">
					<text class="question-header-text">再答1题领取报告</text>
				</view>
				<view class="question-content">
					<text class="question-text">是否感觉舌头黏糊糊的？</text>
					<view class="answer-buttons">
						<view class="answer-btn" :class="{'selected': selectedAnswer === 'yes'}" @tap="selectAnswer('yes')">
							<text class="answer-text">有</text>
						</view>
						<view class="answer-btn" :class="{'selected': selectedAnswer === 'no'}" @tap="selectAnswer('no')">
							<text class="answer-text">没有</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			pre_url: '',
			tongueImageUrl: '',
			currentStep: 1,
			step1Completed: false,
			step2Completed: false,
			step3Completed: false,
			step4Completed: false,
			step5Completed: false,
			step6Completed: false,
			analysisTimer: null,
			// 弹窗控制
			showModal: false,
			showQuestionModal: false,
			selectedAnswer: '',
			// 所有分析步骤
			allSteps: [
				{ id: 1, text: '解析舌色...', completed: false },
				{ id: 2, text: '解析舌苔...', completed: false },
				{ id: 3, text: '解析舌型...', completed: false },
				{ id: 4, text: '分析体质...', completed: false },
				{ id: 5, text: '病症勘察...', completed: false },
				{ id: 6, text: '输出方案...', completed: false }
			],
			// 当前显示的步骤索引范围
			displayStartIndex: 0
		};
	},
	
	onLoad: function(opt) {
		this.pre_url = app.globalData.pre_url;
		this.tongueImageUrl = opt.imageUrl || '';
		
		uni.setNavigationBarTitle({
			title: 'AI在线舌诊'
		});
		
		// 开始模拟分析过程
		this.startAnalysis();
	},
	
	onUnload: function() {
		// 清理定时器
		if (this.analysisTimer) {
			clearTimeout(this.analysisTimer);
		}
	},

	computed: {
		// 当前显示的步骤（最多4个）
		displayedSteps: function() {
			return this.allSteps.slice(this.displayStartIndex, this.displayStartIndex + 4);
		}
	},
	
	methods: {
		// 开始分析过程
		startAnalysis: function() {
			var that = this;

			// 第一步：解析舌色
			setTimeout(function() {
				that.completeStep(1);
				that.currentStep = 2;
			}, 1500);

			// 第二步：解析舌苔
			setTimeout(function() {
				that.completeStep(2);
				that.currentStep = 3;
			}, 3000);

			// 第三步：解析舌型
			setTimeout(function() {
				that.completeStep(3);
				that.currentStep = 4;
			}, 4500);

			// 第四步：分析体质
			setTimeout(function() {
				that.completeStep(4);
				that.currentStep = 5;
				that.updateScrollPosition();
			}, 6000);

			// 第五步：病症勘察
			setTimeout(function() {
				that.completeStep(5);
				that.currentStep = 6;
				that.updateScrollPosition();
			}, 7500);

			// 第六步：输出方案
			setTimeout(function() {
				that.completeStep(6);
				that.updateScrollPosition();
				// 分析完成，跳转到结果页面
				setTimeout(function() {
					that.goToResult();
				}, 1000);
			}, 9000);
		},

		// 完成指定步骤
		completeStep: function(stepId) {
			var step = this.allSteps.find(s => s.id === stepId);
			if (step) {
				step.completed = true;
			}
			// 同时更新对应的data属性（保持兼容性）
			this['step' + stepId + 'Completed'] = true;
		},

		// 更新滚动位置
		updateScrollPosition: function() {
			// 计算应该显示的起始索引
			// 目标：让当前步骤始终在显示范围内，优先显示在中间位置
			var targetStartIndex = 0;

			if (this.currentStep <= 4) {
				// 前4步：显示步骤1-4
				targetStartIndex = 0;
			} else if (this.currentStep === 5) {
				// 第5步：显示步骤2-5
				targetStartIndex = 1;
			} else if (this.currentStep === 6) {
				// 第6步：显示步骤3-6
				targetStartIndex = 2;
			}

			// 确保不超出边界
			var maxStartIndex = Math.max(0, this.allSteps.length - 4);
			this.displayStartIndex = Math.min(targetStartIndex, maxStartIndex);
		},

		// 跳转到结果页面
		goToResult: function() {
			uni.redirectTo({
				url: '/pagesB/shezhennew/result?imageUrl=' + encodeURIComponent(this.tongueImageUrl)
			});
		},
		
		// 返回
		goBack: function() {
			uni.navigateBack();
		},

		// 显示提示弹窗
		showTipModal: function() {
			this.showModal = true;
		},

		// 隐藏弹窗
		hideModal: function() {
			this.showModal = false;
		},

		// 重新拍摄
		retakePhoto: function() {
			this.hideModal();
			uni.navigateBack();
		},

		// 切换后置摄像头
		switchToBackCamera: function() {
			this.hideModal();
			uni.showToast({
				title: '已切换到后置摄像头',
				icon: 'none'
			});
			// 这里可以添加切换摄像头的逻辑
		},

		// 显示问答弹窗
		openQuestionModal: function() {
			this.showQuestionModal = true;
			this.selectedAnswer = '';
		},

		// 隐藏问答弹窗
		hideQuestionModal: function() {
			this.showQuestionModal = false;
		},

		// 选择答案
		selectAnswer: function(answer) {
			this.selectedAnswer = answer;
			// 延迟关闭弹窗，让用户看到选择效果
			setTimeout(() => {
				this.hideQuestionModal();
				uni.showToast({
					title: '答案已记录：' + (answer === 'yes' ? '有' : '没有'),
					icon: 'none'
				});
			}, 500);
		}
	}
};
</script>

<style>
.page {
	background-color: #ffffff;
	width: 100%;
	min-height: 100vh;
	position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 32rpx;
	background-color: #ffffff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	border-bottom: 1rpx solid #f0f0f0;
}

.navbar-left, .navbar-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-arrow {
	font-size: 48rpx;
	color: #333333;
	font-weight: 300;
}

.navbar-center {
	flex: 1;
	text-align: center;
}

.navbar-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

.test-btn {
	font-size: 28rpx;
	color: #007AFF;
	padding: 8rpx 16rpx;
	margin-left: 16rpx;
}

/* 主要内容区域 */
.content-container {
	padding-top: 88rpx;
	display: flex;
	flex-direction: column;
	height: 100vh;
}

/* 上半部分：图片区域 */
.image-section {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #ffffff;
	padding: 60rpx 32rpx;
}

.tongue-display {
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
}

.tongue-image {
	width: 416rpx;
	height: 376rpx;
	border-radius: 12rpx;
}

/* 扫描特效容器 */
.scan-effect {
	position: absolute;
	top: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 416rpx;
	height: 376rpx;
	border-radius: 12rpx;
	overflow: hidden;
	pointer-events: none;
}

/* 扫描线 */
.scan-line {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 4rpx;
	background: linear-gradient(90deg, transparent 0%, #00ff88 50%, transparent 100%);
	box-shadow: 0 0 20rpx #00ff88;
	animation: scanMove 2s ease-in-out infinite;
}

/* 扫描边框 */
/* .scan-border {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: 2rpx solid #00ff88;
	border-radius: 12rpx;
	opacity: 0.6;
	animation: borderPulse 2s ease-in-out infinite;
} */

/* 扫描线移动动画 */
@keyframes scanMove {
	0% {
		top: 0;
		opacity: 0;
	}
	10% {
		opacity: 1;
	}
	90% {
		opacity: 1;
	}
	100% {
		top: 372rpx; /* 376rpx - 4rpx */
		opacity: 0;
	}
}

/* 边框脉冲动画 */
@keyframes borderPulse {
	0%, 100% {
		opacity: 0.3;
		box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.4);
	}
	50% {
		opacity: 0.8;
		box-shadow: 0 0 20rpx 4rpx rgba(0, 255, 136, 0.2);
	}
}

/* 下半部分：分析进度区域 */
.analysis-section {
	background-color: #1e2639;
	padding: 40rpx 32rpx 0rpx; /* 增加底部内边距，确保有足够空间 */
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;
}

.analysis-header {
	text-align: center;
	margin-bottom: 34rpx;
}

.analysis-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #f5f5f5;
}

/* 进度列表 */
.progress-list {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 320rpx; /* 足够的高度显示4个完整项目：(40+34)*3+40=262rpx，留足余量 */
	overflow: hidden;
	position: relative;
}

.progress-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 312rpx;
	height: 40rpx;
	margin-bottom: 34rpx;
	transition: all 0.5s ease;
	flex-shrink: 0; /* 防止压缩 */
}

.progress-item:last-child {
	margin-bottom: 0;
}

.progress-text {
	font-size: 32rpx;
	font-weight: 700;
	color: #ffffff;
	opacity: 0.6;
	transition: opacity 0.3s ease;
}

.progress-item.active .progress-text,
.progress-item.completed .progress-text {
	opacity: 1;
}

.progress-indicator {
	width: 36rpx;
	height: 36rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 未开始状态：空圆圈 */
.status-circle {
	width: 34rpx;
	height: 34rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 50%;
}

/* 进行中状态：转圈动画 */
.status-loading {
	width: 34rpx;
	height: 34rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	border-top: 2rpx solid #2bc175;
	border-radius: 50%;
	animation: rotate 1.5s linear infinite;
}

/* 完成状态：对号图标 */
.status-check {
	width: 34rpx;
	height: 34rpx;
}

/* 转圈动画 */
@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.modal-container {
	background-color: #ffffff;
	border-radius: 22rpx;
	width: 622rpx;
	margin: 0 32rpx;
	position: relative;
}

.modal-header {
	text-align: center;
	padding: 40rpx 0 0 0;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #000000;
}

.modal-tip-header {
	display: flex;
	align-items: center;
	padding: 46rpx 32rpx 0 32rpx;
}

.tip-warning-icon {
	width: 34rpx;
	height: 28rpx;
	margin-right: 16rpx;
}

.tip-title-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex: 1;
}

.tip-title {
	font-size: 40rpx;
	font-weight: 700;
	color: #000000;
}

.tip-close-icon {
	width: 34rpx;
	height: 28rpx;
}

.modal-content {
	padding: 46rpx 88rpx 0 88rpx;
}

.tip-content {
	font-size: 32rpx;
	color: #000000;
	text-align: center;
	line-height: 1.5;
}

.modal-actions {
	padding: 42rpx 64rpx 48rpx 64rpx;
	display: flex;
	flex-direction: column;
	gap: 32rpx;
}

.action-btn {
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.primary {
	background-color: #2bc175;
}

.action-btn.secondary {
	border: 2rpx solid #2bc175;
	background-color: transparent;
}

.btn-text {
	font-size: 32rpx;
	color: #f5f5f5;
	font-weight: 400;
}

.btn-text.secondary {
	color: #000000;
}

/* 问答弹窗样式 */
.question-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 9998;
}

.question-modal-container {
	width: 100%;
	height: 100%;
	position: relative;
}

/* 背景分析进度 */
.background-analysis {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.bg-image-section {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #ffffff;
	padding: 60rpx 32rpx;
}

.bg-tongue-image {
	width: 416rpx;
	height: 376rpx;
	border-radius: 12rpx;
}

.bg-analysis-section {
	background-color: #1e2639;
	padding: 40rpx 32rpx;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;
}

.bg-analysis-header {
	text-align: center;
	margin-bottom: 34rpx;
}

.bg-analysis-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #f5f5f5;
}

.bg-progress-list {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.bg-progress-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 312rpx;
	height: 40rpx;
	margin-bottom: 34rpx;
}

.bg-progress-item:last-child {
	margin-bottom: 0;
}

.bg-progress-text {
	font-size: 32rpx;
	font-weight: 700;
	color: #ffffff;
}

.bg-status-loading {
	width: 34rpx;
	height: 34rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	border-top: 2rpx solid #2bc175;
	border-radius: 50%;
	animation: rotate 1.5s linear infinite;
}

.bg-status-check {
	width: 34rpx;
	height: 34rpx;
}

/* 前景问答卡片 */
.question-card {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 560rpx;
	background-color: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
}

.question-header {
	background-color: #2bc175;
	height: 106rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.question-header-text {
	font-size: 36rpx;
	color: #ffffff;
	font-weight: 400;
}

.question-content {
	padding: 62rpx 82rpx 64rpx 82rpx;
}

.question-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #000000;
	text-align: center;
	margin-bottom: 70rpx;
}

.answer-buttons {
	display: flex;
	justify-content: space-between;
	gap: 56rpx;
}

.answer-btn {
	flex: 1;
	height: 92rpx;
	border: 2rpx solid #2bc175;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.answer-btn.selected {
	background-color: #2bc175;
}

.answer-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #2bc175;
	transition: color 0.3s ease;
}

.answer-btn.selected .answer-text {
	color: #ffffff;
}
</style>
