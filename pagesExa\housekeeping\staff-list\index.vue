<template>
  <view class="staff-list-container">
    <!-- 搜索区域 -->
    <view class="search-section">
      <u-search
        v-model="searchKeyword"
        :show-action="false"
        placeholder="搜索员工姓名或工号"
        @change="handleSearch"
      ></u-search>
    </view>

    <!-- 员工列表 -->
    <view class="staff-list">
      <view class="staff-card" v-for="(item, index) in staffList" :key="index" @click="handleStaffClick(item)">
        <view class="staff-avatar">
          <u-avatar :src="item.avatar" size="large"></u-avatar>
        </view>
        <view class="staff-info">
          <view class="staff-name">{{item.name}}</view>
          <view class="staff-id">工号：{{item.id}}</view>
          <view class="staff-status" :class="item.status === '在岗' ? 'status-active' : 'status-inactive'">
            {{item.status}}
          </view>
        </view>
        <view class="staff-details">
          <view class="detail-item">
            <text class="label">评分</text>
            <u-rate v-model="item.rating" readonly size="18"></u-rate>
          </view>
          <view class="detail-item">
            <text class="label">服务次数</text>
            <text class="value">{{item.serviceCount}}次</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部分页 -->
    <view class="pagination">
      <u-loadmore :status="loadMoreStatus" @loadmore="loadMore"/>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      currentPage: 1,
      loadMoreStatus: 'loadmore',
      staffList: [
        {
          id: '001',
          name: '张阿姨',
          avatar: '/static/default-avatar.png',
          status: '在岗',
          rating: 4.5,
          serviceCount: 128
        },
        {
          id: '002',
          name: '李阿姨',
          avatar: '/static/default-avatar.png',
          status: '休息中',
          rating: 4.8,
          serviceCount: 256
        }
      ]
    }
  },
  methods: {
    handleSearch(value) {
      // 实现搜索逻辑
      console.log('搜索关键词：', value)
    },
    handleStaffClick(staff) {
      // 点击员工卡片，跳转到详情页
      uni.navigateTo({
        url: `/pagesExa/housekeeping/staff-detail/index?id=${staff.id}`
      })
    },
    loadMore() {
      // 实现加载更多逻辑
      if (this.currentPage >= 3) {
        this.loadMoreStatus = 'nomore'
        return
      }
      this.loadMoreStatus = 'loading'
      setTimeout(() => {
        this.currentPage++
        // 模拟加载更多数据
        this.loadMoreStatus = 'loadmore'
      }, 1000)
    }
  }
}
</script>

<style lang="scss">
.staff-list-container {
  padding: 20rpx;
  background-color: #f5f5f5;

  .search-section {
    margin-bottom: 20rpx;
  }

  .staff-list {
    .staff-card {
      background-color: #ffffff;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

      .staff-avatar {
        margin-right: 20rpx;
      }

      .staff-info {
        flex: 1;

        .staff-name {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 8rpx;
        }

        .staff-id {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 8rpx;
        }

        .staff-status {
          display: inline-block;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          font-size: 24rpx;

          &.status-active {
            background-color: #e8f5e9;
            color: #4caf50;
          }

          &.status-inactive {
            background-color: #ffebee;
            color: #f44336;
          }
        }
      }

      .staff-details {
        .detail-item {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          .label {
            font-size: 24rpx;
            color: #999;
            margin-right: 10rpx;
          }

          .value {
            font-size: 24rpx;
            color: #333;
          }
        }
      }
    }
  }
}
</style> 