<template>
<view>
	<block v-if="isload">
		<view class="container">
			<view class="header">
				<text class="title" v-if="detail.showname==1">{{detail.name}}</text>
				<view class="training-meta" v-if="detail.subname">
					<text class="subtitle">{{detail.subname}}</text>
				</view>
				<view class="artinfo" v-if="detail.showsendtime==1 || detail.showauthor==1 || detail.showreadcount==1">
					<text class="t1" v-if="detail.showsendtime==1">{{detail.createtime}}</text>
					<text class="t2" v-if="detail.showauthor==1">{{detail.author}}</text>
					<text class="t3" v-if="detail.showreadcount==1">阅读：{{detail.readcount}}</text>
				</view>
				
				<!-- 课程信息显示 -->
				<view class="course-binding" v-if="detail.kcname">
					<view class="course-info-card" :style="{background:'linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
						<view class="course-left">
							<image class="course-pic" :src="detail.kcpic || '/static/img/default-course.png'" mode="aspectFill"></image>
						</view>
						<view class="course-right">
							<view class="course-badge">
								<!-- <image class="course-icon" src="/static/img/course-icon.png"></image> -->
								<text class="course-label">关联课程</text>
							</view>
							<text class="course-title">{{detail.kcname}}</text>
							<view class="course-meta">
								<text class="course-price" v-if="detail.kcprice > 0">¥{{detail.kcprice}}</text>
								<text class="course-price free" v-else>免费</text>
								<text class="course-tip" @tap="toCourse" :data-id="detail.kcid">查看课程 ></text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 权限检查和内容显示 -->
				<block v-if="detail.has_permission || detail.need_buy == 0">
					<view style="padding:8rpx 0">
						<dp :pagecontent="pagecontent"></dp>
					</view>
				</block>
				<block v-else>
					<!-- 无权限时显示购买提示 -->
					<view class="no-permission-container">
						<view class="preview-content">
							<view class="preview-text">{{detail.preview_content || '该训练营需要购买相关课程后才能查看完整内容'}}</view>
						</view>
						<view class="buy-prompt-card" v-if="buyInfo" :style="{background:'linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
							<view class="lock-icon">
								<image src="/static/img/lock-icon.png" class="lock-img"></image>
							</view>
							<view class="buy-content">
								<text class="buy-title">解锁完整内容</text>
								<text class="buy-subtitle">购买《{{buyInfo.kc_name}}》课程即可学习</text>
								<view class="course-preview" v-if="buyInfo.kc_pic">
									<image class="course-thumb" :src="buyInfo.kc_pic" mode="aspectFill"></image>
									<view class="course-details">
										<text class="course-price">¥{{buyInfo.kc_price}}</text>
									</view>
								</view>
								<button class="buy-btn" @tap="buyCourse" :style="{background: t('color1')}">
									立即购买课程
								</button>
							</view>
						</view>
					</view>
				</block>
			</view>
		
			<!--评论部分 - 只有有权限才显示评论-->
			<block v-if="detail.canpl==1 && (detail.has_permission || detail.need_buy == 0)">
			<view class="plbox">
				<view class="plbox_title"><text class="t1">评论</text><text>({{plcount}})</text></view>
				<view class="plbox_content">
					<block v-for="(item, idx) in datalist" :key="item.id">
					<view class="item1 flex">
						<view class="f1 flex0"><image :src="item.headimg"></image></view>
						<view class="f2 flex-col">
							<text class="t1">{{item.nickname}}</text>
							<view class="t2 plcontent"><parse :content="item.content" /></view>
							<block v-if="item.replylist.length>0">
							<view class="relist">
								<block v-for="(hfitem, index) in item.replylist" :key="index">
								<view class="item2">
									<view>{{hfitem.nickname}}：</view>
									<view class="f2 plcontent"><parse :content="hfitem.content" /></view>
								</view>
								 </block>
							</view>
							</block>
							<view class="t3 flex">
								<text>{{item.createtime}}</text>
								<view class="flex1"><text v-if="detail.canplrp==1" class="phuifu" style="cursor:pointer" @tap="goto" :data-url="'../training/pinglun?type=1&id=' + detail.id + '&hfid=' + item.id">回复</text></view>
								<view class="flex-y-center pzan" @tap="pzan" :data-id="item.id" :data-index="idx"><image :src="'/static/img/zan-' + (item.iszan==1?'2':'1') + '.png'"></image>{{item.zan}}</view>
							</view>
						</view>
					</view>
					</block>
				</view>
				<loading v-if="loading"></loading>
			</view>
			<view style="height:160rpx"></view>
			<view class="pinglun notabbarbot">
				<view class="pinput" @tap="goto" :data-url="'../training/pinglun?type=0&id=' + detail.id">发表评论</view>
				<view class="zan flex-y-center" @tap="zan" :data-id="detail.id">
					<image :src="'/static/img/zan-' + (iszan?'2':'1') + '.png'"/><text style="padding-left:2px">{{detail.zan}}</text>
				</view>
			</view>
			</block>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
            opt:{},
            loading:false,
            isload: false,
            menuindex:-1,
            pre_url:app.globalData.pre_url,

            detail:[],
            datalist: [],
            pagenum: 1,
            id: 0,
            pagecontent: "",
            title: "",
            sharepic: "",
            nodata:false,
            nomore:false,
            iszan: "",
			plcount:0,
			buyInfo: null,
    };
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		// 2025-01-03 22:55:53,565-INFO-[training_detail][onLoad_001] 初始化训练营详情页面
		console.log('2025-01-03 22:55:53,565-INFO-[training_detail][onLoad_001] 初始化训练营详情页面, id:', opt.id);
    this.getdata();
  },
	onPullDownRefresh: function () {
		// 2025-01-03 22:55:53,565-INFO-[training_detail][onPullDownRefresh_001] 下拉刷新训练营详情
		console.log('2025-01-03 22:55:53,565-INFO-[training_detail][onPullDownRefresh_001] 下拉刷新训练营详情');
		this.getdata();
	},
	onShareAppMessage:function(){
		var that = this;
		return this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic,callback: function() {
					that.sharecallback();
				}});
	},
	onShareTimeline:function(){
		var that = this;
		var sharewxdata = this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic,
			callback: function() {
					that.sharecallback();
				}});
		var query = (sharewxdata.path).split('?')[1];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore && this.detail.canpl==1 && (this.detail.has_permission || this.detail.need_buy == 0)) {
      // 2025-01-03 22:55:53,565-INFO-[training_detail][onReachBottom_001] 上拉加载更多评论
      console.log('2025-01-03 22:55:53,565-INFO-[training_detail][onReachBottom_001] 上拉加载更多评论, pagenum:', this.pagenum + 1);
      this.pagenum = this.pagenum + 1
      this.getpllist();
    }
  },
  methods: {
	  sharecallback: function() {
	  	app.post("ApiKechengTraining/giveScorenum",{}, function(res) {
	  		// 2025-01-03 22:55:53,565-INFO-[training_detail][sharecallback_001] 分享训练营获得积分
	  		console.log('2025-01-03 22:55:53,565-INFO-[training_detail][sharecallback_001] 分享训练营获得积分:', res);
	  	});	
	  },
		getdata:function(){
			var that = this;
			var id = that.opt.id;
			
			// 2025-01-03 22:55:53,565-INFO-[training_detail][getdata_001] 开始获取训练营详情
			console.log('2025-01-03 22:55:53,565-INFO-[training_detail][getdata_001] 开始获取训练营详情, id:', id);
			
			that.loading = true;
			app.get('ApiKechengTraining/detail', {id: id}, function (res) {
				that.loading = false;
				
				// 2025-01-03 22:55:53,565-INFO-[training_detail][getdata_002] 获取训练营详情响应
				console.log('2025-01-03 22:55:53,565-INFO-[training_detail][getdata_002] 获取训练营详情响应, status:', res.status);
				
				if (res.status == 1){
					that.detail = res.detail;
					that.pagecontent = res.pagecontent;
					that.plcount = res.plcount || 0;
					that.iszan = res.iszan;
					that.title = res.detail.name;
					that.sharepic = res.detail.pic;
					
					// 设置购买信息
					if (res.detail.buy_info) {
						that.buyInfo = res.detail.buy_info;
						// 2025-01-03 22:55:53,565-INFO-[training_detail][getdata_003] 设置购买信息
						console.log('2025-01-03 22:55:53,565-INFO-[training_detail][getdata_003] 设置购买信息:', res.detail.buy_info);
					}

					uni.setNavigationBarTitle({
						title: res.detail.name
					});
					
					// 2025-01-03 22:55:53,565-INFO-[training_detail][getdata_004] 权限检查结果
					console.log('2025-01-03 22:55:53,565-INFO-[training_detail][getdata_004] 权限检查结果, has_permission:', res.detail.has_permission, 'need_buy:', res.detail.need_buy);
				} else {
					// 2025-01-03 22:55:53,565-ERROR-[training_detail][getdata_005] 获取训练营详情失败
					console.error('2025-01-03 22:55:53,565-ERROR-[training_detail][getdata_005] 获取训练营详情失败:', res.msg);
					app.alert(res.msg);
				}
				that.pagenum = 1;
				that.datalist = [];
				that.getpllist();
				that.loaded({title:res.detail ? res.detail.name : '',desc:res.detail ? res.detail.subname : '',pic:res.detail ? res.detail.pic : ''});
			});
		},
    getpllist: function () {
        var that = this;
        var pagenum = that.pagenum;
        
        // 只有有权限的情况下才获取评论
        if (!that.detail.has_permission && that.detail.need_buy == 1) {
        	// 2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_001] 无权限，跳过获取评论
        	console.log('2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_001] 无权限，跳过获取评论');
        	return;
        }
        
        // 2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_002] 开始获取评论列表
        console.log('2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_002] 开始获取评论列表, pagenum:', pagenum);
        
		that.loading = true;
		that.nodata = false;
		that.nomore = false;
        app.post('ApiKechengTraining/getpllist', {pagenum: pagenum,id: that.detail.id}, function (res) {
			that.loading = false;
			
			// 2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_003] 获取评论列表响应
			console.log('2025-01-03 22:55:53,565-INFO-[training_detail][getpllist_003] 获取评论列表响应, data_length:', res.data ? res.data.length : 0);
			
            var data = res.data || [];
            if (data.length == 0) {
                if(pagenum == 1){
                    that.nodata = true;
                }else{
                    that.nomore = true;
                }
            }
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
        });
    },
    zan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      
      // 2025-01-03 22:55:53,565-INFO-[training_detail][zan_001] 点赞训练营
      console.log('2025-01-03 22:55:53,565-INFO-[training_detail][zan_001] 点赞训练营, id:', id);
      
      app.post("ApiKechengTraining/zan", {id: id}, function (res) {
      	// 2025-01-03 22:55:53,565-INFO-[training_detail][zan_002] 点赞响应
      	console.log('2025-01-03 22:55:53,565-INFO-[training_detail][zan_002] 点赞响应, type:', res.type, 'zancount:', res.zancount);
      	
        if (res.type == 0) {
          //取消点赞
          var iszan = 0;
        } else {
          var iszan = 1;
        }
        that.iszan = iszan;
        that.detail.zan = res.zancount;
      });
    },
    pzan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var index = e.currentTarget.dataset.index;
      var datalist = that.datalist;
      
      // 2025-01-03 22:55:53,565-INFO-[training_detail][pzan_001] 点赞评论
      console.log('2025-01-03 22:55:53,565-INFO-[training_detail][pzan_001] 点赞评论, id:', id, 'index:', index);
      
      app.post("ApiKechengTraining/pzan", {id: id}, function (res) {
      	// 2025-01-03 22:55:53,565-INFO-[training_detail][pzan_002] 评论点赞响应
      	console.log('2025-01-03 22:55:53,565-INFO-[training_detail][pzan_002] 评论点赞响应, type:', res.type, 'zancount:', res.zancount);
      	
        if (res.type == 0) {
          //取消点赞
          var iszan = 0;
        } else {
          var iszan = 1;
        }

        datalist[index].iszan = iszan;
        datalist[index].zan = res.zancount;
        that.datalist = datalist;
      });
    },
    buyCourse: function() {
    	var that = this;
    	if (!that.buyInfo || !that.buyInfo.kc_id) {
    		app.error('课程信息错误');
    		return;
    	}
    	
    	// 2025-01-03 22:55:53,565-INFO-[training_detail][buyCourse_001] 跳转到课程购买页面
    	console.log('2025-01-03 22:55:53,565-INFO-[training_detail][buyCourse_001] 跳转到课程购买页面, kc_id:', that.buyInfo.kc_id);
    	
    	// 跳转到课程详情购买页面
    	app.goto('/activity/kecheng/product?id=' + that.buyInfo.kc_id);
    },
    toCourse: function(e) {
    	var that = this;
    	var kcid = e.currentTarget.dataset.id;
    	if (!kcid) {
    		app.error('课程信息错误');
    		return;
    	}
    	
    	// 2025-01-03 22:55:53,565-INFO-[training_detail][toCourse_001] 跳转到课程详情页面
    	console.log('2025-01-03 22:55:53,565-INFO-[training_detail][toCourse_001] 跳转到课程详情页面, kcid:', kcid);
    	
    	// 跳转到课程详情页面
    	app.goto('/activity/kecheng/product?id=' + kcid);
    }
  }
};
</script>
<style>
.header{ background-color: #fff;padding: 10rpx 20rpx 0 20rpx;position: relative;display:flex;flex-direction:column;}
.header .title{width:100%;font-size: 36rpx;color:#333;line-height: 1.4;margin:10rpx 0;margin-top:20rpx;font-weight:bold}
.header .training-meta{width:100%;margin:10rpx 0;}
.header .subtitle{font-size:28rpx;color:#666;line-height:1.5}
.header .artinfo{width:100%;font-size:28rpx;color: #8c8c8c;font-style: normal;overflow: hidden;display:flex;margin:10rpx 0;}
.header .artinfo .t1{padding-right:8rpx}
.header .artinfo .t2{color:#777;padding-right:8rpx}
.header .artinfo .t3{text-align:right;flex:1;}

/* 课程绑定信息样式 */
.course-binding {
  margin: 20rpx 0;
}
.course-info-card {
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.course-left {
  margin-right: 20rpx;
}
.course-pic {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}
.course-right {
  flex: 1;
  text-align: left;
}
.course-badge {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.course-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.course-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 24rpx;
}
.course-title {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  flex: 1;
}
.course-meta {
  margin-top: 10rpx;
}
.course-price {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 10rpx;
}
.course-price.free {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}
.course-tip {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  cursor: pointer;
}

/* 无权限容器样式 */
.no-permission-container {
  padding: 20rpx 0;
}
.preview-content {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border-left: 4rpx solid #ffc107;
}
.preview-text {
  color: #666;
  font-size: 28rpx;
  line-height: 1.6;
  text-align: center;
}

/* 购买提示卡片样式 */
.buy-prompt-card {
  border-radius: 16rpx;
  padding: 40rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);
}
.buy-prompt-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}
.lock-icon {
  text-align: center;
  margin-bottom: 20rpx;
}
.lock-img {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(10);
}
.buy-content {
  text-align: center;
  position: relative;
  z-index: 2;
}
.buy-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
}
.buy-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  display: block;
  margin-bottom: 24rpx;
}
.course-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 24rpx 0;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}
.course-thumb {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.course-details {
  flex: 1;
  text-align: left;
}
.course-price {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}
.buy-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: bold;
  margin-top: 20rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s;
}
.buy-btn:active {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(0.95);
}

.pinglun{ width:96%;max-width:750px;margin:0 auto;position:fixed;display:flex;align-items:center;bottom:0;left:0;right:0;height:100rpx;background:#fff;z-index:10;border-top:1px solid #f7f7f7;padding:0 2%;box-sizing:content-box}
.pinglun .pinput{flex:1;color:#a5adb5;font-size:32rpx;padding:0;line-height:100rpx}
.pinglun .zan{padding:0 12rpx;line-height:100rpx;border-radius:50rpx}
.pinglun .zan image{width:48rpx;height:48rpx}
.pinglun .zan span{height:40rpx;line-height:50rpx;font-size:32rpx}
.pinglun .buybtn{margin-left:0.08rpx;background:#31C88E;height:72rpx;line-height:72rpx;padding:0 20rpx;color:#fff;border-radius:6rpx}

.plbox{width:100%;padding:40rpx 20rpx;background:#fff;margin-top:10px}
.plbox_title{font-size:28rpx;height:60rpx;line-height:60rpx;margin-bottom:20rpx}
.plbox_title .t1{color:#000;font-weight:bold}
.plbox_content .plcontent{vertical-align: middle;color:#111}
.plbox_content .plcontent image{ width:44rpx;height:44rpx;vertical-align: inherit;}
.plbox_content .item1{width:100%;margin-bottom:20rpx}
.plbox_content .item1 .f1{width:80rpx;}
.plbox_content .item1 .f1 image{width:60rpx;height:60rpx;border-radius:50%}
.plbox_content .item1 .f2{flex:1}
.plbox_content .item1 .f2 .t1{}
.plbox_content .item1 .f2 .t2{color:#000;margin:10rpx 0;line-height:60rpx;}
.plbox_content .item1 .f2 .t3{color:#999;font-size:20rpx}
.plbox_content .item1 .f2 .pzan image{width:32rpx;height:32rpx;margin-right:2px}
.plbox_content .item1 .f2 .phuifu{margin-left:6px}
.plbox_content .relist{width:100%;background:#f5f5f5;padding:4rpx 20rpx;margin-bottom:20rpx}
.plbox_content .relist .item2{font-size:24rpx;margin-bottom:10rpx}

.copyright{display:none}
</style> 