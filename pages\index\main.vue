<template>
<view class="container" :style="{backgroundColor:pageinfo.bgcolor}">
	<dp :pagecontent="pagecontent" :menuindex="menuindex" :latitude="latitude" :longitude="longitude"></dp>
	<dp-guanggao :guanggaopic="guanggaopic" :guanggaourl="guanggaourl"></dp-guanggao>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>
<script>
var app = getApp();
export default {
	data() {
	return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			id: 0,
			pageinfo: [],
			pagecontent: [],
			title: "",
			oglist: "", 
			guanggaopic: "",
			guanggaourl: "",
			latitude:'',
			longitude:'',
			area:[]
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		//读全局缓存的地区信息
		var cachearea = app.getCache('user_current_area');
		var cachelongitude = app.getCache('user_current_longitude');
		var cachelatitude = app.getCache('user_current_latitude');
		if(cachearea || cachearea!=1){
			this.area = cachearea
			this.latitude = cachelatitude
			this.longitude = cachelongitude
		}
		this.getdata();
	},
	onPullDownRefresh:function(e){
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.title});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.title});
		var query = (sharewxdata.path).split('?')[1];
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
	onPageScroll: function (e) {
		uni.$emit('onPageScroll',e);
	},
	methods: {
		getdata:function(){
			var that = this;
			var opt = this.opt
			var id = 0;
			if (opt && opt.id) {
			  id = opt.id;
			}
			that.loading = true;
			app.get('ApiIndex/index', {id: id,latitude:that.latitude,longitude:that.longitude,area:that.area}, function (data) {
				that.loading = false;
				uni.stopPullDownRefresh();
			  if (data.status == 2) {
			    //付费查看
			    app.goto('/pages/pay/pay?id='+data.payorderid, 'redirect');
			    return;
			  }
			  if (data.status == 1) {
			    var pagecontent = data.pagecontent;
					that.title = data.pageinfo.title;
					that.oglist = data.oglist;
					that.guanggaopic = data.guanggaopic;
					that.guanggaourl = data.guanggaourl;
					that.pageinfo = data.pageinfo;
					that.pagecontent = data.pagecontent;
			    uni.setNavigationBarTitle({
			      title: data.pageinfo.title
			    });
					that.loaded({title:that.title});
					if(that.latitude=='' && that.longitude=='' && data.needlocation){
						app.getLocation(function (res) {
							that.latitude = res.latitude;
							that.longitude = res.longitude;
							that.getdata();
						});
					}
			  } else {
			    if (data.msg) {
			      app.alert(data.msg, function () {
			        if (data.url) app.goto(data.url);
			      });
			    } else if (data.url) {
			      app.goto(data.url);
			    } else {
			      app.alert('您无查看权限');
			    }
			  }
			});
		}
	}
}
</script>
<style>

</style>