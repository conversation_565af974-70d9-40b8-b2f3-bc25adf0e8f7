<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit">
			<view class="form-box">
				<view class="form-item1" style="font-weight:bold">评价</view>
				
				<!-- 星星评价模式 -->
				<view class="form-item2 flex flex-y-center" v-if="setting.comment_type === 0">
					<view class="label">您的打分</view>
					<view class="i-rate" @touchmove="handleTouchMove">
						<input type="text" name="score" :value="score" class="i-rate-hide-input"></input>
						<view v-for="(item, index) in 5" :key="index" class="i-rate-star" :class="( index < score ? 'i-rate-current':'' )" :data-index="index" @tap="handleClick">
								<image v-if="index < score" :src="pre_url+'/static/img/star2.png'"></image>
								<image v-else :src="pre_url+'/static/img/star.png'"></image>
						</view>
						<view class="i-rate-text"></view>
					</view>
				</view>
				
				<!-- 好中差评价模式 -->
				<view class="form-item2 flex-col" v-if="setting.comment_type === 1">
					<view class="label">您的评价</view>
					<view class="level-rating">
						<view class="level-item" :class="{'active': level === 1}" @tap="setLevel(1)">
							<view class="level-icon good" :class="{'active': level === 1}">
								<image :src="level === 1 ? pre_url+'/static/img/yuyue/good_gray.png' : pre_url+'/static/img/yuyue/good.png'" mode="widthFix"></image>
							</view>
							<view class="level-text">好评</view>
						</view>
						<view class="level-item" :class="{'active': level === 2}" @tap="setLevel(2)">
							<view class="level-icon medium" :class="{'active': level === 2}">
								<image :src="level === 2 ? pre_url+'/static/img/yuyue/medium.png' : pre_url+'/static/img/yuyue/medium_gray.png'" mode="widthFix"></image>
							</view>
							<view class="level-text">中评</view>
						</view>
						<view class="level-item" :class="{'active': level === 3}" @tap="setLevel(3)">
							<view class="level-icon bad" :class="{'active': level === 3}">
								<image :src="level === 3 ? pre_url+'/static/img/yuyue/bad_gray.png' : pre_url+'/static/img/yuyue/bad.png'" mode="widthFix"></image>
							</view>
							<view class="level-text">差评</view>
						</view>
					</view>
					<input type="text" name="score" :value="score" class="i-rate-hide-input"></input>
				</view>
				
				<view class="form-item3 flex-col">
					<view class="label">您的评价</view>
					<textarea placeholder="输入您的评价内容" placeholder-style="color:#ccc;" name="content" :value="comment.content" style="height:200rpx" :disabled="comment.id?true:false"></textarea>
				</view>
				
				<view class="form-item4 flex-col">
					<view class="label">上传图片</view>
					<view id="content_picpreview" class="flex" style="flex-wrap:wrap;padding-top:20rpx">
						<view v-for="(item, index) in content_pic" :key="index" class="layui-imgbox">
							<view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="content_pic" v-if="!comment.id"><image :src="pre_url+'/static/img/ico-del.png'"></image></view>
							<view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
						</view>
						<view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="content_pic" v-if="!comment.id && content_pic.length<5"></view>
					</view>
				</view>
				
				<!-- 显示奖励信息 -->
				<view class="form-item5" v-if="setting.reward_node === 1 && worker_order && worker_order.ticheng > 0">
					<view class="reward-info">
						<view class="reward-title">评价奖励说明：</view>
						<view class="reward-desc" v-if="setting.comment_type === 0">
							您的评价将直接影响服务人员的奖励发放
						</view>
						<view class="reward-desc" v-if="setting.comment_type === 1">
							<view>好评：服务人员将获得100%的奖励</view>
							<view>中评：服务人员将获得70%的奖励</view>
							<view>差评：服务人员将获得50%的奖励</view>
						</view>
					</view>
				</view>
			</view>
			<button class="subbtn" form-type="submit" v-if="!comment.id" :style="{background:t('color1')}">确定</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			pre_url:app.globalData.pre_url,
			worker_order: null,
			comment:{},
      score: 5, // 默认5分
      level: 1, // 默认好评
      content_pic: [],
      tempFilePaths: "",
      setting: {
        comment_type: 0, // 默认星星评价
        reward_node: 0  // 默认服务完成后奖励
      }
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			var id = that.opt.id;
			that.loading = true;
			app.get('ApiYuyue/commentps', {id: id}, function (res) {
				that.loading = false;
				that.worker_order = res.worker_order;
				that.setting = res.setting || {
					comment_type: 0,
					reward_node: 0
				};
				
				if (res.comment){
					that.comment = res.comment;
					that.score = res.comment.score;
					
					// 根据评分设置好中差评级别
					if (that.score >= 4) {
						that.level = 1; // 好评
					} else if (that.score == 3) {
						that.level = 2; // 中评
					} else {
						that.level = 3; // 差评
					}
					
					var content_pic = res.comment.content_pic;
					if (content_pic) {
						that.content_pic = content_pic.split(',');
					}
				}
				that.loaded();
			});
		},
    formSubmit: function (e) {
      var that = this;
      var id = that.opt.id;
      var score = e.detail.value.score;
      var content = e.detail.value.content;
      var content_pic = that.content_pic;
      if (score == 0) {
        app.error('请打分');
        return;
      }
      if (content == '') {
        app.error('请填写评价内容');
        return;
      }
			app.showLoading('提交中');
      app.post('ApiYuyue/commentps', {id: id, content: content, content_pic: content_pic.join(','), score: score}, function (data) {
				app.showLoading(false);
        app.success(data.msg);
        setTimeout(function () {
          app.goback(true);
        }, 2000);
      });
    },
    handleClick: function (e) {
      if (this.comment && this.comment.id) return;
      var index = e.currentTarget.dataset.index;
      this.score = index + 1;
    },
    handleTouchMove: function (e) {
      if (this.comment && this.comment.id) return;
      var clientWidth = uni.getSystemInfoSync().windowWidth;
      if (!e.changedTouches[0]) return;
      var movePageX = e.changedTouches[0].pageX;
      var space = movePageX - 150 / 750 * clientWidth;
      if (space <= 0) return;
      var starwidth = 60 / 750 * clientWidth;
      var setIndex = Math.ceil(space / starwidth);
      setIndex = setIndex > 5 ? 5 : setIndex;
      this.score = setIndex;
    },
		// 设置好中差评级别
		setLevel: function(level) {
			if (this.comment && this.comment.id) return;
			this.level = level;
			
			// 根据好中差评价设置对应的分数
			if (level === 1) {
				this.score = 5; // 好评
			} else if (level === 2) {
				this.score = 3; // 中评
			} else {
				this.score = 1; // 差评
			}
		},
		uploadimg:function(e){
			var that = this;
			var field= e.currentTarget.dataset.field
			var pics = that[field]
			if(!pics) pics = [];
			app.chooseImage(function(urls){
				for(var i=0;i<urls.length;i++){
					pics.push(urls[i]);
				}
				if(field == 'content_pic') that.content_pic = pics;
			},1)
		},
		removeimg:function(e){
			var that = this;
			var index= e.currentTarget.dataset.index
			var field= e.currentTarget.dataset.field
			var pics = that[field]
			pics.splice(index,1)
		},
		previewImage: function(e) {
			var url = e.currentTarget.dataset.url;
			var urls = this.content_pic;
			uni.previewImage({
				current: url,
				urls: urls
			});
		},
		loaded: function() {
			this.isload = true;
		},
		t: function(key) {
			return app.t(key);
		}
  }
};
</script>
<style>
.form-box{width:94%;margin:16rpx 3%;border-radius:16rpx;overflow:hidden}
.form-item1{ width:100%;background: #fff; padding:18rpx 20rpx;}
.form-item1 .label{ width:100%;height:60rpx;line-height:60rpx}
.product{ width: 100%; background: #fff; }
.product .info{padding-left:20rpx;}
.product .info .f2{color: #a4a4a4; font-size:24rpx}
.product .info .f3{color: #ff0d51; font-size:28rpx}
.product image{ width:140rpx;height:140rpx}

.form-item2{width:100%;background: #fff; padding: 8rpx 20rpx;margin-top:1px}
.form-item2 .label{ width:150rpx;height:60rpx;line-height:60rpx}

.form-item3{width:100%;background: #fff; padding: 8rpx 20rpx;margin-top:1px}
.form-item3 .label{ width:100%;height:60rpx;line-height:60rpx}
.form-item3 textarea{width: 100%;border: 1px #dedede solid; border-radius: 10rpx; padding: 10rpx;height: 120rpx;}


.form-item4{width:100%;background: #fff; padding: 20rpx 20rpx;margin-top:1px}
.form-item4 .label{ width:150rpx;}

.form-item5{width:100%;background: #fff; padding: 20rpx 20rpx;margin-top:1px}
.reward-info{padding: 20rpx;background: #f8f8f8;border-radius: 10rpx;}
.reward-title{font-weight: bold;margin-bottom: 10rpx;color: #333;}
.reward-desc{color: #666;font-size: 26rpx;line-height: 1.5;}

.subbtn{ width: 90%; margin: 0 5%;margin-top:40rpx; height: 90rpx; line-height: 90rpx; color: #fff; background: #e94745;border-radius:16rpx}

.i-rate{margin:0;padding:0;display:inline-block;vertical-align:middle;}
.i-rate-hide-input{display:none}
.i-rate-star{display:inline-block;color:#e9e9e9;padding:0 10rpx}
.i-rate-star image{width:50rpx;height:50rpx}
.i-rate-current{color:#f5a623}
.i-rate-text{display:inline-block;vertical-align:middle;margin-left:6px;font-size:14px}

/* 好中差评价样式 */
.level-rating{display: flex;justify-content: space-around;width: 100%;padding: 20rpx 0;}
.level-item{display: flex;flex-direction: column;align-items: center;width: 33.33%;transition: all 0.3s;}
.level-item.active{transform: scale(1.1);}
.level-icon{width: 120rpx;height: 120rpx;display: flex;align-items: center;justify-content: center;overflow: hidden;}
.level-icon image{width: 100%;height: 100%;}
.level-text{margin-top: 10rpx;font-size: 28rpx;color: #333;font-weight: bold;}

.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.layui-imgbox-close image{width:100%;height:100%}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}
</style>