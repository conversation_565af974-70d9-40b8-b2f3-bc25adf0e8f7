<template>
	<view v-if="isload">
		<view class="container">
			<view class="header" :style="{background:'linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
				<text class="title">推广产品</text>
				<text class="subtitle">为顾客提供优质服务，提升您的收入</text>
			</view>
			
			<view class="content">
				<block v-if="!loading">
					<view class="product-list" v-if="dataList && dataList.length > 0">
						<view class="product-item" v-for="(item, index) in dataList" :key="item.id">
							<image class="product-image" :src="item.pic" mode="aspectFill"></image>
							<view class="product-info">
								<view class="product-name">{{ item.name }}</view>
								<view class="product-price" :style="{color:t('color1')}">
									<text class="price-symbol">¥</text>
									<text class="price-integer">{{ String(item.price || 0).split('.')[0] }}</text>
									<text class="price-decimal">.{{ (String(item.price || 0).split('.')[1] || '00') }}</text>
								</view>
								<view class="product-sales">已售 {{ item.sales || 0 }}</view>
							</view>
							<view class="poster-button-wrapper">
								<view class="poster-button" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="generatePoster(item.id)">
									<text class="button-text">生成海报</text>
								</view>
							</view>
						</view>
						<uni-load-more :status="loadingType"></uni-load-more>
					</view>
					<view class="no-data" v-else>
						<image class="no-data-image" src="/static/img/no-data.png" mode="aspectFit"></image>
						<text class="no-data-text">暂无可推广的产品</text>
					</view>
				</block>
				<loading v-else></loading>
			</view>

			<!-- 海报展示弹窗 -->
			<view class="poster-popup" v-if="showPosterPopup" @touchmove.stop.prevent="stopPrevent">
				<view class="popup-mask" @tap="closePosterPopup"></view>
				<view class="poster-content">
					<view class="poster-header">
						<text class="poster-title">产品推广海报</text>
						<view class="close-icon" @tap="closePosterPopup">×</view>
					</view>
					<image class="poster-image-preview" :src="posterImageUrl" mode="widthFix" @tap="previewPoster"></image>
					<view class="poster-tip">
						<view class="tip-icon" :style="{color:t('color1')}">i</view>
						<text class="tip-text">长按图片保存或分享给好友</text>
					</view>
					<view class="action-buttons">
						<view class="save-button" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="savePoster">
							<text>保存到相册</text>
						</view>
						<view class="close-button" @tap="closePosterPopup">
							<text>关闭</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 添加通用组件 -->
			<dp-tabbar :opt="opt"></dp-tabbar>
			<popmsg ref="popmsg"></popmsg>
		</view>
	</view>
	<loading v-else></loading>
</template>

<script>
const app = getApp();
import loading from '@/components/loading/loading';
import uniLoadMore from '@/components/uni-load-more/uni-load-more';
import dpTabbar from '@/components/dp-tabbar/dp-tabbar.vue';
import popmsg from '@/components/popmsg/popmsg.vue';

export default {
	components: {
		loading,
		uniLoadMore,
		dpTabbar,
		popmsg
	},
	data() {
		return {
			opt: {},
			isload: false,
			loading: true,
			dataList: [],
			pagenum: 1,
			pernum: 20,
			total: 0,
			loadingType: 'more',
			showPosterPopup: false,
			posterImageUrl: ''
		};
	},
	onLoad: function (options) {
		this.opt = options;
		this.loaded();
		this.getdata(true);
	},
	onReachBottom: function () {
		if (this.loadingType == 'noMore') return;
		this.pagenum++;
		this.getdata();
	},
	onPullDownRefresh: function() {
		this.getdata(true);
		setTimeout(function() {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		getdata: function (isRefresh) {
			if (isRefresh) {
				this.pagenum = 1;
				this.dataList = [];
				this.loadingType = 'more';
			}
			this.loading = true;
			this.loadingType = 'loading';
			var that = this;
			app.post('/ApiYuyueWorker/getPromotionalProducts', { pagenum: this.pagenum, pernum: this.pernum }, function (res) {
				that.loading = false;
				if (res.status == 1) {
					var data = res.data.list;
					if (isRefresh)
						that.dataList = data;
					else
						that.dataList = that.dataList.concat(data);
					that.total = res.data.count;
					if (that.pagenum * that.pernum >= that.total) {
						that.loadingType = 'noMore';
					} else {
						that.loadingType = 'more';
					}
				} else {
					if(that.isload) app.alert(res.msg);
					that.loadingType = 'noMore';
				}
			});
		},
		generatePoster: function (productId) {
			uni.showLoading({ title: '海报生成中...' });
			var that = this;
			app.post('/ApiYuyueWorker/getWorkerPoster', { proid: productId }, function (res) {
				uni.hideLoading();
				if (res.status == 1 && res.poster) {
					that.posterImageUrl = res.poster;
					that.showPosterPopup = true;
				} else {
					app.alert(res.msg || '海报生成失败');
				}
			});
		},
		closePosterPopup: function () {
			this.showPosterPopup = false;
			this.posterImageUrl = '';
		},
		previewPoster: function () {
			if (this.posterImageUrl) {
				uni.previewImage({
					urls: [this.posterImageUrl]
				});
			}
		},
		savePoster: function() {
			if (!this.posterImageUrl) return;
			uni.showLoading({ title: '保存中...' });
			var that = this;
			// 下载海报到本地
			uni.downloadFile({
				url: this.posterImageUrl,
				success: function(res) {
					if (res.statusCode === 200) {
						// 保存图片到相册
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: function() {
								uni.hideLoading();
								uni.showToast({
									title: '保存成功',
									icon: 'success'
								});
							},
							fail: function(err) {
								uni.hideLoading();
								if (err.errMsg.indexOf('auth deny') >= 0) {
									uni.showModal({
										title: '提示',
										content: '需要您授权保存图片到相册',
										showCancel: false
									});
								} else {
									uni.showToast({
										title: '保存失败',
										icon: 'none'
									});
								}
							}
						});
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '图片下载失败',
							icon: 'none'
						});
					}
				},
				fail: function() {
					uni.hideLoading();
					uni.showToast({
						title: '图片下载失败',
						icon: 'none'
					});
				}
			});
		},
        stopPrevent: function() {
            // 阻止背景滚动
        }
	}
};
</script>

<style>
.container {
	min-height: 100vh;
	background-color: #f5f7fa;
	display: flex;
	flex-direction: column;
}

.header {
	padding: 40rpx 30rpx;
	box-sizing: border-box;
	color: #fff;
	position: relative;
	z-index: 1;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}

.subtitle {
	font-size: 26rpx;
	opacity: 0.8;
	display: block;
}

.content {
	flex: 1;
	padding: 20rpx;
}

.product-list {
	width: 100%;
}

.product-item {
	display: flex;
	background-color: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 20rpx;
	align-items: center;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	position: relative;
	overflow: hidden;
}

.product-image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.product-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-width: 0; /* 防止内容撑开 */
	height: 160rpx;
	padding: 10rpx 0;
}

.product-name {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.product-price {
	font-size: 28rpx;
	display: flex;
	align-items: baseline;
}

.price-symbol {
	font-size: 24rpx;
}

.price-integer {
	font-size: 36rpx;
	font-weight: bold;
	margin: 0 2rpx;
}

.price-decimal {
	font-size: 24rpx;
}

.product-sales {
	font-size: 24rpx;
	color: #999;
}

.poster-button-wrapper {
	margin-left: 20rpx;
}

.poster-button {
	padding: 14rpx 20rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.15);
}

.button-text {
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding-top: 100rpx;
}

.no-data-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.no-data-text {
	color: #999;
	font-size: 28rpx;
}

/* 海报弹窗样式 */
.poster-popup {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.popup-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.7);
}

.poster-content {
	width: 650rpx;
	background-color: #fff;
	border-radius: 20rpx;
	position: relative;
	z-index: 1000;
	overflow: hidden;
	box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.2);
	animation: popup-in 0.3s ease-out;
}

@keyframes popup-in {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

.poster-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.poster-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.close-icon {
	font-size: 40rpx;
	line-height: 40rpx;
	width: 40rpx;
	height: 40rpx;
	text-align: center;
	color: #999;
}

.poster-image-preview {
	width: 100%;
	padding: 30rpx;
	box-sizing: border-box;
}

.poster-tip {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 30rpx 30rpx;
}

.tip-icon {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	font-size: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid;
	margin-right: 10rpx;
}

.tip-text {
	font-size: 26rpx;
	color: #666;
}

.action-buttons {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
}

.save-button, .close-button {
	flex: 1;
	padding: 25rpx 0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.save-button {
	color: #fff;
	font-size: 28rpx;
}

.close-button {
	background-color: #f7f7f7;
	color: #666;
	font-size: 28rpx;
}
</style> 