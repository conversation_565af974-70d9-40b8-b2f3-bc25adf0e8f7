<template>
<view class="container">
	<block v-if="isload">
		<view class="header-container">
			<view class="activity-info">
				<view class="activity-name">{{config.name}}</view>
				<view class="activity-desc">{{config.copy_mode}}复制模式 · 财富奖励{{config.wealth_reward_amount}}元</view>
			</view>
		</view>
		
		<view class="stats-container">
			<view class="stats-item">
				<view class="stats-value" style="color:#007aff">{{stats.total_positions || 0}}</view>
				<view class="stats-label">总点位数</view>
			</view>
			<view class="stats-item">
				<view class="stats-value" style="color:#007aff">{{stats.active_positions || 0}}</view>
				<view class="stats-label">活跃点位</view>
			</view>
			<view class="stats-item">
				<view class="stats-value" style="color:#007aff">¥{{stats.total_rewards || 0}}</view>
				<view class="stats-label">累计奖励</view>
			</view>
		</view>
		
		<!-- 新增：测试功能区域 -->
		<view class="test-actions" v-if="config.id">
			<view class="test-title">测试功能</view>
			<view class="test-buttons">
				<view class="test-btn" @click="showAddPositionModal" style="background:linear-gradient(90deg,#007aff 0%,rgba(0,122,255,0.8) 100%)">
					<text>手动增加点位</text>
				</view>
				<view class="test-btn outline" @click="toPositionTree">
					<text>查看排单树</text>
				</view>
			</view>
		</view>
		
		<view class="content-container">
			<view class="tab-container">
				<view class="tab-item" :class="{active: currentTab == 'positions'}" @click="switchTab('positions')">
					<text>我的点位</text>
				</view>
				<view class="tab-item" :class="{active: currentTab == 'rewards'}" @click="switchTab('rewards')">
					<text>奖励记录</text>
				</view>
			</view>
			
			<scroll-view class="list-box" scroll-y="true" @scrolltolower="scrolltolower">
				<!-- 点位列表 -->
				<view class="position-list" v-if="currentTab == 'positions'">
					<view class="position-item" v-for="(item,index) in positionList" :key="item.id">
						<view class="position-info">
							<view class="position-title">
								<text class="position-name">点位 #{{item.position_number}}</text>
								<text class="position-status" :class="item.status">{{item.status_text}}</text>
							</view>
							<view class="position-details">
								<view class="detail-item">
									<text class="label">购买商品：</text>
									<text class="value">{{item.product_name}}</text>
								</view>
								<view class="detail-item">
									<text class="label">购买金额：</text>
									<text class="value" style="color:#007aff">¥{{item.amount}}</text>
								</view>
								<view class="detail-item">
									<text class="label">创建时间：</text>
									<text class="value">{{item.create_time}}</text>
								</view>
							</view>
						</view>
						<view class="position-action" v-if="item.can_claim && config.wealth_reward_enabled">
							<view class="claim-btn" @click="claimReward" :data-id="item.id" style="background:linear-gradient(90deg,#007aff 0%,rgba(0,122,255,0.8) 100%)">领取奖励</view>
						</view>
					</view>
				</view>
				
				<!-- 奖励记录列表 -->
				<view class="reward-list" v-if="currentTab == 'rewards'">
					<view class="reward-item" v-for="(item,index) in rewardList" :key="item.id">
						<view class="reward-info">
							<view class="reward-title">
								<text class="reward-type">{{item.reward_type_text}}</text>
								<text class="reward-amount" style="color:#007aff">+¥{{item.amount}}</text>
							</view>
							<view class="reward-details">
								<view class="detail-item">
									<text class="label">奖励说明：</text>
									<text class="value">{{item.remark}}</text>
								</view>
								<view class="detail-item">
									<text class="label">获得时间：</text>
									<text class="value">{{item.create_time}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<nomore text="没有更多数据了" v-if="nomore"></nomore>
				<nodata text="暂无数据" v-if="nodata"></nodata>
				<view style="width:100%;height:100rpx"></view>
			</scroll-view>
		</view>
		
		<!-- 新增：手动添加点位弹窗 -->
		<view class="modal-overlay" v-if="showAddModal" @click="hideAddPositionModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">手动增加点位</text>
					<text class="modal-close" @click="hideAddPositionModal">×</text>
				</view>
				<view class="modal-body">
					<view class="form-item">
						<text class="form-label">父点位ID（可选）：</text>
						<input type="number" class="form-input" placeholder="不填则创建根点位" v-model="addForm.parent_id"/>
					</view>
					<view class="form-item">
						<text class="form-label">订单ID（可选）：</text>
						<input type="number" class="form-input" placeholder="关联的订单ID" v-model="addForm.order_id"/>
					</view>
					<view class="form-tips">
						<text>• 不填父点位ID将创建根点位</text>
						<text>• 系统会自动计算层级和位置</text>
						<text>• 财富点位根据配置自动判断</text>
					</view>
				</view>
				<view class="modal-footer">
					<view class="modal-btn cancel" @click="hideAddPositionModal">取消</view>
					<view class="modal-btn confirm" @click="confirmAddPosition" style="background:#007aff">确认添加</view>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			pagenum: 1,
			nomore: false,
			nodata: false,
			config_id: 0,
			config: {
				name: '',
				copy_mode: '',
				wealth_reward_amount: '0',
				wealth_reward_enabled: 1 // 默认启用，确保兼容性
			},
			stats: {
				total_positions: 0,
				active_positions: 0,
				total_rewards: '0.00'
			},
			currentTab: 'positions',
			positionList: [],
			rewardList: [],
			showAddModal: false,
			addForm: {
				parent_id: '',
				order_id: ''
			}
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.config_id = opt.config_id || 0;
		if(!this.config_id){
			uni.showToast({title: '活动参数错误', icon: 'none'});
			return;
		}
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	methods: {
		getdata:function(){
			var that = this;
			that.pagenum = 1;
			that.positionList = [];
			that.rewardList = [];
			that.getPositionData();
		},
		
		getPositionData: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				if(this.currentTab == 'positions'){
					this.positionList = [];
				}else{
					this.rewardList = [];
				}
			}

			var that = this;
			var pagenum = that.pagenum;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
			var apiUrl = that.currentTab == 'positions' ? 'ApiPaidan/getMyPositions' : 'ApiPaidan/getMyRewards';
			
			app.post(apiUrl, {
				config_id: that.config_id,
				page: pagenum, 
				limit: 10
			}, function (res) { 
				that.loading = false;
				uni.stopPullDownRefresh();

				if(res.code == 1){
					var data = res.data;
					that.config = data.config || that.config;
					that.stats = data.stats || that.stats;
					
					var list = data.list;
					if (list.length == 0) {
						if(pagenum == 1){
							that.nodata = true;
						}else{
							that.nomore = true;
						}
					}
					
					if(that.currentTab == 'positions'){
						var datalist = that.positionList;
						var newdata = datalist.concat(list);
						that.positionList = newdata;
					}else{
						var datalist = that.rewardList;
						var newdata = datalist.concat(list);
						that.rewardList = newdata;
					}
					
					if(pagenum == 1 && !that.isload){
						that.loaded();
					}
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
					if(pagenum == 1 && !that.isload){
						that.loaded();
					}
				}
			});
		},

		scrolltolower: function () {
			if (!this.nomore) {
				this.pagenum = this.pagenum + 1;    
				this.getPositionData(true);
			}
		},
		
		switchTab: function(tab){
			this.currentTab = tab;
			this.getPositionData();
		},
		
		claimReward: function(e){
			var id = e.currentTarget.dataset.id;
			var that = this;
			
			// 前端验证：检查财富点位奖励功能是否启用
			if(!that.config.wealth_reward_enabled){
				that.$refs.popmsg.show({type: 'error', msg: '该活动的财富点位奖励功能已关闭'});
				return;
			}
			
			uni.showModal({
				title: '确认领取',
				content: '确定要领取此点位的奖励吗？',
				success: function(res) {
					if (res.confirm) {
						app.post('ApiPaidan/claimReward', {
							position_id: id
						}, function (res) {
							if(res.code == 1){
								that.$refs.popmsg.show({type: 'success', msg: res.msg});
								that.getPositionData();
							}else{
								that.$refs.popmsg.show({type: 'error', msg: res.msg});
							}
						});
					}
				}
			});
		},
		
		showAddPositionModal: function(){
			this.showAddModal = true;
			this.addForm = {
				parent_id: '',
				order_id: ''
			};
		},
		
		hideAddPositionModal: function(){
			this.showAddModal = false;
		},
		
		confirmAddPosition: function(){
			var that = this;
			
			// 参数验证
			var params = {
				config_id: that.config_id
			};
			
			if(that.addForm.parent_id){
				params.parent_id = parseInt(that.addForm.parent_id);
			}
			
			if(that.addForm.order_id){
				params.order_id = parseInt(that.addForm.order_id);
			}
			
			that.loading = true;
			
			app.post('ApiPaidan/addPosition', params, function (res) {
				that.loading = false;
				
				if(res.code == 1){
					that.$refs.popmsg.show({type: 'success', msg: res.msg});
					that.hideAddPositionModal();
					that.getPositionData(); // 刷新列表
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		toPositionTree: function(){
			app.goto('/pagesB/paidan/position-tree?config_id=' + this.config_id);
		},
		
		loaded: function () {
			this.isload = true;
		},
		
		getmenuindex: function (e) {
			this.menuindex = e;
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}
.header-container {width: 100%;padding: 30rpx;background-color: #fff;border-bottom:1px solid #f5f5f5;}
.activity-info{}
.activity-name{font-size:32rpx;font-weight:bold;color:#333;margin-bottom:8rpx;}
.activity-desc{font-size:24rpx;color:#666;}
.stats-container{width:100%;padding:30rpx;background-color:#fff;margin-bottom:20rpx;display:flex;}
.stats-item{flex:1;text-align:center;}
.stats-value{font-size:36rpx;font-weight:bold;margin-bottom:8rpx;}
.stats-label{font-size:24rpx;color:#666;}

/* 新增：测试功能样式 */
.test-actions{width:100%;padding:30rpx;background-color:#fff;margin-bottom:20rpx;}
.test-title{font-size:28rpx;font-weight:bold;color:#333;margin-bottom:20rpx;}
.test-buttons{display:flex;justify-content:space-between;}
.test-btn{flex:1;height:80rpx;line-height:80rpx;text-align:center;color:#fff;font-size:26rpx;border-radius:40rpx;margin:0 10rpx;}
.test-btn:first-child{margin-left:0;}
.test-btn:last-child{margin-right:0;}
.test-btn.outline{background:#fff;color:#333;border:2rpx solid #ddd;}

.content-container{flex:1;height:100%;display:flex;flex-direction:column;overflow: hidden;}
.tab-container{width:100%;display:flex;background-color:#fff;border-bottom:1px solid #f5f5f5;}
.tab-item{flex:1;height:88rpx;line-height:88rpx;text-align:center;font-size:28rpx;color:#666;position:relative;}
.tab-item.active{color:#333;font-weight:bold;}
.tab-item.active::after{content:'';position:absolute;bottom:0;left:50%;transform:translateX(-50%);width:60rpx;height:4rpx;background-color:#007aff;border-radius:2rpx;}
.list-box{flex:1;height:100%;padding:20rpx;}
.position-item, .reward-item{width:100%;background:#fff;margin-bottom:20rpx;padding:30rpx;border-radius:16rpx;}
.position-title, .reward-title{display:flex;justify-content:space-between;align-items:center;margin-bottom:20rpx;}
.position-name, .reward-type{font-size:32rpx;font-weight:bold;color:#333;}
.position-status{padding:8rpx 16rpx;font-size:22rpx;border-radius:12rpx;}
.position-status.active{background:#e8f5e8;color:#52c41a;}
.position-status.waiting{background:#fff7e6;color:#fa8c16;}
.position-status.completed{background:#f6f6f6;color:#999;}
.reward-amount{font-size:32rpx;font-weight:bold;}
.position-details, .reward-details{}
.detail-item{display:flex;margin-bottom:12rpx;font-size:26rpx;}
.detail-item:last-child{margin-bottom:0;}
.label{color:#666;width:160rpx;}
.value{color:#333;flex:1;}
.position-action{margin-top:20rpx;text-align:right;}
.claim-btn{display:inline-block;padding:16rpx 32rpx;color:#fff;font-size:26rpx;border-radius:30rpx;text-align:center;}

/* 新增：弹窗样式 */
.modal-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:9999;}
.modal-content{width:600rpx;background:#fff;border-radius:20rpx;overflow:hidden;}
.modal-header{display:flex;justify-content:space-between;align-items:center;padding:30rpx;border-bottom:1px solid #f5f5f5;}
.modal-title{font-size:32rpx;font-weight:bold;color:#333;}
.modal-close{font-size:40rpx;color:#999;width:40rpx;height:40rpx;text-align:center;line-height:32rpx;}
.modal-body{padding:30rpx;}
.form-item{margin-bottom:30rpx;}
.form-label{display:block;font-size:26rpx;color:#333;margin-bottom:16rpx;}
.form-input{width:100%;height:80rpx;padding:0 20rpx;background:#f8f8f8;border-radius:12rpx;font-size:26rpx;border:none;}
.form-tips{background:#f8f9fa;padding:20rpx;border-radius:12rpx;margin-top:20rpx;}
.form-tips text{display:block;font-size:22rpx;color:#666;margin-bottom:8rpx;line-height:1.5;}
.form-tips text:last-child{margin-bottom:0;}
.modal-footer{display:flex;padding:30rpx;border-top:1px solid #f5f5f5;}
.modal-btn{flex:1;height:80rpx;line-height:80rpx;text-align:center;font-size:28rpx;border-radius:40rpx;margin:0 10rpx;}
.modal-btn:first-child{margin-left:0;}
.modal-btn:last-child{margin-right:0;}
.modal-btn.cancel{background:#f8f8f8;color:#666;}
.modal-btn.confirm{color:#fff;}
</style>