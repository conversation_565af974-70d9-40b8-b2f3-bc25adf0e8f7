<template>
	<view style="width:100%">
		<view class="dp-product-zhaopin">
			<view class="job-card" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/zhaopin/partdetails?id='+item[zwid]">
				<!-- 顶部描述区 -->
				<view class="card-desc" v-if="item.apply_tag">
					<view class="desc-gradient"></view>
					<view class="desc-content">
						<image src="/static/img/verify.png"></image>
						<text class="desc-text ellipsis">担保企业</text>
					</view>
				</view>
	
				<!-- 职位标题和薪资 -->
				<view class="card-header">
					<text class="job-title ellipsis" :style="{color: t('text-primary')}">{{item.title}}</text>
					<view class="job-salary">
						<text class="salary-num" :style="{color: t('color1')}">{{item.salary}}</text>
					</view>
					<view class="collect-btn">
						<image
							class="collect-icon"
							:src="item.is_favorite ? '/static/img/star2.png' : '/static/img/star.png'"
						/>
					</view>
				</view>
	
				<!-- 标签列表 -->
				<view class="tag-list">
					<view class="tag-item" v-if="item.descripti">{{item.descripti}}</view>
					<view class="tag-item" v-if="item.numbers">{{item.numbers}}人</view>
					<view class="tag-item" v-if="item.experience">{{item.experience}}</view>
					<view class="tag-item" v-if="item.education">{{item.education}}</view>
				</view>
	
				<!-- 地址信息 -->
				<view class="location-info" v-if="showaddress && item.work_address">
					<image class="location-icon" src="../../static/img/address3.png"></image>
					<text class="address ellipsis">{{item.work_address}}</text>
				</view>
	
				<!-- 公司信息 -->
				<view class="company-info">
					<view class="company-left">
						<view class="company-logo">
							<image 
								v-if="item.company_logo" 
								:src="item.company_logo" 
								mode="aspectFit"
							/>
							<!-- 无logo时显示默认图片 -->
							<image 
								v-else 
								src="/static/img/default-company-logo.png" 
								mode="aspectFit"
							/>
						</view>
						<text class="company-name ellipsis">{{item.company_name}}</text>
					</view>
					<view class="apply-btn" :style="{background: t('color1')}">立即报名</view>
				</view>
	
				<!-- 福利标签 -->
				<view class="welfare-list" v-if="item.welfare && item.welfare.length>0">
					<view class="tag-item" v-for="(wf,wk) in item.welfare" :key="wk">{{wf}}</view>
				</view>
			</view>
		</view>
	</view>
	</template>
	
	<script>
	export default {
		data(){
			return {
				buydialogShow:false,
				proid:0,
			}
		},
		props: {
			menuindex:{default:-1},
			namecolor:{default:'#333'},
			showaddress:{default:'1'},
			data:{},
			idfield:{default:'id'},
			zwid:{default:'zwid'},
			t:{
				type: Function,
				required: true
			}
		},
		methods: {
			goto(e) {
				const url = e.currentTarget.dataset.url;
				if(url) {
					uni.navigateTo({
						url: url
					});
				}
			}
		}
	}
	</script>
	
	<style lang="scss">
	.dp-product-zhaopin {
		height: auto; 
		position: relative;
		overflow: hidden; 
		padding: 16rpx;
	}
	
	.job-card {
		background: #FFFFFF;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		padding: 24rpx;
		position: relative;
		box-shadow: 0 4rpx 12rpx rgba(101, 97, 97, 0.09);
		
		// 顶部描述区
		.card-desc {
			position: relative;
			height: 56rpx;
			margin-bottom: 16rpx;
	
			.desc-gradient {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				border-radius: 8rpx;
				background: #F8F9FB;
			}
	
			.desc-content {
				position: relative;
				z-index: 1;
				display: flex;
				align-items: center;
				padding: 0 16rpx;
				height: 100%;
	
				image {
					width: 32rpx;
					height: 32rpx;
					margin-right: 8rpx;
				}
	
				.desc-text {
					font-size: 26rpx;
					color: #666666;
				}
			}
		}
	
		// 职位标题和薪资
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
	
			.job-title {
				flex: 1;
				font-size: 42rpx;
				font-weight: 600;
				margin-right: 16rpx;
			}
	
			.job-salary {
				font-size: 32rpx;
				font-weight: 600;
			}
	
			.collect-btn {
				margin-left: 16rpx;
				padding: 8rpx;
				
				.collect-icon {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}
	
		// 标签列表
		.tag-list {
			display: flex;
			flex-wrap: wrap;
			margin-bottom: 16rpx;
			gap: 12rpx;
	
			.tag-item {
				height: 40rpx;
				padding: 0 16rpx;
				background: #F8F9FB;
				border-radius: 6rpx;
				font-size: 24rpx;
				color: #666666;
				line-height: 40rpx;
			}
		}
	
		// 地址信息
		.location-info {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
	
			.location-icon {
				width: 24rpx;
				height: 24rpx;
				margin-right: 8rpx;
				opacity: 0.7;
			}
	
			.address {
				flex: 1;
				font-size: 24rpx;
				color: #999999;
			}
		}
	
		// 公司信息
		.company-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-top: 16rpx;
			margin-top: 16rpx;
			border-top: 1rpx solid #F5F5F5;
	
			.company-left {
				display: flex;
				align-items: center;
				flex: 1;
	
				.company-logo {
					width: 48rpx;
					height: 48rpx;
					border-radius: 8rpx;
					margin-right: 12rpx;
					overflow: hidden;
					background: #F8F9FB;
	
					image {
						width: 100%;
						height: 100%;
					}
				}
	
				.company-name {
					flex: 1;
					font-size: 26rpx;
					color: #666666;
				}
			}
	
			.apply-btn {
				position: relative;
				min-width: 120rpx;
				height: 52rpx;
				border-radius: 26rpx;
				color: #FFFFFF;
				font-size: 26rpx;
				font-weight: 500;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 20rpx;
				overflow: hidden;
	
				&::before {
					content: '';
					position: absolute;
					top: -150%;
					left: -100%;
					width: 60rpx;
					height: 200%;
					background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,.3) 50%, rgba(255,255,255,0) 100%);
					transform: rotate(45deg);
					animation: shiny-btn 3s ease-in-out infinite;
				}
	
				&:active {
					opacity: 0.9;
				}
			}
		}
	
		// 福利标签
		.welfare-list {
			display: flex;
			flex-wrap: wrap;
			gap: 12rpx;
			margin-top: 16rpx;
	
			.tag-item {
				height: 40rpx;
				padding: 0 16rpx;
				background: #F8F9FB;
				border-radius: 6rpx;
				font-size: 24rpx;
				color: #666666;
				line-height: 40rpx;
			}
		}
	}
	
	// 通用样式
	.ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	@keyframes shiny-btn {
		0% {
			left: -100%;
		}
		20% {
			left: 100%;
		}
		100% {
			left: 100%;
		}
	}
	</style>