<template>
<view class="container">
	<block v-if="isload">
		<view class="orderinfo">
			<view class="item">
				<text class="t1">订单编号</text>
				<text class="t2">{{detail.ordernum}}</text>
			</view>
			<view class="item">
				<text class="t1">服务类型</text>
				<text class="t2">AI智能舌诊</text>
			</view>
			<view class="item">
				<text class="t1">服务金额</text>
				<text class="t2">￥{{detail.money}}</text>
			</view>
			<view class="item">
				<text class="t1">实付金额</text>
				<text class="t2" style="font-size:32rpx;color:#e94745">￥{{detail.paymoney}}</text>
			</view>
			<view class="item">
				<text class="t1">付款方式</text>
				<text class="t2">{{detail.paytype}}</text>
			</view>
			<view class="item">
				<text class="t1">状态</text>
				<text class="t2" v-if="detail.status==1" style="color:green">已付款</text>
				<text class="t2" v-else style="color:red">未付款</text>
			</view>
			<view class="item" v-if="detail.status>0 && detail.pay_time">
				<text class="t1">付款时间</text>
				<text class="t2">{{detail.paytime}}</text>
			</view>
			<view class="item" v-if="detail.disprice>0">
				<text class="t1">{{t('会员')}}折扣</text>
				<text class="t2">-￥{{detail.disprice}}</text>
			</view>
			<view class="item" v-if="detail.scoredk>0">
				<text class="t1">{{t('积分')}}抵扣</text>
				<text class="t2">-￥{{detail.scoredk}}</text>
			</view>
			<view class="item" v-if="detail.couponrid">
				<text class="t1">{{t('优惠券')}}抵扣</text>
				<text class="t2">-￥{{detail.couponmoney}}</text>
			</view>
			<view class="item" v-if="couponrecord">
				<text class="t1">{{t('优惠券')}}名称</text>
				<text class="t2">{{couponrecord.couponname}}</text>
			</view>
			<view class="item" v-if="detail.use_free == 1">
				<text class="t1">使用免费次数</text>
				<text class="t2" style="color:green">是</text>
			</view>
		</view>
		
		<!-- 舌头图片预览 -->
		<view class="image-preview" v-if="detail.image_url">
			<view class="preview-title">舌诊图片</view>
			<image class="tongue-image" :src="detail.image_url" mode="aspectFit" @tap="previewImage"></image>
		</view>
		
		<!-- 分析结果按钮 -->
		<view class="action-buttons" v-if="detail.status == 1">
			<view class="btn-secondary" @tap="viewResult">查看分析结果</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
		opt:{},
		loading:false,
		isload: false,
		menuindex:-1,
		
		detail: {},
		couponrecord: {},
    };
  },

  onLoad: function (opt) {
	this.opt = app.getopts(opt);
	this.getdata();
  },
  
  onPullDownRefresh: function () {
	this.getdata();
  },
  
  methods: {
	getdata: function (option) {
		var that = this;
		that.loading= true;
		app.get('ApiSheZhen/getOrderDetail', {id: that.opt.id}, function (res) {
			that.loading= false;
			if(res.code == 0){
				app.alert(res.msg, function() {
					app.goback();
				});
				return;
			}
			that.detail = res.data.detail;
			that.couponrecord = res.data.couponrecord;
			that.loaded();
		});
	},

	// 预览图片
	previewImage: function() {
		if(this.detail.image_url){
			uni.previewImage({
				urls: [this.detail.image_url],
				current: this.detail.image_url
			});
		}
	},

	// 查看分析结果
	viewResult: function() {
		if(this.detail.record_id) {
			app.goto('/pagesB/shezhen/result?id=' + this.detail.record_id);
		} else {
			app.error('分析结果不存在');
		}
	}
  }
};
</script>

<style>
.orderinfo{ 
	width:94%;
	margin:20rpx 3%;
	border-radius:5px;
	padding:20rpx 20rpx;
	padding: 14rpx 3%;
	background: #FFF;
}

.orderinfo .item{
	display:flex;
	width:100%;
	padding:20rpx 0;
	border-bottom:1px dashed #ededed;
}

.orderinfo .item:last-child{ 
	border-bottom: 0;
}

.orderinfo .item .t1{
	width:200rpx;
}

.orderinfo .item .t2{
	flex:1;
	text-align:right
}

.orderinfo .item .red{
	color:red
}

.image-preview{
	width:94%;
	margin:20rpx 3%;
	border-radius:5px;
	padding:20rpx;
	background: #FFF;
}

.preview-title{
	font-size:28rpx;
	color:#333;
	margin-bottom:20rpx;
	font-weight:bold;
}

.tongue-image{
	width:200rpx;
	height:200rpx;
	border-radius:10rpx;
	border:1px solid #f0f0f0;
}

.action-buttons{
	width:94%;
	margin:20rpx 3%;
	padding:20rpx;
}

.btn-secondary{
	width:100%;
	height:80rpx;
	line-height:80rpx;
	text-align:center;
	background:#fff;
	border:2rpx solid #667eea;
	color:#667eea;
	border-radius:40rpx;
	font-size:32rpx;
	font-weight:bold;
}
</style> 