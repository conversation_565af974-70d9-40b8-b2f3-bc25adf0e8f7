<template>
<view class="container">
	<block v-if="isload">
		<block v-if="logintype!=4 && logintype!=5">
			<form @submit="formSubmit" @reset="formReset">
			<view class="title">注册账号</view>
			<view class="regform">
				<!-- 系统注册S -->
				<block v-if="!show_custom_field">
					<view class="form-item">
						<image src="/static/img/reg-tel.png" class="img"/>
						<input type="text" class="input" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" @input="telinput"/>
					</view>
					<view class="form-item" v-if="needsms">
						<image src="/static/img/reg-code.png" class="img"/>
						<input type="text" class="input" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/>
						<view class="code" :style="{color:t('color1')}" @tap="smscode">{{smsdjs||'获取验证码'}}</view>
					</view>
					<view class="form-item">
						<image src="/static/img/reg-pwd.png" class="img"/>
						<input type="text" class="input" placeholder="6-16位字母数字组合密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" :password="true"/>
					</view>
					<view class="form-item">
						<image src="/static/img/reg-pwd.png" class="img"/>
						<input type="text" class="input" placeholder="再次输入登录密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="repwd" value="" :password="true"/>
					</view>
					<view class="form-item" v-if="reg_invite_code && !parent">
						<image src="/static/img/reg-yqcode.png" class="img"/>
						<input type="text" class="input" :placeholder="'请输入'+(reg_invite_code_type==0?'邀请人手机号':'邀请码')+(reg_invite_code==2?'(必填)':'(选填)')" placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value=""/>
					</view>
					<view class="form-item" v-if="reg_invite_code && parent" style="color:#666;">
						<block v-if="reg_invite_code_type == 0 ">
						邀请人：<image :src="parent.headimg" style="width: 80rpx; height: 80rpx;border-radius: 50%;"></image> {{parent.nickname}} 
						</block>
						<block v-else>
						邀请码：{{parent.yqcode}} 
						</block>
					</view>
					<view class="form-item" v-if="xlevelarr" style="color:#666;">
						<block>
						注册等级：{{xlevelarr.name}} 
						</block>
					</view>
				</block>
				<!-- 系统注册E -->
				<!-- 自定义注册S -->
				<block v-if="show_custom_field">
					<view class="dp-form-item">
						<view class="label">手机号<text style="color:red"> * </text></view>
						<input type="text" class="input" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" @input="telinput"/>
					</view>
					<view class="dp-form-item" v-if="needsms">
						<text class="label">验证码</text>
						<input type="text" class="input" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/>
						<view class="code" :style="{color:t('color1')}" @tap="smscode">{{smsdjs||'获取验证码'}}</view>
					</view>
					<view class="dp-form-item">
						<view class="label">密码<text style="color:red"> * </text></view>
						<input type="text" class="input" placeholder="6-16位字母数字组合密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" :password="true"/>
					</view>
					<view class="dp-form-item">
						<view class="label">确认密码</view>
						<input type="text" class="input" placeholder="再次输入登录密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="repwd" value="" :password="true"/>
					</view>
					<view class="dp-form-item" v-if="reg_invite_code && !parent">
						<text class="label">邀请码</text>
						<input type="text" class="input" :placeholder="'请输入'+(reg_invite_code_type==0?'邀请人手机号':'邀请码')+(reg_invite_code==2?'(必填)':'(选填)')" placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value=""/>
					</view>
					<view class="dp-form-item" v-if="reg_invite_code && parent" style="color:#666;">
						<block v-if="reg_invite_code_type == 0 ">
						邀请人：<image :src="parent.headimg" style="width: 80rpx; height: 80rpx;border-radius: 50%;"></image> {{parent.nickname}} 
						</block>
						<block v-else>
						邀请码：{{parent.yqcode}} 
						</block>
					</view>
					
					<view class="custom_field" v-if="show_custom_field">
						<view :class="'dp-form-item'" v-for="(item,idx) in formfields.content"  :key="idx">
							<view class="label">{{item.val1}}<text v-if="item.val3==1" style="color:red"> *</text></view>
							<block v-if="item.key=='input'">
								<text v-if="item.val5" style="margin-right:10rpx">{{item.val5}}</text>
								<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" :name="'form'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx" :value="custom_formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
							</block>
							<block v-if="item.key=='textarea'">
								<textarea :name="'form'+idx" class='textarea' :placeholder="item.val2" placeholder-style="font-size:28rpx"  :value="custom_formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
							</block>
							<block v-if="item.key=='radio'">
								<radio-group class="flex" :name="'form'+idx" style="flex-wrap:wrap" @change="setfield" :data-formidx="'form'+idx">
									<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
											<radio class="radio" :value="item1" style="transform: scale(0.8);" :checked="custom_formdata['form'+idx] && custom_formdata['form'+idx]==item1 ? true : false"/>{{item1}}
									</label>
								</radio-group>
							</block>
							<block v-if="item.key=='checkbox'">
								<checkbox-group :name="'form'+idx" class="flex" style="flex-wrap:wrap" @change="setfield" :data-formidx="'form'+idx">
									<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
										<checkbox class="checkbox" style="transform: scale(0.8);" :value="item1" :checked="custom_formdata['form'+idx] && inArray(item1,custom_formdata['form'+idx]) ? true : false"/>{{item1}}
									</label>
								</checkbox-group>
							</block>
							<block v-if="item.key=='selector'">
								<picker class="picker" mode="selector" :name="'form'+idx" :value="editorFormdata[idx]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
									<view v-if="editorFormdata[idx] || editorFormdata[idx]===0"> {{item.val2[editorFormdata[idx]]}}</view>
									<view v-else style="color: #b2b5be;">请选择</view>
								</picker>
							</block>
							<block v-if="item.key=='time'">
								<picker class="picker" mode="time" :name="'form'+idx" :value="custom_formdata['form'+idx]" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
									<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view>
									<view v-else style="color: #b2b5be;">请选择</view>
								</picker>
							</block>
							<block v-if="item.key=='date'">
								<picker class="picker" mode="date" :name="'form'+idx" :value="custom_formdata['form'+idx]" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
									<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view>
									<view v-else style="color: #b2b5be;">请选择</view>
								</picker>
							</block>
						
							<block v-if="item.key=='region'">
								<uni-data-picker :localdata="items" popup-title="请选择省市区" :placeholder="custom_formdata['form'+idx] || '请选择省市区'" @change="onchange" :data-formidx="'form'+idx"></uni-data-picker>
								<input type="text" style="display:none" :name="'form'+idx" :value="regiondata ? regiondata : custom_formdata['form'+idx]"/>
							</block>
							<block v-if="item.key=='upload'">
								<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
								<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
									<view class="dp-form-imgbox" v-if="editorFormdata[idx]">
										<view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx"><image src="/static/img/ico-del.png" class="image"></image></view>
										<view class="dp-form-imgbox-img"><image class="image" :src="editorFormdata[idx]" @click="previewImage" :data-url="editorFormdata[idx]" mode="widthFix" :data-idx="idx"/></view>
									</view>
									<view v-else class="dp-form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-idx="idx" :data-formidx="'form'+idx"></view>
								</view>
							</block>
						</view>
					</view>
					<view style="display:none">{{test}}</view>
				</block>
				<!-- 自定义注册E -->
				<view class="xieyi-item" v-if="xystatus==1">
					<checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox class="checkbox" value="1" :checked="isagree"/>阅读并同意</label></checkbox-group>
					<text :style="{color:t('color1')}" @tap="showxieyiFun">{{xyname}}</text>
					<text @tap="showxieyiFun" v-if="xyname2">和</text>
					<text :style="{color:t('color1')}" @tap="showxieyiFun2" v-if="xyname2">{{xyname2}}</text>
				</view>

				<button class="form-btn" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" form-type="submit">注册</button>
			</view>
			</form>
			<view class="tologin" @tap="goto" data-url="login" data-opentype="redirect">已有账号? 前去登录</view>
			
			<block v-if="logintype_2 || logintype_3">
				<view class="othertip">
					<view class="othertip-line"></view>
					<view class="othertip-text">
						<text class="txt">其他方式登录</text>
					</view>
					<view class="othertip-line"></view>
				</view>
				<view class="othertype">
					<view class="othertype-item" v-if="logintype_3" @tap="weixinlogin">
						<image class="img" :src="pre_url+'/static/img/login-'+platformimg+'.png'"/>
						<text class="txt">{{platformname}}登录</text>
					</view>
					<view class="othertype-item" v-if="logintype_2" @tap="goto" data-url="login?logintype=2" data-opentype="redirect">
						<image class="img" :src="pre_url+'/static/img/reg-tellogin.png'"/>
						<text class="txt">手机号登录</text>
					</view>
				</view>
			</block>
		</block>
		<!-- 绑定手机号 -->
		<block v-if="logintype==4">
			<!--  #ifdef MP-WEIXIN -->
				<view class="authlogin">
					<view class="authlogin-logo"><image :src="logo" style="width:100%;height:100%"/></view>
					<view class="authlogin-name">授权登录{{name}}</view>
					<button class="authlogin-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{platformname}}授权绑定手机号</button>
					<button class="authlogin-btn2" @tap="nobindregister" v-if="login_bind==1">暂不绑定</button>
				</view>
			<!-- #endif -->
			<!--  #ifndef MP-WEIXIN -->
				<form @submit="bindregister" @reset="formReset">
					<view class="title">绑定手机号</view>
					<view class="regform">
						<view class="form-item">
							<image src="/static/img/reg-tel.png" class="img"/>
							<input type="text" class="input" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" @input="telinput"/>
						</view>
						<view class="form-item">
							<image src="/static/img/reg-code.png" class="img"/>
							<input type="text" class="input" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/>
							<view class="code" :style="{color:t('color1')}" @tap="smscode">{{smsdjs||'获取验证码'}}</view>
						</view>
						<button class="form-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">确定</button>
						<button class="form-btn2" @tap="nobindregister" v-if="login_bind==1">暂不绑定</button>
					</view>
				</form>
			<!-- #endif -->
		</block>

		
		<!-- 设置头像昵称 -->
		<block v-if="logintype==5">
			<form @submit="setnicknameregister" @reset="formReset">
				<view class="title">请设置头像昵称</view>
				<view class="regform">
					<!--  #ifdef MP-WEIXIN -->
					<view class="form-item" style="height:120rpx;line-height:120rpx">
						<view class="flex1">头像</view>
						<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" style="width:100rpx;height:100rpx;">
							<image :src="headimg || default_headimg" style="width:100%;height:100%;border-radius:50%"></image>
						</button> 
					</view>
					<view class="form-item" style="height:120rpx;line-height:120rpx">
						<view class="flex1">昵称</view>
						<input type="nickname" class="input" placeholder="请输入昵称" name="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE" style="text-align:right"/>
					</view>
					<!-- #endif -->
					<!--  #ifndef MP-WEIXIN -->
					<view class="form-item" style="height:120rpx;line-height:120rpx">
						<view class="flex1">头像</view>
						<image :src="headimg || default_headimg" style="width:100rpx;height:100rpx;border-radius:50%" @tap="uploadHeadimg"></image>
					</view>
					<view class="form-item">
						<view class="flex1">昵称</view>
						<input type="text" class="input" placeholder="请输入昵称" name="nickname" value="" placeholder-style="font-size:30rpx;color:#B2B5BE" style="text-align:right"/>
					</view>
					<!-- #endif -->
					<button class="form-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">确定</button>
					<button class="form-btn2" @tap="nosetnicknameregister" v-if="login_setnickname==1">暂不设置</button>
				</view>
			</form>
		</block>

		<view v-if="showxieyi" class="xieyibox">
			<view class="xieyibox-content">
				<view style="overflow:scroll;height:100%;">
					<parse :content="xycontent" @navigate="navigate"></parse>
				</view>
				<view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"  @tap="hidexieyi">已阅读并同意</view>
			</view>
		</view>

		<view v-if="showxieyi2" class="xieyibox">
			<view class="xieyibox-content">
				<view style="overflow:scroll;height:100%;">
					<parse :content="xycontent2" @navigate="navigate"></parse>
				</view>
				<view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"  @tap="hidexieyi2">已阅读并同意</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			platform2:app.globalData.platform2,
			
			platform:'',
			platformname:'',
			platformimg:'weixin',
			logintype:0,
			logintype_1:true,
			logintype_2:false,
			logintype_3:false,
			logo:'',
			name:'',
			xystatus:0,
			xyname:'',
			xycontent:'',
			xyname2:'',
			xycontent2:'',
			needsms:false,
			showxieyi:false,
			showxieyi2:false,
			isagree:false,
      smsdjs: '',
			tel:'',
      hqing: 0,
			frompage:'/pages/my/usercenter',
			wxloginclick:false,
			login_bind:0,
			login_setnickname:0,
      reg_invite_code:0,
      reg_invite_code_text:'',
			reg_invite_code_type:0,
      parent:{},
	        xlevelarr:{},
			//自定义表单Start
			has_custom:0,
			show_custom_field:false,
			regiondata:'',
			editorFormdata:{},
			test:'',
			formfields:[],
			custom_formdata:[],
			items: [],
			formvaldata:{},
			submitDisabled:false,
			//自定义表单End
			tmplids:[],
			default_headimg:app.globalData.pre_url + '/static/img/touxiang.png',
			headimg:'',
			nickname:'',
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		
		if(this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);
		if(this.opt.logintype) this.logintype = this.opt.logintype;
		if(this.opt.login_bind) this.login_bind = this.opt.login_bind;
		   // 如果 opt 中有 xlevel，则保存到全局变量中
		 
		this.getdata();
  },
   
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiIndex/reg', {pid:app.globalData.pid,xlevel:app.globalData.xlevel}, function (res) {
				that.loading = false;
				if(res.status == 0){
					app.alert(res.msg);return;
				}
				that.logintype_2 = res.logintype_2;
				that.logintype_3 = res.logintype_3;
				that.logintype_3 = res.logintype_3;
				// #ifdef APP-PLUS
				if(that.platform2 == 'ios'){
					if (plus.runtime.isApplicationExist({ pname: 'com.tencent.mm', action: 'weixin://' })) {
						
					}else{
						that.logintype_3 = false;
					}
				}
				// #endif

				that.xystatus = res.xystatus;
				that.xyname = res.xyname;
				that.xycontent = res.xycontent;
				that.xyname2 = res.xyname2;
				that.xycontent2 = res.xycontent2;
				that.logo = res.logo;
				that.name = res.name;
				that.needsms = res.needsms;
				that.platform = res.platform;
        that.reg_invite_code = res.reg_invite_code;
        that.reg_invite_code_text = res.reg_invite_code_text;
        that.reg_invite_code_type = res.reg_invite_code_type;
        that.parent = res.parent;
		that.xlevelarr = res.xlevelarr;
				if(that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app'){
					that.platformname = '微信';
					that.platformimg = 'weixin';
				}
				if(that.platform == 'toutiao'){
					that.platformname = '头条';
					that.platformimg = 'toutiao';
				}
				if(that.platform == 'alipay'){
					that.platformname = '支付宝';
					that.platformimg = 'alipay';
				}
				if(that.platform == 'qq'){
					that.platformname = 'QQ';
					that.platformimg = 'qq';
				}
				if(that.platform == 'baidu'){
					that.platformname = '百度';
					that.platformimg = 'baidu';
				}
				
				//自定义表单
				if(res.has_custom){
					that.formfields = res.custom_form_field;
					that.has_custom = res.has_custom
					that.show_custom_field = true
					uni.request({
						url: app.globalData.pre_url+'/static/area.json',
						data: {},
						method: 'GET',
						header: { 'content-type': 'application/json' },
						success: function(res2) {
							that.items = res2.data
						}
					});
				}
				that.loaded();
			});
		},
    formSubmit: function (e) {
			var that = this;
      var formdata = e.detail.value;
      if (formdata.tel == ''){
        app.alert('请输入手机号');
        return;
      }
      if (formdata.pwd == '') {
        app.alert('请输入密码');
        return;
      }
      if (formdata.pwd.length < 6) {
        app.alert('新密码不小于6位');
        return;
      }
      if (formdata.repwd == '') {
        app.alert('请再次输入新密码');
        return;
      }
      if (formdata.pwd != formdata.repwd) {
        app.alert('两次密码不一致');
        return;
      }
			if(that.needsms){
				if (formdata.smscode == '') {
					app.alert('请输入短信验证码');
					return;
				}
			}else{
				formdata.smscode = '';
			}
			var postdata = {xlevel:app.globalData.xlevel,tel:formdata.tel,pwd:formdata.pwd,smscode:formdata.smscode,pid:app.globalData.pid,yqcode:formdata.yqcode,xlevel:app.globalData.xlevel}
			//如果有自定义表单则验证表单内容
			if(that.show_custom_field){
				var customformdata = {};
				var customData = that.checkCustomFormFields();
				if(!customData){
					return;
				}
				postdata['customformdata'] = customData
				postdata['customformid'] = that.formfields.id
			}
			
      if (that.xystatus == 1 && !that.isagree) {
        app.error('请先阅读并同意用户注册协议');
        return false;
      }
			app.showLoading('提交中');
      app.post("ApiIndex/regsub", postdata, function (data) {
				app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
					if(data.tmplids){
						that.tmplids = data.tmplids;
					}
					that.subscribeMessage(function () {
						setTimeout(function () {
							if(that.opt.fromapp==1 && data.toappurl){
								app.goto(data.toappurl,'redirect');
							}else{
								app.goto('/pages/my/usercenter','redirect');
							}
						}, 1000);
					});
        } else {
          app.error(data.msg);
        }
      });
    },
		getPhoneNumber: function (e) {
			var that = this
			if(e.detail.errMsg == "getPhoneNumber:fail user deny"){
				app.error('请同意授权获取手机号');return;
			}
			wx.login({success (res1){
				console.log(res1);
				var code = res1.code;
				//用户允许授权
				app.post('ApiIndex/wxRegister',{ iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){
					if (res2.status == 1) {
						app.success(res2.msg);
						setTimeout(function () {
							app.goto(that.frompage,'redirect');
						}, 1000);
					} else {
						app.error(res2.msg);
					}
					return;
				})
			}});
		},
		setnicknameregister:function(e){
			//console.log(e);
			//return;
			this.nickname = e.detail.value.nickname;
			if(this.nickname == '' || this.headimg == ''){
				app.alert('请设置头像和昵称');
				return;
			}
			if(this.login_bind!=0){
				this.logintype = 4;
			}else{
				this.register(this.headimg,this.nickname,'','');
			}
		},
		nosetnicknameregister:function(){
			this.nickname = '';
			this.headimg = '';
			if(this.login_bind!=0){
				this.logintype = 4;
			}else{
				this.register('','','','');
			}
		},
		bindregister:function(e){
			var that = this;
			var formdata = e.detail.value;
      if (formdata.tel == ''){
        app.alert('请输入手机号');
        return;
      }
			if (formdata.smscode == '') {
				app.alert('请输入短信验证码');
				return;
			}
			that.register(this.headimg,this.nickname,formdata.tel,formdata.smscode);
		},
		nobindregister:function(){
			this.register(this.headimg,this.nickname,'','');
		},
		register:function(headimg,nickname,tel,smscode){
			var that = this;
			var url = '';
			if(that.platform == 'app') {
				url = 'ApiIndex/appwxRegister';
			} else if(that.platform=='mp' || that.platform=='h5') {
				url = 'ApiIndex/shouquanRegister';
			} else {
				url = 'ApiIndex/'+that.platform+'Register';
			}
			app.post(url,{headimg:headimg,nickname:nickname,tel:tel,smscode:smscode,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){
				if (res2.status == 1) {
					app.success(res2.msg);
					setTimeout(function () {
						app.goto(that.frompage,'redirect');
					}, 1000);
				} else {
					app.error(res2.msg);
				}
				return;
			});
		},
		weixinlogin:function(){
			var that = this;
			if (that.xystatus == 1 && !that.isagree) {
				that.showxieyi = true;
				that.wxloginclick = true;
				return;
			}
			that.wxloginclick = false;
			app.authlogin(function(res){
				if (res.status == 1) {
					app.success(res.msg);
					setTimeout(function () {
						app.goto(that.frompage,'redirect');
					}, 1000);
				} else if (res.status == 3) {
					that.logintype = 5;
					that.login_setnickname = res.login_setnickname
					that.login_bind = res.login_bind
				} else if (res.status == 2) {
					that.logintype = 4;
					that.login_bind = res.login_bind
				} else {
					app.error(res.msg);
				}
			});
		},
    isagreeChange: function (e) {
      var val = e.detail.value;
      if (val.length > 0) {
        this.isagree = true;
      } else {
        this.isagree = false;
      }
      console.log(this.isagree);
    },
    showxieyiFun: function () {
      this.showxieyi = true;
    },
    hidexieyi: function () {
      this.showxieyi = false;
			this.isagree = true;
			if(this.wxloginclick){
				this.weixinlogin();
			}
    },
    showxieyiFun2: function () {
      this.showxieyi2 = true;
    },
    hidexieyi2: function () {
      this.showxieyi2 = false;
			this.isagree = true;
			if(this.wxloginclick){
				this.weixinlogin();
			}
			if(this.iosloginclick){
				this.ioslogin();
			}
    },
    telinput: function (e) {
      this.tel = e.detail.value
    },
		uploadHeadimg:function(){
			var that = this;
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera'],
				success: function(res) {
					var tempFilePaths = res.tempFilePaths;
					var tempFilePath = tempFilePaths[0];
					app.showLoading('上传中');
					uni.uploadFile({
						url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',
						filePath: tempFilePath,
						name: 'file',
						success: function(res) {
							console.log(res)
							app.showLoading(false);
							var data = JSON.parse(res.data);
							if (data.status == 1) {
								that.headimg = data.url;
							} else {
								app.alert(data.msg);
							}
						},
						fail: function(res) {
							app.showLoading(false);
							app.alert(res.errMsg);
						}
					});
				},
				fail: function(res) { //alert(res.errMsg);
				}
			});
		},
		onChooseAvatar:function(e){
			console.log(e)
			var that = this;
			app.showLoading('上传中');
			uni.uploadFile({
				url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',
				filePath: e.detail.avatarUrl,
				name: 'file',
				success: function(res) {
					app.showLoading(false);
					var data = JSON.parse(res.data);
					if (data.status == 1) {
						that.headimg = data.url;
					} else {
						app.alert(data.msg);
					}
				},
				fail: function(res) {
					app.showLoading(false);
					app.alert(res.errMsg);
				}
			});
		},
    smscode: function () {
      var that = this;
      if (that.hqing == 1) return;
      that.hqing = 1;
      var tel = that.tel;
      if (tel == '') {
        app.alert('请输入手机号码');
        that.hqing = 0;
        return false;
      }
      if (!/^1[3456789]\d{9}$/.test(tel)) {
        app.alert("手机号码有误，请重填");
        that.hqing = 0;
        return false;
      }
      app.post("ApiIndex/sendsms", {tel: tel}, function (data) {
        if (data.status != 1) {
          app.alert(data.msg);
        } 
      });
      var time = 120;
      var interval1 = setInterval(function () {
        time--;
        if (time < 0) {
          that.smsdjs = '重新获取';
          that.hqing = 0;
          clearInterval(interval1);
        } else if (time >= 0) {
          that.smsdjs = time + '秒';
        }
      }, 1000);
    },
		//自定义表单
		onchange(e) {
		  const value = e.detail.value
			this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;
		},
		setfield:function(e){
			var field = e.currentTarget.dataset.formidx;
			var value = e.detail.value;
			this.formvaldata[field] = value;
		},
		editorBindPickerChange:function(e){
			var that = this;
			var idx = e.currentTarget.dataset.idx;
			var val = e.detail.value;
			var editorFormdata = this.editorFormdata;
			
			if(!editorFormdata) editorFormdata = {};
			editorFormdata[idx] = val;
			that.editorFormdata = editorFormdata
			this.test = Math.random();
			var field = e.currentTarget.dataset.formidx;
			this.formvaldata[field] = val;
		},
		checkCustomFormFields:function(e){
			var that = this;
			var subdata = this.formvaldata;
			var formcontent = that.formfields.content;
			var formid = that.formfields.id;
			var formdata = new Array();
			for (var i = 0; i < formcontent.length;i++){
				//console.log(subdata['form' + i]);
				if (formcontent[i].key == 'region') {
						subdata['form' + i] = that.regiondata;
				}
				if (formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === null || subdata['form' + i] === undefined || subdata['form' + i].length==0)){
						app.alert(formcontent[i].val1+' 必填');return false;
				}
				if (formcontent[i].key =='switch'){
						if (subdata['form' + i]==false){
								subdata['form' + i] = '否'
						}else{
								subdata['form' + i] = '是'
						}
				}
				if (formcontent[i].key == 'selector') {
						subdata['form' + i] = formcontent[i].val2[subdata['form' + i]]
				}
				if (formcontent[i].key == 'input' && formcontent[i].val4 && subdata['form' + i]!==''){
					if(formcontent[i].val4 == '2'){ //手机号
						if (!/^1[3456789]\d{9}$/.test(subdata['form' + i])) {
							app.alert(formcontent[i].val1+' 格式错误');return false;
						}
					}
					if(formcontent[i].val4 == '3'){ //身份证号
						if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(subdata['form' + i])) {
							app.alert(formcontent[i].val1+' 格式错误');return false;
						}
					}
					if(formcontent[i].val4 == '4'){ //邮箱
						if (!/^(.+)@(.+)$/.test(subdata['form' + i])) {
							app.alert(formcontent[i].val1+' 格式错误');return false;
						}
					}
				}
				formdata.push(subdata['form' + i])
			}
			return subdata;
		},
		editorChooseImage: function (e) {
			var that = this;
			var idx = e.currentTarget.dataset.idx;
			var tplindex = e.currentTarget.dataset.tplindex;
			var editorFormdata = this.editorFormdata;
			if(!editorFormdata) editorFormdata = [];
			app.chooseImage(function(data){
				editorFormdata[idx] = data[0];
				console.log(editorFormdata)
				that.editorFormdata = editorFormdata
				that.test = Math.random();
		
				var field = e.currentTarget.dataset.formidx;
				that.formvaldata[field] = data[0];
		
			})
		},
		removeimg:function(e){
			var that = this;
			var idx = e.currentTarget.dataset.idx;
			var tplindex = e.currentTarget.dataset.tplindex;
			var field = e.currentTarget.dataset.formidx;
			var editorFormdata = this.editorFormdata;
			if(!editorFormdata) editorFormdata = [];
			editorFormdata[idx] = '';
			that.editorFormdata = editorFormdata
			that.test = Math.random();
			that.formvaldata[field] = '';
		},
  }
};
</script>

<style>
page{background:#ffffff}
.container{width:100%;}
.title{margin:70rpx 50rpx 50rpx 40rpx;height:60rpx;line-height:60rpx;font-size: 48rpx;font-weight: bold;color: #000000;}
.regform{ width:100%;padding:0 50rpx;border-radius:5px;background: #FFF;}
.regform .form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:88rpx;line-height:88rpx;border-bottom:1px solid #F0F3F6;margin-top:20rpx}
.regform .form-item:last-child{border:0}
.regform .form-item .img{width:44rpx;height:44rpx;margin-right:30rpx}
.regform .form-item .input{flex:1;color: #000;}
.regform .form-item .code{font-size:30rpx}
.regform .xieyi-item{display:flex;align-items:center;margin-top:50rpx}
.regform .xieyi-item{font-size:24rpx;color:#B2B5BE}
.regform .xieyi-item .checkbox{transform: scale(0.6);}
.regform .form-btn{margin-top:20rpx;width:100%;height:96rpx;line-height:96rpx;color:#fff;font-size:30rpx;border-radius: 48rpx;}
.regform .form-btn2{width:100%;height:80rpx;line-height:80rpx;background:#EEEEEE;border-radius:40rpx;color:#A9A9A9;margin-top:30rpx}
.tologin{color:#737785;font-size:26rpx;display:flex;width:100%;padding:0 80rpx;margin-top:30rpx}

.othertip{height:auto;overflow: hidden;display:flex;align-items:center;width:580rpx;padding:20rpx 20rpx;margin:0 auto;margin-top:60rpx;}
.othertip-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #F2F2F2}
.othertip-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}
.othertip-text .txt{color:#A3A3A3;font-size:22rpx}

.othertype{width:70%;margin:20rpx 15%;display:flex;justify-content:center;}
.othertype-item{width:50%;display:flex;flex-direction:column;align-items:center;}
.othertype-item .img{width:88rpx;height:88rpx;margin-bottom:20rpx}
.othertype-item .txt{color:#A3A3A3;font-size:24rpx}

.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}
.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}


.authlogin{display:flex;flex-direction:column;align-items:center}
.authlogin-logo{width:180rpx;height:180rpx;margin-top:120rpx}
.authlogin-name{color:#999999;font-size:30rpx;margin-top:60rpx;}
.authlogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#51B1F5;border-radius:48rpx;color:#fff;margin-top:100rpx}
.authlogin-btn2{width:580rpx;height:96rpx;line-height:96rpx;background:#EEEEEE;border-radius:48rpx;color:#A9A9A9;margin-top:20rpx}


/* 自定义字段显示 */
.dp-form-item{width: 100%;border-bottom: 1px #ededed solid;padding:16rpx 0px;display:flex;align-items: center;}
.dp-form-item:last-child{border:0}
.dp-form-item .label{line-height: 40rpx;width:140rpx;margin-right: 10px;flex-shrink:0;text-align: right;color: #666666;}
.dp-form-item .input{height: 70rpx;line-height: 70rpx;overflow: hidden;flex:1;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff}
.dp-form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}
.dp-form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.dp-form-item .radio2{display:flex;align-items:center;}
.dp-form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.dp-form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item .layui-form-switch{}
.dp-form-item .picker{height: 70rpx;line-height:70rpx;flex:1;}

.dp-form-item2{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;flex-direction:column;align-items: flex-start;}
.dp-form-item2:last-child{border:0}
.dp-form-item2 .label{height:70rpx;line-height: 70rpx;width:100%;margin-right: 10px;}
.dp-form-item2 .input{height: 70rpx;line-height: 70rpx;overflow: hidden;width:100%;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff}
.dp-form-item2 .textarea{height:180rpx;line-height:40rpx;overflow: hidden;width:100%;border:1px solid #eee;border-radius:2px;padding:8rpx}
.dp-form-item2 .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}
.dp-form-item2 .radio2{display:flex;align-items:center;}
.dp-form-item2 .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item2 .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}
.dp-form-item2 .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item2 .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item2 .layui-form-switch{}
.dp-form-item2 .picker{height: 70rpx;line-height:70rpx;flex:1;width:100%;}
.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}

.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.dp-form-imgbox-close .image{width:100%;height:100%}
.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.dp-form-imgbox-img>.image{max-width:100%;}
.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}
</style>