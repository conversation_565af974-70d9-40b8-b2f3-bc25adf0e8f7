# UniApp 微信小程序组件开发注意事项

## 类名绑定语法

在 UniApp 中开发微信小程序组件时，类名绑定有特定规则：

1. 微信小程序不支持在 `:class` 中直接调用方法，例如 `:class="getItemClass(item)"`

2. 应该使用对象语法来绑定类名，例如：
   ```html
   <view :class="{
     'class-a': condition1,
     'class-b': condition2,
     'class-c': condition3
   }">
   ```

3. 或者使用数组语法：
   ```html
   <view :class="['base-class', condition ? 'conditional-class' : '']">
   ```

## 子包优化

为了优化小程序包体积和加载速度，建议：

1. 将仅在特定功能页面使用的组件移动到对应的子包中

2. 例如：
   - `popmsg` 组件已经从 `/components/popmsg/` 移动到 `/pages/live/components/popmsg/`
   - 这样可以减小主包体积，提高首次加载速度

## 组件代码结构标准

每个组件应遵循以下结构标准：

1. 日志记录使用统一的时间戳-级别-[文件名]-[函数名_数字序号]格式
   ```js
   console.log('2025-06-10 20:55:53,565-INFO-[component-name][function_001] 日志内容');
   ```

2. 组件 props 应该有清晰的类型定义和默认值

3. 组件样式应尽量使用局部样式，避免全局污染

## 常见错误及解决方案

1. `:class` 不支持方法调用
   - 错误：`:class="getClassName(item)"`
   - 解决方案：使用对象或数组语法，`:class="{ 'class-name': condition }"`

2. 组件应放置在合适的路径
   - 通用组件放在 `/components/`
   - 特定页面组件放在对应子包的 `/pages/xxx/components/` 