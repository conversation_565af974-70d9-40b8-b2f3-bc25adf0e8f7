<template>
	<view>
		<block v-if="isload">
			<view class="container">
				<view class="header">
					<view class="flex-y-center">
						<image class="header_icon" :src="logo"></image>
						<view class="flex1">
							<view class="header_name">{{name}}</view>
							<view class="header_shop">
								<text>选择门店:</text><text @tap="selectmd">{{mdlist[mdkey].name}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="page">
					<view class="page_module flex-y-center" @click="handleShowKey">
						<text class="page_tag">￥</text>
						<!-- <input class="page_price flex1" type="digit" @input="inputMoney" placeholder="请输入金额"></input> -->
						<view class="page_price flex-y-center">
							<text v-if="keyHidden&&!money" class="page_notice">请输入金额</text>
							<text v-if="money">{{ money }}</text>
							<view v-if="!keyHidden" class="page_cursor"></view>
						</view>
					</view>
					<view>
						<view class="info-box">
							<view class="dkdiv-item flex" v-if="userinfo.discount>0 && userinfo.discount<10">
								<text class="f1">{{t('会员')}}折扣({{userinfo.discount*100/100}}折)</text>
								<text class="f2" style="color: #e94745;">-￥{{disprice}}</text>
							</view>
							<view class="dkdiv-item flex-y-center">
								<text class="f1">{{t('优惠券')}}</text>
								<text class="f2" v-if="couponList.length>0" @tap="showCouponList" style="color:#e94745">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+t('优惠券')}}</text>
								<text class="f2" v-else style="color:#999">无可用{{t('优惠券')}}</text>
								<image class="f3" src="/static/img/arrowright.png"></image>
							</view>
							<view class="dkdiv-item flex" v-if="userinfo.scoredkmaxpercent > 0">
								<checkbox-group @change="scoredk" class="flex" style="width:100%">
									<view class="f1">
										<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text
												style="color:#e94745">{{userinfo.dkmoney*1}}</text> 元</view>
										<view style="font-size:22rpx;color:#999"
											v-if="userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100">
											最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>
									</view>
									<view class="f2">使用{{t('积分')}}抵扣
										<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
									</view>
								</checkbox-group>
							</view>
							<view class="dkdiv-item flex flex-bt">
								<text class="t1">实付金额:</text>
								<text class="t2">￥{{paymoney}}</text>
							</view>

							<view v-if="keyHidden" class="op">
								<view class="btn" @tap="topay" :style="{background:t('color1')}">去支付</view>
							</view>
						</view>
					</view>
				</view>



				<!-- <view class="info-box">
					<view class="info-item">
						<text class="t1">商户名称:</text>
						<text class="t2">{{name}}</text>
					</view>
					<view class="info-item" v-if="mdlist.length>0">
						<text class="t1">选择门店:</text>
						<text class="t2" @tap="selectmd">{{mdlist[mdkey].name}}</text>
					</view>
					<view class="info-item">
						<text class="t1">支付金额:</text>
						<view class="t2 flex-y-center" style="justify-content:flex-end"><input type="digit" @input="inputMoney" value="" placeholder="请输入金额"></input> 元</view>
					</view>
				</view> -->
				<!-- <view class="info-box">
					<view class="dkdiv-item flex" v-if="userinfo.discount>0 && userinfo.discount<10">
						<text class="f1">{{t('会员')}}折扣({{userinfo.discount*100/100}}折)</text>
						<text class="f2" style="color: #e94745;">-￥{{disprice}}</text>
					</view>
					<view class="dkdiv-item flex">
						<text class="f1">{{t('优惠券')}}</text>
						<text class="f2" @tap="showCouponList"
							style="color:#e94745">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+t('优惠券')}}</text>
						<image class="f3" src="/static/img/arrowright.png"></image>
					</view>
					<view class="dkdiv-item flex">
						<checkbox-group @change="scoredk" class="flex" style="width:100%">
							<text class="f1">{{userinfo.score*1}} {{t('积分')}}可抵扣 <text
									style="color:#e94745">{{userinfo.dkmoney*1}}</text> 元</text>
							<view class="f2">使用{{t('积分')}}抵扣
								<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
							</view>
						</checkbox-group>
					</view>
				</view> -->
				<!-- <view class="info-box">
					<view class="info-item">
						<text class="t1">实付金额:</text>
						<text class="t2">￥{{paymoney}}</text>
					</view>
				</view> -->
				<!-- <view class="op">
					<view class="btn" @tap="topay" :style="{background:t('color1')}">去支付</view>
				</view> -->
			</view>

			<view v-if="couponvisible" class="popup__container">
				<view class="popup__overlay" @tap.stop="handleClickMask"></view>
				<view class="popup__modal">
					<view class="popup__title">
						<text class="popup__title-text">请选择{{t('优惠券')}}</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx"
							@tap.stop="handleClickMask" />
					</view>
					<view class="popup__content">
						<couponlist :couponlist="couponList" :choosecoupon="true" :selectedrid="couponrid"
							@chooseCoupon="chooseCoupon"></couponlist>
					</view>
				</view>
			</view>

			<view v-if="!keyHidden" class="keyboard_page">
				<view @click="handleHiddenKey" class="keyboard_none"></view>
				<view class="keyboard_key hind_box" :class="menuindex>-1?'tabbarbot':'notabbarbot'">
					<image @click="handleHiddenKey" class="key-down" :src="pre_url+'/static/img/pack_up.png'" mode=""></image>
					<view class="key-box">
						<view class="number-box clearfix">
							<view v-for="(item,index) in KeyboardKeys" :key="index"
								:class="index === 9 ? 'key key-zero' : 'key'" hover-class="number-box-hover"
								@click="handleKey(item)">{{item}}</view>
						</view>
						<view class="btn-box">
							<!-- TODO: 需要替换成删除icon -->
							<view class="key" hover-class="number-box-hover" data-key="X" @click="handleKey('X')">×
							</view>
							<view :class="money ? 'key pay_btn' : 'key pay_btn pay-btn-display'"
								hover-class="pay-btn-hover" @tap="topay">付款</view>
						</view>
					</view>
				</view>
			</view>

			<view v-if="selectmdDialogShow" class="popup__container">
				<view class="popup__overlay" @tap.stop="hideSelectmdDialog"></view>
				<view class="popup__modal">
					<view class="popup__title">
						<text class="popup__title-text">请选择门店</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hideSelectmdDialog"/>
					</view>
					<view class="popup__content">
						<view class="pstime-item" v-for="(item, index) in mdlist" :key="index" @tap="selectmdRadioChange" :data-index="index">
							<view class="flex1">{{item.name}}</view>
							<view style="color:#999;font-size:24rpx;margin-right:10rpx">{{item.juli ? ' 距离:' + item.juli + '千米' : ''}}</view>
							<view class="radio" :style="index==mdkey ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
						</view>
					</view>
				</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				pre_url: app.globalData.pre_url,
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				bid: 0,
                ymid: 0,
				hiddenmodalput: true,
				wxpayst: '',
				alipay: '',
				paypwd: '',
				moneypay: '',
				mdlist: "",
				name: "",
				userinfo: "",
				couponList: [],
				couponrid: 0,
				coupontype: 1,
				usescore: 0,
				money: '',
				disprice: 0,
				dkmoney: 0,
				couponmoney: 0,
				paymoney: 0,
				mdkey: 0,
				couponvisible: false,
				couponkey: 0,
				logo:"",

				KeyboardKeys: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, '.'],
				keyHidden: false,
				selectmdDialogShow: false,
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.bid = this.opt.bid || 0;
            if(this.opt.ymid){
                this.ymid          = this.opt.ymid;
                app.globalData.pid = this.opt.ymid;
                uni.setStorageSync('pid', this.opt.ymid);
            }
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {

			handleHiddenKey() {
				this.keyHidden = true;
			},
			// 显示键盘
			handleShowKey() {
				this.keyHidden = false;
			},
			// 键盘输入
			handleKey(key) {
				const that = this
				const {
					money
				} = this
				// 删除金额
				if (key === 'X') {
					if (money !== '') {
						const payMoney = money.slice(0, money.length - 1)
						that.money = payMoney
					}
				} else {
					// 添加金额
					const payMoney = money + key
					console.log(/^(\d+\.?\d{0,2})$/.test(payMoney), payMoney, 'payMoney')
					if (/^(\d+\.?\d{0,2})$/.test(payMoney)) {
						that.money = payMoney
					}
				}
				this.calculatePrice();
			},


			getdata: function() {
				var that = this; //获取产品信息
				that.loading = true;
				app.get('ApiMaidan/maidan', {
					bid: that.bid
				}, function(res) {
					that.loading = false;
					if (res.status == 0) {
						app.alert(res.msg, function() {
							app.goback();
						});
						return;
					}
					var userinfo = res.userinfo;
					var couponList = res.couponList;
					var mdlist = res.mdlist;
					that.wxpayst = res.wxpayst;
					that.alipay = res.alipay;
					that.couponList = res.couponList;
					that.mdlist = res.mdlist;
					that.moneypay = res.moneypay;
					that.name = res.name;
					that.userinfo = res.userinfo;
					that.logo = res.logo;
					that.loaded();

					if (mdlist.length > 0) {
						app.getLocation(function(res) {
							var latitude = res.latitude;
							var longitude = res.longitude;
							var speed = res.speed;
							var accuracy = res.accuracy;

							for (var i in mdlist) {
								mdlist[i].juli = that.GetDistance(latitude, longitude, mdlist[i]
									.latitude, mdlist[i].longitude);
							}

							mdlist = mdlist.sort(that.compare('juli'));
							console.log(mdlist);
							that.mdlist = mdlist;
						});
					}
				});
			},
			modalinput: function() {
				this.$refs.dialogInput.open()
			},
			//选择门店
			selectmd: function(e) {
				var that = this;
				var itemlist = [];
				var mdlist = this.mdlist;
				for (var i = 0; i < mdlist.length; i++) {
					itemlist.push(mdlist[i].name + (mdlist[i].juli ? ' 距离:' + mdlist[i].juli + '千米' : ''));
				}
				if (itemlist.length > 6) {
					that.selectmdDialogShow = true;
				} else {
					uni.showActionSheet({
						itemList: itemlist,
						success: function(res) {
							if (res.tapIndex >= 0) {
								that.mdkey = res.tapIndex;
							}
						}
					});
				}
			},
			selectmdRadioChange: function (e) {
				this.mdkey = e.currentTarget.dataset.index;
				this.selectmdDialogShow = false;
			},
			hideSelectmdDialog: function () {
				this.selectmdDialogShow = false
			},
			//积分抵扣
			scoredk: function(e) {
				var usescore = e.detail.value[0];
				if (!usescore) usescore = 0;
				this.usescore = usescore;
				this.calculatePrice();
			},
			inputMoney: function(e) {
				console.log(e);
				var money = e.detail.value;
				if (!money) money = 0;
				var money = parseFloat(money);
				if (money <= 0) money = 0;
				this.money = money;
				this.calculatePrice();
			},
			cancel: function() {
				this.hiddenmodalput = true;
			},
			//计算价格
			calculatePrice: function() {
				var that = this;

				var money = ''
				if (that.money == '') {
					money = 0;
				} else {
					money = parseFloat(that.money);
				}
				if (that.userinfo.discount > 0 && that.userinfo.discount < 10) {
					var disprice = Math.round(money * (1 - 0.1 * that.userinfo.discount) * 100) / 100; //-会员折扣
				} else {
					var disprice = 0;
				}
				var couponmoney = parseFloat(that.couponmoney); //-优惠券抵扣 
				if (that.usescore) {
					var dkmoney = parseFloat(that.userinfo.dkmoney); //-积分抵扣
				} else {
					var dkmoney = 0;
				}
				var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例
				if (dkmoney > 0 && scoredkmaxpercent >= 0 && scoredkmaxpercent < 100 &&
					dkmoney > (money - disprice - couponmoney) * scoredkmaxpercent * 0.01) {
					dkmoney = (money - disprice - couponmoney) * scoredkmaxpercent * 0.01;
				}

				var paymoney = money - disprice - couponmoney - dkmoney; // 商品金额 - 会员折扣 - 优惠券抵扣 - 积分抵扣
				if (paymoney < 0) paymoney = 0;
				paymoney = paymoney.toFixed(2);
				that.paymoney = paymoney;
				that.disprice = disprice;
			},
			chooseCoupon: function(e) {
				var couponrid = e.rid;
				var couponkey = e.key;

				if (couponrid == this.couponrid) {
					this.couponkey = 0;
					this.couponrid = 0;
					this.coupontype = 1;
					this.couponmoney = 0;
					this.couponvisible = false;
				} else {
					var couponList = this.couponList;
					var couponmoney = couponList[couponkey]['money'];
					var coupontype = couponList[couponkey]['type'];
					if (coupontype == 4) {
						couponmoney = this.freightprice;
					}
					this.couponkey = couponkey;
					this.couponrid = couponrid;
					this.coupontype = coupontype;
					this.couponmoney = couponmoney;
					this.couponvisible = false;
				}
				this.calculatePrice();
			},
			topay: function(e) {
				var that = this;
				var money = that.money;
				var couponrid = that.couponrid;
				var usescore = that.usescore;

				if (that.mdlist.length > 0) {
					var mdid = that.mdlist[that.mdkey].id;
				} else {
					var mdid = 0;
				}
				//
				let charge_s = [];
				charge_s.push(that.bid+"-"+money);
				app.post('ApiMoney/recharge', {
					bid: that.bid,
                    ymid: that.ymid,
					money: money,
					couponrid: couponrid,
					usescore: usescore,
					'charge_s':charge_s,
					mdid: mdid
				}, function(res) {
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					app.goto('/pages/pay/pay?id=' + res.payorderid);
				});
			},
			showCouponList: function() {
				this.couponvisible = true;
			},
			handleClickMask: function() {
				this.couponvisible = false;
			},
			GetDistance: function(lat1, lng1, lat2, lng2) {
				var radLat1 = lat1 * Math.PI / 180.0;
				var radLat2 = lat2 * Math.PI / 180.0;
				var a = radLat1 - radLat2;
				var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
				var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) *
					Math.pow(Math.sin(b / 2), 2)));
				s = s * 6378.137; // EARTH_RADIUS;
				s = Math.round(s * 100) / 100;
				return s;
			},
			compare: function(property) {
				return function(a, b) {
					var value1 = a[property];
					var value2 = b[property];
					return value1 - value2;
				};
			}
		}
	}
</script>
<style>
	page {
		background: #f0f0f0;
	}

	.container {
		position: fixed;
		height: 100%;
		width: 100%;
		overflow: hidden;
		z-index: 5;
	}

	.header {
		position: relative;
		padding: 30rpx;
	}

	.header_text {
		font-size: 24rpx;
		color: #666;
	}

	.header_name {
		font-size: 36rpx;
		color: #333;
		font-weight: bold;
	}

	.header_icon {
		position: relative;
		height: 85rpx;
		width: 85rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
		background: #37b053;
	}

	.header_shop {
		font-size: 28rpx;
		color: #333;
		margin-top: 10rpx;
	}

	.page {
		position: relative;
		padding: 20rpx 50rpx 20rpx 50rpx;
		border-radius: 30rpx 30rpx 0 0;
		background: #fff;
		box-sizing: border-box;
		width: 100%;
		height: calc(100% - 185rpx);
	}

	.page_title {
		font-size: 24rpx;
		color: #333;
	}

	.page_module {
		position: relative;
		height: 125rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.page_notice {
		color: #999;
		font-size: 32rpx;
		font-weight: normal;
	}

	.page_tag {
		font-size: 58rpx;
		color: #333;
		font-weight: bold;
	}

	.page_price {
		margin-left: 20rpx;
		font-size: 54rpx;
		color: #333;
		font-weight: bold;
	}

	.page_cursor {
		width: 4rpx;
		height: 70rpx;
		background: #1AAD19;
		border-radius: 6rpx;
		animation: twinkling 1.5s infinite;
	}

	@keyframes twinkling {
		0% {
			opacity: 0;
		}

		90% {
			opacity: .8;
		}

		100% {
			opacity: 1;
		}
	}

	.info-box {
		position: relative;
		background: #fff;
	}

	.info-item {
		display: flex;
		align-items: center;
		border-bottom: 1px #f3f3f3 solid;
	}

	.info-item:last-child {
		border: none
	}

	.info-item .t1 {
		width: 200rpx;
		height: 120rpx;
		line-height: 120rpx;
		color: #000;
	}

	.info-item .t2 {
		height: 120rpx;
		line-height: 120rpx;
		color: #000;
		text-align: right;
		flex: 1;
		font-size: 28rpx
	}

	.info-item .t2 input {
		height: 80rpx;
		line-height: 80rpx;
		border: 1px solid #f5f5f5;
		padding: 0 5px;
		width: 240rpx;
		font-size: 30rpx;
		margin-right: 10rpx
	}

	.dkdiv {
		margin-top: 20rpx
	}

	.dkdiv-item {
		width: 100%;
		padding: 30rpx 0;
		background: #fff;
		border-bottom: 1px #ededed solid;
	}

	.dkdiv-item:last-child {
		border: none;
	}

	.dkdiv-item .f1 {}

	.dkdiv-item .f2 {
		text-align: right;
		flex: 1
	}

	.dkdiv-item .f3 {
		width: 30rpx;
		height: 30rpx;
	}

	.fpay-btn {
		width: 90%;
		margin: 0 5%;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 40rpx;
		float: left;
		border-radius: 10rpx;
		color: #fff;
		background: #1aac19;
		border: none;
		font-size: 30rpx;
	}

	.fpay-btn2 {
		width: 90%;
		margin: 0 5%;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 20rpx;
		float: left;
		border-radius: 10rpx;
		color: #fff;
		background: #e2cc05;
		border: none;
		font-size: 30rpx;
	}

	.mendian {
		width: 90%;
		line-height: 60rpx;
		border-radius: 10rpx;
		padding: 30rpx 5%;
		height: 800rpx;
		overflow-y: scroll;
		border: none;
		border-radius: 5px;
		-webkit-animation-duration: .5s;
		animation-duration: .5s;
	}

	.mendian label {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #f5f5f5;
		padding: 20rpx 0;
		color: #333
	}

	.mendian input {
		margin-right: 10rpx
	}

	.submit {
		text-align: center
	}

	.mendian button {
		padding: 20rpx 60rpx;
		border-radius: 40rpx;
		border: 0;
		margin-top: 20rpx;
		color: #fff;
		background: #31C88E
	}

	.i-as {
		position: fixed;
		width: 100%;
		box-sizing: border-box;
		left: 0;
		right: 0;
		bottom: 0;
		background: #f7f7f8;
		transform: translate3d(0, 100%, 0);
		transform-origin: center;
		transition: all .2s ease-in-out;
		z-index: 900;
		visibility: hidden
	}

	.i-as-show {
		transform: translate3d(0, 0, 0);
		visibility: visible
	}

	.i-as-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, .7);
		z-index: 900;
		transition: all .2s ease-in-out;
		opacity: 0;
		visibility: hidden
	}

	.i-as-mask-show {
		opacity: 1;
		visibility: visible
	}

	.i-as-header {
		background: #fff;
		text-align: center;
		position: relative;
		font-size: 30rpx;
		color: #555;
		height: 80rpx;
		line-height: 80rpx
	}

	.i-as-header::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 200%;
		height: 200%;
		transform: scale(.5);
		transform-origin: 0 0;
		pointer-events: none;
		box-sizing: border-box;
		border: 0 solid #e9eaec;
		border-bottom-width: 1px
	}

	.i-as-cancel {
		margin-top: 20rpx
	}

	.i-as-cancel button {
		border: 0
	}

	.i-as-cancel button::after {
		border: 0;
	}

	.i-as-content {
		height: 700rpx;
		width: 710rpx;
		margin: 20rpx;
	}


	.op {
		width: 96%;
		margin: 20rpx 2%;
		display: flex;
		align-items: center;
		margin-top: 40rpx
	}

	.op .btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		background: #07C160;
		width: 90%;
		margin: 0 10rpx;
		border-radius: 10rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center
	}

	.op .btn .img {
		width: 48rpx;
		height: 48rpx;
		margin-right: 20rpx
	}


	.keyboard_page {
		position: fixed;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
		z-index: 999;
	}

	.keyboard_none {
		position: absolute;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
	}

	.keyboard_key {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 0;
		z-index: 10;
		background: #f7f7f7;
		z-index: 9999999999;
		transition: height 0.3s;
		padding: 20rpx 0 0 0;
	}

	.hind_box {
		height: 515rpx;
	}

	.key-box {
		display: flex;
		padding-left: 16rpx;
		padding-bottom: 16rpx;
		padding-bottom: calc(16rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
	}
	
	.key-down{
		height: 50rpx;
		width: 50rpx;
		display: block;
		margin: 0 auto;
	}

	.number-box {
		flex: 3;
	}

	.number-box .key {
		float: left;
		margin: 16rpx 16rpx 0 0;
		width: calc(100% / 3 - 16rpx);
		height: 90rpx;
		border-radius: 10rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 40rpx;
		font-weight: bold;
		background-color: #fff;
	}

	.number-box .key.key-zero {
		width: calc((100% / 3) * 2 - 16rpx);
	}

	.keyboard .number-box-hover {
		/* 临时定义颜色 */
		background-color: #e1e1e1 !important;
	}

	.btn-box {
		flex: 1;
	}

	.btn-box .key {
		margin: 16rpx 16rpx 0 0;
		height: 90rpx;
		border-radius: 10rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 40rpx;
		font-weight: bold;
		background-color: #fff;
	}

	.btn-box .pay_btn {
		height: 298rpx;
		line-height: 298rpx;
		font-weight: normal;
		background-color: #1AAD19;
		color: #fff;
		font-size: 32rpx;
	}

	.btn-box .pay_btn.pay-btn-display {
		background-color: #9ED99D !important;
	}

	.btn-box .pay_btn.pay-btn-hover {
		background-color: #179B16;
	}
	.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}
.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.pstime-item .radio .radio-img {width: 100%;height: 100%}
</style>
