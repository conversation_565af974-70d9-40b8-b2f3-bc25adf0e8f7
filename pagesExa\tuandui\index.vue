<template>
<view>
	<block v-if="isload">
		<view class="container">
			<view class="header">
				<text class="title">团队业绩奖励</text>
			</view>

			<!-- 用户奖励信息 -->
			<view class="reward-info-box" v-if="userRewardInfo && userRewardInfo.activity_info">
				<view class="section-title">我的奖励信息</view>
				<view class="reward-card">
					<view class="activity-title">{{userRewardInfo.activity_info.title}}</view>
					<view class="algorithm-info" v-if="userRewardInfo.activity_info.algorithm_type">
						<text class="algorithm-tag" :class="userRewardInfo.activity_info.algorithm_type === 'layered_reduction' ? 'algorithm-layered' : 'algorithm-standard'">
							{{userRewardInfo.activity_info.algorithm_type === 'layered_reduction' ? '分层递减算法' : '传统算法'}}
						</text>
					</view>
					<view class="performance-info">
						<view class="perf-type">业绩类型：{{userRewardInfo.activity_info.performance_type == 1 ? '团队金额' : '团队数量'}}</view>
						<view class="reward-type">奖励类型：{{userRewardInfo.activity_info.reward_type == 1 ? '比例奖励' : '固定金额'}}</view>
					</view>
					<view class="team-performance" v-if="userRewardInfo.user_performance && userRewardInfo.team_structure">
						<view class="perf-value">团队业绩：{{userRewardInfo.user_performance.team_performance}}{{userRewardInfo.activity_info.performance_type == 1 ? '元' : '件'}}</view>
						<view class="team-count">团队成员：{{userRewardInfo.team_structure.total_members}}人</view>
					</view>
					<view class="stage-info" v-if="userRewardInfo.current_stage">
						<view class="current-stage">当前阶段：{{userRewardInfo.current_stage.achievement}} ({{userRewardInfo.current_stage.reward_value}}{{userRewardInfo.activity_info.reward_type == 1 ? '%' : '元'}})</view>
						<view class="next-stage" v-if="userRewardInfo.next_stage">下一阶段：{{userRewardInfo.next_stage.achievement}} ({{userRewardInfo.next_stage.reward_value}}{{userRewardInfo.activity_info.reward_type == 1 ? '%' : '元'}})</view>
					</view>
					<view class="reward-summary" v-if="userRewardInfo.user_performance">
						<view class="theoretical-reward">理论奖励：¥{{userRewardInfo.user_performance.theoretical_reward}}</view>
						<view class="actual-reward" v-if="userRewardInfo.activity_info.algorithm_type === 'layered_reduction'">实际奖励：¥{{userRewardInfo.user_performance.actual_reward}}</view>
						<view class="calculation-detail" v-if="userRewardInfo.user_performance.calculation_detail">
							{{userRewardInfo.user_performance.calculation_detail}}
						</view>
					</view>
				</view>
			</view>

			<!-- 奖励详情列表 -->
			<view class="reward-details-box" v-if="userRewardInfo && userRewardInfo.reward_details && userRewardInfo.activity_info">
				<view class="section-title">奖励详情</view>
				<view class="reward-details">
					<block v-for="(item, index) in userRewardInfo.reward_details" :key="index">
						<view class="detail-item">
							<view class="detail-header">
								<view class="achievement-target">目标业绩：{{item.achievement_target}}</view>
								<view class="reward-value">奖励：{{item.reward_value}}{{userRewardInfo.activity_info.reward_type == 1 ? '%' : '元'}}</view>
							</view>
							<view class="detail-content">
								<view class="reward-amount-info">
									<view class="theoretical-amount">理论奖励：¥{{item.theoretical_reward_amount}}</view>
									<view class="actual-amount" v-if="userRewardInfo.activity_info.algorithm_type === 'layered_reduction' && item.actual_reward_amount !== item.theoretical_reward_amount">
										实际奖励：¥{{item.actual_reward_amount}}
									</view>
								</view>
								<view class="status-info">
									<text class="status" :class="item.is_achieved ? (item.is_paid ? 'status-paid' : (item.is_claimed ? 'status-claimed' : 'status-achieved')) : 'status-not-achieved'">{{item.is_achieved ? (item.is_paid ? '已发放' : (item.is_claimed ? '已领取' : '已达成')) : '未达成'}}</text>
									<view class="action-btn" v-if="item.is_achieved && !item.is_claimed && !item.is_paid">
										<text class="claim-btn" @tap="claimReward" :data-activity="item.activity_id" :data-level="item.achievement_level">立即领取</text>
									</view>
								</view>
							</view>
							<view class="calculation" v-if="item.calculation">{{item.calculation}}</view>
						</view>
					</block>
				</view>
			</view>

			<!-- 活动列表 -->
			<view class="activity-list-box">
				<view class="section-title">所有活动</view>
				<view class="activity-list">
					<block v-for="(item, index) in activityList" :key="item.id">
						<view class="activity-item" @tap="selectActivity" :data-id="item.id">
							<view class="activity-header">
								<text class="activity-title">{{item.title}}</text>
								<text class="activity-time">{{item.createtime}}</text>
							</view>
							<view class="activity-info">
								<view class="type-info">
									<text class="perf-type">{{item.performance_name || (item.performance_type == 1 ? '团队金额' : '团队数量')}}</text>
									<text class="reward-type">{{item.reward_name || (item.reward_type == 1 ? '比例奖励' : '固定金额')}}</text>
									<text class="algorithm-type" :class="item.algorithm_type === 'layered_reduction' ? 'algorithm-layered' : 'algorithm-standard'">
										{{item.algorithm_name || (item.algorithm_type === 'layered_reduction' ? '分层递减算法' : '传统算法')}}
									</text>
								</view>
								<view class="participation-info" v-if="item.user_participated">
									<text class="user-performance">我的业绩：{{item.user_performance}}</text>
									<text class="user-reward">我的奖励：¥{{item.user_reward}}</text>
								</view>
							</view>
							<view class="activity-footer">
								<view class="activity-status">
									<text class="status-text" :class="item.status == 1 ? 'status-active' : 'status-ended'">{{item.status_text || (item.status == 1 ? '进行中' : '已结束')}}</text>
								</view>
								<view class="activity-actions">
									<text class="detail-btn" @tap.stop="gotoDetail" :data-id="item.id">查看详情</text>
									<text class="switch-btn" @tap.stop="switchActivity" :data-id="item.id" v-if="selectedActivityId != item.id">切换</text>
									<text class="current-btn" v-if="selectedActivityId == item.id">当前</text>
								</view>
							</view>
						</view>
					</block>
				</view>
			</view>

			<!-- 奖励记录入口 -->
			<view class="records-entry">
				<view class="entry-btn" @tap="gotoRecords">
					<text>查看奖励记录</text>
					<image src="/static/img/arrow-right.png" class="arrow-icon"></image>
				</view>
			</view>
		</view>
	</block>

	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			userRewardInfo: null,
			activityList: [],
			selectedActivityId: 0
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.selectedActivityId = opt.activity_id || 0;
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShow: function() {
		// 页面显示时刷新数据
		if (this.isload) {
			this.getUserRewardInfo();
		}
	},
	methods: {
		getdata: function() {
			var that = this;
			that.loading = true;
			
			// 获取活动列表
			app.get('ApiTuandui/getActivityList', {}, function(res) {
				if (res.status == 1) {
					that.activityList = res.data || [];
					// 如果没有指定活动ID且有活动列表，使用第一个活动
					if (!that.selectedActivityId && that.activityList.length > 0) {
						that.selectedActivityId = that.activityList[0].id;
					}
				}
				// 获取用户奖励信息
				that.getUserRewardInfo();
			});
		},
		
		getUserRewardInfo: function() {
			var that = this;
			var params = {};
			if (that.selectedActivityId > 0) {
				params.activity_id = that.selectedActivityId;
			}
			
			app.get('ApiTuandui/getUserRewardInfo', params, function(res) {
				that.loading = false;
				if (res.status == 1) {
					that.userRewardInfo = res.data;
					that.isload = true;
					// 安全访问 activity_info 属性
					var algorithmText = '传统模式';
					if (res.data && res.data.activity_info && res.data.activity_info.algorithm_type === 'layered_reduction') {
						algorithmText = '分层递减';
					}
					uni.setNavigationBarTitle({
						title: '团队业绩奖励 - ' + algorithmText
					});
				} else {
					app.alert(res.msg || '获取奖励信息失败');
					that.isload = true;
				}
				uni.stopPullDownRefresh();
			});
		},
		
		selectActivity: function(e) {
			var that = this;
			var activityId = e.currentTarget.dataset.id;
			console.log('2025-01-03 22:55:53,565-INFO-[index.vue][selectActivity_001] 点击活动ID:', activityId);
			
			// 跳转到活动详情页面
			app.goto('/pagesExa/tuandui/detail?id=' + activityId);
		},
		
		gotoDetail: function(e) {
			var that = this;
			var activityId = e.currentTarget.dataset.id;
			console.log('2025-01-03 22:55:53,565-INFO-[index.vue][gotoDetail_001] 查看详情ID:', activityId);
			
			// 跳转到活动详情页面
			app.goto('/pagesExa/tuandui/detail?id=' + activityId);
		},
		
		switchActivity: function(e) {
			var that = this;
			var activityId = e.currentTarget.dataset.id;
			console.log('2025-01-03 22:55:53,565-INFO-[index.vue][switchActivity_001] 切换活动ID:', activityId);
			
			that.selectedActivityId = activityId;
			that.getUserRewardInfo();
		},
		
		claimReward: function(e) {
			var activityId = e.currentTarget.dataset.activity;
			var level = e.currentTarget.dataset.level;
			
			console.log('2025-01-03 22:55:53,565-INFO-[index.vue][claimReward_001] 领取奖励:', activityId, level);
			
			// TODO: 实现领取奖励逻辑
			this.$refs.popmsg.show('提示', '领取奖励功能待实现');
		},
		
		gotoRecords: function() {
			app.goto('/pagesExa/tuandui/records');
		}
	}
};
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.header {
	background: linear-gradient(to right, #ff8c00, #ff5722);
	padding: 30rpx 20rpx;
	text-align: center;
	border-bottom: none;
}

.header .title {
	font-size: 38rpx;
	color: #fff;
	font-weight: bold;
}

.section-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	padding: 25rpx 30rpx;
	background-color: transparent;
	border-bottom: none;
	margin-top: 20rpx;
}

/* 用户奖励信息样式 */
.reward-info-box {
	margin: 20rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.reward-card {
	padding: 30rpx;
}

.reward-info-box .activity-title {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 15rpx;
	text-align: center;
}

.algorithm-info {
	text-align: center;
	margin-bottom: 20rpx;
}

.algorithm-tag {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-weight: 500;
}

.algorithm-layered {
	background-color: #e3f2fd;
	color: #1976d2;
}

.algorithm-standard {
	background-color: #f3e5f5;
	color: #7b1fa2;
}

.performance-info,
.team-performance,
.stage-info,
.reward-summary {
	margin-bottom: 20rpx;
	font-size: 28rpx;
}

.performance-info view,
.team-performance view,
.stage-info view {
	line-height: 1.7;
	color: #555;
	display: flex;
	justify-content: space-between;
	padding: 8rpx 0;
}

.reward-summary {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 15rpx;
}

.theoretical-reward,
.actual-reward {
	padding: 8rpx 0;
	font-weight: bold;
}

.theoretical-reward {
	color: #ff9800;
}

.actual-reward {
	color: #ff5722;
}

.calculation-detail {
	font-size: 24rpx;
	color: #888;
	background-color: #f9f9f9;
	padding: 10rpx;
	border-radius: 8rpx;
	margin-top: 10rpx;
}

.perf-value,
.total-reward,
.rewarded-amount,
.pending-reward {
	color: #ff5722;
	font-weight: bold;
}

.reward-summary .rewarded-amount {
	color: #4caf50;
}

.reward-summary .pending-reward {
	color: #ff9800;
}

/* 奖励详情样式 */
.reward-details-box {
	margin: 20rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.reward-details-box .section-title {
	margin-top: 0;
	border-bottom: 1rpx solid #eee;
}

.detail-item {
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
	border-bottom: none;
}

.detail-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.achievement-target {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.detail-header .reward-value {
	font-size: 26rpx;
	color: #ff5722;
	font-weight: bold;
}

.detail-content {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 10rpx;
}

.reward-amount-info {
	flex: 1;
}

.theoretical-amount,
.actual-amount {
	font-size: 26rpx;
	font-weight: bold;
	line-height: 1.4;
}

.theoretical-amount {
	color: #ff9800;
}

.actual-amount {
	color: #ff5722;
}

.status-info {
	display: flex;
	align-items: center;
	flex-shrink: 0;
	margin-left: 15rpx;
}

.status {
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
	margin-right: 15rpx;
	font-weight: 500;
}

.status-unachieved {
	background-color: #f0f0f0;
	color: #999;
}

.status-paid {
	background-color: #e8f5e9;
	color: #4caf50;
}

.status-pending {
	background-color: #fff8e1;
	color: #ff9800;
}

.status-claimable {
	background-color: #e3f2fd;
	color: #2196f3;
}

.claim-btn {
	background-color: #ff5722;
	color: #fff;
	padding: 10rpx 20rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	font-weight: 500;
}

.claim-btn:active {
	background-color: #e64a19;
}

.calculation {
	font-size: 24rpx;
	color: #888;
	margin-top: 10rpx;
	background-color: #f9f9f9;
	padding: 10rpx;
	border-radius: 8rpx;
}

/* 活动列表样式 */
.activity-list-box {
	margin: 20rpx;
	border-radius: 16rpx;
	background-color: #fff;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.activity-list-box .section-title {
	margin-top: 0;
	border-bottom: 1rpx solid #eee;
}

.activity-item {
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.activity-item:last-child {
	border-bottom: none;
}

.activity-item:active {
	background-color: #f9f9f9;
}

.activity-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.activity-item .activity-title {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
}

.activity-time {
	font-size: 22rpx;
	color: #999;
}

.activity-info {
	margin-bottom: 10rpx;
}

.type-info {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.perf-type,
.reward-type,
.algorithm-type {
	font-size: 22rpx;
	color: #666;
	margin-right: 15rpx;
	padding: 6rpx 12rpx;
	background-color: #f0f0f0;
	border-radius: 8rpx;
}

.participation-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.user-performance,
.user-reward {
	font-size: 24rpx;
	color: #555;
	font-weight: 500;
}

.user-reward {
	color: #ff5722;
}

.activity-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 10rpx;
}

.activity-status {
	display: flex;
	justify-content: flex-end;
}

.status-text {
	font-size: 24rpx;
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
	font-weight: 500;
}

.status-active {
	background-color: #e8f5e9;
	color: #4caf50;
}

.status-ended {
	background-color: #f0f0f0;
	color: #999;
}

.activity-actions {
	display: flex;
	align-items: center;
}

.detail-btn,
.switch-btn,
.current-btn {
	font-size: 24rpx;
	color: #333;
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
	margin-left: 10rpx;
}

.detail-btn {
	background-color: #e3f2fd;
}

.switch-btn {
	background-color: #fff;
}

.current-btn {
	background-color: #e8f5e9;
}

/* 奖励记录入口 */
.records-entry {
	margin: 30rpx 20rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.entry-btn {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	transition: background-color 0.2s;
}

.entry-btn:active {
	background-color: #f9f9f9;
}

.arrow-icon {
	width: 28rpx;
	height: 28rpx;
	opacity: 0.7;
}
</style> 