<template>
	<view>
		<view class="jc_center">
			<view class="content">
				<view class="message">
					<view class="message_txt1">金满贯乒羽贾西店</view>
					<view class="message_txt2">金满贯乒羽贾西店场地趣运动官方自营套餐费用</view>
					<view class="message_txt3">
						<text>还需支付：</text>
						<text>65.00元</text>
					</view>
				</view>
				<view class="modePayment spaceBetween">
					<view class="alignItems">
						<image src="@/static/img/withdraw-weixin.png" class="modePayment_img"></image>
						<view>微信</view>
					</view>
					<radio value="r1" color="#6b63ec" :checked="selectedState" @click="selectedState=!selectedState" /></label>
				</view>
			</view>
		</view>
		<view class="bottom">
			<uni-notice-bar show-icon text="请在1分53秒内完成付款,否则订单自动取消" />
			<view class="jc_center" style="background-color: #ffffff; margin-top: -10px;">
				<view class="bt">确认支付</view>
			</view>
		</view>
		
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedState:true,
				
			}
		},
		methods: {

		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f5f5;
	}
	.jc_center {  
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.alignItems {
		display: flex;
		align-items: center;
	}
	
	.spaceBetween {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.content{
		width: 90%;
		padding-top: 50rpx;
	}
	.message{
		background-color: #ffffff;
		padding: 20rpx 50rpx 50rpx;
		border-radius: 20rpx;
		.message_txt1{
			font-size: 30rpx;
			font-weight: bold;
		}
		.message_txt2{
			font-size: 26rpx;
			color: #797979;
			margin-top: 15rpx;
		}
		.message_txt3{
			text-align: right;
			margin-top: 50rpx;
		}
		.message_txt3 :last-child {
			color: #6c64ec;
			font-size: 30rpx;
			font-weight: bold;
		}
	}
	.modePayment{
		background-color: #ffffff;
		padding: 30rpx 50rpx;
		border-radius: 20rpx;
		margin-top: 20rpx;
		.modePayment_img{
			width: 50rpx;
			height: 50rpx;
			margin-right: 20rpx;
			font-size: 26rpx;
		}
	}
	.bottom{
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		.bt{
			padding: 20rpx 40%;
			background-color: #6b63ec;
			border-radius: 40rpx;
			color: #ffffff;
			font-size: 30rpx;
			margin: 10rpx auto ;
		}
	}
</style>