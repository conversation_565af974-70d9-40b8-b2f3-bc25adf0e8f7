<template>
<view class="container">
    <block v-if="isload">
        <!-- 顶部余额信息 -->
        <view class="mymoney" :style="{background:t('color1')}">
            <view class="balance-info">
                <view class="balance-item">
                    <view class="f1">账户余额</view>
                    <view class="f2"><text style="font-size:26rpx">￥</text>{{userinfo.money}}</view>
                </view>
                <view class="balance-item">
                    <view class="f1">进货款余额</view>
                    <view class="f2"><text style="font-size:26rpx">￥</text>{{userinfo.purchase_money || '0.00'}}</view>
                </view>
            </view>
            <view class="f3" @tap="goto" data-url="/pages/money/moneylog"><text>余额明细</text><text
                    class="iconfont iconjiantou" style="font-size:20rpx"></text></view>
        </view>

        <!-- 转账表单 -->
        <view class="content2">
            <block v-if="caninput==1">
                <view class="item3">
                    <view class="f1">￥</view>
                    <view class="f2">
                        <input type="digit" 
                               name="money" 
                               :value="money" 
                               placeholder="请输入转入金额"
                               placeholder-style="color:#999;font-size:40rpx" 
                               @input="moneyinput"
                               style="font-size:60rpx" />
                    </view>
                </view>
            </block>
            
            <view style="margin-top:40rpx;padding:0 30rpx;line-height:42rpx;" v-if="shuoming">
                <parse :content="shuoming" @navigate="navigate"></parse>
            </view>
        </view>

        <!-- 操作按钮 -->
        <view class="op">
            <view class="btn" @tap="topay" :style="{background:t('color1')}">确认转入</view>
        </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
    data() {
        return {
            opt: {},
            loading: false,
            isload: false,
            textset: {},
            userinfo: {
                money: '0.00',
                purchase_money: '0.00'
            },
            shuoming: '',
            money: '',
            caninput: 1
        };
    },
    onLoad: function(opt) {
        this.opt = app.getopts(opt);
        this.getdata();
    },
    onPullDownRefresh: function() {
        this.getdata();
    },
    methods: {
        getdata: function() {
            var that = this;
            app.loading = true;
            app.get('ApiAdminFinance/getBusinessBalance', {}, function(res) {
                app.loading = false;
                if (res.status == 0) {
                    app.alert(res.msg);
                    return;
                }
                that.isload = true;
                that.textset = app.globalData.textset;
                uni.setNavigationBarTitle({
                    title: '余额转入进货款'
                });
                that.userinfo = {
                    money: res.data.money || '0.00',
                    purchase_money: res.data.balance || '0.00'
                };
                that.loaded();
            });
        },
        moneyinput: function(e) {
            var money = e.detail.value;
            if (parseFloat(money) < 0) {
                app.error('必须大于0');
            } else {
                this.money = money;
            }
        },
        topay: function() {
            var that = this;
            if(!this.money) {
                app.error('请输入转入金额');
                return;
            }
            if(parseFloat(this.money) <= 0) {
                app.error('转入金额必须大于0');
                return;
            }
            if(parseFloat(this.money) > parseFloat(this.userinfo.money)) {
                app.error('转入金额不能大于当前余额');
                return;
            }
            
            app.loading = true;
            app.post('ApiAdminFinance/balanceTransfer', {
                amount: this.money
            }, function(res) {
                app.loading = false;
                if(res.status == 0) {
                    app.alert(res.msg);
                    return;
                }
                app.alert('转入成功');
                that.getdata();
            });
        }
    }
};
</script>

<style>
@import "../common.css";
.mymoney{width: 100%;min-height: 200rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;color: #fff;padding: 30rpx 0;}
.balance-info {
    width: 100%;
    display: flex;
    justify-content: space-around;
    padding: 0 30rpx;
    box-sizing: border-box;
}
.balance-item {
    text-align: center;
}
.mymoney .f1{font-size: 32rpx;margin-bottom: 10rpx;}
.mymoney .f2{font-size: 60rpx;font-weight: bold;margin-bottom: 10rpx;}
.mymoney .f3{font-size: 28rpx;display: flex;align-items: center;margin-top: 20rpx;}
.content2{width: 100%;padding: 30rpx;background: #fff;margin-top: 20rpx;}
.item3{display: flex;align-items: center;border-bottom: 1px solid #eee;padding: 20rpx 0;}
.item3 .f1{font-size: 60rpx;color: #333;margin-right: 20rpx;}
.item3 .f2{flex: 1;}
.op{width: 100%;padding: 30rpx;}
.btn{width: 100%;height: 80rpx;line-height: 80rpx;text-align: center;color: #fff;border-radius: 40rpx;font-size: 32rpx;}
</style>