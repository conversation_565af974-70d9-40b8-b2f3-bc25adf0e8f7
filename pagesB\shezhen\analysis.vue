<template>
	<view class="analysis-container">
		<!-- 背景装饰 - 优化小程序兼容性 -->
		<view class="bg-decoration">
			<view class="gradient-bg"></view>
			<view class="floating-elements">
				<view class="element" v-for="n in 6" :key="n" :class="'element-' + n"></view>
			</view>
		</view>

		<!-- 顶部状态指示器 -->
		<view class="status-header">
			<view class="status-steps">
				<view 
					v-for="(step, index) in analysisSteps" 
					:key="index"
					class="status-step"
					:class="{ 
						'active': index <= currentStep, 
						'completed': index < currentStep,
						'current': index === currentStep 
					}"
				>
					<view class="step-circle">
						<text class="step-icon" v-if="index < currentStep">✓</text>
						<text class="step-number" v-else>{{ index + 1 }}</text>
						<view class="step-pulse" v-if="index === currentStep"></view>
					</view>
					<text class="step-label">{{ step.name }}</text>
				</view>
			</view>
		</view>

		<!-- 内容滚动区域 - 新增独立滚动容器 -->
		<scroll-view class="main-scroll-container" scroll-y="true" enhanced="true" bounces="false">
			<!-- 图片预览区域 -->
			<view class="image-section">
				<view class="image-container">
					<image 
						:src="imageUrl" 
						class="analysis-image"
						mode="aspectFit"
						@error="onImageError"
					/>
					
					<!-- 分析扫描覆盖层 -->
					<view class="scan-overlay" v-if="isAnalyzing">
						<view class="scan-grid">
							<view 
								v-for="n in 16" 
								:key="n" 
								class="grid-cell"
								:class="{ 'scanned': scannedCells.includes(n) }"
							></view>
						</view>
						
						<view class="scan-line horizontal" :style="{ top: scanPosition.y + '%' }"></view>
						<view class="scan-line vertical" :style="{ left: scanPosition.x + '%' }"></view>
						
						<!-- 分析区域标记 -->
						<view 
							v-for="(region, index) in analysisRegions" 
							:key="index"
							class="analysis-region"
							:class="{ 'active': region.active }"
							:style="{ 
								left: region.x + '%', 
								top: region.y + '%',
								width: region.width + '%',
								height: region.height + '%'
							}"
						>
							<view class="region-label">{{ region.name }}</view>
							<view class="region-progress">
								<view class="progress-fill" :style="{ width: region.progress + '%' }"></view>
							</view>
						</view>
					</view>

					<!-- 分析完成标记 -->
					<view class="analysis-markers" v-if="!isAnalyzing && analysisComplete">
						<view 
							v-for="(marker, index) in analysisMarkers" 
							:key="index"
							class="analysis-marker"
							:style="{ left: marker.x + '%', top: marker.y + '%' }"
							:class="marker.type"
						>
							<view class="marker-dot"></view>
							<view class="marker-label">{{ marker.label }}</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 进度信息区域 -->
			<view class="progress-section">
				<view class="progress-card">
					<view class="progress-header">
						<text class="progress-title">{{ currentTask.title }}</text>
						<text class="progress-percentage">{{ formattedAnalysisProgress }}%</text>
					</view>
					
					<view class="progress-bar">
						<view class="progress-track">
							<view 
								class="progress-fill" 
								:style="{ width: formattedAnalysisProgress + '%' }"
							></view>
							<view class="progress-glow"></view>
						</view>
					</view>
					
					<text class="progress-desc">{{ currentTask.description }}</text>
				</view>
			</view>

			<!-- 分析日志区域 -->
			<view class="logs-section">
				<view class="logs-header">
					<text class="logs-title">🔍 分析日志</text>
					<view class="logs-indicator">
						<view class="indicator-dot"></view>
						<text class="indicator-text">实时监控</text>
					</view>
				</view>
				
				<view class="logs-container">
					<view 
						v-for="(log, index) in analysisLogs" 
						:key="index"
						class="log-item"
						:class="log.status"
					>
						<view class="log-icon">
							<text v-if="log.status === 'completed'">✓</text>
							<text v-else-if="log.status === 'processing'">⚡</text>
							<text v-else>⏳</text>
						</view>
						<view class="log-content">
							<text class="log-title">{{ log.title }}</text>
							<text class="log-time">{{ log.time }}</text>
						</view>
						<view class="log-status">
							<view class="status-indicator" :class="log.status"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部提示区域 -->
			<view class="tips-section">
				<view class="tips-card">
					<view class="tips-icon">⏱️</view>
					<text class="tips-text">分析预计需要 {{ estimatedTime }} 秒，请耐心等待...</text>
				</view>
			</view>

			<!-- 底部安全区域 -->
			<view class="safe-area-bottom"></view>
		</scroll-view>

		<!-- AI分析动画 - 固定定位优化 -->
		<view class="ai-animation" v-if="isAnalyzing">
			<view class="ai-brain">
				<view class="brain-core">
					<text class="brain-icon">🧠</text>
					<view class="neural-network">
						<view 
							v-for="n in 6" 
							:key="n" 
							class="neural-node"
							:class="'node-' + n"
						></view>
					</view>
				</view>
				<text class="ai-text">AI 正在分析中...</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ShezhenAnalysis',
	data() {
		return {
			// 图片信息
			imageUrl: '',
			
			// 分析状态
			isAnalyzing: true,
			analysisComplete: false,
			currentStep: 0,
			analysisProgress: 0,
			currentTaskIndex: 0,
			
			// 扫描位置
			scanPosition: { x: 0, y: 0 },
			scannedCells: [],
			
			// 预计时间
			estimatedTime: 15,
			
			// 分析步骤
			analysisSteps: [
				{ name: '图像识别', key: 'recognition' },
				{ name: '特征分析', key: 'analysis' },
				{ name: '生成报告', key: 'report' }
			],
			
			// 分析任务
			analysisTasks: [
				{
					title: '正在识别舌象特征',
					description: '使用深度学习算法识别舌质、舌苔等基本特征...',
					duration: 3000
				},
				{
					title: '分析舌质颜色',
					description: '检测舌质的颜色分布，判断气血状况...',
					duration: 2500
				},
				{
					title: '检测舌苔厚薄',
					description: '分析舌苔的厚薄程度和分布情况...',
					duration: 2000
				},
				{
					title: '识别舌体形态',
					description: '检测舌体的大小、形状和边缘特征...',
					duration: 2500
				},
				{
					title: '综合中医理论分析',
					description: '结合传统中医舌诊理论进行综合分析...',
					duration: 3000
				},
				{
					title: '生成个性化报告',
					description: '基于分析结果生成详细的健康报告...',
					duration: 2000
				}
			],
			
			// 分析区域
			analysisRegions: [
				{ name: '舌尖', x: 35, y: 20, width: 30, height: 25, progress: 0, active: false },
				{ name: '舌中', x: 30, y: 40, width: 40, height: 20, progress: 0, active: false },
				{ name: '舌根', x: 25, y: 65, width: 50, height: 20, progress: 0, active: false },
				{ name: '舌边', x: 15, y: 30, width: 15, height: 40, progress: 0, active: false },
				{ name: '舌边', x: 70, y: 30, width: 15, height: 40, progress: 0, active: false }
			],
			
			// 分析标记
			analysisMarkers: [
				{ x: 45, y: 25, type: 'normal', label: '舌尖正常' },
				{ x: 50, y: 45, type: 'attention', label: '舌苔偏厚' },
				{ x: 40, y: 65, type: 'normal', label: '舌根正常' },
				{ x: 25, y: 40, type: 'warning', label: '舌边有齿痕' },
				{ x: 75, y: 40, type: 'normal', label: '舌边正常' }
			],
			
			// 分析日志
			analysisLogs: [],
			
			// 分析记录ID
			recordId: '',
			
			// 配置信息
			configData: {},
			
			// 分析接口相关
			analysisApiCompleted: false,
			orderNo: '',
			apiType: '',
			
			// 2025-01-27 新增支付订单ID
			orderId: '',
			
			// 防重复初始化标志
			isInitialized: false,
			
			// 新增API调用状态
			isApiCalling: false,
			
			// 新增跳转状态
			isNavigatingToResult: false
		}
	},
	computed: {
		/**
		 * 当前任务信息
		 */
		currentTask() {
			return this.analysisTasks[this.currentTaskIndex] || this.analysisTasks[0];
		},
		
		/**
		 * 格式化的分析进度 - 确保显示整数
		 */
		formattedAnalysisProgress() {
			return Math.round(this.analysisProgress);
		}
	},
	onLoad(options) {
		console.log('2025-01-26 12:00:00,001-INFO-[analysis][onLoad_001] 舌诊分析页面加载完成');
		
		// 防重复初始化检查
		if (this.isInitialized) {
			console.log('2025-01-27 12:00:00,001-WARN-[analysis][onLoad_001_duplicate] 检测到重复初始化，忽略');
			return;
		}
		
		// 设置初始化标志
		this.isInitialized = true;
		
		// 获取传递的图片路径
		if (options.image_url) {
			this.imageUrl = decodeURIComponent(options.image_url);
			console.log('2025-01-26 12:00:00,002-INFO-[analysis][onLoad_002] 获取图片路径:', this.imageUrl);
		}
		
		// 获取传递的配置信息
		if (options.config) {
			try {
				this.configData = JSON.parse(decodeURIComponent(options.config));
				console.log('2025-01-26 12:00:00,003-INFO-[analysis][onLoad_003] 获取配置信息:', this.configData);
			} catch (e) {
				console.error('2025-01-26 12:00:00,004-ERROR-[analysis][onLoad_004] 配置信息解析失败:', e);
			}
		}
		
		// 获取传递的分析记录ID
		if (options.recordId) {
			this.recordId = options.recordId;
			console.log('2025-01-26 12:00:00,005-INFO-[analysis][onLoad_005] 获取分析记录ID:', this.recordId);
		}
		
	// 2025-01-27 获取传递的订单ID
		// 2025-01-27 获取传递的订单ID
		if (options.order_id) {
			this.orderId = options.order_id;
			console.log('2025-01-27 12:00:00,006-INFO-[analysis][onLoad_006] 获取订单ID:', this.orderId);
		}
		
		// 开始真实的分析流程
		this.startRealAnalysis();
	},
	methods: {
		/**
		 * 开始真实的分析流程
		 */
		startRealAnalysis() {
			console.log('2025-01-26 11:30:00,006-INFO-[analysis][startRealAnalysis_001] 开始真实的分析流程');
			
			this.isAnalyzing = true;
			this.analysisComplete = false;
			this.currentStep = 0;
			this.analysisProgress = 0;
			this.currentTaskIndex = 0;
			
			// 初始化日志
			this.initAnalysisLogs();
			
			// 开始扫描动画
			this.startScanAnimation();
			
			// 开始动画任务展示
			this.executeAnalysisTasks();
			
			// 同时调用真实的分析接口
			this.callAnalysisApi();
		},

		/**
		 * 初始化分析日志
		 */
		initAnalysisLogs() {
			this.analysisLogs = this.analysisTasks.map((task, index) => ({
				title: task.title,
				time: this.getCurrentTime(),
				status: index === 0 ? 'processing' : 'waiting'
			}));
		},

		/**
		 * 获取当前时间
		 */
		getCurrentTime() {
			const now = new Date();
			return `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
		},

		/**
		 * 开始扫描动画
		 */
		startScanAnimation() {
			const scanInterval = setInterval(() => {
				if (!this.isAnalyzing) {
					clearInterval(scanInterval);
					return;
				}
				
				// 更新扫描位置
				this.scanPosition.x = Math.random() * 100;
				this.scanPosition.y = Math.random() * 100;
				
				// 随机添加已扫描的格子
				if (this.scannedCells.length < 16) {
					const newCell = Math.floor(Math.random() * 16) + 1;
					if (!this.scannedCells.includes(newCell)) {
						this.scannedCells.push(newCell);
					}
				}
			}, 200);
		},

		/**
		 * 执行分析任务
		 */
		executeAnalysisTasks() {
			this.executeTask(0);
		},

		/**
		 * 执行单个任务
		 */
		executeTask(taskIndex) {
			if (taskIndex >= this.analysisTasks.length) {
				this.completeAnalysis();
				return;
			}

			console.log(`2025-01-26 11:30:00,${String(taskIndex + 4).padStart(3, '0')}-INFO-[analysis][executeTask_${String(taskIndex + 1).padStart(3, '0')}] 执行任务: ${this.analysisTasks[taskIndex].title}`);
			
			this.currentTaskIndex = taskIndex;
			
			// 更新当前步骤
			if (taskIndex < 2) {
				this.currentStep = 0;
			} else if (taskIndex < 4) {
				this.currentStep = 1;
			} else {
				this.currentStep = 2;
			}
			
			// 更新日志状态
			this.analysisLogs[taskIndex].status = 'processing';
			this.analysisLogs[taskIndex].time = this.getCurrentTime();
			
			// 自动滚动到最新日志
			this.$nextTick(() => {
				this.scrollToLatestLog();
			});
			
			// 激活对应的分析区域
			if (taskIndex < this.analysisRegions.length) {
				this.analysisRegions[taskIndex].active = true;
			}
			
			// 模拟任务进度
			const task = this.analysisTasks[taskIndex];
			const progressInterval = setInterval(() => {
				if (taskIndex < this.analysisRegions.length) {
					this.analysisRegions[taskIndex].progress += 2;
					if (this.analysisRegions[taskIndex].progress > 100) {
						this.analysisRegions[taskIndex].progress = 100;
					}
				}
				
				// 更新总进度 - 确保为整数
				const baseProgress = (taskIndex / this.analysisTasks.length) * 100;
				const taskProgress = (this.analysisRegions[taskIndex]?.progress || 0) / this.analysisTasks.length;
				this.analysisProgress = Math.round(Math.min(baseProgress + taskProgress, 100));
			}, 50);
			
			// 任务完成
			setTimeout(() => {
				clearInterval(progressInterval);
				
				// 完成当前任务
				this.analysisLogs[taskIndex].status = 'completed';
				this.analysisLogs[taskIndex].time = this.getCurrentTime();
				
				if (taskIndex < this.analysisRegions.length) {
					this.analysisRegions[taskIndex].progress = 100;
					this.analysisRegions[taskIndex].active = false;
				}
				
				// 执行下一个任务
				setTimeout(() => {
					this.executeTask(taskIndex + 1);
				}, 500);
				
			}, task.duration);
		},

		/**
		 * 完成分析
		 */
		completeAnalysis() {
			console.log('2025-01-26 11:30:00,024-INFO-[analysis][completeAnalysis_001] 动画分析完成');
			
			this.isAnalyzing = false;
			this.analysisComplete = true;
			this.analysisProgress = 100;
			this.currentStep = 3;
			
			// 清空扫描状态
			this.scannedCells = [];
			
			// 检查API是否完成，如果完成则跳转
			this.checkCompletionAndJump();
		},

		/**
		 * 跳转到结果页面
		 */
		navigateToResult() {
			console.log('2025-01-26 11:30:00,030-INFO-[analysis][navigateToResult_001] 跳转到完成页面');
			
			// 防重复跳转检查
			if (this.isNavigatingToResult) {
				console.log('2025-01-27 11:30:00,030-WARN-[analysis][navigateToResult_001_duplicate] 检测到重复跳转，忽略');
				return;
			}
			
			// 设置跳转状态
			this.isNavigatingToResult = true;
			
			if (!this.recordId) {
				console.error('2025-01-26 11:30:00,031-ERROR-[analysis][navigateToResult_002] 记录ID为空，无法跳转');
				this.isNavigatingToResult = false; // 重置状态
				uni.showToast({
					title: '分析结果异常，请重试',
					icon: 'none'
				});
				return;
			}
			
			const resultParams = {
				record_id: this.recordId,
				order_no: this.orderNo || '',
				api_type: this.apiType || '',
				order_id: this.orderId || ''
			};
			
			console.log('2025-01-26 11:30:00,032-INFO-[analysis][navigateToResult_003] 跳转参数:', resultParams);
			
			uni.navigateTo({
				url: `/pagesB/shezhen/complete?record_id=${resultParams.record_id}&order_no=${resultParams.order_no}&api_type=${resultParams.api_type}&order_id=${resultParams.order_id}`,
				success: () => {
					console.log('2025-01-26 11:30:00,033-INFO-[analysis][navigateToResult_004] 成功跳转到完成页面');
					// 跳转成功，可以保持isNavigatingToResult状态，防止用户快速返回再次跳转
				},
				fail: (err) => {
					console.error('2025-01-26 11:30:00,034-ERROR-[analysis][navigateToResult_005] 跳转完成页面失败:', err);
					this.isNavigatingToResult = false; // 重置状态
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
		},

		/**
		 * 图片加载错误处理
		 */
		onImageError(error) {
			console.error('2025-01-26 11:30:00,014-ERROR-[analysis][onImageError_001] 图片加载错误:', error);
			
			uni.showModal({
				title: '图片加载失败',
				content: '无法加载分析图片，请返回重新拍摄',
				showCancel: false,
				confirmText: '返回',
				success: () => {
					uni.navigateBack();
				}
			});
		},

		/**
		 * 自动滚动到最新日志
		 */
		scrollToLatestLog() {
			try {
				// 2025-01-27 16:00:00,004-INFO-[analysis][scrollToLatestLog_001] 修复小程序滚动兼容性问题
				console.log('2025-01-27 16:00:00,004-INFO-[analysis][scrollToLatestLog_001] 尝试滚动到最新日志');
				
				// 使用 setTimeout 确保 DOM 更新完成
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.logs-container').boundingClientRect((rect) => {
						if (rect && rect.height) {
							console.log('2025-01-27 16:00:00,005-INFO-[analysis][scrollToLatestLog_002] 获取日志容器信息成功，高度:', rect.height);
							
							// 使用 scrollIntoView 方法滚动到最后一个日志项
							const lastLogQuery = uni.createSelectorQuery().in(this);
							lastLogQuery.select('.log-item:last-child').boundingClientRect((lastRect) => {
								if (lastRect) {
									console.log('2025-01-27 16:00:00,006-INFO-[analysis][scrollToLatestLog_003] 找到最后一个日志项，执行滚动');
									
									// 使用 scrollIntoView 滚动到最后一个元素
									lastLogQuery.select('.log-item:last-child').scrollIntoView({
										duration: 300,
										success: () => {
											console.log('2025-01-27 16:00:00,007-INFO-[analysis][scrollToLatestLog_004] 滚动到最新日志成功');
										},
										fail: (err) => {
											console.log('2025-01-27 16:00:00,008-WARN-[analysis][scrollToLatestLog_005] 滚动到最新日志失败:', err);
										}
									});
								} else {
									console.log('2025-01-27 16:00:00,009-WARN-[analysis][scrollToLatestLog_006] 未找到最后一个日志项');
								}
							});
							lastLogQuery.exec();
						} else {
							console.log('2025-01-27 16:00:00,010-WARN-[analysis][scrollToLatestLog_007] 获取日志容器信息失败');
						}
					});
					query.exec();
				}, 100); // 延迟100ms确保DOM更新完成
				
			} catch (error) {
				console.error('2025-01-27 16:00:00,011-ERROR-[analysis][scrollToLatestLog_008] 自动滚动失败:', error);
				// 滚动失败不影响主要功能，静默处理
			}
		},

		/**
		 * 调用真实的分析接口
		 */
		callAnalysisApi() {
			console.log('2025-01-26 11:30:00,007-INFO-[analysis][callAnalysisApi_001] 调用舌诊分析接口');
			
			// 防重复API调用检查
			if (this.analysisApiCompleted || this.isApiCalling) {
				console.log('2025-01-27 11:30:00,007-WARN-[analysis][callAnalysisApi_001_duplicate] 检测到重复API调用，忽略');
				return;
			}
			
			// 设置API调用状态
			this.isApiCalling = true;
			
			const app = getApp();
			
			// 根据配置信息智能判断是否使用免费次数
			let useFree = 0;
			if (this.configData && this.configData.can_use_free) {
				useFree = 1;
			}
			
			// 2025-01-27 构建请求参数，加入订单ID
			const requestData = {
				image_url: this.imageUrl,
				use_free: useFree
			};
			
			// 如果有订单ID，添加到请求参数中
			if (this.orderId) {
				requestData.order_id = this.orderId;
				console.log('2025-01-27 11:30:00,008-INFO-[analysis][callAnalysisApi_002] 使用已创建的订单ID:', this.orderId);
			}
			
			// 2025-01-27 15:00:00,001-INFO-[analysis][callAnalysisApi_002_1] 记录调用前的详细参数
			console.log('2025-01-27 15:00:00,002-INFO-[analysis][callAnalysisApi_002_2] 请求参数详情 - ', {
				...requestData,
				configData: this.configData ? {
					is_free: this.configData.is_free,
					can_use_free: this.configData.can_use_free,
					price: this.configData.price
				} : null
			});
			
			app.post('ApiSheZhen/analyze', requestData, (response) => {
				console.log('2025-01-26 11:30:00,009-INFO-[analysis][callAnalysisApi_003] 分析接口响应:', response);
				console.log('2025-01-26 11:30:00,009-INFO-[analysis][callAnalysisApi_003_detail] 响应详情 - status:', response?.status, 'code:', response?.code, 'msg:', response?.msg, 'data:', response?.data);
				
				// 增强状态判断逻辑 - 支持多种成功状态标识
				const isSuccess = response && (
					response.status === 1 || 
					response.status === '1' || 
					response.status === 200 || 
					response.status === '200' || 
					response.code === 1 ||        
					response.code === '1' ||      
					response.code === 200 || 
					response.code === '200' ||
					(response.success === true) ||
					(response.success === 'true') ||
					(response.msg && (response.msg.includes('成功') || response.msg.includes('完成')))  // 增强消息匹配
				);
				
				if (isSuccess && response.data) {
					console.log('2025-01-26 11:30:00,010-INFO-[analysis][callAnalysisApi_004] 分析成功判断通过');
					
					// 重置API调用状态
					this.isApiCalling = false;
					
					// 分析成功，保存结果
					this.recordId = response.data.record_id || response.data.recordId || response.data.id;
					this.orderNo = response.data.order_no || response.data.orderNo || '';
					this.apiType = response.data.api_type || response.data.apiType || '';
					
					console.log('2025-01-26 11:30:00,011-INFO-[analysis][callAnalysisApi_005] 分析成功，记录ID:', this.recordId, '订单号:', this.orderNo, 'API类型:', this.apiType);
					
					if (!this.recordId) {
						console.error('2025-01-26 11:30:00,012-ERROR-[analysis][callAnalysisApi_006] 分析成功但缺少记录ID');
						this.handleAnalysisError('分析成功但缺少必要数据，请重试', response);
						return;
					}
					
					// 显示成功提示
					uni.showToast({
						title: '分析完成',
						icon: 'success',
						duration: 1500
					});
					
					// 确保动画播放完毕后再跳转
					this.analysisApiCompleted = true;
					this.checkCompletionAndJump();
					
				} else {
					console.error('2025-01-26 11:30:00,013-ERROR-[analysis][callAnalysisApi_007] 分析失败或响应格式错误');
					console.error('2025-01-26 11:30:00,013-ERROR-[analysis][callAnalysisApi_007_detail] 失败原因分析 - response:', JSON.stringify(response));
					
					// 重置API调用状态
					this.isApiCalling = false;
					
					// 2025-01-27 16:00:00,012-INFO-[analysis][callAnalysisApi_007_1] 保存返回的配置数据
					if (response && response.data && typeof response.data === 'object') {
						// 合并配置数据，确保能获取到 recharge_url 等信息
						this.configData = {
							...this.configData,
							...response.data
						};
						console.log('2025-01-27 16:00:00,013-INFO-[analysis][callAnalysisApi_007_2] 保存错误响应中的配置数据:', this.configData);
					}
					
					this.handleAnalysisError(response?.msg || response?.message || '分析失败，请重试', response);
				}
			}, (error) => {
				// 重置API调用状态
				this.isApiCalling = false;
				
				console.error('2025-01-26 11:30:00,014-ERROR-[analysis][callAnalysisApi_008] 分析接口调用失败:', error);
				
				this.handleAnalysisError('网络连接失败，请检查网络后重试', error);
			});
		},

		/**
		 * 处理分析错误
		 */
		handleAnalysisError(message, errorData) {
			console.log('2025-01-26 11:30:00,015-INFO-[analysis][handleAnalysisError_001] 处理分析错误:', message);
			
			// 停止所有动画
			this.isAnalyzing = false;
			
			// 隐藏可能存在的loading
			uni.hideLoading();
			
			// 2025-01-27 13:00:00,001-INFO-[analysis][handleAnalysisError_002] 增强错误处理，处理余额不足情况
			
			// 检查是否为余额不足错误
			if (message && (
				message.includes('余额不足') || 
				message.includes('充值') || 
				message.includes('积分不足') ||
				(errorData && errorData.code === 0 && errorData.msg && errorData.msg.includes('余额不足'))
			)) {
				console.log('2025-01-27 13:00:00,002-INFO-[analysis][handleAnalysisError_003] 检测到余额不足错误，显示支付确认弹窗');
				
				// 显示余额不足弹窗
				uni.showModal({
					title: '余额不足',
					content: '您的账户余额不足，请充值后继续使用舌诊分析功能',
					confirmText: '去充值',
					cancelText: '返回',
					success: (res) => {
						if (res.confirm) {
							// 用户选择去充值
							console.log('2025-01-27 13:00:00,003-INFO-[analysis][handleAnalysisError_004] 用户选择去充值');
							
							// 2025-01-27 16:00:00,001-INFO-[analysis][handleAnalysisError_004_1] 获取正确的充值页面路径
							let rechargeUrl = '/pagesExb/money/recharge'; // 默认路径
							
							// 从配置数据中获取充值页面路径
							if (this.configData && this.configData.recharge_url) {
								rechargeUrl = this.configData.recharge_url;
								console.log('2025-01-27 16:00:00,002-INFO-[analysis][handleAnalysisError_004_2] 使用配置中的充值路径:', rechargeUrl);
							} else {
								console.log('2025-01-27 16:00:00,003-INFO-[analysis][handleAnalysisError_004_3] 配置中无充值路径，使用默认路径:', rechargeUrl);
							}
							
							uni.navigateTo({
								url: rechargeUrl,
								success: () => {
									console.log('2025-01-27 13:00:00,004-INFO-[analysis][handleAnalysisError_005] 成功跳转到充值页面');
								},
								fail: (err) => {
									console.error('2025-01-27 13:00:00,005-ERROR-[analysis][handleAnalysisError_006] 跳转充值页面失败:', err);
									
									// 如果跳转失败，尝试跳转到tabBar页面或其他已知页面
									uni.showToast({
										title: '跳转失败，请手动前往充值页面',
										icon: 'none',
										duration: 2000
									});
									
									// 可以尝试跳转到首页或用户中心
									setTimeout(() => {
										uni.switchTab({
											url: '/pages/index/index',
											fail: () => {
												// 如果连首页都跳转失败，就返回上一页
												uni.navigateBack();
											}
										});
									}, 2000);
								}
							});
						} else {
							// 用户选择返回拍摄页面
							console.log('2025-01-27 13:00:00,006-INFO-[analysis][handleAnalysisError_007] 用户选择返回');
							uni.navigateBack({
								delta: 1  // 返回一层
							});
						}
					}
				});
				
				return; // 不继续执行后面的通用错误处理
			}
			
			// 其他类型错误的通用处理
			uni.showModal({
				title: '分析失败',
				content: message,
				showCancel: true,
				cancelText: '重试',
				confirmText: '返回',
				success: (res) => {
					if (res.confirm) {
						// 返回拍摄页面
						console.log('2025-01-26 11:30:00,016-INFO-[analysis][handleAnalysisError_002] 用户选择返回拍摄页面');
						uni.navigateBack();
					} else {
						// 重试分析
						console.log('2025-01-26 11:30:00,017-INFO-[analysis][handleAnalysisError_003] 用户选择重试分析');
						this.startRealAnalysis();
					}
				}
			});
		},

		/**
		 * 检查分析完成并跳转
		 */
		checkCompletionAndJump() {
			console.log('2025-01-26 11:30:00,018-INFO-[analysis][checkCompletionAndJump_001] 检查分析完成并跳转');
			console.log('2025-01-26 11:30:00,018-INFO-[analysis][checkCompletionAndJump_001_detail] API完成状态:', this.analysisApiCompleted, '动画完成状态:', this.analysisComplete, '记录ID:', this.recordId);
			
			if (this.analysisApiCompleted) {
				console.log('2025-01-26 11:30:00,019-INFO-[analysis][checkCompletionAndJump_002] API已完成');
				
				// 验证必要数据
				if (!this.recordId) {
					console.error('2025-01-26 11:30:00,020-ERROR-[analysis][checkCompletionAndJump_003] 缺少记录ID，无法跳转');
					this.handleAnalysisError('分析数据异常，缺少记录ID', null);
					return;
				}
				
				// 如果动画也完成了，立即跳转
				if (this.analysisComplete) {
					console.log('2025-01-26 11:30:00,021-INFO-[analysis][checkCompletionAndJump_004] 动画也完成，立即跳转');
					uni.hideLoading();
					setTimeout(() => {
						this.navigateToResult();
					}, 500); // 稍微延迟确保UI更新完成
				} else {
					console.log('2025-01-26 11:30:00,022-INFO-[analysis][checkCompletionAndJump_005] 等待动画完成');
					// API完成了但动画还在进行，显示即将完成的提示
					uni.showLoading({
						title: '即将完成...',
						mask: true
					});
				}
			} else {
				console.log('2025-01-26 11:30:00,023-INFO-[analysis][checkCompletionAndJump_006] API未完成，继续等待');
			}
		}
	}
}
</script>

<style scoped>
/* 医学主题色彩变量 - 优化小程序兼容性 */
:root {
	--medical-primary: #2E7D8A;
	--medical-secondary: #4A9B8E;
	--medical-accent: #6BB6B0;
	--medical-success: #28A745;
	--medical-warning: #FFC107;
	--medical-danger: #DC3545;
	--medical-light: #F8F9FA;
	--medical-dark: #1A4B52;
}

/* 主容器 - 优化小程序滚动兼容性 */
.analysis-container {
	width: 100%;
	height: 100vh;
	background: linear-gradient(135deg, #1A4B52 0%, #2E7D8A 50%, #4A9B8E 100%);
	position: relative;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

/* 背景装饰 - 优化性能和兼容性 */
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 0;
	pointer-events: none;
}

.gradient-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: 
		radial-gradient(circle at 20% 30%, rgba(46, 125, 138, 0.2) 0%, transparent 40%),
		radial-gradient(circle at 80% 70%, rgba(74, 155, 142, 0.15) 0%, transparent 40%),
		radial-gradient(circle at 50% 50%, rgba(107, 182, 176, 0.1) 0%, transparent 40%);
}

.floating-elements {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.element {
	position: absolute;
	background: rgba(107, 182, 176, 0.08);
	border-radius: 50%;
	animation: medical-float 25s infinite linear;
	will-change: transform;
}

.element-1 { width: 60rpx; height: 60rpx; top: 10%; left: 10%; animation-delay: 0s; }
.element-2 { width: 40rpx; height: 40rpx; top: 20%; right: 15%; animation-delay: 4s; }
.element-3 { width: 80rpx; height: 80rpx; top: 60%; left: 5%; animation-delay: 8s; }
.element-4 { width: 50rpx; height: 50rpx; top: 80%; right: 20%; animation-delay: 12s; }
.element-5 { width: 70rpx; height: 70rpx; top: 40%; left: 80%; animation-delay: 16s; }
.element-6 { width: 35rpx; height: 35rpx; top: 70%; left: 60%; animation-delay: 20s; }

@keyframes medical-float {
	0% { transform: translateY(0px) rotate(0deg); opacity: 0.2; }
	25% { transform: translateY(-15px) rotate(90deg); opacity: 0.4; }
	50% { transform: translateY(-8px) rotate(180deg); opacity: 0.3; }
	75% { transform: translateY(-20px) rotate(270deg); opacity: 0.5; }
	100% { transform: translateY(0px) rotate(360deg); opacity: 0.2; }
}

/* 顶部状态指示器 - 固定定位优化 */
.status-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
	padding: 20rpx 30rpx;
	background: rgba(26, 75, 82, 0.95);
	backdrop-filter: blur(10rpx);
	border-bottom: 1rpx solid rgba(107, 182, 176, 0.3);
	flex-shrink: 0;
}

.status-steps {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(248, 249, 250, 0.9);
	border-radius: 20rpx;
	padding: 25rpx 15rpx;
	border: 1rpx solid rgba(46, 125, 138, 0.2);
	box-shadow: 0 4rpx 20rpx rgba(26, 75, 82, 0.1);
}

.status-step {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	position: relative;
}

.status-step:not(:last-child)::after {
	content: '';
	position: absolute;
	top: 25rpx;
	right: -50%;
	width: 100%;
	height: 2rpx;
	background: linear-gradient(90deg, rgba(46, 125, 138, 0.3), rgba(74, 155, 142, 0.3));
	z-index: -1;
}

.status-step.active:not(:last-child)::after {
	background: linear-gradient(90deg, #2E7D8A, #4A9B8E);
}

.step-circle {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background: rgba(46, 125, 138, 0.1);
	border: 2rpx solid rgba(46, 125, 138, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8rpx;
	position: relative;
	transition: all 0.3s ease;
	will-change: transform;
}

.status-step.completed .step-circle {
	background: linear-gradient(135deg, #28A745, #4A9B8E);
	border-color: #28A745;
}

.status-step.current .step-circle {
	background: linear-gradient(135deg, #2E7D8A, #4A9B8E);
	border-color: #2E7D8A;
	animation: medical-pulse 2s infinite;
}

.step-icon, .step-number {
	font-size: 22rpx;
	font-weight: bold;
	color: white;
}

.step-pulse {
	position: absolute;
	top: -8rpx;
	left: -8rpx;
	right: -8rpx;
	bottom: -8rpx;
	border: 1rpx solid #2E7D8A;
	border-radius: 50%;
	animation: medical-pulse-ring 2s infinite;
}

.step-label {
	font-size: 20rpx;
	color: #1A4B52;
	text-align: center;
	font-weight: 500;
}

.status-step.active .step-label {
	color: #2E7D8A;
	font-weight: bold;
}

@keyframes medical-pulse {
	0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(46, 125, 138, 0.7); }
	50% { transform: scale(1.03); box-shadow: 0 0 0 8rpx rgba(46, 125, 138, 0); }
}

@keyframes medical-pulse-ring {
	0% { transform: scale(1); opacity: 1; }
	100% { transform: scale(1.2); opacity: 0; }
}

/* 主滚动容器 - 优化滚动性能和体验 */
.main-scroll-container {
	position: absolute;
	top: 140rpx; /* 为顶部状态指示器留出空间 */
	left: 0;
	right: 0;
	bottom: 0;
	height: calc(100vh - 140rpx); /* 固定高度，确保滚动生效 */
	z-index: 1;
	background: transparent;
	overflow-x: hidden;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
	scroll-behavior: smooth; /* 平滑滚动 */
}

/* 图片预览区域 - 优化小程序兼容性 */
.image-section {
	margin: 20rpx 30rpx 30rpx;
}

.image-container {
	position: relative;
	border-radius: 20rpx;
	overflow: hidden;
	background: rgba(248, 249, 250, 0.9);
	border: 1rpx solid rgba(46, 125, 138, 0.2);
	box-shadow: 0 6rpx 25rpx rgba(26, 75, 82, 0.1);
}

.analysis-image {
	width: 100%;
	height: 400rpx;
	background: #F8F9FA;
	display: block;
}

/* 扫描覆盖层 - 优化动画性能 */
.scan-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(26, 75, 82, 0.3);
}

.scan-grid {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	grid-template-rows: repeat(4, 1fr);
	gap: 1rpx;
}

.grid-cell {
	background: rgba(248, 249, 250, 0.03);
	transition: all 0.4s ease;
}

.grid-cell.scanned {
	background: rgba(46, 125, 138, 0.25);
	box-shadow: inset 0 0 8rpx rgba(107, 182, 176, 0.4);
}

.scan-line {
	position: absolute;
	background: linear-gradient(90deg, transparent, #2E7D8A, transparent);
	animation: medical-scan 2.5s infinite;
	will-change: transform;
}

.scan-line.horizontal {
	left: 0;
	right: 0;
	height: 2rpx;
}

.scan-line.vertical {
	top: 0;
	bottom: 0;
	width: 2rpx;
}

@keyframes medical-scan {
	0%, 100% { opacity: 0; }
	50% { opacity: 0.8; }
}

/* 分析区域 - 优化性能 */
.analysis-region {
	position: absolute;
	border: 2rpx solid rgba(46, 125, 138, 0.5);
	border-radius: 8rpx;
	background: rgba(107, 182, 176, 0.12);
	transition: all 0.4s ease;
	will-change: transform;
}

.analysis-region.active {
	border-color: #2E7D8A;
	background: rgba(46, 125, 138, 0.2);
	animation: medical-region-pulse 2s infinite;
}

.region-label {
	position: absolute;
	top: -25rpx;
	left: 50%;
	transform: translateX(-50%);
	font-size: 18rpx;
	color: #2E7D8A;
	background: rgba(248, 249, 250, 0.9);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	white-space: nowrap;
	font-weight: 600;
	border: 1rpx solid rgba(46, 125, 138, 0.3);
}

.region-progress {
	position: absolute;
	bottom: 4rpx;
	left: 4rpx;
	right: 4rpx;
	height: 5rpx;
	background: rgba(248, 249, 250, 0.3);
	border-radius: 3rpx;
	overflow: hidden;
}

.region-progress .progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #2E7D8A, #4A9B8E);
	border-radius: 3rpx;
	transition: width 0.3s ease;
}

@keyframes medical-region-pulse {
	0%, 100% { box-shadow: 0 0 10rpx rgba(46, 125, 138, 0.3); }
	50% { box-shadow: 0 0 18rpx rgba(46, 125, 138, 0.6); }
}

/* 分析标记 - 优化显示效果 */
.analysis-markers {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.analysis-marker {
	position: absolute;
	transform: translate(-50%, -50%);
}

.marker-dot {
	width: 18rpx;
	height: 18rpx;
	border-radius: 50%;
	margin: 0 auto 4rpx;
	animation: medical-marker-pulse 2s infinite;
	will-change: transform;
}

.analysis-marker.normal .marker-dot {
	background: #28A745;
	box-shadow: 0 0 8rpx rgba(40, 167, 69, 0.4);
}

.analysis-marker.attention .marker-dot {
	background: #FFC107;
	box-shadow: 0 0 8rpx rgba(255, 193, 7, 0.4);
}

.analysis-marker.warning .marker-dot {
	background: #DC3545;
	box-shadow: 0 0 8rpx rgba(220, 53, 69, 0.4);
}

.marker-label {
	font-size: 16rpx;
	color: #1A4B52;
	background: rgba(248, 249, 250, 0.9);
	padding: 2rpx 6rpx;
	border-radius: 6rpx;
	white-space: nowrap;
	text-align: center;
	font-weight: 500;
	border: 1rpx solid rgba(46, 125, 138, 0.2);
}

@keyframes medical-marker-pulse {
	0%, 100% { transform: scale(1); opacity: 1; }
	50% { transform: scale(1.15); opacity: 0.8; }
}

/* 进度信息区域 - 优化布局 */
.progress-section {
	margin: 30rpx;
}

.progress-card {
	background: rgba(248, 249, 250, 0.9);
	border-radius: 18rpx;
	padding: 25rpx;
	border: 1rpx solid rgba(46, 125, 138, 0.2);
	box-shadow: 0 4rpx 20rpx rgba(26, 75, 82, 0.1);
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 18rpx;
}

.progress-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #1A4B52;
}

.progress-percentage {
	font-size: 30rpx;
	font-weight: bold;
	color: #2E7D8A;
}

.progress-bar {
	margin-bottom: 12rpx;
}

.progress-track {
	height: 10rpx;
	background: rgba(46, 125, 138, 0.1);
	border-radius: 5rpx;
	overflow: hidden;
	position: relative;
}

.progress-track .progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #2E7D8A, #4A9B8E);
	border-radius: 5rpx;
	transition: width 0.3s ease;
	position: relative;
}

.progress-glow {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(90deg, transparent, rgba(46, 125, 138, 0.5), transparent);
	animation: medical-progress-glow 2s infinite;
	will-change: transform;
}

.progress-desc {
	font-size: 22rpx;
	color: rgba(26, 75, 82, 0.7);
	line-height: 1.4;
}

@keyframes medical-progress-glow {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

/* 分析日志区域 - 增强滚动体验 */
.logs-section {
	margin: 30rpx;
	min-height: 400rpx; /* 确保有足够的内容高度触发滚动 */
}

.logs-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 18rpx;
}

.logs-title {
	font-size: 26rpx;
	font-weight: bold;
	color: white;
}

.logs-indicator {
	display: flex;
	align-items: center;
}

.indicator-dot {
	width: 10rpx;
	height: 10rpx;
	background: #2E7D8A;
	border-radius: 50%;
	margin-right: 6rpx;
	animation: medical-indicator-blink 2s infinite;
}

.indicator-text {
	font-size: 20rpx;
	color: #6BB6B0;
}

@keyframes medical-indicator-blink {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.3; }
}

.logs-container {
	background: rgba(248, 249, 250, 0.9);
	border-radius: 15rpx;
	border: 1rpx solid rgba(46, 125, 138, 0.2);
	box-shadow: 0 4rpx 20rpx rgba(26, 75, 82, 0.1);
	overflow: hidden;
	max-height: 600rpx; /* 限制最大高度，内部可滚动 */
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
}

.log-item {
	display: flex;
	align-items: center;
	padding: 18rpx;
	border-bottom: 1rpx solid rgba(46, 125, 138, 0.1);
	transition: all 0.3s ease;
}

.log-item:last-child {
	border-bottom: none;
}

.log-item.processing {
	background: rgba(46, 125, 138, 0.08);
}

.log-item.completed {
	background: rgba(40, 167, 69, 0.08);
}

.log-icon {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12rpx;
	font-size: 18rpx;
}

.log-item.processing .log-icon {
	background: rgba(46, 125, 138, 0.15);
	color: #2E7D8A;
	animation: medical-log-processing 2s infinite;
}

.log-item.completed .log-icon {
	background: rgba(40, 167, 69, 0.15);
	color: #28A745;
}

.log-item.waiting .log-icon {
	background: rgba(26, 75, 82, 0.08);
	color: rgba(26, 75, 82, 0.5);
}

.log-content {
	flex: 1;
}

.log-title {
	font-size: 22rpx;
	color: #1A4B52;
	display: block;
	margin-bottom: 4rpx;
	font-weight: 500;
}

.log-time {
	font-size: 18rpx;
	color: rgba(26, 75, 82, 0.6);
}

.log-status {
	width: 18rpx;
	height: 18rpx;
}

.status-indicator {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.status-indicator.processing {
	background: #2E7D8A;
	animation: medical-status-pulse 2s infinite;
}

.status-indicator.completed {
	background: #28A745;
}

.status-indicator.waiting {
	background: rgba(26, 75, 82, 0.3);
}

@keyframes medical-log-processing {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.08); }
}

@keyframes medical-status-pulse {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.5; }
}

/* 底部提示区域 - 优化样式，更加显眼 */
.tips-section {
	margin: 30rpx;
	position: relative;
}

.tips-card {
	background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.15));
	border-radius: 20rpx;
	padding: 25rpx 20rpx;
	border: 2rpx solid rgba(255, 193, 7, 0.5);
	display: flex;
	align-items: center;
	box-shadow: 
		0 8rpx 30rpx rgba(255, 193, 7, 0.2),
		0 0 0 1rpx rgba(255, 193, 7, 0.1) inset;
	position: relative;
	overflow: hidden;
	animation: tips-glow 3s ease-in-out infinite;
}

.tips-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	animation: tips-shine 4s infinite;
}

.tips-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
	animation: tips-bounce 2s ease-in-out infinite;
	filter: drop-shadow(0 2rpx 4rpx rgba(255, 193, 7, 0.3));
}

.tips-text {
	font-size: 26rpx;
	color: #1A4B52;
	line-height: 1.5;
	flex: 1;
	font-weight: 600;
	text-shadow: 0 1rpx 2rpx rgba(255, 193, 7, 0.2);
}

@keyframes tips-glow {
	0%, 100% { 
		box-shadow: 
			0 8rpx 30rpx rgba(255, 193, 7, 0.2),
			0 0 0 1rpx rgba(255, 193, 7, 0.1) inset;
	}
	50% { 
		box-shadow: 
			0 12rpx 40rpx rgba(255, 193, 7, 0.3),
			0 0 0 2rpx rgba(255, 193, 7, 0.2) inset;
	}
}

@keyframes tips-shine {
	0% { left: -100%; }
	100% { left: 100%; }
}

@keyframes tips-bounce {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-3rpx); }
}

/* 底部安全区域 */
.safe-area-bottom {
	height: 60rpx;
}

/* AI分析动画 - 优化固定定位 */
.ai-animation {
	position: fixed;
	bottom: 120rpx;
	right: 30rpx;
	z-index: 100;
}

.ai-brain {
	display: flex;
	flex-direction: column;
	align-items: center;
	background: rgba(248, 249, 250, 0.95);
	border-radius: 18rpx;
	padding: 20rpx;
	border: 1rpx solid rgba(46, 125, 138, 0.3);
	box-shadow: 0 8rpx 30rpx rgba(26, 75, 82, 0.15);
}

.brain-core {
	position: relative;
	margin-bottom: 12rpx;
}

.brain-icon {
	font-size: 36rpx;
	animation: medical-brain-think 3s infinite;
	will-change: transform;
}

.neural-network {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 70rpx;
	height: 70rpx;
}

.neural-node {
	position: absolute;
	width: 6rpx;
	height: 6rpx;
	background: #2E7D8A;
	border-radius: 50%;
	animation: medical-neural-pulse 2s infinite;
	will-change: transform, opacity;
}

.node-1 { top: 0; left: 50%; transform: translateX(-50%); animation-delay: 0s; }
.node-2 { top: 25%; right: 0; animation-delay: 0.3s; }
.node-3 { bottom: 25%; right: 0; animation-delay: 0.6s; }
.node-4 { bottom: 0; left: 50%; transform: translateX(-50%); animation-delay: 0.9s; }
.node-5 { bottom: 25%; left: 0; animation-delay: 1.2s; }
.node-6 { top: 25%; left: 0; animation-delay: 1.5s; }

.ai-text {
	font-size: 20rpx;
	color: #2E7D8A;
	text-align: center;
	animation: medical-text-glow 2s infinite;
	font-weight: 600;
}

@keyframes medical-brain-think {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.08); }
}

@keyframes medical-neural-pulse {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.4); }
}

@keyframes medical-text-glow {
	0%, 100% { text-shadow: 0 0 8rpx rgba(46, 125, 138, 0.4); }
	50% { text-shadow: 0 0 15rpx rgba(46, 125, 138, 0.8); }
}

/* 小程序特殊优化 */
@media screen and (max-width: 750px) {
	.floating-elements .element {
		animation-duration: 30s; /* 减慢动画速度 */
	}
	
	.progress-glow {
		animation-duration: 3s; /* 减慢光效动画 */
	}
	
	.scan-line {
		animation-duration: 3s; /* 减慢扫描动画 */
	}
}

/* 减少不必要的动画 - 提升性能 */
@media (prefers-reduced-motion: reduce) {
	.analysis-container * {
		animation-duration: 0.01ms !important;
		animation-iteration-count: 1 !important;
		transition-duration: 0.01ms !important;
	}
}
</style>