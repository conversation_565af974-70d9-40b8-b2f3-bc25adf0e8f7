<template>
<view>
	<block v-if="isload">
		<!-- 顶部横幅 -->
		<view class="banner" :style="{background:'linear-gradient(180deg,'+(getColor('color1') || '#4CAF50')+' 0%,rgba('+(getColorRgb('color1rgb') || '76,175,80')+',0) 100%)'}">
			<image :src="(agentinfo && agentinfo.headimg) || '/static/img/default-avatar.png'" background-size="cover"/>
			<view class="info">
				<text class="nickname">{{(agentinfo && agentinfo.name) || '加载中...'}}（代理ID：{{(agentinfo && agentinfo.id) || '--'}}）</text>
				<text>联系电话：{{(agentinfo && agentinfo.tel) || '--'}}</text>
				<text>代理级别：{{(agentinfo && agentinfo.agent_level_name) || '--'}}</text>
				<text>覆盖区域：{{(agentinfo && agentinfo.coverage_area_names) || '暂无'}}</text>
			</view>
		</view>
		
		<view class="contentdata">
			<!-- 我的收益 -->
			<view class="order">
				<view class="head">
					<text class="f1">我的收益</text>
					<view class="f2" @tap="goWithdraw">
						<text>申请提现</text>
						<image src="/static/img/arrowright.png"></image>
					</view>
				</view>
				<view class="content">
					<view class="item">
						<text class="t1">￥{{(agentinfo && agentinfo.money) || '0.00'}}</text>
						<text class="t3">账户余额</text>
					</view>
					<view class="item">
						<text class="t1">￥{{(agentinfo && agentinfo.total_income) || '0.00'}}</text>
						<text class="t3">累计收入</text>
					</view>
					<view class="item">
						<text class="t1">{{(agentinfo && agentinfo.commission_rate) || '0'}}%</text>
						<text class="t3">佣金比例</text>
					</view>
				</view>
				<!-- 操作按钮 -->
				<!-- <view class="action-buttons">
					<view class="btn-group">
						<view class="action-btn convert-btn" @tap="goConvert" :style="{background:'linear-gradient(90deg,'+getColor('color1')+' 0%,rgba('+getColorRgb('color1rgb')+',0.8) 100%)', color:'#fff'}">
							<image src="/static/img/icon-convert.png"></image>
							<text>转佣金</text>
						</view>
					</view>
				</view> -->
			</view>

			<!-- 业务数据 -->
			<view class="order">
				<view class="head">
					<text class="f1">业务数据</text>
					<view class="f2" @tap="goto" data-url="statistics">
						<text>查看详情</text>
						<image src="/static/img/arrowright.png"></image>
					</view>
				</view>
				<view class="content">
					<view class="item">
						<text class="t1">{{(statistics && statistics.today_orders) || '0'}}</text>
						<text class="t3">今日订单数</text>
					</view>
					<view class="item">
						<text class="t1">￥{{(statistics && statistics.today_amount) || '0.00'}}</text>
						<text class="t3">今日业绩</text>
					</view>
					<view class="item">
						<text class="t1">￥{{(statistics && statistics.today_commission) || '0.00'}}</text>
						<text class="t3">今日佣金</text>
					</view>
				</view>
				<view class="content">
					<view class="item">
						<text class="t1">{{(statistics && statistics.month_orders) || '0'}}</text>
						<text class="t3">本月订单数</text>
					</view>
					<view class="item">
						<text class="t1">￥{{(statistics && statistics.month_amount) || '0.00'}}</text>
						<text class="t3">本月业绩</text>
					</view>
					<view class="item">
						<text class="t1">￥{{(statistics && statistics.month_commission) || '0.00'}}</text>
						<text class="t3">本月佣金</text>
					</view>
				</view>
			</view>

			<!-- 覆盖区域 -->
			<view class="order" v-if="agentinfo && agentinfo.coverage_areas && agentinfo.coverage_areas.length > 0">
				<view class="head">
					<text class="f1">覆盖区域</text>
					<view class="f2" @tap="goto" data-url="coverage">
						<text>管理区域</text>
						<image src="/static/img/arrowright.png"></image>
					</view>
				</view>
				<view class="coverage-areas">
					<view class="area-item" v-for="(area, index) in agentinfo.coverage_areas" :key="index">
						<text class="area-name">{{area.name}}</text>
						<text class="area-type">{{area.type}}</text>
					</view>
				</view>
			</view>

			<!-- 功能菜单 -->
			<view class="list">
				<view class="item" @tap="goto" data-url="income">
					<view class="f1">
						<image src="/static/img/icon-income.png"></image>
					</view>
					<view class="f2">收益明细</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				
				<view class="item" @tap="goto" data-url="withdrawlog?st=1">
					<view class="f1">
						<image src="/static/img/icon-withdraw.png"></image>
					</view>
					<view class="f2">提现记录</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				
				<view class="item" @tap="goConvert">
					<view class="f1">
						<image src="/static/img/icon-convert.png"></image>
					</view>
					<view class="f2">转佣金</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				
				<view class="item" @tap="goto" data-url="orders">
					<view class="f1">
						<image src="/static/img/icon-order.png"></image>
					</view>
					<view class="f2">订单统计</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				
				<view class="item" @tap="goto" data-url="merchant">
					<view class="f1">
						<image src="/static/img/icon-merchant.png"></image>
					</view>
					<view class="f2">商户管理</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				
				<!-- <view class="item" @tap="goto" data-url="settings">
					<view class="f1">
						<image src="/static/img/icon-setting.png"></image>
					</view>
					<view class="f2">代理设置</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				
				<view class="item" @tap="goto" data-url="help">
					<view class="f1">
						<image src="/static/img/icon-help.png"></image>
					</view>
					<view class="f2">帮助中心</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view> -->
			</view>
		</view>
		
		<view style="width:100%;height:20rpx"></view>
	</block>
	
	<!-- 加载中组件 -->
	<loading v-if="loading"></loading>
	
	<!-- 底部导航 -->
	<dp-tabbar :opt="opt"></dp-tabbar>
	
	<!-- 消息提示 -->
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {}, // 页面参数
			loading: false, // 加载状态
			isload: false, // 数据是否加载完成
			pre_url: app.globalData.pre_url, // 图片前缀
			agentinfo: {}, // 代理信息
			statistics: {}, // 统计数据
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},

	onPullDownRefresh: function () {
		this.getdata();
	},

	methods: {
		// 安全获取颜色值
		getColor: function(colorKey) {
			try {
				if (typeof this.t === 'function') {
					return this.t(colorKey);
				}
				return null;
			} catch (e) {
				console.log('获取颜色失败:', e);
				return null;
			}
		},
		
		// 安全获取RGB颜色值
		getColorRgb: function(colorKey) {
			try {
				if (typeof this.t === 'function') {
					return this.t(colorKey);
				}
				return null;
			} catch (e) {
				console.log('获取RGB颜色失败:', e);
				return null;
			}
		},
		
		// 获取代理数据
		getdata: function () {
			var that = this;
			that.loading = true;
			
			// 调用代理信息接口
			app.get('ApiCityAgent/getAgentInfo', {}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 0) {
					app.error(res.msg);
					// 如果未绑定代理，跳转到绑定页面
					if (res.code == 'NOT_AGENT') {
						app.goto('/pagesExt/cityagent/bind');
					}
					return;
				}
				
				// 设置导航标题
				uni.setNavigationBarTitle({
					title: '代理中心'
				});
				
				// 更新数据
				that.agentinfo = res.agentinfo;
				that.statistics = res.statistics;
				that.loaded();
			});
		},
		
		// 页面跳转
		goto: function (e) {
			var url = e.currentTarget.dataset.url;
			if (url) {
				app.goto('/pagesExt/cityagent/' + url);
			}
		},
		
		// 提现
		towithdraw: function () {
			app.goto('/pagesExt/cityagent/withdraw');
		},
		
		// 申请提现
		goWithdraw: function() {
			app.goto('/pagesExt/cityagent/withdraw');
		},
		
		// 余额转佣金
		goConvert: function() {
			var that = this;
			
			// 检查余额是否足够
			if (!that.agentinfo || !that.agentinfo.money || parseFloat(that.agentinfo.money) <= 0) {
				app.error('账户余额不足');
				return;
			}
			
			// 弹出确认对话框
			uni.showModal({
				title: '余额转换佣金',
				content: '是否将账户余额转换为佣金？转换后可提现到微信/支付宝',
				success: function(res) {
					if (res.confirm) {
						that.convertBalance();
					}
				}
			});
		},
		
		// 执行余额转换
		convertBalance: function() {
			var that = this;
			
			uni.showLoading({
				title: '转换中...'
			});
			
			// 暂时显示功能开发中
			setTimeout(function() {
				uni.hideLoading();
				uni.showModal({
					title: '功能提示', 
					content: '余额转换功能正在开发中，敬请期待',
					showCancel: false
				});
			}, 1000);
		},
		
		// 数据加载完成
		loaded: function () {
			this.isload = true;
		}
	}
};
</script>

<style>
/* 顶部横幅样式 */
.banner {
	display: flex;
	width: 100%;
	height: 560rpx;
	padding: 40rpx 32rpx;
	color: #fff;
	position: relative;
}

.banner image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.banner .info {
	display: flex;
	flex: auto;
	flex-direction: column;
	padding-top: 10rpx;
}

.banner .info .nickname {
	font-size: 32rpx;
	font-weight: bold;
	padding-bottom: 12rpx;
}

.banner .info text {
	font-size: 28rpx;
	margin-bottom: 8rpx;
}

/* 内容区域样式 */
.contentdata {
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 0 30rpx;
	margin-top: -380rpx;
	position: relative;
	margin-bottom: 20rpx;
}

/* 订单卡片样式 */
.order {
	width: 100%;
	background: #fff;
	padding: 0 20rpx;
	margin-top: 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.order .head {
	display: flex;
	align-items: center;
	width: 100%;
	padding: 20rpx 0;
	border-bottom: 1px solid #eee;
}

.order .head .f1 {
	flex: auto;
	color: #333;
	font-size: 32rpx;
	font-weight: bold;
}

.order .head .f2 {
	display: flex;
	align-items: center;
	color: #FE2B2E;
	font-size: 28rpx;
	padding: 10rpx 0;
}

.order .head .f2 image {
	width: 30rpx;
	height: 30rpx;
	margin-left: 10rpx;
}

.order .content {
	display: flex;
	width: 100%;
	padding: 20rpx 0;
	align-items: center;
	font-size: 24rpx;
}

.order .content .item {
	padding: 10rpx 0;
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
}

.order .content .item .t1 {
	color: #FE2B2E;
	font-size: 36rpx;
	font-weight: bold;
}

.order .content .item .t3 {
	padding-top: 10rpx;
	color: #666;
	font-size: 24rpx;
}

/* 操作按钮样式 */
.action-buttons {
	padding: 20rpx 0 30rpx 0;
}

.btn-group {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	position: relative;
	cursor: pointer;
	transition: all 0.3s ease;
}

.action-btn image {
	width: 32rpx;
	height: 32rpx;
	margin-right: 10rpx;
}

.action-btn text {
	color: inherit;
}

.withdraw-btn {
	color: #fff;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.withdraw-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.convert-btn {
	background: #fff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.convert-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 覆盖区域样式 */
.coverage-areas {
	padding: 20rpx 0;
}

.area-item {
	display: inline-block;
	margin: 5rpx 10rpx 5rpx 0;
	padding: 10rpx 20rpx;
	background: #f8f8f8;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.area-name {
	color: #333;
	margin-right: 10rpx;
}

.area-type {
	color: #999;
	font-size: 20rpx;
}

/* 功能列表样式 */
.list {
	width: 100%;
	background: #fff;
	margin-top: 20rpx;
	padding: 0 20rpx;
	font-size: 30rpx;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.list .item {
	height: 100rpx;
	display: flex;
	align-items: center;
	border-bottom: 1px solid #eee;
}

.list .item:last-child {
	border-bottom: 0;
}

.list .f1 {
	width: 50rpx;
	height: 50rpx;
	margin-right: 20rpx;
	display: flex;
	align-items: center;
}

.list .f1 image {
	width: 40rpx;
	height: 40rpx;
}

.list .f2 {
	color: #222;
	flex: 1;
}

.list .f3 {
	color: #FC5648;
	text-align: right;
	margin-right: 20rpx;
}

.list .f4 {
	width: 24rpx;
	height: 24rpx;
}
</style> 