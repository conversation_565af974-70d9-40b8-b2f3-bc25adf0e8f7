# 股权池功能开发文档

## 1. 功能概述

股权池功能是一个让用户参与平台股权分配的系统。用户可以通过持有股权来分享平台的收益。股权池功能包括以下几个主要部分：

- 股权池概览：显示股权池总金额、股权总数量、单位股权价值等信息
- 用户股权详情：显示用户持有的股权数量和价值
- 股权排行榜：展示用户持有股权的排名情况
- 股权价值走势图：显示历史股权价值的变化趋势

## 2. 页面结构

本功能共包含三个页面：

1. **股权池概览页** (pagesExt/equity_pool/index.vue)
   - 显示股权池基本信息
   - 展示用户持有的股权数据
   - 显示股权价值走势图
   - 展示股权持有排行榜（前5名）

2. **我的股权详情页** (pagesExt/equity_pool/myEquity.vue)
   - 展示当前股权单价
   - 显示用户持有的股权数量和价值
   - 股权相关说明

3. **股权排行榜页** (pagesExt/equity_pool/ranking.vue)
   - 展示当前股权单价和用户排名
   - 显示股权持有排行榜详情
   - 支持分页加载更多数据

## 3. API接口

本功能使用以下API接口：

1. `/ApiEquityPool/index` - 获取股权池概览数据
2. `/ApiEquityPool/myEquity` - 获取用户股权详情
3. `/ApiEquityPool/ranking` - 获取股权排行榜数据

## 4. 代码结构

### 4.1 页面配置 (pages.json)

在pages.json的pagesExt子包中添加了三个页面：

```json
{
  "path": "equity_pool/index",
  "style": {
    "navigationBarTitleText": "股权池",
    "enablePullDownRefresh": true,
    "titleNView": {
      "buttons": [{
        "type": "home"
      }]
    }
  }
},
{
  "path": "equity_pool/myEquity",
  "style": {
    "navigationBarTitleText": "我的股权",
    "enablePullDownRefresh": true,
    "titleNView": {
      "buttons": [{
        "type": "home"
      }]
    }
  }
},
{
  "path": "equity_pool/ranking",
  "style": {
    "navigationBarTitleText": "股权排行榜",
    "enablePullDownRefresh": true,
    "titleNView": {
      "buttons": [{
        "type": "home"
      }]
    }
  }
}
```

### 4.2 通用组件和样式

股权池页面使用了以下通用组件：

- `loading`: 加载状态组件
- `popmsg`: 消息提示组件
- `dp-tabbar`: 底部导航栏组件
- `echarts`: 图表组件，用于显示股权价值走势

样式中使用了项目的主题色系统：
- 通过 `t('color1')` 和 `t('color1rgb')` 获取主题色值
- 使用与项目一致的布局和风格

## 5. 注意事项

1. 股权池功能需要用户登录才能使用，所有API接口都需要用户登录状态
2. 图表展示需要依赖echarts组件库
3. 分页加载使用pagenum参数，从1开始计数
4. 需要处理API返回的错误信息，包括股权池未初始化等特殊情况

## 6. 未来可能的扩展

1. 添加股权交易功能
2. 增加股权获取的更多渠道
3. 开发股权收益分配记录查询功能
4. 增加更详细的股权分析和预测功能

## 7. 关联功能

股权池功能可以与以下功能关联：
- 用户中心：在用户中心页面添加股权入口
- 商城系统：可通过消费获得股权奖励
- 推广系统：通过推广获得股权奖励 