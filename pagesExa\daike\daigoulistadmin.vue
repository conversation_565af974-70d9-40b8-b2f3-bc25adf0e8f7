<template>
<view class="container">
	
	<block v-if="isload">
		<view style="width:100%;height:80rpx;text-align: center;line-height: 80rpx;">
			<view class="search-text" style="height: 80rpx;line-height: 80rpx;font-size: 35rpx;">我发起的代购单</view>
		</view> 
		<dd-tab style="margin-top: 75rpx;" :itemdata="['全部','待付款','待发货','待收货','已完成']" :itemst="['all','0','1','2','3']" :st="st" :isfixed="true" @changetab="changetab"></dd-tab>
		<!-- #ifndef H5 || APP-PLUS -->
		<!-- <view class="topsearch flex-y-center">
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
			</view>
		</view> -->
		<!--  #endif -->
		<view class="order-content" style="margin-top: 85rpx;">
			<block v-for="(item, index) in datalist" :key="index">
				<view class="order-box" @tap="goto" :data-url="'/shopPackage/shop/daikebuy?id=' + item.id">
					<view class="head">
						<!-- <view class="f1" v-if="item.bid!=0" @tap.stop="goto" :data-url="'/pagesExt/business/index?id=' + item.bid"><image src="/static/img/ico-shop.png"></image> {{item.binfo.name}}</view>
						<view class="f1" v-else><image :src="item.binfo.logo" class="logo-row"></image> {{item.binfo.name}}</view> -->
						<view class="flex1"></view>
						<text v-if="item.status==0" class="st0">待付款</text>
						<text v-if="item.status==1 && item.freight_type!=1" class="st1">待发货</text>
						<text v-if="item.status==1 && item.freight_type==1" class="st1">待提货</text>
						<text v-if="item.status==2" class="st2">待收货</text>
						<text v-if="item.status==3" class="st3">已完成</text>
						<text v-if="item.status==4" class="st4">已关闭</text>
					</view>

					<block v-for="(item2, idx) in item.product" :key="idx">
						<view class="content" :style="'margin-bottom:20rpx'">
							<view @tap.stop="goto" :data-url="'/shopPackage/shop/product?id=' + item2.proid">
								<image :src="item2.pic"></image>
							</view>
							<view class="detail">
								<text class="t1">{{item2.name}}</text>
								<text class="t2">{{item2.ggname}}</text>
								<view class="t3">
									<text class="x1 flex1">￥{{item2.sell_price}}</text>
									<text class="x2">×{{item2.num}}</text>
								</view>
								<view class="t3">
									<!-- <text class="x1 flex1">￥{{item2.sell_price}}</text>
									<text class="x2">×{{item2.num}}</text> -->
									
								</view>
							</view>
						</view>
					</block>
					
					<view  class="bottom"><text class="t2">{{item.typenamename}}</text></view>
					<view class="bottom">
						<text>共计{{item.procount}}件商品 实付:￥{{item.totalprice}}  <span v-if="item.balance_price > 0 && item.balance_pay_status == 0"  style="display: block; float: right;">尾款：￥{{item.balance_price}}</span></text>
						<text v-if="item.refund_status==1" style="color:red;padding-left:6rpx">退款中￥{{item.refund_money}}</text>
						<text v-if="item.refund_status==2" style="color:red;padding-left:6rpx">已退款￥{{item.refund_money}}</text>
						<text v-if="item.refund_status==3" style="color:red;padding-left:6rpx">退款申请已驳回</text>
						
					</view>
					
					<!-- 新增的收货人信息 -->
					<view class="bottom">
					    <text>收货人：{{item.name}} {{item.address_tel}}</text>
					</view>
					<view class="bottom">
					    <text>地址：{{item.province}}{{item.city}}{{item.district}}{{item.area}}{{item.default_address}}</text>
					</view>
					<view class="bottom" v-if="item.tips!=''">
						<text style="color:red">{{item.tips}}</text>
					</view>
					<view class="op">
						<block v-if="([1,2,3]).includes(item.status) && item.invoice">
							<view class="btn2" @tap.stop="goto" :data-url="'invoice?type=shop&orderid=' + item.id">发票</view>
						</block>
						
						<view @tap.stop="goto" :data-url="'/pagesExa/daike/daikebuy?id=' + item.id + '&mid=' + item.mid"  class="btn2">详情</view>
						
						<block v-if="item.status==0">
							<view class="btn2" @tap.stop="toclose" :data-id="item.id">关闭订单</view>
						
							<block v-if="item.paytypeid==5">
                                <view class="btn1" v-if="item.transfer_check == 1" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pages/pay/transfer?id=' + item.payorderid">付款凭证</view>
                                <view class="btn1" v-else :style="{background:t('color1')}">
                                    <text v-if="item.transfer_check == 0">转账待审核</text>
                                    <text v-if="item.transfer_check == -1">转账已驳回</text>
                                </view>
                            </block>
							
						</block>
						<block v-if="item.status==1">
							<block v-if="item.paytypeid!='4'">
								<view class="btn2" @tap.stop="goto" :data-url="'refundSelect?orderid=' + item.id + '&price=' + item.totalprice" v-if="canrefund==1 && item.refundnum < item.procount">退款</view>
							</block>
							<block v-else>
								<!-- <view class="btn2">{{codtxt}}</view> -->
							</block>
						</block>
						<block v-if="item.status==2">
							<block v-if="item.paytypeid!='4'">
								<view class="btn2" @tap.stop="goto" :data-url="'refundSelect?orderid=' + item.id + '&price=' + item.totalprice" v-if="canrefund==1 && item.refundnum < item.procount">退款</view>
							</block>
							<block v-else>
								<!-- <view class="btn2">{{codtxt}}</view> -->
							</block>
							<block v-if="item.freight_type!=3 && item.freight_type!=4">
									<view class="btn2" v-if="item.express_type =='express_wx'" @tap.stop="logistics" :data-index="index">订单跟踪</view>
									<view class="btn2" v-else @tap.stop="logistics" :data-index="index">查看物流</view>
							</block>
							
							<view v-if="item.balance_pay_status == 0 && item.balance_price > 0" class="btn1" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pages/pay/pay?id=' + item.balance_pay_orderid">支付尾款</view>
							<view v-if="item.paytypeid!='4' && (item.balance_pay_status==1 || item.balance_price==0)" class="btn1" :style="{background:t('color1')}" @tap.stop="orderCollect" :data-id="item.id">确认收货</view>
						</block>
						<block v-if="(item.status==1 || item.status==2) && item.freight_type==1">
							<view class="btn2" @tap.stop="showhxqr" :data-hexiao_qr="item.hexiao_qr">核销码</view>
						</block>
						<view v-if="item.refundCount" class="btn2" @tap.stop="goto" :data-url="'refundlist?orderid='+ item.id">查看退款</view>
						<block v-if="item.status==3 || item.status==4">
							<view class="btn2" @tap.stop="todel" :data-id="item.id">删除订单</view>
						</block>
						<block v-if="item.bid>0 && item.status==3">
							<view v-if="item.iscommentdp==0" class="btn1" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pagesExt/order/commentdp?orderid=' + item.id">评价店铺</view>
						</block>
					</view>
				</view>
			</block>
		</view>
		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>

		
		<uni-popup id="dialogHxqr" ref="dialogHxqr" type="dialog">
			<view class="hxqrbox">
				<image :src="hexiao_qr" @tap="previewImage" :data-url="hexiao_qr" class="img"/>
				<view class="txt">请出示核销码给核销员进行核销</view>
				<view class="close" @tap="closeHxqr">
					<image src="/static/img/close2.png" style="width:100%;height:100%"/>
				</view>
			</view>
		</uni-popup>

		<uni-popup id="dialogSelectExpress" ref="dialogSelectExpress" type="dialog">
			<view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx" v-if="express_content">
				<view class="sendexpress" v-for="(item, index) in express_content" :key="index" style="border-bottom: 1px solid #f5f5f5;padding:20rpx 0;">
					<view class="sendexpress-item" @tap="goto" :data-url="'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no" style="display: flex;">
						<view class="flex1" style="color:#121212">{{item.express_com}} - {{item.express_no}}</view>
						<image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx"/>
					</view>
					<view v-if="item.express_oglist" style="margin-top:20rpx">
						<view class="oginfo-item" v-for="(item2, index2) in item.express_oglist" :key="index2" style="display: flex;align-items:center;margin-bottom:10rpx">
							<image :src="item2.pic" style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0"/>
							<view class="flex1" style="color:#555">{{item2.name}}({{item2.ggname}})</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

	</block>
	<!-- <loading v-if="loading"></loading> -->
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
	
	<uni-popup ref="more_one" type="center" >
		<uni-popup-dialog mode="input" message="成功消息" :duration="2000" valueType="number" :before-close="true"  @close="close" @confirm="confirm"></uni-popup-dialog>
		<!-- <view class="uni-popup-dialog">
			<view class="uni-dialog-title">
				<text>test</text>
			</view>
			<view class="uni-dialog-content">
				<input type="number"/>
			</view>
			<view class="uni-dialog-button-group">
				 <view></view>
			</view>
		</view> -->
	</uni-popup>
</view>
</template>
<script> 
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      st: 'all',
      datalist: [],
      page: 1,
      limit: 10,
      total: 0,
      pages: 0,
      nomore: false,
      nodata: false,
      codtxt: "",
      canrefund: 1,
      express_content: '',
      selectExpressShow: false,
      hexiao_qr: '',
      keyword: '',
      is_more: 0,
      cart_info: [],
      loop_point: 0,
      loop_length: 0,
    };
  },

  onLoad: function(opt) {
    this.opt = app.getopts(opt);
    if (this.opt && this.opt.st) {
      this.st = this.opt.st;
    }
    this.getdata();
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.getdata();
    uni.stopPullDownRefresh();
  },
  
  // 上拉加载更多
  onReachBottom: function() {
    if (!this.nomore && !this.nodata) {
      this.page = this.page + 1;
      this.getdata(true);
    }
  },

  onNavigationBarSearchInputConfirmed: function(e) {
    this.searchConfirm({ detail: { value: e.text } });
  },

  methods: {
    add_cart(data) {
      let that = this;
      app.post('ApiShop/addcart', {
        'ggid': data['ggid'],
        'num': data['num'],
        'proid': data['id'],
        'tid': 0,
        'glass_record_id': 0
      }, function(res) {
        if (res.status == 1) {
          if (that.loop_point == that.loop_length - 1) {
            uni.redirectTo({
              url: '/shopPackage/shop/cart'
            });
          } else {
            that.loop_point++;
            that.add_cart(that.cart_info[that.loop_point]);
          }
        }
      });
    },

    more_one(e) {
      let data = e.target.dataset;
      let proInfo = [];
      let o_index = data['key'];
      let order = this.datalist[o_index];
      let prolist = order['prolist'];

      for (let i = 0; i < prolist.length; i++) {
        let obj = {};
        obj['id'] = prolist[i]['proid'];
        obj['num'] = prolist[i]['num'];
        obj['ggid'] = prolist[i]['ggid'];
        proInfo.push(obj);
      }

      this.loop_length = proInfo.length;
      this.loop_point = 0;
      this.cart_info = proInfo;
      this.add_cart(proInfo[0]);
    },

    more_one2(e) {
      let data = e.target.dataset;
      app.post('/ApiShop/addcart', {
        'ggid': data['ggid'],
        'num': data['num'],
        'proid': data['id'],
        'tid': 0,
        'glass_record_id': 0
      }, function(res) {
        if (res.status == 1) {
          uni.redirectTo({
            url: '/shopPackage/shop/cart'
          });
        }
      });
    },

    confirm(value1, value2) {
      let data = this.more_data;
      let num = value2;
      let that = this;

      if (num == "" || num == 0) {
        uni.showModal({
          title: "提示",
          content: "请输入数量",
          success() {
            that.$refs.more_one.close();
          }
        });
      }
      let url = "/shopPackage/shop/buy?" + "prodata=" + data['id'] + "," + data['ggid'] + "," + num;
      uni.redirectTo({
        url: url,
      });
      this.$refs.more_one.close();
    },

    close() {
      this.$refs.more_one.close();
    },

    getdata: function(loadmore = false) {
      if (!loadmore) {
        this.page = 1;
        this.datalist = [];
      }
      let that = this;
      let page = this.page;
      let limit = this.limit;
      let st = this.st;
      this.nodata = false;
      this.nomore = false;
      this.loading = true;

      app.post('ApiDaigou/daigoulistadmin', {
        st: st,
        page: page,
        limit: limit,
        keyword: this.keyword
      }, function(res) {
        that.loading = false;
        if (res.status == 1) {
          var data = res.data;
          that.total = res.total || 0;
          that.pages = res.pages || 0;
          
          if (page == 1) {
            that.codtxt = res.codtxt;
            that.canrefund = res.canrefund;
            that.datalist = data;
            if (data.length == 0) {
              that.nodata = true;
            }
          } else {
            if (data.length == 0) {
              that.nomore = true;
            } else {
              let datalist = that.datalist;
              let newdata = datalist.concat(data);
              that.datalist = newdata;
            }
          }
          
          // 判断是否还有更多数据
          if (that.page >= that.pages && that.pages > 0) {
            that.nomore = true;
          }
        } else {
          app.toast(res.msg || '获取数据失败');
        }
        
        that.loaded();
      });
    },

    changetab: function(st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },

    toclose: function(e) {
      let that = this;
      let orderid = e.currentTarget.dataset.id;
      app.confirm('确定要关闭该订单吗?', function() {
        app.showLoading('提交中');
        app.post('ApiDaigou/closeOrder', {
          orderid: orderid
        }, function(data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function() {
            that.getdata();
          }, 1000);
        });
      });
    },

    todel: function(e) {
      let that = this;
      let orderid = e.currentTarget.dataset.id;
      app.confirm('确定要删除该订单吗?', function() {
        app.showLoading('删除中');
        app.post('ApiOrder/delOrder', {
          orderid: orderid
        }, function(data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function() {
            that.getdata();
          }, 1000);
        });
      });
    },

    orderCollect: function(e) {
      let that = this;
      let orderid = e.currentTarget.dataset.id;
      app.confirm('确定要收货吗?', function() {
        app.showLoading('提交中');
        app.post('ApiOrder/orderCollect', {
          orderid: orderid
        }, function(data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function() {
            that.getdata();
          }, 1000);
        });
      });
    },

    logistics: function(e) {
      let index = e.currentTarget.dataset.index;
      let orderinfo = this.datalist[index];
      let express_com = orderinfo.express_com;
      let express_no = orderinfo.express_no;
      let express_content = orderinfo.express_content;
      let express_type = orderinfo.express_type;
      let prolist = orderinfo.prolist;

      if (!express_content) {
        app.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no + '&type=' + express_type);
      } else {
        express_content = JSON.parse(express_content);
        for (let i in express_content) {
          if (express_content[i].express_ogids) {
            let express_ogids = (express_content[i].express_ogids).split(',');
            let express_oglist = [];
            for (let j in prolist) {
              if (app.inArray(prolist[j].id + '', express_ogids)) {
                express_oglist.push(prolist[j]);
              }
            }
            express_content[i].express_oglist = express_oglist;
          }
        }
        this.express_content = express_content;
        this.$refs.dialogSelectExpress.open();
      }
    },

    hideSelectExpressDialog: function() {
      this.$refs.dialogSelectExpress.close();
    },

    showhxqr: function(e) {
      this.hexiao_qr = e.currentTarget.dataset.hexiao_qr;
      this.$refs.dialogHxqr.open();
    },

    closeHxqr: function() {
      this.$refs.dialogHxqr.close();
    },

    searchConfirm: function(e) {
      this.keyword = e.detail.value;
      this.getdata(false);
    }
  }
};
</script>

<style>
.container{ width:100%;}
.topsearch{width:94%;margin:10rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.order-content{display:flex;flex-direction:column}
.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}
.order-box .head .f1{display:flex;align-items:center;color:#333}
.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}
.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }
.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }
.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }
.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }
.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }

.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}
.order-box .content:last-child{ border-bottom: 0; }
.order-box .content image{ width: 140rpx; height: 140rpx;}
.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}
.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}
.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}
.order-box .content .detail .x1{ flex:1}
.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}

.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}

.btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}
.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}

.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.hxqrbox .img{width:400rpx;height:400rpx}
.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}
</style>
