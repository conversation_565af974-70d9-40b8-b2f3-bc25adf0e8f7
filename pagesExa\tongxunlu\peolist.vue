<template>
  <view class="container">
    <view class="header-image">
      <image src="https://kuaifengimg.azheteng.cn/upload/1/20240825/cd61ae6d329b86ca6717f0c25a197005_thumb.png" mode="widthFix" class="header-img"></image>
    </view>
    <block v-if="isload">
         <!-- 选择所在区域文字 -->
          <view class="select-region-text">
            选择所在区域
          </view>
          
          <!-- 区县和小区筛选器 -->
          <view class="region-select">
            <picker mode="selector" :range="regionNames" @change="onRegionChange" class="picker-region">
              <view class="picker-view">{{ selectedRegion.name || '请选择区县' }}</view>
            </picker>
            <picker mode="selector" :range="subRegionNames" @change="onSubRegionChange" :disabled="!selectedRegion.id" class="picker-subregion">
              <view class="picker-view">{{ selectedSubRegion.name || '请选择小区' }}</view>
            </picker>
          </view>

      <!-- 搜索框 -->
     <!-- <view class="topsearch flex-y-center">
        <view class="f1 flex-y-center">
          <image class="img" src="/static/img/search_ico.png"></image>
          <input :value="keyword" placeholder="输入姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm" @input="searchChange"></input>
        </view>
      </view> -->

      <!-- 表头 -->
      <view class="table-header">
        <view class="header-cell">小区名称</view>
        <view class="header-cell">服务人员</view>
        <view class="header-cell">电话</view>
      </view>

     <view v-for="(item, index) in datalist" :key="index" class="table-row" :data-id="item.id">
         <view class="table-cell community-name">
             {{ item.community_name }}
         </view>
         <view class="table-cell service-staff">
             {{ item.realname }}
         </view>
         <view class="table-cell service-tel">
             <image src="/static/img/tongxunlutle.png" mode="aspectFit" class="call-icon" @click="call(item.tel)" />
         </view>
     </view>


      <nodata v-if="nodata"></nodata>
      <nomore v-if="nomore"></nomore>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      keyword: '', // 搜索关键词
      datalist: [], // 用于存放人员数据
      type: "",
      nodata: false,
      curTopIndex: -1,
      index: 0,
      curCid: 0, // 当前选择的小区ID
      nomore: false,
      pagenum: 1,
      regionList: [], // 存放区县列表对象
      regionNames: [], // 存放区县名称的字符串数组，供 picker 使用
      subRegionList: [], // 存放小区列表对象
      subRegionNames: [], // 存放小区名称的字符串数组，供 picker 使用
      selectedRegion: {}, // 用户选择的区县
      selectedSubRegion: {}, // 用户选择的小区
      clist: [], // 存放API返回的数据，确保其定义并初始化
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.type = this.opt.type || '';
    this.getdata();
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdatalist(true);
    }
  },
  methods: {
    getdata: function () {
      var that = this;
      var nowcid = that.opt.cid;
      var bid = that.opt.bid || 0;
      if (!nowcid) nowcid = '';
      that.loading = true;
      
      app.get('Apitongxunlu/peocategory', { cid: nowcid, bid: bid }, function (res) {
        that.loading = false;

        if (res.data && res.data.length > 0) {
          that.regionList = res.data.map(item => ({
            id: item.id, 
            name: item.name,
            children: item.children || []
          }));
          that.regionNames = res.data.map(item => item.name); // 提取区县名称供 picker 使用

          that.clist = res.data;

          if (that.regionList.length > 0) {
            that.selectedRegion = that.regionList[0];
            // 递归获取所有子层级的小区列表
            that.subRegionList = that.getAllChildren(that.regionList[0].children);
            that.subRegionNames = that.subRegionList.map(item => item.name); // 提取小区名称供 picker 使用
          }
          
          that.getdatalist();
        }
        
        that.loaded();
      });
    },

 onRegionChange: function (e) {
  var selectedRegionIndex = e.detail.value;
  this.selectedRegion = this.regionList[selectedRegionIndex];

  // 更新 subRegionList 和 subRegionNames 只关注 children 的 children
  this.subRegionList = this.getAllChildren(this.selectedRegion.children);
  this.subRegionNames = this.subRegionList.map(item => item.name); // 更新小区名称供 picker 使用

  this.selectedSubRegion = {}; 
},


    onSubRegionChange: function (e) {
      var selectedSubRegionIndex = e.detail.value;
      this.selectedSubRegion = this.subRegionList[selectedSubRegionIndex];
      this.curCid = this.selectedSubRegion.id;

      // 调用接口获取选定小区的人员数据
      this.getdatalist();
    },

   // 递归获取所有子层级的小区
   getAllChildren: function (children) {
     let allChildren = [];
   
     // 遍历当前层级的 children
     children.forEach(child => {
       if (child.children && child.children.length > 0) {
         // 如果 child 有子级，则递归获取子级的小区
         allChildren = allChildren.concat(this.getAllChildren(child.children));
       } else {
         // 如果没有子级，则添加到结果中
         allChildren.push({ id: child.id, name: child.name });
       }
     });
   
     return allChildren;
   },

    
    getdatalist: function (loadmore) {
      if (!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
      }
      var that = this;
      var pagenum = that.pagenum;
      var cid = that.curCid; // 使用选择的小区ID
      var bid = that.opt.bid ? that.opt.bid : '';
      var order = that.order;
      var keyword = that.keyword; // 关键字
      var field = that.field;
      that.loading = true;
      that.nodata = false;
      that.nomore = false;
      var latitude = that.latitude;
      var longitude = that.longitude;
      
      app.post('Apitongxunlu/selectpeople', { pagenum: pagenum, keyword: keyword, field: field, order: order, cid: cid, bid: bid, type: 'list', longitude: longitude, latitude: latitude }, function (res) { 
        that.loading = false;
        var data = res.data;

        // 遍历每个数据项并提取服务站信息
        data.forEach(item => {
          if (item.second_level_name) {
            item.service_area = item.second_level_name; // 使用 second_level_name 作为服务站信息
          }
        });

        if (pagenum == 1) {
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
        } else {
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },

    searchChange: function (e) {
      this.keyword = e.detail.value; // 更新搜索关键字
    },

    searchConfirm: function () {
      this.getdatalist(false); // 当用户确认搜索时调用，重新加载数据
    },

    call: function(tel) {
      wx.makePhoneCall({
        phoneNumber: tel
      });
    }
  }
};
</script>


<style>
.topsearch{width:94%;margin:16rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}
.search-history {padding: 24rpx 34rpx;}
.search-history .search-history-title {color: #666;}
.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}
.search-history-list {padding: 24rpx 0 0 0;}
.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}


.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx}
.order-tab2{display:flex;width:auto;min-width:100%}
.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:80rpx; line-height:80rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}
.order-tab2 .on{color:#222222;}
.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:1.5px;width:40rpx}
.order-tab2 .on .after{display:block}


.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:20rpx 40rpx; justify-content: space-between;}
.content .f1{display:flex;align-items:center}
.content .f1 image{ width: 140rpx; height: 140rpx; border-radius: 10rpx;}
.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:32rpx;margin-left:10rpx;}
.content .f1 .t2{color:#999999;font-size:28rpx; background: #E8E8F7;color:#7A83EC; margin-left: 10rpx; padding:3rpx 20rpx; font-size: 20rpx; border-radius: 18rpx;}
.content .f1 .t3{ margin-left:10rpx;display: block; height: 40rpx;line-height: 40rpx;}
.content .f2{color:#2b2b2b;font-size:26rpx;line-height:42rpx;padding-bottom:20rpx;}
.content .f3{height:96rpx;display:flex;align-items:center}
.content .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}
.content .radio .radio-img{width:100%;height:100%}
.content .mrtxt{color:#2B2B2B;font-size:26rpx;margin-left:10rpx}


.text2{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}
.text3{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}
.text3 .t5{ margin-left: 20rpx;}
.text3 .t5 text{ color:#7A83EC}
.text3 .t4 text{ color:#7A83EC}
.yuyue{ background: #7A83EC; height: 40rpx; line-height: 40rpx; padding: 0 10rpx; color:#fff; border-radius:28rpx; width: 80rpx; font-size: 20rpx; text-align: center; margin-top: 20rpx;}
.text1{ margin-left: 10rpx;}
.container .btn-add{width:90%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:0px;right:0;bottom:20rpx;}
/* 容器样式 */
.container {
  padding: 16rpx;
  background-color: #f7f7f7;
}

/* 筛选器容器 */
.region-select {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

/* 每个筛选器的样式 */
.picker-region,
.picker-subregion {
  flex: 1;
  margin-right: 10rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #dcdcdc;
  overflow: hidden;
}

/* picker 内部的文本样式 */
.picker-view {
  padding: 20rpx;
  color: #333333;
  font-size: 28rpx;
  text-align: center;
}

/* 禁用状态下的样式 */
.picker-subregion:disabled .picker-view {
  background-color: #f0f0f0;
  color: #999999;
}

/* 当 picker 被点击时的效果 */
.picker-view:active {
  background-color: #eeeeee;
}
.container {
  padding: 16rpx;
  background-color: #f7f7f7;
}

/* 顶部图片的容器样式 */
.header-image {
  width: 100%;
  margin-bottom: 20rpx;
}

/* 图片的样式 */
.header-img {
  width: 100%;
  border-radius: 12rpx;
}

/* 筛选器容器 */
.region-select {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

/* 每个筛选器的样式 */
.picker-region,
.picker-subregion {
  flex: 1;
  margin-right: 10rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #dcdcdc;
  overflow: hidden;
}

/* picker 内部的文本样式 */
.picker-view {
  padding: 20rpx;
  color: #333333;
  font-size: 28rpx;
  text-align: center;
}

/* 禁用状态下的样式 */
.picker-subregion:disabled .picker-view {
  background-color: #f0f0f0;
  color: #999999;
}

/* 当 picker 被点击时的效果 */
.picker-view:active {
  background-color: #eeeeee;
}


/* 拨打电话图片样式 */
.call-btn-img {
  width: 50rpx !important;
  height: 50rpx !important;
  display: block; /* 确保图片是块级元素 */
  margin: auto; /* 居中对齐 */
}

/* 通用容器样式 */
.container {
  padding: 16rpx;
  background-color: #f7f7f7;
}

/* 顶部图片的容器样式 */
.header-image {
  width: 100%;
  margin-bottom: 20rpx;
}

/* 图片的样式 */
.header-img {
  width: 100%;
  border-radius: 12rpx;
}

/* 表头样式 */
.table-header {
  display: flex;
  background-color: #f0f0f0;
  padding: 10rpx 20rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #dcdcdc;
}

.header-cell {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
}

/* 行样式 */
.table-row {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 单元格样式 */
.table-cell {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

/* 服务人员图标 */
.call-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

</style>