<template>
  <view class="job-detail">
    <!-- 加载状态 -->
    <view class="loading-wrapper" v-if="loading">
      <view class="loading-spinner"></view>
    </view>

    <block v-else>
      <!-- 顶部信息 -->
      <view class="header">
        <view class="title-wrapper">
          <text class="title">{{jobInfo.title}}</text>
          <text class="status" :class="{'status-active': jobInfo.status === 1}">
            {{jobInfo.status === 1 ? '招聘中' : '已结束'}}
          </text>
        </view>
        <view class="salary" :style="{color: t('color1')}">{{jobInfo.salary}}</view>
        <view class="basic-info">
          <text><text class="iconfont icon-location"></text>{{jobInfo.work_address}}</text>
          <text><text class="iconfont icon-education"></text>{{jobInfo.education}}</text>
          <text><text class="iconfont icon-experience"></text>{{jobInfo.experience}}</text>
        </view>
        <scroll-view scroll-x class="tags-scroll" show-scrollbar="false">
          <view class="tags">
            <text class="tag" v-for="(tags, type) in jobInfo.formatted_options" :key="type">
              <text v-for="(tag, index) in tags" :key="index">{{tag}}</text>
            </text>
          </view>
        </scroll-view>
      </view>

      <!-- 公司信息 -->
      <view class="company" hover-class="company-hover" @click="goToCompany">
        <image class="logo" :src="jobInfo.company_logo" mode="aspectFit"></image>
        <view class="company-info">
          <view class="name">{{jobInfo.company_name}}</view>
          <view class="desc">
            <text>{{getCompanyNature}}</text>
            <text>{{getCompanyScale}}</text>
          </view>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>

      <!-- 主要内容区域 -->
      <scroll-view class="content-scroll" scroll-y>
        <!-- 工作信息 -->
        <view class="section">
          <view class="section-title">
            <text class="iconfont icon-info"></text>
            工作信息
          </view>
          <view class="info-list">
            <view class="info-item">
              <text class="label">工作方式</text>
              <text class="value">{{jobInfo.work_mode}}</text>
            </view>
            <view class="info-item">
              <text class="label">工作强度</text>
              <text class="value">{{jobInfo.work_intensity}}</text>
            </view>
            <view class="info-item">
              <text class="label">工作时间</text>
              <text class="value">{{jobInfo.work_time_type}}</text>
            </view>
            <view class="info-item">
              <text class="label">结算方式</text>
              <text class="value">{{jobInfo.payment}}</text>
            </view>
            <view class="info-item">
              <text class="label">招聘人数</text>
              <text class="value highlight">{{jobInfo.numbers}}人</text>
            </view>
          </view>
        </view>

        <!-- 职位描述 -->
        <view class="section">
          <view class="section-title">
            <text class="iconfont icon-description"></text>
            职位描述
          </view>
          <rich-text :nodes="jobInfo.description" class="rich-content"></rich-text>
        </view>

        <!-- 任职要求 -->
        <view class="section">
          <view class="section-title">
            <text class="iconfont icon-requirement"></text>
            任职要求
          </view>
          <rich-text :nodes="jobInfo.requirement" class="rich-content"></rich-text>
        </view>

        <!-- 工作福利 -->
        <view class="section">
          <view class="section-title">
            <text class="iconfont icon-welfare"></text>
            工作福利
          </view>
          <rich-text :nodes="jobInfo.benefits" class="rich-content"></rich-text>
        </view>

        <!-- 底部占位 -->
        <view style="height: 120rpx;"></view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="footer">
        <view class="collect" @click="handleCollect">
          <text :class="['iconfont', isCollected ? 'icon-heart-fill' : 'icon-heart']"></text>
          <text>{{isCollected ? '已收藏' : '收藏'}}</text>
        </view>
        <button 
          class="apply-btn" 
          :class="{
            'apply-btn-disabled': jobInfo.status !== 1,
            'apply-btn-applied': hasApplied
          }"
          :style="{
            background: hasApplied ? '#52c41a' : 'linear-gradient(135deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)',
            boxShadow: hasApplied ? '0 8rpx 24rpx rgba(82, 196, 26, 0.25)' : '0 8rpx 24rpx rgba('+t('color1rgb')+',0.25)'
          }"
          :disabled="jobInfo.status !== 1 || hasApplied"
          @click="handleApply"
        >
          <block v-if="jobInfo.status === 1">
            {{hasApplied ? '已报名' : '立即报名'}}
          </block>
          <block v-else>已结束</block>
        </button>
      </view>
    </block>

    <!-- 报名弹窗 -->
    <view class="report-dialog" v-if="reportVisible">
      <view class="dialog-mask" @click="closeReportDialog"></view>
      <view class="dialog-content">
        <!-- 弹窗头部 -->
        <view class="dialog-header">
          <text class="dialog-title">职位报名</text>
          <text class="dialog-close iconfont icon-close" @click="closeReportDialog"></text>
        </view>

        <!-- 职位信息 -->
        <view class="dialog-job-info">
          <text class="job-title">{{jobInfo.title}}</text>
          <text class="job-salary">{{jobInfo.salary}}</text>
          <text class="job-company">{{jobInfo.company_name}}</text>
        </view>

        <!-- 报名表单 -->
        <scroll-view class="dialog-form" scroll-y>
          <!-- 基本信息 -->
          <view class="form-section">
            <view class="form-title">基本信息</view>
            <view class="form-item">
              <text class="label">姓名</text>
              <input 
                class="input" 
                v-model="formData.name" 
                placeholder="请输入真实姓名"
                :maxlength="20"
              />
            </view>
            <view class="form-item">
              <text class="label">手机号</text>
              <input 
                class="input" 
                v-model="formData.phone" 
                type="number"
                placeholder="请输入手机号"
                :maxlength="11"
              />
            </view>
          </view>
        </scroll-view>

        <!-- 底部按钮 -->
        <view class="dialog-footer">
          <checkbox-group class="agreement" @change="handleAgreementChange">
            <checkbox :checked="formData.agreement" />
            <text>我已阅读并同意</text>
            <text class="link" @click="showAgreement">《用户服务协议》</text>
          </checkbox-group>
          <button 
            class="submit-btn" 
            :disabled="!isFormValid || submitting"
            :style="{background: 'linear-gradient(135deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}"
            @click="submitApply"
          >
            {{submitting ? '提交中...' : '确认报名'}}
          </button>
        </view>
      </view>
    </view>

    <!-- 协议弹窗 -->
    <view class="agreement-dialog" v-if="agreementVisible">
      <view class="dialog-mask" @click="closeAgreement"></view>
      <view class="dialog-content">
        <view class="dialog-header">
          <text class="dialog-title">用户服务协议</text>
          <text class="dialog-close iconfont icon-close" @click="closeAgreement"></text>
        </view>
        <scroll-view class="agreement-content" scroll-y>
          <rich-text :nodes="agreementContent"></rich-text>
        </scroll-view>
        <view class="dialog-footer">
          <button class="confirm-btn" 
            :style="{background: t('color1')}"
            @click="closeAgreement"
          >我知道了</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp()

export default {
  data() {
    return {
      loading: true,
      jobInfo: {
        id: 0,
        title: '',
        salary: '',
        work_address: '',
        education: '',
        experience: '',
        company_logo: '',
        company_name: '',
        description: '',
        requirement: '',
        benefits: '',
        work_mode: '',
        work_intensity: '',
        work_time_type: '',
        payment: '',
        numbers: 0,
        nature: 1,
        scale: 1,
        formatted_options: {},
        is_favorite: 0,
        status: 1,
        hasApplied: false
      },
      isCollected: false,
      reportVisible: false,
      agreementVisible: false,
      agreementContent: '',
      submitting: false,
      hasApplied: false,
      formData: {
        name: '',
        phone: '',
        agreement: false
      },
      genderOptions: [
        { label: '男', value: '1' },
        { label: '女', value: '2' }
      ],
      experienceOptions: [
        { label: '应届生', value: '0' },
        { label: '1年以下', value: '1' },
        { label: '1-3年', value: '2' },
        { label: '3-5年', value: '3' },
        { label: '5-10年', value: '4' },
        { label: '10年以上', value: '5' }
      ],
      educationOptions: [
        { label: '初中及以下', value: '1' },
        { label: '高中', value: '2' },
        { label: '大专', value: '3' },
        { label: '本科', value: '4' },
        { label: '硕士', value: '5' },
        { label: '博士', value: '6' }
      ]
    }
  },

  computed: {
    getCompanyNature() {
      const natureMap = {
        1: '民营企业',
        2: '国有企业',
        3: '合资企业',
        4: '外资企业'
      }
      return natureMap[this.jobInfo.nature] || '其他企业'
    },

    getCompanyScale() {
      const scaleMap = {
        1: '20-99人',
        2: '100-499人',
        3: '500-999人',
        4: '1000-9999人',
        5: '10000人以上'
      }
      return scaleMap[this.jobInfo.scale] || '规模未知'
    },

    isFormValid() {
      const { name, phone, agreement } = this.formData
      return name && 
             phone && 
             phone.length === 11 && 
             agreement
    }
  },

  onLoad(options) {
    if (options.id) {
      this.getJobDetail(options.id)
    } else {
      this.loading = false
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      })
    }
  },

  onPullDownRefresh() {
    if (this.jobInfo.id) {
      this.getJobDetail(this.jobInfo.id)
    }
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },

  methods: {
    getJobDetail(id) {
      this.loading = true
      app.get('ApiZhaopin/getPositionDetail', {
        id: id
      }, (res) => {
        this.loading = false
        if (res.status === 1 && res.data) {
          const data = res.data
          ;['description', 'requirement', 'benefits'].forEach(key => {
            if (data[key]) {
              data[key] = data[key].replace(/<img/gi, '<img style="max-width:100%;height:auto;display:block;margin:10rpx 0;"')
            }
          })
          
          this.jobInfo = data
          this.isCollected = data.is_favorite === 1
          this.hasApplied = data.hasApplied || false
          
          uni.setNavigationBarTitle({
            title: data.title || '职位详情'
          })
        } else {
          uni.showToast({
            title: res.msg || '获取数据失败',
            icon: 'none'
          })
        }
      }, () => {
        this.loading = false
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      })
    },

    handleCollect() {
      if (!this.jobInfo.id) return
      
      const prevState = this.isCollected
      this.isCollected = !this.isCollected // 优先切换状态，提升体验
      
      app.post('apiZhaopin/favoritePosition', {
        position_id: this.jobInfo.id
      }, (res) => {
        if (res.status === 1) {
          this.isCollected = res.data.is_favorite === 1
          uni.showToast({
            title: this.isCollected ? '收藏成功' : '已取消收藏',
            icon: 'none'
          })
        } else {
          this.isCollected = prevState // 恢复之前的状态
          uni.showToast({
            title: res.msg || '操作失败',
            icon: 'none'
          })
        }
      }, () => {
        this.isCollected = prevState // 恢复之前的状态
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      })
    },

    handleApply() {
      if (this.jobInfo.status !== 1) {
        uni.showToast({
          title: '该职位已结束招聘',
          icon: 'none'
        })
        return
      }

      if (this.hasApplied) {
        uni.showToast({
          title: '您已经报名过该职位',
          icon: 'none'
        })
        return
      }

      this.reportVisible = true
    },

    closeReportDialog() {
      this.reportVisible = false
      // 重置表单
      this.resetForm()
    },

    resetForm() {
      this.formData = {
        name: '',
        phone: '',
        agreement: false
      }
    },

    handleAgreementChange(e) {
      this.formData.agreement = e.detail.value.length > 0
    },

    showAgreement() {
      // 获取协议内容
      app.get('ApiZhaopin/getAgreement', {}, (res) => {
        if (res.status === 1) {
          this.agreementContent = res.data.content
          this.agreementVisible = true
        }
      })
    },

    closeAgreement() {
      this.agreementVisible = false
    },

    submitApply() {
      if (!this.isFormValid) return

      this.submitting = true
      app.post('ApiZhaopin/submitApply', {
        position_id: this.jobInfo.id,
        name: this.formData.name,
        phone: this.formData.phone
      }, (res) => {
        this.submitting = false
        if (res.status === 1) {
          this.hasApplied = true
          this.reportVisible = false
          uni.showToast({
            title: '报名成功',
            icon: 'success'
          })
          // 重新获取职位详情，更新状态
          this.getJobDetail(this.jobInfo.id)
        } else {
          uni.showToast({
            title: res.msg || '报名失败，请重试',
            icon: 'none'
          })
        }
      }, () => {
        this.submitting = false
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      })
    },

    goToCompany() {
      if (!this.jobInfo.company_id) return
      
      uni.navigateTo({
        url: `/zhaopin/company?id=${this.jobInfo.company_id}`,
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
@import './jobDetail.scss';

/* 添加弹窗相关样式 */
.report-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  .dialog-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
  }

  .dialog-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 30rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease-out;
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .dialog-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }

    .dialog-close {
      font-size: 40rpx;
      color: #999;
      padding: 10rpx;
    }
  }

  .dialog-job-info {
    background: #f8f9fa;
    padding: 20rpx;
    border-radius: 12rpx;
    margin-bottom: 30rpx;

    .job-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 12rpx;
    }

    .job-salary {
      font-size: 28rpx;
      color: #ff4d4f;
      margin-bottom: 8rpx;
    }

    .job-company {
      font-size: 26rpx;
      color: #666;
    }
  }

  .dialog-form {
    flex: 1;
    overflow-y: auto;

    .form-section {
      margin-bottom: 30rpx;

      .form-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
      }

      .form-item {
        margin-bottom: 20rpx;

        .label {
          font-size: 28rpx;
          color: #666;
          margin-bottom: 12rpx;
          display: block;
        }

        .input {
          width: 100%;
          height: 88rpx;
          background: #f8f9fa;
          border-radius: 12rpx;
          padding: 0 24rpx;
          font-size: 28rpx;
        }
      }
    }
  }

  .dialog-footer {
    padding-top: 20rpx;
    border-top: 1px solid #f0f0f0;

    .agreement {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      font-size: 26rpx;
      color: #666;

      .link {
        color: var(--primary-color);
      }
    }

    .submit-btn {
      width: 100%;
      height: 88rpx;
      border-radius: 44rpx;
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;

      &:disabled {
        opacity: 0.6;
      }
    }
  }
}

.agreement-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;

  .dialog-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
  }

  .dialog-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 600rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 30rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }

  .agreement-content {
    flex: 1;
    overflow-y: auto;
    margin: 30rpx 0;
    padding: 0 20rpx;
  }

  .confirm-btn {
    width: 100%;
    height: 88rpx;
    background: #ff4d4f;
    border-radius: 44rpx;
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 修改底部按钮样式 */
.footer {
  .apply-btn {
    &.apply-btn-disabled {
      opacity: 0.6;
    }
  }
}
</style> 