<template>
  <view class="container">
    <!-- 定位信息和地图 -->
    <view class="fixed-section">
      <view class="location-info">
        <view class="location-text">
          <text>您当前所在位置</text>
          <text>{{ nearestStation }}</text>
        </view>
        <button class="location-button" @tap="locateMe">使用定位</button>
      </view>
 

      <view class="map-container">
        <map 
         :style="{ width: '100%', height: selectedMarker ? '250px' : '300px' }"" 
          :latitude="latitude" 
          :longitude="longitude" 
          :scale="scale" 
          :markers="markers" 
          @markertap="onMarkerTap">
        </map>
		<view v-if="selectedMarker" class="navigation-bar">
		    <text class="station-name">导航到 {{ selectedMarker.callout.content }}</text>
		    <button class="navigate-button" @tap="navigateToMarker">点击导航</button>
		  </view>
      </view>
 <!-- 导航弹窗 -->
     
      <!-- 表头 -->
      <view class="table-header">
        <view class="header-cell">小区名称</view>
        <view class="header-cell">服务人员</view>
        <view class="header-cell">电话</view>
      </view>
    </view>

    <!-- 滚动数据列表 -->
    <scroll-view class="scroll-view" scroll-y="true">
      <view v-for="(item, index) in datalist" :key="index" class="table-row" :data-id="item.id">
        <view class="table-cell community-name">
          {{ item.third_level_name }}
        </view>
        <view class="table-cell service-staff">
          {{ item.realname }}
        </view>
        <view class="table-cell service-tel">
          <image src="/static/img/tongxunlutle.png" mode="aspectFit" class="call-icon" @click="call(item.tel)" />
        </view>
      </view>
      <nodata v-if="nodata"></nodata>
      <nomore v-if="nomore"></nomore>
    </scroll-view>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>
<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      keyword: '', // 搜索关键词
      datalist: [], // 用于存放人员数据
      type: "",
      nodata: false,
      curTopIndex: -1,
      index: 0,
      curCid: 0, // 当前选择的小区ID
      nomore: false,
      pagenum: 1,
      regionList: [], // 存放区县列表对象
      regionNames: [], // 存放区县名称的字符串数组，供 picker 使用
      subRegionList: [], // 存放小区列表对象
      subRegionNames: [], // 存放小区名称的字符串数组，供 picker 使用
      selectedRegion: {}, // 用户选择的区县
      selectedSubRegion: {}, // 用户选择的小区
      clist: [], // 存放API返回的数据，确保其定义并初始化
      markers: [], // 地图标记点
      latitude: 39.9042, // 默认中心纬度 (北京)
      longitude: 116.4074, // 默认中心经度 (北京)
      scale: 12, // 地图缩放级别
      nearestStation: '*****小区', // 最近的服务站信息，默认显示
	   selectedMarker: null, // 当前选中的标记
	      showNavigation: false, // 控制导航弹窗显示
      userMarkerId: 'user_location' // 用户位置标记的ID
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.type = this.opt.type || '';
    this.getdata();
    this.getNearbyRegions(); // 加载地图标记点
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdatalist(true);
    }
  },
  methods: {
    getdata: function () {
      var that = this;
      var nowcid = that.opt.cid;
      var bid = that.opt.bid || 0;
      if (!nowcid) nowcid = '';
      that.loading = true;

      app.get('Apitongxunlu/peocategory', { cid: nowcid, bid: bid }, function (res) {
        that.loading = false;

        if (res.data && res.data.length > 0) {
          that.regionList = res.data.map(item => ({
            id: item.id,
            name: item.name,
            children: item.children || []
          }));
          that.regionNames = res.data.map(item => item.name); // 提取区县名称供 picker 使用

          that.clist = res.data;

          if (that.regionList.length > 0) {
            that.selectedRegion = that.regionList[0];
            // 递归获取所有子层级的小区列表
            that.subRegionList = that.getAllChildren(that.regionList[0].children);
            that.subRegionNames = that.subRegionList.map(item => item.name); // 提取小区名称供 picker 使用
          }

          that.getdatalist();
        }

        that.loaded();
      });
    },

    onRegionChange: function (e) {
      var selectedRegionIndex = e.detail.value;
      this.selectedRegion = this.regionList[selectedRegionIndex];

      // 使用递归函数处理子层级小区
      this.subRegionList = this.getAllChildren(this.selectedRegion.children);
      this.subRegionNames = this.subRegionList.map(item => item.name); // 更新小区名称供 picker 使用

      this.selectedSubRegion = {};
    },

    onSubRegionChange: function (e) {
      var selectedSubRegionIndex = e.detail.value;
      this.selectedSubRegion = this.subRegionList[selectedSubRegionIndex];
      this.curCid = this.selectedSubRegion.id;

      // 调用接口获取选定小区的人员数据
      this.getdatalist();
    },

    // 递归获取所有子层级的小区
    getAllChildren: function (children) {
      let allChildren = [];
      children.forEach(child => {
        allChildren.push({ id: child.id, name: child.name });
        if (child.children && child.children.length > 0) {
          allChildren = allChildren.concat(this.getAllChildren(child.children));
        }
      });
      return allChildren;
    },

    getdatalist: function (loadmore) {
      if (!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
      }
      var that = this;
      var pagenum = that.pagenum;
      var cid = that.curCid; // 使用选择的小区ID
      var bid = that.opt.bid ? that.opt.bid : '';
      var order = that.order;
      var keyword = that.keyword; // 关键字
      var field = that.field;
      that.loading = true;
      that.nodata = false;
      that.nomore = false;
      var latitude = that.latitude;
      var longitude = that.longitude;

      app.post('Apitongxunlu/selectpeople2', {
        pagenum: pagenum,
        keyword: keyword,
        field: field,
        order: order,
        cid: cid,
        bid: bid,
        type: 'list',
        longitude: longitude,
        latitude: latitude
      }, function (res) {
        that.loading = false;
        var data = res.data;

        // 遍历每个数据项并提取服务站信息
        data.forEach(item => {
          if (item.second_level_name) {
            item.service_area = item.third_level_name; // 使用 second_level_name 作为服务站信息
          }
        });

        if (pagenum == 1) {
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
        } else {
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
   loadMoreData: function () {
       console.log('滚动到底部，加载更多');
       if (!this.nomore && !this.nodata) {
         this.pagenum += 1;
         this.getdatalist(true);
       }
     },
    searchChange: function (e) {
      this.keyword = e.detail.value; // 更新搜索关键字
    },

    searchConfirm: function () {
      this.getdatalist(false); // 当用户确认搜索时调用，重新加载数据
    },

    call: function (tel) {
      wx.makePhoneCall({
        phoneNumber: tel
      });
    },

    // 获取附近的服务区域并添加到地图标记点中
    getNearbyRegions: function () {
      var that = this;
      app.get('Apitongxunlu/peocategory', {}, (res) => {
        console.log("接口返回的数据：", res.data); // 输出接口返回的数据
        if (res.data && res.data.length > 0) {
          const firstRegion = res.data[0]; // 获取第一个区域的children
          const children = firstRegion.children || [];

          // 确保地图中心点有坐标值
          if (children.length > 0) {
            that.latitude = parseFloat(children[0].latitude);
            that.longitude = parseFloat(children[0].longitude);
          }

          that.markers = children.map((item) => {
            console.log("子区域数据：", item); // 输出每个子区域的数据
            return {
              id: item.id,
              latitude: parseFloat(item.latitude), // 确保经纬度是浮点数
              longitude: parseFloat(item.longitude),
              iconPath: item.pic || '/static/img/addre.png', // 确保有图标路径
              width: 40,
              height: 40,
              callout: {
                content: item.name,
                color: '#ffffff',
                fontSize: 14,
                borderRadius: 10,
                bgColor: '#333',
                display: 'ALWAYS'
              }
            };
          });

          // 根据当前位置计算最近的服务站
          that.calculateNearestStation();
        }
      });
    },

     onMarkerTap: function (e) {
       console.log("标记点点击事件触发，ID:", e.detail.markerId);
       const tappedMarker = this.markers.find(marker => marker.id === e.detail.markerId);
       if (tappedMarker) {
		   this.curCid = e.detail.markerId; // 获取标记点的 ID 作为 curCid
		         this.getdatalist(false); // 触发获取列表数据的方法

         this.selectedMarker = tappedMarker; // 保存选中的标记
         this.showNavigation = true; // 显示导航弹窗
       }
     },
   
     // 关闭导航弹窗
     closeNavigation: function () {
       this.showNavigation = false;
       this.selectedMarker = null;
     },
   
     // 一键导航方法
     navigateToMarker: function () {
       if (this.selectedMarker) {
         wx.openLocation({
           latitude: this.selectedMarker.latitude,
           longitude: this.selectedMarker.longitude,
           name: this.selectedMarker.callout.content || '服务站',
           address: this.selectedMarker.address || '', // 如果有地址信息，可以传递
           scale: 18 // 缩放级别，可根据需要调整
         });
         this.closeNavigation();
       }
     },

    // 获取用户当前位置并更新地图中心点
    locateMe: function () {
      var that = this;
      wx.getLocation({
        type: 'gcj02', // 使用 'gcj02' 以便地图组件能正确解析
        success(res) {
          that.latitude = res.latitude;
          that.longitude = res.longitude;

          // 删除之前的用户位置标记（如果存在）
          that.markers = that.markers.filter(marker => marker.id !== that.userMarkerId);

          // 添加当前定位的标记点
          that.markers.push({
            id: that.userMarkerId,
            latitude: res.latitude,
            longitude: res.longitude,
            iconPath: '/static/img/userloc.png', // 用户定位图标
            width: 40,
            height: 40,
            callout: {
              content: '当前位置',
              color: '#ffffff',
              fontSize: 14,
              borderRadius: 10,
              bgColor: '#1aad19',
              display: 'ALWAYS'
            }
          });

          // 更新列表数据为当前位置的附近人员信息
          that.curCid = ''; // 清空 curCid 以便重新获取数据
          that.getdatalist(false);

          // 重新计算最近的服务站
          that.calculateNearestStation();
        },
        fail() {
          wx.showToast({
            title: '无法获取定位信息',
            icon: 'none'
          });
        }
      });
    },

    // 计算最近的服务站
    calculateNearestStation: function () {
      var that = this;
      if (that.markers.length > 0) {
        let nearest = that.markers[0];
        let minDistance = that.getDistance(nearest.latitude, nearest.longitude, that.latitude, that.longitude);
        that.markers.forEach(marker => {
          const distance = that.getDistance(marker.latitude, marker.longitude, that.latitude, that.longitude);
          if (distance < minDistance && marker.id !== that.userMarkerId) { // 忽略用户自身位置标记
            minDistance = distance;
            nearest = marker;
          }
        });
        that.nearestStation = nearest.callout.content || '*****小区';
      }
    },

    // 计算两个坐标之间的距离
    getDistance: function (lat1, lon1, lat2, lon2) {
      const radLat1 = this.toRad(lat1);
      const radLat2 = this.toRad(lat2);
      const a = radLat1 - radLat2;
      const b = this.toRad(lon1) - this.toRad(lon2);
      const s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
        Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
      return s * 6378137; // 地球半径，单位为米
    },

    toRad: function (d) {
      return d * Math.PI / 180.0;
    }
  }
};
</script>

<style>
/* 样式代码保持与之前一致 */
.container {
  padding: 16rpx;
  background-color: #f7f7f7;
}

.header-image {
  width: 100%;
  margin-bottom: 20rpx;
}

.header-img {
  width: 100%;
  border-radius: 12rpx;
}

/* 定位信息的样式 */
.location-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background-color: #ffffff;
  border: 1rpx solid #e6e6e6;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

/* 定位按钮的样式 */
.location-button {
  background-color: #1aad19;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
}

/* 地图样式 */
.map-container {
  margin-bottom: 20rpx;
  position: relative;
}

/* 其他样式保持不变 */
.table-header {
  display: flex;
  background-color: #f0f0f0;
  padding: 10rpx 20rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #dcdcdc;
}

.header-cell {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
}

.table-row {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.table-cell {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.call-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

/* 固定定位信息和地图部分 */
.fixed-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #fff;
}

/* 定位信息的样式 */
.location-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background-color: #ffffff;
  border: 1rpx solid #e6e6e6;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

/* 地图样式 */
.map-container {
  width: 100%;
  height: 300px;
  margin-bottom: 20rpx;
}

/* 表头固定 */
.table-header {
  display: flex;
  background-color: #f0f0f0;
  padding: 10rpx 20rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #dcdcdc;
}

.header-cell {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
}

/* 滚动区域的样式 */
.scroll-view {
  margin-top: 420rpx; /* 确保滚动区域在固定部分下方 */
  height: calc(100vh - 420rpx); /* 动态计算滚动视图高度 */
  overflow-y: scroll;
  padding: 16rpx;
}

.table-row {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.table-cell {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.call-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

/* 样式代码保持与之前一致 */
.container {
  padding: 16rpx;
  background-color: #f7f7f7;
}

.header-image {
  width: 100%;
  margin-bottom: 20rpx;
}

.header-img {
  width: 100%;
  border-radius: 12rpx;
}

/* 定位信息的样式 */
.location-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background-color: #ffffff;
  border: 1rpx solid #e6e6e6;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

/* 定位按钮的样式 */
.location-button {
  background-color: #1aad19;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
}

/* 地图样式 */
.map-container {
  margin-bottom: 20rpx;
  position: relative;
}

.location-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background-color: #ffffff;
  border: 1rpx solid #e6e6e6;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.map-container {
  margin-bottom: 10rpx;
}

.navigation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx;
  background-color: #fff;
  border: 1rpx solid #ddd;
  margin-bottom: 10rpx;
}

.station-name {
  font-size: 32rpx;
  color: #333;
  flex: 1;  /* Ensures the text takes up the remaining space */
}

.navigate-button {
  background-color: #f90;
  color: white;
  padding: 5rpx 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  white-space: nowrap; /* Prevents button text from wrapping */
}

.table-header, .table-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx;
  background-color: #f0f0f0;
  margin-bottom: 5rpx;
}

.header-cell, .table-cell {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
}

.call-icon {
  width: 40rpx;
  height: 40rpx;
}


</style>
