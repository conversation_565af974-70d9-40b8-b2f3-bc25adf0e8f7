<template>
	<view class="camera-container">
		<!-- 拍摄模式 -->
		<view v-if="!capturedImage" class="camera-mode">
			<!-- 相机预览区域 -->
			<!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ -->
			<camera 
				class="camera-preview"
				:device-position="devicePosition"
				:flash="flashMode"
				@error="onCameraError"
				@initdone="onCameraInit"
			>
			</camera>
			<!-- #endif -->
			
			<!-- #ifdef H5 -->
			<!-- H5环境下显示替代预览区域 -->
			<view class="camera-preview h5-camera-placeholder">
				<view class="h5-camera-content">
					<text class="h5-camera-text">相机预览</text>
					<text class="h5-camera-tip">点击拍照按钮选择图片</text>
				</view>
			</view>
			<!-- #endif -->

			<!-- 顶部状态栏 -->
			<view class="status-bar">
				<view class="back-btn" @touchstart="goBack" @tap="goBack">
					<text class="back-icon">←</text>
				</view>
				<text class="page-title">舌诊拍摄</text>
				<view class="flash-btn" :class="{ active: flashOn }" @touchstart="toggleFlash" @tap="toggleFlash">
					<text class="flash-icon">{{ flashOn ? '💡' : '🔦' }}</text>
				</view>
			</view>

			<!-- 拍摄指导区域 -->
			<view class="guide-overlay">
				<view class="guide-frame">
					<view class="frame-corner corner-tl"></view>
					<view class="frame-corner corner-tr"></view>
					<view class="frame-corner corner-bl"></view>
					<view class="frame-corner corner-br"></view>
					<view class="frame-center">
						<text class="guide-text">请将舌头置于框内</text>
						<view class="pulse-dot"></view>
					</view>
				</view>
			</view>

			<!-- 拍摄提示区域 -->
			<view class="tips-section">
				<view class="tips-container">
					<view class="tip-item" v-for="(tip, index) in shootingTips" :key="index">
						<view class="tip-icon">{{ tip.icon }}</view>
						<text class="tip-text">{{ tip.text }}</text>
					</view>
				</view>
			</view>

			<!-- 控制区域 -->
			<view class="control-section">
				<view class="control-container">
					<!-- 选择图片 -->
					<view class="control-item">
						<view class="control-btn secondary" @touchstart="chooseImage" @tap="chooseImage">
							<image class="control-icon-img" :src="pre_url + '/static/img/xuanzetupian.png'" mode="aspectFit"></image>
						</view>
						<text class="control-label">选择图片</text>
					</view>
					
					<!-- 拍照按钮 -->
					<view class="control-item capture">
						<view class="control-btn primary" :class="{ capturing: captureLoading }" @touchstart="takePhoto" @tap="takePhoto">
							<view class="capture-ring" v-if="!captureLoading"></view>
							<view class="capture-dot"></view>
						</view>
						<text class="control-label">拍照</text>
					</view>
					
					<!-- 翻转相机 -->
					<view class="control-item">
						<view class="control-btn secondary" @touchstart="flipCamera" @tap="flipCamera" :class="{ 'active': isFlipping }">
							<image class="control-icon-img" :src="pre_url + '/static/img/fanzhuanxiangji.png'" mode="aspectFit"></image>
						</view>
						<text class="control-label">翻转相机</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 预览模式 -->
		<view v-else class="preview-mode">
			<!-- 顶部标题 -->
			<view class="preview-header">
				<text class="preview-title">预览图片</text>
				<text class="preview-subtitle">请确认图片清晰度</text>
			</view>

			<!-- 图片预览区域 -->
			<view class="image-preview">
				<image 
					:src="capturedImage" 
					class="preview-image"
					mode="aspectFit"
					@error="onImageError"
				/>
				
				<!-- 分析指示器 -->
				<view class="analysis-indicators">
					<view 
						v-for="(point, index) in analysisPoints" 
						:key="index"
						class="analysis-point"
						:style="{ left: point.x + '%', top: point.y + '%' }"
					>
						<view class="point-ring"></view>
						<view class="point-dot"></view>
					</view>
				</view>

				<!-- 质量检测结果 -->
				<view class="quality-check">
					<view class="quality-item" v-for="(check, index) in qualityChecks" :key="index">
						<view class="quality-icon" :class="check.status">
							<text>{{ check.status === 'pass' ? '✓' : '!' }}</text>
						</view>
						<text class="quality-text">{{ check.text }}</text>
					</view>
				</view>
			</view>

			<!-- 底部操作区域 -->
			<view class="preview-actions">
				<view class="action-btn secondary" @touchstart="retakePhoto" @tap="retakePhoto">
					<view class="btn-content">
						<text class="btn-icon">🔄</text>
						<text class="btn-text">重新拍摄</text>
					</view>
				</view>
				
				<view class="action-btn primary" @touchstart="confirmPhoto" @tap="confirmPhoto">
					<view class="btn-content">
						<text class="btn-icon">✨</text>
						<text class="btn-text">开始分析</text>
					</view>
					<view class="btn-glow"></view>
				</view>
			</view>
		</view>

		<!-- 加载遮罩 -->
		<view v-if="isLoading" class="loading-overlay">
			<view class="loading-content">
				<view class="loading-spinner">
					<view class="spinner-ring"></view>
					<view class="spinner-dot"></view>
				</view>
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ShezhenCamera',
	data() {
		return {
			// 云资源前缀URL
			pre_url: '',
			
			// 相机状态
			cameraReady: false,
			capturedImage: '',
			captureLoading: false,
			flashOn: false,
			flashMode: 'off',
			devicePosition: 'back', // 相机位置：back(后置) / front(前置)
			isFlipping: false, // 是否正在切换相机
			
			// 加载状态
			isLoading: false,
			loadingText: '正在拍摄...',
			
			// 2025-01-27 17:00:00,010-INFO-[camera][data_001] 添加防重复调用标识
			isProcessingPayment: false, // 防止重复处理支付流程
			
			// 拍摄提示
			shootingTips: [
				{ icon: '💡', text: '保持充足光线' },
				{ icon: '📱', text: '手机保持稳定' },
				{ icon: '👅', text: '舌头完整露出' },
				{ icon: '🎯', text: '对准拍摄框' }
			],
			
			// 分析点位
			analysisPoints: [
				{ x: 30, y: 25 },
				{ x: 70, y: 25 },
				{ x: 50, y: 45 },
				{ x: 35, y: 65 },
				{ x: 65, y: 65 }
			],

			// 质量检测结果
			qualityChecks: [
				{ text: '图片清晰度', status: 'pass' },
				{ text: '光线充足', status: 'pass' },
				{ text: '舌头完整', status: 'pass' },
				{ text: '角度适宜', status: 'warning' }
			],

			// 舌诊配置信息
			configData: {}
		}
	},
	onLoad() {
		console.log('2025-01-26 11:00:00,001-INFO-[camera][onLoad_001] 舌诊拍摄页面加载完成');
		
		// 2025-01-27 17:00:00,021-INFO-[camera][onLoad_001_1] 重置处理标识
		this.isProcessingPayment = false;
		
		// 初始化云资源前缀URL
		const app = getApp();
		this.pre_url = app.globalData.pre_url || '';
		
		// 初始化相机权限检查
		this.checkCameraPermission();
		
		// 获取舌诊配置信息
		this.getSheZhenConfig();
	},
	
	// 2025-01-27 17:00:00,022-INFO-[camera][onUnload_001] 页面卸载时重置状态
	onUnload() {
		console.log('2025-01-27 17:00:00,023-INFO-[camera][onUnload_002] 舌诊拍摄页面卸载，重置处理标识');
		this.isProcessingPayment = false;
	},
	methods: {
		/**
		 * 检查相机权限
		 */
		checkCameraPermission() {
			console.log('2025-01-26 11:00:00,002-INFO-[camera][checkCameraPermission_001] 检查相机权限');
			
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
			// 只在小程序环境下检查相机权限
			uni.authorize({
				scope: 'scope.camera',
				success: () => {
					console.log('2025-01-26 11:00:00,003-INFO-[camera][checkCameraPermission_002] 相机权限获取成功');
				},
				fail: () => {
					console.log('2025-01-26 11:00:00,004-ERROR-[camera][checkCameraPermission_003] 相机权限获取失败');
					uni.showModal({
						title: '权限提示',
						content: '需要相机权限才能进行舌诊拍摄',
						success: (res) => {
							if (res.confirm) {
								uni.openSetting();
							}
						}
					});
				}
			});
			// #endif
			
			// #ifdef H5
			// H5环境下直接设置相机就绪状态
			console.log('2025-01-26 11:00:00,005-INFO-[camera][checkCameraPermission_004] H5环境下跳过权限检查');
			this.cameraReady = true;
			// #endif
		},

		/**
		 * 相机初始化完成
		 */
		onCameraInit() {
			console.log('2025-01-26 11:00:00,005-INFO-[camera][onCameraInit_001] 相机初始化完成');
			this.cameraReady = true;
		},

		/**
		 * 相机错误处理
		 */
		onCameraError(error) {
			console.error('2025-01-26 11:00:00,006-ERROR-[camera][onCameraError_001] 相机错误:', error);
			uni.showToast({
				title: '相机启动失败',
				icon: 'none'
			});
		},

		/**
		 * 选择图片
		 */
		chooseImage() {
			console.log('2025-01-26 11:00:00,023-INFO-[camera][chooseImage_001] 选择图片按钮被点击');
			
			// 添加触觉反馈
			uni.vibrateShort();
			
			const app = getApp();
			if (app && app.chooseImage) {
				// 直接使用app的chooseImage方法，它会处理图片选择和上传
				app.chooseImage((imageUrls) => {
					console.log('2025-01-26 11:00:00,024-INFO-[camera][chooseImage_002] 选择并上传图片成功:', imageUrls);
					
					if (imageUrls && imageUrls.length > 0) {
						// 获取上传后的图片链接
						const imageUrl = imageUrls[0];
						
						// 先展示一下图片
						this.capturedImage = imageUrl;
						
						// 检查图片质量
						this.checkImageQuality();
						
						// 显示选择成功提示
						uni.showToast({
							title: '图片选择成功',
							icon: 'success',
							duration: 1500
						});
					}
				}, 1);
			} else {
				// 如果app方法不可用，使用原生选择图片
				console.log('2025-01-26 11:00:00,025-INFO-[camera][chooseImage_003] app.chooseImage方法不存在，使用原生方法');
				
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: (res) => {
						console.log('2025-01-26 11:00:00,026-INFO-[camera][chooseImage_004] 选择图片成功:', res.tempFilePaths);
						
						// 只显示临时图片，不进行上传
						this.capturedImage = res.tempFilePaths[0];
						
						// 检查图片质量
						this.checkImageQuality();
						
						// 显示选择成功提示
						uni.showToast({
							title: '图片选择成功，请点击开始分析',
							icon: 'success',
							duration: 2000
						});
					},
					fail: (err) => {
						console.error('2025-01-26 11:00:00,027-ERROR-[camera][chooseImage_005] 选择图片失败:', err);
						
						uni.showToast({
							title: '选择图片失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			}
		},

		/**
		 * 拍摄照片
		 */
		takePhoto() {
			console.log('2025-01-26 11:00:00,008-INFO-[camera][takePhoto_001] 拍照按钮被点击');
			console.log('2025-01-26 11:00:00,008-INFO-[camera][takePhoto_002] 相机状态检查 - cameraReady:', this.cameraReady, 'captureLoading:', this.captureLoading);
			
			if (!this.cameraReady || this.captureLoading) {
				console.log('2025-01-26 11:00:00,007-INFO-[camera][takePhoto_003] 相机未就绪或正在拍摄中');
				uni.showToast({
					title: '相机未准备好',
					icon: 'none'
				});
				return;
			}

			console.log('2025-01-26 11:00:00,008-INFO-[camera][takePhoto_004] 开始拍摄照片');
			
			// 添加触觉反馈
			uni.vibrateShort();
			
			// #ifdef H5
			// H5环境下直接调用选择图片
			console.log('2025-01-26 11:00:00,009-INFO-[camera][takePhoto_005] H5环境下调用选择图片');
			this.chooseImage();
			return;
			// #endif
			
			this.captureLoading = true;
			this.isLoading = true;
			this.loadingText = '正在拍摄...';

			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
			// 创建相机上下文
			const cameraContext = uni.createCameraContext();
			
			cameraContext.takePhoto({
				quality: 'high',
				success: (res) => {
					console.log('2025-01-26 11:00:00,009-INFO-[camera][takePhoto_005] 拍摄成功:', res.tempImagePath);
					
					this.capturedImage = res.tempImagePath;
					this.captureLoading = false;
					this.isLoading = false;
					
					// 检查图片质量
					this.checkImageQuality();
				},
				fail: (err) => {
					console.error('2025-01-26 11:00:00,010-ERROR-[camera][takePhoto_006] 拍摄失败:', err);
					
					this.captureLoading = false;
					this.isLoading = false;
					
					uni.showToast({
						title: '拍摄失败，请重试',
						icon: 'none'
					});
				}
			});
			// #endif
		},

		/**
		 * 检查图片质量
		 */
		checkImageQuality() {
			console.log('2025-01-26 11:00:00,011-INFO-[camera][checkImageQuality_001] 开始检查图片质量');
			
			// 模拟质量检测
			setTimeout(() => {
				// 随机生成质量检测结果
				this.qualityChecks = [
					{ text: '图片清晰度', status: Math.random() > 0.2 ? 'pass' : 'warning' },
					{ text: '光线充足', status: Math.random() > 0.3 ? 'pass' : 'warning' },
					{ text: '舌头完整', status: Math.random() > 0.1 ? 'pass' : 'warning' },
					{ text: '角度适宜', status: Math.random() > 0.4 ? 'pass' : 'warning' }
				];
				
				console.log('2025-01-26 11:00:00,012-INFO-[camera][checkImageQuality_002] 图片质量检测完成');
			}, 1000);
		},

		/**
		 * 重新拍摄
		 */
		retakePhoto() {
			console.log('2025-01-26 11:00:00,013-INFO-[camera][retakePhoto_001] 重新拍摄按钮被点击');
			
			// 添加触觉反馈
			uni.vibrateShort();
			
			this.capturedImage = '';
			this.qualityChecks = [];
			
			uni.showToast({
				title: '重新拍摄',
				icon: 'none',
				duration: 1000
			});
		},

		/**
		 * 确认照片并跳转到分析页面
		 */
		confirmPhoto() {
			console.log('2025-01-26 11:00:00,014-INFO-[camera][confirmPhoto_001] 确认照片按钮被点击');
			
			// 添加触觉反馈
			uni.vibrateShort();
			
			if (!this.capturedImage) {
				uni.showToast({
					title: '请先拍摄照片',
					icon: 'none'
				});
				return;
			}
			
			// 判断图片是否已经是服务器URL
			if (this.capturedImage.startsWith('http://') || this.capturedImage.startsWith('https://')) {
				// 已经是服务器URL，直接进行支付检查
				console.log('2025-01-27 11:00:00,070-INFO-[camera][confirmPhoto_002] 图片已是服务器URL，直接进行支付检查');
				this.checkPaymentInfo(this.capturedImage);
			} else {
				// 是本地路径，需要先上传
				console.log('2025-01-27 11:00:00,071-INFO-[camera][confirmPhoto_003] 图片为本地路径，需要先上传');
				// 显示上传进度
				this.isLoading = true;
				this.loadingText = '正在上传图片...';
				
				// 上传图片到服务器
				this.uploadImageToServer();
			}
		},
		
		/**
		 * 上传图片到服务器并获取分析结果
		 */
		uploadImageToServer() {
			console.log('2025-01-26 11:00:00,015-INFO-[camera][uploadImageToServer_001] 开始上传图片:', this.capturedImage);
			
			const app = getApp();
			const that = this;
			
			// 显示上传中提示
			uni.showLoading({
				title: '上传中...'
			});
			
			// 使用uni.uploadFile上传图片
			uni.uploadFile({
				url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + 
					'/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,
				filePath: this.capturedImage,
				name: 'file',
				success: (res) => {
					console.log('2025-01-26 11:00:00,016-INFO-[camera][uploadImageToServer_002] 上传图片成功:', res);
					
					uni.hideLoading();
					this.isLoading = false;
					
					// 解析响应数据
					try {
						const data = JSON.parse(res.data);
						
						if (data.status == 1) {
							console.log('2025-01-26 11:00:00,017-INFO-[camera][uploadImageToServer_003] 获取图片URL成功:', data.url);
							
							// 获取到上传后的图片URL，跳转到分析页面
							this.navigateToAnalysisPage(data.url);
						} else {
							console.error('2025-01-26 11:00:00,018-ERROR-[camera][uploadImageToServer_004] 上传图片失败:', data.msg);
							
							uni.showToast({
								title: data.msg || '图片上传失败',
								icon: 'none',
								duration: 2000
							});
							
							this.isLoading = false;
						}
					} catch (e) {
						console.error('2025-01-26 11:00:00,019-ERROR-[camera][uploadImageToServer_005] 解析响应数据失败:', e);
						
						uni.showToast({
							title: '图片上传失败',
							icon: 'none',
							duration: 2000
						});
						
						this.isLoading = false;
					}
				},
				fail: (err) => {
					console.error('2025-01-26 11:00:00,020-ERROR-[camera][uploadImageToServer_006] 上传图片请求失败:', err);
					
					uni.hideLoading();
					this.isLoading = false;
					
					uni.showToast({
						title: '图片上传失败，请重试',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		/**
		 * 跳转到分析页面
		 */
		navigateToAnalysisPage(imageUrl) {
			console.log('2025-01-26 11:00:00,021-INFO-[camera][navigateToAnalysisPage_001] 跳转到分析页面，图片链接:', imageUrl);
			
			// 检查支付信息并确认支付
			this.checkPaymentInfo(imageUrl);
		},
		
		/**
		 * 检查支付信息并确认支付
		 */
		checkPaymentInfo(imageUrl) {
			console.log('2025-01-27 11:00:00,050-INFO-[camera][checkPaymentInfo_001] 开始检查支付信息');
			
			// 2025-01-27 17:00:00,011-INFO-[camera][checkPaymentInfo_001_1] 防止重复处理支付流程
			if (this.isProcessingPayment) {
				console.log('2025-01-27 17:00:00,012-INFO-[camera][checkPaymentInfo_001_2] 正在处理支付流程，忽略重复调用');
				return;
			}
			
			this.isProcessingPayment = true; // 设置处理中标识
			
			const app = getApp();
			
			// 显示加载提示
			uni.showLoading({
				title: '获取支付信息...',
				mask: true
			});
			
			// 请求支付信息
			app.post('ApiSheZhen/getPayInfo', {
				image_url: imageUrl,
				use_free: 1  // 默认尝试使用免费次数
			}, (response) => {
				uni.hideLoading();
				this.isProcessingPayment = false; // 重置处理标识
				console.log('2025-01-27 11:00:00,051-INFO-[camera][checkPaymentInfo_002] 支付信息获取成功:', response);
				
				// 2025-01-27 11:00:00,052-INFO-[camera][checkPaymentInfo_003] 增强判断逻辑，同时支持code和status返回
				if (response && (response.status === 1 || response.code === 1) && response.data) {
					const payData = response.data;
					
					// 判断是否可以免费使用
					if (payData.is_free) {
						console.log('2025-01-27 11:00:00,052-INFO-[camera][checkPaymentInfo_003] 可以免费使用，直接跳转分析页面');
						this.proceedToAnalysis(imageUrl);
						return;
					}
					
					// 2025-01-27 14:00:00,001-INFO-[camera][checkPaymentInfo_003_1] 检查价格和用户余额
					const price = parseFloat(payData.price) || 0;
					const userMoney = parseFloat(payData.userinfo?.money) || 0;
					
					// 如果价格为0，视为可免费使用
					if (price <= 0) {
						console.log('2025-01-27 14:00:00,002-INFO-[camera][checkPaymentInfo_003_2] 价格为0，视为免费使用');
						this.proceedToAnalysis(imageUrl);
						return;
					}
					
					// 显示支付确认对话框
					this.showPaymentConfirmDialog(payData, imageUrl);
					
				} else {
					console.error('2025-01-27 11:00:00,053-ERROR-[camera][checkPaymentInfo_004] 支付信息获取失败:', response);
					uni.showModal({
						title: '获取支付信息失败',
						content: response?.msg || '无法获取支付信息，请重试',
						showCancel: true,
						confirmText: '重试',
						cancelText: '返回',
						success: (res) => {
							if (res.confirm) {
								this.checkPaymentInfo(imageUrl);
							} else {
								uni.navigateBack();
							}
						}
					});
				}
			}, (error) => {
				uni.hideLoading();
				this.isProcessingPayment = false; // 重置处理标识
				console.error('2025-01-27 11:00:00,054-ERROR-[camera][checkPaymentInfo_005] 支付信息请求失败:', error);
				
				uni.showModal({
					title: '网络错误',
					content: '获取支付信息失败，请检查网络连接后重试',
					showCancel: true,
					confirmText: '重试',
					cancelText: '返回',
					success: (res) => {
						if (res.confirm) {
							this.checkPaymentInfo(imageUrl);
						} else {
							uni.navigateBack();
						}
					}
				});
			});
		},
		
		/**
		 * 显示支付确认对话框
		 */
		showPaymentConfirmDialog(payData, imageUrl) {
			console.log('2025-01-27 11:00:00,055-INFO-[camera][showPaymentConfirmDialog_001] 显示支付确认对话框');
			
			// 2025-01-27 14:00:00,003-INFO-[camera][showPaymentConfirmDialog_001_1] 保证数字类型并设置默认值
			const price = parseFloat(payData.price) || 0;
			const userMoney = parseFloat(payData.userinfo?.money) || 0;
			const formattedPrice = price.toFixed(2);
			const formattedBalance = userMoney.toFixed(2);
			
			console.log('2025-01-27 14:00:00,004-INFO-[camera][showPaymentConfirmDialog_001_2] 处理后的价格:', price, '用户余额:', userMoney);
			
			// 2025-01-27 17:00:00,013-INFO-[camera][showPaymentConfirmDialog_001_3] 获取正确的充值页面路径
			let rechargeUrl = '/pagesExb/money/recharge'; // 默认充值路径
			
			// 从配置数据中获取充值页面路径
			if (this.configData && this.configData.recharge_url) {
				rechargeUrl = this.configData.recharge_url;
				console.log('2025-01-27 17:00:00,014-INFO-[camera][showPaymentConfirmDialog_001_4] 使用配置中的充值路径:', rechargeUrl);
			} else {
				console.log('2025-01-27 17:00:00,015-INFO-[camera][showPaymentConfirmDialog_001_5] 配置中无充值路径，使用默认路径:', rechargeUrl);
			}
			
			// 检查余额是否足够
			if (userMoney < price) {
				// 余额不足，提示充值
				uni.showModal({
					title: '余额不足',
					content: `舌诊分析费用为${formattedPrice}元，当前余额${formattedBalance}元，是否前往充值？`,
					confirmText: '去充值',
					cancelText: '返回',
					success: (res) => {
						if (res.confirm) {
							console.log('2025-01-27 11:00:00,056-INFO-[camera][showPaymentConfirmDialog_002] 用户选择去充值，路径:', rechargeUrl);
							// 跳转到充值页面，使用配置的路径
							uni.navigateTo({
								url: rechargeUrl,
								fail: (err) => {
									console.error('2025-01-27 17:00:00,016-ERROR-[camera][showPaymentConfirmDialog_002_1] 跳转充值页面失败:', err);
									// 如果跳转失败，尝试使用默认路径
									uni.navigateTo({
										url: '/pagesExb/money/recharge'
									});
								}
							});
						} else {
							console.log('2025-01-27 11:00:00,057-INFO-[camera][showPaymentConfirmDialog_003] 用户取消支付');
							uni.navigateBack();
						}
					}
				});
			} else {
				// 余额充足，显示确认支付对话框
				uni.showModal({
					title: '费用确认',
					content: `舌诊分析费用为${formattedPrice}元，当前余额${formattedBalance}元，是否确认使用余额支付？`,
					confirmText: '确认支付',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							console.log('2025-01-27 11:00:00,058-INFO-[camera][showPaymentConfirmDialog_004] 用户确认支付');
							
							// 创建支付订单
							this.createPayOrder(imageUrl);
						} else {
							console.log('2025-01-27 11:00:00,059-INFO-[camera][showPaymentConfirmDialog_005] 用户取消支付');
							
							// 显示取消后的二次确认，提供返回和充值选项
							uni.showModal({
								title: '已取消支付',
								content: '您可以返回上一页或前往充值',
								confirmText: '去充值',
								cancelText: '返回',
								success: (res2) => {
									if (res2.confirm) {
										// 跳转到充值页面，使用配置的路径
										console.log('2025-01-27 17:00:00,017-INFO-[camera][showPaymentConfirmDialog_005_1] 用户选择去充值，路径:', rechargeUrl);
										uni.navigateTo({
											url: rechargeUrl,
											fail: (err) => {
												console.error('2025-01-27 17:00:00,018-ERROR-[camera][showPaymentConfirmDialog_005_2] 跳转充值页面失败:', err);
												// 如果跳转失败，尝试使用默认路径
												uni.navigateTo({
													url: '/pagesExb/money/recharge'
												});
											}
										});
									} else {
										uni.navigateBack();
									}
								}
							});
						}
					}
				});
			}
		},
		
		/**
		 * 创建支付订单
		 */
		createPayOrder(imageUrl) {
			console.log('2025-01-27 11:00:00,060-INFO-[camera][createPayOrder_001] 开始创建支付订单');
			
			// 2025-01-27 17:00:00,019-INFO-[camera][createPayOrder_001_1] 防止重复创建订单
			if (this.isProcessingPayment) {
				console.log('2025-01-27 17:00:00,020-INFO-[camera][createPayOrder_001_2] 正在处理支付流程，忽略重复调用');
				return;
			}
			
			this.isProcessingPayment = true; // 设置处理中标识
			
			const app = getApp();
			
			// 显示加载提示
			uni.showLoading({
				title: '正在创建订单...',
				mask: true
			});
			
			// 创建支付订单
			app.post('ApiSheZhen/createPayOrder', {
				image_url: imageUrl,
				use_free: 0  // 使用余额支付
			}, (response) => {
				uni.hideLoading();
				this.isProcessingPayment = false; // 重置处理标识
				console.log('2025-01-27 11:00:00,061-INFO-[camera][createPayOrder_002] 支付订单创建成功:', response);
				
				// 2025-01-27 11:00:00,062-INFO-[camera][createPayOrder_003] 增强判断逻辑，同时支持code和status返回
				if (response && ((response.status === 1 || response.code === 1)) && response.data) {
					// 订单创建成功
					const orderData = response.data;
					
					// 继续跳转到分析页面，传递订单ID
					this.proceedToAnalysis(imageUrl, orderData.orderid || orderData.payorderid);
				} else {
					console.error('2025-01-27 11:00:00,063-ERROR-[camera][createPayOrder_004] 订单创建失败:', response);
					
					uni.showModal({
						title: '创建订单失败',
						content: response?.msg || '支付订单创建失败，请重试',
						showCancel: true,
						confirmText: '重试',
						cancelText: '返回',
						success: (res) => {
							if (res.confirm) {
								this.createPayOrder(imageUrl);
							} else {
								uni.navigateBack();
							}
						}
					});
				}
			}, (error) => {
				uni.hideLoading();
				this.isProcessingPayment = false; // 重置处理标识
				console.error('2025-01-27 11:00:00,064-ERROR-[camera][createPayOrder_005] 订单创建请求失败:', error);
				
				uni.showModal({
					title: '网络错误',
					content: '创建订单失败，请检查网络连接后重试',
					showCancel: true,
					confirmText: '重试',
					cancelText: '返回',
					success: (res) => {
						if (res.confirm) {
							this.createPayOrder(imageUrl);
						} else {
							uni.navigateBack();
						}
					}
				});
			});
		},
		
		/**
		 * 跳转到分析页面
		 */
		proceedToAnalysis(imageUrl, orderId = '') {
			console.log('2025-01-27 11:00:00,065-INFO-[camera][proceedToAnalysis_001] 准备跳转到分析页面');
			
			// 将配置信息JSON序列化传递给分析页面
			const configData = this.configData ? encodeURIComponent(JSON.stringify(this.configData)) : '';
			
			// 构建URL参数
			let url = `/pagesB/shezhen/analysis?image_url=${encodeURIComponent(imageUrl)}&config=${configData}`;
			
			// 如果有订单ID，添加到URL参数中
			if (orderId) {
				url += `&order_id=${orderId}`;
			}
			
			// 跳转到分析页面，传递图片路径
			uni.navigateTo({
				url: url,
				success: () => {
					console.log('2025-01-27 11:00:00,066-INFO-[camera][proceedToAnalysis_002] 页面跳转成功');
				},
				fail: (err) => {
					console.error('2025-01-27 11:00:00,067-ERROR-[camera][proceedToAnalysis_003] 页面跳转失败:', err);
					
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
		},

		/**
		 * 切换相机前后置
		 */
		flipCamera() {
			console.log('2025-01-26 11:00:00,024-INFO-[camera][flipCamera_001] 切换相机按钮被点击');
			
			// 添加触觉反馈
			uni.vibrateShort();
			
			// #ifdef H5
			// H5环境下提示不支持
			uni.showToast({
				title: 'H5环境下不支持切换相机',
				icon: 'none',
				duration: 2000
			});
			return;
			// #endif
			
			if (this.isFlipping) {
				console.log('2025-01-26 11:00:00,025-INFO-[camera][flipCamera_002] 相机正在切换中，忽略此次点击');
				return;
			}
			
			this.isFlipping = true;
			
			// 切换相机位置
			this.devicePosition = this.devicePosition === 'back' ? 'front' : 'back';
			
			console.log('2025-01-26 11:00:00,026-INFO-[camera][flipCamera_003] 相机切换至:', this.devicePosition);
			
			uni.showToast({
				title: this.devicePosition === 'back' ? '切换到后置摄像头' : '切换到前置摄像头',
				icon: 'none',
				duration: 1000
			});
			
			// 延时重置切换状态
			setTimeout(() => {
				this.isFlipping = false;
				console.log('2025-01-26 11:00:00,027-INFO-[camera][flipCamera_004] 相机切换状态重置');
			}, 1000);
		},

		/**
		 * 切换闪光灯
		 */
		toggleFlash() {
			console.log('2025-01-26 11:00:00,017-INFO-[camera][toggleFlash_001] 切换闪光灯按钮被点击');
			
			// 添加触觉反馈
			uni.vibrateShort();
			
			// #ifdef H5
			// H5环境下提示不支持
			uni.showToast({
				title: 'H5环境下不支持闪光灯',
				icon: 'none',
				duration: 2000
			});
			return;
			// #endif
			
			this.flashOn = !this.flashOn;
			this.flashMode = this.flashOn ? 'on' : 'off';
			
			console.log('2025-01-26 11:00:00,018-INFO-[camera][toggleFlash_002] 闪光灯状态:', this.flashOn, 'flashMode:', this.flashMode);
			
			uni.showToast({
				title: this.flashOn ? '闪光灯已开启' : '闪光灯已关闭',
				icon: 'none',
				duration: 1000
			});
		},

		/**
		 * 返回上一页
		 */
		goBack() {
			console.log('2025-01-26 11:00:00,018-INFO-[camera][goBack_001] 返回按钮被点击');
			
			// 添加触觉反馈
			uni.vibrateShort();
			
			uni.navigateBack({
				delta: 1,
				success: () => {
					console.log('2025-01-26 11:00:00,019-INFO-[camera][goBack_002] 成功返回上一页');
				},
				fail: (err) => {
					console.error('2025-01-26 11:00:00,020-ERROR-[camera][goBack_003] 返回上一页失败:', err);
				}
			});
		},

		/**
		 * 图片加载错误处理
		 */
		onImageError(error) {
			console.error('2025-01-26 11:00:00,021-ERROR-[camera][onImageError_001] 图片加载错误:', error);
			
			uni.showToast({
				title: '图片加载失败',
				icon: 'none'
			});
		},

		/**
		 * 获取舌诊配置信息
		 */
		getSheZhenConfig() {
			console.log('2025-01-26 11:00:00,022-INFO-[camera][getSheZhenConfig_001] 获取舌诊配置信息');
			
			const app = getApp();
			
			// 检查app实例和post方法是否存在
			if (!app || !app.post) {
				console.log('2025-01-26 11:00:00,025-INFO-[camera][getSheZhenConfig_002] app实例或post方法不存在，跳过配置获取');
				// 设置默认配置，允许继续使用
				this.configData = {
					can_use_free: true,
					use_own_aliyun: false,
					use_public_api: true,
					platform_calls_remaining: 100,
					price: 0
				};
				return;
			}
			
			app.post('ApiSheZhen/getConfig', {}, (response) => {
				if (response && response.status === 1) {
					console.log('舌诊配置获取成功:', response.data);
					this.configData = response.data;
					
					// 检查舌诊功能是否可用
					if (!response.data.use_own_aliyun && !response.data.use_public_api) {
						uni.showModal({
							title: '提示',
							content: '舌诊功能暂不可用，请联系管理员',
							showCancel: false,
							success: () => {
								uni.navigateBack();
							}
						});
					} else if (response.data.use_public_api && response.data.platform_calls_remaining <= 0) {
						uni.showModal({
							title: '提示', 
							content: '平台舌诊次数不足，请联系管理员',
							showCancel: false,
							success: () => {
								uni.navigateBack();
							}
						});
					}
				} else {
					console.log('2025-01-26 11:00:00,026-INFO-[camera][getSheZhenConfig_003] 舌诊配置获取失败，使用默认配置:', response);
					// 设置默认配置，允许继续使用
					this.configData = {
						can_use_free: true,
						use_own_aliyun: false,
						use_public_api: true,
						platform_calls_remaining: 100,
						price: 0
					};
					
					// 不显示错误提示，静默处理
				}
			}, (error) => {
				console.log('2025-01-26 11:00:00,027-INFO-[camera][getSheZhenConfig_004] 舌诊配置接口调用失败，使用默认配置:', error);
				// 设置默认配置，允许继续使用
				this.configData = {
					can_use_free: true,
					use_own_aliyun: false,
					use_public_api: true,
					platform_calls_remaining: 100,
					price: 0
				};
				
				// 不显示错误提示，静默处理
			});
		}
	}
}
</script>

<style scoped>
.camera-container {
	width: 100vw;
	height: 100vh;
	position: relative;
	overflow: hidden;
	background: #000;
}

/* 背景装饰 */
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 0;
}

.gradient-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.floating-particles {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	overflow: hidden;
}

.particle {
	position: absolute;
	width: 4rpx;
	height: 4rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	animation: float-particle 8s infinite linear;
}

.particle-1 { left: 10%; animation-delay: 0s; }
.particle-2 { left: 20%; animation-delay: 1s; }
.particle-3 { left: 40%; animation-delay: 2s; }
.particle-4 { left: 60%; animation-delay: 3s; }
.particle-5 { left: 80%; animation-delay: 4s; }
.particle-6 { left: 90%; animation-delay: 5s; }

@keyframes float-particle {
	0% { transform: translateY(100vh) scale(0); opacity: 0; }
	10% { opacity: 1; }
	90% { opacity: 1; }
	100% { transform: translateY(-100rpx) scale(1); opacity: 0; }
}

/* 拍摄模式 */
.camera-mode {
	position: relative;
	width: 100%;
	height: 100%;
	z-index: 1;
}

/* 顶部状态栏 */
.status-bar {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 30rpx 20rpx;
	background: linear-gradient(180deg, rgba(0, 0, 0, 0.8), transparent);
	z-index: 999;
}

.back-btn, .flip-btn, .flash-btn {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	z-index: 1000;
	position: relative;
}

.back-btn:active, .flip-btn:active, .flash-btn:active {
	transform: scale(0.9);
	background: rgba(255, 255, 255, 0.5);
}

.flip-btn.active {
	background: rgba(33, 150, 243, 0.5);
	transform: rotate(180deg);
}

.flash-btn.active {
	background: rgba(255, 193, 7, 0.5);
	transform: scale(1.1);
}

.back-icon, .flip-icon, .flash-icon {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
}

.page-title {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 拍摄指导区域 */
.guide-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 5;
	pointer-events: none;
}

.guide-frame {
	width: 500rpx;
	height: 400rpx;
	position: relative;
}

.frame-corner {
	position: absolute;
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #fff;
}

.corner-tl {
	top: 0;
	left: 0;
	border-right: none;
	border-bottom: none;
	border-radius: 20rpx 0 0 0;
}

.corner-tr {
	top: 0;
	right: 0;
	border-left: none;
	border-bottom: none;
	border-radius: 0 20rpx 0 0;
}

.corner-bl {
	bottom: 0;
	left: 0;
	border-right: none;
	border-top: none;
	border-radius: 0 0 0 20rpx;
}

.corner-br {
	bottom: 0;
	right: 0;
	border-left: none;
	border-top: none;
	border-radius: 0 0 20rpx 0;
}

.frame-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
}

.guide-text {
	font-size: 28rpx;
	color: white;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.8);
	margin-bottom: 20rpx;
	display: block;
}

.pulse-dot {
	width: 20rpx;
	height: 20rpx;
	background: #fff;
	border-radius: 50%;
	margin: 0 auto;
	animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
	0%, 100% { opacity: 1; transform: scale(1); }
	50% { opacity: 0.5; transform: scale(1.5); }
}

/* 相机预览区域 */
.camera-preview {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
}

.camera-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.scan-line {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 500rpx;
	height: 2rpx;
	background: linear-gradient(90deg, transparent, #fff, transparent);
	animation: scan 3s infinite;
}

@keyframes scan {
	0%, 100% { opacity: 0; }
	50% { opacity: 1; }
}

/* 拍摄提示区域 */
.tips-section {
	position: absolute;
	bottom: 300rpx;
	left: 0;
	right: 0;
	z-index: 5;
	pointer-events: none;
}

.tips-container {
	display: flex;
	justify-content: space-around;
	padding: 0 40rpx;
}

.tip-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 20rpx;
	padding: 20rpx 15rpx;
	min-width: 120rpx;
}

.tip-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.tip-text {
	font-size: 20rpx;
	color: white;
	text-align: center;
	line-height: 1.2;
}

/* 控制区域 */
.control-section {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 280rpx;
	background: linear-gradient(0deg, rgba(0, 0, 0, 0.8), transparent);
	z-index: 999;
}

.control-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 100%;
	padding: 40rpx 60rpx;
	z-index: 1000;
	position: relative;
}

.control-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	max-width: 160rpx;
	z-index: 1001;
	position: relative;
}

.control-btn {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
	transition: all 0.3s ease;
	position: relative;
	z-index: 1002;
}

/* 拍照按钮特殊处理 */
.control-item.capture .control-btn {
	width: 120rpx;
	height: 120rpx;
}

.control-btn.secondary {
	background: rgba(255, 255, 255, 0.3);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.control-btn.primary {
	background: linear-gradient(135deg, #ff9a9e, #fecfef);
	box-shadow: 0 10rpx 30rpx rgba(255, 154, 158, 0.4);
}

.control-btn:active {
	transform: scale(0.9);
}

.control-btn.active {
	background: rgba(255, 193, 7, 0.5);
}

.control-icon {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
}

.control-icon-img {
	width: 40rpx;
	height: 40rpx;
	filter: brightness(0) invert(1); /* 将图片转为白色 */
}

.control-label {
	font-size: 22rpx;
	color: white;
	text-align: center;
	white-space: nowrap;
}

/* 拍摄按钮特效 */
.capture-ring {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	right: -10rpx;
	bottom: -10rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.5);
	border-radius: 50%;
	animation: capture-pulse 2s infinite;
}

.capture-dot {
	width: 60rpx;
	height: 60rpx;
	background: white;
	border-radius: 50%;
}

.control-btn.capturing .capture-ring {
	animation: capture-flash 0.3s ease-out;
}

@keyframes capture-pulse {
	0%, 100% { transform: scale(1); opacity: 1; }
	50% { transform: scale(1.1); opacity: 0.7; }
}

@keyframes capture-flash {
	0% { transform: scale(1); opacity: 1; }
	100% { transform: scale(1.5); opacity: 0; }
}

/* 预览模式 */
.preview-mode {
	position: relative;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #667eea, #764ba2);
	z-index: 1;
}

/* 预览头部 */
.preview-header {
	text-align: center;
	padding: 80rpx 40rpx 40rpx;
	background: linear-gradient(180deg, rgba(0, 0, 0, 0.3), transparent);
}

.preview-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.preview-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
}

/* 图片预览区域 */
.image-preview {
	position: relative;
	margin: 40rpx 30rpx;
	border-radius: 25rpx;
	overflow: hidden;
	background: white;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.preview-image {
	width: 100%;
	height: 600rpx;
	background: #f5f5f5;
}

/* 分析指示器 */
.analysis-indicators {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}

.analysis-point {
	position: absolute;
	width: 40rpx;
	height: 40rpx;
	transform: translate(-50%, -50%);
}

.point-ring {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border: 2rpx solid #ff9a9e;
	border-radius: 50%;
	animation: point-pulse 2s infinite;
}

.point-dot {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 8rpx;
	height: 8rpx;
	background: #ff9a9e;
	border-radius: 50%;
	transform: translate(-50%, -50%);
}

@keyframes point-pulse {
	0%, 100% { transform: scale(1); opacity: 1; }
	50% { transform: scale(1.5); opacity: 0.5; }
}

/* 质量检测结果 */
.quality-check {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(0deg, rgba(0, 0, 0, 0.8), transparent);
	padding: 30rpx;
	display: flex;
	justify-content: space-around;
	pointer-events: none;
}

.quality-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.quality-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8rpx;
	font-size: 20rpx;
	font-weight: bold;
}

.quality-icon.pass {
	background: #4caf50;
	color: white;
}

.quality-icon.warning {
	background: #ff9800;
	color: white;
}

.quality-text {
	font-size: 20rpx;
	color: white;
	text-align: center;
}

/* 预览操作区域 */
.preview-actions {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 40rpx 30rpx 60rpx;
	display: flex;
	gap: 20rpx;
	z-index: 999;
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.action-btn.secondary {
	background: rgba(255, 255, 255, 0.3);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff9a9e, #fecfef);
	box-shadow: 0 15rpx 35rpx rgba(255, 154, 158, 0.4);
}

.action-btn:active {
	transform: translateY(2rpx) scale(0.98);
}

.btn-content {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	position: relative;
	z-index: 2;
}

.btn-icon {
	font-size: 28rpx;
	margin-right: 10rpx;
}

.btn-text {
	font-size: 28rpx;
	font-weight: bold;
	color: white;
}

.action-btn.primary .btn-text {
	color: #333;
}

.btn-glow {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 0;
	height: 0;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	transform: translate(-50%, -50%);
	animation: btn-glow 3s infinite;
}

@keyframes btn-glow {
	0% { width: 0; height: 0; opacity: 1; }
	100% { width: 200rpx; height: 200rpx; opacity: 0; }
}

/* 加载遮罩 */
.loading-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-content {
	text-align: center;
}

.loading-spinner {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	margin: 0 auto 30rpx;
}

.spinner-ring {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border: 4rpx solid rgba(255, 255, 255, 0.2);
	border-top: 4rpx solid #fff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.spinner-dot {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 20rpx;
	height: 20rpx;
	background: #fff;
	border-radius: 50%;
	transform: translate(-50%, -50%);
	animation: pulse 2s infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes pulse {
	0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
	50% { opacity: 0.5; transform: translate(-50%, -50%) scale(1.2); }
}

.loading-text {
	font-size: 28rpx;
	color: white;
	font-weight: bold;
}

/* H5环境下显示替代预览区域 */
.h5-camera-placeholder {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 25rpx;
	z-index: 1;
}

.h5-camera-content {
	text-align: center;
}

.h5-camera-text {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.h5-camera-tip {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
}
</style>
