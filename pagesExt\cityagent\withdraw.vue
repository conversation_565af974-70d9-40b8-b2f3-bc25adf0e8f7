<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit">
			<view class="mymoney" :style="{background:t('color1')}">
				<view class="f1">我的可提现余额</view>
				<view class="f2"><text style="font-size:26rpx">￥</text>{{(agentInfo && agentInfo.money) || '0.00'}}</view>
				<view class="f3" @tap="goto" data-url="withdrawlog?st=1"><text>提现记录</text><text class="iconfont iconjiantou" style="font-size:20rpx"></text></view>
			</view>
			<view class="content2">
				<view class="item2"><view class="f1">提现金额(元)</view></view>
				<view class="item3"><view class="f1">￥</view><view class="f2"><input class="input" type="digit" name="money" value="" placeholder="请输入提现金额" placeholder-style="color:#999;font-size:40rpx" @input="moneyinput"></input></view></view>
				<view class="item4" v-if="sysset.withdrawfee>0 || sysset.withdrawmin>0">
					<text v-if="sysset.withdrawmin>0" style="margin-right:10rpx">最低提现金额{{sysset.withdrawmin}}元 </text>
					<text v-if="sysset.withdrawfee>0">提现手续费{{sysset.withdrawfee}}% </text>
				</view>
			</view>
			<view class="withdrawtype">
				<view class="f1">选择提现方式：</view>
				<view class="f2">
					<view class="item" v-if="sysset.withdraw_weixin==1" @tap.stop="changeradio" data-paytype="微信钱包">
						<view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"/>微信钱包</view>
						<view class="radio" :style="paytype=='微信钱包' ? 'background:'+t('color1')+';border:0' :''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<label class="item" v-if="sysset.withdraw_aliaccount==1" @tap.stop="changeradio" data-paytype="支付宝">
						<view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝</view>
						<view class="radio" :style="paytype=='支付宝' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</label>
					<label class="item" v-if="sysset.withdraw_bankcard==1" @tap.stop="changeradio" data-paytype="银行卡">
						<view class="t1"><image class="img" src="/static/img/withdraw-cash.png"/>银行卡</view>
						<view class="radio" :style="paytype=='银行卡' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</label>
				</view>
			</view>
			
			<!-- 支付宝账号输入 -->
			<view class="account-form" v-if="paytype=='支付宝'">
				<view class="form">
					<view class="form-item">
						<view class="label">账户名</view>
						<input type="text" class="input" placeholder="请输入支付宝账户名" placeholder-style="color:#BBBBBB;font-size:28rpx" v-model="accountInfo.aliaccountname"></input>
					</view>
					<view class="form-item">
						<view class="label">支付宝账号</view>
						<input type="text" class="input" placeholder="请输入支付宝账号" placeholder-style="color:#BBBBBB;font-size:28rpx" v-model="accountInfo.aliaccount"></input>
					</view>
				</view>
			</view>
			
			<!-- 银行卡信息输入 -->
			<view class="account-form" v-if="paytype=='银行卡'">
				<view class="form">
					<view class="form-item">
						<view class="label">持卡人姓名</view>
						<input type="text" class="input" placeholder="请输入持卡人姓名" placeholder-style="color:#BBBBBB;font-size:28rpx" v-model="accountInfo.bankcarduser"></input>
					</view>
					<view class="form-item">
						<view class="label">银行卡号</view>
						<input type="text" class="input" placeholder="请输入银行卡号" placeholder-style="color:#BBBBBB;font-size:28rpx" v-model="accountInfo.bankcardnum"></input>
					</view>
					<view class="form-item">
						<view class="label">开户银行</view>
						<input type="text" class="input" placeholder="请输入开户银行" placeholder-style="color:#BBBBBB;font-size:28rpx" v-model="accountInfo.bankname"></input>
					</view>
					<view class="form-item">
						<view class="label">开户地址</view>
						<input type="text" class="input" placeholder="请输入开户地址（可选）" placeholder-style="color:#BBBBBB;font-size:28rpx" v-model="accountInfo.bankaddress"></input>
					</view>
				</view>
			</view>
			
			<button class="btn" :style="{background:t('color1')}" @tap="formSubmit">立即提现</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			menuindex: -1,
			
			agentInfo: {},
			money: 0,
			sysset: {},
			paytype: '微信钱包',
			tmplids: [],
			
			// 账号信息
			accountInfo: {
				// 支付宝信息
				aliaccountname: '',
				aliaccount: '',
				// 银行卡信息  
				bankcarduser: '',
				bankcardnum: '',
				bankname: '',
				bankaddress: ''
			}
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		var that = this;
		this.getdata();
	},
	
	onPullDownRefresh: function () {
		this.getdata();
	},
	
	methods: {
		// 获取数据
		getdata: function () {
			var that = this;
			that.loading = true;
			
			// 获取代理信息和提现配置
			app.post('ApiCityAgent/getAgentInfo', {}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 1) {
					that.agentInfo = res.agentinfo;
					that.tmplids = [];
					
					// 获取提现配置（异步调用，配置获取完成后才显示页面）
					that.getWithdrawSetting();
				} else {
					app.error(res.msg);
				}
			});
		},
		
		// 获取提现配置
		getWithdrawSetting: function () {
			var that = this;
			
			app.post('ApiCityAgent/getWithdrawSetting', {}, function (res) {
				if (res && res.status == 1) {
					// 检查返回数据结构
					var withdraw_setting = res.withdraw_setting || {};
					var convert_setting = res.convert_setting || {};
					
					// 使用后台真实配置，如果withdraw_setting不存在，尝试使用convert_setting
					that.sysset = {
						withdrawmin: withdraw_setting.withdraw_min || convert_setting.min_convert_amount || 0,
						withdrawfee: withdraw_setting.withdraw_fee || convert_setting.convert_fee_rate || 0,
						withdraw_weixin: withdraw_setting.withdraw_weixin !== undefined ? withdraw_setting.withdraw_weixin : 1,
						withdraw_aliaccount: withdraw_setting.withdraw_aliaccount !== undefined ? withdraw_setting.withdraw_aliaccount : 1,
						withdraw_bankcard: withdraw_setting.withdraw_bankcard !== undefined ? withdraw_setting.withdraw_bankcard : 1
					};
					
					// 设置默认提现方式
					var paytype = '微信钱包';
					if (that.sysset.withdraw_weixin == 1) {
						paytype = '微信钱包';
					}
					if (!that.sysset.withdraw_weixin || that.sysset.withdraw_weixin == 0) {
						paytype = '支付宝';
					}
					if ((!that.sysset.withdraw_weixin || that.sysset.withdraw_weixin == 0) && (!that.sysset.withdraw_aliaccount || that.sysset.withdraw_aliaccount == 0)) {
						paytype = '银行卡';
					}
					that.paytype = paytype;
					
					// 配置获取完成后才显示页面
					that.loaded();
				} else {
					// 如果获取失败，使用默认配置
					that.sysset = {
						withdrawmin: 0,
						withdrawfee: 0,
						withdraw_weixin: 1,
						withdraw_aliaccount: 1,
						withdraw_bankcard: 1
					};
					that.paytype = '微信钱包';
					
					// 即使失败也要显示页面
					that.loaded();
				}
			}, function(error) {
				// 处理网络错误
				that.sysset = {
					withdrawmin: 0,
					withdrawfee: 0,
					withdraw_weixin: 1,
					withdraw_aliaccount: 1,
					withdraw_bankcard: 1
				};
				that.paytype = '微信钱包';
				that.loaded();
			});
		},
		
		loaded: function() {
			this.isload = true;
			uni.setNavigationBarTitle({
				title: '余额提现'
			});
		},
		
		// 金额输入处理
		moneyinput: function (e) {
			var usermoney = parseFloat((this.agentInfo && this.agentInfo.money) || 0);
			var money = parseFloat(e.detail.value);
			if (money < 0) {
				app.error('必须大于0');
			} else if (money > usermoney) {
				app.error('可提现余额不足');
			}
			this.money = money;
		},
		
		// 选择提现方式
		changeradio: function (e) {
			var that = this;
			var paytype = e.currentTarget.dataset.paytype;
			that.paytype = paytype;
		},
		
		// 表单提交
		formSubmit: function () {
			var that = this;
			var usermoney = parseFloat((this.agentInfo && this.agentInfo.money) || 0);
			var withdrawmin = parseFloat(this.sysset.withdrawmin || 0);
			
			var money = parseFloat(that.money);
			var paytype = this.paytype;
			if (isNaN(money) || money <= 0) {
				app.error('提现金额必须大于0');
				return;
			}
			if (withdrawmin > 0 && money < withdrawmin) {
				app.error('提现金额必须大于¥' + withdrawmin);
				return;
			}

			if (money > usermoney) {
				app.error('余额不足');
				return;
			}

			// 验证账号信息
			if (paytype == '支付宝') {
				if (!this.accountInfo.aliaccount || this.accountInfo.aliaccount.trim() == '') {
					app.error('请输入支付宝账号');
					return;
				}
				if (!this.accountInfo.aliaccountname || this.accountInfo.aliaccountname.trim() == '') {
					app.error('请输入支付宝账户名');
					return;
				}
			}
			if (paytype == '银行卡') {
				if (!this.accountInfo.bankcardnum || this.accountInfo.bankcardnum.trim() == '') {
					app.error('请输入银行卡号');
					return;
				}
				if (!this.accountInfo.bankcarduser || this.accountInfo.bankcarduser.trim() == '') {
					app.error('请输入持卡人姓名');
					return;
				}
				if (!this.accountInfo.bankname || this.accountInfo.bankname.trim() == '') {
					app.error('请输入开户银行');
					return;
				}
			}
			
			// 提交提现申请
			app.showLoading('提交中');
			
			var params = {
				amount: money,
				paytype: paytype,
				remark: '城市代理提现申请',
				// 添加账号信息
				account_info: this.accountInfo
			};
			
			app.post('ApiCityAgent/applyWithdraw', params, function (res) {
				app.showLoading(false);
				
				if (res.status == 1) {
					var msg = '提现申请成功！\n';
					msg += '申请金额：¥' + res.data.amount + '\n';
					if (parseFloat(res.data.fee) > 0) {
						msg += '手续费：¥' + res.data.fee + '\n';
						msg += '实际到账：¥' + res.data.actual_amount + '\n';
					}
					msg += '请等待审核...';
					
					app.alert(msg, function() {
						// 跳转到提现记录页面
						app.goto('/pagesExt/cityagent/withdrawlog?st=1');
					});
				} else {
					app.error(res.msg);
				}
			}, function(error) {
				app.showLoading(false);
				console.log('提现申请失败:', error);
				app.error('网络错误，请重试');
			});
		},
		
		// 页面跳转
		goto: function (e) {
			var url = e.currentTarget.dataset.url;
			if (url) {
				app.goto('/pagesExt/cityagent/' + url);
			}
		}
	}
};
</script>

<style>
.container{display:flex;flex-direction:column}
.mymoney{width:94%;margin:20rpx 3%;border-radius: 10rpx 56rpx 10rpx 10rpx;position:relative;display:flex;flex-direction:column;padding:70rpx 0}
.mymoney .f1{margin:0 0 0 60rpx;color:rgba(255,255,255,0.8);font-size:24rpx;}
.mymoney .f2{margin:20rpx 0 0 60rpx;color:#fff;font-size:64rpx;font-weight:bold}
.mymoney .f3{height:56rpx;padding:0 10rpx 0 20rpx;border-radius: 28rpx 0px 0px 28rpx;background:rgba(255,255,255,0.2);font-size:20rpx;font-weight:bold;color:#fff;display:flex;align-items:center;position:absolute;top:94rpx;right:0}

/* 账号信息表单样式 */
.account-form{width:94%;margin:20rpx 3%;border-radius:10rpx;background:#fff;}
.account-form .form{padding:0 3%;}
.account-form .form-item{display:flex;align-items:center;width:100%;border-bottom:1px solid #ededed;height:98rpx;line-height:98rpx;}
.account-form .form-item:last-child{border:0}
.account-form .form-item .label{color:#000;width:200rpx;font-size:30rpx;}
.account-form .form-item .input{flex:1;color:#000;font-size:30rpx;}

.content2{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff}
.content2 .item1{display:flex;width:100%;border-bottom:1px solid #F0F0F0;padding:0 30rpx}
.content2 .item1 .f1{flex:1;font-size:32rpx;color:#333333;font-weight:bold;height:120rpx;line-height:120rpx}
.content2 .item1 .f2{color:#FC4343;font-size:44rpx;font-weight:bold;height:120rpx;line-height:120rpx}

.content2 .item2{display:flex;width:100%;padding:0 30rpx;padding-top:10rpx}
.content2 .item2 .f1{height:80rpx;line-height:80rpx;color:#999999;font-size:28rpx}

.content2 .item3{display:flex;width:100%;padding:0 30rpx;padding-bottom:20rpx}
.content2 .item3 .f1{height:100rpx;line-height:100rpx;font-size:60rpx;color:#333333;font-weight:bold;margin-right:20rpx}
.content2 .item3 .f2{display:flex;align-items:center;font-size:60rpx;color:#333333;font-weight:bold}
.content2 .item3 .f2 .input{font-size:60rpx;height:100rpx;line-height:100rpx;}
.content2 .item4{display:flex;width:94%;margin:0 3%;border-top:1px solid #F0F0F0;height:100rpx;line-height:100rpx;color:#8C8C8C;font-size:28rpx}

.withdrawtype{width:94%;margin:20rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;margin-top:20rpx;background:#fff}
.withdrawtype .f1{height:100rpx;line-height:100rpx;padding:0 30rpx;color:#333333;font-weight:bold}

.withdrawtype .f2{padding:0 30rpx}
.withdrawtype .f2 .item{border-bottom:1px solid #f5f5f5;height:100rpx;display:flex;align-items:center}
.withdrawtype .f2 .item:last-child{border-bottom:0}
.withdrawtype .f2 .item .t1{flex:1;display:flex;align-items:center;color:#333}
.withdrawtype .f2 .item .t1 .img{width:44rpx;height:44rpx;margin-right:40rpx}

.withdrawtype .f2 .item .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}
.withdrawtype .f2 .item .radio .radio-img{width:100%;height:100%}

.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}
</style> 