<template>
<view class="dp-holiday" :style="{
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	borderRadius:(params.borderradius*2)+'rpx'
}">
	<!-- 列表样式 -->
	<view v-if="params.style=='1'" class="holiday-list">
		<view v-for="(item,index) in data" :key="item.id" class="holiday-item">
			<view v-if="params.showicon=='1'" class="holiday-icon">
				<image :src="item.icon" :style="{
					width:(params.iconsize*2)+'rpx',
					height:(params.iconsize*2)+'rpx'
				}" mode="aspectFill"/>
			</view>
			<view class="holiday-content">
				<view class="holiday-title" :style="{
					color:params.titlecolor,
					fontSize:'28rpx',
					fontWeight:'bold'
				}">
					{{item.name}}
				</view>
				<view class="holiday-date" :style="{
					color:params.datecolor,
					fontSize:'24rpx'
				}">
					{{item.holiday_date_text}}
				</view>
				<view v-if="params.showdesc=='1' && item.description" class="holiday-desc" :style="{
					color:params.desccolor,
					fontSize:'22rpx'
				}">
					{{item.description}}
				</view>
			</view>
			<view class="holiday-countdown" :style="{
				color:params.countdowncolor,
				fontSize:'24rpx',
				fontWeight:'bold'
			}">
				{{item.countdown_text}}
			</view>
		</view>
	</view>
	
	<!-- Bento Grid样式 -->
	<view v-if="params.style=='2'" class="holiday-bento-grid">
		<view v-for="(item,index) in data" :key="item.id"
			  :class="['holiday-bento-card', bentoCardClasses[index % bentoCardClasses.length]]"
			  :style="{
				borderRadius:(params.borderradius*2)+'rpx',
				background: bentoCardBackgrounds[index % bentoCardBackgrounds.length]
			}">
			<!-- 卡片内容容器 -->
			<view class="bento-card-content">
				<!-- 图标和标题区域 -->
				<view class="bento-header">
					<view v-if="params.showicon=='1'" class="bento-icon">
						<image :src="item.icon" :style="{
							width:(params.iconsize*2.2)+'rpx',
							height:(params.iconsize*2.2)+'rpx'
						}" mode="aspectFill"/>
					</view>
					<view class="bento-title-area">
						<view class="bento-title" :style="{
							color:params.titlecolor,
							fontSize: bentoTitleSizes[index % bentoTitleSizes.length],
							fontWeight:'600'
						}">
							{{item.name}}
						</view>
						<view class="bento-date" :style="{
							color:params.datecolor,
							fontSize: bentoDateSizes[index % bentoDateSizes.length]
						}">
							{{item.holiday_date_text}}
						</view>
					</view>
				</view>

				<!-- 倒计时区域 -->
				<view class="bento-countdown-area">
					<view class="bento-countdown-main" :style="{
						color:params.countdowncolor,
						fontSize: bentoCountdownSizes[index % bentoCountdownSizes.length],
						fontWeight:'700'
					}">
						{{item.countdown_text}}
					</view>
					<view v-if="item.countdown_days > 0" class="bento-countdown-sub">
						距离还有 {{item.countdown_days}} 天
					</view>
				</view>

				<!-- 描述区域 -->
				<view v-if="params.showdesc=='1' && item.description && bentoShowDescFlags[index % bentoShowDescFlags.length]"
					  class="bento-desc" :style="{
					color:params.desccolor,
					fontSize: bentoDescSizes[index % bentoDescSizes.length]
				}">
					{{item.description}}
				</view>

				<!-- 装饰元素 -->
				<view class="bento-decoration" :style="{
					background: bentoDecorationColors[index % bentoDecorationColors.length]
				}"></view>
			</view>
		</view>
	</view>
	
	<!-- 无数据提示 -->
	<view v-if="!data || data.length==0" class="holiday-empty">
		<view class="empty-icon">📅</view>
		<view class="empty-text">暂无即将到来的节日</view>
	</view>
</view>
</template>

<script>
export default {
	props: {
		params: {
			type: Object,
			default: () => ({})
		},
		data: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			// Bento Grid样式配置数组
			bentoCardClasses: ['bento-large', 'bento-medium', 'bento-small', 'bento-wide', 'bento-tall'],
			bentoCardBackgrounds: [
				'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
				'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
				'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
				'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
				'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
				'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
				'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
			],
			bentoTitleSizes: ['32rpx', '28rpx', '24rpx', '30rpx', '26rpx'],
			bentoDateSizes: ['24rpx', '22rpx', '20rpx', '22rpx', '20rpx'],
			bentoCountdownSizes: ['48rpx', '36rpx', '28rpx', '40rpx', '32rpx'],
			bentoDescSizes: ['22rpx', '20rpx', '18rpx', '20rpx', '18rpx'],
			bentoShowDescFlags: [true, true, false, true, false],
			bentoDecorationColors: [
				'rgba(255,255,255,0.2)',
				'rgba(255,255,255,0.15)',
				'rgba(255,255,255,0.1)',
				'rgba(255,255,255,0.25)',
				'rgba(255,255,255,0.2)'
			]
		}
	},
	mounted() {
		console.log('dp-holiday组件加载完成', this.params, this.data);
	},
	methods: {

	}
}
</script>

<style scoped>
.dp-holiday {
	position: relative;
	overflow: hidden;
}

/* 列表样式 */
.holiday-list {
	width: 100%;
}

.holiday-item {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.holiday-item:last-child {
	border-bottom: none;
}

.holiday-icon {
	margin-right: 20rpx;
	flex-shrink: 0;
}

.holiday-icon image {
	border-radius: 50%;
}

.holiday-content {
	flex: 1;
	min-width: 0;
}

.holiday-title {
	margin-bottom: 4rpx;
	line-height: 1.4;
}

.holiday-date {
	margin-bottom: 4rpx;
	line-height: 1.3;
}

.holiday-desc {
	line-height: 1.3;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}

.holiday-countdown {
	text-align: right;
	flex-shrink: 0;
	margin-left: 20rpx;
}

/* Bento Grid样式 */
.holiday-bento-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
	grid-auto-rows: minmax(160rpx, auto);
	gap: 20rpx;
	padding: 10rpx;
}

.holiday-bento-card {
	position: relative;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.holiday-bento-card:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.18);
}

/* Bento Grid 不同尺寸的卡片 */
.bento-large {
	grid-column: span 2;
	grid-row: span 2;
}

.bento-medium {
	grid-column: span 1;
	grid-row: span 2;
}

.bento-small {
	grid-column: span 1;
	grid-row: span 1;
}

.bento-wide {
	grid-column: span 2;
	grid-row: span 1;
}

.bento-tall {
	grid-column: span 1;
	grid-row: span 2;
}

/* 卡片内容布局 */
.bento-card-content {
	position: relative;
	height: 100%;
	padding: 24rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	z-index: 2;
}

.bento-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.bento-icon {
	margin-right: 16rpx;
	flex-shrink: 0;
}

.bento-icon image {
	border-radius: 50%;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.bento-title-area {
	flex: 1;
	min-width: 0;
}

.bento-title {
	line-height: 1.3;
	margin-bottom: 6rpx;
	color: white;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.bento-date {
	line-height: 1.2;
	opacity: 0.9;
	color: white;
}

.bento-countdown-area {
	margin: 20rpx 0;
	text-align: center;
}

.bento-countdown-main {
	line-height: 1.1;
	margin-bottom: 8rpx;
	color: white;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.4);
}

.bento-countdown-sub {
	font-size: 20rpx;
	opacity: 0.8;
	color: white;
}

.bento-desc {
	line-height: 1.4;
	opacity: 0.9;
	color: white;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}

/* 装饰元素 */
.bento-decoration {
	position: absolute;
	top: -50%;
	right: -50%;
	width: 200%;
	height: 200%;
	border-radius: 50%;
	z-index: 1;
	opacity: 0.1;
}

/* 无数据样式 */
.holiday-empty {
	text-align: center;
	padding: 80rpx 40rpx;
	color: #999;
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	line-height: 1.4;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
	.holiday-bento-grid {
		grid-template-columns: repeat(auto-fit, minmax(160rpx, 1fr));
		gap: 16rpx;
		padding: 8rpx;
	}

	.bento-large,
	.bento-wide {
		grid-column: span 2;
	}

	.bento-medium,
	.bento-tall,
	.bento-small {
		grid-column: span 1;
	}

	.bento-card-content {
		padding: 20rpx;
	}
}

@media screen and (max-width: 500rpx) {
	.holiday-bento-grid {
		grid-template-columns: 1fr 1fr;
		gap: 12rpx;
	}

	.bento-large {
		grid-column: span 2;
		grid-row: span 1;
	}

	.bento-wide {
		grid-column: span 2;
		grid-row: span 1;
	}

	.bento-card-content {
		padding: 16rpx;
	}

	.bento-countdown-area {
		margin: 12rpx 0;
	}
}
</style>
