<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit" @reset="formReset">
		<view class="form">
			<view class="form-item">
				<input type="text" class="input" :placeholder="inviteCodePlaceholder" placeholder-style="color:#BBBBBB;font-size:28rpx" name="invite_code" v-model="invite_code" @input="inviteCodeInput"></input>
			</view>
			<view class="form-item" v-if="parentInfo.nickname" style="border:0;padding:20rpx 0;">
				<view style="color:#666;font-size:28rpx;">
					<view style="margin-bottom:10rpx;">邀请人信息：</view>
					<view style="display:flex;align-items:center;">
						<image :src="parentInfo.headimg" style="width:60rpx;height:60rpx;border-radius:50%;margin-right:20rpx;"></image>
						<view>
							<view style="font-size:30rpx;color:#333;">{{parentInfo.nickname}}</view>
							<view style="font-size:26rpx;color:#999;" v-if="parentInfo.tel">{{parentInfo.tel}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<button class="set-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">确认绑定</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			invite_code:'',
			inviteCodeType: 0, // 0=手机号, 1=邀请码, 2=用户ID
			inviteCodePlaceholder: '请输入邀请人手机号',
			parentInfo: {} // 邀请人信息
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata:function(){
			var that = this;
			this.loading = true
			app.get('ApiMy/setinvite', {}, function(res){
				that.loading = false;
				that.inviteCodeType = res.invite_code_type || 0;
				that.invite_code = res.current_invite_code || '';
				that.parentInfo = res.parent_info || {};
				
				// 设置placeholder
				if(that.inviteCodeType == 0) {
					that.inviteCodePlaceholder = '请输入邀请人手机号';
				} else if(that.inviteCodeType == 1) {
					that.inviteCodePlaceholder = '请输入邀请码';
				} else {
					that.inviteCodePlaceholder = '请输入邀请人ID';
				}
				
				that.loaded();
			})
		},
    formSubmit: function (e) {
      var formdata = e.detail.value;
			var invite_code = formdata.invite_code;
      if (invite_code == '') {
				var tipText = '请输入邀请码';
				if(this.inviteCodeType == 0) {
					tipText = '请输入邀请人手机号';
				} else if(this.inviteCodeType == 2) {
					tipText = '请输入邀请人ID';
				}
        app.alert(tipText); 
        return;
      }
			app.showLoading('提交中');
      app.post("ApiMy/setinvitesub", {invite_code:invite_code}, function (data) {
				app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function () {
            app.goback(true);
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    },
    inviteCodeInput: function (e) {
      this.invite_code = e.detail.value;
			
			// 实时查询邀请人信息（可选功能）
			var that = this;
			if(e.detail.value && e.detail.value.length >= 3) {
				setTimeout(function() {
					that.queryParentInfo(e.detail.value);
				}, 500);
			} else {
				that.parentInfo = {};
			}
    },
		queryParentInfo: function(code) {
			var that = this;
			app.post("ApiMy/queryparent", {invite_code: code}, function (data) {
				if (data.status == 1 && data.parent) {
					that.parentInfo = data.parent;
				} else {
					that.parentInfo = {};
				}
			});
		}
  }
};
</script>

<style>
.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;}
.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;min-height:98rpx;line-height:98rpx;}
.form-item:last-child{border:0}
.form-item .label{color: #000;width:200rpx;}
.form-item .input{flex:1;color: #000;}
.set-btn{width: 90%;margin:60rpx 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
</style> 