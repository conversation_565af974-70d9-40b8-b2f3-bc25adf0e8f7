<template>
	<view class="container">
		<!-- 规则列表 -->
		<view class="rules-container">
			<view class="no-data" v-if="rulesList.length === 0">
				<text>暂无可用奖励规则</text>
			</view>
			
			<view class="rule-card" v-for="(item, index) in rulesList" :key="index" @click="goToRulePreview(item.id)">
				<view class="rule-header">
					<view class="rule-name">{{item.name}}</view>
					<view class="rule-tag">{{item.reward_mode_name}}</view>
				</view>
				<view class="rule-content">
					<view class="rule-item">
						<text class="item-label">排名类型：</text>
						<text class="item-value">{{item.rank_type_name}}</text>
					</view>
					<view class="rule-item">
						<text class="item-label">奖励比例：</text>
						<text class="item-value">{{item.total_reward_rate}}%</text>
					</view>
					<view class="rule-item">
						<text class="item-label">奖励排名：</text>
						<text class="item-value">前{{item.reward_top_num}}名</text>
					</view>
				</view>
				<view class="rule-footer">
					<view class="rule-btn">查看详情</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				rulesList: [],
				loading: false
			}
		},
		onLoad() {
			this.getRulesList();
		},
		onPullDownRefresh() {
			this.getRulesList(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			// 获取规则列表
			getRulesList(callback) {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/rules][getRulesList_001] 开始获取排名奖励规则列表`);
				
				this.loading = true;
				var that = this;
				
				app.post('ApiPaimingjiang/getRankingRules', {}, function(res) {
					that.loading = false;
					
					if (res.status === 1) {
						console.log(`${that.formatTime()}-INFO-[ranking-reward/rules][getRulesList_002] 获取排名奖励规则列表成功，共${res.data.length}条数据`);
						that.rulesList = res.data;
					} else {
						console.log(`${that.formatTime()}-ERROR-[ranking-reward/rules][getRulesList_003] 获取排名奖励规则列表失败：${res.msg}`);
						uni.showToast({
							title: res.msg || '获取规则列表失败',
							icon: 'none'
						});
					}
					
					if (callback && typeof callback === 'function') {
						callback();
					}
				});
			},
			
			// 跳转到规则预览页
			goToRulePreview(ruleId) {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/rules][goToRulePreview_001] 跳转到规则预览页，规则ID：${ruleId}`);
				uni.navigateTo({
					url: `/pagesExa/ranking-reward/preview?rule_id=${ruleId}`
				});
			},
			
			// 格式化时间，用于日志
			formatTime() {
				const date = new Date();
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				const seconds = date.getSeconds().toString().padStart(2, '0');
				const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${milliseconds}`;
			}
		}
	}
</script>

<style lang="scss">
.container {
	padding: 30rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 规则列表
.rule-card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
	overflow: hidden;
}

.rule-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #EEEEEE;
}

.rule-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.rule-tag {
	font-size: 24rpx;
	color: #FFFFFF;
	background-color: #FF6600;
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
}

.rule-content {
	padding: 20rpx 30rpx;
}

.rule-item {
	display: flex;
	margin-bottom: 16rpx;
}

.rule-item:last-child {
	margin-bottom: 0;
}

.item-label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
}

.item-value {
	font-size: 28rpx;
	color: #333;
}

.rule-footer {
	padding: 20rpx 30rpx 30rpx;
	display: flex;
	justify-content: center;
}

.rule-btn {
	background-color: #FF6600;
	color: #FFFFFF;
	font-size: 28rpx;
	padding: 16rpx 0;
	border-radius: 40rpx;
	text-align: center;
	width: 300rpx;
}

// 无数据提示
.no-data {
	text-align: center;
	padding: 100rpx 0;
	font-size: 28rpx;
	color: #999;
	background-color: #FFFFFF;
	border-radius: 16rpx;
}
</style> 