<template>
<view>
	<block v-if="isload">
		<block v-if="sysset.showgzts">
			<view style="width:100%;height:88rpx"> </view>
			<view class="follow_topbar">
				<view class="headimg"><image :src="sysset.logo"/></view>
				<view class="info">
					<view class="i">欢迎进入 <text :style="{color:t('color1')}">{{sysset.name}}</text></view>
					<view class="i">关注公众号享更多专属服务</view>
				</view>
				<view class="sub" @tap="showsubqrcode" :style="{'background-color':t('color1')}">立即关注</view>
			</view>
			<uni-popup id="qrcodeDialog" ref="qrcodeDialog" type="dialog">
				<view class="qrcodebox">
					<image :src="sysset.qrcode" @tap="previewImage" :data-url="sysset.qrcode" class="img"/>
					<view class="txt">长按识别二维码关注</view>
					<view class="close" @tap="closesubqrcode">
						<image :src="pre_url+'/static/img/close2.png'" style="width:100%;height:100%"/>
					</view>
				</view>
			</uni-popup>
		</block>

		<view style="position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx" v-if="bboglist.length>0">
			<swiper style="position:relative;height:54rpx;width:350rpx;" :autoplay="true" :interval="5000" :vertical="true">
				<swiper-item v-for="(item, index) in bboglist" :key="index" @tap="goto" :data-url="'/pagesB/huodongbaoming/product?id=' + item.proid" class="flex-y-center">
					<image :src="item.headimg" style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px"/>
					<view style="width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx">{{item.nickname}} {{item.showtime}}预约了该活动</view>
				</swiper-item>
			</swiper>
		</view>
		<!-- <view class="topbox"><image :src="pre_url+'/static/img/goback.png'" class="goback" /></view> -->
		<view class="swiper-container" v-if="isplay==0" >	
			<swiper class="swiper" :indicator-dots="false" :autoplay="true" :interval="500000" @change="swiperChange">
				<block v-for="(item, index) in product.pics" :key="index">
					<swiper-item class="swiper-item">
						<view class="swiper-item-view"><image class="img" :src="item" mode="widthFix"/></view>
					</swiper-item>
				</block>
			</swiper>
			<view class="imageCount" v-if="(product.pics).length > 1">{{current+1}}/{{(product.pics).length}}</view>
		</view>	
		<view class="seckill_title" >
					 
			<view class="f3" >
				<view class="t1" v-if="!product.is_start" :style="!product.is_start?'padding-left:35%':'padding-left:20%'">报名未开始</view>
				<view class="t1" v-else  style="letter-spacing: 0.5px;align-items: center;padding-top: 3px;" >距报名截止还剩</view>
				<view class="t2"   v-if="product.is_start" id="djstime" style="align-items: center;padding-top: 3px;" ><text class="djsspan">{{day}}</text> 天<text class="djsspan">{{djshour}}</text> 时 <text class="djsspan">{{djsmin}}</text> 分</view>
				
			</view>
		 </view>
		<view class="header"> 
			<view class="price_share">
				<view class="title">{{product.name}}</view>
				<view class="share" @tap="shareClick"><image class="img" :src="pre_url+'/static/img/share.png'"/><text class="txt">分享</text></view>
			</view>
			<view class="pricebox flex">
				<view class="price">
					<block v-if="product.is_fufei==1 && (product.score_price>0 || product.sell_price>0)">
						<view class="f1"  :style="{color:t('color1')}"  >
							<block v-if="product.score_price>0 && product.sell_price>0">
								{{product.score_price}}{{t('积分')}}<text v-if="product.sell_price*1>0">+{{product.sell_price}}元</text>
							</block>
							<block v-else-if="product.sell_price>0">
								<text v-if="product.sell_price*1>0">{{product.sell_price}}元</text>
							</block>
							<block v-else-if="product.score_price>0">
								<text >{{product.score_price}}{{t('积分')}}</text>
							</block>
							<!--{{product.min_price}}<text v-if="product.max_price!=product.min_price">-{{product.max_price}}</text><text style="font-size:24rpx;font-weight:normal;padding-left:6rpx">元</text>-->
						</view>
						
					</block>
					<view class="f1" v-else :style="{color:t('color1')}">
						免费
					</view>
					<!-- <view class="f2" v-if="product.market_price*1 > product.sell_price*1">￥{{product.market_price}}</view> -->
				</view>
				<view class="sales_stock">
					<view class="f1" >已报名：{{product.sales}} </view>
					<!-- <view class="f1" >访问量：{{product.viewnum}} </view> -->
				</view>
				
			</view>			
			<view class="sellpoint" v-if="product.sellpoint">{{product.sellpoint}}</view>	
			<view style="margin:20rpx 0;font-size:22rpx">访问量：{{product.viewnum}}  </view>		
		</view>		
		<view class="cuxiaopoint" v-if="couponlist.length>0">
			<view class="f0">优惠</view>
			<view class="f1" @tap="showcuxiaodetail">
				<view v-for="(item, index) in couponlist" :key="index" class="t" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}"><text class="t0" style="padding:0 6px">券</text><text class="t1">{{item.name}}</text></view>
			</view>
			<view class="f2" @tap="showcuxiaodetail">
				<image :src="pre_url+'/static/img/arrow-point.png'" mode="widthFix"/>
			</view>
		</view>	
		<view class="choosebox">
			
			<block v-if="product.huodong_danwei.length>0">
				<block v-for="(item,index) in product.huodong_danwei">
					<view class="choose">
						<view class="f0">{{text['活动单位']}}</view>
						<view class="f1 flex1">
							{{item.name}}
						</view>
						<!-- <image class="f2" :src="pre_url+'/static/img/arrowright.png'"/> -->
					</view>
					<view  class="choose" >
						<view class="f0">{{text['联系电话']}}</view>
						<view class="f1 flex1">{{item.tel}}</view>
						<image class="f2" :src="pre_url+'/static/img/peisong/tel2.png'" @tap.stop="callphone" :data-phone="item.tel" />
					</view>
				</block>
			</block>
			<block v-else>
				<view class="choose">
					<view class="f0">{{text['活动单位']}}</view>
					<view class="f1 flex1">
						{{product.zhubanfang_name}}
					</view>
					<!-- <image class="f2" :src="pre_url+'/static/img/arrowright.png'"/> -->
				</view>
				<view  class="choose" >
					<view class="f0">{{text['联系电话']}}</view>
					<view class="f1 flex1">{{product.zhubanfang_tel}}</view>
					<image class="f2" :src="pre_url+'/static/img/peisong/tel2.png'" @tap.stop="callphone" :data-phone="product.zhubanfang_tel" />
				</view>
			</block>	
			
			<view  class="choose" v-if="product.huodong_address"  >
				<view class="f0">{{text['活动地址']}}</view>
				<view class="f1 flex1">{{product.huodong_address}}</view>
				<image class="f2" :src="pre_url+'/static/img/address.png'" @tap="openLocation" :data-address="product.huodong_address" :data-latitude="product.latitude" :data-longitude="product.longitude" />
			</view>
			<view class="choose">
				<view class="f0">{{text['活动时间']}}</view>
				<view class="f1 flex1 flex-y-center" >
					<text class="xuanzefuwu-text">{{product.huodong_start_time}}至</text> 
					<text class="xuanzefuwu-text">{{product.huodong_end_time}}</text> 
				</view>
				<!-- <image class="f2" :src="pre_url+'/static/img/arrowright.png'"/> -->
			</view>
		</view>	
		<view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">活动详情</view><view class="t2"></view><view class="t1"></view></view>
		<view class="detail">
			<dp :pagecontent="pagecontent"></dp>
		</view>
		<view class="orderlistbox" v-if="product.show_member_order ==1 && orderlistlength>0">
			<view class="title">
				<view class="f1">已报名</view>
				<view class="f2" @tap="goto" :data-url="'proorderlist?proid=' + product.id">更多<image style="width:32rpx;height:32rpx;" :src="pre_url+'/static/img/arrowright.png'"/></view>
			</view>
			<view class="orderlist">
				<view class="item" v-if="orderlistlength>0">
				<block v-for="(itemp, index) in orderlist" :key="index">
					<view class="f1">
						<image class="t1" :src="itemp.headimg" style="margin: 7px 0px 6px 0px;"/>
						<view class="t2" style="margin-bottom: 10px;">{{itemp.nickname}}</view>
						<view class="flex1"></view>						
					</view>
					</block>
				</view>
				<view v-else class="nocomment">暂无</view>
			</view>
		</view>
		<view style="width:100%;height:140rpx;"></view>
		<view class="bottombar flex-row" :class="menuindex>-1?'tabbarbot':''" v-if="product.status==1">
			<view class="f1">
				<view class="item" @tap="goto" :data-url="'prolist?bid=' + product.bid" >
					<image class="img" :src="pre_url+'/static/img/shou.png'"/>
					<view class="t1">首页</view>
				</view>
				<view class="item" @tap="goto" :data-url="kfurl" v-if="kfurl!='contact::'">
					<image class="img" :src="pre_url+'/static/img/kefu.png'"/>
					<view class="t1">客服</view>
				</view>
				<button class="item" v-else open-type="contact" show-message-card="true">
					<image class="img" :src="pre_url+'/static/img/kefu.png'"/>
					<view class="t1">客服</view>
				</button>
				<view class="item" @tap="addfavorite">
					<image class="img" :src="pre_url+'/static/img/shoucang.png'"/>
					<view class="t1">{{isfavorite?'已收藏':'收藏'}}</view>
				</view>
			</view>
			<view class="op">
				<view class="tobuy flex-x-center flex-y-center" :style="{background:'#aaa'}" v-if="product.isend">已结束</view>
				<view class="tobuy flex-x-center flex-y-center" @tap="tobuy" :style="{background:t('color1')}" v-else-if="product.is_fufei == 1">立即报名</view>
				<view class="tobuy flex-x-center flex-y-center" @tap="tobuy2" :style="{background:t('color1')}" v-else>立即报名</view>
			</view>
		</view>
		<huodongbmbuydialog v-if="buydialogShow" :proid="product.id" :btntype="btntype"  @currgg="currgg" @buydialogChange="buydialogChange" :menuindex="menuindex" @addcart="addcart" :isfuwu="isfuwu"  @tobuy="tobuy"></huodongbmbuydialog>
		<scrolltop :isshow="scrolltopshow"></scrolltop>	
		<view v-if="sharetypevisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal" style="height:320rpx;min-height:320rpx">
				<!-- <view class="popup__title">
					<text class="popup__title-text">请选择分享方式</text>
					<image :src="pre_url+'/static/img/close.png'" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hidePstimeDialog"/>
				</view> -->
				<view class="popup__content">
					<view class="sharetypecontent">
						<view class="f1" @tap="shareapp" v-if="getplatform() == 'app'">
							<image class="img" :src="pre_url+'/static/img/sharefriends.png'"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'mp'">
							<image class="img" :src="pre_url+'/static/img/sharefriends.png'"/>
							<text class="t1">分享给好友</text>
						</view>
					<!-- 	<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'h5'">
							<image class="img" :src="pre_url+'/static/img/sharefriends.png'"/>
							<text class="t1">分享给好友</text>
						</view> -->
						<button class="f1" open-type="share" v-else-if="getplatform() != 'h5'">
							<image class="img" :src="pre_url+'/static/img/sharefriends.png'"/>
							<text class="t1">分享给好友</text>
						</button>
						<view class="f2" @tap="showPoster">
							<image class="img" :src="pre_url+'/static/img/sharepic.png'"/>
							<text class="t1">生成分享图片</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="posterDialog" v-if="showposter">
			<view class="main">
				<view class="close" @tap="posterDialogClose"><image class="img" :src="pre_url+'/static/img/close.png'"/></view>
				<view class="content">
					<image class="img" :src="posterpic" mode="widthFix" @tap="previewImage" :data-url="posterpic"></image>
				</view>
			</view>
		</view>	
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
var interval = null;

export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			text:[],
			pre_url:app.globalData.pre_url,
			isload:false,
			buydialogShow: false,
			btntype:1,
			isfavorite: false,
			current: 0,
			isplay: 0,
			business: "",
			product: [],
			couponlist: "",
			pagecontent: "",
			shopset: {},
			sysset:{},
			title: "",
			orderlistlength:0,
			bboglist: "",
			sharepic: "",
			sharetypevisible: false,
			showposter: false,
			posterpic: "",
			scrolltopshow: false,
			kfurl:'',
			ggname:'请选择信息',
			index:0,
			num:0,
			set:[],
			end_time:'',
			djshour:'00',
			djsmin:'00',
			djssec:'00',
			day:'',
			orderlist:[],
		};
	},
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(shareOption){
		return this._sharewx({title:this.product.name,pic:this.product.pic});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});
		var query = (sharewxdata.path).split('?')[1]+'&seetype=circle';
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
	onUnload: function () {
		clearInterval(interval);
	},

	methods: {
		getdata:function(){
			var that = this;
			var id = this.opt.id || 0;
			that.loading = true;
			app.get('ApiHuodongBaoming/product', {id: id}, function (res) {
				
				if (res.status == 0) {
					app.alert(res.msg);
					return;
				}
				var product = res.product;
				var pagecontent = JSON.parse(product.detail);			
				that.business = res.business;
				that.product = product;
				that.end_time = res.date;
				that.orderlist = res.orderlist;
				that.orderlistlength = res.orderlist.length;
				that.pagecontent = pagecontent;
				that.shopset = res.shopset;
				that.sysset = res.sysset;
				that.set = res.set;
				that.title = product.name;
				that.isfavorite = res.isfavorite;
				that.sharepic = product.pics[0];
				that.isfuwu = res.isfuwu
				that.text = res.text
				that.huodong_end_time_date = res.huodong_end_time_date
				that.huodong_start_time_date = res.huodong_start_time_date
				that.nowtime = res.now_time		 
				uni.setNavigationBarTitle({
					title: product.name
				});
				that.kfurl = '/pagesExt/kefu/index?bid='+product.bid;
				if(app.globalData.initdata.kfurl != ''){
					that.kfurl = app.globalData.initdata.kfurl;
				}
				if(that.business && that.business.kfurl){
					that.kfurl = that.business.kfurl;
				}
				that.loading = false;
				that.loaded({title:product.name,pic:product.pic});
				setInterval(function () {
						that.nowtime = that.nowtime + 1;
						that.getTgdjs();
					}, 1000);
			});
		},
		swiperChange: function (e) {
			var that = this;
			that.current = e.detail.current
		},
		buydialogChange: function (e) {
			if(!this.buydialogShow){
				this.btntype = e.currentTarget.dataset.btntype;
			}
			this.buydialogShow = !this.buydialogShow;
		},
		currgg: function (e) {
			console.log(e);
			var that = this
			this.ggname = e.ggname;
			that.ggid = e.ggid
			that.proid = e.proid
			that.num = e.num
		},
		//拨打电话
		callphone:function(e) {
			var phone = e.currentTarget.dataset.phone;
			uni.makePhoneCall({
				phoneNumber: phone,
				fail: function () {
				}
			});
		  },
		
		//导航
		openLocation:function(e){
			console.log(e)
			var latitude = parseFloat(e.currentTarget.dataset.latitude)
			var longitude = parseFloat(e.currentTarget.dataset.longitude)
			var address = e.currentTarget.dataset.address
			uni.openLocation({
			 latitude:latitude,
			 longitude:longitude,
			 name:address,
			 scale: 13
		 })		
		},
		
 

		//收藏操作
		addfavorite: function () {
			var that = this;
			var proid = that.product.id;
			app.post('ApiHuodongBaoming/addfavorite', {proid: proid,type: 'yuyue'}, function (data) {
				if (data.status == 1) {
					that.isfavorite = !that.isfavorite;
				}
				app.success(data.msg);
			});
		},
		shareClick: function () {
			this.sharetypevisible = true;
		},
		handleClickMask: function () {
			this.sharetypevisible = false
		},
		showPoster: function () {
			var that = this;
			that.showposter = true;
			that.sharetypevisible = false;
			app.showLoading('生成海报中');
			app.post('ApiHuodongBaoming/getposter', {proid: that.product.id}, function (data) {
				app.showLoading(false);
				if (data.status == 0) {
					app.alert(data.msg);
				} else {
					that.posterpic = data.poster;
				}
			});
		},	
		
	
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			that.sharetypevisible = false;
			uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.product.name;
						//sharedata.summary = app.globalData.initdata.desc;
						sharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pagesB/huodongbaoming/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;
						sharedata.imageUrl = that.product.pic;
						var sharelist = app.globalData.initdata.sharelist;
						if(sharelist){
							for(var i=0;i<sharelist.length;i++){
								if(sharelist[i]['indexurl'] == '/pagesB/huodongbaoming/product'){
									sharedata.title = sharelist[i].title;
									sharedata.summary = sharelist[i].desc;
									sharedata.imageUrl = sharelist[i].pic;
									if(sharelist[i].url){
										var sharelink = sharelist[i].url;
										if(sharelink.indexOf('/') === 0){
											sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;
										}
										if(app.globalData.mid>0){
											 sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;
										}
										sharedata.href = sharelink;
									}
								}
							}
						}
						uni.share(sharedata);
					}
        }
      });
		},
		showsubqrcode:function(){
			this.$refs.qrcodeDialog.open();
		},
		closesubqrcode:function(){
			this.$refs.qrcodeDialog.close();
		},
		getTgdjs:function(){
			var that = this
			var nowtime = that.nowtime*1;
			var starttime = that.product.starttime*1;
			var endtime = that.huodong_end_time_date*1;
		
			if(endtime < nowtime || nowtime < starttime){ //已结束
				that.tuangou_status = 2
				that.djshour = '00';
				that.day ="00";
				that.djsmin = '00';
				that.djssec = '00';
			}else{
				if(starttime > nowtime){ //未开始
					that.tuangou_status = 0
					var totalsec = starttime - nowtime;
				}else{ //进行中
					that.tuangou_status = 1
					var totalsec = endtime - nowtime;
				}
				var day = Math.floor(totalsec/(3600*24)); 
			 

				var houer = Math.floor((totalsec-day*3600*24)/3600);
				var min = Math.floor((totalsec -day*3600*24- houer *3600)/60);
				var sec = totalsec -day*3600*24- houer*3600 - min*60
				// var djs = (houer<10?'0':'')+houer+'时'+(min<10?'0':'')+min+'分'+(sec<10?'0':'')+sec+'秒';
				var djshour = (houer<10?'0':'')+houer
				var djsmin = (min<10?'0':'')+min
				var djssec = (sec<10?'0':'')+sec
				that.djshour = djshour;
				that.djsmin = djsmin;
				that.djssec = djssec;
				that.day = day;
			}
		},
		tobuy: function (e) {
			var that = this;
			var ks = that.ks;
			var proid = that.product.id;
			var ggid = that.ggid;
			var num = that.num;
			var prodata = proid + ',' + ggid + ',' + num;
			if(!ggid || ggid==undefined){
					this.buydialogShow = !this.buydialogShow;
					return;
			}

			app.goto('/pagesB/huodongbaoming/buy?prodata=' + prodata);
		},
		tobuy2: function (e) {
			var that = this;
			var ks = that.ks;
			var proid = that.product.id;
			var ggid = 0;
			var num = 1;
			var prodata = proid + ',' + ggid + ',' + num;
			app.goto('/pagesB/huodongbaoming/buy?prodata=' + prodata);
		},
		posterDialogClose: function () {
			this.showposter = false;;
		},
	}
};
</script>
<style>
	.dp-cover{height: auto; position: relative;}
	.dp-cover-cover{position:fixed;z-index:99999;cursor:pointer;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color: inherit;}
	
.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}
.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}
.follow_topbar .headimg image {height:64rpx; width:64rpx;}
.follow_topbar .info {height:56rpx; padding:16rpx 0;}
.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;}
.follow_topbar .info {height:80rpx; float:left;}
.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}
.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.qrcodebox .img{width:400rpx;height:400rpx}
.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}

.goback{ position: absolute; top:0 ;width:64rpx ; height: 64rpx;z-index: 10000; margin: 30rpx;}
.goback img{ width:64rpx ; height: 64rpx;}

.swiper-container{position:relative}
.swiper {width: 100%;height: 750rpx;overflow: hidden;}
.swiper-item-view{width: 100%;height: 750rpx;}
.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}

.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:80rpx;}

.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}
.provideo image{width:50rpx;height:50rpx;}
.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}

.header {padding: 20rpx 3%;background: #fff; width: 94%; border-radius:10rpx; margin: auto; margin-bottom: 20rpx; position: relative;}
.header .pricebox{ width: 100%;border:1px solid #fff; justify-content: space-between;}
.header .pricebox .price{display:flex;align-items:flex-end}
.header .pricebox .price .f1{font-size:36rpx;color:#51B539;font-weight:bold}
.header .pricebox .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}
.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between}
.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center}
.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}
.header .price_share .share .txt{color:#333333;font-size:20rpx}
.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}
.header .sellpoint{font-size:28rpx;color: #666;padding-top:20rpx;}
.header .sales_stock{height:60rpx;line-height:60rpx;font-size:24rpx;color:#777777; }
.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}

.choosebox{margin: auto;width: 94%; border-radius:10rpx; background: #fff;  }

.choose{ width: 100%;display:flex;align-items:center;justify-content: center; margin: auto; padding: 20rpx 3%; color: #333; border-bottom:1px solid #eee }
.choose .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center; width: 20%;}
.choose .f2{ width: 32rpx; height: 32rpx;}
.choose .xuanzefuwu-text{display: inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;max-width: 385rpx;font-size:22rpx;}
.choose .f1{ width:77%}

.choosedate{ display:flex;align-items:center;ustify-content: center;   margin:auto; height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; }
.choosedate .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}
.choosedate .f2{ width: 32rpx; height: 32rpx;}

.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}
.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}
.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}
.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}
.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}
.cuxiaopoint .f1 .t1{padding:0 4px}
.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}
.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}


.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}
.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}
.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1600rpx;}
.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}
.popup__title-text{font-size:32rpx}
.popup__close{position:absolute;top:34rpx;right:34rpx}
.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}

.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}
.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}
.shop .p2{padding-left:10rpx}
.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}
.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}
.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}

.detail{min-height:200rpx; width: 94%; margin: auto; border-radius: 10rpx;}

.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:40rpx;margin-bottom:30rpx}
.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}
.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}
.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}

.commentbox{width:90%;background:#fff;padding:0 3%;border-radius:10rpx;margin: auto;margin-top:20rpx; }
.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}
.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}
.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}
.commentbox .nocomment{height:100rpx;line-height:100rpx}

.comment{display:flex;flex-direction:column;min-height:200rpx;}
.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}
.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}
.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}
.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}
.comment .item .f1 .t3{text-align:right;}
.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}
.comment .item .score{ font-size: 24rpx;color:#f99716;}
.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}
.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}
.comment .item .f2 .t1{color:#333;font-size:28rpx;}
.comment .item .f2 .t2{display:flex;width:100%}
.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}
.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}
.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}

.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 30rpx 0 10rpx;align-items:center;}
.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}
.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:50%;position:relative}
.bottombar .f1 .item .img{ width:44rpx;height:44rpx}
.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}
.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}
.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; border-top-right-radius:20rpx;border-bottom-left-radius:20rpx;}

/*订单列表*/
.orderlistbox{width: 94%;background:#fff;padding:0 3%;border-radius:10rpx;margin: auto;margin-top:20rpx; }
.orderlistbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}
.orderlistbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:24rpx}
.orderlistbox .title .f2{color:#333;font-size:24rpx;display:flex;align-items:center}
.orderlistbox .nocomment{height:100rpx;line-height:100rpx}
.orderlist .item{background-color:#fff;display:flex; width:100%; align-items:center;justify-content:flex-start}
.orderlist .item .f1{display: flex; flex-direction: column; width: 15%;}
.orderlist .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}
.orderlist .item .f1 .t2{color:#333;font-weight:bold;width:70rpx;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;text-align: center;}

.tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold;width:90%;margin:20rpx 5%;border-radius:36rpx;}

.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}
.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}
.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}
.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}
.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}
.cuxiaopoint .f1 .t1{padding:0 4px}
.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}
.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}
.seckill_title{ width:100%;height:50rpx;position: absolute;z-index: 999;top: 702rpx;}
 
.seckill_title .f0{width:88rpx;height:88rpx;margin-left:20rpx}
.seckill_title .f1{flex:1;padding:10rpx 20rpx;display:flex;flex-direction:column;}
.seckill_title .f1 .t1{font-size:40rpx;color:#fff;line-height:50rpx;}
.seckill_title .f1 .t1 .x2{padding-left:8rpx;font-size:26rpx;color:#fff;text-decoration:line-through}
.seckill_title .f1 .t2{color:#fff;font-size:22rpx}
.seckill_title .f1 .t1{margin-left: 50px;}
.seckill_title .f3{padding-left: 20%;padding-right: 20%; height:50rpx;background-color:rgba(255,230,233,0.8);color:#333;display:flex;justify-content: space-between;}
.seckill_title .f3 .t2{color:#FF3143}
.seckill_title .djsspan{font-size:22rpx;border-radius:8rpx;background:#FF3143;color:#fff;text-align:center;padding:4rpx 8rpx;margin:0 4rpx}
</style>