<template>
<view class="container">
	<view class="search-bar">
		<view class="search-input">
			<image class="search-icon" src="/static/img/search_ico.png"></image>
			<input type="text" v-model="search.keyword" placeholder="搜索商品名称" @confirm="searchProducts" />
		</view>
		<view class="search-btn" @tap="searchProducts">搜索</view>
	</view>

	<view class="category-filter" v-if="categories.length > 0">
		<scroll-view scroll-x="true" class="category-scroll">
			<view class="category-item" :class="{active: search.cid === ''}" @tap="selectCategory('')">全部</view>
			<view class="category-item" :class="{active: search.cid === item.id}" v-for="(item, index) in categories" :key="index" @tap="selectCategory(item.id)">
				{{item.name}}
			</view>
		</scroll-view>
	</view>

	<view class="product-list" v-if="productList.length > 0">
		<view class="product-item" v-for="(item, index) in productList" :key="index">
			<image class="product-img" :src="item.pic" mode="aspectFill"></image>
			<view class="product-info">
				<text class="product-name">{{item.name}}</text>
				<view class="product-price">￥{{item.cost_price || 0}}</view>
				<view class="product-stock">库存: {{item.stock}}</view>
			</view>
			<view class="product-action">
				<view class="quantity-control">
					<view class="minus" @tap="updateQuantity(item, -1)">-</view>
					<input type="number" class="quantity-input" :value="getSelectedQuantity(item.id)" @input="onQuantityInput($event, item)" />
					<view class="plus" @tap="updateQuantity(item, 1)">+</view>
				</view>
			</view>
		</view>
	</view>
	<nodata v-if="productList.length === 0 && !loading"></nodata>
	<loading v-if="loading"></loading>
	<nomore v-if="nomore"></nomore>

	<view class="bottom-bar" v-if="selectedProducts.length > 0">
		<view class="order-summary">
			<text class="summary-text">已选择 {{getTotalItems()}} 种商品</text>
			<text class="summary-price">总计: ￥{{getTotalPrice()}}</text>
		</view>
		<view class="order-actions">
			<view class="action-btn clear" @tap="clearSelected">清空</view>
			<view class="action-btn submit" @tap="showConfirmPopup">提交订单</view>
		</view>
	</view>

	<!-- 提交订单弹窗 -->
	<view class="popup-mask" v-if="showConfirm" @tap="hideConfirmPopup"></view>
	<view class="popup-content" v-if="showConfirm">
		<view class="popup-header">
			<text class="popup-title">确认订单</text>
			<text class="popup-close" @tap="hideConfirmPopup">×</text>
		</view>
		<view class="popup-body">
			<view class="popup-section">
				<view class="popup-section-title">订单商品</view>
				<view class="popup-product-list">
					<view class="popup-product-item" v-for="(item, index) in selectedProductsDetail" :key="index">
						<text class="popup-product-name">{{item.name}}</text>
						<text class="popup-product-quantity">x{{item.quantity}}</text>
						<text class="popup-product-price">￥{{(item.price * item.quantity).toFixed(2)}}</text>
					</view>
				</view>
			</view>
			
			<view class="popup-section">
				<view class="popup-section-title">订单备注</view>
				<textarea class="popup-remark" v-model="orderRemark" placeholder="请输入订单备注信息（选填）"></textarea>
			</view>
			
			<view class="popup-summary">
				<text class="popup-summary-label">订单总计</text>
				<text class="popup-summary-value">￥{{getTotalPrice()}}</text>
			</view>
		</view>
		<view class="popup-footer">
			<view class="popup-btn cancel" @tap="hideConfirmPopup">取消</view>
			<view class="popup-btn confirm" @tap="submitOrder">确认提交</view>
		</view>
	</view>
	
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      loading: false,
      nomore: false,
      pagenum: 1,
      productList: [],
      selectedProducts: [], // 选中的商品 [{id, quantity}]
      search: {
        keyword: '',
        cid: ''
      },
      categories: [],
      showConfirm: false,
      orderRemark: '',
      selectedProductsDetail: [] // 选中商品的详细信息，包括名称、价格等
    };
  },
  onLoad: function(opt) {
    this.opt = app.getopts(opt);
    this.getCategories();
    this.getProductList();
  },
  onReachBottom: function() {
    if (!this.nomore && !this.loading) {
      this.pagenum++;
      this.getProductList(true);
    }
  },
  methods: {
    getCategories: function() {
      var that = this;
      app.post('ApiAdminPurchase/getProductCategories', {}, function(res) {
        if (res.code === 0) {
          that.categories = res.data || [];
        } else {
          app.error(res.msg);
        }
      });
    },
    getProductList: function(loadmore) {
      if (!loadmore) {
        this.pagenum = 1;
        this.productList = [];
        this.nomore = false;
      }
      
      var that = this;
      that.loading = true;
      
      app.post('ApiAdminPurchase/getProductList', {
        page: that.pagenum,
        limit: 10,
        name: that.search.keyword,
        cid: that.search.cid
      }, function(res) {
        that.loading = false;
        
        if (res.code === 0) {
          var data = res.data.list;
          
          if (that.pagenum === 1) {
            that.productList = data;
            if (data.length === 0) {
              that.nomore = true;
            }
          } else {
            if (data.length === 0) {
              that.nomore = true;
            } else {
              that.productList = that.productList.concat(data);
            }
          }
        } else {
          app.error(res.msg);
        }
      });
    },
    searchProducts: function() {
      this.getProductList();
    },
    selectCategory: function(cid) {
      this.search.cid = cid;
      this.getProductList();
    },
    getSelectedQuantity: function(productId) {
      var selected = this.selectedProducts.find(item => item.id === productId);
      return selected ? selected.quantity : 0;
    },
    updateQuantity: function(product, delta) {
      var that = this;
      var index = that.selectedProducts.findIndex(item => item.id === product.id);
      
      if (index > -1) {
        var newQuantity = that.selectedProducts[index].quantity + delta;
        
        if (newQuantity <= 0) {
          // 如果数量减为0，则从选中列表中移除
          that.selectedProducts.splice(index, 1);
        } else if (newQuantity <= product.stock) {
          // 如果数量在库存范围内，则更新数量
          that.selectedProducts[index].quantity = newQuantity;
        } else {
          app.error('超出可用库存');
        }
      } else if (delta > 0) {
        // 如果之前未选中且要增加数量，则添加到选中列表
        that.selectedProducts.push({
          id: product.id,
          quantity: 1
        });
      }
      
      // 更新选中商品的详细信息
      that.updateSelectedProductsDetail();
    },
    onQuantityInput: function(e, product) {
      var that = this;
      var value = parseInt(e.detail.value);
      
      if (isNaN(value) || value < 0) {
        value = 0;
      }
      
      if (value > product.stock) {
        value = product.stock;
        app.error('超出可用库存');
      }
      
      var index = that.selectedProducts.findIndex(item => item.id === product.id);
      
      if (value === 0 && index > -1) {
        // 如果输入为0且之前已选中，则移除
        that.selectedProducts.splice(index, 1);
      } else if (value > 0) {
        if (index > -1) {
          // 如果之前已选中，则更新数量
          that.selectedProducts[index].quantity = value;
        } else {
          // 如果之前未选中，则添加
          that.selectedProducts.push({
            id: product.id,
            quantity: value
          });
        }
      }
      
      // 更新选中商品的详细信息
      that.updateSelectedProductsDetail();
    },
    updateSelectedProductsDetail: function() {
      var that = this;
      that.selectedProductsDetail = [];
      
      that.selectedProducts.forEach(selectedItem => {
        var productInfo = that.productList.find(product => product.id === selectedItem.id);
        
        if (productInfo) {
          that.selectedProductsDetail.push({
            id: productInfo.id,
            name: productInfo.name,
            price: productInfo.cost_price || 0,
            pic: productInfo.pic,
            quantity: selectedItem.quantity
          });
        }
      });
    },
    getTotalItems: function() {
      return this.selectedProducts.length;
    },
    getTotalPrice: function() {
      var totalPrice = 0;
      
      this.selectedProductsDetail.forEach(item => {
        totalPrice += item.price * item.quantity;
      });
      
      return totalPrice.toFixed(2);
    },
    clearSelected: function() {
      var that = this;
      
      app.confirm('确定要清空已选商品吗?', function() {
        that.selectedProducts = [];
        that.selectedProductsDetail = [];
      });
    },
    showConfirmPopup: function() {
      if (this.selectedProducts.length === 0) {
        app.error('请选择要进货的商品');
        return;
      }
      
      this.showConfirm = true;
    },
    hideConfirmPopup: function() {
      this.showConfirm = false;
    },
    submitOrder: function() {
      var that = this;
      
      if (that.selectedProducts.length === 0) {
        app.error('请选择要进货的商品');
        return;
      }
      
      var productIds = [];
      var quantities = [];
      
      that.selectedProducts.forEach(item => {
        productIds.push(item.id);
        quantities.push(item.quantity);
      });
      
      app.post('ApiAdminPurchase/createPurchaseOrder', {
        productIds: productIds.join(','),
        quantities: quantities.join(','),
        remark: that.orderRemark
      }, function(res) {
        if (res.code === 0) {
          app.success('订单创建成功');
          that.hideConfirmPopup();
          
          setTimeout(function() {
            app.goto('detail?id=' + res.data.order_id);
          }, 1000);
        } else {
          app.error(res.msg);
        }
      });
    }
  }
};
</script>

<style>
.container{ width:100%; padding-bottom:120rpx; }

.search-bar{ display:flex; align-items:center; padding:20rpx; background:#fff; }
.search-input{ flex:1; height:70rpx; display:flex; align-items:center; background:#f5f5f5; border-radius:35rpx; padding:0 20rpx; }
.search-icon{ width:28rpx; height:28rpx; margin-right:10rpx; }
.search-input input{ flex:1; height:100%; font-size:28rpx; }
.search-btn{ width:100rpx; height:70rpx; line-height:70rpx; text-align:center; font-size:28rpx; margin-left:20rpx; color:#1989fa; }

.category-filter{ background:#fff; border-bottom:1px solid #f0f0f0; }
.category-scroll{ white-space:nowrap; padding:15rpx 20rpx; }
.category-item{ display:inline-block; padding:10rpx 30rpx; margin-right:20rpx; font-size:26rpx; color:#666; background:#f5f5f5; border-radius:30rpx; }
.category-item.active{ background:#1989fa; color:#fff; }

.product-list{ padding:10rpx 20rpx; }
.product-item{ display:flex; padding:20rpx; margin-bottom:20rpx; background:#fff; border-radius:8rpx; }
.product-img{ width:160rpx; height:160rpx; border-radius:8rpx; }
.product-info{ flex:1; margin-left:20rpx; display:flex; flex-direction:column; justify-content:space-between; }
.product-name{ font-size:28rpx; color:#333; margin-bottom:10rpx; line-height:1.3; }
.product-price{ font-size:30rpx; color:#ff6b00; margin-bottom:10rpx; }
.product-stock{ font-size:24rpx; color:#999; }
.product-action{ width:200rpx; display:flex; flex-direction:column; justify-content:flex-end; }

.quantity-control{ display:flex; align-items:center; justify-content:space-between; border:1px solid #ddd; border-radius:8rpx; overflow:hidden; }
.minus, .plus{ width:60rpx; height:60rpx; line-height:60rpx; text-align:center; font-size:36rpx; background:#f5f5f5; }
.quantity-input{ flex:1; height:60rpx; text-align:center; font-size:28rpx; border-left:1px solid #ddd; border-right:1px solid #ddd; }

.bottom-bar{ position:fixed; bottom:0; left:0; width:100%; height:100rpx; display:flex; background:#fff; border-top:1px solid #f0f0f0; z-index:100; }
.order-summary{ flex:1; display:flex; flex-direction:column; justify-content:center; padding-left:30rpx; }
.summary-text{ font-size:24rpx; color:#666; }
.summary-price{ font-size:30rpx; color:#ff6b00; font-weight:bold; }
.order-actions{ display:flex; }
.action-btn{ width:160rpx; height:100rpx; line-height:100rpx; text-align:center; font-size:28rpx; }
.action-btn.clear{ background:#f5f5f5; color:#666; }
.action-btn.submit{ background:#1989fa; color:#fff; }

.popup-mask{ position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:101; }
.popup-content{ position:fixed; bottom:0; left:0; width:100%; background:#fff; border-radius:20rpx 20rpx 0 0; z-index:102; animation:slideUp 0.3s ease; }
.popup-header{ display:flex; justify-content:space-between; align-items:center; padding:20rpx 30rpx; border-bottom:1px solid #f0f0f0; }
.popup-title{ font-size:32rpx; font-weight:bold; }
.popup-close{ font-size:40rpx; color:#999; }
.popup-body{ padding:20rpx 30rpx; max-height:700rpx; overflow-y:auto; }
.popup-section{ margin-bottom:30rpx; }
.popup-section-title{ font-size:28rpx; color:#666; margin-bottom:15rpx; }
.popup-product-list{ max-height:300rpx; overflow-y:auto; }
.popup-product-item{ display:flex; justify-content:space-between; padding:15rpx 0; border-bottom:1px solid #f5f5f5; }
.popup-product-name{ flex:1; font-size:28rpx; }
.popup-product-quantity{ font-size:28rpx; color:#666; margin:0 20rpx; }
.popup-product-price{ font-size:28rpx; color:#ff6b00; }
.popup-remark{ width:100%; height:150rpx; background:#f5f5f5; border-radius:8rpx; padding:20rpx; font-size:28rpx; box-sizing:border-box; }
.popup-summary{ display:flex; justify-content:space-between; padding:20rpx 0; font-size:32rpx; border-top:1px solid #f0f0f0; }
.popup-summary-value{ color:#ff6b00; font-weight:bold; }
.popup-footer{ display:flex; border-top:1px solid #f0f0f0; }
.popup-btn{ flex:1; height:100rpx; line-height:100rpx; text-align:center; font-size:32rpx; }
.popup-btn.cancel{ background:#f5f5f5; color:#666; }
.popup-btn.confirm{ background:#1989fa; color:#fff; }

@keyframes slideUp {
  from { transform:translateY(100%); }
  to { transform:translateY(0); }
}
</style> 