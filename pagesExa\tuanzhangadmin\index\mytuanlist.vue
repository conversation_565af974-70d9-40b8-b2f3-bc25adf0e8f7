<template>
	<view class="container">

		<block v-if="isload">
			<view class="background-container">
				<view class="header-bg" :style="'background:'+t('color1')"></view>
				<view class="header-gradient" :style="'background-image: linear-gradient('+t('color1')+', #f1f1f1);'">
				</view>
			</view>
			<view class="content-container">
				<view class="search-container" :style="'background:'+t('color1')">
					<view class="search-bar">
			<!-- 			<image class="back-icon" src="/static/img/arrow-left.png"></image> -->
						<view class="search-box">
							<view class="f1 flex-y-center">
								<image class="img" src="/static/img/search_ico.png"></image>
								<input :value="keyword" placeholder="搜索感兴趣的团"
									placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
							</view>
						</view>
					</view>


				
				</view>
			</view>

			<view class="article_list">
			
				
				
				<!--单排三图e-->
				<!-- 新增类型6 -->
				<!-- 新增类型6 -->

				<view style="position: absolute;top:50px;left: 0;width: 100%;">

					<view v-if="listtype=='6'" class="item-list" v-for="(item, index) in datalist" :key="item.id"
						>
						<view class="item-header" style="justify-content: space-between;">
							<view style="display: flex;align-items: center;">
								<image class="item-image" :src="item.pic"></image>
								<view class="item-author">{{item.author}}</view>
							</view>
							<view :style="'background:'+t('color1')" class="mbtn" @click="goto" :data-url="'/daihuobiji/kuaituan/tuanzhangdetail?id='+item.id">查看团详情</view>
						</view>
						<view @click="goto" :data-url="'/daihuobiji/kuaituan/detail?id='+item.id">
							<view class="item-name">{{item.name}}</view>
							<view class="item-price">
								<text class="currency-symbol">￥</text>
								<text class="price-value">{{item.priceRange}}</text>
							</view>
							<view class="item-pics">
								<image class="item-pic" v-for="(ite, ind) in item.pics" :key="ind" :src="ite" v-if="ind<3"></image>
							</view>
							<view class="item-footer">
								<view class="viewers">
									<image class="viewer-icon"
										src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
									<image class="viewer-icon"
										src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
									<image class="viewer-icon"
										src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
									<text class="view-count">{{item.readcount}}人看过</text>
								</view>
								<view class="footer-actions">
									<view class="contact-btn" @click.stop="contactMerchant(item.business_tel)" v-if="item.business_tel">
										<image class="contact-icon" src="/static/img/phone.png"></image>
										<text class="contact-text">联系商家</text>
									</view>
									<view class="share" @click.stop="shareTuan(item)">
										<image class="share-icon" src="../../../static/img/share.png"></image>
										<text class="share-text">点击分享</text>
									</view>
								</view>
							</view>
							
							<!-- 新增操作按钮区域 -->
							<view class="operation-buttons">
								<!-- <view class="op-btn" :style="{background:t('color1')}" @click.stop="editTuan(item.id)">
									<text class="op-btn-text">编辑</text>
								</view> -->
								<view class="op-btn" :style="{background:t('color1')}" @click.stop="copyTuan(item.id)">
									<text class="op-btn-text">复制</text>
								</view>
								<view class="op-btn" :style="{background:t('color1')}" @click.stop="setStatus(item.id, 1)" v-if="!item.status || item.status==0">
									<text class="op-btn-text">上架</text>
								</view>
								<view class="op-btn" :style="{background:t('color2')}" @click.stop="setStatus(item.id, 0)" v-else>
									<text class="op-btn-text">下架</text>
								</view>
								<!-- <view class="op-btn" :style="{background:item.is_hidden ? t('color1') : '#999'}" @click.stop="toggleHidden(item.id, item.is_hidden ? 0 : 1)">
									<text class="op-btn-text">{{item.is_hidden ? '显示' : '隐藏'}}</text>
								</view> -->
								<view class="op-btn" style="background:#ff4d4f" @click.stop="deleteTuan(item.id)">
									<text class="op-btn-text">删除</text>
								</view>
							</view>
						</view>
					</view>

				</view>


			</view>
		</block>
		<nodata v-if="nodata"></nodata>
		<nomore v-if="nomore"></nomore>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				nodata: false,
				nomore: false,
				keyword: '',
				datalist: [],
				pagenum: 1,
				clist: [],
				cnamelist: [],
				cidlist: [],
				datalist: [],
				cid: 0,
				bid: 0,
				listtype: 0,
				set: '',
				look_type: false,
			};
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.cid = this.opt.cid || 0;
			this.bid = this.opt.bid || 0;
			this.look_type = this.opt.look_type || false;
			if (this.opt.keyword) {
				this.keyword = this.opt.keyword;
			}
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onReachBottom: function() {
			if (!this.nomore && !this.nodata) {
				this.pagenum = this.pagenum + 1;
				this.getdata(true);
			}
		},
		methods: {
			getdata: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var keyword = that.keyword;
				var cid = that.cid;
				console.log(cid)
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
				app.post('ApituanzhangAdminIndex/getmylisttuan', {
					bid: that.bid,
					cid: cid,
					pagenum: pagenum,
					keyword: keyword
				}, function(res) {
					that.loading = false;
					var data = res.data;
					// 确保数据存在并处理图片字段
					if (data && data.length > 0) {
						data.forEach((item) => {
							// 检查 pic2 字段是否存在，并将其转换为图片数组
							if (item.pic2) {
								item.pics = item.pic2.split(',');
							} else {
								item.pics = []; // 如果 pic2 不存在，则使用空数组
							}
						});
					}
					if (pagenum == 1) {
						that.listtype = res.listtype || 0;
						that.clist = res.clist;
						that.set = res.set;
						if ((res.clist).length > 0) {
							var cnamelist = [];
							var cidlist = [];
							cnamelist.push('全部');
							cidlist.push('0');
							for (var i in that.clist) {
								cnamelist.push(that.clist[i].name);
								cidlist.push(that.clist[i].id);
							}
							that.cnamelist = cnamelist;
							that.cidlist = cidlist;
						}

						uni.setNavigationBarTitle({
							title: res.title
						});
						that.datalist = data;
						if (data.length == 0) {
							that.nodata = true;
						}
						that.loaded();
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}
				});
			},
			searchConfirm: function(e) {
				var that = this;
				var keyword = e.detail.value;
				that.keyword = keyword
				that.getdata();
			},
			changetab: function(cid) {
				this.cid = cid;
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				if (this.listtype == 2) {
					this.$refs.waterfall.refresh();
				}
				this.getdata();
			},
			contactMerchant(phone) {
				if (!phone) {
					return;
				}
				uni.makePhoneCall({
					phoneNumber: phone,
					success: (res) => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.log('拨打电话失败', err);
					}
				});
			},
			// 新增方法 - 分享团
			shareTuan(item) {
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession",
					type: 0,
					title: item.name,
					summary: item.subname || "快来看看这个团吧",
					imageUrl: item.pic,
					href: "/daihuobiji/kuaituan/detail?id=" + item.id,
					success: function (res) {
						console.log("分享成功：" + JSON.stringify(res));
					},
					fail: function (err) {
						console.log("分享失败：" + JSON.stringify(err));
					}
				});
			},
			// 编辑团
			editTuan(id) {
				app.goto('/pagesExa/tuanzhangadmin/tuan/edit?id=' + id);
			},
			// 复制团
			copyTuan(id) {
				var that = this;
				app.confirm('确定要复制该团吗?', function () {
					app.post('ApituanzhangAdminIndex/copytuan', {id: id}, function (res) {
						if (res.status == 1) {
							app.success(res.msg);
							that.getdata();
						} else {
							app.error(res.msg);
						}
					});
				});
			},
			// 设置团状态（上架/下架）
			setStatus(id, status) {
				var that = this;
				var statusText = status == 1 ? '上架' : '下架';
				app.confirm('确定要' + statusText + '该团吗?', function () {
					app.post('ApituanzhangAdminIndex/setstatus', {id: id, status: status}, function (res) {
						if (res.status == 1) {
							app.success(res.msg);
							that.getdata();
						} else {
							app.error(res.msg);
						}
					});
				});
			},
			// 切换隐藏状态
			toggleHidden(id, is_hidden) {
				var that = this;
				var statusText = is_hidden == 1 ? '隐藏' : '显示';
				app.confirm('确定要' + statusText + '该团吗?', function () {
					app.post('ApituanzhangAdminIndex/sethidden', {id: id, is_hidden: is_hidden}, function (res) {
						if (res.status == 1) {
							app.success(res.msg);
							that.getdata();
						} else {
							app.error(res.msg);
						}
					});
				});
			},
			// 删除团
			deleteTuan(id) {
				var that = this;
				app.confirm('确定要删除该团吗?', function () {
					app.post('ApituanzhangAdminIndex/deltuan', {id: id}, function (res) {
						if (res.status == 1) {
							app.success(res.msg);
							that.getdata();
						} else {
							app.error(res.msg);
						}
					});
				});
			}
		}
	};
</script>
<style>
	page {
		background: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	.topsearch {
		width: 100%;
		padding: 20rpx 30rpx;
		background: #fff;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.topsearch .f1 {
		height: 70rpx;
		border-radius: 35rpx;
		border: 0;
		background-color: #f5f5f5;
		flex: 1;
		overflow: hidden;
	}

	.topsearch .f1 image {
		width: 30rpx;
		height: 30rpx;
		margin-left: 20rpx;
	}

	.topsearch .f1 input {
		height: 100%;
		flex: 1;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
		background-color: #f5f5f5;
	}

	.article_list {
		padding: 20rpx 24rpx;
		background: #f8f8f8;
		margin-top: 6rpx;
	}

	.article_list .article-item1 {
		width: 100%;
		display: inline-block;
		position: relative;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	}

	.article_list .article-item1 .article-pic {
		width: 100%;
		height: auto;
		overflow: hidden;
		background: #ffffff;
	}

	.article_list .article-item1 .article-pic .image {
		width: 100%;
		height: auto;
		transition: transform 0.3s ease;
	}

	.article_list .article-item1 .article-pic .image:hover {
		transform: scale(1.05);
	}

	.article_list .article-item1 .article-info {
		padding: 20rpx 30rpx 30rpx 30rpx;
	}

	.article_list .article-item1 .article-info .p1 {
		color: #333333;
		font-weight: bold;
		font-size: 30rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.article_list .article-item1 .article-info .t1 {
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		display: block;
		font-size: 32rpx;
	}

	.article_list .article-item1 .article-info .t2 {
		word-break: break-all;
		text-overflow: ellipsis;
		padding-top: 4rpx;
		overflow: hidden;
	}

	.article_list .article-item1 .article-info .p2 {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		padding: 10rpx 0;
		font-size: 24rpx;
		color: #999;
		overflow: hidden;
	}

	.article_list .article-item2 {
		width: 49%;
		display: inline-block;
		position: relative;
		margin-bottom: 16rpx;
		background: #fff;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	}

	.article_list .article-item2 .article-pic {
		width: 100%;
		height: 0;
		overflow: hidden;
		background: #ffffff;
		padding-bottom: 70%;
		position: relative;
		border-radius: 12rpx 12rpx 0 0;
	}

	.article_list .article-item2 .article-pic .image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: auto;
		transition: transform 0.3s ease;
	}

	.article_list .article-item2 .article-pic .image:hover {
		transform: scale(1.05);
	}

	.article_list .article-item2 .article-info {
		padding: 16rpx 24rpx 24rpx 24rpx;
		display: flex;
		flex-direction: column;
	}

	.article_list .article-item2 .article-info .p1 {
		color: #333333;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.article_list .article-item2 .article-info .p2 {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		padding-top: 10rpx;
		font-size: 24rpx;
		color: #999;
		overflow: hidden;
	}

	.article_list .article-itemlist {
		width: 100%;
		display: inline-block;
		position: relative;
		margin-bottom: 16rpx;
		padding: 16rpx;
		background: #fff;
		display: flex;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	}

	.article_list .article-itemlist .article-pic {
		width: 35%;
		height: 0;
		overflow: hidden;
		background: #ffffff;
		padding-bottom: 25%;
		position: relative;
		border-radius: 8rpx;
	}

	.article_list .article-itemlist .article-pic .image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: auto;
		transition: transform 0.3s ease;
	}

	.article_list .article-itemlist .article-pic .image:hover {
		transform: scale(1.05);
	}

	.article_list .article-itemlist .article-info {
		width: 65%;
		height: auto;
		overflow: hidden;
		padding: 0 24rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.article_list .article-itemlist .article-info .p1 {
		color: #333333;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		height: 92rpx;
	}

	.article_list .article-itemlist .article-info .p2 {
		display: flex;
		flex-grow: 0;
		flex-shrink: 0;
		font-size: 24rpx;
		color: #999;
		overflow: hidden;
		padding-bottom: 6rpx;
	}

	.article_list .article-item3 {
		width: 32%;
		display: inline-block;
		position: relative;
		margin-bottom: 16rpx;
		background: #fff;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	}

	.article_list .article-item3 .article-pic {
		width: 100%;
		height: 0;
		overflow: hidden;
		background: #ffffff;
		padding-bottom: 70%;
		position: relative;
		border-radius: 12rpx 12rpx 0 0;
	}

	.article_list .article-item3 .article-pic .image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: auto;
		transition: transform 0.3s ease;
	}

	.article_list .article-item3 .article-pic .image:hover {
		transform: scale(1.05);
	}

	.article_list .article-item3 .article-info {
		padding: 16rpx 24rpx 24rpx 24rpx;
		display: flex;
		flex-direction: column;
	}

	.article_list .article-item3 .article-info .p1 {
		color: #333333;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.article_list .article-item3 .article-info .p2 {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		padding-top: 10rpx;
		font-size: 24rpx;
		color: #999;
		overflow: hidden;
	}

	.p3 {
		color: #8c8c8c;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.background-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}

	.header-bg {
		background: #58A27E;
		height: 180px;
	}

	.header-gradient {
		background-image: linear-gradient(#58A27E, #f8f8f8);
		height: 120px;
	}

	.content-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}

	.search-bar {
		display: flex;
		align-items: center;
	}

	.back-icon {
		width: 18px;
		height: 18px;
	}

	.search-container {
		width: 100%;
		padding: 20rpx 30rpx;
		background: #5AA37B;
		position: relative;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.search-box {
		display: flex;
		align-items: center;
		height: 70rpx;
		border-radius: 35rpx;
		background-color: rgba(255, 255, 255, 0.95);
		flex: 1;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.search-box .img {
		width: 28rpx;
		height: 28rpx;
		margin-right: 12rpx;
		margin-left: 30rpx;
	}

	.search-box .search-text {
		font-size: 26rpx;
		color: #C2C2C2;
		width: 100%;
	}

	.top-nav {
		margin-top: 30px;
		margin-bottom: 16px;
		display: flex;
		justify-content: space-between;
		padding: 0;
		background-color: #fff;
		align-items: center;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	}

	.nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		flex: 1;
		background: #5AA37B;
		padding: 16rpx 0;
		border-radius: 12rpx;
	}

	.nav-icon {
		width: 64rpx;
		height: 64rpx;
		margin-bottom: 8rpx;
	}

	.nav-label {
		font-size: 24rpx;
		color: #fff;
	}

	.item-list {
		background: #fff;
		margin: 20rpx;
		padding: 24rpx;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		transition: transform 0.2s ease, box-shadow 0.2s ease;
	}

	.item-list:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.item-header {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #f0f0f0;
		padding-bottom: 16rpx;
		margin-bottom: 16rpx;
	}

	.item-image {
		width: 80rpx;
		height: 80rpx;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.item-author {
		margin-left: 16rpx;
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}

	.item-name {
		overflow-wrap: break-word;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
		line-height: 1.4;
	}

	.item-price {
		margin: 16rpx 0;
		color: #ff4d4f;
	}

	.currency-symbol {
		font-size: 24rpx;
		font-weight: bold;
	}

	.price-value {
		font-size: 36rpx;
		font-weight: bold;
	}

	.item-pics {
		display: flex;
		margin: 16rpx 0;
	}

	.item-pic {
		width: 180rpx;
		height: 180rpx;
		border-radius: 12rpx;
		margin-right: 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		object-fit: cover;
	}

	.item-footer {
		display: flex;
		background: #f8f8f8;
		padding: 16rpx;
		border-radius: 12rpx;
		align-items: center;
		justify-content: space-between;
		margin-top: 16rpx;
	}

	.viewers {
		display: flex;
		align-items: center;
	}

	.viewer-icon {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		margin-left: -16rpx;
		border: 2rpx solid #fff;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.viewer-icon:first-child {
		margin-left: 0;
	}

	.view-count {
		font-size: 24rpx;
		margin-left: 16rpx;
		color: #507C85;
	}

	.footer-actions {
		display: flex;
		align-items: center;
	}

	.contact-btn {
		display: flex;
		align-items: center;
		padding: 0 16rpx;
		margin-right: 16rpx;
		border-right: 1px solid #eee;
		height: 40rpx;
	}

	.contact-icon {
		width: 28rpx;
		height: 28rpx;
	}

	.contact-text {
		font-size: 24rpx;
		margin-left: 8rpx;
		color: #507C85;
	}

	.share {
		display: flex;
		align-items: center;
		padding: 0 16rpx;
		height: 40rpx;
	}

	.share-icon {
		width: 28rpx;
		height: 28rpx;
	}

	.share-text {
		font-size: 24rpx;
		margin-left: 8rpx;
		color: #507C85;
	}
	
	.mbtn {
		color: #fff;
		border-radius: 50rpx;
		padding: 8rpx 24rpx;
		font-size: 24rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;
	}
	
	.mbtn:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
	
	/* 新增操作按钮样式 */
	.operation-buttons {
		display: flex;
		flex-wrap: wrap;
		margin-top: 16rpx;
		padding-top: 16rpx;
		border-top: 1px solid #f0f0f0;
		justify-content: flex-end;
	}
	
	.op-btn {
		padding: 8rpx 20rpx;
		margin-right: 16rpx;
		margin-bottom: 10rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;
	}
	
	.op-btn:last-child {
		margin-right: 0;
	}
	
	.op-btn:active {
		transform: scale(0.95);
		box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
	}
	
	.op-btn-text {
		color: #fff;
		font-size: 24rpx;
	}
</style>