<template>
<view class="container">
	<block v-if="isload">
		<view class="header-container">
			<view class="title">排单统计</view>
			<view class="subtitle">我的排单数据概览</view>
		</view>
		
		<view class="stats-grid">
			<view class="stats-card">
				<view class="stats-icon">
					<image src="/static/img/paidan-total.png" class="icon"/>
				</view>
				<view class="stats-info">
					<view class="stats-value" style="color: #FF5722">{{statistics.total_positions || 0}}</view>
					<view class="stats-label">总点位数</view>
				</view>
			</view>
			
			<view class="stats-card">
				<view class="stats-icon">
					<image src="/static/img/paidan-wealth.png" class="icon"/>
				</view>
				<view class="stats-info">
					<view class="stats-value" style="color: #FF5722">{{statistics.wealth_positions || 0}}</view>
					<view class="stats-label">财富点位</view>
				</view>
			</view>
			
			<view class="stats-card">
				<view class="stats-icon">
					<image src="/static/img/paidan-rewarded.png" class="icon"/>
				</view>
				<view class="stats-info">
					<view class="stats-value" style="color: #FF5722">{{statistics.rewarded_positions || 0}}</view>
					<view class="stats-label">已奖励点位</view>
				</view>
			</view>
			
			<view class="stats-card">
				<view class="stats-icon">
					<image src="/static/img/paidan-unrewarded.png" class="icon"/>
				</view>
				<view class="stats-info">
					<view class="stats-value" style="color: #FF5722">{{statistics.unrewarded_positions || 0}}</view>
					<view class="stats-label">待奖励点位</view>
				</view>
			</view>
			
			<view class="stats-card reward-card">
				<view class="stats-icon">
					<image src="/static/img/paidan-reward.png" class="icon"/>
				</view>
				<view class="stats-info">
					<view class="stats-value" style="color: #FF5722">¥{{statistics.total_reward || 0}}</view>
					<view class="stats-label">累计奖励</view>
				</view>
			</view>
		</view>
		
		<view class="config-selector">
			<view class="selector-title">选择活动配置</view>
			<scroll-view class="config-list" scroll-x="true">
				<view class="config-item" 
					  v-for="(item,index) in configList" 
					  :key="item.id" 
					  :class="{active: selectedConfigId == item.id}"
					  @click="selectConfig(item.id)">
					<view class="config-name">{{item.name}}</view>
					<view class="config-info">{{item.copy_mode_text}} · ¥{{item.wealth_reward_amount}}</view>
				</view>
				<view class="config-item" 
					  :class="{active: selectedConfigId == 0}"
					  @click="selectConfig(0)">
					<view class="config-name">全部活动</view>
					<view class="config-info">所有配置统计</view>
				</view>
			</scroll-view>
		</view>
		
		<view class="action-buttons">
			<view class="action-btn" @click="toPositionList" style="background: linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%)">查看点位</view>
			<view class="action-btn" @click="toRewardList" style="background: linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%)">奖励记录</view>
			<view class="action-btn" @click="toPositionTree" style="background: linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%)">排单树图</view>
		</view>
		
		<view class="chart-container" v-if="chartData.length > 0">
			<view class="chart-title">最近7天点位增长</view>
			<view class="chart-content">
				<view class="chart-item" v-for="(item,index) in chartData" :key="index">
					<view class="chart-bar">
						<view class="bar-fill" style="height: item.percentage + '%'; background: #FF5722"></view>
					</view>
					<view class="chart-label">{{item.date}}</view>
					<view class="chart-value">{{item.count}}</view>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			statistics: {},
			configList: [],
			selectedConfigId: 0,
			chartData: []
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	methods: {
		getdata:function(){
			var that = this;
			that.loaded();
			that.getConfigs();
			that.getStatistics();
			that.generateChartData();
		},
		
		getConfigs: function(){
			var that = this;
			app.post('ApiPaidan/getConfigs', {}, function (res) {
				if(res.code == 1){
					that.configList = res.data;
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		getStatistics: function(){
			var that = this;
			that.loading = true;
			
			app.post('ApiPaidan/getStatistics', {
				config_id: that.selectedConfigId
			}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if(res.code == 1){
					that.statistics = res.data;
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		selectConfig: function(configId){
			this.selectedConfigId = configId;
			this.getStatistics();
		},
		
		generateChartData: function(){
			// 模拟最近7天的数据
			var chartData = [];
			var today = new Date();
			var maxCount = 0;
			
			for(var i = 6; i >= 0; i--){
				var date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
				var dateStr = (date.getMonth() + 1) + '/' + date.getDate();
				var count = Math.floor(Math.random() * 10) + 1;
				if(count > maxCount) maxCount = count;
				
				chartData.push({
					date: dateStr,
					count: count,
					percentage: 0
				});
			}
			
			// 计算百分比
			for(var j = 0; j < chartData.length; j++){
				chartData[j].percentage = maxCount > 0 ? (chartData[j].count / maxCount * 100) : 0;
			}
			
			this.chartData = chartData;
		},
		
		toPositionList: function(){
			var url = '/pagesB/paidan/my-position';
			if(this.selectedConfigId > 0){
				url += '?config_id=' + this.selectedConfigId;
			}
			app.goto(url);
		},
		
		toRewardList: function(){
			var url = '/pagesB/paidan/my-position';
			if(this.selectedConfigId > 0){
				url += '?config_id=' + this.selectedConfigId;
			}
			app.goto(url);
		},
		
		toPositionTree: function(){
			var url = '/pagesB/paidan/position-tree';
			if(this.selectedConfigId > 0){
				url += '?config_id=' + this.selectedConfigId;
			}
			app.goto(url);
		},
		
		loaded: function () {
			this.isload = true;
		},
		
		getmenuindex: function (e) {
			this.menuindex = e;
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}
.header-container{width:100%;padding:40rpx 30rpx;background-color:#fff;text-align:center;border-bottom:1px solid #f5f5f5;}
.title{font-size:36rpx;font-weight:bold;color:#333;margin-bottom:12rpx;}
.subtitle{font-size:26rpx;color:#666;}
.stats-grid{width:100%;padding:30rpx;display:flex;flex-wrap:wrap;justify-content:space-between;}
.stats-card{width:48%;background:#fff;padding:30rpx;border-radius:16rpx;margin-bottom:20rpx;display:flex;align-items:center;box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);}
.reward-card{width:100%;}
.stats-icon{width:80rpx;height:80rpx;margin-right:20rpx;}
.icon{width:100%;height:100%;}
.stats-info{flex:1;}
.stats-value{font-size:36rpx;font-weight:bold;margin-bottom:8rpx;}
.stats-label{font-size:24rpx;color:#666;}
.config-selector{width:100%;padding:30rpx;background:#fff;margin-bottom:20rpx;}
.selector-title{font-size:28rpx;font-weight:bold;color:#333;margin-bottom:20rpx;}
.config-list{width:100%;white-space:nowrap;}
.config-item{display:inline-block;padding:20rpx 30rpx;margin-right:20rpx;background:#f8f8f8;border-radius:30rpx;text-align:center;min-width:200rpx;}
.config-item.active{background-color:#007aff;color:#fff;}
.config-item.active .config-name,
.config-item.active .config-info{color:#fff;}
.config-name{font-size:26rpx;font-weight:bold;color:#333;margin-bottom:8rpx;}
.config-info{font-size:22rpx;color:#666;}
.action-buttons{width:100%;padding:0 30rpx;display:flex;justify-content:space-between;margin-bottom:30rpx;}
.action-btn{flex:1;height:80rpx;line-height:80rpx;text-align:center;color:#fff;font-size:26rpx;border-radius:40rpx;margin:0 10rpx;}
.chart-container{width:100%;padding:30rpx;background:#fff;margin-bottom:20rpx;}
.chart-title{font-size:28rpx;font-weight:bold;color:#333;margin-bottom:30rpx;text-align:center;}
.chart-content{display:flex;justify-content:space-between;align-items:end;height:200rpx;}
.chart-item{flex:1;display:flex;flex-direction:column;align-items:center;}
.chart-bar{width:30rpx;height:160rpx;background:#f0f0f0;border-radius:15rpx;position:relative;margin-bottom:10rpx;}
.bar-fill{position:absolute;bottom:0;width:100%;border-radius:15rpx;transition:height 0.3s ease;}
.chart-label{font-size:20rpx;color:#999;margin-bottom:4rpx;}
.chart-value{font-size:22rpx;color:#333;font-weight:bold;}
</style>