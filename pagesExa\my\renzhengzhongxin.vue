<template>
  <view class="container">
    <block v-if="isload">
      <!-- 认证项目列表 -->
      <view v-for="(item, index) in items" :key="index" class="content" @tap="goToAuthPage(item)">
        <view class="f1">
          <image :src="item.icon" class="t3" mode="aspectFit"></image>
          <view class="text-section">
            <text class="t1">{{ item.title }}</text>
            <text class="t2">{{ item.subtitle }}</text>
          </view>
          <text class="flex1"></text>
        </view>
        <!-- 认证状态丝带，标记在右上角 -->
        <view :class="item.status ? 'ribbon-3 certified' : 'ribbon-3 uncertified'">
          <span>{{ item.status ? '已认证' : '未认证' }}</span>
        </view>
      </view>

      <view style="height:140rpx"></view>
      <!-- 如需添加其他按钮，可在此处添加 -->
	  <!-- 返回个人中心的按钮 -->
	  <view class="back-btn" @tap="goto" data-url="/pages/my/usercenter">返回个人中心</view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      items: [], // 认证项目列表，将从服务器获取
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata();

  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  methods: {
	  	  
    // 获取认证项目列表和用户的认证状态
    getdata: function () {
      var that = this;
      that.loading = true;
      app.post('ApiMy/getRenzhengItems', {}, function (data) {
		  
		  console.log(data);
        that.loading = false;
        if (data.status == 1) {
          that.isload = true;
          that.items = data.data.items; // 从服务器获取的认证项目列表
        } else {
          app.error(data.msg);
        }
      });
    },
    // 跳转到对应的认证页面
    goToAuthPage: function (item) {
      if (item.url) {
        wx.navigateTo({
          url: item.url
        });
      } else {
        app.alert('该认证暂未开放');
      }
    },
	
	goToProfile: function () {
	  // 直接跳转到个人中心 tab 页
	  wx.switchTab({
	    url: '/pages/my/usercenter' // 个人中心 tab 页的路径
	  });
	},


    getmenuindex: function (index) {
      this.menuindex = index;
    },
  }
};
</script>

<style>
	.back-btn {
	  width: 90%;
	  margin: 20rpx auto;
	  padding: 20rpx;
	  text-align: center;
	  background-color: #ff4246; /* 按钮颜色 */
	  color: #fff;
	  font-size: 32rpx;
	  border-radius: 8rpx;
	  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1); /* 按钮阴影效果 */
	  cursor: pointer;
	  transition: all 0.3s ease;
	}
	
	.back-btn:hover {
	  background-color: #ff2e2e; /* 悬停时颜色变化 */
	}

.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content { 
  width: 94%; 
  margin: 20rpx 3%; 
  background: #fff; 
  border-radius: 5px; 
  padding: 20rpx 40rpx; 
  position: relative; /* 让认证状态丝带定位在右上角 */
}
.content .f1 { 
  height: 96rpx; 
  line-height: 46rpx; 
  display: flex; 
  align-items: center; 
}
.content .f1 .t1 { 
  color: #2B2B2B; 
  font-weight: bold; 
  font-size: 30rpx; 
}
.content .f1 .t2 { 
  color: #999999; 
  font-size: 28rpx; 
  margin-left: 0rpx; 
}
.content .t3 { 
  width: 68rpx; 
  height: 68rpx; 
  margin-right: 10rpx; 
}
.text-section { 
  display: flex; 
  flex-direction: column; 
  margin-left: 20rpx; 
}
.ribbon-3 {
  position: absolute;
  top: 0;
  right: 0;
  width: 100rpx;
  height: 100rpx;
  overflow: hidden;
}
.ribbon-3 > span {
  position: absolute;
  top: 20%;
  right: -40%;
  z-index: 2;
  width: 150%;
  height: 32rpx;
  overflow: hidden;
  transform: rotate(45deg);
  background: #57DD43;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 24rpx;
}
.certified > span {
  background: #57DD43; /* 绿色背景代表已认证 */
}
.uncertified > span {
  background: #FF5252; /* 红色背景代表未认证 */
}
</style>
