<template>
	<view class="result-container">
		<!-- 顶部结果概览 -->
		<view class="result-header">
			<view class="header-content">
				<view class="result-image-section">
					<view class="image-wrapper">
						<image 
							class="result-image" 
							:src="resultData.image" 
							mode="aspectFit"
							@error="onImageError"
						/>
						<view class="analysis-badge">
							<text class="badge-text">AI分析完成</text>
						</view>
					</view>
				</view>
				
				<view class="result-summary">
					<view class="summary-header">
						<text class="summary-title">舌诊分析报告</text>
						<text class="summary-date">{{ currentDate }}</text>
					</view>
					
					<view class="health-score">
						<view class="score-container">
							<view class="score-circle">
								<text class="score-number">{{ resultData.score }}</text>
								<text class="score-label">分</text>
							</view>
							<view class="score-details">
								<text class="score-desc">综合健康评分</text>
								<view class="score-level" :class="scoreLevelClass">
									<text class="level-text">{{ scoreLevelText }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 主要诊断结果 -->
		<view class="diagnosis-section">
			<view class="section-header">
				<text class="section-title">主要诊断结果</text>
			</view>
			
			<view class="diagnosis-cards">
				<view class="diagnosis-card" v-for="(card, index) in diagnosisCards" :key="index">
					<view class="card-content">
						<view class="card-header">
							<text class="card-icon">{{ card.icon }}</text>
							<text class="card-title">{{ card.title }}</text>
						</view>
						<view class="card-body">
							<text class="card-value">{{ card.value }}</text>
							<text class="card-desc">{{ card.description }}</text>
						</view>
						<view class="card-indicator" :class="card.status"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 详细分析报告 -->
		<view class="analysis-section">
			<view class="section-header">
				<text class="section-title">详细分析报告</text>
			</view>
			
			<view class="analysis-tabs">
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'features' }"
					@click="switchTab('features')"
				>
					<text class="tab-text">舌象特征</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'health' }"
					@click="switchTab('health')"
				>
					<text class="tab-text">健康状况</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'tcm' }"
					@click="switchTab('tcm')"
				>
					<text class="tab-text">中医理论</text>
				</view>
			</view>
			
			<view class="tab-content-wrapper">
				<!-- 舌象特征 -->
				<view v-if="activeTab === 'features'" class="content-panel features-panel">
					<view class="feature-grid">
						<view 
							v-for="(feature, index) in tongueFeatures" 
							:key="index"
							class="feature-card"
						>
							<view class="feature-header">
								<text class="feature-icon">{{ feature.icon }}</text>
								<text class="feature-name">{{ feature.name }}</text>
								<text class="feature-value">{{ feature.value }}</text>
							</view>
							<text class="feature-desc">{{ feature.description }}</text>
							<view class="feature-progress">
								<view class="progress-bar">
									<view 
										class="progress-fill"
										:style="{ width: feature.percentage + '%' }"
									></view>
								</view>
								<text class="progress-text">{{ feature.percentage }}%</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 健康状况 -->
				<view v-if="activeTab === 'health'" class="content-panel health-panel">
					<view class="health-indicators">
						<view 
							v-for="(indicator, index) in healthIndicators" 
							:key="index"
							class="indicator-card"
						>
							<view class="indicator-header">
								<text class="indicator-icon">{{ indicator.icon }}</text>
								<view class="indicator-info">
									<text class="indicator-name">{{ indicator.name }}</text>
									<text class="indicator-level">{{ indicator.levelText }}</text>
								</view>
							</view>
							<view class="indicator-progress">
								<view class="progress-track">
									<view 
										class="progress-fill"
										:style="{ width: indicator.percentage + '%' }"
									></view>
								</view>
								<text class="progress-text">{{ indicator.percentage }}%</text>
							</view>
							<text class="indicator-desc">{{ indicator.description }}</text>
						</view>
					</view>
				</view>
				
				<!-- 中医理论 -->
				<view v-if="activeTab === 'tcm'" class="content-panel tcm-panel">
					<view class="tcm-analysis">
						<view class="tcm-card" v-for="(item, index) in tcmAnalysis" :key="index">
							<view class="tcm-header">
								<text class="tcm-icon">{{ item.icon }}</text>
								<text class="tcm-title">{{ item.title }}</text>
							</view>
							<view class="tcm-content">
								<text class="tcm-text">{{ item.content }}</text>
							</view>
							<view class="tcm-tags">
								<view 
									class="tcm-tag" 
									v-for="(tag, tagIndex) in item.tags" 
									:key="tagIndex"
								>
									<text class="tag-text">{{ tag }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 调理建议 -->
		<view class="suggestions-section">
			<view class="section-header">
				<text class="section-title">个性化调理建议</text>
			</view>
			
			<view class="suggestions-grid">
				<view 
					v-for="(suggestion, index) in suggestionCategories" 
					:key="index"
					class="suggestion-card"
				>
					<view class="suggestion-header">
						<text class="suggestion-icon">{{ suggestion.icon }}</text>
						<text class="suggestion-title">{{ suggestion.title }}</text>
					</view>
					
					<view class="suggestion-content">
						<view 
							v-for="(item, itemIndex) in suggestion.items" 
							:key="itemIndex"
							class="suggestion-item"
						>
							<text class="suggestion-text">• {{ item }}</text>
						</view>
					</view>
					
					<view class="suggestion-footer">
						<text class="suggestion-priority">{{ suggestion.priority }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="action-section">
			<view class="action-container">
				<!-- 主要操作按钮 -->
				<view class="action-buttons">
					<button 
						class="action-btn outline" 
						@click="goBack"
					>
						<text class="btn-text">返回</text>
					</button>
					
					<button 
						class="action-btn outline" 
						@click="retakeTest"
						:class="{ 'loading': isRetaking }"
					>
						<text class="btn-text">{{ isRetaking ? '拍摄中...' : '重新拍照' }}</text>
					</button>
					
					<button 
						class="action-btn primary" 
						@click="shareReport"
						:class="{ 'loading': isSharing }"
					>
						<view class="btn-content">
							<text class="btn-icon">📤</text>
							<text class="btn-text">{{ isSharing ? '分享中...' : '分享' }}</text>
						</view>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ShezhenResult',
	data() {
		return {
			// 分析结果数据
			resultData: {
				image: '',
				tongueQuality: '淡红',
				tongueCoating: '薄白',
				constitution: '气虚质',
				score: 85,
				timestamp: Date.now()
			},
			
			// 当前日期
			currentDate: '',
			
			// 当前激活的标签页
			activeTab: 'features',
			
			// 诊断卡片数据
			diagnosisCards: [
				{
					icon: '👅',
					title: '舌质',
					value: '淡红',
					description: '舌质颜色正常，气血运行良好',
					status: 'normal'
				},
				{
					icon: '🔍',
					title: '舌苔',
					value: '薄白',
					description: '舌苔厚薄适中，脾胃功能正常',
					status: 'normal'
				},
				{
					icon: '⚕️',
					title: '体质类型',
					value: '气虚质',
					description: '需要注意补气养血，增强体质',
					status: 'attention'
				}
			],
			
			// 舌象特征数据
			tongueFeatures: [
				{
					icon: '🎨',
					name: '舌质颜色',
					value: '淡红',
					description: '舌质颜色正常，反映气血运行状况良好',
					percentage: 85
				},
				{
					icon: '📏',
					name: '舌体大小',
					value: '适中',
					description: '舌体大小正常，脾胃功能运化正常',
					percentage: 90
				},
				{
					icon: '🔍',
					name: '舌苔厚薄',
					value: '薄',
					description: '舌苔薄，胃气正常，消化功能良好',
					percentage: 88
				},
				{
					icon: '🌫️',
					name: '舌苔颜色',
					value: '白',
					description: '舌苔白色，体内寒热平衡',
					percentage: 82
				},
				{
					icon: '📐',
					name: '舌体形态',
					value: '正常',
					description: '舌体形态规整，无明显异常',
					percentage: 87
				},
				{
					icon: '🔄',
					name: '舌体运动',
					value: '灵活',
					description: '舌体运动灵活，神经功能正常',
					percentage: 92
				}
			],
			
			// 健康指标数据
			healthIndicators: [
				{
					icon: '❤️',
					name: '气血状况',
					levelText: '良好',
					level: 'good',
					percentage: 85,
					description: '气血运行通畅，心脏功能正常'
				},
				{
					icon: '🫁',
					name: '肺脾功能',
					levelText: '正常',
					level: 'normal',
					percentage: 78,
					description: '肺脾功能基本正常，需适当调理'
				},
				{
					icon: '🔥',
					name: '体内湿热',
					levelText: '轻微',
					level: 'attention',
					percentage: 65,
					description: '体内有轻微湿热，建议清淡饮食'
				},
				{
					icon: '⚡',
					name: '阳气充足',
					levelText: '偏弱',
					level: 'warning',
					percentage: 60,
					description: '阳气略显不足，建议适当温补'
				},
				{
					icon: '🌊',
					name: '津液平衡',
					levelText: '良好',
					level: 'good',
					percentage: 88,
					description: '体内津液平衡，水液代谢正常'
				}
			],
			
			// 中医理论分析
			tcmAnalysis: [
				{
					icon: '🏛️',
					title: '五脏六腑分析',
					content: '根据舌诊理论，您的心、肝、脾、肺、肾五脏功能基本正常，其中脾胃功能略显不足，建议适当调理。',
					tags: ['脾胃调理', '五脏平衡', '整体协调']
				},
				{
					icon: '☯️',
					title: '阴阳平衡状态',
					content: '您的体质偏向阴虚阳弱，建议在日常生活中注意温补阳气，避免过度寒凉食物。',
					tags: ['阴阳调和', '温补阳气', '体质调理']
				},
				{
					icon: '🌿',
					title: '气血津液状况',
					content: '气血运行基本通畅，但气虚现象较为明显，津液代谢正常，建议补气养血。',
					tags: ['补气养血', '津液调节', '气血通畅']
				},
				{
					icon: '🔄',
					title: '经络运行状态',
					content: '经络运行较为通畅，但部分经络可能存在轻微阻滞，建议适当运动和按摩。',
					tags: ['经络疏通', '适量运动', '按摩调理']
				}
			],
			
			// 调理建议分类
			suggestionCategories: [
				{
					icon: '🍽️',
					title: '饮食调理',
					priority: '重要',
					items: [
						'多食用温性食物，如生姜、红枣、桂圆等',
						'避免生冷寒凉食物，减少冰饮摄入',
						'适量食用补气食材，如黄芪、党参、山药',
						'保持饮食规律，三餐定时定量',
						'多喝温开水，促进新陈代谢'
					]
				},
				{
					icon: '🏃‍♂️',
					title: '运动养生',
					priority: '推荐',
					items: [
						'每天进行30分钟有氧运动，如散步、慢跑',
						'练习太极拳或八段锦，调和气血',
						'适当进行力量训练，增强体质',
						'避免过度剧烈运动，以免耗气伤阳',
						'运动后注意保暖，避免受凉'
					]
				},
				{
					icon: '😴',
					title: '作息调理',
					priority: '重要',
					items: [
						'保持规律作息，晚上11点前入睡',
						'保证充足睡眠，每天7-8小时',
						'午休时间不宜过长，20-30分钟为宜',
						'睡前避免剧烈运动和刺激性食物',
						'创造良好睡眠环境，保持室内安静'
					]
				},
				{
					icon: '🧘‍♀️',
					title: '情志调养',
					priority: '建议',
					items: [
						'保持心情愉悦，避免过度焦虑',
						'学会释放压力，适当放松身心',
						'培养兴趣爱好，丰富精神生活',
						'与家人朋友多交流，获得情感支持',
						'遇到困难时保持乐观积极的心态'
					]
				},
				{
					icon: '🌿',
					title: '中药调理',
					priority: '可选',
					items: [
						'可考虑服用四君子汤调理脾胃',
						'适量使用黄芪、党参等补气药材',
						'根据体质选择合适的中成药',
						'建议咨询专业中医师制定个性化方案',
						'注意中药与西药的相互作用'
					]
				},
				{
					icon: '🏠',
					title: '生活环境',
					priority: '建议',
					items: [
						'保持居住环境温暖干燥',
						'注意室内通风，保持空气新鲜',
						'避免长期处于潮湿寒冷环境',
						'适当使用加湿器调节室内湿度',
						'定期清洁居住环境，减少过敏原'
					]
				}
			],
			
			// 按钮状态控制
			isRetaking: false,        // 重新检测按钮加载状态
			isSaving: false,          // 保存报告按钮加载状态  
			isSharing: false,         // 分享报告按钮加载状态
			isExporting: false,       // 导出PDF按钮加载状态
			isReportSaved: false,     // 报告是否已保存
			isFavorited: false,       // 是否已收藏
			shouldConsult: false,      // 是否建议咨询医师（基于分析结果）
			recordId: null            // 记录ID
		}
	},
	computed: {
		/**
		 * 获取评分等级样式类
		 */
		scoreLevelClass() {
			const score = this.resultData.score;
			if (score >= 90) return 'excellent';
			if (score >= 80) return 'good';
			if (score >= 70) return 'normal';
			if (score >= 60) return 'attention';
			return 'warning';
		},
		
		/**
		 * 获取评分等级文本
		 */
		scoreLevelText() {
			const score = this.resultData.score;
			if (score >= 90) return '优秀';
			if (score >= 80) return '良好';
			if (score >= 70) return '正常';
			if (score >= 60) return '注意';
			return '需改善';
		}
	},
	onLoad(options) {
		console.log('2025-01-26 12:00:00,001-INFO-[result][onLoad_001] 舌诊结果页面加载完成');
		
		// 获取传递的记录ID（支持record_id和recordId两种格式）
		if (options.record_id || options.recordId) {
			this.recordId = options.record_id || options.recordId;
			console.log('2025-01-26 12:00:00,002-INFO-[result][onLoad_002] 获取记录ID:', this.recordId);
			// 根据记录ID获取分析结果
			this.getdata();
		} else if (options.image) {
			// 兼容旧版本的图片路径传递
			this.resultData.image = decodeURIComponent(options.image);
			console.log('2025-01-26 12:00:00,003-INFO-[result][onLoad_003] 获取图片路径:', this.resultData.image);
			// 模拟加载分析结果
			this.loadAnalysisResult();
		}
		
		// 设置当前日期
		this.setCurrentDate();
	},
	methods: {
		/**
		 * 设置当前日期
		 */
		setCurrentDate() {
			const now = new Date();
			this.currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
		},

		/**
		 * 获取数据
		 */
		getdata() {
			console.log('[2024-12-28 02:15:00] 开始获取分析记录，记录ID：', this.recordId);
			
			uni.showLoading({
				title: '加载中...'
			});
			
			this.getAnalysisRecord();
		},

		/**
		 * 获取分析记录
		 */
		getAnalysisRecord() {
			console.log('2025-01-26 12:00:00,010-INFO-[result][getAnalysisRecord_001] 开始获取分析记录');
			
			const app = getApp();
			
			app.post('ApiSheZhen/getRecord', {
				id: this.recordId
			}, (response) => {
				uni.hideLoading();
				console.log('2025-01-26 12:00:00,011-INFO-[result][getAnalysisRecord_002] 获取记录结果:', response);
				
				// 修复：后端接口返回 code 字段，统一检查 code 字段
				if (response && response.code === 1) {
					const data = response.data;
					
					// 更新结果数据
					this.resultData = {
						image: data.tongue_image || this.resultData.image,
						tongueQuality: this.parseAnalysisData(data, 'tongue_quality') || '淡红',
						tongueCoating: this.parseAnalysisData(data, 'tongue_coating') || '薄白',
						constitution: data.constitution_type || '平和质',
						score: data.constitution_score || 85,
						timestamp: data.createtime_format || this.currentDate
					};
					
					console.log('2025-01-26 12:00:00,012-INFO-[result][getAnalysisRecord_003] 处理后的结果数据:', this.resultData);
					
					// 解析详细分析结果
					if (data.analysis_result) {
						this.parseDetailedAnalysis(data.analysis_result);
					}
					
					// 更新诊断卡片数据
					this.updateDiagnosisCards();
					
					// 检查是否需要建议咨询医师
					this.checkConsultRecommendation();
					
					console.log('2025-01-26 12:00:00,013-INFO-[result][getAnalysisRecord_004] 分析记录加载成功');
				} else {
					console.error('2025-01-26 12:00:00,014-ERROR-[result][getAnalysisRecord_005] 获取记录失败:', response?.msg);
					uni.showToast({
						title: response?.msg || '获取记录失败',
						icon: 'none'
					});
					
					// 降级到模拟数据
					this.loadAnalysisResult();
				}
			}, (error) => {
				uni.hideLoading();
				console.error('2025-01-26 12:00:00,015-ERROR-[result][getAnalysisRecord_006] 获取记录接口调用失败:', error);
				
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
				
				// 降级到模拟数据
				this.loadAnalysisResult();
			});
		},

		/**
		 * 加载分析结果
		 */
		loadAnalysisResult() {
			console.log('[2024-12-28 02:15:05] 加载分析结果');
			
			// 模拟从服务器获取分析结果
			// 这里可以根据实际情况调用API获取真实的分析结果
			
			// 更新诊断卡片数据
			this.updateDiagnosisCards();
			
			// 检查是否需要建议咨询医师
			this.checkConsultRecommendation();
		},

		/**
		 * 检查是否需要建议咨询医师
		 */
		checkConsultRecommendation() {
			console.log('2025-01-26 12:00:00,027-INFO-[result][checkConsultRecommendation_001] 检查咨询建议');
			
			// 根据健康评分和异常指标判断是否需要咨询医师
			const lowScore = this.resultData.score < 70;
			const hasWarningIndicators = this.healthIndicators.some(indicator => indicator.level === 'warning');
			const hasAttentionIndicators = this.healthIndicators.filter(indicator => indicator.level === 'attention').length >= 2;
			
			this.shouldConsult = lowScore || hasWarningIndicators || hasAttentionIndicators;
			
			if (this.shouldConsult) {
				console.log('2025-01-26 12:00:00,028-INFO-[result][checkConsultRecommendation_002] 建议咨询医师');
			}
		},

		/**
		 * 更新诊断卡片数据
		 */
		updateDiagnosisCards() {
			this.diagnosisCards[0].value = this.resultData.tongueQuality;
			this.diagnosisCards[0].description = this.getTongueQualityDesc(this.resultData.tongueQuality);
			
			this.diagnosisCards[1].value = this.resultData.tongueCoating;
			this.diagnosisCards[1].description = this.getTongueCoatingDesc(this.resultData.tongueCoating);
			
			this.diagnosisCards[2].value = this.resultData.constitution;
			this.diagnosisCards[2].description = this.getConstitutionDesc(this.resultData.constitution);
		},

		/**
		 * 切换标签页
		 */
		switchTab(tab) {
			console.log('2025-01-26 12:00:00,004-INFO-[result][switchTab_001] 切换标签页:', tab);
			this.activeTab = tab;
		},

		/**
		 * 获取舌质描述
		 */
		getTongueQualityDesc(quality) {
			const descriptions = {
				'淡红': '舌质颜色正常，气血运行良好',
				'红': '体内有热，需要清热降火',
				'淡白': '气血不足，需要补气养血',
				'紫暗': '血瘀较重，需要活血化瘀'
			};
			return descriptions[quality] || '需要进一步观察';
		},

		/**
		 * 获取舌苔描述
		 */
		getTongueCoatingDesc(coating) {
			const descriptions = {
				'薄白': '舌苔正常，脾胃功能良好',
				'厚白': '脾胃湿盛，需要健脾化湿',
				'薄黄': '体内有轻微热象',
				'厚黄': '湿热较重，需要清热化湿',
				'无苔': '胃阴不足，需要养阴生津'
			};
			return descriptions[coating] || '需要进一步观察';
		},

		/**
		 * 获取体质描述
		 */
		getConstitutionDesc(constitution) {
			const descriptions = {
				'气虚质': '需要补气养血，增强体质',
				'阳虚质': '需要温补阳气，避免寒凉',
				'阴虚质': '需要滋阴润燥，清热降火',
				'痰湿质': '需要健脾化湿，清淡饮食',
				'湿热质': '需要清热化湿，避免辛辣',
				'血瘀质': '需要活血化瘀，适当运动',
				'气郁质': '需要疏肝理气，调节情志',
				'特禀质': '需要避免过敏原，增强免疫',
				'平和质': '体质平和，继续保持良好习惯'
			};
			return descriptions[constitution] || '需要进一步分析';
		},

		/**
		 * 重新检测
		 */
		retakeTest() {
			console.log('2025-01-26 12:00:00,008-INFO-[result][retakeTest_001] 重新检测');
			
			// 设置加载状态
			this.isRetaking = true;
			
			uni.showModal({
				title: '重新检测',
				content: '确定要重新进行舌诊检测吗？',
				success: (res) => {
					this.isRetaking = false;
					if (res.confirm) {
						uni.navigateTo({
							url: '/pagesB/shezhen/camera',
							success: () => {
								console.log('2025-01-26 12:00:00,009-INFO-[result][retakeTest_002] 跳转到拍摄页面');
							}
						});
					}
				},
				fail: () => {
					this.isRetaking = false;
				}
			});
		},

		/**
		 * 保存报告 - 更新版本
		 */
		saveReport() {
			console.log('2025-01-26 12:00:00,011-INFO-[result][saveReport_001] 保存报告');
			
			// 设置加载状态
			this.isSaving = true;
			
			uni.showLoading({
				title: '保存中...'
			});
			
			// 模拟保存过程
			setTimeout(() => {
				this.isSaving = false;
				this.isReportSaved = true;
				uni.hideLoading();
				uni.showToast({
					title: '报告已保存',
					icon: 'success'
				});
				console.log('2025-01-26 12:00:00,012-INFO-[result][saveReport_002] 报告保存成功');
			}, 1500);
		},

		/**
		 * 分享报告
		 */
		async shareReport() {
			console.log('2025-01-04 10:30:01,004-INFO-[result][shareReport_001] 开始分享报告');
			
			this.isSharing = true;
			
			try {
				// 模拟分享操作
				await this.simulateDelay(1000);
				
				// 调用系统分享
				await uni.share({
					provider: 'weixin',
					type: 0,
					title: '我的舌诊健康报告',
					summary: `健康评分：${this.resultData.score}分 - 快来查看你的舌诊分析结果！`,
					imageUrl: this.resultData.image || '',
					success: (res) => {
						console.log('2025-01-04 10:30:01,005-INFO-[result][shareReport_002] 分享成功', res);
						this.showToast('分享成功', 'success');
					},
					fail: (err) => {
						console.log('2025-01-04 10:30:01,006-ERROR-[result][shareReport_003] 分享失败', err);
						this.showToast('分享失败，请重试', 'error');
					}
				});
				
			} catch (error) {
				console.log('2025-01-04 10:30:01,007-ERROR-[result][shareReport_004] 分享过程出错', error);
				this.showToast('分享失败，请重试', 'error');
			} finally {
				this.isSharing = false;
				console.log('2025-01-04 10:30:01,008-INFO-[result][shareReport_005] 分享操作结束');
			}
		},

		/**
		 * 导出PDF报告
		 */
		exportPDF() {
			console.log('2025-01-26 12:00:00,015-INFO-[result][exportPDF_001] 导出PDF报告');
			
			// 设置加载状态
			this.isExporting = true;
			
			uni.showLoading({
				title: '生成PDF中...'
			});
			
			// 模拟PDF生成过程
			setTimeout(() => {
				this.isExporting = false;
				uni.hideLoading();
				
				uni.showModal({
					title: 'PDF生成完成',
					content: '报告已生成PDF格式，是否保存到相册？',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: 'PDF已保存到相册',
								icon: 'success'
							});
							console.log('2025-01-26 12:00:00,016-INFO-[result][exportPDF_002] PDF保存成功');
						}
					}
				});
			}, 2000);
		},

		/**
		 * 咨询医师
		 */
		consultDoctor() {
			console.log('2025-01-26 12:00:00,017-INFO-[result][consultDoctor_001] 咨询医师');
			
			uni.showActionSheet({
				itemList: ['在线咨询专家', '预约医师面诊', '查看附近医院', '健康热线咨询'],
				success: (res) => {
					console.log('2025-01-26 12:00:00,018-INFO-[result][consultDoctor_002] 选择咨询方式:', res.tapIndex);
					
					switch (res.tapIndex) {
						case 0:
							this.onlineConsult();
							break;
						case 1:
							this.appointmentConsult();
							break;
						case 2:
							this.findNearbyHospitals();
							break;
						case 3:
							this.healthHotline();
							break;
					}
				}
			});
		},

		/**
		 * 添加到收藏
		 */
		addToFavorites() {
			console.log('2025-01-26 12:00:00,019-INFO-[result][addToFavorites_001] 添加到收藏');
			
			this.isFavorited = !this.isFavorited;
			
			uni.showToast({
				title: this.isFavorited ? '已添加到收藏' : '已取消收藏',
				icon: 'success'
			});
			
			console.log('2025-01-26 12:00:00,020-INFO-[result][addToFavorites_002] 收藏状态:', this.isFavorited);
		},

		/**
		 * 设置提醒
		 */
		setReminder() {
			console.log('2025-01-26 12:00:00,021-INFO-[result][setReminder_001] 设置提醒');
			
			uni.showActionSheet({
				itemList: ['明天提醒复查', '一周后提醒', '一个月后提醒', '自定义提醒时间'],
				success: (res) => {
					console.log('2025-01-26 12:00:00,022-INFO-[result][setReminder_002] 选择提醒时间:', res.tapIndex);
					
					const reminderTexts = ['明天', '一周后', '一个月后', '自定义时间'];
					uni.showToast({
						title: `已设置${reminderTexts[res.tapIndex]}提醒`,
						icon: 'success'
					});
				}
			});
		},

		/**
		 * 查看历史记录
		 */
		viewHistory() {
			console.log('2025-01-26 12:00:00,023-INFO-[result][viewHistory_001] 查看历史记录');
			
			uni.navigateTo({
				url: '/pagesB/shezhen/history',
				success: () => {
					console.log('2025-01-26 12:00:00,024-INFO-[result][viewHistory_002] 跳转到历史记录页面');
				},
				fail: () => {
					uni.showToast({
						title: '历史记录功能开发中',
						icon: 'none'
					});
				}
			});
		},

		/**
		 * 获取更多建议
		 */
		getMoreTips() {
			console.log('2025-01-26 12:00:00,025-INFO-[result][getMoreTips_001] 获取更多建议');
			
			uni.navigateTo({
				url: '/pagesB/health/tips',
				success: () => {
					console.log('2025-01-26 12:00:00,026-INFO-[result][getMoreTips_002] 跳转到健康建议页面');
				},
				fail: () => {
					uni.showToast({
						title: '健康建议功能开发中',
						icon: 'none'
					});
				}
			});
		},

		/**
		 * 在线咨询专家
		 */
		onlineConsult() {
			uni.showToast({
				title: '在线咨询功能开发中',
				icon: 'none'
			});
		},

		/**
		 * 预约医师面诊
		 */
		appointmentConsult() {
			uni.showToast({
				title: '预约面诊功能开发中',
				icon: 'none'
			});
		},

		/**
		 * 查找附近医院
		 */
		findNearbyHospitals() {
			uni.showToast({
				title: '查找医院功能开发中',
				icon: 'none'
			});
		},

		/**
		 * 健康热线咨询
		 */
		healthHotline() {
			uni.showToast({
				title: '健康热线：************',
				icon: 'none',
				duration: 3000
			});
		},

		/**
		 * 复制分享链接
		 */
		copyShareLink() {
			const shareLink = `https://example.com/report/${this.resultData.timestamp}`;
			uni.setClipboardData({
				data: shareLink,
				success: () => {
					uni.showToast({
						title: '分享链接已复制',
						icon: 'success'
					});
				}
			});
		},

		/**
		 * 生成分享图片
		 */
		generateShareImage() {
			uni.showLoading({
				title: '生成分享图片中...'
			});
			
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '分享图片已生成',
					icon: 'success'
				});
			}, 1500);
		},

		/**
		 * 图片加载错误处理
		 */
		onImageError(error) {
			console.error('2025-01-26 12:00:00,010-ERROR-[result][onImageError_001] 图片加载错误:', error);
			
			uni.showToast({
				title: '图片加载失败',
				icon: 'none'
			});
		},

		/**
		 * 返回上一页
		 */
		goBack() {
			console.log('2025-01-04 10:30:01,001-INFO-[result][goBack_001] 用户点击返回按钮');
			uni.navigateBack({
				delta: 1,
				success: (res) => {
					console.log('2025-01-04 10:30:01,002-INFO-[result][goBack_002] 返回成功', res);
				},
				fail: (err) => {
					console.log('2025-01-04 10:30:01,003-ERROR-[result][goBack_003] 返回失败', err);
					// 如果无法返回，则跳转到首页
					uni.reLaunch({
						url: '/pages/index/index'
					});
				}
			});
		},

		/**
		 * 模拟延迟
		 */
		simulateDelay(ms) {
			return new Promise(resolve => setTimeout(resolve, ms));
		},

		/**
		 * 显示Toast
		 */
		showToast(title, icon) {
			uni.showToast({
				title: title,
				icon: icon
			});
		},

		/**
		 * 解析分析数据
		 */
		parseAnalysisData(data, field) {
			console.log('2025-01-26 12:00:00,020-INFO-[result][parseAnalysisData_001] 解析分析数据字段:', field);
			
			try {
				if (data.analysis_result && typeof data.analysis_result === 'string') {
					const analysisResult = JSON.parse(data.analysis_result);
					return analysisResult[field];
				} else if (data.analysis_result && typeof data.analysis_result === 'object') {
					return data.analysis_result[field];
				}
			} catch (e) {
				console.error('2025-01-26 12:00:00,021-ERROR-[result][parseAnalysisData_002] 解析分析数据失败:', e);
			}
			
			return null;
		},

		/**
		 * 解析详细分析结果
		 */
		parseDetailedAnalysis(analysisResult) {
			console.log('2025-01-26 12:00:00,022-INFO-[result][parseDetailedAnalysis_001] 解析详细分析结果');
			
			try {
				let analysisData = analysisResult;
				if (typeof analysisResult === 'string') {
					analysisData = JSON.parse(analysisResult);
				}
				
				console.log('2025-01-26 12:00:00,023-INFO-[result][parseDetailedAnalysis_002] 解析后的数据:', analysisData);
				
				// 更新舌象特征
				if (analysisData.tongue_features) {
					this.updateTongueFeatures(analysisData.tongue_features);
				}
				
				// 更新健康指标
				if (analysisData.health_indicators) {
					this.updateHealthIndicators(analysisData.health_indicators);
				}
				
				// 更新中医分析
				if (analysisData.tcm_analysis) {
					this.updateTcmAnalysis(analysisData.tcm_analysis);
				}
				
			} catch (e) {
				console.error('2025-01-26 12:00:00,024-ERROR-[result][parseDetailedAnalysis_003] 解析详细分析结果失败:', e);
			}
		},

		/**
		 * 更新舌象特征
		 */
		updateTongueFeatures(features) {
			console.log('2025-01-26 12:00:00,025-INFO-[result][updateTongueFeatures_001] 更新舌象特征');
			
			if (Array.isArray(features) && features.length > 0) {
				this.tongueFeatures = features.map(feature => ({
					icon: this.getFeatureIcon(feature.name),
					name: feature.name || '未知特征',
					value: feature.value || '正常',
					description: feature.description || '特征描述',
					percentage: feature.percentage || 80
				}));
			}
		},

		/**
		 * 更新健康指标
		 */
		updateHealthIndicators(indicators) {
			console.log('2025-01-26 12:00:00,026-INFO-[result][updateHealthIndicators_001] 更新健康指标');
			
			if (Array.isArray(indicators) && indicators.length > 0) {
				this.healthIndicators = indicators.map(indicator => ({
					icon: this.getIndicatorIcon(indicator.name),
					name: indicator.name || '健康指标',
					level: indicator.level || 'normal',
					levelText: this.getLevelText(indicator.level),
					percentage: indicator.percentage || 75,
					description: indicator.description || '指标描述'
				}));
			}
		},

		/**
		 * 更新中医分析
		 */
		updateTcmAnalysis(tcmData) {
			console.log('2025-01-26 12:00:00,027-INFO-[result][updateTcmAnalysis_001] 更新中医分析');
			
			if (Array.isArray(tcmData) && tcmData.length > 0) {
				this.tcmAnalysis = tcmData.map(item => ({
					icon: this.getTcmIcon(item.category),
					title: item.title || '中医分析',
					content: item.content || '分析内容',
					tags: item.tags || []
				}));
			}
		},

		/**
		 * 获取特征图标
		 */
		getFeatureIcon(featureName) {
			const iconMap = {
				'舌质颜色': '🎨',
				'舌苔厚度': '📏',
				'舌体形状': '👅',
				'舌边齿痕': '🦷',
				'舌下络脉': '🩸'
			};
			return iconMap[featureName] || '📊';
		},

		/**
		 * 获取指标图标
		 */
		getIndicatorIcon(indicatorName) {
			const iconMap = {
				'气血状况': '💓',
				'脾胃功能': '🥘',
				'心肾功能': '💖',
				'肝胆功能': '🫘',
				'情志状态': '😊'
			};
			return iconMap[indicatorName] || '📈';
		},

		/**
		 * 获取中医图标
		 */
		getTcmIcon(category) {
			const iconMap = {
				'体质分析': '🧬',
				'脏腑功能': '🫁',
				'气血津液': '🌿',
				'经络运行': '🔄'
			};
			return iconMap[category] || '🏥';
		},

		/**
		 * 获取等级文本
		 */
		getLevelText(level) {
			const levelMap = {
				'excellent': '优秀',
				'good': '良好',
				'normal': '正常',
				'attention': '注意',
				'warning': '警告'
			};
			return levelMap[level] || '正常';
		}
	}
}
</script>

<style scoped>
/* 全局样式 - 简洁医学专业风格 */
.result-container {
	background: #f5f7fa;
	min-height: 100vh;
	overflow: hidden;
}

/* 顶部结果概览 */
.result-header {
	position: relative;
	z-index: 1;
	padding: 40rpx 30rpx 30rpx;
}

.header-content {
	background: white;
	border-radius: 15rpx;
	padding: 30rpx;
	border: 1rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	display: flex;
	gap: 30rpx;
}

.result-image-section {
	flex-shrink: 0;
}

.image-wrapper {
	position: relative;
	width: 180rpx;
	height: 180rpx;
	border-radius: 15rpx;
	overflow: hidden;
	border: 2rpx solid #e8e8e8;
}

.result-image {
	width: 100%;
	height: 100%;
}

.analysis-badge {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	background: rgba(46, 125, 138, 0.9);
	padding: 8rpx 15rpx;
	text-align: center;
}

.badge-text {
	font-size: 22rpx;
	color: white;
	font-weight: 500;
}

.result-summary {
	flex: 1;
}

.summary-header {
	margin-bottom: 25rpx;
}

.summary-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #1A4B52;
	display: block;
	margin-bottom: 8rpx;
}

.summary-date {
	font-size: 24rpx;
	color: #666;
}

.health-score {
	display: flex;
	align-items: center;
}

.score-container {
	display: flex;
	align-items: center;
	gap: 25rpx;
}

.score-circle {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: #f8f9fa;
	border: 4rpx solid #2E7D8A;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.score-number {
	font-size: 32rpx;
	font-weight: bold;
	color: #2E7D8A;
}

.score-label {
	font-size: 20rpx;
	color: #2E7D8A;
	margin-top: -5rpx;
}

.score-details {
	flex: 1;
}

.score-desc {
	font-size: 26rpx;
	color: #1A4B52;
	display: block;
	margin-bottom: 10rpx;
}

.score-level {
	padding: 8rpx 15rpx;
	border-radius: 10rpx;
	font-size: 22rpx;
	font-weight: bold;
	background: rgba(46, 125, 138, 0.1);
	color: #2E7D8A;
	border: 1rpx solid rgba(46, 125, 138, 0.2);
	display: inline-block;
}

.level-text {
	font-size: 22rpx;
	font-weight: bold;
}

/* 主要诊断结果 */
.diagnosis-section,
.analysis-section,
.suggestions-section {
	position: relative;
	z-index: 1;
	padding: 0 30rpx 30rpx;
}

.section-header {
	margin-bottom: 25rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #1A4B52;
}

.diagnosis-cards {
	display: grid;
	gap: 20rpx;
}

.diagnosis-card {
	background: white;
	border-radius: 15rpx;
	padding: 25rpx;
	border: 1rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-content {
	position: relative;
	z-index: 1;
}

.card-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.card-icon {
	font-size: 24rpx;
	color: #2E7D8A;
}

.card-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #1A4B52;
}

.card-body {
	margin-bottom: 15rpx;
}

.card-value {
	font-size: 24rpx;
	font-weight: bold;
	color: #2E7D8A;
	display: block;
	margin-bottom: 8rpx;
}

.card-desc {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

/* 详细分析报告 */
.analysis-tabs {
	display: flex;
	background: #2E7D8A;
	border-radius: 15rpx;
	margin-bottom: 25rpx;
	overflow: hidden;
}

.tab-item {
	flex: 1;
	position: relative;
	background: transparent;
	border: none;
	padding: 20rpx 15rpx;
	text-align: center;
	cursor: pointer;
}

.tab-item.active {
	background: rgba(255, 255, 255, 0.2);
}

.tab-text {
	font-size: 26rpx;
	font-weight: 500;
	color: rgba(255, 255, 255, 0.7);
}

.tab-item.active .tab-text {
	color: white;
}

.tab-content-wrapper {
	background: white;
	border-radius: 15rpx;
	padding: 30rpx;
	border: 1rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 舌象特征面板 */
.feature-grid {
	display: grid;
	gap: 20rpx;
}

.feature-card {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 20rpx;
	border: 1rpx solid #e8e8e8;
}

.feature-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.feature-icon {
	font-size: 24rpx;
	color: #2E7D8A;
}

.feature-name {
	font-size: 26rpx;
	font-weight: bold;
	color: #1A4B52;
	flex: 1;
}

.feature-value {
	font-size: 20rpx;
	font-weight: bold;
	color: #2E7D8A;
}

.feature-desc {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 15rpx;
}

.feature-progress {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.progress-bar {
	flex: 1;
	height: 8rpx;
	background: #e8e8e8;
	border-radius: 4rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: #2E7D8A;
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.progress-text {
	font-size: 20rpx;
	color: #2E7D8A;
	font-weight: bold;
	min-width: 50rpx;
}

/* 健康状况面板 */
.health-indicators {
	display: grid;
	gap: 20rpx;
}

.indicator-card {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 20rpx;
	border: 1rpx solid #e8e8e8;
}

.indicator-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.indicator-icon {
	font-size: 24rpx;
	margin-right: 15rpx;
	color: #2E7D8A;
}

.indicator-info {
	flex: 1;
}

.indicator-name {
	font-size: 26rpx;
	font-weight: bold;
	color: #1A4B52;
	display: block;
	margin-bottom: 5rpx;
}

.indicator-level {
	font-size: 20rpx;
	color: #2E7D8A;
	font-weight: 500;
}

.indicator-progress {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.progress-track {
	flex: 1;
	height: 8rpx;
	background: #e8e8e8;
	border-radius: 4rpx;
	overflow: hidden;
	margin-right: 15rpx;
}

.progress-track .progress-fill {
	height: 100%;
	background: #2E7D8A;
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.indicator-desc {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

/* 中医理论面板 */
.tcm-analysis {
	display: grid;
	gap: 20rpx;
}

.tcm-card {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 20rpx;
	border: 1rpx solid #e8e8e8;
}

.tcm-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.tcm-icon {
	font-size: 24rpx;
	margin-right: 15rpx;
	color: #2E7D8A;
}

.tcm-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #1A4B52;
}

.tcm-content {
	margin-bottom: 15rpx;
}

.tcm-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
}

.tcm-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.tcm-tag {
	background: rgba(46, 125, 138, 0.1);
	border: 1rpx solid rgba(46, 125, 138, 0.2);
	border-radius: 10rpx;
	padding: 6rpx 12rpx;
}

.tag-text {
	font-size: 20rpx;
	color: #2E7D8A;
	font-weight: 500;
}

/* 调理建议 */
.suggestions-grid {
	display: grid;
	gap: 20rpx;
}

.suggestion-card {
	background: white;
	border-radius: 15rpx;
	padding: 25rpx;
	border: 1rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.suggestion-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.suggestion-icon {
	font-size: 24rpx;
	color: #2E7D8A;
	margin-right: 15rpx;
}

.suggestion-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #1A4B52;
}

.suggestion-content {
	margin-bottom: 15rpx;
}

.suggestion-item {
	margin-bottom: 10rpx;
}

.suggestion-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
}

.suggestion-footer {
	text-align: right;
}

.suggestion-priority {
	font-size: 20rpx;
	color: #2E7D8A;
	background: rgba(46, 125, 138, 0.1);
	padding: 6rpx 12rpx;
	border-radius: 10rpx;
	border: 1rpx solid rgba(46, 125, 138, 0.2);
	font-weight: 500;
}

/* 底部操作区域 */
.action-section {
	background: #ffffff;
	padding: 40rpx 30rpx;
	padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
	box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.05);
}

.action-container {
	max-width: 750rpx;
	margin: 0 auto;
}

/* 按钮布局 */
.action-buttons {
	display: flex;
	gap: 20rpx;
	justify-content: space-between;
	align-items: center;
}

/* 基础按钮样式 */
.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

/* 线框按钮样式 */
.action-btn.outline {
	background: #ffffff;
	color: #4A90E2;
	border: 2rpx solid #4A90E2;
}

.action-btn.outline:active {
	transform: scale(0.98);
	background: rgba(74, 144, 226, 0.05);
}

/* 主要按钮样式 */
.action-btn.primary {
	background: #4A90E2;
	color: #ffffff;
}

.action-btn.primary:active {
	transform: scale(0.98);
	background: #3B7BC9;
}

/* 按钮文字 */
.btn-text {
	font-size: 32rpx;
	font-weight: 500;
	line-height: 1;
}

/* 按钮内容布局 */
.btn-content {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.btn-icon {
	font-size: 28rpx;
	line-height: 1;
}

/* 加载状态 */
.action-btn.loading {
	opacity: 0.7;
	pointer-events: none;
}

.action-btn.loading::after {
	content: '';
	position: absolute;
	width: 30rpx;
	height: 30rpx;
	border: 4rpx solid transparent;
	border-top: 4rpx solid currentColor;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media screen and (max-width: 400px) {
	.action-buttons {
		gap: 15rpx;
	}
	
	.action-btn {
		height: 80rpx;
		font-size: 28rpx;
	}
	
	.btn-text {
		font-size: 28rpx;
	}
}

/* 加载动画 */
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style> 