# 售卖模块使用文档

## 1. 模块概述

售卖模块是一个允许用户将自己的资产（佣金、积分、余额等）以折扣方式出售给其他用户的功能。该模块支持用户之间的点对点交易，买卖双方可直接通过平台进行沟通和交易确认。

## 2. 主要功能与页面

### 2.1 前台页面说明

#### 钱包设置页 (walletsite.vue)
- **路径**: `/pagesExa/shoumai/walletsite`
- **功能**: 设置用户的收款信息，包括支付通道、钱包地址和收款码
- **主要字段**:
  - 支付通道: 选择收款方式
  - 钱包地址: 用户的USDT收款地址
  - 收款二维码: 上传支付收款码图片
  - 交易密码: 设置交易密码用于安全确认

#### 资产售卖页 (sale.vue)
- **路径**: `/pagesExa/shoumai/sale`
- **功能**: 用户发布资产售卖信息
- **主要字段**:
  - 资产类型: 支持多种资产（余额、积分、佣金等）
  - 售卖金额: 用户输入想要售卖的金额
  - 折扣率: 用户可在系统设定的范围内调整
  - 预计收款: 根据折扣率计算实际收款金额

#### 交易列表页 (index.vue)
- **路径**: `/pagesExa/shoumai/index`
- **功能**: 显示交易列表，包括售卖中、交易中、已完成三个状态
- **主要功能**:
  - 筛选: 支持切换查看全部/我的交易
  - 买入: 购买他人发布的资产
  - 取消: 取消自己发布的售卖信息
  - 上传: 上传支付凭证
  - 审核: 卖家审核买家上传的支付凭证

#### 支付凭证页 (voucher.vue)
- **路径**: `/pagesExa/shoumai/voucher`
- **功能**: 买家上传支付凭证的页面
- **主要字段**:
  - 卖家信息: 显示卖家的钱包地址和收款码
  - 交易信息: 显示交易金额和应付金额
  - 支付凭证上传: 上传支付后的截图作为凭证

## 3. 交易流程

### 3.1 售卖流程
1. 用户首先需要设置钱包信息（walletsite.vue）
2. 进入售卖页面（sale.vue），选择资产类型
3. 输入要售卖的金额，调整折扣率
4. 系统自动计算折扣后的实际收款金额
5. 确认发布后，资产进入"售卖中"状态
6. 其他用户可在交易列表中看到并购买

### 3.2 购买流程
1. 买家在交易列表（index.vue）中选择要购买的资产
2. 点击"买入"按钮，确认购买金额
3. 系统跳转到支付凭证页（voucher.vue）
4. 买家通过卖家的收款方式支付相应金额
5. 上传支付凭证后，等待卖家审核

### 3.3 审核流程
1. 卖家在"交易中"标签下查看收到的支付
2. 核对支付金额和凭证，确认是否收到款项
3. 选择"通过"或"不通过"
4. 交易完成后，资产转移给买家，状态变为"已完成"

## 4. 模块接口说明

### 4.1 售卖设置接口
- **接口URL**: `/ApiShoumai/getSaleSettings`
- **请求方式**: GET
- **功能**: 获取用户可售卖的资产类型和系统设置
- **返回参数**: 包含系统设置（settings）和可售卖资产列表（assets）

### 4.2 发布售卖接口
- **接口URL**: `/ApiShoumai/postSale`
- **请求方式**: POST
- **请求参数**:
  - amount: 售卖数量
  - discount: 折扣率（0-1之间）
  - asset_type: 资产类型
- **功能**: 提交售卖信息

### 4.3 钱包设置接口
- **接口URL**: `/ApiShoumai/setWalletInfo`
- **请求方式**: GET/POST
- **功能**: 获取/设置用户钱包信息

### 4.4 交易列表接口
- **接口URL**: `/ApiShoumai/getList`
- **请求方式**: GET
- **请求参数**:
  - st: 状态（0=售卖中，1=交易中，2=已完成）
  - pagenum: 页码
  - my: 是否只显示我的（0=否，1=是）
- **功能**: 获取交易列表数据

### 4.5 购买接口
- **接口URL**: `/ApiShoumai/postBuy`
- **请求方式**: POST
- **请求参数**:
  - id: 订单ID
  - money: 购买金额
- **功能**: 提交购买请求

### 4.6 凭证信息接口
- **接口URL**: `/ApiShoumai/postVoucher`
- **请求方式**: GET/POST
- **功能**: 获取/提交支付凭证

### 4.7 审核接口
- **接口URL**: `/ApiShoumai/postAudit`
- **请求方式**: POST
- **请求参数**:
  - id: 订单ID
  - st: 状态（1=通过，2=驳回）
- **功能**: 审核支付凭证

### 4.8 取消订单接口
- **接口URL**: `/ApiShoumai/cancelOrder`
- **请求方式**: POST
- **请求参数**:
  - id: 订单ID
- **功能**: 取消已发布的售卖或购买

## 5. 界面优化说明

### 5.1 钱包设置页优化
1. 增加详细的设置说明和帮助信息
2. 改进表单验证，提供更明确的错误提示
3. 优化UI交互，提升用户体验
4. 增加设置成功后的自动跳转

### 5.2 资产售卖页优化
1. 支持多种资产类型选择
2. 增加可视化的折扣率调整滑块
3. 显示实时计算的折扣后金额
4. 添加资产售卖说明，帮助用户理解流程
5. 二次确认机制，避免误操作

### 5.3 交易列表页优化
1. 改进分段控制器，清晰区分不同状态
2. 增加筛选功能，可切换查看全部或我的交易
3. 优化列表项显示，增加资产类型标识
4. 状态标识使用不同颜色，一目了然
5. 改进弹窗交互，提供更直观的操作体验
6. 增加空状态提示，引导用户操作

## 6. 注意事项

1. 用户必须先设置钱包信息才能发布售卖
2. 售卖折扣率需在系统设定的范围内调整
3. 买家要确保支付凭证清晰可见，便于卖家核实
4. 卖家应及时审核买家提交的支付凭证
5. 交易过程中资产将被冻结，无法重复售卖
6. 系统支持多种支付通道，用户可根据自身情况选择

## 7. 常见问题

1. **问**: 为什么我无法发布售卖？
   **答**: 请先设置钱包信息，包括支付通道、钱包地址和收款码。

2. **问**: 如何修改已发布的售卖信息？
   **答**: 已发布的售卖信息不可修改，需要先取消后重新发布。

3. **问**: 买家支付后多久能收到资产？
   **答**: 卖家确认收到款项并审核通过后，买家将立即收到资产。

4. **问**: 如果买家上传了错误的支付凭证怎么办？
   **答**: 卖家可以选择"不通过"，订单将回到待处理状态。

5. **问**: 交易密码忘记了怎么办？
   **答**: 请联系客服重置交易密码。

## 8. 更新日志

### v1.0.0 (2023-06-01)
- 初始版本发布
- 支持佣金售卖功能
- 实现基本的买卖交易流程

### v1.1.0 (2023-08-15)
- 优化用户界面体验
- 增加交易详情页面
- 增强安全性验证

### v1.2.0 (2023-10-20)
- 增加更多资产类型支持
- 优化交易确认流程
- 修复已知问题 

### v1.3.0 (2023-12-15)
- 全面改版UI界面，提升用户体验
- 支持多种资产类型的售卖功能
- 增加可调节的折扣率设置
- 优化交易列表页显示和交互
- 改进凭证上传和审核流程 