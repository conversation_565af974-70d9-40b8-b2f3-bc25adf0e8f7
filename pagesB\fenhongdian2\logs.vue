<template>
<view class="container">
	<block v-if="isload">
		<!-- 筛选条件 -->
		<view class="filter-container">
			<view class="filter-tabs">
				<view class="filter-tab" :class="{active: filterType == ''}" @click="changeFilter('')">全部</view>
				<view class="filter-tab" :class="{active: filterType == 'add'}" @click="changeFilter('add')">增加</view>
				<view class="filter-tab" :class="{active: filterType == 'sub'}" @click="changeFilter('sub')">减少</view>
			</view>
		</view>

		<!-- 记录列表 -->
		<view class="content-container">
			<scroll-view class="logs-box" scroll-y="true" @scrolltolower="scrolltolower">
				<view class="logs-list">
					<view class="log-item" v-for="(item,index) in datalist" :key="index">
						<view class="log-header">
							<view class="log-type" :class="item.type">
								<text class="type-icon">{{item.type == 'add' ? '📈' : '📉'}}</text>
								<text class="type-text">{{item.type_text}}</text>
							</view>
							<view class="log-points" :class="item.type">{{item.points_text}}</view>
						</view>
						<view class="log-content">
							<view class="log-remark">{{item.remark}}</view>
							<view class="log-time">{{item.createtime_text}}</view>
						</view>
					</view>
				</view>
				<nomore text="没有更多记录了" v-if="nomore"></nomore>
				<nodata text="暂无变动记录" v-if="nodata"></nodata>
				<view style="width:100%;height:100rpx"></view>
			</scroll-view>
		</view>

		<!-- 统计信息 -->
		<view class="summary-container" v-if="summary">
			<view class="summary-item">
				<text class="summary-label">累计增加：</text>
				<text class="summary-value add">+{{summary.total_add}}</text>
			</view>
			<view class="summary-item">
				<text class="summary-label">累计减少：</text>
				<text class="summary-value sub">-{{summary.total_sub}}</text>
			</view>
			<view class="summary-item">
				<text class="summary-label">净增长：</text>
				<text class="summary-value" :class="summary.net_change >= 0 ? 'add' : 'sub'">
					{{summary.net_change >= 0 ? '+' : ''}}{{summary.net_change}}
				</text>
			</view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			menuindex: -1,
			pagenum: 1,
			nomore: false,
			nodata: false,
			datalist: [],
			filterType: '', // 筛选类型：''全部 'add'增加 'sub'减少
			summary: null
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	
	onPullDownRefresh: function () {
		this.getdata();
	},

	methods: {
		getdata: function () {
			var that = this;
			that.pagenum = 1;
			that.datalist = [];
			that.loaded();
			that.getdatalist();
			that.getSummary();
		},

		getdatalist: function (loadmore) {
			if (!loadmore) {
				this.pagenum = 1;
				this.datalist = [];
			}

			var that = this;
			var pagenum = that.pagenum;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
			var params = {
				page: pagenum,
				limit: 20
			};
			
			if (that.filterType) {
				params.type = that.filterType;
			}
			
			app.post('ApiFenhongdian/getFenhongdianLogs', params, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();

				if (res.status == 1) {
					var data = res.data.list;
					if (data.length == 0) {
						if (pagenum == 1) {
							that.nodata = true;
						} else {
							that.nomore = true;
						}
					}
					var datalist = that.datalist;
					var newdata = datalist.concat(data);
					that.datalist = newdata;
				} else if (res.status == -1) {
					app.gotoLogin();
				} else {
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},

		// 获取统计信息
		getSummary: function () {
			var that = this;
			var totalAdd = 0;
			var totalSub = 0;
			
			// 这里可以调用统计接口，暂时从已有数据计算
			that.datalist.forEach(function(item) {
				if (item.type == 'add') {
					totalAdd += item.points;
				} else {
					totalSub += item.points;
				}
			});
			
			that.summary = {
				total_add: totalAdd,
				total_sub: totalSub,
				net_change: totalAdd - totalSub
			};
		},

		scrolltolower: function () {
			if (!this.nomore) {
				this.pagenum = this.pagenum + 1;
				this.getdatalist(true);
			}
		},

		// 切换筛选类型
		changeFilter: function (type) {
			if (this.filterType != type) {
				this.filterType = type;
				this.getdata();
			}
		},

		loaded: function () {
			this.isload = true;
		},

		getmenuindex: function (e) {
			this.menuindex = e;
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}

/* 筛选条件 */
.filter-container{
	background: #fff;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
	border-bottom: 1rpx solid #f5f5f5;
}
.filter-tabs{
	display: flex;
	background: #f8f8f8;
	border-radius: 30rpx;
	padding: 6rpx;
}
.filter-tab{
	flex: 1;
	text-align: center;
	padding: 16rpx 20rpx;
	font-size: 28rpx;
	color: #666;
	border-radius: 24rpx;
	transition: all 0.3s;
}
.filter-tab.active{
	background: #FF6B35;
	color: #fff;
	font-weight: bold;
}

/* 内容区域 */
.content-container{
	flex: 1;
	height: 100%;
	display: flex;
	overflow: hidden;
}
.logs-box{
	width: 100%;
	height: 100%;
	padding: 0 30rpx;
}
.logs-list{
	height: auto;
	position: relative;
	overflow: hidden;
}

/* 记录项 */
.log-item{
	background: #fff;
	margin-bottom: 20rpx;
	padding: 30rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}
.log-header{
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.log-type{
	display: flex;
	align-items: center;
}
.type-icon{
	font-size: 32rpx;
	margin-right: 12rpx;
}
.type-text{
	font-size: 28rpx;
	font-weight: bold;
}
.log-type.add .type-text{
	color: #4CAF50;
}
.log-type.sub .type-text{
	color: #F44336;
}
.log-points{
	font-size: 32rpx;
	font-weight: bold;
}
.log-points.add{
	color: #4CAF50;
}
.log-points.sub{
	color: #F44336;
}
.log-content{
	border-top: 1rpx solid #f5f5f5;
	padding-top: 20rpx;
}
.log-remark{
	color: #333;
	font-size: 28rpx;
	margin-bottom: 12rpx;
	line-height: 1.5;
}
.log-time{
	color: #999;
	font-size: 24rpx;
}

/* 统计信息 */
.summary-container{
	background: #fff;
	margin: 20rpx 30rpx 30rpx;
	padding: 30rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}
.summary-item{
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}
.summary-item:last-child{
	border-bottom: none;
}
.summary-label{
	color: #666;
	font-size: 28rpx;
}
.summary-value{
	font-size: 30rpx;
	font-weight: bold;
}
.summary-value.add{
	color: #4CAF50;
}
.summary-value.sub{
	color: #F44336;
}
</style> 