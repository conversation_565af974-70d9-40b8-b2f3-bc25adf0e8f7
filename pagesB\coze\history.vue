<template>
<view class="container">
	<block v-if="isload">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="header-title">对话历史</view>
			<view class="header-actions">
				<view class="action-btn" @tap="clearAllHistory">
					<text class="iconfont iconqingchu"></text>
					<text>清空</text>
				</view>
			</view>
		</view>

		<!-- 对话列表 -->
		<view class="conversation-list">
			<block v-for="(conversation, index) in conversationList" :key="index">
				<view class="conversation-item" @tap="openConversation" :data-conversation="JSON.stringify(conversation)">
					<view class="conversation-header">
						<view class="conversation-title">
							{{conversation.title || '对话 ' + (index + 1)}}
						</view>
						<view class="conversation-time">{{conversation.update_time_text}}</view>
					</view>
					<view class="conversation-preview" v-if="conversation.last_message">
						{{conversation.last_message}}
					</view>
					<view class="conversation-actions">
						<view class="action-item" @tap.stop="deleteConversation" :data-conversation-id="conversation.conversation_id">
							<text class="iconfont iconshanchu"></text>
							<text>删除</text>
						</view>
					</view>
				</view>
			</block>
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
		</view>

		<!-- 对话详情弹窗 -->
		<uni-popup ref="conversationDetailPopup" type="bottom">
			<view class="detail-popup">
				<view class="popup-header">
					<view class="popup-title">对话详情</view>
					<view class="popup-close" @tap="closeConversationDetail">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				<scroll-view class="detail-content" scroll-y="true">
					<view class="message-list">
						<block v-for="(message, index) in currentMessages" :key="index">
							<view class="message-item" :class="message.role === 'user' ? 'user-message' : 'bot-message'">
								<view class="message-avatar">
									<image v-if="message.role === 'user'" :src="userInfo.avatar || '/static/img/default-user.png'"></image>
									<image v-else src="/static/img/default-bot.png"></image>
								</view>
								<view class="message-content">
									<view class="message-text">{{message.content}}</view>
									<view class="message-time">{{message.create_time_text}}</view>
								</view>
							</view>
						</block>
					</view>
				</scroll-view>
				<view class="popup-footer">
					<view class="btn-cancel" @tap="closeConversationDetail">关闭</view>
					<view class="btn-confirm" :style="{background: t('color1')}" @tap="continueConversation">继续对话</view>
				</view>
			</view>
		</uni-popup>

		<!-- 确认删除弹窗 -->
		<uni-popup ref="confirmDeletePopup" type="dialog">
			<uni-popup-dialog 
				type="confirm" 
				title="确认删除" 
				content="确定要删除这个对话吗？删除后无法恢复。"
				@confirm="confirmDelete"
				@close="closeConfirmDelete"
			></uni-popup-dialog>
		</uni-popup>

		<!-- 清空历史确认弹窗 -->
		<uni-popup ref="confirmClearPopup" type="dialog">
			<uni-popup-dialog 
				type="confirm" 
				title="确认清空" 
				content="确定要清空所有对话历史吗？清空后无法恢复。"
				@confirm="confirmClearAll"
				@close="closeClearConfirm"
			></uni-popup-dialog>
		</uni-popup>
	</block>
	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			isload: false,
			loading: false,
			userInfo: {},
			conversationList: [],
			currentMessages: [],
			currentConversation: {},
			deleteConversationId: '',
			pagenum: 1,
			nomore: false,
			nodata: false
		};
	},

	onLoad: function(opt) {
		this.userInfo = app.getUserInfo();
		this.getConversationList();
	},

	onReachBottom: function() {
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getConversationList(true);
		}
	},

	onPullDownRefresh: function() {
		this.getConversationList();
	},

	methods: {
		// 获取对话列表
		getConversationList: function(loadmore) {
			if (!loadmore) {
				this.pagenum = 1;
				this.conversationList = [];
			}

			var that = this;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;

			app.post('ApiCoze/getconversations', {
				pagenum: that.pagenum,
				pagesize: 20
			}, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();

				if (res.code === 1) {
					var data = res.data || [];
					
					// 处理数据
					data.forEach(function(item) {
						item.update_time_text = that.formatTime(item.update_time);
						item.create_time_text = that.formatTime(item.create_time);
					});

					if (that.pagenum === 1) {
						that.conversationList = data;
						if (data.length === 0) {
							that.nodata = true;
						}
					} else {
						if (data.length === 0) {
							that.nomore = true;
						} else {
							that.conversationList = that.conversationList.concat(data);
						}
					}
				} else {
					that.$refs.popmsg.show(res.msg);
					if (that.pagenum === 1) {
						that.nodata = true;
					}
				}
				that.loaded();
			});
		},

		// 打开对话详情
		openConversation: function(e) {
			var conversation = JSON.parse(e.currentTarget.dataset.conversation);
			this.currentConversation = conversation;
			this.getConversationMessages(conversation.conversation_id);
		},

		// 获取对话消息
		getConversationMessages: function(conversationId) {
			var that = this;
			that.loading = true;

			app.post('ApiCoze/getmessages', {
				conversation_id: conversationId,
				pagenum: 1,
				pagesize: 100
			}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					that.currentMessages = res.data || [];
					that.$refs.conversationDetailPopup.open();
				} else {
					that.$refs.popmsg.show(res.msg);
				}
			});
		},

		// 关闭对话详情
		closeConversationDetail: function() {
			this.$refs.conversationDetailPopup.close();
		},

		// 继续对话
		continueConversation: function() {
			this.closeConversationDetail();
			uni.navigateTo({
				url: '/pagesB/coze/chat?conversation_id=' + this.currentConversation.conversation_id
			});
		},

		// 删除对话
		deleteConversation: function(e) {
			this.deleteConversationId = e.currentTarget.dataset.conversationId;
			this.$refs.confirmDeletePopup.open();
		},

		// 确认删除
		confirmDelete: function() {
			var that = this;
			that.loading = true;

			app.post('ApiCoze/deleteconversation', {
				conversation_id: that.deleteConversationId
			}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					that.$refs.popmsg.show('删除成功');
					that.getConversationList();
				} else {
					that.$refs.popmsg.show(res.msg);
				}
			});

			this.closeConfirmDelete();
		},

		// 关闭删除确认
		closeConfirmDelete: function() {
			this.$refs.confirmDeletePopup.close();
		},

		// 清空所有历史
		clearAllHistory: function() {
			this.$refs.confirmClearPopup.open();
		},

		// 确认清空所有
		confirmClearAll: function() {
			var that = this;
			that.loading = true;

			// 批量删除所有对话
			var deletePromises = that.conversationList.map(function(conversation) {
				return new Promise(function(resolve) {
					app.post('ApiCoze/deleteconversation', {
						conversation_id: conversation.conversation_id
					}, function(res) {
						resolve(res);
					});
				});
			});

			Promise.all(deletePromises).then(function() {
				that.loading = false;
				that.$refs.popmsg.show('清空成功');
				that.getConversationList();
			});

			this.closeClearConfirm();
		},

		// 关闭清空确认
		closeClearConfirm: function() {
			this.$refs.confirmClearPopup.close();
		},

		// 格式化时间
		formatTime: function(timestamp) {
			var date = new Date(timestamp * 1000);
			var now = new Date();
			var diff = now - date;
			
			if (diff < 60000) { // 1分钟内
				return '刚刚';
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前';
			} else if (diff < 86400000) { // 24小时内
				return Math.floor(diff / 3600000) + '小时前';
			} else if (diff < 604800000) { // 7天内
				return Math.floor(diff / 86400000) + '天前';
			} else {
				return date.getFullYear() + '-' + 
					   (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
					   date.getDate().toString().padStart(2, '0');
			}
		}
	}
};
</script>

<style>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	background: #fff;
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #eee;
	position: fixed;
	top: var(--window-top);
	left: 0;
	right: 0;
	z-index: 100;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.header-actions {
	display: flex;
	align-items: center;
}

.action-btn {
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	background: #f0f0f0;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #666;
}

.action-btn .iconfont {
	margin-right: 10rpx;
	font-size: 28rpx;
}

.conversation-list {
	padding: 30rpx;
	margin-top: 120rpx;
}

.conversation-item {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.conversation-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.conversation-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.conversation-time {
	font-size: 24rpx;
	color: #999;
}

.conversation-preview {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 20rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}

.conversation-actions {
	display: flex;
	justify-content: flex-end;
}

.action-item {
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	background: #f5f5f5;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #ff4757;
}

.action-item .iconfont {
	margin-right: 8rpx;
	font-size: 24rpx;
}

.detail-popup {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.popup-header {
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 32rpx;
	color: #999;
}

.detail-content {
	flex: 1;
	padding: 30rpx;
	max-height: 50vh;
}

.message-list {
	padding-bottom: 20rpx;
}

.message-item {
	display: flex;
	margin-bottom: 30rpx;
}

.user-message {
	flex-direction: row-reverse;
}

.message-avatar {
	width: 60rpx;
	height: 60rpx;
	margin: 0 20rpx;
}

.message-avatar image {
	width: 100%;
	height: 100%;
	border-radius: 30rpx;
}

.message-content {
	max-width: 70%;
}

.user-message .message-content {
	text-align: right;
}

.message-text {
	background: #fff;
	padding: 20rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	color: #333;
	word-wrap: break-word;
	border: 1rpx solid #eee;
}

.user-message .message-text {
	background: #007aff;
	color: #fff;
	border: none;
}

.message-time {
	font-size: 20rpx;
	color: #999;
	margin-top: 10rpx;
}

.popup-footer {
	padding: 30rpx;
	border-top: 1rpx solid #eee;
	display: flex;
	justify-content: space-between;
}

.btn-cancel {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	background: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #666;
	margin-right: 20rpx;
}

.btn-confirm {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #fff;
}
</style>
