# 多城市API接口说明文档

本文档描述了系统中多城市模块的API接口，用于前端获取省市区数据及相关功能。

## 1. 接口基本信息

- **接口前缀**: `/ApiArea`

## 2. 接口列表

### 2.1 获取省份列表

获取所有省份数据，并按首字母分组返回。

- **接口地址**: `/ApiArea/getProvinces`
- **请求方式**: GET
- **请求参数**: 无
- **返回格式**:
```json
{
  "status": 1,
  "data": {
    "A": [
      {"id": 1, "name": "安徽省", "first_letter": "A"}
    ],
    "B": [
      {"id": 2, "name": "北京市", "first_letter": "B"}
    ],
    // 其他按首字母分组的省份数据
  }
}
```

### 2.2 获取城市列表

根据省份ID获取对应的城市列表。

- **接口地址**: `/ApiArea/getCities`
- **请求方式**: GET
- **请求参数**:
  - `province`: 省份ID（必填）
- **返回格式**:
```json
{
  "status": 1,
  "data": [
    {"id": 0, "name": "全部", "first_letter": "", "level": 1},
    {"id": 101, "name": "合肥市", "first_letter": "H", "level": 1},
    {"id": 102, "name": "芜湖市", "first_letter": "W", "level": 1},
    // 更多城市数据
  ]
}
```

### 2.3 获取区县列表

根据城市ID获取对应的区县列表。

- **接口地址**: `/ApiArea/getDistricts`
- **请求方式**: GET
- **请求参数**:
  - `city`: 城市ID（非必填，不传或传0则返回仅含"全部"选项的列表）
- **返回格式**:
```json
{
  "status": 1,
  "data": [
    {"id": 0, "name": "全部", "first_letter": "", "level": 2},
    {"id": 1001, "name": "蜀山区", "first_letter": "S", "level": 2},
    {"id": 1002, "name": "庐阳区", "first_letter": "L", "level": 2},
    // 更多区县数据
  ]
}
```

### 2.4 根据经纬度获取地区信息

根据用户当前位置的经纬度，获取对应的地区信息。

- **接口地址**: `/ApiArea/getAreaByLocation`
- **请求方式**: GET
- **请求参数**:
  - `latitude`: 纬度（必填）
  - `longitude`: 经度（必填）
  - `mode`: 地区级别（0:省份 1:城市 2:区县，默认0）
- **返回格式**:
```json
{
  "status": 1,
  "data": {
    "id": 1001,
    "name": "合肥市",
    "parent_id": 1
  }
}
```

### 2.5 获取已开通业务的地区列表

获取系统中已设置模板的地区列表，即已开通业务的地区。

- **接口地址**: `/ApiArea/getActiveAreas`
- **请求方式**: GET
- **请求参数**:
  - `mode`: 地区级别（0:省份 1:城市 2:区县，默认0）
- **返回格式**:
```json
{
  "status": 1,
  "data": {
    "B": [
      {"id": 2, "name": "北京市", "first_letter": "B"}
    ],
    "G": [
      {"id": 20, "name": "广州市", "first_letter": "G"}
    ],
    // 其他按首字母分组的地区数据
  }
}
```

## 3. 错误码说明

| 状态码 | 说明 |
|--------|------|
| 0 | 请求失败 |
| 1 | 请求成功 |

## 4. 调用示例

### 前端调用示例（JavaScript）

```javascript
// 获取省份列表
function getProvinces() {
  return new Promise((resolve, reject) => {
    getApp().get("ApiArea/getProvinces", {}, function(res) {
      if(res.status === 1) {
        resolve(res.data);
      } else {
        reject(res.msg);
      }
    });
  });
}

// 获取城市列表
function getCities(province) {
  return new Promise((resolve, reject) => {
    getApp().get("ApiArea/getCities", { province }, function(res) {
      if(res.status === 1) {
        resolve(res.data);
      } else {
        reject(res.msg);
      }
    });
  });
}

// 获取区县列表
function getDistricts(city) {
  return new Promise((resolve, reject) => {
    getApp().get("ApiArea/getDistricts", { city }, function(res) {
      if(res.status === 1) {
        resolve(res.data);
      } else {
        reject(res.msg);
      }
    });
  });
}

// 根据位置获取地区
function getAreaByLocation(latitude, longitude, mode = 0) {
  return new Promise((resolve, reject) => {
    getApp().get("ApiArea/getAreaByLocation", { 
      latitude, 
      longitude, 
      mode 
    }, function(res) {
      if(res.status === 1) {
        resolve(res.data);
      } else {
        reject(res.msg);
      }
    });
  });
}
```

## 5. 注意事项

1. 所有接口都需要传递 `aid` 参数（商户ID），这个参数在系统初始化时已经设置。
2. 经纬度获取地区接口依赖于系统设置中的腾讯地图API密钥（qq_geocoder_key），请确保已正确配置。
3. 地区数据表 `ddwx_area` 中的字段说明：
   - `id`: 地区ID
   - `name`: 地区名称
   - `parent_id`: 上级地区ID
   - `level`: 地区级别（0:省份 1:城市 2:区县）
   - `first_letter`: 地区名称首字母，用于分组展示 