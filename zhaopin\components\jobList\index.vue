<template>
    <view>
        <view class="common-tab" v-if="isTabList">
            <view class="common-tab-header">
                <view @tap="onTabChange" 
                    class="common-tab-header-item" 
                    :class="tabCurrent === 0 ? 'active' : ''" 
                    :style="[tabCurrent === 0 ? {color: t('color1')} : {}]"
                    data-i="0" 
                    data-ptpid="10e4-1302-bf52-5bd6">
                    相关推荐
                    <view v-if="tabCurrent === 0" class="active-line" :style="{background: t('color1')}"></view>
                </view>
                <view @tap="onTabChange" 
                    class="common-tab-header-item" 
                    :class="tabCurrent === 1 ? 'active' : ''" 
                    :style="[tabCurrent === 1 ? {color: t('color1')} : {}]"
                    data-i="1" 
                    data-ptpid="a595-16aa-8066-b092">
                    周围的人都在看
                    <view v-if="tabCurrent === 1" class="active-line" :style="{background: t('color1')}"></view>
                </view>
            </view>
            <block v-if="recommendList.length > 0">
                <regular-item
                    :data="item"
                    :hiddenClassImage="true"
                    :index="index"
                    :listIndex="tabCurrent"
                    :ptpId="tabCurrent ? 'fjfg-2jff-2fhg-2jfg' : 'dkef-lfm3-pfk2-c82m'"
                    v-for="(item, index) in recommendList"
                    :key="index"
                ></regular-item>
            </block>
            <view class="recommond-blank" v-else>
                <image lazyLoad class="recommond-blank-pic" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20200623_blank.png"></image>
                <view>这里暂时空空如也</view>
            </view>
        </view>
        <block v-else>
            <block v-if="recommendList.length > 0">
                <view class="jobList-common-title">{{ title }}</view>
                <regular-item :data="item" :hiddenClassImage="true" :index="index" ptpId="dkef-lfm3-pfk2-c82m" v-for="(item, index) in recommendList" :key="index"></regular-item>
            </block>
        </block>
    </view>
</template>

<script>
import regularItem from '../../components/regularItem/index';
export default {
    data() {
        return {};
    },
    components: {
        regularItem
    },
    props: {
        isTabList: {
            type: Boolean,
            default: false
        },
        tabCurrent: {
            type: Number,
            default: 0
        },
        recommendList: {
            type: Array,
            default: () => []
        },
        title: {
            type: String,
            default: '大家都在看'
        }
    },
    created() {
        console.log('job-list组件created:', {
            isTabList: this.isTabList,
            tabCurrent: this.tabCurrent,
            recommendList: this.recommendList
        });
    },
    methods: {
        onTabChange(e) {
            this.$emit('tabChange', {
                detail: e
            });
        }
    }
};
</script>

<style>
.common-tab-header {
    display: flex;
    margin: 48rpx 32rpx 24rpx;
    height: 96rpx;
}

.common-tab-header-item {
    color: rgba(17, 30, 56, 0.52);
    font-size: 36rpx;
    font-weight: 400;
    margin-right: 64rpx;
    overflow: visible;
    height: 98rpx;
    line-height: 98rpx;
    position: relative;
}

.common-tab-header-item.active {
    font-weight: 600;
}

.active-line {
    position: absolute;
    width: 100%;
    height: 8rpx;
    border-radius: 4rpx;
    left: 0;
    bottom: 0;
}

.recommond-blank {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 100rpx 0 136rpx;
    color: #aeb2bb;
    font-size: 24rpx;
    font-weight: 400;
}

.recommond-blank-pic {
    display: block;
    width: 520rpx;
    height: 360rpx;
    margin-bottom: 32rpx;
}

.jobList-common-title {
    color: #111e38;
    font-size: 36rpx;
    font-weight: 700;
    line-height: 50rpx;
    padding: 48rpx 0 24rpx 32rpx;
}
</style>
