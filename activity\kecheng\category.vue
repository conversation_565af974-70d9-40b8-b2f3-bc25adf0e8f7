<template>
<view class="container">
	<block v-if="isload">
		<view class="view-show">
			<view @tap.stop="goto" data-url="/activity/kecheng/list" class="search-container">
				<view class="search-box">
					<image class="img" src="/static/img/search_ico.png"></image>
					<view class="search-text">搜索感兴趣的课程</view>
				</view>
			</view>
			<!-- 顶部导航栏 -->
			<scroll-view class="top-nav" scroll-x="true" :scroll-left="scrollLeft" :scroll-with-animation="true" @scroll="onNavScroll">
				<view 
					v-for="(item, index) in data" 
					:key="index"
					class="nav-item" 
					:class="index===currentActiveIndex?'active':''"
					@tap="clickRootItem" 
					:data-root-item-id="item.id" 
					:data-root-item-index="index"
					:id="'nav-item-' + index"
				>
					{{item.name}}
					<view class="course-count-badge" v-if="item.course_count > 0">{{item.course_count}}</view>
				</view>
			</scroll-view>
			<!-- 课程内容 - 使用swiper实现左右滑动 -->
			<swiper class="content-swiper" :current="currentActiveIndex" @change="onSwiperChange" :style="{ height: swiperHeight + 'px' }">
				<swiper-item v-for="(item, tabIndex) in data" :key="tabIndex" class="swiper-item">
					<scroll-view 
						class="course-list" 
						scroll-y="true" 
						:class="menuindex>-1?'tabbarbot':'notabbarbot'"
					>
						<view class="course-item" v-for="(detail, index) in tabIndex === currentActiveIndex ? clist : []" :key="index">
							<view class="course-header">
								<view class="txt">{{detail.name}}</view>
								<view class="show-all" @tap="hideshow" :data-key="index" :data-id="detail.id">{{detail.ishide!=1 ? '收起' : '展开'}}</view>
							</view>
							<view class="course-content" v-if="detail.ishide!=1">
								<block v-if="courseList.length > 0">
									<view 
										v-for="(item, itemIndex) in courseList" 
										:key="itemIndex" 
										@tap.stop="goto" 
										:data-url="'/activity/kecheng/product?id='+item.id" 
										class="course-card"
									>
										<view class="course-pic">
											<image class="img" :src="item.pic"></image>
										</view>
										<view class="course-info">
											<view class="course-name">{{item.name}}</view>
											<view class="course-bottom">
												<view class="course-price" v-if="item.price > 0">￥{{item.price}}</view>
												<view class="course-price free" v-else>免费</view>
												<view class="course-count">{{getJoinNum(item)}}人学习</view>
											</view>
										</view>
									</view>
								</block>
								<view v-else class="no-course">暂无课程</view>
							</view>
						</view>
						<nodata v-if="nodata && tabIndex === currentActiveIndex"></nodata>
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>
	</block>
	<view style="display:none">{{test}}</view>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      data: [],
      currentActiveIndex: 0,
      animation: true,
      clist: [],
			bid:'',
			test:'',
			nodata: false,
			courseList: [],
			pagenum: 1,
			hasMore: true,
			currentCid: '',
			scrollLeft: 0,
			tabsData: {}, // 存储每个标签页的数据
			swiperHeight: 500, // 默认高度值
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.bid = this.opt.bid ? this.opt.bid : '';
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onReady: function() {
		// 设置页面高度
		this.setContainerHeight();
	},
	onShow: function() {
		// 页面显示时也更新高度
		this.setContainerHeight();
	},
  methods: {
		// 获取参与学习人数
		getJoinNum: function(item) {
			// 兼容不同字段名称：join_num或join_nums或study_num
			return item.join_num || item.join_nums || item.study_num || 0;
		},
		// 设置容器高度
		setContainerHeight: function() {
			const that = this;
			const systemInfo = uni.getSystemInfoSync();
			const windowHeight = systemInfo.windowHeight;
			
			// 动态设置容器高度
			uni.createSelectorQuery().in(that)
				.select('.search-container').boundingClientRect()
				.select('.top-nav').boundingClientRect()
				.exec(function(res) {
					if(res && res[0] && res[1]) {
						const searchHeight = res[0].height || 94;
						const navHeight = res[1].height || 80;
						// 计算swiper高度 = 窗口高度 - 搜索框高度 - 导航栏高度 - tabbar高度(如果有)
						let swiperHeight = windowHeight - searchHeight - navHeight;
						if(that.menuindex > -1) {
							swiperHeight -= 100; // tabbar高度，通常是100rpx
						}
						// 直接更新数据中的高度值，不使用DOM API
						that.swiperHeight = swiperHeight;
					}
				});
		},
		getdata: function () {
			var that = this;
			that.loading = true;
			that.isload = false;
			that.clist = [];
			that.courseList = [];
			that.nodata = false;
			that.tabsData = {};

			app.get('ApiKecheng/category5', {bid:that.bid}, function (categoryRes) {
				that.data = categoryRes.data || [];
				if (that.data.length > 0) {
					that.currentActiveIndex = 0;
					that.currentCid = that.data[0].id;

					let callsToDo = 2;
					let callsDone = 0;
					function checkDone() {
						callsDone++;
						if (callsDone === callsToDo) {
							if ((!that.clist || that.clist.length === 0) && that.courseList && that.courseList.length > 0) {
								let currentTopCategory = that.data.find(cat => cat.id === that.currentCid);
								let sectionName = currentTopCategory ? currentTopCategory.name : '课程';
								that.clist = [{ name: sectionName, id: 'default_section_' + that.currentCid, ishide: 0 }];
							}
							if ((!that.clist || that.clist.length === 0) && (!that.courseList || that.courseList.length === 0)) {
								that.nodata = true;
							} else {
								that.nodata = false;
							}
							that.loading = false;
							that.isload = true;
							
							// 保存当前标签页数据
							that.tabsData[that.currentCid] = {
								clist: JSON.parse(JSON.stringify(that.clist)),
								courseList: JSON.parse(JSON.stringify(that.courseList)),
								nodata: that.nodata
							};
							
							// 更新页面布局
							that.$nextTick(function() {
								that.setContainerHeight();
							});
						}
					}

					app.post("ApiKecheng/getdownclist3", {id: that.currentCid, bid:that.bid}, function (sectionRes) {
						if (sectionRes && sectionRes.data && Array.isArray(sectionRes.data)) {
							that.clist = sectionRes.data.map(item => ({ ...item, ishide: item.ishide === 1 ? 1 : 0 }));
						} else {
							that.clist = [];
						}
						checkDone();
					});

					app.post('ApiKecheng/getlist', {
						pagenum: 1, keyword: '', field: '', order: '',
						cid: that.currentCid, cpid: 0, bid: that.bid
					}, function(courseRes) {
						if(courseRes.status == 1 && courseRes.data && Array.isArray(courseRes.data)) {
							that.courseList = courseRes.data;
							that.pagenum = 1;
							that.hasMore = courseRes.data.length > 0;
						} else {
							that.courseList = [];
							that.hasMore = false;
						}
						checkDone();
					});
				} else {
					that.nodata = true;
					that.loading = false;
					that.isload = true;
				}
			});
		},
		// 加载特定分类的数据
		loadTabData: function(id, index) {
			var that = this;
			
			// 如果已经有缓存数据，直接使用
			if (that.tabsData[id]) {
				that.clist = that.tabsData[id].clist;
				that.courseList = that.tabsData[id].courseList;
				that.nodata = that.tabsData[id].nodata;
				that.loading = false;
				return;
			}
			
			that.loading = true;
			that.clist = [];
			that.courseList = [];
			that.nodata = false;

			let callsToDo = 2;
			let callsDone = 0;
			function checkDone() {
				callsDone++;
				if (callsDone === callsToDo) {
					if ((!that.clist || that.clist.length === 0) && that.courseList && that.courseList.length > 0) {
						let currentTopCategory = that.data.find(cat => cat.id === id);
						let sectionName = currentTopCategory ? currentTopCategory.name : '课程';
						that.clist = [{ name: sectionName, id: 'default_section_' + id, ishide: 0 }];
					}
					if ((!that.clist || that.clist.length === 0) && (!that.courseList || that.courseList.length === 0)) {
						that.nodata = true;
					} else {
						that.nodata = false;
					}
					that.loading = false;
					
					// 保存当前标签页数据
					that.tabsData[id] = {
						clist: JSON.parse(JSON.stringify(that.clist)),
						courseList: JSON.parse(JSON.stringify(that.courseList)),
						nodata: that.nodata
					};
				}
			}

			app.post("ApiKecheng/getdownclist3", {id: id, bid:that.bid}, function (sectionRes) {
				if (sectionRes && sectionRes.data && Array.isArray(sectionRes.data)) {
					that.clist = sectionRes.data.map(item => ({ ...item, ishide: item.ishide === 1 ? 1 : 0 }));
				} else {
					that.clist = [];
				}
				checkDone();
			});

			app.post('ApiKecheng/getlist', {
				pagenum: 1, keyword: '', field: '', order: '',
				cid: id, cpid: 0, bid: that.bid
			}, function(courseRes) {
				if(courseRes.status == 1 && courseRes.data && Array.isArray(courseRes.data)) {
					that.courseList = courseRes.data;
					that.pagenum = 1;
					that.hasMore = courseRes.data.length > 0;
				} else {
					that.courseList = [];
					that.hasMore = false;
				}
				checkDone();
			});
		},
		// swiper滑动改变事件
		onSwiperChange: function(e) {
			const index = e.detail.current;
			this.currentActiveIndex = index;
			this.currentCid = this.data[index].id;
			this.updateNavPosition(index);
			
			// 加载当前标签页数据
			this.loadTabData(this.currentCid, index);
		},
		// 导航栏滚动事件
		onNavScroll: function(e) {
			// 可以在这里处理导航栏滚动逻辑
		},
		// 更新导航位置
		updateNavPosition: function(index) {
			const that = this;
			const query = uni.createSelectorQuery().in(that);
			query.select('#nav-item-' + index).boundingClientRect();
			query.selectViewport().scrollOffset();
			query.exec(function(res) {
				if (res && res[0]) {
					const tabWidth = res[0].width || 0;
					const offsetLeft = res[0].left || 0;
					const windowWidth = uni.getSystemInfoSync().windowWidth;
					that.scrollLeft = offsetLeft - (windowWidth / 2) + (tabWidth / 2);
				}
			});
		},
    clickRootItem: function (t) {
      var that = this;
      var e = t.currentTarget.dataset;
      that.currentActiveIndex = e.rootItemIndex;
      var id = e.rootItemId;
			that.currentCid = id;
			
			// 加载当前标签页数据
			that.loadTabData(id, e.rootItemIndex);
    },
    gotoCatproductPage: function (t) {
      var e = t.currentTarget.dataset;
			app.goto('/activity/kecheng/list?cid=' + e.id);
    },
		hideshow:function(e){
      var that = this;
			var index = e.currentTarget.dataset.key;
			if(that.clist[index].ishide == 1){
				that.clist[index].ishide = 0;
			}else{
				that.clist[index].ishide = 1;
			}
			that.test = Math.random();
			
			// 更新缓存数据
			if (that.tabsData[that.currentCid]) {
				that.tabsData[that.currentCid].clist = JSON.parse(JSON.stringify(that.clist));
			}
		},
		getmenuindex: function(e) {
			this.menuindex = e;
			// tabbar显示状态变化时，重新计算容器高度
			this.$nextTick(() => {
				this.setContainerHeight();
			});
		},
		goto: function(e) {
			let url = e.currentTarget.dataset.url;
			if(url) {
				app.goto(url);
			}
		},
		loaded: function() {
			this.isload = true;
		}
  }
};
</script>
<style>
page {position: relative;width: 100%;height: 100%;}
button {border: 0 solid!important;}
.container{height:100%;}
.view-show{background-color: white;line-height: 1;width: 100%;height: 100%;display: flex;flex-direction: column;}
.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5;flex-shrink: 0;}
.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}
.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}
.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}

/* 顶部导航栏样式 */
.top-nav {
  display: flex;
  white-space: nowrap;
  width: 100%;
  height: 80rpx;
  background: #fff;
  border-bottom: 1px solid #f5f5f5;
  padding: 0 10rpx;
  flex-shrink: 0;
}
.top-nav .nav-item {
  display: inline-block;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.top-nav .nav-item.active {
  color: #222;
  font-weight: bold;
  position: relative;
}
.top-nav .nav-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #222;
}
.top-nav .nav-item .course-count-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  min-width: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  background: #FF5722;
  color: #fff;
  border-radius: 15rpx;
  font-size: 20rpx;
  padding: 0 6rpx;
  font-weight: normal;
}

/* Swiper样式 */
.content-swiper {
  width: 100%;
  flex: 1;
}
.swiper-item {
  height: 100%;
  overflow: hidden;
}

/* 课程列表样式 */
.course-list {
  height: 100%;
  padding: 20rpx;
  background: #f6f6f6;
  box-sizing: border-box;
}
.course-item {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}
.course-header {
  height: 82rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  background: #fff;
}
.course-header .txt {
  color: #222222;
  font-weight: bold;
  font-size: 28rpx;
}
.course-header .show-all {
  font-size: 22rpx;
  color: #949494;
}
.course-content {
  padding: 0 20rpx;
}
.course-card {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}
.course-card:last-child {
  border-bottom: none;
}
.course-pic {
  width: 200rpx;
  height: 140rpx;
  background: #f7f7f7;
  border-radius: 8rpx;
  overflow: hidden;
}
.course-pic .img {
  width: 100%;
  height: 100%;
}
.course-info {
  flex: 1;
  margin-left: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.course-name {
  font-size: 28rpx;
  color: #222;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.course-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.course-price {
  font-size: 28rpx;
  color: #FF5722;
  font-weight: bold;
}
.course-price.free {
  color: #4CAF50;
}
.course-count {
  font-size: 24rpx;
  color: #999;
}
.no-course {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 保持加载和tabbar样式 */
.tabbarbot{padding-bottom:100rpx;}
.notabbarbot{padding-bottom:0rpx;}
</style>