<template>
	<view class="container">
		<block v-if="isload">
			<view class="ordertop" :style="'background:url('+(shopset.order_detail_toppic?shopset.order_detail_toppic: pre_url + '/static/img/ordertop.png')+');background-size:100%'">
				<view class="f1" v-if="detail.status==0">
					<view class="t1">等待买家付款</view>
					<view class="t2" v-if="djs">剩余时间：{{djs}}</view>
				</view>
				<view class="f1" v-if="detail.status==1">
					<view class="t1">{{detail.paytypeid==4 ? '已选择'+detail.paytype : '已成功付款'}}</view>
					<view class="t2" v-if="detail.freight_type!=1">请尽快发货</view>
					<view class="t2" v-if="detail.freight_type==1">待提货</view>
				</view>
				<view class="f1" v-if="detail.status==2">
					<view class="t1">订单已发货</view>
					<view class="t2" v-if="detail.freight_type!=3">发货信息：{{detail.express_com}} {{detail.express_no}}</view>
				</view>
				<view class="f1" v-if="detail.status==3">
					<view class="t1">订单已完成</view>
				</view>
				<view class="f1" v-if="detail.status==4">
					<view class="t1">订单已取消</view>
				</view>
			</view>
			<view class="address">
				<view class="img">
					<image src="/static/img/address3.png"></image>
				</view>
				<view class="info">
					<text class="t1" user-select="true" selectable="true">{{detail.linkman}} <text v-if="detail.tel" @tap="goto" :data-url="'tel:'+detail.tel" style="margin-left: 20rpx;">{{detail.tel}}</text></text>
					<text class="t2" v-if="detail.freight_type!=1 && detail.freight_type!=3" user-select="true" selectable="true">地址：{{detail.area}}{{detail.address}}</text>
					<text class="t2" v-if="detail.freight_type==1" @tap="openLocation" :data-address="storeinfo.address" :data-latitude="storeinfo.latitude" :data-longitude="storeinfo.longitude" user-select="true" selectable="true">取货地点：{{storeinfo.name}} - {{storeinfo.address}}</text>
				</view>
			</view>
			<view class="product">
				<view  v-for="(item, idx) in prolist" :key="idx" class="content">
					<view @tap="goto" :data-url="'/shopPackage/shop/product?id=' + item.proid">
						<image :src="item.pic"></image>
					</view>
					<view class="detail">
						<text class="t1">{{item.name}}</text>
						<text class="t2">{{item.ggname}}</text>
						<view v-if="item.is_yh == 0" class="t3">
							<text class="x1 flex1">￥{{item.sell_price}}</text><text class="x2">×{{item.num}}</text>
						</view>
						<view v-if="item.is_yh == 1" class="t3">
							<text class="x1 flex1">￥{{item.yh_prices}}(优惠金额)</text><text class="x2">×{{item.yh_nums}}</text>
						</view>
						<!-- 修改框价格信息的样式 -->
						<view class="t3 frame-price" v-if="detail.total_frame_price">
							<text class="frame-label">框价格:</text>
							<text class="frame-value">￥{{item.frame_price}}</text>
							<text class="frame-multiply">×</text>
							<text class="frame-num">{{item.num}}</text>
							<text class="frame-equal">=</text>
							<text class="frame-total">￥{{item.frame_total}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="orderinfo" v-if="(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)">
				<view class="item flex-col">
					<text class="t1" style="color:#111">发货信息</text>
					<text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx" user-select="true" selectable="true">{{detail.freight_content}}</text>
				</view>
			</view>
			
			<view class="orderinfo">
				<view class="item">
					<text class="t1">下单人</text>
					<text class="flex1"></text>
					<image :src="detail.headimg" style="width:80rpx;height:80rpx;margin-right:8rpx"/>
					<text  style="height:80rpx;line-height:80rpx">{{detail.nickname}}</text>
				</view>
				<view class="item">
					<text class="t1">{{t('会员')}}ID</text>
					<text class="t2">{{detail.mid}}</text>
				</view>
			</view>
			<view class="orderinfo" v-if="detail.remark">
				<view class="item">
					<text class="t1">备注</text>
					<text class="t2" user-select="true" selectable="true">{{detail.remark}}</text>
				</view>
			</view>
			<view class="orderinfo">
				<view class="item">
					<text class="t1">订单编号</text>
					<text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text>
				</view>
				<view class="item">
					<text class="t1">下单时间</text>
					<text class="t2">{{detail.createtime}}</text>
				</view>
				<view class="item" v-if="detail.status>0 && detail.paytypeid!='4' && detail.paytime">
					<text class="t1">支付时间</text>
					<text class="t2">{{detail.paytime}}</text>
				</view>
				<view class="item" v-if="detail.status>0 && detail.paytime">
					<text class="t1">支付方式</text>
					<text class="t2">{{detail.paytype}}</text>
				</view>
				<view class="item" v-if="detail.status>1 && detail.send_time">
					<text class="t1">发货时间</text>
					<text class="t2">{{detail.send_time}}</text>
				</view>
				<view class="item" v-if="detail.status==3 && detail.collect_time">
					<text class="t1">收货时间</text>
					<text class="t2">{{detail.collect_time}}</text>
				</view>
			</view>
			<view class="orderinfo">
				<view class="item">
					<text class="t1">商品金额</text>
					<text class="t2 red">¥{{detail.product_price}}</text>
				</view>
				<!-- 添加框总价显示 -->
				<view class="item" v-if="detail.total_frame_price">
					<text class="t1">框总价</text>
					<text class="t2 red">¥{{detail.total_frame_price}}</text>
				</view>
				<view class="item" v-if="detail.disprice > 0">
					<text class="t1">{{t('会员')}}折扣</text>
					<text class="t2 red">-¥{{detail.leveldk_money}}</text>
				</view>
				<view class="item" v-if="detail.jianmoney > 0">
					<text class="t1">满减活动</text>
					<text class="t2 red">-¥{{detail.manjian_money}}</text>
				</view>
				<view class="item" v-if="detail.invoice_money > 0">
					<text class="t1">发票费用</text>
					<text class="t2 red">+¥{{detail.invoice_money}}</text>
				</view>
				<view class="item">
					<text class="t1">配送方式</text>
					<text class="t2">{{detail.freight_text}}</text>
				</view>
				<view class="item" v-if="detail.freight_type==1 && detail.freightprice > 0">
					<text class="t1">服务费</text>
					<text class="t2 red">+¥{{detail.freight_price}}</text>
				</view>
				<view class="item" v-if="detail.freight_time">
					<text class="t1">{{detail.freight_type!=1?'配送':'提货'}}时间</text>
					<text class="t2">{{detail.freight_time}}</text>
				</view>
				<view class="item" v-if="detail.coupon_money > 0">
					<text class="t1">{{t('优惠券')}}抵扣</text>
					<text class="t2 red">-¥{{detail.coupon_money}}</text>
				</view>
	
				<view class="item" v-if="detail.scoredk > 0">
					<text class="t1">{{t('积分')}}抵扣</text>
					<text class="t2 red">-¥{{detail.scoredk_money}}</text>
				</view>
				<view class="item">
					<text class="t1">实付款</text>
					<text class="t2 red">¥{{detail.totalprice}}</text>
				</view>
	
				<view class="item">
					<text class="t1">订单状态</text>
					<text class="t2" v-if="detail.status==0">未付款</text>
					<text class="t2" v-if="detail.status==1">已付款</text>
					<text class="t2" v-if="detail.status==2">已发货</text>
					<text class="t2" v-if="detail.status==3">已收货</text>
					<text class="t2" v-if="detail.status==4">已关闭</text>
				</view>
				<view class="item" v-if="detail.refund_status>0">
					<text class="t1">退款状态</text>
					<text class="t2 red" v-if="detail.refund_status==1">审核中,¥{{detail.refund_money}}</text>
					<text class="t2 red" v-if="detail.refund_status==2">已退款,¥{{detail.refund_money}}</text>
					<text class="t2 red" v-if="detail.refund_status==3">已驳回,¥{{detail.refund_money}}</text>
				</view>
				<view class="item" v-if="detail.refund_status>0">
					<text class="t1">退款原因</text>
					<text class="t2 red">{{detail.refund_reason}}</text>
				</view>
				<view class="item" v-if="detail.refund_checkremark">
					<text class="t1">审核备注</text>
					<text class="t2 red">{{detail.refund_checkremark}}</text>
				</view>
				<view class="item flex-col" v-if="(detail.status==1 || detail.status==2) && detail.freight_type==1">
					<text class="t1">核销码</text>
					<view class="flex-x-center">
						<image :src="detail.hexiao_qr" style="width:400rpx;height:400rpx" @tap="previewImage" :data-url="detail.hexiao_qr"></image>
					</view>
				</view>
	
				<view class="item" v-if="detail.balance_price>0">
					<text class="t1">尾款</text>
					<text class="t2 red">¥{{detail.balance_price}}</text>
				</view>
				<view class="item" v-if="detail.balance_price>0">
					<text class="t1">尾款状态</text>
					<text class="t2" v-if="detail.balance_pay_status==1">已支付</text>
					<text class="t2" v-if="detail.balance_pay_status==0">未支付</text>
				</view>
			</view>
			<view class="orderinfo" v-if="detail.checkmemid">
				<view class="item">
					<text class="t1">所选会员</text>
					<text class="flex1"></text>
					<image :src="detail.checkmember.headimg" style="width:80rpx;height:80rpx;margin-right:8rpx"/>
					<text  style="height:80rpx;line-height:80rpx">{{detail.checkmember.nickname}}</text>
				</view>
			</view>
	
			<view class="orderinfo" v-if="(detail.formdata).length > 0">
				<view class="item" v-for="item in detail.formdata" :key="index">
					<text class="t1">{{item[0]}}</text>
					<view class="t2" v-if="item[2]=='upload'"><image :src="item[1]" style="width:400rpx;height:auto" mode="widthFix" @tap="previewImage" :data-url="item[1]"/></view>
					<text class="t2" v-else user-select="true" selectable="true">{{item[1]}}</text>
				</view>
			</view>
	
			<view style="width:100%;height:160rpx"></view>
	
			<view class="bottom notabbarbot">
				<view class="btn2" @tap="goto" :data-url="'/admin/member/historys?id='+detail.mid" v-if="detail.bid==0">历史订单</view>
				<view v-if="detail.refund_status==1" class="btn2" @tap="refundnopass" :data-id="detail.id">退款驳回</view>
				<view v-if="detail.refund_status==1" class="btn2" @tap="refundpass" :data-id="detail.id">退款通过</view>
				<view v-if="detail.status==0" class="btn2" @tap="closeOrder" :data-id="detail.id">关闭订单</view>
				<view v-if="detail.status==0 && detail.bid==0" class="btn2" @tap="ispay" :data-id="detail.id">改为已支付</view>
				<view v-if="detail.status==1" class="btn2" @tap="fahuo" :data-id="detail.id">发货</view>
				<view v-if="(detail.status==1 || detail.status==2) && detail.freight_type==1" class="btn2" @tap="hexiao" :data-id="detail.id">核销</view>
				<block v-if="detail.status==1 && detail.canpeisong">
					<view class="btn2" v-if="detail.express_wx_status" @tap="peisongWx" :data-id="detail.id">即时配送</view>
					<view class="btn2" v-else @tap="peisong" :data-id="detail.id">配送</view>
				</block>
				<block v-if="(detail.status==2 || detail.status==3) && detail.express_com">
					<view v-if="detail.express_type =='express_wx'" class="btn2" @tap="goto" :data-url="'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no+'&type=express_wx'">订单跟踪</view>
					<!-- <view v-else class="btn2" @tap="goto" :data-url="'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no+'&express_content='+detail.express_content">查物流</view> -->
					<view  class="btn2" v-else @tap.stop="logistics(detail)" :data-index="index">查物流 </view>
				</block>
				<block v-if="(detail.status==2 || detail.status==3) && detail.express_com">
					<view  class="btn2" @tap.stop="fahuoedit" :data-index="index">改物流 </view>
				</block>
				<view v-if="detail.status==2 && detail.freight_type==10" class="btn2" @tap="fahuo" :data-id="detail.id">修改物流</view>
				<view v-if="detail.status==4" class="btn2" @tap="delOrder" :data-id="detail.id">删除</view>
				<view class="btn2" @tap="setremark" :data-id="detail.id">设置备注</view>
				<!-- 新增的复制订单信息按钮 -->
				        <view class="btn2" @tap="openCopyDialog">复制信息</view>
				<!-- 新增退款-2024-12-11 -->
				<view v-if="detail.status==1" class="btn2" @tap="tuikuan" :data-id="detail.id">退款</view>
			</view>
			<uni-popup id="dialogSetremark" ref="dialogSetremark" type="dialog">
				<uni-popup-dialog mode="input" title="设置备注" :value="detail.remark" placeholder="请输入备注" @confirm="setremarkconfirm"></uni-popup-dialog>
			</uni-popup>
			
  <!-- 复制订单信息的弹窗 -->
  			<uni-popup id="copyDialog" ref="copyDialog" type="dialog">
  				<view class="copy-dialog">
  					<view class="copy-dialog-header">
  						<text class="copy-dialog-title">选择要复制的信息</text>
  						<text class="copy-dialog-close" @tap="closeCopyDialog">×</text>
  					</view>
  					
  					<view class="copy-dialog-content">
  						<checkbox-group @change="onFieldChange">
  							<label class="copy-field-item" v-for="field in copyFields" :key="field.value">
  								<checkbox :value="field.value" :checked="field.checked" color="#007AFF" />
  								<text class="copy-field-label">{{field.label}}</text>
  							</label>
  						</checkbox-group>
  					</view>
  					
  					<view class="copy-dialog-footer">
  						<view class="copy-dialog-btn cancel" @tap="closeCopyDialog">取消</view>
  						<view class="copy-dialog-btn confirm" @tap="confirmCopy">确定</view>
  					</view>
  				</view>
  			</uni-popup>
			<uni-popup id="dialogExpress" ref="dialogExpress" type="dialog">
				<view class="express-dialog">
					<view class="express-dialog-header">
						<text class="express-dialog-title">发货</text>
					</view>
					<scroll-view scroll-y class="express-dialog-content">
						<!-- 第一个物流信息 -->
						<view class="express-item">
							<view class="express-item-header">
								<text class="express-item-title">物流信息 1</text>
								<view class="express-item-delete" @tap.stop="deletefahuo(0)" v-if="fahuodatas.length > 1">删除</view>
							</view>
							<view class="express-item-body">
								<view class="form-item">
									<text class="form-label">快递公司：</text>
									<picker @change="expresschange" :value="express_index" :range="expressdata" class="form-picker">
										<view class="picker">{{expressdata[express_index]}}</view>
									</picker>
								</view>
								<view class="form-item">
									<text class="form-label">快递单号：</text>
									<input type="text" 
										placeholder="请输入快递单号" 
										@input="setexpressno" 
										class="form-input"
										:value="express_no"/>
								</view>
								<view class="form-item" v-if="prolist.length>=2">
									<text class="form-label">选择商品：</text>
									<checkbox-group @change="checkboxChange_fahuo($event,0)" class="checkbox-group">
										<label v-for="(pi, px) in prolist" :key="px" class="checkbox-item">
											<checkbox :value="pi.id.toString()" />
											<text class="checkbox-label">{{ pi.name }}</text>
										</label>
									</checkbox-group>
								</view>
							</view>
						</view>

						<!-- 额外的物流信息 -->
						<view v-for="(item, index) in fahuodatas.slice(1)" :key="index+1" class="express-item">
							<view class="express-item-header">
								<text class="express-item-title">物流信息 {{index + 2}}</text>
								<view class="express-item-delete" @tap.stop="deletefahuo(index + 1)">删除</view>
							</view>
							<view class="express-item-body">
								<view class="form-item">
									<text class="form-label">快递公司：</text>
									<picker @change="expresschange_fahuo($event,index+1)" 
										:value="item.express_index" 
										:range="expressdata" 
										class="form-picker">
										<view class="picker">{{expressdata[item.express_index]}}</view>
									</picker>
								</view>
								<view class="form-item">
									<text class="form-label">快递单号：</text>
									<input type="text" 
										placeholder="请输入快递单号" 
										@input="setexpressno_fahuo($event,index+1)" 
										class="form-input"
										:value="item.express_no"/>
								</view>
								<view class="form-item" v-if="prolist.length>=2">
									<text class="form-label">选择商品：</text>
									<checkbox-group @change="checkboxChange_fahuo($event,index+1)" class="checkbox-group">
										<label v-for="(pi, px) in prolist" :key="px" class="checkbox-item">
											<checkbox :value="pi.id.toString()" />
											<text class="checkbox-label">{{ pi.name }}</text>
										</label>
									</checkbox-group>
								</view>
							</view>
						</view>
					</scroll-view>
					<view class="express-dialog-footer">
						<view class="dialog-btn cancel" @tap="dialogExpressClose">取消</view>
						<view class="dialog-btn normal" @tap="addfahuo">新增发货</view>
						<view class="dialog-btn confirm" @tap="confirmfahuo_news">确定</view>
					</view>
				</view>
			</uni-popup>


			<uni-popup id="dialogExpress_edit" ref="dialogExpress_edit" type="dialog">
				<view class="express-dialog">
					<view class="express-dialog-header">
						<text class="express-dialog-title">修改物流</text>
					</view>
					<scroll-view scroll-y class="express-dialog-content">
						<view v-for="(item, index) in fahuodatas" :key="index" class="express-item">
							<view class="express-item-header">
								<text class="express-item-title">物流信息 {{index + 1}}</text>
								<view class="express-item-delete" @tap.stop="deletefahuo(index)" v-if="fahuodatas.length > 1">删除</view>
							</view>
							<view class="express-item-body">
								<view class="form-item">
									<text class="form-label">快递公司：</text>
									<picker @change="expresschange_fahuo($event,index)" 
										:value="item.express_index" 
										:range="expressdata" 
										class="form-picker">
										<view class="picker">{{expressdata[item.express_index]}}</view>
									</picker>
								</view>
								<view class="form-item">
									<text class="form-label">快递单号：</text>
									<input type="text" 
										placeholder="请输入快递单号" 
										:value="item.express_no"
										@input="setexpressno_fahuo($event,index)" 
										class="form-input"/>
								</view>
								<view class="form-item" v-if="prolist.length>=2">
									<text class="form-label">选择商品：</text>
									<checkbox-group @change="checkboxChange_fahuo($event,index)" class="checkbox-group">
										<label v-for="(pi, px) in prolist" :key="px" class="checkbox-item">
											<checkbox :value="pi.id.toString()" :checked="item.express_ogids.includes(pi.id.toString())" />
											<text class="checkbox-label">{{ pi.name }}</text>
										</label>
									</checkbox-group>
								</view>
							</view>
						</view>
					</scroll-view>
					<view class="express-dialog-footer">
						<view class="dialog-btn cancel" @tap="dialogExpressClose">取消</view>
						<view class="dialog-btn normal" @tap="addfahuo">新增发货</view>
						<view class="dialog-btn confirm" @tap="confirmfahuo_news_edit">确定</view>
					</view>
				</view>
			</uni-popup>



			<uni-popup id="dialogPeisong" ref="dialogPeisong" type="dialog">
				<view class="uni-popup-dialog">
					<view class="uni-dialog-title">
						<text class="uni-dialog-title-text">请选择配送员</text>
					</view>
					<view class="uni-dialog-content">
						<view>
							<picker @change="peisongChange" :value="index2" :range="peisonguser2" style="font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1">
								<view class="picker">{{peisonguser2[index2]}}</view>
							</picker>
						</view>
					</view>
					<view class="uni-dialog-button-group">
						<view class="uni-dialog-button" @tap="dialogPeisongClose">
							<text class="uni-dialog-button-text">取消</text>
						</view>
						<view class="uni-dialog-button uni-border-left" @tap="confirmPeisong">
							<text class="uni-dialog-button-text uni-button-color">确定</text>
						</view>
					</view>
				</view>
			</uni-popup>
			
			<uni-popup id="dialogExpress10" ref="dialogExpress10" type="dialog">
				<view class="uni-popup-dialog">
					<view class="uni-dialog-title">
						<text class="uni-dialog-title-text">发货信息</text>
					</view>
					<view class="uni-dialog-content">
						<view>
							<view class="form-item flex" style="border-bottom:0;">
								<view class="f1" style="margin-right:20rpx">物流单照片</view>
								<view class="f2">
									<view class="layui-imgbox" v-if="express_pic">
										<view class="layui-imgbox-close" @tap="removeimg" :data-index="0" data-field="express_pic"><image style="display:block" src="/static/img/ico-del.png"></image></view>
										<view class="layui-imgbox-img"><image :src="express_pic" @tap="previewImage" :data-url="express_pic" mode="widthFix"></image></view>
									</view>
									<view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="express_pic" data-pernum="1" v-else></view>
								</view>
								<input type="text" hidden="true" name="express_pic" :value="express_pic" maxlength="-1"/>
							</view>
							<view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;">
								<view style="font-size:28rpx;color:#555">发货人：</view>
								<input type="text" placeholder="请输入发货人信息" @input="setexpress_fhname" style="border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;"/>
							</view>
							<view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;">
								<view style="font-size:28rpx;color:#555">发货地址：</view>
								<input type="text" placeholder="请输入发货地址" @input="setexpress_fhaddress" style="border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;"/>
							</view>
							<view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;">
								<view style="font-size:28rpx;color:#555">收货人：</view>
								<input type="text" placeholder="请输入发货人信息" @input="setexpress_shname" style="border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;"/>
							</view>
							<view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;">
								<view style="font-size:28rpx;color:#555">收货地址：</view>
								<input type="text" placeholder="请输入发货地址" @input="setexpress_shaddress" style="border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;"/>
							</view>
							<view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;">
								<view style="font-size:28rpx;color:#555">备注：</view>
								<input type="text" placeholder="请输入备注" @input="setexpress_remark" style="border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;"/>
							</view>
						</view>
					</view>
					<view class="uni-dialog-button-group">
						<view class="uni-dialog-button" @tap="dialogExpress10Close">
							<text class="uni-dialog-button-text">取消</text>
						</view>
						<view class="uni-dialog-button uni-border-left" @tap="confirmfahuo10">
							<text class="uni-dialog-button-text uni-button-color">确定</text>
						</view>
					</view>
				</view>
			</uni-popup>
			
			<uni-popup id="dialogSelectExpress" ref="dialogSelectExpress" type="dialog">
				<view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx" v-if="express_content">
					<view class="sendexpress" v-for="(item, index) in express_content" :key="index" style="border-bottom: 1px solid #f5f5f5;padding:20rpx 0;">
						<view class="sendexpress-item" @tap="goto" :data-url="'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no" style="display: flex;">
							<view class="flex1" style="color:#121212">{{item.express_com}} - {{item.express_no}}</view>
							<image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx"/>
						</view>
						<view v-if="item.express_oglist" style="margin-top:20rpx">
							<view class="oginfo-item" v-for="(item2, index2) in item.express_oglist" :key="index2" style="display: flex;align-items:center;margin-bottom:10rpx">
								<image :src="item2.pic" style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0"/>
								<view class="flex1" style="color:#555">{{item2.name}}({{item2.ggname}})</view>
							</view>
						</view>
					</view>
				</view>
			</uni-popup>
			
			
			<uni-popup id="tuikuandis" ref="tuikuandis" type="dialog">
				<view class="refund-dialog">
					<view class="uni-dialog-title">
						<text class="uni-dialog-title-text">退款</text>
					</view>
					
					<view class="refund-content">
						<view class="refund-goods-list" :key="dddd">
							<h5 class="section-title">选择退款商品</h5>
							<view v-for="(item, idx) in prolist" :key="idx" class="refund-goods-item">
								<!-- 商品信息行 -->
								<view class="refund-goods-row">
									<checkbox-group class="checkbox-group" @change="toggleRefundItem(idx)">
										<checkbox :value="item.id.toString()" :checked="refund.refundNum[idx].selected" />
									</checkbox-group>
									
									<view class="refund-goods-info">
										<image :src="item.pic" class="refund-goods-img" mode="aspectFill"/>
										<view class="refund-goods-detail">
											<view class="refund-goods-name">{{item.name}}</view>
											<view class="refund-goods-spec">{{item.ggname}}</view>
											<view class="refund-goods-price">￥{{item.sell_price}}</view>
										</view>
									</view>
								</view>

								<!-- 数量输入行 -->
								<view class="refund-goods-num" v-if="refund.refundNum[idx].selected">
									<text class="num-title">退款数量:</text>
									<view class="num-control">
										<input 
											type="number" 
											class="num-input"
											:value="refund.refundNum[idx].num"
											@input="setrefundNum($event,idx)"
											:placeholder="'最多' + item.num + '件'"
										/>
										<text class="num-total">共{{item.num}}件</text>
									</view>
								</view>
							</view>
						</view>

						<!-- 退款说明 -->
						<view class="refund-form">
							<h5 class="section-title">退款说明</h5>
							<view class="form-item">
								<text class="form-label">退款原因:</text>
								<input 
									type="text"
									class="form-input"
									placeholder="请输入退款原因"
									:value="refund.reason"
									@input="setrefundreason"
								/>
							</view>
							
							<view class="form-item">
								<text class="form-label">退款金额:</text>
								<input
									type="digit" 
									class="form-input"
									placeholder="请输入退款金额"
									:value="refund.money"
									@input="setrefundmoney"
								/>
							</view>
						</view>
					</view>

					<!-- 底部按钮 -->
					<view class="dialog-footer">
						<button class="btn cancel" @tap="closeTuikuan">取消</button>
						<button class="btn confirm" @tap="getRefundSubmit">确定</button>
					</view>
				</view>
			</uni-popup>	
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
	</template>
	
	<script>
	var app = getApp();
	var interval = null;
	
	export default {
	  data() {
	    return {
	      opt: {},
	      loading: false,
	      isload: false,
	      menuindex: -1,
		  dddd:'1111',
		  express_content:'',
	      pre_url: app.globalData.pre_url,
	      expressdata: [],
	      express_index: 0,
	      express_no: '',
		  fahuodatas:[{express_index:0,express_no:'',express_ogids:[]}],
		  refund:{reason:'',money:'',refundNum:[]},
	      prodata: '',
	      djs: '',
	      detail: "",
	      prolist: "",
	      shopset: "",
	      storeinfo: "",
	      lefttime: "",
	      codtxt: "",
	      peisonguser: [],
	      peisonguser2: [],
	      index2: 0,
	      express_pic: '',
	      express_fhname: '',
	      express_fhaddress: '',
	      express_shname: '',
	      express_shaddress: '',
	      express_remark: '',
	      // 新增的复制字段列表
	      copyFields: [
	        { label: '收货信息', value: 'address', checked: false },
	        { label: '商品信息', value: 'product', checked: false },
	        { label: '商城订单号', value: 'shopordernum', checked: false },
	        // 添加订单ID选项
	        { label: '订单ID', value: 'orderid', checked: false }, 
	        { label: '快递单号', value: 'express', checked: false },
	        { label: '订单状态', value: 'status', checked: false },
	        { label: '价格信息', value: 'price', checked: false },
	        { label: '运费', value: 'freight', checked: false },
	        { label: '实付款', value: 'totalprice', checked: false },
	        { label: '下单人', value: 'buyer', checked: false },
	      ],
	    };
	  },
	  onLoad: function (opt) {
	    this.opt = app.getopts(opt);
	    this.getdata();
	  },
	  onPullDownRefresh: function () {
	    this.getdata();
	  },
	  onUnload: function () {
	    clearInterval(interval);
	  },
	  methods: {
	    getdata: function () {
	      var that = this;
	      that.loading = true;
	      app.get('ApiAdminOrder/shoporderdetail', { id: that.opt.id }, function (res) {
	        that.loading = false;
	        that.expressdata = res.expressdata;
	        that.detail = res.detail;
	        that.prolist = res.prolist;
	        that.shopset = res.shopset;
	        that.storeinfo = res.storeinfo;
	        that.lefttime = res.lefttime;
	        that.codtxt = res.codtxt;
			//发货处理  fahuodatas:[{express_index:0,express_no:'',express_ogids:[]}],
			that.fahuodatas=[];
			if(res.detail.express_content){
				var fahuodatas=JSON.parse(res.detail.express_content);
				fahuodatas.forEach(dds=>{
					 that.fahuodatas.push({express_index:res.expressdata.indexOf(dds.express_com),express_no:dds.express_no,express_ogids:dds.express_ogids})
				})
			}
			 that.refund.refundNum=[]
			 that.prolist.forEach(p=>{
				 that.refund.refundNum.push({
					  ogid:p.id,
					  num:p.num,
					  selected: false
				 })
			 })
			console.log(that.refund,985654)
	        if (res.lefttime > 0) {
	          interval = setInterval(function () {
	            that.lefttime = that.lefttime - 1;
	            that.getdjs();
	          }, 1000);
	        }
	        that.loaded();
	      });
	    },
	    getdjs: function () {
	      var that = this;
	      var totalsec = that.lefttime;
	
	      if (totalsec <= 0) {
	        that.djs = '00时00分00秒';
	      } else {
	        var houer = Math.floor(totalsec / 3600);
	        var min = Math.floor((totalsec - houer * 3600) / 60);
	        var sec = totalsec - houer * 3600 - min * 60;
	        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';
	        that.djs = djs;
	      }
	    },
	    setremark: function () {
	      this.$refs.dialogSetremark.open();
	    },
	    setremarkconfirm: function (done, remark) {
	      this.$refs.dialogSetremark.close();
	      var that = this
	      app.post('ApiAdminOrder/setremark', { type: 'shop', orderid: that.detail.id, content: remark }, function (res) {
	        app.success(res.msg);
	        setTimeout(function () {
	          that.getdata();
	        }, 1000)
	      })
	    },tuikuan:function(){
			// 设置默认退款金额为订单实付款金额，并确保是合法的数字格式
			this.refund.money = parseFloat(this.detail.totalprice || 0).toFixed(2);
			// 默认选中所有商品
			this.refund.refundNum.forEach((item, index) => {
				this.$set(this.refund.refundNum[index], 'selected', true);
			});
			this.$refs.tuikuandis.open();
		},getRefundSubmit:function(){
			this.$refs.dialogSetremark.close();
			var that = this
			app.post('ApiAdminOrder/setrefund', 
				{ 
					type: 'shop', 
					orderid: that.detail.id, 
					 money: that.refund.money,
					 reason:that.refund.reason,
					 refundNum:that.refund.refundNum
				}, function (res) {
					  app.success(res.msg);
					  // 关闭退款弹窗
					  that.$refs.tuikuandis.close();
					  setTimeout(function () {
						// 刷新订单数据
						that.getdata();
					  }, 1000)
			})	
		},setrefundreason:function(e){
			 this.refund.reason = e.detail.value;
		},setrefundmoney:function(e){
			 this.refund.money = e.detail.value;
		},setrefundNum:function(e,idx){
			let num = e.detail.value;
			if(num > this.refund.refundNum[idx].num){
				app.error("退款数量不能超过购买数量");
				return;
			}
			
			// 更新退款数量
			this.$set(this.refund.refundNum[idx], 'num', num);
			
			// 计算选中商品的总金额（包含框价格）
			let totalRefund = 0;
			this.refund.refundNum.forEach((item, index) => {
				if(item.selected) {
					const product = this.prolist[index];
					// 商品金额
					const productPrice = product.sell_price * item.num;
					// 框价格（如果有）
					const framePrice = product.frame_price ? (product.frame_price * item.num) : 0;
					totalRefund += productPrice + framePrice;
				}
			});
			
			// 更新退款金额，保留2位小数
			this.refund.money = parseFloat(totalRefund).toFixed(2);
			
			// 更新视图
			this.dddd = Math.random().toString(36).substring(7);
		},
	    fahuo: function () {
	      if (this.detail.freight_type == 10) {
	        this.$refs.dialogExpress10.open();
	      } else {
	        // 初始化第一个发货数据时使用原始的express_no和express_index
	        this.fahuodatas = [{
	          express_index: this.express_index,
	          express_no: this.express_no,  // 使用原始的express_no
	          express_ogids: []
	        }];
	        this.$refs.dialogExpress.open();
	      }
	    },fahuoedit: function () {
	      // 初始化发货数据
	      this.fahuodatas = [];
	      
	      // 解析现有的物流信息
	      if(this.detail.express_content) {
	        var express_content = JSON.parse(this.detail.express_content);
	        express_content.forEach(item => {
	          this.fahuodatas.push({
	            express_index: this.expressdata.indexOf(item.express_com),
	            express_no: item.express_no,
	            express_ogids: item.express_ogids || []
	          });
	        });
	      } else {
	        // 如果没有express_content，说明是单个物流
	        this.fahuodatas = [{
	          express_index: this.expressdata.indexOf(this.detail.express_com),
	          express_no: this.detail.express_no,
	          express_ogids: this.prolist.length == 1 ? [this.prolist[0].id.toString()] : []
	        }];
	      }
	      
	      this.$refs.dialogExpress_edit.open();
	    },
	    dialogExpressClose: function () {
	      this.$refs.dialogExpress.close();
	      this.$refs.dialogExpress_edit.close();
	    },
	    dialogExpress10Close: function () {
	      this.$refs.dialogExpress10.close();
	    },
	    expresschange: function (e) {
	      this.express_index = e.detail.value;
	    },
		expresschange_fahuo:function(e,index){
			this.fahuodatas[index].express_index = e.detail.value;
	    },
	    setexpressno: function (e) {
	      this.express_no = e.detail.value;
	    },
		setexpressno_fahuo:function(e,index){
			this.fahuodatas[index].express_no = e.detail.value;
		},checkboxChange_fahuo:function(e,index){
			this.fahuodatas[index].express_ogids = e.detail.value;
		},
		addfahuo:function(){
			// 检查现有物流框是否都已填写
			for(let item of this.fahuodatas) {
				if(!item.express_no.trim()) {
					app.error('请先填写已有物流单号');
					return;
				}
			}
			this.fahuodatas.push({express_index:0,express_no:'',express_ogids:[]});
		},
		deletefahuo: function(index) {
			if(this.fahuodatas.length <= 1) {
				app.error('至少需要保留一个物流信息');
				return;
			}
			if(index === 0) {
				// 如果删除第一个，需要将第二个的信息设置为主要物流信息
				this.express_index = this.fahuodatas[1].express_index;
				this.express_no = this.fahuodatas[1].express_no;
				this.fahuodatas.splice(1, 1);
			} else {
				this.fahuodatas.splice(index, 1);
			}
		},
		logistics:function(orderinfo){
			var express_com = orderinfo.express_com
			var express_no = orderinfo.express_no
			var express_content = orderinfo.express_content
			var express_type = orderinfo.express_type
			var prolist = this.prolist
			if(!express_content){
				app.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);
			}else{
				express_content = JSON.parse(express_content);
				for(var i in express_content){
					if(express_content[i].express_ogids){
						//var express_ogids = (express_content[i].express_ogids).split(',');
						var express_ogids = (express_content[i].express_ogids);
						console.log(express_ogids);
						var express_oglist = [];
						for(var j in prolist){
							if(app.inArray(prolist[j].id+'',express_ogids)){
								express_oglist.push(prolist[j]);
							}
						}
						express_content[i].express_oglist = express_oglist;
					}
				}
				this.express_content = express_content;
				console.log(express_content);
				this.$refs.dialogSelectExpress.open();
			}
	    },
	    confirmfahuo: function () {
	      this.$refs.dialogExpress.close();
	      var that = this
	      var express_com = this.expressdata[this.express_index]
	      app.post('ApiAdminOrder/sendExpress', { type: 'shop', orderid: that.detail.id, express_no: that.express_no, express_com: express_com }, function (res) {
	        if (res.status == 0) {
	          app.error(res.msg);
	          return;
	        }
	        app.success(res.msg);
	        setTimeout(function () {
	          that.getdata();
	        }, 1000)
	      })
	    },
	    confirmfahuo_news: function() {
	      // 如果只有一个商品，直接使用express_no
	      if(this.prolist.length == 1) {
	        if(!this.express_no.trim()) {
	          app.error('请填写快递单号');
	          return;
	        }
	        this.$refs.dialogExpress.close();
	        var that = this;
	        app.post('ApiAdminOrder/sendExpress', {
	          type: 'shop',
	          orderid: that.detail.id,
	          express_no: that.express_no,
	          express_com: that.expressdata[that.express_index]
	        }, function(res) {
	          if(res.status == 0) {
	            app.error(res.msg);
	            return;
	          }
	          app.success(res.msg);
	          setTimeout(function() {
	            that.getdata();
	          }, 1000);
	        });
	        return;
	      }
	      
	      // 多个商品的处理逻辑
	      for(let item of this.fahuodatas) {
	        if(!item.express_no.trim()) {
	          app.error('请填写所有物流单号');
	          return;
	        }
	      }
	      
	      this.$refs.dialogExpress.close();
	      var that = this;
	      var express_no = [];
	      var express_com = [];
	      var express_ogids = [];
	      
	      this.fahuodatas.forEach(res => {
	        express_no.push(res.express_no.trim());
	        express_com.push(this.expressdata[res.express_index]);
	        express_ogids.push(res.express_ogids);
	      });
	      
	      app.post('ApiAdminOrder/sendExpress', {
	        type: 'shop',
	        orderid: that.detail.id,
	        express_no: express_no,
	        express_com: express_com,
	        express_ogids: express_ogids
	      }, function(res) {
	        if(res.status == 0) {
	          app.error(res.msg);
	          return;
	        }
	        app.success(res.msg);
	        setTimeout(function() {
	          that.getdata();
	        }, 1000);
	      });
	    },
	    confirmfahuo_news_edit: function() {
	      this.$refs.dialogExpress_edit.close();
	      var that = this;
	      
	      // 如果只有一个商品且没有express_content，使用简单模式
	      if(this.prolist.length == 1 && !this.detail.express_content) {
	        app.post('ApiAdminOrder/sendExpressEdit', {
	          type: 'shop',
	          orderid: that.detail.id,
	          express_no: that.fahuodatas[0].express_no,
	          express_com: that.expressdata[that.fahuodatas[0].express_index]
	        }, function(res) {
	          if(res.status == 0) {
	            app.error(res.msg);
	            return;
	          }
	          app.success(res.msg);
	          setTimeout(function() {
	            that.getdata();
	          }, 1000);
	        });
	        return;
	      }
	
	      // 多物流模式
	      var express_no = [];
	      var express_com = [];
	      var express_ogids = [];
	      
	      this.fahuodatas.forEach(res => {
	        express_no.push(res.express_no);
	        express_com.push(this.expressdata[res.express_index]);
	        express_ogids.push(res.express_ogids);
	      });
	
	      app.post('ApiAdminOrder/sendExpressEdit', {
	        type: 'shop',
	        orderid: that.detail.id,
	        express_no: express_no,
	        express_com: express_com,
	        express_ogids: express_ogids
	      }, function(res) {
	        if(res.status == 0) {
	          app.error(res.msg);
	          return;
	        }
	        app.success(res.msg);
	        setTimeout(function() {
	          that.getdata();
	        }, 1000);
	      });
	    },
	    setexpress_pic: function (e) {
	      this.express_pic = e.detail.value;
	    },
	    setexpress_fhname: function (e) {
	      this.express_fhname = e.detail.value;
	    },
	    setexpress_fhaddress: function (e) {
	      this.express_fhaddress = e.detail.value;
	    },
	    setexpress_shname: function (e) {
	      this.express_shname = e.detail.value;
	    },
	    setexpress_shaddress: function (e) {
	      this.express_shaddress = e.detail.value;
	    },
	    setexpress_remark: function (e) {
	      this.express_remark = e.detail.value;
	    },
	    confirmfahuo10: function () {
	      this.$refs.dialogExpress10.close();
	      var that = this
	      var express_com = this.expressdata[this.express_index]
	      app.post('ApiAdminOrder/sendExpress', { type: 'shop', orderid: that.detail.id, pic: that.express_pic, fhname: that.express_fhname, fhaddress: that.express_fhaddress, shname: that.express_shname, shaddress: that.express_shaddress, remark: that.express_remark }, function (res) {
	        app.success(res.msg);
	        setTimeout(function () {
	          that.getdata();
	        }, 1000)
	      })
	    },
	    ispay: function (e) {
	      var that = this;
	      var orderid = e.currentTarget.dataset.id
	      app.confirm('确定要改为已支付吗?', function () {
	        app.showLoading('提交中');
	        app.post('ApiAdminOrder/ispay', { type: 'shop', orderid: orderid }, function (data) {
	          app.showLoading(false);
	          app.success(data.msg);
	          setTimeout(function () {
	            that.getdata();
	          }, 1000)
	        })
	      });
	    },
	    hexiao: function (e) {
	      var that = this;
	      var orderid = e.currentTarget.dataset.id
	      app.confirm('确定要核销并改为已完成状态吗?', function () {
	        app.showLoading('提交中');
	        app.post('ApiAdminOrder/hexiao', { type: 'shop', orderid: orderid }, function (data) {
	          app.showLoading(false);
	          app.success(data.msg);
	          setTimeout(function () {
	            that.getdata();
	          }, 1000)
	        })
	      });
	    },
	    peisong: function () {
	      var that = this;
	      that.loading = true;
	      app.post('ApiAdminOrder/getpeisonguser', { type: 'shop_order', orderid: that.detail.id }, function (res) {
	        that.loading = false;
	        var peisonguser = res.peisonguser
	        var paidantype = res.paidantype
	        var psfee = res.psfee
	        var ticheng = res.ticheng
	
	        var peisonguser2 = [];
	        for (var i in peisonguser) {
	          peisonguser2.push(peisonguser[i].title);
	        }
	        that.peisonguser = res.peisonguser;
	        that.peisonguser2 = peisonguser2;
	        if (paidantype == 1) {
	          that.$refs.dialogPeisong.open();
	        } else {
	            var tips = '选择配送员配送，订单将发布到抢单大厅由配送员抢单，需扣除配送费￥' + psfee + '，确定要配送员配送吗？';
	            if (paidantype == 2) {
	                var psid = '-1';
	            } else {
	                var psid = '0';
	            }
	            app.confirm(tips, function () {
	                app.post('ApiAdminOrder/peisong', { type: 'shop_order', orderid: that.detail.id, psid: psid }, function (res) {
	                    app.success(res.msg);
	                    setTimeout(function () {
	                        that.getdata();
	                    }, 1000)
	                })
	            })
	        }
	      })
	    },
	    dialogPeisongClose: function () {
	      this.$refs.dialogPeisong.close();
	    },
	    peisongChange: function (e) {
	      this.index2 = e.detail.value;
	    },
	    confirmPeisong: function () {
	      var that = this
	      var psid = this.peisonguser[this.index2].id
	      app.post('ApiAdminOrder/peisong', { type: 'shop_order', orderid: that.detail.id, psid: psid }, function (res) {
	        app.success(res.msg);
	        that.$refs.dialogPeisong.close();
	        setTimeout(function () {
	          that.getdata();
	        }, 1000)
	      })
	    },
	    peisongWx: function () {
	      var that = this;
	      var psfee = that.detail.freight_price;
	      if (that.detail.bid == 0) {
	        var tips = '选择即时配送，订单将派单到第三方配送平台，并扣除相应费用，确定要派单吗？';
	      } else {
	        var tips = '选择即时配送，订单将派单到第三方配送平台，需扣除配送费￥' + psfee + '，确定要派单吗？';
	      }
	      app.confirm(tips, function () {
	        that.loading = true;
	        app.post('ApiAdminOrder/peisongWx', { type: 'shop_order', orderid: that.detail.id }, function (res) {
	          that.loading = false;
	          app.success(res.msg);
	          setTimeout(function () {
	            that.getdata();
	          }, 1000)
	        })
	      })
	    },
	    uploadimg: function (e) {
	      var that = this;
	      var pernum = parseInt(e.currentTarget.dataset.pernum);
	      if (!pernum) pernum = 1;
	      var field = e.currentTarget.dataset.field
	      var pics = that[field]
	      if (!pics) pics = [];
	      app.chooseImage(function (urls) {
	        for (var i = 0; i < urls.length; i++) {
	          pics.push(urls[i]);
	        }
	        if (field == 'express_pic') that.express_pic = pics[0];
	      }, pernum);
	    },
	    removeimg: function (e) {
	      var that = this;
	      var index = e.currentTarget.dataset.index
	      var field = e.currentTarget.dataset.field
	      if (field == 'express_pic') {
	        that.express_pic = '';
	      }
	    },
	    // 新增的复制订单信息相关方法
	    openCopyDialog() {
	      this.$refs.copyDialog.open();
	    },
	    closeCopyDialog() {
	      this.$refs.copyDialog.close();
	    },
	    onFieldChange(e) {
	      const selectedValues = e.detail.value; // 获取选中的值
	      this.copyFields.forEach(field => {
	        field.checked = selectedValues.includes(field.value); // 更新状态
	      });
	    },
	    confirmCopy() {
	      const selectedFields = this.copyFields.filter(f => f.checked).map(f => f.value);
	      let copyText = '';
	      const detail = this.detail;
	      const prolist = this.prolist;
	
	      selectedFields.forEach(field => {
	        switch (field) {
	          case 'address':
	            copyText += '收货信息：' + detail.linkman + ' ' + detail.tel + ' ' + detail.area + detail.address + '\n';
	            break;
	          case 'product':
	            copyText += '商品信息：\n';
	            prolist.forEach(item => {
	              copyText += '名称：' + item.name + '\n';
	              copyText += '规格：' + item.ggname + '\n';
	              copyText += '数量：' + item.num + '\n';
	            });
	            break;
	          case 'shopordernum':
	            copyText += '商城订单号：' + detail.ordernum + '\n';
	            break;
	          case 'express':
	            if (detail.express_content) {
	              const expressInfos = JSON.parse(detail.express_content);
	              copyText += '快递单号：\n';
	              expressInfos.forEach((info, index) => {
	                if (info.express_ogids && info.express_ogids.length > 0) {
	                  // 查找对应的商品信息
	                  const products = prolist.filter(p => info.express_ogids.includes(p.id.toString()));
	                  const productNames = products.map(p => p.name).join('、');
	                  copyText += `${productNames}：\n${info.express_com} - ${info.express_no}\n`;
	                } else {
	                  copyText += `${info.express_com} - ${info.express_no}\n`;
	                }
	              });
	            } else if (detail.express_no) {
	              copyText += `快递单号：${detail.express_com} - ${detail.express_no}\n`;
	            } else {
	              copyText += '快递单号：暂无\n';
	            }
	            break;
	          case 'status':
	            let statusText = '';
	            if (detail.status == 0) statusText = '未付款';
	            else if (detail.status == 1) statusText = '已付款';
	            else if (detail.status == 2) statusText = '已发货';
	            else if (detail.status == 3) statusText = '已收货';
	            else if (detail.status == 4) statusText = '已关闭';
	            copyText += '订单状态：' + statusText + '\n';
	            break;
	          case 'price':
	            copyText += '价格信息：\n';
	            prolist.forEach(item => {
	              copyText += item.name + '：' + item.sell_price + '×' + item.num + '=' + (item.sell_price * item.num).toFixed(2) + '\n';
	            });
	            break;
	          case 'freight':
	            copyText += '运费：¥' + detail.freight_price + '\n';
	            break;
	          case 'totalprice':
	            copyText += '实付款：¥' + detail.totalprice + ' (=' + detail.product_price + '+' + detail.freight_price + ')\n';
	            break;
	          case 'buyer':
	            copyText += '下单人：' + detail.nickname + '\n';
	            break;
	          // 添加订单ID的处理
	          case 'orderid':
	            copyText += '订单ID：' + detail.id + '\n';
	            break;
	        }
	      });
	
	      // 复制到剪贴板
	      uni.setClipboardData({
	        data: copyText,
	        success: () => {
	          this.closeCopyDialog();
	          app.success('复制成功');
	        }
	      });
	    },
	    closeTuikuan() {
	      this.$refs.tuikuandis.close();
	    },
	    tuikuan() {
	      this.$refs.tuikuandis.open();
	    },
	    // 切换商品选择状态
	    toggleRefundItem(idx) {
	      // 切换选中状态
	      this.$set(this.refund.refundNum[idx], 'selected', !this.refund.refundNum[idx].selected);
	      // 设置默认退款数量为商品购买数量
	      if(this.refund.refundNum[idx].selected) {
	          this.$set(this.refund.refundNum[idx], 'num', this.prolist[idx].num);
	      } else {
	          this.$set(this.refund.refundNum[idx], 'num', 0);
	      }
	      
	      // 计算选中商品的总金额（包含框价格）
	      let totalRefund = 0;
	      this.refund.refundNum.forEach((item, index) => {
	          if(item.selected) {
	              const product = this.prolist[index];
	              // 商品金额
	              const productPrice = product.sell_price * item.num;
	              // 框价格（如果有）
	              const framePrice = product.frame_price ? (product.frame_price * item.num) : 0;
	              totalRefund += productPrice + framePrice;
	          }
	      });
	      
	      // 更新退款金额，保留2位小数
	      this.refund.money = parseFloat(totalRefund).toFixed(2);
	    },
	    closeOrder: function(e) {
	        var that = this;
	        var orderid = e.currentTarget.dataset.id;
	        app.confirm('确定要关闭订单吗?', function() {
	            app.showLoading('提交中');
	            app.post('ApiAdminOrder/closeOrder', { type: 'shop', orderid: orderid }, function(data) {
	                app.showLoading(false);
	                app.success(data.msg);
	                setTimeout(function() {
	                    that.getdata();
	                }, 1000);
	            });
	        });
	    },
	    delOrder: function(e) {
	        var that = this;
	        var orderid = e.currentTarget.dataset.id;
	        app.confirm('确定要删除此订单吗?', function() {
	            app.showLoading('提交中');
	            app.post('ApiAdminOrder/delOrder', { type: 'shop', orderid: orderid }, function(data) {
	                app.showLoading(false);
	                app.success(data.msg);
	                setTimeout(function() {
	                    // 删除成功后返回上一页
	                    uni.navigateBack();
	                }, 1000);
	            });
	        });
	    }
	  }
	};
	</script>


	<style scoped>
	.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}
	.ordertop .f1{color:#fff}
	.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}
	.ordertop .f1 .t2{font-size:24rpx}
	
	/* 新增样式 */
	.copy-field-item {
	  margin: 10rpx 0;
	  font-size: 28rpx;
	  color: #333;
	}
	.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}
	.address .img{width:40rpx}
	.address image{width:40rpx; height:40rpx;}
	.address .info{flex:1;display:flex;flex-direction:column;}
	.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}
	.address .info .t2{font-size:24rpx;color:#999}
	
	.product{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}
	.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}
	.product .content:last-child{ border-bottom: 0; }
	.product .content image{ width: 140rpx; height: 140rpx;}
	.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}
	.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
	.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}
	.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}
	.product .content .detail .x1{ flex:1}
	.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}
	.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}
	.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}
	
	.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}
	.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}
	.orderinfo .item:last-child{ border-bottom: 0;}
	.orderinfo .item .t1{width:200rpx;}
	.orderinfo .item .t2{flex:1;text-align:right}
	.orderinfo .item .red{color:red}
	
	.bottom{ width: 100%;height:92rpx;padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}
	
	.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}
	.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}
	.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}
	
	.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}
	.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}
	.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}
	
	.picker{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
	
	.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}
	.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}
	.uni-dialog-title-text {font-size: 16px;font-weight: 500;}
	.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}
	.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}
	.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}
	.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}
	.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}
	.uni-dialog-button-text {font-size: 14px;}
	.uni-button-color {color: #007aff;}
	
	.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
	.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:5;color:#999;font-size:32rpx;background:#fff}
	.layui-imgbox-close image{width:100%;height:100%}
	.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
	.layui-imgbox-img>image{max-width:100%;}
	.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
	.uploadbtn{position:relative;height:200rpx;width:200rpx}
	
	/* 复制订单弹窗样式 */
	.copy-dialog {
	    width: 600rpx;
	    background: #FFFFFF;
	    border-radius: 20rpx;
	    overflow: hidden;
	    display: flex;
	    flex-direction: column;
	    /* 增加整体弹窗高度 */
	    max-height: 90vh;
	}
	
	.copy-dialog-header {
	    position: relative;
	    padding: 30rpx;
	    text-align: center;
	    border-bottom: 1rpx solid #EEEEEE;
	    flex-shrink: 0;
	}
	
	.copy-dialog-title {
	    font-size: 32rpx;
	    font-weight: 500;
	    color: #333333;
	}
	
	.copy-dialog-close {
	    position: absolute;
	    right: 30rpx;
	    top: 30rpx;
	    font-size: 40rpx;
	    color: #999999;
	    line-height: 1;
	}
	
	.copy-dialog-content {
	    padding: 30rpx;
	    overflow-y: auto;
	    /* 增加内容区域高度到1000rpx */
	    height: 1000rpx;
	}
	
	.copy-field-item {
	    display: flex;
	    align-items: center;
	    /* 增加选项间距 */
	    padding: 25rpx 0;
	    margin: 0;
	}
	
	.copy-field-label {
	    margin-left: 20rpx;
	    /* 增大字体 */
	    font-size: 30rpx;
	    color: #333333;
	}
	
	.copy-dialog-footer {
	    display: flex;
	    border-top: 1rpx solid #EEEEEE;
	    /* 固定底部 */
	    flex-shrink: 0;
	}
	
	.copy-dialog-btn {
	    flex: 1;
	    height: 100rpx; /* 增加按钮高度 */
	    line-height: 100rpx;
	    text-align: center;
	    font-size: 32rpx;
	}
	
	.copy-dialog-btn.cancel {
	    color: #666666;
	    background: #F5F5F5;
	}
	
	.copy-dialog-btn.confirm {
	    color: #FFFFFF;
	    background: #007AFF;
	}
	
	/* 优化复选框样式 */
	checkbox .wx-checkbox-input {
	    width: 36rpx;
	    height: 36rpx;
	    border-radius: 50%;
	}
	
	checkbox .wx-checkbox-input.wx-checkbox-input-checked {
	    background: #007AFF;
	    border-color: #007AFF;
	}
	
	/* 退款弹窗样式 */
	.refund-dialog {
	  background: #fff;
	  border-radius: 12rpx;
	  width: 680rpx;
	  max-height: 80vh;
	  padding: 30rpx;
	}
	
	.section-title {
	  font-size: 28rpx;
	  font-weight: 600;
	  color: #333;
	  margin: 20rpx 0;
	}
	
	.refund-content {
	  max-height: calc(80vh - 180rpx);
	  overflow-y: auto;
	}
	
	.refund-goods-item {
	  padding: 20rpx;
	  background: #f8f8f8;
	  border-radius: 8rpx;
	  margin-bottom: 20rpx;
	}
	
	.refund-goods-row {
	  display: flex;
	  align-items: center;
	}
	
	.checkbox-group {
	  margin-right: 20rpx;
	}
	
	.refund-goods-info {
	  flex: 1;
	  display: flex;
	}
	
	.refund-goods-img {
	  width: 120rpx;
	  height: 120rpx;
	  border-radius: 6rpx;
	  margin-right: 20rpx;
	}
	
	.refund-goods-detail {
	  flex: 1;
	}
	
	.refund-goods-name {
	  font-size: 28rpx;
	  color: #333;
	  margin-bottom: 10rpx;
	}
	
	.refund-goods-spec {
	  font-size: 24rpx;
	  color: #999;
	}
	
	.refund-goods-price {
	  color: #ff4246;
	  font-size: 28rpx;
	  margin-top: 10rpx;
	}
	
	.refund-goods-num {
	  display: flex;
	  align-items: center;
	  margin-top: 20rpx;
	  padding-left: 60rpx;
	}
	
	.num-title {
	  font-size: 26rpx;
	  margin-right: 20rpx;
	}
	
	.num-input {
	  width: 120rpx;
	  height: 60rpx;
	  text-align: center;
	  background: #fff;
	  border: 1px solid #eee;
	  border-radius: 4rpx;
	}
	
	.num-total {
	  font-size: 24rpx;
	  color: #999;
	  margin-left: 20rpx;
	}
	
	.form-item {
	  margin-bottom: 20rpx;
	}
	
	.form-label {
	  display: block;
	  font-size: 26rpx;
	  color: #333;
	  margin-bottom: 10rpx;
	}
	
	.form-input {
	  width: 100%;
	  height: 80rpx;
	  padding: 0 20rpx;
	  background: #f8f8f8;
	  border: 1px solid #eee;
	  border-radius: 6rpx;
	}
	
	.dialog-footer {
	  display: flex;
	  justify-content: space-between;
	  margin-top: 30rpx;
	}
	
	.btn {
	  width: 45%;
	  height: 80rpx;
	  line-height: 80rpx;
	  text-align: center;
	  border-radius: 6rpx;
	  font-size: 28rpx;
	}
	
	.btn.cancel {
	  background: #f5f5f5;
	  color: #666;
	}
	
	.btn.confirm {
	  background: #007AFF;
	  color: #fff;
	}
	
	.refund-goods-name {
	  font-size: 28rpx;
	  color: #333;
	  margin-bottom: 10rpx;
	}
	
	.refund-goods-spec {
	  font-size: 24rpx;
	  color: #999;
	}
	
	.refund-goods-price {
	  color: #ff4246;
	  font-size: 28rpx;
	  margin-top: 10rpx;
	}
	
	.refund-goods-num {
	  display: flex;
	  align-items: center;
	  margin-top: 20rpx;
	  padding-left: 60rpx;
	}
	
	.num-title {
	  font-size: 26rpx;
	  margin-right: 20rpx;
	}
	
	.num-input {
	  width: 120rpx;
	  height: 60rpx;
	  text-align: center;
	  background: #fff;
	  border: 1px solid #eee;
	  border-radius: 4rpx;
	}
	
	.num-total {
	  font-size: 24rpx;
	  color: #999;
	  margin-left: 20rpx;
	}
	
	.form-item {
	  margin-bottom: 20rpx;
	}
	
	.form-label {
	  display: block;
	  font-size: 26rpx;
	  color: #333;
	  margin-bottom: 10rpx;
	}
	
	.form-input {
	  width: 100%;
	  height: 80rpx;
	  padding: 0 20rpx;
	  background: #f8f8f8;
	  border: 1px solid #eee;
	  border-radius: 6rpx;
	}
	
	.dialog-footer {
	  display: flex;
	  justify-content: space-between;
	  margin-top: 30rpx;
	}
	
	.btn {
	  width: 45%;
	  height: 80rpx;
	  line-height: 80rpx;
	  text-align: center;
	  border-radius: 6rpx;
	  font-size: 28rpx;
	}
	
	.btn.cancel {
	  background: #f5f5f5;
	  color: #666;
	}
	
	.btn.confirm {
	  background: #007AFF;
	  color: #fff;
	}
	
	.num-btn {
		width: 60rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		background: #f5f5f5;
		border: 1px solid #eee;
	}

	.num-input {
		width: 100rpx;
		height: 60rpx;
		text-align: center;
		border-top: 1px solid #eee;
		border-bottom: 1px solid #eee;
	}

	.num-total {
		font-size: 24rpx;
		color: #999;
		margin-left: 20rpx;
	}

	/* 添加新样式 */
	.express-item {
		position: relative;
		border: 1px solid #eee;
		border-radius: 8rpx;
		margin: 20rpx;
		padding: 20rpx;
	}

	.express-item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.express-item-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
	}

	.express-item-delete {
		width: 40rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
		font-size: 32rpx;
		color: #999;
		background: #f5f5f5;
		border-radius: 50%;
	}

	/* 新增样式 */
	.express-dialog {
		width: 650rpx;
		background: #fff;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		max-height: 90vh;
	}

	.express-dialog-header {
		padding: 30rpx;
		text-align: center;
		border-bottom: 1rpx solid #eee;
	}

	.express-dialog-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.express-dialog-content {
		flex: 1;
		padding: 20rpx;
		max-height: 60vh;
	}

	.express-dialog-footer {
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		border-top: 1rpx solid #eee;
	}

	.express-item {
		background: #f8f8f8;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
	}

	.express-item-header {
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;
		border-bottom: 1rpx solid #eee;
	}

	.express-item-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}

	.express-item-delete {
		width: 44rpx;
		height: 44rpx;
		line-height: 40rpx;
		text-align: center;
		font-size: 36rpx;
		color: #999;
		background: #f5f5f5;
		border-radius: 50%;
	}

	.express-item-body {
		padding: 20rpx;
	}

	.form-item {
		margin-bottom: 20rpx;
	}

	.form-label {
		display: block;
		font-size: 26rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.form-input {
		width: 100%;
		height: 70rpx;
		background: #fff;
		border: 1rpx solid #eee;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
	}

	.form-picker {
		width: 100%;
		height: 70rpx;
		background: #fff;
		border: 1rpx solid #eee;
		border-radius: 8rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
	}

	.checkbox-group {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		margin-top: 10rpx;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		background: #fff;
		padding: 10rpx 20rpx;
		border-radius: 6rpx;
	}

	.checkbox-label {
		font-size: 26rpx;
		color: #333;
		margin-left: 10rpx;
	}

	.dialog-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 8rpx;
		font-size: 28rpx;
		margin: 0 10rpx;
	}

	.dialog-btn.cancel {
		background: #f5f5f5;
		color: #666;
	}

	.dialog-btn.normal {
		background: #fff;
		color: #666;
		border: 1rpx solid #ddd;
	}

	.dialog-btn.confirm {
		background: #007AFF;
		color: #fff;
	}

	/* 修改删除按钮样式 */
	.express-item-delete {
		padding: 0 24rpx;
		min-width: 100rpx;
		height: 48rpx;
		line-height: 48rpx;
		text-align: center;
		font-size: 24rpx;
		color: #fff;
		background: #ff4444;
		border-radius: 24rpx;
		margin-left: 20rpx;
		flex-shrink: 0;
	}

	.express-item-header {
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;
		border-bottom: 1rpx solid #eee;
	}

	.express-item-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 框价格样式 */
	.frame-price {
		font-size: 24rpx !important;
		color: #666 !important;
		margin-top: 8rpx !important;
		display: flex !important;
		align-items: center !important;
	}

	.frame-label {
		color: #666;
		margin-right: 10rpx;
	}

	.frame-value {
		color: #ff4246;
		margin-right: 10rpx;
	}

	.frame-multiply {
		color: #666;
		margin: 0 10rpx;
	}

	.frame-num {
		color: #666;
	}

	.frame-equal {
		color: #666;
		margin: 0 10rpx;
	}

	.frame-total {
		color: #ff4246;
	}
	</style>
