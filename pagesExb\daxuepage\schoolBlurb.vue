<template>
	<view style="padding: 30rpx;">
		<view v-html="content"></view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				opt: {},
				content:''
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			const that  =this
			app.get('ApiDaxue/index', {
				id: this.opt.id
			}, function(res) {
				that.content = res.daxue.content
			})
		},
		methods: {

		}
	}
</script>

<style>

</style>
