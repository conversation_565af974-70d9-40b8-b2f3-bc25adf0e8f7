<template>
<view class="container">
	<block v-if="isload">
		<view class="title">地理位置</view>
		
		<view class="ip-info">
			<view class="ip-item">
				<text>IP地址：</text>
				<text>{{ipAddress || '未获取'}}</text>
			</view>
			<view class="ip-item">
				<text>地理位置：</text>
				<text>{{fullAddress || '未获取'}}</text>
			</view>
			<view class="ip-item">
				<text>最后更新时间：</text>
				<text>{{ipLastUpdateTime || '未获取'}}</text>
			</view>
			<button class="update-ip-btn" v-if="ipCaptureEnabled" @tap="updateIpAddress" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">更新IP地址</button>
		</view>
		
		<form @submit="formSubmit" @reset="formReset">
		<view class="form">
			<view class="form-item">
				<view class="label">国家</view>
				<input type="text" class="input" placeholder="请输入国家" placeholder-style="color:#BBBBBB;font-size:28rpx" name="country" v-model="addressInfo.country"></input>
			</view>
			<view class="form-item">
				<view class="label">省份</view>
				<input type="text" class="input" placeholder="请输入省份" placeholder-style="color:#BBBBBB;font-size:28rpx" name="province" v-model="addressInfo.province"></input>
			</view>
			<view class="form-item">
				<view class="label">城市</view>
				<input type="text" class="input" placeholder="请输入城市" placeholder-style="color:#BBBBBB;font-size:28rpx" name="city" v-model="addressInfo.city"></input>
			</view>
			<view class="form-item">
				<view class="label">地区</view>
				<input type="text" class="input" placeholder="请输入地区" placeholder-style="color:#BBBBBB;font-size:28rpx" name="area" v-model="addressInfo.area"></input>
			</view>
		</view>
		<view class="tips" v-if="addressEditRemain > 0">剩余修改次数：{{addressEditRemain}}次</view>
		<button class="set-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">保 存</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: true,
			menuindex:-1,
			
			addressInfo: {
				country: '',
				province: '',
				city: '',
				area: ''
			},
			addressEditRemain: 0,
			frompage: '',
			
			// 新增IP相关数据
			ipAddress: '',
			fullAddress: '',
			ipLastUpdateTime: '',
			ipCaptureEnabled: false
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		if(this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);
		this.getAddressInfo();
  },
	
	onPullDownRefresh: function () {
		this.getAddressInfo();
	},
	
  methods: {
		getAddressInfo: function() {
			var that = this;
			that.loading = true;
			app.get('ApiMy/set', {}, function (res) {
				that.loading = false;
				if(res.status == 0){
					app.alert(res.msg);
					return;
				}
				
				// 从用户信息中获取地址信息
				if(res.userinfo) {
					that.addressInfo = {
						country: res.userinfo.country || '',
						province: res.userinfo.province || '',
						city: res.userinfo.city || '',
						area: res.userinfo.area || ''
					};
					
					// 加载IP地址相关信息
					that.ipAddress = res.userinfo.ip_address || '';
					that.fullAddress = res.userinfo.full_address || '';
					that.ipLastUpdateTime = res.userinfo.ip_last_update_time || '';
				}
				
				that.ipCaptureEnabled = res.ip_capture_enabled || false;
				that.addressEditRemain = res.address_edit_remain || 0;
				that.isload = true;
			});
		},
		
		// 更新IP地址
		updateIpAddress: function() {
			var that = this;
			
			// 获取当前IP地址
			that.getUserIpAddress(function(ip) {
				if(!ip) {
					app.alert('获取IP地址失败');
					return;
				}
				
				app.showLoading('更新中');
				app.post('ApiMy/updateIpAddress', {ip_address: ip}, function(res) {
					app.showLoading(false);
					if(res.status == 1) {
						app.success(res.msg || 'IP地址更新成功');
						// 更新显示的IP信息
						that.ipAddress = res.ip_address || that.ipAddress;
						that.fullAddress = res.full_address || that.fullAddress;
						that.ipLastUpdateTime = res.ip_last_update_time || that.ipLastUpdateTime;
					} else {
						app.error(res.msg || 'IP地址更新失败');
					}
				});
			});
		},
		
		// 获取用户IP地址
		getUserIpAddress: function(callback) {
			var that = this;
			// 使用ipify API获取IP地址
			uni.request({
				url: 'https://api.ipify.org?format=json',
				method: 'GET',
				success: function(res) {
					if(res.data && res.data.ip) {
						callback(res.data.ip);
					} else {
						// 第一个API失败，尝试备用方法
						that.getUserIpAddressBackup(callback);
					}
				},
				fail: function(err) {
					// 尝试使用备用方法
					that.getUserIpAddressBackup(callback);
				}
			});
		},
		
		// 备用IP获取方法
		getUserIpAddressBackup: function(callback) {
			var that = this;
			// 备用方法，使用ip-api.com
			uni.request({
				url: 'https://ip-api.com/json',
				method: 'GET',
				success: function(res) {
					if(res.data && res.data.query) {
						callback(res.data.query); // ip-api使用query字段返回IP
					} else {
						// 尝试第三个备用API
						that.getUserIpAddressBackup2(callback);
					}
				},
				fail: function(err) {
					// 尝试第三个备用API
					that.getUserIpAddressBackup2(callback);
				}
			});
		},
		
		// 第二备用IP获取方法
		getUserIpAddressBackup2: function(callback) {
			// 第三备用方法，使用jsonip.com
			uni.request({
				url: 'https://jsonip.com',
				method: 'GET',
				success: function(res) {
					if(res.data && res.data.ip) {
						callback(res.data.ip);
					} else {
						callback(''); // 所有方法都失败
					}
				},
				fail: function(err) {
					callback(''); // 所有方法都失败
				}
			});
		},
		
    formSubmit: function (e) {
      var that = this;
			var formdata = e.detail.value;
			
      if (formdata.country == '') {
        app.alert('请输入国家');
				return;
      }
			
			if (formdata.province == '') {
        app.alert('请输入省份');
				return;
      }
			
			if (formdata.city == '') {
        app.alert('请输入城市');
				return;
      }
			
			if (formdata.area == '') {
        app.alert('请输入地区');
				return;
      }
			
			app.showLoading('提交中');
      app.post("ApiMy/update_address", {
				country: formdata.country,
				province: formdata.province,
				city: formdata.city,
				area: formdata.area
			}, function (res) {
				app.showLoading(false);
        if (res.status == 1) {
          app.success(res.msg || '地址修改成功');
					
					// 更新剩余修改次数
					if (res.data && res.data.address_edit_remain) {
						that.addressEditRemain = res.data.address_edit_remain;
					}
					
          setTimeout(function () {
						if(that.frompage) {
							app.goto(that.frompage, 'redirect');
						} else {
							app.goback(true);
						}
          }, 1000);
        } else {
          app.error(res.msg || '地址修改失败');
        }
      });
    },
  }
};
</script>

<style>
.title{margin:30rpx 50rpx 20rpx 40rpx;height:60rpx;line-height:60rpx;font-size:36rpx;font-weight:bold;color:#000000;}
.ip-info{width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx;background:#F8F8F8;}
.ip-item{font-size:28rpx;color:#666;margin-bottom:10rpx;display:flex;}
.ip-item text:first-child{width:200rpx;color:#8B8B8B;}
.ip-item text:last-child{flex:1;word-break:break-all;}
.update-ip-btn{width:50%;height:70rpx;line-height:70rpx;font-size:28rpx;color:#fff;border-radius:35rpx;margin:20rpx auto 10rpx;}
.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;}
.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;line-height:98rpx;}
.form-item:last-child{border:0}
.form-item .label{color: #000;width:200rpx;}
.form-item .input{flex:1;color: #000;}
.tips{width:94%;margin:20rpx 3%;font-size:24rpx;color:#999;text-align:right;}
.set-btn{width: 90%;margin:40rpx 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
</style> 