{
  "pageTemplates": {
    "list": {
      "structure": {
        "search": {
          "required": true,
          "position": "top",
          "template": "search-navbar",
          "components": ["search-input", "filter-options"]
        },
        "tabs": {
          "style": "order-tab",
          "template": "order-tab2",
          "itemStyle": {
            "width": "20%",
            "height": "80rpx",
            "fontSize": "28rpx",
            "activeColor": "#222222",
            "defaultColor": "#999999"
          }
        },
        "list": {
          "style": "waterfall-wrapper",
          "template": "note-item",
          "itemStructure": {
            "image": {
              "ratio": "1:1",
              "borderRadius": "8rpx"
            },
            "title": {
              "lines": 2,
              "fontSize": "28rpx"
            },
            "description": {
              "lines": 3,
              "fontSize": "26rpx"
            }
          }
        }
      }
    },
    "detail": {
      "structure": {
        "header": {
          "required": true,
          "components": ["back-btn", "share-btn"],
          "style": "nav-header"
        },
        "content": {
          "sections": [
            {
              "type": "info-box",
              "template": "user-info",
              "style": {
                "padding": "24rpx",
                "background": "#fff"
              }
            },
            {
              "type": "comment-box",
              "template": "comment-section",
              "required": true,
              "style": {
                "marginTop": "20rpx",
                "background": "#fff"
              }
            }
          ]
        }
      }
    },
    "form": {
      "structure": {
        "input": {
          "height": "120rpx",
          "lineHeight": "120rpx",
          "fontSize": "60rpx",
          "template": "content2-item3"
        },
        "button": {
          "template": "op-btn",
          "style": {
            "height": "100rpx",
            "background": "#07C160",
            "borderRadius": "10rpx",
            "fontSize": "28rpx"
          }
        }
      }
    }
  },
  "commonComponents": {
    "dialog": {
      "template": "uni-dialog",
      "style": {
        "content": {
          "padding": "30rpx",
          "maxHeight": "1000rpx"
        },
        "button": {
          "height": "100rpx",
          "fontSize": "32rpx"
        }
      }
    },
    "loadMore": {
      "template": "uni-load-more",
      "style": {
        "height": "80rpx"
      }
    }
  },
  "styleGuide": {
    "spacing": {
      "pagePadding": "32rpx",
      "sectionGap": "20rpx",
      "elementGap": "10rpx"
    },
    "colors": {
      "primary": "#07C160",
      "secondary": "#f58d40",
      "warning": "#FC4343",
      "background": "#f5f5f5",
      "border": "#F0F0F0",
      "text": {
        "primary": "#333333",
        "secondary": "#666666",
        "placeholder": "#999999"
      }
    },
    "typography": {
      "title": {
        "size": "32rpx",
        "weight": "bold",
        "color": "@text.primary"
      },
      "content": {
        "size": "28rpx",
        "color": "@text.secondary"
      },
      "description": {
        "size": "24rpx",
        "color": "@text.placeholder"
      }
    }
  },
  "businessComponents": {
    "transfer": {
      "template": "content2",
      "structure": {
        "header": {
          "template": "item1",
          "style": {
            "display": "flex",
            "padding": "0 30rpx",
            "borderBottom": "1px solid #F0F0F0"
          },
          "fields": {
            "title": {
              "fontSize": "32rpx",
              "color": "#333333",
              "height": "120rpx"
            },
            "amount": {
              "fontSize": "44rpx",
              "color": "#FC4343"
            }
          }
        },
        "input": {
          "template": "item3",
          "style": {
            "height": "120rpx",
            "fontSize": "60rpx",
            "color": "#333333"
          }
        }
      }
    },
    "orderList": {
      "template": "order-tab",
      "tabs": ["全部", "待付款", "待发货", "待收货", "已完成"],
      "listItem": {
        "template": "order-item",
        "structure": {
          "header": ["订单号", "下单时间"],
          "content": ["商品信息", "价格", "数量"],
          "footer": ["合计", "操作按钮"]
        }
      }
    }
  },
  "pageLayouts": {
    "activity": {
      "template": "activity-layout",
      "components": {
        "banner": {
          "height": "316rpx",
          "marginTop": "-60rpx"
        },
        "counter": {
          "template": "border",
          "style": {
            "width": "380rpx",
            "height": "64rpx",
            "background": "#fb3a13",
            "borderRadius": "45rpx"
          }
        },
        "scroll": {
          "width": "550rpx",
          "height": "185rpx",
          "marginTop": "75rpx"
        }
      }
    }
  },
  "dataStructure": {
    "userInfo": {
      "required": ["id", "nickname", "avatar"],
      "optional": ["description", "followers", "following"]
    },
    "product": {
      "required": ["id", "title", "price", "images"],
      "optional": ["description", "specs", "stock"]
    },
    "order": {
      "required": ["orderId", "status", "amount", "createTime"],
      "optional": ["payTime", "shipTime", "completeTime"]
    }
  },
  "apiFormat": {
    "response": {
      "success": {
        "type": 1,
        "data": {},
        "msg": "success"
      },
      "error": {
        "type": 0,
        "msg": "error message"
      }
    },
    "pagination": {
      "pagenum": 1,
      "pagesize": 10,
      "total": 0,
      "totalpage": 0
    }
  },
  "globalConfig": {
    "appStructure": {
      "entry": "App.vue",
      "globalStyle": "/static/css/common.css",
      "themeConfig": "/static/css/theme.scss",
      "iconPath": "/static/img/",
      "componentPath": "/components/",
      "utilsPath": "/utils/"
    },
    
    "messageHandler": {
      "socket": {
        "entry": "/utils/socket.js",
        "events": {
          "connect": "onSocketConnect",
          "message": "onSocketMessage",
          "close": "onSocketClose"
        }
      },
      "notification": {
        "entry": "/utils/notification.js",
        "methods": {
          "show": "showNotification",
          "clear": "clearNotification" 
        }
      }
    },

    "tabBar": {
      "entry": "/components/tab-bar/tab-bar.vue",
      "style": {
        "height": "100rpx",
        "background": "#ffffff",
        "borderTop": "1px solid #f5f5f5"
      },
      "items": [
        {
          "pagePath": "pages/index/index",
          "text": "首页",
          "iconPath": "/static/img/home.png",
          "selectedIconPath": "/static/img/home-active.png"
        },
        {
          "pagePath": "shopPackage/shop/index", 
          "text": "商城",
          "iconPath": "/static/img/shop.png",
          "selectedIconPath": "/static/img/shop-active.png"
        }
      ]
    },

    "pullRefresh": {
      "entry": "/mixins/pullRefresh.js",
      "methods": {
        "onPullDownRefresh": "handlePullDownRefresh",
        "onReachBottom": "handleReachBottom"
      },
      "config": {
        "enablePullDownRefresh": true,
        "backgroundTextStyle": "dark",
        "backgroundColor": "#f5f5f5"
      }
    },

    "pageStructure": {
      "basic": {
        "template": "basic-layout",
        "components": [
          "nav-header",
          "page-content",
          "tab-bar"
        ],
        "style": {
          "minHeight": "100vh",
          "background": "#f5f5f5",
          "paddingBottom": "calc(100rpx + env(safe-area-inset-bottom))"
        }
      },
      "scroll": {
        "template": "scroll-layout",
        "extends": "basic",
        "components": [
          "scroll-view",
          "load-more"
        ],
        "methods": {
          "onScroll": "handleScroll",
          "onLoadMore": "handleLoadMore"
        }
      }
    },

    "styleSystem": {
      "variables": "/static/css/variables.scss",
      "mixins": "/static/css/mixins.scss",
      "themes": {
        "default": "/static/css/theme-default.scss",
        "dark": "/static/css/theme-dark.scss"
      },
      "commonClasses": {
        "flex": {
          "display": "flex",
          "alignItems": "center"
        },
        "ellipsis": {
          "overflow": "hidden",
          "textOverflow": "ellipsis",
          "whiteSpace": "nowrap"
        },
        "safe-bottom": {
          "paddingBottom": "env(safe-area-inset-bottom)"
        }
      }
    }
  },
  "apiConfig": {
    "baseConfig": {
      "baseUrl": {
        "dev": "https://dev-api.example.com",
        "prod": "https://api.example.com"
      },
      "timeout": 30000,
      "header": {
        "content-type": "application/json"
      }
    },

    "requestStructure": {
      "entry": "/utils/request.js",
      "methods": {
        "get": "app.get",
        "post": "app.post",
        "upload": "app.upload"
      },
      "interceptors": {
        "request": "handleRequestInterceptor",
        "response": "handleResponseInterceptor",
        "error": "handleErrorInterceptor"
      }
    },

    "commonAPIs": {
      "user": {
        "login": {
          "url": "ApiUser/login",
          "method": "post",
          "params": {
            "required": ["mobile", "password"],
            "optional": ["code"]
          }
        },
        "info": {
          "url": "ApiUser/info",
          "method": "get"
        },
        "update": {
          "url": "ApiUser/update",
          "method": "post",
          "params": {
            "optional": ["nickname", "avatar", "gender"]
          }
        }
      },
      "order": {
        "list": {
          "url": "ApiOrder/list",
          "method": "get",
          "params": {
            "required": ["pagenum", "pagesize"],
            "optional": ["status", "keyword"]
          }
        },
        "detail": {
          "url": "ApiOrder/detail",
          "method": "get",
          "params": {
            "required": ["id"]
          }
        }
      },
      "upload": {
        "image": {
          "url": "ApiUpload/image",
          "method": "upload",
          "params": {
            "required": ["file"],
            "optional": ["type"]
          }
        }
      }
    },

    "errorHandler": {
      "entry": "/utils/errorHandler.js",
      "codes": {
        "401": "handleUnauthorized",
        "403": "handleForbidden",
        "404": "handleNotFound",
        "500": "handleServerError"
      },
      "defaultHandler": "handleDefaultError"
    },

    "mockConfig": {
      "enabled": true,
      "path": "/mock/",
      "timeout": 1000,
      "responseTemplate": {
        "type": 1,
        "data": null,
        "msg": "success"
      }
    },

    "apiCallExample": {
      "basic": {
        "template": "app.post(url, data)",
        "example": `
          app.post('ApiUser/login', {
            mobile: this.mobile,
            password: this.password
          }, function(res) {
            if(res.type == 1) {
              // success handler
            }
          });
        `
      },
      "withLoading": {
        "template": "app.showLoading() -> app.post() -> app.hideLoading()",
        "example": `
          app.showLoading();
          app.post('ApiOrder/create', {
            // request params
          }, function(res) {
            app.hideLoading();
            if(res.type == 1) {
              // success handler
            }
          });
        `
      }
    }
  },
  "developmentRules": {
    "naming": {
      "files": {
        "pages": "kebab-case (例: user-center.vue)",
        "components": "PascalCase (例: UserAvatar.vue)",
        "utils": "camelCase (例: httpRequest.js)"
      },
      "variables": {
        "data": "camelCase (例: userInfo)",
        "methods": "camelCase (例: handleSubmit)",
        "components": "PascalCase (例: UserAvatar)",
        "constants": "UPPER_SNAKE_CASE (例: MAX_COUNT)"
      }
    },

    "componentStructure": {
      "order": [
        "name",
        "components",
        "mixins",
        "props",
        "data",
        "computed",
        "watch",
        "created",
        "mounted",
        "methods"
      ],
      "template": {
        "maxLength": 500,
        "splitComponents": true
      }
    },

    "commitRules": {
      "types": {
        "feat": "新功能",
        "fix": "修复bug",
        "docs": "文档更新",
        "style": "代码格式（不影响功能）",
        "refactor": "重构",
        "perf": "性能优化",
        "test": "测试相关",
        "build": "构建相关",
        "ci": "CI相关",
        "chore": "其他更改"
      },
      "format": "<type>(<scope>): <subject>",
      "example": "feat(user): 添加用户登录功能"
    },

    "performance": {
      "imageRules": {
        "maxSize": "200KB",
        "formats": ["jpg", "png", "webp"],
        "compress": true,
        "lazyLoad": true
      },
      "listRules": {
        "pageSize": 10,
        "maxItems": 100,
        "lazyRender": true,
        "virtualList": {
          "enable": true,
          "threshold": 50
        }
      }
    },

    "security": {
      "dataStorage": {
        "sensitive": ["token", "password", "mobile"],
        "storage": {
          "token": "localStorage",
          "userInfo": "localStorage",
          "tempData": "sessionStorage"
        }
      },
      "api": {
        "encryption": ["password", "idCard"],
        "tokenRefresh": {
          "timing": "beforeExpire",
          "threshold": 300 // 5分钟
        }
      }
    },

    "documentation": {
      "required": {
        "components": [
          "功能描述",
          "Props说明",
          "Events说明",
          "使用示例"
        ],
        "api": [
          "接口说明",
          "请求参数",
          "响应格式",
          "错误码说明"
        ],
        "functions": [
          "功能说明",
          "参数说明",
          "返回值",
          "使用示例"
        ]
      },
      "template": {
        "component": `
          /**
           * @description 组件描述
           * @props {Type} propName - 属性说明
           * @event {Type} eventName - 事件说明
           * @example
           *   <ComponentName :prop="value" @event="handler"/>
           */
        `,
        "function": `
          /**
           * @description 函数说明
           * @param {Type} paramName - 参数说明
           * @returns {Type} 返回说明
           * @example
           *   functionName(param)
           */
        `
      }
    }
  }
} 