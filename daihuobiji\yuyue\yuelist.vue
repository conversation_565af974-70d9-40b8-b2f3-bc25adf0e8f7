<template>
	<view class="container">

		<view class="busbox" v-for="(item, idx) in datalist" :key="idx">

			<view class="businfo">

				<view class="f1">

					<image class="image" :src="item.banner"></image>

					<view class="icon" v-if="item.status == 0">报名中</view>  

				</view>

				<view class="f2">

					<view class="title">{{item.name}}</view>

					<!-- <view class="address">{{item.helptext}}</view> -->

					<view class="address">{{item.distance}} {{item.venues_title}}</view>



				</view>

			</view>

			<view class="tab">

				<view class="imgs">
<!-- 
                     <image class="img":src="item.headimg"></image>

					<image class="img" :src="m.headimg" v-for="(m,n) in item.statistics" :key="n" ></image> -->

					<text>{{item.joinnum}}人已报名</text>

				</view>

				<view class="btn" :style="'background:'+primary_color" @click="goDeatail(item.id)">{{item.helptext}}</view>


			</view>


		</view>

		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>


	</view>

</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				datalist: [],
				pagenum: 1,
				nomore: false,
				nodata: false,
				primary_color : '',
				secondary_color : ''
			};
		},

		onLoad: function(opt) {

			this.getList();

			this.primary_color = app.getCache('primary_color')
			this.secondary_color = app.getCache('secondary_color')

		},
		onPullDownRefresh: function() {

			this.getList();
		},
		onReachBottom: function() {
			if (!this.nodata && !this.nomore) {
				this.pagenum = this.pagenum + 1;

				this.getList();
			}
		},
		methods: {
			getList: function() {
				var that = this;
				var pagenum = that.pagenum;
				that.nodata = false;
				that.nomore = false;

				var obj = {
					pagenum: pagenum
				}
				app.get('/ApiJianbanbaoming/baominglist', obj, function(res) {
					
					console.log(res)
					if (res.status == 1) {
						var data = res.data.list
						// if (pagenum == 1) {
							that.datalist = data;
							if (data.length == 0) {
								that.nodata = true;
							}
						// } else {
						// 	if (data.length == 0) {
						// 		that.nomore = true;
						// 	} else {
						// 		var datalist = that.datalist;
						// 		var newdata = datalist.concat(data);
						// 		that.datalist = newdata;
						// 	}
						// }
					}

				});
			},
			
			goDeatail(id){
				uni.navigateTo({
					url:'./yuedetail?id='+id
				})
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
	}

	.tab {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 20rpx 0 20rpx;
		border-top: 1px solid #eee
	}

	.tab .btn {
		background: #ccc;
		color: #fff;
		padding: 5px 10px;
		border-radius: 50rpx;
	}

	.tab .imgs {
		padding: 5px;
		display: flex;
		align-items: center;
	}

	.imgs .img {
		width: 20px;
		height: 20px;
		border-radius: 50px;
		margin-left: -10px;
	}
	
	.imgs text{
		font-size: 24rpx;
		color: #999;
/* 		margin-left: 10px; */
	}

	.busbox {
		background: #fff;
		padding: 16rpx;
		overflow: hidden;
		margin: 25rpx 25rpx 0 25rpx;
		border-radius: 20rpx;
	}

	.businfo {
		display: flex;
		width: 100%;
		padding-bottom: 10rpx;
	}

	.businfo .f1 {
		width: 280rpx;
		height: 200rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
		position: relative;
	}

	.businfo .f1 .image {
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
		object-fit: cover;
	}

	.f1 .icon {
		position: absolute;
		top: 0px;
		background: #ccc;
		font-size: 10px;
		padding: 2px 5px;
		border-radius: 5px;
		color: #fff;
	}

	.businfo .f2 {
		width: 55%;
	}

	.businfo .f2 .title {
		font-size: 28rpx;
		font-weight: bold;
		color: #222;
		line-height: 46rpx;
		margin-bottom: 3px;
	}

	.businfo .f2 .address {
		color: #999;
		font-size: 24rpx;
		line-height: 40rpx;
		margin-bottom: 3px;
		align-items: center;
		display: flex;
	}
	
	.address .img {
		width: 20px;
		height: 20px;
		border-radius: 50px;
		margin-right: 10px;
	}
	
</style>