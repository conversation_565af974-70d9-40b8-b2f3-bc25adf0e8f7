<template>
  <view class="page">
    <!-- 加载中 -->
    <loading v-if="loading"></loading>
    
    <!-- 详情内容 -->
    <view class="detail-content" v-if="detail">
      <!-- 头部信息 -->
      <view class="header">
        <view class="title">{{detail.title}}</view>
        <view class="info">
          <text class="category">{{detail.categoryname}}</text>
          <text class="time">{{detail.addtime}}</text>
        </view>
      </view>
      
      <!-- 主要内容 -->
      <view class="main-content">
        <view class="section">
          <view class="section-title">详细内容</view>
          <view class="content-text">{{detail.content}}</view>
        </view>
        
        <view class="section">
          <view class="section-title">采购信息</view>
          <view class="info-item">
            <text class="label">单价：</text>
            <text class="value price">￥{{detail.price || '面议'}}</text>
          </view>
          <view class="info-item">
            <text class="label">数量：</text>
            <text class="value">{{detail.quantity || '待商议'}}{{detail.unit || ''}}</text>
          </view>
          <view class="info-item" v-if="detail.total_price">
            <text class="label">总价：</text>
            <text class="value price">￥{{detail.total_price}}</text>
          </view>
        </view>
        
        <view class="section">
          <view class="section-title">联系方式</view>
          <view class="info-item">
            <text class="label">联系人：</text>
            <text class="value">{{detail.contact_name}}</text>
          </view>
          <view class="info-item">
            <text class="label">联系电话：</text>
            <text class="value phone" @tap="makePhoneCall">{{detail.contact_phone}}</text>
          </view>
        </view>

        <!-- 评论区域 -->
        <view class="section comment-section">
          <view class="section-title">评论区</view>
          <!-- 评论列表 -->
          <view class="comment-list" v-if="comments.list.length > 0">
            <view class="comment-item" v-for="(item, index) in comments.list" :key="index">
              <view class="comment-header">
                <image class="avatar" :src="item.headimg || '/static/images/default-avatar.png'" mode="aspectFill"></image>
                <view class="comment-info">
                  <text class="username">{{item.nickname}}</text>
                  <text class="time">{{item.addtime}}</text>
                </view>
              </view>
              <view class="comment-content">{{item.content}}</view>
              <!-- 图片列表 -->
              <view class="image-list" v-if="item.images">
                <image 
                  v-for="(img, imgIndex) in item.images.split(',')"
                  :key="imgIndex"
                  :src="img"
                  mode="aspectFill"
                  class="comment-image"
                  @tap="previewImage(item.images.split(','), imgIndex)"
                ></image>
              </view>
              <!-- 回复按钮 -->
              <view class="action-bar">
                <text class="reply-btn" @tap="showReply(item.id)">回复</text>
                <text class="reply-count" v-if="item.reply_count > 0" @tap="getReplyList(item.id)">
                  {{item.reply_count}}条回复
                </text>
              </view>
              <!-- 回复列表 -->
              <view class="reply-list" v-if="item.replies && item.replies.length > 0">
                <view class="reply-item" v-for="(reply, replyIndex) in item.replies" :key="replyIndex">
                  <view class="reply-header">
                    <image class="avatar small" :src="reply.headimg || '/static/images/default-avatar.png'" mode="aspectFill"></image>
                    <view class="reply-info">
                      <text class="username">{{reply.nickname}}</text>
                      <text class="time">{{reply.addtime}}</text>
                    </view>
                  </view>
                  <view class="reply-content">{{reply.content}}</view>
                  <!-- 回复图片 -->
                  <view class="image-list" v-if="reply.images">
                    <image 
                      v-for="(img, imgIndex) in reply.images.split(',')"
                      :key="imgIndex"
                      :src="img"
                      mode="aspectFill"
                      class="reply-image"
                      @tap="previewImage(reply.images.split(','), imgIndex)"
                    ></image>
                  </view>
                </view>
              </view>
              <!-- 回复输入框 -->
              <view class="reply-input-wrapper" v-if="showReplyInput && currentCommentId === item.id">
                <input 
                  class="reply-input"
                  type="text"
                  v-model="replyContent"
                  placeholder="回复..."
                  @confirm="submitReply(item.id)"
                />
                <view class="reply-btn-group">
                  <view class="cancel-btn" @tap="hideReply">
                    <text class="btn-text">取消</text>
                  </view>
                  <view class="submit-btn" @tap="submitReply(item.id)">
                    <text class="btn-text">发送</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!-- 暂无评论提示 -->
          <view class="no-comment" v-else>
            <text class="no-comment-text">暂无评论</text>
          </view>
        </view>
      </view>

      <!-- 底部评论输入框 -->
      <view class="comment-input-wrapper">
        <input 
          class="comment-input"
          type="text"
          v-model="commentContent"
          placeholder="说点什么..."
          @confirm="submitComment"
        />
        <view class="submit-btn" @tap="submitComment">
          <text class="submit-text">发送</text>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="footer">
        <view class="btn share" @tap="shareInfo">
          <text class="btn-text">分享</text>
        </view>
        <view class="btn contact" @tap="makePhoneCall">
          <text class="btn-text">立即联系</text>
        </view>
      </view>
    </view>
    
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      loading: true,
      detail: null,
      id: '',
      type: '',
      commentContent: '',
      comments: {
        list: [],
        page: 1,
        pagesize: 10,
        count: 0
      },
      replyContent: '',
      currentCommentId: null,
      showReplyInput: false
    }
  },
  onLoad(options) {
    this.id = options.id;
    this.type = options.type;
    this.getDetail();
    this.getComments();
  },
  methods: {
    // 获取详情数据
    getDetail() {
      var that = this;
      app.get('ApiCaigou/detail', {
        id: that.id
      }, function(res) {
        that.loading = false;
        if(res.status == 1) {
          that.detail = res.data;
          // 设置页面标题
          uni.setNavigationBarTitle({
            title: that.type == '1' ? '采购详情' : '供应详情'
          });
        } else {
          app.error(res.msg);
        }
      });
    },
    
    // 获取评论列表
    getComments(loadmore) {
      var that = this;
      if(!loadmore) {
        that.comments.page = 1;
        that.comments.list = [];
      }
      
      app.get('ApiCaigou/comment_list', {
        id: that.id,
        page: that.comments.page,
        pagesize: that.comments.pagesize
      }, function(res) {
        if(res.status == 1) {
          const data = res.data;
          if(loadmore) {
            that.comments.list = that.comments.list.concat(data.list);
          } else {
            that.comments.list = data.list;
          }
          that.comments.count = data.count;
          that.comments.page = data.page;
          that.comments.pagesize = data.pagesize;
        } else {
          app.error(res.msg);
        }
      });
    },
    
    // 获取评论回复列表
    getReplyList(commentId) {
      var that = this;
      app.get('ApiCaigou/reply_list', {
        id: commentId,
        page: 1,
        pagesize: 10
      }, function(res) {
        if(res.status == 1) {
          // 找到对应的评论并更新回复列表
          const commentIndex = that.comments.list.findIndex(item => item.id === commentId);
          if(commentIndex > -1) {
            that.$set(that.comments.list[commentIndex], 'replies', res.data.list);
          }
        } else {
          app.error(res.msg);
        }
      });
    },
    
    // 提交评论
    submitComment() {
      if(!this.commentContent.trim()) {
        app.error('请输入评论内容');
        return;
      }
      
      var that = this;
      app.post('ApiCaigou/comment_publish', {
        caigou_id: that.id,
        content: that.commentContent
      }, function(res) {
        if(res.status == 1) {
          that.commentContent = '';
          that.getComments();
          app.success('评论成功');
        } else {
          app.error(res.msg);
        }
      });
    },
    
    // 提交回复
    submitReply(commentId) {
      if(!this.replyContent.trim()) {
        app.error('请输入回复内容');
        return;
      }
      
      var that = this;
      app.post('ApiCaigou/reply_publish', {
        pid: commentId,
        comment_id: commentId,
        content: that.replyContent
      }, function(res) {
        if(res.status == 1) {
          that.replyContent = '';
          that.showReplyInput = false;
          that.currentCommentId = null;
          that.getReplyList(commentId);
          app.success('回复成功');
        } else {
          app.error(res.msg);
        }
      });
    },
    
    // 显示回复输入框
    showReply(commentId) {
      this.currentCommentId = commentId;
      this.showReplyInput = true;
      this.replyContent = '';
    },
    
    // 隐藏回复输入框
    hideReply() {
      this.showReplyInput = false;
      this.currentCommentId = null;
      this.replyContent = '';
    },
    
    // 拨打电话
    makePhoneCall() {
      if(this.detail && this.detail.contact_phone) {
        uni.makePhoneCall({
          phoneNumber: this.detail.contact_phone,
          fail: function() {
            app.error('拨打电话失败');
          }
        });
      }
    },
    
    // 分享信息
    shareInfo() {
      if(!this.detail) return;
      
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    },
    
    // 图片预览
    previewImage(urls, current) {
      uni.previewImage({
        urls: urls,
        current: current
      });
    },
    
    // 触底加载更多
    onReachBottom() {
      if(this.comments.list.length < this.comments.count) {
        this.comments.page++;
        this.getComments(true);
      }
    }
  },
  
  // 分享给好友
  onShareAppMessage() {
    return {
      title: this.detail ? this.detail.title : '',
      path: '/pagesExa/caigougongying/detail?id=' + this.id + '&type=' + this.type
    }
  },
  
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.detail ? this.detail.title : '',
      query: 'id=' + this.id + '&type=' + this.type
    }
  },
  
  // 触底加载更多
  onReachBottom() {
    if(this.hasMore) {
      this.page++;
      this.getComments(true);
    }
  }
}
</script>

<style>
.page {
  min-height: 100vh;
  background: #f6f7f9;
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
}

.detail-content {
  padding: 24rpx;
}

/* 头部区域 */
.header {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.title {
  font-size: 36rpx;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.category {
  font-size: 24rpx;
  color: #ff4757;
  background: rgba(255,71,87,0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.time {
  font-size: 24rpx;
  color: #95a5a6;
}

/* 主要内容区域 */
.main-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.section {
  margin-bottom: 32rpx;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 24rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background: #ff4757;
  border-radius: 3rpx;
}

.content-text {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.8;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #7f8c8d;
  width: 140rpx;
}

.value {
  font-size: 28rpx;
  color: #2c3e50;
  flex: 1;
}

.price {
  color: #ff4757;
  font-weight: 600;
  font-size: 32rpx;
}

.phone {
  color: #3498db;
  text-decoration: none;
  position: relative;
  padding-bottom: 4rpx;
}

.phone::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2rpx;
  background: #3498db;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.phone:active::after {
  transform: scaleX(1);
}

/* 评论区域 */
.comment-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 120rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.comment-list {
  margin-bottom: 24rpx;
}

.comment-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.comment-info {
  flex: 1;
}

.username {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.comment-content {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.comment-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
}

.action-bar {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.reply-btn {
  font-size: 24rpx;
  color: #7f8c8d;
}

.reply-count {
  font-size: 24rpx;
  color: #3498db;
}

.reply-list {
  margin-top: 16rpx;
  padding-left: 64rpx;
}

/* 底部按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 24rpx;
  box-shadow: 0 -2rpx 20rpx rgba(0,0,0,0.05);
  z-index: 99;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.btn:active {
  opacity: 0.8;
}

.share {
  background: #f5f6fa;
}

.share .btn-text {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

.contact {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
}

.contact .btn-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 评论输入框 */
.comment-input-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: calc(140rpx + env(safe-area-inset-bottom));
  background: #ffffff;
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 -2rpx 20rpx rgba(0,0,0,0.05);
  z-index: 98;
}

.comment-input {
  flex: 1;
  height: 72rpx;
  background: #f5f6fa;
  border-radius: 36rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #34495e;
}

/* 回复输入框样式 */
.reply-input-wrapper {
  margin-top: 16rpx;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 16rpx;
}

.reply-input {
  width: 100%;
  height: 72rpx;
  background: #f5f6fa;
  border-radius: 36rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #34495e;
  margin-bottom: 16rpx;
}

.reply-btn-group {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.cancel-btn, .submit-btn {
  height: 64rpx;
  padding: 0 32rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f5f6fa;
}

.cancel-btn .btn-text {
  font-size: 28rpx;
  color: #7f8c8d;
}

.submit-btn {
  background: #ff4757;
}

.submit-btn .btn-text {
  font-size: 28rpx;
  color: #ffffff;
}

/* 修复回复列表样式 */
.reply-list {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #ffffff;
  border-radius: 12rpx;
}

.reply-item {
  padding: 16rpx;
  border-bottom: 2rpx solid #f5f6fa;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.avatar.small {
  width: 56rpx;
  height: 56rpx;
}

.reply-content {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.6;
}

.reply-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}
</style>