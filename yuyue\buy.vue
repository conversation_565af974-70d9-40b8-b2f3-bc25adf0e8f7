<template>
	<view class="container">
		<block v-if="isload">
			<form @submit="topay">
				<view v-if="sindex==1" class="address-add">
					<view class="linkitem">
						<text class="f1">联 系 人：</text>
						<input type="text" class="input" :value="linkman" placeholder="请输入您的姓名" @input="inputLinkman" placeholder-style="color:#626262;font-size:28rpx;"/>
					</view>
					<view class="linkitem">
						<text class="f1">联系电话：</text>
						<input type="text" class="input" :value="tel" placeholder="请输入您的手机号" @input="inputTel" placeholder-style="color:#626262;font-size:28rpx;"/>
					</view>
				</view>
				<view v-else class="address-add flex-y-center" @tap="goto"
					:data-url="'/pages/address/'+(address.id ? 'address' : 'addressadd')+'?fromPage=buy&type=1'">
					<view class="f1">
						<image class="img" :src="pre_url + '/static/img/address.png'" />
					</view>
					<view class="f2 flex1" v-if="address.id">
						<view style="font-weight:bold;color:#111111;font-size:30rpx">{{address.name}} {{address.tel}} <text v-if="address.company">{{address.company}}</text></view>
						<view style="font-size:24rpx">{{address.area}} {{address.address}}</view>
					</view>
					<view v-else class="f2 flex1">请选择您的地点</view>
					<image :src="pre_url + '/static/img/arrowright.png'" class="f3"></image>
				</view>
				<view v-for="(buydata, index) in allbuydata" :key="index" class="buydata">			
					<view class="bcontent">
						<view class="btitle">
							服务信息
						</view>
						<view class="product">
							<view v-for="(item, index2) in buydata.prodata" :key="index2" class="item flex">
								<view class="img" @tap="goto" :data-url="'product?id=' + item.product.id">
									<image v-if="item.guige.pic" :src="item.guige.pic"></image>
									<image v-else :src="item.product.pic"></image>
								</view>
								<view class="info flex1">
									<view class="f1">{{item.product.name}}</view>
									<view class="f2">{{item.guige.name}}</view>
									<view class="f3"><text style="font-weight:bold;">￥{{item.guige.sell_price}}</text><text
											style="padding-left:20rpx"> × {{item.num}}</text></view>
								</view>
							</view>
						</view>
				
						<view class="body_item">
							<view class="body_title flex flex-bt">服务方式<text class="body_text">请选择服务方式</text></view>
							<view class="body_content">
								<block v-for="(item, idx2) in fwtypelist" :key="idx2">
									<view class="body_tag" :class="item.key==sindex?'body_active':''"
										@tap="selectFwtype" :data-index="item.key">{{item.name}}
									</view>
								</block>
							</view>
						</view>
						
						<view class="body_item" v-if="order_flow_mode !== 1">
							<view class="body_title flex flex-bt">
								<text>预约时间</text>
								<view class="body_data" v-if="isdate" @tap="chooseTime">
									{{yydate?yydate:'请选择预约时间'}}
									<image class="body_detail" :src="pre_url + '/static/img/arrowright.png'" style="width:26rpx;height:26rpx;"></image>
								</view>
								<view class="body_data" v-else>{{yydate}}</view>
							</view>
						</view>

						<view class="body_item" v-if="buydata.fwpeople==1 && order_flow_mode !== 1">
							<view class="body_title flex flex-bt">
								<text>服务人员</text>
								<view class="body_data" @tap="gotopeople"
									:data-url="'selectpeople?prodata='+prodata+'&yydate='+yydate+'&sindex='+sindex+'&linkman='+linkman+'&tel='+tel"> 
									{{!isEmpty(buydata.fw)?buydata.fw.realname:'请选择人员'}}
									<image class="body_detail" :src="pre_url + '/static/img/arrowright.png'" style="width:26rpx;height:26rpx;"></image>
								</view>
							</view>
						</view>
					</view>
					<view class="bcontent2">
						<view class="price" v-if="buydata.leveldk_money>0">
							<text class="f1">{{t('会员')}}折扣({{userinfo.discount}}折)</text>
							<text class="f2">-¥{{buydata.leveldk_money}}</text>
						</view>
						<view class="price" v-if="yyset.iscoupon==1">
							<view class="f1">{{t('优惠券')}}</view>
							<view v-if="buydata.couponCount > 0" class="f2" @tap="showCouponList" :data-bid="buydata.bid">
								<text
									style="color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx"
									:style="{background:t('color1')}">{{buydata.couponrid!=0?buydata.couponList[buydata.couponkey].couponname:buydata.couponCount+'张可用'}}</text><text
									class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</view>
							<text class="f2" v-else style="color:#999">无可用{{t('优惠券')}}</text>
						</view>
						
				
						
						<view class="price">
							<text class="f1">服务价格</text>
							<text class="f2">¥{{buydata.product_price}}</text>
						</view>
						<view class="price">
							<text class="f1">应付定金</text>
							<text class="f2">¥{{buydata.sell_price}}</text>
						</view>
						
						<view class="price" v-if="buydata.coupontype==3">
							<text class="f1">计次卡</text>
							<text class="f2" style="color: red;">-{{buydata.product_price}}</text>
						</view>
							
							
						<view style="display:none">{{test}}</view>
						<view class="form-item" v-for="(item,idx) in buydata.formdata" :key="item.id">
							<view class="label">{{item.val1}}<text v-if="item.val3==1" style="color:red"> *</text></view>
							<block v-if="item.key=='input'">
								<input type="text" :name="'form'+buydata.bid+'_'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx"/>
							</block>
							<block v-if="item.key=='textarea'">
								<textarea :name="'form'+buydata.bid+'_'+idx" class='textarea' :placeholder="item.val2" placeholder-style="font-size:28rpx"/>
							</block>
							<block v-if="item.key=='radio'">
								<radio-group class="radio-group" :name="'form'+buydata.bid+'_'+idx">
									<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
										<radio class="radio" :value="item1"/>{{item1}}
									</label>
								</radio-group>
							</block>
							<block v-if="item.key=='checkbox'">
								<checkbox-group :name="'form'+buydata.bid+'_'+idx" class="checkbox-group">
									<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
										<checkbox class="checkbox" :value="item1"/>{{item1}}
									</label>
								</checkbox-group>
							</block>
							<block v-if="item.key=='selector'">
								<picker class="picker" mode="selector" :name="'form'+buydata.bid+'_'+idx" value="" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx] || buydata.editorFormdata[idx]===0"> {{item.val2[buydata.editorFormdata[idx]]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='time'">
								<picker class="picker" mode="time" :name="'form'+buydata.bid+'_'+idx" value="" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx]">{{buydata.editorFormdata[idx]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='date'">
								<picker class="picker" mode="date" :name="'form'+buydata.bid+'_'+idx" value="" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx]">{{buydata.editorFormdata[idx]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='upload'">
								<input type="text" style="display:none" :name="'form'+buydata.bid+'_'+idx" :value="buydata.editorFormdata[idx]"/>
								<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
									<view class="form-imgbox" v-if="buydata.editorFormdata[idx]">
										<view class="form-imgbox-img"><image class="image" :src="buydata.editorFormdata[idx]" @click="previewImage" :data-url="buydata.editorFormdata[idx]" mode="widthFix"/></view>
									</view>
									<view class="form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-bid="buydata.bid" :data-idx="idx"></view>
								</view>
							</block>
						</view>
					</view>
					
				</view>


				<view style="width: 100%; height:110rpx;"></view>
				<view class="footer flex">
					<view class="text1 flex1">总计：
						<text style="font-weight:bold;font-size:36rpx">￥{{alltotalprice}}</text>
					</view>
					<button v-if="issubmit" class="op" style="background: #999;" >
						确认提交</button>
					<button v-else class="op" form-type="submit" :style="{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
							确认提交</button>
				</view>
			</form>

			<view v-if="couponvisible" class="popup__container">
				<view class="popup__overlay" @tap.stop="handleClickMask"></view>
				<view class="popup__modal">
					<view class="popup__title">
						<text class="popup__title-text">请选择{{t('优惠券')}}</text>
						<image :src="pre_url + '/static/img/close.png'" class="popup__close" style="width:36rpx;height:36rpx"
							@tap.stop="handleClickMask" />
					</view>
					<view class="popup__content">
						<couponlist :couponlist="allbuydata[bid].couponList" :choosecoupon="true"
							:selectedrid="allbuydata[bid].couponrid" :bid="bid" @chooseCoupon="chooseCoupon">
						</couponlist>
					</view>
				</view>
			</view>
		</block>
		
		
		<view v-if="timeDialogShow" class="popup__container">
			<view class="popup__overlay" @tap.stop="hidetimeDialog"></view>
			<view class="popup__modal">
				<view class="popup__title">
					<text class="popup__title-text">请选择时间</text>
					<image :src="pre_url + '/static/img/close.png'" class="popup__close" style="width:36rpx;height:36rpx"
						@tap.stop="hidetimeDialog" />
				</view>
				
				<view class="order-tab">
					<view class="order-tab2">
						<block v-for="(item, index) in datelist" :key="index">
							<view :class="'item ' + (curTopIndex == index ? 'on' : '')" @tap="switchTopTab" :data-index="index" :data-id="item.id">
								<view class="datetext">{{item.weeks}}</view>
								<view class="datetext2">{{item.date}}</view>
								<view class="after" :style="{background:t('color1')}"></view>
							</view>			
						</block>
					</view>
				</view>
				<view class="flex daydate">
					<block v-for="(item,index2) in timelist" :key="index2">
						<view :class="'date ' + ((timeindex==index2 && item.status==1) ? 'on' : '') + (item.status==0 ?'hui' : '') "  
							@tap="switchDateTab" :data-index2="index2" :data-status="item.status" :data-time="item.timeint"> 
							{{item.time}}
						</view>						
					</block>
				</view>
				<view class="op">
					<button class="tobuy on" :style="{backgroundColor:t('color1')}" @tap="selectDate">确 定</button>
				</view>
			</view>
		</view>
		
		
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				pre_url:app.globalData.pre_url,
				test:'test',
				address: [],
				totalprice: '0.00',
				couponvisible: false,
				bid: 0,
				nowbid: 0,
				needaddress: 1,
				linkman: '',
				tel: '',
				userinfo: {},
				latitude: "",
				longitude: "",
				allbuydata: {},
				alltotalprice: "",
				type11visible: false,
				type11key: -1,
				regiondata: '',
				items: [],
				editorFormdata:[],
				sindex:'',
				prodata:'',
				yydate:'',
				yyset:'',
				issubmit:false,
				isdate:false,
				timeDialogShow: false,
				datelist:[],
				daydate:[],
				curTopIndex: 0,
				index:0,
				day: -1,
				days:'请选择服务时间',
				dates:'',
				num:0,
				timeindex:-1,
				startTime:0,
				selectDates:'',
				timelist:[],
				proid:'',
				order_flow_mode: 0,
			};
		},

		onLoad: function(opt) {
			this.opt = opt;
			var opts = app.getopts(opt);
			this.opt.prodata = opts.prodata;
			this.opt.worker_id = opts.worker_id ? opts.worker_id : 0;
			this.opt.yydate = opts.yydate;
			this.opt.frompage = opts.frompage;
			this.yydate = opts.yydate;
			
			// 获取URL参数中的流程模式
			this.opt.order_flow_mode = opts.order_flow_mode;
			if (this.opt.order_flow_mode) {
				this.order_flow_mode = parseInt(this.opt.order_flow_mode);
				console.log("从URL获取流程模式:", this.order_flow_mode);
			}
			
			this.prodata = this.opt.prodata;
			this.sindex = opt.sindex;
			this.linkman = opt.linkman;
			this.tel = opt.tel;
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			getdata: function() {
				var that = this;
				
				app.get('ApiYuyue/buy', {
					prodata: that.opt.prodata,
					worker_id:that.opt.worker_id,
				}, function(res) {
					that.address = res.address;
					if(!that.linkman ){
						that.linkman = res.linkman;
					}
					if(!that.tel ){
						that.tel = res.tel;
					}
					that.userinfo = res.userinfo;
					that.yyset = res.yyset;
					that.allbuydata = res.allbuydata;
					that.fwtypelist = res.fwtypelist;
					
					// 确保URL参数的优先级高于接口返回值
					if (!that.opt.order_flow_mode) {
						that.order_flow_mode = res.order_flow_mode || 0;
					}
					
					if(!that.sindex ){
						that.sindex = that.fwtypelist[0].key;
					}
					that.isdate = res.isdate
					that.datelist = res.datelist;
					that.calculatePrice();
					that.loaded();
				});
			},
			chooseCoupon: function(e) {
				var allbuydata = this.allbuydata;
				var bid = e.bid;
				var couponrid = e.rid;
				var couponkey = e.key;
				if (couponrid == allbuydata[bid].couponrid) {
					allbuydata[bid].couponkey = 0;
					allbuydata[bid].couponrid = 0;
					allbuydata[bid].coupontype = 1;
					allbuydata[bid].coupon_money = 0;
					this.allbuydata = allbuydata;
					this.couponvisible = false;
				} else {
					var couponList = allbuydata[bid].couponList;
					var coupon_money = couponList[couponkey]['money'];
					var coupontype = couponList[couponkey]['type'];
					if(coupontype == 10){
						coupon_money = allbuydata[bid].sell_price * (100 - couponList[couponkey]['discount']) * 0.01;
					}
					allbuydata[bid].couponkey = couponkey;
					allbuydata[bid].couponrid = couponrid;
					allbuydata[bid].coupontype = coupontype;
					allbuydata[bid].coupon_money = coupon_money;
					this.allbuydata = allbuydata;
					this.couponvisible = false;
				}
				this.calculatePrice();
			},
			showCouponList: function(e) {
				this.couponvisible = true;
				this.bid = e.currentTarget.dataset.bid;
			},
			handleClickMask: function() {
				this.couponvisible = false;
			},
			//计算价格
			calculatePrice: function() {
				var that = this;
				var address = that.address;
				var allbuydata = that.allbuydata;
				var alltotalprice = 0;
				var needaddress = 0;
				for (var k in allbuydata) {
					var product_price = parseFloat(allbuydata[k].sell_price);
					var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 
				  if(allbuydata[k].coupontype==3) coupon_money =  product_price
					var totalprice = product_price - coupon_money ;
					if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费					
					alltotalprice += totalprice;
				}
				
				that.needaddress = needaddress;
				var oldalltotalprice = alltotalprice;
				if (alltotalprice < 0) alltotalprice = 0;
				alltotalprice = alltotalprice.toFixed(2);
				that.alltotalprice = alltotalprice;
				that.allbuydata = allbuydata;
			},
			//提交并支付
			topay: function(e) {
				var that = this;
				var addressid = this.address.id;
				var linkman = this.linkman;
				var tel = this.tel;
				var worker_id = that.opt.worker_id;
				var frompage = that.opt.frompage ? that.opt.frompage : '';
				var allbuydata = that.allbuydata;
				
				// 添加日志输出
				console.log("buy.vue中的流程模式:", that.order_flow_mode, typeof that.order_flow_mode);
				console.log("URL参数中的流程模式:", that.opt.order_flow_mode);
				console.log("服务人员ID:", worker_id);
				console.log("预约时间:", that.yydate);
				
				// 确保流程模式是数字类型
				var flowMode = parseInt(that.order_flow_mode || that.opt.order_flow_mode || 0);
				
				// 根据流程模式判断必填项
				if (that.sindex == 0) {
					app.error('请选择服务方式');
					return;
				}
				if(that.sindex==2 && addressid == undefined) {
					app.error('请选择地址');
					return;
				}
				if(that.sindex==1 && (!linkman || !tel)) {
					app.error('请填写联系人及联系电话');
					return;
				}
				
				// 传统模式下需要选择服务人员和预约时间
				if (flowMode !== 1) {
					if (worker_id == 0) {
						app.error('请选择服务人员');
						return;
					}
					if (!that.yydate) {
						app.error('请选择预约时间');
						return;
					}
				}

				var buydata = [];
				for (var i in allbuydata) {
					var formdata_fields = allbuydata[i].formdata;
					var formdata = e.detail.value;
					var newformdata = {};
					for (var j = 0; j < formdata_fields.length;j++){
						var thisfield = 'form'+allbuydata[i].bid + '_' + j;
						if (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length==0)){
							app.alert(formdata_fields[j].val1+' 必填');
							return;
						}
						if (formdata_fields[j].key == 'selector') {
							formdata[thisfield] = formdata_fields[j].val2[formdata[thisfield]]
						}
						newformdata['form'+j] = formdata[thisfield];
					}			
					buydata.push({
						bid: allbuydata[i].bid,
						prodata: allbuydata[i].prodatastr,
						couponrid: allbuydata[i].couponrid,
						formdata:newformdata
					});
				}
				
				var remark = this.remark;
				var yydate = that.yydate;
				
				app.showLoading('提交中');
				app.post('ApiYuyue/createOrder', {
					frompage: frompage,
					buydata: buydata,
					addressid: addressid,
					linkman: linkman,
					tel: tel,
					remark:remark,
					yydate:that.yydate,
					worker_id:worker_id,
					fwtype:that.sindex,
					order_flow_mode: flowMode
				}, function(res) {
					app.showLoading(false);
					if(res.status==1 && res.payorderid){
						that.	issubmit = true	
						app.goto('/pages/pay/pay?id=' + res.payorderid);
					}else if(res.status==1 && !res.payorderid){
						if(that.yyset.yuyue_success){
							app.alert(that.yyset.yuyue_success,function(){
								app.goto('/yuyue/orderlist');
							});
						}else{
							app.goto('/yuyue/orderlist');
						}
					}else	if (res.status == 0) {
						app.error(res.msg);
						return;
					}
				});
			},
			editorChooseImage: function (e) {
				var that = this;
				var bid = e.currentTarget.dataset.bid;
				var idx = e.currentTarget.dataset.idx;
				var editorFormdata = that.allbuydata[bid].editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				app.chooseImage(function(data){
					editorFormdata[idx] = data[0];
					// console.log(editorFormdata)
					that.allbuydata[bid].editorFormdata = editorFormdata
					that.test = Math.random();
				})
			},editorBindPickerChange:function(e){
				var that = this;
				var bid = e.currentTarget.dataset.bid;
				var idx = e.currentTarget.dataset.idx;
				var val = e.detail.value;
				var editorFormdata = that.allbuydata[bid].editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = val;
				// console.log(editorFormdata)
				that.allbuydata[bid].editorFormdata = editorFormdata;
				that.test = Math.random();
			},
			//选择服务方式
			selectFwtype: function(e) {
				var that = this;
				var index = e.currentTarget.dataset.index;
				that.sindex = index
			},
			inputLinkman: function (e) {
				this.linkman = e.detail.value
			},
			inputTel: function (e) {
				this.tel = e.detail.value
			},
			//选择时间
			chooseTime: function(e) {
				var that = this;
				var prodata = that.prodata.split(',');
				that.proid = prodata[0]
				that.timeDialogShow = true;
				that.timeIndex = -1;
				var curTopIndex = that.datelist[0];
				that.nowdate = that.datelist[that.curTopIndex].year+that.datelist[that.curTopIndex].date;
				that.loading = true;
				app.get('ApiYuyue/isgetTime', { date:that.nowdate,proid:that.proid}, function (res) {
				  that.loading = false;
				  that.timelist = res.data;
				})
			},	
			switchTopTab: function (e) {
			  var that = this;
			  var id = e.currentTarget.dataset.id;
			  var index = parseInt(e.currentTarget.dataset.index);
			  this.curTopIndex = index;
			  that.days = that.datelist[index].year+that.datelist[index].date
			  that.nowdate = that.datelist[index].nowdate
			   // if(!that.dates ){ that.dates = that.daydate[0] }
			  this.curIndex = -1;
			  this.curIndex2 = -1;
			  //检测预约时间是否可预约
			  that.loading = true;

			  app.get('ApiYuyue/isgetTime', { date: that.days,proid:that.proid}, function (res) {
				  that.loading = false;
				  that.timelist = res.data;
			  })
				
			},
			switchDateTab: function (e) {
			  var that = this;
			  var index2 = parseInt(e.currentTarget.dataset.index2);
			  var timeint = e.currentTarget.dataset.time
			  var status = e.currentTarget.dataset.status
			  if(status==0){
					app.error('此时间不可选择');return;	
			  }
			  that.timeint = timeint
			  that.timeindex = index2
				//console.log(that.timelist);
			  that.starttime1 = that.timelist[index2].time
			  if(!that.days || that.days=='请选择服务时间'){ that.days = that.datelist[0].year + that.datelist[0].date }
			  that.selectDates = that.starttime1;
			},
			selectDate:function(e){
				var that=this
				if(that.timeindex >= 0 && that.timelist[that.timeindex].status==0){
						that.starttime1='';
				}
				if(!that.starttime1){
					app.error('请选择预约时间');return;
				}
				 var yydate = that.days+' '+that.selectDates
				 that.yydate = yydate
				 this.timeDialogShow = false;
			},
			hidetimeDialog: function() {
				this.timeDialogShow = false;
			},
			gotopeople:function(e){
				var that=this
				// 传统模式下需要先选择预约时间
				if (that.order_flow_mode !== 1 && !that.yydate){
					app.error('请先选择预约时间');
					return;
				} 
				app.goto('selectpeople?prodata='+that.prodata+'&yydate='+that.yydate+'&sindex='+that.sindex+'&linkman='+that.linkman+'&tel='+that.tel);
			}
		}
	}
</script>

<style>
	.redBg{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx; width: auto; display: inline-block; margin-top: 4rpx;}
.address-add {width: 94%;margin: 20rpx 3%;background: #fff;border-radius: 20rpx;padding: 20rpx 3%;min-height: 140rpx;}
.address-add .f1 {margin-right: 20rpx}
.address-add .f1 .img {width: 66rpx;height: 66rpx;}
.address-add .f2 {color: #666;}
.address-add .f3 {width: 26rpx;height: 26rpx;}
.linkitem {width: 100%;padding: 1px 0;background: #fff;display: flex;align-items: center}.cf3 {width: 200rpx;height: 26rpx;display: block;
    text-align: right;}
.linkitem .f1 {width: 160rpx;color: #111111}
.linkitem .input {height: 50rpx;padding-left: 10rpx;color: #222222;font-weight: bold;font-size: 28rpx;flex: 1}
.buydata {width: 94%;margin: 0 3%;margin-bottom: 20rpx;}
.btitle {width: 100%;padding: 20rpx 20rpx;display: flex;align-items: center;color: #111111;font-weight: bold;font-size: 30rpx}
.btitle .img {width: 34rpx;height: 34rpx;margin-right: 10rpx}
.bcontent {width: 100%;padding: 0 20rpx;background: #fff;border-radius: 20rpx;}
.bcontent2 {width: 100%;padding: 0 20rpx; margin-top: 30rpx;background: #fff;border-radius: 20rpx;}
.product {width: 100%;border-bottom: 1px solid #f4f4f4}
.product .item {width: 100%;padding: 20rpx 0;background: #fff;border-bottom: 1px #ededed dashed;}
.product .item:last-child {border: none}
.product .info {padding-left: 20rpx;}
.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
.product .info .f2 {color: #999999;font-size: 24rpx}
.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}
.product image {width: 140rpx;height: 140rpx}
.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}
.freight .f1 {color: #333;margin-bottom: 10rpx}
.freight .f2 {color: #111111;text-align: right;flex: 1}
.freight .f3 {width: 24rpx;height: 28rpx;}
.freighttips {color: red;font-size: 24rpx;}
.freight-ul {width: 100%;display: flex;}
.freight-li {flex-shrink: 0;display: flex;background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;text-align: center;height: 48rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0}

.price {width: 100%;padding: 20rpx 0;background: #fff;display: flex;align-items: center}
.price .f1 {color: #333}
.price .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}
.price .f3 {width: 24rpx;height: 24rpx;}
.scoredk {width: 94%;margin: 0 3%;margin-bottom: 20rpx;border-radius: 20rpx;padding: 24rpx 20rpx;background: #fff;display: flex;align-items: center}
.scoredk .f1 {color: #333333}
.scoredk .f2 {color: #999999;text-align: right;flex: 1}
.remark {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center}
.remark .f1 {color: #333;width: 200rpx}
.remark input {border: 0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right}
.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 20rpx;display: flex;align-items: center;z-index: 8}
.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}
.footer .text1 text {color: #e94745;font-size: 32rpx;}
.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}
.storeitem {width: 100%;padding: 20rpx 0;display: flex;flex-direction: column;color: #333}
.storeitem .panel {width: 100%;height: 60rpx;line-height: 60rpx;font-size: 28rpx;color: #333;margin-bottom: 10rpx;display: flex}
.storeitem .panel .f1 {color: #333}
.storeitem .panel .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}
.storeitem .radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;border-bottom: 0 solid #eee;padding: 8rpx 20rpx;}
.storeitem .radio-item:last-child {border: 0}
.storeitem .radio-item .f1 {color: #666;flex: 1}
.storeitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left: 30rpx}
.storeitem .radio .radio-img {width: 100%;height: 100%}
.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}
.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.pstime-item .radio .radio-img {width: 100%;height: 100%}
.cuxiao-desc {width: 100%}
.cuxiao-item {display: flex;padding: 0 40rpx 20rpx 40rpx;}
.cuxiao-item .type-name {font-size: 28rpx;color: #49aa34;margin-bottom: 10rpx;flex: 1}
.cuxiao-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.cuxiao-item .radio .radio-img {width: 100%;height: 100%}

.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}
.form-item .label {color: #333;width: 200rpx;flex-shrink:0}
.form-item .radio{transform:scale(.7);}
.form-item .checkbox{transform:scale(.7);}
.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}
.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}
.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}
.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .radio2{display:flex;align-items:center;}
.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}
.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}

.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.form-imgbox-close .image{width:100%;height:100%}
.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.form-imgbox-img>.image{max-width:100%;}
.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.form-uploadbtn{position:relative;height:180rpx;width:180rpx}

.member_search{width:100%;padding:0 40rpx;display:flex;align-items:center}
.searchMemberButton{height:60rpx;background-color: #007AFF;border-radius: 10rpx;width: 160rpx;line-height: 60rpx;color: #fff;text-align: center;font-size: 28rpx;display: block;}
.memberlist{width:100%;padding:0 40rpx;height: auto;margin:20rpx auto;}
.memberitem{display:flex;align-items:center;border-bottom:1px solid #f5f5f5;padding:20rpx 0}
.memberitem image{display: block;height:100rpx;width:100rpx;margin-right:20rpx;}
.memberitem .t1{color:#333;font-weight:bold}
.memberitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.memberitem .radio .radio-img {width: 100%;height: 100%}

.checkMem{ display: inline-block; }
.checkMem p{ height: 30px; width: 100%; display: inline-block; }
.placeholder{  font-size: 26rpx;line-height: 80rpx;}
.selected-item span{ font-size: 26rpx !important;}
.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;flex-shrink:0}
.orderinfo .item .t2{flex:1;text-align:right}
.orderinfo .item .red{color:red}


/*时间范围*/
.datetab{ display: flex; border:1px solid red; width: 200rpx; text-align: center;}
.order-tab{ }
.order-tab2{display:flex;width:auto;min-width:100%}
.order-tab2 .item{width:auto;font-size:28rpx;font-weight:bold;text-align: center; color:#999999;overflow: hidden;flex-shrink:0;flex-grow: 1; display: flex; flex-direction: column; justify-content: center; align-items: center;}
.order-tab2 .item .datetext{ line-height: 60rpx; height:60rpx;}
.order-tab2 .item .datetext2{ line-height: 60rpx; height:60rpx;font-size: 22rpx;}
.order-tab2 .on{color:#222222;}
.order-tab2 .after{display:none;margin-left:-10rpx;bottom:5rpx;height:6rpx;border-radius:1.5px;width:70rpx}
.order-tab2 .on .after{display:block}
.daydate{ padding:20rpx; flex-wrap: wrap; overflow-y: scroll; height:400rpx; }
.daydate .date{ width:20%;text-align: center;line-height: 60rpx;height: 60rpx; margin-top: 30rpx;}
.daydate .on{ background:red; color:#fff;}
.daydate .hui{ border:1px solid #f0f0f0; background:#f0f0f0;border-radius: 5rpx;}
.tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold;width:90%;margin:20rpx 5%;border-radius:36rpx;}

/* 从planWrite.vue引入的样式 */
.body_item {
	padding: 20px 0;
	border-bottom: 1px solid #f6f6f6;
}

.body_item:last-child {
	border-bottom: none;
}

.body_title {
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #323232;
}

.body_text {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #999999;
	margin-left: 20rpx;
}

.body_content {
	position: relative;
	display: flex;
	flex-wrap: wrap;
	margin-top: 10rpx;
}

.body_tag {
	padding: 0 20rpx;
	height: 54rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #F4F4F4;
	border-radius: 27rpx;
	margin: 20rpx 10rpx 0 0;
	font-size: 22rpx;
	border: 1px solid rgba(0, 0, 0, 0);
	font-family: PingFang SC;
}

.body_active {
	background: rgba(252, 67, 67, 0.1200);
	border: 1px solid #FC4343;
	color: #FC4343;
	box-sizing: border-box;
}

.body_data {
	font-size: 28rpx;
	font-weight: normal;
	font-family: PingFang SC;
	font-weight: 500;
	color: #686868;
	display: flex;
	align-items: center;
}

.body_detail {
	height: 35rpx;
	width: 35rpx;
	margin-left: 10rpx;
}

.flex-bt {
	justify-content: space-between;
}
</style>
