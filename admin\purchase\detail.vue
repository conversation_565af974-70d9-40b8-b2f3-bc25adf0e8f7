<template>
<view class="container">
	<view class="order-header">
		<view class="order-status" :style="{backgroundColor: getStatusColor(order.status)}">
			<text>{{getStatusText(order.status)}}</text>
		</view>
		<view class="order-basic">
			<view class="info-item">
				<text class="label">订单号：</text>
				<text class="value">{{order.order_no}}</text>
			</view>
			<view class="info-item">
				<text class="label">创建时间：</text>
				<text class="value">{{order.create_time}}</text>
			</view>
		</view>
	</view>

	<view class="section-title">商品信息</view>
	<view class="product-list">
		<view class="product-item" v-for="(item, index) in order.items" :key="index">
			<image class="product-img" :src="item.product_pic || (item.product_detail ? item.product_detail.pic : '')" mode="aspectFill"></image>
			<view class="product-info">
				<text class="product-name">{{item.product_name || (item.product_detail ? item.product_detail.name : '')}}</text>
				<view class="product-meta">
					<text>单价：￥{{item.price || (item.product_detail ? (item.product_detail.sell_price || item.product_detail.cost_price) : 0)}}</text>
					<text>数量：{{item.quantity}}</text>
				</view>
				<view class="product-meta" v-if="item.available_for_return !== undefined">
					<text>可退数量：{{item.available_for_return}}</text>
				</view>
				<text class="product-subtotal">小计：￥{{((item.price || (item.product_detail ? (item.product_detail.sell_price || item.product_detail.cost_price) : 0)) * item.quantity).toFixed(2)}}</text>
			</view>
		</view>
	</view>

	<view class="section-title">订单信息</view>
	<view class="order-detail">
		<view class="detail-item">
			<text class="label">总金额</text>
			<text class="value highlight">￥{{order.total_price}}</text>
		</view>
		<view class="detail-item" v-if="order.status > 0">
			<text class="label">审核时间</text>
			<text class="value">{{order.audit_time || '-'}}</text>
		</view>
		<view class="detail-item" v-if="order.status > 0">
			<text class="label">审核备注</text>
			<text class="value">{{order.audit_remark || '无'}}</text>
		</view>
		<view class="detail-item">
			<text class="label">备注</text>
			<text class="value">{{order.remark || '无'}}</text>
		</view>
	</view>

	<!-- 管理员审核操作按钮，只在管理员身份且状态为待审核时显示 -->
	<view class="action-bar" v-if="isAdmin && order.status == 0">
		<view class="action-title">审核操作</view>
		<view class="audit-form">
			<view class="form-item">
				<text class="form-label">审核备注</text>
				<textarea class="form-input" v-model="auditRemark" placeholder="请输入审核备注"></textarea>
			</view>
		</view>
		<view class="action-buttons">
			<view class="btn danger" @tap="auditOrder" data-status="2">驳回</view>
			<view class="btn primary" @tap="auditOrder" data-status="1">通过</view>
		</view>
	</view>

	<!-- 操作按钮 -->
	<view class="bottom-action" v-if="!isAdmin && order.status == 1">
		<view class="btn" @tap="completeOrder">确认收货</view>
	</view>

	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      id: 0,
      order: {
        items: []
      },
      loading: true,
      isAdmin: false,
      auditRemark: ''
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.id = this.opt.id || 0;
    
    // 判断是否为管理员
    this.isAdmin = app.globalData.isAdmin || false;
    
    this.getOrderDetail();
  },
  onPullDownRefresh: function () {
    this.getOrderDetail();
  },
  methods: {
    getOrderDetail: function () {
      var that = this;
      that.loading = true;
      
      app.post('ApiAdminPurchase/getPurchaseOrderDetail', {
        id: that.id
      }, function (res) {
        that.loading = false;
        
        if (res.code === 0) {
          // 处理API返回的数据结构
          if (res.data.order && res.data.items) {
            // API返回了拆分的数据结构
            var orderData = res.data.order;
            var itemsData = res.data.items;
            
            // 处理商品数据，确保数据完整性
            itemsData.forEach(function(item) {
              // 如果没有product_pic但有product_detail，使用product_detail的pic
              if (!item.product_pic && item.product_detail && item.product_detail.pic) {
                item.product_pic = item.product_detail.pic;
              }
              
              // 如果没有product_name但有product_detail，使用product_detail的name
              if (!item.product_name && item.product_detail && item.product_detail.name) {
                item.product_name = item.product_detail.name;
              }
              
              // 如果没有price但有product_detail，使用product_detail的sell_price或cost_price
              if (!item.price && item.product_detail) {
                item.price = item.product_detail.sell_price || item.product_detail.cost_price;
              }
            });
            
            // 将items合并到order对象中
            orderData.items = itemsData;
            that.order = orderData;
          } else if (res.data.items) {
            // 只有items是单独的
            that.order = res.data;
            that.order.items = res.data.items;
            
            // 同样处理商品数据
            that.order.items.forEach(function(item) {
              if (!item.product_pic && item.product_detail && item.product_detail.pic) {
                item.product_pic = item.product_detail.pic;
              }
              if (!item.product_name && item.product_detail && item.product_detail.name) {
                item.product_name = item.product_detail.name;
              }
              if (!item.price && item.product_detail) {
                item.price = item.product_detail.sell_price || item.product_detail.cost_price;
              }
            });
          } else {
            // 直接返回了完整对象
            that.order = res.data;
            // 确保items存在
            if (!that.order.items) {
              that.order.items = [];
            }
          }
          
          // 控制台输出方便调试
          console.log('处理后的订单数据:', that.order);
        } else {
          app.error(res.msg);
        }
      });
    },
    getStatusText: function(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回',
        3: '已完成'
      };
      return statusMap[status] || '未知状态';
    },
    getStatusColor: function(status) {
      const colorMap = {
        0: '#FF9800', // 待审核 - 橙色
        1: '#4CAF50', // 已通过 - 绿色
        2: '#F44336', // 已驳回 - 红色
        3: '#2196F3'  // 已完成 - 蓝色
      };
      return colorMap[status] || '#999999';
    },
    auditOrder: function(e) {
      var that = this;
      var status = e.currentTarget.dataset.status;
      
      if (!that.auditRemark.trim()) {
        app.error('请输入审核备注');
        return;
      }
      
      app.confirm('确定要' + (status == 1 ? '通过' : '驳回') + '此订单吗?', function () {
        app.post('ApiAdminPurchase/auditPurchaseOrder', {
          id: that.id,
          status: status,
          audit_remark: that.auditRemark
        }, function (res) {
          if (res.code === 0) {
            app.success('操作成功');
            setTimeout(function() {
              that.getOrderDetail();
            }, 1000);
          } else {
            app.error(res.msg);
          }
        });
      });
    },
    completeOrder: function() {
      var that = this;
      
      app.confirm('确认已收到货物吗?', function () {
        app.post('ApiAdminPurchase/completePurchaseOrder', {
          id: that.id
        }, function (res) {
          if (res.code === 0) {
            app.success('操作成功');
            setTimeout(function() {
              that.getOrderDetail();
            }, 1000);
          } else {
            app.error(res.msg);
          }
        });
      });
    }
  }
};
</script>

<style>
.container{ width:100%; padding-bottom:100rpx; }

.order-header{ width:100%; background:#fff; margin-bottom:20rpx; }
.order-status{ padding:30rpx; color:#fff; font-size:32rpx; font-weight:bold; }
.order-basic{ padding:20rpx 30rpx; }
.info-item{ display:flex; margin-bottom:10rpx; }
.info-item .label{ width:160rpx; font-size:28rpx; color:#666; }
.info-item .value{ flex:1; font-size:28rpx; color:#333; }

.section-title{ padding:20rpx 30rpx; font-size:30rpx; font-weight:bold; color:#333; background:#f5f5f5; }

.product-list{ background:#fff; padding:10rpx 20rpx; }
.product-item{ display:flex; padding:20rpx 0; border-bottom:1px solid #f0f0f0; }
.product-item:last-child{ border-bottom:none; }
.product-img{ width:160rpx; height:160rpx; border-radius:8rpx; }
.product-info{ flex:1; margin-left:20rpx; display:flex; flex-direction:column; justify-content:space-between; }
.product-name{ font-size:28rpx; color:#333; margin-bottom:10rpx; line-height:1.3; }
.product-meta{ display:flex; justify-content:space-between; font-size:24rpx; color:#666; margin-bottom:10rpx; }
.product-subtotal{ font-size:26rpx; color:#ff6b00; align-self:flex-end; }

.order-detail{ background:#fff; padding:20rpx 30rpx; }
.detail-item{ display:flex; justify-content:space-between; padding:15rpx 0; border-bottom:1px solid #f5f5f5; }
.detail-item:last-child{ border-bottom:none; }
.detail-item .label{ font-size:28rpx; color:#666; }
.detail-item .value{ font-size:28rpx; color:#333; }
.detail-item .highlight{ color:#ff6b00; font-weight:bold; font-size:32rpx; }

.action-bar{ background:#fff; margin-top:20rpx; padding:20rpx 30rpx; }
.action-title{ font-size:30rpx; font-weight:bold; color:#333; margin-bottom:20rpx; }
.audit-form{ margin-bottom:20rpx; }
.form-item{ margin-bottom:15rpx; }
.form-label{ display:block; font-size:28rpx; color:#666; margin-bottom:10rpx; }
.form-input{ width:100%; height:150rpx; background:#f5f5f5; border-radius:8rpx; padding:15rpx; font-size:28rpx; box-sizing:border-box; }
.action-buttons{ display:flex; justify-content:flex-end; }
.btn{ padding:15rpx 40rpx; font-size:28rpx; background:#f5f5f5; color:#333; border-radius:30rpx; margin-left:20rpx; }
.btn.primary{ background:#1989fa; color:#fff; }
.btn.danger{ background:#ff5252; color:#fff; }

.bottom-action{ position:fixed; bottom:0; left:0; width:100%; padding:20rpx; background:#fff; border-top:1px solid #f0f0f0; display:flex; justify-content:flex-end; }
.bottom-action .btn{ width:240rpx; text-align:center; background:#1989fa; color:#fff; }
</style> 