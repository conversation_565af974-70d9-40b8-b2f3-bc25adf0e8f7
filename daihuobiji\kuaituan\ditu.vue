<template>
	<view>
		
		<view style="display: flex;justify-content: space-between;align-items: center;padding: 20px;background: #fff;">
			
			<view>
				
				<view>您当前所在位置</view>
				
				<view>*****小区</view>
				
			</view>
			
			<view style="background: #58A27E;color: #fff;padding: 5px 10px;border-radius: 50px;">
				使用定位
			</view>
			
		</view>
		
		<map class="aroundmap" :style="'height:'+screenHeight+'px'" scale="15" :longitude="112.939000"
			:latitude="28.228700" :markers="markers"></map>
		
		<view style="position: fixed;z-index: 100;bottom: 0;width: 100%;background: #fff;padding: 15px;">
			
			<view style="display: flex;align-items: center;justify-content: space-between;">
				
				<text>上一页</text>
				
				<image style="width: 20px;height: 20px;" src="../../static/img/arrowdown.png" @click="openZhe"></image>
				
				<text>下一页</text>
				
			</view>
			
			<view style="background: #eee;padding: 5px;border-radius: 5px;margin: 10px 0;width: 100%;display: flex;text-align: center;" v-if="zhedie">
				
				<view style="width: 60%;">小区名称</view>
				
				<view style="width: 40%;">服务人员</view>
				
			</view>
			
			<scroll-view scroll-y  v-if="zhedie">
				
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 0 20px;">
				
					<view >放大法嘎嘎</view>
					
					<view style="display: flex;align-items: center;">
						
						<view>高德</view>
						
						<image style="width: 30px;height: 30px;margin-left: 10px;" src="../../static/img/tel2.png"></image> 
						
					</view>
				
				</view>
				
			</scroll-view>
			
			
		</view>
		
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				screenHeight : '',
				markers: [{
					latitude: 28.228700,
					longitude: 112.939000,
					iconPath: '../../static/img/address.png',
				}],
				zhedie : true
			};
		},
		
		onLoad() {
			
			const systemInfo = uni.getSystemInfoSync();
			
			this.screenHeight = systemInfo.windowHeight;
		},
	  methods: {
		  openZhe(){
			  this.zhedie = !this.zhedie
			  
		  }
		  
	  }
		
	}
</script>

<style lang="less">

	.aroundmap {
		width: 100%;
		border-radius: 15rpx;
		position: relative;
		z-index: 8;
	}

</style>
