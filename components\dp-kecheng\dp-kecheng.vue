<template>
<view class="dp-product" :style="{
	backgroundColor:params.bgcolor,
	margin:params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0',
	padding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx',
	width:'calc(100% - '+params.margin_x*2.2*2+'rpx)'
}">
	<!--123排-->
	<dp-kecheng-item v-if="params.style=='1' || params.style=='2' || params.style=='3'" :showstyle="params.style" :data="data" :saleimg="params.saleimg" :showname="params.showname" :showprice="params.showprice" :showsales="params.showsales" idfield="proid" :menuindex="menuindex"></dp-kecheng-item>
	<!--横排-->
	<dp-kecheng-itemlist v-if="params.style=='list'" :data="data" :saleimg="params.saleimg" :showname="params.showname" :showprice="params.showprice" :showsales="params.showsales" idfield="proid" :menuindex="menuindex"></dp-kecheng-itemlist>
	<!--推荐样式-->
	<dp-kecheng-recommend v-if="params.style=='recommend'" :params="params" :data="data" :saleimg="params.saleimg" :showname="params.showname" :showprice="params.showprice" :showsales="params.showsales" idfield="proid" :menuindex="menuindex" :sysset="sysset" @getdata="handleRecommendRefresh"></dp-kecheng-recommend>
	<!--精品样式-->
	<dp-kecheng-boutique v-if="params.style=='boutique'" :params="params" :data="data" :saleimg="params.saleimg" :showname="params.showname" :showprice="params.showprice" :showsales="params.showsales" idfield="proid" :menuindex="menuindex" :sysset="sysset" @getdata="handleBoutiqueRefresh"></dp-kecheng-boutique>
</view>
</template>
<script>
	export default {
		props: {
			menuindex:{default:-1},
			params:{},
			data:{},
			sysset:{ type: Object, default: () => ({}) }
		},
		data() {
			return {
				// 记录原始数据
				originalData: [],
				// 记录推荐样式的刷新次数
				recommendRefreshCount: 0,
				// 记录精品样式的刷新次数
				boutiqueRefreshCount: 0,
				// 当前平台
				currentPlatform: '',
				// 组件ID
				componentId: ''
			}
		},
		created() {
			// 保存原始数据
			this.originalData = JSON.parse(JSON.stringify(this.data || []));
			
			// 获取当前平台
			// #ifdef MP-WEIXIN
			this.currentPlatform = 'mp-weixin';
			// #endif
			
			// #ifdef H5
			this.currentPlatform = 'h5';
			// #endif
			
			// 保存组件ID（在小程序环境中很重要）
			this.componentId = this.$attrs.id || '';
			console.log('组件ID:', this.componentId, '样式:', this.params.style);
		},
		methods: {
			getdata(event) {
				console.log('dp-kecheng接收到事件:', JSON.stringify(event));
				
				// 处理从子组件传递的刷新类型
				if (event && event.refreshType) {
					const refreshType = event.refreshType;
					console.log('收到刷新类型:', refreshType);
					
					// #ifdef MP-WEIXIN
					console.log('小程序环境：直接传递事件到父组件');
					// 确保在小程序环境中传递完整的信息
					this.$emit('getdata', {
						id: this.componentId, // 使用组件自己的ID
						style: refreshType,   // 使用子组件传来的样式
						forceRefresh: true,
						_timestamp: new Date().getTime()
					});
					return;
					// #endif
					
					// 其他环境使用常规处理方式
					if (refreshType === 'recommend') {
						this.handleRecommendRefresh();
					} else if (refreshType === 'boutique') {
						this.handleBoutiqueRefresh();
					} else {
						// 默认传递到父组件
						this.$emit('getdata');
					}
					return;
				}
				
				// 向上传递刷新数据事件
				this.$emit('getdata');
			},
			handleRecommendRefresh() {
				// 增加推荐样式刷新次数
				this.recommendRefreshCount++;
				
				// 如果数据不足，不做操作
				if (!this.originalData || this.originalData.length <= 3) {
					// 向上传递事件
					this.$emit('getdata', {
						id: this.$attrs.id || '',
						style: 'recommend',
						_timestamp: new Date().getTime()
					});
					return;
				}
				
				// 本地模拟数据刷新 - 随机排序数据
				const shuffled = [...this.originalData].sort(() => 0.5 - Math.random());
				
				// 创建一个新的数组并通过Vue的变更检测更新
				const newData = shuffled.slice(0, this.originalData.length);
				
				if (this.params.style === 'recommend') {
					// 不同平台使用不同的刷新策略
					// #ifdef MP-WEIXIN
					// 小程序环境直接走网络请求刷新
					console.log('小程序环境：通过网络请求刷新数据');
					this.$emit('getdata', {
						id: this.$attrs.id || '',
						style: 'recommend',
						forceRefresh: true,
						_timestamp: new Date().getTime()
					});
					// #endif
					
					// H5环境可以直接操作数据
					// #ifdef H5
					console.log('H5环境：本地刷新推荐样式数据');
					try {
						// 尝试直接修改父组件数据
						this.$parent.$parent.pagecontent.forEach(item => {
							if (item.id === this.$attrs.id) {
								item.data = newData;
								console.log('本地刷新推荐样式数据成功');
							}
						});
					} catch (error) {
						console.error('本地刷新数据失败，使用网络请求刷新:', error);
						this.$emit('getdata', {
							id: this.$attrs.id || '',
							style: 'recommend',
							forceRefresh: true,
							_timestamp: new Date().getTime()
						});
					}
					// #endif
					
					// 其他平台也使用请求刷新
					// #ifndef MP-WEIXIN || H5
					this.$emit('getdata', {
						id: this.$attrs.id || '',
						style: 'recommend',
						forceRefresh: true,
						_timestamp: new Date().getTime()
					});
					// #endif
				} else {
					// 不是推荐样式，向上传递事件
					this.$emit('getdata', {
						id: this.$attrs.id || '',
						style: 'recommend',
						_timestamp: new Date().getTime()
					});
				}
			},
			handleBoutiqueRefresh() {
				// 增加精品样式刷新次数
				this.boutiqueRefreshCount++;
				
				// 如果数据不足，不做操作
				if (!this.originalData || this.originalData.length <= 4) {
					// 向上传递事件
					this.$emit('getdata', {
						id: this.$attrs.id || '',
						style: 'boutique',
						_timestamp: new Date().getTime()
					});
					return;
				}
				
				// 本地模拟数据刷新 - 随机排序数据
				const shuffled = [...this.originalData].sort(() => 0.5 - Math.random());
				
				// 创建一个新的数组并通过Vue的变更检测更新
				const newData = shuffled.slice(0, this.originalData.length);
				
				if (this.params.style === 'boutique') {
					// 不同平台使用不同的刷新策略
					// #ifdef MP-WEIXIN
					// 小程序环境直接走网络请求刷新
					console.log('小程序环境：通过网络请求刷新数据');
					this.$emit('getdata', {
						id: this.$attrs.id || '',
						style: 'boutique',
						forceRefresh: true,
						_timestamp: new Date().getTime()
					});
					// #endif
					
					// H5环境可以直接操作数据
					// #ifdef H5
					console.log('H5环境：本地刷新精品样式数据');
					try {
						// 尝试直接修改父组件数据
						this.$parent.$parent.pagecontent.forEach(item => {
							if (item.id === this.$attrs.id) {
								item.data = newData;
								console.log('本地刷新精品样式数据成功');
							}
						});
					} catch (error) {
						console.error('本地刷新数据失败，使用网络请求刷新:', error);
						this.$emit('getdata', {
							id: this.$attrs.id || '',
							style: 'boutique',
							forceRefresh: true,
							_timestamp: new Date().getTime()
						});
					}
					// #endif
					
					// 其他平台也使用请求刷新
					// #ifndef MP-WEIXIN || H5
					this.$emit('getdata', {
						id: this.$attrs.id || '',
						style: 'boutique',
						forceRefresh: true,
						_timestamp: new Date().getTime()
					});
					// #endif
				} else {
					// 不是精品样式，向上传递事件
					this.$emit('getdata', {
						id: this.$attrs.id || '',
						style: 'boutique',
						_timestamp: new Date().getTime()
					});
				}
			}
		}
	}
</script>
<style>
.dp-product{width:100%;height: auto; position: relative;overflow: hidden; padding: 0px; background: #fff;}
</style>