<template>
  <view class="page">
    <!-- 加载中 -->
    <loading v-if="loading"></loading>
    
    <view class="form-container">
      <!-- 分类选择 -->
      <view class="form-item">
        <text class="label required">选择分类</text>
        <picker 
          @change="bindCategoryChange" 
          :value="categoryIndex" 
          :range="categories" 
          range-key="name"
        >
          <view class="picker">
            <text v-if="categoryIndex > -1">{{categories[categoryIndex].name}}</text>
            <text v-else class="placeholder">请选择分类</text>
            <text class="arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 标题输入 -->
      <view class="form-item">
        <text class="label required">{{type === 1 ? '采购标题' : '供应标题'}}</text>
        <input 
          type="text"
          v-model="formData.title"
          :placeholder="type === 1 ? '请输入您要采购的商品标题' : '请输入您要供应的商品标题'"
          class="input"
        />
      </view>

      <!-- 内容输入 -->
      <view class="form-item">
        <text class="label required">详细说明</text>
        <textarea
          v-model="formData.content"
          :placeholder="type === 1 ? '请详细描述您的采购需求，包括商品规格、质量要求等' : '请详细描述您的供应商品，包括商品规格、质量等级等'"
          class="textarea"
          maxlength="500"
        />
        <text class="word-count">{{formData.content.length}}/500</text>
      </view>

      <!-- 采购数量/供应库存 -->
      <view class="form-item">
        <text class="label required">{{type === 1 ? '采购数量' : '供应库存'}}</text>
        <input 
          type="number"
          v-model="formData.quantity"
          :placeholder="type === 1 ? '请输入采购数量' : '请输入供应库存'"
          class="input"
        />
      </view>

      <!-- 期望单价/供应单价 -->
      <view class="form-item">
        <text class="label">{{type === 1 ? '期望单价' : '供应单价'}}</text>
        <view class="price-input">
          <text class="price-symbol">¥</text>
          <input 
            type="digit"
            v-model="formData.price"
            :placeholder="type === 1 ? '请输入期望单价(选填)' : '请输入供应单价(选填)'"
            class="input"
          />
          <text class="price-unit">/件</text>
        </view>
        <text class="price-tip" v-if="type === 1">填写期望单价可以帮助供应商更好地了解您的预算</text>
      </view>

      <!-- 交货期/发货期 -->
      <view class="form-item" v-if="type === 1">
        <text class="label">期望交货日期</text>
        <picker 
          mode="date" 
          :value="formData.delivery_date" 
          :start="minDate"
          @change="bindDateChange"
        >
          <view class="picker">
            <text v-if="formData.delivery_date">{{formData.delivery_date}}</text>
            <text v-else class="placeholder">请选择期望交货日期(选填)</text>
            <text class="arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 发货地 -->
      <view class="form-item" v-if="type === 2">
        <text class="label required">发货地</text>
        <input 
          type="text"
          v-model="formData.shipping_address"
          placeholder="请输入发货地"
          class="input"
        />
      </view>

      <!-- 联系人 -->
      <view class="form-item">
        <text class="label required">联系人</text>
        <input 
          type="text"
          v-model="formData.contact_name"
          placeholder="请输入联系人姓名"
          class="input"
        />
      </view>

      <!-- 联系电话 -->
      <view class="form-item">
        <text class="label required">联系电话</text>
        <input 
          type="number"
          v-model="formData.contact_phone"
          placeholder="请输入联系电话"
          class="input"
        />
      </view>

      <!-- 备注信息 -->
      <view class="form-item">
        <text class="label">补充说明</text>
        <textarea
          v-model="formData.remark"
          :placeholder="type === 1 ? '其他采购要求说明(选填)' : '其他供应说明(选填)'"
          class="textarea"
          maxlength="200"
        />
        <text class="word-count">{{formData.remark.length}}/200</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn" @tap="submitForm">
      <text class="btn-text">{{type === 1 ? '发布采购' : '发布供应'}}</text>
    </view>

    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      loading: false,
      type: 1, // 1采购 2供应
      categories: [], // 分类列表
      categoryIndex: -1, // 选中的分类索引
      minDate: this.getMinDate(), // 最小可选日期
      formData: {
        title: '',
        content: '',
        cid: '',
        contact_name: '',
        contact_phone: '',
        price: '',
        quantity: '',
        delivery_date: '', // 交货日期
        shipping_address: '', // 发货地
        remark: '' // 备注
      }
    }
  },
  onLoad(options) {
    // 设置类型
    if(options.type === 'gongying') {
      this.type = 2;
    } else if(options.type === 'qiugou') {
      this.type = 1;
    }
    // 设置标题
    uni.setNavigationBarTitle({
      title: this.type === 1 ? '发布采购' : '发布供应'
    });
    // 获取分类
    this.getCategories();
  },
  methods: {
    // 获取最小可选日期(今天)
    getMinDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 获取分类列表
    getCategories() {
      var that = this;
      that.loading = true;
      app.get('ApiCaigou/category', {}, function(res) {
        that.loading = false;
        if(res.status === 1) {
          that.categories = res.data || [];
        } else {
          app.error(res.msg || '获取分类失败');
        }
      }, function(err) {
        that.loading = false;
        app.error('获取分类失败');
      });
    },

    // 选择分类
    bindCategoryChange(e) {
      this.categoryIndex = e.detail.value;
      this.formData.cid = this.categories[this.categoryIndex].id;
    },

    // 选择日期
    bindDateChange(e) {
      this.formData.delivery_date = e.detail.value;
    },

    // 表单验证
    validateForm() {
      if(!this.formData.cid) {
        app.error('请选择分类');
        return false;
      }
      if(!this.formData.title) {
        app.error('请输入标题');
        return false;
      }
      if(!this.formData.content) {
        app.error('请输入详细说明');
        return false;
      }
      if(!this.formData.quantity) {
        app.error(this.type === 1 ? '请输入采购数量' : '请输入供应库存');
        return false;
      }
      if(this.type === 2 && !this.formData.shipping_address) {
        app.error('请输入发货地');
        return false;
      }
      if(!this.formData.contact_name) {
        app.error('请输入联系人');
        return false;
      }
      if(!this.formData.contact_phone) {
        app.error('请输入联系电话');
        return false;
      }
      // 验证手机号格式
      if(!/^1[3-9]\d{9}$/.test(this.formData.contact_phone)) {
        app.error('请输入正确的手机号');
        return false;
      }
      return true;
    },

    // 提交表单
    submitForm() {
      if(!this.validateForm()) return;

      var that = this;
      that.loading = true;

      // 构建请求参数
      const params = {
        type: that.type,
        ...that.formData
      };

      app.post('ApiCaigou/publish', params, function(res) {
        that.loading = false;
        if(res.status === 1) {
          app.success('发布成功');
          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          app.error(res.msg || '发布失败');
        }
      }, function(err) {
        that.loading = false;
        app.error('发布失败');
      });
    }
  }
}
</script>

<style>
.page {
  min-height: 100vh;
  background: #f6f7f9;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.form-container {
  padding: 24rpx;
}

.form-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required::before {
  content: '*';
  color: #ff4757;
  margin-right: 8rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  background: #f5f6fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #2c3e50;
}

.textarea {
  width: 100%;
  height: 240rpx;
  background: #f5f6fa;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #2c3e50;
}

.word-count {
  font-size: 24rpx;
  color: #95a5a6;
  text-align: right;
  margin-top: 8rpx;
}

.picker {
  width: 100%;
  height: 80rpx;
  background: #f5f6fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.placeholder {
  font-size: 28rpx;
  color: #95a5a6;
}

.arrow {
  font-size: 28rpx;
  color: #95a5a6;
  transform: rotate(90deg);
}

/* 价格输入框样式 */
.price-input {
  display: flex;
  align-items: center;
  background: #f5f6fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
}

.price-symbol {
  font-size: 32rpx;
  color: #2c3e50;
  margin-right: 8rpx;
}

.price-unit {
  font-size: 28rpx;
  color: #95a5a6;
  margin-left: 8rpx;
}

.price-tip {
  font-size: 24rpx;
  color: #95a5a6;
  margin-top: 8rpx;
}

.submit-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 20rpx rgba(0,0,0,0.05);
}

.submit-btn {
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 32rpx;
  transition: opacity 0.3s ease;
}

.submit-btn:active {
  opacity: 0.8;
}

.btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}
</style>