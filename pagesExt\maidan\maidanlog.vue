<template>
<view class="container">
	<block v-if="isload">
		<!-- 搜索容器 -->
		<view class="search-container">
			<view class="topsearch flex-y-center">
				<view class="f1 flex-y-center">
					<image class="img" :src="pre_url+'/static/img/search_ico.png'"></image>
					<input :value="keyword" placeholder="搜索订单号、商家名称" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" @confirm="searchConfirm"></input>
				</view>
			</view>

			<!-- 筛选导航栏 -->
			<view class="filter-navbar">
				<view @tap="filterClick" class="filter-item" :style="status==''?'color:'+t('color1'):''" data-status="">全部</view>
				<view @tap="filterClick" class="filter-item" :style="status=='1'?'color:'+t('color1'):''" data-status="1">已完成</view>
				<view @tap="filterClick" class="filter-item" :style="status=='0'?'color:'+t('color1'):''" data-status="0">待处理</view>
				<view @tap="sortClick" class="filter-item" data-field="money" :data-order="order=='asc'?'desc':'asc'">
					<text :style="field=='money'?'color:'+t('color1'):''">金额排序</text>
					<text class="iconfont iconshangla" :style="field=='money'&&order=='asc'?'color:'+t('color1'):''"></text>
					<text class="iconfont icondaoxu" :style="field=='money'&&order=='desc'?'color:'+t('color1'):''"></text>
				</view>
			</view>
		</view>

		<!-- 订单列表 -->
		<view class="order-list" id="datalist">
			<block v-for="(item, index) in datalist" :key="index">
			<view class="order-item" @tap="goto" :data-url="'maidandetail?id=' + item.id">
				<!-- 订单头部信息 -->
				<view class="order-header">
					<view class="order-time">{{item.paytime}}</view>
					<view class="order-status" :style="{color:item.status==1?t('color1'):'#999'}">
						{{item.status==1?'已完成':'待处理'}}
					</view>
				</view>

				<!-- 订单主体信息 -->
				<view class="order-body">
					<view class="order-info">
						<view class="order-title">{{item.title}}</view>
						<view class="order-detail">
							<text class="detail-item">订单号：{{item.ordernum}}</text>
							<text class="detail-item">支付方式：{{item.paytype}}</text>
						</view>
						<!-- 奖励信息 -->
						<view class="reward-info" v-if="item.paidui_jiang && parseFloat(item.paidui_jiang) > 0">
							<text class="reward-label">排队奖励：</text>
							<text class="reward-amount" :style="{color:t('color1')}">+￥{{item.paidui_jiang}}</text>
						</view>
					</view>
					<view class="order-amount">
						<text class="amount-text">-￥{{item.money}}</text>
						<text class="amount-label">支付金额</text>
					</view>
				</view>

				<!-- 订单底部信息 -->
				<!-- <view class="order-footer" v-if="item.tuozhanfei && parseFloat(item.tuozhanfei) > 0">
					<text class="fee-info">拓展费：￥{{item.tuozhanfei}}</text>
				</view> -->
			</view>
			</block>
		</view>
	</block>
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

			pre_url:app.globalData.pre_url,
      field: 'id',
			order:'desc',
      status: '',
      datalist: [],
      pagenum: 1,
			nodata:false,
      nomore: false,
			keyword:'',
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
	onNavigationBarSearchInputConfirmed:function(e){
		this.searchConfirm({detail:{value:e.text}});
	},
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
			this.nodata = false;
			this.nomore = false;
			this.loading = true;
      app.post('ApiMaidan/maidanlog', {
				pagenum: pagenum,
				keyword:that.keyword,
				status:that.status,
				field:that.field,
				order:that.order
			}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
        var data = res.data;
        if (pagenum == 1) {
					that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
    filterClick: function (e) {
      var that = this;
      var status = e.currentTarget.dataset.status;
      that.status = status;
      that.pagenum = 1;
      that.datalist = [];
      that.getdata();
    },
    sortClick: function (e) {
      var that = this;
      var t = e.currentTarget.dataset;
      that.field = t.field;
      that.order = t.order;
      that.getdata();
    },
		searchConfirm:function(e){
			this.keyword = e.detail.value;
			this.pagenum = 1;
			this.datalist = [];
      this.getdata(false);
		}
  }
};
</script>
<style>
/* 搜索容器样式 */
.search-container {
	position: fixed;
	width: 100%;
	background: #fff;
	z-index: 9;
	top: var(--window-top);
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.topsearch {
	width: 100%;
	padding: 16rpx 20rpx;
}

.topsearch .f1 {
	height: 60rpx;
	border-radius: 30rpx;
	border: 0;
	background-color: #f7f7f7;
	flex: 1;
}

.topsearch .f1 .img {
	width: 24rpx;
	height: 24rpx;
	margin-left: 10px;
}

.topsearch .f1 input {
	height: 100%;
	flex: 1;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
}

/* 筛选导航栏样式 */
.filter-navbar {
	display: flex;
	text-align: center;
	align-items: center;
	padding: 5rpx 0;
	border-top: 1rpx solid #f5f5f5;
}

.filter-item {
	flex: 1;
	height: 70rpx;
	line-height: 70rpx;
	position: relative;
	font-size: 26rpx;
	font-weight: bold;
	color: #323232;
	padding: 0 5rpx;
}

.filter-item .iconshangla {
	position: absolute;
	top: -4rpx;
	padding: 0 6rpx;
	font-size: 20rpx;
	color: #7D7D7D;
}

.filter-item .icondaoxu {
	position: absolute;
	top: 8rpx;
	padding: 0 6rpx;
	font-size: 20rpx;
	color: #7D7D7D;
}

/* 订单列表样式 */
.order-list {
	width: 100%;
	margin-top: 200rpx;
	padding: 0 24rpx;
}

.order-item {
	width: 100%;
	background: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

/* 订单头部样式 */
.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx 16rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.order-time {
	font-size: 26rpx;
	color: #666;
}

.order-status {
	font-size: 24rpx;
	font-weight: bold;
}

/* 订单主体样式 */
.order-body {
	display: flex;
	padding: 20rpx 24rpx;
	align-items: flex-start;
}

.order-info {
	flex: 1;
	margin-right: 20rpx;
}

.order-title {
	font-size: 30rpx;
	color: #222;
	font-weight: bold;
	line-height: 42rpx;
	margin-bottom: 12rpx;
}

.order-detail {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.detail-item {
	font-size: 24rpx;
	color: #666;
	line-height: 34rpx;
}

.reward-info {
	margin-top: 12rpx;
	padding: 8rpx 12rpx;
	background: rgba(255,107,107,0.1);
	border-radius: 8rpx;
	display: flex;
	align-items: center;
}

.reward-label {
	font-size: 22rpx;
	color: #666;
}

.reward-amount {
	font-size: 24rpx;
	font-weight: bold;
	margin-left: 8rpx;
}

.order-amount {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	text-align: right;
}

.amount-text {
	font-size: 32rpx;
	color: #ff4757;
	font-weight: bold;
	line-height: 44rpx;
}

.amount-label {
	font-size: 22rpx;
	color: #999;
	margin-top: 4rpx;
}

/* 订单底部样式 */
.order-footer {
	padding: 12rpx 24rpx 20rpx;
	border-top: 1rpx solid #f5f5f5;
}

.fee-info {
	font-size: 22rpx;
	color: #999;
}
</style>