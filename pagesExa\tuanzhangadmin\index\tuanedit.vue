<template>
	<view>
		<block>

			<form @submit="subform">

				<view class="form-box">
					<view class="form-item flex-col">
						<text>商品详情</text>
						<view class="detailop">
							<view class="btn" @tap="detailAddtxt">+文本</view>
							<view class="btn" @tap="detailAddpic">+图片</view>
							<view class="btn" @tap="detailBiji">+选择笔记</view>
						</view>
						<view>
							<block v-for="(setData, index) in pagecontent" :key="index">
								<view class="detaildp">
									<view class="op">
										<view class="flex1"></view>
										<view class="btn" @tap="detailMoveup" :data-index="index">上移</view>
										<view class="btn" @tap="detailMovedown" :data-index="index">下移</view>
										<view class="btn" @tap="detailMovedel" :data-index="index">删除</view>
									</view>
									<view class="detailbox">
										<block v-if="setData.temp=='notice'">
											<dp-notice :params="setData.params" :data="setData.data"></dp-notice>
										</block>
										<block v-if="setData.temp=='banner'">
											<dp-banner :params="setData.params" :data="setData.data"></dp-banner>
										</block>
										<block v-if="setData.temp=='search'">
											<dp-search :params="setData.params" :data="setData.data"></dp-search>
										</block>
										<block v-if="setData.temp=='text'">
											<dp-text :params="setData.params" :data="setData.data"></dp-text>
										</block>
										<block v-if="setData.temp=='title'">
											<dp-title :params="setData.params" :data="setData.data"></dp-title>
										</block>
										<block v-if="setData.temp=='dhlist'">
											<dp-dhlist :params="setData.params" :data="setData.data"></dp-dhlist>
										</block>
										<block v-if="setData.temp=='line'">
											<dp-line :params="setData.params" :data="setData.data"></dp-line>
										</block>
										<block v-if="setData.temp=='blank'">
											<dp-blank :params="setData.params" :data="setData.data"></dp-blank>
										</block>
										<block v-if="setData.temp=='menu'">
											<dp-menu :params="setData.params" :data="setData.data"></dp-menu>
										</block>
										<block v-if="setData.temp=='map'">
											<dp-map :params="setData.params" :data="setData.data"></dp-map>
										</block>
										<block v-if="setData.temp=='cube'">
											<dp-cube :params="setData.params" :data="setData.data"></dp-cube>
										</block>
										<block v-if="setData.temp=='picture'">
											<dp-picture :params="setData.params" :data="setData.data"></dp-picture>
										</block>
										<block v-if="setData.temp=='pictures'">
											<dp-pictures :params="setData.params" :data="setData.data"></dp-pictures>
										</block>
										<block v-if="setData.temp=='video'">
											<dp-video :params="setData.params" :data="setData.data"></dp-video>
										</block>
										<block v-if="setData.temp=='shop'">
											<dp-shop :params="setData.params" :data="setData.data"
												:shopinfo="setData.shopinfo"></dp-shop>
										</block>
										<block v-if="setData.temp=='product'">
											<dp-product :params="setData.params" :data="setData.data"
												:menuindex="menuindex"></dp-product>
										</block>
										<block v-if="setData.temp=='collage'">
											<dp-collage :params="setData.params" :data="setData.data"></dp-collage>
										</block>
										<block v-if="setData.temp=='kanjia'">
											<dp-kanjia :params="setData.params" :data="setData.data"></dp-kanjia>
										</block>
										<block v-if="setData.temp=='seckill'">
											<dp-seckill :params="setData.params" :data="setData.data"></dp-seckill>
										</block>
										<block v-if="setData.temp=='scoreshop'">
											<dp-scoreshop :params="setData.params" :data="setData.data"></dp-scoreshop>
										</block>
										<block v-if="setData.temp=='coupon'">
											<dp-coupon :params="setData.params" :data="setData.data"></dp-coupon>
										</block>
										<block v-if="setData.temp=='article'">
											<dp-article :params="setData.params" :data="setData.data"></dp-article>
										</block>
										<block v-if="setData.temp=='business'">
											<dp-business :params="setData.params" :data="setData.data"></dp-business>
										</block>
										<block v-if="setData.temp=='liveroom'">
											<dp-liveroom :params="setData.params" :data="setData.data"></dp-liveroom>
										</block>
										<block v-if="setData.temp=='button'">
											<dp-button :params="setData.params" :data="setData.data"></dp-button>
										</block>
										<block v-if="setData.temp=='hotspot'">
											<dp-hotspot :params="setData.params" :data="setData.data"></dp-hotspot>
										</block>
										<block v-if="setData.temp=='cover'">
											<dp-cover :params="setData.params" :data="setData.data"></dp-cover>
										</block>
										<block v-if="setData.temp=='richtext'">
											<dp-richtext :params="setData.params" :data="setData.data"
												:content="setData.content"></dp-richtext>
										</block>
										<block v-if="setData.temp=='form'">
											<dp-form :params="setData.params" :data="setData.data"
												:content="setData.content"></dp-form>
										</block>
										<block v-if="setData.temp=='userinfo'">
											<dp-userinfo :params="setData.params" :data="setData.data"
												:content="setData.content"></dp-userinfo>
										</block>
									</view>
								</view>
							</block>
						</view>
					</view>
				</view>

				<button class="savebtn"
					:style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"
					form-type="submit">提交</button>
				<view style="height:50rpx"></view>
			</form>



			<view
				style="position: fixed;height: 400px;background: #eee;bottom: 0;z-index: 100;left: 0;width: 100%;border-radius: 10px 10px 0 0;"
				v-if="showModel">

				<view style="display: flex;justify-content: space-between;padding:10px 20px;">

					<image style="width: 15px;height: 15px;"></image>

					<view>选择笔记</view>

					<image style="width: 15px;height: 15px;" src="../../../static/img/close.png" @click="selectbiji">
					</image>

				</view>


			   <view class="search-bar">
					<view class="search-box">
						<view class="f1 flex-y-center">
							<image class="img" src="/static/img/search_ico.png"></image>
							<input :value="keyword" placeholder="搜索笔记"
								placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
						</view>
					</view>
				</view>

				<view class="uni-list">
	
					<view class="list" style="padding: 10px;">

						<view class="pbl" v-for="(item, j) in ls" :key="j"  @click="selectItem(item.id)">

							<view class="image" style="position: relative;min-height: 150px;">
								<image fade-show lazy-load :lazy-load-margin="0" mode="widthFix" :src="item.coverimg">
								</image>


								<image v-if="item.checked"
									style="position: absolute;right: 5px;top: 5px;width: 20px;height:20px;z-index: 100;"
									src="../../../static/highVoltage/checked.png"></image>

							</view>

							<rich-text class="p1" :nodes="item.content"></rich-text>

							<view
								style="display: flex;align-items: center;justify-content: space-between;padding: 10px;color: #aaa;">

								<view style="display: flex;align-items: center;width: 60%;">

									<img style="width: 20px;height: 20px;border-radius: 50px;"
										:src="item.headimg"></img>

									<view
										style="font-size: 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;margin-left: 5px;">
										{{item.nickname}}</view>

								</view>

								<view style="display: flex;align-items: center;">

									<image style="width: 12px;height: 12px;" src="../../../static/restaurant/like1.png">
									</image>

									<view style="font-size: 10px;margin-left: 5px;">{{item.readcount}}</view>

								</view>

							</view>


						</view>

					</view>

				</view>


				<button class="savebtn"
					:style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"
					@click="selectbiji">确定</button>

			</view>


			<uni-popup id="dialogDetailtxt" ref="dialogDetailtxt" type="dialog">
				<view class="uni-popup-dialog">
					<view class="uni-dialog-title">
						<text class="uni-dialog-title-text">请输入文本内容</text>
					</view>
					<view class="uni-dialog-content">
						<textarea value="" placeholder="请输入文本内容" @input="catcheDetailtxt"></textarea>
					</view>
					<view class="uni-dialog-button-group">
						<view class="uni-dialog-button" @click="dialogDetailtxtClose">
							<text class="uni-dialog-button-text">取消</text>
						</view>
						<view class="uni-dialog-button uni-border-left" @click="dialogDetailtxtConfirm">
							<text class="uni-dialog-button-text uni-button-color">确定</text>
						</view>
					</view>
					<view class="uni-popup-dialog__close" @click="dialogDetailtxtClose">
						<span class="uni-popup-dialog__close-icon "></span>
					</view>
				</view>
			</uni-popup>






		</block>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				isload: false,
				loading: false,
				pre_url: app.globalData.pre_url,
				info: {},
				pagecontent: [],
				aglevellist: [],
				levellist: [],
				clist: [],
				clist2: [],
				cateArr: [],
				cateArr2: [],
				glist: [],
				groupArr: [],
				freighttypeArr: ['全部模板', '指定模板', '自动发货', '在线卡密'],
				freightindex: 0,
				freightList: [],
				freightdata: [],
				freightIds: [],
				guigedata: [],
				pic: [],
				pics: [],
				cids: [],
				cids2: [],
				gids: [],
				xiaofeizhi: [],
				cnames: '',
				cnames2: '',
				gnames: '',
				clistshow: false,
				clist2show: false,
				glistshow: false,
				ggname: '',
				ggindex: 0,
				ggindex2: 0,
				oldgglist: [],
				gglist: [],
				catche_detailtxt: '',
				start_time1: '-选择日期-',
				start_time2: '-选择时间-',
				end_time1: '-选择日期-',
				end_time2: '-选择时间-',
				start_hours: '-开始时间-',
				end_hours: '-结束时间-',
				gettjArr: ['-1'],
				product_showset: 1,
				commission_canset: 1,
				bid: 0,
				paramList: [],
				paramdata: [],
				editorFormdata: [],
				test: '',

				ls: [],
				checks: [],
				showModel: false,
				keyword: ''
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			//this.getdata();
			this.getList();
		},
		methods: {
			getList() {
				let that = this
				app.post('Apidaihuobiji/ltlist', {keyword : that.keyword}, function(res) {

					let arr = [];
					res.datalist.filter((item,k)=>{
						
						item.checked = false 
						
						that.checks.filter(m=>{
							
							 if(item.id == m){
								 item.checked = true  
							 }
							
						})
						
						arr.push(item);
						
					})
					
					that.ls = arr;


				});
			},
			searchConfirm(e){
				this.keyword = e.detail.value;
				this.getList();
			},
			
			selectItem(id){
				
				let ind = -1;
				this.checks.filter((item,k)=>{
					if(item == id){
						ind = k;
					}
				
				})

				if(ind>-1){
					this.checks.splice(ind, 1);
				}else{
					this.checks.push(id);
				}
	
	            let arr = [];
				this.ls.filter((item,k)=>{
					
					this.checks.filter(m=>{
						
						 if(item.id == m){
							 item.checked = true  
						 }
					})
					
					if(ind>-1&&item.id == id){
						 item.checked = false  					 
					}
					
					arr.push(item);
	
				})

				this.ls = arr;

			},

			editorBindPickerChange: function(e) {
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var val = e.detail.value;
				var editorFormdata = this.editorFormdata;
				if (!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = val;
				console.log(editorFormdata)
				this.editorFormdata = editorFormdata;
				this.test = Math.random();

				var field = e.currentTarget.dataset.formidx;
				this.paramdata[field] = val;
			},
			setfield: function(e) {
				var field = e.currentTarget.dataset.formidx;
				var value = e.detail.value;
				this.paramdata[field] = value;
			},

			selectbiji() {
				this.showModel = false;
			},
			detailBiji() {
				this.showModel = true;
			},

			subform: function(e) {

				var that = this;
				var formdata = e.detail.value;

				var id = that.opt.id ? that.opt.id : '';
				
				console.log(that.checks.toString()) 

				app.post('ApituanzhangAdminIndex/adddaihuo', {
					id: id,
					info: formdata, 
					bijiid: that.checks.toString()
				}, function(res) {
					if (res.status == 0) {
						app.error(res.msg);
					} else {
						app.success(res.msg);
						setTimeout(function() {
							app.goto('index', 'redirect');
						}, 1000);
					}
				});

			},
			detailAddtxt: function() {
				this.$refs.dialogDetailtxt.open();
			},
			dialogDetailtxtClose: function() {
				this.$refs.dialogDetailtxt.close();
			},
			catcheDetailtxt: function(e) {
				console.log(e)
				this.catche_detailtxt = e.detail.value;
			},
			dialogDetailtxtConfirm: function(e) {
				var detailtxt = this.catche_detailtxt;
				console.log(detailtxt)
				var Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);
				var pagecontent = this.pagecontent;
				pagecontent.push({
					"id": Mid,
					"temp": "text",
					"params": {
						"content": detailtxt,
						"showcontent": detailtxt,
						"bgcolor": "#ffffff",
						"fontsize": "14",
						"lineheight": "20",
						"letter_spacing": "0",
						"bgpic": "",
						"align": "left",
						"color": "#000",
						"margin_x": "0",
						"margin_y": "0",
						"padding_x": "5",
						"padding_y": "5",
						"quanxian": {
							"all": true
						},
						"platform": {
							"all": true
						}
					},
					"data": "",
					"other": "",
					"content": ""
				});
				this.pagecontent = pagecontent;
				this.$refs.dialogDetailtxt.close();
			},
			detailAddpic: function() {
				var that = this;
				app.chooseImage(function(urls) {
					var Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);
					var pics = [];
					for (var i in urls) {
						var picid = 'p' + new Date().getTime() + parseInt(Math.random() * 1000000);
						pics.push({
							"id": picid,
							"imgurl": urls[i],
							"hrefurl": "",
							"option": "0"
						})
					}
					var pagecontent = that.pagecontent;
					pagecontent.push({
						"id": Mid,
						"temp": "picture",
						"params": {
							"bgcolor": "#FFFFFF",
							"margin_x": "0",
							"margin_y": "0",
							"padding_x": "0",
							"padding_y": "0",
							"quanxian": {
								"all": true
							},
							"platform": {
								"all": true
							}
						},
						"data": pics,
						"other": "",
						"content": ""
					});
					that.pagecontent = pagecontent;
				}, 9);
			},
			detailMoveup: function(e) {
				var index = e.currentTarget.dataset.index;
				var pagecontent = this.pagecontent;
				if (index > 0)
					pagecontent[index] = pagecontent.splice(index - 1, 1, pagecontent[index])[0];
			},
			detailMovedown: function(e) {
				var index = e.currentTarget.dataset.index;
				var pagecontent = this.pagecontent;
				if (index < pagecontent.length - 1)
					pagecontent[index] = pagecontent.splice(index + 1, 1, pagecontent[index])[0];
			},
			detailMovedel: function(e) {
				var index = e.currentTarget.dataset.index;
				var pagecontent = this.pagecontent;
				pagecontent.splice(index, 1);
			},
			changeFrieght: function(e) {
				var id = e.currentTarget.dataset.id;
				var index = e.currentTarget.dataset.index;
				var freightIds = this.freightIds;
				var newfreightIds = [];
				var ischecked = false;
				for (var i in freightIds) {
					if (freightIds[i] != id) {
						newfreightIds.push(freightIds[i]);
					} else {
						ischecked = true;
					}
				}
				if (!ischecked) newfreightIds.push(id);
				this.freightIds = newfreightIds;
			},
			freighttypeChange: function(e) {
				this.freightindex = e.detail.value;
			},
			bindStatusChange: function(e) {
				this.info.status = e.detail.value;
			},
			bindStartTime1Change: function(e) {
				this.start_time1 = e.target.value
			},
			bindStartTime2Change: function(e) {
				this.start_time2 = e.target.value
			},
			bindEndTime1Change: function(e) {
				this.end_time1 = e.target.value
			},
			bindEndTime2Change: function(e) {
				this.end_time2 = e.target.value
			},
			bindStartHoursChange: function(e) {
				this.start_hours = e.target.value
			},
			bindEndHoursChange: function(e) {
				this.end_hours = e.target.value
			},
			gglistInput: function(e) {
				var index = e.currentTarget.dataset.index;
				var field = e.currentTarget.dataset.field;
				var gglist = this.gglist;
				gglist[index][field] = e.detail.value;
				this.gglist = gglist;
				console.log(gglist)
			},
			getgglist: function() {
				var oldgglist = this.oldgglist;
				var guigedata = this.guigedata;
				var gglist = [];
				var len = guigedata.length;
				var newlen = 1;
				var h = new Array(len);
				for (var i = 0; i < len; i++) {
					var itemlen = guigedata[i].items.length;
					if (itemlen <= 0) {
						return;
					};
					newlen *= itemlen;
					h[i] = new Array(itemlen);
					for (var j = 0; j < itemlen; j++) {
						h[i][j] = {
							k: guigedata[i].items[j].k,
							title: guigedata[i].items[j].title
						};
					}
				}

				//排列组合算法
				var arr = h; //原二维数组
				var sarr = [
					[]
				]; //排列组合后的数组
				for (var i = 0; i < arr.length; i++) {
					var tarr = [];
					for (var j = 0; j < sarr.length; j++)
						for (var k = 0; k < arr[i].length; k++) {
							tarr.push(sarr[j].concat(arr[i][k]));
						}
					sarr = tarr;
				}
				console.log(sarr);
				console.log(' ------ ');

				for (var i = 0; i < sarr.length; i++) {
					var ks = [];
					var titles = [];
					for (var j = 0; j < sarr[i].length; j++) {
						ks.push(sarr[i][j].k);
						titles.push(sarr[i][j].title);
					}
					ks = ks.join(',');
					titles = titles.join(',');
					//console.log(ks);
					//console.log(titles);
					if (typeof(oldgglist[ks]) != 'undefined') {
						var val = oldgglist[ks];
					} else {
						var val = {
							ks: ks,
							name: titles,
							market_price: '',
							cost_price: '',
							sell_price: '',
							weight: '100',
							stock: '1000',
							pic: '',
							givescore: '0',
							lvprice_data: null
						};
					}
					gglist.push(val);
				}
				this.gglist = gglist;
				console.log(gglist);
			},
			addgggroupname: function(e) {
				this.ggname = '';
				this.ggindex = -1;
				this.$refs.dialogInput2.open();
			},
			delgggroupname: function(e) {
				var that = this;
				var ggindex = e.currentTarget.dataset.index;
				var title = e.currentTarget.dataset.title;
				uni.showActionSheet({
					itemList: ['修改', '删除'],
					success: function(res) {
						if (res.tapIndex >= 0) {
							if (res.tapIndex == 0) { //修改规格项
								that.ggname = title;
								that.ggindex = ggindex;
								that.$refs.dialogInput2.open();
								return;
							} else if (res.tapIndex == 1) { //删除规格项
								var guigedata = that.guigedata;
								var newguigedata = [];
								for (var i in guigedata) {
									if (i != ggindex) {
										newguigedata.push(guigedata[i]);
									}
								}
								that.guigedata = newguigedata;
								console.log(newguigedata);
								that.getgglist();
							}
						}
					}
				});
			},
			setgggroupname: function(done, val) {
				var guigedata = this.guigedata;
				var ggindex = this.ggindex;
				if (ggindex == -1) { //新增规格分组
					ggindex = guigedata.length;
					guigedata.push({
						k: ggindex,
						title: val,
						items: []
					});
					this.guigedata = guigedata;
				} else { //修改规格分组名称
					guigedata[ggindex].title = val;
					this.guigedata = guigedata;
				}
				this.$refs.dialogInput2.close();
				this.getgglist();
			},
			addggname: function(e) {
				var ggindex = e.currentTarget.dataset.index;
				this.ggname = '';
				this.ggindex = ggindex;
				this.ggindex2 = -1;
				this.$refs.dialogInput.open();
			},
			delggname: function(e) {
				var that = this;
				var ggindex = e.currentTarget.dataset.index;
				var ggindex2 = e.currentTarget.dataset.index2;
				var k = e.currentTarget.dataset.k;
				var title = e.currentTarget.dataset.title;
				uni.showActionSheet({
					itemList: ['修改', '删除'],
					success: function(res) {
						if (res.tapIndex >= 0) {
							if (res.tapIndex == 0) { //修改规格项
								that.ggname = title;
								that.ggindex = ggindex;
								that.ggindex2 = ggindex2;
								that.$refs.dialogInput.open();
								return;
							} else if (res.tapIndex == 1) { //删除规格项
								var guigedata = that.guigedata;
								var newguigedata = [];
								for (var i in guigedata) {
									if (i == ggindex) {
										var newitems = [];
										var index2 = 0;
										for (var j in guigedata[i].items) {
											if (j != ggindex2) {
												newitems.push({
													k: index2,
													title: guigedata[i].items[j].title
												});
												index2++;
											}
										}
										guigedata[i].items = newitems;
									}
									newguigedata.push(guigedata[i]);
								}
								that.guigedata = newguigedata;
								console.log(newguigedata)
								that.getgglist();
							}
						}
					}
				});
			},
			setggname: function(done, val) {
				var guigedata = this.guigedata;
				var ggindex = this.ggindex;
				var ggindex2 = this.ggindex2;
				if (ggindex2 == -1) { //新增规格名称
					var items = guigedata[ggindex].items;
					ggindex2 = items.length;
					items.push({
						k: ggindex2,
						title: val
					});
					guigedata[ggindex].items = items;
					this.guigedata = guigedata;
				} else { //修改规格名称
					guigedata[ggindex].items[ggindex2].title = val;
					this.guigedata = guigedata;
				}
				this.$refs.dialogInput.close();
				this.getgglist();
			},
			cidsChange: function(e) {
				var clist = this.clist;
				var cids = this.cids;
				var cid = e.currentTarget.dataset.id;
				var newcids = [];
				var ischecked = false;
				for (var i in cids) {
					if (cids[i] != cid) {
						newcids.push(cids[i]);
					} else {
						ischecked = true;
					}
				}
				if (ischecked == false) {
					if (newcids.length >= 5) {
						app.error('最多只能选择五个分类');
						return;
					}
					newcids.push(cid);
				}
				this.cids = newcids;
				this.getcnames();
			},
			getcnames: function() {
				var cateArr = this.cateArr;
				var cids = this.cids;
				var cnames = [];
				for (var i in cids) {
					cnames.push(cateArr[cids[i]]);
				}
				this.cnames = cnames.join(',');
			},
			cids2Change: function(e) {
				var clist = this.clist2;
				var cids = this.cids2;
				var cid = e.currentTarget.dataset.id;
				var newcids = [];
				var ischecked = false;
				for (var i in cids) {
					if (cids[i] != cid) {
						newcids.push(cids[i]);
					} else {
						ischecked = true;
					}
				}
				if (ischecked == false) {
					if (newcids.length >= 5) {
						app.error('最多只能选择五个分类');
						return;
					}
					newcids.push(cid);
				}
				this.cids2 = newcids;
				this.getcnames2();
			},
			getcnames2: function() {
				var cateArr = this.cateArr2;
				var cids = this.cids2;
				var cnames = [];
				for (var i in cids) {
					cnames.push(cateArr[cids[i]]);
				}
				this.cnames2 = cnames.join(',');
			},
			gidsChange: function(e) {
				var glist = this.glist;
				var gids = this.gids;
				var gid = e.currentTarget.dataset.id;
				var newgids = [];
				var ischecked = false;
				for (var i in gids) {
					if (gids[i] != gid) {
						newgids.push(gids[i]);
					} else {
						ischecked = true;
					}
				}
				if (ischecked == false) {
					newgids.push(gid);
				}
				this.gids = newgids;
				this.getgnames();
			},
			getgnames: function() {
				var groupArr = this.groupArr;
				var gids = this.gids;
				var gnames = [];
				for (var i in gids) {
					gnames.push(groupArr[gids[i]]);
				}
				this.gnames = gnames.join(',');
			},
			changeClistDialog: function() {
				this.clistshow = !this.clistshow
			},
			changeClist2Dialog: function() {
				this.clist2show = !this.clist2show
			},
			changeGlistDialog: function() {
				this.glistshow = !this.glistshow
			},
			uploadimg: function(e) {
				var that = this;
				var pernum = parseInt(e.currentTarget.dataset.pernum);
				if (!pernum) pernum = 1;
				var field = e.currentTarget.dataset.field
				var pics = that[field]
				if (!pics) pics = [];
				app.chooseImage(function(urls) {
					for (var i = 0; i < urls.length; i++) {
						pics.push(urls[i]);
					}
					if (field == 'pic') that.pic = pics;
					if (field == 'pics') that.pics = pics;
				}, pernum);
			},
			removeimg: function(e) {
				var that = this;
				var index = e.currentTarget.dataset.index
				var field = e.currentTarget.dataset.field
				if (field == 'pic') {
					var pics = that.pic
					pics.splice(index, 1);
					that.pic = pics;
				} else if (field == 'pics') {
					var pics = that.pics
					pics.splice(index, 1);
					that.pics = pics;
				}
			},
			uploadimg2: function(e) {
				var that = this;
				var index = e.currentTarget.dataset.index
				app.chooseImage(function(urls) {
					that.gglist[index].pic = urls[0];
				}, 1);
			},
			removeimg2: function(e) {
				var that = this;
				var index = e.currentTarget.dataset.index
				that.gglist[index].pic = '';
			},
		}
	};
</script>
<style lang="less">
	radio {
		transform: scale(0.6);
	}

	checkbox {
		transform: scale(0.6);
	}

	.form-box {
		padding: 2rpx 24rpx 0 24rpx;
		background: #fff;
		margin: 24rpx;
		border-radius: 10rpx
	}

	.form-item {
		line-height: 100rpx;
		display: flex;
		justify-content: space-between;
		border-bottom: 1px solid #eee
	}

	.form-item .f1 {
		color: #222;
		width: 200rpx;
		flex-shrink: 0
	}

	.form-item .f2 {
		display: flex;
		align-items: center
	}

	.form-box .form-item:last-child {
		border: none
	}

	.form-box .flex-col {
		padding-bottom: 20rpx
	}

	.form-item input {
		width: 100%;
		border: none;
		color: #111;
		font-size: 28rpx;
		text-align: right
	}

	.form-item textarea {
		width: 100%;
		min-height: 200rpx;
		padding: 20rpx 0;
		border: none;
	}

	.form-item .upload_pic {
		margin: 50rpx 0;
		background: #F3F3F3;
		width: 90rpx;
		height: 90rpx;
		text-align: center
	}

	.form-item .upload_pic image {
		width: 32rpx;
		height: 32rpx;
	}

	.savebtn {
		width: 90%;
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		border-radius: 48rpx;
		color: #fff;
		font-weight: bold;
		margin: 0 5%;
		margin-top: 60rpx;
		border: none;
	}

	.ggtitle {
		height: 60rpx;
		line-height: 60rpx;
		color: #111;
		font-weight: bold;
		font-size: 26rpx;
		display: flex;
		border-bottom: 1px solid #f4f4f4
	}

	.ggtitle .t1 {
		width: 200rpx;
	}

	.ggcontent {
		line-height: 60rpx;
		margin-top: 10rpx;
		color: #111;
		font-size: 26rpx;
		display: flex
	}

	.ggcontent .t1 {
		width: 200rpx;
		display: flex;
		align-items: center;
		flex-shrink: 0
	}

	.ggcontent .t1 .edit {
		width: 40rpx;
		height: 40rpx
	}

	.ggcontent .t2 {
		display: flex;
		flex-wrap: wrap;
		align-items: center
	}

	.ggcontent .ggname {
		background: #f55;
		color: #fff;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 20rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		margin-bottom: 10rpx;
		font-size: 24rpx;
		position: relative
	}

	.ggcontent .ggname .close {
		position: absolute;
		top: -14rpx;
		right: -14rpx;
		background: #fff;
		height: 28rpx;
		width: 28rpx;
		border-radius: 14rpx
	}

	.ggcontent .ggnameadd {
		background: #ccc;
		font-size: 36rpx;
		color: #fff;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 20rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		margin-left: 10rpx;
		position: relative
	}

	.ggcontent .ggadd {
		font-size: 26rpx;
		color: #558
	}

	.ggbox {
		line-height: 50rpx;
	}


	.layui-imgbox {
		margin-right: 16rpx;
		margin-bottom: 10rpx;
		font-size: 24rpx;
		position: relative;
	}

	.layui-imgbox-close {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		right: -16rpx;
		top: -16rpx;
		z-index: 5;
		color: #999;
		font-size: 32rpx;
		background: #fff;
		border-radius: 50%
	}

	.layui-imgbox-close image {
		width: 100%;
		height: 100%
	}

	.layui-imgbox-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		padding: 2px;
		border: #d3d3d3 1px solid;
		background-color: #f6f6f6;
		overflow: hidden
	}

	.layui-imgbox-img>image {
		max-width: 100%;
	}

	.layui-imgbox-repeat {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		line-height: 28rpx;
		right: 2px;
		bottom: 2px;
		color: #999;
		font-size: 30rpx;
		background: #fff
	}

	.uploadbtn {
		position: relative;
		height: 200rpx;
		width: 200rpx
	}


	.clist-item {
		display: flex;
		border-bottom: 1px solid #f5f5f5;
		padding: 20rpx 30rpx;
	}

	.radio {
		flex-shrink: 0;
		width: 32rpx;
		height: 32rpx;
		background: #FFFFFF;
		border: 2rpx solid #BFBFBF;
		border-radius: 50%;
		margin-right: 30rpx
	}

	.radio .radio-img {
		width: 100%;
		height: 100%;
		display: block
	}

	.freightitem {
		width: 100%;
		height: 60rpx;
		display: flex;
		align-items: center;
		margin-left: 40rpx
	}

	.freightitem .f1 {
		color: #666;
		flex: 1
	}

	.detailop {
		display: flex;
		line-height: 60rpx
	}

	.detailop .btn {
		border: 1px solid #ccc;
		margin-right: 10rpx;
		padding: 0 16rpx;
		color: #222;
		border-radius: 10rpx
	}

	.detaildp {
		position: relative;
		line-height: 50rpx
	}

	.detaildp .op {
		width: 100%;
		display: flex;
		justify-content: flex-end;
		font-size: 24rpx;
		height: 60rpx;
		line-height: 60rpx;
		margin-top: 10rpx
	}

	.detaildp .op .btn {
		background: rgba(0, 0, 0, 0.4);
		margin-right: 10rpx;
		padding: 0 10rpx;
		color: #fff
	}

	.detaildp .detailbox {
		border: 2px dashed #00a0e9
	}

	.uni-popup-dialog {
		width: 300px;
		border-radius: 5px;
		background-color: #fff;
	}

	.uni-dialog-title {
		display: flex;
		flex-direction: row;
		justify-content: center;
		padding-top: 15px;
		padding-bottom: 5px;
	}

	.uni-dialog-title-text {
		font-size: 16px;
		font-weight: 500;
	}

	.uni-dialog-content {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 5px 15px 15px 15px;
	}

	.uni-dialog-content-text {
		font-size: 14px;
		color: #6e6e6e;
	}

	.uni-dialog-button-group {
		display: flex;
		flex-direction: row;
		border-top-color: #f5f5f5;
		border-top-style: solid;
		border-top-width: 1px;
	}

	.uni-dialog-button {
		display: flex;
		flex: 1;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		height: 45px;
	}

	.uni-border-left {
		border-left-color: #f0f0f0;
		border-left-style: solid;
		border-left-width: 1px;
	}

	.uni-dialog-button-text {
		font-size: 14px;
	}

	.uni-button-color {
		color: #007aff;
	}

	.uni-list-cell {
		display: flex;
		justify-content: flex-start;
		padding: 5px 10px;
	}


	.uni-list {
		height: 270px;
		overflow-y: auto;
	}


	.list {
		column-count: 2;
		box-sizing: content-box;
		margin-top: 10px;
	}

	.pbl {
		width: 100%;
		break-inside: avoid;
		overflow: hidden;
		border-radius: 5px;
		overflow: hidden;
		margin-bottom: 20rpx;
		background-color: #fff;
		box-sizing: border-box;

		&:last-child {
			margin-bottom: 10rpx;
		}

		.image {
			width: 100%;
			border-radius: 5px;
			overflow: hidden;

			&>image {
				width: 100%;
				height: 100%;
			}
		}

		.title {
			font-size: 32rpx;
			margin-bottom: 6rpx;
			display: -webkit-box;
			text-overflow: ellipsis;
			overflow: hidden;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2; //当属性值为3，表示超出3行隐
			padding: 5px 10px;
			font-weight: bold;
		}

		.more {
			display: flex;
			justify-content: space-between;
			color: #9499aa;
			margin-bottom: 6rpx;
			font-size: 26rpx;
		}
	}


	/* 多行省略 */
	.multi-line-omit {
		word-break: break-all; // 允许单词内自动换行，如果一个单词很长的话
		text-overflow: ellipsis; // 溢出用省略号显示
		overflow: hidden; // 超出的文本隐藏
		display: -webkit-box; // 作为弹性伸缩盒子模型显示
		-webkit-line-clamp: 2; // 显示的行
		-webkit-box-orient: vertical; // 设置伸缩盒子的子元素排列方式--从上到下垂直排列
	}

	/* 单行省略 */
	.one-line-omit {
		width: 100%; // 宽度100%：1vw等于视口宽度的1%；1vh等于视口高度的1%
		white-space: nowrap; // 溢出不换行
		overflow: hidden; // 超出的文本隐藏
		text-overflow: ellipsis; // 溢出用省略号显示
	}

	.p1 {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		padding: 5px 10px; 
	}
	
	.search-bar {
		display: flex;
		align-items: center;
		margin: 0 10px;
	}
	
	.back-icon {
		width: 18px;
		height: 18px;
	}
	
	.search-container {
		width: 100%;
		padding: 16rpx 23rpx;
		background: #5AA37B;
		position: relative;
		overflow: hidden;
	}
	
	.search-box {
		display: flex;
		align-items: center;
		height: 60rpx;
		border-radius: 10rpx;
		background-color: #fff;
		flex: 1;
	/* 	margin-left: 15px; */
	}
	
	.search-box .img {
		width: 24rpx;
		height: 24rpx;
		margin-right: 10rpx;
		margin-left: 30rpx;
	}
	
	.search-box .search-text {
		font-size: 24rpx;
		color: #C2C2C2;
		width: 100%;
	}
</style>