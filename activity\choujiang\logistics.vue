<template>
<view class="container">
	<block v-if="isload">
		<view class="orderinfo">
			<view class="item">
				<view class="t1">奖品名称</view>
				<view class="t2">{{record.jxmc}}</view>
			</view>
			<view class="item">
				<view class="t1">快递公司</view>
				<view class="t2">{{record.express_com}}</view>
			</view>
			<view class="item">
				<view class="t1">快递单号</view>
				<view class="t2">{{record.express_no}}</view>
			</view>
			<view class="item">
				<view class="t1">发货时间</view>
				<view class="t2">{{record.send_time_str}}</view>
			</view>
		</view>
		
		<view class="logistics" v-if="datalist.length > 0">
			<view v-for="(item, index) in datalist" :key="index" :class="index==0?'item on':'item'">
				<view class="f1">
					<image :src="pre_url+'/static/img/wuliu.png'"></image>
				</view>
				<view class="f2">
					<view class="t1">{{item.context}}</view>
					<view class="t2">{{item.time}}</view>
				</view>
			</view>
		</view>
		
		<view v-if="datalist.length == 0" class="nodata">
			<image :src="pre_url+'/static/img/nodata.png'" class="img"></image>
			<view class="txt">暂无物流信息</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			pre_url:app.globalData.pre_url,
      datalist: [],
      record: {}
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiChoujiang/getExpress', {recordid: that.opt.recordid}, function (res) {
				that.loading = false;
				if(res.status == 1){
					that.record = res.record;
					that.record.send_time_str = app.formatTime(res.record.send_time);
					that.datalist = res.data || [];
				} else {
					app.error(res.msg);
				}
				that.loaded();
			});
		},
		loaded: function () {
			this.loading = false;
			this.isload = true;
			uni.stopPullDownRefresh();
		}
  }
};
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.orderinfo {
	background: #fff;
	margin-bottom: 20rpx;
}

.orderinfo .item {
	display: flex;
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.orderinfo .item:last-child {
	border-bottom: none;
}

.orderinfo .item .t1 {
	width: 160rpx;
	color: #666;
	font-size: 28rpx;
}

.orderinfo .item .t2 {
	flex: 1;
	color: #333;
	font-size: 28rpx;
}

.logistics {
	background: #fff;
	padding: 0 30rpx;
}

.logistics .item {
	display: flex;
	padding: 30rpx 0;
	border-left: 2rpx solid #e0e0e0;
	position: relative;
}

.logistics .item.on {
	color: #23aa5e;
}

.logistics .item .f1 {
	width: 60rpx;
	position: relative;
}

.logistics .item .f1 image {
	width: 30rpx;
	height: 30rpx;
	position: absolute;
	left: -16rpx;
	top: 10rpx;
}

.logistics .item .f2 {
	flex: 1;
	padding-left: 20rpx;
}

.logistics .item .f2 .t1 {
	font-size: 28rpx;
	line-height: 40rpx;
	margin-bottom: 10rpx;
}

.logistics .item .f2 .t2 {
	font-size: 24rpx;
	color: #999;
}

.nodata {
	text-align: center;
	padding: 100rpx 0;
	background: #fff;
}

.nodata .img {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.nodata .txt {
	color: #999;
	font-size: 28rpx;
}
</style>
