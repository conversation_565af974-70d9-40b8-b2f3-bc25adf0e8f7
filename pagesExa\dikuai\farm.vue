<template>
	<view>
		<view class="container">
			<!-- 用户信息 -->
			<view class="user-info">
				<view class="avatar-container">
					<image class="avatar" :src="headimg" />
					<text class="nickname">{{ nickname }}</text>
				</view>
				<view class="info-text">
					<view class="info-row">
						<text>余额：{{ balance }}</text>
						<text>积分：{{ points }}</text>
					</view>
					<view class="info-row">
						<text>佣金：{{ commission }}</text>
						<text>RMP：{{ heiscore }}</text>
					</view>
				</view>
			</view>
			<!-- 大地块 -->
			<view class="big-land">
				<view class="plot"   :locked="plot.isPig ? 'true' : 'false'" v-for="(plot, index) in plots" :key="index">
					<!-- 如果地块未解锁，显示锁的图标，点击可以解锁 -->
					<image v-if="plot.isLocked" :src="pre_url+'/static/img/reg-pwd.png'" class="lock-icon"
						@click="handlePlotClick(index)" />
					<!-- 如果地块已解锁，显示排队和买猪仔按钮 -->
					<view v-else class="unlocked-plot">
						<view class="plot-content" v-if="!plot.isPig">
							<!-- 如果地块未开通，显示开通按钮 -->
							<button v-if="plot.status === 0" 
									@click="handlePlotClick(index)" 
									class="unlock-button">
								开通地块
							</button>
							<!-- 如果地块已开通但没有猪，显示排队和买猪仔按钮 -->
							<template v-else-if="plot.status === 1">
								<button @click="joinQueue(plot.plot_id)" class="queue-button">排队</button>
								<button @click="buyPiglet(plot.plot_id)" class="buy-button">买猪仔</button>
							</template>
						</view>
					</view>
				</view>
			</view>
			<!-- 新增：进入养猪场按钮 -->
			<button @click="goToPigFarm" class="enter-farm-button">进入养猪场</button>
		</view>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
		<dp-tabbar :opt="opt"></dp-tabbar>
	</view>
</template>
<script>
	var app = getApp();
	export default {
		components: {
			loading: () => import('@/components/loading/loading.vue'),
			dpTabbar: () => import('@/components/dp-tabbar/dp-tabbar.vue'),
			popmsg: () => import('@/components/popmsg/popmsg.vue')
		},
		data() {
			return {
				pre_url: app.globalData.pre_url, // 添加pre_url
				headimg: '', // 默认头像为空，在getMy方法中会设置
				nickname: '',
				balance: 0, // 用户余额，初始为 1000 元
				points: 0, // 用户积分，初始为 0
				commission: 0, // 用户佣金，初始为 0
				heiscore: 0, // 用户RMP，初始为 0
				plots: [{
						plot_number: 1,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
					{
						plot_number: 2,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
					{
						plot_number: 3,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
					{
						plot_number: 4,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
					{
						plot_number: 5,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
					{
						plot_number: 6,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
					{
						plot_number: 7,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
					{
						plot_number: 8,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
					{
						plot_number: 9,
						plot_id: 0,
						isLocked: true,
						isPig : false
					},
				], // 九个小地块的状态
				loading: false,
				opt: {
					selected: 0,  // 当前选中的tab
					list: [
						{
							text: '菜单一',
							pagePath: '/pages/index/index'
						},
						{
							text: '菜单二',
							pagePath: '/pages/user/index'
						}
					]
				}, // tabbar配置项
			};
		},
		onShow() {
			this.getMy();
			this.getData();
		},
		methods: {
			getData() {
				let that = this
				app.get('ApiPlot/getPlots', {}, function(res) {
					console.log(res)
					res.data.forEach((item, k) => {
						that.plots[k].isLocked = item.status === 0 // 状态为0时表示锁定
						that.plots[k].plot_id = item.plot_id
						that.plots[k].status = item.status
						that.plots[k].isPig = item.pig !== null
					})
				});
			},


			// 处理地块点击事件（解锁）
			handlePlotClick(index) {
				const plot = this.plots[index];
				if (plot.isLocked) {
					// 弹出支付确认对话框
					uni.showModal({
						title: '解锁地块',
						content: '支付 200 元解锁此地块，并获得 20000 积分，是否继续？',
						success: (res) => {
							if (res.confirm) {
								this.unlockPlot(index);
							}
						},
					});
				} else {
					// 地块已解锁，提示用户
					uni.showToast({
						title: '地块已解锁',
						icon: 'none',
					});
				}
			},

			getMy() {
				let that = this
				app.get('ApiMy/usercenter', {}, function(data) {
					that.headimg = data.pagecontent[0].data.userinfo.headimg ? 
						(data.pagecontent[0].data.userinfo.headimg.includes('http') ? 
							data.pagecontent[0].data.userinfo.headimg : 
							that.pre_url + data.pagecontent[0].data.userinfo.headimg) : 
						that.pre_url + '/static/img/default-avatar.png';
					that.nickname = data.pagecontent[0].data.userinfo.nickname;
					that.balance = data.pagecontent[0].data.userinfo.money;
					that.points = data.pagecontent[0].data.userinfo.score;
					that.commission = data.pagecontent[0].data.userinfo.commission;
					that.heiscore = data.pagecontent[0].data.userinfo.heiscore;
				});
			},

			// 解锁地块
			unlockPlot(index) {

				let that = this

				if (that.balance >= 200) {

					app.post('ApiPlot/unlockPlot', {
						'plot_number': index + 1
					}, function(data) {

						if (data.status == 1) {
							// 提示用户
							uni.showToast({
								title: data.message,
								icon: 'success',
							});
							
							that.getData();
							
						} else {

							uni.showToast({
								title: data.message,
								icon: 'none',
							});
						}
					})

					// TODO: 调用后端接口，更新用户余额、积分和地块状态
				} else {
					uni.showToast({
						title: '余额不足，无法解锁',
						icon: 'none',
					});
				}
			},
			// 加入排队
			joinQueue(plot_id) {
				// TODO: 调用后端接口，加入排队队列

				app.post('ApiPlot/joinQueue', {
					'plot_id': plot_id
				}, function(res) {
					uni.showToast({
						title: res.message,
						icon: 'none',
					});
				})
			},
			// 买猪仔
			buyPiglet(plot_id) {
				// TODO: 检查购买资格，调用后端接口进行购买
				// 跳转到猪仔详情页面
				
				app.post('ApiPlot/buyPiglet', {
					'plot_id': plot_id
				}, function(res) {
					uni.showToast({
						title: res.message,
						icon: 'none',
					});
				})
				
				uni.showToast({
					title: '购买成功',
				});

				setTimeout(function() {
					uni.navigateTo({
						url: '/pagesExa/dikuai/pigfarm',
					});
				}, 1000);
			},
			// 新增：进入养猪场
			goToPigFarm() {
				uni.navigateTo({
					url: '/pagesExa/dikuai/pigfarm',
				});
			},
		},
	};
</script>

<style>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx;
		background-image: url('{{pre_url}}/static/img2/page-bg.png');
		/* 页面背景图 */
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		min-height: 100vh;
		padding-bottom: 100rpx; /* 为底部tabbar预留空间 */
	}

	/* 确保tabbar固定在底部 */
	.dp-tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #fff;
	}

	.user-info {
		display: flex;
		align-items: center;
		width: 90%;
		margin: 20rpx 0;
		padding: 20rpx;
		border-radius: 15rpx;
	}

	.avatar-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-right: 30rpx;
	}

	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		border: 4rpx solid #fff;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 10rpx; /* 头像和昵称之间的间距 */
	}

	.nickname {
		font-size: 28rpx;
		color: #333;
		max-width: 120rpx; /* 与头像同宽 */
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.info-text {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		height: 120rpx;
	}

	.info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10rpx 0;
	}

	.info-row text {
		font-size: 32rpx;
		color: #333;
		font-weight: normal;
		padding: 8rpx 20rpx;
		border-radius: 30rpx;
		background: rgba(255, 255, 255, 0.8);
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		min-width: 160rpx;
		text-align: center;
		margin: 0 10rpx;
	}

	/* 第一行（余额和积分）的特殊样式 */
	.info-row:first-child text {
		color: #666;
		font-size: 28rpx;
	}

	/* 第二行（佣金和RMP）的特殊样式 */
	.info-row:last-child text {
		background: rgba(255, 255, 255, 0.9);
		font-weight: normal;
		color: #444;
	}

	.big-land {
		display: flex;
		flex-wrap: wrap;
		width: 90%;
		height: 600rpx;
		margin-top: 160rpx;
		margin-bottom: 20rpx;
	}

	.plot {
		width: 31%;
		/* 调整宽度以适应边距 */
		height: 200rpx;
		box-sizing: border-box;
		position: relative;
		border: 1rpx solid #ccc;
		background-size: cover;
		background-position: center;
		margin: 0% 1% 2%;
		/* 新增：设置边距，确保各地块间有间隙 */
	}

	.plot[locked="true"] {
		background-image: url('{{pre_url}}/static/img2/unlocked-bg.png');
	}

	.plot[locked="false"] {
		background-image: url('{{pre_url}}/static/img2/locked-bg.png');
	}
	
	

	.lock-icon {
		width: 80rpx;
		height: 80rpx;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.unlocked-plot {
		width: 100%;
		height: 100%;
	}

	.plot-content {
		position: absolute;
		bottom: 10rpx;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-around;
	}

	.queue-button,
	.buy-button {
		width: 100rpx;
		height: 50rpx;
		font-size: 24rpx;
		border: none;
		border-radius: 25rpx;
		color: #fff;
	}

	.queue-button {
		background-color: #4caf50;
	}

	.buy-button {
		background-color: #ff9800;
	}

	.enter-farm-button {
		width: 80%;
		height: 70rpx;
		font-size: 28rpx;
		color: #fff;
		background-color: #2196f3;
		border: none;
		border-radius: 35rpx;
		margin-top: 20rpx;
		box-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.1);
	}
</style>