# 预约服务模块API接口文档

## 接口概述

本文档包含预约服务模块优化后的API接口，主要实现了"先支付后预约"的流程模式。

## 接口列表

### 1. 获取商品详情接口

- **接口URL**: ` apiyuyue/product`
- **请求方式**: POST
- **接口说明**: 获取商品详情信息，现已增加商品预约流程模式返回
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 商品ID |

- **返回参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| status | int | 状态码：1成功，0失败 |
| msg | string | 提示信息 |
| data | object | 商品信息，包含order_flow_mode |

- **流程模式说明**:
  - order_flow_mode=0: 传统模式(先选时间再支付)
  - order_flow_mode=1: 新模式(先支付再选时间)

- **修改说明**:
  - 增加了对商品流程模式的判断
  - 增加了order_flow_mode字段返回
  - 增加了流程模式确定的逻辑判断（先判商品单独设置，再取系统设置）

### 2. 创建订单接口

- **接口URL**: ` apiyuyue/createOrder`
- **请求方式**: POST
- **接口说明**: 创建预约订单，根据流程模式采用不同的预约流程
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| proid | int | 是 | 商品ID |
| yydate | string | 条件 | 预约日期(传统模式必填) |
| yytime | string | 条件 | 预约时间段(传统模式必填) |
| worker_id | int | 条件 | 服务人员ID(传统模式下且fwpeople=1时必填) |
| ... | ... | ... | 其他订单参数 |

- **返回参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| status | int | 状态码：1成功，0失败 |
| msg | string | 提示信息 |
| orderid | int | 订单ID |

- **修改说明**:
  - 增加了对订单流程模式的判断
  - 根据不同流程模式执行不同的验证逻辑
  - 新增字段appointment_status和worker_assign_status用于标记订单状态
  - 新模式下，允许下单时不选择预约时间，支持后续预约

### 3. 预约时间选择接口

- **接口URL**: ` apiyuyue/appointTime`
- **请求方式**: POST
- **接口说明**: 为已支付订单设置预约时间
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 订单ID |
| yydate | string | 是 | 预约日期 |
| yytime | string | 是 | 预约时间段 |

- **返回参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| status | int | 状态码：1成功，0失败 |
| msg | string | 提示信息 |

- **接口说明**:
  - 此接口仅在新流程模式下使用
  - 用于在订单支付后选择预约时间
  - 会验证预约时间是否有效
  - 会更新订单的appointment_status状态为1(已预约)

### 4. 获取可用服务人员接口

- **接口URL**: ` apiyuyue/getAvailableWorkers`
- **请求方式**: POST
- **接口说明**: 获取指定日期时间可用的服务人员列表
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 订单ID |
| yydate | string | 是 | 预约日期 |

- **返回参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| status | int | 状态码：1成功，0失败 |
| msg | string | 提示信息 |
| data | array | 可用服务人员列表 |

- **接口说明**:
  - 此接口用于获取特定时间段内可用的服务人员
  - 会过滤掉已被预约的服务人员
  - 返回的服务人员信息包含评分、完成单数等信息

### 5. 指定服务人员接口

- **接口URL**: ` apiyuyue/assignWorker`
- **请求方式**: POST
- **接口说明**: 为订单指定服务人员
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 订单ID |
| worker_id | int | 是 | 服务人员ID |

- **返回参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| status | int | 状态码：1成功，0失败 |
| msg | string | 提示信息 |

- **接口说明**:
  - 此接口用于在预约时间后选择服务人员
  - 会验证服务人员在该时间段是否可用
  - 会更新订单的worker_assign_status状态为1(已分配)
  - 如果预约和服务人员都已完成分配，会触发派单操作

## 流程说明

### 传统预约流程（order_flow_mode=0）
1. 用户选择商品 -> 选择预约时间 -> 选择服务人员 -> 下单支付
2. 下单时需要传入预约时间和服务人员信息
3. 订单创建时已完成预约和服务人员分配

### 新预约流程（order_flow_mode=1）
1. 用户选择商品 -> 下单支付 -> 选择预约时间 -> 选择/分配服务人员
2. 下单时不需要传入预约时间和服务人员信息
3. 支付成功后，可通过appointTime接口设置预约时间
4. 预约时间设置后，可通过assignWorker接口指定服务人员
5. 预约和服务人员都已完成后，订单状态会更新为已派单 