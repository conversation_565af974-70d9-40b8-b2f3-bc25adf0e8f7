<template>
	<view class="history-container">
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @tap="goBack">
				<text class="iconfont icon-back back-icon"></text>
			</view>
			<view class="nav-title">舌诊记录</view>
			<view class="nav-right"></view>
		</view>
		
		<!-- 记录列表 -->
		<view class="record-list">
			<view v-if="recordList.length === 0 && !isLoading" class="empty-state">
				<image src="/static/img/empty-record.png" class="empty-image" mode="aspectFit"></image>
				<text class="empty-text">暂无舌诊记录</text>
				<view class="start-btn" @tap="startNewDiagnosis">
					<text class="start-text">开始舌诊</text>
				</view>
			</view>
			
			<view v-for="(record, index) in recordList" :key="record.id" class="record-item" @tap="viewRecord(record)">
				<view class="record-image">
					<image :src="record.tongue_image" class="tongue-image" mode="aspectFill"></image>
					<view class="status-completed">
						<text class="status-text">已完成</text>
					</view>
				</view>
				
				<view class="record-info">
					<view class="record-header">
						<text class="record-title">舌诊分析报告</text>
						<text class="record-time">{{ formatTime(record.createtime) }}</text>
					</view>
					
					<view class="record-details">
						<view class="detail-item">
							<text class="detail-label">舌质：</text>
							<text class="detail-value">{{ record.tongue_quality || '待分析' }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">舌苔：</text>
							<text class="detail-value">{{ record.tongue_coating || '待分析' }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">体质：</text>
							<text class="detail-value">{{ record.constitution_type || '待分析' }}</text>
						</view>
					</view>
					
					<view class="record-score" v-if="record.constitution_score">
						<text class="score-label">健康评分：</text>
						<text class="score-value" :style="{color: getScoreColor(record.constitution_score)}">{{ record.constitution_score }}分</text>
					</view>
				</view>
				
				<view class="record-actions">
					<text class="iconfont icon-arrow-right action-icon"></text>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view v-if="hasMore && recordList.length > 0" class="load-more" @tap="loadMore">
			<text class="load-text">{{ isLoadingMore ? '加载中...' : '加载更多' }}</text>
		</view>
		
		<!-- 底部提示 -->
		<view v-if="!hasMore && recordList.length > 0" class="no-more">
			<text class="no-more-text">没有更多记录了</text>
		</view>

		<!-- 加载状态 -->
		<loading v-if="loading"></loading>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			// 2025-01-26 12:00:00,001-INFO-[history][data_001] 初始化历史记录页面数据
			recordList: [],           // 记录列表
			pagenum: 1,              // 当前页码，保持与其他页面一致
			pageSize: 10,            // 每页数量
			hasMore: true,           // 是否有更多数据
			loading: false,          // 是否正在加载，保持与其他页面一致
			isLoadingMore: false,    // 是否正在加载更多
			nodata: false,           // 是否无数据，保持与其他页面一致
			nomore: false            // 是否无更多数据，保持与其他页面一致
		}
	},
	onLoad() {
		console.log('2025-01-26 12:00:00,001-INFO-[history][onLoad_001] 舌诊历史记录页面加载完成');
		this.getdata();
	},
	onPullDownRefresh() {
		console.log('2025-01-26 12:00:00,002-INFO-[history][onPullDownRefresh_001] 下拉刷新');
		this.getdata();
	},
	onReachBottom() {
		console.log('2025-01-26 12:00:00,003-INFO-[history][onReachBottom_001] 触底加载更多');
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getRecordList();
		}
	},
	methods: {
		/**
		 * 获取数据 - 保持与article/detail.vue一致的命名
		 */
		getdata() {
			console.log('2025-01-26 12:00:00,010-INFO-[history][getdata_001] 开始获取记录数据');
			var that = this;
			that.loading = true;
			that.pagenum = 1;
			that.recordList = [];
			that.getRecordList();
		},

		/**
		 * 获取记录列表
		 */
		getRecordList() {
			console.log('2025-01-26 12:00:00,015-INFO-[history][getRecordList_001] 开始获取记录列表');
			var that = this;
			var pagenum = that.pagenum;
			
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
			// 使用统一的app.get接口调用方式
			app.get('ApiSheZhen/getRecordList', {
				page: pagenum,
				limit: that.pageSize
			}, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				console.log('2025-01-26 12:00:00,016-INFO-[history][getRecordList_002] 获取记录列表响应:', res);
				
				if (res.code == 1) {
					var data = res.data.list || [];
					if (data.length == 0) {
						if (pagenum == 1) {
							that.nodata = true;
						} else {
							that.nomore = true;
						}
					}
					
					var recordList = that.recordList;
					var newdata = recordList.concat(data);
					that.recordList = newdata;
					
					console.log('2025-01-26 12:00:00,017-INFO-[history][getRecordList_003] 记录列表更新完成，共', that.recordList.length, '条记录');
				} else {
					console.error('2025-01-26 12:00:00,018-ERROR-[history][getRecordList_004] 获取记录列表失败:', res.msg);
					uni.showToast({
						title: res.msg || '获取记录失败',
						icon: 'none'
					});
				}
			});
		},
		
		/**
		 * 查看记录详情
		 */
		viewRecord(record) {
			console.log('2025-01-26 12:00:00,040-INFO-[history][viewRecord_001] 查看记录详情:', record.id);
			
			// 直接跳转到结果页面，因为后端已经过滤了status=1的记录
			uni.navigateTo({
				url: `/pagesB/shezhen/complete?recordId=${record.id}`,
				success: () => {
					console.log('2025-01-26 12:00:00,041-INFO-[history][viewRecord_002] 成功跳转到结果页面');
				},
				fail: (err) => {
					console.error('2025-01-26 12:00:00,042-ERROR-[history][viewRecord_003] 跳转结果页面失败:', err);
				}
			});
		},
		
		/**
		 * 开始新的舌诊
		 */
		startNewDiagnosis() {
			console.log('2025-01-26 12:00:00,050-INFO-[history][startNewDiagnosis_001] 开始新的舌诊');
			uni.navigateTo({
				url: '/pagesB/shezhen/guide',
				success: () => {
					console.log('2025-01-26 12:00:00,051-INFO-[history][startNewDiagnosis_002] 成功跳转到引导页面');
				},
				fail: (err) => {
					console.error('2025-01-26 12:00:00,052-ERROR-[history][startNewDiagnosis_003] 跳转引导页面失败:', err);
				}
			});
		},
		
		/**
		 * 返回上一页
		 */
		goBack() {
			console.log('2025-01-26 12:00:00,060-INFO-[history][goBack_001] 返回上一页');
			uni.navigateBack({
				success: () => {
					console.log('2025-01-26 12:00:00,061-INFO-[history][goBack_002] 成功返回上一页');
				},
				fail: (err) => {
					console.error('2025-01-26 12:00:00,062-ERROR-[history][goBack_003] 返回上一页失败:', err);
				}
			});
		},
		
		/**
		 * 获取评分颜色
		 */
		getScoreColor(score) {
			if (score >= 80) return '#52c41a';
			if (score >= 60) return '#faad14';
			return '#ff4d4f';
		},
		
		/**
		 * 格式化时间
		 */
		formatTime(timestamp) {
			if (!timestamp) return '';
			
			const date = new Date(timestamp);
			const now = new Date();
			const diff = now.getTime() - date.getTime();
			
			// 一天内显示时间
			if (diff < 24 * 60 * 60 * 1000) {
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				return `今天 ${hours}:${minutes}`;
			}
			
			// 一年内显示月日
			if (date.getFullYear() === now.getFullYear()) {
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				return `${month}-${day}`;
			}
			
			// 超过一年显示年月日
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		}
	}
}
</script>

<style scoped>
/* 舌诊历史记录页面样式定义 - 现代化设计 */

.history-container {
	background-color: #f8f9fc;
	min-height: 100vh;
}

/* 顶部导航样式 */
.top-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 30rpx;
	background: linear-gradient(135deg, #ffffff 0%, #f4f6fc 100%);
	position: relative;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
	border-bottom-left-radius: 20rpx;
	border-bottom-right-radius: 20rpx;
}

.nav-left {
	display: flex;
	align-items: center;
	width: 60rpx;
	height: 60rpx;
	justify-content: center;
	background-color: rgba(0, 0, 0, 0.03);
	border-radius: 50%;
}

.back-icon {
	font-size: 36rpx;
	color: #333;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	letter-spacing: 2rpx;
}

.nav-right {
	width: 60rpx;
}

/* 记录列表样式 */
.record-list {
	padding: 24rpx;
}

.record-item {
	display: flex;
	background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
	border-radius: 20rpx;
	margin-bottom: 24rpx;
	padding: 28rpx 24rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06), 0 1rpx 6rpx rgba(0, 0, 0, 0.03);
	position: relative;
	border: 1px solid rgba(230, 235, 245, 0.8);
	transition: all 0.2s ease-out;
}

.record-item:active {
	transform: scale(0.98);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}

.record-image {
	width: 140rpx;
	height: 140rpx;
	border-radius: 16rpx;
	overflow: hidden;
	position: relative;
	margin-right: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	border: 3rpx solid rgba(255, 255, 255, 0.9);
}

.tongue-image {
	width: 100%;
	height: 100%;
	transition: transform 0.3s ease;
}

.record-item:active .tongue-image {
	transform: scale(1.05);
}

.record-status {
	position: absolute;
	top: 5rpx;
	right: 5rpx;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
	font-size: 20rpx;
}

.status-pending {
	background-color: rgba(250, 173, 20, 0.9);
	color: white;
}

.status-processing {
	background-color: rgba(24, 144, 255, 0.9);
	color: white;
}

.status-completed {
	background-color: rgba(65, 184, 131, 0.9);
	color: white;
	position: absolute;
	top: 6rpx;
	right: 6rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(65, 184, 131, 0.3);
	backdrop-filter: blur(2px);
}

.status-failed {
	background-color: rgba(255, 77, 79, 0.9);
	color: white;
}

.status-text {
	font-size: 20rpx;
	font-weight: 500;
}

.record-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.record-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	background: linear-gradient(135deg, #333 30%, #666 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.record-time {
	font-size: 24rpx;
	color: #888;
	background-color: rgba(0, 0, 0, 0.03);
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
}

.record-details {
	margin-bottom: 16rpx;
	background-color: rgba(248, 250, 252, 0.6);
	border-radius: 12rpx;
	padding: 12rpx;
}

.detail-item {
	display: flex;
	margin-bottom: 8rpx;
}

.detail-label {
	font-size: 26rpx;
	color: #666;
	width: 80rpx;
	font-weight: 500;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	flex: 1;
	font-weight: 400;
}

.record-score {
	display: flex;
	align-items: center;
	background: linear-gradient(90deg, rgba(240, 245, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}

.score-label {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
}

.score-value {
	font-size: 30rpx;
	font-weight: 600;
	margin-left: 10rpx;
}

.record-actions {
	display: flex;
	align-items: center;
	margin-left: 20rpx;
}

.action-icon {
	font-size: 30rpx;
	color: #bbb;
	background-color: rgba(0, 0, 0, 0.03);
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 50rpx;
	text-align: center;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 40rpx;
	opacity: 0.7;
	animation: float 3s ease-in-out infinite;
}

@keyframes float {
	0% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-15px);
	}
	100% {
		transform: translateY(0px);
	}
}

.empty-text {
	font-size: 30rpx;
	color: #888;
	margin-bottom: 50rpx;
	font-weight: 400;
	letter-spacing: 1rpx;
}

.start-btn {
	background: linear-gradient(135deg, #5a7bef 0%, #7c4cdd 100%);
	padding: 24rpx 50rpx;
	border-radius: 30rpx;
	box-shadow: 0 10rpx 20rpx rgba(90, 123, 239, 0.3);
	transition: all 0.3s ease;
	border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.start-btn:active {
	transform: scale(0.95);
	box-shadow: 0 5rpx 10rpx rgba(90, 123, 239, 0.2);
}

.start-text {
	font-size: 30rpx;
	color: white;
	font-weight: 500;
	letter-spacing: 2rpx;
}

/* 加载更多样式 */
.load-more {
	text-align: center;
	padding: 30rpx;
}

.load-text {
	font-size: 28rpx;
	color: #666;
	background-color: rgba(0, 0, 0, 0.03);
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	display: inline-block;
}

.no-more {
	text-align: center;
	padding: 30rpx;
}

.no-more-text {
	font-size: 24rpx;
	color: #999;
	position: relative;
	display: inline-block;
	padding: 0 30rpx;
}

.no-more-text::before,
.no-more-text::after {
	content: "";
	position: absolute;
	top: 50%;
	width: 60rpx;
	height: 1rpx;
	background: linear-gradient(90deg, transparent, #ddd);
}

.no-more-text::before {
	left: -30rpx;
}

.no-more-text::after {
	right: -30rpx;
	transform: rotate(180deg);
}
</style>