<template>
<view class="container" :style="{backgroundColor:pageinfo.bgcolor}">
	<dp :pagecontent="pagecontent"></dp>
	<block v-if="xixie">
        <dp-xixie-mendian-log ></dp-xixie-mendian-log>
    </block>
	<view v-if="copyright!=''" class="copyright">{{copyright}}</view>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
		<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
	return {
			opt:{},
			loading:false,
      isload: false,
			pageinfo: [],
			pagecontent: [],
			copyright:'',
			xixie:false
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata(); 
	},
	onPullDownRefresh:function(e){
		this.getdata();
	},
	methods: {
		getdata:function(){
			var that = this;
			that.loading = true;
			app.get('ApiMy/usercenter',{},function (data){
				that.loading = false;
			  var pagecontent = data.pagecontent;
				that.pageinfo = data.pageinfo;
				that.pagecontent = data.pagecontent;
				that.copyright = data.copyright;
				uni.setNavigationBarTitle({
					title: data.pageinfo.title
				});
				if(data.xixie){
                    that.xixie = data.xixie;
                }
				that.loaded();
			});
		}
	}
}
</script>
<style>

</style>