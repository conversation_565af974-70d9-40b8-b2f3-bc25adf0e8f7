<template>

	<view>
		
		<view class="dp-search">
			
			<view class="dp-search-search">
				<view class="dp-search-search-f1"></view>
				<view class="dp-search-search-f2">
					<input class="dp-search-search-input"  @input="inputKeyword"  name="keyword"
						placeholder="输入城市名查询" placeholder-style="color:#aaa;font-size:28rpx"/>  
				</view> 
			</view>
			
		</view>
		
		<view class="tab-main" v-if="hotarea == 1">  
			
<!-- 			<view class="tit">历史访问城市</view>
			
			<view class="tab-ll">
				
				<view class="tab" v-for="(item, index) in hs_list" :key="index">{{item}}</view>
	
			</view> -->
			
			
			<view class="tit" v-if="hot_list.length>0">国内热门城市</view>
			
			<view class="tab-ll" v-if="hot_list.length>0">
				
				<view class="tab" v-for="(item, index) in hot_list" :key="index" @click="selectOne(index)">{{item}}</view>
	
			</view> 

			
		</view>


		<t-index-address @select="select" :data="data"></t-index-address>

	</view>

</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
                 data : '' ,  
				 data_c : '',
				 hot_list : [],
				 hot_ids : [],
				 hotarea : 0
				 
			};  
		},
		onLoad: function(opt) {
			if(opt.data){
				 let json = JSON.parse(decodeURIComponent(opt.data));

				 this.hot_list = json.area_config.hotareas_str.split('、');
				 
				 this.hot_ids = json.area_config.hotareas.split(',');
				 
				 this.hotarea = json.area_config.hotarea;
				 
				 if(json.area_config.switcharearange == 0){
					 
					 let data = json.areas;
					 
					 if(json.area_config.switcharearangeareas_str && json.area_config.switcharearangeareas_str.length > 0) {
						 Object.keys(data).forEach((key) => {
							let arr = [];
							data[key].filter(m=>{
								if(json.area_config.switcharearangeareas_str.indexOf(m.name)!=-1){
									arr.push(m);
								}
							}) 
							if(arr.length>0){
								data[key] = arr;
							}else{
								this.$delete(data,key)
							}
						 });
					 }
					 
					 this.data = data;
					 this.data_c = JSON.stringify(data);
					 
				 }else{
					 this.data  = json.areas;
					 
					 this.data_c = JSON.stringify(json.areas);
				 }

				
			}
		},  
		methods: {
			select(data) {
				console.log('2023-06-15 11:05:23-INFO-[city][select_001] 选择城市:', data);
				// 确保数据包含first_letter字段
				if(data && !data.first_letter && data.name) {
					let firstChar = data.name.substring(0, 1);
					let pinyinChar = this.getPinyinFirstChar(firstChar);
					data.first_letter = pinyinChar;
				}
				uni.$emit("city", data);
				uni.navigateBack();
			},
			selectOne(index){
				let obj = {
					id: this.hot_ids[index],
					name: this.hot_list[index],
					// 为热门城市添加first_letter字段
					first_letter: this.getPinyinFirstChar(this.hot_list[index].substring(0, 1))
				}
				
				console.log('2023-06-15 11:05:23-INFO-[city][selectOne_001] 选择热门城市:', obj);
				uni.$emit("city", obj);
				uni.navigateBack();
			},
			// 获取汉字的拼音首字母
			getPinyinFirstChar(char) {
				// 这里使用简单判断，实际应该使用拼音库
				// 返回大写字母
				const charCode = char.charCodeAt(0);
				// 简单的映射表，不全面
				if (charCode >= 0x4e00 && charCode <= 0x9fff) {
					// 是汉字，根据区域简单判断（不精确）
					if (charCode < 0x5516) return 'A';
					if (charCode < 0x5635) return 'B';
					if (charCode < 0x598F) return 'C';
					if (charCode < 0x5C7E) return 'D';
					if (charCode < 0x5E7A) return 'E';
					if (charCode < 0x61D2) return 'F';
					if (charCode < 0x64B0) return 'G';
					if (charCode < 0x6816) return 'H';
					if (charCode < 0x6A47) return 'J';
					if (charCode < 0x6D46) return 'K';
					if (charCode < 0x706C) return 'L';
					if (charCode < 0x7230) return 'M';
					if (charCode < 0x784C) return 'N';
					if (charCode < 0x7B4C) return 'P';
					if (charCode < 0x7F9E) return 'Q';
					if (charCode < 0x8574) return 'R';
					if (charCode < 0x8C46) return 'S';
					if (charCode < 0x8E8F) return 'T';
					if (charCode < 0x9006) return 'W';
					if (charCode < 0x92A8) return 'X';
					if (charCode < 0x973C) return 'Y';
					return 'Z';
				}
				// 如果不是汉字，则返回字符本身的大写形式
				return char.toUpperCase();
			},
			inputKeyword(e){
				
	            let that = this 
				let name  = e.detail.value;
				
				let obj = JSON.parse(that.data_c);
				
				if(name!=''){
		
					Object.keys(obj).forEach((key) => {
						let arr = [];
						obj[key].filter(m=>{
							if(m.name.indexOf(name)!=-1){
								arr.push(m);
							}
						})
						if(arr.length>0){
							obj[key] = arr;
						}else{
							that.$delete(obj,key)
						}

					});
				}
				
		        uni.$emit("syncCity",obj);
				

				
			}
		}
	};
</script>

<style>
	
	.dp-search {padding:20rpx;height: auto; position: relative;display: flex;align-items: center;background: #fff}
	.dp-search-search {height:72rpx;background: #EFEFEF;border-radius: 50rpx;overflow: hidden;display:flex;width:100%;}
	.dp-search-search-f1 {height:72rpx;width:72rpx;color: #888;border: 0px;padding: 0px;margin: 0px;background:url('~@/static/img/search_ico.png') center no-repeat; background-size:30rpx;}
	.dp-search-search-f2{height: 72rpx;flex:1}
	.dp-search-search-f3 {height:72rpx;width:72rpx;color: #666;border: 0px;padding: 0px;margin: 0px;background-position: center;background-repeat: no-repeat; background-size:40rpx;}
	.dp-search-search-input {height:72rpx;width: 100%;border: 0px;padding: 0px;margin: 0px;outline: none;color: #666;}
	
	.tab-main{
		background: #fff;
		padding: 15px;
	}
	
	.tit{
		font-size: 24rpx;
		color: #aaa;
	}
	
	.tab-ll{
		 flex-wrap: wrap;
		 display: flex;
	}
	
	.tab{
		padding: 5px 10px;
		border: 1rpx solid #aaa;
		margin: 10px 10px 10px 0;
		border-radius: 5px;
	}
	
	
</style>