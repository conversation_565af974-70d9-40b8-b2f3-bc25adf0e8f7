<template>
  <view class="calendar-page">
    <view class="header">
      <view class="month-selector">
        <view class="selector-btn prev" @tap="changeMonth(-1)">
          <text>上一个月</text>
        </view>
        <view class="current-month">{{currentYear}}年{{currentMonth}}月</view>
        <view class="selector-btn next" @tap="changeMonth(1)">
          <text>下一个月</text>
        </view>
      </view>
    </view>

    <view class="calendar">
      <view class="calendar-header">
        <view class="weekday" v-for="(day, index) in weekdays" :key="index">{{day}}</view>
      </view>
      <view class="calendar-body">
        <view 
          class="calendar-day" 
          v-for="(day, index) in days" 
          :key="index"
          :class="{
            'empty': !day.date, 
            'today': day.isToday,
            'has-orders': day.count > 0
          }"
          :style="day.isToday ? 'background-color:rgba(' + t('color1rgb') + ',0.1)' : ''"
          @tap="day.date && showDayOrders(day)"
        >
          <text class="day-number" :style="day.isToday ? 'color:' + t('color1') : ''">{{day.day}}</text>
          <view v-if="day.count > 0" class="order-dots">
            <view 
              v-if="day.status_count && day.status_count['1'] > 0" 
              class="dot status-1"
              :style="'opacity:' + (day.status_count['1'] > 0 ? 1 : 0.3)"
            ></view>
            <view 
              v-if="day.status_count && day.status_count['2'] > 0" 
              class="dot status-2"
              :style="'opacity:' + (day.status_count['2'] > 0 ? 1 : 0.3)"
            ></view>
            <view 
              v-if="day.status_count && day.status_count['3'] > 0" 
              class="dot status-3"
              :style="'opacity:' + (day.status_count['3'] > 0 ? 1 : 0.3)"
            ></view>
            <view 
              v-if="day.status_count && (day.status_count['4'] > 0 || day.status_count['5'] > 0)" 
              class="dot status-4"
              :style="'opacity:' + ((day.status_count['4'] > 0 || day.status_count['5'] > 0) ? 1 : 0.3)"
            ></view>
          </view>
          <view v-if="day.count > 0" class="order-count" :style="'background-color:' + t('color1')">{{day.count}}</view>
        </view>
      </view>
    </view>

    <view class="legend">
      <view class="legend-item">
        <view class="dot status-1"></view>
        <text>已支付</text>
      </view>
      <view class="legend-item">
        <view class="dot status-2"></view>
        <text>已派单</text>
      </view>
      <view class="legend-item">
        <view class="dot status-3"></view>
        <text>已完成</text>
      </view>
      <view class="legend-item">
        <view class="dot status-4"></view>
        <text>评价</text>
      </view>
    </view>

    <view v-if="showOrderList" class="order-list-popup">
      <view class="popup-header">
        <text class="date-text">{{selectedDate}}</text>
        <text class="close-btn" @tap="closeOrderList">×</text>
      </view>
      <view class="status-filter">
        <view 
          v-for="(status, index) in statusOptions" 
          :key="index" 
          class="filter-item" 
          :class="{active: selectedStatus === status.value}"
          :style="selectedStatus === status.value ? 'background-color:' + t('color1') : ''"
          @tap="filterByStatus(status.value)"
        >
          {{status.label}}
        </view>
      </view>
      
      <!-- 订单列表，参考jdorderlist样式 -->
      <scroll-view scroll-y class="order-list">
        <view v-if="dayOrders.length === 0" class="no-orders">
          <text>当天没有订单</text>
        </view>
        
        <block v-for="(item, index) in dayOrders" :key="item.id">
          <view class="order-box" @tap="viewOrderDetail(item.id)">
            <view class="head">
              <view class="f1">
                <image :src="pre_url+'/static/peisong/ps_time.png'" class="img"/>
                <text v-if="item.status == 1">已支付</text>
                <text v-else-if="item.status == 2">已派单</text>
                <text v-else-if="item.status == 3">已完成</text>
                <text v-else-if="item.status == 4">待评价</text>
                <text v-else-if="item.status == 5">已评价</text>
                <text v-else>未知状态</text>
                <text class="t1" :style="'color:' + t('color1')">{{item.yy_time}}</text>
              </view>
              <view class="flex1"></view>
              <view class="f2"><text class="t1">{{item.product_price}}</text>元</view>
            </view>
            
            <view class="content">
              <view class="content-info">
                <view class="info-item">
                  <text class="label">订单号：</text>
                  <text class="value">{{item.ordernum}}</text>
                </view>
                <view class="info-item">
                  <text class="label">商品名称：</text>
                  <text class="value">{{item.product_name}}</text>
                </view>
                <view class="info-item">
                  <text class="label">客户信息：</text>
                  <text class="value">{{item.member_name}} {{item.member_tel}}</text>
                </view>
                <view class="info-item">
                  <text class="label">地址：</text>
                  <text class="value">{{item.address}}</text>
                </view>
                <view class="info-item">
                  <text class="label">下单时间：</text>
                  <text class="value">{{item.create_time_formatted}}</text>
                </view>
              </view>
            </view>
            
            <view class="op">
              <view class="t1" v-if="item.status == 1">等待服务</view>
              <view class="t1" v-if="item.status == 2">服务中</view>
              <view class="t1" v-if="item.status == 3">已完成</view>
              <view class="t1" v-if="item.status == 4 || item.status == 5">已评价</view>
              <view class="flex1"></view>
              
              <!-- 所有按钮都改为跳转到相应页面 -->
              <view class="btn1" v-if="item.status == 1" @tap.stop="viewOrderDetail(item.id)" :style="'background:' + t('color1')">开始服务</view>
              <view class="btn1" v-if="item.status == 2" @tap.stop="viewOrderDetail(item.id)" :style="'background:' + t('color1')">完成服务</view>
              <view class="btn1" v-if="item.status == 3" @tap.stop="viewOrderDetail(item.id)" :style="'background:' + t('color1')">查看详情</view>
              <view class="btn1" v-if="item.status == 4 || item.status == 5" @tap.stop="viewOrderDetail(item.id)" :style="'background:' + t('color1')">查看评价</view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>

    <!-- 评价详情弹窗 -->
    <view v-if="showComment" class="comment-popup">
      <view class="popup-header">
        <text class="title">评价详情</text>
        <text class="close-btn" @tap="closeComment">×</text>
      </view>
      <scroll-view scroll-y class="comment-content">
        <view v-if="!commentData || (!commentData.worker_comment && !commentData.product_comment)" class="no-comment">
          <text>暂无评价信息</text>
        </view>
        <view v-else>
          <view v-if="commentData.worker_comment" class="comment-section">
            <view class="section-title">服务评价</view>
            <view class="comment-stars">
              <text v-for="n in 5" :key="n" class="star" :class="{active: n <= commentData.worker_comment.star}">★</text>
              <text class="level-text">{{getLevelText(commentData.worker_comment.level)}}</text>
            </view>
            <view class="comment-text">{{commentData.worker_comment.content}}</view>
            <view v-if="commentData.worker_comment.content_pic_array && commentData.worker_comment.content_pic_array.length > 0" class="comment-images">
              <image 
                v-for="(img, idx) in commentData.worker_comment.content_pic_array" 
                :key="idx" 
                :src="img" 
                mode="aspectFill"
                @tap="previewImage(commentData.worker_comment.content_pic_array, idx)"
              ></image>
            </view>
          </view>
          
          <view v-if="commentData.product_comment" class="comment-section">
            <view class="section-title">商品评价</view>
            <view class="comment-stars">
              <text v-for="n in 5" :key="n" class="star" :class="{active: n <= commentData.product_comment.score}">★</text>
            </view>
            <view class="comment-text">{{commentData.product_comment.content}}</view>
            <view v-if="commentData.product_comment.content_pic_array && commentData.product_comment.content_pic_array.length > 0" class="comment-images">
              <image 
                v-for="(img, idx) in commentData.product_comment.content_pic_array" 
                :key="idx" 
                :src="img" 
                mode="aspectFill"
                @tap="previewImage(commentData.product_comment.content_pic_array, idx)"
              ></image>
            </view>
            <view v-if="commentData.product_comment.reply_content" class="reply-section">
              <view class="reply-title" :style="'color:' + t('color1')">商家回复</view>
              <view class="reply-text">{{commentData.product_comment.reply_content}}</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <view class="tabbar">
      <view class="tabbar-bot"></view>
      <view class="tabbar-bar" style="background-color:#ffffff">
        <view @tap="goto" data-url="dating" data-opentype="reLaunch" class="tabbar-item">
          <view class="tabbar-image-box">
            <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/home.png'"></image>
          </view>
          <view class="tabbar-text">大厅</view>
        </view>
        <view @tap="goto" data-url="jdorderlist" data-opentype="reLaunch" class="tabbar-item">
          <view class="tabbar-image-box">
            <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/order.png'"></image>
          </view>
          <view class="tabbar-text">订单</view>
        </view>
        <view @tap="goto" data-url="jdorderlist?st=3" data-opentype="reLaunch" class="tabbar-item">
          <view class="tabbar-image-box">
            <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/orderwc.png'"></image>
          </view>
          <view class="tabbar-text">已完成</view>
        </view>
        <view v-if="showform" @tap="goto" data-url="formlog" data-opentype="reLaunch" class="tabbar-item">
          <view class="tabbar-image-box">
            <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/dangan.png'"></image>
          </view>
          <view class="tabbar-text">档案</view>
        </view>
        <view @tap="goto" data-url="calendar" data-opentype="reLaunch" class="tabbar-item">
          <view class="tabbar-image-box">
            <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/calendar.png'"></image>
          </view>
          <view class="tabbar-text active" :style="'color:' + t('color1')">日历</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      pre_url: app.globalData.pre_url,
      showform: 0,
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      days: [],
      worker_id: 0,
      showOrderList: false,
      selectedDate: '',
      selectedDay: null,
      dayOrders: [],
      selectedStatus: '', // 筛选状态
      statusOptions: [
        { label: '全部', value: '' },
        { label: '已支付', value: '1' },
        { label: '已派单', value: '2' },
        { label: '已完成', value: '3' },
        { label: '评价', value: '4,5' }
      ],
      showComment: false,
      commentData: null,
      loading: false,
      isload: true
    };
  },
  onLoad: function (opt) {
    this.getWorkerInfo();
  },
  onPullDownRefresh: function () {
    this.getCalendarData();
    setTimeout(function () {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  methods: {
    t: function(name) {
      return app.t(name);
    },
    getWorkerInfo() {
      var that = this;
      app.get('ApiYuyueWorker/my', {}, function (res) {
        that.worker_id = res.worker.id;
        that.showform = res.showform;
        that.getCalendarData();
      });
    },
    getCalendarData() {
      var that = this;
      const timestamp = new Date().getTime();
      app.get('ApiYuyue/worker_calendar', {
        worker_id: that.worker_id,
        year: that.currentYear,
        month: that.currentMonth,
        _t: timestamp
      }, function (res) {
        if (res.status == 1) {
          // 生成日历数据
          that.generateCalendar(res.data);
        } else {
          app.alert(res.msg);
        }
      });
    },
    generateCalendar(calendarData) {
      const year = this.currentYear;
      const month = this.currentMonth;
      const today = new Date();
      const firstDay = new Date(year, month - 1, 1);
      const lastDay = new Date(year, month, 0);
      const daysInMonth = lastDay.getDate();
      const startingDay = firstDay.getDay(); // 0 (Sunday) to 6 (Saturday)

      let days = [];

      // 填充月份开始前的空白
      for (let i = 0; i < startingDay; i++) {
        days.push({
          date: '',
          day: '',
          isToday: false,
          count: 0
        });
      }

      // 填充日期
      for (let i = 1; i <= daysInMonth; i++) {
        const dateStr = `${year}-${month.toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`;
        const dayData = calendarData.find(item => item.date === dateStr) || { count: 0, status_count: {} };
        
        days.push({
          date: dateStr,
          day: i,
          isToday: year === today.getFullYear() && month === today.getMonth() + 1 && i === today.getDate(),
          count: dayData.count || 0,
          status_count: dayData.status_count || {}
        });
      }

      // 填充月份结束后的空白，补齐42个格子（6行7列）
      const totalSlots = 42;
      const remainingSlots = totalSlots - days.length;
      for (let i = 0; i < remainingSlots; i++) {
        days.push({
          date: '',
          day: '',
          isToday: false,
          count: 0
        });
      }

      this.days = days;
    },
    changeMonth(diff) {
      let year = this.currentYear;
      let month = this.currentMonth + diff;

      if (month > 12) {
        month = 1;
        year++;
      } else if (month < 1) {
        month = 12;
        year--;
      }

      this.currentYear = year;
      this.currentMonth = month;
      this.getCalendarData();
    },
    showDayOrders(day) {
      if (!day.date || day.count === 0) return;
      
      this.selectedDate = day.date;
      this.selectedDay = day;
      this.selectedStatus = '';
      this.getDayOrders();
      this.showOrderList = true;
    },
    getDayOrders() {
      if (!this.selectedDate) return;
      
      var that = this;
      const timestamp = new Date().getTime();
      const params = {
        worker_id: that.worker_id,
        date: that.selectedDate,
        _t: timestamp
      };
      
      if (that.selectedStatus) {
        params.status = that.selectedStatus;
      }
      
      app.get('ApiYuyue/worker_date_orders', params, function (res) {
        if (res.status == 1) {
          that.dayOrders = res.data || [];
          console.log('获取日期订单成功：', that.dayOrders);
        } else {
          app.alert(res.msg);
          that.dayOrders = [];
        }
      });
    },
    closeOrderList() {
      this.showOrderList = false;
      this.dayOrders = [];
    },
    filterByStatus(status) {
      this.selectedStatus = status;
      this.getDayOrders();
    },
    viewOrderDetail(orderId) {
      app.goto('jdorderdetail?id=' + orderId);
    },
    setOrderStatus(orderId, status) {
      let that = this;
      let statusText = '';
      
      switch(status) {
        case 2:
          statusText = '开始服务';
          break;
        case 3:
          statusText = '完成服务';
          break;
        default:
          statusText = '更新状态';
      }
      
      uni.showModal({
        title: '提示',
        content: '确定要' + statusText + '吗？',
        success: function(res) {
          if (res.confirm) {
            app.post('ApiYuyueWorker/updateOrderStatus', {
              order_id: orderId,
              status: status
            }, function(response) {
              if (response.status == 1) {
                app.toast(statusText + '成功');
                // 更新订单状态后重新获取订单列表
                that.getDayOrders();
              } else {
                app.alert(response.msg || statusText + '失败');
              }
            });
          }
        }
      });
    },
    viewComment(orderId) {
      var that = this;
      const timestamp = new Date().getTime();
      app.get('ApiYuyue/order_comment', {
        order_id: orderId,
        _t: timestamp
      }, function (res) {
        if (res.status == 1) {
          that.commentData = res.data;
          that.showComment = true;
        } else {
          app.alert(res.msg || '获取评价失败');
        }
      });
    },
    closeComment() {
      this.showComment = false;
      this.commentData = null;
    },
    getLevelText(level) {
      switch (level) {
        case 1: return '好评';
        case 2: return '中评';
        case 3: return '差评';
        default: return '';
      }
    },
    previewImage(images, index) {
      uni.previewImage({
        urls: images,
        current: images[index]
      });
    },
    goto: function (e) {
      let url;
      if (typeof e === 'string') {
        url = e;
      } else {
        url = e.currentTarget.dataset.url;
      }
      let opentype = e.currentTarget.dataset.opentype || '';
      if (!url) return false;
      app.goto(url, opentype);
    }
  }
};
</script>

<style>
.calendar-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  position: relative;
  font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
}

.header {
  padding: 30rpx 30rpx;
  background: #fff;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border-radius: 0 0 24rpx 24rpx;
}

.month-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
}

.selector-btn {
  padding: 0 20rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 30rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.03);
  font-size: 24rpx;
  color: #606266;
}

.selector-btn:active {
  transform: scale(0.95);
  background-color: #eef0f6;
}

.current-month {
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  background: linear-gradient(120deg, #2c3e50, #4a5568);
  -webkit-background-clip: text;
  color: transparent;
  position: relative;
}

.current-month::after {
  content: "";
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, rgba(0,0,0,0), rgba(0,0,0,0.1), rgba(0,0,0,0));
}

.calendar {
  background-color: #fff;
  border-radius: 24rpx;
  margin: 0 20rpx 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(235, 238, 245, 0.8);
}

.calendar-header {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(235, 238, 245, 0.8);
  background: linear-gradient(to bottom, #fafbfc, #fff);
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #909399;
  font-weight: 500;
}

.calendar-body {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx 0;
}

.calendar-day {
  width: 14.28%;
  height: 130rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 10rpx 0;
  transition: all 0.2s ease;
  overflow: hidden;
}

.calendar-day::before {
  content: "";
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  right: 8rpx;
  bottom: 8rpx;
  border-radius: 16rpx;
  z-index: -1;
  transition: all 0.3s ease;
  opacity: 0;
}

.calendar-day:active::before {
  opacity: 0.05;
  background-color: #000;
}

.has-orders::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.02);
  z-index: -2;
}

.day-number {
  font-size: 30rpx;
  margin-bottom: 12rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
  transition: transform 0.2s ease;
}

.today .day-number {
  font-weight: 600;
  transform: scale(1.1);
}

.empty .day-number {
  opacity: 0;
}

.has-orders {
  background-color: rgba(250, 250, 252, 0.7);
}

.order-dots {
  display: flex;
  justify-content: center;
  margin-top: 5rpx;
  position: relative;
  z-index: 1;
}

.dot {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  margin: 0 3rpx;
  position: relative;
  overflow: hidden;
}

.dot::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  opacity: 0.2;
  background-color: inherit;
  transform: scale(1.6);
  z-index: -1;
}

.status-1 {
  background-color: #ff9800; /* 已支付 */
}

.status-2 {
  background-color: #2196f3; /* 已派单 */
}

.status-3 {
  background-color: #4caf50; /* 已完成 */
}

.status-4 {
  background-color: #9c27b0; /* 评价 */
}

.order-count {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  min-width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
  padding: 0 6rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.legend {
  display: flex;
  justify-content: space-around;
  padding: 26rpx 30rpx;
  background-color: #fff;
  margin: 0 20rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.04);
}

.legend-item {
  display: flex;
  align-items: center;
}

.legend-item .dot {
  margin-right: 10rpx;
  width: 14rpx;
  height: 14rpx;
}

.legend-item text {
  font-size: 24rpx;
  color: #606266;
}

.order-list-popup, .comment-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 999;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(5%);
    opacity: 0.8;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.popup-header {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f2f5;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.02);
}

.date-text, .title {
  font-size: 34rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44rpx;
  color: #909399;
  border-radius: 50%;
  background-color: #f5f7fa;
  transition: all 0.2s ease;
}

.close-btn:active {
  transform: scale(0.95);
  background-color: #eef0f5;
}

.status-filter {
  display: flex;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #f0f2f5;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}

.filter-item {
  padding: 12rpx 30rpx;
  background-color: #f5f7fa;
  border-radius: 30rpx;
  margin-right: 20rpx;
  font-size: 26rpx;
  white-space: nowrap;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}

.filter-item:active {
  transform: scale(0.98);
}

.filter-item.active {
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.order-list, .comment-content {
  flex: 1;
  padding: 20rpx;
  height: 0; /* 确保flex布局下能正确计算高度 */
  overflow: hidden; /* 防止内容溢出 */
}

.no-orders, .no-comment {
  text-align: center;
  padding: 120rpx 0;
  color: #909399;
  font-size: 28rpx;
}

.no-orders::before, .no-comment::before {
  content: "📅";
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

/* 订单列表样式，参考jdorderlist */
.order-box {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1rpx solid rgba(235, 238, 245, 0.8);
  transition: all 0.2s ease;
}

.order-box:active {
  transform: scale(0.99);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.head {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(to bottom, #fafbfc, #fff);
  border-bottom: 1rpx solid #f0f2f5;
}

.head .f1 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #303133;
}

.head .img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.head .t1 {
  margin-left: 10rpx;
  font-weight: 500;
}

.flex1 {
  flex: 1;
}

.head .f2 {
  font-size: 28rpx;
  color: #ff5722;
  font-weight: 500;
}

.head .f2 .t1 {
  font-weight: 600;
  font-size: 34rpx;
}

.content {
  padding: 24rpx;
}

.content-info {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  margin-bottom: 14rpx;
}

.info-item .label {
  width: 170rpx;
  color: #909399;
  font-size: 28rpx;
}

.info-item .value {
  flex: 1;
  color: #303133;
  font-size: 28rpx;
}

.op {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  border-top: 1rpx solid #f0f2f5;
  background-color: #fafbfc;
}

.op .t1 {
  font-size: 28rpx;
  color: #606266;
  letter-spacing: 1rpx;
}

.op .btn1 {
  padding: 14rpx 32rpx;
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.op .btn1:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 评价详情样式 */
.comment-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(235, 238, 245, 0.8);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  color: #303133;
  border-bottom: 1rpx solid #f0f2f5;
  padding-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.comment-stars {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.star {
  font-size: 38rpx;
  color: #dcdfe6;
  margin-right: 5rpx;
  transition: all 0.3s ease;
}

.star.active {
  color: #ffba00;
  text-shadow: 0 2rpx 8rpx rgba(255, 186, 0, 0.3);
}

.level-text {
  margin-left: 20rpx;
  font-size: 26rpx;
  color: #ff9800;
  padding: 6rpx 18rpx;
  background-color: rgba(255, 152, 0, 0.08);
  border-radius: 20rpx;
  font-weight: 500;
}

.comment-text {
  font-size: 28rpx;
  color: #303133;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
}

.comment-images image {
  width: 160rpx;
  height: 160rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.comment-images image:active {
  transform: scale(0.98);
}

.reply-section {
  margin-top: 24rpx;
  background-color: #f9f9fc;
  padding: 20rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(235, 238, 245, 0.8);
}

.reply-title {
  font-size: 26rpx;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.reply-text {
  font-size: 26rpx;
  color: #606266;
  line-height: 1.6;
}

/* Tabbar样式 */
.tabbar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 10;
}

.tabbar-bot {
  width: 100%;
  height: 20rpx;
  background-color: #ffffff;
}

.tabbar-bar {
  width: 100%;
  height: 110rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  border-top: 1rpx solid rgba(235, 238, 245, 0.8);
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
}

.tabbar-item:active {
  transform: scale(0.96);
}

.tabbar-image-box {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tabbar-icon {
  width: 44rpx;
  height: 44rpx;
  transition: all 0.2s ease;
}

.tabbar-text {
  font-size: 22rpx;
  font-weight: 400;
  color: #909399;
  margin-top: 4rpx;
  transition: all 0.2s ease;
}

.tabbar-text.active {
  font-weight: 500;
}
</style> 