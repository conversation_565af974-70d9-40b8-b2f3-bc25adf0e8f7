<template>
<view class="container">
	<view class="order-info" v-if="purchaseOrder.order_no">
		<view class="section-title">进货单信息</view>
		<view class="order-header">
			<text class="order-no">订单号: {{purchaseOrder.order_no}}</text>
			<text class="order-status" :style="{color: getStatusColor(purchaseOrder.status)}">{{getStatusText(purchaseOrder.status)}}</text>
		</view>
		<view class="order-detail">
			<view class="detail-row">
				<text class="detail-label">总金额:</text>
				<text class="detail-value">￥{{purchaseOrder.total_price}}</text>
			</view>
			<view class="detail-row">
				<text class="detail-label">下单时间:</text>
				<text class="detail-value">{{purchaseOrder.create_time}}</text>
			</view>
		</view>
	</view>

	<view class="section-title">可退商品</view>
	<view class="product-list" v-if="productList.length > 0">
		<view class="product-item" v-for="(item, index) in productList" :key="index">
			<view class="product-checkbox">
				<checkbox :checked="isProductSelected(item.id)" @tap="toggleSelectProduct(item)" />
			</view>
			<image class="product-img" :src="item.product_pic || (item.product_detail ? item.product_detail.pic : item.pic)" mode="aspectFill"></image>
			<view class="product-info">
				<text class="product-name">{{item.product_name || (item.product_detail ? item.product_detail.name : item.name)}}</text>
				<view class="product-price">￥{{item.price || (item.product_detail ? (item.product_detail.sell_price || item.product_detail.cost_price) : (item.sell_price || item.cost_price || 0))}}</view>
				<view class="product-quantity">可退数量: {{item.available_for_return !== undefined ? item.available_for_return : item.quantity}}</view>
			</view>
			<view class="product-action" v-if="isProductSelected(item.id)">
				<view class="quantity-control">
					<view class="minus" @tap="updateQuantity(item, -1)">-</view>
					<input type="number" class="quantity-input" :value="getSelectedQuantity(item.id)" @input="onQuantityInput($event, item)" />
					<view class="plus" @tap="updateQuantity(item, 1)">+</view>
				</view>
			</view>
		</view>
	</view>
	<nodata v-if="productList.length === 0 && !loading"></nodata>
	<loading v-if="loading"></loading>

	<view class="remark-section">
		<view class="section-title">退货原因</view>
		<textarea class="remark-input" v-model="remark" placeholder="请输入退货原因（必填）"></textarea>
	</view>

	<view class="bottom-bar" v-if="selectedProducts.length > 0">
		<view class="order-summary">
			<text class="summary-text">已选择 {{getTotalItems()}} 种商品</text>
			<text class="summary-price">退款: ￥{{getTotalPrice()}}</text>
		</view>
		<view class="order-actions">
			<view class="action-btn submit" @tap="submitOrder">提交申请</view>
		</view>
	</view>
	
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      purchaseId: 0,
      purchaseOrder: {},
      loading: false,
      productList: [],
      selectedProducts: [], // 选中的商品 [{id, item_id, quantity}]
      remark: ''
    };
  },
  onLoad: function(opt) {
    this.opt = app.getopts(opt);
    this.purchaseId = this.opt.purchase_id || 0;
    
    if (!this.purchaseId) {
      app.error('缺少进货单ID');
      setTimeout(function() {
        app.goback();
      }, 1500);
      return;
    }
    
    this.getPurchaseOrderDetail();
  },
  methods: {
    getPurchaseOrderDetail: function() {
      var that = this;
      
      app.post('ApiAdminPurchase/getPurchaseOrderDetail', {
        id: that.purchaseId
      }, function(res) {
        if (res.code === 0) {
          that.purchaseOrder = res.data.order || {};
          
          console.log('获取到订单详情:', that.purchaseOrder);
          
          // 如果API返回了商品列表，直接处理
          if (res.data.items && res.data.items.length > 0) {
            that.processOrderItems(res.data.items);
          } else {
            // 如果没有包含商品数据，需要再次获取
            that.getOrderItems();
          }
          
          // 检查订单状态是否允许退货
          if (that.purchaseOrder.status != 1 && that.purchaseOrder.status != 3) {
            that.loading = false;
            app.error('只有已通过或已完成的订单才能申请退货');
            setTimeout(function() {
              app.goback();
            }, 1500);
            return;
          }
          
          // 如果订单没有基本信息，提示错误
          if (!that.purchaseOrder.id || !that.purchaseOrder.order_no) {
            that.loading = false;
            app.error('订单信息不完整，无法申请退货');
            setTimeout(function() {
              app.goback();
            }, 1500);
            return;
          }
        } else {
          that.loading = false;
          app.error(res.msg || '获取订单详情失败');
          setTimeout(function() {
            app.goback();
          }, 1500);
        }
      });
    },
    getOrderItems: function() {
      var that = this;
      
      app.post('ApiAdminPurchase/getPurchaseOrderItems', {
        purchase_order_id: that.purchaseId
      }, function(res) {
        that.loading = false;
        
        if (res.code === 0) {
          var items = res.data.items || [];
          console.log('获取到订单商品:', items);
          that.processOrderItems(items);
        } else {
          app.error(res.msg || '获取商品数据失败');
          setTimeout(function() {
            app.goback();
          }, 1500);
        }
      });
    },
    processOrderItems: function(items) {
      var that = this;
      
      // 处理空商品列表情况
      if (!items || items.length === 0) {
        console.log('订单没有可退商品:', that.purchaseId);
        app.error('该订单没有可退商品');
        setTimeout(function() {
          app.goback();
        }, 1500);
        return;
      }
      
      // 处理商品数据，确保字段一致性
      items.forEach(function(item) {
        // 确保图片字段
        if (!item.pic && item.product_pic) {
          item.pic = item.product_pic;
        }
        if (!item.pic && item.product_detail && item.product_detail.pic) {
          item.pic = item.product_detail.pic;
        }
        
        // 确保名称字段
        if (!item.name && item.product_name) {
          item.name = item.product_name;
        }
        if (!item.name && item.product_detail && item.product_detail.name) {
          item.name = item.product_detail.name;
        }
        
        // 可退数量处理 - 确保有正确的可退数量
        if (item.available_for_return === undefined && item.quantity !== undefined) {
          item.available_for_return = item.quantity - (item.returned_quantity || 0);
        } else if (item.available_for_return === undefined) {
          item.available_for_return = 0; // 默认设为0，避免undefined
        }
      });
      
      // 过滤出可退数量大于0的商品
      items = items.filter(function(item) {
        return (item.available_for_return || 0) > 0;
      });
      
      // 再次检查过滤后是否还有可退商品
      if (items.length === 0) {
        console.log('订单所有商品已退货完毕:', that.purchaseId);
        app.error('该订单所有商品已退货完毕，没有可退商品');
        setTimeout(function() {
          app.goback();
        }, 1500);
        return;
      }
      
      that.productList = items;
      that.loading = false;
      console.log('处理后的商品数据:', that.productList);
    },
    getStatusText: function(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回',
        3: '已完成'
      };
      return statusMap[status] || '未知状态';
    },
    getStatusColor: function(status) {
      const colorMap = {
        0: '#FF9800', // 待审核 - 橙色
        1: '#4CAF50', // 已通过 - 绿色
        2: '#F44336', // 已驳回 - 红色
        3: '#2196F3'  // 已完成 - 蓝色
      };
      return colorMap[status] || '#999999';
    },
    isProductSelected: function(productId) {
      return this.selectedProducts.findIndex(item => item.id === productId) > -1;
    },
    toggleSelectProduct: function(product) {
      var that = this;
      var index = that.selectedProducts.findIndex(item => item.id === product.id);
      
      if (index > -1) {
        // 如果已选中，则取消选中
        that.selectedProducts.splice(index, 1);
      } else {
        // 如果未选中，则添加到选中列表，默认数量为1
        that.selectedProducts.push({
          id: product.id,
          item_id: product.item_id,
          quantity: 1
        });
      }
    },
    getSelectedQuantity: function(productId) {
      var selected = this.selectedProducts.find(item => item.id === productId);
      return selected ? selected.quantity : 0;
    },
    updateQuantity: function(product, delta) {
      var that = this;
      var index = that.selectedProducts.findIndex(item => item.id === product.id);
      var maxQuantity = product.available_for_return !== undefined ? product.available_for_return : product.quantity;
      
      if (index > -1) {
        var newQuantity = that.selectedProducts[index].quantity + delta;
        
        if (newQuantity <= 0) {
          // 如果数量减为0，则从选中列表中移除
          that.selectedProducts.splice(index, 1);
        } else if (newQuantity <= maxQuantity) {
          // 如果数量在可退范围内，则更新数量
          that.selectedProducts[index].quantity = newQuantity;
        } else {
          app.error('超出可退数量');
        }
      }
    },
    onQuantityInput: function(e, product) {
      var that = this;
      var value = parseInt(e.detail.value);
      var maxQuantity = product.available_for_return !== undefined ? product.available_for_return : product.quantity;
      
      if (isNaN(value) || value < 0) {
        value = 0;
      }
      
      if (value > maxQuantity) {
        value = maxQuantity;
        app.error('超出可退数量');
      }
      
      var index = that.selectedProducts.findIndex(item => item.id === product.id);
      
      if (value === 0 && index > -1) {
        // 如果输入为0且之前已选中，则移除
        that.selectedProducts.splice(index, 1);
      } else if (value > 0) {
        if (index > -1) {
          // 如果之前已选中，则更新数量
          that.selectedProducts[index].quantity = value;
        } else {
          // 如果之前未选中，则添加
          that.selectedProducts.push({
            id: product.id,
            item_id: product.item_id,
            quantity: value
          });
        }
      }
    },
    getTotalItems: function() {
      return this.selectedProducts.length;
    },
    getTotalPrice: function() {
      var totalPrice = 0;
      
      this.selectedProducts.forEach(selectedItem => {
        var productInfo = this.productList.find(product => product.id === selectedItem.id);
        
        if (productInfo) {
          var price = productInfo.price || 
                     (productInfo.product_detail ? 
                      (productInfo.product_detail.sell_price || productInfo.product_detail.cost_price) : 
                      (productInfo.sell_price || productInfo.cost_price || 0));
          
          totalPrice += price * selectedItem.quantity;
        }
      });
      
      return totalPrice.toFixed(2);
    },
    submitOrder: function() {
      var that = this;
      
      if (that.selectedProducts.length === 0) {
        app.error('请选择要退货的商品');
        return;
      }
      
      if (!that.remark.trim()) {
        app.error('请输入退货原因');
        return;
      }
      
      var itemIds = [];
      var quantities = [];
      
      that.selectedProducts.forEach(item => {
        itemIds.push(item.item_id);
        quantities.push(item.quantity);
      });
      
      app.post('ApiAdminPurchase/createReturnOrder', {
        purchase_order_id: that.purchaseId,
        item_ids: itemIds.join(','),
        quantities: quantities.join(','),
        remark: that.remark
      }, function(res) {
        if (res.code === 0) {
          app.success('退货申请提交成功');
          setTimeout(function() {
            app.goto('../return/detail?id=' + res.data.order_id);
          }, 1000);
        } else {
          app.error(res.msg);
        }
      });
    }
  }
};
</script>

<style>
.container{ width:100%; padding-bottom:120rpx; }

.section-title{ padding:20rpx 30rpx; font-size:30rpx; font-weight:bold; color:#333; background:#f5f5f5; }

.order-info{ background:#fff; margin-bottom:20rpx; }
.order-header{ padding:20rpx 30rpx; display:flex; justify-content:space-between; border-bottom:1px solid #f0f0f0; }
.order-no{ font-size:28rpx; color:#333; font-weight:bold; }
.order-status{ font-size:28rpx; }
.order-detail{ padding:20rpx 30rpx; }
.detail-row{ display:flex; margin-bottom:10rpx; }
.detail-label{ width:140rpx; font-size:26rpx; color:#666; }
.detail-value{ flex:1; font-size:26rpx; color:#333; }

.product-list{ background:#fff; padding:10rpx 20rpx; }
.product-item{ display:flex; padding:20rpx 0; border-bottom:1px solid #f0f0f0; align-items:center; }
.product-item:last-child{ border-bottom:none; }
.product-checkbox{ width:60rpx; display:flex; justify-content:center; align-items:center; }
.product-img{ width:140rpx; height:140rpx; border-radius:8rpx; }
.product-info{ flex:1; margin-left:20rpx; display:flex; flex-direction:column; }
.product-name{ font-size:28rpx; color:#333; margin-bottom:10rpx; line-height:1.3; }
.product-price{ font-size:30rpx; color:#ff6b00; margin-bottom:10rpx; }
.product-quantity{ font-size:24rpx; color:#999; }
.product-action{ width:200rpx; }

.quantity-control{ display:flex; align-items:center; justify-content:space-between; border:1px solid #ddd; border-radius:8rpx; overflow:hidden; }
.minus, .plus{ width:60rpx; height:60rpx; line-height:60rpx; text-align:center; font-size:36rpx; background:#f5f5f5; }
.quantity-input{ flex:1; height:60rpx; text-align:center; font-size:28rpx; border-left:1px solid #ddd; border-right:1px solid #ddd; }

.remark-section{ background:#fff; margin-top:20rpx; }
.remark-input{ width:100%; height:200rpx; padding:20rpx 30rpx; box-sizing:border-box; font-size:28rpx; color:#333; }

.bottom-bar{ position:fixed; bottom:0; left:0; width:100%; height:100rpx; display:flex; background:#fff; border-top:1px solid #f0f0f0; z-index:100; }
.order-summary{ flex:1; display:flex; flex-direction:column; justify-content:center; padding-left:30rpx; }
.summary-text{ font-size:24rpx; color:#666; }
.summary-price{ font-size:30rpx; color:#ff6b00; font-weight:bold; }
.order-actions{ display:flex; align-items:center; }
.action-btn{ width:240rpx; height:70rpx; line-height:70rpx; text-align:center; font-size:28rpx; border-radius:35rpx; margin-right:20rpx; }
.action-btn.submit{ background:#1989fa; color:#fff; }
</style> 