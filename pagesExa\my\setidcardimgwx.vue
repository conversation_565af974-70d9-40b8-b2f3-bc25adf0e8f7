<template>
	<view class="container">
		<block v-if="isload">
			<form @submit="formSubmit" @reset="formReset">
				<view class="auth">
					<view class="infos">
						<view class="list">
							<text>您的姓名</text>
							<input placeholder="请输入内容"
								placeholderStyle="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;"
								border="surround" name="realname" v-model="realname"></input>
						</view>
						<view class="list">
							<text>您的身份证</text>
							<input placeholder="请输入身份证号码"
								placeholderStyle="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;"
								border="surround" name="idcard" v-model="idcard"></input>
						</view>
						<view class="list">
							<text>上传身份证头像面</text>
							<view class="upload" @click="upIdcardHead">

								<image :src="idcard_front"></image>

								<!-- 	<text>请保证身份证完整，身份证号清晰</text> -->
							</view>
						</view>

						<view class="list">
							<text>上传身份证背面</text>
							<view class="upload" @click="upIdcardBack">

								<image :src="idcard_back"></image>
								<!-- 	<text>请保证身份证完整，身份证号清晰</text> -->
							</view>
						</view>

						<view class="button" @click="formSubmit">
							<text>提交认证信息</text>
						</view>
						<view class="text">
							<text>根据监管要求身份证照片仅用于实名认证</text>
						</view>
					</view>
				</view>

				<!-- 				<view class="form">
					<view class="form-item">
						<input type="text" class="input" placeholder="请输入姓名"
							placeholder-style="color:#BBBBBB;font-size:28rpx" name="realname"
							v-model="realname"></input>
					</view>
					<view class="form-item">
						<input type="text" class="input" placeholder="请输入身份证号码"
							placeholder-style="color:#BBBBBB;font-size:28rpx" name="idcard" v-model="idcard"></input>
					</view>
				</view>
				<button class="set-btn" form-type="submit"
					:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">保
					存</button> -->
			</form>


			<view class="posterDialog" v-if="showposter">
				<view class="main">
					<view class="close" @tap="posterDialogClose">
						<image class="img" src="/static/img/close.png" />
					</view>
					<view class="content" @click="baocun">
						<canvas canvas-id="qrcode" />
					</view>
					<view class="tips">
						<text>请使用{{cert_type === 'ZHIMACREDIT' ? '支付宝' : '微信'}}扫码进行认证</text>
						<text class="sub-tips">{{cert_type === 'ZHIMACREDIT' ? '请保存二维码打开支付宝扫码认证' : '请用另一台手机微信进行人脸验证'}}</text>
						<text class="sub-tips">认证成功后打开此页面点击<text class="red">我已认证</text></text>
					</view>
					<view class="btn-s" @click="baocun">长按识别</view>
					<view class="btn-s" @click="renzheng">我已认证</view>
				</view>
			</view>

		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	import uqrcode from './uqrcode.js'
	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				realname: '',
				idcard: '',
				textset: {},
				haspwd: 0,
				idcard_front: '../../static/img/upload.png',
				idcard_back: '../../static/img/upload.png',

				showposter: false,
				orderNumber : '',
				cert_url: '',
				cert_type: ''
			};
		},

		onLoad: function(opt) {
			// this.opt = app.getopts(opt);


			this.loaded();
			
			this.getInfo();


			// const _that = this
			// app.get('ApiMy/set', {}, function(data) {

			// 	console.log(data)

			// 	const user = data.userinfo
			// 	_that.realname = user.realname
			// 	_that.idcard = user.usercard || ''

			// 	if (user.is_eid_verify) {
			// 		app.goto('/pages/my/seteid');
			// 	}

			// });
			// setTimeout(function() {
			// 	// 	app.goback(true);
			// 	app.goto('/pages/my/seteid');
			// }, 2000);
		},
		onPullDownRefresh: function() {
			//this.getdata();
		},
		methods: {

            getInfo(){
				let that = this 

				app.post('ApiFace/info', {}, function(res) {
					if(res.status == 1){
					   that.realname = res.data.name;
					   that.idcard = res.data.id_number;
					   that.idcard_front = res.data.img_front;
					   that.idcard_back = res.data.img_reverse; 
					   that.orderNumber = res.data.order_num;    
					   that.cert_type = res.data.cert_type;
					   
					   if(that.orderNumber) {
						   that.showposter = true;
						   uqrcode.make({
							   canvasId: 'qrcode',
							   componentInstance: that,
							   text: res.data.cert_url,
							   size: 250,
							   margin: 0,
							   backgroundColor: '#ffffff',
							   foregroundColor: '#000000',
							   fileType: 'jpg',
							   errorCorrectLevel: uqrcode.errorCorrectLevel.H,
						   });
					   }
					   
					   if(res.data.status == 1){
						   uni.showModal({
							   content:'您已成功认证',
							   showCancel:false,
							   success(){
								   uni.navigateBack();
							   }
						   })    
					   }
					}else{
						app.alert(res.msg);
					}
				});
			},


			baocun() {

				uni.canvasToTempFilePath({
					canvasId: 'qrcode',
					success(res) {
						console.log(res)

						uni.previewImage({
							urls: [res.tempFilePath],
							current: 0, //点击图片传过来的下标
							success: (res) => {
								console.log('预览图片成功');
								// uni.showToast({
								// 	title: '预览图片成功'
								// })
							}
						})


						// uni.saveImageToPhotosAlbum({
						// 	filePath: res.tempFilePath,
						// 	success() {
						// 		uni.showToast({
						// 			title: '图片保存成功'
						// 		});
						// 	},
						// 	fail() {
						// 		console.log('保存失败：', err);
						// 		// 处理用户拒绝权限的情况
						// 		if (err.errMsg.indexOf('authorize') !== -1) {
						// 			uni.showModal({
						// 				title: '提示',
						// 				content: '需要授权保存到相册',
						// 				success: function(modalRes) {
						// 					if (modalRes.confirm) {
						// 						uni.openSetting();
						// 					}
						// 				}
						// 			});
						// 		}
						// 	}
						// })
					},
					fail(err) {
						console.log(err)
					}
				})

			},

			renzheng() {

				if (this.orderNumber == '') {
					app.alert('请先提交认证信息');
					return;
				}
				app.post('ApiFace/query', {
					log_id: this.orderNumber,
				}, function(data) {
					
					uni.showModal({
						   content:data.msg,
						   showCancel:false,
						   success(){
							   uni.navigateBack();
						   }
					})	
					
				});

			},

			posterDialogClose() {
				this.showposter = false

			},

			upIdcardHead() {

				let that = this;
				app.chooseImage(function(data) {
					//console.log(data)
					that.idcard_front = data[0]
				})
			},

			upIdcardBack() {
				let that = this;
				app.chooseImage(function(data) {
					//console.log(data)
					that.idcard_back = data[0]
				})
			},

			formSubmit: function() {
				let that = this;
				var realname = this.realname
				var idcard = this.idcard
				var idcard_front = this.idcard_front;
				var idcard_back = this.idcard_back;
				var is_eid_verify = this.is_eid_verify;
				
				if (realname == '') {
					app.alert('请输入姓名');
					return;
				}
				if (idcard == '') {
						app.alert('请输入身份证号码');
						return;
				}
				
				if(idcard_front == '../../static/img/upload.png'){
					app.alert('请上传身份证正面');
					return;
				}
				
				if(idcard_back== '../../static/img/upload.png'){
					app.alert('请上传身份证背面');
					return;
				}
				
				app.post('ApiFace/detect', {
					name: realname,
					card_id: idcard,
					type: 'WECHAT',
					img_front: idcard_front,
					img_reverse: idcard_back
				}, function(data) {
					if (data.status == 1) {
						that.orderNumber = data.data.orderNumber;
						that.showposter = true
						uqrcode.make({
							canvasId: 'qrcode',
							componentInstance: that,
							text: data.data.originalUrl,
							size: 250,
							margin: 0,
							backgroundColor: '#ffffff',
							foregroundColor: '#000000',
							fileType: 'jpg',
							errorCorrectLevel: uqrcode.errorCorrectLevel.H,
						})
					} else {
						app.error(data.msg);
					}	
				 
				});

			}
		}
	};
</script>
<style lang="scss" scoped>
	.auth {
		height: 100%;
		background-color: #FFFFFF;

		& text {
			font-family: PingFangSC-Regular, PingFang SC;
			color: #7C7597;
		}

		.infos {
			.list {
				display: flex;
				flex-direction: column;
				padding: 60rpx 78rpx 0;

				>text {
					font-size: 28rpx;
					font-weight: 600;
					color: #160651;
					line-height: 40rpx;
					margin-bottom: 14rpx;
				}

				>input {
					height: 80rpx;
					background: #F5F5FB;
					border-radius: 10rpx;
					padding-left: 32rpx;
				}

				.upload {
					display: flex;
					flex-direction: column;
					align-items: center;
					background: #F5F5FB;
					border-radius: 10rpx;
					padding: 62rpx 54rpx 30rpx 54rpx;

					& image {
						width: 248rpx;
						height: 134rpx;
					}

					>text {
						margin-top: 38rpx;
					}
				}
			}
		}

		.button {
			display: flex;
			padding: 0 82rpx;
			margin-top: 78rpx;

			>text {
				width: 100%;
				height: 84rpx;
				background: #533CD7;
				box-shadow: 0rpx 6rpx 30rpx 0rpx rgba(83, 60, 215, 0.4600);
				border-radius: 43rpx;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 84rpx;
				text-align: center;
			}
		}

		.text {
			width: 100%;
			display: flex;
			justify-content: center;
			margin-top: 10rpx;
		}
	}

	.form {
		width: 94%;
		margin: 20rpx 3%;
		border-radius: 5px;
		padding: 20rpx 20rpx;
		padding: 0 3%;
		background: #FFF;
	}

	.form-item {
		display: flex;
		align-items: center;
		width: 100%;
		border-bottom: 1px #ededed solid;
		height: 98rpx;
		line-height: 98rpx;
	}

	.form-item:last-child {
		border: 0
	}

	.form-item .label {
		color: #000;
		width: 200rpx;
	}

	.form-item .input {
		flex: 1;
		color: #000;
	}

	.set-btn {
		width: 90%;
		margin: 60rpx 5%;
		height: 96rpx;
		line-height: 96rpx;
		border-radius: 48rpx;
		color: #FFFFFF;
		font-weight: bold;
	}

	.posterDialog {
		position: fixed;
		z-index: 9;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.8);
		top: var(--window-top);
		left: 0
	}

	.posterDialog .main {
		width: 74%;
		margin: 100px 13% 15px 13%;
		background: #fff;
		position: relative;
		border-radius: 20rpx;
		padding-bottom: 10px;
	}

	.posterDialog .close {
		position: absolute;
		padding: 20rpx;
		top: 0;
		right: 0
	}

	.posterDialog .close .img {
		width: 40rpx;
		height: 40rpx;
	}

	.posterDialog .content {
		width: 100%;
		padding: 70rpx 20rpx 10rpx 20rpx;
		color: #333;
		font-size: 30rpx;
		text-align: center
	}

	.posterDialog .content canvas {
		width: 500rpx;
		height: 500rpx;
	}

	.btn-s {
		text-align: center;
		padding: 10px;
		background: #533CD7;
		margin: 10px 20px;
		border-radius: 20px;
		color: #fff;
	}

	.posterDialog .tips {
		width: 100%;
		padding: 0 20rpx 20rpx 20rpx;
		text-align: center;
		
		text {
			font-size: 28rpx;
			color: #666;
			display: block;
		}
		
		.sub-tips {
			margin-top: 10rpx;
			color: #999;
			font-size: 24rpx;

			.red {
				color: #ff0000;
				display: inline;
			}
		}
	}
</style>