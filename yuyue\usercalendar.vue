<template>
<view class="container">
  <block v-if="isload">
    <!-- 顶部标题栏 -->
    <view class="page-header">
      <view class="title">
        <text class="title-text">服务日程</text>
      </view>
    </view>
    
    <!-- 日历标题 -->
    <view class="calendar-header">
      <view class="month-nav">
        <view class="prev-month ripple" @tap="prevMonth">
          <text class="iconfont iconleft"></text>
          <text>上个月</text>
        </view>
        <view class="current-month">
          <text class="month-text">{{year}}年{{month}}月</text>
        </view>
        <view class="next-month ripple" @tap="nextMonth">
          <text>下个月</text>
          <text class="iconfont iconright"></text>
        </view>
      </view>
      
      <!-- 日历周标题 -->
      <view class="week-header">
        <view class="week-day" v-for="(day, index) in weekDays" :key="index">
          <text :class="{'weekend': index === 0 || index === 6}">{{day}}</text>
        </view>
      </view>
    </view>
    
    <!-- 日历主体 -->
    <view class="calendar-body">
      <view 
        class="calendar-day" 
        v-for="(day, index) in calendarDays" 
        :key="index"
        :class="{
          'empty': !day.day,
          'disabled': day.disabled,
          'available': day.available && !day.disabled,
          'selected': selectedDate === day.date,
          'today': day.isToday
        }"
        @tap="selectDate(day)"
      >
        <text class="day-number">{{day.day || ''}}</text>
        <view class="day-marker" v-if="day.available">
          <text v-if="day.specialPrice" class="special-price"></text>
          <text v-if="day.available_worker_count > 0" class="has-service"></text>
        </view>
      </view>
    </view>
    
    <!-- 图例说明 -->
    <view class="calendar-legend">
      <view class="legend-item">
        <view class="legend-marker special-price-marker"></view>
        <text>特价</text>
      </view>
      <view class="legend-item">
        <view class="legend-marker has-service-marker"></view>
        <text>有服务</text>
      </view>
    </view>
    
    <!-- 未预约订单列表 -->
    <view class="order-section" v-if="orderList.length > 0">
      <view class="section-title">可预约订单</view>
      <view class="order-list">
        <view class="order-item" 
          v-for="(item, index) in orderList" 
          :key="index" 
          :class="{'selected-order': selectedOrderId === item.id}"
          @tap="selectAndShowCalendar(item)"
        >
          <view class="order-left">
            <image :src="item.propic" mode="aspectFill"></image>
            <view class="order-badge" v-if="item.status == 5 || item.status == 6">待预约</view>
          </view>
          <view class="order-center">
            <text class="order-name">{{item.proname}}</text>
            <view class="order-info">
              <text class="order-number">订单号：{{item.ordernum}}</text>
              <text class="order-status-label" :class="'status-'+item.status">{{getStatusText(item)}}</text>
            </view>
            <view class="order-bottom">
              <text class="order-price">¥{{item.totalprice}}</text>
            </view>
          </view>
          <view class="order-right">
            <view class="select-btn-wrap" :style="selectedOrderId === item.id ? 'background: #56aaff' : 'background: #f0f7ff'">
              <text class="select-btn" :style="selectedOrderId === item.id ? 'color: #fff' : 'color: #56aaff'">选择</text>
              <text class="iconfont iconright" :style="selectedOrderId === item.id ? 'color: #fff' : 'color: #56aaff'"></text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 无预约订单时显示 -->
    <view class="no-order-container" v-else>
      <view class="no-order-tip">
        <text>您暂时没有可预约的服务项目~</text>
      </view>
      <button class="purchase-btn" :style="{background:'#56aaff'}" @tap="goToPurchase">购买服务</button>
    </view>
    
    <!-- 选择订单界面 -->
    <view class="order-panel" v-if="showOrderPanel">
      <view class="order-panel-header">
        <text>选择订单进行预约</text>
        <text class="close-btn" @tap="closeOrderPanel">×</text>
      </view>
      
      <scroll-view class="order-list" scroll-y="true">
        <view class="order-item" v-for="(item, index) in orderList" :key="index" @tap="selectOrder(item)">
          <view class="order-left">
            <image :src="item.propic" mode="aspectFill"></image>
          </view>
          <view class="order-center">
            <text class="order-name">{{item.proname}}</text>
            <text class="order-number">订单号：{{item.ordernum}}</text>
            <text class="order-price">¥{{item.totalprice}}</text>
          </view>
          <view class="order-right">
            <text :class="['order-status', 'status-'+item.status]">{{getStatusText(item)}}</text>
          </view>
        </view>
      </scroll-view>
      
      <view class="no-data" v-if="orderList.length === 0">
        <text>暂无可预约订单</text>
      </view>
    </view>
    
    <!-- 日期选择面板 -->
    <view class="date-panel" v-if="showDatePanel">
      <view class="date-panel-header">
        <text>选择预约日期</text>
        <text class="close-btn" @tap="closeDatePanel">×</text>
      </view>
      
      <view class="date-content">
        <view class="selected-date-info">
          <text>已选择：{{selectedDate}} ({{selectedDayText}})</text>
          <text>可用服务人员：{{selectedDayWorkers}}人</text>
        </view>
        
        <button class="confirm-btn" :style="{background:t('color1')}" @tap="confirmDate">确认预约</button>
      </view>
    </view>
    
    <view class="mask" v-if="showOrderPanel || showDatePanel" @tap="closeAllPanels"></view>
    
    <nomore v-if="nomore"></nomore>
    <nodata v-if="nodata"></nodata>
  </block>
  <loading v-if="loading"></loading>
  <dp-tabbar :opt="opt" :active="3"></dp-tabbar>
  <popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      
      // 日历数据
      year: new Date().getFullYear(),
      month: new Date().getMonth() + 1,
      weekDays: ['日', '一', '二', '三', '四', '五', '六'],
      calendarDays: [],
      selectedDate: '',
      selectedDayText: '',
      selectedDayWorkers: 0,
      
      // 商品数据
      product: {},
      
      // 订单数据
      orderList: [],
      pagenum: 1,
      nomore: false,
      nodata: false,
      selectedOrderId: null,
      orderSelected: false,
      
      // 日历原始数据
      calendarData: [],
      
      // 面板控制
      showOrderPanel: false,
      showDatePanel: false
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    
    // 如果传入了订单ID，设置为当前选中的订单
    if (opt.id) {
      this.selectedOrderId = opt.id;
    }
    
    // 如果传入了商品ID，直接查询该商品的日历
    if (opt.proid) {
      this.getCalendarData(opt.proid);
    } else {
      this.getCalendarData();
      this.getOrderList();
    }
    
    this.isload = true;
  },
  
  onPullDownRefresh: function () {
    this.getCalendarData();
    this.getOrderList();
  },
  
  methods: {
    // 获取订单列表
    getOrderList: function (loadmore) {
      if (!loadmore) {
        this.pagenum = 1;
        this.orderList = [];
      }
      
      var that = this;
      var pagenum = that.pagenum;
      
      that.nodata = false;
      that.nomore = false;
      that.loading = true;
      
      app.post('ApiYuyue/orderlistrili', {pagenum: pagenum}, function (res) {
        that.loading = false;
        var data = res.datalist || [];
        
        console.log('获取到订单列表:', data);
        
        // 筛选可预约的订单
        var appointableOrders = data.filter(function(item) {
          return item.status == 1 || item.status == 5 || item.status == 6;
        });
        
        if (pagenum == 1) {
          that.orderList = appointableOrders;
          
          if (appointableOrders.length == 0) {
            that.nodata = true;
          }
          
          // 如果有selectedOrderId，选中对应订单
          if (that.selectedOrderId) {
            for (let i = 0; i < appointableOrders.length; i++) {
              if (appointableOrders[i].id == that.selectedOrderId) {
                that.selectOrder(appointableOrders[i]);
                break;
              }
            }
          }
        } else {
          if (appointableOrders.length == 0) {
            that.nomore = true;
          } else {
            var orderList = that.orderList;
            var newdata = orderList.concat(appointableOrders);
            that.orderList = newdata;
          }
        }
      });
    },
    
    // 获取日历数据
    getCalendarData: function (proid) {
      var that = this;
      var params = {
        year: that.year,
        month: that.month
      };
      
      // 如果传入了商品ID，优先使用
      if (proid) {
        params.proid = proid;
      } 
      // 如果选择了订单，使用订单中的商品ID
      else if (that.selectedOrderId) {
        params.id = that.selectedOrderId;
      }
      
      that.loading = true;
      app.post('ApiYuyue/userCalendar', params, function (res) {
        that.loading = false;
        
        console.log('获取到日历数据:', res);
        
        if (res.status == 1) {
          // 保存商品信息
          that.product = res.product || {};
          
          // 保存日历数据
          that.calendarData = res.calendar || [];
          
          // 为每个日期添加随机特价标记（仅用于演示）
          that.calendarData.forEach(function(day) {
            if (day.available) {
              day.specialPrice = Math.random() > 0.7;
            }
          });
          
          // 生成日历视图
          that.generateCalendar();
        } else {
          app.error(res.msg || '获取日历数据失败');
        }
      });
    },
    
    // 生成日历视图
    generateCalendar: function () {
      const firstDay = new Date(this.year, this.month - 1, 1).getDay(); // 当月第一天是星期几
      const totalDays = new Date(this.year, this.month, 0).getDate(); // 当月总天数
      
      let calendarDays = [];
      
      // 添加上个月末尾的空白天数
      for (let i = 0; i < firstDay; i++) {
        calendarDays.push({
          day: 0,
          disabled: true
        });
      }
      
      // 获取今天的日期字符串用于比较
      const today = new Date();
      const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      
      // 添加当月的天数
      for (let i = 1; i <= totalDays; i++) {
        const dateStr = `${this.year}-${String(this.month).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
        
        // 查找当天在日历数据中的数据
        const dayData = this.calendarData.find(item => item.date === dateStr) || {};
        
        const isToday = dateStr === todayStr;
        
        // 如果日期已过，或者没有服务人员可用，则禁用该日期
        const isPastDate = new Date(dateStr) < new Date(todayStr);
        const isDisabled = isPastDate || dayData.disabled === true || !dayData.available || !dayData.available_worker_count;
        
        calendarDays.push({
          day: i,
          date: dateStr,
          weekday: new Date(dateStr).getDay(),
          weekdayText: this.weekDays[new Date(dateStr).getDay()],
          available: !isDisabled && dayData.available,
          disabled: isDisabled,
          isToday: isToday,
          orders: dayData.orders || 0,
          available_worker_count: dayData.available_worker_count || 0,
          specialPrice: dayData.specialPrice || false
        });
      }
      
      // 添加足够的行，使每行都有7天
      const remaining = 7 - (calendarDays.length % 7);
      if (remaining < 7) {
        for (let i = 0; i < remaining; i++) {
          calendarDays.push({
            day: 0,
            disabled: true
          });
        }
      }
      
      this.calendarDays = calendarDays;
    },
    
    // 选择日期
    selectDate: function (day) {
      if (!day.day || day.disabled) {
        return;
      }
      
      // 只有可预约的日期才能点击
      if (!day.available) {
        app.error('该日期不可预约');
        return;
      }
      
      this.selectedDate = day.date;
      this.selectedDayText = this.weekDays[day.weekday] + '（' + this.month + '月' + day.day + '日）';
      this.selectedDayWorkers = day.available_worker_count;
      
      console.log('选择的日期:', this.selectedDate);
      
      // 如果已选择订单，则显示日期确认面板
      if (this.selectedOrderId) {
        this.showDatePanel = true;
      } else {
        app.alert('请先选择一个订单');
        // 滚动到订单列表位置
        uni.pageScrollTo({
          scrollTop: 500,
          duration: 300
        });
      }
    },
    
    // 确认预约选中的日期
    confirmDate: function () {
      if (!this.selectedDate) {
        app.alert('请选择预约日期');
        return;
      }
      
      if (!this.selectedOrderId) {
        app.alert('请先选择一个订单');
        return;
      }
      
      console.log('准备跳转到预约页面，订单ID:', this.selectedOrderId, '选中日期:', this.selectedDate);
      
      // 确保日期格式正确 (YYYY-MM-DD)
      let dateParam = this.selectedDate;
      
      // 跳转到预约页面，传递订单ID和选择的日期
      const url = `/yuyue/appoint?id=${this.selectedOrderId}&date=${dateParam}`;
      console.log('跳转URL:', url);
      
      try {
        app.goto(url);
      } catch (error) {
        console.error('跳转失败:', error);
        // 尝试备用方式跳转
        uni.navigateTo({
          url: url,
          fail: function(err) {
            console.error('备用跳转也失败:', err);
            app.error('页面跳转失败，请稍后重试');
          }
        });
      }
    },
    
    // 选择订单
    selectOrder: function (order) {
      console.log('选择订单:', order);
      
      this.selectedOrderId = order.id;
      this.showOrderPanel = false;
      
      // 如果已选择日期，则显示日期确认面板
      if (this.selectedDate) {
        this.showDatePanel = true;
      } else {
        // 重新获取日历数据
        this.getCalendarData();
      }
    },
    
    // 上个月
    prevMonth: function () {
      if (this.month === 1) {
        this.year--;
        this.month = 12;
      } else {
        this.month--;
      }
      
      this.selectedDate = '';
      this.selectedDayText = '';
      this.getCalendarData();
    },
    
    // 下个月
    nextMonth: function () {
      if (this.month === 12) {
        this.year++;
        this.month = 1;
      } else {
        this.month++;
      }
      
      this.selectedDate = '';
      this.selectedDayText = '';
      this.getCalendarData();
    },
    
    // 获取订单状态文本
    getStatusText: function (item) {
      if (item.status == 0) return '待付款';
      if (item.status == 1) {
        if (item.refund_status == 1) return '退款审核中';
        if (item.appointment_status === 0 || !item.appointment_status) return '待预约';
        if (item.appointment_status === 1 && (item.worker_assign_status === 0 || !item.worker_id)) return '待选择服务人员';
        return '派单中';
      }
      if (item.status == 2) return '服务中';
      if (item.status == 3) return '已完成';
      if (item.status == 4) return '已取消';
      if (item.status == 5) return '待选择时间';
      if (item.status == 6) return '待分配服务';
      
      return '未知状态';
    },
    
    // 前往购买服务页面
    goToPurchase: function() {
      app.goto('/pages/index/index');
    },
    
    // 关闭订单选择面板
    closeOrderPanel: function() {
      this.showOrderPanel = false;
    },
    
    // 关闭日期选择面板
    closeDatePanel: function() {
      this.showDatePanel = false;
    },
    
    // 关闭所有面板
    closeAllPanels: function() {
      this.showOrderPanel = false;
      this.showDatePanel = false;
    },
    
    // 选择订单并显示日历
    selectAndShowCalendar: function (order) {
      console.log('选择订单并展示日历:', order);
      
      this.selectedOrderId = order.id;
      
      // 如果已选择日期，则显示日期确认面板
      if (this.selectedDate) {
        console.log('已选中日期:', this.selectedDate);
        this.showDatePanel = true;
      } else {
        // 重新获取日历数据
        this.getCalendarData();
        
        // 滚动到日历位置
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });
      }
    }
  }
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* 顶部标题 */
.page-header {
  padding: 30rpx 0;
  background-color: #fff;
  text-align: center;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

/* 日历标题 */
.calendar-header {
  background-color: #fff;
  padding: 0 0 10rpx 0;
  margin: 0 20rpx;
}

.month-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.current-month {
  display: flex;
  align-items: center;
  justify-content: center;
}

.month-text {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.prev-month, .next-month {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  padding: 10rpx;
}

.iconleft {
  margin-right: 4rpx;
}

.iconright {
  margin-left: 4rpx;
}

/* 周标题 */
.week-header {
  display: flex;
  padding: 16rpx 0 8rpx;
}

.week-day {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.week-day text {
  font-size: 28rpx;
  color: #666;
}

.weekend {
  color: #999;
}

/* 日历主体 */
.calendar-body {
  display: flex;
  flex-wrap: wrap;
  background-color: #fff;
  padding: 10rpx 0 20rpx 0;
  margin: 0 20rpx 20rpx;
}

.calendar-day {
  width: calc(100% / 7);
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  padding: 8rpx 0;
}

.day-number {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 4rpx;
  z-index: 2;
}

.day-marker {
  display: flex;
  justify-content: center;
  margin-top: 2rpx;
  z-index: 2;
}

.special-price, .has-service {
  width: 6rpx;
  height: 6rpx;
  border-radius: 3rpx;
  margin: 0 2rpx;
}

.special-price {
  background-color: #FF9800;
}

.has-service {
  background-color: #4CAF50;
}

.calendar-day.empty {
  background-color: #fff;
}

.calendar-day.disabled .day-number {
  color: #ccc;
}

.calendar-day.available {
  cursor: pointer;
}

.calendar-day.today .day-number {
  color: #007AFF;
}

/* 保留图片中的选中日期样式 */
.calendar-day.selected {
  position: relative;
}

.calendar-day.selected::after {
  content: "";
  display: block;
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background-color: #7E7DFA;
  position: absolute;
  z-index: 1;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.calendar-day.selected .day-number {
  color: #fff;
  font-weight: bold;
  z-index: 3;
}

.calendar-day.selected::before {
  content: '';
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background-color: #fff;
  border-radius: 50%;
  bottom: 22rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

/* 图例说明 */
.calendar-legend {
  display: flex;
  padding: 16rpx 20rpx;
  background-color: #fff;
  margin: 20rpx 20rpx 0;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.legend-marker {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
}

.special-price-marker {
  background-color: #FF9800;
}

.has-service-marker {
  background-color: #4CAF50;
}

.legend-item text {
  font-size: 24rpx;
  color: #666;
}

/* 订单部分 */
.order-section {
  margin: 20rpx 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: #fff;
}

.order-list {
  display: flex;
  flex-direction: column;
}

.order-item {
  display: flex;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #fff;
  border-left: 4rpx solid transparent;
}

.order-item:last-child {
  margin-bottom: 0;
}

.order-item.selected-order {
  border-left: 4rpx solid #7E7DFA;
  background-color: #f9f9ff;
}

.order-left {
  width: 120rpx;
  height: 120rpx;
  margin-right: 16rpx;
  border-radius: 6rpx;
  overflow: hidden;
  position: relative;
}

.order-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 73, 73, 0.9);
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 0 0 6rpx 0;
}

.order-left image {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}

.order-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4rpx 0;
  min-width: 0;
}

.order-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.order-number {
  font-size: 24rpx;
  color: #999;
  max-width: 65%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-status-label {
  font-size: 22rpx;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  color: #fff;
  background: #bbb;
  text-align: center;
}

.status-0 {
  background: #ff9500;
}

.status-1, .status-5, .status-6 {
  background: #ff3b30;
}

.status-2 {
  background: #5856d6;
}

.status-3 {
  background: #34c759;
}

.status-4 {
  background: #8e8e93;
}

.order-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-price {
  font-size: 30rpx;
  color: #ff3b30;
  font-weight: bold;
}

.order-right {
  display: flex;
  align-items: center;
  padding-left: 16rpx;
}

.select-btn-wrap {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f0f7ff;
}

.selected-order .select-btn-wrap {
  background: #7E7DFA;
}

.select-btn {
  font-size: 24rpx;
  color: #7E7DFA;
}

.selected-order .select-btn {
  color: #fff;
}

.iconright {
  font-size: 22rpx;
  color: #7E7DFA;
}

.selected-order .iconright {
  color: #fff;
}

/* 无订单时的容器 */
.no-order-container {
  margin: 20rpx;
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-order-tip {
  margin: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.purchase-btn {
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  background: #7E7DFA !important;
}

/* 日期选择面板 */
.date-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  z-index: 1001;
  padding-bottom: calc(30rpx + 100rpx); /* 增加底部padding，确保内容不被底部导航栏遮挡 */
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.date-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.date-panel-header text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 36rpx;
  color: #999;
}

.date-content {
  padding: 20rpx;
}

.selected-date-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.selected-date-info text {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  background: #7E7DFA !important;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 999;
}
</style> 