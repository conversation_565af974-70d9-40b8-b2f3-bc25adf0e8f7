<template>
	<view class="tongue-result-container">
		<!-- 时间戳-级别-[文件名]-[函数名_数字序号] 2025-01-03 22:55:53,565-INF0-[result][init_001] 舌诊结果页面初始化 -->
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<view class="nav-title">健康报告</view>
			<view class="nav-right">
				<text class="menu-icon">•••</text>
				<text class="record-icon">●</text>
			</view>
		</view>
		
		<!-- 医院标识 -->
		<view class="hospital-info">
			<text>广东省中医院出品 | 国家重点实验室项目 | 国家重大专项</text>
		</view>
		
		<!-- 报告类型选择 -->
		<view class="report-tabs">
			<view class="tab-item active">
				<text class="tab-text">舌象报告</text>
				<view class="tab-line"></view>
			</view>
			<view class="tab-item">
				<text class="tab-text">足象报告</text>
			</view>
		</view>
		
		<!-- 健康得分卡片 -->
		<view class="health-card">
			<view class="health-content">
				<view class="health-left">
					<image class="health-avatar" src="/static/images/moisture-avatar.png"></image>
					<view class="health-symptoms">
						<view class="main-symptom">
							<text class="symptom-label">主体征：</text>
							<text class="symptom-value">水湿</text>
						</view>
						<view class="sub-symptom">
							<text class="symptom-label">副体征：</text>
							<text class="symptom-value">脾胃虚</text>
						</view>
					</view>
				</view>
				<view class="health-right">
					<view class="score-circle">
						<view class="score-inner">
							<text class="score-number">89</text>
							<text class="score-text">健康得分</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 拍摄提示 -->
		<view class="photo-tip">
			<text class="tip-icon">💡</text>
			<text class="tip-text">拍摄时，角度、光线与距离会影响诊断结果。</text>
		</view>
		
		<!-- 智能问诊 -->
		<view class="ai-diagnosis">
			<view class="ai-left">
				<view class="ai-title">智能问诊</view>
				<text class="ai-subtitle">您好,请点击登录!</text>
				<text class="ai-description">补充信息，为您提供详尽的舌诊报告。</text>
			</view>
			<view class="ai-right">
				<view class="login-btn">去填写</view>
			</view>
		</view>
		
		<!-- 可能体征 -->
		<view class="symptoms-section">
			<text class="section-title">您可能有以下体征</text>
			<view class="symptoms-grid">
				<view class="symptom-tag">胸闷</view>
				<view class="symptom-tag">关节疼痛</view>
				<view class="symptom-tag">肥胖</view>
				<view class="symptom-tag">脸油头油多</view>
				<view class="symptom-tag">大便黏腻</view>
				<view class="symptom-tag">腹胀</view>
			</view>
		</view>
		
		<!-- 舌象分析 -->
		<view class="tongue-analysis">
			<text class="section-title">舌象分析</text>
			<view class="tongue-diagram">
				<image class="tongue-image" src="/static/images/tongue-diagram.png"></image>
				<!-- 舌象标注点 -->
				<view class="tongue-markers">
					<view class="marker good">
						<text class="marker-icon">✓</text>
						<text class="marker-text">舌色淡红</text>
					</view>
					<view class="marker bad top-right">
						<text class="marker-icon">!</text>
						<text class="marker-text">舌苔白色</text>
					</view>
					<view class="marker bad left">
						<text class="marker-icon">!</text>
						<text class="marker-text">有裂纹</text>
					</view>
					<view class="marker bad right">
						<text class="marker-icon">!</text>
						<text class="marker-text">有齿痕</text>
					</view>
					<view class="marker bad bottom">
						<text class="marker-icon">!</text>
						<text class="marker-text">有黏腻感</text>
					</view>
				</view>
				<view class="my-photo-btn">我的照片</view>
			</view>
		</view>
		
		<!-- 体征异常详情 -->
		<view class="abnormal-section">
			<view class="abnormal-title">
				<text class="abnormal-count">您有3项表征异常</text>
			</view>
			
			<view class="abnormal-list">
				<!-- 裂纹 -->
				<view class="abnormal-item">
					<view class="abnormal-header">
						<text class="abnormal-icon">!</text>
						<text class="abnormal-name">裂纹：</text>
					</view>
					<text class="abnormal-desc">极少数人天生出现裂纹属于正常情况，而其他人出现裂纹说明体内热盛伤阴，或血虚不润，或脾虚湿侵。</text>
				</view>
				
				<!-- 齿痕 -->
				<view class="abnormal-item">
					<view class="abnormal-header">
						<text class="abnormal-icon">!</text>
						<text class="abnormal-name">齿痕：</text>
					</view>
					<view class="abnormal-desc-container">
						<text class="abnormal-desc">正常是没有齿痕的，出现齿痕脾虚不能运化水湿，舌体胖大而受齿缘齿痕。</text>
						<image class="doctor-avatar" src="/static/images/doctor-avatar.png"></image>
					</view>
				</view>
				
				<!-- 厚苔 -->
				<view class="abnormal-item">
					<view class="abnormal-header">
						<text class="abnormal-icon">!</text>
						<text class="abnormal-name">厚苔：</text>
					</view>
					<text class="abnormal-desc">正常苔质是薄苔，出现厚苔说明胃气夹湿浊，疹浊，食积等邪气重甚苔厚</text>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="btn-secondary">返回</view>
			<view class="btn-secondary">重新拍照</view>
			<view class="btn-primary">分享</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 2025-01-03 22:55:53,565-INF0-[result][data_001] 初始化页面数据
				healthScore: 89,
				mainSymptom: '水湿',
				subSymptom: '脾胃虚',
				symptoms: ['胸闷', '关节疼痛', '肥胖', '脸油头油多', '大便黏腻', '腹胀'],
				abnormalItems: [
					{
						name: '裂纹',
						description: '极少数人天生出现裂纹属于正常情况，而其他人出现裂纹说明体内热盛伤阴，或血虚不润，或脾虚湿侵。'
					},
					{
						name: '齿痕',
						description: '正常是没有齿痕的，出现齿痕脾虚不能运化水湿，舌体胖大而受齿缘齿痕。'
					},
					{
						name: '厚苔',
						description: '正常苔质是薄苔，出现厚苔说明胃气夹湿浊，疹浊，食积等邪气重甚苔厚'
					}
				]
			}
		},
		methods: {
			// 2025-01-03 22:55:53,565-INF0-[result][goBack_001] 返回上一页功能
			goBack() {
				console.log('2025-01-03 22:55:53,565-INF0-[result][goBack_001] 用户点击返回');
				uni.navigateBack();
			},
			
			// 2025-01-03 22:55:53,565-INF0-[result][share_001] 分享功能
			share() {
				console.log('2025-01-03 22:55:53,565-INF0-[result][share_001] 用户点击分享');
				// 分享逻辑
			},
			
			// 2025-01-03 22:55:53,565-INF0-[result][retake_001] 重新拍照功能
			retakePhoto() {
				console.log('2025-01-03 22:55:53,565-INF0-[result][retake_001] 用户点击重新拍照');
				// 重新拍照逻辑
			}
		},
		
		onLoad() {
			console.log('2025-01-03 22:55:53,565-INF0-[result][onLoad_001] 舌诊结果页面加载完成');
		}
	}
</script>

<style scoped>
/* 2025-01-03 22:55:53,565-INF0-[result][style_001] 舌诊结果页面样式定义 */

.tongue-result-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 顶部导航样式 */
.top-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: white;
	position: relative;
}

.nav-left {
	display: flex;
	align-items: center;
}

.back-icon {
	font-size: 40rpx;
	color: #333;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.nav-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.menu-icon {
	font-size: 30rpx;
	color: #666;
}

.record-icon {
	font-size: 30rpx;
	color: #333;
}

/* 医院信息 */
.hospital-info {
	background-color: #e8f4ff;
	padding: 20rpx;
	text-align: center;
	font-size: 24rpx;
	color: #666;
}

/* 报告标签页 */
.report-tabs {
	display: flex;
	background-color: white;
	padding: 0 30rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 0;
	position: relative;
}

.tab-item.active .tab-text {
	color: #1890ff;
	font-weight: 500;
}

.tab-line {
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 6rpx;
	background-color: #1890ff;
	border-radius: 3rpx;
}

.tab-text {
	font-size: 32rpx;
	color: #333;
}

/* 健康得分卡片 */
.health-card {
	margin: 30rpx;
	background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
	border-radius: 20rpx;
	padding: 40rpx;
	color: white;
}

.health-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.health-left {
	display: flex;
	align-items: center;
	gap: 30rpx;
}

.health-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
}

.health-symptoms {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.main-symptom, .sub-symptom {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.symptom-label {
	font-size: 28rpx;
	opacity: 0.9;
}

.symptom-value {
	font-size: 32rpx;
	font-weight: 500;
}

.score-circle {
	width: 160rpx;
	height: 160rpx;
	border: 6rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.score-inner {
	text-align: center;
}

.score-number {
	font-size: 48rpx;
	font-weight: bold;
	display: block;
}

.score-text {
	font-size: 24rpx;
	opacity: 0.9;
}

/* 拍摄提示 */
.photo-tip {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin: 30rpx;
	padding: 20rpx;
	background-color: white;
	border-radius: 15rpx;
}

.tip-icon {
	font-size: 32rpx;
}

.tip-text {
	font-size: 28rpx;
	color: #666;
}

/* 智能问诊 */
.ai-diagnosis {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 30rpx;
	padding: 30rpx;
	background-color: white;
	border-radius: 15rpx;
}

.ai-left {
	flex: 1;
}

.ai-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.ai-subtitle {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 5rpx;
}

.ai-description {
	font-size: 24rpx;
	color: #999;
}

.login-btn {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
}

/* 可能体征 */
.symptoms-section {
	margin: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 30rpx;
}

.symptoms-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.symptom-tag {
	background-color: #f0f0f0;
	padding: 15rpx 30rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #666;
}

/* 舌象分析 */
.tongue-analysis {
	margin: 30rpx;
}

.tongue-diagram {
	background-color: white;
	border-radius: 15rpx;
	padding: 40rpx;
	position: relative;
	text-align: center;
}

.tongue-image {
	width: 400rpx;
	height: 300rpx;
	border-radius: 15rpx;
}

.tongue-markers {
	position: absolute;
	top: 40rpx;
	left: 40rpx;
	right: 40rpx;
	bottom: 40rpx;
}

.marker {
	position: absolute;
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 24rpx;
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	background-color: white;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.marker.good {
	color: #52c41a;
	top: 10rpx;
	left: 10rpx;
}

.marker.bad {
	color: #ff4d4f;
}

.marker.top-right {
	top: 10rpx;
	right: 10rpx;
}

.marker.left {
	left: -20rpx;
	top: 50%;
	transform: translateY(-50%);
}

.marker.right {
	right: -20rpx;
	top: 50%;
	transform: translateY(-50%);
}

.marker.bottom {
	bottom: 10rpx;
	left: 50%;
	transform: translateX(-50%);
}

.marker-icon {
	font-size: 20rpx;
}

.my-photo-btn {
	position: absolute;
	bottom: 80rpx;
	right: 80rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: white;
	padding: 15rpx 25rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

/* 体征异常 */
.abnormal-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.abnormal-title {
	margin-bottom: 30rpx;
}

.abnormal-count {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.abnormal-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.abnormal-item {
	border-bottom: 1rpx solid #f0f0f0;
	padding-bottom: 30rpx;
}

.abnormal-item:last-child {
	border-bottom: none;
	padding-bottom: 0;
}

.abnormal-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.abnormal-icon {
	width: 32rpx;
	height: 32rpx;
	background-color: #ff4d4f;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
}

.abnormal-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #ff4d4f;
}

.abnormal-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.abnormal-desc-container {
	display: flex;
	align-items: flex-end;
	gap: 20rpx;
}

.doctor-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	flex-shrink: 0;
}

/* 底部按钮 */
.bottom-buttons {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	background-color: white;
	margin-top: 30rpx;
}

.btn-secondary {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	border: 2rpx solid #ddd;
	border-radius: 25rpx;
	font-size: 28rpx;
	color: #666;
}

.btn-primary {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	background-color: #1890ff;
	color: white;
	border-radius: 25rpx;
	font-size: 28rpx;
}
</style> 