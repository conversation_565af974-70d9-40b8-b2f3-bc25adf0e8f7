<template>
	<view class="container">
		<!-- 头部背景 -->
		<view style="background: linear-gradient(45deg, rgb(253, 74, 70) 0%, rgba(253, 74, 70, 0.8) 100%);">
			<view class="dp-userinfo2">
				<view class="flex" style="margin-top: 80px;">
					<view class="flex">
						<view
							style="width: 60px; height: 60px; background: #FFF8AF; border-radius: 5px; position: relative;">
							<image
								style="width: 30px; height: 30px; position: absolute; left: 0; right: 0; top: 0; bottom: 0; margin: auto;"
								:src="pre_url+'/static/icon/photo.png'"></image>
						</view>
						<view style="font-size: 14px; margin-left: 10px; color: #fff;">快团用户</view>
					</view>
					<view class="btn-b">已开启智能背景</view>
				</view>
			</view>
		</view>

		<!-- 返回按钮 -->
		<view style="position: fixed; z-index: 1000; top: 10px; left: 10px;">
			<image style="width: 30px; height: 30px;" :src="pre_url+'/static/img/goback.png'" @click="goBack"></image>
		</view>

		<!-- 顶部导航标签 -->
		<view style="height: 100px; position: fixed; top: 0; width: 100%; background: #fff; z-index: 100;"
			class="load-list" v-if="showTab">
			<view style="display: flex; justify-content: space-between; padding: 70px 50px 0 50px;">
				<view :class="toView === 'tgjs' ? 'on_tab' : ''" @click="scrollToElement('tgjs')">介绍</view>
				<view :class="toView === 'tgsp' ? 'on_tab' : ''" @click="scrollToElement('tgsp')">商品</view>
				<view :class="toView === 'tgsz' ? 'on_tab' : ''" @click="scrollToElement('tgsz')">设置</view>
			</view>
		</view>

		<!-- 跑马灯信息 -->
		<view style="background: #FFF9E3; border-radius: 8px; margin: -15px 11px 11px; padding: 8px; overflow: hidden;"
			class="flex2">
			<MarqueeComponent :duration="10">
				<p style="width: 300px;">
					{{ notice }}
				</p>
			</MarqueeComponent>
			<img style="width: 15px; height: 15px;" :src="pre_url+'/static/icon/close.png'"></img>
		</view>


		<!-- 团购介绍 -->
		<view id="tgjs" class="dp-userinfo-order" style="margin: 0px 11px 11px; padding: 10px 0;">
			<view
				style="display: flex; justify-content: space-between; padding-bottom: 15px; align-items: center; border-bottom: 1px solid #eee; padding:10px;">
				<view class="tit-m">团购介绍</view>
				<view class="btn-m" style="">复制已有团购</view>
			</view>

			<view style="padding: 0 10px;">
				<view style="padding: 12px 0; border-bottom: 1px solid #eee;">
					<input style="font-size: 14px;" placeholder="请输入团购活动标题" v-model="title" />
				</view>

				<view style="padding: 12px 0;" v-if="showTxt">
					<input style="font-size: 14px;" placeholder="请输入团购活动内容" v-model="content" @blur="editTxt" />
				</view>
			</view>

			<view style="padding: 0 10px;" v-if="!showTxt">
				<view style="padding-top: 10px; padding-bottom: 40px;" v-for="(setData, index) in items" :key="index">
					<view class="flex">
						<view>
							<text v-if="setData.temp=='text'">文字</text>
							<text v-if="setData.temp=='pictures'">小图</text>
							<text v-if="setData.temp=='picture'">大图</text>
							<text v-if="setData.temp=='video'">视频</text>
						</view>


						<view class="flex">
							<view class="mz" :style="index == 0 ? 'color:#ccc;' : ''" @click="toTop(index)">上移</view>
							<view class="mz" :style="items.length == (index + 1) ? 'color:#ccc;' : ''"
								@click="toBottom(index)">下移</view>
							<view class="mz" :style="index == 0 ? 'color:#ccc;' : ''" @click="toOne(index)">置顶</view>
							<view class="mz" @click="toAdd(index)">添加</view>
							<view class="mz" @click="delItem(index)">删除</view>
						</view>
					</view>

					<view style="padding-top: 10px;">

						<block v-if="setData.temp=='notice'">
							<dp-notice :params="setData.params" :data="setData.data"></dp-notice>
						</block>
						<block v-if="setData.temp=='banner'">
							<dp-banner :params="setData.params" :data="setData.data"></dp-banner>
						</block>
						<block v-if="setData.temp=='search'">
							<dp-search :params="setData.params" :data="setData.data"></dp-search>
						</block>
						<block v-if="setData.temp=='text'">
							<!-- 			<dp-text :params="setData.params" :data="setData.data"></dp-text>
							 -->
							<input style="font-size: 14px;" placeholder="请输入内容" :value="setData.params.content"
								@input="iptItem($event, index)">
						</block>
						<block v-if="setData.temp=='title'">
							<dp-title :params="setData.params" :data="setData.data"></dp-title>
						</block>
						<block v-if="setData.temp=='dhlist'">
							<dp-dhlist :params="setData.params" :data="setData.data"></dp-dhlist>
						</block>
						<block v-if="setData.temp=='line'">
							<dp-line :params="setData.params" :data="setData.data"></dp-line>
						</block>
						<block v-if="setData.temp=='blank'">
							<dp-blank :params="setData.params" :data="setData.data"></dp-blank>
						</block>
						<block v-if="setData.temp=='menu'">
							<dp-menu :params="setData.params" :data="setData.data"></dp-menu>
						</block>
						<block v-if="setData.temp=='map'">
							<dp-map :params="setData.params" :data="setData.data"></dp-map>
						</block>
						<block v-if="setData.temp=='cube'">
							<dp-cube :params="setData.params" :data="setData.data"></dp-cube>
						</block>
						<block v-if="setData.temp=='picture'">

							<dp-picture :params="setData.params" :data="setData.data"></dp-picture>
						</block>
						<block v-if="setData.temp=='pictures'">

							<view style="display: flex; flex-wrap: wrap; padding-top:20rpx;">
								<view v-for="(it, idx) in setData.data" :key="idx" class="layui-imgbox">
									<view class="layui-imgbox-close" @tap="removeimg(idx, index)">
										<image :src="pre_url+'/static/img/ico-del.png'"></image>
									</view>
									<view class="layui-imgbox-img">
										<image :src="it.imgurl" @tap="previewImage(it.imgurl)" :data-url="it.imgurl"
											mode="widthFix"></image>
									</view>
								</view>
								<view class="uploadbtn"
									:style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'"
									@tap="uploadSmallimg(index)" v-if="setData.data.length < 9">
								</view>
							</view>

							<!-- <dp-pictures :params="setData.params" :data="setData.data"></dp-pictures> -->
						</block>
						<block v-if="setData.temp=='video'">
							<dp-video :params="setData.params" :data="setData.data"></dp-video>
						</block>
						<block v-if="setData.temp=='shop'">
							<dp-shop :params="setData.params" :data="setData.data"
								:shopinfo="setData.shopinfo"></dp-shop>
						</block>
						<block v-if="setData.temp=='product'">
							<dp-product :params="setData.params" :data="setData.data"
								:menuindex="menuindex"></dp-product>
						</block>
						<block v-if="setData.temp=='collage'">
							<dp-collage :params="setData.params" :data="setData.data"></dp-collage>
						</block>
						<block v-if="setData.temp=='kanjia'">
							<dp-kanjia :params="setData.params" :data="setData.data"></dp-kanjia>
						</block>
						<block v-if="setData.temp=='seckill'">
							<dp-seckill :params="setData.params" :data="setData.data"></dp-seckill>
						</block>
						<block v-if="setData.temp=='scoreshop'">
							<dp-scoreshop :params="setData.params" :data="setData.data"></dp-scoreshop>
						</block>
						<block v-if="setData.temp=='coupon'">
							<dp-coupon :params="setData.params" :data="setData.data"></dp-coupon>
						</block>
						<block v-if="setData.temp=='article'">
							<dp-article :params="setData.params" :data="setData.data"></dp-article>
						</block>
						<block v-if="setData.temp=='business'">
							<dp-business :params="setData.params" :data="setData.data"></dp-business>
						</block>
						<block v-if="setData.temp=='liveroom'">
							<dp-liveroom :params="setData.params" :data="setData.data"></dp-liveroom>
						</block>
						<block v-if="setData.temp=='button'">
							<dp-button :params="setData.params" :data="setData.data"></dp-button>
						</block>
						<block v-if="setData.temp=='hotspot'">
							<dp-hotspot :params="setData.params" :data="setData.data"></dp-hotspot>
						</block>
						<block v-if="setData.temp=='cover'">
							<dp-cover :params="setData.params" :data="setData.data"></dp-cover>
						</block>
						<block v-if="setData.temp=='richtext'">
							<dp-richtext :params="setData.params" :data="setData.data"
								:content="setData.content"></dp-richtext>
						</block>
						<block v-if="setData.temp=='form'">
							<dp-form :params="setData.params" :data="setData.data" :content="setData.content"></dp-form>
						</block>
						<block v-if="setData.temp=='userinfo'">
							<dp-userinfo :params="setData.params" :data="setData.data"
								:content="setData.content"></dp-userinfo>
						</block>

						<!-- 						<input v-if="item.temp == 'text'" style="font-size: 14px;" placeholder="请输入团购活动内容"
							:value="item.params.content" @input="iptItem($event, index)" />
						<image v-if="item.temp == 'picture'" style="width: 100%;" mode="widthFix" :src="item.data">
						</image>
						<view v-if="item.temp == 'pictures'">
							<view style="display: flex; flex-wrap: wrap; padding-top:20rpx;">
								<view v-for="(it, idx) in item.data" :key="idx" class="layui-imgbox">
									<view class="layui-imgbox-close" @tap="removeimg(idx, index)">
										<image src="/static/img/ico-del.png"></image>
									</view>
									<view class="layui-imgbox-img">
										<image :src="it" @tap="previewImage(it)" :data-url="it" mode="widthFix"></image>
									</view>
								</view>
								<view class="uploadbtn"
									:style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'"
									@tap="uploadSmallimg(index)" v-if="item.data.length < 9">
								</view>
							</view>
						</view>
						<view v-if="item.temp == 'video'">
							<view class="flex-y-center" style="width:100%; padding:20rpx 0; margin-top:20rpx;">
								<image :src="pre_url+'/static/img/uploadvideo.png'"
									style="width:200rpx; height:200rpx; background:#eee;" @tap="uploadVideo(index)">
								</image>
							</view>
							<input type="text" hidden="true" name="video" :value="item.data" maxlength="-1"></input>
						</view> -->
					</view>
				</view>
			</view>

			<view style="display: flex; margin-top: 40px; flex-wrap: wrap;">
				<view style="text-align: center; width: 20%; margin-top: 10px;" v-for="(item, index) in tabs"
					:key="index" @click="goCom(item.temp)">
					<image style="width: 20px;" :src="item.img" mode="widthFix"></image>
					<view>{{item.txt}}</view>
				</view>
			</view>
		</view>

		<!-- 团购商品 -->
		<view id="tgsp" class="dp-userinfo-order" style="margin: 0px 11px 11px;">
			<view style="display: flex; justify-content: space-between; margin-bottom: 15px; align-items: center;">
				<view class="tit-m">团购商品</view>
				<view class="btn-m" @click="navigateToSelectGoods">从商品库导入</view>
			</view>

			<!-- 已选择的商品显示部分 -->
			<view v-if="selectedGoods.length === 0">
				<view style="text-align: center; padding: 20px;">尚未选择商品</view>
			</view>
			<view v-else>
				<view v-for="(item, index) in selectedGoods" :key="item.id"
					style="padding: 10px; background: #F6F6F6; margin-top: 10px; border-radius: 10rpx; position: relative;">
					<view class="ltn">{{ index + 1 }}</view>
					<view class="flex">
						<view></view>
						<view class="flex">
							<view class="mz" :style="index == 0 ? 'color:#ccc;' : ''" @click="goodTop(index)">上移</view>
							<view class="mz" :style="selectedGoods.length == (index + 1) ? 'color:#ccc;' : ''"
								@click="goodBottom(index)">下移</view>
							<view class="mz" :style="index == 0 ? 'color:#ccc;' : ''" @click="goodOne(index)">置顶</view>
							<view class="mz" @click="removeSelectedGood(index)">删除</view>
						</view>
					</view>
					<view class="ml">
						<view style="font-size: 12px; width: 60px;">名称</view>
						<input style="font-size: 12px;" placeholder="商品名称" v-model="item.name" />
					</view>
					<view class="ml">
						<view style="font-size: 12px; width: 60px;">描述</view>
						<input style="font-size: 12px;" placeholder="商品描述" v-model="item.sellpoint" />
					</view>
					<!-- <view class="ml">
                      <view style="font-size: 12px; width: 60px;">规格</view>
                      <input style="font-size: 12px;" placeholder="规格信息" v-model="item.specifications" />
                  </view> -->
					<view class="ml">
						<view style="font-size: 12px; width: 60px;">价格(￥)</view>
						<input style="font-size: 12px;" type="number" placeholder="价格" v-model="item.sell_price" />
					</view>
					<view class="ml">
						<view style="font-size: 12px; width: 60px;">库存</view>
						<input style="font-size: 12px;" type="number" placeholder="库存" v-model="item.stock" />
					</view>
				</view>
			</view>

			<!-- 添加商品按钮 -->
			<view class="abtn" @click="navigateToSelectGoods">
				+ 添加商品
			</view>
		</view>


		<!-- 团购设置 -->
		<view id="tgsz" class="dp-userinfo-order" style="margin: 0px 11px 11px; padding: 10px 0;">
			<view
				style="display: flex; justify-content: space-between; padding-bottom: 15px; align-items: center; border-bottom: 1px solid #eee; padding:10px;">
				<view class="tit-m">团购设置</view>
			</view>

			<view style="padding: 10px 0; border-bottom:1px solid #eee; margin: 0 10px;" class="flex">
				<view style="font-size: 13px;">物流方式</view>
				<view class="flex">
					<view>未设置</view>
					<image style="width: 12px;" :src="pre_url+'/static/icon/right.png'" mode="widthFix"></image>
				</view>
			</view>

			<view style="padding: 10px 0; border-bottom:1px solid #eee; margin: 0 10px;" class="flex">
				<view style="font-size: 13px;">团购时间</view>
				<view class="flex">
					<view>发团即开始，7天后结束</view>
					<image style="width: 12px;" :src="pre_url+'/static/icon/right.png'" mode="widthFix"></image>
				</view>
			</view>

			<view style="padding: 10px 0; border-bottom:1px solid #eee; margin: 0 10px;" class="flex">
				<view style="font-size: 13px;">开团通知推送</view>
				<view class="flex">
					<view>全部订阅成员</view>
					<image style="width: 12px;" :src="pre_url+'/static/icon/right.png'" mode="widthFix"></image>
				</view>
			</view>

			<view style="padding: 10px 0; margin: 0 10px;" class="flex">
				<view>
					<view style="font-size: 13px;">更多团购设置</view>
					<view style="color: #aaa; font-size: 10px;">优惠设置、帮卖设置、隐私设置</view>
				</view>
				<image style="width: 12px;" :src="pre_url+'/static/img/arrowdown.png'" mode="widthFix"></image>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="bll">
			<button class="btn1" type="button" @click="formsubmit">保存并预览</button>
			<button class="btn2" type="button" @click="formsubmit">发布团购</button>
		</view>
	</view>
</template>
<script>
	import MarqueeComponent from './MarqueeComponent.vue';

	var app = getApp();

	export default {
		components: {
			MarqueeComponent
		},
		data() {
			return {
				tabs: [{
					txt: '大图',
					temp: 'picture',
					img: '../../static/daihuo/dt.png'
				}, {
					txt: '小图',
					temp: 'pictures',
					img: '../../static/daihuo/xt.png'
				}, {
					txt: '视频',
					temp: 'video',
					img: '../../static/daihuo/sp.png'
				}, {
					txt: '文字',
					temp: 'text',
					img: '../../static/daihuo/wz.png'
				}, {
					txt: '标签',
					img: '../../static/daihuo/bq.png'
				}, {
					txt: '加粉',
					img: '../../static/daihuo/jf.png'
				}, {
					txt: '承诺',
					img: '../../static/daihuo/cl.png'
				}],
				items: [],
				clist: [], // 分类列表，如果有的话
				cindex: -1, // 选中的分类索引

				showTab: false,
				showTxt: true,
				pre_url: app.globalData.pre_url,
				keyword: '',
				selectedGoods: [], // 存储选择的商品详细信息
				toView: 'tgjs',
				title: '', // 团购活动标题
				content: '', // 团购活动内容
				mobile: '', // 联系手机
				video: '', // 视频链接或路径
				pics: [], // 图片列表
				notice: '', // 存储公告列表
				// 其他需要的 data 属性,
				id : ''
			};
		},

		onLoad(opt) {
			// 监听返回页面传递的数据
			this.fetchNotices(); // 页面加载时请求公告信息
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on('selectedGoods', (data) => {
				if (data && Array.isArray(data)) {
					console.log('接收到的商品ID数组:', data);
					this.fetchSelectedGoodsDetails(data);
				}
			});

			if (opt.id) {
				this.id = opt.id;
				this.getData();
			}
		},

		onPageScroll(e) {
			if (e.scrollTop > 100) {
				this.showTab = true;
			} else {
				this.showTab = false;
			}
		},

		methods: {
			goBack() {
				uni.navigateBack();
			},
			getData() {
				var that = this;
				app.get('/Apidaihuoyiuan/detail', {
					id: that.id,
					pid: 0
				}, function(res) {

					console.log(res)
					if (res.status === 1) {
						that.items = res.pagecontent
						that.title = res.detail.name
						that.showTxt = false
						that.selectedGoods = res.products
					}
				}, function(error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
				});
			},

			fetchNotices() {
				var that = this;
				app.post('/ApiAdmintuan/tongzhilist', {}, function(res) {
					if (res.status === 1 && Array.isArray(res.data)) {

						console.log(res)
						let contents = '';
						// 遍历公告列表，解析 content 字段
						res.data.forEach(function(item) {
							contents += item.title;
						});
						that.notice = contents;
					} else {
						uni.showToast({
							title: res.msg || '获取公告失败1',
							icon: 'none'
						});
					}
				}, function(error) {
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
					console.error('获取公告失败:', error);
				});
			},
			goCom(temp) {
				var Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);
				this.showTxt = false;
				if (temp == 'text') {

					this.items.push({
						"id": Mid,
						"temp": "text",
						"params": {
							"content": '',
							"showcontent": '',
							"bgcolor": "#ffffff",
							"fontsize": "14",
							"lineheight": "20",
							"letter_spacing": "0",
							"bgpic": "",
							"align": "left",
							"color": "#000",
							"margin_x": "0",
							"margin_y": "0",
							"padding_x": "5",
							"padding_y": "5",
							"quanxian": {
								"all": true
							},
							"platform": {
								"all": true
							}
						},
						"data": "",
						"other": "",
						"content": ""
					});

				} else if (temp == 'picture') {
					this.uploadBigimg();
				} else if (temp == 'pictures') {
					this.items.push({
						id: Mid,
						temp: 'pictures',
						params: {
							"bgcolor": "#FFFFFF",
							"margin_x": "0",
							"margin_y": "0",
							"padding_x": "0",
							"padding_y": "0",
							"quanxian": {
								"all": true
							},
							"platform": {
								"all": true
							}
						},
						data: [],
						other: "",
						content: ""
					});
				} else if (temp == 'video') {
					
					this.uploadVideo();
				}
			},

			scrollToElement(id) {
				this.toView = id;
				uni.createSelectorQuery().select('#' + id).boundingClientRect(res => {
					if (res) {
						uni.pageScrollTo({
							scrollTop: res.top + (uni.getSystemInfoSync().screenHeight / 2), // 适当调整滚动位置
							duration: 300
						});
					}
				}).exec();
			},

			uploadBigimg() {
				var that = this;
				var Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);
				app.chooseImage(function(urls) {

					let obj = [{
						imgurl: urls[0]
					}]

					that.items.push({
						id: Mid,
						temp: 'picture',
						params: {
							"bgcolor": "#FFFFFF",
							"margin_x": "0",
							"margin_y": "0",
							"padding_x": "0",
							"padding_y": "0",
							"quanxian": {
								"all": true
							},
							"platform": {
								"all": true
							}
						},
						data: obj,
						other: "",
						content: ""
					});

				}, 1);
			},

			uploadSmallimg(ind) {
				var that = this;
				app.chooseImage(function(urls) {

					let arr = []
					urls.filter(item => {
						arr.push({
							imgurl: item
						})
					})

					that.items[ind].data = that.items[ind].data.concat(arr);
				}, 9);
			},

			uploadVideo() {
				  var that = this;
				  uni.chooseVideo({
					sourceType: ['album', 'camera'],
					maxDuration: 60,
					success: function (res) {
					  var tempFilePath = res.tempFilePath;
					  app.showLoading('上传中');
					  uni.uploadFile({
						url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,
						filePath: tempFilePath,
						name: 'file',
						success: function (res) {
						  app.showLoading(false);
						  var data = JSON.parse(res.data);

						  if (data.status == 1) {
							  
							  that.items.push({
							  	id: Mid,
							  	temp: 'video',
							  	params: {
							  		"bgcolor": "#FFFFFF",
							  		"margin_x": "0",
							  		"margin_y": "0",
							  		"padding_x": "0",
							  		"padding_y": "0",
							  		"quanxian": {
							  			"all": true
							  		},
							  		"platform": {
							  			"all": true
							  		}
							  	},
							  	data: data.url,
							  	other: "",
							  	content: ""
							  });
		
						  } else {
							app.alert(data.msg);
						  }
						},
						fail: function (res) {
						  app.showLoading(false);
						  app.alert(res.errMsg);
						}
					  });
					},
					fail: function (res) {
					  console.log(res); //alert(res.errMsg);
					}
				  });

			},

			iptItem(e, ind) {

				this.items[ind].params.content = e.detail.value;
				this.items[ind].params.showcontent = e.detail.value;
			},

			editTxt(e) {
				var Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);
				this.items.push({
					"id": Mid,
					"temp": "text",
					"params": {
						"content": e.detail.value,
						"showcontent": e.detail.value,
						"bgcolor": "#ffffff",
						"fontsize": "14",
						"lineheight": "20",
						"letter_spacing": "0",
						"bgpic": "",
						"align": "left",
						"color": "#000",
						"margin_x": "0",
						"margin_y": "0",
						"padding_x": "5",
						"padding_y": "5",
						"quanxian": {
							"all": true
						},
						"platform": {
							"all": true
						}
					},
					"data": "",
					"other": "",
					"content": ""
				});

				this.showTxt = false;
			},

			toAdd(ind) {
				let that = this
				uni.showActionSheet({
					itemList: [
						'向下添加大图',
						'向下添加小图',
						'向下添加视频',
						'向下添加文字',
						'向下添加加粉卡片',
					],
					success(e) {

						let txt = '';

						if (e.tapIndex == 0) {
							txt = 'picture';
						} else if (e.tapIndex == 1) {
							txt = 'pictures';
						} else if (e.tapIndex == 2) {
							txt = 'video';
						} else if (e.tapIndex == 3) {
							txt = 'text';
						} else if (e.tapIndex == 4) {
							txt = '加粉';
						}


						that.goCom(txt);
					}
				})

			},

			// 上移
			toTop(index) {
				if (index > 0) { // 确保不是第一个元素
					const temp = this.items[index];

					this.$set(this.items, index, this.items[index - 1]);
					this.$set(this.items, index - 1, temp);
				}
			},

			// 下移
			toBottom(index) {
				if (index < this.items.length - 1) { // 确保不是最后一个元素
					const temp = this.items[index];
					this.$set(this.items, index, this.items[index + 1]);
					this.$set(this.items, index + 1, temp);
				}
			},

			// 置顶
			toOne(index) {
				if (index > 0) { // 确保不是第一个元素
					const item = this.items.splice(index, 1)[0]; // 移除当前项
					this.items.unshift(item); // 将该项插入到数组首位
				}
			},

			// 删除
			delItem(index) {
				this.items.splice(index, 1); // 从 items 数组中移除当前项
			},

			// 上移商品
			goodTop(index) {
				if (index > 0) {
					const temp = this.selectedGoods[index];
					this.$set(this.selectedGoods, index, this.selectedGoods[index - 1]);
					this.$set(this.selectedGoods, index - 1, temp);
				}
			},

			// 下移商品
			goodBottom(index) {
				if (index < this.selectedGoods.length - 1) {
					const temp = this.selectedGoods[index];
					this.$set(this.selectedGoods, index, this.selectedGoods[index + 1]);
					this.$set(this.selectedGoods, index + 1, temp);
				}
			},

			// 置顶商品
			goodOne(index) {
				if (index > 0) {
					const item = this.selectedGoods.splice(index, 1)[0];
					this.selectedGoods.unshift(item);
				}
			},

			// 删除商品
			removeSelectedGood(index) {
				this.selectedGoods.splice(index, 1);
			},
			removeSelectedGood(ind) {
				let that = this;
				uni.showModal({
					content: '确定删除商品吗？',
					success(res) {
						if (res.confirm) {
							that.selectedGoods.splice(ind, 1);
						}
					}
				});
			},

			removeimg(idx, parentIndex) {
				this.items[parentIndex].value.splice(idx, 1);
			},

			previewImage(url) {
				uni.previewImage({
					current: url,
					urls: [url]
				});
			},

			uploadVideo(index) {
				var that = this;
				// 根据您的需求实现视频上传逻辑
				// 示例：选择视频后上传并获取URL
				uni.chooseVideo({
					success: function(res) {
						// 假设上传成功后获取视频URL
						// 这里需要根据实际情况实现上传逻辑
						that.items[index].data = res.tempFilePath; // 示例，实际应为上传后的URL
					},
					fail: function(err) {
						console.error('选择视频失败:', err);
					}
				});
			},

			/**
			 * 导航到商品选择页面
			 */
			navigateToSelectGoods() {
				const that = this;
				uni.navigateTo({
					url: '/admin/daihuotuan/selectgoods',
					events: {
						selectedGoods: function(data) {
							// 接收到数据后，使用API获取商品详细信息
							console.log('接收到的商品ID数组:', data);
							that.fetchSelectedGoodsDetails(data);
						}
					}
				});
			},

			/**
			 * 通过API获取选中商品的详细信息
			 * @param {Array} ids - 选中的商品ID数组
			 */
			fetchSelectedGoodsDetails(ids) {
				var that = this;

				if (!Array.isArray(ids) || ids.length === 0) {
					return;
				}

				// 显示加载提示
				uni.showLoading({
					title: '加载商品中...'
				});

				// 假设后端API为 'ApiDaihuoyiuan/getProducts'，接受参数为商品ID数组
				app.post('ApiAdmintuan/getProducts', {
					ids: ids
				}, function(res) {
					uni.hideLoading();
					if (res.status === 1 && Array.isArray(res.data)) {
						that.selectedGoods = res.data;
					} else {
						uni.showToast({
							title: res.msg || '获取商品详情失败',
							icon: 'none'
						});
					}
				}, function(error) {
					uni.hideLoading();
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					});
					console.error('获取商品详情失败:', error);
				});
			},

			/**
			 * 提交带货团信息
			 * @param {Object} e - 表单提交事件对象
			 */
			formsubmit(e) {
				var that = this;
				console.log(that.items);
				var clist = that.clist || [];
				var cid = 0;

				if (clist.length > 0) {
					if (that.cindex == -1) {
						app.error('请选择分类');
						return false;
					}
					cid = clist[that.cindex].id;
				}

				// 获取表单数据
				var formdata = e.detail.value || {};
				var title = formdata.title || that.title;
				var content = formdata.content || that.content;
				var pics = formdata.pics || that.pics;
				var video = formdata.video || that.video;
				var mobile = formdata.mobile || that.mobile;
				var items = that.items;
				var goods = that.selectedGoods.map(good => good.id); // 商品ID数组

				// 数据验证
				if (!title) {
					app.error('请输入团购活动标题');
					return false;
				}
				if ((!content && items.length === 0) && pics.length === 0) {
					app.error('请输入内容或添加图片');
					return false;
				}

				console.log(items)

				if (goods.length === 0) {
					app.error('请至少选择一个商品');
					return false;
				}

				// 构建提交数据
				var payload = {
					cid: cid,
					title: title,
					content: content,
					pics: pics,
					video: video,
					mobile: mobile,
					items: items,
					goods: goods
				};
				
				if(this.id){
					payload.id = this.id
				}

				// 显示加载提示
				app.showLoading(true);

				// 发送 POST 请求
				app.post('ApiAdmintuan/mobileadddaihuotuan', {
					payload: payload
				}, function(res) {
					app.showLoading(false);
					if (res.status == 1) {
						app.success(res.msg);
						setTimeout(function() {
							uni.navigateTo({
								url: '/daihuobiji/kuaituan/detail?id=' + res.id
							});
						}, 1000);
					} else {
						app.error(res.msg);
					}
				}, function(error) {
					app.showLoading(false);
					app.error('提交失败，请稍后再试');
					console.error('提交带货团失败:', error);
				});
			},
		}
	}
</script>


<style>
	.container {
		background: #F6F6F6;
		padding-bottom: 40px;
	}

	.dp-userinfo2 {
		width: 100%;
		height: 180px;
		display: flex;
		flex-direction: column;
		position: relative;
		background: url(https://qixian.zhonghengyikang.com.cn/static/imgsrc/userinfobg.png) 0% 0% / 100% no-repeat;
		margin: 0px;
		padding: 11px;
	}

	.dp-userinfo-order {
		background: #fff;
		padding: 15px 10px;
		border-radius: 8px;
	}

	.tit-m {
		font-size: 15px;
		font-weight: bold;
	}

	.btn-m {
		border: 1px solid rgb(253, 74, 70);
		border-radius: 3px;
		color: rgb(253, 74, 70);
		font-size: 10px;
		padding: 2px 5px;
	}

	.btn-b {
		border: 1px solid #fff;
		border-radius: 3px;
		color: #fff;
		font-size: 10px;
		padding: 2px 5px;
		background: #00000011;
	}

	.flex {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.flex2 {
		display: flex;
		flex-wrap: nowrap;
		/* 控制是否允许换行，默认不换行 */
		justify-content: space-between;
		/* 项目之间的距离自动分配 */
		align-items: center;
		/* 垂直方向居中对齐 */
		gap: 10px;
		/* 控制项目之间的间距，可以根据需要调整 */
		padding: 0 10px;
		/* 可以添加一些内边距，让内容与边框保持距离 */
		box-sizing: border-box;
		/* 确保 padding 包含在元素的总宽度和高度内 */

	}

	.load-list {
		opacity: 1;
		animation-name: list;
		animation-duration: 0.1s;
		animation-timing-function: linear;
		animation-direction: alternate;
	}

	@keyframes list {
		from {
			opacity: 0
		}

		to {
			opacity: 1
		}
	}

	.mz {
		border: 1px solid #ccc;
		border-radius: 3px;
		padding: 1px 4px;
		margin-left: 10px;
		font-size: 10px;
	}

	.layui-imgbox {
		margin-right: 16rpx;
		margin-bottom: 10rpx;
		font-size: 24rpx;
		position: relative;
	}

	.layui-imgbox-close {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		right: -16rpx;
		top: -16rpx;
		z-index: 90;
		color: #999;
		font-size: 32rpx;
		background: #fff
	}

	.layui-imgbox-close image {
		width: 100%;
		height: 100%
	}

	.layui-imgbox-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		padding: 2px;
		border: #d3d3d3 1px solid;
		background-color: #f6f6f6;
		overflow: hidden
	}

	.layui-imgbox-img>image {
		max-width: 100%;
	}

	.uploadbtn {
		position: relative;
		height: 200rpx;
		width: 200rpx
	}

	.search-bar {
		display: flex;
		align-items: center;
	}

	.search-box {
		display: flex;
		align-items: center;
		height: 80rpx;
		border-radius: 10rpx;
		background-color: #f5f5f5;
		flex: 1;
	}

	.search-box .img {
		width: 24rpx;
		height: 24rpx;
		margin-right: 10rpx;
		margin-left: 30rpx;
	}

	.search-box .search-text {
		font-size: 24rpx;
		color: #C2C2C2;
		width: 100%;
	}

	.ml {
		display: flex;
		margin-top: 30px;
		align-items: center;
	}

	.ltn {
		position: absolute;
		left: 0;
		top: 0;
		padding: 3px 10px;
		background: #EA594E33;
		color: #EA594E;
		border-radius: 10rpx 0 10rpx 0;
	}

	.abtn {
		border: 1px solid #EA594E;
		text-align: center;
		padding: 10px;
		margin-top: 10px;
		border-radius: 10rpx;
		color: #EA594E;
	}

	.bll {
		position: fixed;
		bottom: 0;
		width: 100%;
		background: #fff;
		padding: 10px;
		display: flex;
		justify-content: space-between;
	}

	.bll .btn1 {
		text-align: center;
		width: 50%;
		border-radius: 10rpx;
		border: 1px solid #EA594E;
		color: #EA594E;
		padding: 10px;
		margin: 0 5px;
	}

	.bll .btn2 {
		text-align: center;
		width: 50%;
		border-radius: 10rpx;
		background: #EA594E;
		color: #fff;
		padding: 10px;
		margin: 0 5px;
	}

	.on_tab {
		border-bottom: 2px solid #EA594E;
		padding-bottom: 5px;
		color: #EA594E
	}
</style>
