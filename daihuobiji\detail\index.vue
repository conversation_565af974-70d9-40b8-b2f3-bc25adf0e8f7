<template>
	<!-- #ifdef MP-WEIXIN -->
	<view class="daihuobiji-detail-page" :style="'padding-top:' + totalBarHeight + 'px;'">
	<!-- #endif -->
	<!-- #ifndef MP-WEIXIN -->
	<view class="daihuobiji-detail-page">
	<!-- #endif -->
		<!-- 骨架屏 -->
		<view class="skeleton" v-if="loading">
			<view class="skeleton-swiper"></view>
			<view class="skeleton-info">
				<view class="skeleton-title"></view>
				<view class="skeleton-content"></view>
				<view class="skeleton-time"></view>
			</view>
		</view>

		<!-- 实际内容 -->
		<template v-else>
			<!-- 顶部导航 -->
			<DetailNav v-if="detailInfo" :userInfo="detailInfo" :noteId="String(detailInfo.id)" />
			
			<view class="cover-swipper">
				<swiper class="swiper" :style="{ height: swiperHeight + 'px' }" circular indicator-dots="#D9D9D9"
					:autoplay="false" :interval="interval" indicator-active-color="#E3582F" :duration="duration"
					@change="onSwiperChange">
					<swiper-item v-for="(item, index) in detailInfo.videoAndImg" :key="index">
						<view class="swiper-item">
							<image v-if="item.type == 'img'" :src="item.url" class="cover-img" mode="widthFix"
								@load="onImageLoad($event, index)" @tap="openImagePreview(index)"></image>
							<video :src="item.url" v-if="item.type == 'video'"></video>
						</view>
					</swiper-item>

				</swiper>
			</view>

			<view class="info-box">
				<text class="title-text">{{ detailInfo.title || '' }}</text>
				<text class="content-text">{{ detailInfo.content }}</text>
				<view class="create-time">{{ detailInfo.showtime }}</view>
				<!-- 修改显示条件 -->
				<view class="commission-box" v-if="Number(totalCommission) > 0">
					<view class="commission-content">
						<image class="commission-icon" :src="pre_url+'/static/img/shortvideo_cart.png'" mode="aspectFit"></image>
						<view class="commission-info">
							<text class="commission-text">预计佣金</text>
							<text class="commission-amount">￥{{totalCommission}}</text>
							<text class="commission-unit">{{commission_desc}}</text>
						</view>
						<text class="commission-tips">分享商品后用户下单可获得</text>
					</view>
				</view>
				<!-- 店铺和带货团按钮 -->
				<view class="action-buttons" v-if="detailInfo.shop_id > 0 || detailInfo.leader_id > 0">
					<view class="action-btn shop" v-if="detailInfo.shop_id > 0" @tap="goto" 
						:data-url="'/pagesExt/business/index?id=' + detailInfo.shop_id"
						:style="'background: ' + t('color2')">
						<image :src="pre_url+'/static/img/set.png'" mode="widthFix"></image>
						<!-- <text>{{detailInfo.shop_name || 'TA的店铺'}}</text> -->
						<text>TA的店铺</text>
					</view>
					<view class="action-btn team" v-if="detailInfo.leader_id > 0" @tap="goto"
						:data-url="'/daihuobiji/kuaituan/tuanzhangtuanlist?bid=' + detailInfo.leader_id"
						:style="'background: ' + t('color2')">
						<image :src="pre_url+'/static/img/add.png'" mode="widthFix"></image>
						<text>带货团</text>
					</view>
				</view>
				<!-- 编辑删除按钮 -->
				<view class="action-buttons" v-if="detailInfo.mid == mid">
					<view class="action-btn edit" @tap="editNote">
						<image :src="pre_url+'/static/img/set.png'" mode="widthFix"></image>
						<text>编辑</text>
					</view>
					<view class="action-btn delete" @tap="deleteNote">
						<image :src="pre_url+'/static/img/close2.png'" mode="widthFix"></image>
						<text>删除</text>
					</view>
				</view>
			</view>

			<view class="comment-box">
				<view class="top-tips">共{{ datalist.length }}条评论</view>

				<view class="comment-input-box" style="margin: 15px 0;" @tap="goto"
					:data-url="'pinglun?type=0&id=' + detailInfo.id">
					<input class="comment-input" placeholder="请在此评论" />
				</view>

				<view class="plbox_content" style="padding-bottom: 50px;" id="datalist">
					<block v-for="(item, idx) in datalist" :key="idx">
						<view class="item1 flex">
							<view class="f1 flex0">
								<image :src="item.headimg"></image>
							</view>
							<view class="f2 flex-col">
								<text class="t1">{{ item.nickname }}</text>
								<text class="t11">{{ item.createtime }}</text>
								<view class="t2 plcontent">
									<parse :content="item.content" />
								</view>
								<block v-if="item.replylist.length > 0">
									<view class="relist">
										<block v-for="(hfitem, index) in item.replylist" :key="index">
											<view class="item2">
												<view class="f1">
													{{ hfitem.nickname }}
													<text class="t1">{{ hfitem.createtime }}</text>
													<text v-if="hfitem.mid == mid" class="phuifu"
														style="font-size:20rpx;margin-left:20rpx;font-weight:normal"
														@tap="delplreply" :data-id="hfitem.id">
														删除
													</text>
												</view>
												<view class="f2 plcontent">
													<parse :content="hfitem.content" />
												</view>
											</view>
										</block>
									</view>
								</block>
								<view class="t3 flex">
									<view class="flex1">
										<text class="phuifu" style="cursor:pointer" @tap="goto"
											:data-url="'pinglun?type=1&id=' + detailInfo.id + '&hfid=' + item.id">
											回复
										</text>
										<text v-if="item.mid == mid" class="phuifu" style="cursor:pointer;margin-left:20rpx"
											@tap="delpinglun" :data-id="item.id">
											删除
										</text>
									</view>
									<view class="flex-y-center pzan" @tap="pzan" :data-id="detailInfo.id" :data-index="idx">
										<image :src="pre_url+'/static/img/lt_like' + (item.iszan == 1 ? '2' : '') + '.png'"></image>
										{{ item.zan }}
									</view>
								</view>
							</view>
						</view>
					</block>
				</view>
			</view>

			<view class="footer-box">
				<view class="buy">
					<view class="good-img" style="margin-top: 0px;">
						<image style="width: 100%;height: 100%;border-radius: 50px;" :src="yindaologo"></image>
					</view>
					<view class="buy-text" @tap="openShop">
						<text class="text-tips">{{ linkname }}</text>
						<!-- <text class="price-text">￥{{price}}</text> -->
					</view>
				</view>
				<view class="footer-actions">
					<view class="action-item" @tap="goto" :data-url="'pinglun?type=0&id=' + detailInfo.id">
						<image class="action-item-img" :src="pre_url+'/static/img/daihuobiji_comment.png'"></image>
						<text class="action-item-label">评论</text>
					</view>
					<!-- 
                    <view class="action-item" @tap="goto" :data-url="'pinglun?type=0&id=' + detailInfo.id">
                        <image class="action-item-img" src="../../static/img/daihuobiji_comment.png"></image>
                        <text class="action-item-label">收藏</text>
                    </view> 
                    -->
					<view class="action-item" @tap="zan" :data-id="detailInfo.id">
						<image class="action-item-img" :src="pre_url+'/static/img/lt_like' + (iszan ? '2' : '') + '.png'"></image>
						<text class="action-item-label">
							{{ detailInfo.zan }}
						</text>
					</view>
				</view>
			</view>

			<!-- 商品购物 -->
			<uni-popup ref="shopShow" type="bottom">
				<view class="viewShop">
					<view style="padding: 20rpx;position: sticky;top: 0;background: #fff;z-index: 1;">
						<view style="text-align: center;font-weight: bold;margin-bottom: 10rpx;">
							文中提到的商品（{{matchedData.length}}）
						</view>
						<view style="text-align: center;font-size: 24rpx;color: #999;">
							预估佣金合计: <text style="color: #E54D42;font-weight: bold;">￥{{totalCommission}}</text>
						</view>
					</view>

					<view class="cart-container">
						<scroll-view :scroll-top="0" scroll-y="true" style="height: 50vh;">
							<view class="cart-item" v-for="(item, index) in matchedData" :key="index">
								<view class="item-selector" @click="toggleSelection(item)">
									<checkbox :checked="item.selected" style="transform:scale(0.8)"></checkbox>
								</view>
								<view class="image-box" @click="goto" :data-url="'/shopPackage/shop/product?id='+item.id">
									<image class="item-image" :src="item.pic" mode="heightFix"></image>
								</view>

								<view class="item-info">
									<text class="item-name" @click="goto" :data-url="'/shopPackage/shop/product?id='+item.id">{{ item.name }}</text>
									<view style="display: flex;justify-content: space-between;width: 100%;">
										<view>
											<text class="item-price">
												<span>￥</span>
												<span style="font-size: 34rpx;font-weight: bold;">{{ item.sell_price }}</span>
											</text>
											<text style="font-size: 20rpx;color: #999;margin-left: 8px;" v-if="item.commission > 0">
												预估佣金 ￥{{item.commission}}{{item.commission_desc}}
											</text>
										</view>
										<view style="background: #E54D42;color: #fff;border-radius: 25px;padding: 3px 12px;font-size: 11px;line-height: 1.2;white-space: nowrap;height: 20px;display: flex;align-items: center;" 
											@click.stop="addCartOne(item)">加入购物车</view>
									</view>
								</view>
							</view>
						</scroll-view>

						<view style="position: sticky;bottom: 0;height: 100rpx;display: flex;justify-content: space-between;align-items: center;padding: 0 20rpx;background: #fff;">
							<view @tap="shopAllSelectedClick">
								<checkbox :checked="shopAllSelected" style="transform:scale(0.8)"></checkbox>
								全选
							</view>

							<view style="text-align: center;margin-left: 100px;position: relative;" @tap="goto" data-url="/shopPackage/shop/cart">
								<view>
									<image style="width: 25px;height: 25px;" :src="pre_url+'/static/icon/buy.png'" />
								</view>
								<view style="position: absolute;top:-5px;right:0;background: red;color: #fff;border-radius: 50px;padding: 0 5px;"
									v-if="cart_num>0">{{cart_num}}</view>
							</view>

							<view :class="isAnyItemSelected?'shopButtonActive':'shopButton'" @click="shop">
								加入购物车
							</view>
						</view>
					</view>
				</view>
			</uni-popup>

		</template>

		<!-- 规格选择弹窗 -->
		<uni-popup ref="specPopup" type="bottom" :maskClick="false" :animation="false">
			<view class="spec-dialog">
				<buydialog 
					v-if="buydialogShow" 
					:proid="product_id" 
					:tid="tid" 
					:btntype="btntype"
					@buydialogChange="buydialogChange" 
					@showLinkChange="showLinkChange" 
					@addcart="addcart">
				</buydialog>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	import DetailNav from '../components/DetailNav.vue'
	var app = getApp();

	export default {
		components: {
			DetailNav
		},
		data() {
			return {
				opt: {},
				indicatorDots: true,
				autoplay: true,
				interval: 2000,
				duration: 500,
				detailInfo: {},
				datalist: [],
				price: 0,
				pre_url: app.globalData.pre_url,
				need_call: false,
				pagenum: 1,
				nomore: false,
				iszan: "",
				yindaologo: '',
				weixin: false,
				yindao_link: '',
				linkname: '',
				mid: "",
				plcount: 0,
				myscore: 0,
				nodata: false,
				imageHeights: [], // 存储每张图片的显示高度
				swiperHeight: 300, // Swiper 初始高度
				imagesLoaded: 0, // 已加载图片数量
				maxSwiperHeight: 500, // Swiper 最大高度
				showImagePreview: false, // 控制大图片遮罩层的显示
				currentPreviewIndex: 0, // 当前预览的图片索引
				currentSwiperIndex: 0, // 当前 Swiper 的索引
				statusBarHeight: 0,
				navigationBarHeight: 0,
				totalBarHeight: 0, // 状态栏高度 + 导航栏高度
				matchedData: [],
				
				shopAllSelected: false,
				cart_num : 0,
				safeAreaBottom: 0,
				totalCommission: 0,
				commission_desc: '元',
				is_h5: false,
				buydialogShow: false, // 规格选择弹窗显示状态
				btntype: 1, // 按钮类型：1加入购物车，2立即购买
				product_id: '', // 当前选择的商品ID
				tid: 0, // 团ID
				loading: true,  // 添加loading状态
			}
		},
		
		computed: {
			isAnyItemSelected() {
				return this.matchedData.some(item => item.selected);
			},
			containerStyle() {
				let style = '';
				// #ifdef H5
				if (this.is_h5) {
					style = `padding-bottom: ${this.safeAreaBottom}px;`;
				}
				// #endif
				return style;
			}
		},
		onShow() {
			try {
				// 确保app对象存在
				if(typeof app !== 'undefined') {
					this.getCart();
				} else {
					console.warn('app对象未定义');
				}
			} catch(err) {
				console.error('onShow错误:', err);
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			
			// 显示加载中
			uni.showLoading({
				title: '加载中'
			});

			// 先加载基础数据
			this.getBaseData();

			// #ifdef MP-WEIXIN
			this.initWeixinInfo();
			// #endif

			// #ifdef H5
			this.initH5Info();
			// #endif
		},
		methods: {
			getCart() {
				let that = this
				app.get('ApiShop/cart', {}, function(res) {
					
					if (res.cartlist.length > 0) {
						that.cart_num = res.cartlist[0].prolist.length;
					}
				});
			},
			getdata: function(loadmore) {
				var that = this;
				var id = that.opt.id;
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
					this.nomore = false;
				}
				var pagenum = that.pagenum;
				app.get('Apidaihuobiji/detail', {
					id: id,
					pagenum: pagenum
				}, function(res) {
					console.log('接口返回数据:', res); 

					that.loading = false;
					if (res.need_call) {
						that.need_call = true;
					}

					var data = res.datalist;
					if (pagenum === 1) {
						that.mid = res.mid;
						that.datalist = res.datalist;
						that.plcount = res.plcount;
						that.iszan = res.iszan;
						that.detailInfo = res.detail;
						
						// 检查商品数据
						console.log('商品数据来源:', res.detail.productids); 
						
						// 如果有商品ID，调用获取商品列表
						if(res.detail && res.detail.productids) {
							that.getShopList(res.detail.productids);
						}
						
						console.log('that.detailInfo.video', that.detailInfo.video)
						that.detailInfo.video = that.detailInfo.video == '' ? [] : that.detailInfo.video.split(
							',')
						that.detailInfo.videoAndImg = []
						if (that.detailInfo.pics.length > 0) { // 图片
							that.detailInfo.pics.forEach(item => {
								that.detailInfo.videoAndImg.push({
									url: item,
									type: 'img'
								})
							})
						}
						if (that.detailInfo.video.length > 0) { // 视频
							that.detailInfo.video.forEach(item => {
								that.detailInfo.videoAndImg.push({
									url: item,
									type: 'video'
								})
							})
						}
						that.myscore = res.myscore;
						that.yindaologo = res.yindaologo;
						that.yindao_link = res.yindao_link;
						that.linkname = res.linkname;
						if (data.length === 0) {
							that.nodata = true;
						} else {
							that.nodata = false;
						}
						// 重置图片相关数据
						that.imageHeights = [];
						that.swiperHeight = 300; // 确保 Swiper 有初始高度
						that.imagesLoaded = 0;
						that.totalCommission = res.totalCommission || 0;
						that.commission_desc = res.commission_desc || '元';
						
						// 处理商品数据
						if(res.products && res.products.length > 0) {
							that.matchedData = res.products.map(item => {
								return {
									...item,
									selected: false // 添加选中状态
								}
							});
						}
						
						// 打印检查数据
						console.log('商品数据:', that.matchedData);
						console.log('佣金信息:', {
							totalCommission: that.totalCommission,
							commission_desc: that.commission_desc
						});
					} else {
						if (data.length === 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}

					that.pagenum += 1;
				})
			},

			zan: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				app.post("Apidaihuobiji/zan", {
					id: id
				}, function(res) {
					that.iszan = res.type !== 0;
					that.detailInfo.zan = res.zancount;
				});
			},
			pzan: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				var index = e.currentTarget.dataset.index;
				var datalist = that.datalist;
				app.post("Apidaihuobiji/pzan", {
					id: id
				}, function(res) {
					datalist[index].iszan = res.type !== 0 ? 1 : 0;
					datalist[index].zan = res.zancount;
					that.datalist = datalist;
				});
			},

			delplreply: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				app.confirm('确定要删除吗?', function() {
					app.post("Apidaihuobiji/delplreply", {
						id: id
					}, function(res) {
						if(res.status === 1) {
							app.success(res.msg);
							// 遍历datalist找到并删除对应的回复评论
							that.datalist = that.datalist.map(item => {
								if(item.replylist && item.replylist.length > 0) {
									item.replylist = item.replylist.filter(reply => reply.id !== id);
								}
								return item;
							});
						} else {
							app.error(res.msg || '删除失败');
						}
					});
				});
			},

			delpinglun: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				app.confirm('确定要删除这条评论吗?', function() {
					app.post("Apidaihuobiji/delpinglun", {
						id: id
					}, function(res) {
						if(res.status === 1) {
							app.success(res.msg);
							// 直接从datalist中移除被删除的评论
							that.datalist = that.datalist.filter(item => item.id !== id);
							// 更新评论总数
							that.plcount = that.datalist.length;
						} else {
							app.error(res.msg || '删除失败');
						}
					});
				});
			},




			onImageLoad: function(e, index) {
				// 获取图片尺寸
				var imgWidth = e.detail.width;
				var imgHeight = e.detail.height;

				// 防止宽高为零
				if (imgWidth === 0 || imgHeight === 0) {
					return;
				}

				// 计算显示高度
				var viewWidth = uni.getSystemInfoSync().windowWidth;
				var ratio = imgHeight / imgWidth;
				var viewHeight = viewWidth * ratio;

				// 如果图片高度超过最大高度，使用最大高度
				var displayHeight = viewHeight > this.maxSwiperHeight ? this.maxSwiperHeight : viewHeight;

				// 存储每张图片的显示高度
				this.$set(this.imageHeights, index, displayHeight);

				// 增加已加载图片数量
				this.imagesLoaded += 1;

				// 当所有图片加载完成后，计算 Swiper 的高度
				if (this.imagesLoaded === this.detailInfo.pics.length) {
					// 取所有图显示高度中的最大值作为 Swiper 的高度
					this.swiperHeight = Math.max(...this.imageHeights);

					// 更新视图
					this.$forceUpdate();
				}
			},

			onSwiperChange: function(e) {
				this.currentSwiperIndex = e.detail.current;
			},

			openImagePreview(index) {
				// 调用 uni.previewImage 打开图片预览
				uni.previewImage({
					current: this.detailInfo.pics[index], // 当前预览的图片
					urls: this.detailInfo.pics, // 所有图片的 URL 列表
					indicator: 'number', // 预览图片时的指示器样式，可选 'default' 或 'number'
					loop: true, // 是否循环显示图片
				});
			},
			closeImagePreview: function() {
				this.showImagePreview = false;
			},

			// 开购物车
			openShop() {
				if(this.detailInfo.productids) {
					// 如果有商品ID,先获取商品列表
					let idsArr = this.detailInfo.productids.split(',').filter(id => id);
					if(idsArr.length > 0) {
						this.getShopList(this.detailInfo.productids);
						this.$refs.shopShow.open('top');
						this.getCart();
					} else {
						// 商品ID为空时跳转
						if(this.yindao_link) {
							uni.navigateTo({
								url: this.yindao_link
							});
						} else {
							uni.reLaunch({
								url: '/pages/index/index'
							});
						}
					}
				} else {
					// 没有商品ID时跳转
					if(this.yindao_link) {
						uni.navigateTo({
							url: this.yindao_link
						});
					} else {
						uni.reLaunch({
							url: '/pages/index/index'
						});
					}
				}
			},
			
			// 获取商品列表
			getShopList(ids) {
				if(!ids) return;
				let that = this;
				let idsArr = ids.split(',').filter(id => id);
				
				// 清空现有列表
				that.matchedData = [];
				
				// 遍历获取商品信息
				idsArr.forEach(id => {
					app.post('ApiShop/product', {
						id: id
					}, function(res) {
						if(res.product) {
							that.matchedData.push({
								id: res.product.id,
								pic: res.product.pic,
								name: res.product.name,
								sell_price: res.product.sell_price,
								selected: false
							});
						}
					});
				});
			},
			
			// 跳转到店铺
			gotoShop() {
				if(this.detailInfo.shop_id) {
					this.goto({
						url: '/shopPackage/shop/shop?id=' + this.detailInfo.shop_id
					})
				}
			},
			
			// 跳转到带货团
			gotoTeam() {
				if(this.detailInfo.leader_id) {
					this.goto({
						url: '/pages/leader/index?id=' + this.detailInfo.leader_id
					})
				}
			},
			
			// 编辑笔记
			editNote() {
				uni.navigateTo({
					url: '/daihuobiji/detail/fatieedit?id=' + this.detailInfo.id + '&type=edit'
				})
			},
			
			// 删除笔记
			deleteNote() {
				const that = this
				app.confirm('确定要删除这条笔记吗?', function() {
					app.post('Apidaihuobiji/detalbiji', {
						id: that.detailInfo.id
					}, function(res) {
						if(res.status === 1) {
							app.success('删除成功')
							// 立即触发事件，不要等待
							const pages = getCurrentPages()
							const prevPage = pages[pages.length - 2] // 获取上一页
							if (prevPage) {
								// 直接调用上一页的方法
								prevPage.$vm.getdata()
							}
							setTimeout(function() {
								uni.navigateBack()
							}, 1000)
						} else {
							app.error('删除失败')
						}
					})
				})
			},

			// 打开规格选择弹窗
			openSpecPopup(item) {
				this.product_id = item.id;
				this.btntype = 1;
				this.buydialogShow = true;
				this.$refs.specPopup.open('bottom');
			},
			
			// 关闭规格选择弹窗
			closeSpecPopup() {
				this.buydialogShow = false;
				this.$refs.specPopup.close();
			},
			
			// 规格选择弹窗变化回调
			buydialogChange(e) {
				if (!this.buydialogShow) {
					this.btntype = e.currentTarget.dataset.btntype;
				}
				this.buydialogShow = !this.buydialogShow;
				
				if (this.buydialogShow) {
					this.$refs.specPopup.open('bottom');
				} else {
					this.$refs.specPopup.close();
				}
			},
			
			// 添加到购物车回调
			addcart(e) {
				let that = this;
				if(e && e.num) {
					that.cart_num = that.cart_num + e.num;
				}
				// 关闭规格选择弹窗
				that.closeSpecPopup();
				// 更新购物车数量
				setTimeout(() => {
					that.getCart();
					app.success('添加成功');
				}, 500);
			},
			
			// 单个商品加入购物车
			addCartOne(item) {
				this.openSpecPopup(item);
			},
			
			// 批量加入购物车
			shop() {
				if (!this.isAnyItemSelected) {
					app.success('请选择商品');
					return;
				}
				
				const firstSelected = this.matchedData.find(item => item.selected);
				if(firstSelected) {
					this.openSpecPopup(firstSelected);
				}
			},
			
			// 修改打开规格选择弹窗的方法
			buydialogChange(e) {
				let that = this;
				if (!that.buydialogShow) {
					that.btntype = e.currentTarget.dataset.btntype;
				}
				that.buydialogShow = !that.buydialogShow;
				
				// 使用uni-popup打开/关闭弹窗
				if (that.buydialogShow) {
					that.$refs.specPopup.open('bottom');
				} else {
					that.$refs.specPopup.close();
				}

				setTimeout(function() {
					that.getCart();
				}, 1000);
			},

			// 切换选中状态
			toggleSelection(item) {
				this.$set(item, 'selected', !item.selected);
				this.shopAllSelected = this.matchedData.every(item => item.selected);
			},
			
			// 全选
			shopAllSelectedClick() {
				this.shopAllSelected = !this.shopAllSelected;
				this.matchedData.forEach(item => {
					item.selected = this.shopAllSelected;
				});
			},
			
			// 初始化微信环境信息
			initWeixinInfo() {
				this.weixin = true;
				try {
					const systemInfo = wx.getSystemInfoSync();
					this.statusBarHeight = systemInfo.statusBarHeight;
					this.navigationBarHeight = systemInfo.platform === 'android' ? 48 : 44;
					this.totalBarHeight = this.statusBarHeight + this.navigationBarHeight;
					this.safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0;
				} catch(err) {
					console.error('获取系统信息失败:', err);
				}
			},

			// 初始化H5环境信息
			initH5Info() {
				this.is_h5 = true;
				uni.getSystemInfo({
					success: (res) => {
						this.statusBarHeight = 44;
					}
				});
			},

			// 获取基础数据
			getBaseData() {
				let that = this;
				let id = that.opt.id;
				
				app.get('Apidaihuobiji/detail', {
					id: id,
					pagenum: 1
				}, function(res) {
					console.log('接口返回数据:', res);
					
					// 处理基础数据
					that.handleBaseData(res);
					
					// 延迟加载次要数据
					setTimeout(() => {
						that.loadSecondaryData(res);
					}, 300);
					
					// 关闭加载提示
					uni.hideLoading();
					that.loading = false;
				});
			},

			// 处理基础数据
			handleBaseData(res) {
				if(!res) return;
				
				this.mid = res.mid;
				this.detailInfo = res.detail;
				this.iszan = res.iszan;
				this.totalCommission = res.totalCommission || 0;
				this.commission_desc = res.commission_desc || '元';
				
				// 处理视频和图片数据
				if(this.detailInfo) {
					this.detailInfo.video = this.detailInfo.video == '' ? [] : this.detailInfo.video.split(',');
					this.detailInfo.videoAndImg = [];
					
					// 处理图片
					if (this.detailInfo.pics && this.detailInfo.pics.length > 0) {
						this.detailInfo.pics.forEach(item => {
							this.detailInfo.videoAndImg.push({
								url: item,
								type: 'img'
							});
						});
					}
					
					// 处理视频
					if (this.detailInfo.video.length > 0) {
						this.detailInfo.video.forEach(item => {
							this.detailInfo.videoAndImg.push({
								url: item,
								type: 'video'
							});
						});
					}
				}
			},

			// 加载次要数据
			loadSecondaryData(res) {
				// 加载评论数据
				if(res.datalist) {
					this.datalist = res.datalist;
					this.plcount = res.plcount;
				}
				
				// 加载商品数据
				if(res.detail && res.detail.productids) {
					this.getShopList(res.detail.productids);
				}
				
				// 加载其他信息
				this.myscore = res.myscore;
				this.yindaologo = res.yindaologo;
				this.yindao_link = res.yindao_link;
				this.linkname = res.linkname;
				
				// 更新页码
				this.pagenum = 2;
			},
		},
		// 分享给好友
		onShareAppMessage() {
			const that = this;
			return this._sharewx({
				title: that.detailInfo.title,
				desc: that.detailInfo.content,
				pic: that.detailInfo.pics[0],
				link: app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/daihuobiji/detail/index?scene=id_' + that.detailInfo.id + '-pid_' + app.globalData.mid
			});
		},
		
		// 分享到朋友圈
		onShareTimeline() {
			const that = this;
			const sharewxdata = this._sharewx({
				title: that.detailInfo.title,
				desc: that.detailInfo.content,
				pic: that.detailInfo.pics[0],
				link: app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/daihuobiji/detail/index?scene=id_' + that.detailInfo.id + '-pid_' + app.globalData.mid
			});
			const query = (sharewxdata.path).split('?')[1];
			return {
				title: sharewxdata.title,
				imageUrl: sharewxdata.imageUrl,
				query: query
			}
		},
	}
</script>




<style lang="scss">
.daihuobiji-detail-page {
	width: 100vw;
	min-height: 100vh;
	background-color: #f5f5f5;
	box-sizing: border-box;
	padding-bottom: calc(100rpx + env(safe-area-inset-bottom)); // 确保内容不被底部栏遮挡
	padding-top: 0; // 移除顶部padding

		/* 标题部分的样式 */
		.title-box {
			padding: 20rpx;
			text-align: center;
			background-color: #fff;
			margin-bottom: 20rpx;
		}

		.title-text {
			font-size: 40rpx;
			font-weight: bold;
			color: #333;
		}

		.title-box2 {
			padding: 20rpx;
			text-align: center;
			background-color: #fff;
			margin-bottom: 20rpx;
		}

		.title-text2 {
			font-size: 35rpx;
			font-weight: bold;
			color: #6d6d6d;
		}

		.cover-swipper {
			background-color: #FFFFFF;
			width: 100%;
			/* #ifdef MP-WEIXIN */
			margin-top: 0; // 小程序不需要预留空间，因为已经通过页面整体padding处理
			/* #endif */
			/* #ifndef MP-WEIXIN */
			margin-top: 140rpx; // 非小程序环境下为导航栏预留空间
			/* #endif */

			.swiper {
				width: 100%;
				background-color: #FFFFFF;
			}

		.swiper-item {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 100%;
			/* 继承 Swiper 高度 */
		}

			.cover-img {
				width: 100%;
				height: auto;
			}
		}

		/* 放大图片的遮罩层样式 */
		.image-preview-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.8);
			z-index: 1000;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	.preview-image {
		max-width: 90%;
		max-height: 90%;
	}

	.close-button {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		background-color: rgba(0, 0, 0, 0.6);
		color: #fff;
		font-size: 50rpx;
		border-radius: 30rpx;
		cursor: pointer;
	}


	.info-box {
		background-color: #fff;
		padding: 24rpx;

		.title-text {
			font-size: 40rpx;
			font-weight: bold;
			color: #333;
		}

		// 添加内容文本样式
		.content-text {
			display: block;
			font-size: 28rpx;
			color: #666666;
			line-height: 1.5;
			margin: 20rpx 0;
			white-space: pre-wrap; /* 保持换行示 */
			word-break: break-all;
			word-wrap: break-word;
		}

		.create-time {
			/* font-family: MiSans VF, MiSans VF; */
			font-weight: 400;
			font-size: 22rpx;
			color: #999999;
			text-align: left;
			font-style: normal;
			text-transform: none;
			margin-top: 32rpx;
		}

		.action-buttons {
			display: flex;
			justify-content: flex-end;
			margin-top: 20rpx;
			padding-top: 20rpx;
			border-top: 1px solid #EEEEEE;
			gap: 20rpx;
			
			.action-btn {
				display: flex;
				align-items: center;
				padding: 12rpx 24rpx;
				border-radius: 32rpx;
				
				image {
					width: 28rpx;
					height: 28rpx;
					margin-right: 8rpx;
				}
				
				text {
					font-size: 24rpx;
					line-height: 1;
				}
				
				&.shop, &.team {
					background-color: #F5F5F5;
					min-width: 160rpx;
					justify-content: center;
					
					text {
						color: #FFFFFF;
						font-size: 26rpx;
					}
					
					&:active {
						background-color: #E8E8E8;
					}
					
					image {
						width: 32rpx;
						height: 32rpx;
					}
				}
				
				&.edit {
					background-color: #F5F5F5;
					
					text {
						color: #666666;
					}
					
					&:active {
						background-color: #E8E8E8;
					}
				}
				
				&.delete {
					background-color: #FFF1F0;
					
					text {
						color: #FF4D4F;
					}
					
					&:active {
						background-color: #FFE7E7;
					}
				}
			}
			
			// 第一个action-buttons不显示顶部边框
			&:first-of-type {
				border-top: none;
				padding-top: 0;
			}
		}
	}

	.info-box {
		background-color: #fff;
		padding: 24rpx;

		.desc {
			/* font-family: MiSans VF, MiSans VF; */
			font-weight: 400;
			font-size: 26rpx;
			color: #666666;
			line-height: 30rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.create-time {
			/* font-family: MiSans VF, MiSans VF; */
			font-weight: 400;
			font-size: 22rpx;
			color: #999999;
			text-align: left;
			font-style: normal;
			text-transform: none;
			margin-top: 32rpx;
		}
	}

	.info-box {
		background-color: #fff;
		padding: 24rpx;

		.desc {
			/* font-family: MiSans VF, MiSans VF; */
			font-weight: 400;
			font-size: 26rpx;
			color: #666666;
			line-height: 30rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.create-time {
			/* font-family: MiSans VF, MiSans VF; */
			font-weight: 400;
			font-size: 22rpx;
			color: #999999;
			text-align: left;
			font-style: normal;
			text-transform: none;
			margin-top: 32rpx;
		}
	}

	.comment-box {
		padding: 24rpx;
		background-color: #FFFFFF;
		margin-top: 20rpx;
		box-sizing: border-box;

		.top-tips {
			height: 40rpx;
			font-size: 28rpx;
			color: #333;
			line-height: 40rpx;
			margin-bottom: 24rpx;
			font-weight: 500;
		}

		.comment-input-box {
			height: 72rpx;
			background: #F5F5F5;
			border-radius: 36rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			transition: all 0.3s;
			
			&:active {
				background: #EBEBEB;
			}

			.comment-input {
				width: 100%;
				height: 100%;
				font-size: 26rpx;
				color: #999999;
				padding: 0 32rpx;
				border: none;
				background: transparent;
			}
		}

		.plbox_content {
			padding-top: 30rpx;

			.plcontent {
				vertical-align: middle;
				color: #333;
				word-wrap: break-word;
				word-break: break-all;
				font-size: 26rpx;
				line-height: 1.6;
			}

			.plcontent image {
				width: 44rpx;
				height: 44rpx;
				vertical-align: middle;
				margin: 0 4rpx;
			}

			.item1 {
				width: 100%;
				margin-bottom: 30rpx;
				display: flex;
				flex-direction: row;
				position: relative;
				
				&:last-child {
					margin-bottom: 0;
				}

				.f1 {
					width: 80rpx;
					flex-shrink: 0;
					
					image {
						width: 70rpx;
						height: 70rpx;
						border-radius: 35rpx;
						object-fit: cover;
						background: #f5f5f5;
					}
				}

				.f2 {
					flex: 1;
					display: flex;
					flex-direction: column;
					margin-left: 16rpx;

					.t1 {
						color: #333;
						font-weight: 600;
						font-size: 26rpx;
					}

					.t11 {
						color: #999;
						font-size: 22rpx;
						margin-top: 4rpx;
					}

					.t2 {
						color: #333;
						margin: 12rpx 0;
						line-height: 1.6;
						font-size: 26rpx;
					}

					.t3 {
						color: #999;
						font-size: 24rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-top: 8rpx;
					}

					.pzan {
						display: flex;
						align-items: center;
						padding: 6rpx 12rpx;
						border-radius: 24rpx;
						transition: all 0.3s;
						
						&:active {
							background: #f5f5f5;
						}

						image {
							width: 32rpx;
							height: 32rpx;
							margin-right: 8rpx;
						}
					}

					.phuifu {
						color: #507DAF;
						font-size: 24rpx;
						padding: 6rpx 12rpx;
						border-radius: 24rpx;
						transition: all 0.3s;
						
						&:active {
							background: #f5f5f5;
						}
					}
				}
			}

			.relist {
				width: 100%;
				background: #F8F8F8;
				border-radius: 12rpx;
				padding: 16rpx 24rpx;
				margin: 16rpx 0;
				box-sizing: border-box;

				.item2 {
					font-size: 26rpx;
					margin-bottom: 16rpx;
					display: flex;
					flex-direction: column;
					
					&:last-child {
						margin-bottom: 0;
					}

					.f1 {
						font-weight: 600;
						color: #333;
						width: 100%;
						display: flex;
						align-items: center;
						font-size: 24rpx;

						.t1 {
							font-weight: normal;
							color: #999;
							font-size: 22rpx;
							margin-left: 16rpx;
						}
					}
					
					.f2 {
						margin-top: 8rpx;
						color: #666;
						font-size: 24rpx;
						line-height: 1.6;
					}
				}
			}
		}
	}

	.footer-box {
		position: fixed;
		bottom: 0;
		right: 0;
		left: 0;
		height: 100rpx;
		background: #FFFFFF;
		box-shadow: inset 0rpx 1rpx 0rpx 0rpx #EAEAEA;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center; // 添加垂直居中对齐
		padding: 0 24rpx; // 移除上下padding，只保留左右
		box-sizing: border-box;
		z-index: 98;
		
		// #ifdef H5
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		height: calc(100rpx + constant(safe-area-inset-bottom));
		height: calc(100rpx + env(safe-area-inset-bottom));
		// #endif
		
		// #ifdef MP-WEIXIN
		height: calc(100rpx + env(safe-area-inset-bottom));
		padding-bottom: env(safe-area-inset-bottom);
		// #endif

		.buy {
			height: 80rpx; // 调整高度以适应容器
			width: 280rpx;
			background: #F5F5F5;
			border-radius: 50rpx;
			padding: 0rpx 24rpx;
			display: flex;
			align-items: center;

			.good-img {
				width: 52rpx;
				height: 52rpx;
				margin-right: 7rpx;
				flex-shrink: 0;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 100%;
					height: 100%;
					border-radius: 50px;
					object-fit: cover;
				}
			}

			.buy-text {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: flex-start;
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				line-height: 28rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;

				.text-tips {
					margin-bottom: 2rpx;
				}
			}
		}

		.footer-actions {
			height: 80rpx; // 调整高度以适应容器
			width: calc(100% - 300rpx);
			display: flex;
			flex-direction: row;

			.action-item {
				display: flex;
				flex: 1;
				align-items: center;
				justify-content: center;
				flex-direction: column;

				.action-item-img {
					width: 44rpx;
					height: 44rpx;
					margin-bottom: 4rpx;
				}

				.action-item-label {
					font-family: MiSans VF, MiSans VF;
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
					line-height: 28rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}

	.covermy {
		position: fixed;
		z-index: 99999;
		bottom: 0;
		right: 0;
		width: 130rpx;
		height: 130rpx;
		box-sizing: content-box;
	}

	.covermy image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.info-box {
	.commission-box {
		margin: 24rpx 0;
		padding: 20rpx 24rpx;
		background: linear-gradient(to right, #FFF5F5, #FFF8F8);
		border-radius: 16rpx;
		border: 1px solid rgba(229, 77, 66, 0.1);
	}

	.commission-content {
		display: flex;
		align-items: center;
		position: relative;
	}

	.commission-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 16rpx;
	}

	.commission-info {
		display: flex;
		align-items: baseline;
	}

	.commission-text {
		font-size: 26rpx;
		color: #666;
		margin-right: 12rpx;
	}

	.commission-amount {
		color: #E54D42;
		font-size: 36rpx;
		font-weight: bold;
		font-family: DIN;
	}

	.commission-unit {
		color: #E54D42;
		font-size: 24rpx;
		margin-left: 4rpx;
	}
	
	.commission-tips {
		position: absolute;
		right: 0;
		font-size: 22rpx;
		color: #999;
		background: rgba(229, 77, 66, 0.05);
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
	}
}

/* #ifdef H5 */
.cover-swipper {
	position: relative;
	z-index: 1; // 确保不会被导航栏遮挡
}
/* #endif */

::v-deep .detail-nav {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
	height: 88rpx; // 固定导航栏高度
	display: flex;
	align-items: center;
	padding: 0 24rpx;
	box-sizing: border-box;
}
</style>

<style>
	

	
	.viewShop {
		height: 60vh;
		width: 100%;
		border-radius: 20rpx 20rpx 0 0;
		background-color: #fff;
		padding-top: 20rpx;
		z-index: 99999 !important;
	}

	/* #ifdef H5 */
	.viewShop {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
	/* #endif */

	/* #ifdef MP-WEIXIN */
	.viewShop {
		padding-bottom: env(safe-area-inset-bottom);
	}
	/* #endif */

	.cart-container {
		display: flex;
		flex-direction: column;
		height: calc(100% - 120rpx); /* 减去头部高度 */
		overflow-y: auto;
	}

	.cart-item {
		display: flex;
		align-items: center;
		padding: 10px;
	}

	.item-selector {
		margin-right: 10px;
	}

	.image-box {
		background-color: #f4f4f4;
		border-radius: 10rpx;
		width: 130rpx;
		height: 130rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.item-image {
		height: 70%;
	}

	.item-info {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 130rpx;
		width: 67%;
	}

	.item-name {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}

	.item-price {
		font-size: 14px;
		color: #e65602;
	}

	.shopButton {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #a0a0a0;
		background-color: #ddd;
		font-size: 20rpx;
	}

	.shopButtonActive {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #fff;
		background-color: #eb8200;
		font-size: 20rpx;
	}
</style>

<style>
	/* 规格选择弹窗样式 */
	.spec-dialog {
		width: 100%;
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
	}

	::v-deep .uni-popup .uni-popup__wrapper {
		z-index: 999999 !important;
	}

	::v-deep .uni-popup.uni-popup-bottom {
		z-index: 999999 !important;
	}

	/* 商品列表弹窗样式 */
	.viewShop {
		height: 60vh;
		width: 100%;
		border-radius: 20rpx 20rpx 0 0;
		background-color: #fff;
		padding-top: 20rpx;
		position: relative;
		z-index: 99999;
	}

	/* 确保规格选择弹窗在最上层 */
	::v-deep .buydialog {
		position: relative;
		z-index: 999999;
	}
</style>




<style>
	/* 规格选择弹窗样式 */
	::v-deep .uni-popup {
		&.spec-popup {
			z-index: 999999 !important;
		}
	}

	::v-deep .uni-popup__wrapper {
		z-index: 999999 !important;
	}

	::v-deep .uni-popup__mask {
		z-index: 999998 !important;
	}

	.spec-dialog {
		width: 100%;
		background: transparent;
		position: relative;
		z-index: 999999;
	}

	/* 商品列表弹窗样式 */
	.viewShop {
		height: 60vh;
		width: 100%;
		border-radius: 20rpx 20rpx 0 0;
		background-color: #fff;
		padding-top: 20rpx;
	}

	/* ... 其他样式保持不变 ... */

