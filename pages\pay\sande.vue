<template>
	<view class="container">
	<view>
		<title>一键快捷</title>
	</view>

		<form @submit="formSubmit" @reset="formReset" report-submit="true">
			<!-- <view class="form-content">
				<view class="form-item">
					<text class="label">转账汇款</text>
				</view>
			</view> -->
			<view class="orderinfo">
				<view class="item">
					<text class="t1">charset</text>
					<text class="t2" user-select="true" selectable="true"></text>
				</view>
				
				<view class="item">
					<text class="t1">data</text>
					<text class="t2 red">¥</text>
				</view>
				<view class="item">
					<text class="t1">signType</text>
					<text class="t2"></text>
				</view>
				<view class="item">
					<text class="t1">sign</text>
					<text class="t2"></text>
				</view>
		
			</view>
	
			<button class="btn" form-type="submit" :style="{background:t('color1')}">确定</button>
			<view style="padding-top:30rpx"></view>
		</form>
	

	</view>
</template>

<script>
var app = getApp();
	export default {
		data() {
			return {
				opt:{},
				loading:false,
				isload: false,
				pre_url:app.globalData.pre_url,
				
				pics:[],
				detail:{},
				orderDetail:{},
				pay_transfer_info:{}
			}
		},
		onLoad: function (opt) {
			this.opt = app.getopts(opt);
			this.getdata();
		},
		onPullDownRefresh: function () {
			this.getdata();
		},
		methods: {
			getdata: function () {
				var that = this;
				that.loading = true;
				app.get('ApiPay/transfer', {id: that.opt.id}, function (res) {
					that.loading = false;
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					that.detail = res.detail;
					
					that.loaded();
					//
				});
			},
    formSubmit: function (e) {
      var that = this;
      var id = that.opt.id;
			alert(123)
			return;

			app.showLoading('提交中');
      app.post('ApiPay/transfer', {id: id}, function (res) {
				app.showLoading(false);
        app.alert(res.msg);
        if (res.status == 1) {
         setTimeout(function () {
           // app.goto('/pages/order/detail?id='+that.detail.orderid);
					 app.goto('/pages/my/usercenter');
         }, 1000);
        }
      });
    },
		uploadimg:function(e){
			var that = this;
			var field= e.currentTarget.dataset.field
			var pics = that[field]
			if(!pics) pics = [];
			app.chooseImage(function(urls){
				for(var i=0;i<urls.length;i++){
					pics.push(urls[i]);
				}
			},1)
		},
		removeimg:function(e){
			var that = this;
			var index= e.currentTarget.dataset.index
			var field= e.currentTarget.dataset.field
			var pics = that[field]
			pics.splice(index,1)
		},
		}
	}
</script>

<style>
.btn-a { text-align: center; padding: 30rpx; color: rgb(253, 74, 70);}
.text-min { font-size: 24rpx; color: #999;}
.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;flex-shrink:0}
.orderinfo .item .t2{flex:1;text-align:right}
.orderinfo .item .red{color:red}
.orderinfo .item .grey{color:grey}

.form-item4{width:100%;background: #fff; padding: 20rpx 20rpx;margin-top:1px}
.form-item4 .label{ width:150rpx;}
.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.layui-imgbox-close image{width:100%;height:100%}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}
	

.form-content{width:94%;margin:16rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff;overflow:hidden}
.form-item{ width:100%;padding: 32rpx 20rpx;}
.form-item .label{ width:100%;height:60rpx;line-height:60rpx}
.form-item .input-item{ width:100%;}
.form-item textarea{ width:100%;height:200rpx;border: 1px #eee solid;padding: 20rpx;}
.form-item input{ width:100%;border: 1px #f5f5f5 solid;padding: 10rpx;height:80rpx}
.form-item .mid{ height:80rpx;line-height:80rpx;padding:0 20rpx;}
.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:50rpx;color: #fff;font-size: 30rpx;font-weight:bold}
</style>
