<template>
<view class="container">
	<block v-if="isload">
		
		<view class="ind_business">
			<view class="ind_buslist" id="datalist">
				
				<view class="content">
					<view class="label">
						<!-- <text class="t1">成员信息 (有效人数 {{userinfo.teamnum || 0}} 人)</text> -->
						<text class="t1">用户推广统计 </text>
						<text class="t2"></text>
					</view>
					<block v-if="showtype == 0">
					<!-- <scroll-view scroll-x="true" style="width: 510rpx;"> -->
					<view class="prolist" style="margin-top: -10px;background-color: white;">
						<view class="product2">
							<view class="f2 hah">{{member.day}}</view>
							<view class="f2">今日新增</view>
						</view>
					
						<view class="product2">
							<view class="f2 hah">{{member.zuo}}</view>
							<view class="f2">昨日新增</view>
						</view>
						<view class="product2">
							<view class="f2 hah">{{member.month}}</view>
							<view class="f2">本月新增</view>
						</view>
						<view class="product2">
							<view class="f2 hah">{{member.zongji}}</view>
							<view class="f2">总计</view>
						</view>
					</view>
				<view class="prolist" style="margin-top: -10px;background-color: white;">
					<view class="product2">
						<view class="f2 hah">￥{{memberxiaoliang.day}}</view>
						<view class="f2">今日销量</view>
					</view>
				
					<view class="product2">
						<view class="f2 hah">￥{{memberxiaoliang.zuo}}</view>
						<view class="f2">昨日销量</view>
					</view>
					<view class="product2">
						<view class="f2 hah">￥{{memberxiaoliang.month}}</view>
						<view class="f2">本月销量</view>
					</view>
					<view class="product2">
						<view class="f2 hah">￥{{memberxiaoliang.zongji}}</view>
						<view class="f2">总销量</view>
					</view>
				</view>
					</block>
				</view>
				<view class="content">
					<view class="label">
						<!-- <text class="t1">成员信息 (有效人数 {{userinfo.teamnum || 0}} 人)</text> -->
						<text class="t1">商户推广统计 </text>
						<text class="t2"></text>
					</view>
					<block v-if="showtype == 0">
					<!-- <scroll-view scroll-x="true" style="width: 510rpx;"> -->
					<view class="prolist" style="margin-top: -10px;background-color: white;">
						<view class="product2">
									<view class="f2 hah">{{business.day}}</view>
									<view class="f2">今日新增</view>
								</view>
							
								<view class="product2">
									<view class="f2 hah">{{business.zuo}}</view>
									<view class="f2">昨日新增</view>
								</view>
								<view class="product2">
									<view class="f2 hah">{{business.month}}</view>
									<view class="f2">本月新增</view>
								</view>
								<view class="product2">
									<view class="f2 hah">{{business.zongji}}</view>
									<view class="f2">总计</view>
								</view>
							</view>
						<view class="prolist" style="margin-top: -10px;background-color: white;">
							<view class="product2">
								<view class="f2 hah">￥{{businessxiaoliang.day}}</view>
								<view class="f2">今日销量</view>
							</view>
						
							<view class="product2">
								<view class="f2 hah">￥{{businessxiaoliang.zuo}}</view>
								<view class="f2">昨日销量</view>
							</view>
							<view class="product2">
								<view class="f2 hah">￥{{businessxiaoliang.month}}</view>
								<view class="f2">本月销量</view>
							</view>
							<view class="product2">
								<view class="f2 hah">￥{{businessxiaoliang.zongji}}</view>
								<view class="f2">总销量</view>
							</view>
				</view>
					</block>
				</view>
				<nomore v-if="nomore"></nomore>
				<nodata v-if="nodata"></nodata>
			</view>
		</view>
		<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange" :menuindex="menuindex"></buydialog>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			pre_url:app.globalData.pre_url,
      field: 'juli',
			order:'asc',
      oldcid: "",
      catchecid: "",
      longitude: '',
      latitude: '',
			clist:[],
      datalist: [],
      pagenum: 1,
      keyword: '',
      cid: '',
      nomore: false,
      nodata: false,
      types: "",
      showfilter: "",
			showtype:0,
			buydialogShow:false,
			proid:0,
			member:{},
			memberxiaoliang:{},
			business:{},
			businessxiaoliang:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.oldcid = this.opt.cid;
		this.catchecid = this.opt.cid;
		this.cid = this.opt.cid;
        if(this.opt.keyword) {
        	this.keyword = this.opt.keyword;
        }
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getDataList(true);
    }
  },
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiYihuo/mydata', {}, function (res) {
				that.loading = false;
				that.clist = res.clist;
				that.member= res.member;
				that.memberxiaoliang = res.memberxiaoliang;
				that.business = res.business;
				that.businessxiaoliang = res.businessxiaoliang;
				that.showtype = res.showtype || 0;
				that.loaded();
			});
			app.getLocation(function (res) {
				var latitude = res.latitude;
				var longitude = res.longitude;
				that.longitude = longitude;
				that.latitude = latitude;
				that.getDataList();
			},
			function () {
				that.getDataList();
			});
		},
    getDataList: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var latitude = that.latitude;
      var longitude = that.longitude;
      var keyword = that.keyword;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
      app.post('ApiBusiness/mylist', {pagenum: pagenum,cid: that.cid,field: that.field,order: that.order,longitude: longitude,latitude: latitude,keyword: keyword}, function (res) {
        that.loading = false;
				uni.stopPullDownRefresh();
	    if(res.status == 0)
		{
			app.alert(res.msg, function () {
				app.goto('/pages/my/usercenter', 'redirect');
			})
			return;
		}
        var data = res.data;
        if (pagenum == 1) {
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
		// 打开窗口
		showDrawer(e) {
			console.log(e)
			this.$refs[e].open()
		},
		// 关闭窗口
		closeDrawer(e) {
			this.$refs[e].close()
		},
		// 抽屉状态发生变化触发
		change(e, type) {
			console.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));
			this[type] = e
		},
    cateClick: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
      that.catchecid = cid
    },
		filterConfirm(){
			this.cid = this.catchecid;
			this.gid = this.catchegid;
			this.getDataList();
			this.$refs['showRight'].close()
		},
		filterReset(){
			this.catchecid = this.oldcid;
			this.catchegid = '';
		},
    filterClick: function () {
      this.showfilter = !this.showfilter
    },
    changetab: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
      that.cid = cid
      that.pagenum = 1;
      that.datalist = [];
      that.getDataList();
    },
    search: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
			that.pagenum = 1;
      that.datalist = [];
      that.getDataList();
    },
    sortClick: function (e) {
      var that = this;
      var t = e.currentTarget.dataset;
      that.field = t.field;
      that.order = t.order;
      that.getDataList();
    },
    filterClick: function (e) {
      var that = this;
      var types = e.currentTarget.dataset.types;
      that.types = types;
    },
		openLocation:function(e){
			//console.log(e)
			var latitude = parseFloat(e.currentTarget.dataset.latitude)
			var longitude = parseFloat(e.currentTarget.dataset.longitude)
			var address = e.currentTarget.dataset.address
			uni.openLocation({
			 latitude:latitude,
			 longitude:longitude,
			 name:address,
			 scale: 13
		 })		
		},
		phone:function(e) {
			var phone = e.currentTarget.dataset.phone;
			uni.makePhoneCall({
				phoneNumber: phone,
				fail: function () {
				}
			});
		},
		buydialogChange: function (e) {
			if(!this.buydialogShow){
				this.proid = e.currentTarget.dataset.proid
			}
			this.buydialogShow = !this.buydialogShow;
			console.log(this.buydialogShow);
		},
  }
};
</script>
<style>

.content{width:94%;margin:0 3%;border-radius:16rpx;background: #fff;margin-top: 20rpx;}
.content .label{display:flex;width: 100%;padding: 16rpx;color: #333;}
.content .label .t1{flex:1}
.content .label .t2{ width:300rpx;text-align:right}

.content .item{width: 100%;padding: 32rpx;border-top: 1px #eaeaea solid;min-height: 112rpx;display:flex;align-items:center;}
.content .item image{width: 90rpx;height: 90rpx;border-radius:4px}
.content .item .f1{display:flex;flex:1;align-items:center;}
.content .item .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}
.content .item .f1 .t2 .x1{color: #333;font-size:26rpx;}
.content .item .f1 .t2 .x2{color: #999;font-size:24rpx;}

.content .item .f2{display:flex;flex-direction:column;width:200rpx;text-align:right;border-left:1px solid #eee}
.content .item .f2 .t1{ font-size: 40rpx;color: #666;height: 40rpx;line-height: 40rpx;}
.content .item .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}
.content .item .f2 .t3{ display:flex;justify-content:space-around;margin-top:10rpx; flex-wrap: wrap;}
.content .item .f2 .t3 .x1{height:40rpx;line-height:40rpx;padding:0 8rpx;border:1px solid #ccc;border-radius:6rpx;font-size:22rpx;color:#666;margin-top: 10rpx;}

.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}
.topsearch{width:100%;padding:16rpx 20rpx;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.topsearch .search-btn{display:flex;align-items:center;color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}
.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0}
.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:28rpx;font-weight:bold;color:#323232}
.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}

.filter-scroll-view{margin-top:var(--window-top)}
.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}
.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}
.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:60rpx 0 30rpx 20rpx;}
.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}
.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}
.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}
.search-filter button .icon{margin-top:6rpx;height:54rpx;}
.search-filter-btn{display:flex;padding:30rpx 30rpx;justify-content: space-between}
.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}
.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}

.ind_business {width: 100%;margin-top: 190rpx;font-size:26rpx;padding:0 24rpx}
.ind_business .ind_busbox{ width:100%;background: #fff;padding:20rpx;overflow: hidden; margin-bottom:20rpx;border-radius:8rpx;position:relative}
.ind_business .ind_buspic{ width:120rpx;height:120rpx; margin-right: 28rpx; }
.ind_business .ind_buspic image{ width: 100%;height:100%;border-radius: 8rpx;object-fit: cover;}
.ind_business .bus_title{ font-size: 30rpx; color: #222;font-weight:bold;line-height:46rpx}
.ind_business .bus_score{font-size: 24rpx;color:#FC5648;display:flex;align-items:center}
.ind_business .bus_score .img{width:24rpx;height:24rpx;margin-right:10rpx}
.ind_business .bus_score .txt{margin-left:20rpx}
.ind_business .indsale_box{ display: flex}
.ind_business .bus_sales{ font-size: 24rpx; color:#999;position:absolute;top:20rpx;right:28rpx}

.ind_business .bus_address{color:#999;font-size: 22rpx;height:36rpx;line-height: 36rpx;margin-top:6rpx;display:flex;align-items:center;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.ind_business .bus_address .x2{padding-left:20rpx}
.ind_business .prolist{white-space: nowrap;margin-top:16rpx; margin-bottom: 10rpx;}
.ind_business .prolist .product{width:158rpx;height:100rpx;overflow:hidden;display:inline-flex;flex-direction:column;align-items:center;margin-right:24rpx}
.ind_business .prolist .product .f1{width:158rpx;height:100rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist .product .f2{font-size:28rpx;color:#666;font-weight:bold;margin-top:4rpx}
.ind_business .prolist .product .hah{color:#fe2b2e;}
.ind_business .prolist2{margin-top:16rpx; margin-bottom: 10rpx;}
.ind_business .prolist2 .product{width:158rpx;overflow:hidden;display:inline-flex;flex-direction:column;margin-right:10rpx;position:relative;min-height:200rpx;padding-bottom:20rpx}
.ind_business .prolist2 .product .f1{width:118rpx;height:118rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist2 .product .f2{font-size:28rpx;color:#FC5648;font-weight:bold;margin-top:4rpx;}
.ind_business .prolist2 .product .f3{font-size:22rpx;font-weight:normal;color: #aaa;text-decoration: line-through;}
.ind_business .prolist2 .product .f4{font-size:20rpx;font-weight:normal;color: #888;}

.ind_business .prolist2 .product .f5{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;top:140rpx;right:0rpx;text-align:center;}
.ind_business .prolist2 .product .f5 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}
.ind_business .prolist2 .product .f5 .img{width:100%;height:100%}
.ind_business .prolist .product2{width:22%;height:100rpx;overflow:hidden;display:inline-flex;flex-direction:column;align-items:center;margin-right:24rpx}
.ind_business .prolist .product2 .f1{width:158rpx;height:100rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist .product2 .f2{font-size:28rpx;color:#666;margin-top:4rpx}
.ind_business .prolist .product2 .hah{color:#fe2b2e;}
.ind_business .prolist2{margin-top:16rpx; margin-bottom: 10rpx;}
.ind_business .prolist2 .product{width:158rpx;overflow:hidden;display:inline-flex;flex-direction:column;margin-right:10rpx;position:relative;min-height:200rpx;padding-bottom:20rpx}
.ind_business .prolist2 .product .f1{width:118rpx;height:118rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist2 .product .f2{font-size:28rpx;color:#FC5648;font-weight:bold;margin-top:4rpx;}
.ind_business .prolist2 .product .f3{font-size:22rpx;font-weight:normal;color: #aaa;text-decoration: line-through;}
.ind_business .prolist2 .product .f4{font-size:20rpx;font-weight:normal;color: #888;}

.ind_business .prolist2 .product .f5{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;top:140rpx;right:0rpx;text-align:center;}
.ind_business .prolist2 .product .f5 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}
.ind_business .prolist2 .product .f5 .img{width:100%;height:100%}


</style>