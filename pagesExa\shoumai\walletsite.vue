<template>
    <view class="container">
        <block v-if="isload">
            <form @submit="formSubmit" @reset="formReset">
                <view class="auth">
                   <view class="text">
                        <text>設置說明:<br>1、請正確填寫錢包地址和上傳收款碼，這將用於收款和確認身份。<br>2、支持多種收款通道，請選擇您常用的收款方式。<br>3、保持收款碼清晰完整，確保買家能夠順利支付。<br>4、設置交易密碼用於保護您的資產安全，請妥善保管。</text>
                    </view>
                    <view class="infos">
                    	<view class="list">
    						<text>支付通道</text>
    						<picker  class="picker" mode="selector" name="corridor" :value="corridorIndex" :range="corridorList" range-key="name" @change="corridorChange">
    							<view style="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;">{{corridorItem.name ? corridorItem.name : '請選擇通道'}}</view>
    						</picker>
                            <text class="tips">選擇您常用的收款通道，不同通道可能有不同的手續費</text>
	    				</view>
                        <view class="list">
                            <text>錢包地址</text>
                            <input placeholder="請輸入收款錢包地址" placeholderStyle="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;" border="surround" name="wallet_address" v-model="wallet_address"></input>
                            <text class="tips">收款地址將顯示給買家，請確保地址正確</text>
                        </view>
                        <view class="list">
                            <text>收款二維碼</text>
                            <view class="upload" @click="upIdcardHead">
                                <image :src="receiving_code"></image>
                                <text>點擊上傳收款碼，確保清晰完整</text>
                            </view>
                            <text class="tips">買家將通過此收款碼向您支付</text>
                        </view>
                        <view class="list">
                            <text>交易密碼</text>
                            <input type="password" placeholder="請輸入交易密碼（至少6位）" placeholderStyle="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;" border="surround" name="pypwd" v-model="pypwd"></input>
                            <text class="tips">用於保護您的交易安全，請設置復雜密碼</text>
                        </view>
                        <view class="button" @click="formSubmit">
                            <text>提交保存</text>
                        </view>
                        <view class="info-text">
                            <text>提醒：設置錢包信息後，您才能進行資產售賣和購買操作。</text>
                        </view>
                    </view>
                </view>
            </form>
        </block>
        <loading v-if="loading"></loading>
        <dp-tabbar :opt="opt"></dp-tabbar>
        <popmsg ref="popmsg"></popmsg>
    </view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				corridorIndex: 0,
				corridorList: [],
				corridorItem: {},
				wallet_address: '',
				pypwd: '',
				receiving_code: ''
			};
		},
		onLoad: function(opt) {
			this.isload = true;
			const _that = this
			this.loading = true;
			app.get('ApiShoumai/setWalletInfo', {}, function(data) {
				_that.loading = false;
				if(data.status == 2){
					app.error(data.msg);
					setTimeout(function() {
						app.goto(data.to_url);
					}, 1000);
					return;
				}

				_that.corridorList = data.data.corridorList || [];
				if(data.data.corridor > 0){
					_that.corridorItem = _that.corridorList.find(item => item.id === data.data.corridor) || {};
					_that.corridorIndex = _that.corridorList.findIndex(item => item.id === data.data.corridor);
				}

				if(data.data.wallet_address != '')
					_that.wallet_address = data.data.wallet_address;

				if(data.data.wallet_receiving_code == ''){
					_that.receiving_code = '/static/img/alipay.jpg';
				} else {
					_that.receiving_code = data.data.wallet_receiving_code;
				}
				
				uni.setNavigationBarTitle({
					title: '設置錢包信息'
				});
			});
		},
		onPullDownRefresh: function() {
			this.onLoad(this.opt);
			uni.stopPullDownRefresh();
		},
		methods: {
			corridorChange(e){
				this.corridorIndex = e.target.value;
				this.corridorItem = this.corridorList[e.target.value] || {};
			},
			upIdcardHead(){
				const _that = this;
				app.chooseImage(function(data){
					if(data && data.length > 0) {
						_that.receiving_code = data[0];
					}
				}, 1);
			},
			formSubmit: function() {
				const _that = this;

				var wallet_address = _that.wallet_address.trim();
				var pypwd = _that.pypwd;
				var receiving_code = _that.receiving_code;

				// 验证通道选择
				if(!_that.corridorItem || !_that.corridorItem.id){
					app.alert('請選擇支付通道');
					return;
				}

				// 验证交易密码
				if (pypwd == '') {
					app.alert('請輸入交易密碼');
					return;
				}

				if(pypwd.length < 6){
					app.alert('交易密碼最少為6位');
					return;
				}
				
				// 验证钱包地址
				if (wallet_address == '') {
					app.alert('請輸入錢包地址');
					return;
				}
				
				// 验证收款码
				if(receiving_code == '/static/img/alipay.jpg'){
					app.alert('請上傳收款二維碼');
					return;
				}
				
				app.showLoading('提交中');
				app.post('ApiShoumai/setWalletInfo', {
					corridor: _that.corridorItem.id,
					wallet_address: wallet_address,
					receiving_code: receiving_code,
					pypwd: pypwd
				}, function(data) {
					app.showLoading(false);
					if (data.status == 1) {
						app.success(data.msg || '設置成功');
						setTimeout(function() {
							app.goto("/pagesExa/shoumai/index");
						}, 1500);
					} else {
						app.error(data.msg || '設置失敗');
					}
				});
			}
		}
	};
</script>
<style lang="scss" scoped>
	.picker{
		height: 80rpx;
		background: #F5F5FB;
		border-radius: 10rpx;
		padding-left: 32rpx;
	}
	.auth{
		height: 100%;
		background-color: #FFFFFF;
		& text{
			font-family: PingFangSC-Regular, PingFang SC;
			color: #7C7597;
		}
		.text{
			line-height: 35px;
			padding: 20px 30px;
			height: auto;
			display: block;
			background: #f9f9fe;
			margin: 20rpx 30rpx;
			border-radius: 10rpx;
			border-left: 4px solid #533CD7;
		}
		.infos{
			.list{
				display: flex;
				flex-direction: column;
				padding: 40rpx 50rpx 0;
				>text{
					font-size: 28rpx;
					font-weight: 600;
					color: #160651;
					line-height: 40rpx;
					margin-bottom: 14rpx;
				}
				>input{
					height: 80rpx;
					background: #F5F5FB;
					border-radius: 10rpx;
					padding-left: 32rpx;
				}
				.tips {
					font-size: 24rpx;
					color: #999;
					margin-top: 10rpx;
					font-weight: normal;
				}
				.upload{
					display: flex;
					flex-direction: column;
					align-items: center;
					background: #F5F5FB;
					border-radius: 10rpx;
					padding: 40rpx 40rpx 20rpx 40rpx;
					& image{
						width: 248rpx;
						height: 334rpx;
						border-radius: 8rpx;
					}
					>text{
						margin-top: 20rpx;
						color: #999;
						font-size: 24rpx;
					}
				}
			}
		}
		.button{
			display: flex;
			padding: 0 82rpx 40rpx 82rpx;
			margin-top: 60rpx;
			>text{
				width: 100%;
				height: 84rpx;
				background: #533CD7;
				box-shadow: 0rpx 6rpx 30rpx 0rpx rgba(83,60,215,0.4600);
				border-radius: 43rpx;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 84rpx;
				text-align: center;
			}
		}
		.info-text {
			text-align: center;
			padding: 0 50rpx 50rpx 50rpx;
			color: #999;
			font-size: 26rpx;
		}
	}
	
	.form {
		width: 94%;
		margin: 20rpx 3%;
		border-radius: 5px;
		padding: 20rpx 20rpx;
		padding: 0 3%;
		background: #FFF;
	}

	.form-item {
		display: flex;
		align-items: center;
		width: 100%;
		border-bottom: 1px #ededed solid;
		height: 98rpx;
		line-height: 98rpx;
	}

	.form-item:last-child {
		border: 0
	}

	.form-item .label {
		color: #000;
		width: 200rpx;
	}

	.form-item .input {
		flex: 1;
		color: #000;
	}

	.set-btn {
		width: 90%;
		margin: 60rpx 5%;
		height: 96rpx;
		line-height: 96rpx;
		border-radius: 48rpx;
		color: #FFFFFF;
		font-weight: bold;
	}
</style>