<template>
<view>
	<block v-for="(setData, index) in pagecontent" :key="index">
		<block v-if="setData.temp=='notice'">
			<dp-notice :params="setData.params" :data="setData.data"></dp-notice>
		</block>
		<block v-if="setData.temp=='banner'">
			<dp-banner :params="setData.params" :data="setData.data"></dp-banner> 
		</block>
		<block v-if="setData.temp=='search'">
			<dp-search :params="setData.params" :data="setData.data"></dp-search>
		</block>
		<block v-if="setData.temp=='lbssearch'">
			<dp-lbs-search :params="setData.params" :data="setData.data"></dp-lbs-search>
		</block>
		<block v-if="setData.temp=='text'">
			<dp-text :params="setData.params" :data="setData.data"></dp-text>
		</block>
		<block v-if="setData.temp=='title'">
			<dp-title :params="setData.params" :data="setData.data"></dp-title>
		</block>
		<block v-if="setData.temp=='dhlist'">
			<dp-dhlist :params="setData.params" :data="setData.data"></dp-dhlist>
		</block>
		<block v-if="setData.temp=='line'">
			<dp-line :params="setData.params" :data="setData.data"></dp-line>
		</block>
		<block v-if="setData.temp=='blank'">
			<dp-blank :params="setData.params" :data="setData.data"></dp-blank>
		</block>
		<block v-if="setData.temp=='menu'">
			<dp-menu :params="setData.params" :data="setData.data"></dp-menu> 
		</block>
		<block v-if="setData.temp=='map'">
			<dp-map :params="setData.params" :data="setData.data"></dp-map> 
		</block>
		<block v-if="setData.temp=='cube'">
			<dp-cube :params="setData.params" :data="setData.data"></dp-cube> 
		</block>
		<block v-if="setData.temp=='picture'">
			<dp-picture :params="setData.params" :data="setData.data"></dp-picture> 
		</block>
		<block v-if="setData.temp=='pictures'"> 
			<dp-pictures :params="setData.params" :data="setData.data"></dp-pictures> 
		</block>
		<block v-if="setData.temp=='video'">
			<dp-video :params="setData.params" :data="setData.data"></dp-video> 
		</block>
		<block v-if="setData.temp=='tab'">
			<dp-tab :params="setData.params" :data="setData.data" :tabid="setData.id" :menuindex="menuindex" @getdata="getdata($event, setData)"></dp-tab> 
		</block>
		<block v-if="setData.temp=='shop'">
			<dp-shop :params="setData.params" :data="setData.data" :shopinfo="setData.shopinfo"></dp-shop> 
		</block>
		<block v-if="setData.temp=='product'">
			<dp-product :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-product> 
		</block>
		<block v-if="setData.temp=='collage'">
			<dp-collage :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-collage> 
		</block>
		<block v-if="setData.temp=='luckycollage'">
			<dp-luckycollage :params="setData.params" :data="setData.data"></dp-luckycollage> 
		</block>

		<block v-if="setData.temp=='kanjia'">
			<dp-kanjia :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-kanjia> 
		</block>
		<block v-if="setData.temp=='yuyue'">
			<dp-yuyue :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-yuyue>
		</block>
		<block v-if="setData.temp=='seckill'">
			<dp-seckill :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-seckill> 
		</block>
		<block v-if="setData.temp=='scoreshop'">
			<dp-scoreshop :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-scoreshop> 
		</block>
		<block v-if="setData.temp=='tuangou'">
			<dp-tuangou :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-tuangou> 
		</block>
		<block v-if="setData.temp=='kecheng'">
			<dp-kecheng :params="setData.params" :data="setData.data" :menuindex="menuindex" :sysset="setData.sysset" @getdata="getdata($event, setData)" :componentId="setData.id"></dp-kecheng> 
		</block>
		<block v-if="setData.temp=='exam'">
					<dp-exam :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-exam> 
				</block>
		<block v-if="setData.temp=='restaurant_product'">
			<dp-restaurant-product :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-restaurant-product> 
		</block>
		<block v-if="setData.temp=='coupon'">
			<dp-coupon :params="setData.params" :data="setData.data"></dp-coupon> 
		</block>
		<block v-if="setData.temp=='article'">
			<dp-article :params="setData.params" :data="setData.data"></dp-article>
		</block>
		<block v-if="setData.temp=='holiday'">
			<dp-holiday :params="setData.params" :data="setData.data"></dp-holiday>
		</block>
		<block v-if="setData.temp=='business'">
			<dp-business :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-business> 
		</block>
		<block v-if="setData.temp=='venues'">
			<dp-venues :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-venues> 
		</block>
		<block v-if="setData.temp=='shortvideo'">
			<dp-shortvideo :params="setData.params" :data="setData.data"></dp-shortvideo> 
		</block>
		<block v-if="setData.temp=='liveroom'">
			<dp-liveroom :params="setData.params" :data="setData.data"></dp-liveroom> 
		</block>
		<block v-if="setData.temp=='button'">
			<dp-button :params="setData.params" :data="setData.data"></dp-button> 
		</block>
		<block v-if="setData.temp=='hotspot'">
			<dp-hotspot :params="setData.params" :data="setData.data"></dp-hotspot> 
		</block>
		<block v-if="setData.temp=='cover'">
			<dp-cover :params="setData.params" :data="setData.data"></dp-cover> 
		</block>
		<block v-if="setData.temp=='richtext'">
			<dp-richtext :params="setData.params" :data="setData.data" :content="setData.content"></dp-richtext> 
		</block>
		<block v-if="setData.temp=='form'">
			<dp-form :params="setData.params" :data="setData.data" :content="setData.content" :latitude="latitude" :longitude="longitude"></dp-form> 
		</block>
		<block v-if="setData.temp=='form-log'">
			<dp-form-log :params="setData.params" :data="setData.data"></dp-form-log> 
		</block>
		<block v-if="setData.temp=='userinfo'">
			<dp-userinfo :params="setData.params" :data="setData.data" :content="setData.content"></dp-userinfo> 
		</block>
		<block v-if="setData.temp=='wxad'">
			<dp-wxad :params="setData.params" :data="setData.data"></dp-wxad> 
		</block>
		<block v-if="setData.temp=='jidian'">
			<dp-jidian :params="setData.params" :data="setData.data"></dp-jidian>
		</block>
		
		<block v-if="setData.temp=='zhaopin'">
			<dp-zhaopin :params="setData.params" :data="setData.data"></dp-zhaopin>
		</block>
		<block v-if="setData.temp=='zhaopinzhiwei'">
			<dp-zhaopinzhiwei :params="setData.params" :data="setData.data"></dp-zhaopinzhiwei>
		</block>
		<block v-if="setData.temp=='qiuzhi'">
			<dp-qiuzhi :params="setData.params" :data="setData.data"></dp-qiuzhi>
		</block>
        <block v-if="setData.temp=='xixie'">
        	<dp-xixie :params="setData.params" :data="setData.data" @getdata="getdata($event, setData)"></dp-xixie>
        </block>
		<block v-if="setData.temp=='cycle'">
			<dp-cycle :params="setData.params" :data="setData.data" @getdata="getdata($event, setData)"></dp-cycle>
		</block>
		<block v-if="setData.temp=='supervipcard'">
			<dp-supervipcard :params="setData.params" :data="setData.data"></dp-supervipcard>  
		</block>
		<block v-if="setData.temp=='daihuobiji'">
			<dp-daihuobiji :params="setData.params" :data="setData.data"></dp-daihuobiji> 
		</block>
		
		<block v-if="setData.temp=='daihuoyiuan'">
			<dp-daihuoyiuan :params="setData.params" :data="setData.data"></dp-daihuoyiuan> 
		</block>
		
		<block v-if="setData.temp=='electricity_form'">
			<dp-electricity-form :params="setData.params" :data="setData.data" :content="setData.content" :latitude="latitude" :longitude="longitude"></dp-electricity-form> 
		</block>
		
		<block v-if="setData.temp=='referral'">
			<dp-referral :params="setData.params" :data="setData.data"></dp-referral> 
		</block>
		
		<block v-if="setData.temp=='membercard'">
			<dp-membercard :params="setData.params" :data="setData.data"></dp-membercard> 
		</block>
		
	</block>
</view>
</template>
<script>
	export default {
		props: {
			menuindex:{default:-1},
			pagecontent:{},
			latitude:'',
			longitude:'',
		},
        methods: {
            getdata:function(event, sourceData){
                // 检查是否有特定的组件数据需要刷新
                // 使用条件编译分别处理小程序和H5环境
                
                // #ifdef MP-WEIXIN
                // 小程序环境特殊处理
                console.log('小程序环境: 处理getdata事件', event);
                if(event && typeof event === 'object') {
                    // 确保参数结构正确 - 小程序可能会丢失部分事件数据
                    const componentId = event.id || (event.detail && event.detail.id) || '';
                    const style = event.style || (event.detail && event.detail.style) || '';
                    const forceRefresh = event.forceRefresh || (event.detail && event.detail.forceRefresh) || false;
                    const refreshType = event.refreshType || (event.detail && event.detail.refreshType) || '';
                    
                    // 打印更详细的日志，帮助排查问题
                    console.log('小程序环境解析事件参数:', {
                        原始事件: event,
                        组件ID: componentId,
                        样式: style, 
                        刷新类型: refreshType,
                        强制刷新: forceRefresh,
                        时间戳: event._timestamp || (event.detail && event.detail._timestamp) || new Date().getTime()
                    });
                    
                    // 添加源组件样式标记
                    const sourceStyle = sourceData && sourceData.params && sourceData.params.style ? sourceData.params.style : '';
                    const sourceId = sourceData && sourceData.id ? sourceData.id : '';
                    
                    console.log('源组件信息:', { ID: sourceId, 样式: sourceStyle });
                    
                    // 优化：确保组件ID不为空（使用源组件ID）
                    const effectiveComponentId = componentId || sourceId;
                    const effectiveStyle = style || refreshType || sourceStyle;
                    
                    if(effectiveComponentId && effectiveComponentId !== '') {
                        // 有特定组件需要刷新，只刷新该组件
                        console.log('传递到首页的参数:', {
                            componentId: effectiveComponentId,
                            style: effectiveStyle,
                            forceRefresh: true
                        });
                        
                        this.$emit('getdata', {
                            componentId: effectiveComponentId,
                            style: effectiveStyle,
                            forceRefresh: true,
                            _timestamp: new Date().getTime() // 添加时间戳确保每次请求不会被缓存
                        });
                        return;
                    }
                }
                // 如果不是特定组件刷新，执行全局刷新
                this.$emit('getdata');
                // #endif
                
                // #ifdef H5
                // H5环境下的处理
                if(event && typeof event === 'object' && (event.id || event.componentId)) {
                    // 有特定组件需要刷新，只刷新该组件
                    const componentId = event.id || event.componentId;
                    const style = event.style || '';
                    
                    console.log('H5环境处理事件:', {
                        组件ID: componentId,
                        样式: style,
                        强制刷新: event.forceRefresh
                    });
                    
                    this.$emit('getdata', {
                        componentId: componentId,
                        style: style,
                        forceRefresh: event.forceRefresh,
                        _timestamp: event._timestamp || new Date().getTime()
                    });
                } else {
                    // 原有的无参数刷新，刷新所有组件
                    this.$emit('getdata');
                }
                // #endif
                
                // #ifndef MP-WEIXIN || H5
                // 其他平台环境
                if(event && typeof event === 'object' && (event.id || event.componentId)) {
                    // 有特定组件需要刷新，只刷新该组件
                    const componentId = event.id || event.componentId;
                    const style = event.style || '';
                    
                    this.$emit('getdata', {
                        componentId: componentId,
                        style: style,
                        forceRefresh: event.forceRefresh,
                        _timestamp: event._timestamp || new Date().getTime()
                    });
                } else {
                    // 原有的无参数刷新，刷新所有组件
                    this.$emit('getdata');
                }
                // #endif
            }
        }
	}
</script>
