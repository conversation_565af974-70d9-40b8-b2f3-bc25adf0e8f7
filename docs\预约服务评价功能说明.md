# 预约服务评价功能说明

## 1. 功能概述

预约服务评价功能支持两种评价方式：星星评价和好中差评价，以及两种提成发放模式：服务完成后立即发放和评价完成后发放。

## 2. 接口说明

### 2.1 获取评价页面数据

**接口地址**：`/ApiYuyue/commentps`

**请求方式**：GET

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| id | int | 是 | 工单ID |

**响应参数**：

```json
{
  "worker_order": {
    "id": 123,
    "ordernum": "YY202405010001",
    "worker_id": 10,
    "ticheng": 50.00,
    "reward_status": 0,
    "bid": 0
  },
  "comment": null,
  "setting": {
    "comment_type": 0,
    "reward_node": 0
  }
}
```

**参数说明**：

- `worker_order`: 工单信息
  - `id`: 工单ID
  - `ordernum`: 订单号
  - `worker_id`: 服务人员ID
  - `ticheng`: 提成金额
  - `reward_status`: 奖励状态（0-未奖励，1-已奖励）
  - `bid`: 商户ID
- `comment`: 评价信息，如果已评价则返回评价详情，否则为null
- `setting`: 系统设置
  - `comment_type`: 评价方式（0-星星评价，1-好中差评价）
  - `reward_node`: 提成奖励节点（0-服务完成后，1-评价完成后）

### 2.2 提交评价

**接口地址**：`/ApiYuyue/commentps`

**请求方式**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| id | int | 是 | 工单ID |
| content | string | 是 | 评价内容 |
| content_pic | string | 否 | 评价图片，多张图片用逗号分隔 |
| score | int | 是 | 评分（1-5分） |

**响应参数**：

```json
{
  "status": 1,
  "msg": "评价成功"
}
```

## 3. 评价流程

### 3.1 星星评价流程（comment_type=0）

1. 前端调用 `/ApiYuyue/commentps` GET 接口获取评价页面数据
2. 根据返回的 `setting.comment_type` 值判断评价方式，如果为0则显示星星评价界面
3. 用户选择1-5星评分
4. 填写评价内容，可上传图片
5. 提交评价到 `/ApiYuyue/commentps` POST 接口
6. 后端处理：
   - 如果 `setting.reward_node=0`（服务完成后奖励），则不处理奖励（已在服务完成时发放）
   - 如果 `setting.reward_node=1`（评价完成后奖励）且评价无需审核，则服务人员立即获得全额提成
   - 如果评价需要审核，则等待管理员审核通过后，服务人员获得全额提成

### 3.2 好中差评价流程（comment_type=1）

1. 前端调用 `/ApiYuyue/commentps` GET 接口获取评价页面数据
2. 根据返回的 `setting.comment_type` 值判断评价方式，如果为1则显示好中差评价界面
3. 用户选择好评、中评或差评（前端根据评分自动判断：4-5分为好评，3分为中评，1-2分为差评）
4. 填写评价内容，可上传图片
5. 提交评价到 `/ApiYuyue/commentps` POST 接口
6. 后端处理：
   - 如果 `setting.reward_node=0`（服务完成后奖励），则不处理奖励（已在服务完成时发放）
   - 如果 `setting.reward_node=1`（评价完成后奖励）且评价无需审核：
     - 好评：服务人员获得设定比例的提成（默认100%）
     - 中评：服务人员获得设定比例的提成（默认70%）
     - 差评：服务人员获得设定比例的提成（默认50%）
   - 如果评价需要审核，则等待管理员审核通过后，服务人员根据评价结果获得相应比例的提成

## 4. 前端实现说明

### 4.1 评价界面

前端根据系统设置的评价方式（`setting.comment_type`）显示对应的评价界面：

- 当 `setting.comment_type=0` 时，显示星星评价界面
- 当 `setting.comment_type=1` 时，显示好中差评价界面

### 4.2 评分规则

- 星星评价：用户可以选择1-5星
- 好中差评价：
  - 好评：对应5分
  - 中评：对应3分
  - 差评：对应1分

### 4.3 奖励说明

当 `setting.reward_node=1`（评价完成后奖励）时，前端会显示奖励说明信息：

- 星星评价模式：显示"您的评价将直接影响服务人员的奖励发放"
- 好中差评价模式：
  - 好评：服务人员将获得100%的奖励
  - 中评：服务人员将获得70%的奖励
  - 差评：服务人员将获得50%的奖励

### 4.4 图片资源

评价功能需要以下图片资源：

1. 星星评价图标：
   - `star.png`：未选中的星星
   - `star2.png`：选中的星星

2. 好中差评价图标：
   - `good.png`：好评选中状态图标
   - `good_gray.png`：好评未选中状态图标
   - `medium.png`：中评选中状态图标
   - `medium_gray.png`：中评未选中状态图标
   - `bad.png`：差评选中状态图标
   - `bad_gray.png`：差评未选中状态图标

3. 其他图标：
   - `ico-del.png`：删除图片按钮图标
   - `shaitu_icon.png`：上传图片按钮图标

**注意**：所有图片资源引用时需要使用完整的URL路径，包含域名前缀，例如：
```html
<image :src="pre_url+'/static/img/good.png'"></image>
```

## 5. 注意事项

1. 前端需要根据系统设置的评价方式（`setting.comment_type`）显示对应的评价界面
2. 评价提交后，根据系统设置的评价审核开关决定是否需要审核
3. 如果系统设置为评价后奖励（`setting.reward_node=1`），且评价需要审核，则服务人员需要等待评价审核通过后才能获得提成
4. 好中差评价模式下，服务人员获得的提成金额会根据评价结果按比例计算
5. 前端可以根据评分自动判断好中差评价的等级：4-5分为好评，3分为中评，1-2分为差评
6. 所有图片资源引用时必须使用完整的URL路径，包含域名前缀 