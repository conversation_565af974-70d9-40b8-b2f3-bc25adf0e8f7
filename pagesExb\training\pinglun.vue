<template>
<view class="container">
	<view class="header">
		<view class="title">发表评论</view>
	</view>
	
	<view class="content">
		<view class="training-info" v-if="training">
			<view class="training-title">{{training.name}}</view>
			<view class="training-desc" v-if="training.subname">{{training.subname}}</view>
		</view>
		
		<view class="form-item" v-if="type==1">
			<view class="label">回复 {{hfname}}：</view>
		</view>
		
		<view class="form-item">
			<textarea v-model="content" placeholder="写下你的想法..." class="textarea" auto-height></textarea>
		</view>
		
		<view class="form-item" v-if="canImage">
			<view class="label">图片（可选）</view>
			<view class="image-upload">
				<view class="upload-btn" @tap="chooseImage" v-if="images.length < 3">
					<image src="/static/img/add-image.png" class="add-icon"></image>
					<text>添加图片</text>
				</view>
				<view class="image-item" v-for="(item, index) in images" :key="index">
					<image :src="item" mode="aspectFill" class="image"></image>
					<view class="remove-btn" @tap="removeImage" :data-index="index">×</view>
				</view>
			</view>
		</view>
	</view>
	
	<view class="footer">
		<button class="submit-btn" @tap="submit" :style="{background: t('color1')}" :disabled="!content.trim()">
			发表评论
		</button>
	</view>
	
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			type: 0, // 0评论 1回复
			id: 0, // 训练营ID
			hfid: 0, // 回复评论ID
			hfname: '', // 回复用户名
			content: '',
			images: [],
			training: null,
			canImage: true
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.type = parseInt(opt.type) || 0;
		this.id = parseInt(opt.id) || 0;
		this.hfid = parseInt(opt.hfid) || 0;
		this.hfname = opt.hfname || '';
		
		// 2025-01-03 22:55:53,565-INFO-[training_pinglun][onLoad_001] 初始化训练营评论页面
		console.log('2025-01-03 22:55:53,565-INFO-[training_pinglun][onLoad_001] 初始化训练营评论页面, type:', this.type, 'id:', this.id, 'hfid:', this.hfid);
		
		this.getTrainingInfo();
	},
	methods: {
		getTrainingInfo: function() {
			var that = this;
			
			// 2025-01-03 22:55:53,565-INFO-[training_pinglun][getTrainingInfo_001] 获取训练营信息
			console.log('2025-01-03 22:55:53,565-INFO-[training_pinglun][getTrainingInfo_001] 获取训练营信息, id:', that.id);
			
			app.get('ApiKechengTraining/detail', {id: that.id}, function(res) {
				// 2025-01-03 22:55:53,565-INFO-[training_pinglun][getTrainingInfo_002] 获取训练营信息响应
				console.log('2025-01-03 22:55:53,565-INFO-[training_pinglun][getTrainingInfo_002] 获取训练营信息响应, status:', res.status);
				
				if (res.status == 1) {
					that.training = res.data;
					uni.setNavigationBarTitle({
						title: that.type == 0 ? '发表评论' : '回复评论'
					});
				}
			});
		},
		chooseImage: function() {
			var that = this;
			uni.chooseImage({
				count: 3 - that.images.length,
				success: function(res) {
					// 2025-01-03 22:55:53,565-INFO-[training_pinglun][chooseImage_001] 选择图片
					console.log('2025-01-03 22:55:53,565-INFO-[training_pinglun][chooseImage_001] 选择图片, count:', res.tempFilePaths.length);
					
					var tempFilePaths = res.tempFilePaths;
					for (var i = 0; i < tempFilePaths.length; i++) {
						that.images.push(tempFilePaths[i]);
					}
				}
			});
		},
		removeImage: function(e) {
			var index = e.currentTarget.dataset.index;
			// 2025-01-03 22:55:53,565-INFO-[training_pinglun][removeImage_001] 移除图片
			console.log('2025-01-03 22:55:53,565-INFO-[training_pinglun][removeImage_001] 移除图片, index:', index);
			this.images.splice(index, 1);
		},
		submit: function() {
			var that = this;
			var content = that.content.trim();
			
			if (!content) {
				app.error('请输入评论内容');
				return;
			}
			
			// 2025-01-03 22:55:53,565-INFO-[training_pinglun][submit_001] 提交评论
			console.log('2025-01-03 22:55:53,565-INFO-[training_pinglun][submit_001] 提交评论, type:', that.type, 'content_length:', content.length);
			
			app.showLoading('发表中...');
			
			var data = {
				id: that.id,
				content: content,
				type: that.type
			};
			
			if (that.type == 1) {
				data.hfid = that.hfid;
			}
			
			if (that.images.length > 0) {
				data.images = that.images.join(',');
			}
			
			app.post('ApiKechengTraining/addpinglun', data, function(res) {
				app.showLoading(false);
				
				// 2025-01-03 22:55:53,565-INFO-[training_pinglun][submit_002] 提交评论响应
				console.log('2025-01-03 22:55:53,565-INFO-[training_pinglun][submit_002] 提交评论响应, status:', res.status);
				
				if (res.status == 1) {
					app.success('发表成功');
					setTimeout(function() {
						uni.navigateBack();
					}, 1000);
				} else {
					app.error(res.msg);
				}
			});
		}
	}
};
</script>

<style>
.container {
	min-height: 100vh;
	background: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.header {
	background: #fff;
	padding: 20rpx;
	border-bottom: 1rpx solid #eee;
}

.title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
}

.content {
	flex: 1;
	padding: 20rpx;
}

.training-info {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.training-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.training-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.form-item {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
}

.textarea {
	width: 100%;
	min-height: 200rpx;
	font-size: 28rpx;
	line-height: 1.6;
	color: #333;
	background: transparent;
}

.image-upload {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.upload-btn {
	width: 160rpx;
	height: 160rpx;
	border: 2rpx dashed #ddd;
	border-radius: 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 24rpx;
}

.add-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 10rpx;
}

.image-item {
	position: relative;
	width: 160rpx;
	height: 160rpx;
}

.image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
}

.remove-btn {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background: #ff4757;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: bold;
}

.footer {
	padding: 20rpx;
	background: #fff;
	border-top: 1rpx solid #eee;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn[disabled] {
	background: #ccc !important;
	color: #999 !important;
}
</style> 