<template>
  <view class="container">
    <block v-if="isload">
      <!-- 月份选择器 -->
      <view class="month-selector">
        <picker mode="date" fields="month" :value="selectedMonth" @change="onMonthChange">
          <view class="picker">
            <text>{{ selectedMonth ? selectedMonth : '请选择月份' }}</text>
          </view>
        </picker>
      </view>
   <!-- 显示总佣金 -->
      <view class="total-commission" v-if="totalCommission !== null">
        <text>本月总{{ t('佣金') }}：{{ totalCommission }}</text>
      </view>
      <dd-tab :itemdata="[t('佣金')+'明细','提现记录']" :itemst="['0','1']" :st="st" :isfixed="false" @changetab="changetab"></dd-tab>

   

      <view class="content">
        <block v-if="st==0">
          <view v-for="(item, index) in datalist" :key="index" class="item">
            <view class="f1">
              <text class="t1">{{item.remark}}</text>
              <text class="t2">{{item.createtime}}</text>
              <text class="t3">变更后: {{item.after}}</text>
            </view>
            <view class="f2">
              <text class="t1" v-if="item.money>0">+{{item.money}}</text>
              <text class="t2" v-else>{{item.money}}</text>
            </view>
          </view>
        </block>
        <block v-if="st==1">
          <view v-for="(item, index) in datalist" :key="index" class="item">
            <view class="f1">
              <text class="t1">提现：{{item.money}} </text>
              <text class="t2">{{item.createtime}}</text>
            </view>
            <view class="f3">
              <text class="t1" v-if="item.status==0">审核中</text>
              <text class="t1" v-if="item.status==1">已审核</text>
              <text class="t2" v-if="item.status==2">已驳回</text>
              <text class="t1" v-if="item.status==3">已打款</text>
            </view>
          </view>
        </block>
      </view>
    </block>
    <nodata v-if="nodata"></nodata>
    <nomore v-if="nomore"></nomore>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>
<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      nodata: false,
      nomore: false,
      st: 0,
      datalist: [],
      textset: {},
      pagenum: 1,
      selectedMonth: '', // 存储所选月份，格式为 'YYYY-MM'
      totalCommission: null, // 总佣金
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.st = this.opt.st || 0;
	this.mingxiid = this.opt.mingxiid || 0;
    // 初始化 selectedMonth 为当前月份
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    this.selectedMonth = `${currentYear}-${currentMonth}`;

    this.getdata();
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    // 处理月份变化
    onMonthChange: function (e) {
      this.selectedMonth = e.detail.value;
      this.getdata();
    },
 getdata: function (loadmore) {
 		if(!loadmore){
 			this.pagenum = 1;
 			this.datalist = [];
 		}
   var that = this;
   var st = that.st;
   var mingxiid = that.mingxiid;
   var pagenum = that.pagenum;
 		that.loading = true;
 		that.nodata = false;
   that.nomore = false;
    app.post('ApiAgent/commissionlog', { mingxiid: mingxiid, st: st, pagenum: pagenum, month: that.selectedMonth }, function (res) {
      that.loading = false;
      console.log('接口返回的数据：', res.data);
		var dataList = res.data.data || [];
		that.totalCommission = res.total_commission || 0;
     var data = res.data;
     if (pagenum == 1) {
 				that.textset = app.globalData.textset;
 				uni.setNavigationBarTitle({
 					title: that.t('佣金') + '明细'
 				});
       that.datalist = data;
	   
       if (data.length == 0) {
         that.nodata = true;
       }
 				that.loaded();
     }else{
       if (data.length == 0) {
         that.nomore = true;
       } else {
         var datalist = that.datalist;
		 
         var newdata = datalist.concat(data);
         that.datalist = newdata;
       }
     }
   });
 },


    // 添加 loaded 方法
    loaded: function() {
      this.isload = true;
      uni.stopPullDownRefresh();
    },
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    // 其他方法...
  }
};
</script>
<style>
.content {
  width: 94%;
  margin: 20rpx 3%;
}
.content .item {
  width: 100%;
  background: #fff;
  margin: 20rpx 0;
  padding: 40rpx 30rpx;
  border-radius: 8px;
  display: flex;
  align-items: center;
}
.content .item .f1 {
  width: 500rpx;
  display: flex;
  flex-direction: column;
}
.content .item .f1 .t1 {
  color: #000000;
  font-size: 30rpx;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content .item .f1 .t2,
.content .item .f1 .t3 {
  color: #666666;
  font-size: 24rpx;
  margin-top: 10rpx;
}
.content .item .f2,
.content .item .f3 {
  flex: 1;
  width: 200rpx;
  font-size: 36rpx;
  text-align: right;
}
.content .item .f2 .t1,
.content .item .f3 .t1 {
  color: #03bc01;
}
.content .item .f2 .t2,
.content .item .f3 .t2 {
  color: #000000;
}

/* 新增样式 */
.month-selector {
  width: 94%;
  margin: 20rpx 3%;
  background: #fff;
  padding: 20rpx 30rpx;
  border-radius: 8px;
}

.month-selector .picker {
  font-size: 30rpx;
  color: #000;
}

.total-commission {
  width: 94%;
  margin: 20rpx 3%;
  background: #fff;
  padding: 20rpx 30rpx;
  border-radius: 8px;
  font-size: 30rpx;
  color: #000;
}

.data-empty {
  background: #fff;
}
</style>
