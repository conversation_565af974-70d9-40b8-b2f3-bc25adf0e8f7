<template>
<view class="container">
	<block v-if="isload">
		<view class="yellow-points-header">
			<view class="title">我的{{t('黄积分')}}</view>
		</view>
		
		<view class="yellow-points-card" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}">
			<view class="card-item">
				<view class="label">当前持有</view>
				<view class="value">{{current_amount}} {{t('黄积分')}}</view>
			</view>
			<view class="card-item">
				<view class="label">当前价格</view>
				<view class="value">{{current_price}} {{t('元')}}</view>
			</view>
			<view class="card-item">
				<view class="label">我的{{t('现金券')}}</view>
				<view class="value">{{contribution}}</view>
			</view>
		</view>
		
		<view class="yellow-points-info">
			<view class="info-item">
				<view class="info-label">兑换比例</view>
				<view class="info-value">{{exchange_ratio}} {{t('现金券')}} = 1 {{t('黄积分')}}</view>
			</view>
			<view class="info-item" v-if="exchange_deadline > 0">
				<view class="info-label">兑换截止时间</view>
				<view class="info-value">{{exchange_deadline_format}}</view>
			</view>
		</view>
		
		<view class="yellow-points-progress">
			<view class="progress-title">
				<span>奖金池进度</span>
				<span>{{bonus_pool}}/{{target_amount}}{{t('元')}}</span>
			</view>
			<view class="progress-bar">
				<view class="progress-inner" :style="{width: progressPercent+'%', background:t('color1')}"></view>
			</view>
		</view>
		
		<view class="yellow-points-actions">
			<button class="btn-exchange" :style="{background:t('color1')}" @tap="exchangeYellowPoints" :disabled="!is_exchangeable">兑换{{t('黄积分')}}</button>
			<button class="btn-cashout" :style="{background:t('color1')}" @tap="cashoutYellowPoints">提现{{t('黄积分')}}</button>
		</view>
		
		<view class="yellow-points-records">
			<view class="record-tabs">
				<view :class="'tab-item ' + (activeTab === 'exchange' ? 'active' : '')" 
					:style="activeTab === 'exchange' ? 'color:'+t('color1')+';border-bottom-color:'+t('color1') : ''" 
					@tap="switchTab('exchange')">兑换记录</view>
				<view :class="'tab-item ' + (activeTab === 'cashout' ? 'active' : '')" 
					:style="activeTab === 'cashout' ? 'color:'+t('color1')+';border-bottom-color:'+t('color1') : ''" 
					@tap="switchTab('cashout')">提现记录</view>
			</view>
			
			<!-- 兑换记录 -->
			<view class="record-list" v-if="activeTab === 'exchange'">
				<block v-if="exchangeRecords.length > 0">
					<view class="record-item" v-for="(item, index) in exchangeRecords" :key="index">
						<view class="record-left">
							<view class="record-title">{{t('黄积分')}}兑换</view>
							<view class="record-time">{{item.exchange_time_format}}</view>
						</view>
						<view class="record-right">
							<view class="record-amount">+{{item.amount}} {{t('黄积分')}}</view>
							<view class="record-desc">消耗{{t('现金券')}}: {{item.contribution}}</view>
						</view>
					</view>
				</block>
				<nodata v-if="exchangeRecords.length === 0"></nodata>
				<nomore v-if="exchangeNomore"></nomore>
			</view>
			
			<!-- 提现记录 -->
			<view class="record-list" v-if="activeTab === 'cashout'">
				<block v-if="cashoutRecords.length > 0">
					<view class="record-item" v-for="(item, index) in cashoutRecords" :key="index">
						<view class="record-left">
							<view class="record-title">{{t('黄积分')}}提现</view>
							<view class="record-time">{{item.cashout_time_format}}</view>
						</view>
						<view class="record-right">
							<view class="record-amount">-{{item.amount}} {{t('黄积分')}}</view>
							<view class="record-desc">
								<text>提现金额: {{item.money}}{{t('元')}}</text>
								<text class="status" :class="'status-' + item.status">{{item.status_text}}</text>
							</view>
						</view>
					</view>
				</block>
				<nodata v-if="cashoutRecords.length === 0"></nodata>
				<nomore v-if="cashoutNomore"></nomore>
			</view>
		</view>
	</block>
	
	<!-- 兑换弹窗 -->
	<view class="popup-mask" v-if="showExchangePopup" @tap="closeExchangePopup"></view>
	<view class="exchange-popup" v-if="showExchangePopup">
		<view class="popup-header">
			<text class="popup-title">兑换{{t('黄积分')}}</text>
			<text class="popup-close" @tap="closeExchangePopup">×</text>
		</view>
		
		<view class="popup-body">
			<view class="info-row">
				<text>当前{{t('现金券')}}:</text>
				<text>{{contribution}}</text>
			</view>
			<view class="info-row">
				<text>兑换比例:</text>
				<text>{{exchange_ratio}} {{t('现金券')}} = 1 {{t('黄积分')}}</text>
			</view>
			
			<view class="input-row">
				<text>兑换数量:</text>
				<input type="number" v-model="exchangeAmount" min="1" @input="calcContribution"/>
			</view>
			
			<view class="info-row highlight">
				<text>需要{{t('现金券')}}:</text>
				<text>{{needContribution}}</text>
			</view>
		</view>
		
		<view class="popup-footer">
			<button class="btn-cancel" @tap="closeExchangePopup">取消</button>
			<button class="btn-confirm" :style="{background:t('color1')}" @tap="confirmExchange" :disabled="!canExchange">确认兑换</button>
		</view>
	</view>
	
	<!-- 提现弹窗 -->
	<view class="popup-mask" v-if="showCashoutPopup" @tap="closeCashoutPopup"></view>
	<view class="cashout-popup" v-if="showCashoutPopup">
		<view class="popup-header">
			<text class="popup-title">提现{{t('黄积分')}}</text>
			<text class="popup-close" @tap="closeCashoutPopup">×</text>
		</view>
		
		<view class="popup-body">
			<view class="info-row">
				<text>当前持有:</text>
				<text>{{current_amount}} {{t('黄积分')}}</text>
			</view>
			<view class="info-row">
				<text>当前价格:</text>
				<text>{{current_price}} {{t('元')}}/{{t('黄积分')}}</text>
			</view>
			
			<view class="input-row">
				<text>提现数量:</text>
				<input type="number" v-model="cashoutAmount" min="1" :max="current_amount" @input="calcCashoutMoney"/>
			</view>
			
			<view class="info-row highlight">
				<text>提现金额:</text>
				<text>{{cashoutMoney}} {{t('元')}}</text>
			</view>
			
			<view class="note">
				<text>提示: 提现申请需要管理员审核，审核通过后将发放到您的账户</text>
			</view>
		</view>
		
		<view class="popup-footer">
			<button class="btn-cancel" @tap="closeCashoutPopup">取消</button>
			<button class="btn-confirm" :style="{background:t('color1')}" @tap="confirmCashout" :disabled="!canCashout">确认提现</button>
		</view>
	</view>
	
	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			loading: false,
			isload: false,
			
			// 黄积分数据
			current_price: 0,
			exchange_ratio: 0,
			current_amount: 0,
			contribution: 0,
			exchange_deadline: 0,
			bonus_pool: 0,
			target_amount: 0,
			is_exchangeable: 0,
			exchange_deadline_format: '',
			
			// 弹窗控制
			showExchangePopup: false,
			showCashoutPopup: false,
			
			// 表单数据
			exchangeAmount: 1,
			cashoutAmount: 1,
			
			// 分页数据
			activeTab: 'exchange',
			exchangeRecords: [],
			cashoutRecords: [],
			exchangePagenum: 1,
			cashoutPagenum: 1,
			exchangeNomore: false,
			cashoutNomore: false,
			pageSize: 10
		}
	},
	
	computed: {
		progressPercent() {
			if (this.target_amount <= 0) return 0;
			const percent = (this.bonus_pool / this.target_amount) * 100;
			return Math.min(percent, 100);
		},
		needContribution() {
			return this.exchangeAmount * this.exchange_ratio;
		},
		cashoutMoney() {
			return (this.cashoutAmount * this.current_price).toFixed(2);
		},
		canExchange() {
			return this.exchangeAmount > 0 && this.needContribution <= this.contribution && this.is_exchangeable;
		},
		canCashout() {
			return this.cashoutAmount > 0 && this.cashoutAmount <= this.current_amount;
		}
	},
	
	onLoad() {
		this.getData();
	},
	
	onPullDownRefresh() {
		this.getData();
	},
	
	onReachBottom() {
		if (this.activeTab === 'exchange' && !this.exchangeNomore) {
			this.exchangePagenum++;
			this.getExchangeRecords(true);
		} else if (this.activeTab === 'cashout' && !this.cashoutNomore) {
			this.cashoutPagenum++;
			this.getCashoutRecords(true);
		}
	},
	
	methods: {
		// 获取黄积分数据
		getData() {
			const that = this;
			that.loading = true;
			
			app.get('ApiBpv/getData', {}, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.code === 1) {
					const data = res.data;
					that.current_price = data.current_price;
					that.exchange_ratio = data.exchange_ratio;
					that.current_amount = data.current_amount;
					that.contribution = data.contribution;
					that.exchange_deadline = data.exchange_deadline;
					that.bonus_pool = data.bonus_pool;
					that.target_amount = data.target_amount;
					that.is_exchangeable = data.is_exchangeable;
					
					// 格式化日期
					if (that.exchange_deadline > 0) {
						const date = new Date(that.exchange_deadline * 1000);
						that.exchange_deadline_format = that.formatDate(date);
					}
					
					that.isload = true;
					
					// 加载记录
					that.resetRecords();
					that.getExchangeRecords();
					that.getCashoutRecords();
				} else {
					uni.showToast({
						title: res.msg || '获取数据失败',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		// 重置记录数据
		resetRecords() {
			this.exchangePagenum = 1;
			this.cashoutPagenum = 1;
			this.exchangeRecords = [];
			this.cashoutRecords = [];
			this.exchangeNomore = false;
			this.cashoutNomore = false;
		},
		
		// 获取兑换记录
		getExchangeRecords(loadmore) {
			const that = this;
			that.loading = true;
			
			if (!loadmore) {
				that.exchangePagenum = 1;
				that.exchangeRecords = [];
				that.exchangeNomore = false;
			}
			
			app.get('ApiBpv/exchangeRecords', {
				page: that.exchangePagenum,
				limit: that.pageSize
			}, function(res) {
				that.loading = false;
				
				if (res.code === 1) {
					const records = res.data;
					
					if (that.exchangePagenum === 1) {
						that.exchangeRecords = records;
					} else {
						if (records.length === 0) {
							that.exchangeNomore = true;
						} else {
							that.exchangeRecords = that.exchangeRecords.concat(records);
						}
					}
				} else {
					uni.showToast({
						title: res.msg || '获取兑换记录失败',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		// 获取提现记录
		getCashoutRecords(loadmore) {
			const that = this;
			that.loading = true;
			
			if (!loadmore) {
				that.cashoutPagenum = 1;
				that.cashoutRecords = [];
				that.cashoutNomore = false;
			}
			
			app.get('ApiBpv/cashoutRecords', {
				page: that.cashoutPagenum,
				limit: that.pageSize
			}, function(res) {
				that.loading = false;
				
				if (res.code === 1) {
					const records = res.data;
					
					if (that.cashoutPagenum === 1) {
						that.cashoutRecords = records;
					} else {
						if (records.length === 0) {
							that.cashoutNomore = true;
						} else {
							that.cashoutRecords = that.cashoutRecords.concat(records);
						}
					}
				} else {
					uni.showToast({
						title: res.msg || '获取提现记录失败',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		// 切换标签页
		switchTab(tab) {
			this.activeTab = tab;
		},
		
		// 兑换黄积分
		exchangeYellowPoints() {
			if (!this.is_exchangeable) {
				this.$refs.popmsg.show({
					msg: '当前不可兑换' + this.t('黄积分'),
					type: 'warning'
				});
				return;
			}
			
			this.showExchangePopup = true;
			this.exchangeAmount = 1;
		},
		
		// 确认兑换
		confirmExchange() {
			if (!this.canExchange) return;
			
			const that = this;
			that.loading = true;
			
			app.post('ApiBpv/exchange', {
				amount: that.exchangeAmount
			}, function(res) {
				that.loading = false;
				
				if (res.code === 1) {
					// 使用uni.showToast确保提示显示
					uni.showToast({
						title: res.msg || '兑换成功',
						icon: 'success',
						duration: 2000
					});
					
					// 关闭弹窗
					that.closeExchangePopup();
					
					// 延迟刷新数据，确保提示显示完成
					setTimeout(function() {
						that.getData();
					}, 500);
				} else {
					uni.showToast({
						title: res.msg || '兑换失败',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		// 提现黄积分
		cashoutYellowPoints() {
			if (this.current_amount <= 0) {
				this.$refs.popmsg.show({
					msg: '当前没有可提现的' + this.t('黄积分'),
					type: 'warning'
				});
				return;
			}
			
			this.showCashoutPopup = true;
			this.cashoutAmount = 1;
		},
		
		// 确认提现
		confirmCashout() {
			if (!this.canCashout) return;
			
			const that = this;
			that.loading = true;
			
			app.post('ApiBpv/cashout', {
				amount: that.cashoutAmount
			}, function(res) {
				that.loading = false;
				
				if (res.code === 1) {
					// 使用uni.showToast确保提示显示
					uni.showToast({
						title: res.msg || '提现申请成功',
						icon: 'success',
						duration: 2000
					});
					
					// 关闭弹窗
					that.closeCashoutPopup();
					
					// 延迟刷新数据，确保提示显示完成
					setTimeout(function() {
						that.getData();
					}, 500);
				} else {
					uni.showToast({
						title: res.msg || '提现申请失败',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},
		
		// 关闭兑换弹窗
		closeExchangePopup() {
			this.showExchangePopup = false;
		},
		
		// 关闭提现弹窗
		closeCashoutPopup() {
			this.showCashoutPopup = false;
		},
		
		// 计算需要的现金券
		calcContribution() {
			// 限制输入为正整数
			this.exchangeAmount = Math.max(1, parseInt(this.exchangeAmount) || 0);
		},
		
		// 计算提现金额
		calcCashoutMoney() {
			// 限制输入为正整数且不超过持有量
			this.cashoutAmount = Math.min(
				this.current_amount,
				Math.max(1, parseInt(this.cashoutAmount) || 0)
			);
		},
		
		// 日期格式化
		formatDate(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		}
	},
}
</script>

<style>
.container {
	padding-bottom: 30rpx;
}

.yellow-points-header {
	padding: 30rpx;
}

.yellow-points-header .title {
	font-size: 36rpx;
	font-weight: bold;
}

.yellow-points-card {
	margin: 0 30rpx;
	padding: 30rpx;
	border-radius: 16rpx;
	color: #fff;
	display: flex;
	justify-content: space-between;
}

.card-item {
	text-align: center;
}

.card-item .label {
	font-size: 26rpx;
	opacity: 0.8;
	margin-bottom: 10rpx;
}

.card-item .value {
	font-size: 32rpx;
	font-weight: bold;
}

.yellow-points-info {
	margin: 30rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	background-color: #f9f9f9;
}

.info-item {
	display: flex;
	justify-content: space-between;
	padding: 15rpx 0;
	font-size: 28rpx;
}

.yellow-points-progress {
	margin: 30rpx;
}

.progress-title {
	display: flex;
	justify-content: space-between;
	font-size: 28rpx;
	margin-bottom: 15rpx;
}

.progress-bar {
	height: 18rpx;
	background-color: #f1f1f1;
	border-radius: 9rpx;
	overflow: hidden;
}

.progress-inner {
	height: 100%;
	border-radius: 9rpx;
}

.yellow-points-actions {
	display: flex;
	justify-content: space-between;
	margin: 30rpx;
}

.btn-exchange, .btn-cashout {
	width: 45%;
	color: #fff;
	font-size: 28rpx;
	padding: 18rpx 0;
	border-radius: 10rpx;
}

button[disabled] {
	background-color: #cccccc !important;
	color: #ffffff !important;
}

.yellow-points-records {
	margin: 30rpx;
}

.record-tabs {
	display: flex;
	border-bottom: 1rpx solid #eee;
	margin-bottom: 20rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 20rpx 0;
	font-size: 28rpx;
	position: relative;
	border-bottom: 4rpx solid transparent;
}

.tab-item.active {
	font-weight: bold;
}

.record-item {
	display: flex;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f2f2f2;
}

.record-title {
	font-size: 28rpx;
	margin-bottom: 10rpx;
}

.record-time {
	font-size: 24rpx;
	color: #999;
}

.record-amount {
	font-size: 32rpx;
	font-weight: bold;
	text-align: right;
	margin-bottom: 10rpx;
}

.record-desc {
	font-size: 24rpx;
	color: #666;
	text-align: right;
}

.status {
	margin-left: 10rpx;
	padding: 2rpx 10rpx;
	border-radius: 6rpx;
	font-size: 22rpx;
}

.status-0 {
	background-color: #f0f0f0;
	color: #666;
}

.status-1 {
	background-color: #e1f3d8;
	color: #67c23a;
}

.status-2 {
	background-color: #fef0f0;
	color: #f56c6c;
}

/* 弹窗样式 */
.popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
}

.exchange-popup, .cashout-popup {
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 85%;
	background-color: #fff;
	border-radius: 12rpx;
	z-index: 1000;
	overflow: hidden;
}

.popup-header {
	position: relative;
	padding: 30rpx;
	text-align: center;
	border-bottom: 1rpx solid #f2f2f2;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
}

.popup-close {
	position: absolute;
	right: 30rpx;
	top: 30rpx;
	font-size: 36rpx;
	line-height: 1;
}

.popup-body {
	padding: 30rpx;
}

.info-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	font-size: 28rpx;
}

.highlight {
	font-weight: bold;
}

.input-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	font-size: 28rpx;
}

.input-row input {
	width: 60%;
	border: 1rpx solid #ddd;
	border-radius: 6rpx;
	padding: 10rpx 20rpx;
	text-align: right;
}

.note {
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #666;
	margin-top: 20rpx;
}

.popup-footer {
	display: flex;
	border-top: 1rpx solid #f2f2f2;
}

.btn-cancel, .btn-confirm {
	flex: 1;
	padding: 20rpx 0;
	text-align: center;
	font-size: 28rpx;
}

.btn-cancel {
	background-color: #f5f5f5;
	color: #666;
}

.btn-confirm {
	color: #fff;
}
</style> 