<template>
<view class="container">
	<block v-if="isload">
		
		<view class="content">
			<view class="info-item" style="height:136rpx;line-height:136rpx">
				<view class="t1" style="flex:1;">微信二维码</view>
				<image :src="userinfo.wximg" style="width:88rpx;height:88rpx;" @tap="uploadwximg"/>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" style="height:156rpx;line-height:156rpx;margin-right:100rpx;">
				<view class="t1" style="flex:1;">支付宝二维码</view>
				<image :src="userinfo.zfbimg" style="width:88rpx;height:88rpx;" @tap="uploadzfbimg"/>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view> 
			<view class="info-item" @tap="goto" data-url="setaliaccount">
				<view class="t1">支付宝账号</view>
				<view class="t2">{{userinfo.aliaccount}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<!-- <view class="info-item" @tap="goto" data-url="setusd">
				<view class="t1">U地址</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view> -->
			<view class="info-item" @tap="goto" data-url="setbankinfo">
				<text class="t1">银行卡</text>
				<text class="t2">{{userinfo.bankname ? '已设置' : ''}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		
		<view class="content">
			<view class="info-item" @tap="fanhui">
				<view class="t1"  style="background-color: red; color: #fff; width: 100%;text-align: center; border-radius: 10rpx;">返回</view>
			</view>
		</view>
		<!-- view class="content">
			<view class="info-item" @tap="logout">
				<view class="t1">退出登录</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view> -->
		<!-- #ifdef APP-PLUS -->
		<!-- <view class="content">
			<view class="info-item" @tap="delaccount">
				<view class="t1">注销账号</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view> -->
		<!-- #endif -->
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			prodata:'',
			userinfo:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/set', {}, function (data) {
				that.loading = false;
				that.userinfo = data.userinfo;
				that.loaded();
			});
		},
		uploadHeadimg:function(){
			var that = this;
			app.chooseImage(function(urls){
				var headimg = urls[0];
				that.userinfo.headimg = headimg;
				app.post('ApiMiaosha/setfield',{headimg:headimg});
			},1)
		},
		uploadwximg:function(){
			var that = this;
			app.chooseImage(function(urls){
				var wximg = urls[0];
				that.userinfo.wximg = wximg;
				app.post('ApiMiaosha/setfield',{wximg:wximg});
			},1)
		},
		uploadzfbimg:function()
		{
			var that = this;
			app.chooseImage(function(urls){
				var zfbimg = urls[0];
				that.userinfo.zfbimg = zfbimg;
				app.post('ApiMiaosha/setfield',{zfbimg:zfbimg});
			},1)
		},
		delaccount:function(){
			app.confirm('注销账号后该账号下的所有数据都将删除并且无法恢复，确定要注销吗？',function(){
				app.showLoading('注销中');
				app.get('ApiMy/delaccount', {}, function (data) {
					app.showLoading(false);
					if(data.status == 1){
						app.alert(data.msg,function(){
							app.goto('/pages/index/index');
						});
					}else{
						app.alert(data.msg);
					}
				});
			})
		},
		fanhui:function(){
			var that = this;
			if(that.opt.prodata)
			{
			    app.goto('/shopPackage/shop/buy?prodata='+that.opt.prodata);	
			}else if(that.opt.id){
				 app.goto('/pagesExa/miaosha/buy?id='+that.opt.id);	
			}else{
				app.goto('/pages/my/usercenter');
			}
			
			// app.get('ApiIndex/logout', {}, function (data) {
			// 	app.showLoading(false);
			// 	if(data.status == 0){
			// 		app.alert(data.msg);
			// 	}
			// });
		}
  }
};
</script>
<style>
.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}


</style>