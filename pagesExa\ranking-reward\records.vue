<template>
	<view class="container">
		<!-- 规则筛选 -->
		<view class="filter-container">
			<view class="filter-item" @click="showRuleSelector = true">
				<text class="filter-label">规则：</text>
				<text class="filter-value">{{selectedRuleName || '全部'}}</text>
				<text class="filter-arrow">▼</text>
			</view>
		</view>
		
		<!-- 记录列表 -->
		<view class="records-list">
			<view class="no-data" v-if="recordsList.length === 0">
				<text>暂无奖励记录</text>
			</view>
			
			<view class="record-card" v-for="(item, index) in recordsList" :key="index">
				<view class="record-top">
					<view class="record-title">{{item.rule_name}}</view>
					<view class="record-status" :class="{'status-success': item.status === 1, 'status-pending': item.status === 0, 'status-rejected': item.status === 2}">
						{{item.status_text}}
					</view>
				</view>
				<view class="record-info">
					<view class="record-item">
						<text class="record-label">统计周期：</text>
						<text class="record-value">{{item.period_text}}</text>
					</view>
					<view class="record-item">
						<text class="record-label">排名：</text>
						<text class="record-value">第{{item.rank}}名</text>
					</view>
					<view class="record-item">
						<text class="record-label">统计数量：</text>
						<text class="record-value">{{item.count_num}}</text>
					</view>
					<view class="record-item">
						<text class="record-label">奖励比例：</text>
						<text class="record-value">{{item.reward_rate}}%</text>
					</view>
					<view class="record-item">
						<text class="record-label">奖励金额：</text>
						<text class="record-value reward-color">¥{{item.reward_amount}}</text>
					</view>
					<view class="record-item">
						<text class="record-label">创建时间：</text>
						<text class="record-value">{{item.create_time_text}}</text>
					</view>
					<view class="record-item" v-if="item.status === 1">
						<text class="record-label">发放时间：</text>
						<text class="record-value">{{item.issue_time_text}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="recordsList.length > 0">
			<text v-if="loading">加载中...</text>
			<text v-else-if="hasMore" @click="loadMore">点击加载更多</text>
			<text v-else>没有更多数据了</text>
		</view>
		
		<!-- 规则选择器弹窗 -->
		<view class="rule-selector" v-if="showRuleSelector">
			<view class="selector-mask" @click="showRuleSelector = false"></view>
			<view class="selector-content">
				<view class="selector-header">
					<text class="selector-title">选择规则</text>
					<text class="selector-close" @click="showRuleSelector = false">×</text>
				</view>
				<scroll-view scroll-y class="selector-body">
					<view 
						class="selector-item" 
						:class="{'item-selected': selectedRuleId === 0}"
						@click="selectRule(0, '全部')"
					>
						<text>全部</text>
						<text class="item-check" v-if="selectedRuleId === 0">✓</text>
					</view>
					<view 
						class="selector-item" 
						:class="{'item-selected': selectedRuleId === item.id}"
						v-for="(item, index) in rulesList" 
						:key="index"
						@click="selectRule(item.id, item.name)"
					>
						<text>{{item.name}}</text>
						<text class="item-check" v-if="selectedRuleId === item.id">✓</text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				recordsList: [],
				rulesList: [],
				
				currentPage: 1,
				pageSize: 10,
				hasMore: true,
				
				selectedRuleId: 0,
				selectedRuleName: '全部',
				showRuleSelector: false,
				
				loading: false
			}
		},
		onLoad() {
			this.initData();
		},
		onPullDownRefresh() {
			this.resetData();
			this.initData(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			// 初始化数据
			initData(callback) {
				// 获取规则列表
				this.getRulesList();
				// 获取记录列表
				this.getRecordsList();
				
				if (callback && typeof callback === 'function') {
					callback();
				}
			},
			
			// 重置数据
			resetData() {
				this.currentPage = 1;
				this.hasMore = true;
				this.recordsList = [];
			},
			
			// 获取规则列表
			getRulesList() {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/records][getRulesList_001] 开始获取排名奖励规则列表`);
				
				var that = this;
				
				app.post('ApiPaimingjiang/getRankingRules', {}, function(res) {
					if (res.status === 1) {
						console.log(`${that.formatTime()}-INFO-[ranking-reward/records][getRulesList_002] 获取排名奖励规则列表成功，共${res.data.length}条数据`);
						that.rulesList = res.data;
					} else {
						console.log(`${that.formatTime()}-ERROR-[ranking-reward/records][getRulesList_003] 获取排名奖励规则列表失败：${res.msg}`);
						uni.showToast({
							title: res.msg || '获取规则列表失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 获取记录列表
			getRecordsList(isLoadMore = false) {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/records][getRecordsList_001] 开始获取排名奖励记录，页码：${this.currentPage}，规则ID：${this.selectedRuleId}`);
				
				if (this.loading) return;
				
				this.loading = true;
				
				let params = {
					page: this.currentPage,
					limit: this.pageSize
				};
				
				if (this.selectedRuleId > 0) {
					params.rule_id = this.selectedRuleId;
				}
				
				var that = this;
				
				app.post('ApiPaimingjiang/getRankingRecords', params, function(res) {
					that.loading = false;
					
					if (res.status === 1) {
						console.log(`${that.formatTime()}-INFO-[ranking-reward/records][getRecordsList_002] 获取排名奖励记录成功，当前页数据：${res.data.list.length}条`);
						
						if (isLoadMore) {
							that.recordsList = [...that.recordsList, ...res.data.list];
						} else {
							that.recordsList = res.data.list;
						}
						
						// 判断是否还有更多数据
						that.hasMore = that.recordsList.length < res.data.total;
					} else {
						console.log(`${that.formatTime()}-ERROR-[ranking-reward/records][getRecordsList_003] 获取排名奖励记录失败：${res.msg}`);
						uni.showToast({
							title: res.msg || '获取奖励记录失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 加载更多
			loadMore() {
				if (!this.hasMore || this.loading) return;
				
				this.currentPage++;
				this.getRecordsList(true);
			},
			
			// 选择规则
			selectRule(ruleId, ruleName) {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/records][selectRule_001] 选择规则，ID：${ruleId}，名称：${ruleName}`);
				
				this.selectedRuleId = ruleId;
				this.selectedRuleName = ruleName;
				this.showRuleSelector = false;
				
				// 重置数据，重新加载列表
				this.resetData();
				this.getRecordsList();
			},
			
			// 格式化时间，用于日志
			formatTime() {
				const date = new Date();
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				const seconds = date.getSeconds().toString().padStart(2, '0');
				const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${milliseconds}`;
			}
		}
	}
</script>

<style lang="scss">
.container {
	padding: 30rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.reward-color {
	color: #FF6600 !important;
}

// 筛选器
.filter-container {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.filter-item {
	display: flex;
	align-items: center;
}

.filter-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
}

.filter-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	flex: 1;
}

.filter-arrow {
	font-size: 24rpx;
	color: #999;
}

// 记录列表
.record-card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.record-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #EEEEEE;
}

.record-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.record-status {
	font-size: 24rpx;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}

.status-pending {
	color: #FF9900;
	background-color: rgba(255, 153, 0, 0.1);
}

.status-success {
	color: #00CC66;
	background-color: rgba(0, 204, 102, 0.1);
}

.status-rejected {
	color: #FF3333;
	background-color: rgba(255, 51, 51, 0.1);
}

.record-info {
}

.record-item {
	display: flex;
	margin-bottom: 10rpx;
}

.record-label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
}

.record-value {
	font-size: 28rpx;
	color: #333;
}

// 加载更多
.load-more {
	text-align: center;
	padding: 30rpx 0;
	font-size: 28rpx;
	color: #999;
}

// 无数据提示
.no-data {
	text-align: center;
	padding: 50rpx 0;
	font-size: 28rpx;
	color: #999;
	background-color: #FFFFFF;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
}

// 规则选择器弹窗
.rule-selector {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
}

.selector-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0,0,0,0.5);
}

.selector-content {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #FFFFFF;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
}

.selector-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #EEEEEE;
}

.selector-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.selector-close {
	font-size: 40rpx;
	color: #999;
}

.selector-body {
	max-height: 600rpx;
}

.selector-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #EEEEEE;
	font-size: 28rpx;
	color: #333;
}

.item-selected {
	color: #FF6600;
}

.item-check {
	font-size: 32rpx;
	color: #FF6600;
}
</style> 