.container {
    min-height: 100vh;
    background: #fff;
    overflow: hidden;
}

.white-block {
    width: 750rpx;
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 50;
}

.new-page-title {
    height: 70rpx;
    color: #101d37;
    font-size: 36rpx;
    font-weight: 700;
    display: flex;
    align-items: center;
}

.base-line {
    width: 100%;
    height: 0;
}

.select-job-area {
    padding-bottom: 16rpx;
    padding-top: 16rpx;
    background: #f6f7fb;
}

.gradient-bg {
    width: 100%;
}

.gradient-bg.fixed {
    position: fixed;
    left: 0;
    z-index: 100;
}

.gradient-bg.white {
    background: #fff;
}

.select-job-area .title-box {
    padding-left: 32rpx;
    margin-bottom: 24rpx;
}

.school-life-job-list {
    padding-top: 26rpx;
    min-height: 90vh;
}

.padding206 {
    padding-top: 222rpx !important;
}

.padding106 {
    padding-top: 122rpx !important;
}

.padding16 {
    padding-top: 16rpx !important;
}

.padding0 {
    padding-top: 0rpx !important;
}

.noneList {
    width: 100%;
    height: 600rpx;
    text-align: center;
    color: #9c9c9c;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.noneList image {
    width: 520rpx;
    height: 360rpx;
}

.noneList text {
    margin-top: 100rpx;
    font-size: 28rpx;
}

.top-module {
    position: relative;
    padding-top: 88rpx;
}

.activity-mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
}

.activity-content {
    width: 594rpx;
    height: 765rpx;
    background: url('https://qiniu-image.qtshe.com/20210525_mask.png') no-repeat top;
    background-size: 594rpx 765rpx;
    position: absolute;
    left: 50%;
    margin-left: -297rpx;
    top: 50%;
    margin-top: -382.5rpx;
}

.activity-button {
    position: absolute;
    left: 50%;
    width: 400rpx;
    margin-left: -200rpx;
    top: 540rpx;
    height: 100rpx;
}

.activity-close {
    position: absolute;
    width: 60rpx;
    height: 60rpx;
    left: 50%;
    margin-left: -30rpx;
    bottom: -50rpx;
}
