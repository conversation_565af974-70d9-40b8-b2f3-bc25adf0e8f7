<template>
	<view class="main-container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<text class="page-title">首页</text>
			<view class="header-right">
				<text class="header-dot">•••</text>
				<text class="header-circle">○</text>
			</view>
		</view>
		
		<!-- 服务排行榜横幅 -->
		<view class="banner">
			<image class="banner-image" src="/static/img/ranking-banner.jpg" mode="aspectFill"></image>
		</view>
		
		<!-- 功能区图标 -->
		<view class="feature-grid">
			<view class="feature-item">
				<view class="feature-icon icon-blue">年</view>
				<text class="feature-text">包年专区</text>
			</view>
			<view class="feature-item">
				<view class="feature-icon icon-orange">
					<text class="feature-icon-chart"></text>
				</view>
				<text class="feature-text">我要拉新</text>
			</view>
			<view class="feature-item">
				<view class="feature-icon icon-green">
					<text class="feature-icon-heart"></text>
				</view>
				<text class="feature-text">服务运营</text>
			</view>
			<view class="feature-item">
				<view class="feature-icon icon-red">
					<text class="feature-icon-user"></text>
				</view>
				<text class="feature-text">招工招生</text>
			</view>
			<view class="feature-item">
				<view class="feature-icon icon-purple">
					<text class="feature-icon-doc"></text>
				</view>
				<text class="feature-text">轻享学堂</text>
			</view>
		</view>
		
		<!-- 任务区域 -->
		<view class="task-section">
			<view class="section-header">
				<text class="section-title">今日任务</text>
				<text class="login-text">请登录</text>
			</view>
			
			<view class="task-tabs">
				<view class="task-tab active">
					<text class="tab-slash">/</text>
					<text class="tab-text">上单任务</text>
				</view>
				<view class="task-tab">
					<text class="tab-slash">/</text>
					<text class="tab-text">拉新任务</text>
				</view>
				<view class="task-tab">
					<text class="tab-slash">/</text>
					<text class="tab-text">学习任务</text>
				</view>
			</view>
		</view>
		
		<!-- 数据区域 -->
		<view class="data-section">
			<view class="data-nav">
				<view class="data-nav-item active">服务数据</view>
				<view class="data-nav-item">运营数据</view>
				<view class="data-nav-item">拉新数据</view>
			</view>
			
			<view class="monthly-data">
				<view class="monthly-header">
					<text class="monthly-title">本月数据</text>
					<text class="see-all">查看全部 ></text>
				</view>
				
				<view class="data-cards">
					<view class="data-card blue-bg">
						<text class="card-slash">/</text>
						<text class="card-text">上单总数</text>
					</view>
					<view class="data-card green-bg">
						<text class="card-slash">/</text>
						<text class="card-text">产能供应天数</text>
					</view>
					<view class="data-card purple-bg">
						<text class="card-slash">/</text>
						<text class="card-text">总上单数</text>
					</view>
				</view>
				
				<view class="time-card">
					<view class="time-content">
						<text class="time-label">累计时长</text>
						<text class="time-slash">/</text>
						<text class="time-value">时</text>
					</view>
					<image class="time-icon" src="/static/img/clock.png"></image>
				</view>
			</view>
		</view>
		
		<!-- 底部导航 -->
		<view class="footer">
			<view class="footer-item active">
				<view class="footer-icon home-icon"></view>
				<text class="footer-text">首页</text>
			</view>
			<view class="footer-item">
				<view class="footer-icon order-icon"></view>
				<text class="footer-text">我的工单</text>
			</view>
			<view class="footer-item">
				<view class="footer-icon message-icon"></view>
				<text class="footer-text">消息通知</text>
			</view>
			<view class="footer-item">
				<view class="footer-icon profile-icon"></view>
				<text class="footer-text">个人中心</text>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				currentTab: 0,
				isload: true
			}
		},
		onLoad() {
			// 页面加载初始化
		},
		methods: {
			switchTab(index) {
				this.currentTab = index;
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			}
		}
	}
</script>

<style>
	page {
		background-color: #f7f8fc;
		font-family: PingFang SC, Helvetica Neue, Helvetica, sans-serif;
	}
	
	.main-container {
		width: 100%;
		padding-bottom: 110rpx;
	}
	
	/* 顶部导航 */
	.header {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 88rpx;
		position: relative;
		padding: 0 30rpx;
	}
	
	.page-title {
		font-size: 34rpx;
		font-weight: 500;
		color: #333;
	}
	
	.header-right {
		position: absolute;
		right: 30rpx;
		display: flex;
		align-items: center;
	}
	
	.header-dot {
		font-size: 30rpx;
		margin-right: 20rpx;
	}
	
	.header-circle {
		font-size: 40rpx;
	}
	
	/* 横幅区域 */
	.banner {
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	}
	
	.banner-image {
		width: 100%;
		height: 320rpx;
		border-radius: 12rpx;
	}
	
	/* 功能图标区 */
	.feature-grid {
		display: flex;
		justify-content: space-between;
		padding: 0 30rpx 30rpx;
	}
	
	.feature-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.feature-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
		color: white;
		margin-bottom: 10rpx;
	}
	
	.icon-blue { background-color: #4A7BF7; }
	.icon-orange { background-color: #FF9642; }
	.icon-green { background-color: #30C77E; }
	.icon-red { background-color: #FF6A6A; }
	.icon-purple { background-color: #8A6BF7; }
	
	.feature-text {
		font-size: 24rpx;
		color: #333;
	}
	
	/* 任务区域 */
	.task-section {
		padding: 20rpx 30rpx;
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}
	
	.login-text {
		font-size: 26rpx;
		color: #FF6A6A;
	}
	
	.task-tabs {
		display: flex;
		justify-content: space-between;
	}
	
	.task-tab {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
	}
	
	.tab-slash {
		font-size: 36rpx;
		color: #ddd;
		margin-bottom: 10rpx;
	}
	
	.tab-text {
		font-size: 28rpx;
		color: #666;
	}
	
	.task-tab.active .tab-text {
		font-weight: bold;
		color: #333;
	}
	
	/* 数据区域 */
	.data-section {
		padding: 30rpx 20rpx;
	}
	
	.data-nav {
		display: flex;
		margin-bottom: 20rpx;
	}
	
	.data-nav-item {
		padding: 15rpx 30rpx;
		font-size: 28rpx;
		color: #666;
		position: relative;
	}
	
	.data-nav-item.active {
		color: #4A7BF7;
		font-weight: bold;
	}
	
	.data-nav-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #4A7BF7;
		border-radius: 2rpx;
	}
	
	.monthly-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.monthly-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}
	
	.see-all {
		font-size: 24rpx;
		color: #999;
	}
	
	.data-cards {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}
	
	.data-card {
		width: 30%;
		height: 160rpx;
		border-radius: 12rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	
	.blue-bg { background-color: #EDF2FF; }
	.green-bg { background-color: #EBFBF2; }
	.purple-bg { background-color: #F4EFFF; }
	
	.card-slash {
		font-size: 38rpx;
		color: #999;
		margin-bottom: 15rpx;
	}
	
	.card-text {
		font-size: 26rpx;
		color: #666;
	}
	
	.time-card {
		background-color: #FFF4EB;
		border-radius: 12rpx;
		padding: 25rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.time-content {
		display: flex;
		align-items: center;
	}
	
	.time-label {
		font-size: 28rpx;
		color: #FF9642;
	}
	
	.time-slash {
		font-size: 32rpx;
		color: #FF9642;
		margin: 0 10rpx;
	}
	
	.time-value {
		font-size: 28rpx;
		color: #FF9642;
	}
	
	.time-icon {
		width: 60rpx;
		height: 60rpx;
	}
	
	/* 底部导航 */
	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 110rpx;
		background-color: white;
		border-top: 1rpx solid #eee;
		display: flex;
		justify-content: space-around;
	}
	
	.footer-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 15rpx 0;
	}
	
	.footer-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 6rpx;
		background-color: #ddd; /* 临时替代图标 */
	}
	
	.footer-text {
		font-size: 24rpx;
		color: #999;
	}
	
	.footer-item.active .footer-text {
		color: #4A7BF7;
	}
	
	.home-icon {
		background-color: #4A7BF7;
	}
</style>
