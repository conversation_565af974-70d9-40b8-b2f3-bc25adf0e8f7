<template>
	<view class="container">
		<!-- 粒子背景容器 -->
		<canvas canvas-id="particlesCanvas" class="particles-canvas" 
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"></canvas>
		
		<!-- 装饰性背景 -->
		<view class="bg-grid"></view>
		<view class="bg-circles"></view>
		
		<view class="voice-chat-console">
			<!-- 标题区域 -->
			<view class="header-section">
				<view class="connection-status">
					<view class="status-indicator" :class="{connected: isConnected}">
						<view class="status-dot"></view>
						<text class="status-text">{{connectionStatus}}</text>
					</view>
				</view>
				<view class="title-info">
					<text class="main-title" :style="{color:t('color1')}">时空语音通话</text>
					<text class="subtitle">与20年后的自己对话</text>
				</view>
				<view class="time-display">
					<text class="year">2049</text>
					<text class="date">{{currentDate}}</text>
				</view>
			</view>
			
			<!-- 全息头像区域 -->
			<view class="hologram-section">
				<view class="hologram-container">
					<view class="hologram-avatar" :class="{speaking: isAISpeaking}">
						<image v-if="futureAvatarUrl" :src="futureAvatarUrl" class="avatar-image" mode="aspectFit"></image>
						<view v-else class="default-avatar">
							<text class="avatar-icon">🤖</text>
						</view>
						
						<!-- 全息特效 */
						<view class="hologram-effects">
							<view class="scan-lines"></view>
							<view class="energy-field"></view>
							<view class="data-particles"></view>
						</view>
						
						<!-- 语音波纹 -->
						<view v-if="isAISpeaking" class="voice-waves">
							<view class="wave wave-1"></view>
							<view class="wave wave-2"></view>
							<view class="wave wave-3"></view>
						</view>
					</view>
					
					<view class="avatar-info">
						<text class="avatar-name" :style="{color:t('color1')}">{{userName}} (2049年)</text>
						<text class="avatar-status">{{avatarStatus}}</text>
					</view>
				</view>
			</view>
			
			<!-- 对话记录区域 -->
			<view class="dialogue-history">
				<view class="history-header">
					<text class="header-icon">💬</text>
					<text class="header-text">对话记录</text>
					<view class="clear-btn" @tap="clearHistory">
						<text class="clear-icon">🗑️</text>
					</view>
				</view>
				
				<scroll-view class="messages-list" scroll-y="true" :scroll-top="scrollTop">
					<view v-for="(message, index) in messages" :key="index" 
						  :class="['message-item', message.type === 'user' ? 'user-message' : 'ai-message']">
						<view class="message-time">
							<text>{{message.time}}</text>
						</view>
						<view class="message-content">
							<text class="message-text">{{message.text}}</text>
							<view v-if="message.type === 'ai'" class="replay-btn" @tap="replayMessage(index)">
								<text class="replay-icon">🔊</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
			
			<!-- 语音控制区域 -->
			<view class="voice-controls">
				<view class="voice-input-section">
					<!-- 语音输入按钮 -->
					<view class="voice-btn" 
						  :class="{recording: isRecording, disabled: !isConnected}" 
						  @touchstart="startRecording" 
						  @touchend="stopRecording">
						<view class="btn-glow" :class="{active: isRecording}"></view>
						<text class="voice-icon">{{isRecording ? '🎙️' : '🎤'}}</text>
						<text class="voice-text">{{voiceButtonText}}</text>
						
						<!-- 录音波纹 -->
						<view v-if="isRecording" class="recording-waves">
							<view class="recording-wave recording-wave-1"></view>
							<view class="recording-wave recording-wave-2"></view>
							<view class="recording-wave recording-wave-3"></view>
						</view>
					</view>
					
					<!-- 语音状态提示 -->
					<view class="voice-status">
						<text class="status-text">{{voiceStatus}}</text>
					</view>
				</view>
				
				<!-- 控制按钮组 -->
				<view class="control-buttons">
					<view class="control-btn" @tap="toggleMute">
						<text class="btn-icon">{{isMuted ? '🔇' : '🔊'}}</text>
						<text class="btn-text">{{isMuted ? '取消静音' : '静音'}}</text>
					</view>
					<view class="control-btn" @tap="showSettings">
						<text class="btn-icon">⚙</text>
						<text class="btn-text">设置</text>
					</view>
					<view class="control-btn" @tap="endCall">
						<text class="btn-icon">📞</text>
						<text class="btn-text">结束通话</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 设置模态框 -->
		<view v-if="showSettingsModal" class="modal" @tap="hideSettings">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">⚙ 语音设置</text>
					<view class="modal-close" @tap="hideSettings">
						<text class="close-icon">×</text>
					</view>
				</view>
				<view class="modal-body">
					<view class="setting-item">
						<text class="setting-label">语言设置:</text>
						<picker @change="onLanguageChange" :value="languageIndex" :range="languageOptions">
							<view class="picker-input">
								<text>{{languageOptions[languageIndex]}}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</view>
					<view class="setting-item">
						<text class="setting-label">语速设置:</text>
						<slider @change="onSpeedChange" :value="speechSpeed" min="0.5" max="2" step="0.1" 
								activeColor="#00f7ff" backgroundColor="rgba(255,255,255,0.3)" />
						<text class="speed-value">{{speechSpeed}}x</text>
					</view>
					<view class="setting-item">
						<text class="setting-label">音效开关:</text>
						<switch @change="onSoundEffectChange" :checked="soundEffectEnabled" 
								color="#00f7ff" />
					</view>
				</view>
				<view class="modal-footer">
					<view class="modal-btn" @tap="saveSettings">
						<text class="btn-icon">💾</text>
						<text class="btn-text">保存设置</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 页脚信息 -->
		<view class="footer">
			<text class="footer-text">时空语音通话系统 v2049.1 | 量子通信加密 | 与未来自己的深度对话</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			canvasWidth: 375,
			canvasHeight: 667,
			currentDate: '',
			userName: '',
			
			// 连接状态
			isConnected: false,
			connectionStatus: '正在建立时空连接...',
			
			// 头像相关
			futureAvatarUrl: '',
			avatarStatus: '准备中...',
			isAISpeaking: false,
			
			// 语音相关
			isRecording: false,
			isMuted: false,
			voiceButtonText: '按住说话',
			voiceStatus: '点击按钮开始语音对话',
			
			// 对话记录
			messages: [],
			scrollTop: 0,
			
			// 设置相关
			showSettingsModal: false,
			languageIndex: 0,
			languageOptions: ['中文', 'English'],
			speechSpeed: 1.0,
			soundEffectEnabled: true,
			
			// 定时器
			connectionTimer: null,
			recordingTimer: null
		}
	},
	onLoad() {
		this.initCanvas();
		this.initCurrentDate();
		this.loadUserData();
		this.establishConnection();
	},
	onReady() {
		// 页面渲染完成
	},
	onUnload() {
		this.clearTimers();
		this.endCall();
	},
	methods: {
		// 初始化画布
		initCanvas() {
			const systemInfo = uni.getSystemInfoSync();
			this.canvasWidth = systemInfo.windowWidth;
			this.canvasHeight = systemInfo.windowHeight;
		},
		
		// 初始化当前日期
		initCurrentDate() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			this.currentDate = `${year}.${month}.${day}`;
		},
		
		// 加载用户数据
		loadUserData() {
			try {
				const dialogueData = uni.getStorageSync('user_dialogue_data');
				if (dialogueData && dialogueData.name) {
					this.userName = dialogueData.name;
				}
				
				const predictedImageUrl = uni.getStorageSync('predicted_image_url');
				if (predictedImageUrl) {
					this.futureAvatarUrl = predictedImageUrl;
				}
			} catch (e) {
				console.error('加载用户数据失败:', e);
			}
		},
		
		// 建立连接
		establishConnection() {
			this.connectionStatus = '正在建立时空连接...';
			this.avatarStatus = '连接中...';
			
			// 模拟连接过程
			this.connectionTimer = setTimeout(() => {
				this.isConnected = true;
				this.connectionStatus = '时空连接已建立';
				this.avatarStatus = '在线';
				this.voiceStatus = '连接成功！现在可以开始语音对话了';
				
				// 添加欢迎消息
				this.addMessage('ai', `你好${this.userName}！我是来自2049年的你。很高兴能够跨越时空与你对话。`);
			}, 3000);
		},
		
		// 开始录音
		startRecording() {
			if (!this.isConnected) return;
			
			// 检查录音权限
			uni.authorize({
				scope: 'scope.record',
				success: () => {
					this.isRecording = true;
					this.voiceButtonText = '正在录音...';
					this.voiceStatus = '正在录音，松开发送';
					
					// 开始录音
					uni.startRecord({
						success: (res) => {
							console.log('录音成功:', res);
						},
						fail: (err) => {
							console.error('录音失败:', err);
							this.stopRecording();
						}
					});
				},
				fail: () => {
					uni.showToast({
						title: '需要录音权限',
						icon: 'none'
					});
				}
			});
		},
		
		// 停止录音
		stopRecording() {
			if (!this.isRecording) return;
			
			this.isRecording = false;
			this.voiceButtonText = '按住说话';
			this.voiceStatus = '正在处理语音...';
			
			// 停止录音
			uni.stopRecord({
				success: (res) => {
					console.log('录音结束:', res);
					this.processVoiceInput(res.tempFilePath);
				},
				fail: (err) => {
					console.error('停止录音失败:', err);
					this.voiceStatus = '录音失败，请重试';
				}
			});
		},
		
		// 处理语音输入
		processVoiceInput(audioPath) {
			// 模拟语音识别
			setTimeout(() => {
				const userText = '这是模拟的语音识别结果';
				this.addMessage('user', userText);
				this.voiceStatus = '语音识别完成';
				
				// 模拟AI回复
				setTimeout(() => {
					this.generateAIResponse(userText);
				}, 1000);
			}, 2000);
		},
		
		// 生成AI回复
		generateAIResponse(userInput) {
			this.isAISpeaking = true;
			this.avatarStatus = '思考中...';
			
			// 模拟AI思考过程
			setTimeout(() => {
				const aiResponse = '感谢你的分享。作为未来的你，我想告诉你，坚持梦想是非常重要的。';
				this.addMessage('ai', aiResponse);
				this.avatarStatus = '在线';
				this.voiceStatus = '可以继续对话';
				
				// 播放AI语音
				this.playAIVoice(aiResponse);
			}, 2000);
		},
		
		// 播放AI语音
		playAIVoice(text) {
			if (this.isMuted) {
				this.isAISpeaking = false;
				return;
			}
			
			// 模拟语音播放
			setTimeout(() => {
				this.isAISpeaking = false;
			}, 3000);
		},
		
		// 添加消息
		addMessage(type, text) {
			const message = {
				type: type,
				text: text,
				time: this.getCurrentTime()
			};
			
			this.messages.push(message);
			this.scrollToBottom();
		},
		
		// 获取当前时间
		getCurrentTime() {
			const now = new Date();
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			const seconds = String(now.getSeconds()).padStart(2, '0');
			return `${hours}:${minutes}:${seconds}`;
		},
		
		// 滚动到底部
		scrollToBottom() {
			this.$nextTick(() => {
				this.scrollTop = 999999;
			});
		},
		
		// 重播消息
		replayMessage(index) {
			const message = this.messages[index];
			if (message && message.type === 'ai') {
				this.playAIVoice(message.text);
			}
		},
		
		// 清空历史记录
		clearHistory() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空所有对话记录吗？',
				success: (res) => {
					if (res.confirm) {
						this.messages = [];
						uni.showToast({
							title: '已清空对话记录',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 切换静音
		toggleMute() {
			this.isMuted = !this.isMuted;
			uni.showToast({
				title: this.isMuted ? '已静音' : '已取消静音',
				icon: 'none'
			});
		},
		
		// 显示设置
		showSettings() {
			this.showSettingsModal = true;
		},
		
		// 隐藏设置
		hideSettings() {
			this.showSettingsModal = false;
		},
		
		// 语言改变
		onLanguageChange(e) {
			this.languageIndex = e.detail.value;
		},
		
		// 语速改变
		onSpeedChange(e) {
			this.speechSpeed = parseFloat(e.detail.value.toFixed(1));
		},
		
		// 音效开关改变
		onSoundEffectChange(e) {
			this.soundEffectEnabled = e.detail.value;
		},
		
		// 保存设置
		saveSettings() {
			try {
				const settings = {
					language: this.languageOptions[this.languageIndex],
					speechSpeed: this.speechSpeed,
					soundEffectEnabled: this.soundEffectEnabled
				};
				uni.setStorageSync('voice_chat_settings', settings);
				
				uni.showToast({
					title: '设置已保存',
					icon: 'success'
				});
				
				this.hideSettings();
			} catch (e) {
				uni.showToast({
					title: '保存失败',
					icon: 'error'
				});
			}
		},
		
		// 结束通话
		endCall() {
			uni.showModal({
				title: '结束通话',
				content: '确定要结束与未来自己的对话吗？',
				success: (res) => {
					if (res.confirm) {
						this.isConnected = false;
						this.connectionStatus = '通话已结束';
						this.avatarStatus = '离线';
						
						// 跳转到结束页面
						setTimeout(() => {
							uni.navigateTo({
								url: '/pagesB/dreamark/ending'
							});
						}, 1000);
					}
				}
			});
		},
		
		// 清除定时器
		clearTimers() {
			if (this.connectionTimer) {
				clearTimeout(this.connectionTimer);
				this.connectionTimer = null;
			}
			if (this.recordingTimer) {
				clearTimeout(this.recordingTimer);
				this.recordingTimer = null;
			}
		}
	}
}
</script>

<style>
/* 基础样式 */
page {
	background: #0a0a2a;
	color: #ffffff;
	font-family: 'PingFang SC', sans-serif;
}

.container {
	position: relative;
	min-height: 100vh;
	background: linear-gradient(135deg, #0a0a2a 0%, #1a1a3a 30%, #2a2a4a 70%, #1a1a3a 100%);
	overflow: hidden;
	/* 添加微妙的纹理效果 */
	background-image:
		radial-gradient(circle at 30% 70%, rgba(0, 247, 255, 0.02) 0%, transparent 50%),
		radial-gradient(circle at 70% 30%, rgba(189, 0, 255, 0.02) 0%, transparent 50%);
}

/* 粒子画布 */
.particles-canvas {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1;
	pointer-events: none;
}

/* 装饰背景 */
.bg-grid {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: 
		linear-gradient(rgba(0, 247, 255, 0.1) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 247, 255, 0.1) 1px, transparent 1px);
	background-size: 50px 50px;
	z-index: 2;
	animation: gridMove 20s linear infinite;
}

.bg-circles {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 2;
}

@keyframes gridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(50px, 50px); }
}

/* 语音通话控制台 */
.voice-chat-console {
	position: relative;
	z-index: 10;
	padding: 40rpx;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

/* 标题区域 */
.header-section {
	display: flex;
	align-items: center;
	gap: 30rpx;
	margin-bottom: 40rpx;
	padding: 30rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
}

.connection-status {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 10rpx;
	background: rgba(255, 68, 68, 0.1);
	border: 1px solid #ff4444;
	border-radius: 50rpx;
	padding: 10rpx 20rpx;
	transition: all 0.3s ease;
}

.status-indicator.connected {
	background: rgba(0, 255, 128, 0.1);
	border-color: #00ff80;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	background: #ff4444;
	border-radius: 50%;
	animation: statusBlink 1s infinite;
}

.status-indicator.connected .status-dot {
	background: #00ff80;
}

@keyframes statusBlink {
	0%, 50% { opacity: 1; }
	51%, 100% { opacity: 0.3; }
}

.status-text {
	font-size: 20rpx;
	color: #ff4444;
}

.status-indicator.connected .status-text {
	color: #00ff80;
}

.title-info {
	flex: 1;
}

.main-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 8rpx;
}

.subtitle {
	display: block;
	font-size: 22rpx;
	color: #7df9ff;
}

.time-display {
	text-align: right;
}

.year {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 5rpx;
}

.date {
	display: block;
	font-size: 18rpx;
	color: #7df9ff;
}

/* 全息头像区域 */
.hologram-section {
	margin-bottom: 40rpx;
}

.hologram-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30rpx;
	padding: 40rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
}

.hologram-avatar {
	position: relative;
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	overflow: hidden;
	border: 3px solid #00f7ff;
	box-shadow: 0 0 40rpx rgba(0, 247, 255, 0.5);
}

.avatar-image {
	width: 100%;
	height: 100%;
}

.default-avatar {
	width: 100%;
	height: 100%;
	background: linear-gradient(45deg, #333, #666);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-icon {
	font-size: 80rpx;
}

/* 全息特效 */
.hologram-effects {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}

.scan-lines {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: repeating-linear-gradient(
		0deg,
		transparent,
		transparent 2px,
		rgba(0, 247, 255, 0.1) 2px,
		rgba(0, 247, 255, 0.1) 4px
	);
	animation: scanMove 2s linear infinite;
}

@keyframes scanMove {
	0% { transform: translateY(-100%); }
	100% { transform: translateY(100%); }
}

.energy-field {
	position: absolute;
	top: -20rpx;
	left: -20rpx;
	right: -20rpx;
	bottom: -20rpx;
	border: 2px solid rgba(189, 0, 255, 0.3);
	border-radius: 50%;
	animation: energyPulse 3s infinite;
}

@keyframes energyPulse {
	0%, 100% { transform: scale(1); opacity: 0.3; }
	50% { transform: scale(1.1); opacity: 0.7; }
}

.data-particles {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.data-particles::before,
.data-particles::after {
	content: '';
	position: absolute;
	width: 6rpx;
	height: 6rpx;
	background: #00f7ff;
	border-radius: 50%;
	animation: particleFloat 2s infinite;
}

.data-particles::before {
	top: 20%;
	left: 10%;
}

.data-particles::after {
	bottom: 20%;
	right: 10%;
	animation-delay: 1s;
}

@keyframes particleFloat {
	0%, 100% { opacity: 0; transform: translateY(0); }
	50% { opacity: 1; transform: translateY(-20rpx); }
}

/* 语音波纹 */
.voice-waves {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
}

.wave {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border: 2px solid rgba(0, 247, 255, 0.6);
	border-radius: 50%;
	animation: waveExpand 1.5s infinite;
}

.wave-1 {
	width: 120%;
	height: 120%;
}

.wave-2 {
	width: 140%;
	height: 140%;
	animation-delay: 0.5s;
}

.wave-3 {
	width: 160%;
	height: 160%;
	animation-delay: 1s;
}

@keyframes waveExpand {
	0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.8; }
	100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
}

.avatar-info {
	text-align: center;
}

.avatar-name {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 10rpx;
}

.avatar-status {
	display: block;
	font-size: 22rpx;
	color: #7df9ff;
}

/* 对话记录区域 */
.dialogue-history {
	flex: 1;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	margin-bottom: 40rpx;
	display: flex;
	flex-direction: column;
}

.history-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 30rpx;
	border-bottom: 1px solid rgba(0, 247, 255, 0.2);
}

.header-icon {
	font-size: 28rpx;
}

.header-text {
	flex: 1;
	font-size: 26rpx;
	color: #7df9ff;
}

.clear-btn {
	width: 50rpx;
	height: 50rpx;
	background: rgba(255, 68, 68, 0.1);
	border: 1px solid #ff4444;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.clear-icon {
	font-size: 20rpx;
	color: #ff4444;
}

.messages-list {
	flex: 1;
	padding: 30rpx;
	max-height: 400rpx;
}

.message-item {
	margin-bottom: 30rpx;
}

.user-message {
	text-align: right;
}

.message-time {
	font-size: 18rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.message-content {
	display: inline-flex;
	align-items: flex-end;
	gap: 15rpx;
	max-width: 70%;
}

.user-message .message-content {
	flex-direction: row-reverse;
}

.message-text {
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	padding: 20rpx 25rpx;
	font-size: 26rpx;
	color: #7df9ff;
	line-height: 1.4;
}

.user-message .message-text {
	background: rgba(255, 0, 200, 0.1);
	border-color: rgba(255, 0, 200, 0.3);
	color: #ffb3e6;
}

.replay-btn {
	width: 50rpx;
	height: 50rpx;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid #00f7ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.replay-icon {
	font-size: 20rpx;
	color: #00f7ff;
}

/* 语音控制区域 */
.voice-controls {
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	padding: 40rpx;
}

.voice-input-section {
	text-align: center;
	margin-bottom: 40rpx;
}

.voice-btn {
	position: relative;
	width: 200rpx;
	height: 200rpx;
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	border-radius: 50%;
	display: inline-flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	margin: 0 auto 30rpx;
	transition: all 0.3s ease;
	overflow: hidden;
}

.voice-btn.recording {
	background: linear-gradient(45deg, #ff00c8, #ff4444);
	animation: recordingPulse 1s infinite;
}

.voice-btn.disabled {
	opacity: 0.5;
	background: #666;
}

@keyframes recordingPulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.05); }
}

.btn-glow {
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.2), transparent);
	opacity: 0;
	transition: all 0.3s ease;
}

.btn-glow.active {
	opacity: 1;
	animation: glowPulse 1s infinite;
}

@keyframes glowPulse {
	0%, 100% { transform: scale(0.8); }
	50% { transform: scale(1.2); }
}

.voice-icon {
	font-size: 48rpx;
	color: #fff;
}

.voice-text {
	font-size: 24rpx;
	color: #fff;
	font-weight: bold;
}

/* 录音波纹 */
.recording-waves {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
}

.recording-wave {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border: 3px solid rgba(255, 255, 255, 0.6);
	border-radius: 50%;
	animation: recordingWaveExpand 1s infinite;
}

.recording-wave-1 {
	width: 110%;
	height: 110%;
}

.recording-wave-2 {
	width: 130%;
	height: 130%;
	animation-delay: 0.3s;
}

.recording-wave-3 {
	width: 150%;
	height: 150%;
	animation-delay: 0.6s;
}

@keyframes recordingWaveExpand {
	0% { transform: translate(-50%, -50%) scale(0.9); opacity: 0.8; }
	100% { transform: translate(-50%, -50%) scale(1.1); opacity: 0; }
}

.voice-status {
	text-align: center;
}

.status-text {
	font-size: 24rpx;
	color: #7df9ff;
}

/* 控制按钮组 */
.control-buttons {
	display: flex;
	gap: 20rpx;
	justify-content: space-around;
}

.control-btn {
	flex: 1;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 50rpx;
	padding: 25rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	transition: all 0.3s ease;
}

.control-btn:active {
	transform: scale(0.95);
	background: rgba(0, 247, 255, 0.2);
}

.btn-icon {
	font-size: 28rpx;
}

.btn-text {
	font-size: 20rpx;
	color: #7df9ff;
}

/* 模态框 */
.modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	backdrop-filter: blur(5px);
}

.modal-content {
	background: linear-gradient(135deg, #1a1a3a, #2a2a4a);
	border: 2px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	max-width: 600rpx;
	width: 90%;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1px solid rgba(0, 247, 255, 0.2);
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
}

.modal-close {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 68, 68, 0.1);
	border: 1px solid #ff4444;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.close-icon {
	font-size: 32rpx;
	color: #ff4444;
}

.modal-body {
	padding: 30rpx;
	max-height: 400rpx;
	overflow-y: auto;
}

/* 设置项 */
.setting-item {
	margin-bottom: 40rpx;
}

.setting-label {
	display: block;
	font-size: 28rpx;
	color: #7df9ff;
	margin-bottom: 20rpx;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #fff;
}

.picker-arrow {
	font-size: 20rpx;
	color: #7df9ff;
}

.speed-value {
	display: block;
	text-align: center;
	font-size: 24rpx;
	color: #7df9ff;
	margin-top: 15rpx;
}

.modal-footer {
	padding: 30rpx;
	border-top: 1px solid rgba(0, 247, 255, 0.2);
}

.modal-btn {
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	color: #fff;
	padding: 25rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 28rpx;
	font-weight: bold;
}

/* 页脚 */
.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx;
	background: rgba(0, 0, 0, 0.8);
	border-top: 1px solid rgba(0, 247, 255, 0.3);
	z-index: 100;
}

.footer-text {
	text-align: center;
	font-size: 20rpx;
	color: #7df9ff;
	line-height: 1.4;
}
</style>
