<template>
	<view class="uni-calendar">
		<view v-if="!insert&&show" class="uni-calendar__mask" :class="{'uni-calendar--mask-show':aniMaskShow}" @click="clean"></view>
		<view v-if="insert || show" class="uni-calendar__content" :class="{'uni-calendar--fixed':!insert,'uni-calendar--ani-show':aniMaskShow}">
			<view v-if="!insert" class="uni-calendar__header uni-calendar--fixed-top">
				<view class="uni-calendar__header-btn-box" @click="close">
					<text class="uni-calendar__header-text uni-calendar--fixed-width">{{cancelText}}</text>
				</view>
				<view class="uni-calendar__header-btn-box" @click="confirm">
					<text class="uni-calendar__header-text uni-calendar--fixed-width">{{okText}}</text>
				</view>
			</view>
			<view class="uni-calendar__header">
				<view class="uni-calendar__header-btn-box" @click.stop="pre">
					<view class="uni-calendar__header-btn uni-calendar--left"></view>
				</view>
				<picker mode="date" :value="date" fields="month" @change="bindDateChange">
					<text class="uni-calendar__header-text">{{ (nowDate.year||'') +' / '+( nowDate.month||'')}}</text>
				</picker>
				<view class="uni-calendar__header-btn-box" @click.stop="next">
					<view class="uni-calendar__header-btn uni-calendar--right"></view>
				</view>
				<!-- <text class="uni-calendar__backtoday" @click="backtoday">{{todayText}}</text> -->

			</view>
			<view class="uni-calendar__box">
				<view v-if="showMonth" class="uni-calendar__box-bg">
					<text class="uni-calendar__box-bg-text">{{nowDate.month}}</text>
				</view>
				<view class="uni-calendar__weeks">
					<view class="uni-calendar__weeks-day">
						<text class="uni-calendar__weeks-day-text">{{SUNText}}</text>
					</view>
					<view class="uni-calendar__weeks-day">
						<text class="uni-calendar__weeks-day-text">{{monText}}</text>
					</view>
					<view class="uni-calendar__weeks-day">
						<text class="uni-calendar__weeks-day-text">{{TUEText}}</text>
					</view>
					<view class="uni-calendar__weeks-day">
						<text class="uni-calendar__weeks-day-text">{{WEDText}}</text>
					</view>
					<view class="uni-calendar__weeks-day">
						<text class="uni-calendar__weeks-day-text">{{THUText}}</text>
					</view>
					<view class="uni-calendar__weeks-day">
						<text class="uni-calendar__weeks-day-text">{{FRIText}}</text>
					</view>
					<view class="uni-calendar__weeks-day">
						<text class="uni-calendar__weeks-day-text">{{SATText}}</text>
					</view>
				</view>
				<view class="uni-calendar__weeks" v-for="(item,weekIndex) in weeks" :key="weekIndex">
					<view class="uni-calendar__weeks-item" v-for="(weeks,weeksIndex) in item" :key="weeksIndex">
						<calendar-item class="uni-calendar-item--hook" :weeks="weeks" :calendar="calendar" :selected="selected" :lunar="lunar" :backColor= "backColor" :fontColor= "fontColor" @change="choiceDate"></calendar-item>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var CALENDAR = {

		/**
				* 农历1900-2100的润大小信息表
				* @Array Of Property
				* @return Hex
				*/
		lunarInfo: [0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, // 1900-1909
			0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, // 1910-1919
			0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, // 1920-1929
			0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, // 1930-1939
			0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, // 1940-1949
			0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0, // 1950-1959
			0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, // 1960-1969
			0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6, // 1970-1979
			0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, // 1980-1989
			0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x05ac0, 0x0ab60, 0x096d5, 0x092e0, // 1990-1999
			0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, // 2000-2009
			0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, // 2010-2019
			0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, // 2020-2029
			0x05aa0, 0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, // 2030-2039
			0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, // 2040-2049
			/** <NAME_EMAIL>**/
			0x14b63, 0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, // 2050-2059
			0x0a2e0, 0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4, // 2060-2069
			0x052d0, 0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, // 2070-2079
			0x0b273, 0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, // 2080-2089
			0x0e968, 0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, // 2090-2099
			0x0d520], // 2100

		/**
				* 公历每个月份的天数普通表
				* @Array Of Property
				* @return Number
				*/
		solarMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],

		/**
				* 天干地支之天干速查表
				* @Array Of Property trans["甲","乙","丙","丁","戊","己","庚","辛","壬","癸"]
				* @return Cn string
				*/
		Gan: ['\u7532', '\u4e59', '\u4e19', '\u4e01', '\u620a', '\u5df1', '\u5e9a', '\u8f9b', '\u58ec', '\u7678'],

		/**
				* 天干地支之地支速查表
				* @Array Of Property
				* @trans["子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"]
				* @return Cn string
				*/
		Zhi: ['\u5b50', '\u4e11', '\u5bc5', '\u536f', '\u8fb0', '\u5df3', '\u5348', '\u672a', '\u7533', '\u9149', '\u620c', '\u4ea5'],

		/**
				* 天干地支之地支速查表<=>生肖
				* @Array Of Property
				* @trans["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"]
				* @return Cn string
				*/
		Animals: ['\u9f20', '\u725b', '\u864e', '\u5154', '\u9f99', '\u86c7', '\u9a6c', '\u7f8a', '\u7334', '\u9e21', '\u72d7', '\u732a'],

		/**
				* 24节气速查表
				* @Array Of Property
				* @trans["小寒","大寒","立春","雨水","惊蛰","春分","清明","谷雨","立夏","小满","芒种","夏至","小暑","大暑","立秋","处暑","白露","秋分","寒露","霜降","立冬","小雪","大雪","冬至"]
				* @return Cn string
				*/
		solarTerm: ['\u5c0f\u5bd2', '\u5927\u5bd2', '\u7acb\u6625', '\u96e8\u6c34', '\u60ca\u86f0', '\u6625\u5206', '\u6e05\u660e', '\u8c37\u96e8', '\u7acb\u590f', '\u5c0f\u6ee1', '\u8292\u79cd', '\u590f\u81f3', '\u5c0f\u6691', '\u5927\u6691', '\u7acb\u79cb', '\u5904\u6691', '\u767d\u9732', '\u79cb\u5206', '\u5bd2\u9732', '\u971c\u964d', '\u7acb\u51ac', '\u5c0f\u96ea', '\u5927\u96ea', '\u51ac\u81f3'],

		/**
				* 1900-2100各年的24节气日期速查表
				* @Array Of Property
				* @return 0x string For splice
				*/
		sTermInfo: ['9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f',
			'97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
			'97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa',
			'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f',
			'b027097bd097c36b0b6fc9274c91aa', '9778397bd19801ec9210c965cc920e', '97b6b97bd19801ec95f8c965cc920f',
			'97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd197c36c9210c9274c91aa',
			'97b6b97bd19801ec95f8c965cc920e', '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2',
			'9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e', '97bcf97c3598082c95f8e1cfcc920f',
			'97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e',
			'97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
			'97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',
			'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',
			'97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
			'97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
			'97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd097bd07f595b0b6fc920fb0722',
			'9778397bd097c36b0b6fc9210c8dc2', '9778397bd19801ec9210c9274c920e', '97b6b97bd19801ec95f8c965cc920f',
			'97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c920e',
			'97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',
			'9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bd07f1487f595b0b0bc920fb0722',
			'7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
			'97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
			'97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
			'9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f531b0b0bb0b6fb0722',
			'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
			'97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
			'97b6b97bd19801ec9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',
			'9778397bd097c36b0b6fc9210c91aa', '97b6b97bd197c36c9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722',
			'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c920e',
			'97b6b7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',
			'9778397bd097c36b0b70c9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e37f1487f595b0b0bb0b6fb0722',
			'7f0e397bd097c35b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',
			'7f0e27f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
			'97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
			'9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
			'7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',
			'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
			'97b6b7f0e47f531b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',
			'9778397bd097c36b0b6fc9210c91aa', '97b6b7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',
			'7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '977837f0e37f149b0723b0787b0721',
			'7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722', '7f0e397bd097c35b0b6fc9210c8dc2',
			'977837f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e37f1487f595b0b0bb0b6fb0722',
			'7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
			'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '977837f0e37f14998082b0787b06bd',
			'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
			'977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
			'7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
			'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd',
			'7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',
			'977837f0e37f14998082b0723b06bd', '7f07e7f0e37f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',
			'7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721',
			'7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f595b0b0bb0b6fb0722', '7f0e37f0e37f14898082b0723b02d5',
			'7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f531b0b0bb0b6fb0722',
			'7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
			'7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd',
			'7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35',
			'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
			'7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f149b0723b0787b0721',
			'7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0723b06bd',
			'7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722', '7f0e37f0e366aa89801eb072297c35',
			'7ec967f0e37f14998082b0723b06bd', '7f07e7f0e37f14998083b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',
			'7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14898082b0723b02d5', '7f07e7f0e37f14998082b0787b0721',
			'7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66aa89801e9808297c35', '665f67f0e37f14898082b0723b02d5',
			'7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66a449801e9808297c35',
			'665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
			'7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd',
			'7f07e7f0e47f531b0723b0b6fb0721', '7f0e26665b66a449801e9808297c35', '665f67f0e37f1489801eb072297c35',
			'7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722'],

		/**
				* 数字转中文速查表
				* @Array Of Property
				* @trans ['日','一','二','三','四','五','六','七','八','九','十']
				* @return Cn string
				*/
		nStr1: ['\u65e5', '\u4e00', '\u4e8c', '\u4e09', '\u56db', '\u4e94', '\u516d', '\u4e03', '\u516b', '\u4e5d', '\u5341'],

		/**
				* 日期转农历称呼速查表
				* @Array Of Property
				* @trans ['初','十','廿','卅']
				* @return Cn string
				*/
		nStr2: ['\u521d', '\u5341', '\u5eff', '\u5345'],

		/**
				* 月份转农历称呼速查表
				* @Array Of Property
				* @trans ['正','一','二','三','四','五','六','七','八','九','十','冬','腊']
				* @return Cn string
				*/
		nStr3: ['\u6b63', '\u4e8c', '\u4e09', '\u56db', '\u4e94', '\u516d', '\u4e03', '\u516b', '\u4e5d', '\u5341', '\u51ac', '\u814a'],

		/**
				* 返回农历y年一整年的总天数
				* @param lunar Year
				* @return Number
				* @eg:var count = calendar.lYearDays(1987) ;//count=387
				*/
		lYearDays: function (y) {
			var i; var sum = 348
			for (i = 0x8000; i > 0x8; i >>= 1) { sum += (this.lunarInfo[y - 1900] & i) ? 1 : 0 }
			return (sum + this.leapDays(y))
		},

		/**
				* 返回农历y年闰月是哪个月；若y年没有闰月 则返回0
				* @param lunar Year
				* @return Number (0-12)
				* @eg:var leapMonth = calendar.leapMonth(1987) ;//leapMonth=6
				*/
		leapMonth: function (y) { // 闰字编码 \u95f0
			return (this.lunarInfo[y - 1900] & 0xf)
		},

		/**
				* 返回农历y年闰月的天数 若该年没有闰月则返回0
				* @param lunar Year
				* @return Number (0、29、30)
				* @eg:var leapMonthDay = calendar.leapDays(1987) ;//leapMonthDay=29
				*/
		leapDays: function (y) {
			if (this.leapMonth(y)) {
				return ((this.lunarInfo[y - 1900] & 0x10000) ? 30 : 29)
			}
			return (0)
		},

		/**
				* 返回农历y年m月（非闰月）的总天数，计算m为闰月时的天数请使用leapDays方法
				* @param lunar Year
				* @return Number (-1、29、30)
				* @eg:var MonthDay = calendar.monthDays(1987,9) ;//MonthDay=29
				*/
		monthDays: function (y, m) {
			if (m > 12 || m < 1) { return -1 }// 月份参数从1至12，参数错误返回-1
			return ((this.lunarInfo[y - 1900] & (0x10000 >> m)) ? 30 : 29)
		},

		/**
				* 返回公历(!)y年m月的天数
				* @param solar Year
				* @return Number (-1、28、29、30、31)
				* @eg:var solarMonthDay = calendar.leapDays(1987) ;//solarMonthDay=30
				*/
		solarDays: function (y, m) {
			if (m > 12 || m < 1) { return -1 } // 若参数错误 返回-1
			var ms = m - 1
			if (ms == 1) { // 2月份的闰平规律测算后确认返回28或29
				return (((y % 4 == 0) && (y % 100 != 0) || (y % 400 == 0)) ? 29 : 28)
			} else {
				return (this.solarMonth[ms])
			}
		},

		/**
			 * 农历年份转换为干支纪年
			 * @param  lYear 农历年的年份数
			 * @return Cn string
			 */
		toGanZhiYear: function (lYear) {
			var ganKey = (lYear - 3) % 10
			var zhiKey = (lYear - 3) % 12
			if (ganKey == 0) ganKey = 10// 如果余数为0则为最后一个天干
			if (zhiKey == 0) zhiKey = 12// 如果余数为0则为最后一个地支
			return this.Gan[ganKey - 1] + this.Zhi[zhiKey - 1]
		},

		/**
			 * 公历月、日判断所属星座
			 * @param  cMonth [description]
			 * @param  cDay [description]
			 * @return Cn string
			 */
		toAstro: function (cMonth, cDay) {
			var s = '\u9b54\u7faf\u6c34\u74f6\u53cc\u9c7c\u767d\u7f8a\u91d1\u725b\u53cc\u5b50\u5de8\u87f9\u72ee\u5b50\u5904\u5973\u5929\u79e4\u5929\u874e\u5c04\u624b\u9b54\u7faf'
			var arr = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22]
			return s.substr(cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0), 2) + '\u5ea7'// 座
		},

		/**
				* 传入offset偏移量返回干支
				* @param offset 相对甲子的偏移量
				* @return Cn string
				*/
		toGanZhi: function (offset) {
			return this.Gan[offset % 10] + this.Zhi[offset % 12]
		},

		/**
				* 传入公历(!)y年获得该年第n个节气的公历日期
				* @param y公历年(1900-2100)；n二十四节气中的第几个节气(1~24)；从n=1(小寒)算起
				* @return day Number
				* @eg:var _24 = calendar.getTerm(1987,3) ;//_24=4;意即1987年2月4日立春
				*/
		getTerm: function (y, n) {
			if (y < 1900 || y > 2100) { return -1 }
			if (n < 1 || n > 24) { return -1 }
			var _table = this.sTermInfo[y - 1900]
			var _info = [
				parseInt('0x' + _table.substr(0, 5)).toString(),
				parseInt('0x' + _table.substr(5, 5)).toString(),
				parseInt('0x' + _table.substr(10, 5)).toString(),
				parseInt('0x' + _table.substr(15, 5)).toString(),
				parseInt('0x' + _table.substr(20, 5)).toString(),
				parseInt('0x' + _table.substr(25, 5)).toString()
			]
			var _calday = [
				_info[0].substr(0, 1),
				_info[0].substr(1, 2),
				_info[0].substr(3, 1),
				_info[0].substr(4, 2),

				_info[1].substr(0, 1),
				_info[1].substr(1, 2),
				_info[1].substr(3, 1),
				_info[1].substr(4, 2),

				_info[2].substr(0, 1),
				_info[2].substr(1, 2),
				_info[2].substr(3, 1),
				_info[2].substr(4, 2),

				_info[3].substr(0, 1),
				_info[3].substr(1, 2),
				_info[3].substr(3, 1),
				_info[3].substr(4, 2),

				_info[4].substr(0, 1),
				_info[4].substr(1, 2),
				_info[4].substr(3, 1),
				_info[4].substr(4, 2),

				_info[5].substr(0, 1),
				_info[5].substr(1, 2),
				_info[5].substr(3, 1),
				_info[5].substr(4, 2)
			]
			return parseInt(_calday[n - 1])
		},

		/**
				* 传入农历数字月份返回汉语通俗表示法
				* @param lunar month
				* @return Cn string
				* @eg:var cnMonth = calendar.toChinaMonth(12) ;//cnMonth='腊月'
				*/
		toChinaMonth: function (m) { // 月 => \u6708
			if (m > 12 || m < 1) { return -1 } // 若参数错误 返回-1
			var s = this.nStr3[m - 1]
			s += '\u6708'// 加上月字
			return s
		},

		/**
				* 传入农历日期数字返回汉字表示法
				* @param lunar day
				* @return Cn string
				* @eg:var cnDay = calendar.toChinaDay(21) ;//cnMonth='廿一'
				*/
		toChinaDay: function (d) { // 日 => \u65e5
			var s
			switch (d) {
				case 10:
					s = '\u521d\u5341'; break
				case 20:
					s = '\u4e8c\u5341'; break
					break
				case 30:
					s = '\u4e09\u5341'; break
					break
				default :
					s = this.nStr2[Math.floor(d / 10)]
					s += this.nStr1[d % 10]
			}
			return (s)
		},

		/**
				* 年份转生肖[!仅能大致转换] => 精确划分生肖分界线是“立春”
				* @param y year
				* @return Cn string
				* @eg:var animal = calendar.getAnimal(1987) ;//animal='兔'
				*/
		getAnimal: function (y) {
			return this.Animals[(y - 4) % 12]
		},

		/**
				* 传入阳历年月日获得详细的公历、农历object信息 <=>JSON
				* @param y  solar year
				* @param m  solar month
				* @param d  solar day
				* @return JSON object
				* @eg:console.log(calendar.solar2lunar(1987,11,01));
				*/
		solar2lunar: function (y, m, d) { // 参数区间1900.1.31~2100.12.31
			// 年份限定、上限
			if (y < 1900 || y > 2100) {
				return -1// undefined转换为数字变为NaN
			}
			// 公历传参最下限
			if (y == 1900 && m == 1 && d < 31) {
				return -1
			}
			// 未传参  获得当天
			if (!y) {
				var objDate = new Date()
			} else {
				var objDate = new Date(y, parseInt(m) - 1, d)
			}
			var i; var leap = 0; var temp = 0
			// 修正ymd参数
			var y = objDate.getFullYear()
			var m = objDate.getMonth() + 1
			var d = objDate.getDate()
			var offset = (Date.UTC(objDate.getFullYear(), objDate.getMonth(), objDate.getDate()) - Date.UTC(1900, 0, 31)) / 86400000
			for (i = 1900; i < 2101 && offset > 0; i++) {
				temp = this.lYearDays(i)
				offset -= temp
			}
			if (offset < 0) {
				offset += temp; i--
			}

			// 是否今天
			var isTodayObj = new Date()
			var isToday = false
			if (isTodayObj.getFullYear() == y && isTodayObj.getMonth() + 1 == m && isTodayObj.getDate() == d) {
				isToday = true
			}
			// 星期几
			var nWeek = objDate.getDay()
			var cWeek = this.nStr1[nWeek]
			// 数字表示周几顺应天朝周一开始的惯例
			if (nWeek == 0) {
				nWeek = 7
			}
			// 农历年
			var year = i
			var leap = this.leapMonth(i) // 闰哪个月
			var isLeap = false

			// 效验闰月
			for (i = 1; i < 13 && offset > 0; i++) {
				// 闰月
				if (leap > 0 && i == (leap + 1) && isLeap == false) {
					--i
					isLeap = true; temp = this.leapDays(year) // 计算农历闰月天数
				} else {
					temp = this.monthDays(year, i)// 计算农历普通月天数
				}
				// 解除闰月
				if (isLeap == true && i == (leap + 1)) { isLeap = false }
				offset -= temp
			}
			// 闰月导致数组下标重叠取反
			if (offset == 0 && leap > 0 && i == leap + 1) {
				if (isLeap) {
					isLeap = false
				} else {
					isLeap = true; --i
				}
			}
			if (offset < 0) {
				offset += temp; --i
			}
			// 农历月
			var month = i
			// 农历日
			var day = offset + 1
			// 天干地支处理
			var sm = m - 1
			var gzY = this.toGanZhiYear(year)

			// 当月的两个节气
			// bugfix-2017-7-24 11:03:38 use lunar Year Param `y` Not `year`
			var firstNode = this.getTerm(y, (m * 2 - 1))// 返回当月「节」为几日开始
			var secondNode = this.getTerm(y, (m * 2))// 返回当月「节」为几日开始

			// 依据12节气修正干支月
			var gzM = this.toGanZhi((y - 1900) * 12 + m + 11)
			if (d >= firstNode) {
				gzM = this.toGanZhi((y - 1900) * 12 + m + 12)
			}

			// 传入的日期的节气与否
			var isTerm = false
			var Term = null
			if (firstNode == d) {
				isTerm = true
				Term = this.solarTerm[m * 2 - 2]
			}
			if (secondNode == d) {
				isTerm = true
				Term = this.solarTerm[m * 2 - 1]
			}
			// 日柱 当月一日与 1900/1/1 相差天数
			var dayCyclical = Date.UTC(y, sm, 1, 0, 0, 0, 0) / 86400000 + 25567 + 10
			var gzD = this.toGanZhi(dayCyclical + d - 1)
			// 该日期所属的星座
			var astro = this.toAstro(m, d)

			return { 'lYear': year, 'lMonth': month, 'lDay': day, 'Animal': this.getAnimal(year), 'IMonthCn': (isLeap ? '\u95f0' : '') + this.toChinaMonth(month), 'IDayCn': this.toChinaDay(day), 'cYear': y, 'cMonth': m, 'cDay': d, 'gzYear': gzY, 'gzMonth': gzM, 'gzDay': gzD, 'isToday': isToday, 'isLeap': isLeap, 'nWeek': nWeek, 'ncWeek': '\u661f\u671f' + cWeek, 'isTerm': isTerm, 'Term': Term, 'astro': astro }
		},

		/**
				* 传入农历年月日以及传入的月份是否闰月获得详细的公历、农历object信息 <=>JSON
				* @param y  lunar year
				* @param m  lunar month
				* @param d  lunar day
				* @param isLeapMonth  lunar month is leap or not.[如果是农历闰月第四个参数赋值true即可]
				* @return JSON object
				* @eg:console.log(calendar.lunar2solar(1987,9,10));
				*/
		lunar2solar: function (y, m, d, isLeapMonth) { // 参数区间1900.1.31~2100.12.1
			var isLeapMonth = !!isLeapMonth
			var leapOffset = 0
			var leapMonth = this.leapMonth(y)
			var leapDay = this.leapDays(y)
			if (isLeapMonth && (leapMonth != m)) { return -1 }// 传参要求计算该闰月公历 但该年得出的闰月与传参的月份并不同
			if (y == 2100 && m == 12 && d > 1 || y == 1900 && m == 1 && d < 31) { return -1 }// 超出了最大极限值
			var day = this.monthDays(y, m)
			var _day = day
			// bugFix 2016-9-25
			// if month is leap, _day use leapDays method
			if (isLeapMonth) {
				_day = this.leapDays(y, m)
			}
			if (y < 1900 || y > 2100 || d > _day) { return -1 }// 参数合法性效验

			// 计算农历的时间差
			var offset = 0
			for (var i = 1900; i < y; i++) {
				offset += this.lYearDays(i)
			}
			var leap = 0; var isAdd = false
			for (var i = 1; i < m; i++) {
				leap = this.leapMonth(y)
				if (!isAdd) { // 处理闰月
					if (leap <= i && leap > 0) {
						offset += this.leapDays(y); isAdd = true
					}
				}
				offset += this.monthDays(y, i)
			}
			// 转换闰月农历 需补充该年闰月的前一个月的时差
			if (isLeapMonth) { offset += day }
			// 1900年农历正月一日的公历时间为1900年1月30日0时0分0秒(该时间也是本农历的最开始起始点)
			var stmap = Date.UTC(1900, 1, 30, 0, 0, 0)
			var calObj = new Date((offset + d - 31) * 86400000 + stmap)
			var cY = calObj.getUTCFullYear()
			var cM = calObj.getUTCMonth() + 1
			var cD = calObj.getUTCDate()

			return this.solar2lunar(cY, cM, cD)
		}
	}


	class Calendar {
		constructor({
			date,
			selected,
			startDate,
			endDate,
			range
		} = {}) {
			// 当前日期
			this.date = this.getDate(new Date()) // 当前初入日期
			// 打点信息
			this.selected = selected || [];
			// 范围开始
			this.startDate = startDate
			// 范围结束
			this.endDate = endDate
			this.range = range
			// 多选状态
			this.cleanMultipleStatus()
			// 每周日期
			this.weeks = {}
			// this._getWeek(this.date.fullDate)
		}
		/**
		 * 设置日期
		 * @param {Object} date
		 */
		setDate(date) {
			this.selectDate = this.getDate(date)
			this._getWeek(this.selectDate.fullDate)
		}

		/**
		 * 清理多选状态
		 */
		cleanMultipleStatus() {
			this.multipleStatus = {
				before: '',
				after: '',
				data: []
			}
		}

		/**
		 * 重置开始日期
		 */
		resetSatrtDate(startDate) {
			// 范围开始
			this.startDate = startDate

		}

		/**
		 * 重置结束日期
		 */
		resetEndDate(endDate) {
			// 范围结束
			this.endDate = endDate
		}

		/**
		 * 获取任意时间
		 */
		getDate(date, AddDayCount = 0, str = 'day') {
			if (!date) {
				date = new Date()
			}
			if (typeof date !== 'object') {
				date = date.replace(/-/g, '/')
			}
			const dd = new Date(date)
			switch (str) {
				case 'day':
					dd.setDate(dd.getDate() + AddDayCount) // 获取AddDayCount天后的日期
					break
				case 'month':
					if (dd.getDate() === 31) {
						dd.setDate(dd.getDate() + AddDayCount)
					} else {
						dd.setMonth(dd.getMonth() + AddDayCount) // 获取AddDayCount天后的日期
					}
					break
				case 'year':
					dd.setFullYear(dd.getFullYear() + AddDayCount) // 获取AddDayCount天后的日期
					break
			}
			const y = dd.getFullYear()
			const m = dd.getMonth() + 1 < 10 ? '0' + (dd.getMonth() + 1) : dd.getMonth() + 1 // 获取当前月份的日期，不足10补0
			const d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate() // 获取当前几号，不足10补0
			return {
				fullDate: y + '-' + m + '-' + d,
				year: y,
				month: m,
				date: d,
				day: dd.getDay()
			}
		}


		/**
		 * 获取上月剩余天数
		 */
		_getLastMonthDays(firstDay, full) {
			let dateArr = []
			for (let i = firstDay; i > 0; i--) {
				const beforeDate = new Date(full.year, full.month - 1, -i + 1).getDate()
				dateArr.push({
					date: beforeDate,
					month: full.month - 1,
					lunar: this.getlunar(full.year, full.month - 1, beforeDate),
					disable: true
				})
			}
			return dateArr
		}
		/**
		 * 获取本月天数
		 */
		_currentMonthDys(dateData, full) {
			let dateArr = []
			let fullDate = this.date.fullDate
			for (let i = 1; i <= dateData; i++) {
				let isinfo = false
				let nowDate = full.year + '-' + (full.month < 10 ?
					full.month : full.month) + '-' + (i < 10 ?
					'0' + i : i)
				// 是否今天
				let isDay = fullDate === nowDate
				// 获取打点信息
				let info = this.selected && this.selected.find((item) => {
					if (this.dateEqual(nowDate, item.date)) {
						return item
					}
				})

				// 日期禁用
				let disableBefore = true
				let disableAfter = true
				if (this.startDate) {
					// let dateCompBefore = this.dateCompare(this.startDate, fullDate)
					// disableBefore = this.dateCompare(dateCompBefore ? this.startDate : fullDate, nowDate)
					disableBefore = this.dateCompare(this.startDate, nowDate)
				}

				if (this.endDate) {
					// let dateCompAfter = this.dateCompare(fullDate, this.endDate)
					// disableAfter = this.dateCompare(nowDate, dateCompAfter ? this.endDate : fullDate)
					disableAfter = this.dateCompare(nowDate, this.endDate)
				}
				let multiples = this.multipleStatus.data
				let checked = false
				let multiplesStatus = -1
				if (this.range) {
					if (multiples) {
						multiplesStatus = multiples.findIndex((item) => {
							return this.dateEqual(item, nowDate)
						})
					}
					if (multiplesStatus !== -1) {
						checked = true
					}
				}
				let data = {
					fullDate: nowDate,
					year: full.year,
					date: i,
					multiple: this.range ? checked : false,
					beforeMultiple: this.dateEqual(this.multipleStatus.before, nowDate),
					afterMultiple: this.dateEqual(this.multipleStatus.after, nowDate),
					month: full.month,
					lunar: this.getlunar(full.year, full.month, i),
					disable: !(disableBefore && disableAfter),
					isDay
				}
				if (info) {
					data.extraInfo = info
				}

				dateArr.push(data)
			}
			return dateArr
		}
		/**
		 * 获取下月天数
		 */
		_getNextMonthDays(surplus, full) {
			let dateArr = []
			for (let i = 1; i < surplus + 1; i++) {
				dateArr.push({
					date: i,
					month: Number(full.month) + 1,
					lunar: this.getlunar(full.year, Number(full.month) + 1, i),
					disable: true
				})
			}
			return dateArr
		}

		/**
		 * 获取当前日期详情
		 * @param {Object} date
		 */
		getInfo(date) {
			if (!date) {
				date = new Date()
			}
			const dateInfo = this.canlender.find(item => item.fullDate === this.getDate(date).fullDate)
			return dateInfo
		}

		/**
		 * 比较时间大小
		 */
		dateCompare(startDate, endDate) {
			// 计算截止时间
			startDate = new Date(startDate.replace('-', '/').replace('-', '/'))
			// 计算详细项的截止时间
			endDate = new Date(endDate.replace('-', '/').replace('-', '/'))
			if (startDate <= endDate) {
				return true
			} else {
				return false
			}
		}

		/**
		 * 比较时间是否相等
		 */
		dateEqual(before, after) {
			// 计算截止时间
			before = new Date(before.replace('-', '/').replace('-', '/'))
			// 计算详细项的截止时间
			after = new Date(after.replace('-', '/').replace('-', '/'))
			if (before.getTime() - after.getTime() === 0) {
				return true
			} else {
				return false
			}
		}


		/**
		 * 获取日期范围内所有日期
		 * @param {Object} begin
		 * @param {Object} end
		 */
		geDateAll(begin, end) {
			var arr = []
			var ab = begin.split('-')
			var ae = end.split('-')
			var db = new Date()
			db.setFullYear(ab[0], ab[1] - 1, ab[2])
			var de = new Date()
			de.setFullYear(ae[0], ae[1] - 1, ae[2])
			var unixDb = db.getTime() - 24 * 60 * 60 * 1000
			var unixDe = de.getTime() - 24 * 60 * 60 * 1000
			for (var k = unixDb; k <= unixDe;) {
				k = k + 24 * 60 * 60 * 1000
				arr.push(this.getDate(new Date(parseInt(k))).fullDate)
			}
			return arr
		}
		/**
		 * 计算阴历日期显示
		 */
		getlunar(year, month, date) {
			return CALENDAR.solar2lunar(year, month, date)
		}
		/**
		 * 设置打点
		 */
		setSelectInfo(data, value) {
			this.selected = value
			this._getWeek(data)
		}

		/**
		 *  获取多选状态
		 */
		setMultiple(fullDate) {
			let {
				before,
				after
			} = this.multipleStatus

			if (!this.range) return
			if (before && after) {
				this.multipleStatus.before = ''
				this.multipleStatus.after = ''
				this.multipleStatus.data = []
			} else {
				if (!before) {
					this.multipleStatus.before = fullDate
				} else {
					this.multipleStatus.after = fullDate
					if (this.dateCompare(this.multipleStatus.before, this.multipleStatus.after)) {
						this.multipleStatus.data = this.geDateAll(this.multipleStatus.before, this.multipleStatus.after);
					} else {
						this.multipleStatus.data = this.geDateAll(this.multipleStatus.after, this.multipleStatus.before);
					}
				}
			}
			this._getWeek(fullDate)
		}

		/**
		 * 获取每周数据
		 * @param {Object} dateData
		 */
		_getWeek(dateData) {
			const {
				fullDate,
				year,
				month,
				date,
				day
			} = this.getDate(dateData)
			let firstDay = new Date(year, month - 1, 1).getDay()
			let currentDay = new Date(year, month, 0).getDate()
			let dates = {
				lastMonthDays: this._getLastMonthDays(firstDay, this.getDate(dateData)), // 上个月末尾几天
				currentMonthDys: this._currentMonthDys(currentDay, this.getDate(dateData)), // 本月天数
				nextMonthDays: [], // 下个月开始几天
				weeks: []
			}
			let canlender = []
			const surplus = 42 - (dates.lastMonthDays.length + dates.currentMonthDys.length)
			dates.nextMonthDays = this._getNextMonthDays(surplus, this.getDate(dateData))
			canlender = canlender.concat(dates.lastMonthDays, dates.currentMonthDys, dates.nextMonthDays)
			let weeks = {}
			// 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天
			for (let i = 0; i < canlender.length; i++) {
				if (i % 7 === 0) {
					weeks[parseInt(i / 7)] = new Array(7)
				}
				weeks[parseInt(i / 7)][i % 7] = canlender[i]
			}
			this.canlender = canlender
			this.weeks = weeks
		}

		//静态方法
		// static init(date) {
		// 	if (!this.instance) {
		// 		this.instance = new Calendar(date);
		// 	}
		// 	return this.instance;
		// }
	}

	
	//import Calendar from './util.js';

	import calendarItem from './uni-calendar-item.vue'
	import {
	initVueI18n
	} from '@dcloudio/uni-i18n'
	import messages from './i18n/index.js'
	const {	t	} = initVueI18n(messages)
	/**
	 * Calendar 日历
	 * @description 日历组件可以查看日期，选择任意范围内的日期，打点操作。常用场景如：酒店日期预订、火车机票选择购买日期、上下班打卡等
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=56
	 * @property {String} date 自定义当前时间，默认为今天
	 * @property {Boolean} lunar 显示农历
	 * @property {String} startDate 日期选择范围-开始日期
	 * @property {String} endDate 日期选择范围-结束日期
	 * @property {Boolean} range 范围选择
	 * @property {Boolean} insert = [true|false] 插入模式,默认为false
	 * 	@value true 弹窗模式
	 * 	@value false 插入模式
	 * @property {Boolean} clearDate = [true|false] 弹窗模式是否清空上次选择内容
	 * @property {Array} selected 打点，期待格式[{date: '2019-06-27', info: '签到', data: { custom: '自定义信息', name: '自定义消息头',xxx:xxx... }}]
	 * @property {Boolean} showMonth 是否选择月份为背景
	 * @event {Function} change 日期改变，`insert :ture` 时生效
	 * @event {Function} confirm 确认选择`insert :false` 时生效
	 * @event {Function} monthSwitch 切换月份时触发
	 * @example <uni-calendar :insert="true":lunar="true" :start-date="'2019-3-2'":end-date="'2019-5-20'"@change="change" />
	 */
	export default {
		components: {
			calendarItem
		},
		emits:['close','confirm','change','monthSwitch'],
		props: {
			date: {
				type: String,
				default: ''
			},
			selected: {
				type: Array,
				default () {
					return []
				}
			},
			lunar: {
				type: Boolean,
				default: false
			},
			startDate: {
				type: String,
				default: ''
			},
			endDate: {
				type: String,
				default: ''
			},
			range: {
				type: Boolean,
				default: false
			},
			insert: {
				type: Boolean,
				default: true
			},
			showMonth: {
				type: Boolean,
				default: true
			},
			clearDate: {
				type: Boolean,
				default: true
			},
			backColor: "",
			fontColor: ""
		},
		data() {
			return {
				show: false,
				weeks: [],
				calendar: {},
				nowDate: '',
				aniMaskShow: false
			}
		},
		computed:{
			/**
			 * for i18n
			 */

			okText() {
				return t("uni-calender.ok")
			},
			cancelText() {
				return t("uni-calender.cancel")
			},
			todayText() {
				return t("uni-calender.today")
			},
			monText() {
				return t("uni-calender.MON")
			},
			TUEText() {
				return t("uni-calender.TUE")
			},
			WEDText() {
				return t("uni-calender.WED")
			},
			THUText() {
				return t("uni-calender.THU")
			},
			FRIText() {
				return t("uni-calender.FRI")
			},
			SATText() {
				return t("uni-calender.SAT")
			},
			SUNText() {
				return t("uni-calender.SUN")
			},
		},
		watch: {
			date(newVal) {
				// this.cale.setDate(newVal)
				this.init(newVal)
			},
			startDate(val){
				this.cale.resetSatrtDate(val)
				this.cale.setDate(this.nowDate.fullDate)
				this.weeks = this.cale.weeks
			},
			endDate(val){
				this.cale.resetEndDate(val)
				this.cale.setDate(this.nowDate.fullDate)
				this.weeks = this.cale.weeks
			},
			selected(newVal) {
				this.cale.setSelectInfo(this.nowDate.fullDate, newVal)
				this.weeks = this.cale.weeks
			}
		},
		created() {
			// 获取日历方法实例
			this.cale = new Calendar({
				// date: new Date(),
				selected: this.selected,
				startDate: this.startDate,
				endDate: this.endDate,
				range: this.range,
			})
			// 选中某一天
			// this.cale.setDate(this.date)
			this.init(this.date)
			// this.setDay
		},
		methods: {
			// 取消穿透
			clean() {},
			bindDateChange(e) {
				const value = e.detail.value + '-1'
				console.log(this.cale.getDate(value));
				this.init(value)
			},
			/**
			 * 初始化日期显示
			 * @param {Object} date
			 */
			init(date) {
				this.cale.setDate(date)
				this.weeks = this.cale.weeks
				this.nowDate = this.calendar = this.cale.getInfo(date)
			},
			/**
			 * 打开日历弹窗
			 */
			open() {
				// 弹窗模式并且清理数据
				if (this.clearDate && !this.insert) {
					this.cale.cleanMultipleStatus()
					// this.cale.setDate(this.date)
					this.init(this.date)
				}
				this.show = true
				this.$nextTick(() => {
					setTimeout(() => {
						this.aniMaskShow = true
					}, 50)
				})
			},
			/**
			 * 关闭日历弹窗
			 */
			close() {
				this.aniMaskShow = false
				this.$nextTick(() => {
					setTimeout(() => {
						this.show = false
						this.$emit('close')
					}, 300)
				})
			},
			/**
			 * 确认按钮
			 */
			confirm() {
				this.setEmit('confirm')
				this.close()
			},
			/**
			 * 变化触发
			 */
			change() {
				if (!this.insert) return
				this.setEmit('change')
			},
			/**
			 * 选择月份触发
			 */
			monthSwitch() {
				let {
					year,
					month
				} = this.nowDate
				this.$emit('monthSwitch', {
					year,
					month: Number(month)
				})
			},
			/**
			 * 派发事件
			 * @param {Object} name
			 */
			setEmit(name) {
				let {
					year,
					month,
					date,
					fullDate,
					lunar,
					extraInfo
				} = this.calendar
				this.$emit(name, {
					range: this.cale.multipleStatus,
					year,
					month,
					date,
					fulldate: fullDate,
					lunar,
					extraInfo: extraInfo || {}
				})
			},
			/**
			 * 选择天触发
			 * @param {Object} weeks
			 */
			choiceDate(weeks) {
				if (weeks.disable) return
				this.calendar = weeks
				// 设置多选
				this.cale.setMultiple(this.calendar.fullDate)
				this.weeks = this.cale.weeks
				this.change()
			},
			/**
			 * 回到今天
			 */
			backtoday() {
				console.log(this.cale.getDate(new Date()).fullDate);
				let date = this.cale.getDate(new Date()).fullDate
				// this.cale.setDate(date)
				this.init(date)
				this.change()
			},
			/**
			 * 上个月
			 */
			pre() {
				const preDate = this.cale.getDate(this.nowDate.fullDate, -1, 'month').fullDate
				this.setDate(preDate)
				this.monthSwitch()

			},
			/**
			 * 下个月
			 */
			next() {
				const nextDate = this.cale.getDate(this.nowDate.fullDate, +1, 'month').fullDate
				this.setDate(nextDate)
				this.monthSwitch()
			},
			/**
			 * 设置日期
			 * @param {Object} date
			 */
			setDate(date) {
				this.cale.setDate(date)
				this.weeks = this.cale.weeks
				this.nowDate = this.cale.getInfo(date)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.uni-calendar {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
	}

	.uni-calendar__mask {
		position: fixed;
		bottom: 0;
		top: 0;
		left: 0;
		right: 0;
		background-color: $uni-bg-color-mask;
		transition-property: opacity;
		transition-duration: 0.3s;
		opacity: 0;
		/* #ifndef APP-NVUE */
		z-index: 99;
		/* #endif */
	}

	.uni-calendar--mask-show {
		opacity: 1
	}

	.uni-calendar--fixed {
		position: fixed;
		bottom: calc(var(--window-bottom));
		left: 0;
		right: 0;
		transition-property: transform;
		transition-duration: 0.3s;
		transform: translateY(460px);
		/* #ifndef APP-NVUE */
		z-index: 99;
		/* #endif */
	}

	.uni-calendar--ani-show {
		transform: translateY(0);
	}

	.uni-calendar__content {
		background-color: #fff;
	}

	.uni-calendar__header {
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: center;
		align-items: center;
		height: 50px;
	}

	.uni-calendar--fixed-top {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: space-between;
		border-top-color: $uni-border-color;
		border-top-style: solid;
		border-top-width: 1px;
	}

	.uni-calendar--fixed-width {
		width: 50px;
		// padding: 0 15px;
	}

	.uni-calendar__backtoday {
		position: absolute;
		right: 0;
		top: 25rpx;
		padding: 0 5px;
		padding-left: 10px;
		height: 25px;
		line-height: 25px;
		font-size: 12px;
		border-top-left-radius: 25px;
		border-bottom-left-radius: 25px;
		color: $uni-text-color;
		background-color: $uni-bg-color-hover;
	}

	.uni-calendar__header-text {
		text-align: center;
		width: 100px;
		font-size: $uni-font-size-base;
		color: $uni-text-color;
	}

	.uni-calendar__header-btn-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		width: 50px;
		height: 50px;
	}

	.uni-calendar__header-btn {
		width: 10px;
		height: 10px;
		border-left-color: $uni-text-color-placeholder;
		border-left-style: solid;
		border-left-width: 2px;
		border-top-color: $uni-color-subtitle;
		border-top-style: solid;
		border-top-width: 2px;
	}

	.uni-calendar--left {
		transform: rotate(-45deg);
	}

	.uni-calendar--right {
		transform: rotate(135deg);
	}


	.uni-calendar__weeks {
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}

	.uni-calendar__weeks-item {
		flex: 1;
	}

	.uni-calendar__weeks-day {
		flex: 1;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 45px;
		border-bottom-color: #F5F5F5;
		border-bottom-style: solid;
		border-bottom-width: 1px;
	}

	.uni-calendar__weeks-day-text {
		font-size: 14px;
	}

	.uni-calendar__box {
		position: relative;
	}

	.uni-calendar__box-bg {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.uni-calendar__box-bg-text {
		font-size: 200px;
		font-weight: bold;
		color: $uni-text-color-grey;
		opacity: 0.1;
		text-align: center;
		/* #ifndef APP-NVUE */
		line-height: 1;
		/* #endif */
	}
</style>
