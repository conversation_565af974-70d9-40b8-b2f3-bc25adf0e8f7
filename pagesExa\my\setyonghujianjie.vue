<template>
  <view class="container">
    <block v-if="isload">
      <form @submit="formSubmit" @reset="formReset">
        <view class="form">
          <view class="form-item">
            <textarea
              class="input"
              placeholder="请输入用户简介"
              placeholder-style="color:#BBBBBB;font-size:28rpx"
              name="yonghujianjie"
              rows="5"
              v-model="yonghujianjie"
            ></textarea>
          </view>
        </view>
        <button
          class="set-btn"
          form-type="submit"
          :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
        >
          保 存
        </button>
      </form>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      textset: {},
      haspwd: 0,
      yonghujianjie: '', // 用于存储用户简介
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata(); // 页面加载时获取用户数据
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  methods: {
    getdata() {
      const that = this;
      app.showLoading('加载中');
      app.post('ApiMy/getUserInfo', {}, function (data) {
        app.showLoading(false);
        if (data.status == 1) {
          that.yonghujianjie = data.data.yonghujianjie || '';
          that.isload = true;
        } else {
          app.error(data.msg);
        }
      });
    },
    formSubmit: function (e) {
      var yonghujianjie = this.yonghujianjie;
      if (yonghujianjie == '') {
        app.alert('请输入用户简介');
        return;
      }
      app.showLoading('提交中');
      app.post('ApiMy/setfield', { yonghujianjie: yonghujianjie }, (data) => {
        app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(() => {
            uni.redirectTo({
              url: '/daihuobiji/detail/userbiji'
            });
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    },
  },
};
</script>

<style>
.form {
  width: 94%;
  margin: 20rpx 3%;
  border-radius: 5px;
  padding: 0 3%;
  background: #fff;
}
.form-item {
  display: flex;
  align-items: center;
  width: 100%;
  border-bottom: 1px #ededed solid;
  padding: 20rpx 0;
}
.form-item:last-child {
  border: 0;
}
.form-item .label {
  color: #000;
  width: 200rpx;
}
.form-item .input {
  flex: 1;
  color: #000;
}
.set-btn {
  width: 90%;
  margin: 60rpx 5%;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 48rpx;
  color: #ffffff;
  font-weight: bold;
}
</style>
