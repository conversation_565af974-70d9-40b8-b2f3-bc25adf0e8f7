<template>
	<!-- #ifdef MP-WEIXIN -->
	<cover-view class="header">
		<cover-view class="left">
			<cover-image class="avatar" :src="getAnchorHeadimg"></cover-image>
			<cover-view class="info">
				<cover-view class="name">{{getAnchorNickname}}</cover-view>
				<cover-view class="status-info">
					<cover-view class="status" :class="{'offline': !data.online}">
						{{data.online ? '直播中' : '未开播'}}
					</cover-view>
					<!-- <cover-view class="fans">{{onlineCount}}观众</cover-view> -->
				</cover-view>
			</cover-view>
		</cover-view>
		<cover-view class="right">
			<cover-view class="follow" @tap="handleFollow">
				{{isFollowed ? '已关注' : '关注'}}
			</cover-view>
		</cover-view>
	</cover-view>
	<!-- #endif -->

	<!-- #ifdef H5 -->
	<view class="header">
		<view class="top flex">
			<view class="user flex">
				<image class="avatar" :src="getAnchorHeadimg" mode=""></image>
				<view class="userinfo flex flex-column">
					<text class="username">{{getAnchorNickname}}</text>
					<view class="status-wrap">
						<text class="status" :class="{'offline': !data.online}">
							{{data.online ? '直播中' : '未开播'}}
						</text>
						<!-- <text class="desc">{{onlineCount}}在线</text> -->
					</view>
				</view>
				<view class="follow flex" @click="handleFollow">
					<uni-icons type="plusempty" color="" size="12"></uni-icons>
					<text>{{isFollowed ? '已关注' : '关注'}}</text>
				</view>
			</view>
			<!-- <view class="onlineuser flex">
				<view class="avatar flex flex-column">
					<image src="@/static/live/avatar/1.jpeg" mode=""></image>
					<text>1</text>
				</view>
				<view class="avatar flex flex-column">
					<image src="@/static/live/avatar/2.jpeg" mode=""></image>
				</view>
				<view class="avatar flex flex-column">
					<image src="@/static/live/avatar/3.jpeg" mode=""></image>
				</view>
			</view> -->
			<!-- <uni-icons class="close" type="closeempty" size="22" color="#fff"></uni-icons> -->
		</view>
		
		<!-- <view class="title-list flex">
			<view class="title-item flex">
				<uni-icons type="cart" size="16" color="#ffab01"></uni-icons>
				<text class="text">动漫热榜第1名</text>
			</view>
			<view class="title-item flex">
				<uni-icons type="flag" size="16" color="#ffab01"></uni-icons>
				<text class="text">小时榜</text>
			</view>
		</view>
		<view class="share-btn" @click="handleShare">
			<uni-icons type="redo" size="24" color="#fff"></uni-icons>
		</view>
		<view class="online-count">
			<text>{{ onlineCount }}人观看</text>
		</view> -->
	</view>
	<!-- #endif -->
</template>

<script>
	export default {
		props: {
			data: {
				type: Object,
				default: () => ({
					anchor: {},
					online: 0,
					online_count: 0,
					is_silence: 0,
					online_users: []
				})
			}
		},
		data() {
			return {
				defaultAvatar: '@/static/live/video/1-poster.png',
				isFollowed: false,
				onlineCount: 1,
			}
		},
		computed: {
			getAnchorHeadimg() {
				return this.data.anchor && this.data.anchor.headimg ? this.data.anchor.headimg : this.defaultAvatar
			},
			getAnchorNickname() {
				return this.data.anchor && this.data.anchor.nickname ? this.data.anchor.nickname : '主播'
			}
		},
		methods: {
			handleFollow() {
				this.isFollowed = !this.isFollowed;
				// 这里可以添加关注/取消关注的接口调用
				uni.showToast({
					title: this.isFollowed ? '已关注' : '已取消关注',
					icon: 'none'
				});
			},
			handleShare() {
				// 通过事件通知父组件处理分享
				this.$emit('onShare');
			},
			handleUserEnter(user) {
				if(!this.data.online_users) {
					this.$set(this.data, 'online_users', [])
				}
				this.data.online_users.push(user)
				if(user.onlineCount) {
					this.onlineCount = user.onlineCount;
				}
			},
			handleUserLeave(userId) {
				if(!this.data.online_users) return
				const index = this.data.online_users.findIndex(u => u.id === userId)
				if(index > -1) {
					this.data.online_users.splice(index, 1)
				}
			}
		},
		watch: {
			'data.online_count': {
				immediate: true,
				handler(newVal) {
					console.log('watch online_count变化:', {
						newVal: newVal,
						currentOnlineCount: this.onlineCount
					});
					if(newVal) {
						this.onlineCount = newVal;
					}
				}
			}
		}
	}
</script>



<style scoped lang="scss">
	/* 小程序端样式 */
	/* #ifdef MP-WEIXIN */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		padding: 0 30rpx 20rpx 30rpx;
		width: 100%;
		box-sizing: border-box;
		height: 100%;
		
		.left {
			display: flex;
			align-items: center;
			background: rgba(0, 0, 0, 0.3);
			border-radius: 100rpx;
			padding: 12rpx 24rpx;
			flex: 1;
			margin-right: 20rpx;
			max-width: 460rpx;
			
			.avatar {
				width: 72rpx;
				height: 72rpx;
				border-radius: 50%;
				margin-right: 20rpx;
				flex-shrink: 0;
			}
			
			.info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
				
				.name {
					color: #fff;
					font-size: 28rpx;
					font-weight: 500;
					margin-bottom: 6rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					line-height: 1.2;
				}
				
				.status-info {
					display: flex;
					align-items: center;
					
					.status {
						color: #00FF00;
						font-size: 24rpx;
						margin-right: 20rpx;
						
						&.offline {
							color: #FF4656;
						}
					}
				}
			}
		}
		
		.right {
			.follow {
				background: linear-gradient(to right, #FF4656, #FE2B8C);
				color: #fff;
				font-size: 26rpx;
				padding: 12rpx 36rpx;
				border-radius: 100rpx;
				white-space: nowrap;
				min-width: 140rpx;
				text-align: center;
				font-weight: 500;
			}
		}
	}
	/* #endif */
	
	/* H5端样式保持不变 */
	/* #ifdef H5 */
	.header {
		position: absolute;
		top: 20rpx;
		left: 20rpx;
		right: 0;
		min-width: 750rpx;
		
		.top {
			width: 100%;
			justify-content: space-between;
		}
		
		.close {
			margin-right: 20rpx;
		}
		
		.user {
			background: rgba(0, 0, 0, 0.3);
			border-radius: 100px;
			padding: 6rpx;
			
			.avatar {
				height: 60rpx;
				width: 60rpx;
				border-radius: 100%;
			}
			
			.userinfo {
				padding: 0 10rpx;
				.username {
					color: #fff;
					font-size: 26rpx;
					font-weight: 500;
					align-self: flex-start;
				}
				.desc {
					color: #ccc;
					font-size: 22rpx;
					align-self: flex-start;
				}
			}
			
			.follow {
				font-size: 22rpx;
				background: linear-gradient(to right, #FF4656, #FE2B8C);
				color: #fff;
				width: 90rpx;
				border-radius: 100rpx;
				height: 60rpx;
				line-height: 60rpx;
				justify-content: center;
			}
		}
		
		.onlineuser {
			.avatar {
				position: relative;
				
				text {
					width: 72rpx;
					height: 30rpx;
					line-height: 30rpx;
					font-size: 16rpx;
					text-align: center;
					position: absolute;
					left: 2rpx;
					bottom: 0px;
					font-weight: 500;
					color: #fff;
				}
				
				image {
					width: 74rpx;
					height: 74rpx;
					border-radius: 100%;
					margin-right: 20rpx;
				}
				
				&:first-child {
					
					&::after {
						content: " ";
						width: 74rpx;
						height: 74rpx;
						background-image: url(@/static/live/icons/shouhu.webp);
						background-size: 74rpx 74rpx;
						background-repeat: no-repeat;
						background-position: center center;
						display: block;
						position: absolute;
						left: 0px;
						top: 0px;
						z-index: 1;
					}
					
					text {
						border: none;
						background-image: url(https://vr0.6rooms.com/tao/i/n7/2449060832f8a39fe6772f87de1bef9d.png);
						background-size: 100% 100%;
					}
				}
			}
		}
		
		.title-list {
			margin-top: 20rpx;
			.title-item {
				background: var(--live-background);
				border-radius: 100rpx;
				margin-right: 20rpx;
				padding: 5rpx 15rpx;
				.text {
					font-size: 24rpx;
					color: #fff;
					margin-left: 2rpx;
				}
			}
		}
	}
	/* #endif */
	
	.flex-column {
		flex-direction: column;
	}
	
	.flex {
		display: flex;
		justify-content: flex-start;
		align-items: center;
	}
	
	/* H5端新增和修改的样式 */
	/* #ifdef H5 */
	.status-wrap {
		display: flex;
		align-items: center;
		
		.status {
			color: #00FF00;
			font-size: 22rpx;
			margin-right: 20rpx;
			
			&.offline {
				color: #FF4656;
			}
		}
	}
	/* #endif */
	
	.share-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}
	
	/* 在已有样式的基础上添加 */
	.online-count {
		background: rgba(0, 0, 0, 0.3);
		border-radius: 100rpx;
		padding: 8rpx 20rpx;
		position: absolute;
		right: 120rpx;
		top: 20rpx;
		
		text {
			color: #fff;
			font-size: 24rpx;
		}
	}
	
	/* #ifdef H5 */
	.header {
		.online-count {
			right: 120rpx;
			top: 20rpx;
		}
	}
	/* #endif */
	
	/* #ifdef MP-WEIXIN */
	.header {
		.online-count {
			right: 180rpx;
			top: 30rpx;
		}
	}
	/* #endif */
</style>