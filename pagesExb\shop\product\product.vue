<template>
<view class="container">
	<block v-if="isload">
		<block v-if="sysset.showgzts">
			<view style="width:100%;height:88rpx"> </view>
			<view class="follow_topbar">
				<view class="headimg"><image :src="sysset.logo"/></view>
				<view class="info">
					<view class="i">欢迎进入 <text :style="{color:t('color1')}">{{sysset.name}}</text></view>
					<view class="i">关注公众号享更多专属服务</view>
				</view>
				<view class="sub" @tap="showsubqrcode" :style="{'background-color':t('color1')}">立即关注</view>
			</view>
			<uni-popup id="qrcodeDialog" ref="qrcodeDialog" type="dialog">
				<view class="qrcodebox">
					<image :src="sysset.qrcode" @tap="previewImage" :data-url="sysset.qrcode" class="img"/>
					<view class="txt">长按识别二维码关注</view>
					<view class="close" @tap="closesubqrcode">
						<image src="/static/img/close2.png" style="width:100%;height:100%"/>
					</view>
				</view>
			</uni-popup>
		</block>

		<view style="position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx" v-if="bboglist.length>0">
			<swiper style="position:relative;height:54rpx;width:350rpx;" :autoplay="true" :interval="5000" :vertical="true">
				<swiper-item v-for="(item, index) in bboglist" :key="index" @tap="goto" :data-url="'/shopPackage/shop/product?id=' + item.proid" class="flex-y-center">
					<image :src="item.headimg" style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px"/>
					<view style="width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx">{{item.nickname}} {{item.showtime}}购买了该商品</view>
				</swiper-item>
			</swiper>
		</view>

		<view class="toptabbar_tab" v-if="showtoptabbar==1 && toptabbar_show==1">
			<view class="item" :class="toptabbar_index==0?'on':''" :style="{color:toptabbar_index==0?t('color1'):'#333'}" @tap="changetoptab" data-index="0">商品<view class="after" :style="{background:t('color1')}"></view></view>
			<view class="item" :class="toptabbar_index==1?'on':''" :style="{color:toptabbar_index==1?t('color1'):'#333'}" @tap="changetoptab" data-index="1">评价<view class="after" :style="{background:t('color1')}"></view></view>
			<view class="item" :class="toptabbar_index==2?'on':''" :style="{color:toptabbar_index==2?t('color1'):'#333'}" @tap="changetoptab" data-index="2">详情<view class="after" :style="{background:t('color1')}"></view></view>
			<view class="item" v-if="tjdatalist.length > 0" :class="toptabbar_index==3?'on':''" :style="{color:toptabbar_index==3?t('color1'):'#333'}" @tap="changetoptab" data-index="3">推荐<view class="after" :style="{background:t('color1')}"></view></view>
		</view>

		<scroll-view @scroll="scroll" :scrollIntoView="scrollToViewId" :scrollTop="scrollTop" :scroll-y="true" style="height:100%;overflow:scroll">
		
		<view id="scroll_view_tab0">
			<view class="swiper-container" v-if="isplay==0">
				<swiper class="swiper" :indicator-dots="false" :autoplay="true" :interval="5000" @change="swiperChange">
					<block v-for="(item, index) in product.pics" :key="index">
						<swiper-item class="swiper-item">
							<view class="swiper-item-view"><image class="img" :src="item" mode="widthFix" @tap="previewImage" :data-url="item"/></view>
						</swiper-item>
					</block>
				</swiper>
				<view class="imageCount" v-if="product.diypics" @tap="goto" :data-url="'/pagesExt/shop/diylight?id='+product.id" style="bottom: 92rpx; width: 140rpx;">自助试灯</view>
				<view class="imageCount">{{current+1}}/{{(product.pics).length}}</view>
				<view v-if="product.video" class="provideo" @tap="payvideo"><image src="/static/img/video.png"/><view class="txt">{{product.video_duration}}</view></view>
			</view>
			<view class="videobox" v-if="isplay==1">
				<video autoplay="true" class="video" id="video" :src="product.video"></video>
				<view class="parsevideo" @tap="parsevideo">退出播放</view>
			</view>
			
			<view class="cuxiaopoint cuxiaoitem" v-if="showtoptabbar==1 && couponlist.length>0" style="background:#fff;padding:0 16rpx">
				<view class="f1" @tap="showcuxiaodetail">
					<view v-for="(item, index) in couponlist" :key="index" class="t" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}"><text class="t0" style="padding:0 6px">券</text><text class="t1">{{item.name}}</text></view>
				</view>
				<view class="f2" @tap="showcuxiaodetail">
					<image src="/static/img/arrow-point.png" mode="widthFix"/>
				</view>
			</view>

			<view style="background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0" v-if="shopset.detail_guangao1">
				<image :src="shopset.detail_guangao1" style="width:100%;height:auto" mode="widthFix" v-if="shopset.detail_guangao1" @tap="showgg1Dialog"/>
			</view>
			<uni-popup id="gg1Dialog" ref="gg1Dialog" type="dialog" v-if="shopset.detail_guangao1 && shopset.detail_guangao1_t">
				<image :src="shopset.detail_guangao1_t" @tap="previewImage" :data-url="shopset.detail_guangao1_t" class="img" mode="widthFix" style="width:600rpx;height:auto;border-radius:10rpx;"/>
				<view class="ggdiaplog_close" @tap="closegg1Dialog">
					<image src="/static/img/close2.png" style="width:100%;height:100%"/>
				</view>
			</uni-popup>
			
			<view class="header">
				<block v-if="product.price_type != 1 || product.min_price > 0">
					<view class="price_share">
						<view class="price">
							<view class="f1" :style="{color:t('color1')}">
								<text style="font-size:36rpx">￥</text>{{product.min_price}}<text v-if="product.max_price!=product.min_price">-{{product.max_price}}</text>
							</view>
							<view class="f2" v-if="product.market_price*1 > product.sell_price*1">￥{{product.market_price}}<text v-if="product.max_price!=product.min_price">起</text></view>
							<view v-if="product.huang_dx.types" class="huang_bz">
								<view class="huang_i"></view>
								<view class="huang_nums">可用红包：{{product.huang_dx.nums}}</view>
							</view>
						</view>
						<view class="share" @tap="shareClick"><image class="img" src="/static/img/share.png"/><text class="txt">分享</text></view>
					</view>
									<view class="sales_stock" v-if="product.yuanbao" style="margin: 0;font-size: 26rpx;margin-bottom: 10rpx;">
										<view class="f2">元宝价：{{product.yuanbao}}</view>
									</view>
					<view class="title">{{product.name}}</view>
				</block>
				<block v-else>
									<view v-if="product.xunjia_text" class="price_share">
											<view class="price">
													<view class="f1" :style="{color:t('color1')}">
															<text style="font-size:36rpx">询价</text>
													</view>
											</view>
									</view>
					<view class="price_share">
						<view class="title">{{product.name}}</view>
						<view class="share" @tap="shareClick"><image class="img" src="/static/img/share.png"/><text class="txt">分享</text></view>
					</view>
				</block>
				
				<view class="sellpoint" v-if="product.sellpoint">{{product.sellpoint}}</view>
				<view class="sales_stock" v-if="shopset.showcommission==1 && product.commission > 0 && showjiesheng==0">
					<!-- <view class="f1">奖励消费值：{{product.xiaofeizhi2}} </view> -->
						<!-- <view class="f1">奖励创业值：{{product.chuangyezhi}} </view> -->
					<view class="f2">推广佣金：{{product.commission}}元</view>
				</view>
				
				<view class="sales_stock" v-if="shopset.hide_sales != 1 || shopset.hide_stock != 1">
					<view class="f1" v-if="shopset.hide_sales != 1">销量：{{product.sales}} </view>
					<view class="f2" v-if="shopset.hide_stock != 1">库存：{{product.stock}}</view>
				</view>
				<view class="commission" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="shopset.showcommission==1 && product.commission > 0 && showjiesheng==0">分享好友购买预计可得{{t('佣金')}}：<text style="font-weight:bold;padding:0 2px">{{product.commission}}</text>{{product.commission_desc}}</view>
				<view style="margin:20rpx 0;color:#333;font-size:22rpx" v-if="product.balance_price > 0">首付款金额：{{product.advance_price}}元，尾款金额：{{product.balance_price}}元</view>
				<view style="margin:20rpx 0;color:#666;font-size:22rpx" v-if="product.buyselect_commission > 0">下单被选奖励预计可得{{t('佣金')}}：<text style="font-weight:bold;padding:0 2px">{{product.buyselect_commission}}</text>元</view>

				<view class="upsavemoney" :style="{background:'linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)',color:'#653a2b'}" v-if="product.upsavemoney > 0">
					<view class="flex1">升级到 {{product.nextlevelname}} 预计可节省<text style="font-weight:bold;padding:0 2px;color:#ca4312">{{product.upsavemoney}}</text>元</view>
					<view style="margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312" @tap="goto" data-url="/pagesExa/my/levelup">立即升级<image src="/static/img/arrowright2.png" style="width:30rpx;height:30rpx"/></view>
				</view> 
			</view>
			<view class="choose" @tap="buydialogChange" data-btntype="2">
				<view class="f0">规格</view>
				<view class="f1 flex1">
					<block v-if="product.price_type == 1">查看规格</block>
					<block v-else>请选择商品规格及数量</block>
					</view>
				<image class="f2" src="/static/img/arrowright.png"/>
			</view>
			<view class="cuxiaodiv" v-if="product.xiaofeizhi2 > 0">
				<view class="cuxiaopoint">
					<view class="f0">送{{t('消费值')}}</view>
					<view class="f1" style="font-size:26rpx">购买可得{{t('消费值')}}{{product.xiaofeizhi2}} 个</view>
				</view>
			</view>
			<view class="cuxiaodiv" v-if="product.chuangyezhi > 0">
				<view class="cuxiaopoint">
					<view class="f0">送{{t('创业值')}}</view>
					<view class="f1" style="font-size:26rpx;">购买可得{{t('创业值')}}{{product.chuangyezhi}} 个</view>
				</view>
			</view>
			<view class="cuxiaodiv" v-if="product.givescore > 0">
				<view class="cuxiaopoint">
					<view class="f0">送{{t('积分')}}</view>
					<view class="f1" style="font-size:26rpx">购买可得{{t('积分')}}{{product.givescore}}个</view>
				</view>
			</view>

			<view class="cuxiaodiv" v-if="cuxiaolist.length>0 || couponlist.length>0 || fuwulist.length>0 || product.discount_tips!=''">
				<view class="fuwupoint cuxiaoitem" v-if="fuwulist.length>0">
					<view class="f0">服务</view>
					<view class="f1" @tap="showfuwudetail">
						<view class="t" v-for="(item, index) in fuwulist" :key="index">{{item.name}}</view>
					</view>
					<view class="f2" @tap="showfuwudetail">
						<image src="/static/img/arrow-point.png" mode="widthFix"/>
					</view>
				</view>
				<view class="cuxiaopoint cuxiaoitem" v-if="cuxiaolist.length>0">
					<view class="f0">促销</view>
					<view class="f1" @tap="showcuxiaodetail">
						<view v-for="(item, index) in cuxiaolist" :key="index" class="t" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}"><text class="t0">{{item.tip}}</text><text class="t1">{{item.name}}</text></view>
					</view>
					<view class="f2" @tap="showcuxiaodetail">
						<image src="/static/img/arrow-point.png" mode="widthFix"/>
					</view>
				</view>
				<view class="cuxiaopoint cuxiaoitem" v-if="product.discount_tips!=''">
					<view class="f0">折扣</view>
					<view class="f1" style="padding-left:10rpx">{{product.discount_tips}}</view>
					<view class="f2" @tap="goto" data-url="/pagesExa/my/levelinfo">
						<image src="/static/img/arrow-point.png" mode="widthFix"/>
					</view>
				</view>
				<view class="cuxiaopoint cuxiaoitem" v-if="couponlist.length>0 && showtoptabbar==0">
					<view class="f0">优惠</view>
					<view class="f1" @tap="showcuxiaodetail">
						<view v-for="(item, index) in couponlist" :key="index" class="t" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}"><text class="t0" style="padding:0 6px">券</text><text class="t1">{{item.name}}</text></view>
					</view>
					<view class="f2" @tap="showcuxiaodetail">
						<image src="/static/img/arrow-point.png" mode="widthFix"/>
					</view>
				</view>
			</view>
			<view v-if="showfuwudialog" class="popup__container">
				<view class="popup__overlay" @tap.stop="hidefuwudetail"></view>
				<view class="popup__modal">
						<view class="popup__title">
							<text class="popup__title-text">服务</text>
							<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hidefuwudetail"/>
						</view>
						<view class="popup__content">
							<view v-for="(item, index) in fuwulist" :key="index" class="service-item">
								<view class="fuwudialog-content">
									<view class="f1">{{item.name}}</view>
									<text class="f2">{{item.desc}}</text>
								</view>
							</view>
						</view>
				</view>
			</view>
			<view v-if="showcuxiaodialog" class="popup__container">
				<view class="popup__overlay" @tap.stop="hidecuxiaodetail"></view>
				<view class="popup__modal">
						<view class="popup__title">
							<text class="popup__title-text">优惠促销</text>
							<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hidecuxiaodetail"/>
						</view>
						<view class="popup__content">
							<view v-for="(item, index) in cuxiaolist" :key="index" class="service-item">
								<view class="suffix">
									<view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px">{{item.tip}}</text> <text style="color:#333;margin-left:20rpx">{{item.name}}</text></view>
								</view>
							</view>
							<couponlist :couponlist="couponlist" @getcoupon="getcoupon"></couponlist>
						</view>
				</view>
			</view>

			<view style="width:100%;height:auto;padding:20rpx 0 0" v-if="shopset.detail_guangao2">
				<image :src="shopset.detail_guangao2" style="width:100%;height:auto" mode="widthFix" v-if="shopset.detail_guangao2" @tap="showgg2Dialog"/>
			</view>
			<uni-popup id="gg2Dialog" ref="gg2Dialog" type="dialog" v-if="shopset.detail_guangao2 && shopset.detail_guangao2_t">
				<image :src="shopset.detail_guangao2_t" @tap="previewImage" :data-url="shopset.detail_guangao2_t" class="img" mode="widthFix" style="width:600rpx;height:auto;border-radius:10rpx;"/>
				<view class="ggdiaplog_close" @tap="closegg2Dialog">
					<image src="/static/img/close2.png" style="width:100%;height:100%"/>
				</view>
			</uni-popup>
		
		</view>

		<view id="scroll_view_tab1">

			<view class="commentbox" v-if="shopset.comment==1 && commentcount > 0">
				<view class="title">
					<view class="f1">评价({{commentcount}})</view>
					<view class="f2" @tap="goto" :data-url="'commentlist?proid=' + product.id">好评率 <text :style="{color:t('color1')}">{{product.comment_haopercent}}%</text><image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"/></view>
				</view>
				<view class="comment">
					<view class="item" v-if="commentlist.length>0">
						<view class="f1">
							<image class="t1" :src="commentlist[0].headimg"/>
							<view class="t2">{{commentlist[0].nickname}}</view>
							<view class="flex1"></view>
							<view class="t3"><image class="img" v-for="(item2,index2) in [0,1,2,3,4]" :key="index2"  :src="'/static/img/star' + (commentlist[0].score>item2?'2':'') + '.png'"/></view>
						</view>
						<view class="f2">
							<text class="t1">{{commentlist[0].content}}</text>
							<view class="t2">
								<block v-if="commentlist[0].content_pic!=''">
									<block v-for="(itemp, index) in commentlist[0].content_pic" :key="index">
										<view @tap="previewImage" :data-url="itemp" :data-urls="commentlist[0].content_pic">
											<image :src="itemp" mode="widthFix"/>
										</view>
									</block>
								</block>
							</view>
						</view>
						<view class="f3" @tap="goto" :data-url="'commentlist?proid=' + product.id">查看全部评价</view>
					</view>
					<view v-else class="nocomment">暂无评价~</view>
				</view>
			</view>

		</view>

		<view id="scroll_view_tab2">
			
			<view class="shop" v-if="shopset.showjd==1">
				<image :src="business.logo" class="p1"/>
				<view class="p2 flex1">
					<view class="t1">{{business.name}}</view>
					<view class="t2">{{business.desc}}</view>
				</view>
				<button class="p4" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="goto" :data-url="product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid" data-opentype="reLaunch">进入首页</button>
			</view>
			<block v-if="!isEmpty(product.paramdata)">
			<view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品参数</view><view class="t2"></view><view class="t1"></view></view>
			<view style="background:#fff;padding:20rpx 40rpx;" class="paraminfo">
				<view v-for="(item, index) in product.paramdata" class="paramitem">
					<view class="f1">{{index}}</view>
					<view class="f2">{{item}}</view>
				</view>
			</view>
			</block>

			<view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view>
			<view class="detail">
				<dp :pagecontent="pagecontent"></dp>
			</view>

		</view>
		
		<view id="scroll_view_tab3">

			<view v-if="tjdatalist.length > 0">
				<view class="xihuan">
					<view class="xihuan-line"></view>
					<view class="xihuan-text">
						<image src="/static/img/xihuan.png" class="img"/>
						<text class="txt">为您推荐</text>
					</view>
					<view class="xihuan-line"></view>
				</view>
				<view class="prolist">
					<dp-product-item :data="tjdatalist" @addcart="addcart" :menuindex="menuindex"></dp-product-item>
				</view>
			</view>

		</view>

		<view style="width:100%;height:140rpx;"></view>

		</scroll-view>

		<view class="bottombar flex-row" :class="menuindex>-1?'tabbarbot':'notabbarbot'" v-if="product.status==1&&!showcuxiaodialog">
			<view class="f1">
				<view class="item" @tap="goto" :data-url="kfurl" v-if="kfurl!='contact::'">
					<image class="img" src="/static/img/kefu.png"/>
					<view class="t1">客服</view>
				</view>
				<button class="item" v-else open-type="contact">
					<image class="img" src="/static/img/kefu.png"/>
					<view class="t1">客服</view>
				</button>
				<view class="item flex1" @tap="goto" data-url="/shopPackage/shop/cart">
					<image class="img" src="/static/img/gwc.png"/>
					<view class="t1">购物车</view>
					<view class="cartnum" v-if="cartnum>0" :style="{background:'rgba('+t('color1rgb')+',0.8)'}">{{cartnum}}</view>
				</view>
				<view class="item" @tap="addfavorite">
					<image class="img" src="/static/img/shoucang.png"/>
					<view class="t1">{{isfavorite?'已收藏':'收藏'}}</view>
				</view>
			</view>
			<view class="op2" v-if="showjiesheng==1">
				<view class="tocart2" :style="{background:t('color2')}" @tap="shareClick"><text>分享赚钱</text><text style="font-size:24rpx">赚￥{{product.commission}}</text></view>
				<view class="tobuy2" :style="{background:t('color1')}" @tap="buydialogChange" data-btntype="2"><text>立即购买</text><text style="font-size:24rpx" v-if="product.jiesheng_money > 0">省￥{{product.jiesheng_money}}</text></view>
			</view>
			<view class="op" v-else>
				<block v-if="product.price_type == 1">
					<view class="tobuy flex-x-center flex-y-center" :style="{background:t('color1')}" @tap="showLinkChange" data-btntype="2">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</view>
				</block>
				<block v-else>
					<view class="tocart flex-x-center flex-y-center" :style="{background:t('color2')}" @tap="buydialogChange" data-btntype="1" v-if="product.freighttype!=3 && product.freighttype!=4">加入购物车</view>
					<view class="tobuy flex-x-center flex-y-center" :style="{background:t('color1')}" @tap="buydialogChange" data-btntype="2">立即购买</view>
				</block>
			</view>
		</view>

		<buydialog v-if="buydialogShow" :proid="product.id" :btntype="btntype" @buydialogChange="buydialogChange" @showLinkChange="showLinkChange" :menuindex="menuindex" @addcart="addcart"></buydialog>

		<view class="scrolltop" v-show="scrolltopshow" @tap="changetoptab" data-index="0"><image class="image" src="/static/img/gotop.png"/></view>

		<view v-if="sharetypevisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal" style="height:320rpx;min-height:320rpx">
				<!-- <view class="popup__title">
					<text class="popup__title-text">请选择分享方式</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hidePstimeDialog"/>
				</view> -->
				<view class="popup__content">
					<view class="sharetypecontent">
						<view class="f1" @tap="shareapp" v-if="getplatform() == 'app'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'mp'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'h5'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<button class="f1" open-type="share" v-else>
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</button>
						<view class="f2" @tap="showPoster">
							<image class="img" src="/static/img/sharepic.png"/>
							<text class="t1">生成分享图片</text>
						</view>
						
						<view class="f1" @tap="shareScheme" v-if="getplatform() == 'wx' && xcx_scheme">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">小程序链接</text>
						</view>
						
					</view>
				</view>
			</view>
		</view>
		<view class="posterDialog" v-if="showposter">
			<view class="main">
				<view class="close" @tap="posterDialogClose"><image class="img" src="/static/img/close.png"/></view>
				<view class="content">
					<image class="img" :src="posterpic" mode="widthFix" @tap="previewImage" :data-url="posterpic"></image>
				</view>
			</view>
		</view>
		
		
		<view class="posterDialog schemeDialog" v-if="showScheme">
			<view class="main">
				<view class="schemecon">
					<view style="line-height: 60rpx;">{{product.name}} </view>
					<view >购买链接：<text style="color: #00A0E9;">{{schemeurl}}</text></view>
					<view class="copybtn" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"  @tap.stop="copy" :data-text="product.name+'购买链接：'+schemeurl"> 一键复制 </view>
				</view>
			</view>
		</view>
		
		
		<view class="posterDialog linkDialog" v-if="showLinkStatus">
			<view class="main">
				<view class="close" @tap="showLinkChange"><image class="img" src="/static/img/close.png"/></view>
				<view class="content">
					<view class="title">{{sysset.name}}</view>
					<view class="row" v-if="product.bid > 0">
						<view class="f1">店铺名称</view>
						<view class="f2" @tap="goto" :data-url="'/pagesExt/business/index?id='+product.bid">{{business.name}}<image src="/static/img/arrowright.png" class="image"/></view>
					</view>
					<view class="row" v-if="business.tel">
						<view class="f1">联系电话</view>
						<view class="f2" @tap="goto" :data-url="'tel::'+business.tel" :style="{color:t('color1')}">{{business.tel}}<image src="../../static/img/copy.png" class="copyicon" @tap.stop="copy" :data-text="business.tel"></image></view>
					</view>
				</view>
			</view>
		</view>

	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
var interval = null;

export default {
	data() {
		return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			textset:{},
			isload:false,
			buydialogShow: false,
			btntype:1,
			isfavorite: false,
			current: 0,
			isplay: 0,
			showcuxiaodialog: false,
			showfuwudialog:false,
			business: "",
			product: [],
			cartnum: "",
			commentlist: "",
			commentcount: "",
			cuxiaolist: "",
			couponlist: "",
			fuwulist: [],
			pagecontent: "",
			shopset: {},
			sysset:{},
			title: "",
			bboglist: "",
			sharepic: "",
			sharetypevisible: false,
			showposter: false,
			posterpic: "",
			scrolltopshow: false,
			kfurl:'',
			showLinkStatus:false,
			showjiesheng:0,
			tjdatalist:[],
			showtoptabbar:0,
			toptabbar_show:0,
			toptabbar_index:0,
      scrollToViewId: "",
			scrollTop:0,
			scrolltab0Height:0,
			scrolltab1Height:0,
			scrolltab2Height:0,
			scrolltab3Height:0,
			xcx_scheme:false,
			showScheme:false,
			schemeurl:''
		};
	},
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onShow:function(e){
		uni.$emit('getglassrecord');
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.product.sharetitle || this.product.name,pic:this.product.sharepic || this.product.pic});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.product.sharetitle || this.product.name,pic:this.product.sharepic || this.product.pic});
		var query = (sharewxdata.path).split('?')[1];
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
	onUnload: function () {
		clearInterval(interval);
	},

	methods: {
		showLinkChange: function () {
			this.showLinkStatus = !this.showLinkStatus;
		},
		getdata:function(){
			var that = this;
			var id = this.opt.id || 0;
			that.loading = true;
			app.get('ApiShop/product', {id: id}, function (res) {
				that.loading = false;
				if (res.status == 0) {
					app.alert(res.msg);
					return;
				}
				that.textset = app.globalData.textset;
				var product = res.product;
				var pagecontent = JSON.parse(product.detail);
				that.business = res.business;
				that.product = product;
				that.cartnum = res.cartnum;
				that.commentlist = res.commentlist;
				that.commentcount = res.commentcount;
				that.cuxiaolist = res.cuxiaolist;
				that.couponlist = res.couponlist;
				that.fuwulist = res.fuwulist;
				that.pagecontent = pagecontent;
				that.shopset = res.shopset;
				that.sysset = res.sysset;
				that.title = product.name;
				that.isfavorite = res.isfavorite;
				that.showjiesheng = res.showjiesheng || 0;
				that.tjdatalist = res.tjdatalist || [];
				that.showtoptabbar = res.showtoptabbar || 0;
				that.bboglist = res.bboglist;
				that.sharepic = product.pics[0];
				that.xcx_scheme = res.xcx_scheme
				uni.setNavigationBarTitle({
					title: product.name
				});
				
				that.kfurl = '/pagesExt/kefu/index?bid='+product.bid;
				if(app.globalData.initdata.kfurl != ''){
					that.kfurl = app.globalData.initdata.kfurl;
				}
				if(that.business && that.business.kfurl){
					that.kfurl = that.business.kfurl;
				}
				that.loaded({title:product.sharetitle || product.name,pic:product.sharepic || product.pic,desc:product.sharedesc || product.sellpoint});
			
				setTimeout(function(){
					let view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0')
					view0.fields({
						size: true,//是否返回节点尺寸（width height）
						rect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
						scrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					}, (res) => {
						console.log(res)
						that.scrolltab0Height = res.height
					}).exec();
					let view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1')
					view1.fields({
						size: true,//是否返回节点尺寸（width height）
						rect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
						scrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					}, (res) => {
						console.log(res)
						that.scrolltab1Height = res.height
					}).exec();
					let view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2')
					view2.fields({
						size: true,//是否返回节点尺寸（width height）
						rect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
						scrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					}, (res) => {
						console.log(res)
						that.scrolltab2Height = res.height
					}).exec();
				},500)

			});
		},
		swiperChange: function (e) {
			var that = this;
			that.current = e.detail.current
		},
		payvideo: function () {
			this.isplay = 1;
			uni.createVideoContext('video').play();
		},
		parsevideo: function () {
			this.isplay = 0;
			uni.createVideoContext('video').stop();
		},
		buydialogChange: function (e) {
			if(!this.buydialogShow){
				this.btntype = e.currentTarget.dataset.btntype
			}
			this.buydialogShow = !this.buydialogShow;
		},
		//收藏操作
		addfavorite: function () {
			var that = this;
			var proid = that.product.id;
			app.post('ApiShop/addfavorite', {proid: proid,type: 'shop'}, function (data) {
				if (data.status == 1) {
					that.isfavorite = !that.isfavorite;
				}
				app.success(data.msg);
			});
		},
		shareClick: function () {
			this.sharetypevisible = true;
		},
		handleClickMask: function () {
			this.sharetypevisible = false
		},
		showPoster: function () {
			var that = this;
			that.showposter = true;
			that.sharetypevisible = false;
			app.showLoading('生成海报中');
			app.post('ApiShop/getposter', {proid: that.product.id}, function (data) {
				app.showLoading(false);
				if (data.status == 0) {
					app.alert(data.msg);
				} else {
					that.posterpic = data.poster;
				}
			});
		},
		
		shareScheme: function () {
			var that = this;
			app.showLoading();
			app.post('ApiShop/getwxScheme', {proid: that.product.id}, function (data) {
				app.showLoading(false);
				if (data.status == 0) {
					app.alert(data.msg);
				} else {
						that.showScheme = true;
						that.schemeurl=data.openlink
				}
			});
		},
		
		schemeDialogClose: function () {
			this.showScheme = false;
		},
		
		posterDialogClose: function () {
			this.showposter = false;
		},
		showfuwudetail: function () {
			this.showfuwudialog = true;
		},
		hidefuwudetail: function () {
			this.showfuwudialog = false
		},
		showcuxiaodetail: function () {
			this.showcuxiaodialog = true;
		},
		hidecuxiaodetail: function () {
			this.showcuxiaodialog = false
		},
		getcoupon:function(){
			this.showcuxiaodialog = false;
			this.getdata();
		},
		onPageScroll: function (e) {
			//var that = this;
			//var scrollY = e.scrollTop;     
			//if (scrollY > 200) {
			//	that.scrolltopshow = true;
			//}
			//if(scrollY < 150) {
			//	that.scrolltopshow = false
			//}
			//if (scrollY > 100) {
			//	that.toptabbar_show = true;
			//}
			//if(scrollY < 50) {
			//	that.toptabbar_show = false
			//}
		},
		changetoptab:function(e){
			var index = e.currentTarget.dataset.index;
			this.scrollToViewId = 'scroll_view_tab'+index;
			this.toptabbar_index = index;
			if(index == 0) this.scrollTop = 0;
			console.log(index);
		},
		scroll:function(e){
			var scrollTop = e.detail.scrollTop;
			//console.log(e)
			var that = this;
			if (scrollTop > 200) {
				that.scrolltopshow = true;
			}
			if(scrollTop < 150) {
				that.scrolltopshow = false
			}
			if (scrollTop > 100) {
				that.toptabbar_show = true;
			}
			if(scrollTop < 50) {
				that.toptabbar_show = false
			}
			var height0 = that.scrolltab0Height;
			var height1 = that.scrolltab0Height + that.scrolltab1Height;
			var height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;
			//var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;
			console.log('-----------------------');
			console.log(scrollTop);
			console.log(height2);
			if(scrollTop >=0 && scrollTop < height0){
				//this.scrollToViewId = 'scroll_view_tab0';
				this.toptabbar_index = 0;
			}else if(scrollTop >= height0 && scrollTop < height1){
				//this.scrollToViewId = 'scroll_view_tab1';
				this.toptabbar_index = 1;
			}else if(scrollTop >= height1 && scrollTop < height2){
				//this.scrollToViewId = 'scroll_view_tab2';
				this.toptabbar_index = 2;
			}else if(scrollTop >= height2){
				//this.scrollToViewId = 'scroll_view_tab3';
				this.toptabbar_index = 3;
			}
		},
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			that.sharetypevisible = false;
			uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.product.sharetitle || that.product.name;
						sharedata.summary = that.product.sharedesc || that.product.sellpoint;
						sharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/shopPackage/shop/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;
						sharedata.imageUrl = that.product.pic;
						var sharelist = app.globalData.initdata.sharelist;
						if(sharelist){
							for(var i=0;i<sharelist.length;i++){
								if(sharelist[i]['indexurl'] == '/shopPackage/shop/product'){
									sharedata.title = sharelist[i].title;
									sharedata.summary = sharelist[i].desc;
									sharedata.imageUrl = sharelist[i].pic;
									if(sharelist[i].url){
										var sharelink = sharelist[i].url;
										if(sharelink.indexOf('/') === 0){
											sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;
										}
										if(app.globalData.mid>0){
											 sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;
										}
										sharedata.href = sharelink;
									}
								}
							}
						}
						uni.share(sharedata);
					}
        }
      });
		},
		showsubqrcode:function(){
			this.$refs.qrcodeDialog.open();
		},
		closesubqrcode:function(){
			this.$refs.qrcodeDialog.close();
		},
		addcart:function(e){
			console.log(e)
			this.cartnum = this.cartnum + e.num;
		},
		showgg1Dialog:function(){
			this.$refs.gg1Dialog.open();
		},
		closegg1Dialog:function(){
			this.$refs.gg1Dialog.close();
		},
		showgg2Dialog:function(){
			this.$refs.gg2Dialog.open();
		},
		closegg2Dialog:function(){
			this.$refs.gg2Dialog.close();
		},
	}

};
</script>
<style>
page {position: relative;width: 100%;height: 100%;}
.container{height:100%}

.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}
.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}
.follow_topbar .headimg image {height:64rpx; width:64rpx;}
.follow_topbar .info {height:56rpx; padding:16rpx 0;}
.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;} 
.follow_topbar .info {height:80rpx; float:left;}
.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}
.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.qrcodebox .img{width:400rpx;height:400rpx}
.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}

.swiper-container{position:relative;height: 750rpx;overflow: hidden;}
.swiper {width: 100%;height: 750rpx;overflow: hidden;}
.swiper-item-view{width: 100%;height: 750rpx;}
.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}

.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}

.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}
.provideo image{width:50rpx;height:50rpx;}
.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}

.videobox{width:100%;height:750rpx;text-align:center;background:#000}
.videobox .video{width:100%;height:650rpx;}
.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx}

.header {width: 100%;padding: 20rpx 3%;background: #fff;}
.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between}
.header .price_share .price{display:flex;align-items:flex-end}
.header .price_share .price .f1{font-size:50rpx;color:#51B539;font-weight:bold}
.header .price_share .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}
.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center;min-width: 60rpx;}
.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}
.header .price_share .share .txt{color:#333333;font-size:20rpx}
.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}
.header .price_share .title { display:flex;align-items:flex-end;}
.header .sellpoint{font-size:28rpx;color: #666;padding-top:20rpx;}
.header .sales_stock{display:flex;justify-content:space-between;height:60rpx;line-height:60rpx;margin-top:30rpx;font-size:24rpx;color:#777777}
.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}
.header .upsavemoney{display:flex;align-items:center;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:70rpx;padding:0 20rpx}

.choose{ display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; height: 88rpx; line-height: 88rpx;padding: 0 3%; color: #333; }
.choose .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}
.choose .f2{ width: 32rpx; height: 32rpx;}

.cuxiaodiv{background:#fff;margin-top:20rpx;padding:0 3%;}

.fuwupoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}
.fuwupoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}
.fuwupoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}
.fuwupoint .f1 .t{ padding:4rpx 20rpx 4rpx 0;color:#777;flex-shrink:0}
.fuwupoint .f1 .t:before{content: "";display: inline-block;vertical-align: middle;	margin-top: -4rpx;margin-right: 10rpx;	width: 24rpx;	height: 24rpx;	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}
.fuwupoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}
.fuwupoint .f2 .img{width:32rpx;height:32rpx;}
.fuwudialog-content{font-size:24rpx}
.fuwudialog-content .f1{color:#333;height:80rpx;line-height:80rpx;font-weight:bold}
.fuwudialog-content .f1:before{content: "";display: inline-block;vertical-align: middle;	margin-top: -4rpx;margin-right: 10rpx;	width: 24rpx;	height: 24rpx;	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}
.fuwudialog-content .f2{color:#777}

.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}
.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}
.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}
.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}
.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}
.cuxiaopoint .f1 .t1{padding:0 4px}
.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}
.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}
.cuxiaodiv .cuxiaoitem{border-bottom:1px solid #E6E6E6;}
.cuxiaodiv .cuxiaoitem:last-child{border-bottom:0}

.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}
.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}
.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1000rpx;}
.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}
.popup__title-text{font-size:32rpx}
.popup__close{position:absolute;top:34rpx;right:34rpx}
.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}
.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}
.service-item .prefix{padding-top: 2px;}
.service-item .suffix{padding-left: 10rpx;}
.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}


.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}
.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}
.shop .p2{padding-left:10rpx}
.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}
.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}
.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}

.detail{min-height:200rpx;}

.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}
.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}
.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}
.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}

.commentbox{width:100%;background:#fff;padding:0 3%;margin-top:20rpx}
.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}
.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}
.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}
.commentbox .nocomment{height:100rpx;line-height:100rpx}

.comment{display:flex;flex-direction:column;min-height:200rpx;}
.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}
.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}
.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}
.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}
.comment .item .f1 .t3{text-align:right;}
.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}
.comment .item .score{ font-size: 24rpx;color:#f99716;}
.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}
.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}
.comment .item .f2 .t1{color:#333;font-size:28rpx;}
.comment .item .f2 .t2{display:flex;width:100%}
.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}
.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}
.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}

.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}
.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}
.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:80rpx;position:relative}
.bottombar .f1 .item .img{ width:44rpx;height:44rpx}
.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}
.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}
.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}
.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}
.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}

.bottombar .op2{width:60%;overflow:hidden;display:flex;}
.bottombar .tocart2{ flex:1;height: 80rpx;border-radius:10rpx;color: #fff; background: #fa938a; font-size: 28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin-right:10rpx;}
.bottombar .tobuy2{ flex:1; height: 80rpx;border-radius:10rpx;color: #fff; background: #df2e24; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center}


.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx;display:flex;padding:50rpx;align-items:flex-end}
.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}
.sharetypecontent button::after{border:0}
.sharetypecontent .f1 .img{width:90rpx;height:90rpx}
.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}
.sharetypecontent .f2 .img{width:90rpx;height:90rpx}
.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}

.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}
.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}
.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}
.posterDialog .close .img{ width:32rpx;height:32rpx;}
.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}
.posterDialog .content .img{width:540rpx;height:960rpx}
.linkDialog {background:rgba(0,0,0,0.4);z-index:11;}
.linkDialog .main{ width: 90%; position: fixed; top: 50%; left: 50%; margin: 0;
		-webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);}
.linkDialog .title {font-weight: bold;margin-bottom: 30rpx;}
.linkDialog .row {display: flex; height:80rpx;line-height: 80rpx; padding: 0 16rpx;}
.linkDialog .row .f1 {width: 40%; text-align: left;}
.linkDialog .row .f2 {width: 60%; height:80rpx;line-height: 80rpx;text-align: right;align-items:center;}
.linkDialog .image{width: 28rpx; height: 28rpx; margin-left: 8rpx;margin-top: 2rpx;}
.linkDialog .copyicon {width: 28rpx; height: 28rpx; margin-left: 8rpx; position: relative; top: 4rpx;}

.paramitem{display:flex;border-bottom:1px solid #f5f5f5;padding:20rpx}
.paramitem .f1{width:30%;color:#666}
.paramitem .f2{color:#333}
.paramitem:last-child{border-bottom:0}

.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:20rpx 160rpx;margin-top:20rpx}
.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}
.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}
.xihuan-text .txt{color:#111;font-size:30rpx}
.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}
.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}

.toptabbar_tab{display:flex;width:100%;height:90rpx;background: #fff;top:var(--window-top);z-index:11;position:fixed;border-bottom:1px solid #f3f3f3}
.toptabbar_tab .item{flex:1;font-size:28rpx; text-align:center; color:#666; height: 90rpx; line-height: 90rpx;overflow: hidden;position:relative}
.toptabbar_tab .item .after{display:none;position:absolute;left:50%;margin-left:-16rpx;bottom:10rpx;height:3px;border-radius:1.5px;width:32rpx}
.toptabbar_tab .on{color: #323233;}
.toptabbar_tab .on .after{display:block}

.scrolltop{position:fixed;bottom:160rpx;right:20rpx;width:60rpx;height:60rpx;background:rgba(0,0,0,0.4);color:#fff;border-radius:50%;padding:12rpx 10rpx 8rpx 10rpx;z-index:9;}
.scrolltop .image{width:100%;height:100%;}

.ggdiaplog_close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}

.schemeDialog {background:rgba(0,0,0,0.4);z-index:12;}
.schemeDialog .main{ position: absolute;top:30%}
.schemecon{padding: 40rpx 30rpx; }
.copybtn{ text-align: center; margin-top: 30rpx; padding:15rpx 20rpx; border-radius: 50rpx; color:#fff}

.huang_bz{margin: auto;padding-left: 6px;}
.huang_nums{padding: 2px 5px; background: #97e29d;border-radius: 10px;color: #fff;font-size: 10px;}

</style>