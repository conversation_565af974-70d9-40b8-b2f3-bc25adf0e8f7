<template>
	<view class="camera-container">
		<!-- 拍摄模式 -->
		<view v-if="!capturedImage" class="camera-mode">
			<!-- 相机预览区域 -->
			<!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ -->
			<camera
				class="camera-preview"
				:device-position="devicePosition"
				:flash="flashMode"
				@error="onCameraError"
				@initdone="onCameraInit"
			>
			</camera>
			<!-- #endif -->

			<!-- #ifdef H5 -->
			<!-- H5环境下显示替代预览区域 -->
			<view class="camera-preview h5-camera-placeholder">
				<view class="h5-camera-content">
					<text class="h5-camera-text">相机预览</text>
					<text class="h5-camera-tip">点击拍照按钮选择图片</text>
				</view>
			</view>
			<!-- #endif -->

			<!-- 顶部状态栏 -->
			<view class="status-bar">
				<view class="back-btn" @touchstart="goBack" @tap="goBack">
					<text class="back-icon">←</text>
				</view>
				<text class="page-title">面诊拍摄</text>
				<view class="flash-btn" :class="{ active: flashOn }" @touchstart="toggleFlash" @tap="toggleFlash">
					<text class="flash-icon">{{ flashOn ? '💡' : '🔦' }}</text>
				</view>
			</view>

			<!-- 拍摄指导区域 -->
			<view class="guide-overlay">
				<view class="guide-frame">
					<view class="frame-corner corner-tl"></view>
					<view class="frame-corner corner-tr"></view>
					<view class="frame-corner corner-bl"></view>
					<view class="frame-corner corner-br"></view>
					<view class="frame-center">
						<text class="guide-text">请将面部置于框内</text>
						<view class="pulse-dot"></view>
					</view>
				</view>
			</view>

			<!-- 拍摄提示区域 -->
			<view class="tips-section">
				<view class="tips-container">
					<view class="tip-item" v-for="(tip, index) in shootingTips" :key="index">
						<view class="tip-icon">{{ tip.icon }}</view>
						<text class="tip-text">{{ tip.text }}</text>
					</view>
				</view>
			</view>

			<!-- 控制区域 -->
			<view class="control-section">
				<view class="control-container">
					<!-- 选择图片 -->
					<view class="control-item">
						<view class="control-btn secondary" @touchstart="chooseImage" @tap="chooseImage">
							<image class="control-icon-img" :src="pre_url + '/static/img/xuanzetupian.png'" mode="aspectFit"></image>
						</view>
						<text class="control-label">选择图片</text>
					</view>

					<!-- 拍照按钮 -->
					<view class="control-item capture">
						<view class="control-btn primary" :class="{ capturing: captureLoading }" @touchstart="takePhoto" @tap="takePhoto">
							<view class="capture-ring" v-if="!captureLoading"></view>
							<view class="capture-dot"></view>
						</view>
						<text class="control-label">拍照</text>
					</view>

					<!-- 翻转相机 -->
					<view class="control-item">
						<view class="control-btn secondary" @touchstart="flipCamera" @tap="flipCamera" :class="{ 'active': isFlipping }">
							<image class="control-icon-img" :src="pre_url + '/static/img/fanzhuanxiangji.png'" mode="aspectFit"></image>
						</view>
						<text class="control-label">翻转相机</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 预览模式 -->
		<view v-else class="preview-mode">
			<!-- 调试信息 -->
			
			<!-- 科技感顶部区域 -->
			<view class="preview-header">
				<view class="header-bg-pattern"></view>
				<view class="header-content">
					<view class="status-indicator">
						<view class="status-dot"></view>
						<text class="status-text">AI 分析就绪</text>
					</view>
					<text class="preview-title">图像预览</text>
					<text class="preview-subtitle">AI 正在检测图像质量</text>
				</view>
			</view>

			<!-- 图片预览区域 -->
			<view class="preview-container">
				<view class="preview-main">
					<!-- 图片容器 -->
					<view class="image-container" style="border: 2px solid red;">
						<view class="image-frame" style="border: 2px solid blue;">
							<!-- 调试信息 -->
						

							<image
								:src="capturedImage"
								mode="aspectFit"
								class="preview-image"
								@error="onImageError"
								@load="onImageLoad"
								:show-loading="true"
								style="border: 2px solid yellow;"
							></image>

							<!-- 图片加载失败提示 -->
							<view v-if="imageLoadError" class="image-error">
								<text class="error-text">图片加载失败</text>
								<text class="error-tip">请重新拍摄或选择图片</text>
							</view>

							<!-- 扫描线效果 -->
							<view class="scan-line"></view>

							<!-- AI 分析点位 -->
							<view class="analysis-overlay">
								<view
									class="analysis-point"
									v-for="(point, index) in analysisPoints"
									:key="index"
									:style="{
										left: point.x + '%',
										top: point.y + '%',
										'animation-delay': (index * 0.2) + 's'
									}"
								>
									<view class="point-core"></view>
									<view class="point-ring"></view>
									<view class="point-pulse"></view>
								</view>
							</view>

							<!-- 网格线 -->
							<view class="grid-overlay">
								<view class="grid-line grid-vertical" v-for="i in 3" :key="'v'+i" :style="{ left: (i * 25) + '%' }"></view>
								<view class="grid-line grid-horizontal" v-for="i in 3" :key="'h'+i" :style="{ top: (i * 25) + '%' }"></view>
							</view>
						</view>
					</view>

					<!-- 质量检测面板 -->
					<view class="quality-panel">
						<view class="panel-header">
							<view class="panel-icon">🔍</view>
							<text class="panel-title">质量检测</text>
							<view class="panel-status">
								<view class="status-bar">
									<view class="status-progress" :style="{ width: getQualityScore() + '%' }"></view>
								</view>
								<text class="status-score">{{ getQualityScore() }}%</text>
							</view>
						</view>

						<view class="quality-grid">
							<view class="quality-card" v-for="(check, index) in qualityChecks" :key="index">
								<view class="card-icon" :class="check.status">
									<text>{{ check.status === 'pass' ? '✓' : '⚠' }}</text>
								</view>
								<text class="card-text">{{ check.text }}</text>
								<view class="card-indicator" :class="check.status"></view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 科技感操作区域 -->
			<view class="preview-actions">
				<view class="action-container">
					<view class="action-btn secondary" @touchstart="retakePhoto" @tap="retakePhoto">
						<view class="btn-bg"></view>
						<view class="btn-content">
							<view class="btn-icon">
								<text>↻</text>
							</view>
							<text class="btn-text">重新拍摄</text>
						</view>
					</view>

					<view class="action-btn primary" @touchstart="confirmPhoto" @tap="confirmPhoto">
						<view class="btn-bg"></view>
						<view class="btn-content">
							<view class="btn-icon">
								<text>⚡</text>
							</view>
							<text class="btn-text">开始分析</text>
						</view>
						<view class="btn-particles">
							<view class="particle" v-for="i in 6" :key="i" :style="{ 'animation-delay': (i * 0.1) + 's' }"></view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载遮罩 -->
		<view v-if="isLoading" class="loading-overlay">
			<view class="loading-content">
				<view class="loading-spinner">
					<view class="spinner-ring"></view>
					<view class="spinner-dot"></view>
				</view>
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'FaceCamera',
	data() {
		return {
			// 云资源前缀URL
			pre_url: '',

			// 相机状态
			cameraReady: false,
			capturedImage: '',
			captureLoading: false,
			flashOn: false,
			flashMode: 'off',
			devicePosition: 'front', // 面诊使用前置摄像头
			isFlipping: false, // 是否正在切换相机

			// 加载状态
			isLoading: false,
			loadingText: '正在拍摄...',

			// 防重复调用标识
			isProcessingPayment: false,

			// 拍摄提示
			shootingTips: [
				{ icon: '💡', text: '保持充足光线' },
				{ icon: '📱', text: '手机保持稳定' },
				{ icon: '😊', text: '表情自然' },
				{ icon: '🎯', text: '对准拍摄框' }
			],

			// 分析点位（面部关键点）
			analysisPoints: [
				{ x: 25, y: 30 }, // 左眼
				{ x: 75, y: 30 }, // 右眼
				{ x: 50, y: 45 }, // 鼻子
				{ x: 35, y: 70 }, // 左嘴角
				{ x: 65, y: 70 }  // 右嘴角
			],

			// 质量检测结果
			qualityChecks: [
				{ text: '图片清晰度', status: 'pass' },
				{ text: '光线充足', status: 'pass' },
				{ text: '面部完整', status: 'pass' },
				{ text: '角度适宜', status: 'warning' }
			],

			// 面诊配置信息
			configData: {},

			// 图片加载状态
			imageLoadError: false
		}
	},
	onLoad() {
		console.log('2025-07-17 INFO-[face-camera][onLoad_001] 面诊拍摄页面加载完成');

		// 重置处理标识
		this.isProcessingPayment = false;

		// 初始化云资源前缀URL
		const app = getApp();
		this.pre_url = app.globalData.pre_url || '';

		// 初始化相机权限检查
		this.checkCameraPermission();

		// 获取面诊配置信息
		this.getFaceConfig();
	},

	// 页面卸载时重置状态
	onUnload() {
		console.log('2025-07-17 INFO-[face-camera][onUnload_002] 面诊拍摄页面卸载，重置处理标识');
		this.isProcessingPayment = false;
	},
	methods: {
		/**
		 * 检查相机权限
		 */
		checkCameraPermission() {
			console.log('2025-07-17 INFO-[face-camera][checkCameraPermission_001] 检查相机权限');

			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
			// 只在小程序环境下检查相机权限
			uni.authorize({
				scope: 'scope.camera',
				success: () => {
					console.log('2025-07-17 INFO-[face-camera][checkCameraPermission_002] 相机权限获取成功');
				},
				fail: () => {
					console.log('2025-07-17 ERROR-[face-camera][checkCameraPermission_003] 相机权限获取失败');
					uni.showModal({
						title: '权限提示',
						content: '需要相机权限才能进行面诊拍摄',
						success: (res) => {
							if (res.confirm) {
								uni.openSetting();
							}
						}
					});
				}
			});
			// #endif

			// #ifdef H5
			// H5环境下直接设置相机就绪状态
			console.log('2025-07-17 INFO-[face-camera][checkCameraPermission_004] H5环境下跳过权限检查');
			this.cameraReady = true;
			// #endif
		},

		/**
		 * 相机初始化完成
		 */
		onCameraInit() {
			console.log('2025-07-17 INFO-[face-camera][onCameraInit_001] 相机初始化完成');
			this.cameraReady = true;
		},

		/**
		 * 相机错误处理
		 */
		onCameraError(error) {
			console.error('2025-07-17 ERROR-[face-camera][onCameraError_001] 相机错误:', error);
			uni.showToast({
				title: '相机启动失败',
				icon: 'none'
			});
		},

		/**
		 * 选择图片
		 */
		chooseImage() {
			console.log('2025-07-17 INFO-[face-camera][chooseImage_001] 选择图片按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			const app = getApp();
			if (app && app.chooseImage) {
				// 直接使用app的chooseImage方法，它会处理图片选择和上传
				app.chooseImage((imageUrls) => {
					console.log('2025-07-17 INFO-[face-camera][chooseImage_002] 选择并上传图片成功:', imageUrls);

					if (imageUrls && imageUrls.length > 0) {
						// 获取上传后的图片链接
						const imageUrl = imageUrls[0];
						console.log('2025-07-17 INFO-[face-camera][chooseImage_003] 设置图片URL:', imageUrl);

						// 重置错误状态
						this.imageLoadError = false;

						// 先展示一下图片
						this.capturedImage = imageUrl;

						// 检查图片质量
						this.checkImageQuality();
					}
				});
			} else {
				// 备用方案：使用uni.chooseImage
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: (res) => {
						console.log('2025-07-17 INFO-[face-camera][chooseImage_004] 选择图片成功:', res.tempFilePaths[0]);

						// 重置错误状态
						this.imageLoadError = false;

						// 只显示临时图片，不进行上传（在确认时再上传）
						this.capturedImage = res.tempFilePaths[0];

						// 检查图片质量
						this.checkImageQuality();

						uni.showToast({
							title: '图片选择成功',
							icon: 'success',
							duration: 2000
						});
					},
					fail: (error) => {
						console.error('2025-07-17 ERROR-[face-camera][chooseImage_004] 选择图片失败:', error);
					}
				});
			}
		},

		/**
		 * 拍照
		 */
		takePhoto() {
			console.log('2025-07-17 INFO-[face-camera][takePhoto_001] 拍照按钮被点击');

			if (this.captureLoading) {
				console.log('2025-07-17 WARNING-[face-camera][takePhoto_002] 正在拍照中，忽略重复点击');
				return;
			}

			// 添加触觉反馈
			uni.vibrateShort();

			this.captureLoading = true;
			this.loadingText = '正在拍摄...';

			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
			const ctx = uni.createCameraContext();
			ctx.takePhoto({
				quality: 'high',
				success: (res) => {
					console.log('2025-07-17 INFO-[face-camera][takePhoto_003] 拍照成功:', res.tempImagePath);

					// 重置错误状态
					this.imageLoadError = false;

					// 只显示临时图片，不进行上传（在确认时再上传）
					this.capturedImage = res.tempImagePath;
					this.captureLoading = false;

					// 检查图片质量
					this.checkImageQuality();

					uni.showToast({
						title: '拍照成功',
						icon: 'success',
						duration: 2000
					});
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-camera][takePhoto_004] 拍照失败:', error);
					this.captureLoading = false;
					uni.showToast({
						title: '拍照失败，请重试',
						icon: 'none'
					});
				}
			});
			// #endif

			// #ifdef H5
			// H5环境下直接选择图片
			this.chooseImage();
			// #endif
		},

		/**
		 * 上传图片
		 */
		uploadImage(imagePath) {
			console.log('2025-07-17 INFO-[face-camera][uploadImage_001] 开始上传图片:', imagePath);

			this.loadingText = '正在上传...';

			const app = getApp();
			if (app && app.uploadImage) {
				app.uploadImage(imagePath, (imageUrl) => {
					console.log('2025-07-17 INFO-[face-camera][uploadImage_002] 图片上传成功:', imageUrl);

					// 重置错误状态
					this.imageLoadError = false;

					this.capturedImage = imageUrl;
					this.captureLoading = false;
					this.checkImageQuality();
				}, (error) => {
					console.error('2025-07-17 ERROR-[face-camera][uploadImage_003] 图片上传失败:', error);
					this.captureLoading = false;
					uni.showToast({
						title: '图片上传失败',
						icon: 'none'
					});
				});
			} else {
				// 备用方案：使用uni.uploadFile上传图片
				console.log('2025-07-17 WARNING-[face-camera][uploadImage_004] 未找到app.uploadImage方法，使用备用上传方案');

				// 使用uni.uploadFile上传
				const app = getApp();
				uni.uploadFile({
					url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid +
						'/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,
					filePath: imagePath,
					name: 'file',
					success: (uploadRes) => {
						console.log('2025-07-17 INFO-[face-camera][uploadImage_005] 备用上传成功:', uploadRes);

						try {
							const result = JSON.parse(uploadRes.data);
							if (result.status == 1) {
								console.log('2025-07-17 INFO-[face-camera][uploadImage_006] 获取图片URL成功:', result.url);

								// 重置错误状态
								this.imageLoadError = false;

								this.capturedImage = result.url;
								this.captureLoading = false;
								this.checkImageQuality();
							} else {
								throw new Error('上传返回格式错误: ' + (result.msg || '未知错误'));
							}
						} catch (error) {
							console.error('2025-07-17 ERROR-[face-camera][uploadImage_006] 解析上传结果失败:', error);
							this.handleUploadError();
						}
					},
					fail: (error) => {
						console.error('2025-07-17 ERROR-[face-camera][uploadImage_007] 备用上传失败:', error);
						this.handleUploadError();
					}
				});
			}
		},

		/**
		 * 检查图片质量
		 */
		checkImageQuality() {
			console.log('2025-07-17 INFO-[face-camera][checkImageQuality_001] 开始检查图片质量');

			// 模拟质量检测
			setTimeout(() => {
				// 随机生成一些检测结果
				this.qualityChecks = [
					{ text: '图片清晰度', status: Math.random() > 0.2 ? 'pass' : 'warning' },
					{ text: '光线充足', status: Math.random() > 0.3 ? 'pass' : 'warning' },
					{ text: '面部完整', status: Math.random() > 0.1 ? 'pass' : 'warning' },
					{ text: '角度适宜', status: Math.random() > 0.4 ? 'pass' : 'warning' }
				];

				console.log('2025-07-17 INFO-[face-camera][checkImageQuality_002] 图片质量检测完成:', this.qualityChecks);
			}, 1000);
		},

		/**
		 * 翻转相机
		 */
		flipCamera() {
			console.log('2025-07-17 INFO-[face-camera][flipCamera_001] 翻转相机按钮被点击');

			if (this.isFlipping) {
				console.log('2025-07-17 WARNING-[face-camera][flipCamera_002] 正在翻转中，忽略重复点击');
				return;
			}

			// 添加触觉反馈
			uni.vibrateShort();

			this.isFlipping = true;
			this.devicePosition = this.devicePosition === 'front' ? 'back' : 'front';

			console.log('2025-07-17 INFO-[face-camera][flipCamera_003] 相机位置切换为:', this.devicePosition);

			setTimeout(() => {
				this.isFlipping = false;
			}, 500);
		},

		/**
		 * 切换闪光灯
		 */
		toggleFlash() {
			console.log('2025-07-17 INFO-[face-camera][toggleFlash_001] 闪光灯按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			this.flashOn = !this.flashOn;
			this.flashMode = this.flashOn ? 'on' : 'off';

			console.log('2025-07-17 INFO-[face-camera][toggleFlash_002] 闪光灯状态:', this.flashMode);
		},

		/**
		 * 重新拍摄
		 */
		retakePhoto() {
			console.log('2025-07-17 INFO-[face-camera][retakePhoto_001] 重新拍摄按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			this.capturedImage = '';
			this.captureLoading = false;
			this.isLoading = false;
			this.imageLoadError = false; // 重置图片加载错误状态

			// 重置质量检测结果
			this.qualityChecks = [
				{ text: '图片清晰度', status: 'pass' },
				{ text: '光线充足', status: 'pass' },
				{ text: '面部完整', status: 'pass' },
				{ text: '角度适宜', status: 'warning' }
			];
		},

		/**
		 * 确认照片并跳转到分析页面
		 */
		confirmPhoto() {
			console.log('2025-07-17 INFO-[face-camera][confirmPhoto_001] 确认照片按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			if (!this.capturedImage) {
				uni.showToast({
					title: '请先拍摄照片',
					icon: 'none'
				});
				return;
			}

			// 判断图片是否已经是服务器URL
			if (this.capturedImage.startsWith('http://') || this.capturedImage.startsWith('https://')) {
				// 已经是服务器URL，直接进行分析检查
				console.log('2025-07-17 INFO-[face-camera][confirmPhoto_002] 图片已是服务器URL，直接进行分析检查');
				this.checkFaceAnalysisInfo(this.capturedImage);
			} else {
				// 是本地路径，需要先上传
				console.log('2025-07-17 INFO-[face-camera][confirmPhoto_003] 图片为本地路径，需要先上传');
				// 显示上传进度
				this.isLoading = true;
				this.loadingText = '正在上传图片...';

				// 上传图片到服务器
				this.uploadImageToServer();
			}
		},

		/**
		 * 上传图片到服务器
		 */
		uploadImageToServer() {
			console.log('2025-07-17 INFO-[face-camera][uploadImageToServer_001] 开始上传图片:', this.capturedImage);

			const app = getApp();

			// 显示上传中提示
			uni.showLoading({
				title: '上传中...'
			});

			// 使用uni.uploadFile上传图片
			uni.uploadFile({
				url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid +
					'/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,
				filePath: this.capturedImage,
				name: 'file',
				success: (res) => {
					console.log('2025-07-17 INFO-[face-camera][uploadImageToServer_002] 上传图片成功:', res);

					uni.hideLoading();
					this.isLoading = false;

					// 解析响应数据
					try {
						const data = JSON.parse(res.data);

						if (data.status == 1) {
							console.log('2025-07-17 INFO-[face-camera][uploadImageToServer_003] 获取图片URL成功:', data.url);

							// 获取到上传后的图片URL，进行分析检查
							this.navigateToAnalysisPage(data.url);
						} else {
							console.error('2025-07-17 ERROR-[face-camera][uploadImageToServer_004] 上传图片失败:', data.msg);

							this.isProcessingPayment = false;
							uni.showToast({
								title: data.msg || '图片上传失败',
								icon: 'none',
								duration: 2000
							});
						}
					} catch (e) {
						console.error('2025-07-17 ERROR-[face-camera][uploadImageToServer_005] 解析响应数据失败:', e);

						this.isProcessingPayment = false;
						uni.showToast({
							title: '图片上传失败',
							icon: 'none',
							duration: 2000
						});
					}
				},
				fail: (err) => {
					console.error('2025-07-17 ERROR-[face-camera][uploadImageToServer_006] 上传图片失败:', err);

					uni.hideLoading();
					this.isLoading = false;
					this.isProcessingPayment = false;

					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},

		/**
		 * 跳转到分析页面
		 */
		navigateToAnalysisPage(imageUrl) {
			console.log('2025-07-17 INFO-[face-camera][navigateToAnalysisPage_001] 跳转到分析页面，图片链接:', imageUrl);

			// 检查分析信息并确认分析
			this.checkFaceAnalysisInfo(imageUrl);
		},

		/**
		 * 检查面诊分析信息并确认分析
		 */
		checkFaceAnalysisInfo(imageUrl) {
			console.log('2025-07-17 INFO-[face-camera][checkFaceAnalysisInfo_001] 开始检查分析信息');

			// 防止重复处理分析流程
			if (this.isProcessingPayment) {
				console.log('2025-07-17 INFO-[face-camera][checkFaceAnalysisInfo_002] 正在处理分析流程，忽略重复调用');
				return;
			}

			this.isProcessingPayment = true; // 设置处理中标识

			// 显示加载提示
			uni.showLoading({
				title: '检查分析信息...'
			});

			const app = getApp();
			app.post('ApiFaceAnalysis/getConfig', {
				image_url: imageUrl,
				use_free: 1  // 默认尝试使用免费次数
			}, (response) => {
				uni.hideLoading();
				this.isProcessingPayment = false; // 重置处理标识
				console.log('2025-07-17 INFO-[face-camera][checkFaceAnalysisInfo_003] 分析信息获取成功:', response);

				if (response && (response.status === 1 || response.code === 1) && response.data) {
					const analysisData = response.data;

					// 判断是否可以免费使用
					if (analysisData.can_use_free) {
						console.log('2025-07-17 INFO-[face-camera][checkFaceAnalysisInfo_004] 可以免费使用，直接进行分析');
						this.proceedToAnalysis(imageUrl);
						return;
					}

					// 检查价格和用户余额
					const price = parseFloat(analysisData.price) || 0;

					// 如果价格为0，视为可免费使用
					if (price <= 0) {
						console.log('2025-07-17 INFO-[face-camera][checkFaceAnalysisInfo_005] 价格为0，视为免费使用');
						this.proceedToAnalysis(imageUrl);
						return;
					}

					// 显示分析确认对话框
					this.showAnalysisConfirmDialog(analysisData, imageUrl);

				} else {
					console.error('2025-07-17 ERROR-[face-camera][checkFaceAnalysisInfo_006] 分析信息获取失败:', response);
					uni.showModal({
						title: '获取分析信息失败',
						content: response?.msg || '无法获取分析信息，请重试',
						showCancel: true,
						confirmText: '重试',
						cancelText: '返回',
						success: (res) => {
							if (res.confirm) {
								this.checkFaceAnalysisInfo(imageUrl);
							} else {
								uni.navigateBack();
							}
						}
					});
				}
			}, (error) => {
				uni.hideLoading();
				this.isProcessingPayment = false; // 重置处理标识
				console.error('2025-07-17 ERROR-[face-camera][checkFaceAnalysisInfo_007] 分析信息请求失败:', error);

				uni.showModal({
					title: '网络错误',
					content: '获取分析信息失败，请检查网络连接后重试',
					showCancel: true,
					confirmText: '重试',
					cancelText: '返回',
					success: (res) => {
						if (res.confirm) {
							this.checkFaceAnalysisInfo(imageUrl);
						} else {
							uni.navigateBack();
						}
					}
				});
			});
		},

		/**
		 * 显示分析确认对话框
		 */
		showAnalysisConfirmDialog(analysisData, imageUrl) {
			console.log('2025-07-17 INFO-[face-camera][showAnalysisConfirmDialog_001] 显示分析确认对话框');

			const price = parseFloat(analysisData.price) || 0;

			uni.showModal({
				title: '面诊分析',
				content: `本次面诊分析需要 ${price} 元，是否继续？`,
				showCancel: true,
				confirmText: '确认分析',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.proceedToAnalysis(imageUrl);
					} else {
						console.log('2025-07-17 INFO-[face-camera][showAnalysisConfirmDialog_002] 用户取消分析');
					}
				}
			});
		},

		/**
		 * 执行面诊分析
		 */
		proceedToAnalysis(imageUrl) {
			console.log('2025-07-17 INFO-[face-camera][proceedToAnalysis_001] 开始执行面诊分析');

			// 防止重复处理
			if (this.isProcessingPayment) {
				console.log('2025-07-17 WARNING-[face-camera][proceedToAnalysis_002] 正在处理中，忽略重复点击');
				return;
			}

			this.isProcessingPayment = true;

			// 显示分析加载状态
			this.isLoading = true;
			this.loadingText = '正在进行AI面诊分析...';

			// 调用面诊分析接口
			this.startFaceAnalysis(imageUrl);
		},

		/**
		 * 开始面诊分析
		 */
		startFaceAnalysis(imageUrl) {
			console.log('2025-07-17 INFO-[face-camera][startFaceAnalysis_001] 开始调用面诊分析接口');

			const app = getApp();

			// 显示分析加载状态
			this.isLoading = true;
			this.loadingText = '正在进行AI面诊分析...';

			// 调用新的面诊分析接口
			app.post('ApiFaceAnalysis/analyze', {
				image_url: imageUrl,
				use_free: this.configData && this.configData.can_use_free ? 1 : 0
			}, (response) => {
				this.isLoading = false;
				this.isProcessingPayment = false;

				console.log('2025-07-17 INFO-[face-camera][startFaceAnalysis_002] 面诊分析结果:', response);

				if (response && response.code === 1) {
					console.log('2025-07-17 INFO-[face-camera][startFaceAnalysis_003] 分析成功，跳转到结果页面');

					// 分析成功，跳转到结果页面
					uni.redirectTo({
						url: `/pagesB/diagnosis/face/result?recordId=${response.data.record_id}`,
						success: () => {
							console.log('2025-07-17 INFO-[face-camera][startFaceAnalysis_004] 跳转到结果页面成功');
						},
						fail: (error) => {
							console.error('2025-07-17 ERROR-[face-camera][startFaceAnalysis_005] 跳转到结果页面失败:', error);
						}
					});
				} else {
					console.error('2025-07-17 ERROR-[face-camera][startFaceAnalysis_006] 面诊分析失败:', response?.msg);
					uni.showToast({
						title: response?.msg || '分析失败，请重试',
						icon: 'none'
					});
				}
			}, (error) => {
				this.isLoading = false;
				this.isProcessingPayment = false;

				console.error('2025-07-17 ERROR-[face-camera][startFaceAnalysis_007] 面诊分析接口调用失败:', error);
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
			});
		},

		/**
		 * 获取面诊配置信息
		 */
		getFaceConfig() {
			console.log('2025-07-17 INFO-[face-camera][getFaceConfig_001] 获取面诊配置信息');

			const app = getApp();
			app.post('ApiFaceAnalysis/getConfig', {}, (response) => {
				if (response && response.code === 1) {
					console.log('2025-07-17 INFO-[face-camera][getFaceConfig_002] 获取配置成功:', response.data);
					this.configData = response.data;
				} else {
					console.error('2025-07-17 ERROR-[face-camera][getFaceConfig_003] 获取配置失败:', response);
				}
			}, (error) => {
				console.error('2025-07-17 ERROR-[face-camera][getFaceConfig_004] 获取配置接口调用失败:', error);
			});
		},

		/**
		 * 获取质量评分
		 */
		getQualityScore() {
			const passCount = this.qualityChecks.filter(check => check.status === 'pass').length;
			const totalCount = this.qualityChecks.length;
			return Math.round((passCount / totalCount) * 100);
		},

		/**
		 * 处理上传错误
		 */
		handleUploadError() {
			this.captureLoading = false;
			this.imageLoadError = true;
			uni.showToast({
				title: '图片上传失败，请重试',
				icon: 'none'
			});
		},

		/**
		 * 图片加载成功
		 */
		onImageLoad() {
			console.log('2025-07-17 INFO-[face-camera][onImageLoad_001] 图片加载成功');
			this.imageLoadError = false;
		},

		/**
		 * 图片加载失败
		 */
		onImageError(error) {
			console.error('2025-07-17 ERROR-[face-camera][onImageError_001] 图片加载失败:', error);
			this.imageLoadError = true;
		},

		/**
		 * 返回上一页
		 */
		goBack() {
			console.log('2025-07-17 INFO-[face-camera][goBack_001] 返回按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			uni.navigateBack({
				success: () => {
					console.log('2025-07-17 INFO-[face-camera][goBack_002] 返回上一页成功');
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-camera][goBack_003] 返回失败:', error);
				}
			});
		}
	}
}
</script>

<style>
/* 这里会添加完整的样式，参考舌诊拍摄页面的样式 */
.camera-container {
	position: relative;
	width: 100vw;
	height: 100vh;
	background: #000;
	overflow: hidden;
}

.camera-mode {
	width: 100%;
	height: 100%;
	position: relative;
}

.preview-mode {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
	color: #fff;
}

.camera-preview {
	width: 100%;
	height: 100%;
}

.h5-camera-placeholder {
	background: #333;
	display: flex;
	align-items: center;
	justify-content: center;
}

.h5-camera-content {
	text-align: center;
	color: #fff;
}

.h5-camera-text {
	font-size: 32rpx;
	display: block;
	margin-bottom: 20rpx;
}

.h5-camera-tip {
	font-size: 24rpx;
	opacity: 0.7;
}

.status-bar {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	background: linear-gradient(180deg, rgba(0,0,0,0.5) 0%, transparent 100%);
	z-index: 10;
}

.back-btn, .flash-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(0,0,0,0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}

.back-icon, .flash-icon {
	font-size: 32rpx;
}

.page-title {
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
}

.guide-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 5;
}

.guide-frame {
	position: relative;
	width: 500rpx;
	height: 600rpx;
}

.frame-corner {
	position: absolute;
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #fff;
}

.corner-tl {
	top: 0;
	left: 0;
	border-right: none;
	border-bottom: none;
}

.corner-tr {
	top: 0;
	right: 0;
	border-left: none;
	border-bottom: none;
}

.corner-bl {
	bottom: 0;
	left: 0;
	border-right: none;
	border-top: none;
}

.corner-br {
	bottom: 0;
	right: 0;
	border-left: none;
	border-top: none;
}

.frame-center {
	position: absolute;
	bottom: -80rpx;
	left: 50%;
	transform: translateX(-50%);
	text-align: center;
}

.guide-text {
	color: #fff;
	font-size: 28rpx;
	text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

.pulse-dot {
	width: 20rpx;
	height: 20rpx;
	background: #fff;
	border-radius: 50%;
	margin: 20rpx auto 0;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.2); }
}

.tips-section {
	position: absolute;
	top: 200rpx;
	left: 30rpx;
	right: 30rpx;
	z-index: 5;
}

.tips-container {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	flex-wrap: wrap;
}

.tip-item {
	background: rgba(0,0,0,0.6);
	border-radius: 30rpx;
	padding: 25rpx 35rpx;
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 160rpx;
	max-width: 220rpx;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255,255,255,0.1);
	box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.3);
	transition: all 0.3s ease;
}

.tip-item:hover {
	background: rgba(0,0,0,0.7);
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.4);
}

.tip-icon {
	font-size: 28rpx;
	margin-right: 15rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.3));
}

.tip-text {
	color: #fff;
	font-size: 24rpx;
	font-weight: 500;
	text-shadow: 0 1rpx 3rpx rgba(0,0,0,0.5);
	letter-spacing: 0.5rpx;
	flex: 1;
	text-align: left;
}

/* 响应式布局优化 */
@media screen and (max-width: 750rpx) {
	.tips-container {
		flex-wrap: wrap;
		justify-content: center;
		gap: 15rpx;
	}

	.tip-item {
		min-width: 140rpx;
		max-width: 200rpx;
		padding: 20rpx 25rpx;
	}

	.tip-icon {
		font-size: 24rpx;
		margin-right: 12rpx;
	}

	.tip-text {
		font-size: 22rpx;
	}
}

/* 小屏幕设备优化 */
@media screen and (max-width: 600rpx) {
	.tips-container {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: center;
		align-items: center;
		gap: 12rpx;
	}

	.tip-item {
		flex: 0 0 auto;
		width: auto;
		min-width: 140rpx;
		max-width: 250rpx;
		justify-content: flex-start;
		padding: 18rpx 25rpx;
	}

	.tip-text {
		font-size: 20rpx;
		flex: 1;
	}

	.tip-icon {
		font-size: 22rpx;
		margin-right: 10rpx;
		flex-shrink: 0;
	}
}

.control-section {
	position: absolute;
	bottom: 60rpx;
	left: 0;
	right: 0;
	z-index: 10;
}

.control-container {
	display: flex;
	align-items: center;
	justify-content: space-around;
	padding: 0 60rpx;
}

.control-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.control-btn {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.control-btn.secondary {
	background: rgba(255,255,255,0.2);
	border: 2rpx solid rgba(255,255,255,0.3);
}

.control-btn.primary {
	background: #fff;
	position: relative;
}

.control-btn.capture {
	width: 140rpx;
	height: 140rpx;
}

.capture-ring {
	position: absolute;
	top: 10rpx;
	left: 10rpx;
	right: 10rpx;
	bottom: 10rpx;
	border: 4rpx solid #007aff;
	border-radius: 50%;
}

.capture-dot {
	width: 80rpx;
	height: 80rpx;
	background: #007aff;
	border-radius: 50%;
}

.control-btn.capturing .capture-dot {
	background: #ff3b30;
	animation: capturing 0.3s ease;
}

@keyframes capturing {
	0% { transform: scale(1); }
	50% { transform: scale(0.8); }
	100% { transform: scale(1); }
}

.control-icon-img {
	width: 60rpx;
	height: 60rpx;
}

.control-label {
	color: #fff;
	font-size: 22rpx;
	text-align: center;
}

/* 科技感预览头部 */
.preview-header {
	position: relative;
	padding: 80rpx 30rpx 40rpx;
	background: linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 100%);
	border-bottom: 2rpx solid rgba(0, 255, 255, 0.3);
	overflow: hidden;
}

.header-bg-pattern {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image:
		radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
		radial-gradient(circle at 80% 80%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
	animation: pattern-float 8s ease-in-out infinite;
}

@keyframes pattern-float {
	0%, 100% { transform: translateY(0) scale(1); }
	50% { transform: translateY(-20rpx) scale(1.05); }
}

.header-content {
	position: relative;
	z-index: 2;
	text-align: center;
}

.status-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
	gap: 10rpx;
}

.status-dot {
	width: 16rpx;
	height: 16rpx;
	background: #00ff88;
	border-radius: 50%;
	animation: pulse-green 2s infinite;
	box-shadow: 0 0 20rpx rgba(0, 255, 136, 0.5);
}

@keyframes pulse-green {
	0%, 100% { opacity: 1; transform: scale(1); }
	50% { opacity: 0.6; transform: scale(1.2); }
}

.status-text {
	font-size: 24rpx;
	color: #00ff88;
	font-weight: 500;
	letter-spacing: 1rpx;
}

.preview-title {
	font-size: 40rpx;
	font-weight: 700;
	color: #fff;
	margin-bottom: 10rpx;
	text-shadow: 0 0 20rpx rgba(0, 255, 255, 0.3);
	letter-spacing: 2rpx;
}

.preview-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.7);
	font-weight: 300;
}

/* 科技感预览容器 */
.preview-container {
	flex: 1;
	padding: 30rpx;
	overflow-y: auto;
}

.preview-main {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 图片容器 */
.image-container {
	position: relative;
}

.image-frame {
	position: relative;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 20rpx;
	padding: 20rpx;
	border: 2rpx solid rgba(0, 255, 255, 0.2);
	box-shadow:
		0 0 30rpx rgba(0, 255, 255, 0.1),
		inset 0 0 30rpx rgba(0, 255, 255, 0.05);
	overflow: hidden;
}

.preview-image {
	width: 100%;
	height: 500rpx;
	border-radius: 16rpx;
	display: block;
	background: rgba(255, 255, 255, 0.05);
	object-fit: contain;
}

/* 图片加载错误提示 */
.image-error {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	z-index: 10;
	background: rgba(0, 0, 0, 0.8);
	padding: 40rpx;
	border-radius: 20rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.error-text {
	display: block;
	color: #ff6b6b;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 15rpx;
}

.error-tip {
	display: block;
	color: rgba(255, 255, 255, 0.7);
	font-size: 24rpx;
}

/* 扫描线效果 */
.scan-line {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background: linear-gradient(90deg, transparent, #00ff88, transparent);
	animation: scan 3s linear infinite;
	z-index: 3;
}

@keyframes scan {
	0% { transform: translateY(0); opacity: 0; }
	10% { opacity: 1; }
	90% { opacity: 1; }
	100% { transform: translateY(400rpx); opacity: 0; }
}

/* 网格线 */
.grid-overlay {
	position: absolute;
	top: 20rpx;
	left: 20rpx;
	right: 20rpx;
	bottom: 20rpx;
	pointer-events: none;
	z-index: 1;
}

.grid-line {
	position: absolute;
	background: rgba(0, 255, 255, 0.1);
}

.grid-vertical {
	width: 1rpx;
	height: 100%;
}

.grid-horizontal {
	height: 1rpx;
	width: 100%;
}

/* AI 分析点位 */
.analysis-overlay {
	position: absolute;
	top: 20rpx;
	left: 20rpx;
	right: 20rpx;
	bottom: 20rpx;
	pointer-events: none;
	z-index: 2;
}

.analysis-point {
	position: absolute;
	width: 24rpx;
	height: 24rpx;
	transform: translate(-50%, -50%);
	animation: point-appear 0.8s ease-out forwards;
	opacity: 0;
}

@keyframes point-appear {
	0% { opacity: 0; transform: translate(-50%, -50%) scale(0); }
	100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

.point-core {
	width: 8rpx;
	height: 8rpx;
	background: #00ff88;
	border-radius: 50%;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	box-shadow: 0 0 10rpx rgba(0, 255, 136, 0.8);
	z-index: 3;
}

.point-ring {
	position: absolute;
	top: 0;
	left: 0;
	width: 24rpx;
	height: 24rpx;
	border: 2rpx solid rgba(0, 255, 136, 0.6);
	border-radius: 50%;
	animation: ring-rotate 4s linear infinite;
}

@keyframes ring-rotate {
	0% { transform: rotate(0deg) scale(1); }
	50% { transform: rotate(180deg) scale(1.1); }
	100% { transform: rotate(360deg) scale(1); }
}

.point-pulse {
	position: absolute;
	top: 0;
	left: 0;
	width: 24rpx;
	height: 24rpx;
	border: 1rpx solid rgba(0, 255, 136, 0.4);
	border-radius: 50%;
	animation: pulse-expand 2s ease-out infinite;
}

@keyframes pulse-expand {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(3);
		opacity: 0;
	}
}

/* 质量检测面板 */
.quality-panel {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 20rpx;
	padding: 30rpx;
	border: 1rpx solid rgba(0, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
}

.panel-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	gap: 15rpx;
}

.panel-icon {
	font-size: 32rpx;
	color: #00ff88;
}

.panel-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #fff;
	flex: 1;
}

.panel-status {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.status-bar {
	width: 120rpx;
	height: 8rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 4rpx;
	overflow: hidden;
}

.status-progress {
	height: 100%;
	background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #00ff88);
	border-radius: 4rpx;
	transition: width 0.8s ease;
}

.status-score {
	font-size: 24rpx;
	color: #00ff88;
	font-weight: 600;
	min-width: 60rpx;
}

.quality-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.quality-card {
	background: rgba(255, 255, 255, 0.03);
	border-radius: 16rpx;
	padding: 25rpx 20rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.1);
	text-align: center;
	position: relative;
	overflow: hidden;
}

.card-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 15rpx;
	font-size: 20rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.card-icon.pass {
	background: rgba(0, 255, 136, 0.2);
	color: #00ff88;
	border: 2rpx solid rgba(0, 255, 136, 0.4);
	box-shadow: 0 0 15rpx rgba(0, 255, 136, 0.3);
}

.card-icon.warning {
	background: rgba(255, 193, 61, 0.2);
	color: #ffd93d;
	border: 2rpx solid rgba(255, 193, 61, 0.4);
	box-shadow: 0 0 15rpx rgba(255, 193, 61, 0.3);
}

.card-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 10rpx;
}

.card-indicator {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	border-radius: 0 0 16rpx 16rpx;
}

.card-indicator.pass {
	background: linear-gradient(90deg, transparent, #00ff88, transparent);
	animation: indicator-glow 2s ease-in-out infinite;
}

.card-indicator.warning {
	background: linear-gradient(90deg, transparent, #ffd93d, transparent);
	animation: indicator-glow 2s ease-in-out infinite;
}

@keyframes indicator-glow {
	0%, 100% { opacity: 0.5; }
	50% { opacity: 1; }
}

/* 科技感操作区域 */
.preview-actions {
	padding: 40rpx 30rpx;
	background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
	border-top: 1rpx solid rgba(0, 255, 255, 0.2);
}

.action-container {
	display: flex;
	gap: 30rpx;
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	position: relative;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.btn-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 50rpx;
	transition: all 0.3s ease;
}

.action-btn.secondary .btn-bg {
	background: rgba(255, 255, 255, 0.1);
	border: 2rpx solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
}

.action-btn.secondary:active .btn-bg {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(0.95);
}

.action-btn.primary .btn-bg {
	background: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
	box-shadow:
		0 0 30rpx rgba(0, 255, 136, 0.4),
		0 8rpx 30rpx rgba(0, 212, 255, 0.3);
}

.action-btn.primary:active .btn-bg {
	transform: scale(0.95);
	box-shadow:
		0 0 20rpx rgba(0, 255, 136, 0.6),
		0 4rpx 20rpx rgba(0, 212, 255, 0.4);
}

.btn-content {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	position: relative;
	z-index: 2;
}

.btn-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #fff;
}

.action-btn.secondary .btn-icon {
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.8);
}

.btn-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #fff;
	letter-spacing: 1rpx;
}

.action-btn.secondary .btn-text {
	color: rgba(255, 255, 255, 0.8);
}

/* 粒子效果 */
.btn-particles {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	overflow: hidden;
	border-radius: 50rpx;
}

.particle {
	position: absolute;
	width: 4rpx;
	height: 4rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 50%;
	animation: particle-float 3s ease-in-out infinite;
}

.particle:nth-child(1) { left: 10%; top: 20%; }
.particle:nth-child(2) { left: 30%; top: 80%; }
.particle:nth-child(3) { left: 50%; top: 10%; }
.particle:nth-child(4) { left: 70%; top: 70%; }
.particle:nth-child(5) { left: 90%; top: 40%; }
.particle:nth-child(6) { left: 20%; top: 60%; }

@keyframes particle-float {
	0%, 100% {
		transform: translateY(0) scale(1);
		opacity: 0.3;
	}
	50% {
		transform: translateY(-20rpx) scale(1.2);
		opacity: 1;
	}
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.7);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-content {
	background: #fff;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	text-align: center;
	min-width: 300rpx;
}

.loading-spinner {
	position: relative;
	width: 80rpx;
	height: 80rpx;
	margin: 0 auto 30rpx;
}

.spinner-ring {
	position: absolute;
	top: 0;
	left: 0;
	width: 80rpx;
	height: 80rpx;
	border: 4rpx solid #f0f0f0;
	border-top: 4rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.spinner-dot {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 20rpx;
	height: 20rpx;
	background: #007aff;
	border-radius: 50%;
	transform: translate(-50%, -50%);
	animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes pulse {
	0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
	50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.loading-text {
	font-size: 28rpx;
	color: #333;
}
</style>
