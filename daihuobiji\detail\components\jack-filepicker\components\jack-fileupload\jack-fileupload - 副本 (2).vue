<template>
	<view class="">
		<view>
			<!-- 图片预览 -->
			<view class="container">
				<swiper interval="3000" :disable-touch="isEdit" :current="currentIndex" @change="onChange"
					duration="500" circular :style="{'margin-bottom': '10rpx'}">
					<swiper-item :catchtouchmove="isEdit" v-for="(image, index) in images" :key="index" style="text-align: center"
						>
						<view class="content-box" :style="{'overflow':(notMoveCanvas?'hidden':'auto')}">
							<view class="canvas" :style="{height: canvasHeight[index] +'px'}" :id="'canvas-'+index">
								<canvas :style="{'width':itemCanvasInfo.width+'px','height':itemCanvasInfo.height+'px'}"
									:canvas-id="'newCanvas-'+index"></canvas>
								<canvas class="canvasDom" :ref="'itemCanvas-'+index"
									:style="{'width':itemCanvasInfo.width+'px','height':itemCanvasInfo.height+'px'}"
									:canvas-id="'itemCanvas-'+index"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px'}"
									:canvas-id="'imgCanvas-'+index" class="canvasDom"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px'}"
									:canvas-id="'drawCanvas-'+index" @touchmove="touchmove" @touchstart="touchstart"
									@touchend="touchend" class="canvasDom"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px','z-index':99999}"
									:canvas-id="'timeCanvas-'+index" @touchmove="touchmove" @touchstart="touchstart"
									@touchend="touchend" class="canvasDom"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px'}"
									:canvas-id="'clipCanvas-'+index" @touchmove="touchmove" @touchstart="touchstart"
									@touchend="touchend" class="canvasDom"></canvas>
							</view>
						</view>
					</swiper-item>
				</swiper>

				<view>
					<scroll-view class="scroll-view" scroll-x="true" scroll-left="0"
						style="white-space: nowrap;text-align: center;">
						<view v-for="(image,index) in images" :key="index" @click="switchImg(index)" class="img-box"
							:class="index == currentIndex?'img-box-active':'img-box'">
							<image :src="image" style="height: 100%;width: 100%;" mode="scaleToFill" />
						</view>
						<view class="add-img" @click="chooseFile"
							:style="{'transform':images.length == 0?'translateY(0rpx)':'translateY(-22rpx)'}">
							+
						</view>
					</scroll-view>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view>
			<view class="tools">
				<view class="tools_item" @click="!btnCanotUse?addMark('time'):()=>{}"
					:class="[!btnCanotUse?'':'disabled']">
					<image src="@/static/fileupload/time.svg"></image>
					<text v-if="!dateTimeInfo.hash">当前时间</text>
					<text v-if="dateTimeInfo.hash">清除时间</text>
				</view>
				<view class="tools_item" @click="!btnCanotUse?addMark('rotate'):()=>{}"
					:class="[!btnCanotUse?'':'disabled']">
					<image src="@/static/fileupload/refresh.svg"></image>
					<text>旋转</text>
				</view>
				<view class="tools_item" @click="!canDraw?addMark('clip'):()=>{}" :class="[canDraw?'disabled':'']">
					<image src="@/static/fileupload/tailor.svg"></image>
					<text v-if="!isClip">裁剪</text>
					<text v-if="isClip">关闭裁剪</text>
				</view>
				<view class="tools_item">
					<view class="tools_item_strokesetting" v-if="canDraw">
						<view class="tools_item_strokesetting_item">
							<view class="tools_item_strokesetting_item_selector" style="height: 40rpx;">
								<view v-for="item in colorList" @click="strokeInfo.color=item"
									:style="{'background':(item!='clear'?item:'radial-gradient(#555555 1px, white 1px) repeat'),'background-size':'4px 4px'}"
									:class="['colorbox',(item==strokeInfo.color)?'tools_item_strokesetting_item_selector_active':'']">
								</view>
							</view>
							<view style="height: 40rpx;">
								<slider :value="strokeInfo.weight" :min="2" :max="15" block-size="18"
									@change="sliderChange" show-value />
							</view>
						</view>
					</view>
					<image src="@/static/fileupload/brush.svg" @click="!isClip?addMark('draw'):()=>{}"
						:class="[isClip?'disabledClip':'']"></image>
					<text @click="!isClip?addMark('draw'):()=>{}"
						:class="[isClip?'disabledClip':'']">{{canDraw?'停止涂鸦':'涂鸦'}}</text>
				</view>

				<view class="tools_item" @click="!btnCanotUse?addMark('edit'):()=>{}"
					:class="[!btnCanotUse?'':'disabled']">
					<image src="@/static/fileupload/right.svg"></image>
					<text>{{!isEdit?'编辑':'保存'}}</text>
				</view>
				<view class="tools_item" :class="[!btnCanotUse?'':'disabled']" @click="!btnCanotUse?exit():()=>{}">
					<image src="@/static/fileupload/close.svg"></image>
					<text>删除</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import {
		getNowDateTime,
		rpx2px,
		rpxTopx,
		base64toBlob,
		blobToUrl,
		canvasHasContent,
		urlImgRotate,
		base64ToWxfile,

	} from "../../utils/index.js"
	let sto = null
	let sto1 = null

	// #ifdef MP-WEIXIN
	var newCanvas = [] // 最后合并后的canvas
	// #endif
	var imgCanvas = []
	var timeCanvas = []
	var drawCanvas = []
	var clipCanvas = []
	var itemCanvas = []
	export default {
		name: 'JackFileupload',
		components: {

		},
		props: {

		},
		data() {
			return {
				currentIndex: 0,
				// 轮播图图片数组
				images: [],
				isClip: false, //是否裁剪
				canDraw: false,
				dateTimeInfo: { // 当前时间
					left: 0,
					top: 0,
					hash: false,
					nowTime: ''
				},
				colorList: [ // 颜色
					'red',
					'orange',
					'black',
					'white',
					'green',
					'clear'
				],
				strokeInfo: {
					weight: 2,
					color: 'red'
				},
				currentImage: {},
				currentImgInfo: [], // 全部图片的宽高
				canvasHeight: [],
				screenInfo: {
					width: 0,
					height: 0
				},
				// 裁剪截取的每一层的宽高
				itemCanvasInfo: {
					width: 0,
					height: 0
				},
				// 触摸偏移量
				textOffset: {
					left: 0,
					top: 0
				},
				fourPointRange: [],
				// 旋转的角度
				rotateDeg: 0,
				touches: [],
				currentTouchPoint: {}, //当前点的位置
				radius: 10, //顶点半径
				isMovePoint: false, //是否在移动顶点
				isEdit: false
			}
		},
		computed: {
			notMoveCanvas() {
				return this.moveTime || this.canDraw
			},
			// 按钮可用状态
			btnCanotUse() {
				return this.isClip || this.canDraw
			}
		},
		watch: {
			isClip(n) {
				if (!n) {
					// 将itemCanvas宽高设置成裁剪宽高
					this.itemCanvasInfo.width = this.fourPointRange[1].x2 - this.fourPointRange[0].x1
					this.itemCanvasInfo.height = this.fourPointRange[2].y2 - this.fourPointRange[0].y1
					uni.showModal({
						cancelText: '舍弃',
						confirmText: '保留',
						title: '是否保留裁剪区域',
						success: (res) => {
							if (res.confirm) {
								this.saveClip()
							}
							if (res.cancel) {
								clipCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this
									.canvasHeight[this.currentIndex])
								clipCanvas[this.currentIndex].draw()
								this.itemCanvasInfo.width = 0
								this.itemCanvasInfo.height = 0
							}
						}
					})
				}
			}
		},
		methods: {
			onChange(e) {
				this.currentIndex = e.detail.current;
				this.currentImage = this.images[this.currentIndex]
			},
			switchImg(index) {
				if (!this.isEdit) {
					this.currentIndex = index
					this.currentImage = this.images[this.currentIndex]
				}
			},
			sliderChange(e) {
				this.strokeInfo.weight = e.detail.value
			},
			// 加载图片到canvas
			loadImgToCanvas() {
				this.currentImgInfo.forEach(item => {
					let w = item.width
					let h = item.height
					// 计算比例
					let ro = w / h
					this.canvasHeight.push(this.screenInfo.width / ro)
				})


				imgCanvas = []
				timeCanvas = []
				drawCanvas = []
				clipCanvas = []
				itemCanvas = []
				this.images.forEach((item, index) => {
					// #ifdef MP-WEIXIN
					newCanvas.push(uni.createCanvasContext('newCanvas-' + index, this))
					// #endif
					imgCanvas.push(uni.createCanvasContext('imgCanvas-' + index, this))
					timeCanvas.push(uni.createCanvasContext('timeCanvas-' + index, this))
					drawCanvas.push(uni.createCanvasContext('drawCanvas-' + index, this))
					clipCanvas.push(uni.createCanvasContext('clipCanvas-' + index, this))
					itemCanvas.push(uni.createCanvasContext('itemCanvas-' + index, this))
				})
				setTimeout(() => {
					imgCanvas.forEach((item, index) => {
						let w = this.currentImgInfo[index].width
						let h = this.currentImgInfo[index].height
						let ro = w / h
						console.log('ro', ro)
						item.drawImage(this.images[index], 0, 0, this.screenInfo.width, this.screenInfo
							.width / ro)
						item.draw()
					})
				}, 100)
			},
			// 选择图片
			chooseFile() {
				uni.chooseImage({
					extension: ['.png', '.jpg', '.jpeg'],
					sourceType: ['album'],
					complete: (e) => {
						console.log(e)
						if (e.errMsg == "chooseImage:fail cancel") { // 未选择图片

						} else {
							// 获取已经选择的文件对象
							this.currentImage = e.tempFilePaths[0]
							this.images.push(...e.tempFilePaths)
							this.currentImgInfo = []
							this.images.forEach(async item => {
								const res = await uni.getImageInfo({
									src: item
								})
								if (Array.isArray(res)) {
									this.currentImgInfo.push(res[1])
								} else {
									this.currentImgInfo.push(res)
								}
								this.$nextTick(() => {
									this.loadImgToCanvas()
								})
							})
							console.log('this.currentImgInfo', this.currentImgInfo)


						}

					}
				})
			},
			// 在canvas上加标记
			async addMark(index) {
				if (index == 'time' || index == 'rotate' || index == 'draw' || index == 'clip' || index == 'edit') {
					if (this.images.length == 0) {
						uni.showToast({
							title: '请先选择图片',
							duration: 2000,
							icon: 'none'
						});
						return;
					}
				}
				// 设置时间
				if (index == 'time') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return;
					}
					if (!this.dateTimeInfo.hash) {
						timeCanvas[this.currentIndex].setFontSize(14)
						timeCanvas[this.currentIndex].setLineWidth(4)
						timeCanvas[this.currentIndex].setFillStyle('rgb(83, 83, 83)')
						timeCanvas[this.currentIndex].setStrokeStyle('white')
						// 当前时间
						let nowTime = getNowDateTime()
						this.dateTimeInfo.nowTime = nowTime
						this.dateTimeInfo.left = 5
						this.dateTimeInfo.top = this.canvasHeight[this.currentIndex] - 10
						timeCanvas[this.currentIndex].strokeText(nowTime, this.dateTimeInfo.left, this.dateTimeInfo
							.top, 120)
						timeCanvas[this.currentIndex].fillText(nowTime, this.dateTimeInfo.left, this.dateTimeInfo.top,
							120)
						this.dateTimeInfo.hash = true
						timeCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空
					} else {
						// 移除时间
						timeCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this
							.currentIndex])
						timeCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空
						this.dateTimeInfo.hash = false
					}
				}
				// 旋转
				if (index == 'rotate') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return
					}
					// 三个绘制图层上是否有内容
					let timeCanvas = {
						canvasId: "timeCanvas-" + this.currentIndex,
						x: 0,
						y: 0,
						width: this.screenInfo.width,
						height: this.canvasHeight[this.currentIndex],
					}
					let drawCanvas = {
						canvasId: "drawCanvas-" + this.currentIndex,
						x: 0,
						y: 0,
						width: this.screenInfo.width,
						height: this.canvasHeight[this.currentIndex],
					}
					let c1 = await canvasHasContent(timeCanvas, this)
					let c2 = await canvasHasContent(drawCanvas, this)
					if (c1 || c2) {
						uni.showModal({
							cancelText: '旋转',
							confirmText: '取消旋转',
							title: '旋转将清除图片上的编辑内容',
							content: '是否继续？',
							success: (res) => {
								if (res.cancel) {
									// 继续旋转
									this.rotateClearOther()
								}
							}
						})
					} else {
						this.rotateClearOther()
					}
				}
				// 绘制涂鸦
				if (index == 'draw') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return
					}
					this.canDraw = !this.canDraw
					this.isClip = false
				}
				// 裁剪
				if (index == 'clip') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return
					}
					this.isClip = !this.isClip
					// 绘制4个角的顶点
					if (this.isClip) {
						this.fourPointRange = []
						// 初始距离边缘的偏移
						let offset = 40
						this.fourPointRange.push({
							x1: 0 + offset,
							y1: 0 + offset,
							x2: this.radius * 2 + offset,
							y2: this.radius * 2 + offset
						})
						this.fourPointRange.push({
							x1: this.screenInfo.width - this.radius * 2 - offset,
							y1: 0 + offset,
							x2: this.screenInfo.width - offset,
							y2: this.radius * 2 + offset
						})
						this.fourPointRange.push({
							x1: 0 + offset,
							y1: this.canvasHeight[this.currentIndex] - this.radius * 2 - offset,
							x2: this.radius * 2 + offset,
							y2: this.canvasHeight[this.currentIndex] - offset
						})
						this.fourPointRange.push({
							x1: this.screenInfo.width - this.radius * 2 - offset,
							y1: this.canvasHeight[this.currentIndex] - this.radius * 2 - offset,
							x2: this.screenInfo.width - offset,
							y2: this.canvasHeight[this.currentIndex] - offset
						})

						this.drawFourPoint()

					}
				}
				// 编辑/保存
				if (index == 'edit') {
					this.isEdit = !this.isEdit
					if (this.isEdit) {
						return;
					}
					// #ifdef H5
					const canvasList = document.querySelectorAll(`#canvas-${this.currentIndex}>uni-canvas>canvas`);
					const newCanvas = document.createElement('canvas')
					newCanvas.width = this.screenInfo.width
					newCanvas.height = this.canvasHeight[this.currentIndex]
					const context = newCanvas.getContext('2d')
					canvasList.forEach(item => {
						if (item.getAttribute('width') == 0 || item.getAttribute('height') == 0);
						else {
							context.drawImage(item, 0, 0, this.screenInfo.width, this.canvasHeight[this
								.currentIndex])
						}
					})
					let base64 = newCanvas.toDataURL();
					// 将处理好的图片生成临时url
					let url = blobToUrl(base64toBlob(base64))
					this.images[this.currentIndex] = url
					// 清空画布并关闭弹窗
					this.clearCanvas()
					// #endif
					// #ifdef MP-WEIXIN
					this.itemCanvasInfo.width = this.screenInfo.width
					this.itemCanvasInfo.height = this.canvasHeight[this.currentIndex]
					uni.showLoading({
						mask: true
					})
					// 将所有图层绘制到newCanvas中
					let canvasListIds = ['imgCanvas-' + this.currentIndex, 'drawCanvas-' + this.currentIndex,
						'timeCanvas-' + this.currentIndex
					]
					canvasListIds.forEach(async (i, index) => {
						let result = await wx.canvasToTempFilePath({
							canvasId: i,
						}, this)
						let url = result.tempFilePath
						console.log('this.itemCanvasInfo', this.itemCanvasInfo)
						newCanvas[this.currentIndex].drawImage(url, 0, 0, this.itemCanvasInfo.width, this
							.itemCanvasInfo.height)
						newCanvas[this.currentIndex].draw(true)
						console.log('newCanvas[this.currentIndex]', newCanvas[this.currentIndex])
						if (index == canvasListIds.length - 1) {
							let result = await wx.canvasToTempFilePath({
								canvasId: 'newCanvas-' + this.currentIndex,
							}, this)
							let urlr = result.tempFilePath
							this.images[this.currentIndex] = urlr
							// 清空画布并关闭弹窗
							this.clearCanvasContent(true)
							this.clearCanvas()
							uni.hideLoading()
						}
					})
					// #endif

				}

			},
			// 绘制4个顶点
			drawFourPoint() {
				clipCanvas[this.currentIndex].setFillStyle('rgba(0,0,0,0.6)')
				clipCanvas[this.currentIndex].fillRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				clipCanvas[this.currentIndex].clearRect(this.fourPointRange[0].x1, this.fourPointRange[0].y1, this
					.fourPointRange[1].x2 - this
					.fourPointRange[0].x1, this.fourPointRange[2].y2 - this.fourPointRange[0].y1)
				clipCanvas[this.currentIndex].setStrokeStyle('gray')
				clipCanvas[this.currentIndex].setFillStyle('white')
				this.fourPointRange.forEach((i, index) => {
					clipCanvas[this.currentIndex].beginPath()
					clipCanvas[this.currentIndex].arc(i.x1 + (i.x2 - i.x1) / 2, i.y1 + (i.y2 - i.y1) / 2, this
						.radius, 0, 360)
					clipCanvas[this.currentIndex].fill()
					clipCanvas[this.currentIndex].beginPath()
					clipCanvas[this.currentIndex].arc(i.x1 + (i.x2 - i.x1) / 2, i.y1 + (i.y2 - i.y1) / 2, this
						.radius, 0, 360)
					clipCanvas[this.currentIndex].stroke()
				})
				clipCanvas[this.currentIndex].draw()
				this.isClip = true
				this.canDraw = false
				let clipW = this.fourPointRange[1].x2 - this.fourPointRange[0].x1
				let clipH = this.fourPointRange[2].y2 - this.fourPointRange[0].y1
				this.itemCanvasInfo.width = clipW
				this.itemCanvasInfo.height = clipH
			},
			// 旋转图片，清除其他层上的内容
			async rotateClearOther() {
				uni.showLoading({
					mask: true
				})
				this.rotateDeg += 90
				// 清除
				this.clearCanvasContent()
				this.canvasHeight[this.currentIndex] = (this.screenInfo.width / this.canvasHeight[this.currentIndex]) *
					this.screenInfo.width
				setTimeout(async () => {
					let imgurl = await urlImgRotate(this.currentImage, this.rotateDeg, this)
					console.log('imgurl', imgurl)
					uni.getImageInfo({
						src: imgurl
					}).then(res => {
						console.log('===========返回图片宽高');
						console.log(res);
					})
					imgCanvas[this.currentIndex].drawImage(imgurl, 0, 0,
						this.screenInfo.width,
						this.canvasHeight[this.currentIndex])
					imgCanvas[this.currentIndex].draw(false, async () => {
						this.dateTimeInfo.hash = false
						uni.hideLoading()
					})
				})
			},
			// 清空画布内容
			clearCanvasContent(isNewCanvas) {
				// #ifdef MP-WEIXIN
				if (!isNewCanvas) {
					newCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this
						.currentIndex])
					newCanvas[this.currentIndex].draw()
				}
				// #endif
				imgCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				timeCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				drawCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				clipCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				itemCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				imgCanvas[this.currentIndex].draw()
				timeCanvas[this.currentIndex].draw()
				drawCanvas[this.currentIndex].draw()
				clipCanvas[this.currentIndex].draw()
				itemCanvas[this.currentIndex].draw()
			},
			/**
			 * 清空画布并关闭弹窗
			 */
			clearCanvas() {
				this.canDraw = false
				this.dateTimeInfo.hash = false
				this.rotateDeg = 0
			},
			// 删除
			exit() {
				uni.showModal({
					content: '是否删除图片？',
					confirmText: '删除',
					title: '操作将删除此图片',
					cancelText: '关闭',
					success: (e) => {
						if (e.confirm) {
							this.clearCanvas()
							this.clearCanvasContent()
							this.images.splice(this.currentIndex, 1);
							this.currentIndex = 0
						}
					}
				})
			},

			// 手指开始接触
			touchstart(e) {
				this.currentTouchPoint = e.touches[0]
				this.textOffset.left = e.touches[0].x - this.dateTimeInfo.left
				this.textOffset.top = e.touches[0].y - this.dateTimeInfo.top
				if (this.canDraw) {
					drawCanvas[this.currentIndex].moveTo(e.touches[0].x, e.touches[0].y)
				}
			},
			// 接触结束
			touchend(e) {
				this.textOffset.left = 0
				this.textOffset.top = 0
				this.oOffset = 0
			},
			/**
			 * c是否在一个矩形区域t内
			 * @param {x,y} c
			 * @param {x1,y1,x2,y2} t
			 */
			isMoveIn(c, t) {
				if (c.x > t.x1 && c.x < t.x2 && c.y > t.y1 && c.y < t.y2) {
					return true
				}
				return false
			},
			// 接触后移动
			touchmove(e) {
				console.log('e', e)
				this.touches = e.touches
				// 判断是否要移动元素
				if (this.touches.length == 1) {

					// 移动时间
					// 判断在手指是否在时间元素内
					if (!this.canDraw && this.dateTimeInfo.hash && this.isMoveIn({
							x: this.touches[0].x,
							y: this.touches[0].y
						}, {
							x1: this.dateTimeInfo.left,
							y1: this.dateTimeInfo.top - 15,
							x2: this.dateTimeInfo.left + 120,
							y2: this.dateTimeInfo.top + 5,
						})) {
						this.moveTime = true
						if (sto) {
							clearTimeout(sto)
						}
						sto = setTimeout(() => {
							this.moveTime = false
							clearTimeout(sto)
							sto = null
						}, 200)

						let l = this.touches[0].x - this.textOffset.left
						// 碰到左边缘
						if (l < 0) {
							this.dateTimeInfo.left = 0
						}
						// 碰到右边缘
						else if (l + 120 > this.screenInfo.width) {
							this.dateTimeInfo.left = this.screenInfo.width - 120
						} else {
							this.dateTimeInfo.left = l
						}
						let t = this.touches[0].y - this.textOffset.top
						// 碰到上边缘
						if (t < 10) {
							this.dateTimeInfo.top = 10
						} else if (t > this.canvasHeight[this.currentIndex] - 10) {
							// 碰到下边缘
							this.dateTimeInfo.top = this.canvasHeight[this.currentIndex] - 10
						} else {
							this.dateTimeInfo.top = t
						}
						this.reDrawTime()
					}
					// 涂鸦
					if (this.canDraw) {
						if (this.strokeInfo.color != 'clear') {
							drawCanvas[this.currentIndex].setStrokeStyle(this.strokeInfo.color)
							drawCanvas[this.currentIndex].setLineCap('round')
							drawCanvas[this.currentIndex].setLineWidth(this.strokeInfo.weight)
							drawCanvas[this.currentIndex].lineTo(this.touches[0].x, this.touches[0].y)
							drawCanvas[this.currentIndex].stroke()
							drawCanvas[this.currentIndex].draw(true)
							drawCanvas[this.currentIndex].moveTo(this.touches[0].x, this.touches[0].y)
						} else {
							//擦除
							drawCanvas[this.currentIndex].clearRect(this.touches[0].x - this.strokeInfo.weight / 2, this
								.touches[0].y - this
								.strokeInfo.weight / 2, this.strokeInfo.weight, this.strokeInfo.weight)
							drawCanvas[this.currentIndex].draw(true)
						}
					}
					// 移动裁剪顶点
					if (this.isClip) {
						this.fourPointRange.forEach((i, index) => {
							if (this.isMoveIn({
									x: this.touches[0].x,
									y: this.touches[0].y
								}, i)) {
								// 是否在移动点
								this.isMovePoint = true
								this.moveTime = true
								if (sto) {
									clearTimeout(sto)
								}
								sto = setTimeout(() => {
									this.moveTime = false
									this.isMovePoint = false
									clearTimeout(sto)
									sto = null
								}, 200)
								// 禁止移出外边界
								if (this.touches[0].x < this.radius) {
									this.touches[0].x = this.radius
								}
								if (this.touches[0].x > this.screenInfo.width - this.radius) {
									this.touches[0].x = this.screenInfo.width - this.radius
								}
								if (this.touches[0].y < this.radius) {
									this.touches[0].y = this.radius
								}
								if (this.touches[0].y > this.canvasHeight[this.currentIndex] - this.radius) {
									this.touches[0].y = this.canvasHeight[this.currentIndex] - this.radius
								}
								let x = this.touches[0].x
								let y = this.touches[0].y
								// 重新计算点的位置
								this.reComPointPos(x, y, index)
								// 重绘顶点
								this.drawFourPoint()
							}
						})
						// 不在移动点且在 框选范围内
						if (!this.isMovePoint && this.isMoveIn({
								x: this.touches[0].x,
								y: this.touches[0].y
							}, {
								x1: this.fourPointRange[0].x1,
								y1: this.fourPointRange[0].y1,
								x2: this.fourPointRange[3].x2,
								y2: this.fourPointRange[3].y2,
							})) {
							// 在框选范围内
							// 移动选框时禁止滚动
							this.moveTime = true
							if (sto) {
								clearTimeout(sto)
							}
							sto = setTimeout(() => {
								this.moveTime = false
								clearTimeout(sto)
								sto = null
							}, 200)
							// 计算现在的点和原来的点的变化
							let x = this.touches[0].x - this.currentTouchPoint.x
							let y = this.touches[0].y - this.currentTouchPoint.y
							// 检测是否到边缘
							// if (x < 0) {
							//左移
							if (x < 0 && this.currentTouchPoint.x - this.fourPointRange[0].x1 >= this.currentTouchPoint
								.x) {
								x = 0
							}
							//右移
							if (x > 0 && this.fourPointRange[1].x2 - this.currentTouchPoint.x >= this.screenInfo.width -
								this.currentTouchPoint.x) {
								x = 0
							}
							// 上移
							if (y < 0 && this.currentTouchPoint.y - this.fourPointRange[0].y1 >= this.currentTouchPoint
								.y) {
								y = 0
							}
							// 下移
							if (y > 0 && this.fourPointRange[2].y2 - this.currentTouchPoint.y >= this.canvasHeight[this
									.currentIndex] -
								this.currentTouchPoint.y) {
								y = 0
							}

							// 重新计算四点的位置
							this.fourPointRange.forEach(i => {
								i.x1 += x
								i.x2 += x
								i.y1 += y
								i.y2 += y
							})
							this.currentTouchPoint.x = this.touches[0].x
							this.currentTouchPoint.y = this.touches[0].y
							// 重绘四点
							this.drawFourPoint()

						}
					}
				}
				// 双指操作，进行缩放
				// if (this.touches.length == 2) {
				// 	// 计算两点之间的初始距离
				// 	let x = this.touches[0].x - this.touches[1].x
				// 	let y = this.touches[0].y - this.touches[1].y
				// 	if (this.oOffset == 0) {
				// 		this.oOffset = Math.sqrt(x * x + y * y)
				// 	} else {
				// 		// 计算扩大倍数
				// 		this.scaleCanvas = Math.sqrt(x * x + y * y) / this.oOffset

				// 	}
				// }
			},
			// 重新计算各点的位置
			reComPointPos(x, y, index) {
				// 0: 0 1 2
				// 1: 1 0 2 
				// 2: 2 0 3
				// 3: 3 1 2

				if (index == 0) {
					if (x >= this.fourPointRange[1].x1 - this.radius * 2) {
						x = this.fourPointRange[1].x1 - this.radius * 2
					}
					if (y >= this.fourPointRange[2].y1 - this.radius * 2) {
						y = this.fourPointRange[2].y1 - this.radius * 2
					}
				}
				if (index == 1) {
					if (x <= this.fourPointRange[0].x2 + this.radius * 2) {
						x = this.fourPointRange[0].x2 + this.radius * 2
					}
					if (y >= this.fourPointRange[2].y1 - this.radius * 2) {
						y = this.fourPointRange[2].y1 - this.radius * 2
					}
				}
				if (index == 2) {
					if (x >= this.fourPointRange[1].x1 - this.radius * 2) {
						x = this.fourPointRange[1].x1 - this.radius * 2
					}
					if (y <= this.fourPointRange[0].y2 + this.radius * 2) {
						y = this.fourPointRange[0].y2 + this.radius * 2
					}
				}
				if (index == 3) {
					if (x <= this.fourPointRange[0].x2 + this.radius * 2) {
						x = this.fourPointRange[0].x2 + this.radius * 2
					}
					if (y <= this.fourPointRange[0].y2 + this.radius * 2) {
						y = this.fourPointRange[0].y2 + this.radius * 2
					}
				}
				this.fourPointRange[index] = {
					x1: x - this.radius,
					y1: y - this.radius,
					x2: x + this.radius,
					y2: y + this.radius,
				}
				if (index == 0) {
					this.fourPointRange[1].y1 = this.fourPointRange[index].y1
					this.fourPointRange[1].y2 = this.fourPointRange[index].y2
					this.fourPointRange[2].x1 = this.fourPointRange[index].x1
					this.fourPointRange[2].x2 = this.fourPointRange[index].x2
				}
				if (index == 1) {
					this.fourPointRange[0].y1 = this.fourPointRange[index].y1
					this.fourPointRange[0].y2 = this.fourPointRange[index].y2
					this.fourPointRange[3].x1 = this.fourPointRange[index].x1
					this.fourPointRange[3].x2 = this.fourPointRange[index].x2
				}
				if (index == 2) {
					this.fourPointRange[3].y1 = this.fourPointRange[index].y1
					this.fourPointRange[3].y2 = this.fourPointRange[index].y2
					this.fourPointRange[0].x1 = this.fourPointRange[index].x1
					this.fourPointRange[0].x2 = this.fourPointRange[index].x2
				}
				if (index == 3) {
					this.fourPointRange[2].y1 = this.fourPointRange[index].y1
					this.fourPointRange[2].y2 = this.fourPointRange[index].y2
					this.fourPointRange[1].x1 = this.fourPointRange[index].x1
					this.fourPointRange[1].x2 = this.fourPointRange[index].x2
				}

			},
			// 重新绘制时间
			reDrawTime() {
				// 当前时间
				timeCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				timeCanvas[this.currentIndex].strokeText(this.dateTimeInfo.nowTime, this.dateTimeInfo.left, this
					.dateTimeInfo.top, 120)
				timeCanvas[this.currentIndex].fillText(this.dateTimeInfo.nowTime, this.dateTimeInfo.left, this.dateTimeInfo
					.top, 120)
				timeCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空
			},
			// 裁剪后重新绘制
			afterClipDraw(url) {
				uni.getImageInfo({
					src: url,
					success: (res) => {
						let imageW = res.width
						let imageH = res.height
						// 清除
						this.clearCanvasContent()
						this.canvasHeight[this.currentIndex] = (imageH / imageW) * this.screenInfo.width
						this.currentImage = res.path
						uni.showLoading({
							mask: true
						})
						setTimeout(() => {
							uni.hideLoading()
							imgCanvas[this.currentIndex].drawImage(this.currentImage, 0, 0, this
								.screenInfo.width,
								this.canvasHeight[this.currentIndex])
							imgCanvas[this.currentIndex].draw()
							this.dateTimeInfo.hash = false
							this.itemCanvasInfo.width = 0
							this.itemCanvasInfo.height = 0
						}, 100)
					},
					fail: (err) => {
						console.log(err);
					}
				})
			},
			// 每一张itemCanvas画布内容绘制到一个里newCanvas
			itemsToNewH5(context, newCanvas, index) {
				let itemCanvast = this.$refs[`itemCanvas-${this.currentIndex}`][0].$el
				let c = itemCanvast.children[0]
				this.currentImage = c
				uni.showLoading({
					mask: true
				})
				setTimeout(() => {
					uni.hideLoading()
					context.drawImage(c, 0, 0, newCanvas.width, newCanvas.height)
					if (index == 2) {
						let base64 = newCanvas.toDataURL();
						// 将处理好的图片生成临时url
						let url = blobToUrl(base64toBlob(base64))
						this.images[this.currentIndex] = url
						// 重新绘制图像到画布，重置其他所有画布
						this.afterClipDraw(url)
					}
				}, 100)
			},
			itemsToNewWX(index) {
				uni.canvasToTempFilePath({
					canvasId: 'itemCanvas-' + this.currentIndex,
					success: res => {
						newCanvas[this.currentIndex].drawImage(res.tempFilePath, 0, 0, this.itemCanvasInfo
							.width, this.itemCanvasInfo.height)
						newCanvas[this.currentIndex].draw(true)

						if (index == 2) {
							uni.canvasToTempFilePath({
								canvasId: 'newCanvas-' + this.currentIndex,
							}, this).then(res => {
								if (Array.isArray(res)) {
									res = res[1]
								} else {
									res = res
								}
								this.clearCanvasContent()
								// 重新绘制图像到画布，重置其他所有画布
								this.afterClipDraw(res.tempFilePath)
							})
						}

					},
					fail: err => {
						console.log(err);
					}
				}, this)
			},
			// 保留裁剪
			async saveClip() {
				// #ifdef H5
				const newCanvas = document.createElement('canvas')
				newCanvas.width = this.fourPointRange[1].x2 - this.fourPointRange[0].x1
				newCanvas.height = this.fourPointRange[2].y2 - this.fourPointRange[0].y1
				const context = newCanvas.getContext('2d')
				// #endif
				let canvasListIds = ['imgCanvas-' + this.currentIndex, 'drawCanvas-' + this.currentIndex,
					'timeCanvas-' + this.currentIndex
				]
				for (let index = 0; index < canvasListIds.length; index++) {
					// 去除裁剪图层
					let res = await uni.canvasGetImageData({
						canvasId: canvasListIds[index],
						x: this.fourPointRange[0].x1,
						y: this.fourPointRange[0].y1,
						width: this.itemCanvasInfo.width,
						height: this.itemCanvasInfo.height,

					}, this)
					if (Array.isArray(res)) {
						res = res[1]
					}
					// 将每一个画布的数据放入itemCanvas中
					// #ifdef MP-WEIXIN
					try {
						await uni.canvasPutImageData({
							canvasId: 'itemCanvas-' + this.currentIndex,
							data: res.data,
							x: 0,
							y: 0,
							width: Math.floor(this.itemCanvasInfo
								.width), //宽高取整，否则报  canvasputimagedata:fail invalid data format
							height: Math.floor(this.itemCanvasInfo.height),
						}, this)
					} catch (e) {
						console.log(e);
						console.log('若使用微信小程序端，请使用真机调试裁剪功能');
					}
					// #endif
					// #ifdef H5
					try {
						await uni.canvasPutImageData({
							canvasId: 'itemCanvas-' + this.currentIndex,
							data: res.data,
							x: 0,
							y: 0,
							width: this.itemCanvasInfo.width,
						}, this)
					} catch (e) {
						console.log(e);
					}
					// #endif
					// 将每一张itemCanvas画布内容绘制到一个newCanvas里
					// #ifdef H5
					this.itemsToNewH5(context, newCanvas, index)
					// #endif
					// #ifdef MP-WEIXIN
					this.itemsToNewWX(index)
					// #endif
				}
			}
		},
		mounted() {
			uni.getSystemInfo().then(res => {
				if (Array.isArray(res)) {
					this.screenInfo.width = res[1].windowWidth
					this.screenInfo.height = res[1].windowHeight
				} else {
					this.screenInfo.width = res.windowWidth
					this.screenInfo.height = res.windowHeight
				}
			})
		},
		created() {
			// this.chooseFile()
		}
	}
</script>
<style>
	@import url('index.css');
</style>