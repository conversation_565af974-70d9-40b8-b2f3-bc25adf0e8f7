<template>
	<view class="container">
		<view id="turntable2">
			<view class="title">
				<view class="item" @tap="onBack">返回</view>
				<view class="item" @tap="goToOrder">订单</view>
			</view>
			<view class="boxs">
				<view class="total">
					<xzw-notice speed="fast" :showIcon="false" :showMore="false" :list="adList" />
				</view>
				<view class="turn">
					<LotteryDraw
						ref="refLotteryDraw"
						@get_winingIndex="get_winingIndex"
						@luck_draw_finish="luck_draw_finish"
						@on_submit="on_submit"
						:grid_info_arr="grid_info_arr"
					/>
				</view>
				<!-- 				<view class="turn">
					<view class="list">
						<image
							class="list-img"
							:src="checkedList[0].img"
							v-if="checkedList && checkedList.length >= 1"
						/>
						<image class="list-img" src="/game/static/game/free/icon3.png" v-else />
					</view>
					<view class="list"></view>
					<view class="list">
						<image
							class="list-img"
							:src="checkedList[1].img"
							v-if="checkedList && checkedList.length >= 2"
						/>
						<image class="list-img" src="/game/static/game/free/icon5.png" v-else />
					</view>
					<view class="list"></view>
					<view class="btn" :class="{ btn_disabled: isBegin }" @click="onBegin">
						<image class="list-img" src="/game/static/game/free/icon_pay.png" />
					</view>
					<view class="list"></view>
					<view class="list">
						<image
							class="list-img"
							:src="checkedList[2].img"
							v-if="checkedList && checkedList.length >= 3"
						/>
						<image class="list-img" src="/game/static/game/free/icon4.png" v-else />
					</view>
					<view class="list"></view>
					<view class="list">
						<image
							class="list-img"
							:src="checkedList[3].img"
							v-if="checkedList && checkedList.length >= 4"
						/>
						<image class="list-img" src="/game/static/game/free/icon6.png" v-else />
					</view>
				</view> -->
			</view>
			<view class="price">金额：{{ opt.amount || 0 }}x{{ current }}x{{ checkedList.length }}={{ getPrice }}</view>
			<view class="bottom_box">
				<view class="titl">
					<image class="img-dot" src="@/game/static/game/free/icon1.png" />
					<text>选择商品</text>
					<image class="img-dot" src="@/game/static/game/free/icon2.png" />
				</view>
				<scroll-view class="scroll-view_H" scroll-x="true" scroll-left="120">
					<view
						class="box1-item"
						v-for="item in productList"
						:key="item.id"
						@tap="chooseGoods(item)"
					>
						<image class="img-check" src="@/game/static/game/free/check_active.png" v-if="item.checked" />
						<image class="img-check" src="@/game/static/game/free/check.png" v-else />
						<image class="goods-img" :src="item.pic" />
					</view>
				</scroll-view>
				<view class="bot_btn">
					<text
						@tap="onChange(item)"
						class="color"
						:class="{ color_active: item === current }"
						v-for="item in list"
						:key="item"
					>
						{{ item }}倍
					</text>
				</view>
				<view class="go" @tap="on_submit">
					<image class="go-img" src="@/game/static/game/free/icon_submit.png" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import LotteryDraw from '@/components/SJ-LotteryDraw/SJ-LotteryDraw.vue';
import xzwNotice from './components/xzw-notice/xzw-notice.vue'
var app = getApp();
export default {
	components: {
		LotteryDraw,
		xzwNotice
	},
	data() {
		return {
			adList: [{ id: 1, title: '恭喜 949. 获得红牛' }],
			opt: {
				amount: 0
			},
			num: 0,
			isBegin: false,
			current: 1,
			list: [1, 3, 5],
			checkedList: [],
			lottery_draw_param: {
				startIndex: 0, //开始抽奖位置，从0开始
				totalCount: 4, //一共要转的圈数
				winingIndex: 4, //中奖的位置，从0开始
				speed: 50 //抽奖动画的速度 [数字越大越慢,默认100]
			},
			is_lottery: false, // 是否中奖
			productList: [],
			grid_info_arr: [
				{
					logo: require('@/game/static/game/free/icon3.png'),
					defaultLogo: '/game/static/game/free/icon3.png'
				},
				{
					logo: require('@/game/static/game/free/thanks.png')
				},
				{
					logo: require('@/game/static/game/free/icon5.png'),
					defaultLogo: '/game/static/game/free/icon5.png'
				},
				{
					logo: require('@/game/static/game/free/thanks.png')
				},
				{
					logo: require('@/game/static/game/free/icon4.png'),
					defaultLogo: '/game/static/game/free/icon4.png'
				},
				{
					logo: require('@/game/static/game/free/thanks.png')
				},
				{
					logo: require('@/game/static/game/free/icon6.png'),
					defaultLogo: '/game/static/game/free/icon6.png'
				},
				{
					logo: require('@/game/static/game/free/thanks.png')
				},
				{
					logo: require('@/game/static/game/free/icon3.png'),
					defaultLogo: '/game/static/game/free/icon3.png'
				}
			]
		};
	},
	onLoad: function (opt) {
		if (opt) {
			this.opt = app.getopts(opt);
		}

		this.getSweepstakesList();
	},
	onReady() {},
	computed: {
		getPrice() {
			let price = (this.opt.amount || 0) * this.current * this.checkedList.length;
			return price.toFixed(2);
		}
	},
	methods: {
		onBack() {
			uni.redirectTo({ url: `/shopPackage/shop/product?id=${this.opt.id}` });
		},
		goToOrder() {
			app.goto('/game/order');
		},

		/**
		 * 抽奖列表
		 */
		getSweepstakesList() {
			let that = this;
			app.get('ApiSweepstakes/getList', {}, function (res) {
				if (res.status == 0) {
					app.alert(res.msg);
					return;
				}
				let result = res.list.filter((item) => item.keyName === 'free');
				if (result && result.length > 0) {
					that.productList = result[0].product_list;
				}
				// 如果有
				if (that.opt.oid) {
					that.loading = true;
					that.getResult();
				}
			});
		},

		// 修改获奖位置（可以在这里获取后台的数据
		get_winingIndex(callback) {
			if (this.isBegin) {
				//props修改在小程序和APP端不成功，所以在这里使用回调函数传参，
				callback(this.lottery_draw_param);
			} else {
				if (this.checkedList.length === 0) {
					app.alert('请先选择商品并且支付后再开始抽奖');
					return;
				} else {
					app.alert('请先支付后再开始抽奖');
				}
			}
		},
		// 抽奖完成
		luck_draw_finish(param) {
			// console.log(`抽到第${param+1}个方格的奖品`)
			this.isBegin = false;
			let that = this;
			if (this.is_lottery) {
				app.alert('恭喜你，中奖了', function () {
					if (that.opt.oid) {
						app.goto(app._delOrderId());
					}
				});
			} else {
				app.alert('未中奖，请再接再厉', function () {
					if (that.opt.oid) {
						app.goto(app._delOrderId());
					}
				});
			}
		},
		/**
		 * 切换
		 * @param {Object} num
		 */
		onChange(num) {
			this.current = num;
		},
		chooseGoods(item) {
			if (item.checked) {
				this.$set(item, 'checked', !item.checked);
				if (item.checked) {
					this.checkedList.push(item);
					this.handleProductStatusOn();
				} else {
					let index = this.checkedList.findIndex((e) => e.img === item.img);
					this.checkedList.splice(index, 1);
					this.handleProductStatusOff();
				}
			} else {
				let list = this.productList.filter((item) => item.checked);
				if (list && list.length < 4) {
					this.$set(item, 'checked', true);
					this.checkedList.push(item);
					this.handleProductStatusOn();
				} else {
					alert('最多选4个商品');
				}
			}
		},
		handleProductStatusOn() {
			let len = this.checkedList.length;
			if (len === 1) {
				this.$set(this.grid_info_arr, 0, this.checkedList[0]);
				this.$set(this.grid_info_arr[0], 'logo', this.checkedList[0].pic);
			} else if (len === 2) {
				this.$set(this.grid_info_arr, 0, this.checkedList[0]);
				this.$set(this.grid_info_arr[0], 'logo', this.checkedList[0].pic);
				this.$set(this.grid_info_arr, 2, this.checkedList[1]);
				this.$set(this.grid_info_arr[2], 'logo', this.checkedList[1].pic);
			} else if (len === 3) {
				this.$set(this.grid_info_arr, 0, this.checkedList[0]);
				this.$set(this.grid_info_arr[0], 'logo', this.checkedList[0].pic);
				this.$set(this.grid_info_arr, 2, this.checkedList[1]);
				this.$set(this.grid_info_arr[2], 'logo', this.checkedList[1].pic);
				this.$set(this.grid_info_arr, 4, this.checkedList[2]);
				this.$set(this.grid_info_arr[4], 'logo', this.checkedList[2].pic);
			} else if (len === 4) {
				this.$set(this.grid_info_arr, 0, this.checkedList[0]);
				this.$set(this.grid_info_arr[0], 'logo', this.checkedList[0].pic);
				this.$set(this.grid_info_arr, 2, this.checkedList[1]);
				this.$set(this.grid_info_arr[2], 'logo', this.checkedList[1].pic);
				this.$set(this.grid_info_arr, 4, this.checkedList[2]);
				this.$set(this.grid_info_arr[4], 'logo', this.checkedList[2].pic);
				this.$set(this.grid_info_arr, 6, this.checkedList[3]);
				this.$set(this.grid_info_arr[6], 'logo', this.checkedList[3].pic);
			}
		},
		handleProductStatusOff() {
			let len = this.checkedList.length;
			let list = [
				{
					logo: require('@/game/static/game/free/icon3.png'),
					defaultLogo: '/game/static/game/free/icon3.png'
				},
				{
					logo: require('@/game/static/game/free/thanks.png')
				},
				{
					logo: require('@/game/static/game/free/icon5.png'),
					defaultLogo: '/game/static/game/free/icon5.png'
				},
				{
					logo: require('@/game/static/game/free/thanks.png')
				},
				{
					logo: require('@/game/static/game/free/icon4.png'),
					defaultLogo: '/game/static/game/free/icon4.png'
				},
				{
					logo: require('@/game/static/game/free/thanks.png')
				},
				{
					logo: require('@/game/static/game/free/icon6.png'),
					defaultLogo: '/game/static/game/free/icon6.png'
				},
				{
					logo: require('@/game/static/game/free/thanks.png')
				},
				{
					logo: require('@/game/static/game/free/icon3.png'),
					defaultLogo: '/game/static/game/free/icon3.png'
				}
			];
			if (len === 0) {
				this.$set(this.grid_info_arr, 0, list[0]);
				this.$set(this.grid_info_arr, 2, list[2]);
				this.$set(this.grid_info_arr, 4, list[4]);
				this.$set(this.grid_info_arr, 6, list[6]);
			} else if (len === 1) {
				this.$set(this.grid_info_arr, 2, list[2]);
				this.$set(this.grid_info_arr, 4, list[4]);
				this.$set(this.grid_info_arr, 6, list[6]);
			} else if (len === 2) {
				this.$set(this.grid_info_arr, 4, list[4]);
				this.$set(this.grid_info_arr, 6, list[6]);
			} else if (len === 3) {
				this.$set(this.grid_info_arr, 6, list[6]);
			}
		},
		on_submit() {
			let that = this;
			that.loading = true;
			const params = {
				sid: that.opt.gameId,
				pid: that.opt.id,
				amount: that.getPrice,
				ids: that.checkedList.map((item) => item.id).toString(),
				tourl: app._fullurl()
			};
			
			app.post('ApiSweepstakes/postRaffle', params, function (res) {
				switch (res.status) {
					case 1:
						that.handleResult(res);
						break;
					case 2:
						app.confirm(res.msg || '出错了', function () {
							app.goto(res.pathUrl);
						});
						break;
					case 3:
						app.goto(res.pathUrl);
						break;
					default:
						app.alert(res.msg || '出错了');
						break;
				}
			});
		},
		/**
		 * 操作获奖结果
		 */
		handleResult(res) {
			let that = this;
			that.isBegin = true;
			that.is_lottery = !!res.is_lottery;
			if (res.is_lottery === 1) {
				// let id = res.product[0].id;
				let id = res.product.id;
				let index = that.grid_info_arr.findIndex((item) => item.id === id);
				that.$set(that.lottery_draw_param, 'winingIndex', index);
			} else {
				// 未中奖
				let checkLen = that.checkedList.length;
				let list = [1, 3, 5, 7];
				if (checkLen === 1) {
					list = [1, 2, 3, 4, 5, 6, 7];
				} else if (checkLen === 2) {
					list = [1, 3, 4, 5, 6, 7];
				} else if (checkLen === 3) {
					list = [1, 3, 5, 6, 7];
				}
				let listLen = list.length;
				let random = Math.random() * listLen;
				let num = Math.floor(random);
				that.$set(that.lottery_draw_param, 'winingIndex', list[num]);
			}
			that.$refs.refLotteryDraw.luck_draw_auto();
			that.isBegin = false;
		},
		/**
		 * 查询中奖结果
		 */
		getResult() {
			let that = this;
			const params = {
				oid: this.opt.oid
			};
			app.post('ApiSweepstakes/getResult', params, function (res) {
				if (res.status === 1) {
					// 将已选图片回显在九宫格里
					that.checkedList = [];
					let list = [];
					if (that.productList && that.productList.length > 0) {
						list = that.productList.filter((item) => res.ids.includes(item.id));
						that.productList = that.productList.map((item) => {
							return {
								...item,
								checked: res.ids.includes(item.id)
							};
						});
						that.checkedList = [...list];
					}
					that.handleProductStatusOn();
					// 开启中奖结果查询
					that.handleResult(res);
				} else {
					app.alert(res.msg, function () {
						app.goto(app._delOrderId());
					});
				}
			});
		}
	}
};
</script>

<style scoped>
.container {
	height: 100vh;
	overflow: hidden;
}

#turntable2 {
	background: url(../game/static/game/free/views_bg2.png) no-repeat;
	background-size: 100% 100%;
	box-sizing: border-box;
	height: 100%;
	overflow: scroll;
}

#turntable2 .title {
	display: flex;
	justify-content: space-between;
	padding: 10px 20px;
	color: #fff;
	font-size: 13px;
}

#turntable2 .title .item {
	background: rgba(0, 0, 0, 0.4);
	padding: 5px 10px;
	border-radius: 20px;
}

#turntable2 .boxs {
	margin: 40px auto 0;
	height: 360px;
	width: 330px;
	background: url(../game/static/game/free/block_bg1.png) no-repeat;
	background-size: 100% 100%;
}

#turntable2 .boxs .total {
	color: #fff;
	font-size: 14px;
	text-align: center;
	position: relative;
	left: 0;
	width: 230px;
	margin: 0 auto 55px;
	overflow: hidden;
	z-index: 1000;
}

#turntable2 .boxs .turn {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	width: 280px;
	margin: 0 auto;
	padding-top: 0;
	margin-top: -20px;
	position: relative;
}

#turntable2 .boxs .turn .list {
	text-align: center;
	width: 90px;
	height: 80px;
	padding: 7px 0;
	margin-bottom: 2px;
	position: relative;
	left: -1px;
	box-sizing: border-box;
}

#turntable2 .boxs .turn .list .list-img {
	width: 65px;
	height: 65px;
}

.btn {
	left: 0px !important;
	top: 1px !important;
	padding: 0 1px 1px 0 !important;
}

.btn .list-img {
	margin-left: 1px;
	width: 90px !important;
	height: 80px !important;
}

#turntable2 .price {
	text-align: center;
	color: #6f25f7;
	top: 0;
	line-height: 40px;
}

#turntable2 .bottom_box {
	width: 326px;
	background: #f0db5b;
	border: none;
	border-radius: 10px;
	margin: 0 auto;
	box-shadow: none;
	position: relative;
	top: 0;
	margin-bottom: 30px;
}

#turntable2 .bottom_box .box1 {
	height: 70px;
	position: relative;
	margin-top: 10px;
}

#turntable2 .bottom_box .titl {
	text-align: center;
	height: 32px;
	background: -webkit-linear-gradient(bottom, #c655e3, #ff6fe7);
	background: linear-gradient(0deg, #c655e3, #ff6fe7);
	border-radius: 8px 8px 0 0;
	color: #fff;
	font-size: 14px;
	line-height: 32px;
	top: 0;
}

#turntable2 .bottom_box .titl .img-dot {
	width: 15px;
	height: 11px;
}

#turntable2 .bottom_box .bot_btn {
	display: flex;
	justify-content: space-between;
	padding: 0 35px;
	margin-top: 10px;
}

#turntable2 .bottom_box .bot_btn .color {
	width: 50px;
	height: 23px;
	line-height: 23px;
	text-align: center;
	font-size: 14px;
	background: #fe696b;
	color: #fff;
	box-sizing: border-box;
	border-radius: 5px;
}

#turntable2 .bottom_box .bot_btn .color_active {
	background: #f51843;
	box-sizing: border-box;
}

#turntable2 .bottom_box .go {
	text-align: center;
	padding-top: 20px;
	padding-bottom: 20px;
}

#turntable2 .bottom_box .go .go-img {
	width: 92px;
	height: 42px;
}

.scroll-Y {
	height: 300rpx;
}

.scroll-view_H {
	margin-top: 10px;
	white-space: nowrap;
	width: 100%;
}

#turntable2 .bottom_box .box1-item {
	width: 50px;
	display: inline-block;
	margin-left: 15px;
	text-align: center;
	font-size: 12px;
	position: relative;
}

.goods-img {
	width: 50px;
	height: 50px;
}

.img-check {
	position: absolute;
	top: -4px;
	right: -4px;
	width: 20px;
	height: 20px;
	z-index: 1000;
}

</style>
