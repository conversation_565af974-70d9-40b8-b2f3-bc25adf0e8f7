<template>
<view class="container">
	<block v-if="isload">
		<view class="learning-log-container">
			<block v-if="datalist && datalist.length > 0">
				<view class="log-item" v-for="(item, index) in datalist" :key="index" @tap="gotoCourse" :data-kcid="item.kcid">
					<view class="course-info">
						<image class="course-pic" :src="item.course_pic" mode="aspectFill"></image>
						<view class="course-details">
							<view class="course-name">{{ item.course_name }}</view>
							<view class="progress-overall">
								<text>总 {{ item.total_chapters }} 讲 | 已学 {{ item.completed_chapters }} 讲</text>
								<progress :percent="item.overall_progress_percent" activeColor="#10aeff" stroke-width="3" style="flex: 1; margin-left: 10rpx;"></progress>
								<text style="margin-left:10rpx">{{ item.overall_progress_percent }}%</text>
							</view>
						</view>
					</view>
					<view class="chapter-toggle" @tap.stop="toggleChapter(index)" :class="{'active': item.showChapter}">
						<text>{{ item.showChapter ? '收起章节' : '查看章节' }}</text>
						<text class="toggle-icon" :class="{'rotate': item.showChapter}">›</text>
					</view>
					<view class="target-chapter-info" v-if="item.showChapter && item.target_chapter_id">
						<view class="chapter-title">应学章节：{{ item.target_chapter_name }}</view>
						<view class="chapter-meta">
							<text v-if="item.target_chapter_type === 1">类型：图文</text>
							<text v-if="item.target_chapter_type === 2">类型：音频</text>
							<text v-if="item.target_chapter_type === 3">类型：视频</text>
							<text v-if="item.target_chapter_duration && (item.target_chapter_type === 2 || item.target_chapter_type === 3)"> | 时长：{{ formatDuration(item.target_chapter_duration) }}</text>
						</view>
						<view class="chapter-progress">
							<text>进度：</text>
							<block v-if="item.target_chapter_log_status === 1">
								<text class="status-completed">已学完</text>
							</block>
							<block v-else>
								<text v-if="item.target_chapter_type === 1">{{ item.target_chapter_log_jindu === '已学完' ? '已学完' : '未学习' }}</text>
								<text v-else>{{ item.target_chapter_log_jindu }}%</text>
								<text v-if="item.target_chapter_current_time > 0 && (item.target_chapter_type === 2 || item.target_chapter_type === 3)"> (已播：{{ formatDuration(item.target_chapter_current_time) }})</text>
							</block>
						</view>
					</view>
					<view class="target-chapter-info" v-else-if="item.showChapter">
						<view class="chapter-title">暂无学习章节</view>
					</view>
				</view>
			</block>
			<nomore text="没有更多记录了" v-if="nomore"></nomore>
			<nodata text="暂无学习记录" v-if="nodata"></nodata>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      nomore: false,
      nodata: false,
      pagenum: 1,
      pernum: 10, //每页显示条目数
      datalist: []
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
		this.getTabbarMenu();
    this.getLearningProgress(true);
  },
  onPullDownRefresh: function () {
    this.getLearningProgress(true);
  },
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getLearningProgress(false);
    }
  },
  methods: {
		getTabbarMenu: function() {
			var currentRoute = '/' + this.__route__;
			var tarbar = app.globalData.tarbar;
			if (tarbar && tarbar.list) {
				for (var i = 0; i < tarbar.list.length; i++) {
					if (tarbar.list[i].pagePath == currentRoute) {
						this.menuindex = i;
						// uni.setNavigationBarTitle({
						// 	title: tarbar.list[i].text
						// });
					}
				}
			}
			// 自定义导航栏 隐藏原生tabbar需要设置，否则点击返回会有问题
			if(this.opt && this.opt.hidettabbar == 1){
				uni.hideTabBar();
			}
		},
    getLearningProgress: function (isRefresh) {
      var that = this;

      if (isRefresh) {
        that.pagenum = 1;
        that.datalist = [];
      }

      that.loading = true;
      that.nodata = false;
      that.nomore = false;
      
      app.post('ApiKecheng/getMyLearningProgress', {
        pagenum: that.pagenum,
        pernum: that.pernum
      }, function (res) {
        that.loading = false;
        that.isload = true;
        uni.stopPullDownRefresh();

        if (res.status == 1) {
          var data = res.data;
          for(var i = 0; i < data.length; i++) {
            data[i].showChapter = false;
          }
          
          if (that.pagenum == 1) {
            that.datalist = data;
            if (data.length == 0) {
              that.nodata = true;
            }
          } else {
            that.datalist = that.datalist.concat(data);
          }

          if (data.length < that.pernum) {
            that.nomore = true;
          }
        } else if (res.status == -1) {
					app.showModal(res.msg, function () {
						app.goto('/pages/login/login');
					});
				} else {
          app.alert(res.msg);
        }
      });
    },
		gotoCourse: function(e) {
			var kcid = e.currentTarget.dataset.kcid;
			app.goto('/activity/kecheng/detail?id=' + kcid);
		},
    formatDuration: function(seconds) {
      if (!seconds || seconds <= 0) return '00:00';
      seconds = Math.floor(seconds);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      const formattedMinutes = String(minutes).padStart(2, '0');
      const formattedSeconds = String(remainingSeconds).padStart(2, '0');
      return `${formattedMinutes}:${formattedSeconds}`;
    },
		getmenuindex: function getmenuindex(e) {
			this.menuindex = e;
		},
    toggleChapter: function(index) {
      this.datalist[index].showChapter = !this.datalist[index].showChapter;
    }
  }
};
</script>
<style>
.container {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
	background-color: #f6f8fc;
	min-height:100vh;
}

.learning-log-container {
  padding: 30rpx 24rpx;
}

.log-item {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.log-item::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(135deg, #10aeff, #1484fa);
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.log-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.04);
}

.course-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.course-pic {
  width: 180rpx;
  height: 130rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.course-details {
  flex: 1;
  display: flex;
  flex-direction: column;
	justify-content: space-between;
	height: 130rpx;
}

.course-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
  line-height: 1.3;
  margin-bottom: 10rpx;
}

.progress-overall {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.progress-overall text:first-child {
  margin-right: 10rpx;
  margin-bottom: 6rpx;
  color: #888;
}

.progress-overall progress{
	height: 12rpx!important;
	border-radius: 10rpx!important;
  margin: 8rpx 0;
  flex: 1;
}

.progress-overall .uni-progress-bar{
	border-radius: 10rpx!important;
  background-color: #eef2f8;
}

.progress-overall .uni-progress-inner-bar{
	border-radius: 10rpx!important;
  background: linear-gradient(to right, #10aeff, #1484fa);
}

.progress-overall text:last-child {
  font-weight: bold;
  color: #10aeff;
  min-width: 60rpx;
  text-align: right;
}

.chapter-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #10aeff;
  position: relative;
  border-top: 1rpx dashed #eaeef5;
  margin-top: 10rpx;
}

.chapter-toggle.active {
  border-bottom: none;
}

.toggle-icon {
  margin-left: 10rpx;
  font-size: 28rpx;
  font-weight: bold;
  transform: rotate(90deg);
  display: inline-block;
  transition: transform 0.3s ease;
}

.toggle-icon.rotate {
  transform: rotate(-90deg);
}

.target-chapter-info {
  margin-top: 0;
  padding: 20rpx 0;
  border-top: 1rpx dashed #eaeef5;
  position: relative;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chapter-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
  position: relative;
  padding-left: 18rpx;
}

.chapter-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 24rpx;
  background: #10aeff;
  border-radius: 4rpx;
}

.chapter-meta {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.chapter-meta text {
  margin-right: 16rpx;
  background-color: #f6f8fc;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  line-height: 1.5;
}

.chapter-progress {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.chapter-progress text:first-child {
  color: #888;
  margin-right: 8rpx;
}

.chapter-progress .status-completed {
	color: #07c160;
  font-weight: bold;
  position: relative;
  padding-left: 8rpx;
}

.chapter-progress .status-completed::before {
  content: '✓';
  margin-right: 4rpx;
  font-size: 24rpx;
}

/* 高亮进度百分比显示 */
.chapter-progress text:nth-child(2):not(.status-completed) {
  color: #10aeff;
  font-weight: bold;
}

/* 已播放时间显示 */
.chapter-progress text:nth-child(3) {
  margin-left: 8rpx;
  background-color: #f1f7ff;
  border-radius: 16rpx;
  padding: 2rpx 12rpx;
  font-size: 22rpx;
  color: #1484fa;
}

/* Nodata and Nomore styles */
.nodata {
  width: 100%;
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.nodata image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.nomore {
  width: 100%;
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
  position: relative;
}

.nomore::before, .nomore::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 20%;
  height: 1rpx;
  background: #e5e5e5;
}

.nomore::before {
  left: 15%;
}

.nomore::after {
  right: 15%;
}
</style> 