.disabled{
	text{
		color: gray !important;
	}
	image{
		filter: brightness(0.4);
	}
}
.disabledClip{
	filter: brightness(0.4);
}
.image_list {
	display: flex;
	flex-wrap: wrap;

	&_template {
		border: 1px solid #e8e8e8;
		border-radius: 5rpx;
		width: 180rpx;
		height: 180rpx;
		margin: 15rpx 15rpx 15rpx 0;
		background-color: white;
		overflow: hidden;
		position: relative;

		image {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
		}

	}

	&_imgs {
		border: 1px solid #e8e8e8;
		border-radius: 5rpx;
		width: 180rpx;
		height: 180rpx;
		margin: 15rpx 15rpx 15rpx 0;
		overflow: hidden;
		position: relative;

		&_item {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
		}

		&_delete {
			width: 30rpx;
			height: 30rpx;
			display: block;
			padding: 10rpx;
			border-radius: 10rpx;
			position: absolute;
			right: 10rpx;
			top: 10rpx;
			z-index: 2;
			background-color: black;
		}
	}
}

.colorbox {
	width: 20rpx;
	height: 20rpx;
}

.canvas {
	position: absolute;
	top: 0;
	width: 100vw;

	canvas {
		position: absolute;
		top: 0;
		left: 0;
	}
}

.content-box {
	height: 100%;
	position: relative;
}
.jack-file-title{
	display: flex;
	justify-content: space-between;
	&-l{
		font-size: 26rpx;
	}
	&-r{
		font-size: 26rpx;
		color: gray;
	}
}
.tools {
	display: flex;
	justify-content: space-between;
	position: absolute;
	bottom: 10rpx;
	width: 100%;

	&_item {
		width: 33.33%;
		text-align: center;

		image {
			width: 60rpx;
			height: 60rpx;
			opacity: 0.7;
		}

		text {
			display: block;
			font-size: 26rpx;
			color: white;
			opacity: 0.7;
		}

		&_strokesetting {
			border-radius: 40rpx;
			position: absolute;
			left: 0;
			width: 56%;
			background-color: #ffffff80;
			backdrop-filter: blur(11px);
			height: 110rpx;

			&::after {
				position: absolute;
				content: '';
				display: block;
				border-left: 20rpx solid #ffffff90;
				border-top: 20rpx solid transparent;
				border-bottom: 20rpx solid transparent;
				top: 35%;
				right: -20rpx;
			}

			&_item {
				display: flex;
				flex-direction: column;
				height: 100%;
				justify-content: space-evenly;


				&_selector {
					display: flex;
					justify-content: space-evenly;

					&_active {
						border: white solid 5rpx;
						border-radius: 50%;
					}

				}
			}
		}

	}
}

.main {
	width: 100vw;
	height: 100vh;
}

.popup {
	position: relative;
	text-align: center;
	width: 100vw;
	height: 93vh;
	margin-top: 7vh;

	image {
		padding-top: 50rpx;
	}
}

.popupTitle {
	z-index: 1000;
	position: absolute;
	top: 0rpx;
	left: 0;
	right: 0;
	color: white;
	font-size: 32rpx;
	margin: 0 auto;
}

.exitTitle {
	z-index: 1000;
	position: absolute;
	top: 0rpx;
	right: 10rpx;
	color: white;
	font-size: 32rpx;
	text-shadow: 0 0 3px black;
}