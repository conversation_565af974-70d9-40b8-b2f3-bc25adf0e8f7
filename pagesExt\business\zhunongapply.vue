<template>
  <view>
      <block v-if="isload">
          <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==2">
              <parse :content="bset.verify_reject || '审核未通过：'"/>{{info.reason}}，请修改后重新提交
          </view>
          <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==0">
              <parse :content="bset.verify_notice || '您的入驻申请已提交成功，请耐心等待审核，平台将于7个工作日内联系您核实信息，请留意来电'"/>
          </view>
          <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==1">
              <parse :content="bset.verify_success || '恭喜您审核通过！'"/>
          </view>
          <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx">
              <parse :content="bset.verify_normal || '温馨提示：审核通过后需缴纳保证金可完成入驻'"/>
          </view>
  
          <form @submit="subform">
              <view class="apply_box">
                  
                  <view class="apply_item">
                      <view>联系人姓名<text style="color:red"> *</text></view>
                      <view class="flex-y-center"><input type="text" name="linkman" :value="info.linkman" placeholder="请填写姓名"></input></view>
                  </view>
                  <view class="apply_item">
                      <view>联系人电话<text style="color:red"> *</text></view>
                      <view class="flex-y-center"><input type="text" name="linktel" :value="info.linktel" placeholder="请填写手机号码"></input></view>
                  </view>
                  <view class="apply_item">
                      <view>联系人微信<text style="color:red"> *</text></view>
                      <view class="flex-y-center"><input type="text" name="wxid" :value="info.wxid" placeholder="请填写微信号"></input></view>
                  </view>
                  <view class="apply_item" v-if="info.zhanshi == 1">
                      <view>拓展人邀请码</view>
                      <view class="flex-y-center"><input type="text" name="tuozhanid" :value="info.tuozhanid" placeholder="请填写拓展员邀请码"></input></view>
                  </view>
              </view>
              
              <view class="apply_box">
                  <view class="apply_item">
                      <view>商家名称<text style="color:red"> *</text></view>
                      <view class="flex-y-center"><input type="text" name="name" :value="info.name" placeholder="请输入商家名称"></input></view>
                  </view>
                  <view class="apply_item">
                      <view>商家描述<text style="color:red"> *</text></view>
                      <view class="flex-y-center"><input type="text" name="desc" :value="info.desc" placeholder="请输入商家描述"></input></view>
                  </view>
                  <view class="apply_item">
                      <view>主营类目<text style="color:red"> *</text></view>
                      <view>
                          <picker @change="cateChange" :value="cindex" :range="cateArr">
                              <view class="picker">{{cateArr[cindex]}}</view>
                          </picker>
                      </view>
                  </view>
                    <!-- 费率选择 -->
   
              <view class="apply_item">
                <view>商户费率<text style="color:red"> *</text></view>
                <view>
                  <picker @change="rateChange" :value="rateIndex" :range="rateArr">
                    <view class="picker">{{ rateArr[rateIndex] }}</view>
                  </picker>
                </view>
              </view>
  
                  <view class="apply_item">
                      <view>店铺坐标<text style="color:red"> </text></view>
                      <view class="flex-y-center"><input type="text" disabled placeholder="请选择坐标" name="zuobiao" :value="latitude ? latitude+','+longitude:''" @tap="locationSelect"></input></view>
                  </view>
                  <view class="apply_item">
                      <view>店铺地址<text style="color:red"> *</text></view>
                      <view class="flex-y-center"><input type="text" name="address" :value="address" placeholder="请输入商家详细地址"></input></view>
                  </view>
                  <!-- 在店铺地址选项后面添加 -->
<view class="apply_item">
  <view>生产地址</view>
  <view class="flex-y-center">
      <input type="text" name="production_address" :value="info.production_address" placeholder="请输入生产地址(选填)"></input>
  </view>
</view>

<view class="apply_item">
  <view>实体店地址</view>
  <view class="flex-y-center">
      <input type="text" name="shop_address" :value="info.shop_address" placeholder="请输入实体店地址(选填)"></input>
  </view>
</view>

<view class="apply_item">
  <view>线上链接</view>
  <view class="flex-y-center">
      <input type="text" name="shop_url" :value="info.shop_url" placeholder="请输入线上店铺链接(选填)"></input>
  </view>
</view>
                  <input type="text" hidden="true" name="latitude" :value="latitude"></input>
                  <input type="text" hidden="true" name="longitude" :value="longitude"></input>
                  <!-- <view class="apply_item">
                      <view>客服电话<text style="color:red"> *</text></view>
                      <view class="flex-y-center"><input type="text" name="tel" :value="info.tel" placeholder="请填写客服电话"></input></view>
                  </view> -->
                  <view class="apply_item" style="line-height:50rpx"><textarea name="content" placeholder="请输入商家简介" :value="info.content"></textarea></view>
              </view>
              <view class="apply_box">
                  <view class="apply_item" style="border-bottom:0"><text>商家头像<text style="color:red"> *</text></text></view>
                  <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
                      <view v-for="(item, index) in pic" :key="index" class="layui-imgbox">
                          <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pic"><image src="/static/img/ico-del.png"></image></view>
                          <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
                      </view>
                      <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pic" v-if="pic.length==0"></view>
                  </view>
                  <input type="text" hidden="true" name="pic" :value="pic.join(',')" maxlength="-1"></input>
              </view>
              <view class="apply_box">
                  <view class="apply_item" style="border-bottom:0"><text>主要产品图片(3-5张)<text style="color:red"> *</text></text></view>
                  <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
                      <view v-for="(item, index) in pics" :key="index" class="layui-imgbox">
                          <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pics"><image src="/static/img/ico-del.png"></image></view>
                          <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
                      </view>
                      <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pics" v-if="pics.length<5"></view>
                  </view>
                  <input type="text" hidden="true" name="pics" :value="pics.join(',')" maxlength="-1"></input>
              </view>
              <view class="apply_box">
                  <view class="apply_item" style="border-bottom:0"><text>商家身份证正反面，营业执照，及相关资质<text style="color:red"> *</text></text></view>
                  <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
                      <view v-for="(item, index) in zhengming" :key="index" class="layui-imgbox">
                          <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="zhengming"><image src="/static/img/ico-del.png"></image></view>
                          <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
                      </view>
                      <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="zhengming"></view>
                  </view>
                  <input type="text" hidden="true" name="zhengming" :value="zhengming.join(',')" maxlength="-1"></input>
              </view>
              
            
            <!-- 新增提现方式部分 -->
            
              <view class="apply_box">
                  <view class="apply_item">
                      <text>登录用户名<text style="color:red"> *</text></text>
                      <view class="flex-y-center"><input type="text" name="un" :value="info.un" placeholder="请填写登录账号" autocomplete="off"></input></view>
                  </view>
                  <view class="apply_item">
                      <text>登录密码<text style="color:red"> *</text></text>
                      <view class="flex-y-center"><input type="password" name="pwd" :value="info.pwd" placeholder="请填写登录密码" autocomplete="off"></input></view>
                  </view>
                  <view class="apply_item">
                      <text>确认密码<text style="color:red"> *</text></text>
                      <view class="flex-y-center"><input type="password" name="repwd" :value="info.repwd" placeholder="请再次填写密码"></input></view>
                  </view>
              </view>
              <block v-if="bset.xieyi_show==1">
              <view class="flex-y-center" style="margin-left:20rpx;color:#999" v-if="!info.id || info.status==2">
                  <checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox value="1" :checked="isagree"></checkbox>阅读并同意</label></checkbox-group>
                  <text style="color:#666" @tap="showxieyiFun">《商户入驻协议》</text>
              </view>
              </block>
              <view style="padding:30rpx 0"><button v-if="!info.id || info.status==2" form-type="submit" class="set-btn" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">提交申请</button>
  </view>
          </form>
          <!-- 底部返回按钮 -->
             <view class="content">
               <view class="back-button" @tap="goBack">
                 <text class="t1">返回</text>
               </view>
             </view>
          <view id="xieyi" :hidden="!showxieyi" style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)">
              <view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px">
                  <view style="overflow:scroll;height:100%;">
                      <parse :content="bset.xieyi"/>
                  </view>
                  <view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center" @tap="hidexieyi">已阅读并同意</view>
              </view>
          </view>
      </block>
      <loading v-if="loading"></loading>
      <dp-tabbar :opt="opt"></dp-tabbar>
      <popmsg ref="popmsg"></popmsg>
  </view>
  </template>
  <script>
  var app = getApp();
  
  export default {
    data() {
      return {
        opt: {},
        loading: false,
        isload: false,
        menuindex: -1,
        pre_url: app.globalData.pre_url,
        datalist: [],
        pagenum: 1,
        cateArr: [],
        cindex: 0,
        rateArr: [], // 商户费率名称数组
        rateIndex: 0, // 当前选中的费率索引
        selectedRate: null, // 当前选中的费率对象
        isagree: false,
        showxieyi: false,
        pic: [],
        pics: [],
        zhengming: [],
        info: {},
        bset: {},
        latitude: '',
        longitude: '',
        address: '',
        withdrawMethods: ['支付宝', '银行卡'],
        withdrawMethodIndex: 1,
        selectedWithdrawMethod: '银行卡',
        conditions: {
          hasGroup: false,
          hasOnlineShop: false,
          hasOfflineShop: false
        },
        groupPics: [],
        licensePics: [],
      };
    },
  
    onLoad: function (opt) {
      this.opt = app.getopts(opt);
      this.getdata();
    },
    onPullDownRefresh: function () {
      this.getdata();
    },
    methods: {
      getdata: function () {
        var that = this;
        that.loading = true;
        app.get('ApiBusiness/apply', {}, function (res) {
          that.loading = false;
          if (res.status == 2) {
            app.alert(res.msg, function () {
              app.goto('/admin/index/index', 'redirect');
            });
            return;
          }
          uni.setNavigationBarTitle({
            title: res.title
          });
          var clist = res.clist;
          var cateArr = [];
          for (var i in clist) {
            cateArr.push(clist[i].name);
          }
           // 处理费率数据，模仿分类的处理方式
                  var feilv = res.feilv || [];
                  var rateArr = [];
                  for (var i in feilv) {
                    rateArr.push(feilv[i].name);
                  }
                  that.feilv = feilv;
                  that.rateArr = rateArr;
                  
          var pics = res.info ? res.info.pics : '';
          if (pics) {
            pics = pics.split(',');
          } else {
            pics = [];
          }
          var zhengming = res.info ? res.info.zhengming : '';
          if (zhengming) {
            zhengming = zhengming.split(',');
          } else {
            zhengming = [];
          }
          that.clist = res.clist;
          that.bset = res.bset;
          that.info = res.info;
          that.address = res.info.address;
          that.latitude = res.info.latitude;
          that.longitude = res.info.longitude;
          that.cateArr = cateArr;
          that.pic = res.info.logo ? [res.info.logo] : [];
          that.pics = pics;
          that.zhengming = zhengming;
          that.loaded();
          
          // 初始化条件相关数据
          if(res.info && res.info.conditions) {
            const conditions = !empty(res.info.conditions) ? res.info.conditions.split(',') : [];
            that.conditions = {
              hasGroup: conditions.includes('1'),
              hasOnlineShop: conditions.includes('2'),
              hasOfflineShop: conditions.includes('3')
            };
          }
          
          // 初始化图片数据
          that.groupPics = res.info.group_pics ? res.info.group_pics.split(',') : [];
          that.licensePics = res.info.license_pics ? res.info.license_pics.split(',') : [];
        });
      },
      cateChange: function (e) {
        this.cindex = e.detail.value;
      },
        // 费率选择事件处理
          rateChange: function (e) {
            this.rateIndex = e.detail.value;
            this.selectedRate = this.feilv[this.rateIndex]; // 更新选中的费率对象
          },
      withdrawMethodChange: function (e) {
        const index = e.detail.value;
        this.withdrawMethodIndex = index;
        this.selectedWithdrawMethod = this.withdrawMethods[index];
      },
      locationSelect: function () {
        var that = this;
        uni.chooseLocation({
          success: function (res) {
            that.info.address = res.name;
            that.info.latitude = res.latitude;
            that.info.longitude = res.longitude;
            that.address = res.name;
            that.latitude = res.latitude;
            that.longitude = res.longitude;
          }
        });
      },
      subform: function (e) {
        var that = this;
        var info = e.detail.value;
      
        // 如果存在 info.id，说明是修改操作
        if(that.info && that.info.id) {
            info.id = that.info.id;  // 添加 id 字段
        }
      
        // 验证所有必填项
        if (info.linkman == '') {
          app.error('请填写联系人姓名');
          return false;
        }
        if (info.linktel == '') {
          app.error('请填写联系人电话');
          return false;
        }
        if (info.tel == '') {
          app.error('请填写客服电话');
          return false;
        }
        if (info.name == '') {
          app.error('请填写商家名称');
          return false;
        }
        if (info.address == '') {
          app.error('请填写店铺地址');
          return false;
        }
        if (info.pic == '') {
          app.error('请上传商家头像');
          return false;
        }
        if (info.pics == '') {
          app.error('请上传商家照片');
          return false;
        }
        if (info.zhengming == '') {
          app.error('请上传证明材料');
          return false;
        }
        if (info.un == '') {
          app.error('请填写登录账号');
          return false;
        }
        if (info.pwd == '') {
          app.error('请填写登录密码');
          return false;
        }
        if (info.pwd.length < 6) {
          app.error('密码不能小于6位');
          return false;
        }
        if (info.repwd != info.pwd) {
          app.error('两次输入的密码不一致');
          return false;
        }
      
       
      
        // 赋值地址、纬度和经度
        info.address = that.address;
        info.latitude = that.latitude;
        info.longitude = that.longitude;
      
        // 确保商户费率已选择并赋值
        info.rate_id = that.feilv[that.rateIndex] ? that.feilv[that.rateIndex].id : null;
        if (!info.rate_id) {
          app.error('请选择商户费率');
          return false;
        }
      
        // 将提现方式和相关字段添加到 info 对象
        info.withdrawMethod = that.selectedWithdrawMethod;
        if (that.selectedWithdrawMethod === '支付宝') {
          info.alipayName = that.info.alipayName;
          info.alipayAccount = that.info.alipayAccount;
        } else if (that.selectedWithdrawMethod === '银行卡') {
          info.bankcarduser = that.info.bankcarduser;
          info.bankCardAccount = that.info.bankCardAccount;
          info.bankName = that.info.bankName;
        } else if (that.selectedWithdrawMethod === '三方支付') {
          info.jialianMerchantNumber = that.info.jialianMerchantNumber;
          info.jialianTerminalNumber = that.info.jialianTerminalNumber;
        }
      
        // 检查是否同意协议（如果需要）
        if (that.bset.xieyi_show == 1 && !that.isagree) {
          app.error('请先阅读并同意商户入驻协议');
          return false;
        }
      
        // 处理申请条件
        const selectedConditions = [];
        if(that.conditions.hasGroup) {
          if(!that.groupPics.length) {
            app.error('请上传微信群截图');
            return false;
          }
          selectedConditions.push('1');
          info.group_pics = that.groupPics.join(',');
        }
        
        if(that.conditions.hasOnlineShop) {
          if(!info.shopUrl) {
            app.error('请填写线上店铺链接');
            return false;
          }
          selectedConditions.push('2');
        }
        
        if(that.conditions.hasOfflineShop) {
          if(!info.shopAddress) {
            app.error('请填写线下店铺地址');
            return false;
          }
          if(!that.licensePics.length) {
            app.error('请上传营业执照');
            return false;
          }
          selectedConditions.push('3');
          info.license_pics = that.licensePics.join(',');
        }


        info.conditions = selectedConditions.join(',');
      
        // 提交表单数据到服务器
        app.showLoading('提交中');
        app.post("ApiBusiness/apply", { info: info }, function (res) {
          app.showLoading(false);
          if (res.status == 1) {
            app.success(res.msg);
            setTimeout(function () {
              if (res.after_register_url) {
                app.goto(res.after_register_url);
              } else {
                app.goto(app.globalData.indexurl);
              }
            }, 1500);
          } else {
            app.error(res.msg);
          }
        });
      },
  
      isagreeChange: function (e) {
        var val = e.detail.value;
        this.isagree = val.length > 0;
      },
      showxieyiFun: function () {
        this.showxieyi = true;
      },
      hidexieyi: function () {
        this.showxieyi = false;
        this.isagree = true;
      },
      uploadimg: function (e) {
        var that = this;
        var field = e.currentTarget.dataset.field;
        var pics = that[field];
        if(!pics) pics = [];
        app.chooseImage(function(urls) {
          for(var i=0; i<urls.length; i++) {
            pics.push(urls[i]);
          }
          if(field == 'pic') that.pic = pics;
          if(field == 'pics') that.pics = pics;
          if(field == 'zhengming') that.zhengming = pics;
          if(field == 'groupPics') that.groupPics = pics;
          if(field == 'licensePics') that.licensePics = pics;
        }, 1);
      },
      // 返回功能
        goBack: function () {
          uni.navigateBack({
            delta: 1
          });
        },
      removeimg: function (e) {
        var that = this;
        var index = e.currentTarget.dataset.index;
        var field = e.currentTarget.dataset.field;
        if(field == 'groupPics') {
          var pics = that.groupPics;
          pics.splice(index, 1);
          that.groupPics = pics;
        } else if(field == 'licensePics') {
          var pics = that.licensePics;
          pics.splice(index, 1);
          that.licensePics = pics;
        } else {
          if (field == 'pic') {
            var pics = that.pic;
            pics.splice(index, 1);
            that.pic = pics;
          } else if (field == 'pics') {
            var pics = that.pics;
            pics.splice(index, 1);
            that.pics = pics;
          } else if (field == 'zhengming') {
            var pics = that.zhengming;
            pics.splice(index, 1);
            that.zhengming = pics;
          }
        }
      },
      conditionChange(e) {
        const values = e.detail.value;
        this.conditions = {
          hasGroup: values.includes('1'),
          hasOnlineShop: values.includes('2'),
          hasOfflineShop: values.includes('3')
        };
      },
    }
  };
  </script>
  
  <style>
  radio{transform: scale(0.6);}
  checkbox{transform: scale(0.6);}
  .apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
  .apply_title { background: #fff}
  .apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}
  .apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}
  
  .apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }
  .apply_box .apply_item:last-child{ border:none}
  .apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}
  .apply_item input::placeholder{ color:#999999}
  .apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}
  .apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }
  .apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }
  .set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
  
  .layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
  .layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}
  .layui-imgbox-close image{width:100%;height:100%}
  .layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
  .layui-imgbox-img>image{max-width:100%;}
  .layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
  .uploadbtn{position:relative;height:200rpx;width:200rpx}
  
  /* 返回按钮样式 */
  .back-button {
    width: 90%;
    background: #b60000;
    color: #fff;
    text-align: center;
    height: 96rpx;
    line-height: 96rpx;
    border-radius: 50px;
    margin-top: 0rpx;
    margin: auto;
  }
  
  .back-button .t1 {
    font-size: 30rpx;
    color: #fff;
  }
  
  .condition-item {
    padding: 20rpx 0;
    border-bottom: 1px solid #eee;
  }
  
  .condition-item:last-child {
    border-bottom: none;
  }
  
  .group-proof {
    margin-top: 20rpx;
    padding-left: 40rpx;
  }
  
  .placeholder-style {
    color: #999999;
  }
  </style>
  