<template>
	<view class="container">
		<block v-if="isload">
			<dd-tab :itemdata="[t('余额')+'明细','充值记录','提现记录','转账记录',t('现金券')+'明细','冻结明细','消费值/绿积分',t('虚拟账号积分')+'明细','月结明细','贡献值明细','创业值明细',t('黄积分')+'明细','积分明细','收益池明细']" :itemst="['0','1','2','4','8','12','6','11','16','10','7','9','5','15']" :st="st"
				:isfixed="true" @changetab="changetab"></dd-tab>
			<view class="content">
				<view v-if="nodata" class="empty-box">
					<!-- <image src="/static/img/empty.png" mode="aspectFit" class="empty-img"></image>
					<text class="empty-text">暂无记录1</text> -->
				</view>
				<block v-else>
					<block v-if="st==0">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后余额: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==1">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">充值金额：{{item.money}}元</text>
								<text class="t2">{{item.createtime}}</text>
							</view>
							<view class="f3">
								<text class="t1" v-if="item.status==0">充值失败</text>
								<text class="t2" v-if="item.status==1">充值成功</text>
							</view>
						</view>
					</block>
					<block v-if="st==2">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">提现金额：{{item.money}}元</text>
								<text class="t2">{{item.createtime}}</text>
							</view>
							<view class="f3">
								<text class="t1" v-if="item.status==0">审核中</text>
								<text class="t1" v-if="item.status==1">已审核</text>
								<text class="t2" v-if="item.status==2">已驳回</text>
								<text class="t1" v-if="item.status==3">已打款</text>
							</view>
						</view>
					</block>
					<block v-if="st==4">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t1" v-if="item.phone">用户：{{item.phone}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后余额: {{item.after_money}}</text>
								<text class="t3" v-if="item.charge_money">手续费: {{item.charge_money}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==8">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后余额: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==12">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后余额: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.score>0">+{{item.score}}</text>
								<text class="t2" v-else>{{item.score}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==6">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后
									<text v-if="item.money>0">消费值</text>
									<text v-else>绿积分</text>: {{item.after_money}}
								</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==11">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后余额: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==10">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后{{t('贡献值')}}: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==16">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后月结金额: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.arrears>0">+{{item.arrears}}</text>
								<text class="t2" v-else>{{item.arrears}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==7">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后创业值: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.busTotal>0">+{{item.busTotal}}</text>
								<text class="t2" v-else>{{item.busTotal}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==9">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后{{t('黄积分')}}: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==5">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后{{t('积分')}}: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
					<block v-if="st==15">
						<view v-for="(item, index) in datalist" :key="index" class="item">
							<view class="f1">
								<text class="t1">{{item.remark}}</text>
								<text class="t2">{{item.createtime}}</text>
								<text class="t3">变更后收益池: {{item.after}}</text>
							</view>
							<view class="f2">
								<text class="t1" v-if="item.money>0">+{{item.money}}</text>
								<text class="t2" v-else>{{item.money}}</text>
							</view>
						</view>
					</block>
				</block>
			</view>
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		inject: ['reload'], // 注入 reload 方法
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				canwithdraw: false,
				textset: {},
				st: 0,
				datalist: [],
				pagenum: 1,
				nodata: false,
				nomore: false
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.st = this.opt.st || 0;
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata(true);
		},
		onReachBottom: function() {
			if (!this.nodata && !this.nomore) {
				this.pagenum = this.pagenum + 1;
				this.getdata(true);
			}
		},
		methods: {
			getPageTitle(st) {
				const titleMap = {
					'0': '余额明细',
					'1': '充值记录',
					'2': '提现记录',
					'4': '转账记录',
					'8': '现金券明细',
					'12': '冻结明细',
					'6': '消费值/绿积分',
					'11': '虚拟账号积分',
					'16': '月结明细',
					'10': this.t('贡献值') + '明细',
					'7': '创业值明细',
					'9': this.t('黄积分') + '明细',
					'5': this.t('积分') + '明细',
					'15': '收益池明细'
				}
				return titleMap[st] || '记录明细'
			},
			getdata: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var st = that.st;
				that.nodata = false;
				that.nomore = false;
				that.loading = true;
				app.post('ApiMy/moneylog', {
					st: st,
					pagenum: pagenum
				}, function(res) {
					that.loading = false;
					var data = res.data;
					if (pagenum == 1) {
						that.textset = app.globalData.textset;
						uni.setNavigationBarTitle({
							title: that.getPageTitle(st)
						});
						that.canwithdraw = res.canwithdraw;
						that.datalist = data;
						if (data.length == 0) {
							that.nodata = true;
						}
						that.loaded();
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}
				});
			},
			changetab: function(st) {
				this.st = st;
				uni.setNavigationBarTitle({
					title: this.getPageTitle(st)
				});
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				this.getdata();
			},
			onclick1: function(tixianid) {
				var that = this;
				app.showLoading('提交中');
				app.post('ApiMy/withtixiancheck', {
					tixianid: tixianid
				}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					} else {
						app.success(res.msg);
						that.subscribeMessage(function() {
							setTimeout(function() {
								app.goto('moneylog?st=3');
							}, 1000);
						});
					}
				});
			},
			getBalanceText(st) {
				const textMap = {
					'0': '余额',
					'1': '余额',
					'2': '余额', 
					'4': '余额',
					'6': '',
					'8': this.t('现金券'),
					'12': '冻结金额',
					'11': '虚拟积分',
					'16': '月结金额',
					'10': this.t('贡献值'),
					'7': '创业值',
					'9': this.t('黄积分'),
					'5': this.t('积分'),
					'15': '收益池'
				}
				return textMap[st] || '余额'
			},
			getAmount(item, st) {
				const amountMap = {
					'0': 'money',
					'1': 'money',
					'2': 'money',
					'4': 'money', 
					'6': 'money',
					'8': 'money',
					'12': 'score',
					'11': 'money',
					'16': 'arrears',
					'10': 'money',
					'7': 'busTotal',
					'9': 'money',
					'5': 'money',
					'15': 'money'
				}
				const field = amountMap[st] || 'money'
				return item[field]
			},
			getStatusText(item) {
				if(item.status === 0) {
					return '待审核';
				} else if(item.status === 1) {
					return '已通过';
				} else if(item.status === 2) {
					return '已拒绝';
				} else if(item.is_tk === 1) {
					return '已退款';
				}
				return '未知状态';
			},
		}
	};
</script>
<style>
	.container {
		width: 100%;
		margin-top: 90rpx;
		display: flex;
		flex-direction: column
	}

	.content {
		width: 94%;
		margin: 0 3% 20rpx 3%;
	}

	.content .item {
		width: 100%;
		background: #fff;
		margin: 20rpx 0;
		padding: 20rpx 20rpx;
		border-radius: 8px;
		display: flex;
		align-items: center
	}

	.content .item:last-child {
		border: 0
	}

	.content .item .f1 {
		width: 500rpx;
		display: flex;
		flex-direction: column
	}

	.content .item .f1 .t1 {
		color: #000000;
		font-size: 30rpx;
		word-break: break-all;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.content .item .f1 .t2 {
		color: #666666;
		font-size: 26rpx;
		margin: 10rpx 0;
	}

	.content .item .f1 .t3 {
		color: #666666;
		font-size: 26rpx;
	}

	.content .item .f2 {
		flex: 1;
		width: 200rpx;
		font-size: 36rpx;
		text-align: right
	}

	.content .item .f2 .t1 {
		color: #03bc01
	}

	.content .item .f2 .t2 {
		color: #000000
	}

	.content .item .f3 {
		flex: 1;
		width: 200rpx;
		font-size: 32rpx;
		text-align: right
	}

	.content .item .f3 .t1 {
		color: #03bc01
	}

	.content .item .f3 .t2 {
		color: #000000
	}

	.data-empty {
		background: #fff
	}
</style>
<!-- | st值 | 明细类型 |
|------|----------|
| '0' | 余额明细 |
| '1' | 充值记录 |
| '2' | 提现记录 |
| '4' | 转账记录 |
| '8' | 现金券明细 |
| '12' | 冻结明细 |
| '6' | 消费值/绿积分 |
| '11' | 虚拟账号积分 |
| '16' | 月结明细 |
| '10' | 贡献值明细 |
| '7' | 创业值明细 |
| '9' | 黄积分明细 |
| '5' | 积分明细 |
| '15' | 收益池明细 | -->