<template>
	<view class="page flex-col">
		<view class="box_6 flex-col"
			:style="'background: linear-gradient(180deg, ' + t('color1') + ', ' + t('color1') + ')'">


			<div class="nav-bar_1 ">

			</div>
			<view class="box_7 flex-row justify-between">
				<view class="single-avatar_1 flex-col">
					<image class="image_1 weui_avatar_circle" referrerpolicy="no-referrer" :src="pagecontent.headimg" />
				</view>
				<view class="text-wrapper_3 flex-col justify-between">
					<text class="text_3">{{ pagecontent.nickname }}</text> <!-- 显示用户昵称 -->


					<text class="text_4">{{ pagecontent.level_name }}</text> <!-- 显示用户等级 -->
					<text class="text_4">用户ID:{{ pagecontent.id }}</text>
				</view>
			</view>
			<view class="user-intro-section">
				<text class="text_5 user-intro">{{ limitedIntro }}</text>
			</view>
			<view class="box_8 flex-row justify-between">
				<view class="text-group_4 flex-col justify-between" @tap="goto"
					:data-url="'/daihuobiji/detail/member/follower'">
					<text class="text_18">{{ pagecontent.following_count || 0 }}</text> <!-- 显示关注数量 -->
					<text class="text_19">我的关注</text>
				</view>
				<view class="text-group_5 flex-col justify-between" @tap="goto"
					:data-url="'/daihuobiji/detail/member/following'">
					<text class="text_20">{{ pagecontent.followers_count|| 0 }}</text> <!-- 显示粉丝数量 -->
					<text class="text_21">关注我的</text>
				</view>
				<view class="text-wrapper_4 flex-col justify-between">
					<text class="text_22">{{ pagecontent.money|| 0 }}</text> <!-- 显示积分/余额 -->
					<text class="text_23">积分/余额</text>
				</view>
			</view>
			<view class="box_9 flex-row">
				<button class="button_1 flex-col" @click="onClick_2">
					<text class="text_24">我的简介</text>
				</button>
				<view class="icon_7 flex-col" @click="onClick_1">
					<image class="label_2" referrerpolicy="no-referrer" src="../../static/img/set.png" />
				</view>
				<!-- 切换身份事件 -->
				<button class="button_11 flex-col" @click="LinkTo">
					<text class="text_24" style="font-weight: 700 !important;">切换身份</text>
				</button>

			</view>

			<view class="group_3 flex-row">

				<view style="text-align: center;">

					<view class="text_6">{{ pagecontent.citationCount || 0 }}</view>

					<view class="text_7">被引用次数</view>

				</view>

				<view style="width: 0.5px;background-color: #eaeaea;height: 32px"></view>

				<view style="text-align: center;" @click="goToFatielog">

					<view class="text_6">{{ pagecontent.comment_count || 0 }}</view>

					<view class="text_7">我的评论</view>

				</view>

				<view style="width: 0.5px;background-color: #eaeaea;height: 32px"></view>


				<view style="text-align: center;" @click="goto" data-url="/daihuobiji/detail/fatielog">

					<view class="text_6">{{ pagecontent.note_count || 0 }}</view>

					<view class="text_7">我的笔记</view>

				</view>

				

			</view>
		</view>
		<view class="flex-col">
			<view class="text-wrapper_5 flex-row justify-between">
				<!-- 笔记标签 -->
				<text class="text_10" :class="{ 'active': activeTab === 'notes' }"
					:style="'color: ' + (activeTab === 'notes' ? t('color1') : '#999999')" @click="switchTab('notes')">
					笔记
				</text>

				<!-- 关注标签 -->
				<text class="text_11" :class="{ 'active': activeTab === 'favorites' }"
					:style="'color: ' + (activeTab === 'favorites' ? t('color1') : '#999999')"
					@click="switchTab('favorites')">
					点赞
				</text>
			</view>

			<view style="padding: 24rpx 24rpx 60rpx 24rpx; margin-top: 15rpx;">
				
				<view class="list">

					<view class="pbl" v-if="activeTab=== 'notes'" v-for="(item, index) in noteList" :key="index">
						<view class="image" @click="goItem(item)">
							<image fade-show lazy-load :lazy-load-margin="0" mode="widthFix" :src="item.coverimg">
							</image>
						</view>
						<view class="title" v-html="item.title" @click="goItem(item)"></view>

						<view @click="goItem(item)"
							style="display: flex;align-items: center;justify-content: space-between;padding: 10px;color: #aaa;">

							<view style="display: flex;align-items: center;width: 60%;">
								<img style="width: 20px;height: 20px;border-radius: 50px;" :src="item.headimg"></img>
								<view
									style="font-size: 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;margin-left: 5px;">
									{{item.nickname}}
								</view>
							</view>

							<view style="display: flex;align-items: center;">
								<image style="width: 12px;height: 12px;" src="../../static/restaurant/like1.png">
								</image>
								<view style="font-size: 10px;margin-left: 5px;">{{item.zan}}</view>
							</view>
						</view>

					</view>

					<view class="pbl" v-if="activeTab=== 'favorites'" v-for="(item, index) in collectList" :key="index">
						<view class="image" @click="goItem(item)">
							<image fade-show lazy-load :lazy-load-margin="0" mode="widthFix" :src="item.coverimg">
							</image>
						</view>
						<view class="title" v-html="item.title" @click="goItem(item)"></view>

						<view @click="goItem(item)"
							style="display: flex;align-items: center;justify-content: space-between;padding: 10px;color: #aaa;">

							<view style="display: flex;align-items: center;width: 60%;">
								<img style="width: 20px;height: 20px;border-radius: 50px;" :src="item.headimg"></img>
								<view
									style="font-size: 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;margin-left: 5px;">
									{{item.nickname}}
								</view>
							</view>

							<view style="display: flex;align-items: center;">
								<image style="width: 12px;height: 12px;" src="../../static/restaurant/like1.png">
								</image>
								<view style="font-size: 10px;margin-left: 5px;">{{item.zan}}</view>
							</view>
						</view>

					</view>

				</view>

				<uni-load-more :status="loadStatus" v-if="!loading"></uni-load-more>
			</view>

		</view>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();
	export default {

		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				pagecontent: {}, // 这里应该是一个对象
				copyright: '',
				activeTab: 'notes', // 认显示“笔记”标签页
				xixie: false,
				noteList: [],
				collectList: [],
				queryParams: {},
				loadStatus: 'more', // 更多
				needRefresh: false // 添加刷新标记
			}
		},
		watch: {
			activeTab: {
				handler(newValue, oldValue) {
					if (newValue == 'notes') {
						this.queryParams = {
							pagenum_notes: 1,
							pernum_notes: 10
						}
					} else {
						this.queryParams = {
							pagenum_favorites: 1,
							pernum_favorites: 10
						}
					}
				},
				immediate: true, //初始化
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();

		},
		onShow() {
			// 如果是从其他页面返回，则刷新数据
			const pages = getCurrentPages();
			if (pages.length > 1) {
				this.getdata();
			}
		},
		methods: {
			getdata(loadmore) {
				if (!loadmore) {
					// 重置分页参数
					if (this.activeTab === 'notes') {
						this.queryParams.pagenum_notes = 1;
						this.noteList = [];
					} else {
						this.queryParams.pagenum_favorites = 1;
						this.collectList = [];
					}
				}
				
				this.loading = true;
				app.get('ApiMy/userbiji', this.queryParams, (data) => {
					this.loading = false;
					this.pagecontent = data.userinfo;
					if (this.activeTab == 'notes') {
						let arrList = data.notes.data;
						this.noteList = loadmore ? [...this.noteList, ...arrList] : arrList;
						this.loadStatus = arrList.length >= 10 ? 'more' : 'noMore';
					} else {
						let arrList = data.favorite_notes.data;
						this.collectList = loadmore ? [...this.collectList, ...arrList] : arrList;
						this.loadStatus = arrList.length >= 10 ? 'more' : 'noMore';
					}
					uni.stopPullDownRefresh();
				});
			},
			onClick_1: function() {
				// 处理点击事件
				uni.navigateTo({
					url: '/pagesExa/my/set'
				});
			},
			onClick_2: function() {
				// 处理点击事件
				uni.navigateTo({
					url: '/pagesExa/my/setyonghujianjie'
				});
			},
			switchTab(tab) {

				if (this.activeTab == tab) {
					return
				}

				this.activeTab = tab;
				this.$nextTick(() => {
					this.getdata();
				})

			},

			goItem(data) {
				uni.navigateTo({
					url: '../../daihuobiji/detail/index?id=' + data.id
				});
			},
			LinkTo() {
				uni.navigateTo({
					url: '/pages/my/usercenter'
				});
			},
			// 下拉刷新
			onPullDownRefresh() {
				if (this.activeTab == 'notes') {
					this.noteList = []
					this.queryParams.pagenum_notes = 1
				} else {
					this.collectList = [];
					this.queryParams.pagenum_favorites = 1
				}
				this.getdata(); //重新获取数据
			},
			// 上拉加载
			onReachBottom() {
				console.log("上拉动作")
				if (this.activeTab == 'notes') {
					this.queryParams.pagenum_notes += 1
					if ((this.queryParams.pagenum_notes - 1) * this.queryParams.pernum_notes > this.noteList.length) {
						this.loadStatus = 'noMore';
					} else {
						this.loadStatus = 'loading';
						this.getdata();
					}
				} else {

					this.queryParams.pagenum_favorites += 1
					if ((this.queryParams.pagenum_favorites - 1) * this.queryParams.pernum_favorites > this.collectList
						.length) {
						this.loadStatus = 'noMore';
					} else {
						this.loadStatus = 'loading';
						this.getdata();
					}
				}

			},
			goToFatielog() {
				console.log('触发')
				uni.navigateTo({
					url: '../../daihuobiji/detail/fatielog?type=favorites'
				});
			}
		},
		computed: {
			limitedIntro() {
				const intro = this.pagecontent.yonghujianjie || '这个用户很懒，还没有填写简介哦~';
				return intro.length > 30 ? intro.slice(0, 30) + '...' : intro;
			}
		}
	}
</script>
<!-- #ifdef !MP-WEIXIN -->
<style>
	/* 在微信小程序平台中，调整顶部区域高度 */
	.box_6 {
		height: calc(120px - 100px);
	}
</style>
<!-- #endif -->
<style lang="less">
	.weui_avatar_circle {
		border-radius: 50%;
	}

	.box_10 {
		display: flex;
		flex-direction: column;
	}

	.block_4,
	.block_5 {
		display: flex;
	}

	.tabs_2 {
		display: flex;
		flex-direction: column;
	}

	.text-wrapper_5 {
		display: flex;
		justify-content: space-between;
	}

	.text_10,
	.text_11 {
		cursor: pointer;
		padding: 10px;
	}

	.text_10.active,
	.text_11.active {
		font-weight: bold;
		color: #007bff;
		/* 活动标签的颜色 */
	}

	.block_5 {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
		gap: 10px;
		padding: 10px;
	}

	.item {
		display: flex;
		flex-direction: column;
		border: 1px solid #ddd;
		border-radius: 4px;
		overflow: hidden;
	}

	.image_2,
	.image_3 {
		width: 100%;
		height: auto;
	}

	.text_12,
	.text_13 {
		padding: 10px;
	}

	.text_12 {
		width: 303rpx;
		height: 74rpx;
		font-family: MiSans VF, MiSans VF;
		font-weight: 400;
		font-size: 28rpx;
		color: #333333;
		line-height: 33rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.box_6 {
		background-image: linear-gradient(180deg,
				rgba(227, 88, 49, 1) 0,
				rgba(227, 88, 49, 1) 77.110332%,
				rgba(227, 88, 49, 0) 99.153721%);
		position: relative;
		width: 750rpx;
		height: 673rpx;
	}

	.nav-bar_1 {
		height: 73rpx;
		margin-top: 80rpx;
		width: 750rpx;
	}

	.icon_6 {
		width: 44rpx;
		height: 44rpx;
		margin: 12rpx 0 0 674rpx;
	}

	.box_7 {
		width: 335rpx;
		height: 126rpx;
		margin: 29rpx 0 0 32rpx;
	}

	.single-avatar_1 {
		background-color: rgba(255, 255, 255, 1);
		border-radius: 50%;
		height: 126rpx;
		border: 1px solid rgba(255, 255, 255, 1);
		width: 126rpx;
	}

	.image_1 {
		width: 126rpx;
		height: 126rpx;
	}

	.text-wrapper_3 {
		width: 183rpx;
		height: 84rpx;
		margin-top: 21rpx;
	}

	.text_3 {
		width: 158rpx;
		height: 45rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 32rpx;
		font-family: PingFang SC-Semibold;
		font-weight: 600;
		text-align: center;
		white-space: nowrap;
		line-height: 32rpx;
	}

	.text_4 {
		width: 183rpx;
		height: 31rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 0.6);
		font-size: 22rpx;
		font-family: PingFang SC-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 22rpx;
		margin-top: 8rpx;
	}

	.text_5 {
		width: 144rpx;
		height: 32rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 24rpx;
		font-family: MiSans-Medium;
		font-weight: 500;
		text-align: center;
		white-space: nowrap;
		line-height: 24rpx;
		margin: 32rpx 0 0 32rpx;
	}

	.box_8 {
		width: 321rpx;
		height: 67rpx;
		margin: 32rpx 0 202rpx 32rpx;
	}

	.text-group_4 {
		width: 88rpx;
		height: 67rpx;
	}

	.text_18 {
		width: 31rpx;
		height: 32rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 24rpx;
		font-family: MiSans VF-Semibold;
		font-weight: 600;
		text-align: center;
		white-space: nowrap;
		line-height: 24rpx;
		margin-left: 30rpx;
	}

	.text_19 {
		width: 88rpx;
		height: 29rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 22rpx;
		margin-top: 6rpx;
	}

	.text-group_5 {
		width: 88rpx;
		height: 67rpx;
		margin-left: 24rpx;
	}

	.text_20 {
		width: 31rpx;
		height: 32rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 24rpx;
		font-family: MiSans VF-Semibold;
		font-weight: 600;
		text-align: center;
		white-space: nowrap;
		line-height: 24rpx;
		margin-left: 30rpx;
	}

	.text_21 {
		width: 88rpx;
		height: 29rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 22rpx;
		margin-top: 6rpx;
	}

	.text-wrapper_4 {
		width: 97rpx;
		height: 67rpx;
		margin-left: 24rpx;
	}

	.text_22 {
		width: 31rpx;
		height: 32rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 24rpx;
		font-family: MiSans VF-Semibold;
		font-weight: 600;
		text-align: center;
		white-space: nowrap;
		line-height: 24rpx;
		margin-left: 0rpx;
	}

	.text_23 {
		width: 97rpx;
		height: 29rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 22rpx;
		margin-top: 6rpx;
	}

	.box_9 {
		position: absolute;
		left: 321rpx;
		top: -238rpx;
		// width: 824rpx;
		height: 841rpx;
		background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG385ce8759f0b88c7034c8e89504e756e.png) -218rpx 0rpx no-repeat;
		background-size: 1014rpx 1025rpx;

		.changeBtn {
			color: red;
		}
	}

	.button_1 {
		background-color: rgba(255, 255, 255, 0.1);
		border-radius: 54px;
		height: 56rpx;
		border: 1px solid rgba(255, 255, 255, 0.3);
		width: 163rpx;
		margin: 647rpx 0 0 138rpx;
	}

	.button_11 {
		background-color: rgba(255, 255, 255, 0.1);
		border-radius: 54px;
		height: 56rpx;
		border: 1px solid rgba(255, 255, 255, 0.3);
		width: 163rpx;
		position: absolute;
		top: 481rpx;
		left: 230rpx;
		font-weight: 700;
		// margin: 647rpx 0 0 138rpx;
	}

	.text_24 {
		width: 96rpx;
		height: 32rpx;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 24rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 24rpx;
		margin: 12rpx 0 0 34rpx;
	}

	.icon_7 {
		background-color: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
		height: 56rpx;
		border: 1px solid rgba(255, 255, 255, 0.3);
		width: 56rpx;
		// margin: 647rpx 443rpx 0 24rpx;
		margin: 647rpx 0rpx 0 24rpx;
	}

	.label_2 {
		width: 38rpx;
		height: 38rpx;
		margin: 9rpx 0 0 9rpx;
	}

	.group_1 {
		height: 88rpx;
		width: 750rpx;
		position: absolute;
		left: 0;
		top: 0;
	}

	.block_1 {
		width: 750rpx;
		height: 88rpx;
	}

	.text-wrapper_1 {
		width: 108rpx;
		height: 39rpx;
		overflow-wrap: break-word;
		font-size: 0;
		letter-spacing: -0.5600000023841858px;
		font-family: PingFang SC-Semibold;
		font-weight: 600;
		text-align: center;
		white-space: nowrap;
		line-height: 28rpx;
		margin: 31rpx 0 0 42rpx;
	}

	.text_1 {
		width: 108rpx;
		height: 39rpx;
		overflow-wrap: break-word;
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		letter-spacing: -0.5600000023841858px;
		font-family: PingFang SC-Semibold;
		font-weight: 600;
		text-align: center;
		white-space: nowrap;
		line-height: 28rpx;
	}

	.text_notes {
		width: 108rpx;
		height: 39rpx;
		overflow-wrap: break-word;
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		font-family: PingFang SC-Semibold;
		font-weight: 600;
		text-align: left;
		white-space: nowrap;
		line-height: 28rpx;
	}

	.box_1 {
		background-color: rgba(51, 51, 51, 1);
		width: 34rpx;
		height: 22rpx;
		margin: 36rpx 0 0 438rpx;
	}

	.box_2 {
		background-color: rgba(51, 51, 51, 1);
		width: 31rpx;
		height: 22rpx;
		margin: 35rpx 0 0 10rpx;
	}

	.label_1 {
		width: 49rpx;
		height: 23rpx;
		margin: 35rpx 29rpx 0 9rpx;
	}

	.group_3 {
		background-color: rgba(255, 255, 255, 1);
		border-radius: 12px;
		position: absolute;
		left: 24rpx;
		top: 499rpx;
		width: 702rpx;
		height: 152rpx;

		display: flex;
		justify-content: space-between;
		padding: 20px 30px;
	}



	.text-group_6 {
		width: 110rpx;
		height: 86rpx;
		margin: 33rpx 0 0 80rpx;
	}

	.text_6 {
		height: 41rpx;
		overflow-wrap: break-word;
		color: rgba(51, 51, 51, 1);
		font-size: 32rpx;
		font-family: DINPro-Bold;
		font-weight: 700;
		text-align: center;
		white-space: nowrap;
		line-height: 32rpx;

	}

	.text_7 {
		width: 110rpx;
		height: 29rpx;
		overflow-wrap: break-word;
		color: rgba(153, 153, 153, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 22rpx;
		margin-top: 16rpx;
	}

	.box_3 {
		background-color: rgba(234, 234, 234, 1);
		width: 1rpx;
		height: 64rpx;
		margin: 44rpx 0 0 40rpx;
	}

	.text-group_7 {
		width: 88rpx;
		height: 86rpx;
		margin: 33rpx 90rpx 0 90rpx;
	}

	.text-group_8 {
		width: 88rpx;
		height: 86rpx;
		margin: 33rpx 133rpx 0 129rpx;
	}

	.text_8 {
		width: 53rpx;
		height: 41rpx;
		overflow-wrap: break-word;
		color: rgba(51, 51, 51, 1);
		font-size: 32rpx;
		font-family: DINPro-Bold;
		font-weight: 700;
		text-align: center;
		white-space: nowrap;
		line-height: 32rpx;
		margin-left: 18rpx;
	}

	.text_9 {
		width: 88rpx;
		height: 29rpx;
		overflow-wrap: break-word;
		color: rgba(153, 153, 153, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 22rpx;
		margin-top: 16rpx;
	}

	.box_10 {
		width: 750rpx;
		height: 842rpx;
	}

	.block_4 {
		width: 160rpx;
		height: 50rpx;
		margin: 2rpx 0 0 32rpx;
	}

	.tabs_2 {
		height: 37rpx;
		width: 160rpx;
	}

	.text-wrapper_5 {
		width: 160rpx;
		height: 37rpx;
	}

	.text_10 {
		width: 56rpx;
		height: 37rpx;
		overflow-wrap: break-word;
		color: rgba(227, 88, 47, 1);
		font-size: 28rpx;
		font-family: MiSans VF-Medium;
		font-weight: 500;
		text-align: center;
		white-space: nowrap;
		line-height: 28rpx;
	}

	.text_11 {
		width: 56rpx;
		height: 37rpx;
		overflow-wrap: break-word;
		color: rgba(153, 153, 153, 1);
		font-size: 28rpx;
		font-family: MiSans VF-Medium;
		font-weight: 500;
		text-align: center;
		white-space: nowrap;
		line-height: 28rpx;
	}

	.block_5 {
		width: 702rpx;
		height: 620rpx;
		margin: 24rpx 0 159rpx 24rpx;
	}

	.block_2 {
		background-color: rgba(255, 255, 255, 1);
		border-radius: 12px;
		width: 343rpx;
		height: 620rpx;
	}

	.image_2 {
		width: 343rpx;
		height: 490rpx;
	}

	.text_12 {
		width: 303rpx;
		height: 74rpx;
		overflow-wrap: break-word;
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: left;
		margin: 24rpx 0 32rpx 20rpx;
	}

	.block_3 {
		background-color: rgba(255, 255, 255, 1);
		border-radius: 12px;
		width: 343rpx;
		height: 470rpx;
	}

	.image_3 {
		width: 343rpx;
		height: 340rpx;
	}

	.text_13 {
		width: 303rpx;
		height: 74rpx;
		overflow-wrap: break-word;
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: left;
		margin: 24rpx 0 32rpx 20rpx;
	}

	.tab-bar_1 {
		box-shadow: inset 0px 1px 0px 0px rgba(226, 228, 231, 1);
		background-color: rgba(255, 255, 255, 1);
		width: 750rpx;
		height: 110rpx;
		margin-top: -1rpx;
	}

	.tab-bar-item_6 {
		width: 48rpx;
		height: 80rpx;
		margin: 15rpx 0 0 64rpx;
	}

	.icon_1 {
		width: 48rpx;
		height: 48rpx;
	}

	.text_14 {
		width: 44rpx;
		height: 28rpx;
		overflow-wrap: break-word;
		color: rgba(51, 51, 51, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Semibold;
		font-weight: 600;
		text-align: center;
		white-space: nowrap;
		line-height: 28rpx;
		margin: 4rpx 0 0 2rpx;
	}

	.tab-bar-item_7 {
		width: 48rpx;
		height: 80rpx;
		margin: 15rpx 0 0 86rpx;
	}

	.icon_2 {
		width: 48rpx;
		height: 48rpx;
	}

	.text_15 {
		width: 44rpx;
		height: 28rpx;
		overflow-wrap: break-word;
		color: rgba(153, 153, 153, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 28rpx;
		margin: 4rpx 0 0 2rpx;
	}

	.tab-bar-item_8 {
		height: 68rpx;
		width: 87rpx;
		margin: 21rpx 0 0 85rpx;
	}

	.icon_3 {
		width: 87rpx;
		height: 68rpx;
	}

	.tab-bar-item_9 {
		width: 48rpx;
		height: 80rpx;
		margin: 15rpx 0 0 85rpx;
	}

	.icon_4 {
		width: 48rpx;
		height: 48rpx;
	}

	.text_16 {
		width: 44rpx;
		height: 28rpx;
		overflow-wrap: break-word;
		color: rgba(153, 153, 153, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Regular;
		font-weight: normal;
		text-align: center;
		white-space: nowrap;
		line-height: 28rpx;
		margin: 4rpx 0 0 2rpx;
	}

	.tab-bar-item_10 {
		width: 48rpx;
		height: 80rpx;
		margin: 15rpx 65rpx 0 86rpx;
	}

	.icon_5 {
		width: 48rpx;
		height: 48rpx;
	}

	.text_17 {
		width: 44rpx;
		height: 28rpx;
		overflow-wrap: break-word;
		color: rgba(51, 51, 51, 1);
		font-size: 22rpx;
		font-family: MiSans VF-Semibold;
		font-weight: 600;
		text-align: center;
		white-space: nowrap;
		line-height: 28rpx;
		margin: 4rpx 0 0 2rpx;
	}

	.textBottomBox {
		text-align: left;
		margin: 20rpx 24rpx;

		.notesBottom {
			display: flex;
			justify-content: space-between;
			margin-top: 15rpx;
			color: #8391aa;

			.notesBottomL {
				display: flex;
				align-items: center;

				img {
					border-radius: 50%;
					width: 35rpx;
					height: 35rpx;
					margin-right: 5rpx;
				}
			}

			.notesBottomR {
				display: flex;
				align-items: center;

				img {
					width: 35rpx;
					height: 35rpx;
					margin-right: 7rpx;
				}
			}
		}
	}

	.user-intro-section {
		margin-top: 20rpx;
		padding: 20rpx;
		background-color: transparent;
		width: 100%;
		box-sizing: border-box;
		min-height: 60rpx;
		height: auto;
	}

	.user-intro {
		font-size: 26rpx;
		color: #ffffff;
		line-height: 42rpx;
		word-break: break-word;
		white-space: pre-wrap;
		width: 70%;
		display: block;
		box-sizing: border-box;
		overflow-wrap: break-word;
		text-align: left;
		padding: 10rpx 0;
		margin: 0;
		min-height: 42rpx;
		height: auto;
	}

	/* 笔记内容框样式 */
	.list {
		column-count: 2;
		box-sizing: content-box;
		margin-top: 10px;
	}

	.pbl {
		width: 100%;
		break-inside: avoid;
		overflow: hidden;
		border-radius: 5px;
		margin-bottom: 20rpx;
		background-color: #fff;
		box-sizing: border-box;

		&:last-child {
			margin-bottom: 10rpx;
		}

		.image {
			width: 100%;
			border-radius: 5px;
			overflow: hidden;

			&>image {
				width: 100%;
				height: 100%;
			}
		}

		.title {
			font-size: 32rpx;
			margin-bottom: 6rpx;
			display: -webkit-box;
			text-overflow: ellipsis;
			overflow: hidden;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2; // 超出2行省略
			padding: 5px 10px;
			font-weight: bold;
			word-break: break-word;
		}

		.more {
			display: flex;
			justify-content: space-between;
			color: #9499aa;
			margin-bottom: 6rpx;
			font-size: 26rpx;
		}

		.action-buttons {
			display: flex;
			justify-content: space-between;
			padding: 5rpx 16rpx 16rpx;
			/* 按钮的上下内边距 */
			background-color: #fff;

			button {
				flex: 1;
				/* 按钮等宽 */
				margin: 0 8rpx;
				/* 按钮间的间距 */
				padding: 10rpx 0;
				/* 按钮内部间距 */
				font-size: 22rpx;
				border: none;
				border-radius: 8rpx;
				text-align: center;
				cursor: pointer;
				color: #fff;
				transition: opacity 0.2s;
			}

			/* 按钮点击效果 */
			button:active {
				opacity: 0.8;
			}
		}
	}

	/* 多行省略 */
	.multi-line-omit {
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	/* 单行省略 */
	.one-line-omit {
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>