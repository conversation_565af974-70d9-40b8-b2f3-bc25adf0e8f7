<template>
  <view class="page">
    <!-- 加载中 -->
    <loading v-if="loading"></loading>
    
    <block v-if="isload">
      <!-- 搜索栏 -->
      <view class="search-bar">
        <view class="search-input-wrap">
          <input 
            type="text"
            v-model="keywords"
            placeholder="搜索关键词"
            class="search-input"
            @confirm="search"
          />
          <text class="search-icon">🔍</text>
        </view>
      </view>

      <!-- 分类列表 -->
      <scroll-view scroll-x class="category-scroll" v-if="categories.length > 0">
        <view class="category-list">
          <view 
            class="category-item" 
            :class="{'active': currentCid === 0}"
            @tap="selectCategory(0)"
          >
            <text class="category-text">全部</text>
          </view>
          <view 
            class="category-item"
            :class="{'active': currentCid === item.id}"
            v-for="(item, index) in categories" 
            :key="index"
            @tap="selectCategory(item.id)"
          >
            <text class="category-text">{{item.name}}</text>
          </view>
        </view>
      </scroll-view>

      <!-- 列表内容 -->
      <view class="list-container">
        <view 
          class="list-item"
          v-for="(item, index) in list"
          :key="index"
          @tap="goto" 
          :data-url="'detail?id=' + item.id + '&type=' + type"
        >
          <view class="item-header">
            <text class="title">{{item.title}}</text>
            <text class="category">{{item.categoryname}}</text>
          </view>
          <view class="item-content">
            <text class="content">{{item.content}}</text>
          </view>
          <view class="item-footer">
            <view class="info">
              <text class="time">{{item.addtime}}</text>
              <text class="contact">{{item.contact_name}}</text>
            </view>
            <view class="price-info" v-if="type === 2">
              <text class="price">￥{{item.price || '面议'}}</text>
            </view>
            <view class="status" v-else>
              <text class="status-text">待报价</text>
            </view>
          </view>
        </view>

        <!-- 无数据提示 -->
        <view class="empty" v-if="list.length === 0">
          <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无数据</text>
        </view>
      </view>

      <!-- 底部发布按钮 -->
      <view class="footer">
        <view class="publish-btn" @tap="goto" data-url="fatie?type=qiugou" v-if="type === 1">
          <text class="btn-text">发布采购</text>
        </view>
        <view class="publish-btn" @tap="goto" data-url="fatie?type=gongying" v-else>
          <text class="btn-text">发布供应</text>
        </view>
      </view>
    </block>

    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      loading: true,
      type: 1, // 1采购 2供应
      keywords: '',
      currentCid: 0,
      page: 1,
      pagesize: 10,
      list: [],
      categories: [],
      hasMore: true,
      isload: false  // 添加isload状态
    }
  },
  onLoad(options) {
    console.log('页面加载参数:', options);
    // 设置类型
    if(options.type) {
      this.type = parseInt(options.type);
    }
    if(options.cid) {
      this.currentCid = parseInt(options.cid);
    }
    // 设置标题
    uni.setNavigationBarTitle({
      title: this.type === 1 ? '采购列表' : '供应列表'
    });
    
    this.init();
  },
  methods: {
    // 初始化
    init() {
      // 获取分类
      this.getCategories();
      // 获取列表
      this.getList();
    },
    
    // 获取分类列表
    getCategories() {
      console.log('开始获取分类');
      var that = this;
      app.get('ApiCaigou/category', {}, function(res) {
        console.log('分类数据:', res);
        if(res.status === 1) {
          that.categories = res.data || [];
        } else {
          app.error(res.msg || '获取分类失败');
        }
      }, function(err) {
        console.error('获取分类错误:', err);
        app.error('获取分类失败');
      });
    },
    
    // 获取列表数据
    getList(loadmore) {
      console.log('开始获取列表, 参数:', {
        type: this.type,
        page: this.page,
        pagesize: this.pagesize,
        cid: this.currentCid,
        keywords: this.keywords
      });
      
      var that = this;
      if(!loadmore) {
        that.page = 1;
        that.list = [];
        that.loading = true;
      }
      
      app.get('ApiCaigou/lists', {
        type: that.type,
        page: that.page,
        pagesize: that.pagesize,
        cid: that.currentCid || '',
        keywords: that.keywords
      }, function(res) {
        console.log('列表数据:', res);
        that.loading = false;
        that.isload = true;
        
        if(res.status === 1) {
          const data = res.data;
          if(loadmore) {
            that.list = that.list.concat(data.list || []);
          } else {
            that.list = data.list || [];
          }
          that.hasMore = (data.list || []).length >= that.pagesize;
        } else {
          app.error(res.msg || '获取列表失败');
        }
        // 停止下拉刷新
        uni.stopPullDownRefresh();
      }, function(err) {
        console.error('获取列表错误:', err);
        that.loading = false;
        that.isload = true;
        app.error('获取列表失败');
        uni.stopPullDownRefresh();
      });
    },
    
    // 选择分类
    selectCategory(cid) {
      console.log('选择分类:', cid);
      this.currentCid = cid;
      this.getList();
    },
    
    // 搜索
    search() {
      console.log('搜索关键词:', this.keywords);
      this.getList();
    },
    
    // 页面跳转
    goto(e) {
      const url = e.currentTarget.dataset.url;
      console.log('页面跳转:', url);
      if(url) {
        uni.navigateTo({
          url: url
        });
      }
    }
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    console.log('触发下拉刷新');
    this.getList();
  },
  
  // 触底加载
  onReachBottom() {
    console.log('触发触底加载');
    if(this.hasMore) {
      this.page++;
      this.getList(true);
    }
  }
}
</script>

<style>
.page {
  min-height: 100vh;
  background: #f6f7f9;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 搜索栏 */
.search-bar {
  padding: 24rpx;
  background: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-input-wrap {
  position: relative;
  height: 72rpx;
}

.search-input {
  width: 100%;
  height: 100%;
  background: #f5f6fa;
  border-radius: 36rpx;
  padding: 0 80rpx 0 32rpx;
  font-size: 28rpx;
  color: #2c3e50;
}

.search-icon {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #95a5a6;
}

/* 分类列表 */
.category-scroll {
  background: #ffffff;
  padding: 0 24rpx;
  white-space: nowrap;
  border-bottom: 1rpx solid #edf0f3;
}

.category-list {
  display: inline-flex;
  padding: 16rpx 0;
}

.category-item {
  padding: 12rpx 32rpx;
  margin-right: 16rpx;
  border-radius: 32rpx;
  background: #f5f6fa;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #ff4757;
}

.category-text {
  font-size: 26rpx;
  color: #2c3e50;
  transition: all 0.3s ease;
}

.category-item.active .category-text {
  color: #ffffff;
}

/* 列表样式 */
.list-container {
  padding: 24rpx;
}

.list-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.title {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 600;
  flex: 1;
  margin-right: 16rpx;
}

.category {
  font-size: 24rpx;
  color: #ff4757;
  background: rgba(255,71,87,0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.item-content {
  margin-bottom: 16rpx;
}

.content {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time {
  font-size: 24rpx;
  color: #95a5a6;
}

.contact {
  font-size: 24rpx;
  color: #7f8c8d;
}

.price-info {
  display: flex;
  align-items: center;
}

.price {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: 600;
}

.status {
  background: #ff4757;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
}

.status-text {
  font-size: 24rpx;
  color: #ffffff;
}

/* 空状态 */
.empty {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #95a5a6;
}

/* 底部按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 20rpx rgba(0,0,0,0.05);
}

.publish-btn {
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.publish-btn:active {
  opacity: 0.8;
}

.btn-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}
</style>