# 单数奖励系统说明文档

## 1. 系统概述

### 1.1 功能简介
单数奖励系统是一个基于用户订单数量的奖励机制，支持多种奖励模式，可以灵活配置不同等级会员的奖励规则。系统支持自动计算奖励和手动领取奖励两种方式，为用户提供清晰直观的奖励体验。

### 1.2 适用场景
- 会员等级奖励
- 消费激励计划
- 商品促销活动
- 用户留存计划
- 购物返利活动

### 1.3 系统特点
- 多种奖励模式
- 灵活的规则配置
- 实时奖励计算
- 完整的记录追踪
- 手动领取奖励
- 详细的奖励计算说明

### 1.4 奖励统计
系统提供完整的奖励统计功能，包括以下维度：

1. **总奖励金额**
   - 定义：所有已生成的奖励记录总金额
   - 统计SQL：
   ```sql
   SELECT SUM(reward_amount) as total_reward 
   FROM ddwx_danshujiang_record 
   WHERE aid = {aid} AND mid = {mid}
   ```

2. **待领取奖励**
   - 定义：符合奖励条件但用户尚未领取的订单奖励
   - 计算方式：查询符合条件的订单，根据规则计算可获得的奖励金额
   - 统计SQL：
   ```sql
   SELECT o.*, m.levelid
   FROM ddwx_shop_order o
   LEFT JOIN ddwx_member m ON m.id = o.mid
   WHERE o.aid = {aid} 
   AND o.mid = {mid}
   AND o.status = 1
   AND o.id NOT IN (
       SELECT order_id 
       FROM ddwx_danshujiang_record 
       WHERE aid = {aid}
   )
   ```

3. **待发放奖励**
   - 定义：用户已领取但平台尚未发放的奖励
   - 统计SQL：
   ```sql
   SELECT SUM(reward_amount) as pending_amount
   FROM ddwx_danshujiang_record
   WHERE aid = {aid}
   AND mid = {mid}
   AND status = 0
   ```

4. **已发放奖励**
   - 定义：平台已发放到用户账户的奖励金额
   - 统计SQL：
   ```sql
   SELECT SUM(reward_amount) as issued_amount
   FROM ddwx_danshujiang_record
   WHERE aid = {aid}
   AND mid = {mid}
   AND status = 1
   ```

5. **时间维度统计**
   - 今日统计
     ```sql
     SELECT 
         SUM(reward_amount) as total,
         SUM(CASE WHEN status = 0 THEN reward_amount ELSE 0 END) as pending,
         SUM(CASE WHEN status = 1 THEN reward_amount ELSE 0 END) as issued
     FROM ddwx_danshujiang_record
     WHERE aid = {aid}
     AND mid = {mid}
     AND create_time BETWEEN {today_start} AND {today_end}
     ```
   - 本月统计
     ```sql
     SELECT 
         SUM(reward_amount) as total,
         SUM(CASE WHEN status = 0 THEN reward_amount ELSE 0 END) as pending,
         SUM(CASE WHEN status = 1 THEN reward_amount ELSE 0 END) as issued
     FROM ddwx_danshujiang_record
     WHERE aid = {aid}
     AND mid = {mid}
     AND create_time BETWEEN {month_start} AND {month_end}
     ```

6. **排行榜统计**
   - 日榜/周榜/月榜
   ```sql
   SELECT 
       r.mid,
       m.nickname,
       m.avatar,
       COUNT(*) as order_count,
       SUM(r.reward_amount) as total_reward
   FROM ddwx_danshujiang_record r
   LEFT JOIN ddwx_member m ON m.id = r.mid
   WHERE r.aid = {aid}
   AND r.status = 1
   AND r.create_time BETWEEN {start_time} AND {end_time}
   GROUP BY r.mid
   ORDER BY total_reward DESC
   LIMIT 10
   ```

### 1.5 数据分析
系统提供以下数据分析维度：

1. **奖励转化率**
   - 订单转化率 = 已领取奖励订单数 / 符合条件订单总数
   - 发放转化率 = 已发放奖励金额 / 已领取奖励金额

2. **用户活跃度**
   - 奖励领取频率
   - 奖励使用情况
   - 用户参与度分析

3. **奖励效果分析**
   - 不同等级会员的奖励情况
   - 不同商品的奖励分布
   - 不同时间段的奖励趋势

4. **成本收益分析**
   - 奖励支出统计
   - 带动销售额分析
   - ROI计算

### 1.6 接口说明

1. **获取奖励统计**
```php
URL: /api/danshujiang/getRewardStats
Method: GET
Params:
    - date_start: 开始日期(可选)
    - date_end: 结束日期(可选)
Response:
{
    "status": 1,
    "data": {
        "total_reward": 1000.00,    // 总奖励金额
        "pending_claim": 200.00,     // 待领取奖励
        "pending_issue": 300.00,     // 待发放奖励
        "issued_reward": 500.00,     // 已发放奖励
        "today_stats": {
            "total": 100.00,         // 今日总奖励
            "pending_claim": 20.00,   // 今日待领取
            "pending_issue": 30.00,   // 今日待发放
            "issued": 50.00          // 今日已发放
        },
        "month_stats": {
            "total": 1000.00,        // 本月总奖励
            "pending_claim": 200.00,  // 本月待领取
            "pending_issue": 300.00,  // 本月待发放
            "issued": 500.00         // 本月已发放
        }
    }
}
```

2. **获取奖励排行**
```php
URL: apidanshujiang/getRewardRanking
Method: GET
Params:
    - type: 排行类型(day/week/month/total)
    - limit: 返回数量(默认10)
Response:
{
    "status": 1,
    "data": {
        "list": [{
            "mid": 1,
            "nickname": "用户昵称",
            "avatar": "头像URL",
            "order_count": 10,
            "reward_amount": 1000.00
        }],
        "my_rank": {
            "rank": 5,
            "reward_amount": 500.00,
            "order_count": 5
        }
    }
}
```

## 2. 奖励模式说明

### 2.1 普通模式
- **特点**：每个单数独立计算奖励
- **计算方式**：单笔订单金额 × 当前单数对应的奖励比例
- **适用场景**：固定奖励计划
- **领取方式**：用户可以查看每单具体奖励金额并手动领取
- **示例**：
  ```
  第1单：30%奖励
  第2单：40%奖励
  第3单：50%奖励
  ```

### 2.2 阶梯模式
- **特点**：根据累计单数确定奖励等级
- **计算方式**：所有订单金额 × 当前阶段的奖励比例
- **适用场景**：长期激励计划
- **领取方式**：用户达到新阶段时可手动领取新阶段奖励
- **示例**：
  ```
  1-3单：30%奖励
  4-6单：50%奖励
  7单以上：100%奖励
  ```

### 2.3 循环模式
- **特点**：按固定周期循环计算奖励
- **计算方式**：当前订单金额 × 循环位置对应的奖励比例
- **适用场景**：周期性促销活动
- **领取方式**：每个循环位置的订单完成后可手动领取对应奖励
- **示例**：
  ```
  3单为一个循环：
  第1单：50%奖励
  第2单：50%奖励
  第3单：100%奖励
  （之后重新开始）
  ```

## 3. 接口说明

### 3.1 获取用户奖励信息
- **接口路径**：`apidanshujiang/getUserRewardInfo`
- **请求方式**：GET
- **请求参数**：无
- **说明**：获取用户当前的奖励信息，包括详细的奖励计算过程和每单奖励状态
- **响应示例**：
  ```json
  {
    "status": 1,
    "data": {
      "current_order_count": 5,
      "reward_mode": 1,
      "reward_mode_name": "普通模式",
      "current_stage": {
        "order_num": 5,
        "reward_rate": 30
      },
      "total_reward": 1000.00,
      "rewarded_amount": 800.00,
      "pending_reward": 200.00,
      "reward_details": [
        {
          "order_num": 1,
          "reward_rate": 30,
          "order_amount": 100.00,
          "reward_amount": 30.00,
          "order_id": 1001,
          "calculation": "订单金额 100.00 × 奖励比例 30% = 30.00",
          "is_claimed": true,
          "is_paid": true
        }
      ]
    }
  }
  ```

### 3.2 获取奖励记录
- **接口路径**：`apidanshujiang/getRewardRecords`
- **请求方式**：GET
- **请求参数**：
  ```json
  {
    "page": 1,          // 页码
    "limit": 20,        // 每页条数
    "status": "",       // 状态筛选：0待发放，1已发放，2已拒绝
    "date_start": "",   // 开始日期，格式：YYYY-MM-DD
    "date_end": "",     // 结束日期，格式：YYYY-MM-DD
    "order_num": ""     // 订单号筛选
  }
  ```
- **响应示例**：
  ```json
  {
    "status": 1,
    "data": {
      "list": [
        {
          "order_id": 1001,
          "order_num": "202403150001",
          "order_amount": 100.00,
          "reward_amount": 30.00,
          "reward_rate": 30.00,
          "status": 1,
          "create_time": 1678876800,
          "create_time_text": "2024-03-15 12:30:45",
          "status_text": "已发放",
          "reject_reason": ""
        }
      ],
      "total": 100,
      "current_page": 1,
      "per_page": 10,
      "total_pages": 10
    }
  }
  ```

### 3.3 手动领取奖励
- **接口路径**：`apidanshujiang/claimReward`
- **请求方式**：POST
- **请求参数**：
  ```json
  {
    "order_id": 1001    // 要领取奖励的订单ID
  }
  ```
- **响应示例**：
  ```json
  {
    "status": 1,
    "msg": "奖励领取成功，等待系统发放",
    "data": {
      "order_amount": 100.00,
      "reward_amount": 30.00,
      "reward_rate": 30,
      "calculation": "订单金额 100.00 × 奖励比例 30% = 30.00"
    }
  }
  ```

### 3.4 获取奖励统计
- **接口路径**：`apidanshujiang/getRewardStats`
- **请求方式**：GET
- **请求参数**：无
- **响应示例**：
  ```json
  {
    "status": 1,
    "data": {
      "total_reward": 10000.00,         // 总奖励金额（所有类型奖励的总和）
      "issued_reward": 5000.00,         // 已发放奖励（已经发放到账的奖励）
      "pending_issue": 3000.00,         // 待发放奖励（已领取但还未发放的奖励）
      "pending_claim": 2000.00          // 待领取奖励（符合条件但用户还未领取的奖励）
    }
  }
  ```
- **说明**：
  1. `total_reward`：所有类型奖励的总和
  2. `issued_reward`：已经发放到用户账户的奖励金额
  3. `pending_issue`：用户已领取但平台还未发放的奖励金额
  4. `pending_claim`：用户符合条件但还未领取的奖励金额
  5. 所有金额均保留2位小数

### 3.5 获取奖励规则
- **接口路径**：`apidanshujiang/getRewardRules`
- **请求方式**：GET
- **请求参数**：无
- **响应示例**：
  ```json
  {
    "status": 1,
    "data": {
      "reward_mode": 1,                // 奖励模式：1普通，2阶梯，3循环
      "reward_mode_name": "普通模式",
      "cycle_num": 3,                  // 循环模式时的循环单数
      "rules": [
        {
          "order_num": 1,
          "reward_rate": 30
        },
        {
          "order_num": 2,
          "reward_rate": 40
        },
        {
          "order_num": 3,
          "reward_rate": 50
        }
      ],
      "product_list": [               // 参与活动的商品
        {
          "id": 1,
          "name": "商品1"
        }
      ],
      "level_list": [                 // 参与活动的会员等级
        {
          "id": 1,
          "name": "普通会员"
        }
      ]
    }
  }
  ```

### 3.6 获取用户奖励排行
- **接口路径**：`/api/danshujiang/getRewardRanking`
- **请求方式**：GET
- **请求参数**：
  ```json
  {
    "type": "week",     // 排行类型：day今日，week本周，month本月，total总排行
    "limit": 10         // 返回数量
  }
  ```
- **响应示例**：
  ```json
  {
    "status": 1,
    "data": {
      "list": [
        {
          "mid": 1001,
          "nickname": "用户1",
          "avatar": "http://xxx.com/avatar.jpg",
          "reward_amount": 1000.00,    // 奖励金额
          "order_count": 10            // 订单数量
        }
      ],
      "my_rank": {                     // 我的排名信息
        "rank": 5,
        "reward_amount": 500.00,
        "order_count": 5
      }
    }
  }
  ```

## 4. 前端开发指南

### 4.1 页面结构建议
```
|- 奖励概览
   |- 当前单数
   |- 累计奖励
   |- 待发放奖励
|- 奖励规则
   |- 模式说明
   |- 规则列表
|- 奖励详情
   |- 单笔奖励列表
   |- 奖励计算明细
   |- 领取按钮
|- 奖励记录
   |- 记录列表
   |- 分页控件
```

### 4.2 数据展示建议
1. **奖励进度**
   - 使用进度条展示当前单数位置
   - 清晰显示下一个奖励节点
   - 突出显示待发放奖励金额

2. **规则展示**
   - 普通模式：列表形式
   - 阶梯模式：阶梯图表
   - 循环模式：环形进度图

3. **奖励详情展示**
   - 表格或卡片形式展示每单奖励
   - 清晰标记已领取和未领取状态
   - 突出显示可领取的奖励
   - 显示详细的奖励计算过程

4. **记录列表**
   - 时间倒序排列
   - 分页加载
   - 状态清晰标识

### 4.3 交互优化建议
1. **加载优化**
   - 使用骨架屏
   - 预加载数据
   - 缓存历史记录

2. **更新机制**
   - 订单完成后自动更新
   - 定时轮询最新数据
   - 下拉刷新功能

3. **动效建议**
   - 数值变化动画
   - 进度条平滑过渡
   - 状态切换渐变
   - 领取成功动画反馈

### 4.4 手动领取功能实现
1. **领取按钮展示**
   - 对于未领取的奖励显示"领取"按钮
   - 对于已领取待发放的显示"待发放"状态
   - 对于已发放的显示"已发放"状态
   - 对于未达到的单数显示"未达到"状态

2. **领取流程**
   - 用户点击"领取"按钮
   - 调用`claimReward`接口
   - 展示领取成功动画
   - 更新当前页面状态
   - 实时显示最新奖励信息

3. **状态管理**
   - 使用不同颜色标识不同状态
   - 可领取：绿色突出显示
   - 待发放：橙色提示
   - 已发放：灰色标记
   - 未达到：浅灰色禁用状态

## 5. 数据结构

### 5.1 配置表结构
```sql
CREATE TABLE `ddwx_danshujiang_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT 0,
  `