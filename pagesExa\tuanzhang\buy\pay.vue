<template>
<view class="container">
	<block v-if="isload">
		<view class="top">
			<view class="f1">需支付金额</view>
			<view class="f2" v-if="payorder.score==0"><text class="t1">￥</text><text class="t2">{{payorder.money}}</text></view>
			<view class="f2" v-else-if="payorder.money>0 && payorder.score>0"><text class="t1">￥</text><text class="t2">{{payorder.money}}</text><text style="font-size:28rpx"> + {{payorder.score}}{{t('积分')}}</text></view>
			<view class="f2" v-else><text class="t3">{{payorder.score}}{{t('积分')}}</text></view>
			<view class="f3" @tap="goto" :data-url="detailurl" v-if="detailurl!=''">订单详情<text class="iconfont iconjiantou"></text></view>
		</view>
		<view class="paytype" style="margin-bottom: 0px; position: relative;overflow: auto;">
			<view v-if="is_shop_pay==true" style="z-index: 3;width: 100%;height: 200px;background-color: #ccc; position: absolute;opacity: 0.5;"></view>
			<view class="f1">选择支付方式：</view>
			<block v-if="payorder.money==0 && payorder.score>0">
				<view class="f2">
					<view class="item" v-if="moneypay==1" @tap.stop="changeradio" data-typeid="1">
						<view class="t1 flex">
							<image class="img" src="/static/img/pay-money.png"/>
							<view class="flex-col"><text>{{t('积分')}}支付</text><text style="font-size:22rpx;font-weight:normal">剩余{{t('积分')}}<text style="color:#FC5729">{{userinfo.score}}</text></text></view>
						</view>
						<view class="radio" :style="typeid=='1' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
				</view>
			</block>
			<block v-else>
				<view class="f2">
					<view class="item"  @tap.stop="changeradio" data-typeid="29" v-if="sandepay==1">
						<view class="t1"><image class="img" src="/static/img/pay-money.png"/>杉德支付</view>
						<view class="radio" :style="typeid=='29' ? 'background:'+t('color1')+';border:0' : ''">
							<image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<view class="item" v-if="wxpay==1 && (wxpay_type==0 || wxpay_type==1 || wxpay_type==2 || wxpay_type==3||wxpay_type==4)" @tap.stop="changeradio" data-typeid="2">
						<view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"/>微信支付</view>
						<view class="radio" :style="typeid=='2' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<view class="item" v-if="wxpay==1 && (wxpay_type==5 )" @tap.stop="changeradio" data-typeid="2">
						<view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"/>嘉联微信支付</view>
						<view class="radio" :style="typeid=='2' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<view class="item" v-if="wxpay==1 && wxpay_type==22" @tap.stop="changeradio" data-typeid="22">
						<view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"/>微信支付</view>
						<view class="radio" :style="typeid=='22' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					
					<view class="item" v-if="alipay==2" @tap.stop="changeradio" data-typeid="23">
						<view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝支付</view>
						<view class="radio" :style="typeid=='23' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
                    
					

					<view class="item" v-if="alipay==1" @tap.stop="changeradio" data-typeid="3">
						<view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝支付</view>
						<view class="radio" :style="typeid=='3' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
                    
					<block v-if="more_alipay==1">
						<view class="item" v-if="alipay2==1" @tap.stop="changeradio" data-typeid="31">
							<view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝支付2</view>
							<view class="radio" :style="typeid=='31' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
						</view>
						<view class="item" v-if="alipay3==1" @tap.stop="changeradio" data-typeid="32">
							<view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝支付3</view>
							<view class="radio" :style="typeid=='32' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
						</view>
					</block>
					
					<view class="item" v-if="paypal==1" @tap.stop="changeradio" data-typeid="51">
						<view class="t1"><image class="img" src="/static/img/paypal.png"/>PayPal支付</view>
						<view class="radio" :style="typeid=='51' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
                    
					<view class="item" v-if="baidupay==1" @tap.stop="changeradio" data-typeid="11">
						<view class="t1"><image class="img" src="/static/img/pay-money.png"/>在线支付</view>
						<view class="radio" :style="typeid=='11' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<view class="item" v-if="toutiaopay==1" @tap.stop="changeradio" data-typeid="12">
						<view class="t1"><image class="img" src="/static/img/pay-money.png"/>在线支付</view>
						<view class="radio" :style="typeid=='12' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<view class="item" v-if="moneypay==1" @tap.stop="changeradio" data-typeid="1">
						<view class="t1 flex">
							<image class="img" src="/static/img/pay-money.png"/>
							<view class="flex-col"><text>{{t('余额')}}支付</text><text style="font-size:22rpx;font-weight:normal">可用余额<text style="color:#FC5729">￥{{userinfo.money}}</text></text></view>
						</view>
						<view class="radio" :style="typeid=='1' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<view class="item" v-if="heipay==1" @tap.stop="changeradio" data-typeid="88">
						<view class="t1 flex">
							<image class="img" src="/static/img/pay-money.png"/>
							<view class="flex-col"><text>{{t('购物积分')}}支付</text><text style="font-size:22rpx;font-weight:normal">可用购物积分<text style="color:#FC5729">￥{{userinfo.heiscore}}</text></text></view>
						</view>
						<view class="radio" :style="typeid=='88' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<view class="item" v-if="yuanbaopay==1" @tap.stop="changeradio" data-typeid="yuanbao" style="height: 130rpx;">
						<view class="t1 flex">
							<image class="img" src="/static/img/pay-money.png"/>
							<view class="flex-col">
											<text>{{t('元宝')}}支付</text>
											<text style="font-size:22rpx;font-weight:normal">
													可用{{t('元宝')}}
													<text style="color:#FC5729">{{userinfo.yuanbao}}</text>
											</text>
											<text style="font-size:22rpx;font-weight:normal">
													需支付
													<text style="color:#FC5729">{{yuanbao_msg}}</text>
											</text>
									</view>
						</view>
						<view class="radio" :style="typeid=='yuanbao' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
				</view>
			</block>
		</view>
		<view class="paytype" style="margin-bottom: 0px;" v-if="s_base_info.length!=0&&charge_status!=0">
			<view class="f1">使用商店余额支付：<switch :checked='is_shop_pay==true' @change="switch_shop_pay"></switch></view>
		</view>
		
		<view class="paytype" v-if="is_shop_pay==true&&s_base_info.length!=0">
			<view class="f1">商店支付：</view>
			<block >
				<view class="f2" >
					<view class="item"  data-typeid="1" v-for="(item,index) in s_base_info">
						<view class="t1 flex">
							<image class="img" src="/static/img/pay-money.png"/>
							<view class="flex-col">
								<text>{{item.name}}</text>
								<text style="font-size:22rpx;font-weight:normal">剩余{{t('余额')}}<text style="color:#FC5729">{{item.balance}}元</text></text>
								<text style="font-size:22rpx;font-weight:normal">剩余{{t('要支付')}}<text style="color:#FC5729">{{item.pay_total}}元</text></text>
							</view>
						</view>
						<view class="radio" :style="true ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
				</view>
			</block>
		</view>
    <block>
      <view v-if="typeid == 29"  v-html="san_pay.form"></view>
<!--      //杉德支付表单支付-->

    </block>
    <button class="btn" @tap="topay" :style="{background:t('color1')}" v-if="typeid != '0' && typeid != 29">立即支付</button>
		<button class="btn" @tap="topay2" v-if="cancod==1" style="background:rgba(126,113,246,0.5);">{{codtxt}}</button>
		<button class="btn" @tap="topayTransfer" v-if="pay_transfer==1" :style="{background:t('color2')}" :data-text="t('转账汇款')">{{t('转账汇款')}}</button>
		<button class="btn" @tap="topayMonth" v-if="pay_month==1" :style="{background:t('color1')}">{{pay_month_txt}}</button>
		<block v-if="daifu">
			<button class="btn daifu-btn" @tap="todaifu" v-if="getplatform() == 'h5' || getplatform() == 'mp' || getplatform() == 'app'">
				{{daifu_txt}}
			</button>
			<button class="btn daifu-btn" open-type="share" v-else>
				{{daifu_txt}}
			</button>
		</block>
		<uni-popup id="dialogInput" ref="dialogInput" type="dialog">
			<uni-popup-dialog mode="input" title="支付密码" value="" placeholder="请输入支付密码" @confirm="getpwd"></uni-popup-dialog>
		</uni-popup>

		<block v-if="give_coupon_show">
			<view class="give-coupon flex-x-center flex-y-center">
				<view class='coupon-block'>
					<image :src="pre_url+'/static/img/coupon-top.png'" style="width:630rpx;height:330rpx;"></image>
					<view @tap="give_coupon_close" :data-url="give_coupon_close_url" class="coupon-del flex-x-center flex-y-center">
						<image src="/static/img/coupon-del.png"></image>
					</view>
					<view class="flex-x-center">
						<view class="coupon-info">
							<view class="flex-x-center coupon-get">获得{{give_coupon_num}}张{{t('优惠券')}}</view>
							<view style="background:#f5f5f5;padding:10rpx 0">
							<block v-for="(item,index) in give_coupon_list" :key="item.id">
								<block v-if="index < 3">
									<view class="coupon-coupon">
										<view :class="item.type==1?'pt_img1':'pt_img2'"></view>
										<view class="pt_left" :class="item.type==1?'':'bg2'">
											<view class="f1" v-if="item.type==1"><text class="t0">￥</text><text class="t1">{{item.money}}</text></view>
											<view class="f1" v-if="item.type==2">礼品券</view>
											<view class="f1" v-if="item.type==3"><text class="t1">{{item.limit_count}}</text><text class="t2">次</text></view>
											<view class="f1" v-if="item.type==4">抵运费</view>
											<view class="f2" v-if="item.type==1 || item.type==4">
												<text v-if="item.minprice>0">满{{item.minprice}}元可用</text>
												<text v-else>无门槛</text>
											</view>
										</view>
										<view class="pt_right">
											<view class="f1">
												<view class="t1">{{item.name}}</view>
												<view class="t2" v-if="item.type==1">代金券</view>
												<view class="t2" v-if="item.type==2">礼品券</view>
												<view class="t2" v-if="item.type==3">计次券</view>
												<view class="t2" v-if="item.type==4">运费抵扣券</view>
												<!-- <view class="t4" v-if="item.bid>0">适用商家：{{item.bname}}</view> -->
												<!-- <view class="t3">有效期至 {{item.yxqdate}}</view> -->
											</view>
										</view>
										<view class="coupon_num" v-if="item.givenum > 1">×{{item.givenum}}</view>
									</view>
								</block>
							</block>
							</view>
							<view @tap="goto" data-url="/pages/coupon/mycoupon" class="flex-x-center coupon-btn">前往查看</view>
						</view>
					</view>
				</view>
			</view>
		</block>
		<uni-popup id="dialogOpenWeapp" ref="dialogOpenWeapp" type="dialog" :maskClick="false">
			<view style="background:#fff;padding:50rpx;position:relative;border-radius:20rpx">
				<view style="height:80px;line-height:80px;width:200px;margin:0 auto;font-size: 18px;text-align:center;font-weight:bold;text-align:center;color:#333">恭喜您支付成功</view>
				<!-- #ifdef H5 -->
				<wx-open-launch-weapp :username="payorder.payafter_username" :path="payorder.payafter_path">
					<script type="text/wxtag-template">
						<div style="background:#FD4A46;height:50px;line-height: 50px;width:200px;margin:0 auto;border-radius:5px;margin-top:15px;color: #fff;font-size: 15px;font-weight:bold;text-align:center">{{payorder.payafterbtntext}}</div>
					</script>
				</wx-open-launch-weapp>
				<!-- #endif -->
				<view style="height:50px;line-height: 50px;width:200px;margin:0 auto;border-radius:5px;color:#66f;font-size: 14px;text-align:center" @tap="goto" :data-url="detailurl">查看订单详情</view>
			</view>
		</uni-popup>

		
		<uni-popup id="dialogPayconfirm" ref="dialogPayconfirm" type="dialog" :maskClick="false">
			<uni-popup-dialog type="info" title="支付确认" content="是否已完成支付" @confirm="PayconfirmFun"></uni-popup-dialog>
		</uni-popup>
        
        <!-- 元宝s -->
        <view v-if="yuanbaopay==1 && open_pay" style="width: 100%;height: 100%;position: fixed;z-index: 10;background-color: #000;opacity: 0.45;top: 0;"></view>
        <view v-if="yuanbaopay==1 && open_pay" style="width: 90%;position: fixed;z-index: 11;left: 5%;top:25%;background-color:#fff ;">
            <view class="paytype">
                <view class="f2">
                    <view class="item" v-if="wxpay==1 && (wxpay_type==0 || wxpay_type==1 || wxpay_type==2 || wxpay_type==3)" @tap.stop="changeradio" data-typeid="2">
                        <view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"/>微信支付</view>
                        <view class="radio" :style="typeid=='2' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                    </view>
                    <view class="item" v-if="wxpay==1 && wxpay_type==22" @tap.stop="changeradio" data-typeid="22">
                        <view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"/>微信支付</view>
                        <view class="radio" :style="typeid=='22' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                    </view>
					
                    
                    <view class="item" v-if="alipay==2" @tap.stop="changeradio" data-typeid="23">
                        <view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝支付</view>
                        <view class="radio" :style="typeid=='23' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                    </view>
                    <view class="item" v-if="alipay==1" @tap.stop="changeradio" data-typeid="3">
                        <view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝支付</view>
                        <view class="radio" :style="typeid=='3' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                    </view>
                    
                    <block v-if="more_alipay==1">
                        <view class="item" v-if="alipay2==1" @tap.stop="changeradio" data-typeid="31">
                            <view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝支付2</view>
                            <view class="radio" :style="typeid=='31' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                        </view>
                        <view class="item" v-if="alipay3==1" @tap.stop="changeradio" data-typeid="32">
                            <view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"/>支付宝支付3</view>
                            <view class="radio" :style="typeid=='32' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                        </view>
                    </block>
                    
                    <view class="item" v-if="baidupay==1" @tap.stop="changeradio" data-typeid="11">
                        <view class="t1"><image class="img" src="/static/img/pay-money.png"/>在线支付</view>
                        <view class="radio" :style="typeid=='11' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                    </view>
                    <view class="item" v-if="toutiaopay==1" @tap.stop="changeradio" data-typeid="12">
                        <view class="t1"><image class="img" src="/static/img/pay-money.png"/>在线支付</view>
                        <view class="radio" :style="typeid=='12' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                    </view>
                    <view class="item" v-if="moneypay==1" @tap.stop="changeradio" data-typeid="1">
                        <view class="t1 flex">
                            <image class="img" src="/static/img/pay-money.png"/>
                            <view class="flex-col"><text>{{t('余额')}}支付555</text><text style="font-size:22rpx;font-weight:normal">可用余额<text style="color:#FC5729">￥{{userinfo.money}}</text></text></view>
                        </view>
                        <view class="radio" :style="typeid=='1' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
                    </view>
					<!--  -->
					<view class="item" v-if="heipay==1" @tap.stop="changeradio" data-typeid="1">
					    <view class="t1 flex">
					        <image class="img" src="/static/img/pay-money.png"/>
					        <view class="flex-col"><text>{{t('抵扣积分')}}支付</text><text style="font-size:22rpx;font-weight:normal">可用抵扣<text style="color:#FC5729">￥{{userinfo.heiscore}}</text></text></view>
					    </view>
					    <view class="radio" :style="typeid=='1' ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
					<!-- heipay -->
                </view>
            </view>
            <view style="overflow: hidden;width: 100%;">
                <view style="width: 300rpx;float: left;">
                    <view>
                        <button class="btn" @tap="close_pay" style="margin-bottom: 20rpx;background-color: #999;">取消</button>
                    </view>
                </view>
                <view style="width: 300rpx;float: right;">
                    <view>
                        <button class="btn" @tap="topay" :style="{background:t('color1')}" v-if="typeid != '0'" style="margin-bottom: 20rpx;">确定</button>
                    </view>
                </view>
            </view>
        </view>
        <!-- 元宝e -->
	</block>
    <view @tap="closeInvite" v-if="invite_status && invite_free" style="width:100%;height: 100%;background-color: #000;position: fixed;opacity: 0.5;z-index: 99;top:0"></view>
    <view v-if="invite_status && invite_free" style="width: 700rpx;margin: 0 auto;position: fixed;top:10%;left: 25rpx;z-index: 100;">
        <view @tap="gotoInvite" style="background-color: #fff;border-radius: 20rpx;overflow: hidden;width: 100%;min-height: 700rpx;">
            <image :src="invite_free.pic" mode="widthFix" style="width: 100%;height: auto;"></image>
        </view>
        <view @tap="closeInvite" v-if="invite_status && invite_free" style="width: 80rpx;height: 80rpx;line-height: 80rpx;text-align: center;font-size: 30rpx;background-color: #fff;margin: 0 auto;border-radius: 50%;margin-top: 20rpx;">
            X
        </view>
    </view>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
            isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			
			detailurl:'',
			tourl:'',
			typeid:'0',
			sandepay:0,
			wxpay:0,
			wxpay_type:0,
			alipay:0,
			baidupay:0,
			bid:0,
			toutiaopay:0,
			moneypay:0,
			heipay:0,
			cancod:0,
			daifu:0,
			daifu_txt:'好友代付',
			pay_month:0,
			pay_transfer:0,
			codtxt:'',
			pay_month_txt:'',
			give_coupon_list:[],
			give_coupon_num:0,
			userinfo:[],
			paypwd: '',
			hiddenmodalput: true,
			payorder: {},
			san_pay: {},
			tmplids: [],
			give_coupon_show: false,
			give_coupon_close_url: "",
			more_alipay : 0,
			alipay2 : 0,
			alipay3 : 0,
			paypal:0,

			//元宝支付
			yuanbao_money:0,//现金
			total_yuanbao:0,//总元宝
			yuanbao_msg:'',//元宝文字描述
			yuanbaopay:0,//是否开启元宝支付
			open_pay:false,//打开支付选项
			pay_type:'',//支付类型（新增）
			s_base_info:[],
			
			invite_free:'',
			invite_status:false,
			free_tmplids:'',
			sharepic:app.globalData.initdata.logo,
			
			is_shop_pay:false,
			charge_status:0
    };
  },
	onShareAppMessage: function () {
		var that = this;
		var title = '您有一份好友代付待查收，请尽快处理！';
		var sharepic = that.sharepic;
		var sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/pay/daifu?scene=id_'+that.payorder.id;
		var sharedata = this._sharewx({title:title,tolink:sharelink,pic:sharepic});
		console.log('pay share data')
		console.log(sharedata)
		return sharedata;
	},
	onShareTimeline:function(){
		var that = this;
		var title = '您有一份好友代付待查收，请尽快处理1！';
		var sharepic = that.sharepic;
		var sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/pay/daifu?scene=id_'+that.payorder.id;
		var sharewxdata = this._sharewx({title:title,tolink:sharelink,pic:sharepic});
		var query = (sharewxdata.path).split('?')[1];
		var link = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+(sharewxdata.path).split('?')[0];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query,
			link:link
		}
	},

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		console.log(this.opt);
		if(this.opt.tourl) this.tourl = decodeURIComponent(this.opt.tourl);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
	    switch_shop_pay:function()
		{
			this.is_shop_pay = !this.is_shop_pay;
		},
		getdata: function () {
			var that = this;
			that.loading = true;
			var thisurl = '';
			if(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){
				thisurl = location.href;
			}
			app.post('ApiPay/pay', {orderid: that.opt.id,thisurl:thisurl,tourl:that.tourl,scene:app.globalData.scene}, function (res) {

				that.loading = false;
				if (res.status == 0) {
					app.error(res.msg);

          if(res.msg == '未实名认证！')
          {
            setTimeout(function () {
              app.goto(res.url);
            }, 2000)
          }

					return;
				}
                
				that.san_pay = res.san_pay;
				that.sandepay = res.sandepay;
				that.wxpay = res.wxpay;
				that.wxpay_type = res.wxpay_type;
				that.alipay = res.alipay;
				that.baidupay = res.baidupay;
				that.toutiaopay = res.toutiaopay;
				that.cancod = res.cancod;
				that.codtxt = res.codtxt;
				that.daifu = res.daifu;
				that.daifu_txt = res.daifu_txt;
				that.pay_money = res.pay_money;
				that.pay_money_txt = res.pay_money_txt;
				that.moneypay = res.moneypay;
				that.heipay = res.heipay;
				that.pay_transfer = res.pay_transfer;
				that.pay_transfer_info = res.pay_transfer_info;
				that.pay_month = res.pay_month;
				that.pay_month_txt = res.pay_month_txt;
				that.payorder = res.payorder;
				that.bid = that.payorder['bid'];
				that.userinfo = res.userinfo;
				that.tmplids = res.tmplids;
				that.s_base_info = res.s_base_info;
				that.charge_status = res.charge_status;
				
				console.log(that.s_base_info);
				if(that.s_base_info.length!=0&&that.charge_status!=0)
				{
					 that.typeid = 2;
					 that.is_shop_pay = true;
				}
				that.give_coupon_list = res.give_coupon_list;
				if(that.give_coupon_list){
					for(var i in that.give_coupon_list){
						that.give_coupon_num += that.give_coupon_list[i]['givenum'];
					}
				}
				that.detailurl = res.detailurl;
				that.tourl = res.tourl;
        
				that.paypal = res.paypal || 0;
				
				that.more_alipay = res.more_alipay;
				that.alipay2 = res.alipay2;
				that.alipay3 = res.alipay3;
				that.yuanbao_money = res.yuanbao_money;
				that.total_yuanbao = res.total_yuanbao;
				that.yuanbao_msg   = res.yuanbao_msg;
				that.yuanbaopay    = res.yuanbaopay;
				if(that.wxpay){
					if(that.wxpay_type == 22){
						that.typeid = 22;	
					}else{
						that.typeid = 2;
					}
				}else if(that.moneypay){
					that.typeid = 1;
				}else if(that.alipay){
					that.typeid = 3;
					if(that.alipay == 2){
						that.typeid = 23;
					}
				}else if(that.more_alipay){
					if(that.alipay2){
						that.typeid = 31;
					}
                if(that.alipay3){
						that.typeid = 32;
                }
				}else if(that.baidupay){
					that.typeid = 11;
				}else if(that.toutiaopay){
					that.typeid = 12;
				}
				if(that.payorder.money==0 && that.payorder.score>0){
					that.typeid = 1;
				}
				if(res.invite_free){
						that.invite_free = res.invite_free;
				}
				if(res.free_tmplids){
						that.free_tmplids = res.free_tmplids;
				}
				that.loaded();

				
				if(that.opt && that.opt.paypal == 'success'){
					that.typeid = 51;
					app.showLoading('支付中');
          app.post('ApiPay/paypalRedirect', {orderid: that.opt.id,paymentId:that.opt.paymentId,PayerID:that.opt.PayerID}, function (res) {
						app.showLoading(false);
						if(res.status == 1){
							app.success(res.msg);
							that.subscribeMessage(function () {
								if(that.invite_free){
									that.invite_status = true;
								}else{
									setTimeout(function () {
										if (that.give_coupon_list && that.give_coupon_list.length > 0) {
											that.give_coupon_show = true;
											that.give_coupon_close_url = that.tourl;
										} else {
											//uni.navigateBack();
											that.gotourl(that.tourl,'reLaunch');
										}
									}, 1000);
								}
							});
						}else if (res.status == 0){
							app.error(res.msg);
						}
          });
				}
			});
		},
    getpwd: function (done, val) {
			this.paypwd = val;
			this.topay({currentTarget:{dataset:{typeid:1}}});
    },
    changeradio: function (e) {
      var that = this;
      var typeid = e.currentTarget.dataset.typeid;
      that.typeid = typeid;
			console.log(typeid)
    },
	check_base_info:function()
	{
		let info = [];
		for(let j=0;j<this.s_base_info.length;j++)
		{
			let item = this.s_base_info[j];
			if(item['pay_total']>item['balance'])
			{
				 info.push(item);
			}
		}
		return info;
	},
    topay: function (e) {
      var that = this;
      var typeid = that.typeid;
      var orderid = this.payorder.id;
	  
	  
	  if(that.is_shop_pay==true)
	  {
		  let infos = that.check_base_info();
		  if(infos.length!=0)
		  {
			    app.error("对应店铺余额不足,请前去充值!");
				// uni.showToast({
				// 	title: "对应店铺余额不足,请前去充值!",
				// 	icon: 'none',
				// 	duration: 1500
				// });
				
			   uni.showModal({
				title: '对应店铺余额不足,请前去充值!',
				content: "",
				showCancel: false,
				success: function(res) {
					uni.navigateTo({
						url:"/pagesExt/maidan/pay?bid="+that.bid
					})
				}
			});
			  
			  return ;
		  }
        //商店支付;        
		app.post('ApiPay/pay', { 
				        op:'submit',
						orderid: orderid,
					    typeid: typeid,paypwd: that.paypwd,
						pay_type:that.pay_type,
						'is_shop_pay':that.is_shop_pay?that.is_shop_pay:false
					}, function (res) {
	             app.showLoading(false);
	             if (res.status == 0) {
	                 app.error(res.msg);
	                 return;
	             }
	             if (res.status == 2) {
	                 app.success(res.msg);
	                 that.subscribeMessage(function () {
	                     if(that.invite_free){
	                         that.invite_status = true;
	                     }else{
	                         setTimeout(function () {
	                             if (that.give_coupon_list && that.give_coupon_list.length > 0) {
	                                 that.give_coupon_show = true;
	                                 that.give_coupon_close_url = that.tourl;
	                             } else {
	                                 that.gotourl(that.tourl,'reLaunch');
	                             }
	                         }, 1000);
	                     }
	                 });
	               return;
	             }
	         });
			 
			 return '';
	  }
	  
      if (typeid == 1) { //余额支付
        if(that.userinfo.haspwd && that.paypwd==''){
					that.$refs.dialogInput.open();return;
        }
        app.confirm('确定用' + that.t('余额') + '支付吗?', function () {
			app.showLoading('提交中');
            app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid,paypwd: that.paypwd,pay_type:that.pay_type}, function (res) {
                app.showLoading(false);
                if (res.status == 0) {
                    app.error(res.msg);
                    return;
                }
                if (res.status == 2) {
                    app.success(res.msg);
                    that.subscribeMessage(function () {
                        if(that.invite_free){
                            that.invite_status = true;
                        }else{
                            setTimeout(function () {
                                if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                    that.give_coupon_show = true;
                                    that.give_coupon_close_url = that.tourl;
                                } else {
                                    that.gotourl(that.tourl,'reLaunch');
                                }
                            }, 1000);
                        }
                    });
                  return;
                }
            });
        });
      }
	   else if(typeid == 88)
	  {
		  app.confirm('确定用' + that.t('黑积分') + '支付吗?', function () {
		  	app.showLoading('提交中');
		      
			  app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid,paypwd: that.paypwd,pay_type:that.pay_type}, function (res) {
		          app.showLoading(false);
		          if (res.status == 0) {
		              app.error(res.msg);
		              return;
		          }
		          if (res.status == 2) {
		              app.success(res.msg);
		              that.subscribeMessage(function () {
		                  if(that.invite_free){
		                      that.invite_status = true;
		                  }else{
		                      setTimeout(function () {
		                          if (that.give_coupon_list && that.give_coupon_list.length > 0) {
		                              that.give_coupon_show = true;
		                              that.give_coupon_close_url = that.tourl;
		                          } else {
		                              that.gotourl(that.tourl,'reLaunch');
		                          }
		                      }, 1000);
		                  }
		              });
		            return;
		          }
		      });
		  });
	  }
	  else if (typeid == 2) 
	  { //微信支付
		console.log(app)
		app.showLoading('提交中');
        app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {
			app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
                if(that.invite_free){
                    that.invite_status = true;
                }else{
                  setTimeout(function () {
                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                      that.give_coupon_show = true;
                      that.give_coupon_close_url = that.tourl;
                    } else {
                      that.gotourl(that.tourl,'reLaunch');
                    }
                  }, 1000);
                }
            });
            return;
          }
          var opt = res.data;
          if (app.globalData.platform == 'wx') {
						if(that.payorder.type == 'shop' || that.wxpay_type == 2){
							if(opt.orderInfo){
								console.log('requestOrderPayment1');
								wx.requestOrderPayment({
									'timeStamp': opt.timeStamp,
									'nonceStr': opt.nonceStr,
									'package': opt.package,
									'signType': opt.signType ? opt.signType : 'MD5',
									'paySign': opt.paySign,
									'orderInfo':opt.orderInfo,
									'success': function (res2) {
										app.success('付款完成');
										that.subscribeMessage(function () {
                                            if(that.invite_free){
                                                that.invite_status = true;
                                            }else{
                                                setTimeout(function () {
                                                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                        that.give_coupon_show = true;
                                                        that.give_coupon_close_url = that.tourl;
                                                    } else {
                                                        that.gotourl(that.tourl,'reLaunch');
                                                    }
                                                }, 1000);
                                            }
										});
									},
									'fail': function (res2) {
										//app.alert(JSON.stringify(res2))
									}
								});
							}else{
								console.log('requestOrderPayment2');
								wx.requestOrderPayment({
									'timeStamp': opt.timeStamp,
									'nonceStr': opt.nonceStr,
									'package': opt.package,
									'signType': opt.signType ? opt.signType : 'MD5',
									'paySign': opt.paySign,
									'success': function (res2) {
										app.success('付款完成');
										that.subscribeMessage(function () {
                                            if(that.invite_free){
                                                that.invite_status = true;
                                            }else{
                                                setTimeout(function () {
                                                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                        that.give_coupon_show = true;
                                                        that.give_coupon_close_url = that.tourl;
                                                    } else {
                                                        that.gotourl(that.tourl,'reLaunch');
                                                    }
                                                }, 1000);
                                            }
										});
									},
									'fail': function (res2) {
										//app.alert(JSON.stringify(res2))
									}
								});
							}
						}else{
							uni.requestPayment({
								'provider':'wxpay',
								'timeStamp': opt.timeStamp,
								'nonceStr': opt.nonceStr,
								'package': opt.package,
								'signType': opt.signType ? opt.signType : 'MD5',
								'paySign': opt.paySign,
								'success': function (res2) {
									app.success('付款完成');
									that.subscribeMessage(function () {
                                        if(that.invite_free){
                                            that.invite_status = true;
                                        }else{
                                            setTimeout(function () {
                                                if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                    that.give_coupon_show = true;
                                                    that.give_coupon_close_url = that.tourl;
                                                } else {
                                                    that.gotourl(that.tourl,'reLaunch');
                                                }
                                            }, 1000);
                                        }
									});
								},
								'fail': function (res2) {
									//app.alert(JSON.stringify(res2))
								}
							});
						}
					}else if(app.globalData.platform == 'mp'){
						// #ifdef H5
						function jsApiCall(){
							WeixinJSBridge.invoke('getBrandWCPayRequest',opt,function(res){
									if(res.err_msg == "get_brand_wcpay_request:ok" ) {
										app.success('付款完成');
										that.subscribeMessage(function () {
                                            if(that.invite_free){
                                                that.invite_status = true;
                                            }else{
                                                setTimeout(function () {
                                                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                        that.give_coupon_show = true;
                                                        that.give_coupon_close_url = that.tourl;
                                                    } else {
                                                        that.gotourl(that.tourl,'reLaunch');
                                                    }
                                                }, 1000);
                                            }
										});
									}else{

									}
								}
							);
						}
						if (typeof WeixinJSBridge == "undefined"){
							if( document.addEventListener ){
								document.addEventListener('WeixinJSBridgeReady', jsApiCall, false);
							}else if (document.attachEvent){
								document.attachEvent('WeixinJSBridgeReady', jsApiCall); 
								document.attachEvent('onWeixinJSBridgeReady', jsApiCall);
							}
						}else{
							jsApiCall();
						}
						// #endif
						/*
						var jweixin = require('jweixin-module');
						jweixin.chooseWXPay({
							timestamp: opt.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
							nonceStr: opt.nonceStr, // 支付签名随机串，不长于 32 位
							package: opt.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
							signType: opt.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
							paySign: opt.paySign, // 支付签名
							success: function (res2) {
								// 支付成功后的回调函数
								app.success('付款完成');
								that.subscribeMessage(function () {
									setTimeout(function () {
										if (that.give_coupon_list && that.give_coupon_list.length > 0) {
											that.give_coupon_show = true;
											that.give_coupon_close_url = that.tourl;
										} else {
											that.gotourl(that.tourl,'reLaunch');
										}
									}, 1000);
								});
							}
						});
						*/
					}else if(app.globalData.platform == 'h5'){
						location.href = opt.wx_url  + '&redirect_url='+encodeURIComponent(location.href.split('#')[0] + '#'+that.tourl);
					}else if(app.globalData.platform == 'app'){
						console.log(opt)
						uni.requestPayment({
							'provider':'wxpay',
							'orderInfo': opt,
							'success': function (res2) {
								app.success('付款完成');
								that.subscribeMessage(function () {
                                    if(that.invite_free){
                                        that.invite_status = true;
                                    }else{
                                        setTimeout(function () {
                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                that.give_coupon_show = true;
                                                that.give_coupon_close_url = that.tourl;
                                            } else {
                                                that.gotourl(that.tourl,'reLaunch');
                                            }
                                        }, 1000);
                                    }
								});
							},
							'fail': function (res2) {
								console.log(res2)
								//app.alert(JSON.stringify(res2))
							}
						});
					}else if(app.globalData.platform == 'qq'){
						qq.requestWxPayment({
							url: opt.wx_url,
							referer: opt.referer,
							success(res) {
								that.subscribeMessage(function () {
                                    if(that.invite_free){
                                        that.invite_status = true;
                                    }else{
                                        setTimeout(function () {
                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                that.give_coupon_show = true;
                                                that.give_coupon_close_url = that.tourl;
                                            } else {
                                                that.gotourl(that.tourl,'reLaunch');
                                            }
                                        }, 1000);
                                    }
								});
							},
							fail(res) { }
						})
					}
				})
			} else if (typeid == 29) { //杉德支付
		console.log(app)
		app.showLoading('提交中');
        app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {
			app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
                if(that.invite_free){
                    that.invite_status = true;
                }else{
                  setTimeout(function () {
                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                      that.give_coupon_show = true;
                      that.give_coupon_close_url = that.tourl;
                    } else {
                      that.gotourl(that.tourl,'reLaunch');
                    }
                  }, 1000);
                }
            });
            return;
          }

				})
			} else if (typeid == 3 || typeid == 31 || typeid == 32) { //支付宝支付
				app.showLoading('提交中');
                app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {
                            console.log(res)
                            app.showLoading(false);
                  if (res.status == 0) {
                    app.error(res.msg);
                    return;
                  }
                  if (res.status == 2) {
                    //无需付款
                    app.success(res.msg);
                    that.subscribeMessage(function () {
                        if(that.invite_free){
                            that.invite_status = true;
                        }else{
                          setTimeout(function () {
                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                              that.give_coupon_show = true;
                              that.give_coupon_close_url = that.tourl;
                            } else {
                              that.gotourl(that.tourl,'reLaunch');
                            }
                          }, 1000);
                        }
                    });
                    return;
                  }
                  var opt = res.data;
					if (app.globalData.platform == 'alipay') {
						uni.requestPayment({
							'provider':'alipay',
							'orderInfo': opt.trade_no,
							'success': function (res2) {
								console.log(res2)
								if(res2.resultCode == '6001'){
									return;
								}
								app.success('付款完成');
								that.subscribeMessage(function () {
                                    if(that.invite_free){
                                        that.invite_status = true;
                                    }else{
                                        setTimeout(function () {
                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                that.give_coupon_show = true;
                                                that.give_coupon_close_url = that.tourl;
                                            } else {
                                                that.gotourl(that.tourl,'reLaunch');
                                            }
                                        }, 1000);
                                    }
								});
							},
							'fail': function (res2) {
								//app.alert(JSON.stringify(res2))
							}
						});
					}else if(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){
						document.body.innerHTML = res.data;
						document.forms['alipaysubmit'].submit();
					}else if(app.globalData.platform == 'app'){
						console.log('------------alipay----------')
						console.log(opt)
						console.log('------------alipay end----------')
						uni.requestPayment({
							'provider':'alipay',
							'orderInfo': opt,
							'success': function (res2) {
								console.log('------------success----------')
								console.log(res2)
								app.success('付款完成');
								that.subscribeMessage(function () {
                                    if(that.invite_free){
                                        that.invite_status = true;
                                    }else{
                                        setTimeout(function () {
                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                that.give_coupon_show = true;
                                                that.give_coupon_close_url = that.tourl;
                                            } else {
                                                that.gotourl(that.tourl,'reLaunch');
                                            }
                                        }, 1000);
                                    }
								});
							},
							'fail': function (res2) {
								console.log(res2)
								//app.alert(JSON.stringify(res2))
							}
						});
					}
				})
      } else if (typeid == '11') {
			app.showLoading('提交中');
            app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {
					app.showLoading(false);
					swan.requestPolymerPayment({
						'orderInfo': res.orderInfo,
						'success': function (res2) {
							app.success('付款完成');
							that.subscribeMessage(function () {
                                if(that.invite_free){
                                    that.invite_status = true;
                                }else{
                                    setTimeout(function () {
                                        if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                            that.give_coupon_show = true;
                                            that.give_coupon_close_url = that.tourl;
                                        } else {
                                            that.gotourl(that.tourl,'reLaunch');
                                        }
                                    }, 1000);
                                }
							});
						},
						'fail': function (res2) {
							if(res2.errCode!=2){
								app.alert(JSON.stringify(res2))
							}
						}
					});
			});
		} else if (typeid == '12') {
			app.showLoading('提交中');
            app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {
					app.showLoading(false);
					console.log(res.orderInfo);
					tt.pay({
						'service':5,
						'orderInfo': res.orderInfo,
						'success': function (res2) {
							if (res2.code === 0) {
								app.success('付款完成');
								that.subscribeMessage(function () {
                                    if(that.invite_free){
                                        that.invite_status = true;
                                    }else{
                                        setTimeout(function () {
                                            if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                that.give_coupon_show = true;
                                                that.give_coupon_close_url = that.tourl;
                                            } else {
                                                that.gotourl(that.tourl,'reLaunch');
                                            }
                                        }, 1000);
                                    }
								});
							}
						},
						'fail': function (res2) {
							app.alert(JSON.stringify(res2))
						}
					});
				});
			} else if (typeid == '22') {
				if (app.globalData.platform == 'wx') {
					wx.login({
						success:function(res){
							if (res.code) {
								app.showLoading('提交中');
								app.post('ApiPay/getYunMpauthParams',{jscode: res.code},function(res){
									app.showLoading(false);
									app.post('https://showmoney.cn/scanpay/fixed/mpauth',res.params,function(res2){
										console.log(res2.sessionKey);
										app.post('ApiPay/getYunUnifiedParams',{orderid: orderid,sessionKey:res2.sessionKey},function(res3){
											app.post('https://showmoney.cn/scanpay/unified',res3.params,function(res4){
												if(res4.respcd == '09'){
													wx.requestPayment({
														timeStamp: res4.timeStamp,
														nonceStr: res4.nonceStr,
														package: res4.package,
														signType: res4.mpSignType,
														paySign: res4.mpSign,
														success: function success(result) {
															app.success('付款完成');
															that.subscribeMessage(function () {
                                                                if(that.invite_free){
                                                                    that.invite_status = true;
                                                                }else{
                                                                    setTimeout(function () {
                                                                        if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                                                            that.give_coupon_show = true;
                                                                            that.give_coupon_close_url = that.tourl;
                                                                        } else {
                                                                            that.gotourl(that.tourl,'reLaunch');
                                                                        }
                                                                    }, 1000);
                                                                }
															});
														},
														fail: function (res5) {
															//app.alert(JSON.stringify(res5))
														}
													});
												}else{
													app.alert(res4.errorDetail);
												}
											})
										})
									})
								})
							} else {
								console.log('登录失败！' + res.errMsg)
							}
						}
					});
				}else{
					var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
					url += '&op=submit&orderid='+orderid+'&typeid=22';
					location.href = url;
				}
			} else if (typeid == '23') {
				//var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
				//url += '&op=submit&orderid='+orderid+'&typeid=23';
				//location.href = url;
				setTimeout(function () {
					that.$refs.dialogPayconfirm.open();
				}, 1000);

				app.goto('/pages/index/webView2?orderid='+orderid+'&typeid=23'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id);
				return ;
				app.showLoading('提交中');
				app.post('ApiPay/pay',{op:'submit',orderid: orderid,typeid: 23},function(res){
					app.showLoading(false);
					console.log(res)
					app.goto('url::'+res.url);
				});
			} else if (typeid == '24') {
				//var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
				//url += '&op=submit&orderid='+orderid+'&typeid=23';
				//location.href = url;

				app.goto('/pages/index/webView2?orderid='+orderid+'&typeid=24');
				return ;
				app.showLoading('提交中');
				app.post('ApiPay/pay',{op:'submit',orderid: orderid,typeid: 24},function(res){
					app.showLoading(false);
					console.log(res)
					app.goto('url::'+res.url);
				});
			}else if (typeid == 'yuanbao') { 
                //元宝支付
				console.log(app)
                
                var total_yuanbao = that.total_yuanbao-0;
                var u_yuanbao     = that.userinfo.yuanbao-0;
                if(total_yuanbao>u_yuanbao){
                    app.alert(that.t('元宝')+'不足' );
                    return;
                }
                that.open_pay = true;
                that.pay_type = 'yuanbao';
			} else if (typeid == '51') {
				app.showLoading('提交中');
				app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: typeid}, function (res) {
					app.showLoading(false);
					console.log(res);
					if(res.status == 1){
						if(app.globalData.platform == 'app'){
							const wv = plus.webview.create("","custom-webview",{
								top: uni.getSystemInfoSync().statusBarHeight + 44
							});
							wv.loadURL(res.data)
							var currentWebview = that.$scope.$getAppWebview();
							currentWebview.append(wv);
						}else{
							app.goto('url::'+res.data);
						}
					}else{
						app.alert(res.msg)
					}
				});
			}
		},
		topay2:function(){
			var that = this;
            var orderid = this.payorder.id;
			app.confirm('确定要' + that.codtxt + '吗?', function () {
				app.showLoading('提交中');
				app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: 4}, function (res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					if (res.status == 2) {
						//无需付款
						app.success(res.msg);
						that.subscribeMessage(function () {
							setTimeout(function () {
								that.gotourl(that.tourl,'reLaunch');
							}, 1000);
						});
						return;
					}
				});
			})
		},
		topayMonth:function(){
			var that = this;
            var orderid = this.payorder.id;
			app.confirm('确定要' + that.pay_month_txt + '支付吗?', function () {
				app.showLoading('提交中');
				app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: 41}, function (res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					if (res.status == 2) {
						//无需付款
						app.success(res.msg);
						that.subscribeMessage(function () {
							setTimeout(function () {
								that.gotourl(that.tourl,'reLaunch');
							}, 1000);
						});
						return;
					}
				});
			})
		},
		topayTransfer:function(e){
			var that = this;
            var orderid = this.payorder.id;
			app.confirm('确定要' + e.currentTarget.dataset.text + '吗?', function () {
				app.showLoading('提交中');
				app.post('ApiPay/pay', {op:'submit',orderid: orderid,typeid: 5}, function (res) {
					app.showLoading(false);
					
					if (res.status == 1) {
						//需审核付款
						app.success(res.msg);
						setTimeout(function () {
							that.gotourl('/pagesExt/order/orderlist','reLaunch');
						}, 1000);
						return;
					}else if (res.status == 2) {
						//无需付款
						app.success(res.msg);
						that.subscribeMessage(function () {
							
						});
						setTimeout(function () {
							that.gotourl('transfer?id='+orderid,'reLaunch');
						}, 1000);
						return;
					}else{
                        app.error(res.msg);
                        return;
                    }
				});
			})
		},
		give_coupon_close:function(e){
			var that = this;
			var tourl = e.currentTarget.dataset.url;
			this.give_coupon_show = false;
			that.gotourl(tourl,'reLaunch');
		},
		gotourl:function(tourl, opentype){
			var that = this;
			if(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){
				if (tourl.indexOf('miniProgram::') === 0) {
					//其他小程序
					tourl = tourl.slice(13);
					var tourlArr = tourl.split('|');
					console.log(tourlArr)
					that.showOpenWeapp();
					return;
				}
			}
			app.goto(tourl, opentype);
		},
		showOpenWeapp:function(){
			this.$refs.dialogOpenWeapp.open();
		},
		closeOpenWeapp:function(){
			this.$refs.dialogOpenWeapp.close();
		},
		PayconfirmFun:function(){
			this.gotourl(this.tourl,'reLaunch');
		},
        close_pay:function(){
            var that = this;
            that.open_pay = false;
        },
        closeInvite:function(){
           var that = this;
           that.invite_status = false;
           setTimeout(function () {
               if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                   that.give_coupon_show = true;
                   that.give_coupon_close_url = that.tourl;
               } else {
                   that.gotourl(that.tourl,'reLaunch');
               }
           }, 1000);
        },
        gotoInvite:function(){
            var that = this;
            var free_tmplids = that.free_tmplids;
            if(free_tmplids && free_tmplids.length > 0){
            	uni.requestSubscribeMessage({
            		tmplIds: free_tmplids,
            		success:function(res) {
            			console.log(res)
            		},
            		fail:function(res){
            			console.log(res)
            		}
            	})
            }
            app.goto('/pagesExt/invite_free/index','reLaunch')
        },
				todaifu:function(e){
					var that = this;
					var platform = app.getplatform()
					var id = that.payorder.id
					if(platform == 'mp' || platform == 'h5'){
						var sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/pay/daifu?scene=id_'+that.payorder.id;
						this._sharemp({title:"您有一份好友代付待查收，请尽快处理~",link:sharelink,pic:that.sharepic})
						app.error('点击右上角发送给好友或分享到朋友圈');
					}else if(platform == 'app'){
						uni.showActionSheet({
							itemList: ['发送给微信好友', '分享到微信朋友圈'],
							success: function (res){
								if(res.tapIndex >= 0){
									var scene = 'WXSceneSession';
									if (res.tapIndex == 1) {
										scene = 'WXSenceTimeline';
									}
									var sharedata = {};
									sharedata.provider = 'weixin';
									sharedata.type = 0;
									sharedata.scene = scene;
									sharedata.title = '您的好友向您发出了代付请求';
									sharedata.summary = '您有一份好友代付待查收，请尽快处理~';
									sharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/pay/daifu?scene=id_'+that.payorder.id;
									sharedata.imageUrl = '';
									uni.share(sharedata);
								}
							}
						});
					}else{
						app.error('该终端不支持此操作');
					}
				}
  }
}
</script>
<style>

.top{width:100%;display:flex;flex-direction:column;align-items:center;padding-top:60rpx}
.top .f1{height:60rpx;line-height:60rpx;color:#939393;font-size:24rpx;}
.top .f2{color:#101010;font-weight:bold;font-size:72rpx;height:120rpx;line-height:120rpx}
.top .f2 .t1{font-size:44rpx}
.top .f2 .t3{font-size:50rpx}
.top .f3{color:#FC5729;font-size:26rpx;height:70rpx;line-height:70rpx}

.paytype{width:94%;margin:20rpx 3% 80rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;margin-top:20rpx;background:#fff}
.paytype .f1{height:100rpx;line-height:100rpx;padding:0 30rpx;color:#333333;font-weight:bold}

.paytype .f2{padding:0 30rpx}
.paytype .f2 .item{border-bottom:1px solid #f5f5f5;height:100rpx;display:flex;align-items:center}
.paytype .f2 .item:last-child{border-bottom:0}
.paytype .f2 .item .t1{flex:1;display:flex;align-items:center;color:#222222;font-size:30rpx;font-weight:bold}
.paytype .f2 .item .t1 .img{width:44rpx;height:44rpx;margin-right:40rpx}

.paytype .f2 .item .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}
.paytype .f2 .item .radio .radio-img{width:100%;height:100%}

.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:10rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}
.daifu-btn{background: #fc5729;}
.op{width:94%;margin:20rpx 3%;display:flex;align-items:center;margin-top:40rpx}
.op .btn{flex:1;height:100rpx;line-height:100rpx;background:#07C160;width:90%;margin:0 10rpx;border-radius:10rpx;color: #fff;font-size:28rpx;font-weight:bold;display:flex;align-items:center;justify-content:center}
.op .btn .img{width:48rpx;height:48rpx;margin-right:20rpx}

.give-coupon { width: 100%; height: 100%; position: absolute; left: 0; top: 0; z-index: 2000; background-color: rgba(0, 0, 0, 0.5); }
.give-coupon .coupon-block { margin-top: -140rpx; position: relative; }
.give-coupon .coupon-info { width: 559rpx; border-radius: 0 0 30rpx 30rpx; background-color: #fff; margin-top: -20rpx; padding: 20rpx 0 20rpx 0; }
.give-coupon .coupon-one { width: 100%; height: 164rpx; margin-bottom: 10rpx; }
.give-coupon .coupon-bg { width: 514rpx; height: 164rpx; border-radius: 10rpx; box-shadow: 2rpx 2rpx 30rpx #ddd; padding: 14rpx; }
.give-coupon .coupon-bg-1 { width: 100%; height: 100%; border: 2rpx #ff4544 dashed; border-radius: 10rpx; padding: 0 20rpx; }
.give-coupon .coupon-del { position: absolute; right: 34rpx; top: 224rpx; width: 90rpx; height: 90rpx; }
.give-coupon .coupon-del image{ width: 30rpx; height: 30rpx; }
.give-coupon .coupon-text { color: #707070; margin-top: 24rpx; margin-bottom: 34rpx; font-size: 9pt; }
.give-coupon .coupon-text::before { content: ' '; margin-right: 32rpx; width: 50rpx; height: 1rpx; background-color: #707070; overflow: hidden; margin-top: 21rpx; }
.give-coupon .coupon-text::after { content: ' '; margin-left: 32rpx; width: 50rpx; height: 1rpx; background-color: #707070; overflow: hidden; margin-top: 21rpx; }
.give-coupon .coupon-btn { position: relative;margin:0 auto;width:340rpx;height:70rpx;line-height:70rpx;background:linear-gradient(90deg,#F9475F,#EF155B);color:#fff;border-radius:40rpx;margin-top:20rpx}
.give-coupon .coupon-btn image { width: 374rpx; height: 96rpx; }
.give-coupon .coupon-get{ margin-top: 4rpx; margin-bottom: 20rpx; color: #ff4544; font-size: 13pt; }

.give-coupon .coupon-coupon{width:100%;display:flex;padding:0 20rpx;margin:10rpx 0;position: relative;}
.give-coupon .coupon-coupon .pt_img1{background:url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDI3IDEzMCI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM3M2FmNjA7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT7nu7/oibJfMjwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMzkuNSw0Ny43NlY2MS42MmE1LDUsMCwwLDEsMCw5LjgydjMuMzhhNSw1LDAsMCwxLDAsOS44MlY4OGE1LDUsMCwwLDEsMCw5LjgydjMuMzlhNSw1LDAsMCwxLDAsOS44MnYzLjM5YTUsNSwwLDAsMSwwLDkuODJ2My4zOWE1LDUsMCwwLDEsMCw5LjgydjMuMzlhNSw1LDAsMCwxLDAsOS44MnYzLjM4YTUsNSwwLDAsMSwwLDkuODJ2MTMuODdoMjd2LTEzMFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0zOS41IC00Ny43NikiLz48L3N2Zz4=);background-size: 147%;height:140rpx;border-bottom-left-radius:16rpx;border-top-left-radius:16rpx;width:4%}
.give-coupon .coupon-coupon .pt_img2{background:url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDI3IDEzMCI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM2ZmM1ZmE7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT7ok53oibJfMTwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMCwwVjEzLjg2YTUsNSwwLDAsMSw0LjEsNC45MUE1LDUsMCwwLDEsMCwyMy42OHYzLjM5QTUsNSwwLDAsMSw0LjEsMzIsNSw1LDAsMCwxLDAsMzYuODl2My4zOWE1LDUsMCwwLDEsNC4xLDQuOTFBNSw1LDAsMCwxLDAsNTAuMXYzLjM5QTUsNSwwLDAsMSw0LjEsNTguNCw1LDUsMCwwLDEsMCw2My4zMXYzLjM4QTUsNSwwLDAsMSw0LjEsNzEuNiw1LDUsMCwwLDEsMCw3Ni41MVY3OS45YTUsNSwwLDAsMSw0LjEsNC45MUE1LDUsMCwwLDEsMCw4OS43MnYzLjM5QTUsNSwwLDAsMSw0LjEsOTgsNSw1LDAsMCwxLDAsMTAyLjkzdjMuMzlhNSw1LDAsMCwxLDQuMSw0LjkxQTUsNSwwLDAsMSwwLDExNi4xNFYxMzBIMjdWMFoiLz48L3N2Zz4=);background-size: 147%;height:140rpx;border-bottom-left-radius:16rpx;border-top-left-radius:16rpx;width:4%}
.give-coupon .coupon-coupon .pt_left{background: #73af60;height:140rpx;color: #FFF;padding-bottom:20rpx;padding-right:10rpx;width:30%;display:flex;flex-direction:column;align-items:center;justify-content:center}
.give-coupon .coupon-coupon .pt_left.bg2{background:#6fc5fa}
.give-coupon .coupon-coupon .pt_left .f1{font-size:34rpx;text-align:center}
.give-coupon .coupon-coupon .pt_left .t0{padding-right:10rpx;}
.give-coupon .coupon-coupon .pt_left .t1{font-size:46rpx;}
.give-coupon .coupon-coupon .pt_left .t2{padding-left:10rpx}
.give-coupon .coupon-coupon .pt_left .f2{font-size:24rpx;text-align:center;overflow:hidden}
.give-coupon .coupon-coupon .pt_right{background: #fff;width:66%;display:flex;height:140rpx;text-align: left;padding:20rpx 30rpx;border-top-right-radius:16rpx;border-bottom-right-radius:16rpx;position:relative}
.give-coupon .coupon-coupon .pt_right .f1{flex-grow: 1;flex-shrink: 1;}
.give-coupon .coupon-coupon .pt_right .f1 .t1{font-size:36rpx;color:#2c3e50;height:40rpx;line-height: 40rpx;overflow: hidden;}
.give-coupon .coupon-coupon .pt_right .f1 .t2{height:60rpx;line-height:60rpx;font-size:24rpx;color:#727272;}
.give-coupon .coupon-coupon .pt_right .f1 .t2_1{height:40rpx;line-height:40rpx}
.give-coupon .coupon-coupon .pt_right .f1 .t3{font-size:24rpx;color:#2c3e50}
.give-coupon .coupon-coupon .pt_right .f1 .t4{font-size:24rpx;color:#555555}
.give-coupon .coupon-coupon .pt_right .btn{position:absolute;right:30rpx;top:50%;margin-top:-25rpx;border-radius:25rpx;width:140rpx;height:50rpx;line-height:50rpx;background:#07c160;color:#fff;}

.give-coupon .coupon_num{position:absolute;top:50rpx;right:30rpx;font-size:30rpx}
</style>