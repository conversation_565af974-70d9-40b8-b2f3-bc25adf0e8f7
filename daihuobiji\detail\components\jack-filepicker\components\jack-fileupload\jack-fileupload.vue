
<template>
	<view class="">
		<view>
			<!-- 图片预览 -->
			<view class="container">
				<swiper interval="3000" :disable-touch="isEdit" :current="currentIndex" @change="onChange"
					duration="500" circular :style="{'margin-bottom': '10rpx'}">
					<swiper-item :catchtouchmove="isEdit" v-for="(image, index) in images" :key="index"
						style="text-align: center">
						<view class="content-box" :style="{'overflow':(notMoveCanvas?'hidden':'auto')}">
							<view class="canvas" :style="{height: canvasHeight[index] +'px'}" :id="'canvas-'+index">
								<canvas :style="{'width':itemCanvasInfo.width+'px','height':itemCanvasInfo.height+'px'}"
									:canvas-id="'newCanvas-'+index"></canvas>
								<canvas class="canvasDom" :ref="'itemCanvas-'+index"
									:style="{'width':itemCanvasInfo.width+'px','height':itemCanvasInfo.height+'px'}"
									:canvas-id="'itemCanvas-'+index"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px'}"
									:canvas-id="'imgCanvas-'+index" class="canvasDom"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px'}"
									:canvas-id="'drawCanvas-'+index" @touchmove="touchmove" @touchstart="touchstart"
									@touchend="touchend" class="canvasDom"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px','z-index':99999}"
									:canvas-id="'timeCanvas-'+index" @touchmove="touchmove" @touchstart="touchstart"
									@touchend="touchend" class="canvasDom"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px','z-index':99999}"
									:canvas-id="'textCanvas-'+index" @touchmove="touchmove" @touchstart="touchstart"
									@touchend="touchend" class="canvasDom"></canvas>
								<canvas :style="{'width':'100%','height':canvasHeight[index]+'px'}"
									:canvas-id="'clipCanvas-'+index" @touchmove="touchmove" @touchstart="touchstart"
									@touchend="touchend" class="canvasDom"></canvas>
							</view>
						</view>
					</swiper-item>
				</swiper>

				<view>
					<scroll-view class="scroll-view" scroll-x="true" scroll-left="0"
						style="white-space: nowrap;text-align: center;">
						<view v-for="(image,index) in images" :key="index" @click="switchImg(index)"
							style="display: inline-block;text-align: center;margin-right: 20rpx;">

							<view class="img-box" :class="index == currentIndex?'img-box-active':'img-box'"
								:style="{'margin': 0,'transform': index != currentIndex? 'translateY(-22px)':'translateY(0rpx)'}">
								<image :src="image.url" style="height: 100%;width: 100%;" mode="scaleToFill" />
							</view>
							<view v-if="index == currentIndex">
								<view @click="!btnCanotUse?addMark('edit'):()=>{}"
									:class="[!btnCanotUse?'':'disabled']">
									<text style="color: #fff;font-size: 20rpx;">{{!isEdit?'编辑':'保存'}}</text>
								</view>
							</view>
						</view>
						<view class="add-img" @click="chooseFile"
							:style="{'transform':images.length == 0?'translateY(0rpx)':'translateY(-50rpx)'}">
							+
						</view>
					</scroll-view>
				</view>
			</view>
		</view>


		<cover-view class="mini-btn" @click="!btnCanotUse?addMark('edit'):()=>{}"
			size="mini">{{!isEdit?'打开编辑模式':'关闭并保存'}}</cover-view>

		<!-- 操作按钮 -->
		<view>
			<view class="tools">
				<view class="tools_item" @click="!btnCanotUse?addMark('time'):()=>{}"
					:class="[!btnCanotUse?'':'disabled']">
					<image src="@/static/fileupload/time.svg"></image>
					<text v-if="!dateTimeInfo.hash">当前时间</text>
					<text v-if="dateTimeInfo.hash">清除时间</text>
				</view>
				<view class="tools_item" @click="!btnCanotUse?addMark('text'):()=>{}"
					:class="[!btnCanotUse?'':'disabled']">
					<image src="@/static/fileupload/text.svg"></image>
					<text>文字</text>
				</view>
				<view class="tools_item" @click="!btnCanotUse?addMark('rotate'):()=>{}"
					:class="[!btnCanotUse?'':'disabled']">
					<image src="@/static/fileupload/refresh.svg"></image>
					<text>旋转</text>
				</view>
				<view class="tools_item" @click="!canDraw?addMark('clip'):()=>{}" :class="[canDraw?'disabled':'']">
					<image src="@/static/fileupload/tailor.svg"></image>
					<text v-if="!isClip">裁剪</text>
					<text v-if="isClip">确认裁剪</text>
				</view>
				<view class="tools_item">
					<view class="tools_item_strokesetting" v-if="canDraw">
						<view class="tools_item_strokesetting_item">
							<view class="tools_item_strokesetting_item_selector" style="height: 40rpx;">
								<view v-for="item in colorList" @click="strokeInfo.color=item"
									:style="{'background':(item!='clear'?item:'radial-gradient(#555555 1px, white 1px) repeat'),'background-size':'4px 4px'}"
									:class="['colorbox',(item==strokeInfo.color)?'tools_item_strokesetting_item_selector_active':'']">
								</view>
							</view>
							<view style="height: 40rpx;">
								<slider :value="strokeInfo.weight" :min="2" :max="15" block-size="18"
									@change="sliderChange" show-value />
							</view>
						</view>
					</view>
					<image src="@/static/fileupload/brush.svg" @click="!isClip?addMark('draw'):()=>{}"
						:class="[isClip?'disabledClip':'']"></image>
					<text @click="!isClip?addMark('draw'):()=>{}"
						:class="[isClip?'disabledClip':'']">{{canDraw?'停止涂鸦':'涂鸦'}}</text>
				</view>

				<view class="tools_item" @click="!btnCanotUse?addMark('edit'):()=>{}"
					:class="[!btnCanotUse?'':'disabled']">
					<image src="@/static/fileupload/right.svg"></image>
					<text>{{!isEdit?'编辑':'保存'}}</text>
				</view>

				<view class="tools_item" :class="[!btnCanotUse?'':'disabled']" @click="!btnCanotUse?exit():()=>{}">
					<image src="@/static/fileupload/close.svg"></image>
					<text>删除</text>
				</view>
			</view>
		</view>

		<uni-popup ref="popup" type="bottom" border-radius="20rpx 20rpx 0 0" background-color="rgb(230 230 230)"
			style="z-index: 2000;">
			<view
				style="height: 240rpx;border-radius: 20rpx 20rpx 0 0;background-color: rgb(230 230 230);padding-top: 20rpx;position: relative;">
				<view class="tools_item_strokesetting_item_selector" style="height: 40rpx;">
					<view v-for="item in colorList" @click="textInfo.color=item"
						:style="{'background':(item!='clear'?item:'radial-gradient(#555555 1px, white 1px) repeat'),'background-size':'4px 4px'}"
						:class="['colorbox',(item==textInfo.color)?'tools_item_strokesetting_item_selector_active':'']">
					</view>
				</view>
				<input type="text" v-model="textInfo.text" placeholder="请输入文字"
					style="padding-left: 30rpx;margin-top: 30rpx;" />
				<view
					style="display: flex;justify-content: space-between;position: absolute;bottom: 20rpx;width: 100%;padding: 0 30rpx;">
					<view>
					</view>
					<view>
						<image src="@/static/fileupload/match.svg" style="width: 40rpx;" mode="widthFix"
							@click="confirmText"></image>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	import {
		getNowDateTime,
		rpx2px,
		rpxTopx,
		base64toBlob,
		blobToUrl,
		canvasHasContent,
		urlImgRotate,
		base64ToWxfile,

	} from "../../utils/index.js"
	let sto = null
	let sto1 = null

	// #ifdef MP-WEIXIN
	var newCanvas = [] // 最后合并后的canvas
	// #endif
	var imgCanvas = []
	var timeCanvas = []
	var textCanvas = []
	var drawCanvas = []
	var clipCanvas = []
	var itemCanvas = []
	export default {
		name: 'JackFileupload',
		components: {

		},
		props: {

		},
		data() {
			return {
				currentIndex: 0,
				// 轮播图图片数组
				images: [],
				isClip: false, //是否裁剪
				canDraw: false,
				dateTimeInfo: { // 当前时间
					left: 0,
					top: 0,
					hash: false,
					nowTime: ''
				},
				colorList: [ // 颜色
					'red',
					'orange',
					'black',
					'white',
					'green',
					'clear'
				],
				strokeInfo: {
					weight: 2,
					color: 'red'
				},
				textInfo: { // 文字
					left: 0,
					top: 0,
					text: '',
					color: 'red'
				},
				currentImage: {},
				currentImgInfo: [], // 全部图片的宽高
				canvasHeight: [],
				screenInfo: {
					width: 0,
					height: 0
				},
				// 裁剪截取的每一层的宽高
				itemCanvasInfo: {
					width: 0,
					height: 0
				},
				// 触摸偏移量
				textOffset: { // 文字
					left: 0,
					top: 0
				},
				// 触摸偏移量
				timeOffset: { // 时间
					left: 0,
					top: 0
				},
				fourPointRange: [],
				// 旋转的角度
				rotateDeg: 0,
				touches: [],
				currentTouchPoint: {}, //当前点的位置
				radius: 10, //顶点半径
				isMovePoint: false, //是否在移动顶点
				startX: '',
				startY: '',
				draggingCorner: [],
				isEdit: false,
				testImg:''
			}
		},
		computed: {
			notMoveCanvas() {
				return this.moveTime || this.canDraw || this.moveText
			},
			// 按钮可用状态
			btnCanotUse() {
				return this.isClip || this.canDraw
			}
		},
		watch: {
			isClip(n) {
				if (!n) {
					// 将itemCanvas宽高设置成裁剪宽高
					this.itemCanvasInfo.width = this.fourPointRange[1].x2 - this.fourPointRange[0].x1
					this.itemCanvasInfo.height = this.fourPointRange[2].y2 - this.fourPointRange[0].y1
					uni.showModal({
						cancelText: '舍弃',
						confirmText: '保留',
						title: '是否保留裁剪区域',
						success: (res) => {
							if (res.confirm) {
								this.saveClip()
							}
							if (res.cancel) {
								clipCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this
									.canvasHeight[this.currentIndex])
								clipCanvas[this.currentIndex].draw()
								this.itemCanvasInfo.width = 0
								this.itemCanvasInfo.height = 0
							}
						}
					})
				}
			}
		},
		methods: {
			onChange(e) {
				this.currentIndex = e.detail.current;
				this.currentImage = this.images[this.currentIndex]
			},
			switchImg(index) {
				if (!this.isEdit) {
					this.currentIndex = index
					this.currentImage = this.images[this.currentIndex]
				} else {
					uni.showToast({
						title: '请先保存图片',
						duration: 2000,
						icon: 'none'
					});
				}
			},
			sliderChange(e) {
				this.strokeInfo.weight = e.detail.value
			},
			// 加载图片到canvas
			loadImgToCanvas() {
				this.canvasHeight = []
				this.currentImgInfo.forEach(item => {
					let w = item.width
					let h = item.height
					// 计算比例
					let ro = w / h
					const fixedHeight = this.screenInfo.height * 0.73;
					if ((this.screenInfo.width / ro) > fixedHeight) {
						this.canvasHeight.push(fixedHeight)
					} else {
						this.canvasHeight.push(this.screenInfo.width / ro)
					}
				})

				imgCanvas = []
				timeCanvas = []
				textCanvas = []
				drawCanvas = []
				clipCanvas = []
				itemCanvas = []
				this.images.forEach((item, index) => {
					// #ifdef MP-WEIXIN
					newCanvas.push(uni.createCanvasContext('newCanvas-' + index, this))
					// #endif
					imgCanvas.push(uni.createCanvasContext('imgCanvas-' + index, this))
					timeCanvas.push(uni.createCanvasContext('timeCanvas-' + index, this))
					textCanvas.push(uni.createCanvasContext('textCanvas-' + index, this))
					drawCanvas.push(uni.createCanvasContext('drawCanvas-' + index, this))
					clipCanvas.push(uni.createCanvasContext('clipCanvas-' + index, this))
					itemCanvas.push(uni.createCanvasContext('itemCanvas-' + index, this))
				})
				setTimeout(() => {
					const fixedHeight = this.screenInfo.height * 0.73;
					imgCanvas.forEach((item, index) => {
						let w = this.currentImgInfo[index].width
						let h = this.currentImgInfo[index].height
						let ro = w / h
						const width = fixedHeight * ro; // 根据固定高度和宽高比计算宽度
						if ((this.screenInfo.width / ro) > fixedHeight) {
							item.drawImage(this.images[index].url, (this.screenInfo.width - width) / 2, 0,
								width, fixedHeight)
						} else {
							item.drawImage(this.images[index].url, 0, 0, this.screenInfo.width, this
								.screenInfo.width / ro)
						}
						item.draw()
					})
				}, 100)
			},
			// 选择图片
			chooseFile() {
				uni.chooseImage({
					extension: ['.png', '.jpg', '.jpeg'],
					sourceType: ['album'],
					sizeType: ['original'],
					complete: (e) => {
						console.log(e)
						if (e.errMsg == "chooseImage:fail cancel") { // 未选择图片

						} else {
							// 获取已经选择的文件对象
							this.currentImage = {
								url: e.tempFilePaths[0]
							}
							this.images.push(...e.tempFilePaths.map(file => ({
								url: file
							})))
							console.log('this.images.', this.images)
							this.currentImgInfo = []
							this.images.forEach(async item => {
								const res = await uni.getImageInfo({
									src: item.url
								})
								item.width = res.width // 原始图片宽度
								item.height = res.height // 原始图片高度
								if (Array.isArray(res)) {
									this.currentImgInfo.push(res[1])
								} else {
									this.currentImgInfo.push(res)
								}
								this.$nextTick(() => {
									this.loadImgToCanvas()
								})
							})
							console.log('this.currentImgInfo', this.currentImgInfo)


						}

					}
				})
			},
			// 在canvas上加标记
			async addMark(index) {
				if (index == 'time' || index == 'rotate' || index == 'draw' || index == 'clip' || index == 'edit' ||
					index == 'text') {
					if (this.images.length == 0) {
						uni.showToast({
							title: '请先选择图片',
							duration: 2000,
							icon: 'none'
						});
						return;
					}
				}
				// 设置
				if (index == 'time') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return;
					}
					if (!this.dateTimeInfo.hash) {
						timeCanvas[this.currentIndex].setFontSize(14)
						timeCanvas[this.currentIndex].setLineWidth(4)
						timeCanvas[this.currentIndex].setFillStyle('rgb(83, 83, 83)')
						timeCanvas[this.currentIndex].setStrokeStyle('white')
						// 当前时间
						let nowTime = getNowDateTime()
						this.dateTimeInfo.nowTime = nowTime
						this.dateTimeInfo.left = 5
						this.dateTimeInfo.top = this.canvasHeight[this.currentIndex] - 10
						timeCanvas[this.currentIndex].strokeText(nowTime, this.dateTimeInfo.left, this.dateTimeInfo
							.top, 120)
						timeCanvas[this.currentIndex].fillText(nowTime, this.dateTimeInfo.left, this.dateTimeInfo.top,
							120)
						this.dateTimeInfo.hash = true
						timeCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空
					} else {
						// 移除时间
						timeCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this
							.currentIndex])
						timeCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空
						this.dateTimeInfo.hash = false
					}
				}
				// 文字
				if (index == 'text') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return;
					}
					this.$refs.popup.open('top')
				}

				// 旋转
				if (index == 'rotate') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return
					}
					// 三个绘制图层上是否有内容
					let timeCanvas = {
						canvasId: "timeCanvas-" + this.currentIndex,
						x: 0,
						y: 0,
						width: this.screenInfo.width,
						height: this.canvasHeight[this.currentIndex],
					}
					let textCanvas = {
						canvasId: "textCanvas-" + this.currentIndex,
						x: 0,
						y: 0,
						width: this.screenInfo.width,
						height: this.canvasHeight[this.currentIndex],
					}
					let drawCanvas = {
						canvasId: "drawCanvas-" + this.currentIndex,
						x: 0,
						y: 0,
						width: this.screenInfo.width,
						height: this.canvasHeight[this.currentIndex],
					}
					let c1 = await canvasHasContent(timeCanvas, this)
					let c2 = await canvasHasContent(drawCanvas, this)
					let c3 = await canvasHasContent(textCanvas, this)
					if (c1 || c2 || c3) {
						uni.showModal({
							cancelText: '旋转',
							confirmText: '取消旋转',
							title: '旋转将清图片上的编辑内容',
							content: '是否继续？',
							success: (res) => {
								if (res.cancel) {
									// 继续旋转
									this.rotateClearOther()
								}
							}
						})
					} else {
						this.rotateClearOther()
					}
				}
				// 绘制涂鸦
				if (index == 'draw') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return
					}
					this.canDraw = !this.canDraw
					this.isClip = false
				}
				// 裁剪
				if (index == 'clip') {
					if (!this.isEdit) {
						uni.showToast({
							title: '请进入编辑模式',
							duration: 2000,
							icon: 'none'
						});
						return
					}
					this.isClip = !this.isClip
					// 绘制4个角的顶点
					if (this.isClip) {
						this.fourPointRange = []
						// 初始距离边缘的偏移
						let offset = 40
						this.fourPointRange.push({
							x1: 0 + offset,
							y1: 0 + offset,
							x2: this.radius * 2 + offset,
							y2: this.radius * 2 + offset
						})
						this.fourPointRange.push({
							x1: this.screenInfo.width - this.radius * 2 - offset,
							y1: 0 + offset,
							x2: this.screenInfo.width - offset,
							y2: this.radius * 2 + offset
						})
						this.fourPointRange.push({
							x1: 0 + offset,
							y1: this.canvasHeight[this.currentIndex] - this.radius * 2 - offset,
							x2: this.radius * 2 + offset,
							y2: this.canvasHeight[this.currentIndex] - offset
						})
						this.fourPointRange.push({
							x1: this.screenInfo.width - this.radius * 2 - offset,
							y1: this.canvasHeight[this.currentIndex] - this.radius * 2 - offset,
							x2: this.screenInfo.width - offset,
							y2: this.canvasHeight[this.currentIndex] - offset
						})

						this.drawFourPoint()

					}
				}
				// 编辑/保存
				if (index == 'edit') {
					this.isEdit = !this.isEdit
					if (this.isEdit) {
						return;
					}
					// #ifdef H5
					const canvasList = document.querySelectorAll(`#canvas-${this.currentIndex}>uni-canvas>canvas`);
					const newCanvas = document.createElement('canvas')
					newCanvas.width = this.screenInfo.width
					newCanvas.height = this.canvasHeight[this.currentIndex]
					const context = newCanvas.getContext('2d')
					canvasList.forEach(item => {
						if (item.getAttribute('width') == 0 || item.getAttribute('height') == 0);
						else {
							context.drawImage(item, 0, 0, this.screenInfo.width, this.canvasHeight[this
								.currentIndex])
						}
					})
					let base64 = newCanvas.toDataURL();
					// 将处理好的图片生成临时url
					let url = blobToUrl(base64toBlob(base64))
					this.images[this.currentIndex].url = url
					// 清空画布并关闭弹窗
					this.clearCanvas()
					// #endif
					// #ifdef MP-WEIXIN
					this.itemCanvasInfo.width = this.screenInfo.width
					this.itemCanvasInfo.height = this.canvasHeight[this.currentIndex]
					uni.showLoading({
						mask: true
					})
					// 将所有图层绘制到newCanvas中
					let canvasListIds = ['imgCanvas-' + this.currentIndex, 'drawCanvas-' + this.currentIndex,
						'timeCanvas-' + this.currentIndex, 'textCanvas-' + this.currentIndex
					]
					canvasListIds.forEach(async (i, index) => {
						let result = await wx.canvasToTempFilePath({
							canvasId: i,
						}, this)
						let url = result.tempFilePath
						console.log('this.itemCanvasInfo', this.itemCanvasInfo)
						newCanvas[this.currentIndex].drawImage(url, 0, 0, this.itemCanvasInfo.width, this
							.itemCanvasInfo.height)
						newCanvas[this.currentIndex].draw(true)
						console.log('newCanvas[this.currentIndex]', newCanvas[this.currentIndex])
						if (index == canvasListIds.length - 1) {
							let result = await wx.canvasToTempFilePath({
								canvasId: 'newCanvas-' + this.currentIndex,
							}, this)
							let urlr = result.tempFilePath
							this.images[this.currentIndex].url = urlr
							// 清空画布并关闭弹窗
							this.clearCanvasContent(true)
							this.clearCanvas()
							uni.hideLoading()
						}
					})
					// #endif

				}

			},
			// 确认文字
			confirmText() {
				// 移除时间
				textCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this
					.currentIndex])
				textCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空

				textCanvas[this.currentIndex].setFontSize(20)
				textCanvas[this.currentIndex].setLineWidth(4)
				textCanvas[this.currentIndex].setFillStyle(this.textInfo.color)
				textCanvas[this.currentIndex].setStrokeStyle('white')
				this.textInfo.left = 30
				this.textInfo.top = 50
				textCanvas[this.currentIndex].strokeText(this.textInfo.text, this.textInfo.left, this.textInfo
					.top, 120)
				textCanvas[this.currentIndex].fillText(this.textInfo.text, this.textInfo.left, this.textInfo.top,
					120)
				textCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空
				this.$refs.popup.close()
			},

			// 绘制4个顶点
			drawFourPoint() {
				clipCanvas[this.currentIndex].setFillStyle('rgba(0,0,0,0.6)')
				clipCanvas[this.currentIndex].fillRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				clipCanvas[this.currentIndex].clearRect(this.fourPointRange[0].x1, this.fourPointRange[0].y1, this
					.fourPointRange[1].x2 - this
					.fourPointRange[0].x1, this.fourPointRange[2].y2 - this.fourPointRange[0].y1)
				clipCanvas[this.currentIndex].setStrokeStyle('gray')
				clipCanvas[this.currentIndex].setFillStyle('white')
				this.fourPointRange.forEach((i, index) => {
					clipCanvas[this.currentIndex].beginPath()
					clipCanvas[this.currentIndex].arc(i.x1 + (i.x2 - i.x1) / 2, i.y1 + (i.y2 - i.y1) / 2, this
						.radius, 0, 360)
					clipCanvas[this.currentIndex].fill()
					clipCanvas[this.currentIndex].beginPath()
					clipCanvas[this.currentIndex].arc(i.x1 + (i.x2 - i.x1) / 2, i.y1 + (i.y2 - i.y1) / 2, this
						.radius, 0, 360)
					clipCanvas[this.currentIndex].stroke()
				})
				clipCanvas[this.currentIndex].draw()
				this.isClip = true
				this.canDraw = false
				let clipW = this.fourPointRange[1].x2 - this.fourPointRange[0].x1
				let clipH = this.fourPointRange[2].y2 - this.fourPointRange[0].y1
				this.itemCanvasInfo.width = 200
				this.itemCanvasInfo.height = 200
			},
			// 旋转图片，清除其他层上的内容
			async rotateClearOther() {
				uni.showLoading({
					mask: true
				})
				this.rotateDeg += 90
				// 清除
				this.clearCanvasContent()
				this.canvasHeight[this.currentIndex] = (this.screenInfo.width / this.canvasHeight[this.currentIndex]) *
					this.screenInfo.width
				setTimeout(async () => {
					let imgurl = await urlImgRotate(this.currentImage.url, this.rotateDeg, this)
					console.log('imgurl', imgurl)
					uni.getImageInfo({
						src: imgurl
					}).then(res => {
						console.log('===========返回图片宽高');
						console.log(res);
					})
					imgCanvas[this.currentIndex].drawImage(imgurl, 0, 0,
						this.screenInfo.width,
						this.canvasHeight[this.currentIndex])
					imgCanvas[this.currentIndex].draw(false, async () => {
						this.dateTimeInfo.hash = false
						uni.hideLoading()
					})
				})
			},
			// 清空画布内容
			clearCanvasContent(isNewCanvas) {
				// #ifdef MP-WEIXIN
				if (!isNewCanvas) {
					newCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this
						.currentIndex])
					newCanvas[this.currentIndex].draw()
				}
				// #endif
				imgCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				timeCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				textCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				drawCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				clipCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				itemCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				imgCanvas[this.currentIndex].draw()
				timeCanvas[this.currentIndex].draw()
				textCanvas[this.currentIndex].draw()
				drawCanvas[this.currentIndex].draw()
				clipCanvas[this.currentIndex].draw()
				itemCanvas[this.currentIndex].draw()
			},
			/**
			 * 清空画布并关闭弹窗
			 */
			clearCanvas() {
				this.canDraw = false
				this.dateTimeInfo.hash = false
				this.textInfo.text = ''
				this.rotateDeg = 0
			},
			// 删除
			exit() {
				uni.showModal({
					content: '是否删除图片？',
					confirmText: '删除',
					title: '操作将删除此图片',
					cancelText: '关闭',
					success: (e) => {
						if (e.confirm) {
							this.clearCanvas()
							this.clearCanvasContent()
							this.images.splice(this.currentIndex, 1);
							this.currentIndex = 0
						}
					}
				})
			},

			// 手指开始接触
			touchstart(e) {
				// 阻止默认事件和冒泡
				if (e.preventDefault) {
					e.preventDefault();
				}
				if (e.stopPropagation) {
					e.stopPropagation();
				}
				this.currentTouchPoint = e.touches[0]
				this.timeOffset.left = e.touches[0].x - this.dateTimeInfo.left
				this.timeOffset.top = e.touches[0].y - this.dateTimeInfo.top
				this.textOffset.left = e.touches[0].x - this.textInfo.left
				this.textOffset.top = e.touches[0].y - this.textInfo.top
				if (this.canDraw) {
					drawCanvas[this.currentIndex].moveTo(e.touches[0].x, e.touches[0].y)
				}

				this.draggingCorner = this.getCornerByTouch(this.currentTouchPoint, this.fourPointRange);
				// 如果触摸了某个角点
				if (this.draggingCorner) {
					this.isMovePoint = true;
					this.startX = this.currentTouchPoint.x;
					this.startY = this.currentTouchPoint.y;
				}
			},
			// 接触结束
			touchend(e) {
				this.textOffset.left = 0
				this.textOffset.top = 0
				this.timeOffset.left = 0
				this.timeOffset.top = 0
				this.oOffset = 0
				// 重置移动状态
				this.isMovePoint = false;
				this.draggingCorner = null;
			},
			/**
			 * c是否在一个矩形区域t内
			 * @param {x,y} c
			 * @param {x1,y1,x2,y2} t
			 */
			isMoveIn(c, t) {
				if (c.x > t.x1 && c.x < t.x2 && c.y > t.y1 && c.y < t.y2) {
					return true
				}
				return false
			},
			// 接触后移动
			touchmove(e) {
				this.touches = e.touches
				// 判断是否要移动元素
				if (this.touches.length == 1) {

					// 移动时间
					// 判断在手指是否时间元素内
					if (!this.canDraw && this.dateTimeInfo.hash && this.isMoveIn({
							x: this.touches[0].x,
							y: this.touches[0].y
						}, {
							x1: this.dateTimeInfo.left,
							y1: this.dateTimeInfo.top - 15,
							x2: this.dateTimeInfo.left + 120,
							y2: this.dateTimeInfo.top + 5,
						})) {
						this.moveTime = true
						if (sto) {
							clearTimeout(sto)
						}
						sto = setTimeout(() => {
							this.moveTime = false
							clearTimeout(sto)
							sto = null
						}, 200)

						let l = this.touches[0].x - this.timeOffset.left
						// 碰到左边缘
						if (l < 0) {
							this.dateTimeInfo.left = 0
						}
						// 碰到右边缘
						else if (l + 120 > this.screenInfo.width) {
							this.dateTimeInfo.left = this.screenInfo.width - 120
						} else {
							this.dateTimeInfo.left = l
						}
						let t = this.touches[0].y - this.timeOffset.top
						// 碰到上边缘
						if (t < 10) {
							this.dateTimeInfo.top = 10
						} else if (t > this.canvasHeight[this.currentIndex] - 10) {
							// 碰到下边缘
							this.dateTimeInfo.top = this.canvasHeight[this.currentIndex] - 10
						} else {
							this.dateTimeInfo.top = t
						}
						this.reDrawTime()
					}

					// 移动文字
					// 判断在手指是否在文字元素内
					if (!this.canDraw && this.isMoveIn({
							x: this.touches[0].x,
							y: this.touches[0].y
						}, {
							x1: this.textInfo.left,
							y1: this.textInfo.top - 15,
							x2: this.textInfo.left + 120,
							y2: this.textInfo.top + 5,
						})) {
						this.moveText = true
						if (sto) {
							clearTimeout(sto)
						}
						sto = setTimeout(() => {
							this.moveText = false
							clearTimeout(sto)
							sto = null
						}, 200)

						let l = this.touches[0].x - this.textOffset.left
						// 碰到左边缘
						if (l < 0) {
							this.textInfo.left = 0
						}
						// 碰到右边缘
						else if (l + 120 > this.screenInfo.width) {
							this.textInfo.left = this.screenInfo.width - 120
						} else {
							this.textInfo.left = l
						}
						let t = this.touches[0].y - this.textOffset.top
						// 碰到上边缘
						if (t < 10) {
							this.textInfo.top = 10
						} else if (t > this.canvasHeight[this.currentIndex] - 10) {
							// 碰到下边缘
							this.textInfo.top = this.canvasHeight[this.currentIndex] - 10
						} else {
							this.textInfo.top = t
						}
						this.reDrawText()
					}

					// 涂鸦
					if (this.canDraw) {
						if (this.strokeInfo.color != 'clear') {
							drawCanvas[this.currentIndex].setStrokeStyle(this.strokeInfo.color)
							drawCanvas[this.currentIndex].setLineCap('round')
							drawCanvas[this.currentIndex].setLineWidth(this.strokeInfo.weight)
							drawCanvas[this.currentIndex].lineTo(this.touches[0].x, this.touches[0].y)
							drawCanvas[this.currentIndex].stroke()
							drawCanvas[this.currentIndex].draw(true)
							drawCanvas[this.currentIndex].moveTo(this.touches[0].x, this.touches[0].y)
						} else {
							//擦除
							drawCanvas[this.currentIndex].clearRect(this.touches[0].x - this.strokeInfo.weight / 2, this
								.touches[0].y - this
								.strokeInfo.weight / 2, this.strokeInfo.weight, this.strokeInfo.weight)
							drawCanvas[this.currentIndex].draw(true)
						}
					}
					// 移动裁剪顶点
					if (this.isClip) {
						// 如果正在移动角点，则更新位置
						if (this.isMovePoint) {
							this.moveCorner(e.touches);
						}
						// 不在移动点且在 框选范围内
						if (!this.isMovePoint && this.isMoveIn({
								x: this.touches[0].x,
								y: this.touches[0].y
							}, {
								x1: this.fourPointRange[0].x1,
								y1: this.fourPointRange[0].y1,
								x2: this.fourPointRange[3].x2,
								y2: this.fourPointRange[3].y2,
							})) {
							// 在框选范围内
							// 移动选框时禁止滚动
							this.moveTime = true
							this.moveText = true
							if (sto) {
								clearTimeout(sto)
							}
							sto = setTimeout(() => {
								this.moveTime = false
								this.moveText = false
								clearTimeout(sto)
								sto = null
							}, 200)
							// 计算现在的点和原来的点的变化
							let x = this.touches[0].x - this.currentTouchPoint.x
							let y = this.touches[0].y - this.currentTouchPoint.y
							// 检测是否边缘
							// if (x < 0) {
							//左移
							if (x < 0 && this.currentTouchPoint.x - this.fourPointRange[0].x1 >= this.currentTouchPoint
								.x) {
								x = 0
							}
							//右移
							if (x > 0 && this.fourPointRange[1].x2 - this.currentTouchPoint.x >= this.screenInfo.width -
								this.currentTouchPoint.x) {
								x = 0
							}
							// 上移
							if (y < 0 && this.currentTouchPoint.y - this.fourPointRange[0].y1 >= this.currentTouchPoint
								.y) {
								y = 0
							}
							// 下移
							if (y > 0 && this.fourPointRange[2].y2 - this.currentTouchPoint.y >= this.canvasHeight[this
									.currentIndex] -
								this.currentTouchPoint.y) {
								y = 0
							}

							// 重新计算四点的位置
							this.fourPointRange.forEach(i => {
								i.x1 += x
								i.x2 += x
								i.y1 += y
								i.y2 += y
							})
							this.currentTouchPoint.x = this.touches[0].x
							this.currentTouchPoint.y = this.touches[0].y
							// 重绘四点
							this.drawFourPoint()

						}
					}
				}
				// 双指作，进行缩放
				// if (this.touches.length == 2) {
				// 	// 计算两点之间的初始距离
				// 	let x = this.touches[0].x - this.touches[1].x
				// 	let y = this.touches[0].y - this.touches[1].y
				// 	if (this.oOffset == 0) {
				// 		this.oOffset = Math.sqrt(x * x + y * y)
				// 	} else {
				// 		// 计算扩大倍数
				// 		this.scaleCanvas = Math.sqrt(x * x + y * y) / this.oOffset

				// 	}
				// }
			},
			// 根据触摸点获取角点信息
			getCornerByTouch(touch, corners) {
				const touchX = touch.x;
				const touchY = touch.y;
				const tolerance = 10; // 容忍度，可根据实际需要调整

				for (let i = 0; i < corners.length; i++) {
					const corner = corners[i];
					const centerX = corner.x1 + (corner.x2 - corner.x1) / 2;
					const centerY = corner.y1 + (corner.y2 - corner.y1) / 2;
					// 检查触摸点是否在角点的区域内
					if (Math.abs(touchX - centerX) < tolerance && Math.abs(touchY - centerY) < tolerance) {
						return {
							...corner,
							index: i
						}; // 返回角点信息及索引
					}
				}

				return null; // 如果没有角点被触摸，则返回null
			},
			// 移动角点
			moveCorner(touches) {
				if (this.isMovePoint) {
					const touch = touches[0];
					// 更新角点的位置
					const dx = touch.x - this.startX;
					const dy = touch.y - this.startY;
					let new_x = this.draggingCorner.x1 + dx;
					let new_y = this.draggingCorner.y1 + dy;
					// 边界检查
					new_x = Math.max(this.radius, Math.min(this.screenInfo.width - this.radius, new_x));
					new_y = Math.max(this.radius, Math.min(this.canvasHeight[this.currentIndex] - this.radius, new_y));
					this.reComPointPos(new_x, new_y, this.draggingCorner.index)
					// 重绘裁剪框
					this.drawFourPoint();
				}
			},
			// 重新计算各点的位置
			reComPointPos(x, y, index) {
				// 0: 0 1 2
				// 1: 1 0 2 
				// 2: 2 0 3
				// 3: 3 1 2

				if (index == 0) {
					if (x >= this.fourPointRange[1].x1 - this.radius * 2) {
						x = this.fourPointRange[1].x1 - this.radius * 2
					}
					if (y >= this.fourPointRange[2].y1 - this.radius * 2) {
						y = this.fourPointRange[2].y1 - this.radius * 2
					}
				}
				if (index == 1) {
					if (x <= this.fourPointRange[0].x2 + this.radius * 2) {
						x = this.fourPointRange[0].x2 + this.radius * 2
					}
					if (y >= this.fourPointRange[2].y1 - this.radius * 2) {
						y = this.fourPointRange[2].y1 - this.radius * 2
					}
				}
				if (index == 2) {
					if (x >= this.fourPointRange[1].x1 - this.radius * 2) {
						x = this.fourPointRange[1].x1 - this.radius * 2
					}
					if (y <= this.fourPointRange[0].y2 + this.radius * 2) {
						y = this.fourPointRange[0].y2 + this.radius * 2
					}
				}
				if (index == 3) {
					if (x <= this.fourPointRange[0].x2 + this.radius * 2) {
						x = this.fourPointRange[0].x2 + this.radius * 2
					}
					if (y <= this.fourPointRange[0].y2 + this.radius * 2) {
						y = this.fourPointRange[0].y2 + this.radius * 2
					}
				}
				this.fourPointRange[index] = {
					x1: x - this.radius,
					y1: y - this.radius,
					x2: x + this.radius,
					y2: y + this.radius,
				}
				if (index == 0) {
					this.fourPointRange[1].y1 = this.fourPointRange[index].y1
					this.fourPointRange[1].y2 = this.fourPointRange[index].y2
					this.fourPointRange[2].x1 = this.fourPointRange[index].x1
					this.fourPointRange[2].x2 = this.fourPointRange[index].x2
				}
				if (index == 1) {
					this.fourPointRange[0].y1 = this.fourPointRange[index].y1
					this.fourPointRange[0].y2 = this.fourPointRange[index].y2
					this.fourPointRange[3].x1 = this.fourPointRange[index].x1
					this.fourPointRange[3].x2 = this.fourPointRange[index].x2
				}
				if (index == 2) {
					this.fourPointRange[3].y1 = this.fourPointRange[index].y1
					this.fourPointRange[3].y2 = this.fourPointRange[index].y2
					this.fourPointRange[0].x1 = this.fourPointRange[index].x1
					this.fourPointRange[0].x2 = this.fourPointRange[index].x2
				}
				if (index == 3) {
					this.fourPointRange[2].y1 = this.fourPointRange[index].y1
					this.fourPointRange[2].y2 = this.fourPointRange[index].y2
					this.fourPointRange[1].x1 = this.fourPointRange[index].x1
					this.fourPointRange[1].x2 = this.fourPointRange[index].x2
				}

			},
			// 重新绘制时间
			reDrawTime() {
				// 当前时间
				timeCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				timeCanvas[this.currentIndex].strokeText(this.dateTimeInfo.nowTime, this.dateTimeInfo.left, this
					.dateTimeInfo.top, 120)
				timeCanvas[this.currentIndex].fillText(this.dateTimeInfo.nowTime, this.dateTimeInfo.left, this.dateTimeInfo
					.top, 120)
				timeCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空
			},
			// 重新绘制文字
			reDrawText() {
				textCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
				textCanvas[this.currentIndex].setFontSize(20)
				textCanvas[this.currentIndex].setLineWidth(4)
				textCanvas[this.currentIndex].setFillStyle(this.textInfo.color)
				textCanvas[this.currentIndex].setStrokeStyle('white')
				textCanvas[this.currentIndex].strokeText(this.textInfo.text, this.textInfo.left, this.textInfo.top, 120)
				textCanvas[this.currentIndex].fillText(this.textInfo.text, this.textInfo.left, this.textInfo.top, 120)
				textCanvas[this.currentIndex].draw(true) //默认false，false会将之前的清空
			},
			// 裁剪后重新绘制
			async afterClipDraw(url) {
				if (!url) {
					console.error('Invalid image URL')
					uni.showToast({
						title: '图片处理失败',
						icon: 'none'
					})
					return
				}

				uni.showLoading({
					title: '处理中...',
					mask: true
				})

				try {
					const imageInfo = await new Promise((resolve, reject) => {
						uni.getImageInfo({
							src: url,
							success: res => resolve(res),
							fail: err => reject(err)
						})
					})

					// 计算新的画布高度并保持宽高比
					const imageW = imageInfo.width
					const imageH = imageInfo.height
					const newHeight = (imageH / imageW) * this.screenInfo.width
					this.canvasHeight[this.currentIndex] = newHeight

					// 更新图片URL
					this.currentImage.url = url
					this.images[this.currentIndex].url = url

					// 重新绘制到画布
					await this.$nextTick()
					const ctx = uni.createCanvasContext('imgCanvas-' + this.currentIndex, this)
					
					// 确保画布是干净的
					ctx.clearRect(0, 0, this.screenInfo.width, newHeight)
					
					// 使用白色背景（如果需要）
					ctx.fillStyle = '#FFFFFF'
					ctx.fillRect(0, 0, this.screenInfo.width, newHeight)
					
					// 绘制图片
					ctx.drawImage(url, 0, 0, this.screenInfo.width, newHeight)
					
					await new Promise(resolve => {
						ctx.draw(false, () => {
							setTimeout(resolve, 100)
						})
					})

					// 重置状态
					this.dateTimeInfo.hash = false
					this.itemCanvasInfo.width = 0
					this.itemCanvasInfo.height = 0

				} catch (error) {
					console.error('图片处理失败：', error)
					uni.showToast({
						title: '图片处理失败',
						icon: 'none'
					})
				} finally {
					uni.hideLoading()
				}
			},
			// 每一张itemCanvas画布内容绘制到一个里newCanvas
			itemsToNewH5(context, newCanvas, index) {
				let itemCanvast = this.$refs[`itemCanvas-${this.currentIndex}`][0].$el
				let c = itemCanvast.children[0]
				this.currentImage.url = c
				uni.showLoading({
					mask: true
				})
				setTimeout(() => {
					uni.hideLoading()
					context.drawImage(c, 0, 0, newCanvas.width, newCanvas.height)
					if (index == 2) {
						let base64 = newCanvas.toDataURL();
						// 将处理好的图片生成临时url
						let url = blobToUrl(base64toBlob(base64))
						this.images[this.currentIndex].url = url
						// 重新绘制图像到画布，重置其他所有画布
						this.afterClipDraw(url)
					}
				}, 100)
			},
			itemsToNewWX(index) {
				uni.canvasToTempFilePath({
					canvasId: 'itemCanvas-' + this.currentIndex,
					success: res => {
						newCanvas[this.currentIndex].drawImage(res.tempFilePath, 0, 0, this.itemCanvasInfo
							.width, this.itemCanvasInfo.height)
						newCanvas[this.currentIndex].draw(true)

						if (index == 2) {
							uni.canvasToTempFilePath({
								canvasId: 'newCanvas-' + this.currentIndex,
							}, this).then(res => {
								if (Array.isArray(res)) {
									res = res[1]
								} else {
									res = res
								}
								this.clearCanvasContent()
								// 重新绘制图像到画布，重置其他所有画布
								this.afterClipDraw(res.tempFilePath)
							})
						}

					},
					fail: err => {
						console.log(err);
					}
				}, this)
			},
			// 保留裁剪
			async saveClip() {
				try {
					// 获取裁剪区域的宽高
					const clipWidth = this.fourPointRange[1].x2 - this.fourPointRange[0].x1
					const clipHeight = this.fourPointRange[2].y2 - this.fourPointRange[0].y1
					
					// #ifdef MP-WEIXIN
					// 微信小程序环境下的裁剪处理
					const canvasIds = ['imgCanvas', 'drawCanvas', 'timeCanvas', 'textCanvas']
					
					// 清空临时画布
					const tempCtx = uni.createCanvasContext('itemCanvas-' + this.currentIndex, this)
					tempCtx.clearRect(0, 0, clipWidth, clipHeight)
					tempCtx.draw(true)
					
					// 创建临时画布并绘制所有图层
					for (let i = 0; i < canvasIds.length; i++) {
						const canvasId = canvasIds[i] + '-' + this.currentIndex
						
						await new Promise((resolve) => {
							uni.canvasToTempFilePath({
								canvasId,
								x: this.fourPointRange[0].x1,
								y: this.fourPointRange[0].y1,
								width: clipWidth,
								height: clipHeight,
								success: res => {
									const ctx = uni.createCanvasContext('itemCanvas-' + this.currentIndex, this)
									ctx.drawImage(res.tempFilePath, 0, 0, clipWidth, clipHeight)
									ctx.draw(true, resolve)
								},
								fail: err => {
									console.error(`Failed to get layer ${canvasId}:`, err)
									resolve()
								}
							}, this)
						})
					}

					// 导出裁剪后的图片
					const result = await new Promise((resolve, reject) => {
						uni.canvasToTempFilePath({
							canvasId: 'itemCanvas-' + this.currentIndex,
							width: clipWidth,
							height: clipHeight,
							destWidth: clipWidth,
							destHeight: clipHeight,
							success: res => resolve(res),
							fail: err => reject(err)
						}, this)
					})

					if (!result || !result.tempFilePath) {
						throw new Error('Failed to get temp file path')
					}

					// 清除裁剪框
					this.isClip = false
					clipCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
					clipCanvas[this.currentIndex].draw()

					// 处理裁剪后的图片
					await this.afterClipDraw(result.tempFilePath)
					// #endif

					// #ifdef H5
					// H5环境下的裁剪处理
					const canvasList = document.querySelectorAll(`#canvas-${this.currentIndex}>uni-canvas>canvas`)
					if (!canvasList || canvasList.length === 0) {
						throw new Error('Canvas elements not found')
					}

					// 创建临时画布
					const tempCanvas = document.createElement('canvas')
					tempCanvas.width = clipWidth
					tempCanvas.height = clipHeight
					const ctx = tempCanvas.getContext('2d')
					
					// 确保背景是透明的
					ctx.clearRect(0, 0, clipWidth, clipHeight)
					
					// 获取 canvas 的实际显示尺寸和原始尺寸比例
					const firstCanvas = canvasList[0]
					const rect = firstCanvas.getBoundingClientRect()
					const scaleX = firstCanvas.width / rect.width
					const scaleY = firstCanvas.height / rect.height

					// 计算实际裁剪位置
					const actualX = this.fourPointRange[0].x1 * scaleX
					const actualY = this.fourPointRange[0].y1 * scaleY
					const actualWidth = clipWidth * scaleX
					const actualHeight = clipHeight * scaleY

					// 按顺序绘制所有图层
					for (const canvas of canvasList) {
						// 跳过空画布
						if (canvas.width === 0 || canvas.height === 0) continue
						
						try {
							ctx.drawImage(
								canvas,
								actualX,
								actualY,
								actualWidth,
								actualHeight,
								0,
								0,
								clipWidth,
								clipHeight
							)
						} catch (error) {
							console.error('Canvas drawing error:', error)
						}
					}

					// 清除裁剪框
					this.isClip = false
					clipCanvas[this.currentIndex].clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
					clipCanvas[this.currentIndex].draw()

					// 转换为base64并处理
					const base64 = tempCanvas.toDataURL('image/png')
					await this.afterClipDraw(base64)
					// #endif

				} catch (error) {
					console.error('裁剪保存失败：', error)
					uni.showToast({
						title: '裁剪失败，请重试',
						icon: 'none'
					})
				}
			},
			// 添加一个方法用于清除所有画布内容
			clearCanvasContent(keepMainCanvas = false) {
				const canvasList = ['timeCanvas', 'textCanvas', 'drawCanvas', 'clipCanvas']
				canvasList.forEach(type => {
					if (!keepMainCanvas || type !== 'imgCanvas') {
						const canvas = this[type][this.currentIndex]
						if (canvas) {
							canvas.clearRect(0, 0, this.screenInfo.width, this.canvasHeight[this.currentIndex])
							canvas.draw()
						}
					}
				})
			}
		},
		mounted() {
			uni.getSystemInfo().then(res => {
				if (Array.isArray(res)) {
					this.screenInfo.width = res[1].windowWidth
					this.screenInfo.height = res[1].windowHeight
				} else {
					this.screenInfo.width = res.windowWidth
					this.screenInfo.height = res.windowHeight
				}
			})
		},
		created() {
			newCanvas = []
			imgCanvas = []
			timeCanvas = []
			textCanvas = []
			drawCanvas = []
			clipCanvas = []
			itemCanvas = []
		}
	}
</script>


<style>
	@import url('index.css');
</style>
<style>
	.mini-btn {
		position: fixed;
		z-index: 99999;
		background-color: aqua;
		left: calc(50% - 112rpx);
		bottom: 20vh;
		padding: 20rpx 30rpx;
		font-size: 30rpx;
		color: #fff;
		/* 文字颜色 */
		background-color: #ff2600;
		/* 背景颜色 */
		border: none;
		/* 无边框 */
		border-radius: 5px;
		/* 圆角边框 */
		cursor: pointer;
		/* 鼠标悬停时显示指针 */
		text-align: center;
		text-decoration: none;
		outline: none;
		/* 点击时无轮廓线 */
		transition: background-color 0.3s;
		/* 背景色渐变效果 */
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
		/* 添加阴影效果 */
	}

	.mini-btn:hover {
		background-color: #ff4723;
		/* 鼠标悬停时的背景颜色 */
	}

	.mini-btn:active {
		background-color:#550900;
		/* 鼠标点击时的背景颜色 */
	}
</style>