<template>
  <view class="job-match-page">
    <!-- 顶部匹配分析 -->
    <view class="match-analysis">
      <view class="analysis-header">
        <text class="title">智能匹配分析</text>
        <text class="subtitle">基于您的简历信息，为您推荐最适合的工作</text>
      </view>
      
      <view class="match-stats">
        <view class="stat-item">
          <text class="number">{{ matchStats.totalJobs }}</text>
          <text class="label">匹配职位</text>
        </view>
        <view class="stat-item">
          <text class="number">{{ matchStats.highMatch }}%</text>
          <text class="label">较高匹配度</text>
        </view>
        <view class="stat-item">
          <text class="number">{{ matchStats.salary }}</text>
          <text class="label">平均薪资</text>
        </view>
      </view>
      
      <view class="match-chart">
        <view class="chart-title">匹配度分布</view>
        <view class="chart-bars">
          <view 
            v-for="(item, index) in matchDistribution" 
            :key="index"
            class="bar-item"
          >
            <view 
              class="bar" 
              :style="{ height: item.percentage + '%' }"
              :class="item.type"
            ></view>
            <text class="bar-label">{{ item.label }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 标签筛选 -->
    <view class="filter-section">
      <scroll-view scroll-x class="filter-scroll" show-scrollbar="false">
        <view class="filter-tags">
          <view 
            v-for="(tag, index) in filterTags" 
            :key="index"
            class="tag"
            :class="{ active: tag.active }"
            @tap="toggleFilter(index)"
          >
            {{ tag.name }}
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 推荐职位列表 -->
    <view class="job-list">
      <view 
        v-for="(job, index) in matchedJobsWithClass" 
        :key="index"
        class="job-card"
        @tap="viewJobDetail(job.id)"
      >
        <view class="job-header">
          <view class="job-info">
            <text class="job-title">{{ job.title }}</text>
            <text class="company-name">{{ job.company }}</text>
          </view>
          <view class="match-score" :class="job.scoreClass">
            {{ job.matchScore }}%
            <text class="match-label">匹配度</text>
          </view>
        </view>
        
        <view class="job-tags">
          <text class="tag" v-for="(tag, tagIndex) in job.tags" :key="tagIndex">
            {{ tag }}
          </text>
        </view>
        
        <view class="match-details">
          <view class="match-item" v-for="(item, itemIndex) in job.matchDetailsWithClass" :key="itemIndex">
            <text class="match-type">{{ item.type }}</text>
            <view class="progress-bar">
              <view 
                class="progress" 
                :style="{ width: item.score + '%' }"
                :class="item.progressClass"
              ></view>
            </view>
            <text class="match-percent">{{ item.score }}%</text>
          </view>
        </view>
        
        <view class="job-footer">
          <view class="salary">{{ job.salary }}</view>
          <view class="location">{{ job.location }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      matchStats: {
        totalJobs: 0,
        highMatch: 0,
        salary: '0'
      },
      matchDistribution: [
        { type: 'very-high', label: '90%+', percentage: 0 },
        { type: 'high', label: '80-89%', percentage: 0 },
        { type: 'medium', label: '60-79%', percentage: 0 },
        { type: 'low', label: '60%以下', percentage: 0 }
      ],
      filterTags: [
        { name: '全部', active: true },
        { name: '匹配度优先', active: false },
        { name: '薪资优先', active: false },
        { name: '距离优先', active: false },
        { name: '最新发布', active: false }
      ],
      matchedJobs: []
    }
  },
  
  onLoad() {
    this.loadUserProfile()
    this.fetchMatchedJobs()
  },
  
  computed: {
    matchedJobsWithClass() {
      return this.matchedJobs.map(job => ({
        ...job,
        scoreClass: this.getScoreClass(job.matchScore),
        matchDetailsWithClass: job.matchDetails.map(detail => ({
          ...detail,
          progressClass: this.getProgressClass(detail.score)
        }))
      }))
    }
  },
  
  methods: {
    async loadUserProfile() {
      try {
        // TODO: 从后端获取用户简历信息
        const userProfile = await this.$api.getUserProfile()
        // 处理用户简历数据
      } catch (error) {
        uni.showToast({
          title: '获取简历信息失败',
          icon: 'none'
        })
      }
    },
    
    async fetchMatchedJobs() {
      try {
        // TODO: 调用后端接口获取匹配的工作
        const result = await this.$api.getMatchedJobs()
        
        // 示例数据
        this.matchStats = {
          totalJobs: 128,
          highMatch: 85,
          salary: '15k-25k'
        }
        
        this.matchDistribution = [
          { type: 'very-high', label: '90%+', percentage: 30 },
          { type: 'high', label: '80-89%', percentage: 45 },
          { type: 'medium', label: '60-79%', percentage: 20 },
          { type: 'low', label: '60%以下', percentage: 5 }
        ]
        
        this.matchedJobs = [
          {
            id: 1,
            title: '高级前端开发工程师',
            company: '科技有限公司',
            matchScore: 95,
            tags: ['3-5年', '本科', '全职'],
            matchDetails: [
              { type: '技能匹配', score: 95 },
              { type: '经验匹配', score: 90 },
              { type: '薪资匹配', score: 85 }
            ],
            salary: '15k-25k',
            location: '上海'
          },
          // 更多职位数据...
        ]
      } catch (error) {
        uni.showToast({
          title: '获取匹配职位失败',
          icon: 'none'
        })
      }
    },
    
    toggleFilter(index) {
      this.filterTags.forEach((tag, i) => {
        tag.active = i === index
      })
      this.fetchMatchedJobs() // 重新获取筛选后的数据
    },
    
    getScoreClass(score) {
      if (score >= 90) return 'score-excellent'
      if (score >= 80) return 'score-good'
      if (score >= 60) return 'score-medium'
      return 'score-low'
    },
    
    getProgressClass(score) {
      if (score >= 90) return 'progress-excellent'
      if (score >= 80) return 'progress-good'
      if (score >= 60) return 'progress-medium'
      return 'progress-low'
    },
    
    viewJobDetail(jobId) {
      uni.navigateTo({
        url: `/pagesExa/zhaopin/partdetails?id=${jobId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.job-match-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: env(safe-area-inset-bottom);
  
  .match-analysis {
    background: linear-gradient(135deg, #007AFF, #1E90FF);
    padding: 40rpx 30rpx;
    color: #ffffff;
    
    .analysis-header {
      margin-bottom: 30rpx;
      
      .title {
        font-size: 36rpx;
        font-weight: bold;
      }
      
      .subtitle {
        font-size: 26rpx;
        opacity: 0.9;
        margin-top: 10rpx;
      }
    }
    
    .match-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 40rpx;
      
      .stat-item {
        text-align: center;
        
        .number {
          font-size: 40rpx;
          font-weight: bold;
          margin-bottom: 8rpx;
          display: block;
        }
        
        .label {
          font-size: 24rpx;
          opacity: 0.9;
        }
      }
    }
    
    .match-chart {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12rpx;
      padding: 20rpx;
      
      .chart-title {
        font-size: 28rpx;
        margin-bottom: 20rpx;
      }
      
      .chart-bars {
        display: flex;
        justify-content: space-between;
        height: 200rpx;
        align-items: flex-end;
        
        .bar-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .bar {
            width: 40rpx;
            border-radius: 20rpx;
            transition: height 0.3s ease;
            
            &.very-high { background-color: #52c41a; }
            &.high { background-color: #1890ff; }
            &.medium { background-color: #faad14; }
            &.low { background-color: #ff4d4f; }
          }
          
          .bar-label {
            font-size: 22rpx;
            margin-top: 10rpx;
            opacity: 0.9;
          }
        }
      }
    }
  }
  
  .filter-section {
    background-color: #ffffff;
    padding: 20rpx 0;
    
    .filter-scroll {
      white-space: nowrap;
      
      .filter-tags {
        padding: 0 20rpx;
        display: inline-flex;
        
        .tag {
          display: inline-block;
          padding: 12rpx 30rpx;
          font-size: 28rpx;
          color: #666;
          background-color: #f5f5f5;
          border-radius: 32rpx;
          margin-right: 20rpx;
          transition: all 0.3s ease;
          
          &.active {
            background-color: #007AFF;
            color: #ffffff;
          }
          
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
  
  .job-list {
    padding: 20rpx;
    
    .job-card {
      background-color: #ffffff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
      
      .job-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20rpx;
        
        .job-info {
          flex: 1;
          margin-right: 20rpx;
          
          .job-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 8rpx;
            display: block;
          }
          
          .company-name {
            font-size: 26rpx;
            color: #666;
          }
        }
        
        .match-score {
          text-align: center;
          font-size: 36rpx;
          font-weight: bold;
          
          &.score-excellent { color: #52c41a; }
          &.score-good { color: #1890ff; }
          &.score-medium { color: #faad14; }
          &.score-low { color: #ff4d4f; }
          
          .match-label {
            display: block;
            font-size: 22rpx;
            font-weight: normal;
            color: #999;
            margin-top: 4rpx;
          }
        }
      }
      
      .job-tags {
        margin-bottom: 20rpx;
        
        .tag {
          display: inline-block;
          padding: 6rpx 16rpx;
          font-size: 24rpx;
          color: #666;
          background-color: #f5f5f5;
          border-radius: 6rpx;
          margin-right: 16rpx;
          margin-bottom: 12rpx;
          
          &:last-child {
            margin-right: 0;
          }
        }
      }
      
      .match-details {
        background-color: #f8f8f8;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
        
        .match-item {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .match-type {
            width: 140rpx;
            font-size: 26rpx;
            color: #666;
          }
          
          .progress-bar {
            flex: 1;
            height: 16rpx;
            background-color: #eee;
            border-radius: 8rpx;
            margin: 0 20rpx;
            overflow: hidden;
            
            .progress {
              height: 100%;
              border-radius: 8rpx;
              transition: width 0.3s ease;
              
              &.progress-excellent { background-color: #52c41a; }
              &.progress-good { background-color: #1890ff; }
              &.progress-medium { background-color: #faad14; }
              &.progress-low { background-color: #ff4d4f; }
            }
          }
          
          .match-percent {
            font-size: 26rpx;
            color: #666;
            width: 60rpx;
            text-align: right;
          }
        }
      }
      
      .job-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .salary {
          font-size: 32rpx;
          font-weight: bold;
          color: #ff4d4f;
        }
        
        .location {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }
}
</style> 