<template>
	<view>
		<view class="header">
			<view class="top">
				<view class="title-box">
					<view class="title">高压办电</view>
					<view>帮代办、少跑腿</view>
				</view>
				<image src="@/static/highVoltage/icon1.png" class="logo"></image>
			</view>
			<valtageTab :isTab="false"></valtageTab>
		</view>
		<view class="apply">
			<button class="btn" @click="toPage('./voltageApply')">申请办电</button>
		</view>
		<view class="list">
			<view class="title">我的提交</view>
			<view class="box" v-for="(item, index) in dataList" :key="index"
				@click="toPage('./voltageApplicationForm?delta=1&id=' + item.id)">
				<view class="box-title" v-if="item.formdata && item.formdata[0] && item.formdata[0][1]">
				  {{ item.formdata[0][1] }}
				</view>
				<view class="box-title" v-else>
				  数据不完整
				</view>

				<view class="time">
					<image class="icon" src="@/static/highVoltage/time.png"></image>
					{{ item.createtime }}
				</view>
				<view class="remark">{{ item.paytype }}</view>
			</view>
		</view>
		<!-- <view class="back">
			<button class="btn" @click="toPage('/')">返回</button>
		</view> -->
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				dataList: []
			};
		},
		onShow() {
			this.getDataList();
		},
		onPullDownRefresh() {
			this.getDataList();
		},
		methods: {
			toPage(url) {
				uni.navigateTo({
					url: url
				});
			},
			getDataList() {
				uni.showLoading();
				const _this = this;
				app.post('ApiShenqingbandian/orderlist', {}, ({
					datalist
				}) => {
					uni.stopPullDownRefresh();
					uni.hideLoading();
					_this.dataList = datalist;
				});
			}
		}
	};
</script>

<style lang="scss">
	@import 'styled.scss';
</style>