<template>
<view class="container">
	<block v-if="isload">
		<block v-if="logintype==1">
			<form @submit="formSubmit" @reset="formReset">
			<view class="title">用户登录</view>
			<view class="loginform">
				<view class="form-item">
					<image src="/static/img/reg-tel.png" class="img"/>
					<input type="text" class="input" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" @input="telinput"/>
				</view>
				<view class="form-item">
					<image src="/static/img/reg-pwd.png" class="img"/>
					<input type="text" class="input" placeholder="请输入密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" :password="true"/>
				</view>
				<view class="xieyi-item" v-if="xystatus==1">
					<checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox class="checkbox" value="1" :checked="isagree"/>我已阅读并同意</label></checkbox-group>
					<text :style="{color:t('color1')}" @tap="showxieyiFun">{{xyname}}</text>
					<text @tap="showxieyiFun" v-if="xyname2">和</text>
					<text :style="{color:t('color1')}" @tap="showxieyiFun2" v-if="xyname2">{{xyname2}}</text>
				</view>
				
				<button class="form-btn" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" form-type="submit">登录</button>
				<button class="form-btn2" @tap="goback" v-if="!login_mast">暂不登录</button>
				<button v-if="platform2 == 'ios' && logintype_4==true" class="ioslogin-btn" @tap="ioslogin" style="width:100%"><image src="/static/images/apple.png"/>通过Apple登录</button>
				<!-- #ifdef APP-PLUS -->
				<button v-if="logintype_6==true" class="googlelogin-btn" @tap="googlelogin">Google登录</button>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<div v-if="logintype_6==true" class="googlelogin-btn2" id="googleloginBtn" data-shape="circle">Google登录</div>
				<!-- #endif -->
			</view>
			</form>
			<view class="regtip">
				<view @tap="goto" data-url="reg" data-opentype="redirect">注册账号</view>
				<view class="flex1"></view>
				<view @tap="goto" data-url="getpwd" data-opentype="redirect" v-if="needsms">忘记密码？</view>
			</view>

			<block v-if="logintype_2 || logintype_3">
				<view class="othertip">
					<view class="othertip-line"></view>
					<view class="othertip-text">
						<text class="txt">其他方式登录</text>
					</view>
					<view class="othertip-line"></view>
				</view>
				<view class="othertype">
					<view class="othertype-item" v-if="logintype_3" @tap="weixinlogin">
						<image class="img" :src="pre_url+'/static/img/login-'+platformimg+'.png'"/>
						<text class="txt">{{platformname}}登录</text>
					</view>
					<view class="othertype-item" v-if="logintype_2" @tap="changelogintype" data-type="2">
						<image class="img" :src="pre_url+'/static/img/reg-tellogin.png'"/>
						<text class="txt">手机号登录</text>
					</view>
				</view>
			</block>
		</block>
		<block v-if="logintype==2">
			<form @submit="formSubmit" @reset="formReset">
			<view class="title">用户登录</view>
			<view class="loginform">
				<view class="form-item">
					<image src="/static/img/reg-tel.png" class="img"/>
					<input type="text" class="input" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" @input="telinput"/>
				</view>
				<view class="form-item">
					<image src="/static/img/reg-code.png" class="img"/>
					<input type="text" class="input" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/>
					<view class="code" :style="{color:t('color1')}" @tap="smscode">{{smsdjs||'获取验证码'}}</view>
				</view>
        <view class="form-item" v-if="reg_invite_code && !parent">
        	<image src="/static/img/reg-yqcode.png" class="img"/>
        	<input type="text" class="input" :placeholder="'请输入邀请人'+reg_invite_code_text+'(选填)'" placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value=""/>
        </view>
        <view class="form-item" v-if="reg_invite_code && parent" style="color: #333;">
          邀请人：<image :src="parent.headimg" style="width: 80rpx; height: 80rpx;border-radius: 50%;"></image> {{parent.nickname}} 
        </view>
				<view class="xieyi-item" v-if="xystatus==1">
					<checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox class="checkbox" value="1" :checked="isagree"/>我已阅读并同意</label></checkbox-group>
					<text :style="{color:t('color1')}" @tap="showxieyiFun">{{xyname}}</text>
					<text @tap="showxieyiFun" v-if="xyname2">和</text>
					<text :style="{color:t('color1')}" @tap="showxieyiFun2" v-if="xyname2">{{xyname2}}</text>
				</view>
				<button class="form-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">登录</button>
				<button class="form-btn2" @tap="goback" v-if="!login_mast">暂不登录</button>
				<button v-if="platform2 == 'ios' && logintype_4==true" class="ioslogin-btn" @tap="ioslogin" style="width:100%"><image src="/static/images/apple.png"/>通过Apple登录</button>
				<!-- #ifdef APP-PLUS -->
				<button v-if="logintype_6==true" class="googlelogin-btn" @tap="googlelogin">Google登录</button>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<div v-if="logintype_6==true" class="googlelogin-btn2" id="googleloginBtn" data-shape="circle">Google登录</div>
				<!-- #endif -->
			</view>
			</form>
			<block v-if="logintype_1 || logintype_3">
				<view class="othertip">
					<view class="othertip-line"></view>
					<view class="othertip-text">
						<text class="txt">其他方式登录</text>
					</view>
					<view class="othertip-line"></view>
				</view>
				<view class="othertype">
					<view class="othertype-item" v-if="logintype_3" @tap="weixinlogin">
						<image class="img" :src="pre_url+'/static/img/login-'+platformimg+'.png'"/>
						<text class="txt">{{platformname}}登录</text>
					</view>
					<view class="othertype-item" v-if="logintype_1" @tap="changelogintype" data-type="1">
						<image class="img" :src="pre_url+'/static/img/reg-tellogin.png'"/>
						<text class="txt">密码登录</text>
					</view>
				</view>
			</block>
		</block>
		<block v-if="logintype==3">
			<view class="authlogin">
				<view class="authlogin-logo"><image :src="logo" style="width:100%;height:100%"/></view>
				<view class="authlogin-name">授权登录{{name}}</view>
				<button class="authlogin-btn" @tap="authlogin" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{platformname}}授权登录</button>
				<button class="authlogin-btn2" @tap="goback" v-if="!login_mast">暂不登录</button>
				<button v-if="platform2 == 'ios' && logintype_4==true" class="ioslogin-btn" @tap="ioslogin"><image src="/static/images/apple.png"/>通过Apple登录</button>
				<!-- #ifdef APP-PLUS -->
				<button v-if="logintype_6==true" class="googlelogin-btn" @tap="googlelogin">Google登录</button>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<div v-if="logintype_6==true" class="googlelogin-btn2" id="googleloginBtn" data-shape="circle">Google登录</div>
				<!-- #endif -->
				<view class="xieyi-item" v-if="xystatus==1">
					<checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox class="checkbox" value="1" :checked="isagree"/>我已阅读并同意</label></checkbox-group>
					<text :style="{color:t('color1')}" @tap="showxieyiFun">{{xyname}}</text>
					<text @tap="showxieyiFun" v-if="xyname2">和</text>
					<text :style="{color:t('color1')}" @tap="showxieyiFun2" v-if="xyname2">{{xyname2}}</text>
				</view>
			</view>
		</block>
		<!-- 绑定手机号 -->
		<block v-if="logintype==4">
				<!--  #ifdef MP-WEIXIN -->
				<view class="authlogin">
					<view class="authlogin-logo"><image :src="logo" style="width:100%;height:100%"/></view>
					<view class="authlogin-name">授权登录{{name}}</view>
					<button class="authlogin-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{platformname}}授权绑定手机号</button>
					<button class="authlogin-btn2" @tap="nobindregister" v-if="login_bind==1">暂不绑定</button>
				</view>
				<!-- #endif -->
				<!--  #ifndef MP-WEIXIN -->
				<form @submit="bindregister" @reset="formReset">
					<view class="title">绑定手机号</view>
					<view class="loginform">
						<view class="form-item">
							<image src="/static/img/reg-tel.png" class="img"/>
							<input type="text" class="input" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" @input="telinput"/>
						</view>
						<view class="form-item">
							<image src="/static/img/reg-code.png" class="img"/>
							<input type="text" class="input" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/>
							<view class="code" :style="{color:t('color1')}" @tap="smscode">{{smsdjs||'获取验证码'}}</view>
						</view>
						<button class="form-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">确定</button>
						<button class="form-btn2" @tap="nobindregister" v-if="login_bind==1">暂不绑定</button>
					</view>
				</form>
				<!-- #endif -->
		</block>

		
		<!-- 设置头像昵称 -->
		<block v-if="logintype==5">
			<form @submit="setnicknameregister" @reset="formReset">
				<view class="title">请设置头像昵称</view>
				<view class="loginform">
					<!--  #ifdef MP-WEIXIN -->
					<view class="form-item" style="height:120rpx;line-height:120rpx">
						<view class="flex1">头像</view>
						<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" style="width:100rpx;height:100rpx;">
							<image :src="headimg || default_headimg" style="width:100%;height:100%;border-radius:50%"></image>
						</button> 
					</view>
					<view class="form-item" style="height:120rpx;line-height:120rpx">
						<view class="flex1">昵称</view>
						<input type="nickname" class="input" placeholder="请输入昵称" name="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE" style="text-align:right"/>
					</view>
					<!-- #endif -->
					<!--  #ifndef MP-WEIXIN -->
					<view class="form-item" style="height:120rpx;line-height:120rpx">
						<view class="flex1">头像</view>
						<image :src="headimg || default_headimg" style="width:100rpx;height:100rpx;border-radius:50%" @tap="uploadHeadimg"></image>
					</view>
					<view class="form-item">
						<view class="flex1">昵称</view>
						<input type="text" class="input" placeholder="请输入昵称" name="nickname" value="" placeholder-style="font-size:30rpx;color:#B2B5BE" style="text-align:right"/>
					</view>
					<!-- #endif -->
					<button class="form-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">确定</button>
					<button class="form-btn2" @tap="nosetnicknameregister" v-if="login_setnickname==1">暂不设置</button>
				</view>
			</form>
		</block>

		<view v-if="showxieyi" class="xieyibox">
			<view class="xieyibox-content">
				<view style="overflow:scroll;height:100%;">
					<parse :content="xycontent" @navigate="navigate"></parse>
				</view>
				<view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"  @tap="hidexieyi">已阅读并同意</view>
			</view>
		</view>
		<view v-if="showxieyi2" class="xieyibox">
			<view class="xieyibox-content">
				<view style="overflow:scroll;height:100%;">
					<parse :content="xycontent2" @navigate="navigate"></parse>
				</view>
				<view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"  @tap="hidexieyi2">已阅读并同意</view>
			</view>
		</view>

	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			platform2:app.globalData.platform2,
			
			platform:'',
			platformname:'',
			platformimg:'weixin',
			logintype:0,
			logintype_1:true,
			logintype_2:false,
			logintype_3:false,
			logintype_4:false,
			logintype_6:false,
			isioslogin:false,
			isgooglelogin:false,
			google_client_id:'',
			needsms:false,
			logo:'',
			name:'',
			xystatus:0,
			xyname:'',
			xycontent:'',
			xyname2:'',
			xycontent2:'',
			showxieyi:false,
			showxieyi2:false,
			isagree:false,
      smsdjs: '',
			tel:'',
      hqing: 0,
			frompage:'/pages/my/usercenter',
			wxloginclick:false,
			iosloginclick:false,
			googleloginclick:false,
			login_bind:0,
			login_setnickname:0,
			login_mast:false,
      reg_invite_code:0,
      reg_invite_code_text:'',
      parent:{},
			tmplids:[],
			default_headimg:app.globalData.pre_url + '/static/img/touxiang.png',
			headimg:'',
			nickname:'',
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		if(this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);
		console.log(this.frompage,'this.opt.frompage');
		
		if(this.opt.logintype) this.logintype = this.opt.logintype;
		if(this.opt.login_bind) this.login_bind = this.opt.login_bind;
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiIndex/login', {pid:app.globalData.pid,xlevel:app.globalData.xlevel}, function (res) {
				that.loading = false;
				if(res.status == 0){
					app.alert(res.mg);return;
				}
				that.logintype_1 = res.logintype_1;
				that.logintype_2 = res.logintype_2;
				that.logintype_3 = res.logintype_3;
				// #ifdef APP-PLUS
				if(that.platform2 == 'ios'){
					if (plus.runtime.isApplicationExist({ pname: 'com.tencent.mm', action: 'weixin://' })) {
						console.log('已安装微信')
					}else{
						that.logintype_3 = false;
						console.log('未安装微信')
					}
				}
				// #endif
				that.logintype_4 = res.logintype_4;
				that.logintype_6 = res.logintype_6 || false;
				that.google_client_id = res.google_client_id || '';
				that.login_mast = res.login_mast;
				that.needsms = res.needsms;
        that.reg_invite_code = res.reg_invite_code;
        that.reg_invite_code_text = res.reg_invite_code_text;
        that.parent = res.parent;
				if(that.logintype==0){
					if(that.logintype_1){
						that.logintype = 1;
					}else if(that.logintype_2){
						that.logintype = 2;
					}else if(that.logintype_3){
						that.logintype = 3;
					}
				}
				that.xystatus = res.xystatus;
				that.xyname = res.xyname;
				that.xycontent = res.xycontent;
				that.xyname2 = res.xyname2;
				that.xycontent2 = res.xycontent2;
				that.logo = res.logo;
				that.name = res.name;
				that.platform = res.platform;
				if(that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app'){
					that.platformname = '授权';
					that.platformimg = 'weixin';
				}
				if(that.platform == 'toutiao'){
					that.platformname = '头条';
					that.platformimg = 'toutiao';
				}
				if(that.platform == 'alipay'){
					that.platformname = '支付宝';
					that.platformimg = 'alipay';
				}
				if(that.platform == 'qq'){
					that.platformname = 'QQ';
					that.platformimg = 'qq';
				}
				if(that.platform == 'baidu'){
					that.platformname = '百度';
					that.platformimg = 'baidu';
				}
				that.loaded();
				// #ifdef H5
				if(that.logintype_6){
					var hm = document.createElement('script');
					hm.src="https://accounts.google.com/gsi/client";
					document.body.appendChild(hm);
					setTimeout(function(){
						google.accounts.id.initialize({
							client_id: that.google_client_id,
							callback: function(response){
								console.log(response);
								var credential = response.credential;
								var base64Url = credential.split('.')[1];
								var base64 = base64Url.replace(/-/g,'+').replace(/_/g,'/');
								var jsonPayload = decodeURIComponent(
									window.atob(base64).split('').map(function(c){
										return '%'+('00'+c.charCodeAt(0).toString(16)).slice(-2);
									}).join('')
								);
								var resdata = JSON.parse(jsonPayload);
								resdata.openId = resdata.sub;
								console.log(resdata);
								app.showLoading('提交中');
								app.post('ApiIndex/googlelogin',{userInfo:resdata,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){
									app.showLoading(false);
									console.log(res2);
									if (res2.status == 1) {
										app.success(res2.msg);
										setTimeout(function () {
											console.log('frompage')
											console.log(that.frompage)
											app.goto(that.frompage,'redirect');
										}, 1000);
									} else if (res2.status == 3) {
										that.logintype = 5;
										that.isioslogin = false;
										that.isgooglelogin = true;
										that.login_setnickname = res2.login_setnickname;
										that.login_bind = res2.login_bind
									} else if (res2.status == 2) {
										that.logintype = 4;
										that.isioslogin = false;
										that.isgooglelogin = true;
										that.login_bind = res2.login_bind
									} else {
										app.error(res2.msg);
									}
								});
							}
						});
						google.accounts.id.renderButton(
							document.getElementById("googleloginBtn"),
							{ theme: "outline", size: "large",width:'300'}  // customization attributes
						);
						google.accounts.id.prompt();
					},500);
				}
				// #endif
			});
		},
    formSubmit: function (e) {
			var that = this;
      var formdata = e.detail.value;
      if (formdata.tel == ''){
        app.alert('请输入手机号');
        return;
      }
			if(that.logintype == 1){
				if (formdata.pwd == '') {
					app.alert('请输入密码');
					return;
				}
			}
			if(that.logintype == 2){
				if (formdata.smscode == '') {
					app.alert('请输入短信验证码');
					return;
				}
				if (that.xystatus == 1 && !that.isagree) {
					app.error('请先阅读并同意用户注册协议');
					return false;
				}
			}

			if(that.logintype == 4){
				if (typeof(formdata.pwd) != 'undefined' && formdata.pwd == '') {
					app.alert('请输入密码');
					return;
				}
				if (typeof(formdata.smscode) != 'undefined' && formdata.smscode == '') {
					app.alert('请输入短信验证码');
					return;
				}
			}
			
			app.showLoading('提交中');
      app.post("ApiIndex/loginsub", {tel:formdata.tel,pwd:formdata.pwd,smscode:formdata.smscode,logintype:that.logintype,pid:app.globalData.pid,yqcode:formdata.yqcode,xlevel:app.globalData.xlevel}, function (res) {
				app.showLoading(false);
        if (res.status == 1) {
			uni.setStorageSync('mid',res.mid);
          app.success(res.msg);
					if(res.tmplids){
						that.tmplids = res.tmplids;
					}
					that.subscribeMessage(function () {
						setTimeout(function () {
							app.goto(that.frompage,'redirect');
						}, 1000);
					});
        } else {
          app.error(res.msg);
        }
      });
    },
		getPhoneNumber: function (e) {
			var that = this
			console.log(e);
			if(e.detail.errMsg == "getPhoneNumber:fail user deny"){
				app.error('请同意授权获取手机号');return;
			}
			if(!e.detail.iv || !e.detail.encryptedData){
				app.error('请同意授权获取手机号');return;
			}
			wx.login({success (res1){
				console.log(res1);
				var code = res1.code;
				//用户允许授权
				app.post('ApiIndex/wxRegister',{xlevel:app.globalData.xlevel,headimg:that.headimg,nickname:that.nickname,iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){
					if (res2.status == 1) {
						app.success(res2.msg);
						if(res2.tmplids){
							that.tmplids = res2.tmplids;
						}
						that.subscribeMessage(function () {
							setTimeout(function () {
								app.goto(that.frompage,'redirect');
							}, 1000);
						});
					} else {
						app.error(res2.msg);
					}
					return;
				})
			}});
		},
		setnicknameregister:function(e){
			//console.log(e);
			//return;
			this.nickname = e.detail.value.nickname;
			if(this.nickname == '' || this.headimg == ''){
				app.alert('请设置头像和昵称');
				return;
			}
			if(this.login_bind!=0){
				this.logintype = 4;
				this.isioslogin = false;
				this.isgooglelogin = false;
			}else{
				this.register(this.headimg,this.nickname,'','');
			}
		},
		nosetnicknameregister:function(){
			this.nickname = '';
			this.headimg = '';
			if(this.login_bind!=0){
				this.logintype = 4;
				this.isioslogin = false;
				this.isgooglelogin = false;
			}else{
				this.register('','','','');
			}
		},
		bindregister:function(e){
			var that = this;
			var formdata = e.detail.value;
      if (formdata.tel == ''){
        app.alert('请输入手机号');
        return;
      }
			if (formdata.smscode == '') {
				app.alert('请输入短信验证码');
				return;
			}
			that.register(this.headimg,this.nickname,formdata.tel,formdata.smscode);
		},
		nobindregister:function(){
			this.register(this.headimg,this.nickname,'','');
		},
		register:function(headimg,nickname,tel,smscode){
			var that = this;
			var url = '';
			if(that.platform == 'app') {
				url = 'ApiIndex/appwxRegister';
				if(that.isioslogin){
					url = 'ApiIndex/iosRegister';
				}
			} else if(that.platform=='mp' || that.platform=='h5') {
				url = 'ApiIndex/shouquanRegister';
			} else {
				url = 'ApiIndex/'+that.platform+'Register';
			}
			if(that.isgooglelogin){
				url = 'ApiIndex/googleRegister';
			}
			app.post(url,{headimg:headimg,nickname:nickname,tel:tel,smscode:smscode,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){
				if (res2.status == 1) {
					app.success(res2.msg);
					if(res2.tmplids){
						that.tmplids = res2.tmplids;
					}
					that.subscribeMessage(function () {
						setTimeout(function () {
							app.goto(that.frompage,'redirect');
						}, 1000);
					});
				} else {
					app.error(res2.msg);
				}
				return;
			});
		},
		authlogin:function(){
			var that = this;
			if (that.xystatus == 1 && !that.isagree) {
				app.error('请先阅读并同意用户注册协议');
				return false;
			}
			that.weixinlogin();
		},
		weixinlogin:function(){
			var that = this;
			if (that.xystatus == 1 && !that.isagree) {
				that.showxieyi = true;
				that.wxloginclick = true;
				return;
			}
			console.log('weixinlogin')
			that.wxloginclick = false;
			app.authlogin(function(res){
				console.log(res);
				if (res.status == 1) {
					app.success(res.msg);
					setTimeout(function () {
						console.log('frompage')
						console.log(that.frompage)
						app.goto( that.frompage,'redirect');
					}, 1000);
				} else if (res.status == 3) {
					that.logintype = 5;
					that.login_setnickname = res.login_setnickname;
					that.login_bind = res.login_bind;
					that.isioslogin = false;
					that.isgooglelogin = false;
				} else if (res.status == 2) {
					that.logintype = 4;
					that.login_bind = res.login_bind;
					that.isioslogin = false;
					that.isgooglelogin = false;
				} else {
					app.error(res.msg);
				}
			},{frompage:that.frompage});
		},
		ioslogin:function(){
			var that = this;
			if (that.xystatus == 1 && !that.isagree) {
				that.showxieyi = true;
				that.iosloginclick = true;
				return false;
			}
			uni.login({  
				provider: 'apple',  
				success: function (loginRes) {  
					console.log(loginRes);
					// 登录成功  
					uni.getUserInfo({  
						provider: 'apple',  
						success(res) { 
							// 获取用户信息成功
							console.log(res)
							if(res.userInfo && res.userInfo.openId){
								app.post('ApiIndex/ioslogin',{userInfo:res.userInfo,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){
									console.log(res2);
									if (res2.status == 1) {
										app.success(res2.msg);
										setTimeout(function () {
											console.log('frompage')
											console.log(that.frompage)
											app.goto(that.frompage,'redirect');
										}, 1000);
									} else if (res2.status == 3) {
										that.logintype = 5;
										that.isioslogin = true;
										that.isgooglelogin = false;
										that.login_setnickname = res2.login_setnickname
										that.login_bind = res2.login_bind
									} else if (res2.status == 2) {
										that.logintype = 4;
										that.isioslogin = true;
										that.isgooglelogin = false;
										that.login_bind = res2.login_bind
									} else {
										app.error(res2.msg);
									}
								});
							}
						}  
					})  
				},  
				fail: function (err) {  
					console.log(err);
					app.error('登录失败');
				}  
			});  
		},
		googlelogin:function(){
			var that = this;
			if (that.xystatus == 1 && !that.isagree) {
				that.showxieyi = true;
				that.googleloginclick = true;
				return false;
			}
			// #ifdef APP-PLUS
			uni.login({  
				provider: 'google',  
				success: function (loginRes) {  
					console.log(loginRes);
					// 登录成功  
					uni.getUserInfo({  
						provider: 'google',  
						success(res) { 
							// 获取用户信息成功
							console.log(res)
							//alert(JSON.stringify(res));
							//if(res.userInfo && res.userInfo.openId){
								app.post('ApiIndex/googlelogin',{userInfo:res.userInfo,pid:app.globalData.pid,xlevel:app.globalData.xlevel},function(res2){
									console.log(res2);
									if (res2.status == 1) {
										app.success(res2.msg);
										setTimeout(function () {
											console.log('frompage')
											console.log(that.frompage)
											app.goto(that.frompage,'redirect');
										}, 1000);
									} else if (res2.status == 3) {
										that.logintype = 5;
										that.isioslogin = false;
										that.isgooglelogin = true;
										that.login_setnickname = res2.login_setnickname
										that.login_bind = res2.login_bind
									} else if (res2.status == 2) {
										that.logintype = 4;
										that.isioslogin = false;
										that.isgooglelogin = true;
										that.login_bind = res2.login_bind
									} else {
										app.error(res2.msg);
									}
								});
							//}
						}  
					})  
				},  
				fail: function (err) {  
					console.log(err);
					app.error('登录失败');
				}  
			});
			// #endif
		},
		changelogintype:function(e){
			var logintype = e.currentTarget.dataset.type
			this.logintype = logintype;
		},
    isagreeChange: function (e) {
      var val = e.detail.value;
      if (val.length > 0) {
        this.isagree = true;
      } else {
        this.isagree = false;
      }
      console.log(this.isagree);
    },
    showxieyiFun: function () {
      this.showxieyi = true;
    },
    hidexieyi: function () {
      this.showxieyi = false;
			this.isagree = true;
			if(this.wxloginclick){
				this.weixinlogin();
			}
			if(this.iosloginclick){
				this.ioslogin();
			}
			if(this.googleloginclick){
				this.googlelogin();
			}

    },
    showxieyiFun2: function () {
      this.showxieyi2 = true;
    },
    hidexieyi2: function () {
      this.showxieyi2 = false;
			this.isagree = true;
			if(this.wxloginclick){
				this.weixinlogin();
			}
			if(this.iosloginclick){
				this.ioslogin();
			}
			if(this.googleloginclick){
				this.googlelogin();
			}
    },
    telinput: function (e) {
      this.tel = e.detail.value
    },
		uploadHeadimg:function(){
			var that = this;
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera'],
				success: function(res) {
					var tempFilePaths = res.tempFilePaths;
					var tempFilePath = tempFilePaths[0];
					app.showLoading('上传中');
					uni.uploadFile({
						url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',
						filePath: tempFilePath,
						name: 'file',
						success: function(res) {
							console.log(res)
							app.showLoading(false);
							var data = JSON.parse(res.data);
							if (data.status == 1) {
								that.headimg = data.url;
							} else {
								app.alert(data.msg);
							}
						},
						fail: function(res) {
							app.showLoading(false);
							app.alert(res.errMsg);
						}
					});
				},
				fail: function(res) { //alert(res.errMsg);
				}
			});
		},
		onChooseAvatar:function(e){
			console.log(e)
			var that = this;
			app.showLoading('上传中');
			uni.uploadFile({
				url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',
				filePath: e.detail.avatarUrl,
				name: 'file',
				success: function(res) {
					app.showLoading(false);
					var data = JSON.parse(res.data);
					if (data.status == 1) {
						that.headimg = data.url;
					} else {
						app.alert(data.msg);
					}
				},
				fail: function(res) {
					app.showLoading(false);
					app.alert(res.errMsg);
				}
			});
		},
    smscode: function () {
      var that = this;
      if (that.hqing == 1) return;
      that.hqing = 1;
      var tel = that.tel;
      if (tel == '') {
        app.alert('请输入手机号码');
        that.hqing = 0;
        return false;
      }
      if (!/^1[3456789]\d{9}$/.test(tel)) {
        app.alert("手机号码有误，请重填");
        that.hqing = 0;
        return false;
      }
      app.post("ApiIndex/sendsms", {tel: tel}, function (data) {
        if (data.status != 1) {
          app.alert(data.msg);return;
        }
      });
      var time = 120;
      var interval1 = setInterval(function () {
        time--;
        if (time < 0) {
          that.smsdjs = '重新获取';
          that.hqing = 0;
          clearInterval(interval1);
        } else if (time >= 0) {
          that.smsdjs = time + '秒';
        }
      }, 1000);
    }
  }
};
</script>

<style>
page{background:#ffffff}
.container{width:100%;}
.title{margin:70rpx 50rpx 50rpx 40rpx;height:60rpx;line-height:60rpx;font-size: 48rpx;font-weight: bold;color: #000000;}
.loginform{ width:100%;padding:0 50rpx;border-radius:5px;background: #FFF;}
.loginform .form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:88rpx;line-height:88rpx;border-bottom:1px solid #F0F3F6;margin-top:20rpx}
/*.loginform .form-item:last-child{border:0}*/
.loginform .form-item .img{width:44rpx;height:44rpx;margin-right:30rpx}
.loginform .form-item .input{flex:1;color: #000;}
.loginform .form-item .code{font-size:30rpx}
.xieyi-item{display:flex;align-items:center;margin-top:30rpx}
.xieyi-item{font-size:24rpx;color:#B2B5BE}
.xieyi-item .checkbox{transform: scale(0.6);}
.loginform .form-btn{margin-top:60rpx;width:100%;height:96rpx;line-height:96rpx;color:#fff;font-size:30rpx;border-radius: 48rpx;}
.loginform .form-btn2{width:100%;height:80rpx;line-height:80rpx;background:#EEEEEE;border-radius:40rpx;color:#A9A9A9;margin-top:30rpx}
.regtip{color:#737785;font-size:26rpx;display:flex;width:100%;padding:0 80rpx;margin-top:30rpx}

.othertip{height:auto;overflow: hidden;display:flex;align-items:center;width:580rpx;padding:20rpx 20rpx;margin:0 auto;margin-top:160rpx;}
.othertip-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #F2F2F2}
.othertip-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}
.othertip-text .txt{color:#A3A3A3;font-size:22rpx}

.othertype{width:70%;margin:20rpx 15%;display:flex;justify-content:center;}
.othertype-item{width:50%;display:flex;flex-direction:column;align-items:center;}
.othertype-item .img{width:88rpx;height:88rpx;margin-bottom:20rpx}
.othertype-item .txt{color:#A3A3A3;font-size:24rpx}

.authlogin{display:flex;flex-direction:column;align-items:center}
.authlogin-logo{width:180rpx;height:180rpx;margin-top:120rpx}
.authlogin-name{color:#999999;font-size:30rpx;margin-top:60rpx;}
.authlogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#51B1F5;border-radius:48rpx;color:#fff;margin-top:100rpx}
.authlogin-btn2{width:580rpx;height:96rpx;line-height:96rpx;background:#EEEEEE;border-radius:48rpx;color:#A9A9A9;margin-top:20rpx}
.ioslogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#fff;border-radius:48rpx;color:#fff;border:1px solid #555;color:#333;font-weight:bold;margin-top:30rpx;font-size:30rpx;display:flex;justify-content:center;align-items:center}
.ioslogin-btn image{width:26rpx;height:26rpx;margin-right:16rpx;}

.googlelogin-btn{width:580rpx;height:96rpx;line-height:96rpx;background:#fff;border-radius:48rpx;color:#fff;border:1px solid #555;color:#333;font-weight:bold;margin-top:30rpx;font-size:30rpx;display:flex;justify-content:center;align-items:center}

.googlelogin-btn2{margin-top:30rpx;display:flex;justify-content:center;align-items:center}

.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}
.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}
</style>
