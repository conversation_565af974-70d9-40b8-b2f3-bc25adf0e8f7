<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit" @reset="formReset" report-submit="true">
			<view class="orderinfo">
				<view class="item">
					<text class="t1">选中订单数</text>
					<text class="t2">{{orderIds.length}}个</text>
				</view>
				<view class="item">
					<text class="t1">可开票总金额</text>
					<text class="t2 red">¥{{totalAmount}}</text>
				</view>
			</view>
			<view class="orderinfo">
				<view class="item">
					<text class="t1">发票类型</text>
					<view class="t2">
						<radio-group class="radio-group" @change="changeOrderType" name="invoice_type">
							<label class="radio" v-if="inArray(1,invoice_type)">
								<radio value="1" :checked="invoice_type_select == 1 ? true : false"></radio>普通发票
							</label>
							<label class="radio" v-if="inArray(2,invoice_type)">
								<radio value="2" :checked="invoice_type_select == 2 ? true : false"></radio>增值税专用发票
							</label>
						</radio-group>
					</view>
				</view>
				<view class="item">
					<text class="t1">抬头类型</text>
					<view class="t2">
						<radio-group class="radio-group" @change="changeNameType" name="name_type">
							<label class="radio">
								<radio value="1" :checked="name_type_select == 1 ? true : false" :disabled="name_type_personal_disabled ? true : false"></radio>个人
							</label>
							<label class="radio">
								<radio value="2" :checked="name_type_select == 2 ? true : false"></radio>公司
							</label>
						</radio-group>
					</view>
				</view>
				<view class="item">
					<text class="t1">抬头名称</text>
					<input class="t2" type="text" placeholder="抬头名称" placeholder-style="font-size:28rpx;color:#BBBBBB" name="invoice_name" v-model="formData.invoice_name"></input>
				</view>
				<view class="item" v-if="name_type_select == 2">
					<text class="t1">公司税号</text>
					<input class="t2" type="text" placeholder="公司税号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tax_no" v-model="formData.tax_no"></input>
				</view>
				<view class="item" v-if="invoice_type_select == 2">
					<text class="t1">注册地址</text>
					<input class="t2" type="text" placeholder="注册地址" placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" v-model="formData.address"></input>
				</view>
				<view class="item" v-if="invoice_type_select == 2">
					<text class="t1">注册电话</text>
					<input class="t2" type="text" placeholder="注册电话" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" v-model="formData.tel"></input>
				</view>
				<view class="item" v-if="invoice_type_select == 2">
					<text class="t1">开户银行</text>
					<input class="t2" type="text" placeholder="开户银行" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_name" v-model="formData.bank_name"></input>
				</view>
				<view class="item" v-if="invoice_type_select == 2">
					<text class="t1">银行账号</text>
					<input class="t2" type="text" placeholder="银行账号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_account" v-model="formData.bank_account"></input>
				</view>
				<view class="item">
					<text class="t1">手机号</text>
					<input class="t2" type="text" placeholder="接收电子发票手机号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="mobile" v-model="formData.mobile"></input>
				</view>
				<view class="item">
					<text class="t1">邮箱</text>
					<input class="t2" type="text" placeholder="接收电子发票邮箱" placeholder-style="font-size:28rpx;color:#BBBBBB" name="email" v-model="formData.email"></input>
				</view>
			</view>
			<button class="btn" form-type="submit" :style="{background:t('color1')}">确定</button>
			<view class="btn-a" @tap="back">返回上一步</view>
			<view style="padding-top:30rpx"></view>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			orderIds: [],
			totalAmount: 0,
			invoice_type:[1,2],
			invoice_type_select:1,
			name_type_select:1,
			name_type_personal_disabled:false,
			formData: {
				invoice_name: '',
				tax_no: '',
				address: '',
				tel: '',
				bank_name: '',
				bank_account: '',
				mobile: '',
				email: ''
			}
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		if(this.opt.ids) {
			this.orderIds = this.opt.ids.split(',');
		}
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiOrder/batchInvoiceInfo', {ids: that.orderIds.join(',')}, function (res) {
				that.loading = false;
				if (res.status == 0) {
					app.error(res.msg);
					return;
				}
				that.totalAmount = res.total_amount;
				that.loaded();
			});
		},
		formSubmit: function (e) {
			var that = this;
			var formdata = e.detail.value;
			
			if(formdata.invoice_name == '') {
				app.error('请填写抬头名称');
				return;
			}
			if((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {
				app.error('请填写公司税号');
				return;
			}
			if(formdata.invoice_type == 2) {
				if(formdata.address == '') {
					app.error('请填写注册地址');
					return;
				}
				if(formdata.tel == '') {
					app.error('请填写注册电话');
					return;
				}
				if(formdata.bank_name == '') {
					app.error('请填写开户银行');
					return;
				}
				if(formdata.bank_account == '') {
					app.error('请填写银行账号');
					return;
				}
			}
			if (formdata.mobile != '') {
				if(!/^1[3456789]\d{9}$/.test(formdata.mobile)){
					app.error("手机号码有误，请重填");
					return;
				}
			}
			if (formdata.email != '') {
				if(!/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/.test(formdata.email)){
					app.error("邮箱有误，请重填");
					return;
				}
			}
			if(formdata.mobile == '' && formdata.email == '') {
				app.error("手机号和邮箱请填写其中一个");
				return;
			}

			app.showLoading('提交中');
			var postData = {
				order_ids: that.orderIds,
				invoice_data: {
					invoice_type: formdata.invoice_type,
					invoice_name: formdata.invoice_name,
					name_type: formdata.name_type,
					tax_no: formdata.tax_no,
					address: formdata.address,
					tel: formdata.tel,
					bank_name: formdata.bank_name,
					bank_account: formdata.bank_account,
					mobile: formdata.mobile,
					email: formdata.email
				}
			};
			
			app.post('ApiOrder/batchInvoice', postData, function (res) {
				app.showLoading(false);
				app.alert(res.msg);
				if (res.status == 1) {
					setTimeout(function () {
						app.goto('/pagesExt/order/orderlist');
					}, 1000);
				}
			});
		},
		changeOrderType: function(e) {
			var that = this;
			var value = e.detail.value;
			if(value == 2) {
				that.name_type_select = 2;
				that.name_type_personal_disabled = true;
			} else {
				that.name_type_personal_disabled = false;
			}
			that.invoice_type_select = value;
		},
		changeNameType: function(e) {
			var that = this;
			var value = e.detail.value;
			that.name_type_select = value;
		},
		back:function(e) {
			uni.navigateBack({});
		},
		inArray: function(value, array) {
			return app.inArray(value, array);
		}
	}
}
</script>

<style>
.radio radio{transform: scale(0.8);}
.radio:nth-child(2) { margin-left: 30rpx;}
.btn-a { text-align: center; padding: 30rpx; color: rgb(253, 74, 70);}
.text-min { font-size: 24rpx; color: #999;}
.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;flex-shrink:0}
.orderinfo .item .t2{flex:1;text-align:right; font-size: 28rpx;}
.orderinfo .item .red{color:red}
.orderinfo .item .grey{color:grey}

.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:50rpx;color: #fff;font-size: 30rpx;font-weight:bold}
</style> 