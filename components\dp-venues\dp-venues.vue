<template>
<view class="dp-business" :style="{
	color:params.color,
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	fontSize:(params.fontsize*2)+'rpx'
}">
	<!-- Bento Grid 布局 - 样式2 -->
	<view class="bento-grid" v-if="params.csstype == 2">
		<view class="bento-item" v-for="(item,index) in data" :key="index"
			:class="{'bento-item-large': index % 5 === 0 || index % 5 === 3, 'bento-item-small': index % 5 === 1 || index % 5 === 2 || index % 5 === 4}"
			@click="goto" :data-url='"/pagesB/dingchang/dingchangdetail?id="+item.bid+"&services="+ JSON.stringify(item.service)'>

			<view class="bento-image-container">
				<image class="bento-image" :src="item.pic_list" mode="aspectFill"></image>
				<view class="bento-overlay"></view>
				<view class="bento-tags">
					<text v-for="(ite,ind) in item.catalog" :key="ind" class="bento-tag">{{ite.name}}</text>
				</view>
			</view>

			<view class="bento-content">
				<view class="bento-title">{{item.title}}</view>
				<view class="bento-location">
					<text class="location-text">{{item.location}}</text>
					<text class="distance-text">{{item.distance}}</text>
				</view>

				<view class="bento-services" v-if="params.showdservice=='1'">
					<view class="service-tag" :style="'border:1rpx solid '+params.primary_color + ';color:'+params.primary_color"
						v-for="(ite,ind) in item.service" :key="ind">
						{{ite.name}}
					</view>
				</view>

				<view class="bento-footer">
					<view class="bento-intro" :decode="true" v-html="item.introduce"></view>
					<view class="bento-btn" :style="'background:'+params.primary_color">去订场</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 列表布局 - 样式1 (Bento风格优化) -->
	<view class="busbox modern-card" v-for="(item,index) in data" :key="index" v-if="params.csstype == 1">
		<view class="businfo" @click="goto" :data-url='"/pagesB/dingchang/dingchangdetail?id="+item.bid+"&services="+ JSON.stringify(item.service)'>
			<view class="f1">
				<image class="image" :src="item.logo"/>
				<view class="image-overlay"></view>
				<view class="catalog-tags" v-if="item.catalog && item.catalog.length > 0">
					<text class="catalog-tag" v-for="(cat,catIndex) in item.catalog.slice(0,2)" :key="catIndex">{{cat.name}}</text>
				</view>
			</view>
			<view class="f2">
				<view class="title">{{item.title}}</view>
				<view class="location-info">
					<view class="location-main">
						<text v-if="params.location!=='0'" class="location-text">{{item.location}}</text>
					</view>
					<view v-if="params.showdistance" class="distance-badge" :style="{background:'linear-gradient(135deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}">
						{{item.distance}}
					</view>
				</view>
				<view class="intro-text" v-if="params.showjianjie=='1'">
					<view :decode="true" v-html="item.introduce"></view>
				</view>

				<view class="other" v-if="params.showdservice=='1'">
					<view class="otherTag modern-tag" :style="'border:1rpx solid '+params.primary_color + ';color:'+params.primary_color+';background:rgba('+params.primary_color.replace('#','').match(/.{2}/g).map(x=>parseInt(x,16)).join(',')+',0.1)'" v-for="ite,ind in item.service" :key="ind" >
						{{ite.name}}
					</view>
				</view>
			</view>
		</view>
	</view>

	<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange" :menuindex="menuindex"></buydialog>
</view>
</template>
<script>
	export default {
		data(){
			return {
				pre_url:getApp().globalData.pre_url,
				buydialogShow:false,
			}
		},
		props: {
			menuindex:{default:-1},
			params:{},
			data:{}
		},
		
		
		methods: {
			buydialogChange: function (e) {
				if(!this.buydialogShow){
					this.proid = e.currentTarget.dataset.proid
				}
				this.buydialogShow = !this.buydialogShow;
				console.log(this.buydialogShow);
			},
			addcart:function(){
				this.$emit('addcart');
			}
		}
	}
</script>
<style>
/* 基础样式 */
.dp-business{height: auto; position: relative;}

/* Bento Grid 布局样式 */
.bento-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
	gap: 20rpx;
	padding: 20rpx;
}

.bento-item {
	background: #fff;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
}

.bento-item:hover {
	transform: translateY(-8rpx);
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
}

/* 大卡片样式 */
.bento-item-large {
	grid-column: span 2;
	min-height: 400rpx;
}

/* 小卡片样式 */
.bento-item-small {
	grid-column: span 1;
	min-height: 320rpx;
}

/* 响应式布局 */
@media (max-width: 768rpx) {
	.bento-grid {
		grid-template-columns: 1fr;
	}

	.bento-item-large,
	.bento-item-small {
		grid-column: span 1;
	}
}

/* 图片容器 */
.bento-image-container {
	position: relative;
	height: 240rpx;
	overflow: hidden;
}

.bento-item-large .bento-image-container {
	height: 280rpx;
}

.bento-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.bento-item:hover .bento-image {
	transform: scale(1.05);
}

/* 渐变遮罩 */
.bento-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
	pointer-events: none;
}

/* 标签样式 */
.bento-tags {
	position: absolute;
	top: 16rpx;
	left: 16rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.bento-tag {
	background: rgba(0, 0, 0, 0.7);
	color: #fff;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	backdrop-filter: blur(10rpx);
}

/* 内容区域 */
.bento-content {
	padding: 24rpx;
}

.bento-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1a1a1a;
	line-height: 1.4;
	margin-bottom: 12rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	overflow: hidden;
}

.bento-location {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.location-text {
	font-size: 26rpx;
	color: #666;
	flex: 1;
}

.distance-text {
	font-size: 24rpx;
	color: #999;
	margin-left: 16rpx;
}

/* 服务标签 */
.bento-services {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
	margin-bottom: 16rpx;
}

.service-tag {
	border: 1rpx solid #e0e0e0;
	border-radius: 16rpx;
	padding: 6rpx 12rpx;
	font-size: 22rpx;
	color: #666;
	background: #f8f9fa;
}

/* 底部区域 */
.bento-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 16rpx;
}

.bento-intro {
	flex: 1;
	font-size: 24rpx;
	color: #888;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	overflow: hidden;
}

.bento-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	padding: 16rpx 24rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
	white-space: nowrap;
	box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}

.bento-btn:active {
	transform: scale(0.95);
}

/* 原有列表布局样式 - Bento风格优化 */
.dp-business .busbox{
    background: #fff;
    padding: 24rpx;
    overflow: hidden;
    margin-bottom: 24rpx;
    width: 100%;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}
.dp-business .busbox:hover {
    transform: translateY(-6rpx);
    box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.1);
}
.dp-business .businfo{
    display: flex;
    width: 100%;
}
.dp-business .businfo .f1{
    width: 220rpx;
    height: 220rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    border-radius: 20rpx;
}
.dp-business .businfo .f1 .image{
    width: 100%;
    height: 100%;
    border-radius: 20rpx;
    object-fit: cover;
    transition: transform 0.4s ease;
}
.dp-business .busbox:hover .businfo .f1 .image {
    transform: scale(1.05);
}
.dp-business .businfo .f2{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.dp-business .businfo .f2 .title{
    font-size: 32rpx;
    font-weight: 600;
    color: #1a1a1a;
    line-height: 1.4;
    margin-bottom: 12rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
}
.dp-business .businfo .f2 .score{
    font-size: 26rpx;
    color: #f99716;
    display: flex;
    align-items: center;
}
.dp-business .businfo .f2 .score .image{
    width: 140rpx;
    height: 50rpx;
    vertical-align: middle;
    margin-bottom: 3px;
    margin-right: 8rpx;
}
.dp-business .businfo .f2 .sales{
    font-size: 26rpx;
    color: #31C88E;
    margin-bottom: 8rpx;
}
.dp-business .businfo .f2 .address{
    display: flex;
    align-items: center;
    color: #666;
    font-size: 26rpx;
    line-height: 1.5;
    margin-bottom: 8rpx;
}

/* 新增样式1的Bento风格元素 */
.modern-card {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.busbox:hover .image-overlay {
    opacity: 1;
}

.catalog-tags {
    position: absolute;
    top: 12rpx;
    left: 12rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    z-index: 2;
}

.catalog-tag {
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 6rpx 12rpx;
    border-radius: 16rpx;
    font-size: 20rpx;
    backdrop-filter: blur(4rpx);
}

.location-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12rpx;
}

.location-main {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.location-text {
    font-size: 26rpx;
    color: #666;
}

.distance-badge {
    font-size: 22rpx;
    color: #fff;
    padding: 4rpx 12rpx;
    border-radius: 16rpx;
    margin-left: 12rpx;
}

.intro-text {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 16rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
}

.modern-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 20rpx;
    transition: all 0.3s ease;
}

.modern-tag:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.dp-business .buspro{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap;margin-top:32rpx}
.dp-business .buspro .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden;}
.dp-business .buspro .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}
.dp-business .buspro .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-business .buspro .product-info {padding:20rpx 0;position: relative;}
.dp-business .buspro .product-info .p1 {color:#323232;font-weight:bold;font-size:24rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;height:36rpx}
.dp-business .buspro .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}
.dp-business .buspro .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}
.dp-business .buspro .product-info .p2-1 .t1{font-size:28rpx;}
.dp-business .buspro .product-info .p2-1 .t2 {margin-left:10rpx;font-size:22rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-business .buspro .product-info .p2-1 .t3 {margin-left:10rpx;font-size:22rpx;color: #999;}
.dp-business .buspro .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}
.dp-business .buspro .product-info .p3{color:#999999;font-size:20rpx;margin-top:10rpx}
.dp-business .buspro .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:20rpx;right:0;text-align:center;}
.dp-business .buspro .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}
.dp-business .buspro .product-info .p4 .img{width:100%;height:100%}


.other {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	padding-top: 16rpx;
	margin-top: 16rpx;
	border-top: 1rpx solid #f0f0f0;
}

.otherTag {
	padding: 8rpx 16rpx;
	font-size: 24rpx;
	height: auto;
	margin-right: 12rpx;
	margin-bottom: 8rpx;
	border-radius: 20rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border: 1rpx solid #e0e0e0;
	transition: all 0.3s ease;
}

.otherTag:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.item{
	margin: 20rpx;
	padding: 0rpx;
	border-radius: 20rpx;
	background: #fff;
	overflow: hidden;
}

.item-img{
	width: 100%;
	height: 180px;
}

.item-bt{
	padding: 20rpx;
	
}

.item-btn{
    text-align: center;
    color: #fff;
    border-radius: 30px;
    padding: 20rpx 40rpx;
	height: 45px;  
	background: #3BBCCB;
}

.item-other{
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	padding-top: 15rpx;
}

.tab-l{
	position: absolute;
	left: 0;
	top: 0;
	background: #00000044;
	padding: 5px;
	border-radius: 10px 0;
}

</style>