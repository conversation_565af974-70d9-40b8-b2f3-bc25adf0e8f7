.move-box {
    position: fixed;
    bottom: 0;
    z-index: 99;
    pointer-events: none;
}

.content-move-box,
.move-box {
    height: 260rpx;
    width: 100%;
    left: 0;
}

.content-move-box {
    box-shadow: 0 -16rpx 32rpx 0 rgba(0, 0, 0, 0.1);
    border-radius: 24rpx 24rpx 0 0;
    overflow: visible;
    transition: all 0.2s linear;
    position: absolute;
}

.content-box,
.content-move-box {
    background: #fff;
    pointer-events: auto;
}

.content-box {
    width: 100%;
    height: 455px;
    box-sizing: border-box;
    padding-top: 16rpx;
}

.content-box.gray,
.content-move-box.gray {
    background: #f6f7fb;
}

.content-dot {
    height: 72rpx;
    width: 100%;
    position: relative;
}

.content-icon {
    width: 44rpx;
    height: 44rpx;
    margin-right: 4rpx;
}

.content-dot:after {
    content: '';
    position: absolute;
    top: 32rpx;
    left: 50%;
    margin-left: -32rpx;
    width: 64rpx;
    height: 8rpx;
    background: #d8d8d8;
    border-radius: 8rpx;
}

.content-main-title {
    color: #111e38;
    font-size: 32rpx;
    line-height: 36rpx;
    text-align: center;
    padding-top: 32rpx;
    font-weight: 700;
}

.content-title {
    display: flex;
    font-weight: 700;
    color: #111e38;
    font-size: 36rpx;
    padding-bottom: 34rpx;
    width: 686rpx;
    margin: 0 auto;
}

.blank-pic {
    display: block;
    width: 520rpx;
    height: 360rpx;
    margin: 180rpx auto 0;
}

.blank-pic.small {
    width: 340rpx;
    height: 236rpx;
    margin-top: 100rpx;
}

.blank-text {
    color: #9c9c9c;
    font-size: 28rpx;
    line-height: 40rpx;
    text-align: center;
    margin-top: 8rpx;
}
