<template>
  <view>
    <block v-if="isload">
      <view class="working-job-page">
        <!-- 顶部企业信息卡片 -->
        <view class="company-card">
          <view class="company-header">
            <image class="company-logo" :src="jobInfo.companyLogo" mode="aspectFit"></image>
            <view class="company-info">
              <text class="company-name">{{ jobInfo.companyName }}</text>
              <text class="company-industry">{{ jobInfo.industry }}</text>
            </view>
          </view>
          <view class="divider"></view>
          <view class="job-basic-info">
            <view class="info-item">
              <text class="label">目前职位</text>
              <text class="value">{{ jobInfo.title }}</text>
            </view>
            <view class="info-item">
              <text class="label">工作时长</text>
              <text class="value">{{ jobInfo.workingDuration }}</text>
            </view>
          </view>
        </view>

        <!-- 详细信息卡片 -->
        <view class="detail-card">
          <view class="card-title">职位信息</view>
          <view class="info-grid">
            <view class="grid-item">
              <text class="label">目前薪资</text>
              <text class="value salary">{{ jobInfo.currentSalary }}</text>
            </view>
            <view class="grid-item">
              <text class="label">入职时间</text>
              <text class="value">{{ jobInfo.startDate }}</text>
            </view>
            <view class="grid-item">
              <text class="label">工作地点</text>
              <text class="value">{{ jobInfo.location }}</text>
            </view>
            <view class="grid-item">
              <text class="label">部门</text>
              <text class="value">{{ jobInfo.department }}</text>
            </view>
          </view>
        </view>

        <!-- 推荐信息卡片 -->
        <view class="detail-card">
          <view class="card-title">推荐信息</view>
          <view class="referral-info">
            <view class="referrer">
              <image class="avatar" :src="jobInfo.referrer.avatar" mode="aspectFit"></image>
              <view class="referrer-info">
                <text class="name">{{ jobInfo.referrer.name }}</text>
                <text class="title">{{ jobInfo.referrer.title }}</text>
              </view>
            </view>
            <view class="referral-date">
              推荐时间：{{ jobInfo.referralDate }}
            </view>
          </view>
        </view>

        <!-- 工作福利卡片 -->
        <view class="detail-card">
          <view class="card-title">工作福利</view>
          <view class="benefits-list">
            <view class="benefit-item" v-for="(benefit, index) in jobInfo.benefits" :key="index">
              <text class="benefit-text">{{ benefit }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 添加悬浮底部按钮 -->
      <view class="floating-footer" @tap="contactHR">
        <view class="contact-btn">
          <text class="icon">👋</text>
          <text class="text">问题联系人力资源</text>
        </view>
      </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      jobInfo: {
        companyName: '',
        companyLogo: '',
        industry: '',
        title: '',
        workingDuration: '',
        currentSalary: '',
        startDate: '',
        location: '',
        department: '',
        referrer: {
          name: '',
          avatar: '',
          title: ''
        },
        referralDate: '',
        benefits: []
      }
    }
  },

  onLoad(opt) {
    this.opt = app.getopts(opt);
    this.loadJobInfo();
  },

  onShareAppMessage() {
    return this._sharewx({
      title: '我的工作信息',
      desc: '查看工作详情',
      pic: ''
    });
  },

  onShareTimeline() {
    var sharewxdata = this._sharewx({
      title: '我的工作信息',
      desc: '查看工作详情',
      pic: ''
    });
    var query = (sharewxdata.path).split('?')[1];
    return {
      title: sharewxdata.title,
      imageUrl: sharewxdata.imageUrl,
      query: query
    }
  },

  methods: {
    // 加载工作信息
    async loadJobInfo() {
      this.loading = true;
      try {
        // 模拟接口调用
        const res = await this.mockLoadData();
        this.jobInfo = res.data;
        this.isload = true;
      } catch (error) {
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 模拟数据加载
    mockLoadData() {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockData = {
            data: {
              companyName: '科技有限公司',
              companyLogo: '/static/images/company-logo.png',
              industry: '互联网/软件开发',
              title: '高级前端工程师',
              workingDuration: '1年2个月',
              currentSalary: '25k',
              startDate: '2023-01-15',
              location: '上海市浦东新区',
              department: '技术部-前端组',
              referrer: {
                name: '张三',
                avatar: '/static/images/avatar.png',
                title: '技术总监'
              },
              referralDate: '2022-12-20',
              benefits: [
                '五险一金',
                '年终奖',
                '带薪年假',
                '餐补',
                '交通补助',
                '节日福利',
                '团建活动',
                '免费零食'
              ]
            }
          };
          resolve(mockData);
        }, 1000);
      });
    },

    // 联系人力资源
    contactHR() {
      uni.showModal({
        title: '联系人力资源',
        content: '是否联系人力资源部门？',
        success: (res) => {
          if (res.confirm) {
            // 这里可以替换为实际的联系方式
            uni.makePhoneCall({
              phoneNumber: '10086', // 替换为实际的HR电话
              fail: () => {
                uni.showToast({
                  title: '请稍后重试',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
.working-job-page {
  min-height: 100vh;
  background: linear-gradient(125deg, #000428 0%, #004e92 100%);
  padding: 32rpx 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));

  .company-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 32rpx;
    padding: 40rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
      );
    }

    .company-header {
      display: flex;
      align-items: center;
      margin-bottom: 32rpx;

      .company-logo {
        width: 120rpx;
        height: 120rpx;
        border-radius: 30rpx;
        margin-right: 32rpx;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.1);
        padding: 4rpx;
        transition: all 0.3s ease;

        &:active {
          transform: scale(1.05);
          box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.3);
        }
      }

      .company-info {
        flex: 1;

        .company-name {
          font-size: 44rpx;
          font-weight: 700;
          color: #fff;
          margin-bottom: 12rpx;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
          background: linear-gradient(120deg, #fff, #e0e7ff);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          letter-spacing: 1px;
        }

        .company-industry {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
          padding: 8rpx 24rpx;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 24rpx;
          display: inline-block;
          backdrop-filter: blur(4px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
      }
    }

    .divider {
      height: 1px;
      background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      margin: 32rpx 0;
    }

    .job-basic-info {
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx;
        padding: 24rpx 32rpx;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20rpx;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.1);
          transform: translateX(4rpx);
        }

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.6);
          font-weight: 500;
        }

        .value {
          font-size: 32rpx;
          color: #fff;
          font-weight: 600;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }
      }
    }
  }

  .detail-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 32rpx;
    padding: 40rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
      );
    }

    .card-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #fff;
      margin-bottom: 36rpx;
      position: relative;
      padding-left: 32rpx;
      display: flex;
      align-items: center;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8rpx;
        height: 36rpx;
        background: linear-gradient(180deg, #60a5fa, #3b82f6);
        border-radius: 4rpx;
        box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.5);
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 24rpx;

      .grid-item {
        padding: 28rpx;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 24rpx;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.1);
          transform: translateY(2rpx);
        }

        .label {
          font-size: 26rpx;
          color: rgba(255, 255, 255, 0.6);
          margin-bottom: 12rpx;
          display: block;
        }

        .value {
          font-size: 32rpx;
          color: #fff;
          font-weight: 600;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);

          &.salary {
            background: linear-gradient(120deg, #ffd700, #ffa500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 40rpx;
            font-weight: 700;
          }
        }
      }
    }

    .referral-info {
      .referrer {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
        padding: 28rpx;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 24rpx;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.1);
          transform: translateY(2rpx);
        }

        .avatar {
          width: 96rpx;
          height: 96rpx;
          border-radius: 50%;
          margin-right: 24rpx;
          border: 2px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;

          &:active {
            transform: scale(1.1);
            border-color: rgba(255, 255, 255, 0.3);
          }
        }

        .referrer-info {
          .name {
            font-size: 32rpx;
            color: #fff;
            font-weight: 600;
            margin-bottom: 8rpx;
            display: block;
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
          }

          .title {
            font-size: 26rpx;
            color: rgba(255, 255, 255, 0.6);
            padding: 6rpx 20rpx;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20rpx;
            display: inline-block;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.1);
          }
        }
      }

      .referral-date {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.6);
        padding: 20rpx;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20rpx;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
    }

    .benefits-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;

      .benefit-item {
        padding: 16rpx 32rpx;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 24rpx;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.1);
          transform: translateY(2rpx);
        }

        .benefit-text {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
          background: linear-gradient(120deg, #fff, #e0e7ff);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(40rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .company-card {
    animation: slideUp 0.6s ease-out forwards;
  }

  .detail-card {
    opacity: 0;
    animation: slideUp 0.6s ease-out forwards;
  }

  .detail-card:nth-child(2) {
    animation-delay: 0.2s;
  }

  .detail-card:nth-child(3) {
    animation-delay: 0.3s;
  }

  .detail-card:nth-child(4) {
    animation-delay: 0.4s;
  }
}

.floating-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 32rpx calc(env(safe-area-inset-bottom) + 24rpx);
  z-index: 999;
  pointer-events: none;
  background: linear-gradient(180deg, 
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.3) 100%
  );
  backdrop-filter: blur(10px);

  .contact-btn {
    height: 88rpx;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: float 3s ease-in-out infinite;
    
    @keyframes float {
      0%, 100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-6rpx);
      }
    }
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 50%;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0) 100%
      );
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
    }

    .icon {
      font-size: 32rpx;
      margin-right: 12rpx;
      animation: wave 2s ease-in-out infinite;
      
      @keyframes wave {
        0%, 100% {
          transform: rotate(0deg);
        }
        50% {
          transform: rotate(15deg);
        }
      }
    }

    .text {
      font-size: 30rpx;
      color: #fff;
      font-weight: 600;
      letter-spacing: 1px;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }
  }
}
</style> 