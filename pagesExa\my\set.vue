<template>
<view class="container">
	<block v-if="isload">
		<view class="content">
			<view class="info-item" style="height:136rpx;line-height:136rpx">
				<view class="t1" style="flex:1;">头像</view>
				<image :src="userinfo.headimg" style="width:88rpx;height:88rpx;" @tap="uploadHeadimg"/>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setnickname">
				<view class="t1">昵称</view>
				<view class="t2">{{userinfo.nickname}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<view class="content">
			<view class="info-item" @tap="goto" data-url="setidcard">
				<view class="t1">身份证认证</view>
				<view class="t2">{{userinfo.realname}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setrealname">
				<view class="t1">姓名</view>
				<view class="t2">{{userinfo.realname}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="settel">
				<view class="t1">手机号</view>
				<view class="t2">{{userinfo.tel}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setsex">
				<text class="t1">性别</text>
				<text class="t2" v-if="userinfo.sex==1">男</text>
				<text class="t2" v-else-if="userinfo.sex==2">女</text>
				<text class="t2" v-else>未知</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setbirthday">
				<text class="t1">生日</text>
				<text class="t2">{{userinfo.birthday}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="seteducation">
				<text class="t1">学历</text>
				<text class="t2">{{userinfo.education || '未设置'}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setmaritalstatus">
				<text class="t1">情感状态</text>
				<text class="t2">{{userinfo.marital_status || '未设置'}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setprofession">
				<text class="t1">职业</text>
				<text class="t2">{{userinfo.profession || '未设置'}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="editaddress">
				<view class="t1">地理位置</view>
				<view class="t2">{{userinfo.full_address}} </view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		
		 <view class="content">
			<view class="info-item" @tap="goto" data-url="setweixin">
				<view class="t1">微信号</view>
				<view class="t2">{{userinfo.weixin}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setaliaccount">
				<view class="t1">支付宝账号</view>
				<view class="t2">{{userinfo.aliaccount}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setbankinfo">
				<text class="t1">银行卡</text>
				<text class="t2">{{userinfo.bankname ? '已设置' : ''}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		
		<view class="content">
			<view class="info-item" @tap="goto" data-url="/pages/address/address">
				<view class="t1">收货地址</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<view class="content" v-if="userinfo.haspwd==1">
			<view class="info-item" @tap="goto" data-url="/pagesExa/my/setpwd">
				<view class="t1">修改密码</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<view class="content">
			<view class="info-item" @tap="goto" data-url="/pagesB/login/login">
				<view class="t1">切换账号</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<view class="content" v-if="!isAutoLoginEnabled() && member_auto_addlogin != 1">
			<view class="info-item" @tap="logout">
				<view class="t1">退出登录</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<!-- #ifdef APP-PLUS -->
		<view class="content" v-if="!isAutoLoginEnabled() && member_auto_addlogin != 1">
			<view class="info-item" @tap="delaccount">
				<view class="t1">注销账号</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<!-- #endif -->
		 <!-- 底部返回按钮 -->
		    <view class="content">
		      <view class="back-button" @tap="goBack">
		        <text class="t1">返回</text>
		      </view>
		    </view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			userinfo:{},
			member_auto_addlogin: 0, // 会员自动添加登录：1=开启，0=关闭
			member_auto_reg: 0, // 游客自动注册会员：1=开启，0=关闭
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/set', {}, function (data) {
				that.loading = false;
				that.userinfo = data.userinfo;
				
				// 获取系统设置，检查是否开启了免登录和自动注册
				that.member_auto_addlogin = data.member_auto_addlogin || 0;
				that.member_auto_reg = data.member_auto_reg || 0;
				
				// 添加调试信息
				console.log('退出登录调试 - member_auto_addlogin:', that.member_auto_addlogin);
				console.log('退出登录调试 - member_auto_reg:', that.member_auto_reg);
				console.log('退出登录调试 - isAutoLoginEnabled:', that.isAutoLoginEnabled());
				
				that.isload = true;
			});
		},
		uploadHeadimg:function(){
			var that = this;
			app.chooseImage(function(urls){
				var headimg = urls[0];
				that.userinfo.headimg = headimg;
				app.post('ApiMy/setfield',{headimg:headimg});
			},1)
		},
		delaccount:function(){
			app.confirm('注销账号后该账号下的所有数据都将删除并且无法恢复，确定要注销吗？',function(){
				app.showLoading('注销中');
				app.get('ApiMy/delaccount', {}, function (data) {
					app.showLoading(false);
					if(data.status == 1){
						app.alert(data.msg,function(){
							app.goto('/pages/index/index');
						});
					}else{
						app.alert(data.msg);
					}
				});
			})
		},
		// 判断是否启用了自动登录（免登录）
		isAutoLoginEnabled: function() {
			return this.member_auto_addlogin == 1;
		},
		
		logout:function(){
			var that = this;
			
			// 如果启用了免登录，显示提示
			if(this.isAutoLoginEnabled()) {
				app.alert('系统已开启免登录功能，退出登录后将自动重新登录');
				return;
			}
			
			that.loading = true;
			app.get('ApiIndex/logout', {}, function (data) {
				app.showLoading(false);
				if(data.status == 0){
					app.alert(data.msg);
				}
			});
		},
		  // 返回功能
		    goBack: function () {
		      uni.navigateBack({
		        delta: 1
		      });
		    },
		// 添加goto导航方法
		goto: function(e) {
			var url = '';
			if(typeof e === 'object') {
				url = e.currentTarget.dataset.url;
			} else {
				url = e;
			}
			
			// 检查是否是pagesExa/my/目录下的页面，如果是相对路径则补全前缀
			if(url && !url.startsWith('/') && !url.includes('://')) {
				url = '/pagesExa/my/' + url;
			}
			
			app.goto(url);
		}
  }
};
</script>
<style>
.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}


/* 返回按钮样式 */
.back-button {
  width: 100%;
  background: #b60000;
  color: #fff;
  text-align: center;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 50px;
  margin-top: 20rpx;
}

.back-button .t1 {
  font-size: 30rpx;
  color: #fff;
}
</style>