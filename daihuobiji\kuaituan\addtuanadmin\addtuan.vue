<template>
    <view class="container">
        <!-- 头部背景 -->
        <view style="background: linear-gradient(45deg, rgb(253, 74, 70) 0%, rgba(253, 74, 70, 0.8) 100%);">
            <view class="dp-userinfo2">
                <view class="flex" style="margin-top: 80px;">
                    <view class="flex">
                        <view style="width: 60px; height: 60px; background: #FFF8AF; border-radius: 5px; position: relative;">
                            <image
                                style="width: 30px; height: 30px; position: absolute; left: 0; right: 0; top: 0; bottom: 0; margin: auto;"
                                :src="pre_url+'/static/icon/photo.png'"></image>
                        </view>
                        <view style="font-size: 14px; margin-left: 10px; color: #fff;">快团用户</view>
                    </view>
                    <view class="btn-b">已开启智能背景</view>
                </view>
            </view>
        </view>

        <!-- 返回按钮 -->
        <view style="position: fixed; z-index: 1000; top: 10px; left: 10px;">
            <image style="width: 30px; height: 30px;" :src="pre_url+'/static/img/goback.png'" @click="goBack"></image>
        </view>

        <!-- 顶部导航标签 -->
        <view style="height: 100px; position: fixed; top: 0; width: 100%; background: #fff; z-index: 100;" class="load-list"
            v-if="showTab">
            <view style="display: flex; justify-content: space-between; padding: 70px 50px 0 50px;">
                <view :class="toView === 'tgjs' ? 'on_tab' : ''" @click="scrollToElement('tgjs')">介绍</view>
                <view :class="toView === 'tgsp' ? 'on_tab' : ''" @click="scrollToElement('tgsp')">商品</view>
                <view :class="toView === 'tgsz' ? 'on_tab' : ''" @click="scrollToElement('tgsz')">设置</view>
            </view>
        </view>

    <!-- 跑马灯信息 -->
    <view style="background: #FFF9E3; border-radius: 8px; margin: -15px 11px 11px; padding: 8px; overflow: hidden;" class="flex2">
        <MarqueeComponent :duration="10">
            <p v-for="(notice, index) in noticeList" :key="index">
                {{ notice.title }} <!-- 假设公告接口返回的每条公告包含标题 -->
            </p>
        </MarqueeComponent>
        <img style="width: 15px; height: 15px;" :src="pre_url+'/static/icon/close.png'"></img>
    </view>


        <!-- 团购介绍 -->
        <view id="tgjs" class="dp-userinfo-order" style="margin: 0px 11px 11px; padding: 10px 0;">
            <view
                style="display: flex; justify-content: space-between; padding-bottom: 15px; align-items: center; border-bottom: 1px solid #eee; padding:10px;">
                <view class="tit-m">团购介绍</view>
                <view class="btn-m" style="">复制已有团购</view>
            </view>

            <view style="padding: 0 10px;">
                <view style="padding: 12px 0; border-bottom: 1px solid #eee;">
                    <input style="font-size: 14px;" placeholder="请输入团购活动标题" v-model="title" />
                </view>

                <view style="padding: 12px 0;" v-if="showTxt">
                    <input style="font-size: 14px;" placeholder="请输入团购活动内容" v-model="content" @blur="editTxt" />
                </view>
            </view>

            <view style="padding: 0 10px;" v-if="!showTxt">
                <view style="padding-top: 10px; padding-bottom: 40px;" v-for="(item, index) in items" :key="index">
                    <view class="flex">
                        <view>{{item.txt}}</view>
                        <view class="flex">
                            <view class="mz" :style="index == 0 ? 'color:#ccc;' : ''" @click="toTop(index)">上移</view>
                            <view class="mz" :style="items.length == (index + 1) ? 'color:#ccc;' : ''" @click="toBottom(index)">下移</view>
                            <view class="mz" :style="index == 0 ? 'color:#ccc;' : ''" @click="toOne(index)">置顶</view>
                            <view class="mz" @click="toAdd(index)">添加</view>
                            <view class="mz" @click="delItem(index)">删除</view>
                        </view>
                    </view>

                    <view style="padding-top: 10px;">
                        <input v-if="item.txt == '文字'" style="font-size: 14px;" placeholder="请输入团购活动内容"
                            :value="item.value" @input="iptItem($event, index)" />
                        <image v-if="item.txt == '大图'" style="width: 100%;" mode="widthFix" :src="item.value"></image>
                        <view v-if="item.txt == '小图'">
                            <view style="display: flex; flex-wrap: wrap; padding-top:20rpx;">
                                <view v-for="(it, idx) in item.value" :key="idx" class="layui-imgbox">
                                    <view class="layui-imgbox-close" @tap="removeimg(idx, index)">
                                        <image :src="pre_url+'/static/img/ico-del.png'"></image>
                                    </view>
                                    <view class="layui-imgbox-img">
                                        <image :src="it" @tap="previewImage(it)" :data-url="it" mode="widthFix"></image>
                                    </view>
                                </view>
                                <view class="uploadbtn"
                                    :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'"
                                    @tap="uploadSmallimg(index)" v-if="item.value.length < 9">
                                </view>
                            </view>
                        </view>
                        <view v-if="item.txt == '视频'">
                            <view class="flex-y-center" style="width:100%; padding:20rpx 0; margin-top:20rpx;">
                                <image :src="pre_url+'/static/img/uploadvideo.png'"
                                    style="width:200rpx; height:200rpx; background:#eee;" @tap="uploadVideo"></image>
                            </view>
                            <input type="text" hidden="true" name="video" :value="video" maxlength="-1"></input>
                        </view>
                    </view>
                </view>
            </view>

            <view style="display: flex; margin-top: 40px; flex-wrap: wrap;">
                <view style="text-align: center; width: 20%; margin-top: 10px;" v-for="(item, index) in tabs" :key="index"
                    @click="goCom(item.txt)">
                    <image style="width: 20px;" :src="item.img" mode="widthFix"></image>
                    <view>{{item.txt}}</view>
                </view>
            </view>
        </view>

      <!-- 团购商品 -->
      <view id="tgsp" class="dp-userinfo-order" style="margin: 0px 11px 11px;">
          <view style="display: flex; justify-content: space-between; margin-bottom: 15px; align-items: center;">
              <view class="tit-m">团购商品</view>
              <view class="btn-m" @click="navigateToSelectGoods">从商品库导入</view>
          </view>
      
          <!-- 已选择的商品显示部分 -->
          <view v-if="selectedGoods.length === 0">
              <view style="text-align: center; padding: 20px;">尚未选择商品</view>
          </view>
          <view v-else>
              <view v-for="(item, index) in selectedGoods" :key="item.id"
                  style="padding: 10px; background: #F6F6F6; margin-top: 10px; border-radius: 10rpx; position: relative;">
                  <view class="ltn">{{ index + 1 }}</view>
                  <view class="flex">
                      <view></view>
                      <view class="flex">
                          <view class="mz" :style="index == 0 ? 'color:#ccc;' : ''" @click="goodTop(index)">上移</view>
                          <view class="mz" :style="selectedGoods.length == (index + 1) ? 'color:#ccc;' : ''" @click="goodBottom(index)">下移</view>
                          <view class="mz" :style="index == 0 ? 'color:#ccc;' : ''" @click="goodOne(index)">置顶</view>
                          <view class="mz" @click="removeSelectedGood(index)">删除</view>
                      </view>
                  </view>
                  <view class="ml">
                      <view style="font-size: 12px; width: 60px;">名称</view>
                      <input style="font-size: 12px;" placeholder="商品名称" v-model="item.name" />
                  </view>
                  <view class="ml">
                      <view style="font-size: 12px; width: 60px;">描述</view>
                      <input style="font-size: 12px;" placeholder="商品描述" v-model="item.sellpoint" />
                  </view>
                 <!-- <view class="ml">
                      <view style="font-size: 12px; width: 60px;">规格</view>
                      <input style="font-size: 12px;" placeholder="规格信息" v-model="item.specifications" />
                  </view> -->
                  <view class="ml">
                      <view style="font-size: 12px; width: 60px;">价格(￥)</view>
                      <input style="font-size: 12px;" type="number" placeholder="价格" v-model="item.sell_price" />
                  </view>
                  <view class="ml">
                      <view style="font-size: 12px; width: 60px;">库存</view>
                      <input style="font-size: 12px;" type="number" placeholder="库存" v-model="item.stock" />
                  </view>
              </view>
          </view>
      
          <!-- 添加商品按钮 -->
          <view class="abtn" @click="navigateToSelectGoods">
              + 添加商品
          </view>
      </view>


        <!-- 团购设置 -->
        <view id="tgsz" class="dp-userinfo-order" style="margin: 0px 11px 11px; padding: 10px 0;">
            <view
                style="display: flex; justify-content: space-between; padding-bottom: 15px; align-items: center; border-bottom: 1px solid #eee; padding:10px;">
                <view class="tit-m">团购设置</view>
            </view>

            <view style="padding: 10px 0; border-bottom:1px solid #eee; margin: 0 10px;" class="flex">
                <view style="font-size: 13px;">物流方式</view>
                <view class="flex">
                    <view>未设置</view>
                    <image style="width: 12px;" :src="pre_url+'/static/icon/right.png'" mode="widthFix"></image>
                </view>
            </view>

            <view style="padding: 10px 0; border-bottom:1px solid #eee; margin: 0 10px;" class="flex">
                <view style="font-size: 13px;">团购时间</view>
                <view class="flex">
                    <view>发团即开始，7天后结束</view>
                    <image style="width: 12px;" :src="pre_url+'/static/icon/right.png'" mode="widthFix"></image>
                </view>
            </view>

            <view style="padding: 10px 0; border-bottom:1px solid #eee; margin: 0 10px;" class="flex">
                <view style="font-size: 13px;">开团通知推送</view>
                <view class="flex">
                    <view>全部订阅成员</view>
                    <image style="width: 12px;" :src="pre_url+'/static/icon/right.png'" mode="widthFix"></image>
                </view>
            </view>

            <view style="padding: 10px 0; margin: 0 10px;" class="flex">
                <view>
                    <view style="font-size: 13px;">更多团购设置</view>
                    <view style="color: #aaa; font-size: 10px;">优惠设置、帮卖设置、隐私设置</view>
                </view>
                <image style="width: 12px;" :src="pre_url+'/static/img/arrowdown.png'" mode="widthFix"></image>
            </view>
        </view>

        <!-- 提交按钮 -->
        <view class="bll">
                   <button class="btn1" type="button" @click="formsubmit">保存并预览</button>
                   <button class="btn2" type="button" @click="formsubmit">发布团购</button>
               </view>
    </view>
</template>
<script>
import MarqueeComponent from '../components/MarqueeComponent.vue';

var app = getApp();

export default {
    components: {
        MarqueeComponent
    },
    data() {
        return {
            tabs: [{
                txt: '大图',
                img: '../../static/daihuo/dt.png'
            }, {
                txt: '小图',
                img: '../../static/daihuo/xt.png'
            }, {
                txt: '视频',
                img: '../../static/daihuo/sp.png'
            }, {
                txt: '文字',
                img: '../../static/daihuo/wz.png'
            }, {
                txt: '标签',
                img: '../../static/daihuo/bq.png'
            }, {
                txt: '加粉',
                img: '../../static/daihuo/jf.png'
            }, {
                txt: '承诺',
                img: '../../static/daihuo/cl.png'
            }],
            items: [],
            clist: [], // 分类列表，如果有的话
            cindex: -1, // 选中的分类索引

            showTab: false,
            showTxt: true,
            pre_url: app.globalData.pre_url,
            keyword: '',
            selectedGoods: [], // 存储选择的商品详细信息
            toView: 'tgjs',
            title: '', // 团购活动标题
            content: '', // 团购活动内容
            mobile: '', // 联系手机
            video: '', // 视频链接或路径
            pics: [], // 图片列表
			 noticeList: [] // 存储公告列表
            // 其他需要的 data 属性
        };
    },

    onLoad() {
        // 监听返回页面传递的数据
		    this.fetchNotices(); // 页面加载时请求公告信息
        const eventChannel = this.getOpenerEventChannel();
        eventChannel.on('selectedGoods', (data) => {
            if (data && Array.isArray(data)) {
                console.log('接收到的商品ID数组:', data);
                this.fetchSelectedGoodsDetails(data);
            }
        });
    },

    onPageScroll(e) {
        if (e.scrollTop > 100) {
            this.showTab = true;
        } else {
            this.showTab = false;
        }
    },

    methods: {
        goBack() {
            uni.navigateBack();
        },
   fetchNotices() {
         var that = this;
         app.post('/Apidaihuoyiuan/tongzhilist', {}, function(res) {
             if (res.status === 1 && Array.isArray(res.data)) {
                 // 遍历公告列表，解析 content 字段
                 res.data.forEach(function(item) {
                     if (item.content) {
                         try {
                             item.parsedContent = JSON.parse(item.content); // 解析 content 字段
                         } catch (error) {
                             console.error('解析 content 字段失败:', error);
                         }
                     }
                 });
                 that.noticeList = res.data;
             } else {
                 uni.showToast({
                     title: res.msg || '获取公告失败1',
                     icon: 'none'
                 });
             }
         }, function(error) {
             uni.showToast({
                 title: '网络错误，请稍后再试',
                 icon: 'none'
             });
             console.error('获取公告失败:', error);
         });
     },
        goCom(txt) {
            this.showTxt = false;
            if (txt == '文字') {
                this.items.push({
                    txt: '文字',
                    value: ''
                });
            } else if (txt == '大图') {
                this.uploadBigimg('大图');
            } else if (txt == '小图') {
                this.items.push({
                    txt: '小图',
                    value: []
                });
            } else if (txt == '视频') {
                this.items.push({
                    txt: '视频'
                });
            }
        },

        scrollToElement(id) {
            this.toView = id;
            uni.createSelectorQuery().select('#' + id).boundingClientRect(res => {
                if (res) {
                    uni.pageScrollTo({
                        scrollTop: res.top + (uni.getSystemInfoSync().screenHeight / 2), // 适当调整滚动位置
                        duration: 300
                    });
                }
            }).exec();
        },

        uploadBigimg(txt) {
            var that = this;
            app.chooseImage(function(urls) {
                that.items.push({
                    txt: '大图',
                    value: urls[0]
                });
            }, 1);
        },

        uploadSmallimg(ind) {
            var that = this;
            app.chooseImage(function(urls) {
                that.items[ind].value = that.items[ind].value.concat(urls);
            }, 9);
        },

        editTxt(e) {
            this.items.push({
                txt: '文字',
                value: e.detail.value
            });
            this.showTxt = false;
        },

        // 上移
          toTop(index) {
              if (index > 0) { // 确保不是第一个元素
                  const temp = this.items[index];
                  this.$set(this.items, index, this.items[index - 1]);
                  this.$set(this.items, index - 1, temp);
              }
          },
      
          // 下移
          toBottom(index) {
              if (index < this.items.length - 1) { // 确保不是最后一个元素
                  const temp = this.items[index];
                  this.$set(this.items, index, this.items[index + 1]);
                  this.$set(this.items, index + 1, temp);
              }
          },
      
          // 置顶
          toOne(index) {
              if (index > 0) { // 确保不是第一个元素
                  const item = this.items.splice(index, 1)[0]; // 移除当前项
                  this.items.unshift(item); // 将该项插入到数组首位
              }
          },
      
          // 删除
          delItem(index) {
              this.items.splice(index, 1); // 从 items 数组中移除当前项
          },
		  
		   // 上移商品
		      goodTop(index) {
		          if (index > 0) {
		              const temp = this.selectedGoods[index];
		              this.$set(this.selectedGoods, index, this.selectedGoods[index - 1]);
		              this.$set(this.selectedGoods, index - 1, temp);
		          }
		      },
		  
		      // 下移商品
		      goodBottom(index) {
		          if (index < this.selectedGoods.length - 1) {
		              const temp = this.selectedGoods[index];
		              this.$set(this.selectedGoods, index, this.selectedGoods[index + 1]);
		              this.$set(this.selectedGoods, index + 1, temp);
		          }
		      },
		  
		      // 置顶商品
		      goodOne(index) {
		          if (index > 0) {
		              const item = this.selectedGoods.splice(index, 1)[0];
		              this.selectedGoods.unshift(item);
		          }
		      },
		  
		      // 删除商品
		      removeSelectedGood(index) {
		          this.selectedGoods.splice(index, 1);
		      },
        removeSelectedGood(ind) {
            let that = this;
            uni.showModal({
                content: '确定删除商品吗？',
                success(res) {
                    if (res.confirm) {
                        that.selectedGoods.splice(ind, 1);
                    }
                }
            });
        },

        removeimg(idx, parentIndex) {
            this.items[parentIndex].value.splice(idx, 1);
        },

        previewImage(url) {
            uni.previewImage({
                current: url,
                urls: [url]
            });
        },

        uploadVideo() {
            var that = this;
            // 根据您的需求实现视频上传逻辑
            // 示例：选择视频后上传并获取URL
            uni.chooseVideo({
                success: function(res) {
                    // 假设上传成功后获取视频URL
                    // 这里需要根据实际情况实现上传逻辑
                    that.video = res.tempFilePath; // 示例，实际应为上传后的URL
                },
                fail: function(err) {
                    console.error('选择视频失败:', err);
                }
            });
        },

        /**
         * 导航到商品选择页面
         */
        navigateToSelectGoods() {
            const that = this;
            uni.navigateTo({
                url: '/daihuobiji/kuaituan/selectgoods',
                events: {
                    selectedGoods: function(data) {
                        // 接收到数据后，使用API获取商品详细信息
                        console.log('接收到的商品ID数组:', data);
                        that.fetchSelectedGoodsDetails(data);
                    }
                }
            });
        },

        /**
         * 通过API获取选中商品的详细信息
         * @param {Array} ids - 选中的商品ID数组
         */
        fetchSelectedGoodsDetails(ids) {
            var that = this;

            if (!Array.isArray(ids) || ids.length === 0) {
                return;
            }

            // 显示加载提示
            uni.showLoading({
                title: '加载商品中...'
            });

            // 假设后端API为 'ApiDaihuoyiuan/getProducts'，接受参数为商品ID数组
            app.post('Apidaihuoyiuan/getProducts', { ids: ids }, function(res) {
                uni.hideLoading();
                if (res.status === 1 && Array.isArray(res.data)) {
                    that.selectedGoods = res.data;
                } else {
                    uni.showToast({
                        title: res.msg || '获取商品详情失败',
                        icon: 'none'
                    });
                }
            }, function(error) {
                uni.hideLoading();
                uni.showToast({
                    title: '网络错误，请稍后再试',
                    icon: 'none'
                });
                console.error('获取商品详情失败:', error);
            });
        },

        /**
         * 提交带货团信息
         * @param {Object} e - 表单提交事件对象
         */
        formsubmit(e) {
            var that = this;
            console.log(e);
            var clist = that.clist || [];
            var cid = 0;

            if (clist.length > 0) {
                if (that.cindex == -1) {
                    app.error('请选择分类');
                    return false;
                }
                cid = clist[that.cindex].id;
            }

            // 获取表单数据
            var formdata = e.detail.value || {};
            var title = formdata.title || that.title;
            var content = formdata.content || that.content;
            var pics = formdata.pics || that.pics;
            var video = formdata.video || that.video;
            var mobile = formdata.mobile || that.mobile;
            var items = that.items;
            var goods = that.selectedGoods.map(good => good.id); // 商品ID数组

            // 数据验证
            if (!title) {
                app.error('请输入团购活动标题');
                return false;
            }
            if ((!content && items.length === 0) && pics.length === 0) {
                app.error('请输入内容或添加图片');
                return false;
            }
            if (goods.length === 0) {
                app.error('请至少选择一个商品');
                return false;
            }

            // 构建提交数据
            var payload = {
                cid: cid,
                title: title,
                content: content,
                pics: pics,
                video: video,
                mobile: mobile,
                items: items,
                goods: goods
            };

            // 显示加载提示
            app.showLoading(true);

            // 发送 POST 请求
            app.post('Apidaihuoyiuan/mobileadddaihuotuan',{ payload: payload }, function (res) {
                app.showLoading(false);
                if (res.status == 1) {
                    app.success(res.msg);
                    setTimeout(function () {
                        uni.navigateTo({
                                              url: '/daihuobiji/kuaituan/detail?id=' + res.id
                                          });
                    }, 1000);
                } else {
                    app.error(res.msg);
                }
            }, function (error) {
                app.showLoading(false);
                app.error('提交失败，请稍后再试');
                console.error('提交带货团失败:', error);
            });
        },
    }
}
</script>


<style>
    .container {
        background: #F6F6F6;
        padding-bottom: 40px;
    }

    .dp-userinfo2 {
        width: 100%;
        height: 180px;
        display: flex;
        flex-direction: column;
        position: relative;
        background: url(https://qixian.zhonghengyikang.com.cn/static/imgsrc/userinfobg.png) 0% 0% / 100% no-repeat;
        margin: 0px;
        padding: 11px;
    }

    .dp-userinfo-order {
        background: #fff;
        padding: 15px 10px;
        border-radius: 8px;
    }

    .tit-m {
        font-size: 15px;
        font-weight: bold;
    }

    .btn-m {
        border: 1px solid rgb(253, 74, 70);
        border-radius: 3px;
        color: rgb(253, 74, 70);
        font-size: 10px;
        padding: 2px 5px;
    }

    .btn-b {
        border: 1px solid #fff;
        border-radius: 3px;
        color: #fff;
        font-size: 10px;
        padding: 2px 5px;
        background: #00000011;
    }

    .flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

.flex2 {
    display: flex;
    flex-wrap: nowrap; /* 控制是否允许换行，默认不换行 */
    justify-content: space-between; /* 项目之间的距离自动分配 */
    align-items: center; /* 垂直方向居中对齐 */
    gap: 10px; /* 控制项目之间的间距，可以根据需要调整 */
    padding: 0 10px; /* 可以添加一些内边距，让内容与边框保持距离 */
    box-sizing: border-box; /* 确保 padding 包含在元素的总宽度和高度内 */
  
}

    .load-list {
        opacity: 1;
        animation-name: list;
        animation-duration: 0.1s;
        animation-timing-function: linear;
        animation-direction: alternate;
    }

    @keyframes list {
        from {
            opacity: 0
        }
        to {
            opacity: 1
        }
    }

    .mz {
        border: 1px solid #ccc;
        border-radius: 3px;
        padding: 1px 4px;
        margin-left: 10px;
        font-size: 10px;
    }

    .layui-imgbox {
        margin-right: 16rpx;
        margin-bottom: 10rpx;
        font-size: 24rpx;
        position: relative;
    }

    .layui-imgbox-close {
        position: absolute;
        display: block;
        width: 32rpx;
        height: 32rpx;
        right: -16rpx;
        top: -16rpx;
        z-index: 90;
        color: #999;
        font-size: 32rpx;
        background: #fff
    }

    .layui-imgbox-close image {
        width: 100%;
        height: 100%
    }

    .layui-imgbox-img {
        display: block;
        width: 200rpx;
        height: 200rpx;
        padding: 2px;
        border: #d3d3d3 1px solid;
        background-color: #f6f6f6;
        overflow: hidden
    }

    .layui-imgbox-img>image {
        max-width: 100%;
    }

    .uploadbtn {
        position: relative;
        height: 200rpx;
        width: 200rpx
    }

    .search-bar {
        display: flex;
        align-items: center;
    }

    .search-box {
        display: flex;
        align-items: center;
        height: 80rpx;
        border-radius: 10rpx;
        background-color: #f5f5f5;
        flex: 1;
    }

    .search-box .img {
        width: 24rpx;
        height: 24rpx;
        margin-right: 10rpx;
        margin-left: 30rpx;
    }

    .search-box .search-text {
        font-size: 24rpx;
        color: #C2C2C2;
        width: 100%;
    }

    .ml {
        display: flex;
        margin-top: 30px;
        align-items: center;
    }

    .ltn {
        position: absolute;
        left: 0;
        top: 0;
        padding: 3px 10px;
        background: #EA594E33;
        color: #EA594E;
        border-radius: 10rpx 0 10rpx 0;
    }

    .abtn {
        border: 1px solid #EA594E;
        text-align: center;
        padding: 10px;
        margin-top: 10px;
        border-radius: 10rpx;
        color: #EA594E;
    }

    .bll {
        position: fixed;
        bottom: 0;
        width: 100%;
        background: #fff;
        padding: 10px;
        display: flex;
        justify-content: space-between;
    }

    .bll .btn1 {
        text-align: center;
        width: 50%;
        border-radius: 10rpx;
        border: 1px solid #EA594E;
        color: #EA594E;
        padding: 10px;
        margin: 0 5px;
    }

    .bll .btn2 {
        text-align: center;
        width: 50%;
        border-radius: 10rpx;
        background: #EA594E;
        color: #fff;
        padding: 10px;
        margin: 0 5px;
    }

    .on_tab {
        border-bottom: 2px solid #EA594E;
        padding-bottom: 5px;
        color: #EA594E
    }
</style>
