<template>
    <view class="data-ranking-container">
      <!-- 顶部背景和标题区域 -->
      <view class="banner-area">
        <view class="radial-bg"></view>
        <view class="title-area">
          <text class="main-title">数据榜</text>
        </view>
        <view class="trophy-container">
          <!-- 奖杯图标 -->
          <view class="trophy-icon">
            <view class="trophy-cup">
              <view class="trophy-top"></view>
              <view class="trophy-middle"></view>
              <view class="trophy-bottom"></view>
              <view class="trophy-base"></view>
            </view>
          </view>
          <view class="trophy-decorations">
            <view class="decoration left-leaf"></view>
            <view class="decoration right-leaf"></view>
            <view class="decoration top-sparkle"></view>
            <view class="decoration bottom-sparkle"></view>
          </view>
          <!-- 彩旗装饰 -->
          <view class="banner-flags">
            <view class="flag-string"></view>
            <view class="flag flag-1"></view>
            <view class="flag flag-2"></view>
            <view class="flag flag-3"></view>
            <view class="flag flag-4"></view>
            <view class="flag flag-5"></view>
          </view>
          <text class="top-text">TOP</text>
        </view>
      </view>
      
      <!-- 排名类型切换 -->
      <view class="type-switcher">
        <view 
          class="type-item" 
          :class="{ active: rankingType === 'business' }" 
          @tap="switchRankingType('business')"
        >队伍排名</view>
        <view 
          class="type-item" 
          :class="{ active: rankingType === 'category' }" 
          @tap="switchRankingType('category')"
        >学校排名</view>
        <!-- <view class="type-item">个人数据</view> -->
      </view>
      
      <!-- 筛选栏 -->
      <view class="filter-bar">
        <!-- 时间类型选择 -->
        <view class="filter-item date-type">
          <picker 
            mode="selector" 
            :range="dateTypeOptions" 
            @change="handleDateTypeChange"
            range-key="label"
          >
            <view class="picker-content">
              <text>{{ currentDateType.label }}</text>
              <text class="iconfont iconxiala"></text>
            </view>
          </picker>
        </view>
        
        <!-- 日期选择 -->
        <view class="filter-item date-picker">
          <picker 
            :mode="datePickerMode" 
            :value="dateValue" 
            :fields="dateFields"
            @change="handleDateChange"
          >
            <view class="picker-content">
              <text>{{ displayDateValue || '选择日期' }}</text>
              <text class="iconfont iconxiala"></text>
            </view>
          </picker>
        </view>
        
        <!-- 学校选择 - 只在队伍排名时显示 -->
        <view class="filter-item category-picker" v-if="rankingType === 'business'">
          <picker 
            mode="selector" 
            :range="categoryOptions" 
            @change="handleCategoryChange"
            range-key="name"
          >
            <view class="picker-content">
              <text>{{ currentCategory.name || '全部学校' }}</text>
              <text class="iconfont iconxiala"></text>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 搜索框 -->
      <view class="search-bar">
        <input 
          class="search-input" 
          type="text" 
          v-model="keyword" 
          :placeholder="rankingType === 'business' ? '搜索队伍名称' : '搜索学校名称'"
          @confirm="handleSearch"
        />
        <button class="search-btn" @tap="handleSearch">搜索</button>
      </view>
      
      <!-- 数据列表 -->
      <view class="ranking-list">
        <!-- 列表头部 -->
        <view class="list-header">
          <view class="header-rank">排名</view>
          <view class="header-info">{{ rankingType === 'business' ? '队伍信息' : '学校信息' }}</view>
          <view class="header-sales">销售总金额(元)</view>
          <!-- <view class="header-detail">详情</view> -->
        </view>
        
        <!-- 列表内容 -->
        <scroll-view scroll-y class="list-content" v-if="rankingList.length > 0">
          <view 
            class="list-item" 
            v-for="(item, index) in rankingList" 
            :key="rankingType === 'business' ? item.business_id : item.category_id"
          >
            <!-- 排名 -->
            <view class="rank-num" :class="{'top-rank': item.rank <= 3}">{{ item.rank }}</view>
            
            <!-- 信息 -->
            <view class="item-info">
              <template v-if="rankingType === 'business'">
                <image class="item-logo" :src="item.logo || '/static/img/shop_addr.png'" mode="aspectFill"></image>
                <text class="item-name">{{ item.name }}</text>
              </template>
              <template v-else>
                <text class="item-name">{{ item.name }}</text>
              </template>
            </view>
            
            <!-- 销售额 -->
            <view class="item-sales">¥ {{ item.sales_price }}</view>
            
            <!-- 详情按钮 -->
           <!-- <view class="item-detail">详情</view> -->
          </view>
        </scroll-view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else>
          <image class="empty-icon" src="/static/img/order.png" mode="aspectFit"></image>
          <text class="empty-text">暂无数据</text>
        </view>
      </view>
      
      <!-- 分页 -->
      <view class="pagination" v-if="totalCount > 0">
        <view class="page-info">共 {{ totalCount }} 条</view>
        <view class="page-controls">
          <view 
            class="page-btn prev" 
            :class="{ disabled: currentPage <= 1 }"
            @tap="prevPage"
          >上一页</view>
          <view class="page-current">{{ currentPage }} / {{ totalPages }}</view>
          <view 
            class="page-btn next" 
            :class="{ disabled: currentPage >= totalPages }"
            @tap="nextPage"
          >下一页</view>
        </view>
      </view>
    </view>
  </template>
  
  <script>
  const app = getApp();
  
  export default {
    data() {
      return {
        // 排名数据
        rankingType: 'business', // 'business'或'category'
        rankingList: [],
        totalCount: 0,
        
        // 筛选条件
        dateTypeOptions: [
          { value: '', label: '全部时间' },
          { value: 'year', label: '按年' },
          { value: 'month', label: '按月' },
          { value: 'day', label: '按日' }
        ],
        currentDateType: { value: '', label: '全部时间' },
        dateValue: '',
        datePickerMode: 'date',
        dateFields: 'day',
        displayDateValue: '',
        
        // 学校
        categoryOptions: [{ id: '', name: '全部学校' }],
        currentCategory: { id: '', name: '全部学校' },
        
        // 搜索
        keyword: '',
        
        // 分页
        currentPage: 1,
        perPage: 10
      };
    },
    
    computed: {
      totalPages() {
        return Math.ceil(this.totalCount / this.perPage) || 1;
      }
    },
    
    onLoad() {
      this.getCategories();
      this.fetchRankingData();
    },
    
    methods: {
      // 切换排名类型
      switchRankingType(type) {
        if (this.rankingType !== type) {
          this.rankingType = type;
          this.currentPage = 1;
          this.fetchRankingData();
        }
      },
      
      // 日期类型变更
      handleDateTypeChange(e) {
        const index = e.detail.value;
        this.currentDateType = this.dateTypeOptions[index];
        
        // 根据日期类型设置日期选择器的模式
        switch (this.currentDateType.value) {
          case 'year':
            this.datePickerMode = 'date';
            this.dateFields = 'year';
            break;
          case 'month':
            this.datePickerMode = 'date';
            this.dateFields = 'month';
            break;
          case 'day':
            this.datePickerMode = 'date';
            this.dateFields = 'day';
            break;
          default:
            this.dateValue = '';
            this.displayDateValue = '';
            break;
        }
        
        // 清空已选日期并重置分页
        this.dateValue = '';
        this.displayDateValue = '';
        this.currentPage = 1;
        this.fetchRankingData();
      },
      
      // 日期变更
      handleDateChange(e) {
        this.dateValue = e.detail.value;
        this.displayDateValue = this.dateValue;
        this.currentPage = 1;
        this.fetchRankingData();
      },
      
      // 学校变更
      handleCategoryChange(e) {
        const index = e.detail.value;
        this.currentCategory = this.categoryOptions[index];
        this.currentPage = 1;
        this.fetchRankingData();
      },
      
      // 搜索
      handleSearch() {
        this.currentPage = 1;
        this.fetchRankingData();
      },
      
      // 上一页
      prevPage() {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.fetchRankingData();
        }
      },
      
      // 下一页
      nextPage() {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.fetchRankingData();
        }
      },
      
      // 获取学校列表
      getCategories() {
        app.get('/ApiBusiness/getBusinessCategories', {}, (res) => {
          if (res.status === 1 && res.data && res.data.length > 0) {
            // 添加所有学校选项
            this.categoryOptions = [{ id: '', name: '全部学校' }].concat(res.data);
          } else {
            console.error('获取学校列表失败:', res.msg || '未知错误');
          }
        });
      },
      
      // 获取排名数据
      fetchRankingData() {
        uni.showLoading({
          title: '加载中...'
        });
        
        const params = {
          ranking_type: this.rankingType,
          pagenum: this.currentPage,
          pernum: this.perPage
        };
        
        // 添加日期筛选
        if (this.currentDateType.value && this.dateValue) {
          params.date_type = this.currentDateType.value;
          params.date_value = this.dateValue;
        }
        
        // 添加学校筛选
        if (this.rankingType === 'business' && this.currentCategory.id) {
          params.category_id = this.currentCategory.id;
        }
        
        // 添加搜索关键词
        if (this.keyword) {
          params.business_keyword = this.keyword;
        }
        
        app.get('ApiBusiness/salesRanking', params, (res) => {
          uni.hideLoading();
          
          if (res.status === 1) {
            this.rankingList = res.data || [];
            this.totalCount = res.total_count || 0;
          } else {
            app.error(res.msg || '获取数据失败');
            this.rankingList = [];
            this.totalCount = 0;
          }
        }, () => {
          uni.hideLoading();
          app.error('网络请求失败');
        });
      }
    }
  };
  </script>
  
  <style>
  /* 全局背景和字体 */
  .data-ranking-container {
    padding: 0;
    min-height: 100vh;
    background-color: #ffffff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }
  
  /* 顶部背景和标题区域 */
  .banner-area {
    position: relative;
    height: 400rpx;
    background: linear-gradient(to bottom, #33d695, #00c480);
    overflow: hidden;
  }
  
  .radial-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: repeating-linear-gradient(0deg, transparent, transparent 20px, rgba(255,255,255,0.05) 20px, rgba(255,255,255,0.05) 40px);
    transform: rotate(45deg) scale(5);
    opacity: 0.7;
  }
  
  .title-area {
    position: absolute;
    top: 80rpx;
    left: 40rpx;
    display: flex;
    align-items: center;
  }
  
  .main-title {
    font-size: 60rpx;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  }
  
  .trophy-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -30%);
    width: 240rpx;
    height: 280rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .trophy-icon {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .trophy-cup {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .trophy-top {
    position: absolute;
    top: 10rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 140rpx;
    height: 40rpx;
    background-color: #ffcc33;
    border-radius: 70rpx 70rpx 0 0;
    box-shadow: 0 -4rpx 0 rgba(0,0,0,0.1) inset;
  }
  
  .trophy-middle {
    position: absolute;
    top: 50rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 120rpx;
    height: 130rpx;
    background-color: #ffcc33;
    border-radius: 10rpx;
    box-shadow: 0 0 0 10rpx #ffcc33, 
                -25rpx 0 0 -3rpx #e6b800,
                25rpx 0 0 -3rpx #ffdb66;
  }
  
  .trophy-bottom {
    position: absolute;
    top: 180rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 70rpx;
    height: 30rpx;
    background-color: #ffcc33;
    border-radius: 0 0 10rpx 10rpx;
  }
  
  .trophy-base {
    position: absolute;
    top: 210rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 120rpx;
    height: 20rpx;
    background-color: #ffb833;
    border-radius: 10rpx;
    box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.2);
  }
  
  .trophy-decorations {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  
  .decoration {
    position: absolute;
  }
  
  .left-leaf {
    position: absolute;
    top: 90rpx;
    left: 20rpx;
    width: 40rpx;
    height: 120rpx;
    background-color: #00aa66;
    border-radius: 50% 20rpx 50% 20rpx;
    transform: rotate(-20deg);
    opacity: 0.9;
  }
  
  .right-leaf {
    position: absolute;
    top: 90rpx;
    right: 20rpx;
    width: 40rpx;
    height: 120rpx;
    background-color: #00aa66;
    border-radius: 20rpx 50% 20rpx 50%;
    transform: rotate(20deg);
    opacity: 0.9;
  }
  
  .top-sparkle {
    position: absolute;
    top: 0;
    right: 60rpx;
    width: 20rpx;
    height: 20rpx;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 0 10rpx white;
    animation: sparkle 2s infinite alternate;
  }
  
  .bottom-sparkle {
    position: absolute;
    bottom: 60rpx;
    left: 40rpx;
    width: 15rpx;
    height: 15rpx;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 0 8rpx white;
    animation: sparkle 1.5s 0.5s infinite alternate;
  }
  
  @keyframes sparkle {
    0% {
      opacity: 0.4;
      transform: scale(0.8);
    }
    100% {
      opacity: 1;
      transform: scale(1.2);
    }
  }
  
  /* 彩旗装饰 */
  .banner-flags {
    position: absolute;
    bottom: 25rpx;
    left: -160rpx;
    width: 560rpx;
    height: 60rpx;
    z-index: 0;
  }
  
  .flag-string {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2rpx;
    background-color: rgba(255, 255, 255, 0.7);
  }
  
  .flag {
    position: absolute;
    top: 0;
    width: 30rpx;
    height: 40rpx;
    border-radius: 2rpx;
  }
  
  .flag-1 {
    left: 40rpx;
    background-color: #ff6666;
    transform: rotate(-5deg);
  }
  
  .flag-2 {
    left: 120rpx;
    background-color: #ffcc33;
    transform: rotate(8deg);
  }
  
  .flag-3 {
    left: 200rpx;
    background-color: #66ccff;
    transform: rotate(-7deg);
  }
  
  .flag-4 {
    left: 280rpx;
    background-color: #ff9966;
    transform: rotate(5deg);
  }
  
  .flag-5 {
    left: 360rpx;
    background-color: #99cc66;
    transform: rotate(-3deg);
  }
  
  .top-text {
    position: absolute;
    bottom: 40rpx;
    left: 50%;
    transform: translateX(-50%);
    font-size: 60rpx;
    color: #ffffff;
    font-weight: 700;
    letter-spacing: 5rpx;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
    z-index: 1;
    background-color: #00aa66;
    padding: 2rpx 40rpx;
    border-radius: 10rpx;
  }
  
  /* 排名类型切换 */
  .type-switcher {
    display: flex;
    justify-content: space-around;
    margin: 0;
    background-color: #33d695;
    padding: 0;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  }
  
  .type-item {
    flex: 1;
    text-align: center;
    padding: 30rpx 10rpx;
    font-size: 32rpx;
    color: rgba(255,255,255,0.8);
    position: relative;
    transition: all 0.3s ease;
  }
  
  .type-item.active {
    color: #ffffff;
    font-weight: 600;
    background-color: transparent;
    position: relative;
  }
  
  .type-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 30%;
    width: 40%;
    height: 6rpx;
    background-color: #ffffff;
    border-radius: 3rpx;
  }
  
  /* 筛选栏 */
  .filter-bar {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    padding: 30rpx 20rpx;
    background-color: #ffffff;
    border-radius: 0;
    margin-bottom: 20rpx;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .filter-item {
    margin-right: 0;
    margin-bottom: 0;
    flex-grow: 1;
  }
  
  .picker-content {
    background-color: #f9f9f9;
    border-radius: 30rpx;
    padding: 16rpx 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    color: #666666;
    border: 1px solid #eeeeee;
    box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.02);
  }
  
  .iconxiala {
    margin-left: 10rpx;
    font-size: 24rpx;
    color: #999999;
  }
  
  /* 搜索栏 */
  .search-bar {
    display: flex;
    padding: 0 20rpx 20rpx;
    background-color: #ffffff;
    border-radius: 0;
    margin-bottom: 10rpx;
  }
  
  .search-input {
    flex: 1;
    height: 76rpx;
    background-color: #f9f9f9;
    border-radius: 38rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
    border: 1px solid #eeeeee;
  }
  
  .search-btn {
    width: 160rpx;
    height: 76rpx;
    line-height: 76rpx;
    margin-left: 20rpx;
    background-color: #33d695;
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
    text-align: center;
    border-radius: 38rpx;
    box-shadow: 0 4rpx 8rpx rgba(51, 214, 149, 0.3);
  }
  
  /* 数据列表 */
  .ranking-list {
    background-color: #ffffff;
    border-radius: 0;
    margin: 0 20rpx 30rpx;
    box-shadow: none;
    overflow: hidden;
  }
  
  .list-header {
    display: flex;
    padding: 25rpx 20rpx;
    background-color: #ffffff;
    font-size: 28rpx;
    color: #999999;
    font-weight: 400;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .header-rank {
    width: 120rpx;
    text-align: center;
  }
  
  .header-info {
    flex: 1;
    padding-left: 10rpx;
  }
  
  .header-sales {
    width: 220rpx;
    text-align: right;
    padding-right: 20rpx;
  }
  
  .header-detail {
    width: 100rpx;
    text-align: center;
  }
  
  .list-content {
    max-height: 900rpx;
  }
  
  .list-item {
    display: flex;
    padding: 25rpx 20rpx;
    border-bottom: 1px solid #f8f8f8;
    align-items: center;
  }
  .list-item:last-child {
    border-bottom: none;
  }
  
  .rank-num {
    width: 120rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
  }
  
  .rank-num.top-rank {
    color: #ff9500;
    font-size: 38rpx;
    font-weight: 600;
  }
  
  .item-info {
    flex: 1;
    display: flex;
    align-items: center;
    padding-left: 10rpx;
  }
  
  .item-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
    border: 1px solid #f0f0f0;
  }
  
  .item-name {
    font-size: 28rpx;
    color: #333333;
    font-weight: 400;
  }
  
  .item-sales {
    width: 220rpx;
    text-align: right;
    font-size: 28rpx;
    font-weight: 500;
    color: #ff6666;
    padding-right: 20rpx;
  }
  
  .item-detail {
    width: 100rpx;
    text-align: center;
    font-size: 26rpx;
    color: #33d695;
    font-weight: 400;
  }
  
  /* 空状态 */
  .empty-state {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 120rpx 0;
    background-color: #ffffff;
  }
  
  .empty-icon {
    width: 220rpx;
    height: 220rpx;
    margin-bottom: 30rpx;
    opacity: 0.7;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
  
  /* 分页 */
  .pagination {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx 0 80rpx;
  }
  
  .page-info {
    font-size: 26rpx;
    color: #999999;
    margin-bottom: 25rpx;
  }
  
  .page-controls {
    display: flex;
    align-items: center;
  }
  
  .page-btn {
    padding: 12rpx 35rpx;
    background-color: #f8f8f8;
    color: #666666;
    font-size: 26rpx;
    border-radius: 6rpx;
    border: 1px solid #eeeeee;
    margin: 0 10rpx;
  }
  
  .page-btn.disabled {
    color: #cccccc;
    border-color: #f0f0f0;
    background-color: #f8f8f8;
  }
  
  .page-current {
    margin: 0 25rpx;
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
  }
  </style> 