# 等级推荐奖励系统接口文档

## 基础信息


> **特别说明**: 系统中用户ID (mid) 是通用变量，后端接口已经在用户登录后自动获取和处理，前端无需每次请求时传递用户ID参数。

## 接口列表

### 1. 获取等级推荐奖励规则列表

获取系统中已配置的推荐奖励规则列表。

**请求路径**: `/ApiLevelreward/getlist`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| session_id | string | 是 | 用户会话ID |
| pagenum | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页条数，默认20 |

**响应结果**:

```json
{
  "status": 1,
  "data": [
    {
      "id": 1,
      "name": "推荐规则名称",
      "from_level": 1,
      "from_level_name": "黄金会员",
      "from_level_ids": "[1,2,3]",
      "from_level_names": ["黄金会员", "白金会员", "钻石会员"],
      "to_level": 2,
      "to_level_name": "白金会员",
      "to_level_ids": "[2,3]",
      "to_level_names": ["白金会员", "钻石会员"],
      "reward_rules": [
        {
          "recommend_count": 5,
          "reward_amount": 100,
          "reward_type": "balance"
        },
        {
          "recommend_count": 10,
          "reward_amount": 250,
          "reward_type": "balance"
        }
      ],
      "status": 1,
      "createtime": 1652345678
    }
  ],
  "count": 10
}
```

**字段说明**:

- `status`: 1表示成功，0表示失败
- `from_level_name`: 推荐人等级名称
- `to_level_name`: 被推荐人等级名称
- `from_level_names`: 推荐人等级名称数组（当有多个等级时）
- `to_level_names`: 被推荐人等级名称数组（当有多个等级时）
- `reward_rules`: 奖励规则数组，每个规则包含推荐人数、奖励金额和奖励类型
  - `reward_type`: 奖励类型，可能的值包括：
    - `balance`: 余额
    - `points`: 积分
    - `commission`: 佣金
    - `contribution`: 贡献值

### 2. 获取推荐奖励记录

获取当前登录会员的推荐奖励记录。

**请求路径**: `/ApiLevelreward/getrecord`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| session_id | string | 是 | 用户会话ID |
| pagenum | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页条数，默认20 |

**响应结果**:

```json
{
  "status": 1,
  "data": [
    {
      "id": 1,
      "from_mid": 10,
      "from_level": 2,
      "from_level_name": "白金会员",
      "to_mid": 20,
      "to_level": 3,
      "to_level_name": "钻石会员",
      "to_nickname": "张三",
      "to_avatar": "https://example.com/avatar.jpg",
      "to_tel": "138****1234",
      "reward_amount": 100,
      "reward_type": "balance",
      "reward_type_name": "余额",
      "createtime": 1652345678,
      "createtime_format": "2022-05-12 15:34:38"
    }
  ],
  "count": 5
}
```

**字段说明**:

- `from_mid`: 推荐人会员ID
- `to_mid`: 被推荐人会员ID
- `reward_type`: 奖励类型（原始值）
- `reward_type_name`: 奖励类型（中文描述）
- `to_nickname`: 被推荐人昵称
- `to_avatar`: 被推荐人头像
- `to_tel`: 被推荐人手机号（已做隐私处理）

### 3. 获取推荐奖励统计数据

获取当前登录会员的推荐奖励统计数据。

**请求路径**: `/ApiLevelreward/getstats`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| session_id | string | 是 | 用户会话ID |

**响应结果**:

```json
{
  "status": 1,
  "data": {
    "total_recommend": 25,
    "today_recommend": 2,
    "total_reward": 1250.50
  }
}
```

**字段说明**:

- `total_recommend`: 推荐总人数
- `today_recommend`: 今日推荐人数
- `total_reward`: 总奖励金额

### 4. 获取奖励系统设置

获取系统奖励设置信息。

**请求路径**: `/ApiLevelreward/getset`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| session_id | string | 否 | 用户会话ID |

**响应结果**:

```json
{
  "status": 1,
  "data": {
    "settings": {
      "id": 1,
      "aid": 100,
      "status": 1,
      "complete_notice": "恭喜您成功推荐会员升级到{level_name}，获得{reward_amount}{reward_type}奖励！"
    },
    "member_levels": [
      {
        "id": 1,
        "name": "普通会员",
        "icon": "图标URL",
        "description": "等级描述",
        "requirements": "成为该等级的要求"
      },
      {
        "id": 2,
        "name": "白金会员",
        "icon": "图标URL",
        "description": "等级描述",
        "requirements": "成为该等级的要求"
      }
    ]
  }
}
```

**字段说明**:

- `settings`: 奖励系统设置
  - `status`: 系统状态，1表示开启，0表示关闭
  - `complete_notice`: 完成推荐的通知模板
- `member_levels`: 会员等级列表

### 5. 获取我的推荐人信息

获取当前登录会员的推荐人信息。

**请求路径**: `/ApiLevelreward/getmyreferrer`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| session_id | string | 是 | 用户会话ID |

**响应结果**:

```json
{
  "status": 1,
  "data": {
    "id": 15,
    "nickname": "李四",
    "avatar": "头像URL",
    "tel": "139****5678",
    "levelid": 3,
    "level_name": "钻石会员"
  }
}
```

**字段说明**:

- 如果没有推荐人，`data`为`null`

### 6. 获取我推荐的会员列表

获取当前登录会员所推荐的会员列表。

**请求路径**: `/ApiLevelreward/getmyreferrals`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| session_id | string | 是 | 用户会话ID |
| pagenum | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页条数，默认20 |

**响应结果**:

```json
{
  "status": 1,
  "data": [
    {
      "id": 20,
      "nickname": "王五",
      "avatar": "头像URL",
      "tel": "137****4321",
      "levelid": 2,
      "level_name": "白金会员",
      "createtime": 1652345678,
      "createtime_format": "2022-05-12 15:34:38"
    }
  ],
  "count": 15
}
```

**字段说明**:

- `data`数组包含我推荐的所有会员信息
- `createtime`: 被推荐人注册时间戳
- `createtime_format`: 格式化的注册时间

### 7. 获取会员等级列表

获取系统中所有会员等级信息。

**请求路径**: `/ApiLevelreward/getlevels`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| session_id | string | 否 | 用户会话ID |

**响应结果**:

```json
{
  "status": 1,
  "data": [
    {
      "id": 1,
      "name": "普通会员",
      "icon": "图标URL",
      "description": "等级描述",
      "requirements": "成为该等级的要求"
    },
    {
      "id": 2,
      "name": "白金会员",
      "icon": "图标URL",
      "description": "等级描述",
      "requirements": "成为该等级的要求"
    }
  ]
}
```

## 错误码说明

| 错误码 | 描述 |
|-------|------|
| -1 | 服务器错误 |
| -4 | 未登录或需要认证 |
| 0 | 业务错误，具体信息见返回的msg字段 |
| 1 | 请求成功 |

## UniApp 调用示例

```javascript
// 获取等级推荐奖励列表示例
getLevelRewards() {
  uni.request({
    url: config.apiBaseUrl + '/ApiLevelreward/getlist',
    method: 'POST',
    data: {
      session_id: uni.getStorageSync('session_id'),
      pagenum: 1,
      limit: 10
    },
    success: (res) => {
      if (res.data.status === 1) {
        this.rewardList = res.data.data;
        this.total = res.data.count;
      } else {
        uni.showToast({
          title: res.data.msg || '获取数据失败',
          icon: 'none'
        });
      }
    },
    fail: () => {
      uni.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  });
}
```

## 注意事项

1. 所有接口都需要在已登录状态下调用，除非特别说明
2. 分页接口的 `pagenum` 从1开始
3. 响应中的 `status` 为1表示成功，其他值表示失败
4. 部分接口返回的会员等级信息可能会根据系统设置动态变化
5. 所有用户相关接口无需传递用户ID (mid)，系统会根据session_id自动识别当前用户身份
6. 后端API在用户登录后会自动维护用户会话状态，前端只需正确传递session_id即可 