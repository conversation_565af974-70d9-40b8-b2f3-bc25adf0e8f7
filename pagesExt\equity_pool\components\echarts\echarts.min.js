/*!
 * ECharts, a JavaScript visualization library (Optimized version)
 * 
 * This is an optimized version of ECharts for the equity pool chart.
 */
(function(global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
        typeof define === 'function' && define.amd ? define(factory) :
        (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.echarts = factory());
})(this, function() {
    'use strict';

    // Enhanced ECharts implementation
    var echarts = {};
    
    // Utility functions
    function throttle(func, wait) {
        var timeout;
        return function() {
            var context = this;
            var args = arguments;
            if (!timeout) {
                timeout = setTimeout(function() {
                    timeout = null;
                    func.apply(context, args);
                }, wait);
            }
        };
    }
    
    function hexToRgb(hex) {
        // 去掉可能的 #
        hex = hex.replace(/^#/, '');
        
        // 处理简写形式（例如 #abc）
        if (hex.length === 3) {
            hex = hex.split('').map(function(c) {
                return c + c;
            }).join('');
        }
        
        var r = parseInt(hex.substring(0, 2), 16);
        var g = parseInt(hex.substring(2, 4), 16);
        var b = parseInt(hex.substring(4, 6), 16);
        
        return [r, g, b];
    }
    
    function getColor(colorOption, defaultColor) {
        if (typeof colorOption === 'string') {
            return colorOption;
        }
        
        if (colorOption && colorOption.type === 'linear') {
            // 线性渐变
            return colorOption;
        }
        
        return defaultColor || '#5470c6';
    }
    
    // Animation frame handler
    var requestAnimFrame = (function() {
        return window.requestAnimationFrame || 
            window.webkitRequestAnimationFrame || 
            window.mozRequestAnimationFrame || 
            function(callback) {
                window.setTimeout(callback, 1000 / 60);
            };
    })();
    
    // Main chart initialization function
    echarts.init = function(dom) {
        if (!dom) {
            console.error('Initialize failed: invalid DOM element');
            return null;
        }
        
        var chartInstance = {
            dom: dom,
            _option: null,
            _animation: {
                enabled: true,
                duration: 1000,
                currentFrame: 0,
                totalFrames: 60
            },
            _tooltipVisible: false,
            _tooltipData: null,
            _tooltipPosition: {x: 0, y: 0},
            _canvas: null,
            _ctx: null,
            _width: 0,
            _height: 0,
            _animating: false,
            
            setOption: function(option) {
                var oldOption = this._option;
                this._option = option;
                
                // 设置适当的默认值
                if (!this._option.animation && this._option.animation !== false) {
                    this._option.animation = {
                        enabled: true,
                        duration: 1000
                    };
                }
                
                // 如果存在旧数据且启用了动画，使用动画过渡
                if (oldOption && option.animation !== false && this._animation.enabled) {
                    this._animating = true;
                    this._animation.currentFrame = 0;
                    this._startAnimation();
                } else {
                    this._render(1); // 不使用动画，直接渲染完整数据
                }
                
                // 初始化或重新初始化事件
                this._initEvents();
            },
            
            _startAnimation: function() {
                var self = this;
                var progress = this._animation.currentFrame / this._animation.totalFrames;
                
                if (progress < 1) {
                    this._render(progress);
                    this._animation.currentFrame++;
                    requestAnimFrame(function() {
                        self._startAnimation();
                    });
                } else {
                    this._render(1);
                    this._animating = false;
                }
            },
            
            _initEvents: function() {
                var self = this;
                
                // 移除旧事件
                if (this._canvas) {
                    this._canvas.removeEventListener('mousemove', this._handleMouseMove);
                    this._canvas.removeEventListener('mouseout', this._handleMouseOut);
                }
                
                // 设置新画布和上下文
                if (!this._canvas) {
                    this._canvas = document.createElement('canvas');
                    this.dom.innerHTML = '';
                    this.dom.appendChild(this._canvas);
                    this._ctx = this._canvas.getContext('2d');
                }
                
                // 更新尺寸
                this._updateSize();
                
                // 添加事件处理
                this._handleMouseMove = throttle(function(e) {
                    var rect = self._canvas.getBoundingClientRect();
                    var x = e.clientX - rect.left;
                    var y = e.clientY - rect.top;
                    
                    self._handleTooltip(x, y);
                }, 50);
                
                this._handleMouseOut = function() {
                    self._tooltipVisible = false;
                    self._render(1);
                };
                
                this._canvas.addEventListener('mousemove', this._handleMouseMove);
                this._canvas.addEventListener('mouseout', this._handleMouseOut);
                
                // 设置窗口大小变化处理
                if (!this._resizeHandler) {
                    this._resizeHandler = throttle(function() {
                        self._updateSize();
                        self._render(1);
                    }, 100);
                    
                    window.addEventListener('resize', this._resizeHandler);
                }
            },
            
            _updateSize: function() {
                var style = window.getComputedStyle(this.dom);
                this._width = parseInt(style.width) || this.dom.clientWidth || 300;
                this._height = parseInt(style.height) || this.dom.clientHeight || 200;
                
                // 设置画布尺寸，考虑设备像素比
                var pixelRatio = window.devicePixelRatio || 1;
                this._canvas.width = this._width * pixelRatio;
                this._canvas.height = this._height * pixelRatio;
                this._canvas.style.width = this._width + 'px';
                this._canvas.style.height = this._height + 'px';
                this._ctx.scale(pixelRatio, pixelRatio);
            },
            
            _handleTooltip: function(x, y) {
                if (!this._option || !this._option.series || !this._option.series.length) return;
                
                var series = this._option.series[0];
                var data = series.data || [];
                var xData = this._option.xAxis && this._option.xAxis.data || [];
                
                if (data.length === 0) return;
                
                // 计算数据点在画布上的位置
                var padding = { left: 50, right: 20, top: 40, bottom: 40 };
                var chartWidth = this._width - padding.left - padding.right;
                var chartHeight = this._height - padding.top - padding.bottom;
                
                // 找到距离鼠标最近的数据点
                var pointWidth = chartWidth / (data.length - 1);
                var index = Math.round((x - padding.left) / pointWidth);
                
                // 确保索引在有效范围内
                if (index >= 0 && index < data.length) {
                    // 获取数据点的位置
                    var dataValue = data[index];
                    var minValue = Math.min.apply(null, data);
                    var maxValue = Math.max.apply(null, data);
                    var valueRange = maxValue - minValue;
                    
                    minValue = Math.max(0, minValue - valueRange * 0.1);
                    maxValue = maxValue + valueRange * 0.1;
                    valueRange = maxValue - minValue;
                    
                    var pointX = padding.left + index * pointWidth;
                    var pointY = padding.top + chartHeight - ((dataValue - minValue) / valueRange) * chartHeight;
                    
                    // 更新提示框数据
                    this._tooltipVisible = true;
                    this._tooltipData = {
                        value: dataValue,
                        xValue: xData[index] || '',
                        index: index
                    };
                    this._tooltipPosition = { x: pointX, y: pointY };
                    
                    // 重新渲染
                    this._render(1);
                }
            },
            
            _render: function(progress) {
                if (!this._option || !this._ctx) return;
                
                // 获取选项
                var option = this._option;
                var ctx = this._ctx;
                var width = this._width;
                var height = this._height;
                
                // 清除画布
                ctx.clearRect(0, 0, width, height);
                
                // 设置基本样式
                ctx.font = '12px Arial';
                ctx.textBaseline = 'middle';
                
                // 绘制背景
                ctx.fillStyle = option.backgroundColor || '#fff';
                ctx.fillRect(0, 0, width, height);
                
                // 绘制标题
                if (option.title && option.title.text) {
                    ctx.fillStyle = option.title.textStyle && option.title.textStyle.color || '#333';
                    ctx.font = (option.title.textStyle && option.title.textStyle.fontSize || 14) + 'px Arial';
                    ctx.textAlign = option.title.left === 'center' ? 'center' : 'left';
                    ctx.fillText(
                        option.title.text, 
                        option.title.left === 'center' ? width / 2 : 20, 
                        20
                    );
                }
                
                // 如果没有系列数据，不绘制图表
                if (!option.series || !option.series.length || !option.series[0].data) return;
                
                var series = option.series[0];
                var data = series.data.slice(); // 复制数据以便于动画
                
                // 如果是线性图表
                if (series.type === 'line') {
                    // 设置内边距
                    var padding = { left: 50, right: 20, top: 40, bottom: 40 };
                    var chartWidth = width - padding.left - padding.right;
                    var chartHeight = height - padding.top - padding.bottom;
                    
                    // 获取数据范围
                    var minValue = Math.min.apply(null, data);
                    var maxValue = Math.max.apply(null, data);
                    var valueRange = maxValue - minValue;
                    
                    // 添加一些间距
                    minValue = Math.max(0, minValue - valueRange * 0.1);
                    maxValue = maxValue + valueRange * 0.1;
                    valueRange = maxValue - minValue;
                    
                    // 对于动画，调整数据值
                    if (progress < 1) {
                        data = data.map(function(value) {
                            return minValue + (value - minValue) * progress;
                        });
                    }
                    
                    // 绘制坐标轴
                    ctx.strokeStyle = '#ccc';
                    ctx.lineWidth = 1;
                    
                    // X轴
                    ctx.beginPath();
                    ctx.moveTo(padding.left, height - padding.bottom);
                    ctx.lineTo(width - padding.right, height - padding.bottom);
                    ctx.stroke();
                    
                    // Y轴
                    ctx.beginPath();
                    ctx.moveTo(padding.left, padding.top);
                    ctx.lineTo(padding.left, height - padding.bottom);
                    ctx.stroke();
                    
                    // 绘制X轴刻度和标签
                    if (option.xAxis && option.xAxis.data) {
                        var xData = option.xAxis.data;
                        var interval = option.xAxis.axisLabel && option.xAxis.axisLabel.interval || Math.floor(xData.length / 6);
                        
                        ctx.textAlign = 'center';
                        ctx.fillStyle = '#666';
                        
                        for (var i = 0; i < xData.length; i++) {
                            if (i % (interval + 1) === 0 || i === xData.length - 1) {
                                var x = padding.left + (i / (xData.length - 1)) * chartWidth;
                                
                                // 刻度线
                                ctx.beginPath();
                                ctx.moveTo(x, height - padding.bottom);
                                ctx.lineTo(x, height - padding.bottom + 5);
                                ctx.stroke();
                                
                                // 标签
                                ctx.fillText(xData[i], x, height - padding.bottom + 15);
                            }
                        }
                    }
                    
                    // 绘制Y轴刻度和标签
                    var yTickCount = 5;
                    ctx.textAlign = 'right';
                    
                    for (var i = 0; i <= yTickCount; i++) {
                        var value = minValue + (valueRange * i / yTickCount);
                        var y = height - padding.bottom - (chartHeight * i / yTickCount);
                        
                        // 刻度线
                        ctx.beginPath();
                        ctx.moveTo(padding.left, y);
                        ctx.lineTo(padding.left - 5, y);
                        ctx.stroke();
                        
                        // 网格线
                        ctx.beginPath();
                        ctx.strokeStyle = '#eee';
                        ctx.moveTo(padding.left, y);
                        ctx.lineTo(width - padding.right, y);
                        ctx.stroke();
                        ctx.strokeStyle = '#ccc';
                        
                        // 标签
                        ctx.fillText(value.toFixed(2), padding.left - 10, y);
                    }
                    
                    // 绘制Y轴名称
                    if (option.yAxis && option.yAxis.name) {
                        ctx.save();
                        ctx.translate(15, height / 2);
                        ctx.rotate(-Math.PI / 2);
                        ctx.textAlign = 'center';
                        ctx.fillText(option.yAxis.name, 0, 0);
                        ctx.restore();
                    }
                    
                    // 绘制折线
                    ctx.beginPath();
                    ctx.strokeStyle = getColor(series.lineStyle && series.lineStyle.color, '#5470c6');
                    ctx.lineWidth = 2;
                    ctx.lineJoin = 'round';
                    
                    for (var i = 0; i < data.length; i++) {
                        var x = padding.left + (i / (data.length - 1)) * chartWidth;
                        var y = padding.top + chartHeight - ((data[i] - minValue) / valueRange) * chartHeight;
                        
                        if (i === 0) {
                            ctx.moveTo(x, y);
                        } else if (series.smooth) {
                            // 简单的平滑曲线实现
                            var prevX = padding.left + ((i - 1) / (data.length - 1)) * chartWidth;
                            var prevY = padding.top + chartHeight - ((data[i - 1] - minValue) / valueRange) * chartHeight;
                            
                            var cp1x = prevX + (x - prevX) / 3;
                            var cp1y = prevY;
                            var cp2x = prevX + (x - prevX) * 2 / 3;
                            var cp2y = y;
                            
                            ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                    
                    ctx.stroke();
                    
                    // 绘制面积填充
                    if (series.areaStyle) {
                        ctx.lineTo(padding.left + chartWidth, height - padding.bottom);
                        ctx.lineTo(padding.left, height - padding.bottom);
                        ctx.closePath();
                        
                        // 处理渐变色
                        var gradient = ctx.createLinearGradient(0, padding.top, 0, height - padding.bottom);
                        
                        if (series.areaStyle.color && series.areaStyle.color.colorStops) {
                            series.areaStyle.color.colorStops.forEach(function(stop) {
                                gradient.addColorStop(stop.offset, stop.color);
                            });
                        } else {
                            var color = getColor(series.itemStyle && series.itemStyle.color, '#5470c6');
                            var rgb = typeof color === 'string' ? hexToRgb(color.replace('#', '')) : [84, 112, 198];
                            
                            gradient.addColorStop(0, 'rgba(' + rgb[0] + ',' + rgb[1] + ',' + rgb[2] + ',0.5)');
                            gradient.addColorStop(1, 'rgba(' + rgb[0] + ',' + rgb[1] + ',' + rgb[2] + ',0.1)');
                        }
                        
                        ctx.fillStyle = gradient;
                        ctx.fill();
                    }
                    
                    // 绘制数据点
                    for (var i = 0; i < data.length; i++) {
                        var x = padding.left + (i / (data.length - 1)) * chartWidth;
                        var y = padding.top + chartHeight - ((data[i] - minValue) / valueRange) * chartHeight;
                        
                        ctx.beginPath();
                        ctx.fillStyle = getColor(series.itemStyle && series.itemStyle.color, '#5470c6');
                        ctx.arc(x, y, 3, 0, Math.PI * 2);
                        ctx.fill();
                        
                        // 当鼠标悬停在某个点上时，增大该点的半径
                        if (this._tooltipVisible && this._tooltipData && this._tooltipData.index === i) {
                            ctx.beginPath();
                            ctx.fillStyle = '#fff';
                            ctx.arc(x, y, 5, 0, Math.PI * 2);
                            ctx.fill();
                            
                            ctx.beginPath();
                            ctx.strokeStyle = getColor(series.itemStyle && series.itemStyle.color, '#5470c6');
                            ctx.lineWidth = 2;
                            ctx.arc(x, y, 5, 0, Math.PI * 2);
                            ctx.stroke();
                        }
                    }
                    
                    // 绘制提示框
                    if (this._tooltipVisible && this._tooltipData) {
                        var tooltipX = this._tooltipPosition.x;
                        var tooltipY = this._tooltipPosition.y;
                        var tooltipWidth = 120;
                        var tooltipHeight = 60;
                        
                        // 确保提示框在画布内
                        if (tooltipX + tooltipWidth > width) {
                            tooltipX = tooltipX - tooltipWidth;
                        }
                        
                        if (tooltipY + tooltipHeight > height) {
                            tooltipY = tooltipY - tooltipHeight;
                        }
                        
                        // 背景
                        ctx.fillStyle = 'rgba(0,0,0,0.7)';
                        ctx.beginPath();
                        ctx.roundRect(tooltipX, tooltipY, tooltipWidth, tooltipHeight, 4);
                        ctx.fill();
                        
                        // 文本
                        ctx.fillStyle = '#fff';
                        ctx.textAlign = 'left';
                        ctx.font = '12px Arial';
                        
                        // 日期
                        var xValue = this._tooltipData.xValue;
                        if (xValue) {
                            ctx.fillText(xValue, tooltipX + 10, tooltipY + 20);
                        }
                        
                        // 值
                        var valueTxt = series.name + ': ' + this._tooltipData.value.toFixed(2);
                        ctx.fillText(valueTxt, tooltipX + 10, tooltipY + 40);
                    }
                }
            },
            
            resize: function() {
                this._updateSize();
                this._render(1);
            },
            
            dispose: function() {
                if (this._canvas) {
                    this._canvas.removeEventListener('mousemove', this._handleMouseMove);
                    this._canvas.removeEventListener('mouseout', this._handleMouseOut);
                }
                
                if (this._resizeHandler) {
                    window.removeEventListener('resize', this._resizeHandler);
                }
                
                this.dom.innerHTML = '';
                this._canvas = null;
                this._ctx = null;
            }
        };
        
        // 初始化实例
        chartInstance._initEvents();
        
        return chartInstance;
    };
    
    return echarts;
}); 