<template>
	<view class="container">
		<block v-if="isload">
			

			<view style="text-align: center;font-size: 20px;margin-top: 10px;font-weight: bold;">签约中心</view>

			<view style="padding: 20px;"  v-html="content"></view>

			<view class="ll">

				<button class="form-btn"
					:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
					form-type="submit" @tap="showxieyiFun3">签字</button>

			</view>

			<view v-if="showxieyi3" class="xieyibox">
				<view class="container3">

					<view class="sigh-btns">
						<button class="btn" @tap="handleCancel"
							style="background:red;padding:10px;color:#fff;">取消</button>
						<button class="btn" @tap="handleReset"
							style="background:red;padding:10px;color:#fff;">重写</button>
						<button class="btn" @tap="handleConfirm"
							style="background:red;padding:10px;color:#fff;">确认</button>
					</view>

					<view class="sign-box">
						<canvas class="mycanvas" :style="{width:width +'px',height:height +'px'}" canvas-id="mycanvas"
							@touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"></canvas>

						<canvas canvas-id="camCacnvs" :style="{width:height +'px',height:width +'px'}"
							class="canvsborder"></canvas>

					</view>

				</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>


	</view>
</template>

<script>
	var app = getApp();
	var x = 20;
	var y = 20;
	var tempPoint = []; //用来存放当前画纸上的轨迹点
	var id = 0;
	var type = '';
	let that;
	let canvasw;
	let canvash;
	export default {
		data() {
			return {
				opt: {},
				second: 30,
				secondstr: '请阅读至少30秒',
				loading: false,
				isload: false,
				menuindex: -1,
				pre_url: app.globalData.pre_url,
				platform2: app.globalData.platform2,
				pic: [],
				platform: '',
				platformname: '',
				platformimg: 'weixin',
				logintype: 0,
				logintype_1: true,
				logintype_2: false,
				logintype_3: false,
				logo: '',
				name: '',
				xystatus: 0,
				xyname: '',
				xycontent: '',
				xyname2: '',
				xycontent2: '',
				needsms: false,
				showxieyi: false,
				showxieyi2: false,
				showxieyi3: false,
				isagree: false,
				smsdjs: '',
				tel: '',
				hqing: 0,
				frompage: '/pages/my/usercenter',
				wxloginclick: false,
				login_bind: 0,
				login_setnickname: 0,
				reg_invite_code: 0,
				reg_invite_code_text: '',
				reg_invite_code_type: 0,
				parent: {},
				//自定义表单Start
				has_custom: 0,
				show_custom_field: false,
				regiondata: '',
				editorFormdata: {},
				test: '',
				formfields: [],
				custom_formdata: [],
				items: [],
				formvaldata: {},
				submitDisabled: false,
				//自定义表单End
				tmplids: [],
				default_headimg: app.globalData.pre_url + '/static/img/touxiang.png',
				headimg: '',
				nickname: '',
				ctx: '', //绘图图像
				points: [], //路径点集合,
				width: 0,
				height: 0,
				qianmingurl: '',
				content : ''
			};
		},

		onLoad: function(opt) {
			that = this;
			this.opt = app.getopts(opt);
			if (this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);
			if (this.opt.logintype) this.logintype = this.opt.logintype;
			if (this.opt.login_bind) this.login_bind = this.opt.login_bind;
			this.getdata();
			this.getSign();
			console.log(opt);
			id = opt.id;
			type = opt.type;
			this.ctx = uni.createCanvasContext('mycanvas', this); //创建绘图对象
			//设置画笔样式
			this.ctx.lineWidth = 4;
			this.ctx.lineCap = 'round';
			this.ctx.lineJoin = 'round';

			uni.getSystemInfo({
				success: function(res) {
					console.log(res);
					that.width = res.windowWidth * 0.8;
					that.height = res.windowHeight * 0.85;
				}
			});
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			
			getSign(){
                let that = this 
				app.get('ApiMy/getRenzhengItems', {}, function(res) {
                        console.log(res)
					  if(res.data){
						  
						  that.content = res.content;
					  }
					  
				});
			},
			
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiIndex/reg', {
					pid: app.globalData.pid
				}, function(res) {
					that.loading = false;
					if (res.status == 0) {
						app.alert(res.msg);
						return;
					}
					that.logintype_2 = res.logintype_2;
					that.logintype_3 = res.logintype_3;
					that.logintype_3 = res.logintype_3;
					// #ifdef APP-PLUS
					if (that.platform2 == 'ios') {
						if (plus.runtime.isApplicationExist({
								pname: 'com.tencent.mm',
								action: 'weixin://'
							})) {

						} else {
							that.logintype_3 = false;
						}
					}
					// #endif

					that.xystatus = res.xystatus;
					that.xyname = res.xyname;
					that.xycontent = res.xycontent;
					that.xyname2 = res.xyname2;
					that.xycontent2 = res.xycontent2;
					that.logo = res.logo;
					that.name = res.name;
					that.needsms = res.needsms;
					that.platform = res.platform;
					that.reg_invite_code = res.reg_invite_code;
					that.reg_invite_code_text = res.reg_invite_code_text;
					that.reg_invite_code_type = res.reg_invite_code_type;
					that.parent = res.parent;
					if (that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app') {
						that.platformname = '微信';
						that.platformimg = 'weixin';
					}
					if (that.platform == 'toutiao') {
						that.platformname = '头条';
						that.platformimg = 'toutiao';
					}
					if (that.platform == 'alipay') {
						that.platformname = '支付宝';
						that.platformimg = 'alipay';
					}
					if (that.platform == 'qq') {
						that.platformname = 'QQ';
						that.platformimg = 'qq';
					}
					if (that.platform == 'baidu') {
						that.platformname = '百度';
						that.platformimg = 'baidu';
					}

					//自定义表单
					if (res.has_custom) {
						that.formfields = res.custom_form_field;
						that.has_custom = res.has_custom
						that.show_custom_field = true
						uni.request({
							url: app.globalData.pre_url + '/static/area.json',
							data: {},
							method: 'GET',
							header: {
								'content-type': 'application/json'
							},
							success: function(res2) {
								that.items = res2.data
							}
						});
					}
					that.loaded();
				});
			},
			formSubmit: function(e) {
				var that = this;
				var formdata = e.detail.value;
				if (formdata.tel == '') {
					app.alert('请输入手机号');
					return;
				}
				if (formdata.pwd == '') {
					app.alert('请输入密码');
					return;
				}
				if (formdata.pwd.length < 6) {
					app.alert('新密码不小于6位');
					return;
				}
				if (formdata.repwd == '') {
					app.alert('请再次输入新密码');
					return;
				}
				if (formdata.pwd != formdata.repwd) {
					app.alert('两次密码不一致');
					return;
				}


				if (that.needsms) {
					if (formdata.smscode == '') {
						app.alert('请输入短信验证码');
						return;
					}
				} else {
					formdata.smscode = '';
				}
				var postdata = {
					qianmingurl: formdata.qianming,
					nickname: formdata.nickname,
					tel: formdata.tel,
					pwd: formdata.pwd,
					smscode: formdata.smscode,
					pid: app.globalData.pid,
					yqcode: formdata.yqcode
				}
				//如果有自定义表单则验证表单内容
				if (that.show_custom_field) {
					var customformdata = {};
					var customData = that.checkCustomFormFields();
					if (!customData) {
						return;
					}
					postdata['customformdata'] = customData
					postdata['customformid'] = that.formfields.id
				}

				if (that.xystatus == 1 && !that.isagree) {
					app.error('请先阅读并同意用户注册协议');
					return false;
				}
				if (that.second > 0) {
					app.error('请先阅读并同意用户注册协议');
					return false;
				}
				if (formdata.qianming.length <= 0) {
					app.error('请先签名');
					return false;
				}
				app.showLoading('提交中');
				app.post("ApiIndex/regsub", postdata, function(data) {
					app.showLoading(false);
					if (data.status == 1) {
						app.success(data.msg);
						if (data.tmplids) {
							that.tmplids = data.tmplids;
						}
						that.subscribeMessage(function() {
							setTimeout(function() {
								if (that.opt.fromapp == 1 && data.toappurl) {
									app.goto(data.toappurl, 'redirect');
								} else {
									app.goto('/pages/my/usercenter', 'redirect');
								}
							}, 1000);
						});
					} else {
						app.error(data.msg);
					}
				});
			},
			
			
			nosetnicknameregister: function() {
				this.nickname = '';
				this.headimg = '';
				if (this.login_bind != 0) {
					this.logintype = 4;
				} else {
					this.register('', '', '', '');
				}
			},
		
			
			
			isagreeChange: function(e) {
				var val = e.detail.value;
				if (val.length > 0) {
					this.isagree = true;
				} else {
					this.isagree = false;
				}
				console.log(this.isagree);
			},
			showxieyiFun: function() {
				this.showxieyi = true;
				this.addxieyisen();
			},
			showxieyiFun3: function() {
				this.showxieyi3 = true;
			},
		
			hidexieyi: function() {
				if (this.second <= 0) {
					this.showxieyi = false;
					this.isagree = true;
					if (this.wxloginclick) {
						this.weixinlogin();
					}
				}
			},
			showxieyiFun2: function() {
				this.showxieyi2 = true;
			},
			hidexieyi2: function() {
				this.showxieyi2 = false;
				this.isagree = true;
				if (this.wxloginclick) { 
					this.weixinlogin();
				}
				if (this.iosloginclick) {
					this.ioslogin();
				}
			},
			telinput: function(e) {
				this.tel = e.detail.value
			},
			uploadHeadimg: function() {
				var that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: function(res) {
						var tempFilePaths = res.tempFilePaths;
						var tempFilePath = tempFilePaths[0];
						app.showLoading('上传中');
						uni.uploadFile({
							url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app
								.globalData.aid + '/platform/' + app.globalData.platform +
								'/session_id/' + app.globalData.session_id + '/isheadimg/1',
							filePath: tempFilePath,
							name: 'file',
							success: function(res) {
								console.log(res)
								app.showLoading(false);
								var data = JSON.parse(res.data);
								if (data.status == 1) {
									that.headimg = data.url;
								} else {
									app.alert(data.msg);
								}
							},
							fail: function(res) {
								app.showLoading(false);
								app.alert(res.errMsg);
							}
						});
					},
					fail: function(res) { //alert(res.errMsg);
					}
				});
			},
			onChooseAvatar: function(e) {
				console.log(e)
				var that = this;
				app.showLoading('上传中');
				uni.uploadFile({
					url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid +
						'/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id +
						'/isheadimg/1',
					filePath: e.detail.avatarUrl,
					name: 'file',
					success: function(res) {
						app.showLoading(false);
						var data = JSON.parse(res.data);
						if (data.status == 1) {
							that.headimg = data.url;
						} else {
							app.alert(data.msg);
						}
					},
					fail: function(res) {
						app.showLoading(false);
						app.alert(res.errMsg);
					}
				});
			},
			
			//自定义表单
			onchange(e) {
				const value = e.detail.value
				this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;
			},
			setfield: function(e) {
				var field = e.currentTarget.dataset.formidx;
				var value = e.detail.value;
				this.formvaldata[field] = value;
			},
			
			
			removeimg: function(e) {
				var that = this;
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var field = e.currentTarget.dataset.formidx;
				var editorFormdata = this.editorFormdata;
				if (!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = '';
				that.editorFormdata = editorFormdata
				that.test = Math.random();
				that.formvaldata[field] = '';
			},
			//触摸开始，获取到起点
			touchstart: function(e) {

				console.log(e);

				let startX = e.changedTouches[0].x;
				let startY = e.changedTouches[0].y;
				let startPoint = {
					X: startX,
					Y: startY
				};

				/* **************************************************
					    #由于uni对canvas的实现有所不同，这里需要把起点存起来
					 * **************************************************/
				this.points.push(startPoint);

				//每次触摸开始，开启新的路径
				this.ctx.beginPath();
			},

			//触摸移动，获取到路径点
			touchmove: function(e) {
				let moveX = e.changedTouches[0].x;
				let moveY = e.changedTouches[0].y;
				let movePoint = {
					X: moveX,
					Y: moveY
				};
				this.points.push(movePoint); //存点
				let len = this.points.length;
				if (len >= 2) {
					this.draw(); //绘制路径
				}
				tempPoint.push(movePoint);
			},

			// 触摸结束，将未绘制的点清空防止对后续路径产生干扰
			touchend: function() {
				this.points = [];
			},

			/* ***********************************************	
					#   绘制笔迹
					#   1.为保证笔迹实时显示，必须在移动的同时绘制笔迹
					#   2.为保证笔迹连续，每次从路径集合中区两个点作为起点（moveTo）和终点(lineTo)
					#   3.将上一次的终点作为下一次绘制的起点（即清除第一个点）
					************************************************ */
			draw: function() {
				let point1 = this.points[0];
				let point2 = this.points[1];
				this.points.shift();
				this.ctx.moveTo(point1.X, point1.Y);
				this.ctx.lineTo(point2.X, point2.Y);
				this.ctx.stroke();
				this.ctx.draw(true);
			},

			handleCancel() {
				// uni.navigateBack({
				// 	delta: 1
				// });
				this.showxieyi3 = false;
			},

			//清空画布
			handleReset: function() {
				console.log('handleReset');
				that.ctx.clearRect(0, 0, that.width, that.height);
				that.ctx.draw(true);
				tempPoint = [];
			},

			//将签名笔迹上传到服务器，并将返回来的地址存到本地
			handleConfirm: function() {
			    if (tempPoint.length == 0) {
			        uni.showToast({
			            title: '请先签名',
			            icon: 'none',
			            duration: 2000
			        });
			        return;
			    }
			    
			    // 将签名转换为临时文件路径
			    uni.canvasToTempFilePath({
			        canvasId: 'mycanvas',
			        success: function(res) {
			            let tempPath = res.tempFilePath;
			
			            const ctx = uni.createCanvasContext('camCacnvs', that);
			            ctx.translate(0, that.width);
			            ctx.rotate((-90 * Math.PI) / 180);
			            ctx.drawImage(tempPath, 0, 0, that.width, that.height);
			            ctx.draw();
			
			            setTimeout(() => {
			                // 保存签名图片到本地
			                uni.canvasToTempFilePath({
			                    canvasId: 'camCacnvs',
			                    success: function(res) {
			                        let path = res.tempFilePath;
			
			                        // 上传签名图片到服务器
			                        uni.uploadFile({
			                            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,
			                            filePath: path,
			                            name: 'file',
			                            success: function(res) {
			                                var data = JSON.parse(res.data);
			                                if (data.status == 1) {
			                                    // 服务器接口调用，保存签名的 URL
			                                    app.post('ApiMy/setfield', {
			                                        agreement_signed: data.url
			                                    }, function(response) {
			                                        console.log(response);
			                                        app.alert('签约成功');
			
			                                        // 签字成功后返回上一页
			                                        that.showxieyi3 = false;
			                                        wx.navigateBack({
			                                            delta: 1 // 返回上一页
			                                        });
			                                    });
			                                } else {
			                                    app.alert(data.msg);
			                                }
			                            },
			                            fail: function(res) {
			                                app.alert(res.errMsg);
			                            }
			                        });
			                    },
			                    fail: err => {
			                        console.log('fail', err);
			                    }
			                }, this);
			            }, 200);
			        }
			    });
			}

		}
	};
</script>

<style>
	page {
		background: #ffffff
	}

	.container {
		width: 100%;
	}

	.title {
		margin: 70rpx 50rpx 50rpx 40rpx;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 48rpx;
		font-weight: bold;
		color: #000000;
	}

	.regform {
		width: 100%;
		padding: 0 50rpx;
		border-radius: 5px;
		background: #FFF;
	}

	.regform .form-item {
		display: flex;
		align-items: center;
		width: 100%;
		border-bottom: 1px #ededed solid;
		height: 88rpx;
		line-height: 88rpx;
		border-bottom: 1px solid #F0F3F6;
		margin-top: 20rpx
	}

	.regform .form-item:last-child {
		border: 0
	}

	.regform .form-item .img {
		width: 44rpx;
		height: 44rpx;
		margin-right: 30rpx
	}

	.regform .form-item .input {
		flex: 1;
		color: #000;
	}

	.regform .form-item .code {
		font-size: 30rpx
	}

	.regform .xieyi-item {
		display: flex;
		align-items: center;
		margin-top: 50rpx
	}

	.regform .xieyi-item {
		font-size: 24rpx;
		color: #B2B5BE
	}

	.regform .xieyi-item .checkbox {
		transform: scale(0.6);
	}

	.regform .form-btn {
		margin-top: 20rpx;
		width: 100%;
		height: 96rpx;
		line-height: 96rpx;
		color: #fff;
		font-size: 30rpx;
		border-radius: 48rpx;
	}

	.regform .form-btn2 {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background: #EEEEEE;
		border-radius: 40rpx;
		color: #A9A9A9;
		margin-top: 30rpx
	}

	.tologin {
		color: #737785;
		font-size: 26rpx;
		display: flex;
		width: 100%;
		padding: 0 80rpx;
		margin-top: 30rpx
	}

	.othertip {
		height: auto;
		overflow: hidden;
		display: flex;
		align-items: center;
		width: 580rpx;
		padding: 20rpx 20rpx;
		margin: 0 auto;
		margin-top: 60rpx;
	}

	.othertip-line {
		height: auto;
		padding: 0;
		overflow: hidden;
		flex: 1;
		height: 0;
		border-top: 1px solid #F2F2F2
	}

	.othertip-text {
		padding: 0 32rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center
	}

	.othertip-text .txt {
		color: #A3A3A3;
		font-size: 22rpx
	}

	.othertype {
		width: 70%;
		margin: 20rpx 15%;
		display: flex;
		justify-content: center;
	}

	.othertype-item {
		width: 50%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.othertype-item .img {
		width: 88rpx;
		height: 88rpx;
		margin-bottom: 20rpx
	}

	.othertype-item .txt {
		color: #A3A3A3;
		font-size: 24rpx
	}

	.xieyibox {
		width: 100%;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 99;
		background: rgba(0, 0, 0, 0.7)
	}

	.xieyibox-content {
		width: 90%;
		margin: 0 auto;
		height: 80%;
		margin-top: 20%;
		background: #fff;
		color: #333;
		padding: 5px 10px 50px 10px;
		position: relative;
		border-radius: 2px
	}


	.authlogin {
		display: flex;
		flex-direction: column;
		align-items: center
	}

	.authlogin-logo {
		width: 180rpx;
		height: 180rpx;
		margin-top: 120rpx
	}

	.authlogin-name {
		color: #999999;
		font-size: 30rpx;
		margin-top: 60rpx;
	}

	.authlogin-btn {
		width: 580rpx;
		height: 96rpx;
		line-height: 96rpx;
		background: #51B1F5;
		border-radius: 48rpx;
		color: #fff;
		margin-top: 100rpx
	}

	.authlogin-btn2 {
		width: 580rpx;
		height: 96rpx;
		line-height: 96rpx;
		background: #EEEEEE;
		border-radius: 48rpx;
		color: #A9A9A9;
		margin-top: 20rpx
	}


	/* 自定义字段显示 */
	.dp-form-item {
		width: 100%;
		border-bottom: 1px #ededed solid;
		padding: 16rpx 0px;
		display: flex;
		align-items: center;
	}

	.dp-form-item:last-child {
		border: 0
	}

	.dp-form-item .label {
		line-height: 40rpx;
		width: 140rpx;
		margin-right: 10px;
		flex-shrink: 0;
		text-align: right;
		color: #666666;
	}

	.dp-form-item .input {
		height: 70rpx;
		line-height: 70rpx;
		overflow: hidden;
		flex: 1;
		border: 1px solid #eee;
		padding: 0 8rpx;
		border-radius: 2px;
		background: #fff
	}

	.dp-form-item .textarea {
		height: 180rpx;
		line-height: 40rpx;
		overflow: hidden;
		flex: 1;
		border: 1px solid #eee;
		border-radius: 2px;
		padding: 8rpx
	}

	.dp-form-item .radio {
		height: 70rpx;
		line-height: 70rpx;
		display: flex;
		align-items: center
	}

	.dp-form-item .radio2 {
		display: flex;
		align-items: center;
	}

	.dp-form-item .radio .myradio {
		margin-right: 10rpx;
		display: inline-block;
		border: 1px solid #aaa;
		background: #fff;
		height: 32rpx;
		width: 32rpx;
		border-radius: 50%
	}

	.dp-form-item .checkbox {
		height: 70rpx;
		line-height: 70rpx;
		display: flex;
		align-items: center
	}

	.dp-form-item .checkbox2 {
		display: flex;
		align-items: center;
		height: 40rpx;
		line-height: 40rpx;
	}

	.dp-form-item .checkbox .mycheckbox {
		margin-right: 10rpx;
		display: inline-block;
		border: 1px solid #aaa;
		background: #fff;
		height: 32rpx;
		width: 32rpx;
		border-radius: 2px
	}

	.dp-form-item .layui-form-switch {}

	.dp-form-item .picker {
		height: 70rpx;
		line-height: 70rpx;
		flex: 1;
	}

	.dp-form-item2 {
		width: 100%;
		border-bottom: 1px #ededed solid;
		padding: 10rpx 0px;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}

	.dp-form-item2:last-child {
		border: 0
	}

	.dp-form-item2 .label {
		height: 70rpx;
		line-height: 70rpx;
		width: 100%;
		margin-right: 10px;
	}

	.dp-form-item2 .input {
		height: 70rpx;
		line-height: 70rpx;
		overflow: hidden;
		width: 100%;
		border: 1px solid #eee;
		padding: 0 8rpx;
		border-radius: 2px;
		background: #fff
	}

	.dp-form-item2 .textarea {
		height: 180rpx;
		line-height: 40rpx;
		overflow: hidden;
		width: 100%;
		border: 1px solid #eee;
		border-radius: 2px;
		padding: 8rpx
	}

	.dp-form-item2 .radio {
		height: 70rpx;
		line-height: 70rpx;
		display: flex;
		align-items: center;
	}

	.dp-form-item2 .radio2 {
		display: flex;
		align-items: center;
	}

	.dp-form-item2 .radio .myradio {
		margin-right: 10rpx;
		display: inline-block;
		border: 1px solid #aaa;
		background: #fff;
		height: 32rpx;
		width: 32rpx;
		border-radius: 50%
	}

	.dp-form-item2 .checkbox {
		height: 70rpx;
		line-height: 70rpx;
		display: flex;
		align-items: center;
	}

	.dp-form-item2 .checkbox2 {
		display: flex;
		align-items: center;
		height: 40rpx;
		line-height: 40rpx;
	}

	.dp-form-item2 .checkbox .mycheckbox {
		margin-right: 10rpx;
		display: inline-block;
		border: 1px solid #aaa;
		background: #fff;
		height: 32rpx;
		width: 32rpx;
		border-radius: 2px
	}

	.dp-form-item2 .layui-form-switch {}

	.dp-form-item2 .picker {
		height: 70rpx;
		line-height: 70rpx;
		flex: 1;
		width: 100%;
	}

	.dp-form-uploadbtn {
		position: relative;
		height: 200rpx;
		width: 200rpx
	}

	.dp-form-imgbox {
		margin-right: 16rpx;
		margin-bottom: 10rpx;
		font-size: 24rpx;
		position: relative;
	}

	.dp-form-imgbox-close {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		right: -16rpx;
		top: -16rpx;
		color: #999;
		font-size: 32rpx;
		background: #fff
	}

	.dp-form-imgbox-close .image {
		width: 100%;
		height: 100%
	}

	.dp-form-imgbox-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		padding: 2px;
		border: #d3d3d3 1px solid;
		background-color: #f6f6f6;
		overflow: hidden
	}

	.dp-form-imgbox-img>.image {
		max-width: 100%;
	}

	.dp-form-imgbox-repeat {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		line-height: 28rpx;
		right: 2px;
		bottom: 2px;
		color: #999;
		font-size: 30rpx;
		background: #fff
	}

	.dp-form-uploadbtn {
		position: relative;
		height: 200rpx;
		width: 200rpx
	}
</style>
<style lang="scss" scoped>
	.container3 {
		display: flex;
		flex-direction: row;
	}

	.sign-box {
		width: 80%;
		height: 90%;
		margin: auto;
		display: flex;
		flex-direction: column;
		text-align: center;
	}

	.sign-view {
		height: 100%;
	}

	.sigh-btns {
		height: 100%;
		margin: auto;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	.btn {
		margin: auto;
		padding: 8rpx;
		transform: rotate(90deg);
		border: grey 1rpx solid;
	}

	.mycanvas {
		margin: auto 0rpx;
		background-color: #ececec;
	}

	.canvsborder {
		border: 1rpx solid #333;
		position: fixed;
		top: 0;
		left: 10000rpx;
	}

   .ll{
	   width: 100%;
	   padding: 0 50rpx;
	   border-radius: 5px;
	   background: #FFF;
	   position: fixed;
	   bottom: 20px;
   }

	.ll .form-btn {
		margin-top: 20rpx;
		width: 100%;
		height: 96rpx;
		line-height: 96rpx;
		color: #fff;
		font-size: 30rpx;
		border-radius: 48rpx;
	}
</style>
