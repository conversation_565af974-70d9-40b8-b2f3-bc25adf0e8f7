<template>
	<view class="container" :style="{backgroundColor:'pageinfo.bgcolor'}">
		<view>
			<swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval"
				:duration="duration">
				<swiper-item class="swiper-item" v-for="item,index in info.pic_list" :key="index">
					<img :src="item"
						class="swiper-img" />
				</swiper-item>

			</swiper>
			<view class="main">
				<view class="section">
					<view class="title">{{info.title}}</view>
					<view class="spaceBetween" style="margin : 30rpx 0;">
						<view class="tags">
							<view class="tagsItem"  v-for="ite,ind in info.services" :key="ind">{{ite.name}}</view>
						</view>
						<view class="tags_txt">详情></view>
					</view>

					<view class="info">
						<view class="info_l" :style="'background:'+secondary_color">
							<view class="flexMain">
								<view class="info_tag" :style="'background:'+primary_color">{{info.score}}</view>
								{{pl_num}}条
							</view>
							<view class="texts">{{pl_txt}}</view>
						</view>
						<view class="info_r" :style="'background:'+secondary_color">
							<view class="text">{{info.reserve}}人预定</view>
							<view class="text">{{info.visit}}浏览</view>
						</view>
					</view>
					<view class="address">
						<view class="addressInfo">
							<view class="addressInfoLabel">{{info.location}}</view>
							<view class="addressInfodis">距您>{{info.distance}}</view>
						</view>
						<view class="addressTip">
							<view class="addressTipItem" @tap="goMap">
								<image :src="pre_url+'/static/img/map.png'" class="addressTipItemIcon" />
								<view class="addressTipItemName">地图</view>
							</view>
							<view class="addressTipItem" @tap="goCall">
								<image :src="pre_url+'/static/img/phone.png'" class="addressTipItemIcon" />
								<view class="addressTipItemName">电话</view>
							</view>
						</view>
					</view>
					<view class="tip">
						<view class="tipInfo" v-html="info.introduce"></view>
						<view class="tipAction" @tap="lookMust">订场必读></view>
					</view>
				</view>
				<view class="section">
					<view class="block_row">
						<view class="section_species">
							<view v-for="(item,index) in species" :key="index">
								<view class="subTitle"  v-if="index== species_id"  @click="selectSpecies(index)">
									<view class="subTitle_txt" :style="'color:'+primary_color">{{item.title}}</view>

									<view class="subTitle_txt_underline"  :style="'border-bottom: 5rpx solid '+primary_color"></view>
									
								</view>
								
								<view class="subTitle" v-else @click="selectSpecies(index)">
									<view>{{item.title}}</view>
								
									<view></view>
								</view>
								
							</view>
						</view>
						<view class="section_hint">1小时前有人预定</view>
					</view>
					<view class="list">
						<view class="item" v-for="item,index in day_list" :key="index" @click="goDay(item.day)" :style="'border: 1rpx solid '+primary_color+';background:'+secondary_color">
							<view class="name" :style="'color:'+primary_color">{{item.week}}</view>
							<view class="date" :style="'color:'+primary_color">{{item.day}}</view>
							<view class="action" :style="'background:'+primary_color">预定</view>
						</view>
					</view>
				</view>
<!-- 				<view class="section">
					<view class="title">
						优惠
					</view>
					<view class="groupInformation">
						<view class="gi_title">
							<text>员村上社华打球群</text>
							<text>(120+)</text>
						</view>
						<view class="gi_level alignItems">
							<view>水平：</view>
							<uni-rate :readonly="true" :value="3" :size="20" />
							<view>3.0</view>
						</view>
						<view class="gi_time">
							<view>时间：</view>
							<view class="gi_time_r">
								<text>
									周一20:00、周三20:00
									周一20:00、周三20:00
								</text>
								<view>></view>
							</view>
						</view>
						<view class="jc_center">
							<view class="gi_bt jc_center">马上入群</view>
						</view>
					</view>
				</view> -->
				<view class="section">
					<view class="title">
						评论({{pl_num}})
						<view class="titleAction alignItems" @tap="editPl">
							<text style="margin-right: 10rpx; color: #847dee; font-size: 26px;">✏️</text>
							写点评
						</view>
					</view>
<!-- 					<view class="label">
						<view class="label_txt">空调舒服</view>
						<view class="label_txt">空调舒服</view>
						<view class="label_txt">空调舒服</view>
						<view class="label_txt">空调舒服</view>
					</view>
					<view class="commentContent">
						<view class="spaceBetween">
							<view class="content_title">
								<img src="https://img2.baidu.com/it/u=3618595163,3698702119&fm=253&fmt=auto&app=120&f=JPEG"
									class="content_title_img" />
								<view class="content_title_l">
									<view>281747</view>
									<uni-rate :readonly="true" :value="3" :size="20" />
								</view>
							</view>
							<view class="content_title_r">
								<view>2023-05-01</view>
								<view>定场后评论</view>
							</view>
						</view>
						<view class="commentContent_txt">
							<text>场馆是新开的，场馆是新开的，场馆是新开的，场馆是新开的，场馆是新开的，场馆是新开的，场馆是新开的，场馆是新开的</text>
						</view>
					</view> -->
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	var app = getApp();
	export default {
		data() {
			return {
				pageinfo: [],
				date: '',
				indicatorDots: true,
				autoplay: true,
				interval: 2000,
				duration: 500,
				pre_url: app.globalData.pre_url,
				species: [],
				species_id: 0,
				
				day_list : [
					
					
				],
				
				id: '',
				info : '',
				pl : [],
				pl_num : 0,
				pl_txt : '',
				primary_color : '',
				secondary_color : ''
			}
		},
		onLoad: function(opt) {
			
			this.id = opt.id;
			
			this.getdata(opt); 
			
			this.getPl(opt); 
			
			
			this.primary_color = app.getCache('primary_color')
			this.secondary_color = app.getCache('secondary_color')
	

		},
		onPullDownRefresh: function(e) {
			this.getdata();
		},
		onPageScroll: function(e) {
			uni.$emit('onPageScroll', e);
		},
		methods: {
			upper() {

			},			
			lookMust(){
				uni.navigateTo({
					 url:'../../pagesB/dingchang/content?content='+encodeURIComponent(this.info.must_reads)+'&title='+ '订场必读'
				})	

			},
			
			goDay(date){
			   console.log('goDay called with date:', date);

			   // 找到对应日期在日历页面中的索引位置
			   let targetIndex = 0;
			   const today = new Date();

			   // 处理不同的日期格式
			   let targetDate;
			   if (date.includes('-') && date.length <= 5) {
			   	   // 如果是 MM-DD 格式，添加当前年份
			   	   const currentYear = today.getFullYear();
			   	   targetDate = new Date(currentYear + '-' + date);
			   } else {
			   	   // 其他格式直接解析
			   	   targetDate = new Date(date);
			   }

			   console.log('today:', today.toDateString());
			   console.log('targetDate:', targetDate.toDateString());

			   // 计算目标日期与今天的天数差
			   const timeDiff = targetDate.getTime() - today.getTime();
			   const dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

			   console.log('dayDiff:', dayDiff);

			   // 根据天数差确定在日历页面中的索引
			   if (dayDiff >= 0 && dayDiff <= 7) {
			   	   targetIndex = dayDiff;
			   }

			   console.log('targetIndex:', targetIndex);
			   console.log('当前选中的场地类型索引 species_id:', this.species_id);

			   uni.navigateTo({
			   	 url:'../../pagesB/dingchang/dingchangrili?id='+ this.species[this.species_id].id + '&date='+date +'&name='+this.info.title + '&tabIndex=' + targetIndex + '&typeIndex=' + this.species_id
			   })

			   // uni.navigateTo({
			   // 	 url:'../../pagesB/theater/detail?id='+3
			   // })
			},
			
			editPl(){
				uni.navigateTo({
					 url:'../../pagesB/dingchang/commentdp?id='+this.id
				})	
			},
			
			goCall(){
				 uni.makePhoneCall({
						phoneNumber: this.info.contact_phone,
						success: function() {
							console.log('拨打电话成功');
						},
						fail: function() {
							console.log('拨打电话失败');
						}
					});
			},
			
			
			goMap() {
				let that = this;
				

				if(that.info.location_lat&&that.info.location_lon){
					
					//查看位置需要传经纬度才能执行
						let lat = parseFloat(that.info.location_lat)
						let log = parseFloat(that.info.location_lon)
						

						uni.authorize({
							scope: 'scope.userLocation',
							success(res) {
								
								uni.openLocation({
									latitude: log,
									longitude:  lat ,
									success: function(res) {
										console.log(res);
									},
									fail(err) {
										console.log(err);
									}
								});
							},
							fail(err) {
								console.log(err);
							}
						})
					
				}else{
					
					uni.showToast({
						icon:'none',
						title: '没有定位位置'
					})
					
				}

			},

			
			getPl(opt){
				
				var that = this;
				app.get('ApiVenues/getCommentList', {
					id: opt.id
				}, function(data) {
					
					console.log(data);
					
					if (data.status == 1) {
						
						that.pl = data.list;
						that.pl_num = data.count;
						
						// that.pl_txt =  data.data;
							
					}
				});
			},
			
			getdata: function(opt) {
				var that = this;

				that.loading = true;
				
				app.showLoading('加载中');
				
				app.get('ApiVenues/getVenuesInfo', {
					id: opt.id,
					
					latitude : uni.getStorageSync('latitude'),
					
					longitude : uni.getStorageSync('longitude')
					
				}, function(data) {
					
					app.showLoading(false);
					
					that.loading = false;  
					
					console.log(data);
					
					if (data.status == 1) {

			          that.info = data.data;
					  
					  that.species  = data.data.field_list;
					  
					  that.day_list = data.data.day_list;
					  
					  that.info.services = JSON.parse(opt.services);  
					  
					  that.info.pic_list = data.data.pic_list.split(",");
			
			
					} else {
						if (data.msg) {
							app.alert(data.msg, function() {
								if (data.url) app.goto(data.url);
							});
						} else if (data.url) {
							app.goto(data.url);
						} else {
							app.alert('您无查看权限');
						}
					}
				});
			},
			showsubqrcode: function() {
				this.$refs.qrcodeDialog.open();
			},
			closesubqrcode: function() {
				this.$refs.qrcodeDialog.close();
			},
			changePopupAddress: function(status) {
				this.xdata.popup_address = status;
			},
			setMendianData: function(data) {
				this.mendian_data = data;
			},
			toChoseAddress() {
				uni.navigateTo({
					url: '/pages/choseAddress/choseAddress'
				})
			},
			selectSpecies(ind) {
				this.species_id = ind
			}
		}
	}
</script>
<style lang="scss">
	.jc_center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.alignItems {
		display: flex;
		align-items: center;
	}

	.spaceBetween {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.swiper {
		height: 400rpx;
		width: 750rpx;
	}

	.swiper-item,
	.swiper-img {
		height: 400rpx;
		width: 750rpx;
	}

	.main {
		margin-top: -50rpx;
		padding: 0 32rpx;
		position: relative;
		z-index: 999;
	}

	.section {
		background: #ffffff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		padding: 32rpx;
	}

	.title {
		font-size: 38rpx;
		color: #333333;
		font-weight: bold;
		display: flex;
		justify-content: space-between;
	}

	.groupInformation {
		background-color: #f8f8ff;
		color: #999999;
		margin-top: 20rpx;
		padding-left: 30rpx;
		padding-bottom: 20rpx;
		width: 80%;

		.gi_title {
			padding-top: 20rpx;
			color: #000000;
		}
		.gi_title  :last-child {
			color: #999999;
			font-size: 26rpx;
		}
		.gi_level {
			font-size: 26rpx;
			margin-top: 10rpx;
		}

		.gi_level :last-child {
			color: #6b63ec;
		}

		.gi_time {
			display: flex;
			margin-top: 10rpx;

			.gi_time_r {
				display: flex;
				align-items: center;
				width: 80%;
			}

			.gi_time_r :first-child {
				margin-right: 2%;
			}
		}

		.gi_bt {
			padding: 15rpx 25rpx;
			border-radius: 30rpx;
			color: #ffffff;
			margin-top: 30rpx;
			background-color: #6b63ec;
			width: 50%;
		}
	}

	.titleAction {
		color: #847dee;
		font-weight: normal;
	}

	.tags {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.tagsItem {
		padding: 0rpx 8rpx;
		border-radius: 5rpx;
		border: 1rpx solid #E5E8EF;
		font-size: 24rpx;
		color: #666666;
		height: 36rpx;
		line-height: 36rpx;
		margin-right: 15rpx;
	}

	.tags_txt {
		color: #5257a7;
	}

	.info {
		display: flex;
		justify-content: space-between;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #E5E8EF;
	}

	.info_l {
		width: 70%;
		padding: 10rpx 20rpx;
		box-sizing: border-box;
		// height: 100rpx;
		background: #f3f3fe;
		border-radius: 15rpx;
	}

	.info_r {
		width: 28%;
		padding: 10rpx 20rpx;
		box-sizing: border-box;
		// height: 100rpx;
		background: #f3f3fe;
		border-radius: 15rpx;
	}

	.text {
		line-height: 45rpx;
		font-size: 24rpx;
		color: #999;
	}

	.flexMain {
		height: 45rpx;
		display: flex;
		align-items: center;
		color: #999;
		font-size: 28rpx;
	}

	.info_tag {
		border-radius: 32rpx 0 24rpx 32rpx;
		background: linear-gradient(to right, #8ca3fa, #6b63ec);
		color: #ffffff;
		padding: 0 10rpx;
		height: 40rpx;
		font-size: 28rpx;
		font-weight: bold;
		margin-right: 15rpx;
	}

	.texts {
		line-height: 45rpx;
		font-size: 28rpx;
		color: #222222;
	}

	.address {
		padding: 18rpx 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid #E5E8EF;
	}

	.addressInfo {
		width: 70%;
	}

	.addressInfoLabel {
		line-height: 32rpx;
		font-size: 26rpx;
		// font-weight: 700;
		margin-bottom: 15rpx;
	}

	.addressInfodis {
		line-height: 32rpx;
		font-size: 24rpx;
		color: #999999;
	}

	.addressTip {
		width: 20%;
		display: flex;
		justify-content: space-between;
	}

	.addressTipItem {
		// flex: 1;
	   text-align: center;
	}  

	.addressTipItemIcon {
		width: 30rpx;
		height: 30rpx;
		margin-bottom: 10rpx;
	}

	.addressTipItemName {
		line-height: 32rpx;
		font-size: 24rpx;
		color: #999999;
	}

	.tip {
		padding-top: 20rpx;
	}

	.tipInfo {
		font-size: 24rpx;
		color: #999999;
	}

	.tipAction {
		font-size: 24rpx;
		color: #999999;
		text-align: right;
		margin-top: 5rpx;
	}

	.block_row {
		display: flex;
		justify-content: center;
		align-items: center;
		padding-bottom: 20rpx;
	}

	.section_species {
		display: -webkit-box;
		overflow-x: scroll;
		width: 65%;

	}

	.subTitle {
		line-height: 50rpx;
		font-size: 32rpx;
		color: #aaa;
		font-weight: bold;
		margin-right: 30rpx;

		.subTitle_txt {
			// padding-bottom: 5rpx;
			color: #3d31ec;
		}

		.subTitle_txt_underline {
			border-bottom: 5rpx solid #3d31ec;
			margin: 10rpx 20rpx;
		}
	}

	.section_hint {
		color: #999999;
		font-size: 24rpx;
		margin-left: 20rpx;
		margin-bottom: 30rpx;
	}

	.list {
		display: -webkit-box;
		overflow-x: scroll;
	}

	.item {
		width: 150rpx;
		margin-right: 10rpx;
		border: 1rpx solid #6b63ec;
		background: rgba(241, 241, 254, 1.0);
		height: 190rpx;
		position: relative;
		padding-top: 20rpx;
		box-sizing: border-box;
		border-radius: 10rpx;
	}

	.name {
		line-height: 40rpx;
		font-size: 28rpx;
		text-align: center;
		color: blue;
	}

	.date {
		line-height: 50rpx;
		font-size: 32rpx;
		text-align: center;
		color: blue;
	}

	.action {
		width: 100%;
		height: 50rpx;
		background: #6b63ec;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		color: #ffffff;
		position: absolute;
		left: 0;
		bottom: 0;
	}

	.label {
		display: -webkit-box;
		overflow-x: scroll;
		margin-top: 20rpx;

		.label_txt {
			padding: 10rpx;
			background-color: #f3f3fe;
			color: #6b63ec;
			margin-right: 20rpx;
		}
	}

	.commentContent {
		margin-top: 20rpx;
		.content_title {
			display: flex;
			align-items: center;
			.content_title_img {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
			}

			.content_title_l {
				margin-left: 20rpx;
			}
			.content_title_l  :last-child {
			  margin-top: 10rpx;
			}
		}
		.content_title_r {
			color: #999999;
		}
		.content_title_r  :last-child {
		  margin-top: 10rpx;
		  background-color: #fbf2f0;
		  color: #e9a7a7;
		  padding: 5rpx;
		}
		.commentContent_txt{
			margin-top: 30rpx;
		}
	}
</style>