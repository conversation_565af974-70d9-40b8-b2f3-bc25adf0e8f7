<template>
	<view class="container">
		<block v-if="isload">
			<dd-tab :itemdata="[t('余额')+'明细','充值记录','提现记录','转账记录','积分记录','消费值积分记录','创业值明细','黑积分明细']" :itemst="['0','1','2','4','5','6','7','8']" :st="st"
				:isfixed="true" @changetab="changetab"></dd-tab>
			<view class="content">
				<block v-if="st==0">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">{{item.remark}}</text>
							<text class="t2">{{item.createtime}}</text>
							<text class="t3">变更后余额: {{item.after}}</text>
						</view>
						<view class="f2">
							<text class="t1" v-if="item.money>0">+{{item.money}}</text>
							<text class="t2" v-else>{{item.money}}</text>
						</view>
					</view>
				</block>
				<block v-if="st==1">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">充值金额：{{item.money}}元</text>
							<text class="t2">{{item.createtime}}</text>
						</view>
						<view class="f3">
							<text class="t1" v-if="item.status==0">充值失败</text>
							<text class="t2" v-if="item.status==1">充值成功</text>
						</view>
					</view>
				</block>
				<block v-if="st==2">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">提现金额：{{item.money}}元</text>
							<text class="t2">{{item.createtime}}</text>
						</view>
						<view class="f3">
							<text class="t1" v-if="item.status==0">审核中</text>
							<text class="t1" v-if="item.status==1">已审核</text>
							<text class="t2" v-if="item.status==2">已驳回</text>
							<text class="t1" v-if="item.status==3">已打款</text>
						</view>
					</view>
				</block>
				<block v-if="st==3">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">兑换积分：{{item.credit}}</text>
							<text class="t2">兑换时间:{{item.addtime}}</text>
							<text class="t2" v-if="item.status==1">预计兑换余额:{{item.money}}</text>
							<text class="t2" v-if="item.status==2">已兑换余额:{{item.money}}</text>
							<view class="t2" v-if="item.status==1">
								<button @tap="onclick1(item.id)"
									style="border: 1px #03bc01 solid;background: #03bc01;margin-top: 10px;color:#fff;width: 130%;">立即提现</button>
							</view>
						</view>
						<view class="f3">
							<text style="color:blue;" v-if="item.status==2">提现成功</text>
							<text style="color:red;" v-if="item.status==3">提现失败</text>
						</view>
					</view>
				</block>
				<block v-if="st==4">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">{{item.remark}}</text>
							<text class="t1">用户：{{item.phone}}</text>
							<text class="t2">{{item.createtime}}</text>
							<text class="t3">变更后余额: {{item.after_money}}</text>
							<text class="t3">手续费: {{item.charge_money}}</text>
						</view>
						<view class="f2">
							<text class="t1" v-if="item.money > 0">{{item.money}}</text>
							<text class="t2" v-else>{{item.money}}</text>
						</view>
					</view>
				</block>
				<block v-if="st==5">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">{{item.remark}}</text>
							<text class="t1">用户：{{item.phone}}</text>
							<text class="t2">{{item.createtime}}</text>
							<text class="t3">变更后余额: {{item.after_money}}</text>
							<text class="t3">手续费: {{item.charge_money}}</text>
						</view>
						<view class="f2">
							<text class="t1" v-if="item.money > 0">{{item.money}}</text>
							<text class="t2" v-else>{{item.money}}</text>
						</view>
					</view>
				</block>
				<block v-if="st==6">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">{{item.remark}}</text>
							<text class="t1">用户：{{item.phone}}</text>
							<text class="t2">{{item.createtime}}</text>
							<text class="t3">变更后
							<text v-if="item.money > 0">消费值</text> 
							<text class="t2" v-else>绿积分</text>
							{{item.after_money}}</text>
							<!-- <text class="t3">手续费: {{item.charge_money}}</text> -->
						</view>
						<view class="f2">
							<text class="t1" v-if="item.money > 0">{{item.money}}</text>
							<text class="t2" v-else>{{item.money}}</text>
						</view>
					</view>
				</block>
				<block v-if="st==7">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">{{item.remark}}</text>
							<text class="t2">{{item.createtime}}</text>
							<text class="t3">变更后余额: {{item.after}}</text>
						</view>
						<view class="f2">
							<text class="t1" v-if="item.busTotal>0">+{{item.busTotal}}</text>
							<text class="t2" v-else>{{item.busTotal}}</text>
						</view>
					</view>
				</block>
				<block v-if="st==8">
					<view v-for="(item, index) in datalist" :key="index" class="item">
						<view class="f1">
							<text class="t1">{{item.remark}}</text>
							<text class="t2">{{item.createtime}}</text>
							<text class="t3">变更后余额: {{item.after}}</text>
						</view>
						<view class="f2">
							<text class="t1" v-if="item.money>0">+{{item.money}}</text>
							<text class="t2" v-else>{{item.money}}</text>
						</view>
					</view>
				</block>
			</view>
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		inject: ['reload'], // 注入 reload 方法
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				canwithdraw: false,
				textset: {},
				st: 0,
				datalist: [],
				pagenum: 1,
				nodata: false,
				nomore: false
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.st = this.opt.st || 0;
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata(true);
		},
		onReachBottom: function() {
			if (!this.nodata && !this.nomore) {
				this.pagenum = this.pagenum + 1;
				this.getdata(true);
			}
		},
		methods: {
			getdata: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var st = that.st;
				that.nodata = false;
				that.nomore = false;
				that.loading = true;
				app.post('ApiMy/moneylog', {
					st: st,
					pagenum: pagenum
				}, function(res) {
					that.loading = false;
					var data = res.data;
					if (pagenum == 1) {
						that.textset = app.globalData.textset;
						uni.setNavigationBarTitle({
							title: that.t('余额') + '明细'
						});
						that.canwithdraw = res.canwithdraw;
						that.datalist = data;
						if (data.length == 0) {
							that.nodata = true;
						}
						that.loaded();
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}
				});
			},
			changetab: function(st) {
				this.st = st;
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				this.getdata();
			},
			onclick1: function(tixianid) {
				var that = this;
				app.showLoading('提交中');
				app.post('ApiMy/withtixiancheck', {
					tixianid: tixianid
				}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					} else {
						app.success(res.msg);
						that.subscribeMessage(function() {
							setTimeout(function() {
								app.goto('moneylog?st=3');
							}, 1000);
						});
					}
				});
			},
		}
	};
</script>
<style>
	.container {
		width: 100%;
		margin-top: 90rpx;
		display: flex;
		flex-direction: column
	}

	.content {
		width: 94%;
		margin: 0 3% 20rpx 3%;
	}

	.content .item {
		width: 100%;
		background: #fff;
		margin: 20rpx 0;
		padding: 20rpx 20rpx;
		border-radius: 8px;
		display: flex;
		align-items: center
	}

	.content .item:last-child {
		border: 0
	}

	.content .item .f1 {
		width: 500rpx;
		display: flex;
		flex-direction: column
	}

	.content .item .f1 .t1 {
		color: #000000;
		font-size: 30rpx;
		word-break: break-all;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.content .item .f1 .t2 {
		color: #666666
	}

	.content .item .f1 .t3 {
		color: #666666
	}

	.content .item .f2 {
		flex: 1;
		width: 200rpx;
		font-size: 36rpx;
		text-align: right
	}

	.content .item .f2 .t1 {
		color: #03bc01
	}

	.content .item .f2 .t2 {
		color: #000000
	}

	.content .item .f3 {
		flex: 1;
		width: 200rpx;
		font-size: 32rpx;
		text-align: right
	}

	.content .item .f3 .t1 {
		color: #03bc01
	}

	.content .item .f3 .t2 {
		color: #000000
	}

	.data-empty {
		background: #fff
	}
</style>
