<template>
  <view>
    <block v-if="isload">
      <view class="topsearch flex-y-center">
        <view class="f1 flex-y-center">
          <image class="img" src="/static/img/search_ico.png"></image>
          <input :value="keyword" placeholder="输入昵称/姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm" @input="searchChange" />
        </view>
        <!-- 会员等级筛选开始 -->
        <view class="level-filter">
          <picker mode="selector" :range="memberLevels" range-key="name" @change="onLevelChange">
            <view class="picker">
              {{ selectedLevelName }}
            </view>
          </picker>
        </view>
        <!-- 会员等级筛选结束 -->
      </view>
      <view class="content" v-if="datalist && datalist.length>0">
        <view class="label">
          <text class="t1">{{t('会员')}}列表（共{{count}}人）</text>
        </view>
        <block v-for="(item, index) in datalist" :key="index">
          <view class="item">
            <view class="f1" @tap="goto" :data-url="'detail?mid=' + item.id">
              <image :src="item.headimg"></image>
              <view class="t2">
                <view class="x1 flex-y-center">
                  {{item.nickname}}
                  <image style="margin-left:10rpx;width:40rpx;height:40rpx" src="/static/img/nan.png" v-if="item.sex==1" />
                  <image style="margin-left:10rpx;width:40rpx;height:40rpx" src="/static/img/nv.png" v-if="item.sex==2" />
                </view>
                <text class="x2">最后访问：{{item.last_visittime}}</text>
                <text class="x2">加入时间：{{item.createtime}}</text>
                <text class="x2">{{item.province ? '' : item.province}}{{item.city ? '' : item.city}}</text>
                <text class="x2" v-if="item.remark" style="color:#a66;font-size:22rpx">{{item.remark}}</text>
              </view>
            </view>
            <view class="f2">
            <view class="btn" @tap="goto" :data-url="'/daihuobiji/detail/userbijishe?mid=' + item.id">TA的主页</view>
            <!--   <view class="btn" @tap="goto" :data-url="'/admin/member/history?id='+item.id">足迹</view>
               <view class="btn" @tap="goto" :data-url="'/pagesExa/daike/daigoupick?id='+item.id">帮他下单</view> -->
            </view>
          </view>
        </block>
      </view>
      <nomore v-if="nomore"></nomore>
      <nodata v-if="nodata"></nodata>
    </block>
    <popmsg ref="popmsg"></popmsg>
    <loading v-if="loading"></loading>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt:{},
			loading:false,
      isload: false,
			pre_url:app.globalData.pre_url,

      datalist: [],
      pagenum: 1,
      nomore: false,
			nodata:false,
      count: 0,
      keyword: '',
      auth_data: {},
      // 会员等级数据
      memberLevels: [],      // 会员等级列表
      levelid: '',           // 选中的会员等级ID
      selectedLevelName: '选择会员等级', // 显示在选择器上的会员等级名称
    };
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
			var pagenum = that.pagenum;
      var keyword = that.keyword;
      var levelid = that.levelid; // 包含 levelid 参数
			that.nodata = false;
			that.nomore = false;
			that.loading = true;
      app.post('Apidaihuobiji/getFollowersList', {keyword: keyword, pagenum: pagenum, levelid: levelid}, function (res) {
        that.loading = false;
        var data = res.datalist;
        if (pagenum == 1) {
					that.datalist = data;
					that.count = res.count;
					that.auth_data = res.auth_data;
          if (data.length == 0) {
            that.nodata = true;
          }
          // 接收后端传递的会员等级数据
          that.memberLevels = res.member_levels;
          // 如果已有选中的 levelid，更新 selectedLevelName
          if (that.levelid) {
            var selectedLevel = that.memberLevels.find(level => level.id == that.levelid);
            if (selectedLevel) {
              that.selectedLevelName = selectedLevel.name;
            }
          }
					uni.setNavigationBarTitle({
						title: that.t('会员') + '列表'
					});
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
    searchChange: function (e) {
      this.keyword = e.detail.value;
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
      that.getdata();
    },
    // 会员等级选择事件
    onLevelChange: function (e) {
      var index = e.detail.value;
      var selectedLevel = this.memberLevels[index];
      this.levelid = selectedLevel.id;
      this.selectedLevelName = selectedLevel.name;
      // 重新获取数据
      this.getdata();
    }
  }
};
</script>
<style>
@import "../common.css";
.topsearch{width:94%;margin:16rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.content{width: 94%;margin:0 3%;background: #fff;border-radius:16rpx}
.content .label{display:flex;width: 100%;padding:24rpx 16rpx;color: #333;}
.content .label .t1{flex:1}
.content .label .t2{ width:300rpx;text-align:right}

.content .item{width: 100%;padding: 32rpx;border-top: 1px #f5f5f5 solid;min-height: 112rpx;display:flex;align-items:center;}
.content .item image{width:90rpx;height:90rpx;}
.content .item .f1{display:flex;flex:1}
.content .item .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}
.content .item .f1 .t2 .x1{color: #222;font-size:30rpx;}
.content .item .f1 .t2 .x2{color: #999;font-size:24rpx}

.content .item .f2{display:flex;flex-direction:column;width:auto;text-align:right;border-left:1px solid #eee}
.content .item .f2 .t1{ font-size: 40rpx;color: #666;height: 40rpx;line-height: 40rpx;}
.content .item .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}
.content .item .btn{ border-radius:8rpx; padding:3rpx 12rpx;margin-left: 10px;border: 1px #999 solid; text-align:center; font-size:28rpx;color:#333;}
.content .item .btn:nth-child(n+2) {margin-top: 10rpx;}
.topsearch{width:94%;margin:16rpx 3%; display: flex; align-items: center;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1; display: flex; align-items: center;}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.level-filter { margin-left: 10rpx; }
.level-filter .picker { height: 60rpx; line-height: 60rpx; padding: 0 20rpx; background-color: #fff; border-radius: 30rpx; color: #333; }

.content{width: 94%;margin:0 3%;background: #fff;border-radius:16rpx}
</style>