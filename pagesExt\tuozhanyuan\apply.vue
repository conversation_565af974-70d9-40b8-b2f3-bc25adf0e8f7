<template>
  <view>
    <block v-if="isload">
      <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==2">
        <parse :content="tset.verify_reject || '审核未通过：'"/>{{info.reason}}，请修改后重新提交
      </view>
      <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==0">
        <parse :content="tset.verify_notice || '您的拓展员申请已提交成功，请耐心等待审核，平台将于3个工作日内联系您核实信息，请留意来电'"/>
      </view>
      <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==1">
        <parse :content="tset.verify_success || '恭喜您审核通过！您已成为拓展员'"/>
      </view>
      <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx">
        <parse :content="tset.verify_normal || '温馨提示：成为拓展员后可享受推广分佣权益'"/>
      </view>

      <form @submit="subform">
        <view class="apply_box">
          <view class="apply_item">
            <view>真实姓名<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="realname" :value="info.realname" placeholder="请填写真实姓名"></input></view>
          </view>
          <view class="apply_item">
            <view>联系电话<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="tel" :value="info.tel" placeholder="请填写手机号码"></input></view>
          </view>
          <view class="apply_item">
            <view>微信号<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="wechat" :value="info.wechat" placeholder="请填写微信号"></input></view>
          </view>
          <view class="apply_item">
            <view>邀请码</view>
            <view class="flex-y-center"><input type="text" name="invite_code" :value="info.invite_code" placeholder="请填写邀请人邀请码（可选）"></input></view>
          </view>
        </view>

        <view class="apply_box" v-if="!simpleApply">
          <view class="apply_item">
            <view>申请等级<text style="color:red"> *</text></view>
            <view>
              <picker @change="levelChange" :value="levelIndex" :range="levelNames">
                <view class="picker">{{levelNames[levelIndex] || '请选择申请等级'}}</view>
              </picker>
            </view>
          </view>
          <view class="apply_item">
            <view style="white-space: nowrap; min-width: 120rpx;">申请理由<text style="color:red"> *</text></view>
            <view class="reason-container">
              <view class="flex-y-center">
                <textarea name="reason"
                          placeholder="请详细说明您的申请理由，如：推广经验、客户资源、市场优势等"
                          :value="info.reason"
                          style="height: 160rpx; font-size: 28rpx; line-height: 1.5;">
                </textarea>
              </view>
            </view>
          </view>
          <view class="apply_item">
            <view>详细地址<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="address" :value="info.address" placeholder="请输入详细地址"></input></view>
          </view>
        </view>

        <!-- 申请条件显示 -->
        <view class="apply_box" v-if="showApplyConditions && !simpleApply">
          <view class="apply_item" style="border-bottom:0">
            <text style="color: #333; font-weight: bold;">申请条件</text>
          </view>
          <view class="condition-item" v-for="(condition, index) in applyConditionsList" :key="index">
            <view class="condition-text">{{condition.name}}</view>
            <view class="condition-status"
                  :class="condition.completed ? 'completed' : 'incomplete'"
                  @tap="clickCondition(condition, index)">
              {{condition.completed ? '已满足' : '未满足'}}
              <text v-if="!condition.completed && condition.type === 'buy_product'" style="font-size: 20rpx; margin-left: 8rpx;">(点击购买)</text>
            </view>
          </view>
          <view style="padding: 10rpx 0; font-size: 24rpx; color: #999;">
            <text>注：申请此等级需要满足以上所有条件</text>
          </view>
        </view>

        <!-- 升级条件显示 -->
        <view class="apply_box" v-if="showUpgradeConditions && !simpleApply">
          <view class="apply_item" style="border-bottom:0">
            <text style="color: #333; font-weight: bold;">升级条件</text>
          </view>
          <view class="condition-item" v-for="(condition, index) in upgradeConditionsList" :key="index">
            <view class="condition-text">{{condition.name}}</view>
            <view class="condition-status"
                  :class="condition.completed ? 'completed' : 'incomplete'"
                  @tap="clickUpgradeCondition(condition, index)">
              {{condition.completed ? '已完成' : '未完成'}}
              <text v-if="!condition.completed && condition.type === 'buy_product'" style="font-size: 20rpx; margin-left: 8rpx;">(点击购买)</text>
            </view>
          </view>
          <view style="padding: 10rpx 0; font-size: 24rpx; color: #999;">
            <text>注：成为此等级后，满足以上条件可升级到更高等级</text>
          </view>
        </view>

        <view class="apply_box" v-if="!simpleApply">
          <view class="apply_item" style="border-bottom:0"><text>身份证正面<text style="color:red"> *</text></text></view>
          <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
            <view v-for="(item, index) in idcard_front" :key="index" class="layui-imgbox">
              <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="idcard_front"><image src="/static/img/ico-del.png"></image></view>
              <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
            </view>
            <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="idcard_front" v-if="idcard_front.length==0"></view>
          </view>
          <input type="text" hidden="true" name="idcard_front" :value="idcard_front.join(',')" maxlength="-1"></input>
        </view>

        <view class="apply_box" v-if="!simpleApply">
          <view class="apply_item" style="border-bottom:0"><text>身份证反面<text style="color:red"> *</text></text></view>
          <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
            <view v-for="(item, index) in idcard_back" :key="index" class="layui-imgbox">
              <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="idcard_back"><image src="/static/img/ico-del.png"></image></view>
              <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
            </view>
            <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="idcard_back" v-if="idcard_back.length==0"></view>
          </view>
          <input type="text" hidden="true" name="idcard_back" :value="idcard_back.join(',')" maxlength="-1"></input>
        </view>

        <view class="apply_box" v-if="!simpleApply">
          <view class="apply_item" style="border-bottom:0"><text>手持身份证照片<text style="color:red"> *</text></text></view>
          <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
            <view v-for="(item, index) in idcard_hold" :key="index" class="layui-imgbox">
              <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="idcard_hold"><image src="/static/img/ico-del.png"></image></view>
              <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
            </view>
            <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="idcard_hold" v-if="idcard_hold.length==0"></view>
          </view>
          <input type="text" hidden="true" name="idcard_hold" :value="idcard_hold.join(',')" maxlength="-1"></input>
        </view>

        <view class="apply_box" v-if="!simpleApply">
          <view class="apply_item" style="border-bottom:0"><text>其他证明材料</text></view>
          <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
            <view v-for="(item, index) in other_files" :key="index" class="layui-imgbox">
              <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="other_files"><image src="/static/img/ico-del.png"></image></view>
              <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
            </view>
            <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="other_files"></view>
          </view>
          <input type="text" hidden="true" name="other_files" :value="other_files.join(',')" maxlength="-1"></input>
        </view>

        <block v-if="tset.xieyi_show==1">
          <view class="flex-y-center" style="margin-left:20rpx;color:#999" v-if="!info.id || info.status==2">
            <checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox value="1" :checked="isagree"></checkbox>阅读并同意</label></checkbox-group>
            <text style="color:#666" @tap="showxieyiFun">《拓展员入驻协议》</text>
          </view>
        </block>

        <view style="padding:30rpx 0">
          <button v-if="!info.id || info.status==2" form-type="submit" class="set-btn" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">提交申请</button>
        </view>
      </form>

      <!-- 底部返回按钮 -->
      <view class="content">
        <view class="back-button" @tap="goBack">
          <text class="t1">返回</text>
        </view>
      </view>

      <!-- 协议弹窗 -->
      <view id="xieyi" :hidden="!showxieyi" style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)">
        <view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px">
          <view style="overflow:scroll;height:100%;">
            <parse :content="tset.xieyi"/>
          </view>
          <view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center" @tap="hidexieyi">已阅读并同意</view>
        </view>
      </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      pre_url: app.globalData.pre_url,
      levelNames: [],
      levelIndex: 0,
      selectedLevel: null,
      isagree: false,
      showxieyi: false,
      idcard_front: [],
      idcard_back: [],
      idcard_hold: [],
      other_files: [],
      info: {},
      tset: {},
      levels: [],
      showApplyConditions: false,
      showUpgradeConditions: false,
      applyConditionsList: [],
      upgradeConditionsList: [],
      simpleApply: false
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  
  onPullDownRefresh: function () {
    this.getdata();
  },
  
  methods: {
    getdata: function () {
      var that = this;
      that.loading = true;
      app.get('ApiTuozhanyuan/apply', {}, function (res) {
        that.loading = false;
        if (res.status == 2) {
          app.alert(res.msg, function () {
            app.goto('/admin/index/index', 'redirect');
          });
          return;
        }
        uni.setNavigationBarTitle({
          title: res.title || '拓展员申请'
        });
        
        // 处理等级数据
        var levels = res.levels || [];
        var levelNames = [];
        for (var i in levels) {
          levelNames.push(levels[i].name);
          
          // 转换属性名：apply_conditions -> applyConditions
          if (levels[i].apply_conditions) {
            levels[i].applyConditions = levels[i].apply_conditions;
            delete levels[i].apply_conditions;
          }
          
          // 转换属性名：upgrade_conditions -> upgradeConditions
          if (levels[i].upgrade_conditions) {
            levels[i].upgradeConditions = levels[i].upgrade_conditions;
            delete levels[i].upgrade_conditions;
          }
          
          // 解析申请条件（如果是字符串格式）
          if (levels[i].applyConditions && typeof levels[i].applyConditions === 'string') {
            try {
              levels[i].applyConditions = JSON.parse(levels[i].applyConditions);
            } catch (e) {
              console.log('解析申请条件失败:', e);
              levels[i].applyConditions = [];
            }
          }
          
          // 解析升级条件（如果是字符串格式）
          if (levels[i].upgradeConditions && typeof levels[i].upgradeConditions === 'string') {
            try {
              levels[i].upgradeConditions = JSON.parse(levels[i].upgradeConditions);
            } catch (e) {
              console.log('解析升级条件失败:', e);
              levels[i].upgradeConditions = [];
            }
          }
        }
        that.levels = levels;
        that.levelNames = levelNames;
        
        // 处理现有申请信息
        that.tset = res.tset || {};
        that.info = res.info || {};
        that.simpleApply = res.simpleApply || false;
        
        // 处理图片数据
        that.idcard_front = res.info && res.info.idcard_front ? res.info.idcard_front.split(',') : [];
        that.idcard_back = res.info && res.info.idcard_back ? res.info.idcard_back.split(',') : [];
        that.idcard_hold = res.info && res.info.idcard_hold ? res.info.idcard_hold.split(',') : [];
        that.other_files = res.info && res.info.other_files ? res.info.other_files.split(',') : [];
        
        // 设置默认选中的等级
        if (that.info.level_id) {
          // 如果有现有申请，选择对应等级
          for (var i = 0; i < that.levels.length; i++) {
            if (that.levels[i].id == that.info.level_id) {
              that.levelIndex = i;
              that.selectedLevel = that.levels[i];
              that.updateConditionsDisplay();
              break;
            }
          }
        } else if (that.levels.length > 0) {
          // 如果没有现有申请，默认选择第一个等级
          that.levelIndex = 0;
          that.selectedLevel = that.levels[0];
          that.updateConditionsDisplay();
        }

        that.loaded();
      });
    },

    levelChange: function (e) {
      this.levelIndex = e.detail.value;
      this.selectedLevel = this.levels[this.levelIndex];
      this.updateConditionsDisplay();
    },

    updateConditionsDisplay: function() {
      if (this.selectedLevel) {
        // 处理申请条件
        if (this.selectedLevel.applyConditions && this.selectedLevel.applyConditions.length > 0) {
          this.showApplyConditions = true;
          this.applyConditionsList = this.selectedLevel.applyConditions;
        } else {
          this.showApplyConditions = false;
          this.applyConditionsList = [];
        }

        // 处理升级条件
        if (this.selectedLevel.upgradeConditions && this.selectedLevel.upgradeConditions.length > 0) {
          this.showUpgradeConditions = true;
          this.upgradeConditionsList = this.selectedLevel.upgradeConditions;
        } else {
          this.showUpgradeConditions = false;
          this.upgradeConditionsList = [];
        }
      } else {
        this.showApplyConditions = false;
        this.showUpgradeConditions = false;
        this.applyConditionsList = [];
        this.upgradeConditionsList = [];
      }
    },

    clickCondition: function(condition, index) {
      this.handleConditionClick(condition);
    },

    clickUpgradeCondition: function(condition, index) {
      this.handleConditionClick(condition);
    },
    
    subform: function (e) {
      var that = this;
      var info = e.detail.value;
      
      // 添加 id 到表单数据中
      if (that.info && that.info.id) {
        info.id = that.info.id;
      }
      
      // 验证必填项
      if (info.realname == '') {
        app.error('请填写真实姓名');
        return false;
      }
      if (info.tel == '') {
        app.error('请填写联系电话');
        return false;
      }
      if (info.wechat == '') {
        app.error('请填写微信号');
        return false;
      }

      // 如果不是简单申请模式，验证其他必填项
      if (!that.simpleApply) {
        if (info.reason == '') {
          app.error('请填写申请理由');
          return false;
        }
        if (info.address == '') {
          app.error('请填写详细地址');
          return false;
        }
        if (info.idcard_front == '') {
          app.error('请上传身份证正面');
          return false;
        }
        if (info.idcard_back == '') {
          app.error('请上传身份证反面');
          return false;
        }
        if (info.idcard_hold == '') {
          app.error('请上传手持身份证照片');
          return false;
        }
      }
      
      // 设置申请等级
      if (!that.simpleApply) {
        info.level_id = that.selectedLevel ? that.selectedLevel.id : null;
        if (!info.level_id) {
          app.error('请选择申请等级');
          return false;
        }
      }
      
      // 检查是否同意协议
      if (that.tset.xieyi_show == 1 && !that.isagree) {
        app.error('请先阅读并同意拓展员入驻协议');
        return false;
      }
      
      // 提交表单数据
      app.showLoading('提交中');
      app.post("ApiTuozhanyuan/apply", { info: info }, function (res) {
        app.showLoading(false);
        if (res.status == 1) {
          app.success(res.msg);
          setTimeout(function () {
            app.goto('/pages/index/index');
          }, 1500);
        } else {
          app.error(res.msg);
        }
      });
    },
    
    isagreeChange: function (e) {
      var val = e.detail.value;
      this.isagree = val.length > 0;
    },
    
    showxieyiFun: function () {
      this.showxieyi = true;
    },
    
    hidexieyi: function () {
      this.showxieyi = false;
      this.isagree = true;
    },
    
    uploadimg: function (e) {
      var that = this;
      var field = e.currentTarget.dataset.field;
      var pics = that[field];
      if (!pics) pics = [];
      
      var maxCount = 1;
      if (field == 'other_files') maxCount = 5;
      
      app.chooseImage(function (urls) {
        for (var i = 0; i < urls.length; i++) {
          pics.push(urls[i]);
        }
        that[field] = pics;
      }, maxCount);
    },
    
    removeimg: function (e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      var field = e.currentTarget.dataset.field;
      var pics = that[field];
      pics.splice(index, 1);
      that[field] = pics;
    },
    
    goBack: function () {
      uni.navigateBack({
        delta: 1
      });
    },

    handleConditionClick: function (condition) {
      // 如果是未满足的购买商品条件，跳转到商品页面
      if (!condition.completed && condition.type === 'buy_product' && condition.product_id) {
        app.goto('/shopPackage/shop/product?id=' + condition.product_id);
      }
    }
  }
};
</script>

<style>
radio{transform: scale(0.6);}
checkbox{transform: scale(0.6);}
.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
.apply_title { background: #fff}
.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}
.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}

.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }
.apply_box .apply_item:last-child{ border:none}
.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}
.apply_item input::placeholder{ color:#999999}
.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}
.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }
.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }
.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}

.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}
.layui-imgbox-close image{width:100%;height:100%}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}

/* 返回按钮样式 */
.back-button {
  width: 90%;
  background: #b60000;
  color: #fff;
  text-align: center;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 50px;
  margin-top: 0rpx;
  margin: auto;
}

.back-button .t1 {
  font-size: 30rpx;
  color: #fff;
}

/* 升级条件样式 */
.condition-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #eee;
}

.condition-text {
  font-size: 28rpx;
  color: #333;
}

.condition-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.condition-status.completed {
  background: #e8f5e8;
  color: #52c41a;
}

.condition-status.incomplete {
  background: #fff2e8;
  color: #fa8c16;
}

.picker {
  text-align: right;
  color: #111;
  font-size: 28rpx;
}

/* 申请理由相关样式 */
.reason-container {
  width: 100%;
}
</style>