<template>
<view class="container">
	<view class="worker-info" v-if="worker">
		<image class="worker-avatar" :src="worker.headimg || '/static/img/default_avatar.png'"></image>
		<view class="worker-detail">
			<view class="worker-name">{{worker.realname}}</view>
			<view class="worker-role" :class="{'leader': worker.worker_role == 1}">{{worker.worker_role == 1 ? '组长' : '组员'}}</view>
		</view>
	</view>
	
	<view class="comment-section">
		<view class="section-title">服务评价</view>
		
		<!-- 评分区域 -->
		<view class="score-section">
			<view class="score-title">请为该技师的服务打分</view>
			<view class="score-stars">
				<view v-for="(item, index) in 5" :key="index" 
					class="star" 
					:class="{'active': index < score}" 
					@tap="setScore" 
					:data-score="index + 1">
					⭐
				</view>
			</view>
			<view class="score-text">{{getScoreText(score)}}</view>
		</view>
		
		<!-- 评价内容 -->
		<view class="content-section">
			<view class="content-title">评价内容（选填）</view>
			<textarea 
				class="content-input" 
				v-model="content"
				placeholder="请描述该技师的服务表现..."
				maxlength="200"></textarea>
			<view class="content-count">{{content.length}}/200</view>
		</view>
		
		<!-- 图片上传 -->
		<view class="photo-section">
			<view class="photo-title">上传图片（选填）</view>
			<view class="photo-list">
				<view v-for="(item, index) in photos" :key="index" class="photo-item">
					<image :src="item" class="photo-preview" @tap="previewImage" :data-url="item"></image>
					<view class="photo-delete" @tap="deletePhoto" :data-index="index">×</view>
				</view>
				<view class="photo-add" @tap="addPhoto" v-if="photos.length < 3">
					<text class="add-icon">+</text>
					<text class="add-text">添加图片</text>
				</view>
			</view>
		</view>
	</view>
	
	<view class="bottom-bar">
		<view class="submit-btn" @tap="submitComment" :class="{'disabled': score == 0}">提交评价</view>
	</view>
	
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
  export default {
    data() {
      return {
        isload: false,
        loading: false,
        order_id: 0,
        worker_id: 0,
        worker_name: '',
        worker_role: '',
        worker: null,
        order: null,
        comment: null,
        score: 0,
        content: '',
        photos: [],
        opt: {
          icontext: app.gopt.icontext,
          icon: true,
          user: true,
          cart: true,
          index: true,
          category: true
        }
      };
    },
    onLoad: function (option) {
      this.order_id = option.order_id || 0;
      this.worker_id = option.worker_id || 0;
      this.worker_name = option.worker_name || '';
      this.worker_role = option.worker_role || '';
      this.getdata();
    },
    methods: {
      getdata: function () {
        var that = this;
        app.post("ApiYuyue/commentWorkerTeam", {
          order_id: that.order_id,
          worker_id: that.worker_id
        }, function (res) {
          app.stopPullRefresh();
          if (res.status == 1) {
            that.worker = res.worker;
            that.order = res.order;
            that.comment = res.comment;
            
            // 如果已经评价过，显示评价内容
            if (that.comment) {
              that.score = that.comment.score || 0;
              that.content = that.comment.content || '';
              if (that.comment.content_pic) {
                that.photos = that.comment.content_pic.split(',').filter(item => item);
              }
            }
            
            that.isload = true;
            that.loading = false;
          } else {
            app.alert(res.msg, function () {
              app.goback();
            });
          }
        });
      },
      setScore: function (e) {
        if (this.comment) return; // 已评价不能修改
        this.score = parseInt(e.currentTarget.dataset.score);
      },
      getScoreText: function (score) {
        const texts = ['', '非常不满意', '不满意', '一般', '满意', '非常满意'];
        return texts[score] || '';
      },
      addPhoto: function () {
        if (this.comment) return; // 已评价不能修改
        var that = this;
        uni.chooseImage({
          count: 3 - that.photos.length,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
          success: function (res) {
            const tempFilePaths = res.tempFilePaths;
            for (let i = 0; i < tempFilePaths.length && that.photos.length < 3; i++) {
              that.uploadPhoto(tempFilePaths[i]);
            }
          }
        });
      },
      uploadPhoto: function (tempFilePath) {
        var that = this;
        app.showLoading('上传中...');
        uni.uploadFile({
          url: app.d.hostapi + 'ApiCommon/upload',
          filePath: tempFilePath,
          name: 'file',
          header: {
            'Authorization': app.globalData.token
          },
          success: function (res) {
            app.showLoading(false);
            try {
              const data = JSON.parse(res.data);
              if (data.status == 1) {
                that.photos.push(data.url);
              } else {
                app.alert(data.msg || '上传失败');
              }
            } catch (e) {
              app.alert('上传失败');
            }
          },
          fail: function () {
            app.showLoading(false);
            app.alert('上传失败');
          }
        });
      },
      deletePhoto: function (e) {
        if (this.comment) return; // 已评价不能修改
        const index = parseInt(e.currentTarget.dataset.index);
        this.photos.splice(index, 1);
      },
      previewImage: function (e) {
        const url = e.currentTarget.dataset.url;
        uni.previewImage({
          urls: this.photos,
          current: url
        });
      },
      submitComment: function () {
        if (this.comment) {
          app.alert('您已经评价过该技师了');
          return;
        }
        
        if (this.score == 0) {
          app.alert('请先进行评分');
          return;
        }
        
        var that = this;
        app.confirm('确定提交评价吗？提交后无法修改', function () {
          app.showLoading('提交中...');
          app.post("ApiYuyue/commentWorkerTeam", {
            order_id: that.order_id,
            worker_id: that.worker_id,
            score: that.score,
            content: that.content,
            content_pic: that.photos.join(',')
          }, function (res) {
            app.showLoading(false);
            if (res.status == 1) {
              app.success(res.msg);
              setTimeout(function () {
                app.goback();
              }, 1500);
            } else {
              app.alert(res.msg);
            }
          });
        });
      }
    }
  };
</script>

<style scoped>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.worker-info {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.worker-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.worker-detail {
  flex: 1;
}

.worker-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.worker-role {
  font-size: 26rpx;
  color: #999;
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  display: inline-block;
}

.worker-role.leader {
  background: #e8f4ff;
  color: #007aff;
}

.comment-section {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 100rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.score-section {
  margin-bottom: 40rpx;
  text-align: center;
}

.score-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.score-stars {
  display: flex;
  justify-content: center;
  margin-bottom: 10rpx;
}

.star {
  font-size: 60rpx;
  margin: 0 10rpx;
  color: #ddd;
  transition: color 0.3s;
}

.star.active {
  color: #ffb400;
}

.score-text {
  font-size: 26rpx;
  color: #666;
}

.content-section {
  margin-bottom: 40rpx;
}

.content-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.content-input {
  width: 100%;
  min-height: 200rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 26rpx;
  line-height: 1.5;
  box-sizing: border-box;
}

.content-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.photo-section {
  margin-bottom: 20rpx;
}

.photo-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.photo-list {
  display: flex;
  flex-wrap: wrap;
}

.photo-item {
  position: relative;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.photo-preview {
  width: 150rpx;
  height: 150rpx;
  border-radius: 8rpx;
}

.photo-delete {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.photo-add {
  width: 150rpx;
  height: 150rpx;
  border: 2px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-icon {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}

.add-text {
  font-size: 24rpx;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  border-top: 1px solid #e0e0e0;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background: #007aff;
  color: white;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn.disabled {
  background: #ccc;
  pointer-events: none;
}
</style> 