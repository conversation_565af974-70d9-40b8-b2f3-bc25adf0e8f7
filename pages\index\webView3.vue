<template>
  <view>
    <!-- 添加消息显示区域 -->
    <view class="message-area" v-if="message">
      <text>收到消息: {{message}}</text>
    </view>
    
    <web-view :src="url" @message="handleMessage"></web-view>
  </view>
  </template>
  
  <script>
  var app = getApp();
  
  export default {
    data() {
      return {
        url: '',
        message: '' // 存储最新消息
      };
    },
  
    onLoad: function (opt) {
      this.url = decodeURIComponent(opt.url);
    },
    
    methods: {
      handleMessage(e) {
        console.log('接收到消息:', e);
        
        // 显示消息内容
        if (e.detail && e.detail.data && e.detail.data[0]) {
          // 保存并显示消息
          this.message = JSON.stringify(e.detail.data[0]);
          
          // 显示提示
          uni.showToast({
            title: '收到消息',
            icon: 'success'
          });
          
          // 如果是导航消息，打开地图
          const data = e.detail.data[0];
          if (data.type === 'openLocation') {
            uni.openLocation({
              latitude: parseFloat(data.latitude),
              longitude: parseFloat(data.longitude),
              name: data.name,
              address: data.address
            });
          }
        }
      }
    }
  };
  </script>
  
  <style>
  .message-area {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    z-index: 999;
    font-size: 12px;
  }
  </style>