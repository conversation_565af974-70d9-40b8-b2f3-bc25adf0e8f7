<template>
<view>
	<block v-if="isload">
		<view class="banner" :style="{background:'linear-gradient(180deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0) 100%)'}">
			<image :src="userinfo.headimg" background-size="cover"/>
			<view class="info" v-if="set && set.parent_show == 1">
				 <text class="nickname">{{userinfo.nickname}}</text>
				 <text class="nickname"  v-if="jiuxing_status == 1">{{t('我的星级')}}：{{userinfo.xingji}}星</text>
				 <text>{{t('推荐用户')}}：{{userinfo.pid > 0 ? userinfo.pnickname : '无'}}</text>
			</view>
			<view class="info" v-else style="line-height: 120rpx;padding-top: 0;">
				 <text class="nickname">{{userinfo.nickname}}</text>
				 <!--  -->
			</view>
		</view>
		<view class="contentdata">
			<view class="order">
				
				
				<view class="head">
					<text class="f1">我的{{t('佣金')}}</text>
					<view class="f2" @tap="goto" data-url="withdraw" v-if="comwithdraw==1"><text>立即提现</text><image src="/static/img/arrowright.png"></image></view>
					<view class="f2" @tap="tomoney" v-else-if="commission2money=='1'"><text>转到{{t('余额')}}账户</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.commission}}</text>
							<text class="t3">{{comwithdraw==1?'可提现':'剩余'}}{{t('佣金')}}</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{count3}}</text>
							<text class="t3">已提现{{t('佣金')}}</text>
					 </view>
					<view class="item" @tap="goto" data-url="moneylog2?st=14">
							<text class="t1">￥{{userinfo.commission_yj}}</text>
							<text class="t3">在路上</text>
					 </view>
				</view>
				
			</view>
			<view class="order">
				<view class="head" >
					<text class="f1">我的收益</text>
				</view>
				<view class="content"  @tap="goto" data-url="commissionlog" >
					<view class="item">
							<text class="t1">{{total_commission_earned}}</text>
							<text class="t3">总收益</text>
					 </view>
					 <view class="item">
							<text class="t1">{{current_month_commission}}</text>
							<text class="t3">当月收益</text>
					 </view>
				</view>
			
			</view>
			<view class="order" v-if="lunshustatus == 1 && xunizhanhaocount ==0">
				<view class="head" >
					<text class="f1">我的{{t('轮数')}}</text>
				</view>
				<view class="content"  @tap="goto" data-url="commissionlog" >
					<view class="item">
							<text class="t1">{{lunshu}}</text>
							<text class="t3">当前轮数</text>
					 </view>
					 <view class="item">
							<text class="t1">{{yongjin}}</text>
							<text class="t3">佣金</text>
					 </view>
				</view>
			
			</view>
		<!-- 	<view class="order">
			<view class="content">
				 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
						<text class="t1">￥{{userinfo.commissiontuanA}}</text>
						<text class="t3">A区业绩</text>
				 </view>
				 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
						<text class="t1">￥{{userinfo.commissiontuanB}}</text>
						<text class="t3">B区业绩</text>
				 </view>
				 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
						<text class="t1">￥{{userinfo.commissiontuanC}}</text>
						<text class="t3">C区业绩</text>
				 </view>
			</view>
			</view> -->
			<view class="order" v-if="lunshustatus == 1 && xunizhanhaocount !=0">
				<view class="head" >
					<text class="f1">我的{{t('轮数')}}</text>
				</view>
				<view class="content"  @tap="goto" data-url="commissionlog" >
					<view class="item">
							<text class="t1">{{xunizhanhao.count}}</text>
							<text class="t3">我的子账号</text>
					 </view>
					 <view class="item">
							<text class="t1">{{xunizhanhao.shouyi}}</text>
							<text class="t3">子账号收益</text>
					 </view>
					 <view class="item">
							<text class="t1">{{xunizhanhao.dai}}</text>
							<text class="t3">待收集收益</text>
					 </view>
				</view>
			
			</view>
			<view class="order" v-if="xsyj_shi == 1">
				<view class="head" >
					<text class="f1">我的{{t('分红点')}}</text>
				</view>
				<view class="content"  @tap="goto" data-url="commissionlog" >
					<view class="item">
							<text class="t1">{{xsyj.dianshu}}</text>
							<text class="t3">分红点</text>
					 </view>
					 <!-- <view class="item">
							<text class="t1">{{xsyj.daifenhong}}</text>
							<text class="t3">待分红金额</text>
					 </view> -->
					 <view class="item">
							<text class="t1">{{xsyj.yifenhong}}</text>
							<text class="t3">已分红金额</text>
					 </view>
				</view>
			<view class="content" >
				<view class="item" v-if="levelArr.zijifh == 1">
						<text class="t1">{{userinfo.zijifh}}</text>
						<text class="t3">待返分红池</text>
				 </view>
				 <view class="item" v-if="levelArr.fxfhc == 1">
						<text class="t1">{{userinfo.fxfhc}}</text>
						<text class="t3">分享收益池</text>
				 </view>
				 <view class="item" v-if="levelArr.syc == 1">
					<text class="t1">{{userinfo.syc}}</text>
					<text class="t3">收益池</text>
				 </view>
			</view>
			</view>
			
			
			<view class="order" v-if="piaodianstatus == 1">
				<view class="head" >
					<text class="f1">我的{{t('票点')}}</text>
				</view>
				<view class="content"  @tap="goto" data-url="commissionlog" >
					<view class="item">
							<text class="t1">{{piaodian.my}}</text>
							<text class="t3">我的票点</text>
					 </view>
					 <view class="item">
							<text class="t1">{{piaodian.curprice}}</text>
							<text class="t3">当前票点价格</text>
					 </view>
				</view>
				<view class="content"  @tap="goto" data-url="commissionlog" >
					<view class="item">
						<text class="t1">{{piaodian.zongsheng}}</text>
						<text class="t3">总剩余票点</text>
					</view>
					<view class="item">
						<text class="t1">{{piaodian.zongliang}}</text>
						<text class="t3">发行量</text>
					</view>
				</view>
			
			</view>
			
			
			
			<view class="order" v-if="jiuxing_status == 1">
				<view class="head" >
					<text class="f1">我的{{t('星级')}}</text>
				</view>
				<view class="content">
					<!-- xingjiArr -->
					 <view class="item"v-for="(item, index) in datalistxing" :key="index">
							<text class="t1">{{item}}用户</text>
							<text class="t3">{{index}}星</text>
					 </view>
				</view>
				<view class="content">
					<!-- xingjiArr -->
					 <view class="item"v-for="(item, index) in datalistxing2" :key="index">
							<text class="t1">{{item}}用户</text>
							<text class="t3">{{index}}星</text>
					 </view>
				</view>
				<view class="content">
					<!-- xingjiArr -->
					 <view class="item"v-for="(item, index) in datalistxing3" :key="index">
							<text class="t1">{{item}}用户</text>
							<text class="t3">{{index}}星</text>
					 </view>
				</view>
			</view>
			<view class="order"  v-if="jiuxing_status == 1">
				<view class="head" >
					<text class="f1">我的{{t('星级业绩统计')}}</text>
				</view>
				<view class="content">
					<!-- xingjiArr -->
					 <view class="item">
							<text class="t1">{{today_sunshi}}</text>
							<text class="t3">今日损失金额</text>
					 </view>
					 <view class="item">
						<text class="t1">{{total_sunshi}}</text>
						<text class="t3">总计损失金额</text>
					 </view>
					 <view class="item">
						<text class="t1">{{total_jiediancount}}</text>
						<text class="t3">接点团队业绩统计</text>
					 </view>
				</view>

			</view>
			<view class="order" v-if="yunkucun_status==1">
				<view class="head" >
					<text class="f1">我的{{t('云库存收益')}}</text>
				</view>
				<view class="content"  @tap="goto" data-url="commissionlog" >
					<view class="item">
							<text class="t1">{{today_yunshouyi}}</text>
							<text class="t3">今日收益</text>
					 </view>
					 <view class="item">
							<text class="t1">{{zong_yunshouyi}}</text>
							<text class="t3">总收益</text>
					 </view>
				</view>
			
			</view>
			
			<view class="order" v-if="yunkucun_status == 1">
				<view class="head" >
					<text class="f1">我的{{t('详细下级')}}</text>
				</view>
				<view class="content">
					 <view class="item"  v-for="(item, index) in datalistlevel" :key="index">
						 <text class="t1">{{item.membercount}}</text>
						 <text class="t3">{{item.levelname}}</text>
					 </view>
				</view>
			</view>
			<view class="order" v-if="userinfo.jfc_status == 1">
				<view class="head">
					<text class="f1">{{t('积分释放')}}</text>
				<view class="f2" @tap="goto"  data-url="/pagesExt/jifenchi/myjifenchi" v-if="comwithdraw==1"><text>查看明细</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					<!-- <view class="item">
						<text class="t1">￥{{userinfo.score}}</text>
						<text class="t3">目前总积分</text>
					</view> -->
					 <view class="item">
							<text class="t1">￥{{userinfo.scoreA}}</text>
							<text class="t3">待释放静态积分</text>
					 </view>
					 <!-- <view class="item">
					 							<text class="t1">￥{{userinfo.yicountA}}</text>
					 							<text class="t3">已释放静态积分</text>
					 </view> -->
					<view class="item">
							<text class="t1">￥{{userinfo.scoreB}}</text>
							<text class="t3">待释放动态积分</text>
					 </view> 
			 </view>
				<view class="content">
					 <view class="item">
							<text class="t1">￥{{userinfo.yicountA}}</text>
							<text class="t3">已释放静态积分</text>
					 </view>
					 <view class="item">
							<text class="t1">￥{{userinfo.yicountB}}</text>
							<text class="t3">已释放动态积分</text>
					 </view>
				</view> 
			</view>
			<!-- <view class="order">
						<view class="head">
							<text class="f1">签到分红数据</text>
							<view class="f2" @tap="goto" data-url="/pagesExt/sign/index"><text>去签到</text><image src="/static/img/arrowright.png"></image></view>
							
						</view>
						<view class="content">
							
							 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
									<text class="t1">￥{{userinfo.qd.z_nums}}</text>
									<text class="t3">签到总分红</text>
							 </view>
							
							<view class="item" @tap="goto" data-url="../order/shoporder?st=0">
														<text class="t1">{{userinfo.qd.fx_nums}}</text>
														<text class="t3">分红点</text>
							</view>
							
							<view class="item" @tap="goto" data-url="../order/shoporder?st=0">
														<text class="t1">￥{{userinfo.qd.w_nums}}</text>
														<text class="t3">未分红</text>
							</view>
						</view>
					</view> -->
		<!-- 	<view class="order">
				<view class="head">
					<text class="f1">我的数据</text>
					<view class="f2" @tap="goto" data-url="/pagesExt/sign/index"><text>去签到</text><image src="/static/img/arrowright.png"></image></view>
					
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.bustotal}}</text>
							<text class="t3">创业值</text>
					 </view>
					
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.heiscore}}</text>
							<text class="t3">购物积分</text>
					 </view>
				</view>
			</view> -->
			<!-- <view class="order">
				<view class="head">
					<text class="f1">V资产专栏</text>
					<view class="f2" @tap="goto" data-url="/pagesExt/sign/index" v-if="comwithdraw==1"><text>去签到</text><image src="/static/img/arrowright.png"></image></view>
					<view class="f2" @tap="tomoney" v-else-if="commission2money=='1'"><text>转到{{t('余额')}}账户</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.scorehuang}}</text>
							<text class="t3">总计V资产</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.scorehuangyuji}}</text>
							<text class="t3">今日预计释放到余额</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.commission_yj}}</text>
							<text class="t3">在路上的消费值</text>
					 </view>
				</view>
				
			</view> -->
		<!-- 	<view class="order">
				<view class="head">
					<text class="f1">我的{{t('消费值')}}</text>
					<view class="f2" @tap="goto" data-url="withdraw" v-if="comwithdraw==1"><text></text><image src="/static/img/arrowright.png"></image></view>
					<view class="f2" @tap="tomoney" v-else-if="commission2money=='1'"><text>转到{{t('余额')}}账户</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.scorexiaofeizhi}}</text>
							<text class="t3">我的消费值</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.scorelv}}</text>
							<text class="t3">我的{{t('绿积分')}}</text>
					 </view>
					<view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.commission_yj}}</text>
							<text class="t3">在路上的消费值</text>
					 </view>
				</view>
				<view class="content">
					<view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.fanlvjifen}}</text>
							<text class="t3">今日预计释放绿积分</text>
					 </view>
					<view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.scorelvjiazhi}}</text>
							<text class="t3">绿积分价值</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">28768</text>
							<text class="t3">每日数值</text>
					 </view>
				</view>
			</view> -->
			
	<!-- 		<view class="order">
				<view class="head">
					<text class="f1">数据统计{{t('')}}</text>
					<view class="f2" @tap="goto" data-url="withdraw" v-if="comwithdraw==1"><text></text><image src="/static/img/arrowright.png"></image></view>
					<view class="f2" @tap="tomoney" v-else-if="commission2money=='1'"><text>转到{{t('余额')}}账户</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">{{userinfo.commissionzongliang}}</text>
							<text class="t3">{{comwithdraw==1?'绿积分':''}}{{t('总量池')}}</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">{{userinfo.scorelvshifang22}}</text>
							<text class="t3">绿积分释放池</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">{{userinfo.scorelvshifangshengyu22}}</text>
							<text class="t3">绿积分剩余释放池</text>
					 </view>
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">{{userinfo.scorexiaofeizhicountlvzuo}}</text>
							<text class="t3">{{comwithdraw==1?'昨日绿积分燃烧池':''}}{{t('')}}</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">{{userinfo.scorexiaofeizhicountlvjin}}</text>
							<text class="t3">{{t('今日绿积分燃烧池')}}</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">{{userinfo.scorelvranshao}}</text>
							<text class="t3">绿积分总燃烧池</text>
					 </view>
				</view>
			</view> -->
		<!-- 	<view class="order">
				<view class="head">
					<text class="f1">我的团队业绩</text>
					<view class="f2" @tap="goto" data-url="/pages/index/main?id=52" v-if="comwithdraw==1"><text>规则说明</text><image src="/static/img/arrowright.png"></image></view>
					
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.commissiontuanA}}</text>
							<text class="t3">A区业绩</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.commissiontuanB}}</text>
							<text class="t3">B区业绩</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.commissiontuanC}}</text>
							<text class="t3">C区业绩</text>
					 </view>
				</view>
			</view> -->
			<!-- <view class="order">
				<view class="head">
					<text class="f1">平级分红</text>
					<view class="f2" @tap="goto" data-url="withdraw" v-if="comwithdraw==1"><text>规则说明</text><image src="/static/img/arrowright.png"></image></view>
					
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.pingtaigoodsfenh}}</text>
							<text class="t3">平台已分红</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.mygoodsfenh}}</text>
							<text class="t3">今日待分红</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
						<text class="t1">50倍数值</text>
						<text class="t3">剩余分红</text>
					 </view>
				</view>
			</view> -->
			
			<!-- <view class="order">
				<view class="head">
					<text class="f1">我的奖金</text>
					<view class="f2" @tap="goto" data-url="/pages/index/main?id=53" v-if="comwithdraw==1"><text>规则说明</text><image src="/static/img/arrowright.png"></image></view>
					
				</view>
				<view class="content">
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.countjiandian}}</text>
							<text class="t3">代理奖励</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
							<text class="t1">￥{{userinfo.countprofenh}}</text>
							<text class="t3">平级分红</text>
					 </view>
					 <view class="item" @tap="goto" data-url="../order/shoporder?st=0">
						<text class="t1">暂无</text>
						<text class="t3">我的福利</text>
					 </view>
				</view>
			</view> -->
			<!-- <view class="order" >
				<view class="head">
					<text class="f1">{{t('见点奖')}}</text>
					<view class="f2" @tap="goto" data-url="fenhong"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item">
							<text class="t1">￥{{userinfo.countjiandian}}</text>
							<text class="t3">累计获得</text>
					 </view>
					 <view class="item">
							<text class="t1">￥{{userinfo.fenhong_yj}}</text>
							<text class="t3">在路上</text>
					 </view>
					
					 <view class="item"></view>
				</view>
			</view>
			<view class="order" >
				<view class="head">
					<text class="f1">商品{{t('分红奖励')}}</text>
					<view class="f2" @tap="goto" data-url="fenhong"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item">
							<text class="t1">￥{{userinfo.countprofenh}}</text>
							<text class="t3">累计获得</text>
					 </view>
					 <view class="item">
							<text class="t1">￥{{userinfo.fenhong_yj}}</text>
							<text class="t3">在路上</text>
					 </view>
					
					 <view class="item"></view>
				</view>
			</view> -->
			
			<view class="order" v-if="hasfenhong">
				<view class="head">
					<text class="f1">{{t('股东分红')}}</text>
					<view class="f2" @tap="goto" data-url="fenhong"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item">
							<text class="t1">￥{{userinfo.fenhong}}</text>
							<text class="t3">累计获得</text>
					 </view>
					 <view class="item">
							<text class="t1">￥{{userinfo.fenhong_yj}}</text>
							<text class="t3">在路上</text>
					 </view>
					 <view class="item"></view>
				</view>
			</view>
			<view class="order" v-if="hasteamfenhong && set.teamfenhong_show">
				<view class="head">
					<text class="f1">{{t('团队分红')}}</text>
					<view class="f2" @tap="goto" data-url="teamfenhong"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item">
							<text class="t1">￥{{userinfo.teamfenhong}}</text>
							<text class="t3">累计获得</text>
					 </view>
					 <view class="item">
							<text class="t1">￥{{userinfo.teamfenhong_yj}}</text>
							<text class="t3">在路上</text>
					 </view>
					 <view class="item"></view>
				</view>
			</view>
			<view class="order" v-if="jiuxing_status == 1">
				<view class="head">
					<text class="f1">{{t('接点团队信息')}}</text>
					<view class="f2" @tap="goto" data-url="teamfenhong"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item">
							<text class="t1">￥{{userinfo.jiedian_teamfenhong}}</text>
							<text class="t3">分红累计获得</text>
					 </view>
					 <view class="item">
							<text class="t1">{{userinfo.jiedian_count}}</text>
							<text class="t3">团队分红人数</text>
					 </view>
					 <view class="item"></view>
				</view>
			</view>
			<view class="order" v-if="hasareafenhong">
				<view class="head">
					<text class="f1">{{t('区域代理分红')}}</text>
					<view class="f2" @tap="goto" data-url="areafenhong"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item">
							<text class="t1">￥{{userinfo.areafenhong}}</text>
							<text class="t3">累计获得</text>
					 </view>
					 <view class="item">
							<text class="t1">￥{{userinfo.areafenhong_yj}}</text>
							<text class="t3">在路上</text>
					 </view>
					 <view class="item"></view>
				</view>
			</view>
		
			<view class="order" v-if="hasteamfenhong && (teamnum_show==1 || teamyeji_show==1)">
				<view class="head">
					<text class="f1">{{t('我的团队')}}</text>
					<view class="f2" @tap="goto" data-url="myteam"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view>
				</view>
				<view class="content">
					 <view class="item" v-if="teamnum_show==1">
							<text class="t1">{{userinfo.teamnum}}</text>
							<text class="t3">市场总用户数</text>
					 </view>
					 <view class="item">
					 		<block v-if="teamyeji_show==1">
							<text class="t1">￥{{userinfo.teamyeji}}</text>
							<text class="t3">市场总业绩</text>
							</block>
					 </view>
					 <view class="item">
							<block v-if="gongxianfenhong_show==1">
							<text class="t1">￥{{userinfo.gongxianfenhong}}</text>
							<text class="t3">预计{{userinfo.gongxianfenhong_txt || '股东贡献量分红'}}</text>
							</block>
					 </view>
				</view>
			</view>
			
		<!-- 	
			<view class="order" v-if="userinfo.yeji">
				<view class="head">
					<text class="f1">{{t('升级业绩')}}</text>
				</view>
				<view class="content" style="margin-bottom: 10px;">
					<view class="item">
							<text class="t1">{{userinfo.yeji.my}}</text>
							<text class="t3">我的业绩</text>
					</view>
					<view class="item">
							<text class="t1">{{userinfo.yeji.z}}</text>
							<text class="t3">市场总业绩</text>
					</view>
				</view>
				<view style="border: 1px solid red;"></view>
				<view class="content" style="margin-top: 10px;">
					<view class="item">
							<text class="t1">{{userinfo.yeji.dq}}</text>
							<text class="t3">大市场业绩</text>
					</view>
					<view class="item">
							<text class="t1">{{userinfo.yeji.xq}}</text>
							<text class="t3">小市场业绩</text>
					</view>
					<view class="item">
							<text class="t1">{{userinfo.yeji.sj}}</text>
							<text class="t3">市场考核业绩</text>
					</view>
				</view>
			</view> -->
			<!-- 新增销售统计数据块 -->
<view class="order" v-if="ranking_info==true">
    <view class="head">
        <text class="f1">{{t('阶梯奖励销售统计')}}</text>
    </view>
    <view class="content">
      
        <view class="item">
            <text class="t1">￥{{salesStats.totalSalesAmount}}</text>
            <text class="t3">累计销售额</text>
        </view>
        <view class="item">
            <text class="t1">{{salesStats.totalSalesCount}}</text>
            <text class="t3">累计销售数量</text>
        </view>
    </view>
    <view class="content">
		<view class="item">
		    <text class="t1">{{salesStats.last30DaysSales}}</text>
		    <text class="t3">近30天销售数量</text>
		</view>
        <view class="item">
            <text class="t1">{{salesStats.last30DaysNewMembers}}</text>
            <text class="t3">近30天团队新增</text>
        </view>
    </view>
</view>
			
			<view class="order" v-if="yihuo.yihuo_status == 1">
							<view class="head">
								<text class="f1">{{t('我的商家')}}</text>
							</view>
							<view class="content">
								<view class="item">
									<text class="t1">{{yihuo.businesscount}}</text>
									<text class="t3">已绑定商家数量</text>
								</view>
								 <view class="item">
										<text class="t1">￥{{yihuo.zongliushui}}</text>
										<text class="t3">商家总流水</text>
								 </view>
								 <view class="item">
										<text class="t1">￥{{yihuo.jinday}}</text>
										<text class="t3">今日流水</text>
								 </view>
								 
							</view>
							<view class="content">
								<view class="item">
									<text class="t1">￥{{yihuo.zuoday}}</text>
									<text class="t3">昨日流水</text>
								</view>
								 <view class="item">
										<text class="t1">￥{{yihuo.jinmonth}}</text>
										<text class="t3">本月流水</text>
								 </view>
								 <view class="item">
										<text class="t1">￥{{yihuo.chouyong}}</text>
										<text class="t3">抽佣总和</text>
								 </view>
							</view>
						</view>
			
			
			

			<view class="list">
			<!-- 	<view class="item" @tap="toexchange" v-if="comwithdraw==1 && commission2money=='1'">
					<view class="f2">{{'余额交易'}}</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view> -->
				<view class="item" @tap="goto"  v-if="tuozhancrm.tuozhancrm_status == 1"  data-url="/pagesExt/tuozhanyuan/tuozhancrm" >
					<view class="f2">拓展员CRM</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
			<!-- <view class="item"  v-if="jiuxing_status == 1 " @tap="goto" data-url="/pagesExa/kaizhanghao/zhuzhanghao">
				<view class="f2">{{t('开账号')}}</view>
				<text class="f3"></text>
				<image src="/static/img/arrowright.png" class="f4"></image>
			</view> -->
				<view class="item" @tap="tomoney" v-if="comwithdraw==1 && commission2money=='1'">
					<view class="f2" >{{t('佣金')}}转{{t('余额')}}</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				  <!-- 添加团队排行榜入口 -->
				            <block v-if="ranking_info==true">
				                <view class="item"@tap="goto" data-url="/pagesExt/ranking/list">
				                   <view class="f2">{{t('团队风云榜')}}<text class="t1" style="margin-left:400rpx;"></text></view>
				                   <text class="f3"></text>
				                   <image src="/static/img/arrowright.png" class="f4"></image>
				                </view>
								<view class="item"@tap="goto" data-url="/pagesExt/ranking/listjituan">
								   <view class="f2">{{t('集团风云榜')}}<text class="t1" style="margin-left:400rpx;"></text></view>
								   <text class="f3"></text>
								   <image src="/static/img/arrowright.png" class="f4"></image>
								</view>
				            </block>
				<view class="item" @tap="goto" data-url="myteam">
					<view class="f2">{{t('我的团队')}}<text class="t1" style="margin-left:400rpx;"></text></view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="myyukucun" v-if="yunkucun_status==1">
					<view class="f2">{{t('云库存')}}</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				
				<view class="item" @tap="goto" data-url="downorder">
					<view class="f2">{{t('分销订单')}}</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="commissionlog" v-if="set.commissionlog_show">
					<view class="f2">{{t('佣金')}}明细</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="ordergoodsinfo">
					<view class="f2">{{t('订单')}}统计</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<!-- <view class="item" @tap="goto" data-url="commissionloghuang" v-if="set.commissionlog_show">
					<view class="f2">V资产明细</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="commissionloglv" v-if="set.commissionlog_show">
					<view class="f2">绿积分明细</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="commissionlogxiaofeizhi" v-if="set.commissionrecord_show">
					<view class="f2">消费值记录</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view> -->
				<view class="item" @tap="goto" data-url="poster">
					<view class="f2">分享海报</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="fhorder" v-if="showfenhong && set.fhorder_show">
					<view class="f2">分红订单</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="fhlog" v-if="showfenhong && set.fhlog_show">
					<view class="f2">分红记录</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="orderMendian" v-if="showMendianOrder">
					<view class="f2">服务订单</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="commissionlogMendian" v-if="showMendianOrder">
					<view class="f2">服务佣金</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="orderYeji" v-if="showYeji">
					<view class="f2">业绩统计</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
				<view class="item" @tap="goto" data-url="/pagesExt/agent/cardEdit" v-if="set && set.agent_card == 1">
					<view class="f2">代理卡片</view>
					<text class="f3"></text>
					<image src="/static/img/arrowright.png" class="f4"></image>
				</view>
			</view>
		</view>
		<view style="width:100%;height:20rpx"></view>
		
		<uni-popup id="dialogInput" ref="dialogInput" type="dialog">
			<uni-popup-dialog mode="input" :title="t('佣金') + '转' + t('余额')" value="" placeholder="请输入转入金额" @confirm="tomonenyconfirm"></uni-popup-dialog>
		</uni-popup>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			datalistlevel:{},
			yihuo:{},
			xsyj:{},
			piaodianstatus:0,
			piaodian:{},
			today_sunshi:0,
			total_sunshi:0,
			total_jiediancount:0,
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			ranking_info: false,
      hiddenmodalput: true,
      salesStats: {
        last30DaysSales: 0,
        totalSalesAmount: 0,
        totalSalesCount: 0,
        last30DaysNewMembers: 0
      },
      userinfo: [],
	  datalistxing:[],
	  datalistxing2:[],
	  datalistxing3:[],
	  jiuxing_status:0,
	  lunshustatus:0,
	  xsyj_shi:0,
	  lunshu:0,
	  yongjin:0,
      count: 0,
      count1: 0,
      count2: 0,
      count3: 0,
      count4: 0,
      comwithdraw: 0,
	  zong_yunshouyi:0,
	  today_yunshouyi:0,
	  yunkucun_status:0,
      canwithdraw: true,
      money: 0,
      count0: "",
      countdqr: "",
      commission2money: "",
			showfenhong:false,
			showMendianOrder:false,
			hasfenhong:false,
			hasareafenhong:false,
			hasteamfenhong:false,
			showYeji:false,
			fxjiesuantime:0,
			teamyeji_show:0,
			teamnum_show:0,
			gongxianfenhong_show:0,
			set:{},
			xunizhanhaocount:0,
			xunizhanhao:{},
			levelArr:{},
			total_commission_earned:0,
			current_month_commission:0,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		var that = this;
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			// 获取佣金概览数据
			app.get('ApiAgent/commissionSurvey', {}, function (res) {
				that.loading = false;
				uni.setNavigationBarTitle({
					title: '我的' + that.t('佣金')
				});
				that.yihuo = res.yihuo;
				that.tuozhancrm = res.tuozhancrm;
				that.userinfo = res.userinfo;
				that.datalistxing =res.xingjiArr;
				that.datalistxing2 =res.xingjiArr2;
				that.datalistxing3 =res.xingjiArr3;
				that.datalistlevel = res.datalistlevel;
				that.xsyj = res.xsyj;
				that.piaodianstatus = res.piaodianstatus;
				that.piaodian = res.piaodian;
				that.yongjin = res.yongjin;
				that.xunizhanhao = res.xunizhanhao;
				that.xunizhanhaocount = res.xunizhanhaocount;
				that.jiuxing_status = res.jiuxing_status
				that.lunshustatus = res.lunshustatus;
				that.xsyj_shi = res.xsyj_shi;
				that.lunshu = res.lunshu;
				that.today_sunshi = res.today_sunshi;
				that.total_sunshi = res.total_sunshi;
				that.total_jiediancount = res.total_jiediancount
				that.set = res.set;
				that.count = res.count;
				that.count1 = res.count1;
				that.count2 = res.count2;
				that.count3 = res.count3;
				that.count0 = res.count0;
				that.today_yunshouyi = res.today_yunshouyi;
				that.zong_yunshouyi = res.zong_yunshouyi;
				that.levelArr  = res.levelArr;
				that.countdqr = res.countdqr;
				that.comwithdraw = res.comwithdraw;
				that.yunkucun_status = res.yunkucun_status;
				that.commission2money = res.commission2money;
				that.showfenhong = res.showfenhong;
				that.showMendianOrder = res.showMendianOrder;
				that.hasfenhong = res.hasfenhong;
				that.hasareafenhong = res.hasareafenhong;
				that.hasteamfenhong = res.hasteamfenhong;
				that.showYeji = res.hasYeji;
				that.fxjiesuantime = res.fxjiesuantime;
				that.teamyeji_show = res.teamyeji_show;
				that.teamnum_show = res.teamnum_show;
				that.gongxianfenhong_show = res.gongxianfenhong_show;
				that.ranking_info = res.ranking_info;
				that.total_commission_earned = res.total_commission_earned;
				that.current_month_commission = res.current_month_commission;
				that.loaded();
			});
			
			// 获取销售统计数据
			app.get('ApiAgent/salesStatistics', {}, function (res) {
				console.log('销售统计数据:', res);
				that.salesStats = {
					last30DaysNewMembers: Number(res.last30DaysNewMembers || 0),
					last30DaysSales: Number(res.last30DaysSales || 0),
					totalSalesAmount: Number(res.totalSalesAmount || 0).toFixed(2),
					totalSalesCount: Number(res.totalSalesCount || 0)
				};
			});
		},
    cancel: function () {
      this.hiddenmodalput = true;
    },
    tomoney: function () {
      this.$refs.dialogInput.open()
    },
    toexchange:function () {
		app.goto('/pages/money/zhuanzhang');
    },
    tomonenyconfirm: function (done, val) {
			console.log(val)
      var that = this;
      var money = val;
      if (money == '' || parseFloat(money) <= 0) {
        app.alert('请输入转入金额');
        return;
      }
      if (parseFloat(money) > this.userinfo.commission) {
        app.alert('可转入' + that.t('佣金') + '不足');
        return;
      }
			done();
			app.showLoading('提交中');
      app.post('ApiAgent/commission2money', {money: money}, function (data) {
				app.showLoading(false);
        if (data.status == 0) {
          app.error(data.msg);
        } else {
          that.hiddenmodalput = true;
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        }
      });
    }
  }
};
</script>
<style>
.banner{ display:flex;width:100%;height:560rpx;padding:40rpx 32rpx;color:#fff;position:relative}
.banner image{ width:120rpx;height:120rpx;border-radius:50%;margin-right:20rpx}
.banner .info{display:flex;flex:auto;flex-direction:column;padding-top:10rpx}
.banner .info .nickname{font-size:32rpx;font-weight:bold;padding-bottom:12rpx}
.banner .set{ width:70rpx;height:100rpx;line-height:100rpx;font-size:40rpx;text-align:center}
.banner .set image{width:50rpx;height:50rpx;border-radius:0}

.contentdata{display:flex;flex-direction:column;width:100%;padding:0 30rpx;margin-top:-380rpx;position:relative;margin-bottom:20rpx}

.order{width:100%;background:#fff;padding:0 20rpx;margin-top:20rpx;border-radius:16rpx}
.order .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:0px solid #eee}
.order .head .f1{flex:auto;color:#333}
.order .head .f2{ display:flex;align-items:center;color:#FE2B2E;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}
.order .head .f2 image{ width:30rpx;height:30rpx;}
.order .head .t3{ width: 40rpx; height: 40rpx;}
.order .content{ display:flex;width:100%;padding:10rpx 0;align-items:center;font-size:24rpx}
.order .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}
.order .content .item image{ width:50rpx;height:50rpx}
.order .content .item .t1{color:#FE2B2E;font-size:36rpx;font-weight:bold;}
.order .content .item .t3{ padding-top:3px;color:#666}
.order .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}

.list{ width: 100%;background: #fff;margin-top:20rpx;padding:0 20rpx;font-size:30rpx;border-radius:16rpx}
.list .item{ height:100rpx;display:flex;align-items:center;border-bottom:0px solid #eee}
.list .item:last-child{border-bottom:0;}
.list .f1{width:50rpx;height:50rpx;line-height:50rpx;display:flex;align-items:center;}
.list .f1 image{ width:40rpx;height:40rpx;}
.list .f1 span{ width:40rpx;height:40rpx;font-size:40rpx}
.list .f2{color:#222}
.list .f3{ color: #FC5648;text-align:right;flex:1;}
.list .f4{ width: 24rpx; height: 24rpx;}
</style>
