<template>
<view class="dp-product" :style="{
	backgroundColor:params.bgcolor,
	margin:params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0',
	padding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx',
	width:'calc(100% - '+params.margin_x*2.2*2+'rpx)'
}">
	<!--123排-->
	<dp-product-item v-if="params.style=='1' || params.style=='2' || params.style=='3'" 
		:key="'product-item-'+renderKey"
		:showstyle="params.style" 
		:data="displayData" 
		:saleimg="params.saleimg" 
		:showname="params.showname" 
		:showprice="params.showprice" 
		:showsales="params.showsales" 
		:showcart="params.showcart" 
		:cartimg="params.cartimg" 
		idfield="proid" 
		:menuindex="menuindex" 
		:probgcolor="params.probgcolor" 
		:params="{
			bgimg: params.bgimg,
			main_title: params.main_title,
			main_title_color: params.main_title_color,
			sub_title: params.sub_title,
			sub_title_color: params.sub_title_color,
			more_text: params.more_text,
			more_text_color: params.more_text_color,
			hrefurl: params.hrefurl
		}">
	</dp-product-item>
	<!--横排-->
	<dp-product-itemlist v-if="params.style=='list'" 
		:key="'product-itemlist-'+renderKey"
		:data="displayData" 
		:saleimg="params.saleimg" 
		:showname="params.showname" 
		:showprice="params.showprice" 
		:showsales="params.showsales" 
		:showcart="params.showcart" 
		:cartimg="params.cartimg" 
		idfield="proid" 
		:menuindex="menuindex" 
		:probgcolor="params.probgcolor">
	</dp-product-itemlist>
	<!--左右滑动-->
	<dp-product-itemline v-if="params.style=='line'" 
		:key="'product-itemline-'+renderKey"
		:data="displayData" 
		:saleimg="params.saleimg" 
		:showname="params.showname" 
		:showprice="params.showprice" 
		:showsales="params.showsales" 
		:showcart="params.showcart" 
		:cartimg="params.cartimg" 
		idfield="proid" 
		:menuindex="menuindex" 
		:probgcolor="params.probgcolor" 
		:params="{
			bgimg: params.bgimg,
			main_title: params.main_title,
			main_title_color: params.main_title_color,
			sub_title: params.sub_title,
			sub_title_color: params.sub_title_color,
			more_text: params.more_text,
			more_text_color: params.more_text_color,
			hrefurl: params.hrefurl
		}">
	</dp-product-itemline>
	<!--瀑布流-->
	<dp-product-waterfall v-if="params.style=='waterfall'" 
		:key="'product-waterfall-'+renderKey"
		:list="displayData" 
		:saleimg="params.saleimg" 
		:showname="params.showname" 
		:showprice="params.showprice" 
		:showsales="params.showsales" 
		:showcart="params.showcart" 
		:showstock="params.showstock" 
		:showbname="params.showbname" 
		:showcoupon="params.showcoupon" 
		:cartimg="params.cartimg" 
		idfield="proid" 
		:menuindex="menuindex" 
		:probgcolor="params.probgcolor" 
		@image-load="onWaterfallLoad">
	</dp-product-waterfall>
	
	<!--混合模式：双排-->
	<dp-product-mixed-row v-if="params.style=='mixed-row'" 
		:key="'product-mixed-row-'+renderKey"
		:list="displayData" 
		:saleimg="params.saleimg" 
		:showname="params.showname" 
		:showprice="params.showprice" 
		:showsales="params.showsales" 
		:showcart="params.showcart" 
		:showstock="params.showstock" 
		:showbname="params.showbname" 
		:showcoupon="params.showcoupon" 
		:cartimg="params.cartimg" 
		:idfield="'proid'" 
		:menuindex="menuindex" 
		:probgcolor="params.probgcolor"
		:params="{
			bgimg: params.bgimg,
			main_title: params.main_title,
			main_title_color: params.main_title_color,
			sub_title: params.sub_title,
			sub_title_color: params.sub_title_color,
			more_text: params.more_text,
			more_text_color: params.more_text_color,
			hrefurl: params.hrefurl
		}"
		@image-load="onMixedLoad">
	</dp-product-mixed-row>
	
	<!--混合模式：瀑布流-->
	<dp-product-mixed-waterfall v-if="params.style=='mixed-waterfall'" 
		:key="'product-mixed-waterfall-'+renderKey"
		:list="displayData" 
		:saleimg="params.saleimg" 
		:showname="params.showname" 
		:showprice="params.showprice" 
		:showsales="params.showsales" 
		:showcart="params.showcart" 
		:showstock="params.showstock" 
		:showbname="params.showbname" 
		:showcoupon="params.showcoupon" 
		:cartimg="params.cartimg" 
		:idfield="'proid'" 
		:menuindex="menuindex" 
		:probgcolor="params.probgcolor"
		@image-load="onWaterfallLoad">
	</dp-product-mixed-waterfall>
	
	<!-- 底部加载状态区域 -->
	<view class="load-more-area" ref="loadMoreArea" id="loadMoreArea" v-if="data && data.length >= pageSize">
		<view v-if="loading" class="loading">
			<text>加载中...</text>
		</view>
		<view v-else-if="hasMoreData" class="can-load-more">
			<text>上拉加载更多</text>
		</view>
		<view v-else class="no-more">
			<text>没有更多数据了</text>
		</view>
	</view>
</view>
</template>
<script>
	export default {
		props: {
			menuindex:{default:-1},
			params:{},
			data:{},
			// 新增混合模式相关参数
			mixedArticles: {
				type: Array,
				default: () => []
			},
			mixedVideos: {
				type: Array,
				default: () => []
			},
			mixedMode: {
				type: String,
				default: 'random' // 'random' 或 'fixed'
			},
			mixedFrequency: {
				type: String,
				default: 'medium' // 'low', 'medium', 'high'
			},
			mixedPositions: {
				type: String,
				default: '3,6,9' // 固定位置时的位置
			}
		},
		data() {
			return {
				pageSize: 10, // 每页显示数量
				currentPage: 1, // 当前页码
				loading: false, // 是否正在加载
				observer: null, // 观察者对象
				waterfallLoaded: false, // 瀑布流加载状态
				mixedLoaded: false, // 混合模式加载状态
				mixedData: [], // 混合后的数据
				renderKey: 0, // 用于强制组件刷新的key
				scrollTop: 0, // 保存滚动位置
				// 新增小程序兼容性相关数据
				isMiniprogramEnv: false, // 是否是小程序环境
				lastScrollTop: 0, // 记录上次滚动位置
				scrollTimer: null, // 滚动防抖定时器
			}
		},
		computed: {
			// 计算当前应该显示的数据
			displayData() {
				if(!this.data || !this.data.length) return [];
				
				// 如果是混合模式，返回混合后的数据
				if(this.isMixedMode) {
					return this.getMixedData().slice(0, this.currentPage * this.pageSize);
				}
				
				// 普通模式返回商品数据
				return this.data.slice(0, this.currentPage * this.pageSize);
			},
			// 判断是否还有更多数据
			hasMoreData() {
				if(this.isMixedMode) {
					return this.getMixedData().length > this.currentPage * this.pageSize;
				}
				return this.data && this.data.length > this.currentPage * this.pageSize;
			},
			// 是否是瀑布流视图
			isWaterfallView() {
				return this.params && (this.params.style === 'waterfall' || this.params.style === 'mixed-waterfall');
			},
			// 是否是混合模式
			isMixedMode() {
				return this.params && (this.params.style === 'mixed-row' || this.params.style === 'mixed-waterfall');
			}
		},
		methods: {
			// 瀑布流图片加载完成事件
			onWaterfallLoad() {
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product][onWaterfallLoad_001] 瀑布流图片加载完成');
				this.waterfallLoaded = true;
			},
			
			// 混合模式加载完成事件
			onMixedLoad() {
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product][onMixedLoad_001] 混合模式图片加载完成');
				this.mixedLoaded = true;
			},
			
			// 获取混合后的数据
			getMixedData() {
				// 如果已经有混合数据，直接返回
				if(this.mixedData.length > 0) {
					return this.mixedData;
				}
				
				// 没有商品数据时返回空数组
				if(!this.data || this.data.length === 0) {
					return [];
				}
				
				// 复制商品数据
				const productData = [...this.data];
				const result = [...productData];
				
				// 没有混合内容时直接返回商品数据
				if((!this.mixedArticles || this.mixedArticles.length === 0) && 
				   (!this.mixedVideos || this.mixedVideos.length === 0)) {
					return result;
				}
				
				// 准备所有要混合的内容
				const mixedContents = [
					...(this.mixedArticles || []).map(item => ({
						...item,
						content_type: 'article'
					})),
					...(this.mixedVideos || []).map(item => ({
						...item,
						content_type: 'video'
					}))
				];
				
				// 如果没有混合内容，返回原始商品数据
				if(mixedContents.length === 0) {
					return result;
				}
				
				// 根据混合模式处理数据
				if(this.mixedMode === 'random') {
					// 随机混合模式
					let frequency = 5; // 默认每5个商品插入一个
					
					if(this.mixedFrequency === 'low') {
						frequency = 7; // 低频率：每7个商品插入一个
					} else if(this.mixedFrequency === 'high') {
						frequency = 3; // 高频率：每3个商品插入一个
					}
					
					// 随机排序混合内容
					const shuffledContents = this.shuffleArray([...mixedContents]);
					let mixedIndex = 0;
					
					// 按频率插入混合内容
					for(let i = frequency; i < result.length && mixedIndex < shuffledContents.length; i += frequency + 1) {
						result.splice(i, 0, shuffledContents[mixedIndex]);
						mixedIndex++;
					}
					
				} else if(this.mixedMode === 'fixed') {
					// 固定位置混合模式
					const positions = this.mixedPositions.split(',').map(pos => parseInt(pos.trim()) - 1);
					const validPositions = positions.filter(pos => pos >= 0 && pos < result.length);
					
					// 循环插入混合内容
					for(let i = 0; i < validPositions.length && i < mixedContents.length; i++) {
						const pos = validPositions[i];
						result.splice(pos, 0, mixedContents[i % mixedContents.length]);
					}
				}
				
				// 保存混合结果
				this.mixedData = result;
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product][getMixedData_001] 混合数据处理完成，共', this.mixedData.length, '条');
				
				return this.mixedData;
			},
			
			// 数组随机排序
			shuffleArray(array) {
				for (let i = array.length - 1; i > 0; i--) {
					const j = Math.floor(Math.random() * (i + 1));
					[array[i], array[j]] = [array[j], array[i]];
				}
				return array;
			},
			
			// 加载更多数据
			loadMore() {
				if(this.hasMoreData && !this.loading) {
					this.loading = true;
					console.log('2025-06-10 20:55:53,565-INFO-[dp-product][loadMore_001] 自动加载更多数据，当前页码：', this.currentPage);
					
					// 保存当前滚动位置
					this.saveScrollPosition();
					
					// 如果是瀑布流，重置瀑布流加载状态
					if(this.isWaterfallView) {
						this.waterfallLoaded = false;
					}
					
					// 如果是混合模式，重置混合加载状态
					if(this.isMixedMode) {
						this.mixedLoaded = false;
					}
					
					// 模拟加载过程，实际使用时可以删除setTimeout
					setTimeout(() => {
						this.currentPage++;
						this.loading = false;
						this.renderKey++; // 递增renderKey以强制视图更新
						
						// 瀑布流组件需要时间来重新计算布局
						if(this.isWaterfallView) {
							console.log('2025-06-10 20:55:53,565-INFO-[dp-product][loadMore_002] 瀑布流数据已更新');
						}
						
						// 混合模式需要时间重新渲染
						if(this.isMixedMode) {
							console.log('2025-06-10 20:55:53,565-INFO-[dp-product][loadMore_003] 混合模式数据已更新');
						}
						
						// 允许布局重新计算完成后恢复滚动位置
						this.$nextTick(() => {
							setTimeout(() => {
								this.restoreScrollPosition();
							}, 100);
						});
					}, 500);
				}
			},
			// 保存当前滚动位置
			saveScrollPosition() {
				try {
					// 获取当前页面滚动位置
					const pages = getCurrentPages();
					const page = pages[pages.length - 1];
					uni.createSelectorQuery().in(page).selectViewport().scrollOffset(res => {
						this.scrollTop = res.scrollTop;
						console.log('2025-06-10 20:55:53,565-INFO-[dp-product][saveScrollPosition_001] 保存滚动位置:', this.scrollTop);
					}).exec();
				} catch (e) {
					console.error('2025-06-10 20:55:53,565-ERROR-[dp-product][saveScrollPosition_002] 保存滚动位置失败:', e);
				}
			},
			// 恢复滚动位置
			restoreScrollPosition() {
				if(this.scrollTop > 0) {
					try {
						uni.pageScrollTo({
							scrollTop: this.scrollTop,
							duration: 0
						});
						console.log('2025-06-10 20:55:53,565-INFO-[dp-product][restoreScrollPosition_001] 恢复滚动位置:', this.scrollTop);
					} catch (e) {
						console.error('2025-06-10 20:55:53,565-ERROR-[dp-product][restoreScrollPosition_002] 恢复滚动位置失败:', e);
					}
				}
			},
			// 重置分页数据
			resetPagination() {
				this.currentPage = 1;
				this.loading = false;
				this.waterfallLoaded = false;
				this.mixedLoaded = false;
				this.mixedData = []; // 清空混合数据缓存
				this.renderKey = 0; // 重置渲染key
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product][resetPagination_001] 重置分页数据和渲染key');
			},
			// 初始化观察者
			initObserver() {
				// 检测是否是小程序环境
				// #ifdef MP
				this.isMiniprogramEnv = true;
				// #endif
				
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product][initObserver_001] 初始化观察者，小程序环境：', this.isMiniprogramEnv);
				
				if(this.observer) {
					this.observer.disconnect();
				}
				
				try {
					// 创建观察者对象
					this.observer = uni.createIntersectionObserver(this, {
						// 小程序需要设置阈值数组
						thresholds: [0, 0.1, 0.25, 0.5, 0.75, 1],
						// 设置初始比率，当交叉比率大于这个值时会触发
						initialRatio: 0.1,
						// 是否同时观测多个参照节点（而非只有一个）
						observeAll: false
					});
					
					// 小程序环境下使用视窗作为参照，其他环境使用容器
					if(this.isMiniprogramEnv) {
						// 小程序使用相对于视窗的观察
						this.observer
							.relativeToViewport({
								bottom: 100 // 距离视窗底部100px时就触发
							})
							.observe('#loadMoreArea', (res) => {
								console.log('2025-06-10 20:55:53,565-INFO-[dp-product][observe_MP_001] 小程序观察结果：', res);
								this.handleObserverResult(res);
							});
					} else {
						// H5等其他环境使用原有的方式
						this.observer
							.relativeTo('.dp-product', {
								bottom: 50 // 距离容器底部50px时触发
							})
							.observe('#loadMoreArea', (res) => {
								console.log('2025-06-10 20:55:53,565-INFO-[dp-product][observe_H5_001] H5观察结果：', res);
								this.handleObserverResult(res);
							});
					}
					
					// 小程序环境下添加备用的页面滚动监听
					if(this.isMiniprogramEnv) {
						this.initScrollListener();
					}
					
				} catch (error) {
					console.error('2025-06-10 20:55:53,565-ERROR-[dp-product][initObserver_002] 初始化观察者失败：', error);
					// 观察者初始化失败时，使用滚动监听作为备用方案
					this.initScrollListener();
				}
			},
			
			// 处理观察者结果的统一方法
			handleObserverResult(res) {
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product][handleObserverResult_001] 观察者结果：', {
					intersectionRatio: res.intersectionRatio,
					isIntersecting: res.intersectionRatio > 0,
					loading: this.loading,
					hasMoreData: this.hasMoreData
				});
				
				// 当交叉比率大于0.1且不在加载中状态时触发加载
				if(res.intersectionRatio > 0.1 && !this.loading && this.hasMoreData) {
					// 如果是瀑布流视图，确保瀑布流已加载完成才触发新的加载
					if(this.isWaterfallView && !this.waterfallLoaded) {
						console.log('2025-06-10 20:55:53,565-INFO-[dp-product][handleObserverResult_002] 等待瀑布流加载完成');
						return;
					}
					
					// 如果是混合模式，确保混合内容已加载完成才触发新的加载
					if(this.isMixedMode && !this.mixedLoaded) {
						console.log('2025-06-10 20:55:53,565-INFO-[dp-product][handleObserverResult_003] 等待混合模式加载完成');
						return;
					}
					
					console.log('2025-06-10 20:55:53,565-INFO-[dp-product][handleObserverResult_004] 触发自动加载更多');
					this.loadMore();
				}
			},
			
			// 初始化滚动监听（备用方案）
			initScrollListener() {
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product][initScrollListener_001] 初始化滚动监听备用方案');
				
				// 监听页面滚动事件
				uni.onPageScroll((e) => {
					if(this.scrollTimer) {
						clearTimeout(this.scrollTimer);
					}
					
					// 防抖处理，避免频繁触发
					this.scrollTimer = setTimeout(() => {
						this.handlePageScroll(e.scrollTop);
					}, 100);
				});
			},
			
			// 处理页面滚动事件
			handlePageScroll(scrollTop) {
				// 如果正在加载或没有更多数据，直接返回
				if(this.loading || !this.hasMoreData) {
					return;
				}
				
				// 获取页面信息
				const pages = getCurrentPages();
				const page = pages[pages.length - 1];
				
				// 获取窗口信息
				uni.getSystemInfo({
					success: (systemInfo) => {
						const windowHeight = systemInfo.windowHeight;
						
						// 创建选择器查询加载更多区域的位置
						const query = uni.createSelectorQuery().in(page);
						query.select('#loadMoreArea').boundingClientRect((rect) => {
							if(rect) {
								// 计算加载区域是否接近可视区域
								const distanceToViewport = rect.top - windowHeight;
								
								console.log('2025-06-10 20:55:53,565-INFO-[dp-product][handlePageScroll_001] 滚动检测：', {
									scrollTop,
									windowHeight,
									rectTop: rect.top,
									distanceToViewport
								});
								
								// 当加载区域距离视窗底部小于200px时触发加载
								if(distanceToViewport < 200) {
									// 检查瀑布流和混合模式的加载状态
									if(this.isWaterfallView && !this.waterfallLoaded) {
										console.log('2025-06-10 20:55:53,565-INFO-[dp-product][handlePageScroll_002] 等待瀑布流加载完成');
										return;
									}
									
									if(this.isMixedMode && !this.mixedLoaded) {
										console.log('2025-06-10 20:55:53,565-INFO-[dp-product][handlePageScroll_003] 等待混合模式加载完成');
										return;
									}
									
									console.log('2025-06-10 20:55:53,565-INFO-[dp-product][handlePageScroll_004] 通过滚动监听触发自动加载');
									this.loadMore();
								}
							}
						}).exec();
					}
				});
				
				this.lastScrollTop = scrollTop;
			},
		},
		watch: {
			// 监听数据变化，重置分页
			data() {
				this.resetPagination();
			},
			// 监听混合文章数据变化
			mixedArticles() {
				this.resetPagination();
			},
			// 监听混合视频数据变化
			mixedVideos() {
				this.resetPagination();
			}
		},
		mounted() {
			// 组件挂载后初始化观察者
			this.$nextTick(() => {
				setTimeout(() => {
					this.initObserver();
				}, 300); // 延长初始化时间，确保DOM完全渲染
			});
		},
		beforeDestroy() {
			// 组件销毁前断开观察者
			if(this.observer) {
				this.observer.disconnect();
				this.observer = null;
			}
			
			// 清理滚动定时器
			if(this.scrollTimer) {
				clearTimeout(this.scrollTimer);
				this.scrollTimer = null;
			}
			
			// 移除页面滚动监听
			// #ifdef MP
			uni.offPageScroll();
			// #endif
		}
	}
</script>
<style>
.dp-product{width:100%;height: auto; position: relative;overflow: hidden; padding: 0px; background: #fff;}
.load-more-area{
	width: 100%;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 20rpx;
}
.loading, .can-load-more, .no-more {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	font-size: 24rpx;
}
.loading {
	color: #666;
}
.can-load-more {
	color: #666;
}
.no-more {
	color: #999;
}
</style>