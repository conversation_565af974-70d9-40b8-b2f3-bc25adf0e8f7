<template>
	<view>
		<view v-if="isload">
			<view class="buydialog-mask" @tap="buydialogChange"></view>
			<view class="buydialog" :class="menuindex>-1?'tabbarbot':'notabbarbot'">
				<view class="close" @tap="buydialogChange">
					<image src="/static/img/close.png" class="image"/>
				</view>
				<view class="title">
					<image :src="nowguige.pic || product.pic" class="img" @tap="previewImage" :data-url="nowguige.pic || product.pic"/>
					
					
					<view v-if="controller =='ApiRestaurantShop' || controller =='ApiRestaurantTakeaway'" >
						<view class="price"   :style="{color:t('color1')}" >￥{{totalprice}}</view>
					</view>
					<view v-else-if="product.is_member_yh == 0 && product.is_newcustom == 1">
						<view class="price" :style="{color:t('color1')}" v-if="product.price_type != 1 || nowguige.sell_price > 0" >￥{{product.yh_price}}<text v-if="nowguige.market_price > nowguige.sell_price" class="t2">￥{{nowguige.market_price}}</text></view>
					</view>
					<view v-else>
						<view class="price" :style="{color:t('color1')}">
							￥{{nowguige.sell_price}}<text v-if="nowguige.market_price > nowguige.sell_price" class="t2">￥{{nowguige.market_price}}</text>
						</view>
					</view>
					
					<text class="choosename" v-if="product.limit_start > 1"> {{product.limit_start}}件起售</text>
					<view class="stock" v-if="!shopset || shopset.hide_stock!=1">库存：{{nowguige.stock}}</view>
					<view class="choosename" v-if="product.limit_start<=1">已选规格: {{nowguige.name}}{{jltitle}}</view>
				</view>
				
				<block v-if="showglass">
					<view v-if="!glassrecord.id">
						<view class="glassinfo" @tap="goto" :data-url="'/pagesExt/glass/add'">
							<view class="">
								完善你的视力档案
							</view>
							<view class="flex flex-e">
								<image src="../../static/img/arrowright.png">
							</view>
						</view>
					</view>
					<view class="glassinfo" v-else @tap="goto" :data-url="'/pagesExt/glass/index?c=1&sid='+glassrecord.id">
						<view class="">
							视力档案
						</view>
						<view class="flex flex-e">
							<text>{{glassrecord.type==1?'近视':'远视'}}，右眼{{glassrecord.degress_right}}度，左眼{{glassrecord.degress_left}}度</text>
							<image src="../../static/img/arrowright.png">
						</view>
					</view>
				</block>
				<view v-if="nowguige.balance_price" style="width:94%;margin:10rpx 3%;font-size:24rpx;" :style="{color:t('color1')}">首付款金额：{{nowguige.advance_price}}元，尾款金额：{{nowguige.balance_price}}元</view>
				<view style="max-height:50vh;overflow:scroll">
					<view v-for="(item, index) in guigedata" :key="index" class="guigelist flex-col">
						<view class="name">{{item.title}}</view>
						<view class="item flex flex-y-center">
							<block v-for="(item2, index2) in item.items" :key="index2">
								<view v-if="getGuigeStatus(item.k, item2.k)" 
									:data-itemk="item.k" 
									:data-idx="item2.k" 
									:class="'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')" 
									@tap="ggchange">
									{{item2.title}}
								</view>
							</block>
						</view>
					</view>
				</view>
				<!--加料-->
				<view style="max-height:50vh;overflow:scroll" v-if="jialiaodata.length > 0 && (controller =='ApiRestaurantShop' || controller =='ApiRestaurantTakeaway')">
					<view   class="guigelist flex-col">
						<view class="name">加料</view>
						<view  class="item flex flex-y-center">
							
							<!-- <view v-for="(item,index) in tagList" :key="index" class="item" :class="item.active?'active':''" @click="tagClick(index)">{{item.name}}</view> -->
							<view v-for="(jlitem, jlindex) in jialiaodata" :key="jlindex"  class="item2" :class="jlitem.active?'on':''" @click="jlchange(jlindex)">{{jlitem.jltitle}}</view>
						
						</view>
					</view>
				</view>
				<block v-if="product.price_type == 1">
					<button class="addcart" :style="{backgroundColor:t('color2')}" @tap="showLinkChange">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</button>
				</block>
				
				<block v-else>
					<view class="buynum flex flex-y-center">
						<view class="flex1">购买数量：</view>
						<view class="addnum">
							<view class="minus" @tap="gwcminus"><image class="img" src="/static/img/cart-minus.png"/></view>
							<input class="input" type="number" :value="gwcnum" @input="gwcinput"></input>
							<view class="plus" @tap="gwcplus"><image class="img" src="/static/img/cart-plus.png"/></view>
						</view>
					</view>
					<view class="tips-text" :style="{color:t('color1')}" v-if="shopset && shopset.showcommission==1 && nowguige.commission > 0">分享好友购买预计可得{{t('佣金')}}：<text style="font-weight:bold;padding:0 2px">{{nowguige.commission}}</text>{{nowguige.commission_desc}}</view>
					<view class="op">
						<block v-if="nowguige.stock <= 0">
							<button class="nostock">库存不足</button>
						</block>
						<block v-else>
							<button class="addcart" :style="{backgroundColor:t('color2')}" @tap="addcart" v-if="product.hide_price != 1 && btntype==0 && canaddcart">加入购物车</button>
							<button class="tobuy" :style="{backgroundColor:t('color1')}" @tap="tobuy" v-if="product.hide_price != 1 && btntype==0">立即购买</button>
							<button class="addcart" :style="{backgroundColor:t('color2')}" @tap="addcart" v-if="product.hide_price != 1 && btntype==1">确 定</button>
	
							<button class="tobuy" :style="{backgroundColor:t('color1')}" @tap="tobuy" v-if="product.hide_price != 1 && btntype==2">确 定</button>
							<button class="tobuy" :style="{backgroundColor:t('color1')}" @tap="hidePriceLink" v-if="product.hide_price == 1">{{product.hide_price_detail_text}}</button>
						</block>
					</view>
				</block>
			</view>
		</view>
		<loading v-if="loading"></loading>
	</view>
	</template>
	<script>
		var app = getApp();
		export default {
			data() {
				return {
					ks:'',
					product:{},
					guigelist:{},
					guigedata:{},
					ggselected:{},
					nowguige:{},
					jialiaodata:[],
					jlprice:0,
					jltitle:'',
					gwcnum:1,
					isload:false,
					loading:false,
					canaddcart:true,
					shopset:{},
					glassrecord:{},
					showglass:false,
					totalprice:0,
					jlselected:[]
				}
			},
			props: {
				btntype:{default:0},
				menuindex:{default:-1},
				controller:{default:'ApiShop'},
				needaddcart:{default:true},
				proid:{},
				tid:{default:0},
			},
			mounted:function(){
				var that = this;
				uni.$on('getglassrecord', function(data) {
					 that.getglassrecord()
				});
				that.getdata();
			},
			beforeDestroy(){
				uni.$off('getglassrecord')
			},
			methods:{
				getdata:function(){
					var that = this;
					if(this.controller == 'ApiShop' && app.globalData.isdouyin == 1){
						app.showLoading('加载中');
						app.post('ApiShop/getDouyinProductId',{proid:that.proid},function(res){
							app.showLoading(false);
							if(res.status == 1){
								tt.openEcGood({promotionId:res.douyin_product_id});
							}else{
								app.alert(res.msg)
							}
						});
						
						return;
					}
	
					that.loading = true;
					app.post(this.controller+'/getproductdetail',{id:that.proid},function(res){
						that.loading = false;
						that.product = res.product;
						that.shopset = res.shopset;
						if(!that.product.limit_start){
							that.product.limit_start = 1;
						}
						
						that.guigelist = res.guigelist;
						that.guigedata = res.guigedata;
						var guigedata = res.guigedata;
						var ggselected = [];

						// 初始化规格选择,为每个规格选择第一个可用的选项
						for (var i = 0; i < guigedata.length; i++) {
							// 查找第一个可用的规格选项
							let foundValid = false;
							for(let j = 0; j < guigedata[i].items.length; j++) {
								// 临时设置当前规格值检查是否可用
								let tempSelected = [...ggselected];
								tempSelected[i] = j;
								let tempKs = tempSelected.join(',');
								let tempGuige = that.guigelist[tempKs];
								
								if(tempGuige && tempGuige.status === 1) {
									ggselected[i] = j; // 找到可用规格,设置为默认值
									foundValid = true;
									break;
								}
							}
							
							if(!foundValid) {
								ggselected[i] = 0; // 如果没有找到可用规格,设为0
							}
						}

						that.ks = ggselected.join(',');
						that.nowguige = that.guigelist[that.ks];
						that.ggselected = ggselected;
						
						if(that.nowguige.limit_start > 0)
							that.gwcnum = that.nowguige.limit_start;
						else
							that.gwcnum = that.product.limit_start;
						
						that.isload = true;
						if(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车
							that.canaddcart = false;
						}
						//是否是眼睛产品
						if(that.product.product_type==1){
							that.showglass = true
							that.getglassrecord()
						}
						if(that.controller =='ApiRestaurantShop' ||that.controller =='ApiRestaurantTakeaway'){
							
							that.jialiaodata = res.jialiaodata;
							that.totalprice = that.nowguige.sell_price;
						}
					});
				},
				buydialogChange:function(){
					this.$emit('buydialogChange');
				},
				getglassrecord:function(e){
					var that = this;
					var grid = app.getCache('_glass_record_id');
					if(that.showglass===true && (!that.glassrecord || (that.glassrecord && that.glassrecord.id!=grid))){
						app.post('ApiGlass/myrecord', {pagenum:1,listrow:1,id:grid}, function (resG) {
							var datalist = resG.data;
							if(datalist.length>0){
								that.glassrecord = datalist[0]
							}
						});
					}
				},
				showLinkChange:function () {
					this.$emit('showLinkChange');
				},
				//选择规格
				ggchange: function (e){
					let idx = e.currentTarget.dataset.idx;
					let itemk = e.currentTarget.dataset.itemk;
					
					// 检查规格状态
					if(!this.getGuigeStatus(itemk, idx)) {
						app.error('该规格已下架');
						return;
					}

					let ggselected = this.ggselected;
					ggselected[itemk] = idx;
					let ks = ggselected.join(',');
					
					this.ggselected = ggselected;
					this.ks = ks;
					this.nowguige = this.guigelist[this.ks];
					
					if(this.nowguige.limit_start > 0) {
						if (this.gwcnum < this.nowguige.limit_start) {
							this.gwcnum = this.nowguige.limit_start;
						}
					}
					this.totalprice = parseFloat(parseFloat(this.nowguige.sell_price) + this.jlprice).toFixed(2);
				},
				jlchange:function(index){
					
					this.jialiaodata[index].active =this.jialiaodata[index].active==true?false: true;
					var jlprice = 0;
					var title = '';
					let jlselect = [];
					for(let i=0;i<this.jialiaodata.length;i++){
						if(this.jialiaodata[i].active){
							jlprice = jlprice+parseFloat(this.jialiaodata[i].price);	
							title +=','+this.jialiaodata[i].jltitle;
							jlselect.push(this.jialiaodata[i]);
						}
					}
					this.jltitle =title;
					this.jlprice = jlprice;
					 this.totalprice =parseFloat( parseFloat(this.nowguige.sell_price) +jlprice).toFixed(2);
					this.jlselected = jlselect;
					
					this.jialiaodata = this.jialiaodata;
				},
				tobuy: function (e) {
					var that = this;
					var ks = that.ks;
					var proid = that.product.id;
					var ggid = that.guigelist[ks].id;
					var stock = that.guigelist[ks].stock;
					var num = that.gwcnum;
					if (num < 1) num = 1;
					if (stock < num) {
						app.error('库存不足');
						return;
					}
					var prodata = proid + ',' + ggid + ',' + num;
					if(that.showglass){
						var glass_record_id = app.getCache('_glass_record_id');
						prodata += ',' + glass_record_id;
					}
					this.$emit('buydialogChange');
					if(this.controller == 'ApiShop'){
						app.goto('/shopPackage/shop/buy?prodata=' + prodata);
					}else if(this.controller == 'ApiSeckill'){
						app.goto('/activity/seckill/buy?prodata=' + prodata);
					}else if(this.controller == 'ApiSeckill2'){
						app.goto('/activity/seckill2/buy?prodata=' + prodata);
					}else if(this.controller == 'ApiRestaurantTakeaway'){
						app.goto('/restaurant/takeaway/buy?prodata=' + prodata);
					}else if(this.controller == 'ApiRestaurantShop'){
						app.goto('/restaurant/shop/buy?prodata=' + prodata);
					}
				},
				//加入购物车操作
				addcart: function () {
					var that = this;
					var ks = that.ks;
					var num = that.gwcnum;
					var proid = that.product.id;
					var ggid = that.guigelist[ks].id;
					var stock = that.guigelist[ks].stock;
					var tid = that.tid;
					if (num < 1) num = 1;
					if (stock < num) {
						app.error('库存不足');
						return;
					}				
					var glass_record_id = 0;
					if(that.showglass){
						glass_record_id = app.getCache('_glass_record_id');
					}
					if(this.needaddcart){
						app.post(this.controller+'/addcart', {tid:tid,proid: proid,ggid: ggid,num: num,glass_record_id:glass_record_id}, function (res) {
							if (res.status == 1) {
								app.success('添加成功');
							} else {
								app.error(res.msg);
							}
						});
					}
				
					this.$emit('addcart',{proid: proid,ggid: ggid,num: num,jlprice:this.jlprice,jltitle:this.jltitle});
					this.$emit('buydialogChange');
				},
				//加
				gwcplus: function (e) {
					var gwcnum = this.gwcnum + 1;
					var ks = this.ks;
					if (gwcnum > this.guigelist[ks].stock) {
						app.error('库存不足');
						return 1;
					}
					if (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {
						app.error('每单限购'+this.product.perlimitdan+'件');
						return 1;
					}
					this.gwcnum = this.gwcnum + 1;
				},
				//减
				gwcminus: function (e) {
					var gwcnum = this.gwcnum - 1;
					var ks = this.ks;
					if(this.nowguige.limit_start > 0) {
						if (gwcnum <= this.nowguige.limit_start - 1) {
							if(this.nowguige.limit_start > 1){
								app.error('该规格' + this.nowguige.limit_start + '件起售');
							}
							return;
						}
					}else{
						if (gwcnum <= this.product.limit_start - 1) {
							if(this.product.limit_start > 1){
								app.error('该商品' + this.product.limit_start + '件起售');
							}
							return;
						}
					}
					
					this.gwcnum = this.gwcnum - 1;
				},
				//输入
				gwcinput: function (e) {
					var ks = this.ks;
					var gwcnum = parseInt(e.detail.value);
					if (gwcnum < 1) return 1;
					if (gwcnum > this.guigelist[ks].stock) {
						return this.guigelist[ks].stock > 0 ? this.guigelist[ks].stock : 1;
					}
					if(this.nowguige.limit_start > 0) {
						if (gwcnum <= this.nowguige.limit_start - 1) {
							if(this.nowguige.limit_start > 1){
								app.error('该规格' + this.nowguige.limit_start + '件起售');
							}
							gwcnum = this.nowguige.limit_start;
						}
					}else{
						if (gwcnum <= this.product.limit_start - 1) {
							if(this.product.limit_start > 1){
								app.error('该商品' + this.product.limit_start + '件起售');
							}
							gwcnum = this.product.limit_start;
						}
						
					}
					if (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {
						app.error('每单限购'+this.product.perlimitdan+'件');
						gwcnum = this.product.perlimitdan;
					}
					
					this.gwcnum = gwcnum;
				},
				hidePriceLink(){
					app.goto(this.product.hide_price_link)
				},
				// 获取规格状态
				getGuigeStatus(itemk, idx) {
					// 创建新数组来存储选中状态
					let ggselected = this.ggselected.slice(); // 使用 slice() 代替展开运算符
					ggselected[itemk] = idx;
					let ks = ggselected.join(',');
					let guige = this.guigelist[ks];
					return guige && guige.status === 1;
				}
			}
		}
	</script>
	<style>
	
	.buydialog-mask{ position: fixed; top: 0px; left: 0px; width: 100%; background: rgba(0,0,0,0.5); bottom: 0px;z-index:10}
	.buydialog{ position: fixed; width: 100%; left: 0px; bottom: 0px; background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px}
	.buydialog .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:12}
	.buydialog .close .image{ width: 30rpx; height:30rpx; }
	.buydialog .title{ width: 94%;position: relative; margin: 0 3%; padding:30rpx 0px 20rpx 0; border-bottom:0; height: 190rpx;}
	.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}
	.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}
	.buydialog .title .price .t1{ font-size:26rpx}
	.buydialog .title .price .t2{ font-size:26rpx;text-decoration:line-through;color:#aaa; margin-left: 6rpx;}
	.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}
	.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}
	
	.buydialog .guigelist{ width: 94%; position: relative; margin: 0 3%; padding:0px 0px 10px 0px; border-bottom: 0; }
	.buydialog .guigelist .name{ height:70rpx; line-height: 70rpx;}
	.buydialog .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}
	.buydialog .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:4rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4}
	.buydialog .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}
	.buydialog .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }
	.buydialog .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}
	.buydialog .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}
	.buydialog .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}
	.buydialog .addnum .img{width:24rpx;height:24rpx}
	.buydialog .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:28rpx;margin: 0 15rpx;}
	.buydialog .tips-text{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}
	
	.buydialog .op{width:90%;margin:20rpx 5%;border-radius:36rpx;overflow:hidden;display:flex;margin-top:60rpx;}
	.buydialog .addcart{flex:1;height:72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}
	.buydialog .tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}
	.buydialog .nostock{flex:1;height: 72rpx; line-height: 72rpx; background:#aaa; color: #fff; border-radius: 0px; border: none;}
	.glassinfo{width: 94%;margin: 10rpx 3% 0 3%;color: #333;display: flex;justify-content: space-between;align-items: center;padding: 6rpx 10rpx;
	background: #f4f4f4;border-radius: 10rpx;}
	.glassinfo image{width: 32rpx;height: 36rpx;padding-top: 4rpx;}
	</style>