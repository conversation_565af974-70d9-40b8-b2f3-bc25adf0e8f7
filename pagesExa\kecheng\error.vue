<template>
  <view>
    <block v-if="isload">
      <view class="wrap">
        <view class="top flex">
          <view class="f1" v-if="tkdata.type==1">判断题</view>
          <view class="f1" v-if="tkdata.type==2">单选题</view>
          <view class="f1" v-if="tkdata.type==3">多选题</view>
          <view class="f1" v-if="tkdata.type==4">填空题</view>
          <view class="f1" v-if="tkdata.type==5">简答题</view>
          <view class="f3">{{tkdata.sort}}/{{tkdata.nums}}</view>
        </view>
        <view class="question">
          <!-- 题目标题部分 -->
          <view class="title">
            <text class="serial-number">{{tkdata.sort}}.</text>
            <rich-text :nodes="tkdata.title"></rich-text>
          </view>

          <!-- 判断题和单选题 -->
          <block v-if="tkdata.type==1 || tkdata.type==2">
            <view class="option_group">
              <view
                :class="'option ' + (isSelected(index) ? 'on' : '')"
                v-for="(item, index) in tkdata.option"
                :key="index"
                @tap="selectOption(index)"
              >
                <view class="sort">{{tkdata.sorts[index]}}</view>
                <view class="after"></view>
                <view class="t1">
                  <rich-text :nodes="item"></rich-text>
                </view>
              </view>
            </view>
          </block>

          <!-- 多选题 -->
          <block v-if="tkdata.type==3">
            <view class="option_group">
              <view
                :class="'option ' + (isActive(index) ? 'on' : '')"
                v-for="(item, index) in tkdata.option"
                :key="index"
                @tap="selectOption(index)"
              >
                <view class="sort">{{tkdata.sorts[index]}}</view>
                <view class="after"></view>
                <view class="t1">
                  <rich-text :nodes="item"></rich-text>
                </view>
              </view>
            </view>
          </block>

          <!-- 填空题 -->
          <block v-if="tkdata.type==4">
            <view class="option_group">
              <view class="input-group" v-for="(item, index) in tkdata.option" :key="index">
                <input
                  @input="selectInput(index, $event)"
                  class="uni-input"
                  :value="tkdata.answer[index] || ''"
                  placeholder="请填写答案"
                />
              </view>
            </view>
          </block>

          <!-- 简答题 -->
          <block v-if="tkdata.type==5">
            <view class="option_group">
              <view class="uni-textarea">
                <textarea
                  placeholder-style="color:#222"
                  placeholder="答:"
                  @blur="bindTextAreaBlur"
                  :value="tkdata.answer"
                />
              </view>
            </view>
          </block>
        </view>
      </view>
      <view class="right_content">
        <text class="t1">正确答案</text>
        <view class="t2">
          {{getRightOption()}}
          <view>题目解析：{{tkdata.jiexi}}</view>
        </view>

      </view>
      <view class="bottom flex">
        <block v-if="tkdata.isup != 1">
          <button class="upbut flex-x-center flex-y-center hui">上一题</button>
        </block>
        <block v-if="tkdata.isup == 1">
          <button
            @tap="toanswer"
            data-dttype="up"
            class="upbut flex-x-center flex-y-center"
            :style="{ background: t('color1') }"
          >
            上一题
          </button>
        </block>
        <button
          v-if="tkdata.isdown == 1"
          @tap="toanswer"
          data-dttype="down"
          class="downbtn flex-x-center flex-y-center"
          :style="{ background: t('color1') }"
        >
          下一题
        </button>
        <button v-if="tkdata.isdown != 1" class="downbtn flex-x-center flex-y-center hui">
          下一题
        </button>
      </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>
<script>
var app = getApp();
var interval = null;
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      title: "",
      datalist: [],
      logid: '',
      isActive: [],
      currentindex: '',
      op: '',
      right_option: '',
      tkdata: [],
      right_option_list: [],
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  onUnload: function () {
    clearInterval(interval);
  },
  methods: {
    getdata: function () {
      var that = this;
      var id = this.opt.rid || 0;
      that.loading = true;
      app.post('Apipaper/error', { rid: id, op: that.op, logid: that.logid }, function (res) {
        that.loading = false;
        if (res.status == 0) {
          app.alert(res.msg);
          return;
        }
        that.tkdata = res.data;
        if (that.tkdata.type == 1 && res.data.answer.length > 0) {
          that.currentindex = res.data.answer;
        } else {
          that.currentindex = res.data.answer;
        }
        that.logid = res.data.logid;
        that.isload = true; // 确保加载完成
      });
    },
    toanswer: function (e) {
      var that = this;
      that.op = e.currentTarget.dataset.dttype;
      that.isActive = [];
      that.getdata();
    },
    bindTextAreaBlur: function (e) {
      var that = this;
      that.right_option = e.detail.value;
    },
    selectOption: function (index) {
      if (this.tkdata.type == 1 || this.tkdata.type == 2) {
        // 单选或判断题
        this.currentindex = index.toString();
      } else if (this.tkdata.type == 3) {
        // 多选题
        const idx = this.currentindex.indexOf(index.toString());
        if (idx > -1) {
          this.currentindex.splice(idx, 1);
        } else {
          this.currentindex.push(index.toString());
        }
      }
      // 这里可以添加提交选项的逻辑
    },
    isSelected(index) {
      if (this.tkdata.type == 1 || this.tkdata.type == 2) {
        return index.toString() === this.currentindex;
      } else if (this.tkdata.type == 3) {
        return this.currentindex.indexOf(index.toString()) !== -1;
      }
      return false;
    },
    isActive(index) {
      return this.currentindex.indexOf(index.toString()) !== -1;
    },
    getRightOption() {
      if (this.tkdata.type == 3 || this.tkdata.type == 2 || this.tkdata.type == 1 || this.tkdata.type == 5) {
        return this.tkdata.right_option;
      } else if (this.tkdata.type == 4) {
        return this.tkdata.right_option.map((item, index) => `答案${index + 1}: ${item.join(', ')}`).join('\n');
      }
      return '';
    },
    selectInput(index, event) {
      this.$set(this.tkdata.answer, index, event.detail.value);
    },
  }
};
</script>
<style>
.wrap {
  background: #fff;
  margin: 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
}

.top {
  height: 120rpx;
  line-height: 100rpx;
  justify-content: space-between;
}
.top .f1 {
  color: #93949E;
  font-size: 28rpx;
}
.top .f3 {
  color: #93949E;
  font-size: 28rpx;
}
.question .title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.right_content .t2 {
  color: #93949E;
  font-size: 26rpx;
  /* 如果需要，可以在这里添加其他样式 */
}

.right_content {
  background: #fff;
  margin: 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
}
.right_content .t1 {
  color: #333;
  font-size: 30rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}
.right_content .t2 {
  color: #93949E;
  font-size: 26rpx;
}
.option_group {
  margin-top: 30rpx;
}
.option_group .option {
  position: relative;
  padding-left: 60rpx; /* 增加左内边距以容纳排序和标识 */
  padding-top: 15rpx;
  padding-bottom: 15rpx;
  background: #F8F4F4;
  margin-top: 20rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
}
.option_group .option .sort {
  position: absolute;
  left: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #93949E;
}
.option_group .option .after {
  border: 1px solid #BBB;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}
.option_group .option.on {
  background: #FDF1F1;
  color: #FF5347;
  border: 1px solid #FFAEA8;
}
.option_group .option.on .after {
  border: 1px solid #FF8D8D;
  background: #FF8D8D;
}
.option_group .option .t1 {
  margin-left: 60rpx; /* 对齐文本内容 */
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-wrap: break-word;
}
.option_group .option.green {
  background: #36CF7B;
  color: #fff;
}
.option_group .option.green .after {
  border: 1px solid #fff;
}
.bottom .upbut {
  width: 240rpx;
  height: 88rpx;
  line-height: 88rpx;
  color: #fff;
  border-radius: 44rpx;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
  background: #FD4A46;
}
.bottom .upbut.hui {
  background: #E3E3E3;
}
.bottom .downbtn {
  margin-left: 50rpx;
  width: 360rpx;
  height: 88rpx;
  border-radius: 44rpx;
  line-height: 88rpx;
  color: #fff;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
  background: #FD4A46;
}
.bottom .downbtn.hui {
  background: #E3E3E3;
}
.bottom {
  margin-top: 30rpx;
  padding: 30rpx;
}
.uni-textarea {
  margin-top: 30rpx;
  background: #FAFAFA;
  border: 1px solid #EBE5E5;
  border-radius: 8rpx;
  padding: 30rpx;
}
.input-group {
  margin-bottom: 20rpx;
}
.uni-input {
  width: 100%;
  line-height: 40rpx;
  height: 40rpx;
  border: none;
  background: transparent;
}
</style>
