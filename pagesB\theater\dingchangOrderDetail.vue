<template>
	
	<view class="jc_center">

		<view class="content">
			
			<!-- 添加安全检查，确保 data 存在且有 status 属性 -->
			<view class="state" v-if="data && data.status == 3">已退款</view> 
			<view class="state" v-else-if="data && data.status == 2">已完成</view> 
			<view class="state" v-else-if="data && data.status == 1">已支付</view> 
			<view class="state" v-else-if="data">待支付</view> 
			
			<!-- 添加条件判断，确保数据存在再渲染 -->
			<view class="message" v-if="data && data.episode && data.order_detail">
				<view class="message_txt1">{{data.episode.title || ''}}</view>
				
				<!-- 添加锁座订单显示 -->
				<view class="message_txt2" v-if="data.remarks === '锁座订单'" style="color: #ff4246; font-weight: bold;">
					🔒 锁座订单
				</view>
				
				<view class="message_txt2">预定日期: {{data.order_detail.title || ''}}</view>
				<view class="message_txt2"> 
				   <!-- 修正数据访问路径：从 packages_info.remaining_seats 改为 order_detail.list -->
				   <view v-for="(item, index) in data.order_detail.list" :key="index">
				   
				      <text style="margin-right: 10rpx;">{{item.y}}排{{item.x}}座  {{item.money}}元</text>
			
				   </view>
				</view>
				
				<view class="message_txt2">订单编号：{{data.ordernum || ''}}</view>
				
				<view class="message_txt2 fl">
					<text>实付金额：{{data.totalprice || 0}}元</text>
					
					<!-- 修复退款状态访问，添加数组长度检查 -->
					<view v-if="data.status==1">
						
						<text class="mbn" v-if="data.refund_list && data.refund_list.length > 0 && data.refund_list[0].status==0">
						  退款审核中
						</text>  
						
						<text class="mbn" v-else-if="data.refund_list && data.refund_list.length > 0 && data.refund_list[0].status==2">
						  退款未通过  
						</text>
						
						<!-- 锁座订单不显示退款按钮 -->
						<text class="mbt" @click="goList()" v-else-if="data.remarks !== '锁座订单'">
						  退款
						</text>
						
					</view>
					

					
				</view>
			</view>
			
			<!-- 添加条件判断 -->
			<view class="message1" v-if="data && data.status==1 && tel">
				<view class="message_txt1">手机号码：{{tel}}</view>
				
				<view class="message_txt2">凭改手机号后四位验证</view>
			</view>
			
			
		
			<!-- 添加条件判断，确保二维码存在 -->
			<view class="hxqrbox" v-if="data && data.status==1 && data.hexiao_qr">
				<image :src="data.hexiao_qr" @tap="previewImage" :data-url="data.hexiao_qr" class="img"/>
				<view class="txt">请出示核销码给核销员进行核销</view>
			</view>
		
				
		</view>
		
		
		<uni-popup id="dialogShowCategory" ref="dialogShowCategory"  type="bottom" :mask-click="true">
		
		    <view class="view-main">
				  
		          <view class="view-title">
					  
					  <view style="display: flex;align-items: center;">
						  
						  <text style="margin-right: 20px;">退款座位</text>
						  
						  <view>
						  	<checkbox-group @change="allChoose">
						  		<checkbox value="all" :class="{'checked':allChecked}" :checked="allChecked?true:false"></checkbox>
						  		<text>全选 </text>
						  
						  	</checkbox-group>
						  </view>
						  
					  </view>

					  <image class="icon" src="../../static/img/close.png" @click="hideDialog()">
						
				  </view>
			
				  <scroll-view scroll-y style="height: 600rpx;">
					  
						<view class="tl-section">
							<checkbox-group class="block" @change="changeCheckbox">
								<view v-for="item,ind in checkboxData" :key="ind" class="tl-row">
									<view class="tl-center">
										<view>{{item.label}}</view>
									</view>
									<view>
										<checkbox :value="String(item.value)" :checked="checkedArr.includes(String(item.value))" :class="{'checked':checkedArr.includes(String(item.value))}"></checkbox>
									</view>
								</view>
							</checkbox-group>
						</view>

					  
				  </scroll-view>
				  
				  <view class="view-btn" :style="{background:t('color2')}" @click="goRefund()">确定退款</view>

			  </view>
			  
		  
		</uni-popup>
		          
	</view>
	
	
</template>

<script>
	var app = getApp();
	export default {
	  data() {
	    return {
			opt:{},
			// 修改初始化，使用对象而不是空字符串，避免访问 undefined 属性
			data : {
				status: null,
				episode: null,
				order_detail: null,
				refund_list: [],
				ordernum: '',
				totalprice: '',
				hexiao_qr: '',
				remarks: '' // 添加 remarks 字段，用于识别锁座订单
			},
			tel : '',
			
			checkboxData:[],
			checkedArr:[], //复选框选中的值
			allChecked:false //是否全选

	    };
	  },
	  
	   onLoad: function (opt) {
			this.opt = app.getopts(opt);
			this.getData();  
			this.getTel();
	   },
	   
		methods: {
			getData(){
				var that = this;
				
				var obj ={
					id : this.opt.id
				}  
		
				app.get('/ApiTheater/getOrderDetail', obj, function(res) {
					
					console.log(res)
					
					if(res.status == 1){
						
					     that.data = res.data
	
						 let arr = [];
						 // 修正数据访问路径：检查 order_detail.list 是否存在
						 if(res.data.order_detail && res.data.order_detail.list && Array.isArray(res.data.order_detail.list)) {
							 res.data.order_detail.list.filter(item=>{
								 let obj = {
									 value : item.id,
									 label : item.y+'排'+item.x+'座'
								 }
								 arr.push(obj);
							 })
						 }
							
						 that.checkboxData = arr
	
					}   	   
							 
				});
			},
			
			getTel() {
				var that = this;
				app.get('ApiMy/set', {}, function (data) {
					console.log(data)
					// 添加安全检查
					if(data && data.userinfo && data.userinfo.tel) {
						that.tel = data.userinfo.tel.substring(0,3)+'****'+data.userinfo.tel.substring(7,11);
					}
				});
			},
			
			goRefund(){
	
				var that = this;
				uni.showModal({
					title: '是否确定退款？',
					success() {
						let param = {
							id : that.opt.id,
							sid : that.checkedArr.toString()
						}
						app.post('ApiTheater/postRefund', param, function (data) {
						
						      if(data.status == 1){
								  uni.showToast({
								  	 'title': '已提交申请',
									 'icon': 'none'
								  })
								  
								  that.hideDialog();
								  that.getData();
								  
							  }else{
								  uni.showToast({
								  	'title': data.msg,
								  	'icon': 'none'
								  })
								  
							  }
							
						});
					}
				})
			},
			
			goList(){
			
				this.$refs.dialogShowCategory.open('top');
			},
			hideDialog(){
				
				this.$refs.dialogShowCategory.close();
			},
			
		   // 全选事件
			allChoose(e){
				let chooseItem = e.detail.value;
				// 全选
				if(chooseItem[0]=='all'){
					this.allChecked=true;
					for(let item of this.checkboxData){
						let itemVal=String(item.value);
						if(!this.checkedArr.includes(itemVal)){
							this.checkedArr.push(itemVal);
						}
					}
				}else{
					// 取消全选
					this.allChecked=false;
					this.checkedArr=[];
				}
			},
			// 多选复选框改变事件
			changeCheckbox(e){
				this.checkedArr = e.detail.value;
				// 如果选择的数组中有值，并且长度等于列表的长度，就是全选
				if(this.checkedArr.length>0 && this.checkedArr.length==this.checkboxData.length){
					this.allChecked=true;
				}else{
					this.allChecked=false;
				}
			}


			
		},
	
	}
	
	
</script>

<style lang="scss">     
   
	  
.jc_center {  
	display: flex;
	justify-content: center;
	align-items: center;
}	  
	 
.content{
	width: 90%;
	padding-top: 50rpx;
}

.content .state{
   text-align: center;
	padding-bottom: 10px;
	font-size: 20px
}

.message{
	background-color: #ffffff;
	padding: 30rpx 40rpx 40rpx;
	border-radius: 20rpx;
	
	.message_txt1{
		font-size: 36rpx;
		font-weight: bold;
	}
	.message_txt2{
		font-size: 26rpx;
		color: #797979;
		margin-top: 15rpx;
	}
	.message_txt3{
		text-align: right;
		margin-top: 50rpx;
	}
	.message_txt3 :last-child {
		color: #6c64ec;
		font-size: 30rpx;
		font-weight: bold;
	}
}

.message1{
	background-color: #ffffff;
	padding:  30rpx 40rpx;
	margin-top: 30rpx;
	border-radius: 20rpx;
	display: flex;
	justify-content: space-between;
	
	.message_txt1{
		font-size: 28rpx;
		font-weight: bold;
	}
	.message_txt2{
		font-size: 24rpx;
		color: #797979;
	}
	
}


.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx;text-align: center;margin-top: 30rpx}
.hxqrbox .img{width:400rpx;height:400rpx}
.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}


.fl{
	display: flex;
	justify-content: space-between;
}

	.view-main{
		padding: 28rpx;
		font-size: 28rpx;
		color: #333333;
		border-radius: 20rpx 20rpx 0 0; 
	
		background: #fff;
	}
	
	.view-title {
	    justify-content: space-between;
		align-items: center;
	    display: flex;
		font-size: 30rpx;
	     border-bottom: 1px solid #ccc;
	     padding-bottom: 10px;
	}
	
	.icon{
		width: 25rpx;
		height: 25rpx;
		margin: auto 0;
	}
	
	.icon2{
		width: 45rpx;
		height: 45rpx;
		margin: auto 0;
	}
	
	.tit-t{
		padding: 50rpx 0 20rpx 0;
		font-size: 22rpx;
	}
	
	.tit-date{
		padding: 10px;
		background: red;
		color: #fff;
		border-radius: 10rpx;
	}
	
	.tit-time{
		margin: 10rpx 0;
		border: 1px solid #f1f1f1;
		text-align: center;
		padding: 10rpx;
		border-radius: 10rpx;
	}
	
	.view-btn{
		background: red;
		color: #fff;
		text-align: center;
		padding: 10px;
		border-radius: 50rpx;
	}
	
	
	.tl-section{
	       padding: 10px;
	}
	
	.tl-row{
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
	}
	.tl-img-100{
		width: 100rpx;
		height: 100rpx;
		background: #C1BFBF;
		border-radius: 50%;
	}
	.tl-center{
	   font-size: 30rpx;
	}
	.tl-font-green{
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #4DB046;
		margin-left: 16rpx;
	}
	
	.mbt{
		background: #ff4246;
		color: #fff;
		padding: 2px 10px;
		border-radius: 10px;
	}

	.mbn{
		background: #aaa;
		color: #fff;
		padding: 2px 10px;
		border-radius: 10px;
	}
	
</style>