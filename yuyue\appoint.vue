﻿<template>
  <view class="container">
    <view class="order-info" v-if="orderInfo && orderInfo.product">
      <view class="product-info">
        <image :src="orderInfo.product.pic" mode="aspectFill"></image>
        <view class="info">
          <text class="name">{{orderInfo.product.name}}</text>
          <text class="price" :style="{color:t('color1')}">￥{{orderInfo.product.sell_price}}</text>
        </view>
      </view>
      <view class="order-detail">
        <text>订单号：{{orderInfo.order_no}}</text>
        <text>支付时间：{{orderInfo.pay_time}}</text>
      </view>
    </view>

    <view class="appoint-section">
      <view class="section-title">选择预约时间</view>
      
      <!-- 优化日期选择器 -->
      <view class="date-selector">
        <view class="date-header">
          <text class="date-title">选择日期</text>
          <text class="date-subtitle">请选择您方便的日期</text>
        </view>
        
        <view class="date-scroll-container">
          <scroll-view scroll-x class="date-scroll" show-scrollbar="false">
            <view 
              v-for="(date, index) in dateList" 
              :key="index" 
              class="date-item" 
              :class="{'date-active': selectedDate === date.value}"
              :style="selectedDate === date.value ? 'background:'+t('color1')+'; box-shadow: 0 4rpx 10rpx rgba('+t('color1rgb')+',0.2)' : ''"
              @tap="selectDate(date)">
              <text class="date-weekday" :style="selectedDate === date.value ? 'color:#fff' : ''">{{date.weekday}}</text>
              <text class="date-day" :style="selectedDate === date.value ? 'color:#fff' : ''">{{date.day}}</text>
              <text class="date-month" :style="selectedDate === date.value ? 'color:#fff' : ''">{{date.month}}月</text>
            </view>
          </scroll-view>
        </view>
      </view>
      
      <!-- 优化时间段选择 -->
      <view class="time-selector" v-if="selectedDate">
        <view class="time-header">
          <text class="time-title">选择时间段</text>
          <text class="time-subtitle">请选择您方便的时间</text>
        </view>
        
        <view class="time-period-tabs">
          <view 
            v-for="(period, idx) in timePeriods" 
            :key="idx"
            class="time-period-tab"
            :class="{'period-active': activePeriod === period.value}"
            :style="activePeriod === period.value ? 'background:'+t('color1')+'; box-shadow: 0 2rpx 6rpx rgba('+t('color1rgb')+',0.2); color: #fff' : ''"
            @tap="switchPeriod(period.value)">
            {{period.label}}
          </view>
        </view>
        
        <view class="time-list">
          <view class="time-item" 
            v-for="(item, index) in filteredTimeList" 
            :key="index"
            :class="{'disabled': !item.available}"
            :style="selectedTime === item.time ? 'background:'+t('color1')+'; transform: scale(1.05); box-shadow: 0 4rpx 10rpx rgba('+t('color1rgb')+',0.2)' : ''"
            @tap="selectTime(item)">
            <view class="time-content">
              <text class="time-text" :style="selectedTime === item.time ? 'color:#fff' : ''">{{item.time}}</text>
              <text class="time-status" :style="selectedTime === item.time ? 'color:#fff' : ''">
                <template v-if="item.available">可预约</template>
                <template v-else>{{item.reason || '已约满'}}</template>
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="footer">
      <view class="selected-info" v-if="selectedDate && selectedTime">
        <text>已选：{{formattedSelectedDate}} {{selectedTime}}</text>
      </view>
      <button class="submit-btn" :disabled="!canSubmit" 
        :style="canSubmit ? 'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%); box-shadow: 0 4rpx 10rpx rgba('+t('color1rgb')+',0.2)' : 'background:#ccc'" 
        @tap="submitAppoint">确认预约</button>
    </view>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      orderId: '',
      orderInfo: {
        product: {
          pic: '',
          name: '',
          sell_price: 0
        },
        order_no: '',
        pay_time: ''
      },
      selectedDate: '',
      selectedTime: '',
      timeList: [],
      dateList: [],
      startDate: '',
      endDate: '',
      loading: true,
      activePeriod: 'all',
      timePeriods: [
        { label: '全部', value: 'all' },
        { label: '上午', value: '上午' },
        { label: '下午', value: '下午' },
        { label: '傍晚', value: '傍晚' }
      ],
      productId: '', // 存储商品ID
      dateType: 1,    // 日期类型：1基于周期，2固定范围，3指定天数
      dayDate: '',     // 当前日期
      dateFromUrl: false // 标记日期是否来自URL参数
    }
  },
  
  computed: {
    canSubmit() {
      return this.selectedDate && this.selectedTime;
    },
    
    filteredTimeList() {
      if (this.activePeriod === 'all') {
        return this.timeList;
      } else {
        return this.timeList.filter(item => item.period === this.activePeriod);
      }
    },
    
    formattedSelectedDate() {
      if (!this.selectedDate) return '';
      
      if (this.selectedDate.includes('-')) {
        const parts = this.selectedDate.split('-');
        return `${parts[0]}年${parts[1]}月${parts[2]}日`;
      }
      
      return this.selectedDate;
    }
  },
  
  onLoad(options) {
    console.log('加载预约页面，参数:', options);
    // 初始化日期范围 - 提前初始化，确保有默认值
    this.initDateRange();
    
    if(options && options.id) {
      this.orderId = options.id;
      
      // 如果传入了日期参数，保存起来
      if(options.date) {
        console.log('已传入日期参数:', options.date);
        this.selectedDate = options.date;
        // 标记日期来自URL参数
        this.dateFromUrl = true;
      } else {
        // 没有传入日期参数
        console.log('未传入日期参数，不会向接口发送日期');
        this.dateFromUrl = false;
      }
      
      this.getOrderInfo();
    } else {
      app.error('未找到订单信息');
    }
  },
  
  methods: {
    // 获取订单信息
    getOrderInfo() {
      var that = this;
      app.showLoading('加载订单信息');
      
      console.log('当前选择的日期(获取订单前):', that.selectedDate);
      
      app.get('ApiYuyue/orderDetail', {
        id: that.orderId
      }, function(res) {
        app.showLoading(false);
        console.log('获取订单信息原始返回:', res);
        
        // 兼容不同的返回结构
        if (res.status == 1 && res.data) {
          console.log('标准接口返回结构 status+data');
          that.orderInfo = res.data;
        } else if (res.detail) {
          // 直接返回detail结构
          console.log('直接detail结构返回');
          that.orderInfo = res.detail;
        } else {
          // 尝试直接使用返回对象
          console.log('使用整个返回对象');
          that.orderInfo = res;
        }
        
        console.log('处理后的订单信息:', that.orderInfo);
        
        // 确保product存在
        if(!that.orderInfo.product) {
          // 构建产品信息
          that.orderInfo.product = {
            pic: that.orderInfo.propic || '',
            name: that.orderInfo.proname || '商品信息',
            sell_price: that.orderInfo.product_price || that.orderInfo.totalprice || 0
          };
          console.log('构建的产品信息:', that.orderInfo.product);
        }
        
        // 添加订单号和支付时间
        if (!that.orderInfo.order_no) {
          that.orderInfo.order_no = that.orderInfo.ordernum || '';
        }
        
        if (!that.orderInfo.pay_time) {
          // 尝试转换时间戳格式的支付时间
          if (that.orderInfo.paytime) {
            if (typeof that.orderInfo.paytime === 'number') {
              let date = new Date(that.orderInfo.paytime * 1000);
              that.orderInfo.pay_time = date.getFullYear() + '-' +
                ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
                ('0' + date.getDate()).slice(-2) + ' ' +
                ('0' + date.getHours()).slice(-2) + ':' +
                ('0' + date.getMinutes()).slice(-2) + ':' +
                ('0' + date.getSeconds()).slice(-2);
            } else {
              that.orderInfo.pay_time = that.orderInfo.paytime;
            }
          } else {
            that.orderInfo.pay_time = '';
          }
        }
        
        // 从订单信息中提取商品ID
        that.productId = that.orderInfo.product_id || that.orderInfo.proid || '';
        console.log('提取的商品ID:', that.productId);
        console.log('当前选择的日期(订单信息处理后):', that.selectedDate);
          
        that.loading = false;
        
        // 获取可预约日期列表
        that.getDateList();
      });
    },
    
    // 初始化日期范围
    initDateRange() {
      var today = new Date();
      this.startDate = today.toISOString().split('T')[0];
      
      var endDate = new Date();
      endDate.setDate(today.getDate() + 30); // 最多可预约30天
      this.endDate = endDate.toISOString().split('T')[0];
      
      // 保存当前日期，格式：YYYY-MM-DD
      this.dayDate = today.getFullYear() + '-' + 
        ((today.getMonth() + 1) < 10 ? '0' + (today.getMonth() + 1) : (today.getMonth() + 1)) + '-' + 
        (today.getDate() < 10 ? '0' + today.getDate() : today.getDate());
        
      // 设置默认选中当天日期（如果不是从URL获取的日期）
      if(!this.selectedDate) {
        this.selectedDate = this.dayDate;
        console.log('初始化选中当天日期:', this.selectedDate);
      }
    },
    
    // 获取可预约日期列表
    getDateList() {
      var that = this;
      // 确保有商品ID
      const productId = that.productId || that.orderId;
      
      console.log('获取日期列表前，当前选择的日期:', that.selectedDate);
      console.log('当前系统日期:', that.dayDate);
      console.log('日期是否来自URL:', that.dateFromUrl);
      
      if (!productId) {
        console.log('未找到商品ID，使用本地生成的日期列表');
        that.generateDefaultDateList();
        return;
      }
      
      app.showLoading('获取可预约日期');
      
      // 构建参数对象
      const params = {
        id: productId
      };
      
      // 只有当日期来自URL时才传递给接口
      if (that.dateFromUrl && that.selectedDate) {
        params.date = that.selectedDate;
        console.log('将URL指定的日期传递给接口:', that.selectedDate);
      } else {
        console.log('日期未从URL获取，不传递给接口');
      }
      
      console.log('获取日期列表参数:', params);
      
      app.get('ApiYuyue/getDateList', params, function(res) {
        app.showLoading(false);
        console.log('获取日期列表返回:', res);
        
        if (res.status == 1 && res.datelist && res.datelist.length > 0) {
          // 更新日期类型
          that.dateType = res.rqtype || 1;
          
          // 更新当前日期
          if (res.daydate) {
            that.dayDate = res.daydate;
            console.log('接口返回的当前日期:', that.dayDate);
          }
          
          // 处理接口返回的日期列表
          that.processDateList(res.datelist);
        } else {
          console.log('接口未返回有效的日期列表，使用默认生成');
          that.generateDefaultDateList();
        }
      }, function(err) {
        app.showLoading(false);
        console.log('获取日期列表失败，使用默认列表:', err);
        that.generateDefaultDateList();
      });
    },
    
    // 处理接口返回的日期列表
    processDateList(apiDateList) {
      console.log('处理接口返回的日期列表:', apiDateList);
      this.dateList = [];
      
      // 获取今天的日期用于比较
      const today = new Date();
      const todayStr = today.getFullYear() + '-' + 
        ((today.getMonth() + 1) < 10 ? '0' + (today.getMonth() + 1) : (today.getMonth() + 1)) + '-' + 
        (today.getDate() < 10 ? '0' + today.getDate() : today.getDate());
        
      apiDateList.forEach(item => {
        // 解析日期部分
        let month = '', day = '';
        if (item.date) {
          const dateParts = item.date.split('月');
          if (dateParts.length > 1) {
            month = dateParts[0].replace('月', '');
            day = dateParts[1];
          }
        }
        
        // 构建完整日期
        let fullDate = '';
        if (item.full_date) {
          fullDate = item.full_date;
        } else if (item.year && item.date) {
          // 从year和date构建，格式如：2025年 和 04月12
          const year = item.year.replace('年', '');
          const month = item.date.split('月')[0];
          const day = item.date.split('月')[1];
          fullDate = `${year}-${month}-${day}`;
        }
        
        // 直接使用接口返回的周几标签，不再进行今天/明天的替换
        // 这样可以避免与接口返回的 daydate 不匹配的问题
        let dayLabel = day;
        let weekday = item.weeks || '';
        
        // 构建日期项
        this.dateList.push({
          value: fullDate,
          year: item.year ? item.year.replace('年', '') : '',
          month: month,
          day: dayLabel,
          weekday: weekday,
          isToday: fullDate === todayStr // 标记是否为今天
        });
      });
      
      console.log('处理后的日期列表:', this.dateList);
      
      // 根据是否有预选日期选中相应日期
      if (this.dateList.length > 0) {
        let foundPreselectedDate = false;
        let foundTodayDate = false;
        let todayIndex = -1;
        
        // 首先查找今天的日期
        for (let i = 0; i < this.dateList.length; i++) {
          if (this.dateList[i].isToday) {
            foundTodayDate = true;
            todayIndex = i;
            break;
          }
        }
        
        // 如果有预选的日期，找到对应的日期选中
        if (this.selectedDate) {
          for (let i = 0; i < this.dateList.length; i++) {
            if (this.dateList[i].value === this.selectedDate) {
              console.log('找到预选日期:', this.selectedDate);
              foundPreselectedDate = true;
              break;
            }
          }
          
          // 如果没找到预选日期在列表中
          if (!foundPreselectedDate) {
            // 优先选择今天的日期
            if (foundTodayDate) {
              console.log('选择今天的日期:', this.dateList[todayIndex].value);
              this.selectedDate = this.dateList[todayIndex].value;
            } else {
              // 如果没有今天，则选择第一个可用日期
              console.log('预选日期不在可用日期列表中，使用第一个可用日期');
              this.selectedDate = this.dateList[0].value;
            }
            app.toast('选择的日期不可用，已为您选择最近可用日期');
          }
        } else {
          // 如果没有预选日期，优先选择今天
          if (foundTodayDate) {
            this.selectedDate = this.dateList[todayIndex].value;
            console.log('选择今天的日期:', this.selectedDate);
          } else {
            // 如果没有今天，选中第一个
            this.selectedDate = this.dateList[0].value;
            console.log('没有找到今天的日期，选择第一个日期:', this.selectedDate);
          }
        }
        
        // 加载时间段
        this.$nextTick(() => {
          this.getTimeList();
        });
      }
    },
    
    // 生成默认日期列表（当接口不可用时）
    generateDefaultDateList() {
      this.dateList = [];
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      
      for (let i = 0; i < 14; i++) { // 显示未来14天
        const date = new Date();
        date.setDate(today.getDate() + i);
        
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const weekday = weekdays[date.getDay()];
        
        // 格式化日期为YYYY-MM-DD
        const formattedDate = year + '-' + 
          (month < 10 ? '0' + month : month) + '-' + 
          (day < 10 ? '0' + day : day);
        
        let dayLabel = day.toString();
        if (i === 0) {
          dayLabel = '今天';
        } else if (i === 1) {
          dayLabel = '明天';
        }
        
        this.dateList.push({
          value: formattedDate,
          year: year,
          month: month,
          day: dayLabel,
          weekday: weekday,
          isToday: i === 0
        });
      }
      
      console.log('生成的默认日期列表:', this.dateList);
      
      // 默认选中当天日期
      this.selectedDate = todayStr;
      console.log('默认选中当天日期:', this.selectedDate);
      
      // 加载当天时间段
      this.$nextTick(() => {
        this.getTimeList();
      });
    },
    
    // 选择日期（从日期滚动列表）
    selectDate(date) {
      this.selectedDate = date.value;
      this.selectedTime = '';
      // 用户主动选择了日期，此时不再依赖URL参数
      this.dateFromUrl = false;
      console.log('用户选择了新日期, dateFromUrl设为false');
      this.getTimeList();
    },
    
    // 切换时间段筛选
    switchPeriod(period) {
      this.activePeriod = period;
    },
    
    // 获取时间段列表
    getTimeList() {
      var that = this;
      app.showLoading('加载时间段');
      
      // 确保日期格式正确 (YYYY-MM-DD)
      let formattedDate = that.selectedDate;
      if (formattedDate && formattedDate.includes('年')) {
        // 转换中文格式日期 (YYYY年MM月DD日) 为 YYYY-MM-DD
        formattedDate = formattedDate
          .replace('年', '-')
          .replace('月', '-')
          .replace('日', '');
      }
      
      console.log('当前选择的日期:', formattedDate);
      
      const params = {
        order_id: that.orderId
      };
      
      // 这里的日期已经由用户选择，因此不需要检查dateFromUrl标记
      // 与getDateList不同，时间段查询必须要有日期参数
      params.date = formattedDate;
      
      console.log('获取时间段参数:', params);
      
      // 从API获取时间段列表
      app.get('ApiYuyue/getTimeList', params, function(res) {
        app.showLoading(false);
        console.log('获取时间段返回:', res);
        
        // 检查是否为HTML错误页面或者接口错误
        if (typeof res === 'string' && res.indexOf('<!DOCTYPE html>') !== -1 || (res.status === 0 && res.msg)) {
          console.log('接口报错，使用默认时间段');
          that.createDefaultTimeSlots();
          return;
        }
        
        let timeData = [];
        
        // 兼容不同的返回结构
        if (res.status == 1 && res.data) {
          timeData = res.data;
        } else if (res.data) {
          timeData = res.data;
        } else if (Array.isArray(res)) {
          timeData = res;
        }
        
        // 处理并设置时间段
        that.processTimeData(timeData);
      }, function(err) {
        // 请求出错的回调处理
        app.showLoading(false);
        console.log('时间段获取失败，使用默认时间段:', err);
        that.createDefaultTimeSlots();
      });
    },
    
    // 处理时间段数据
    processTimeData(timeData) {
      console.log('提取的时间段数据:', timeData);
      
      // 设置时间段列表
      if (timeData && timeData.length > 0) {
        // 检查当前时间，用于筛选今天已过期的时间段
        const today = new Date();
        const currentHour = today.getHours();
        const selectedDateIsToday = this.selectedDate === today.toISOString().split('T')[0];
        console.log('处理时间段数据 - 当前小时:', currentHour, '是否今天:', selectedDateIsToday);
        
        // 处理API返回的时间段格式
        this.timeList = timeData.map(item => {
          // 检查是否有disabled字段，如果没有则使用available字段
          let isAvailable = item.disabled !== undefined ? !item.disabled : (item.available !== undefined ? item.available : true);
          
          // 如果是今天，检查时间是否已过期
          if (selectedDateIsToday && isAvailable) {
            // 从时间字符串中提取小时
            let hour = -1;
            if (item.time) {
              const timeParts = item.time.split(':');
              if (timeParts.length > 0) {
                hour = parseInt(timeParts[0]);
              } else if (item.time.includes('-')) {
                // 处理类似 "08:00-09:00" 格式
                const parts = item.time.split('-')[0].split(':');
                if (parts.length > 0) {
                  hour = parseInt(parts[0]);
                }
              }
            }
            
            if (hour >= 0 && hour <= currentHour) {
              isAvailable = false;
              item.reason = '已过期';
              console.log('时间段已过期:', item.time);
            }
          }
          
          // 直接使用接口返回的时间格式，不做转换
          return {
            time: item.time,
            available: isAvailable,
            period: item.period || '',
            reason: item.reason || '不可预约',
            booked_count: item.booked_count || 0,
            max_bookings: item.max_bookings || 999999,
            full_time: item.full_time || ''
          };
        });
        
        console.log('处理后的时间段数据:', this.timeList);
        
        // 检查是否有可用的时间段
        let hasAvailable = false;
        // 默认选择第一个可用的时间段
        if (!this.selectedTime) {
          for (let i = 0; i < this.timeList.length; i++) {
            if (this.timeList[i].available) {
              this.selectedTime = this.timeList[i].time;
              hasAvailable = true;
              console.log('自动选择第一个可用时间段:', this.selectedTime);
              break;
            }
          }
        } else {
          // 检查已选择的时间段是否可用
          const selectedTimeAvailable = this.timeList.some(item => 
            item.time === this.selectedTime && item.available);
            
          if (!selectedTimeAvailable) {
            console.log('已选择的时间段不可用:', this.selectedTime);
            // 尝试选择第一个可用的时间段
            for (let i = 0; i < this.timeList.length; i++) {
              if (this.timeList[i].available) {
                this.selectedTime = this.timeList[i].time;
                hasAvailable = true;
                console.log('重新选择第一个可用时间段:', this.selectedTime);
                break;
              }
            }
          } else {
            hasAvailable = true;
          }
        }
        
        // 如果没有可用时间段，提示用户
        if (!hasAvailable) {
          app.toast('当前日期没有可用的预约时间段，请选择其他日期');
          this.selectedTime = ''; // 清空不可用的时间选择
        }
      } else {
        // 如果没有数据，使用默认时间段
        this.createDefaultTimeSlots();
      }
    },
    
    // 创建默认时间段
    createDefaultTimeSlots() {
      this.timeList = [];
      var today = new Date();
      var currentHour = today.getHours();
      let firstAvailableTime = '';
      
      console.log('创建默认时间段，当前小时:', currentHour);
      console.log('当前选择的日期:', this.selectedDate, '今天日期:', today.toISOString().split('T')[0]);
      
      // 判断选择的日期是否是今天
      const selectedDateIsToday = this.selectedDate === today.toISOString().split('T')[0];
      console.log('选择的日期是否为今天:', selectedDateIsToday);
      
      // 添加默认时间段，从8点到20点，每1小时一个时间段
      for (let i = 8; i < 20; i++) {
        const start = i < 10 ? '0' + i + ':00' : i + ':00';
        const end = (i + 1) < 10 ? '0' + (i + 1) + ':00' : (i + 1) + ':00';
        const timeStr = start + '-' + end;
        
        // 设置时间段
        let period = '';
        if (i < 12) {
          period = '上午';
        } else if (i < 17) {
          period = '下午';
        } else {
          period = '傍晚';
        }
        
        // 今天的过期时间段不可选
        let disabled = false;
        let reason = '';
        
        if (selectedDateIsToday && i <= currentHour) {
          disabled = true;
          reason = '已过期';
          console.log('时间段已过期:', timeStr);
        }
        
        // 记录第一个可用的时间段
        if (!disabled && !firstAvailableTime) {
          firstAvailableTime = timeStr;
          console.log('找到第一个可用时间段:', timeStr);
        }
        
        this.timeList.push({
          time: timeStr,
          available: !disabled,
          period: period,
          reason: reason,
          booked_count: 0,
          max_bookings: 999999,
          full_time: this.selectedDate + ' ' + timeStr
        });
      }
      
      // 默认选择第一个可用的时间段
      if (!this.selectedTime && firstAvailableTime) {
        this.selectedTime = firstAvailableTime;
        console.log('自动选择默认时间段:', this.selectedTime);
      } else {
        console.log('保留已选择的时间段:', this.selectedTime);
      }
      
      console.log('创建的默认时间段:', this.timeList);
    },
    
    // 选择时间段
    selectTime(item) {
      console.log('选择时间段:', item);
      if(!item.available) {
        // 显示不可选原因
        if(item.reason) {
          app.toast(item.reason);
        } else {
          app.toast('该时间段不可预约');
        }
        return;
      }
      this.selectedTime = item.time;
    },
    
    // 提交预约
    submitAppoint() {
      var that = this;
      if(!that.selectedDate || !that.selectedTime) {
        app.error('请选择预约日期和时间');
        return;
      }
      
      // 确保日期格式正确 (YYYY-MM-DD)
      let formattedDate = that.selectedDate;
      if (formattedDate && formattedDate.includes('年')) {
        // 转换中文格式日期 (YYYY年MM月DD日) 为 YYYY-MM-DD
        formattedDate = formattedDate
          .replace('年', '-')
          .replace('月', '-')
          .replace('日', '');
      }
      
      const params = {
        id: that.orderId,
        yydate: formattedDate,
        yytime: that.selectedTime
      };
      
      console.log('提交预约参数:', params);
      
      app.showLoading('提交预约');
      app.post('ApiYuyue/appointTime', params, function(res) {
        app.showLoading(false);
        console.log('预约接口返回:', res);
        
        // 处理不同的返回结构
        let success = false;
        let message = '';
        let needSelectWorker = false;
        
        if (res.status == 1) {
          success = true;
          message = res.msg || '预约成功';
          needSelectWorker = res.needSelectWorker || false;
        } else if (res.code == 200 || res.code == 1) {
          success = true;
          message = res.message || res.msg || '预约成功';
          needSelectWorker = res.needSelectWorker || false;
        }
        
        if (success) {
          app.alert(message, function() {
            // 判断是否需要继续选择服务人员
            if (needSelectWorker) {
              try {
                // 使用完整的路径格式
                console.log('转到选择服务人员页面');
                const url = '/yuyue/selectworker?id=' + that.orderId + '&yydate=' + formattedDate;
                app.goto(url);
                console.log('导航到选择服务人员页面:', url);
              } catch (e) {
                console.error('导航失败:', e);
                app.error('页面跳转失败');
              }
            } else {
              app.goto('/yuyue/orderlist');
            }
          });
        } else {
          // 预约失败，显示错误信息
          app.error(res.msg || res.message || '预约失败');
        }
      });
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.order-info {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.product-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-info image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.info {
  flex: 1;
}

.name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.price {
  font-size: 32rpx;
  font-weight: bold;
}

.order-detail {
  font-size: 24rpx;
  color: #999;
}

.order-detail text {
  display: block;
  margin-bottom: 10rpx;
}

.appoint-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 120rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 日期选择器样式 */
.date-selector {
  margin-bottom: 30rpx;
}

.date-header {
  margin-bottom: 20rpx;
}

.date-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
}

.date-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
  display: block;
}

.date-scroll-container {
  margin: 0 -20rpx;
}

.date-scroll {
  white-space: nowrap;
  padding: 20rpx;
}

.date-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 130rpx;
  background: #f7f7f7;
  border-radius: 12rpx;
  margin-right: 15rpx;
  padding: 10rpx 0;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
  transition: all 0.3s;
}

.date-weekday {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.date-day {
  font-size: 34rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.date-month {
  font-size: 22rpx;
  color: #999;
}

.date-picker-fallback {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.date-picker-fallback text {
  font-size: 24rpx;
  color: #999;
  margin-right: 10rpx;
}

.picker-fallback-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
}

/* 时间选择器样式 */
.time-selector {
  margin-top: 30rpx;
}

.time-header {
  margin-bottom: 20rpx;
}

.time-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
}

.time-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
  display: block;
}

.time-period-tabs {
  display: flex;
  margin-bottom: 20rpx;
  background: #f7f7f7;
  border-radius: 8rpx;
  padding: 6rpx;
}

.time-period-tab {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  border-radius: 6rpx;
  transition: all 0.3s;
}

.time-list {
  display: flex;
  flex-wrap: wrap;
}

.time-item {
  width: 30%;
  height: 90rpx;
  margin: 1.5%;
  border-radius: 8rpx;
  background: #f7f7f7;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
  overflow: hidden;
  transition: all 0.3s;
}

.time-content {
  height: 100%;
  padding: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.time-text {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.2;
}

.time-status {
  font-size: 20rpx;
  color: #999;
  margin-top: 6rpx;
}

.time-item.disabled {
  background: #f0f0f0;
  opacity: 0.8;
}

.time-item.disabled .time-text {
  color: #999;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
}

.selected-info {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  margin-bottom: 15rpx;
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  transition: all 0.3s;
}
</style>
