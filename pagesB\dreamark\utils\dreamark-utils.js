/**
 * 梦想方舟工具类
 * 包含粒子系统、音效管理、数据存储等公共功能
 */

// 粒子系统类
export class ParticleSystem {
	constructor(canvasId, width, height) {
		this.canvasId = canvasId;
		this.width = width;
		this.height = height;
		this.particles = [];
		this.ctx = null;
		this.animationId = null;
		this.isActive = false;
		
		this.init();
	}
	
	// 初始化画布
	init() {
		try {
			this.ctx = uni.createCanvasContext(this.canvasId);
			this.createParticles();
			this.start();
		} catch (e) {
			console.error('粒子系统初始化失败:', e);
		}
	}
	
	// 创建粒子
	createParticles() {
		const particleCount = 50;
		this.particles = [];
		
		for (let i = 0; i < particleCount; i++) {
			this.particles.push({
				x: Math.random() * this.width,
				y: Math.random() * this.height,
				vx: (Math.random() - 0.5) * 2,
				vy: (Math.random() - 0.5) * 2,
				size: Math.random() * 3 + 1,
				opacity: Math.random() * 0.5 + 0.3,
				color: this.getRandomColor()
			});
		}
	}
	
	// 获取随机颜色
	getRandomColor() {
		const colors = [
			'rgba(0, 247, 255, ',
			'rgba(189, 0, 255, ',
			'rgba(255, 0, 200, ',
			'rgba(0, 255, 128, '
		];
		return colors[Math.floor(Math.random() * colors.length)];
	}
	
	// 更新粒子
	updateParticles() {
		this.particles.forEach(particle => {
			particle.x += particle.vx;
			particle.y += particle.vy;
			
			// 边界检测
			if (particle.x < 0 || particle.x > this.width) {
				particle.vx *= -1;
			}
			if (particle.y < 0 || particle.y > this.height) {
				particle.vy *= -1;
			}
			
			// 保持在画布内
			particle.x = Math.max(0, Math.min(this.width, particle.x));
			particle.y = Math.max(0, Math.min(this.height, particle.y));
		});
	}
	
	// 绘制粒子
	drawParticles() {
		if (!this.ctx) return;
		
		// 清空画布
		this.ctx.clearRect(0, 0, this.width, this.height);
		
		// 绘制粒子
		this.particles.forEach(particle => {
			this.ctx.beginPath();
			this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
			this.ctx.fillStyle = particle.color + particle.opacity + ')';
			this.ctx.fill();
		});
		
		// 绘制连接线
		this.drawConnections();
		
		this.ctx.draw();
	}
	
	// 绘制粒子连接线
	drawConnections() {
		const maxDistance = 100;
		
		for (let i = 0; i < this.particles.length; i++) {
			for (let j = i + 1; j < this.particles.length; j++) {
				const dx = this.particles[i].x - this.particles[j].x;
				const dy = this.particles[i].y - this.particles[j].y;
				const distance = Math.sqrt(dx * dx + dy * dy);
				
				if (distance < maxDistance) {
					const opacity = (maxDistance - distance) / maxDistance * 0.3;
					this.ctx.beginPath();
					this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
					this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
					this.ctx.strokeStyle = `rgba(0, 247, 255, ${opacity})`;
					this.ctx.lineWidth = 1;
					this.ctx.stroke();
				}
			}
		}
	}
	
	// 动画循环
	animate() {
		if (!this.isActive) return;
		
		this.updateParticles();
		this.drawParticles();
		
		this.animationId = requestAnimationFrame(() => this.animate());
	}
	
	// 启动动画
	start() {
		this.isActive = true;
		this.animate();
	}
	
	// 停止动画
	stop() {
		this.isActive = false;
		if (this.animationId) {
			cancelAnimationFrame(this.animationId);
			this.animationId = null;
		}
	}
	
	// 激活时空通道效果
	activateTimePortal() {
		// 增加粒子速度和数量
		this.particles.forEach(particle => {
			particle.vx *= 2;
			particle.vy *= 2;
			particle.opacity = Math.min(1, particle.opacity * 1.5);
		});
		
		// 3秒后恢复正常
		setTimeout(() => {
			this.particles.forEach(particle => {
				particle.vx /= 2;
				particle.vy /= 2;
				particle.opacity /= 1.5;
			});
		}, 3000);
	}
	
	// 销毁粒子系统
	destroy() {
		this.stop();
		this.particles = [];
		this.ctx = null;
	}
}

// 打字效果工具类
export class TypewriterEffect {
	constructor(text, callback, speed = 80) {
		this.text = text;
		this.callback = callback;
		this.speed = speed;
		this.index = 0;
		this.timer = null;
		this.isActive = false;
	}
	
	// 开始打字效果
	start() {
		this.isActive = true;
		this.index = 0;
		
		this.timer = setInterval(() => {
			if (this.index < this.text.length && this.isActive) {
				this.callback(this.text.substring(0, this.index + 1), false);
				this.index++;
			} else {
				this.complete();
			}
		}, this.speed);
	}
	
	// 完成打字
	complete() {
		this.stop();
		this.callback(this.text, true);
	}
	
	// 停止打字效果
	stop() {
		this.isActive = false;
		if (this.timer) {
			clearInterval(this.timer);
			this.timer = null;
		}
	}
}

// 音效管理器
export class AudioManager {
	constructor() {
		this.audioContexts = new Map();
		this.isEnabled = true;
	}
	
	// 播放音效
	play(audioPath, options = {}) {
		if (!this.isEnabled) return;
		
		try {
			const audio = uni.createInnerAudioContext();
			audio.src = audioPath;
			audio.volume = options.volume || 1;
			audio.loop = options.loop || false;
			
			audio.onPlay(() => {
				console.log('音效播放开始:', audioPath);
			});
			
			audio.onError((err) => {
				console.error('音效播放失败:', audioPath, err);
			});
			
			audio.onEnded(() => {
				audio.destroy();
			});
			
			audio.play();
			
			// 保存引用以便控制
			if (options.id) {
				this.audioContexts.set(options.id, audio);
			}
			
			return audio;
		} catch (e) {
			console.error('创建音频上下文失败:', e);
		}
	}
	
	// 停止指定音效
	stop(id) {
		const audio = this.audioContexts.get(id);
		if (audio) {
			audio.stop();
			audio.destroy();
			this.audioContexts.delete(id);
		}
	}
	
	// 停止所有音效
	stopAll() {
		this.audioContexts.forEach((audio, id) => {
			audio.stop();
			audio.destroy();
		});
		this.audioContexts.clear();
	}
	
	// 设置音效开关
	setEnabled(enabled) {
		this.isEnabled = enabled;
		if (!enabled) {
			this.stopAll();
		}
	}
	
	// 销毁音效管理器
	destroy() {
		this.stopAll();
	}
}

// 数据存储工具
export class StorageManager {
	// 保存数据
	static save(key, data) {
		try {
			uni.setStorageSync(key, data);
			return true;
		} catch (e) {
			console.error('保存数据失败:', key, e);
			return false;
		}
	}
	
	// 读取数据
	static load(key, defaultValue = null) {
		try {
			const data = uni.getStorageSync(key);
			return data || defaultValue;
		} catch (e) {
			console.error('读取数据失败:', key, e);
			return defaultValue;
		}
	}
	
	// 删除数据
	static remove(key) {
		try {
			uni.removeStorageSync(key);
			return true;
		} catch (e) {
			console.error('删除数据失败:', key, e);
			return false;
		}
	}
	
	// 清空所有数据
	static clear() {
		try {
			uni.clearStorageSync();
			return true;
		} catch (e) {
			console.error('清空数据失败:', e);
			return false;
		}
	}
	
	// 获取存储信息
	static getInfo() {
		try {
			return uni.getStorageInfoSync();
		} catch (e) {
			console.error('获取存储信息失败:', e);
			return null;
		}
	}
}

// 时间工具
export class TimeUtils {
	// 格式化时间
	static formatTime(date = new Date(), format = 'YYYY-MM-DD HH:mm:ss') {
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		const hours = String(date.getHours()).padStart(2, '0');
		const minutes = String(date.getMinutes()).padStart(2, '0');
		const seconds = String(date.getSeconds()).padStart(2, '0');
		
		return format
			.replace('YYYY', year)
			.replace('MM', month)
			.replace('DD', day)
			.replace('HH', hours)
			.replace('mm', minutes)
			.replace('ss', seconds);
	}
	
	// 计算时间差
	static timeDiff(startTime, endTime = new Date()) {
		const diff = endTime.getTime() - startTime.getTime();
		const minutes = Math.floor(diff / 1000 / 60);
		const seconds = Math.floor((diff / 1000) % 60);
		
		return {
			total: diff,
			minutes: minutes,
			seconds: seconds,
			formatted: `${minutes}分${seconds}秒`
		};
	}
}

// 设备信息工具
export class DeviceUtils {
	// 获取系统信息
	static getSystemInfo() {
		try {
			return uni.getSystemInfoSync();
		} catch (e) {
			console.error('获取系统信息失败:', e);
			return null;
		}
	}
	
	// 检查是否为移动设备
	static isMobile() {
		const systemInfo = this.getSystemInfo();
		return systemInfo && (systemInfo.platform === 'android' || systemInfo.platform === 'ios');
	}
	
	// 检查网络状态
	static checkNetwork() {
		return new Promise((resolve) => {
			uni.getNetworkType({
				success: (res) => {
					resolve({
						isConnected: res.networkType !== 'none',
						networkType: res.networkType
					});
				},
				fail: () => {
					resolve({
						isConnected: false,
						networkType: 'unknown'
					});
				}
			});
		});
	}
}

// 权限管理工具
export class PermissionUtils {
	// 检查并请求权限
	static async requestPermission(scope) {
		return new Promise((resolve) => {
			uni.getSetting({
				success: (res) => {
					if (res.authSetting[scope]) {
						resolve(true);
					} else {
						uni.authorize({
							scope: scope,
							success: () => resolve(true),
							fail: () => resolve(false)
						});
					}
				},
				fail: () => resolve(false)
			});
		});
	}
	
	// 检查摄像头权限
	static async checkCameraPermission() {
		return this.requestPermission('scope.camera');
	}
	
	// 检查录音权限
	static async checkRecordPermission() {
		return this.requestPermission('scope.record');
	}
	
	// 检查相册权限
	static async checkAlbumPermission() {
		return this.requestPermission('scope.writePhotosAlbum');
	}
}

// 导出默认配置
export const DreamArkConfig = {
	// 音效文件路径
	audio: {
		startup: '/static/audio/startup.mp3',
		typing: '/static/audio/typing.mp3',
		portal: '/static/audio/portal.mp3',
		celebration: '/static/audio/celebration.mp3',
		selection: '/static/audio/selection.mp3'
	},
	
	// 动画配置
	animation: {
		typingSpeed: 80,
		particleCount: 50,
		fadeInDuration: 500,
		fadeOutDuration: 300
	},
	
	// 存储键名
	storage: {
		dialogueData: 'user_dialogue_data',
		predictedImage: 'predicted_image_url',
		userGender: 'user_gender',
		userProfession: 'user_profession',
		selectedDream: 'selected_dream',
		journeyStartTime: 'journey_start_time',
		voiceChatSettings: 'voice_chat_settings'
	},
	
	// API配置
	api: {
		baseUrl: 'https://api.dreamark.com',
		timeout: 30000
	}
};
