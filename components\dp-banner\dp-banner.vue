<template>
<view class="dp-banner" :style="{
	backgroundColor: params.bgcolor,
	marginTop: shouldShowTop ? '-180rpx' : (params.margin_y*2.2)+'rpx',
	marginRight: (params.margin_x*2.2)+'rpx',
	marginBottom: '0',
	marginLeft: (params.margin_x*2.2)+'rpx',
	padding: (params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	height: bannerHeight,
	width: 'calc(100% - ' + (params.margin_x*2.2*2) + 'rpx)'
}">
	<!-- 背景图 -->
	<view class="banner-background" v-if="params.bgimg" :class="{'is-top': shouldShowTop}">
		<image :src="params.bgimg" mode="aspectFill" class="bg-image"/>
	</view>

	<view class="banner-content" :class="{'is-top': shouldShowTop}">
		<!-- 标题区域 -->
		<view class="banner-header" v-if="params.main_title || params.sub_title">
			<view class="title-box">
				<text class="main-title" v-if="params.main_title" :style="{
					color: params.main_title_color || '#fff'
				}">{{ params.main_title }}</text>
				<text class="sub-title" v-if="params.sub_title" :style="{
					color: params.sub_title_color || 'rgba(255, 255, 255, 0.8)'
				}">{{ params.sub_title }}</text>
			</view>
			<view class="more-link" v-if="params.more_text && params.hrefurl" @click="goto" :data-url="params.hrefurl" :style="{
				color: params.more_text_color || 'rgba(255, 255, 255, 0.9)'
			}">
				{{ params.more_text }} <text class="arrow">></text>
			</view>
		</view>

		<!-- 原有轮播内容 -->
		<block v-if="params.style && params.style==1">
			<view class="sbox">
				<view :style="{height:'40rpx'}"></view>
				<view class="bgswiper" :style="{height:params.height*1.2+'rpx',background:'url('+data[bannerindex].imgurl+')'}"></view>
				<swiper :autoplay="false" @change="bannerchange" :circular="true" displayMultipleItems="1" indicatorActiveColor="white" :indicatorDots="false" nextMargin="80rpx" snapToEdge="true" :style="{height:(params.height*2.2)+'rpx','padding-top':'20rpx'}">
					<swiper-item @click="goto" :data-url="item.hrefurl" class="switem" v-for="(item,index) in data" :key="item.id" :style="{height:(params.height*2.2)+'rpx','padding-top':'20rpx'}">
						<image class="sitem" :class="bannerindex==index?'active':'noactive'" mode="scaleToFill" :src="item.imgurl" :style="{height:(params.height*2.2-20)+'rpx'}"></image>
					</swiper-item>
				</swiper>
			</view> 
		</block>
		<block v-else>
			<swiper class="dp-banner-swiper" :autoplay="true" :indicator-dots="false" :current="0" :style="{height:(params.height*2.2 + 30)+'rpx'}" :interval="params.interval*1000" @change="bannerchange">
				<block v-for="item in data" :key="item.id"> 
					<swiper-item>
						<view :style="{height:(params.height*2.2)+'rpx',borderRadius:(params.borderradius)+'px',overflow:'hidden'}" @click="goto" :data-url="item.hrefurl">
							<image :src="item.imgurl" class="dp-banner-swiper-img" mode="widthFix"/>
						</view>
					</swiper-item>
				</block>
			</swiper>
		</block>
		<view v-if="params.indicatordots=='1'" class="dp-banner-swiper-pagination" :style="{justifyContent:(params.align=='center'?'center':(params.align=='left'?'flex-start':'flex-end')), bottom: params.indicatorBottom || '12px'}">
			<block v-for="(item,index) in data" :key="item.id">
				<block v-if="params.shape==''">
					<view v-if="bannerindex==index" class="dp-banner-swiper-shape0 dp-banner-swiper-shape0-active" :style="{backgroundColor:params.indicatoractivecolor}"></view>
					<view v-else class="dp-banner-swiper-shape0" :style="{backgroundColor:params.indicatorcolor}"></view>
				</block>
				<block v-else-if="params.shape=='shape1'">
					<view v-if="bannerindex==index" class="dp-banner-swiper-shape1" :style="{backgroundColor:params.indicatoractivecolor}"></view>
					<view v-else class="dp-banner-swiper-shape1" :style="{backgroundColor:params.indicatorcolor}"></view> 
				</block>
				<block v-else-if="params.shape=='shape2'">
					<view v-if="bannerindex==index" class="dp-banner-swiper-shape2" :style="{backgroundColor:params.indicatoractivecolor}"></view>
					<view v-else class="dp-banner-swiper-shape2" :style="{backgroundColor:params.indicatorcolor}"></view>
				</block>
				<block v-else-if="params.shape=='shape3'">
					<view v-if="bannerindex==index" class="dp-banner-swiper-shape3" :style="{backgroundColor:params.indicatoractivecolor}"></view>
					<view v-else class="dp-banner-swiper-shape3" :style="{backgroundColor:params.indicatorcolor}"></view>
				</block>
			</block>
		</view>
	</view>
</view>
</template>
<script>
	export default {
		data(){
			return {
				bannerindex: 0,
				platform: ''  // 初始化为空
			}
		},
		computed: {
			shouldShowTop() {
				// #ifdef MP-WEIXIN
				return this.params.istop === '1'
				// #endif
				
				// #ifdef H5
				return false
				// #endif
				
				// 其他平台
				return false
			},
			bannerHeight() {
				// #ifdef MP-WEIXIN
				// 在小程序中增加高度
				return (this.params.height * 2.2 + 30) + 'rpx'
				// #endif
				
				// #ifdef H5
				// H5保持原有高度
				return (this.params.height * 2.2 + 30) + 'rpx'
				// #endif
				
				// 其他平台使用默认高度
				return (this.params.height * 2.2 + 30) + 'rpx'
			}
		},
		mounted() {
			// #ifdef MP-WEIXIN
			this.platform = 'wx'
			// #endif
			
			// #ifdef H5
			this.platform = 'h5'
			// #endif
		},
		props: {
			params:{
				type: Object,
				default: () => ({
					background: '/static/images/banner-bg.png',
					title: '热门推荐',
					subTitle: '输入关键字搜索您感兴趣的商品',
					moreText: '更多',
					moreUrl: '',
					style: '',
					height: 0,
					interval: 5,
					indicatordots: '1',
					indicatorcolor: '#eee',
					indicatoractivecolor: '#fff',
					borderradius: 0,
					align: 'center',
					shape: '',
					main_title: '',
					main_title_color: '',
					sub_title: '',
					sub_title_color: '',
					more_text: '',
					more_text_color: '',
					hrefurl: '',
					istop: '0',
					indicatorBottom: '12px'
				})
			},
			data:{
				type: Array,
				default: () => []
			}
		},
		methods:{
			bannerchange:function(e){
				var that = this
				var idx = e.detail.current;
				that.bannerindex = idx
			},
			goto(e) {
				const url = e.currentTarget.dataset.url;
				if(url) {
					uni.navigateTo({
						url: url
					});
				}
			}
		}
	}
</script>
<style>
.dp-banner {
	position: relative;
	background: #fff;
	overflow: hidden;
	box-sizing: border-box;
}

.banner-background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.bg-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.banner-content {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100%;
}

.dp-banner-swiper {
	position: relative;
	width: 100%;
	height: auto;
	z-index: 2;
	padding-bottom: 30px;
}

.dp-banner-swiper-img {
	width: 100%;
	height: auto;
	display: block; /* 避免图片底部空隙 */
}

.dp-banner-swiper-pagination {
	padding: 0 10px;
	bottom: 12px;
	left: 0;
	position: absolute;
	display: flex;
	justify-content: center;
	width: 100%;
	z-index: 10;
	pointer-events: none; /* 防止指示器遮挡点击事件 */
}

.dp-banner-swiper-shape0 {
	width: 3px;
	height: 3px;
	margin: 0 2px !important;
	border-radius: 1.5px;
}

.dp-banner-swiper-shape0-active {
	width: 13px;
	border-radius: 1.5px;
}

.dp-banner-swiper-shape1 {
	width: 12px;
	height: 6px;
	border-radius: 0;
	margin: 0 2px;
}

.dp-banner-swiper-shape2 {
	width: 8px;
	height: 8px;
	border-radius: 0;
	margin: 0 2px;
}

.dp-banner-swiper-shape3 {
	width: 8px;
	height: 8px;
	border-radius: 50%;
	margin: 0 2px;
}

.dp-banner-swiper-shape4 {
	width: 8px;
	height: 3px;
	border-radius: 50%;
	margin: 0 1px;
}

.dp-banner-swiper-shape4-active {
	width: 13px;
	border-radius: 1.5px;
}

.sbox {
	overflow: hidden;
	position: relative;
}

.switem {
	margin-left: 40rpx;
	width: 526rpx !important;
}

.sitem {
	border-radius: 24rpx;
	overflow: hidden;
	width: 526rpx;
}

.noactive {
	-webkit-transform: scale(.84);
	transform: scale(.84);
	transition: all .2s ease-in 0s;
	z-index: 20;
}

.active {
	-webkit-transform: scale(1.01);
	transform: scale(1.01);
	transition: .5s;
	z-index: 20;
}

.bgswiper {
	-webkit-backdrop-filter: blur(100rpx);
	backdrop-filter: blur(100rpx);
	background-origin: center center;
	background-repeat: no-repeat;
	background-size: cover;
	border-radius: 0 0 30% 30%;
	-webkit-filter: blur(6rpx);
	filter: blur(6rpx);
	left: 0;
	position: absolute;
	right: 0;
	top: 0;
	transition: .2s linear;
	width: 100vw;
}

.banner-header {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 20rpx 30rpx;
	z-index: 3;
}

.title-box {
	display: flex;
	flex-direction: column;
}

.main-title {
	font-size: 36rpx;
	font-weight: bold;
	line-height: 1.2;
	margin-bottom: 8rpx;
}

.sub-title {
	font-size: 24rpx;
	line-height: 1.4;
	margin-top: 6rpx;
}

.more-link {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	margin-top: 16rpx;
}

.more-link .arrow {
	margin-left: 6rpx;
}

.banner-background.is-top {
	top: -20%;
	height: 140%;
}

.banner-content.is-top {
	padding: 180rpx 20rpx 0;
}
</style>