<template>
<view class="container">
	<block v-if="isload">
		<view class="header-container">
			<view class="search-box">
				<input type="text" class="search-input" placeholder="搜索配置名称" v-model="searchKeyword" @confirm="onSearch"/>
				<view class="search-btn" @click="onSearch">
					<text>搜索</text>
				</view>
			</view>
			
			<view class="filter-tabs">
				<view class="tab-item" 
					  v-for="(tab, index) in statusTabs" 
					  :key="index"
					  :class="{active: currentStatus == tab.value}"
					  @click="changeStatus(tab.value)">
					<text>{{tab.label}}</text>
				</view>
			</view>
		</view>
		
		<view class="content-container">
			<scroll-view class="scroll-view" scroll-y="true" @scrolltolower="loadmore" refresher-enabled="true" @refresherrefresh="onRefresh" :refresher-triggered="refreshing">
				<view class="config-list">
					<view class="config-item" v-for="(item, index) in configList" :key="index" @click="toConfigDetail(item.id)">
						<view class="config-header">
							<view class="config-info">
								<text class="config-name">{{item.name}}</text>
								<text class="config-time">创建时间：{{item.createtime_text}}</text>
							</view>
							<view class="status-badge" :class="item.status == 1 ? 'active' : 'inactive'">
								<text>{{getStatusText(item.status)}}</text>
							</view>
						</view>
						
						<view class="config-content">
							<view class="config-desc" v-if="item.description">
								<text>{{item.description}}</text>
							</view>
							
							<view class="config-details">
								<view class="detail-row">
									<view class="detail-item">
										<text class="label">总点位：</text>
										<text class="value" style="color: #FF5722">{{item.total_positions || 0}}</text>
									</view>
									<view class="detail-item">
										<text class="label">财富点位：</text>
										<text class="value" style="color: #FF5722">{{item.wealth_positions || 0}}</text>
									</view>
								</view>
								
								<view class="detail-row">
									<view class="detail-item">
										<text class="label">单数奖励：</text>
										<text class="value">{{item.reward_amount || 0}}</text>
									</view>
									<view class="detail-item">
										<text class="label">奖励类型：</text>
										<text class="value">{{getRewardTypeText(item.reward_type)}}</text>
									</view>
								</view>
								
								<view class="detail-row" v-if="item.auto_claim !== undefined">
									<view class="detail-item">
										<text class="label">领取方式：</text>
										<text class="value">{{item.auto_claim ? '自动领取' : '手动领取'}}</text>
									</view>
									<view class="detail-item" v-if="item.deduct_contribution_rate > 0">
										<text class="label">扣除比例：</text>
										<text class="value">{{item.deduct_contribution_rate}}%</text>
									</view>
								</view>
							</view>
						</view>
						
						<view class="config-actions">
							<view class="action-btn" @click.stop="toActivityProducts(item.id)">
								<text>查看商品</text>
							</view>
							<view class="action-btn" @click.stop="toMyPosition(item.id)">
								<text>我的点位</text>
							</view>
							<view class="action-btn primary" style="background: linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%)"
								  @click.stop="toPositionTree(item.id)">
								<text>排单树</text>
							</view>
						</view>
					</view>
				</view>
				
				<view class="empty-state" v-if="configList.length == 0 && !loading">
					<image src="/static/img/empty-config.png" class="empty-icon"/>
					<view class="empty-text">暂无排单配置</view>
				</view>
				
				<view class="load-more" v-if="configList.length > 0">
					<text v-if="loading">加载中...</text>
					<text v-else-if="!hasMore">没有更多了</text>
					<text v-else>上拉加载更多</text>
				</view>
			</scroll-view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			refreshing: false,
			searchKeyword: '',
			statusTab: [
				{label: '全部', value: -1},
				{label: '启用', value: 1},
				{label: '禁用', value: 0}
			],
			currentStatus: -1,
			configList: [],
			page: 1,
			hasMore: true
		};
	},
	
	computed: {
		statusTab() {
			return [
				{label: '全部', value: -1},
				{label: '启用', value: 1},
				{label: '禁用', value: 0}
			];
		}
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.onRefresh();
	},
	methods: {
		getdata:function(){
			var that = this;
			that.loaded();
			that.getConfigList();
		},
		
		onSearch: function(){
			this.resetList();
			this.getConfigList();
		},
		
		changeStatus: function(status){
			this.currentStatus = status;
			this.resetList();
			this.getConfigList();
		},
		
		resetList: function(){
			this.configList = [];
			this.page = 1;
			this.hasMore = true;
		},
		
		getConfigList: function(){
			var that = this;
			if(that.loading || !that.hasMore) return;
			
			that.loading = true;
			
			var params = {
				page: that.page,
				limit: 10
			};
			
			if(that.searchKeyword){
				params.keyword = that.searchKeyword;
			}
			
			if(that.currentStatus >= 0){
				params.status = that.currentStatus;
			}
			
			app.post('ApiPaidan/getConfigs', params, function (res) {
				that.loading = false;
				that.refreshing = false;
				uni.stopPullDownRefresh();
				
				if(res.code == 1){
					var newList = res.data || [];
					
					if(that.page == 1){
						that.configList = newList;
					}else{
						that.configList = that.configList.concat(newList);
					}
					
					that.hasMore = newList.length >= 10;
					that.page++;
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		loadmore: function(){
			this.getConfigList();
		},
		
		onRefresh: function(){
			this.refreshing = true;
			this.resetList();
			this.getConfigList();
		},
		
		toConfigDetail: function(configId){
			uni.navigateTo({
				url: '/pagesB/paidan/activity-products?config_id=' + configId
			});
		},
		
		toActivityProducts: function(configId){
			uni.navigateTo({
				url: '/pagesB/paidan/activity-products?config_id=' + configId
			});
		},
		
		toMyPosition: function(configId){
			uni.navigateTo({
				url: '/pagesB/paidan/my-position?config_id=' + configId
			});
		},
		
		toPositionTree: function(configId){
			uni.navigateTo({
				url: '/pagesB/paidan/position-tree?config_id=' + configId
			});
		},
		
		getStatusClass: function(status){
			return status == 1 ? 'active' : 'inactive';
		},
		
		getStatusText: function(status){
			return status == 1 ? '启用' : '禁用';
		},
		
		getRewardTypeText: function(type){
			switch(type){
				case 1: return '余额';
				case 2: return '积分';
				case 3: return '贡献值';
				case 4: return '现金券';
				case 5: return '黄积分';
				default: return '未知';
			}
		},
		
		loaded: function () {
			this.isload = true;
		},
		
		getmenuindex: function (e) {
			this.menuindex = e;
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}
.header-container{width:100%;padding:30rpx;background-color:#fff;border-bottom:1px solid #f5f5f5;}
.search-box{display:flex;margin-bottom:20rpx;}
.search-input{flex:1;padding:20rpx;background:#f8f8f8;border-radius:12rpx 0 0 12rpx;font-size:28rpx;border:none;}
.search-btn{padding:20rpx 30rpx;background:#007aff;color:#fff;border-radius:0 12rpx 12rpx 0;font-size:28rpx;text-align:center;}
.filter-tabs{display:flex;}
.tab-item{flex:1;text-align:center;padding:20rpx 0;font-size:28rpx;color:#666;border-bottom:4rpx solid transparent;}
.tab-item.active{color:#007aff;border-bottom-color:#007aff;font-weight:bold;}
.content-container{flex:1;height:100%;overflow:hidden;}
.scroll-view{width:100%;height:100%;}
.config-list{padding:20rpx;}
.config-item{background:#fff;margin-bottom:20rpx;border-radius:16rpx;padding:30rpx;box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);}
.config-header{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20rpx;}
.config-info{flex:1;}
.config-name{font-size:32rpx;color:#333;font-weight:bold;margin-bottom:8rpx;display:block;}
.config-time{font-size:24rpx;color:#999;}
.status-badge{padding:8rpx 16rpx;border-radius:20rpx;font-size:22rpx;}
.status-badge.active{background:#e8f5e8;color:#4caf50;}
.status-badge.inactive{background:#ffebee;color:#f44336;}
.config-content{margin-bottom:20rpx;}
.config-desc{font-size:26rpx;color:#666;margin-bottom:16rpx;line-height:1.5;}
.config-details{}
.detail-row{display:flex;margin-bottom:12rpx;}
.detail-item{flex:1;display:flex;justify-content:space-between;font-size:26rpx;}
.detail-item:not(:last-child){margin-right:30rpx;}
.label{color:#666;}
.value{color:#333;font-weight:bold;}
.config-actions{display:flex;justify-content:space-between;}
.action-btn{flex:1;padding:16rpx 0;margin:0 8rpx;border-radius:8rpx;background:#f8f8f8;color:#666;font-size:26rpx;text-align:center;}
.action-btn:first-child{margin-left:0;}
.action-btn:last-child{margin-right:0;}
.action-btn.primary{color:#fff;}
.empty-state{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:100rpx 0;}
.empty-icon{width:200rpx;height:200rpx;margin-bottom:30rpx;}
.empty-text{font-size:28rpx;color:#666;}
.load-more{text-align:center;padding:30rpx;font-size:24rpx;color:#999;}
</style>