<template>
	<view class="camera-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="bg-grid"></view>
			<view class="bg-particles">
				<view class="particle" v-for="n in 20" :key="n"></view>
			</view>
		</view>

		<!-- 拍摄模式 -->
		<view v-if="currentStep === 'camera'" class="camera-mode">
			<!-- 相机预览区域 -->
			<!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || APP-PLUS -->
			<camera
				v-if="showCamera && !showConfig"
				class="camera-preview"
				device-position="front"
				flash="off"
				@error="onCameraError"
				@initdone="onCameraReady">
			</camera>
			<!-- #endif -->

			<!-- #ifdef H5 -->
			<!-- H5环境下显示替代预览区域 -->
			<view v-if="showCamera && !showConfig" class="camera-preview h5-camera-placeholder">
				<view class="h5-camera-content">
					<view class="camera-icon">📷</view>
					<text class="h5-camera-text">相机预览</text>
					<text class="h5-camera-tip">点击拍照按钮选择图片</text>
				</view>
			</view>
			<!-- #endif -->

			<!-- 摄像头被暂停时的占位符 -->
			<view v-if="showConfig" class="camera-placeholder">
				<view class="placeholder-icon">⏸️</view>
				<text class="placeholder-text">摄像头已暂停</text>
				<text class="placeholder-desc">完成配置后将自动恢复</text>
			</view>

			<!-- 顶部状态栏 -->
			<view class="status-bar">
				<view class="status-left">
					<view class="back-btn" @tap="goBack">
						<text class="back-icon">←</text>
					</view>
				</view>
				<view class="status-center">
					<text class="page-title">AI变脸预测</text>
					<text class="page-subtitle">FACE PREDICTION</text>
				</view>
				<view class="status-right">
					<view class="settings-btn" @tap="forceShowConfig">
						<text class="settings-icon">⚙️</text>
					</view>
				</view>
			</view>

			<!-- 拍摄指导区域 -->
			<view class="guide-overlay">
				<view class="guide-frame">
					<view class="frame-border">
						<view class="frame-corner corner-tl"></view>
						<view class="frame-corner corner-tr"></view>
						<view class="frame-corner corner-bl"></view>
						<view class="frame-corner corner-br"></view>
						<view class="scan-line"></view>
					</view>
					<view class="frame-center">
						<view class="guide-icon">👤</view>
						<text class="guide-text">请将面部置于框内</text>
						<view class="pulse-indicator">
							<view class="pulse-dot"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部控制区域 -->
			<view class="bottom-controls">
				<view class="controls-container">
					<view class="control-item" @tap="chooseImage">
						<view class="control-icon" :style="{borderColor:t('color1')}">
							<text class="icon-text">📁</text>
						</view>
						<text class="control-text">相册</text>
					</view>

					<view class="capture-section">
						<view class="capture-button" @tap="capturePhoto" :class="{disabled: !cameraReady}" :style="{borderColor:t('color1')}">
							<view class="capture-ring" :style="{borderColor:t('color1')}">
								<view class="capture-inner" :style="{background:t('color1')}">
									<view class="capture-dot"></view>
								</view>
							</view>
						</view>
						<text class="capture-text">拍照</text>
					</view>

					<view class="control-item" @tap="switchCamera">
						<view class="control-icon" :style="{borderColor:t('color1')}">
							<text class="icon-text">🔄</text>
						</view>
						<text class="control-text">翻转</text>
					</view>
				</view>
			</view>

			<!-- 摄像头状态提示 -->
			<view class="camera-status-tip" v-if="!cameraReady">
				<view class="status-icon">⚡</view>
				<text class="status-tip-text">{{cameraStatusText}}</text>
			</view>
		</view>

		<!-- 预览模式 -->
		<view v-if="currentStep === 'preview'" class="preview-mode">
			<view class="preview-header">
				<view class="header-left">
					<view class="back-btn" @tap="retakePhoto">
						<text class="back-icon">←</text>
					</view>
				</view>
				<view class="header-center">
					<text class="page-title">预览照片</text>
					<text class="page-subtitle">PHOTO PREVIEW</text>
				</view>
				<view class="header-right">
					<view class="confirm-btn" @tap="startProcessing" :style="{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
						<text class="confirm-text">确认</text>
					</view>
				</view>
			</view>

			<view class="preview-content">
				<view class="preview-container">
					<view class="image-frame" :style="{borderColor:t('color1')}">
						<image
							:src="capturedImageUrl"
							class="preview-image"
							mode="aspectFit"
							@error="onImageError"
							@load="onImageLoad"
							:show-loading="true"
							:lazy-load="false">
						</image>
						<view class="image-overlay" v-if="imageLoaded">
							<view class="scan-effect"></view>
						</view>
						<view class="image-loading" v-if="!imageLoaded">
							<view class="loading-spinner"></view>
							<text class="loading-text">加载中...</text>
						</view>
					</view>
					<view class="preview-info">
						<text class="info-title">照片已准备就绪</text>
						<text class="info-desc">点击确认开始AI分析</text>
						<view class="image-info" v-if="imageLoaded">
							<text class="info-detail">图片尺寸已优化，准备进行AI分析</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 处理中模式 -->
		<view v-if="currentStep === 'processing'" class="processing-mode">
			<view class="processing-content">
				<view class="ai-analysis">
					<view class="ai-brain">
						<view class="brain-core" :style="{background:'radial-gradient(circle, '+t('color1')+', rgba('+t('color1rgb')+',0.3))'}"></view>
						<view class="neural-waves" :style="{borderColor:t('color1')}"></view>
						<view class="neural-waves wave-2" :style="{borderColor:t('color1')}"></view>
						<view class="neural-waves wave-3" :style="{borderColor:t('color1')}"></view>
					</view>
					<view class="processing-steps">
						<view class="step-item" v-for="(step, index) in processingSteps" :key="index" :class="{active: index <= currentProcessStep}">
							<view class="step-icon" :style="{borderColor:t('color1'), background: index <= currentProcessStep ? t('color1') : 'transparent'}">
								<text class="step-text">{{step.icon}}</text>
							</view>
							<text class="step-label" :style="{color: index <= currentProcessStep ? t('color1') : 'rgba(125, 249, 255, 0.5)'}">{{step.text}}</text>
						</view>
					</view>
				</view>
				<view class="processing-info">
					<text class="processing-title" :style="{color:t('color1')}">AI正在分析中...</text>
					<text class="processing-desc">{{processingSteps[currentProcessStep].text}}</text>
					<view class="progress-container">
						<view class="progress-bar">
							<view class="progress-fill" :style="{width: progressPercent + '%', background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"></view>
						</view>
						<text class="progress-text" :style="{color:t('color1')}">{{progressPercent}}%</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 结果模式 -->
		<view v-if="currentStep === 'result'" class="result-mode">
			<view class="result-header">
				<view class="header-left">
					<view class="back-btn" @tap="retakePhoto">
						<text class="back-icon">←</text>
					</view>
				</view>
				<view class="header-center">
					<text class="page-title">预测结果</text>
					<text class="page-subtitle">PREDICTION RESULT</text>
				</view>
				<view class="header-right">
					<view class="share-btn" @tap="shareResult" :style="{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
						<text class="share-text">分享</text>
					</view>
				</view>
			</view>

			<view class="result-content">
				<view class="result-comparison">
					<view class="comparison-container">
						<view class="image-item">
							<view class="image-frame current" :style="{borderColor:t('color1')}">
								<image
									:src="capturedImageUrl"
									class="result-image"
									mode="aspectFit"
									@error="onCurrentImageError"
									@load="onCurrentImageLoad"
									:show-loading="true">
								</image>
								<view class="image-loading" v-if="!currentImageLoaded">
									<view class="loading-spinner"></view>
								</view>
								<view class="image-label">
									<text class="label-text">现在的你</text>
									<text class="label-year">2024</text>
								</view>
							</view>
						</view>
						<view class="transform-arrow">
							<view class="arrow-line" :style="{background:t('color1')}"></view>
							<view class="arrow-head" :style="{borderLeftColor:t('color1')}"></view>
							<text class="arrow-text" :style="{color:t('color1')}">AI预测</text>
						</view>
						<view class="image-item">
							<view class="image-frame future" :style="{borderColor: predictedImageLoaded ? 'rgba(189, 0, 255, 0.8)' : t('color1')}">
								<image
									:src="predictedImageUrl"
									class="result-image"
									mode="aspectFit"
									@error="onPredictedImageError"
									@load="onPredictedImageLoad"
									:show-loading="true"
									v-if="predictedImageUrl">
								</image>
								<view class="image-loading" v-if="!predictedImageLoaded && predictedImageUrl">
									<view class="loading-spinner"></view>
									<text class="loading-text">生成中...</text>
								</view>
								<view class="image-placeholder" v-if="!predictedImageUrl">
									<view class="placeholder-icon">🔮</view>
									<text class="placeholder-text">AI正在生成...</text>
								</view>
								<view class="image-label">
									<text class="label-text">20年后的你</text>
									<text class="label-year">2044</text>
								</view>
								<view class="prediction-badge" v-if="predictedImageLoaded" :style="{background:'linear-gradient(45deg, rgba(189, 0, 255, 0.8), rgba(189, 0, 255, 0.6))'}">
									<text class="badge-text">AI预测</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部固定按钮 -->
			<view class="result-bottom-bar">
				<view class="tech-button-container">
					<!-- 科技装饰线条 -->
					<view class="tech-lines">
						<view class="tech-line left" :style="{background:t('color1')}"></view>
						<view class="tech-line right" :style="{background:t('color1')}"></view>
					</view>

					<!-- 主按钮 -->
					<button class="bottom-chat-btn" @tap="startFutureChat">
						<!-- 按钮背景装饰 -->
						<view class="btn-bg-decoration">
							<view class="bg-grid"></view>
							<view class="bg-particles">
								<view class="particle" v-for="i in 6" :key="i"></view>
							</view>
						</view>

						<!-- 按钮内容 -->
						<view class="btn-content">
							<view class="btn-icon-container">
								<text class="chat-btn-icon">🚀</text>
								<view class="icon-glow" :style="{boxShadow: '0 0 20rpx ' + t('color1')}"></view>
							</view>
							<view class="btn-text-container">
								<text class="chat-btn-text">与未来对话</text>
								<text class="chat-btn-subtitle">FUTURE DIALOGUE</text>
							</view>
							<view class="btn-arrow">
								<text class="arrow-symbol">→</text>
							</view>
						</view>

						<!-- 扫描线效果 -->
						<view class="scan-line" :style="{background:t('color1')}"></view>
					</button>

					<!-- 底部装饰 -->
					<view class="tech-decoration">
						<view class="deco-dot" v-for="i in 5" :key="i" :style="{background:t('color1')}"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 用户信息配置模态框 - 移动端优化版 -->
		<view v-if="showConfig" class="config-modal">
			<view class="config-overlay" @tap="hideConfigModal"></view>
			<view class="config-container-mobile">
				<!-- 简化头部 -->
				<view class="config-header-mobile">
					<view class="header-left">
						<view class="title-icon" :style="{color:t('color1')}">👤</view>
						<text class="config-title-mobile">个人信息</text>
					</view>
					<view class="config-close-mobile" @tap="hideConfigModal">
						<text class="close-text">×</text>
					</view>
				</view>

				<!-- 可滚动内容 -->
				<scroll-view class="config-scroll-mobile" scroll-y="true" :show-scrollbar="false">
					<!-- 简化提示 -->
					<view v-if="showConfigTip" class="config-tip-mobile" :style="{borderColor:t('color1')}">
						<text class="tip-text-mobile">设置基本信息，获得更准确的AI预测</text>
					</view>

					<!-- 性别选择 - 网格布局 -->
					<view class="config-section-mobile">
						<text class="section-title-mobile" :style="{color:t('color1')}">性别选择 *</text>
						<view class="gender-grid-mobile">
							<view v-for="(option, index) in genderOptions" :key="index"
								  v-if="index > 0"
								  class="gender-item-mobile"
								  :class="{active: genderIndex === index}"
								  :style="{
									  borderColor: genderIndex === index ? t('color1') : 'rgba(125, 249, 255, 0.3)',
									  background: genderIndex === index ? 'rgba('+t('color1rgb')+',0.2)' : 'rgba(125, 249, 255, 0.1)'
								  }"
								  @tap="selectGender(index)">
								<view class="gender-icon-mobile">{{index === 1 ? '👨' : index === 2 ? '👩' : '🧑'}}</view>
								<text class="gender-text-mobile" :style="{color: genderIndex === index ? t('color1') : 'rgba(125, 249, 255, 0.8)'}">{{option}}</text>
							</view>
						</view>
					</view>

					<!-- 职业输入 - 紧凑布局 -->
					<view class="config-section-mobile">
						<text class="section-title-mobile" :style="{color:t('color1')}">职业/梦想</text>
						<view class="input-wrapper-mobile">
							<view class="input-container-mobile" :style="{borderColor: userProfession ? t('color1') : 'rgba(125, 249, 255, 0.3)'}">
								<input
									type="text"
									class="profession-input-mobile"
									v-model="userProfession"
									placeholder="如：程序员、医生、教师..."
									placeholder-style="color: rgba(125, 249, 255, 0.5); font-size: 24rpx;"
									confirm-type="done"
									maxlength="20"
									@focus="onInputFocus"
									@blur="onInputBlur"
									@input="onProfessionInput"
									@confirm="onInputConfirm" />
								<view class="input-icon-mobile" :style="{color: userProfession ? t('color1') : 'rgba(125, 249, 255, 0.5)'}">💼</view>
							</view>
							<view class="input-status-mobile" v-if="userProfession">
								<text class="status-text-mobile" :style="{color:t('color1')}">✓ {{userProfession}}</text>
							</view>
						</view>
					</view>

					<!-- 底部间距 -->
					<view class="bottom-spacer-mobile"></view>
				</scroll-view>

				<!-- 固定底部按钮 -->
				<view class="config-actions-mobile">
					<button class="action-btn-mobile secondary" @tap="skipConfig" type="default">
						<text class="btn-text-mobile">跳过</text>
					</button>
					<button class="action-btn-mobile primary" @tap="saveConfig" type="default"
							:style="{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
							:disabled="genderIndex <= 0">
						<text class="btn-icon-mobile">✓</text>
						<text class="btn-text-mobile">确认</text>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentStep: 'camera', // camera, preview, processing, result
			currentStatus: '准备拍照',
			showCamera: false,
			cameraReady: false,
			cameraStatusText: '正在启动摄像头...',
			capturedImageUrl: '',
			predictedImageUrl: '',
			progressPercent: 0,
			currentProcessStep: 0,
			processingSteps: [
				{ icon: '🔍', text: '面部识别' },
				{ icon: '🧠', text: 'AI分析' },
				{ icon: '✨', text: '预测生成' }
			],
			
			// 配置相关
			showConfig: false,
			showConfigTip: false,
			genderIndex: -1,
			genderOptions: ['请选择性别', '男', '女', '其他'],
			userProfession: '',

			// 图片加载状态
			imageLoaded: false,
			currentImageLoaded: false,
			predictedImageLoaded: false,

			// 定时器
			processingTimer: null,
			progressTimer: null,

			// 音频相关
			pre_url: '', // 云资源域名前缀
			processingAudio: null // 处理中音频对象
		}
	},
	
	onLoad() {
		console.log('=== 摄像头页面加载开始 ===');

		// 初始化云资源前缀URL
		const app = getApp();
		this.pre_url = app.globalData.pre_url || '';

		setTimeout(() => {
			this.loadUserConfig();
			this.initCamera();
		}, 500);
	},
	
	onUnload() {
		this.clearTimers();
		this.stopProcessingAudio();
	},
	
	methods: {
		// 加载用户配置
		loadUserConfig() {
			console.log('=== 开始加载用户配置 ===');

			// 尝试加载已保存的配置
			try {
				const savedGender = uni.getStorageSync('user_gender');
				const savedProfession = uni.getStorageSync('user_profession');

				if (savedGender) {
					const genderIndex = this.genderOptions.indexOf(savedGender);
					if (genderIndex > 0) {
						this.genderIndex = genderIndex;
					}
				}

				if (savedProfession) {
					this.userProfession = savedProfession;
				}

				console.log('加载已保存配置:', {
					gender: savedGender,
					profession: savedProfession,
					genderIndex: this.genderIndex
				});
			} catch (e) {
				console.error('加载配置失败:', e);
			}

			this.showConfigTip = true;
			this.showConfig = true;
			console.log('显示配置弹窗');
		},
		
		// 初始化摄像头
		initCamera() {
			console.log('=== 开始初始化摄像头 ===');
			this.showCamera = true;
			this.cameraReady = true;
			this.cameraStatusText = '摄像头已就绪';
			console.log('摄像头状态设置完成');
		},
		
		// 摄像头就绪
		onCameraReady() {
			this.cameraReady = true;
			this.cameraStatusText = '摄像头已就绪';
			this.currentStatus = '准备拍照';
			console.log('摄像头初始化完成');
		},

		// 摄像头错误
		onCameraError(e) {
			console.error('摄像头错误:', e);
			this.cameraStatusText = '摄像头启动失败';
		},
		
		// 拍照
		capturePhoto() {
			if (!this.cameraReady) return;
			console.log('开始拍照');
			
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || APP-PLUS
			const ctx = uni.createCameraContext();
			ctx.takePhoto({
				quality: 'high',
				success: (res) => {
					this.capturedImageUrl = res.tempImagePath;
					this.currentStep = 'preview';
					// 重置图片加载状态
					this.imageLoaded = false;
					this.currentImageLoaded = false;
					this.predictedImageLoaded = false;
					console.log('拍照成功');
				},
				fail: (err) => {
					console.error('拍照失败:', err);
				}
			});
			// #endif
			
			// #ifdef H5
			this.chooseImage();
			// #endif
		},
		
		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.capturedImageUrl = res.tempFilePaths[0];
					this.currentStep = 'preview';
					// 重置图片加载状态
					this.imageLoaded = false;
					this.currentImageLoaded = false;
					this.predictedImageLoaded = false;
					console.log('选择图片成功');
				}
			});
		},
		
		// 切换摄像头
		switchCamera() {
			console.log('切换摄像头');
			// 这里可以实现前后摄像头切换逻辑
		},
		
		// 重新拍照
		retakePhoto() {
			this.currentStep = 'camera';
			this.capturedImageUrl = '';
			this.predictedImageUrl = '';
			// 重置图片加载状态
			this.imageLoaded = false;
			this.currentImageLoaded = false;
			this.predictedImageLoaded = false;
		},

		// 图片加载事件处理
		onImageLoad() {
			this.imageLoaded = true;
			console.log('预览图片加载完成');
		},

		onImageError(e) {
			console.error('预览图片加载失败:', e);
			uni.showToast({
				title: '图片加载失败',
				icon: 'none'
			});
		},

		onCurrentImageLoad() {
			this.currentImageLoaded = true;
			console.log('当前图片加载完成');
		},

		onCurrentImageError(e) {
			console.error('当前图片加载失败:', e);
			this.currentImageLoaded = false;
		},

		onPredictedImageLoad() {
			this.predictedImageLoaded = true;
			console.log('预测图片加载完成');
		},

		onPredictedImageError(e) {
			console.error('预测图片加载失败:', e);
			this.predictedImageLoaded = false;
			uni.showToast({
				title: '预测图片加载失败',
				icon: 'none'
			});
		},
		
		// 开始处理
		startProcessing() {
			this.currentStep = 'processing';
			this.progressPercent = 0;
			this.currentProcessStep = 0;

			// 播放处理中音频
			this.playProcessingAudio();

			// 模拟AI处理过程
			this.simulateAIProcessing();
		},

		// 播放处理中音频
		playProcessingAudio() {
			try {
				// 停止之前的音频
				this.stopProcessingAudio();

				// 创建音频上下文
				this.processingAudio = uni.createInnerAudioContext();

				// 设置音频源（使用云端资源）
				this.processingAudio.src = this.pre_url + '/static/MP3/耐心等待20秒.mp3';

				// 音频播放事件
				this.processingAudio.onPlay(() => {
					console.log('开始播放处理中音频');
				});

				// 音频播放完成事件
				this.processingAudio.onEnded(() => {
					console.log('处理中音频播放完成');
					this.processingAudio.destroy();
					this.processingAudio = null;
				});

				// 音频播放错误事件
				this.processingAudio.onError((res) => {
					console.error('处理中音频播放错误:', res);
					this.processingAudio.destroy();
					this.processingAudio = null;
				});

				// 开始播放
				this.processingAudio.play();

				// 设置定时器，10-15秒后停止播放
				setTimeout(() => {
					this.stopProcessingAudio();
				}, 12000); // 12秒后停止

			} catch (error) {
				console.error('播放处理中音频时发生错误:', error);
			}
		},

		// 停止处理中音频
		stopProcessingAudio() {
			if (this.processingAudio) {
				try {
					this.processingAudio.stop();
					this.processingAudio.destroy();
					this.processingAudio = null;
					console.log('处理中音频已停止');
				} catch (error) {
					console.error('停止处理中音频时发生错误:', error);
				}
			}
		},

		// 模拟AI处理过程
		simulateAIProcessing() {
			// 处理步骤动画
			const stepInterval = setInterval(() => {
				if (this.currentProcessStep < this.processingSteps.length - 1) {
					this.currentProcessStep++;
				}
			}, 1500);

			// 进度条动画
			this.processingTimer = setInterval(() => {
				this.progressPercent += 1;
				if (this.progressPercent >= 100) {
					clearInterval(stepInterval);
					this.completeProcessing();
				}
			}, 50);
		},

		// 完成处理
		completeProcessing() {
			this.clearTimers();

			// 停止处理中音频
			this.stopProcessingAudio();

			// 调用AI接口生成20年后的图片
			this.generateFutureImage();
		},

		// 生成20年后的图片
		generateFutureImage() {
			// 重置预测图片加载状态
			this.predictedImageLoaded = false;

			// 这里应该调用实际的AI接口
			// 目前先模拟生成过程
			uni.showLoading({
				title: '正在生成预测图片...'
			});

			// 模拟API调用延迟，让用户看到生成过程
			setTimeout(() => {
				uni.hideLoading();

				// 实际项目中，这里应该是从AI接口返回的图片URL
				// 现在先使用原图作为示例，实际使用时替换为AI生成的图片URL
				// 注意：在实际项目中，这里应该是AI生成的20年后图片的URL
				this.predictedImageUrl = this.capturedImageUrl;

				// 如果有实际的AI接口，应该调用：
				// this.callAIAPI();

				// 切换到结果页面
				this.currentStep = 'result';

				// 等待一下再显示成功提示，让用户看到图片加载过程
				setTimeout(() => {
					uni.showToast({
						title: '预测完成！',
						icon: 'success'
					});
				}, 500);
			}, 2000); // 增加延迟时间，让用户感受到AI处理过程
		},

		// 调用AI接口（示例方法）
		callAIAPI() {
			// 获取用户配置信息
			const userGender = uni.getStorageSync('user_gender') || '未设置';
			const userProfession = uni.getStorageSync('user_profession') || '未设置';

			// 准备请求参数
			const requestData = {
				image: this.capturedImageUrl,
				gender: userGender,
				profession: userProfession,
				years: 20 // 预测20年后
			};

			// 实际的API调用
			uni.request({
				url: 'https://your-ai-api-endpoint.com/predict-future', // 替换为实际的AI接口地址
				method: 'POST',
				data: requestData,
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					if (res.data && res.data.success) {
						// 设置生成的20年后图片URL
						this.predictedImageUrl = res.data.predictedImageUrl;
						this.currentStep = 'result';

						uni.showToast({
							title: '预测完成！',
							icon: 'success'
						});
					} else {
						this.handleAPIError(res.data.message || '预测失败');
					}
				},
				fail: (error) => {
					this.handleAPIError('网络请求失败');
				}
			});
		},

		// 处理API错误
		handleAPIError(message) {
			uni.showModal({
				title: '预测失败',
				content: message + '，是否重试？',
				success: (res) => {
					if (res.confirm) {
						this.startProcessing();
					} else {
						this.currentStep = 'preview';
					}
				}
			});
		},
		
		// 与未来对话
		startFutureChat() {
			// 跳转到梦想方舟主页面进行对话
			uni.navigateTo({
				url: '/pagesB/dreamark/index'
			});
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		},
		
		// 配置相关方法
		selectGender(index) {
			if (index === 0) return;
			this.genderIndex = index;
		},
		
		onInputFocus() {
			console.log('职业输入框获得焦点');
		},

		onInputBlur() {
			console.log('职业输入框失去焦点');
		},

		onProfessionInput(e) {
			this.userProfession = e.detail.value;
			console.log('职业输入内容:', this.userProfession);
		},

		onInputConfirm(e) {
			this.userProfession = e.detail.value;
			console.log('职业输入确认:', this.userProfession);
			// 可以在这里添加输入验证逻辑
		},
		
		saveConfig() {
			// 验证性别选择
			if (this.genderIndex <= 0) {
				uni.showToast({
					title: '请选择性别',
					icon: 'none'
				});
				return;
			}

			// 验证职业输入（可选，但建议填写）
			if (!this.userProfession || this.userProfession.trim() === '') {
				uni.showModal({
					title: '提示',
					content: '建议填写职业信息以获得更准确的预测结果，是否继续？',
					success: (res) => {
						if (res.confirm) {
							this.doSaveConfig();
						}
					}
				});
				return;
			}

			this.doSaveConfig();
		},

		doSaveConfig() {
			try {
				const profession = this.userProfession.trim();
				uni.setStorageSync('user_gender', this.genderOptions[this.genderIndex]);
				uni.setStorageSync('user_profession', profession);

				console.log('保存配置:', {
					gender: this.genderOptions[this.genderIndex],
					profession: profession
				});

				uni.showToast({
					title: '配置已保存',
					icon: 'success'
				});

				this.hideConfigModal();
			} catch (e) {
				console.error('保存配置失败:', e);
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'error'
				});
			}
		},

		// 跳过配置
		skipConfig() {
			uni.showModal({
				title: '跳过配置',
				content: '跳过配置可能影响AI预测准确性，确定要跳过吗？',
				success: (res) => {
					if (res.confirm) {
						// 设置默认值
						uni.setStorageSync('user_gender', '未设置');
						uni.setStorageSync('user_profession', '未设置');

						uni.showToast({
							title: '已跳过配置',
							icon: 'none'
						});

						this.hideConfigModal();
					}
				}
			});
		},
		
		hideConfigModal() {
			if (this.showConfigTip && (this.genderIndex <= 0)) {
				uni.showToast({
					title: '请先选择性别信息',
					icon: 'none'
				});
				return;
			}
			this.showConfig = false;
			this.showConfigTip = false;
		},
		
		// 强制显示配置
		forceShowConfig() {
			this.showConfig = true;
			this.showConfigTip = true;
		},
		
		// 清除定时器
		clearTimers() {
			if (this.processingTimer) {
				clearInterval(this.processingTimer);
				this.processingTimer = null;
			}
			if (this.progressTimer) {
				clearInterval(this.progressTimer);
				this.progressTimer = null;
			}
		}
	}
}
</script>

<style scoped>
/* 基础容器 */
.camera-container {
	width: 100vw;
	height: 100vh;
	position: relative;
	overflow: hidden;
	background: linear-gradient(135deg, #0a0a2a 0%, #1a1a3a 50%, #2a2a4a 100%);
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 背景装饰 */
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	pointer-events: none;
}

.bg-grid {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		linear-gradient(rgba(125, 249, 255, 0.1) 1px, transparent 1px),
		linear-gradient(90deg, rgba(125, 249, 255, 0.1) 1px, transparent 1px);
	background-size: 50rpx 50rpx;
	animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(50rpx, 50rpx); }
}

.bg-particles {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.particle {
	position: absolute;
	width: 4rpx;
	height: 4rpx;
	background: rgba(125, 249, 255, 0.6);
	border-radius: 50%;
	animation: particleFloat 8s infinite linear;
}

.particle:nth-child(odd) {
	animation-delay: -2s;
}

.particle:nth-child(3n) {
	animation-delay: -4s;
}

@keyframes particleFloat {
	0% {
		transform: translateY(100vh) scale(0);
		opacity: 0;
	}
	10% {
		opacity: 1;
	}
	90% {
		opacity: 1;
	}
	100% {
		transform: translateY(-100rpx) scale(1);
		opacity: 0;
	}
}

/* 模式容器 */
.camera-mode,
.preview-mode,
.processing-mode,
.result-mode {
	width: 100%;
	height: 100%;
	position: relative;
	z-index: 1;
}

/* 相机预览区域 */
.camera-preview {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	border-radius: 0;
}

/* H5摄像头占位符 */
.h5-camera-placeholder {
	background: linear-gradient(135deg, rgba(26, 26, 58, 0.8) 0%, rgba(42, 42, 74, 0.8) 100%);
	backdrop-filter: blur(20rpx);
	display: flex;
	align-items: center;
	justify-content: center;
}

.h5-camera-content {
	text-align: center;
	padding: 60rpx;
}

.camera-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	display: block;
	opacity: 0.8;
}

.h5-camera-text {
	font-size: 36rpx;
	color: #7df9ff;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.h5-camera-tip {
	font-size: 26rpx;
	color: rgba(125, 249, 255, 0.7);
	display: block;
	line-height: 1.4;
}

/* 摄像头占位符 */
.camera-placeholder {
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(20rpx);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 30rpx;
}

.placeholder-icon {
	font-size: 80rpx;
	opacity: 0.8;
	margin-bottom: 20rpx;
}

.placeholder-text {
	font-size: 36rpx;
	color: #7df9ff;
	font-weight: bold;
}

.placeholder-desc {
	font-size: 26rpx;
	color: rgba(125, 249, 255, 0.7);
	text-align: center;
	line-height: 1.4;
}

/* 顶部状态栏 */
.status-bar {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 140rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 50rpx 40rpx 20rpx;
	background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 70%, transparent 100%);
	backdrop-filter: blur(20rpx);
	z-index: 999;
}

.status-left,
.status-right {
	width: 80rpx;
	display: flex;
	justify-content: center;
}

.status-center {
	flex: 1;
	text-align: center;
}

.back-btn,
.settings-btn {
	width: 80rpx;
	height: 80rpx;
	background: rgba(125, 249, 255, 0.1);
	border: 2rpx solid rgba(125, 249, 255, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	backdrop-filter: blur(10rpx);
}

.back-btn:active,
.settings-btn:active {
	transform: scale(0.9);
	background: rgba(125, 249, 255, 0.2);
	border-color: rgba(125, 249, 255, 0.5);
}

.back-icon,
.settings-icon {
	font-size: 32rpx;
	color: #7df9ff;
	font-weight: bold;
}

.page-title {
	font-size: 36rpx;
	color: #fff;
	font-weight: bold;
	margin-bottom: 5rpx;
	display: block;
}

.page-subtitle {
	font-size: 22rpx;
	color: rgba(125, 249, 255, 0.7);
	font-weight: normal;
	letter-spacing: 2rpx;
	display: block;
}

/* 拍摄指导区域 */
.guide-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 100;
}

.guide-frame {
	width: 500rpx;
	height: 500rpx;
	position: relative;
}

.frame-border {
	width: 100%;
	height: 100%;
	position: relative;
}

.frame-corner {
	position: absolute;
	width: 80rpx;
	height: 80rpx;
	border: 4rpx solid #00f7ff;
	border-radius: 8rpx;
	animation: cornerGlow 2s infinite alternate;
}

@keyframes cornerGlow {
	0% {
		border-color: #00f7ff;
		box-shadow: 0 0 10rpx rgba(0, 247, 255, 0.3);
	}
	100% {
		border-color: rgba(0, 247, 255, 0.8);
		box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.6);
	}
}

.corner-tl {
	top: 0;
	left: 0;
	border-right: none;
	border-bottom: none;
}

.corner-tr {
	top: 0;
	right: 0;
	border-left: none;
	border-bottom: none;
}

.corner-bl {
	bottom: 0;
	left: 0;
	border-right: none;
	border-top: none;
}

.corner-br {
	bottom: 0;
	right: 0;
	border-left: none;
	border-top: none;
}

.scan-line {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 4rpx;
	background: linear-gradient(90deg, transparent 0%, #00f7ff 50%, transparent 100%);
	animation: scanMove 3s infinite;
}

@keyframes scanMove {
	0% { transform: translateY(0); opacity: 1; }
	50% { opacity: 0.5; }
	100% { transform: translateY(500rpx); opacity: 1; }
}

.frame-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
}

.guide-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
	display: block;
	opacity: 0.8;
}

.guide-text {
	font-size: 28rpx;
	color: #00f7ff;
	font-weight: bold;
	display: block;
	margin-bottom: 30rpx;
	text-shadow: 0 0 10rpx rgba(0, 247, 255, 0.5);
}

.pulse-indicator {
	display: flex;
	justify-content: center;
}

.pulse-dot {
	width: 20rpx;
	height: 20rpx;
	background: #00f7ff;
	border-radius: 50%;
	animation: pulse 2s infinite;
	box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.8);
}

@keyframes pulse {
	0%, 100% {
		opacity: 1;
		transform: scale(1);
		box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.8);
	}
	50% {
		opacity: 0.5;
		transform: scale(1.5);
		box-shadow: 0 0 30rpx rgba(0, 247, 255, 1);
	}
}

/* 底部控制区域 */
.bottom-controls {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 240rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 70%, transparent 100%);
	backdrop-filter: blur(20rpx);
	z-index: 999;
}

.controls-container {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	max-width: 600rpx;
}

.control-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
}

.control-icon {
	width: 80rpx;
	height: 80rpx;
	background: rgba(125, 249, 255, 0.1);
	border: 2rpx solid rgba(125, 249, 255, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	backdrop-filter: blur(10rpx);
}

.control-item:active .control-icon {
	transform: scale(0.9);
	background: rgba(125, 249, 255, 0.2);
	border-color: rgba(125, 249, 255, 0.5);
}

.icon-text {
	font-size: 32rpx;
}

.control-text {
	font-size: 24rpx;
	color: rgba(125, 249, 255, 0.8);
	font-weight: 500;
}

/* 拍照区域 */
.capture-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
}

.capture-button {
	width: 140rpx;
	height: 140rpx;
	background: rgba(125, 249, 255, 0.1);
	border: 3rpx solid rgba(125, 249, 255, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	backdrop-filter: blur(10rpx);
	position: relative;
}

.capture-button:active {
	transform: scale(0.95);
}

.capture-button.disabled {
	opacity: 0.5;
}

.capture-ring {
	width: 120rpx;
	height: 120rpx;
	border: 2rpx solid rgba(125, 249, 255, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: ringRotate 3s linear infinite;
}

@keyframes ringRotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.capture-inner {
	width: 100rpx;
	height: 100rpx;
	background: rgba(125, 249, 255, 0.8);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 0 20rpx rgba(125, 249, 255, 0.5);
}

.capture-dot {
	width: 80rpx;
	height: 80rpx;
	background: #fff;
	border-radius: 50%;
	box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
}

.capture-text {
	font-size: 24rpx;
	color: rgba(125, 249, 255, 0.8);
	font-weight: 500;
}

/* 摄像头状态提示 */
.camera-status-tip {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(20rpx);
	padding: 30rpx 50rpx;
	border-radius: 50rpx;
	border: 2rpx solid rgba(125, 249, 255, 0.3);
	z-index: 100;
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.status-icon {
	font-size: 32rpx;
	animation: statusPulse 1.5s infinite;
}

@keyframes statusPulse {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.5; }
}

.status-tip-text {
	font-size: 28rpx;
	color: #7df9ff;
	text-align: center;
	font-weight: 500;
}

/* 预览模式和结果模式头部 */
.preview-header,
.result-header {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 140rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 50rpx 40rpx 20rpx;
	background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 70%, transparent 100%);
	backdrop-filter: blur(20rpx);
	z-index: 999;
}

.header-left,
.header-right {
	width: 120rpx;
	display: flex;
	justify-content: center;
}

.header-center {
	flex: 1;
	text-align: center;
}

.confirm-btn,
.share-btn {
	background: linear-gradient(45deg, #00f7ff, rgba(0, 247, 255, 0.8));
	color: #fff;
	padding: 20rpx 35rpx;
	border-radius: 50rpx;
	font-size: 26rpx;
	font-weight: bold;
	border: none;
	box-shadow: 0 4rpx 15rpx rgba(0, 247, 255, 0.3);
	transition: all 0.3s ease;
}

.confirm-btn:active,
.share-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 10rpx rgba(0, 247, 255, 0.5);
}

.confirm-text,
.share-text {
	color: #fff;
	font-weight: bold;
}

/* 预览内容 */
.preview-content {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 140rpx 40rpx 40rpx;
}

.preview-container {
	width: 100%;
	max-width: 600rpx;
	text-align: center;
}

.image-frame {
	position: relative;
	border: 3rpx solid rgba(125, 249, 255, 0.5);
	border-radius: 30rpx;
	overflow: hidden;
	margin-bottom: 40rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 247, 255, 0.2);
}

.preview-image {
	width: 100%;
	height: auto;
	display: block;
}

.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

.scan-effect {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 4rpx;
	background: linear-gradient(90deg, transparent 0%, #00f7ff 50%, transparent 100%);
	animation: scanPreview 2s infinite;
}

@keyframes scanPreview {
	0% { transform: translateY(0); opacity: 1; }
	100% { transform: translateY(500rpx); opacity: 0; }
}

.preview-info {
	text-align: center;
}

.info-title {
	font-size: 32rpx;
	color: #7df9ff;
	font-weight: bold;
	margin-bottom: 15rpx;
	display: block;
}

.info-desc {
	font-size: 26rpx;
	color: rgba(125, 249, 255, 0.7);
	display: block;
	margin-bottom: 20rpx;
}

.info-detail {
	font-size: 22rpx;
	color: rgba(125, 249, 255, 0.6);
	display: block;
}

/* 图片加载状态 */
.image-loading {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(5rpx);
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid rgba(125, 249, 255, 0.3);
	border-top: 4rpx solid #7df9ff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 24rpx;
	color: #7df9ff;
	text-align: center;
}

/* 图片占位符 */
.image-placeholder {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.5);
	backdrop-filter: blur(10rpx);
}

.placeholder-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
	opacity: 0.8;
	animation: float 3s ease-in-out infinite;
}

@keyframes float {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-10rpx); }
}

.placeholder-text {
	font-size: 26rpx;
	color: rgba(125, 249, 255, 0.8);
	text-align: center;
}

/* 处理中模式 */
.processing-content {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 40rpx;
}

.ai-analysis {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 80rpx;
}

.ai-brain {
	width: 200rpx;
	height: 200rpx;
	position: relative;
	margin-bottom: 60rpx;
}

.brain-core {
	width: 100%;
	height: 100%;
	background: radial-gradient(circle, #00f7ff, rgba(0, 247, 255, 0.3));
	border-radius: 50%;
	animation: brainPulse 2s infinite;
	box-shadow: 0 0 40rpx rgba(0, 247, 255, 0.5);
}

.neural-waves {
	position: absolute;
	top: -20rpx;
	left: -20rpx;
	right: -20rpx;
	bottom: -20rpx;
	border: 2rpx solid rgba(125, 249, 255, 0.6);
	border-radius: 50%;
	animation: waveExpand 3s infinite;
}

.wave-2 {
	top: -40rpx;
	left: -40rpx;
	right: -40rpx;
	bottom: -40rpx;
	animation-delay: -1s;
}

.wave-3 {
	top: -60rpx;
	left: -60rpx;
	right: -60rpx;
	bottom: -60rpx;
	animation-delay: -2s;
}

@keyframes brainPulse {
	0%, 100% {
		transform: scale(1);
		opacity: 1;
		box-shadow: 0 0 40rpx rgba(0, 247, 255, 0.5);
	}
	50% {
		transform: scale(1.1);
		opacity: 0.8;
		box-shadow: 0 0 60rpx rgba(0, 247, 255, 0.8);
	}
}

@keyframes waveExpand {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.5);
		opacity: 0;
	}
}

.processing-steps {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 40rpx;
	margin-bottom: 40rpx;
	flex-wrap: nowrap;
	width: 100%;
	max-width: 600rpx;
}

.step-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
	flex: 1;
	min-width: 120rpx;
	max-width: 150rpx;
}

.step-icon {
	width: 60rpx;
	height: 60rpx;
	border: 2rpx solid rgba(125, 249, 255, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.step-item.active .step-icon {
	border-color: #00f7ff;
	background: rgba(0, 247, 255, 0.2);
	box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.5);
}

.step-text {
	font-size: 24rpx;
}

.step-label {
	font-size: 22rpx;
	text-align: center;
	transition: color 0.3s ease;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.8);
	font-weight: 500;
}

.step-item.active .step-label {
	color: #00f7ff;
	text-shadow: 0 0 15rpx rgba(0, 247, 255, 0.8);
	font-weight: bold;
}

.processing-info {
	text-align: center;
	width: 100%;
}

.processing-title {
	font-size: 36rpx;
	color: #00f7ff;
	font-weight: bold;
	margin-bottom: 20rpx;
	text-shadow: 0 0 20rpx rgba(0, 247, 255, 0.5);
}

.processing-desc {
	font-size: 28rpx;
	color: rgba(125, 249, 255, 0.8);
	margin-bottom: 50rpx;
}

.progress-container {
	width: 100%;
	max-width: 500rpx;
	margin: 0 auto;
}

.progress-bar {
	width: 100%;
	height: 12rpx;
	background: rgba(125, 249, 255, 0.2);
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
	box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.3);
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #00f7ff, rgba(0, 247, 255, 0.8));
	border-radius: 6rpx;
	transition: width 0.3s ease;
	box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.5);
}

.progress-text {
	font-size: 24rpx;
	color: #00f7ff;
	font-weight: bold;
	text-align: center;
}

/* 结果模式内容 */
.result-content {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 140rpx 40rpx 200rpx;
}

.result-comparison {
	width: 100%;
	margin-bottom: 60rpx;
}

.comparison-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 20rpx;
	flex-wrap: nowrap;
	width: 100%;
	max-width: 700rpx;
	margin: 0 auto;
}

.image-item {
	text-align: center;
	flex: 1;
	min-width: 250rpx;
	max-width: 300rpx;
}

.image-frame {
	position: relative;
	border: 3rpx solid rgba(125, 249, 255, 0.5);
	border-radius: 30rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 247, 255, 0.2);
	transition: all 0.3s ease;
}

.image-frame.current {
	border-color: rgba(125, 249, 255, 0.8);
	box-shadow: 0 15rpx 40rpx rgba(0, 247, 255, 0.3);
}

.image-frame.future {
	border-color: rgba(189, 0, 255, 0.8);
	box-shadow: 0 15rpx 40rpx rgba(189, 0, 255, 0.3);
}

.result-image {
	width: 100%;
	height: auto;
	display: block;
}

.image-label {
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(10rpx);
	padding: 15rpx 25rpx;
	border-radius: 25rpx;
	margin: 0 20rpx 20rpx;
}

.label-text {
	font-size: 26rpx;
	color: #7df9ff;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.label-year {
	font-size: 22rpx;
	color: rgba(125, 249, 255, 0.7);
	display: block;
}

.transform-arrow {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	margin: 0 20rpx;
	position: relative;
}

.arrow-line {
	width: 60rpx;
	height: 4rpx;
	background: linear-gradient(90deg, #00f7ff, #bd00ff);
	border-radius: 2rpx;
}

.arrow-head {
	width: 0;
	height: 0;
	border-left: 15rpx solid #bd00ff;
	border-top: 10rpx solid transparent;
	border-bottom: 10rpx solid transparent;
}

.arrow-text {
	font-size: 22rpx;
	color: #ffffff;
	font-weight: bold;
	letter-spacing: 1rpx;
	text-shadow: 0 0 15rpx rgba(0, 247, 255, 0.8);
	background: rgba(0, 0, 0, 0.6);
	padding: 8rpx 15rpx;
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(0, 247, 255, 0.3);
}

/* 底部固定按钮区域 */
.result-bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30rpx 40rpx;
	padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
	background: linear-gradient(180deg, rgba(10, 10, 42, 0.8) 0%, rgba(5, 5, 25, 0.95) 100%);
	backdrop-filter: blur(25rpx);
	border-top: 2rpx solid rgba(125, 249, 255, 0.3);
	z-index: 1000;
	overflow: hidden;
}

.result-bottom-bar::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2rpx;
	background: linear-gradient(90deg, transparent 0%, rgba(0, 247, 255, 0.8) 50%, transparent 100%);
	animation: topGlow 3s ease-in-out infinite;
}

@keyframes topGlow {
	0%, 100% { opacity: 0.3; }
	50% { opacity: 1; }
}

/* 科技按钮容器 */
.tech-button-container {
	position: relative;
	width: 100%;
}

/* 科技装饰线条 */
.tech-lines {
	position: absolute;
	top: -15rpx;
	left: 0;
	right: 0;
	height: 2rpx;
	display: flex;
	justify-content: space-between;
	z-index: 1;
}

.tech-line {
	width: 30%;
	height: 2rpx;
	opacity: 0.8;
	animation: techLinePulse 2s ease-in-out infinite;
}

.tech-line.left {
	animation-delay: 0s;
}

.tech-line.right {
	animation-delay: 1s;
}

@keyframes techLinePulse {
	0%, 100% {
		opacity: 0.3;
		transform: scaleX(0.8);
	}
	50% {
		opacity: 1;
		transform: scaleX(1);
	}
}

/* 主按钮 */
.bottom-chat-btn {
	position: relative;
	width: 100%;
	padding: 0 !important;
	border-radius: 15rpx !important;
	border: 2rpx solid rgba(0, 247, 255, 0.4) !important;
	background: linear-gradient(135deg, rgba(0, 247, 255, 0.1) 0%, rgba(0, 150, 255, 0.2) 100%) !important;
	backdrop-filter: blur(15rpx);
	overflow: hidden;
	transition: all 0.4s ease;
	box-shadow:
		0 8rpx 32rpx rgba(0, 247, 255, 0.3),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

/* 按钮背景装饰 */
.btn-bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	overflow: hidden;
	pointer-events: none;
}

.bg-grid {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image:
		linear-gradient(rgba(0, 247, 255, 0.1) 1rpx, transparent 1rpx),
		linear-gradient(90deg, rgba(0, 247, 255, 0.1) 1rpx, transparent 1rpx);
	background-size: 20rpx 20rpx;
	animation: gridMove 4s linear infinite;
}

.bg-particles {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.particle {
	position: absolute;
	width: 4rpx;
	height: 4rpx;
	background: rgba(0, 247, 255, 0.6);
	border-radius: 50%;
	animation: particleFloat 3s ease-in-out infinite;
}

.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 20%; animation-delay: 0.5s; }
.particle:nth-child(3) { top: 30%; left: 70%; animation-delay: 1s; }
.particle:nth-child(4) { top: 80%; left: 60%; animation-delay: 1.5s; }
.particle:nth-child(5) { top: 40%; left: 90%; animation-delay: 2s; }
.particle:nth-child(6) { top: 70%; left: 40%; animation-delay: 2.5s; }

@keyframes particleFloat {
	0%, 100% {
		opacity: 0.3;
		transform: translateY(0) scale(1);
	}
	50% {
		opacity: 1;
		transform: translateY(-10rpx) scale(1.2);
	}
}

/* 按钮内容 */
.btn-content {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx 40rpx;
	z-index: 2;
}

.btn-icon-container {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.chat-btn-icon {
	font-size: 28rpx;
	z-index: 2;
}

.icon-glow {
	position: absolute;
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	animation: iconGlow 2s ease-in-out infinite;
}

@keyframes iconGlow {
	0%, 100% {
		opacity: 0.3;
		transform: scale(0.8);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.2);
	}
}

.btn-text-container {
	flex: 1;
	text-align: center;
	margin: 0 20rpx;
}

.chat-btn-text {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(0, 247, 255, 0.8);
	margin-bottom: 3rpx;
}

.chat-btn-subtitle {
	display: block;
	font-size: 18rpx;
	color: rgba(0, 247, 255, 0.8);
	font-weight: 300;
	letter-spacing: 2rpx;
}

.btn-arrow {
	display: flex;
	align-items: center;
	justify-content: center;
}

.arrow-symbol {
	font-size: 24rpx;
	color: rgba(0, 247, 255, 0.9);
	animation: arrowMove 1.5s ease-in-out infinite;
}

@keyframes arrowMove {
	0%, 100% { transform: translateX(0); }
	50% { transform: translateX(8rpx); }
}

/* 扫描线效果 */
.scan-line {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent 0%, rgba(0, 247, 255, 0.3) 50%, transparent 100%);
	animation: scanMove 3s ease-in-out infinite;
	z-index: 1;
}

@keyframes scanMove {
	0% { left: -100%; }
	50% { left: 100%; }
	100% { left: -100%; }
}

/* 底部装饰 */
.tech-decoration {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 15rpx;
	margin-top: 15rpx;
}

.deco-dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	animation: dotPulse 2s ease-in-out infinite;
}

.deco-dot:nth-child(1) { animation-delay: 0s; }
.deco-dot:nth-child(2) { animation-delay: 0.2s; }
.deco-dot:nth-child(3) { animation-delay: 0.4s; }
.deco-dot:nth-child(4) { animation-delay: 0.6s; }
.deco-dot:nth-child(5) { animation-delay: 0.8s; }

@keyframes dotPulse {
	0%, 100% {
		opacity: 0.3;
		transform: scale(0.8);
	}
	50% {
		opacity: 1;
		transform: scale(1.2);
	}
}

/* 按钮交互效果 */
.bottom-chat-btn::after {
	border: none !important;
}

.bottom-chat-btn:active {
	transform: scale(0.98);
	box-shadow:
		0 4rpx 16rpx rgba(0, 247, 255, 0.4),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

.bottom-chat-btn:hover {
	border-color: rgba(0, 247, 255, 0.8);
	box-shadow:
		0 12rpx 40rpx rgba(0, 247, 255, 0.5),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

.action-btn::after {
	border: none !important;
}

.action-btn:active {
	transform: scale(0.95);
}

.action-btn.primary {
	background: linear-gradient(45deg, #00f7ff, rgba(0, 247, 255, 0.8)) !important;
	color: #fff !important;
	box-shadow: 0 8rpx 25rpx rgba(0, 247, 255, 0.4);
}

.action-btn.secondary {
	background: rgba(125, 249, 255, 0.1) !important;
	color: rgba(125, 249, 255, 0.9) !important;
	border: 2rpx solid rgba(125, 249, 255, 0.3) !important;
	backdrop-filter: blur(10rpx);
}

.btn-icon {
	font-size: 24rpx;
}

.btn-text {
	font-size: 28rpx;
	font-weight: bold;
}

/* 配置模态框 - 移动端优化 */
.config-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 999999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.config-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.7);
	backdrop-filter: blur(10rpx);
}

/* 移动端配置容器 */
.config-container-mobile {
	position: relative;
	background: linear-gradient(135deg, rgba(10, 10, 42, 0.98) 0%, rgba(26, 26, 58, 0.98) 50%, rgba(42, 42, 74, 0.98) 100%);
	backdrop-filter: blur(30rpx);
	width: 100%;
	max-height: 85vh;
	border-radius: 30rpx 30rpx 0 0;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	border: 2rpx solid rgba(125, 249, 255, 0.2);
	border-bottom: none;
	box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.5);
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

/* 移动端头部 */
.config-header-mobile {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 30rpx 20rpx;
	border-bottom: 1rpx solid rgba(125, 249, 255, 0.2);
	min-height: 80rpx;
}

.header-left {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.title-icon {
	font-size: 32rpx;
}

.config-title-mobile {
	font-size: 28rpx;
	color: #00f7ff;
	font-weight: bold;
}

.config-close-mobile {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 68, 68, 0.2);
	border: 1rpx solid rgba(255, 68, 68, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.config-close-mobile:active {
	transform: scale(0.9);
	background: rgba(255, 68, 68, 0.3);
}

.close-text {
	font-size: 28rpx;
	color: #ff6666;
	font-weight: bold;
}

/* 移动端滚动内容 */
.config-scroll-mobile {
	flex: 1;
	padding: 20rpx 30rpx 0;
}

.config-tip-mobile {
	background: rgba(125, 249, 255, 0.1);
	border: 1rpx solid rgba(125, 249, 255, 0.3);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;
	backdrop-filter: blur(10rpx);
	text-align: center;
}

.tip-text-mobile {
	font-size: 24rpx;
	color: rgba(125, 249, 255, 0.9);
	line-height: 1.5;
}

/* 移动端配置区块 */
.config-section-mobile {
	margin-bottom: 40rpx;
}

.section-title-mobile {
	font-size: 26rpx;
	color: #00f7ff;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
}

/* 移动端性别选择网格 */
.gender-grid-mobile {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 15rpx;
}

.gender-item-mobile {
	background: rgba(125, 249, 255, 0.1);
	border: 2rpx solid rgba(125, 249, 255, 0.3);
	border-radius: 20rpx;
	padding: 20rpx 15rpx;
	transition: all 0.3s ease;
	backdrop-filter: blur(10rpx);
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	min-height: 100rpx;
}

.gender-item-mobile:active {
	transform: scale(0.95);
}

.gender-item-mobile.active {
	background: rgba(125, 249, 255, 0.2);
	border-color: #00f7ff;
	box-shadow: 0 0 15rpx rgba(0, 247, 255, 0.4);
}

.gender-icon-mobile {
	font-size: 32rpx;
	margin-bottom: 5rpx;
}

.gender-text-mobile {
	font-size: 22rpx;
	color: rgba(125, 249, 255, 0.9);
	font-weight: 500;
	text-align: center;
}

/* 移动端输入框 */
.input-wrapper-mobile {
	width: 100%;
}

.input-container-mobile {
	position: relative;
	background: rgba(125, 249, 255, 0.1);
	border: 2rpx solid rgba(125, 249, 255, 0.3);
	border-radius: 20rpx;
	overflow: hidden;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.input-container-mobile:focus-within {
	border-color: rgba(125, 249, 255, 0.8);
	background: rgba(125, 249, 255, 0.15);
	box-shadow: 0 0 15rpx rgba(125, 249, 255, 0.3);
}

.profession-input-mobile {
	width: 100%;
	background: transparent;
	border: none;
	outline: none;
	padding: 25rpx 70rpx 25rpx 25rpx;
	font-size: 24rpx;
	color: #7df9ff;
	line-height: 1.4;
	min-height: 70rpx;
	box-sizing: border-box;
}

/* 移动端输入框placeholder */
.profession-input-mobile::-webkit-input-placeholder {
	color: rgba(125, 249, 255, 0.5);
	font-size: 24rpx;
}

.input-icon-mobile {
	position: absolute;
	right: 25rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 24rpx;
	transition: all 0.3s ease;
	pointer-events: none;
}

.input-status-mobile {
	margin-top: 10rpx;
	padding: 8rpx 15rpx;
	background: rgba(125, 249, 255, 0.05);
	border-radius: 15rpx;
	border: 1rpx solid rgba(125, 249, 255, 0.2);
}

.status-text-mobile {
	font-size: 20rpx;
	color: rgba(125, 249, 255, 0.8);
}

.input-tip {
	margin-top: 15rpx;
	padding: 10rpx 20rpx;
	background: rgba(125, 249, 255, 0.05);
	border-radius: 15rpx;
	border: 1rpx solid rgba(125, 249, 255, 0.2);
}

.input-tip .tip-text {
	font-size: 22rpx;
	color: rgba(125, 249, 255, 0.8);
}

.input-debug {
	margin-top: 15rpx;
	padding: 10rpx 20rpx;
	background: rgba(255, 165, 0, 0.1);
	border-radius: 15rpx;
	border: 1rpx solid rgba(255, 165, 0, 0.3);
}

.input-debug .debug-text {
	font-size: 22rpx;
	color: rgba(255, 165, 0, 0.8);
}

/* 移动端底部操作区 */
.config-actions-mobile {
	padding: 20rpx 30rpx;
	border-top: 1rpx solid rgba(125, 249, 255, 0.2);
	display: flex;
	gap: 15rpx;
	background: rgba(10, 10, 42, 0.8);
	backdrop-filter: blur(20rpx);
}

.action-btn-mobile {
	flex: 1;
	padding: 25rpx 20rpx !important;
	border-radius: 25rpx !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	gap: 8rpx !important;
	font-size: 24rpx !important;
	font-weight: bold;
	border: none !important;
	transition: all 0.3s ease;
	min-height: 80rpx;
}

.action-btn-mobile::after {
	border: none !important;
}

.action-btn-mobile:active {
	transform: scale(0.95);
}

.action-btn-mobile.secondary {
	background: rgba(125, 249, 255, 0.1) !important;
	color: rgba(125, 249, 255, 0.8) !important;
	border: 1rpx solid rgba(125, 249, 255, 0.3) !important;
}

.action-btn-mobile.primary {
	background: linear-gradient(45deg, #00f7ff, rgba(0, 247, 255, 0.8)) !important;
	color: #fff !important;
	box-shadow: 0 5rpx 20rpx rgba(0, 247, 255, 0.3);
}

.action-btn-mobile.primary:disabled {
	opacity: 0.5;
	pointer-events: none;
}

.btn-icon-mobile {
	font-size: 20rpx;
}

.btn-text-mobile {
	font-size: 24rpx;
	font-weight: bold;
}

.bottom-spacer-mobile {
	height: 40rpx;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.comparison-container {
		flex-direction: row;
		gap: 15rpx;
		max-width: 100%;
	}

	.image-item {
		min-width: 200rpx;
		max-width: 250rpx;
	}

	.transform-arrow {
		margin: 0 10rpx;
		flex-shrink: 0;
	}

	.arrow-text {
		font-size: 18rpx;
		padding: 6rpx 10rpx;
	}

	.result-bottom-bar {
		padding: 25rpx 30rpx;
		padding-bottom: calc(25rpx + env(safe-area-inset-bottom));
	}

	.btn-content {
		padding: 22rpx 35rpx;
	}

	.chat-btn-icon {
		font-size: 28rpx;
	}

	.chat-btn-text {
		font-size: 28rpx;
	}

	.chat-btn-subtitle {
		font-size: 18rpx;
	}

	.arrow-symbol {
		font-size: 24rpx;
	}

	.tech-decoration {
		margin-top: 15rpx;
		gap: 12rpx;
	}

	.deco-dot {
		width: 6rpx;
		height: 6rpx;
	}

	.processing-steps {
		gap: 25rpx;
		flex-wrap: nowrap;
	}

	.step-item {
		min-width: 80rpx;
		max-width: 120rpx;
	}

	.step-icon {
		width: 50rpx;
		height: 50rpx;
	}

	.step-label {
		font-size: 20rpx;
	}
}

/* 粒子动画随机位置 */
.particle:nth-child(1) { left: 10%; animation-duration: 6s; }
.particle:nth-child(2) { left: 20%; animation-duration: 8s; }
.particle:nth-child(3) { left: 30%; animation-duration: 7s; }
.particle:nth-child(4) { left: 40%; animation-duration: 9s; }
.particle:nth-child(5) { left: 50%; animation-duration: 6s; }
.particle:nth-child(6) { left: 60%; animation-duration: 8s; }
.particle:nth-child(7) { left: 70%; animation-duration: 7s; }
.particle:nth-child(8) { left: 80%; animation-duration: 9s; }
.particle:nth-child(9) { left: 90%; animation-duration: 6s; }
.particle:nth-child(10) { left: 15%; animation-duration: 8s; }
.particle:nth-child(11) { left: 25%; animation-duration: 7s; }
.particle:nth-child(12) { left: 35%; animation-duration: 9s; }
.particle:nth-child(13) { left: 45%; animation-duration: 6s; }
.particle:nth-child(14) { left: 55%; animation-duration: 8s; }
.particle:nth-child(15) { left: 65%; animation-duration: 7s; }
.particle:nth-child(16) { left: 75%; animation-duration: 9s; }
.particle:nth-child(17) { left: 85%; animation-duration: 6s; }
.particle:nth-child(18) { left: 95%; animation-duration: 8s; }
.particle:nth-child(19) { left: 5%; animation-duration: 7s; }
.particle:nth-child(20) { left: 95%; animation-duration: 9s; }

/* 预测徽章 */
.prediction-badge {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	background: linear-gradient(45deg, rgba(189, 0, 255, 0.8), rgba(189, 0, 255, 0.6));
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(189, 0, 255, 0.5);
	box-shadow: 0 4rpx 15rpx rgba(189, 0, 255, 0.3);
}

.badge-text {
	font-size: 20rpx;
	color: #fff;
	font-weight: bold;
	letter-spacing: 1rpx;
}

</style>
