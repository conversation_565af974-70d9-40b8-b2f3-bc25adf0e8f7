<template>
	<view class="container">
		<block v-if="isload">
			<!-- 团队关系统计信息 -->
			<view class="team-stats" v-if="chartData">
				<view class="stat-item">
					<text class="stat-value">{{getTeamMemberCount()}}</text>
					<text class="stat-label">团队总人数</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{chartData.children ? chartData.children.length : 0}}</text>
					<text class="stat-label">直属成员</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-value">{{countNestedChildren()}}</text>
					<text class="stat-label">间接成员</text>
				</view>
			</view>
			
			<!-- 关系图渲染区域 -->
			<view class="chart-container">
				<text v-if="!chartData" class="empty-text">正在加载关系图数据...</text>
				<text v-else-if="chartData === null" class="empty-text">该成员没有团队成员</text>
				<scroll-view v-else class="relation-tree" scroll-y="true">
					<!-- 根节点 -->
					<view class="tree-root">
						<view class="member-card root-card">
							<view class="card-badge">团队长</view>
							<image class="avatar" :src="chartData.headimg" mode="aspectFill"></image>
							<view class="member-info">
								<text class="nickname">{{chartData.nickname}}</text>
								<text class="level">{{chartData.levelName}}</text>
							</view>
						</view>
					</view>
					
					<!-- 连接线 -->
					<view v-if="chartData.children && chartData.children.length > 0" class="tree-connector">
						<view class="connector-line"></view>
						<view class="connector-dot"></view>
					</view>
					
					<!-- 子节点容器 -->
					<view v-if="chartData.children && chartData.children.length > 0" class="children-container">
						<view class="children-label">团队成员 ({{chartData.children.length}}人)</view>
						<view class="children-grid">
							<view v-for="(child, index) in chartData.children" :key="child.id" class="child-node">
								<view class="member-card">
									<image class="avatar" :src="child.headimg" mode="aspectFill"></image>
									<view class="member-info">
										<text class="nickname">{{child.nickname}}</text>
										<text class="level">{{child.levelName}}</text>
										<view v-if="child.children && child.children.length > 0" class="sub-count">
											<image class="sub-icon" src="/static/img/team_icon.png" mode="aspectFit"></image>
											<text>{{child.children.length}}人</text>
										</view>
									</view>
								</view>
								
								<!-- 展开子成员按钮 -->
								<view v-if="child.children && child.children.length > 0" 
									  class="expand-btn" 
									  @tap="toggleExpand(child.id)">
									<text>{{expandedNodes[child.id] ? '收起' : '展开'}}</text>
									<text class="expand-icon">{{expandedNodes[child.id] ? '∧' : '∨'}}</text>
								</view>
								
								<!-- 子成员列表 -->
								<view v-if="child.children && child.children.length > 0 && expandedNodes[child.id]" 
									  class="grandchildren-list">
									<view v-for="(grandchild, idx) in child.children" :key="grandchild.id" 
										  class="grandchild-item">
										<image class="mini-avatar" :src="grandchild.headimg" mode="aspectFill"></image>
										<view class="grandchild-info">
											<text class="grandchild-name">{{grandchild.nickname}}</text>
											<text class="grandchild-level">{{grandchild.levelName}}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading: false,
      isload: false,
			mid: null, // 目标成员ID
			chartData: undefined, // 存储关系图数据
			expandedNodes: {}, // 记录哪些节点被展开
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.mid = this.opt.mid || null; // 获取传递过来的 mid，如果没有则为 null (查看自己的)
		this.getChartData();
		uni.setNavigationBarTitle({
			title: '团队关系图'
		});
  },
  methods: {
    getChartData: function () {
      var that = this;
			that.loading = true;
      app.post('ApiAgent/getTeamRelationChart', { mid: that.mid }, function (res) {
				that.loading = false;
				if (res.status == 1) {
					that.chartData = res.data; // data 可能为 null 或树状对象
					console.log("获取到的关系图数据:", that.chartData);
				} else {
          app.error(res.msg);
					that.chartData = false; // 表示加载失败
        }
				that.isload = true; // 标记页面加载完成（即使数据为空或失败）
      });
    },
		
		// 切换展开/收起状态
		toggleExpand: function(nodeId) {
			if (this.expandedNodes[nodeId]) {
				// 如果已展开，则收起
				this.$set(this.expandedNodes, nodeId, false);
			} else {
				// 如果未展开，则展开
				this.$set(this.expandedNodes, nodeId, true);
			}
		},
		
		// 计算团队总人数
		getTeamMemberCount: function() {
			if (!this.chartData) return 0;
			
			let count = 0;
			// 计算函数
			const countMembers = (node) => {
				if (!node) return 0;
				let nodeCount = 1; // 当前节点
				
				if (node.children && node.children.length > 0) {
					node.children.forEach(child => {
						nodeCount += countMembers(child);
					});
				}
				
				return nodeCount;
			};
			
			// 从根节点开始计算，但不计算根节点自身
			count = countMembers(this.chartData) - 1;
			return count;
		},
		
		// 计算间接成员数（非直属成员）
		countNestedChildren: function() {
			if (!this.chartData || !this.chartData.children) return 0;
			
			let directCount = this.chartData.children.length;
			let totalCount = this.getTeamMemberCount();
			
			return totalCount - directCount;
		}
  }
};
</script>

<style>
.container {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f8f8f8;
}

/* 团队统计信息 */
.team-stats {
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding: 30rpx;
	background: linear-gradient(135deg, #6e8efb, #a777e3);
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	color: #fff;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stat-value {
	font-size: 40rpx;
	font-weight: bold;
	margin-bottom: 6rpx;
}

.stat-label {
	font-size: 24rpx;
	opacity: 0.9;
}

.stat-divider {
	width: 2rpx;
	height: 60rpx;
	background-color: rgba(255,255,255,0.3);
}

/* 图表容器 */
.chart-container {
	width: 100%;
	min-height: 800rpx;
	border-radius: 16rpx;
	background-color: #fff;
	margin-top: 20rpx;
	padding: 20rpx;
	position: relative;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	overflow: hidden;
	box-sizing: border-box;
}

.empty-text {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
	color: #999;
	font-size: 28rpx;
}

.relation-tree {
	width: 100%;
	height: 1000rpx;
	position: relative;
	padding: 20rpx 0;
}

/* 根节点样式 */
.tree-root {
	display: flex;
	justify-content: center;
	padding: 20rpx 0 40rpx;
}

.member-card {
	display: flex;
	padding: 20rpx;
	border-radius: 12rpx;
	background-color: #fff;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
	min-width: 300rpx;
	align-items: center;
	position: relative;
	z-index: 2;
	transition: all 0.3s;
}

.member-card:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.root-card {
	background: linear-gradient(to right, #f7edd5, #f0e6c9);
	border: 2rpx solid #e6d7af;
}

.card-badge {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	background: #ff8c69;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);
	z-index: 3;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 16rpx;
	border: 2rpx solid #eee;
}

.member-info {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.nickname {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 6rpx;
}

.level {
	font-size: 24rpx;
	color: #666;
	background-color: #f5f5f5;
	padding: 2rpx 12rpx;
	border-radius: 6rpx;
}

/* 连接线样式 */
.tree-connector {
	position: relative;
	height: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.connector-line {
	width: 2rpx;
	height: 100%;
	background-color: #ccc;
}

.connector-dot {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background-color: #a777e3;
	border: 2rpx solid white;
}

/* 子节点容器 */
.children-container {
	width: 100%;
}

.children-label {
	text-align: center;
	font-size: 28rpx;
	color: #666;
	margin-bottom: 30rpx;
	padding: 10rpx 0;
	position: relative;
}

.children-label:before,
.children-label:after {
	content: "";
	position: absolute;
	top: 50%;
	width: 100rpx;
	height: 1rpx;
	background-color: #ddd;
}

.children-label:before {
	left: calc(50% - 170rpx);
}

.children-label:after {
	right: calc(50% - 170rpx);
}

.children-grid {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
}

.child-node {
	width: 90%;
	margin-bottom: 30rpx;
	position: relative;
	padding: 10rpx;
	border-radius: 12rpx;
	background-color: rgba(249, 249, 249, 0.6);
}

/* 下级成员信息 */
.sub-count {
	display: flex;
	align-items: center;
	margin-top: 10rpx;
	color: #6e8efb;
	font-size: 22rpx;
}

.sub-icon {
	width: 24rpx;
	height: 24rpx;
	margin-right: 6rpx;
}

/* 展开按钮 */
.expand-btn {
	margin-top: 16rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	height: 60rpx;
	border-top: 1rpx dashed #eee;
	color: #6e8efb;
	font-size: 24rpx;
}

.expand-icon {
	margin-left: 10rpx;
}

/* 孙级成员列表 */
.grandchildren-list {
	margin-top: 20rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	padding: 12rpx;
}

.grandchild-item {
	display: flex;
	align-items: center;
	padding: 10rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.grandchild-item:last-child {
	border-bottom: none;
}

.mini-avatar {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	margin-right: 16rpx;
}

.grandchild-info {
	display: flex;
	flex: 1;
	justify-content: space-between;
	align-items: center;
}

.grandchild-name {
	font-size: 24rpx;
	color: #333;
}

.grandchild-level {
	font-size: 20rpx;
	color: #999;
	background-color: #f5f5f5;
	padding: 0 8rpx;
	border-radius: 4rpx;
}
</style>