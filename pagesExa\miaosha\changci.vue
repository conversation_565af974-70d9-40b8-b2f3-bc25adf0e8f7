<template>
    <view class="container">
      <block v-if="isload">
        <view class="order-content">
          <block v-for="(item, index) in datalist" :key="index">
            <view class="order-box">
              <view class="head">
                <!-- 头部内容 -->
              </view>
              <view class="content">
                <view>
                  <image :src="item.backimg"></image>
                </view>
                <view class="detail">
                  <text class="t1">场次名称:{{item.name}}</text>
                  <!-- 根据不同状态显示不同的内容 -->
              
                <!-- 状态一：正常抢购中 -->
                <view class="t3">
                  <!-- 检查当前时间是否在实际开始时间（考虑提前抢）之前 -->
                  <template v-if="currentTime < item.actual_starttime">
                    <text class="x1 flex1">
                      活动未开始
                      <text v-if="item.qiangtime > 0" style="color: #1E9FFF;">
                        (您可提前{{item.qiangtime}}分钟抢购)
                      </text>
                    </text>
                    <text
                      class="x2"
                      style="background-color: #ccc; color: #fff; padding: 10rpx 20rpx; border-radius: 8rpx;"
                    >
                      未开始
                    </text>
                  </template>
                
                  <!-- 检查当前时间是否在活动结束时间之后 -->
                  <template v-else-if="currentTime > item.endtime">
                    <text class="x1 flex1">活动已结束</text>
                    <text
                      class="x2"
                      style="background-color: #ccc; color: #fff; padding: 10rpx 20rpx; border-radius: 8rpx;"
                    >
                      已结束
                    </text>
                  </template>
                
                  <!-- 活动进行中（包括提前抢时间） -->
                  <template v-else>
                    <text class="x1 flex1">
                      {{ currentTime < item.starttime ? '提前抢购中' : '正常抢购中' }}
                    </text>
                
                    <!-- 如果需要预约 -->
                    <template v-if="item.is_appointment == 1">
                      <!-- 如果已预约，显示"去抢购"按钮 -->
                      <text
                        class="x2"
                        v-if="item.already_reserved"
                           @tap="goto" :data-url="'classify2?ccid=' + item.id"
                        style="background-color: #1E9FFF; color: #fff; padding: 10rpx 20rpx; border-radius: 8rpx;"
                      >
                        去抢购
                      </text>
                      <!-- 如果未预约，显示"立即预约"按钮 -->
                      <text
                        class="x2"
                        v-else
                        @tap="makeReservation(item.id)"
                        :style="item.miaoshacount == 0 ? 'background-color: #ccc; cursor: not-allowed;' : 'background-color: #1E9FFF; color: #fff; padding: 10rpx 20rpx; border-radius: 8rpx; cursor: pointer;'"
                        :disabled="item.miaoshacount == 0"
                      >
                        立即预约
                      </text>
                    </template>
                
                    <!-- 如果不需要预约，直接显示"去抢购"按钮 -->
                    <template v-else>
                      <text
                        class="x2"
                       @tap="goto" :data-url="'classify2?ccid=' + item.id"
                        style="background-color: #1E9FFF; color: #fff; padding: 10rpx 20rpx; border-radius: 8rpx;"
                      >
                        去抢购
                      </text>
                    </template>
                  </template>
                </view>
                  <!-- 其他状态处理 -->
                  <view class="t3" v-if="item.qingsuan_status==1 && item.isfukuan==1">
                    <text class="x1 flex1">订单处理中</text>
                    <text class="x2" @tap="goto" :data-url="'fukuan?id=' + item.id">去查看</text>
                  </view>
                  <view class="t3" v-if="item.qingsuan_status==1 && item.isfukuan==0">
                    <text class="x1 flex1">订单处理中</text>
                    <text class="x2">未生成付款</text>
                  </view>
                  <view class="t3" v-if="item.qingsuan_status==2">
                    <text class="x1 flex1">已清算完成</text>
                  </view>
                  <!-- 显示开始和结束时间 -->
                  <view class="t3" v-if="item.starttime">
                    <text class="x1 flex1">开始时间: {{ formatDate(item.starttime) }}</text>
                  </view>
                  <view class="t3" v-if="item.endtime">
                    <text class="x1 flex1">结束时间: {{ formatDate(item.endtime) }}</text>
                  </view>
                  <!-- 添加倒计时显示 -->
                  <view class="t3" v-if="item.actual_starttime && currentTime < item.actual_starttime">
                    <text class="x1 flex1">
                      距开始：{{ formatCountdown(item.actual_starttime- currentTime) }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </view>
        <nomore v-if="nomore"></nomore>
        <nodata v-if="nodata"></nodata>
      </block>
      <loading v-if="loading"></loading>
      <dp-tabbar :opt="opt"></dp-tabbar>
      <popmsg ref="popmsg"></popmsg>
    </view>
  </template>
  
  <script>
  var app = getApp();
  
  export default {
    data() {
      return {
        opt: {},
        loading: false,
        isload: false,
        menuindex: -1,
  currentTime: Math.floor(Date.now() / 1000), // 初始化为客户端时间
        st: 'all',
        datalist: [],
        pagenum: 1,
        nomore: false,
        nodata: false,
        codtxt: "",
        canrefund: 1,
        express_content: '',
        selectExpressShow: false,
        hexiao_qr: '',
        keyword: '',
        timer: null, // 添加定时器变量
      };
    },
  
    onLoad: function (opt) {
      this.opt = app.getopts(opt);
      if(this.opt && this.opt.st){
        this.st = this.opt.st;
      }
      this.getdata();
    },
    onPullDownRefresh: function () {
      this.getdata();
    },
    onReachBottom: function () {
      if (!this.nodata && !this.nomore) {
        this.pagenum = this.pagenum + 1;
        this.getdata(true);
      }
    },
    onNavigationBarSearchInputConfirmed:function(e){
      this.searchConfirm({detail:{value:e.text}});
    },
    methods: {
      getdata: function (loadmore) {
        if(!loadmore){
          this.pagenum = 1;
          this.datalist = [];
        }
        var that = this;
        var pagenum = that.pagenum;
        var st = that.st;
        that.nodata = false;
        that.nomore = false;
        that.loading = true;
        app.post('ApiMiaosha/changci', {st: st, pagenum: pagenum, keyword: that.keyword}, function (res) {
          that.loading = false;
          var data = res.datalist;
          if (pagenum == 1) {
            that.codtxt = res.codtxt;
            that.canrefund = res.canrefund;
            that.datalist = data;
            that.currentTime = res.current_time;
            
            // 清除旧的定时器
            if (that.timer) {
              clearInterval(that.timer);
            }
            
            // 创建新的定时器，每秒更新当前时间
            that.timer = setInterval(() => {
              that.currentTime++;
            }, 1000);
            
            if (data.length == 0) {
              that.nodata = true;
            }
            that.loaded();
          } else {
            if (data.length == 0) {
              that.nomore = true;
            } else {
              var datalist = that.datalist;
              var newdata = datalist.concat(data);
              that.datalist = newdata;
            }
          }
        });
      },
      searchConfirm: function(e){
        this.keyword = e.detail.value;
        this.getdata(false);
      },
      // “立即预约”方法
      makeReservation: function(id){
        var that = this;
        wx.showModal({
          title: '确认预约',
          content: '确定要预约该场次吗?',
          success(res) {
            if (res.confirm) {
              // 显示加载动画
              wx.showLoading({
                title: '预约中...',
                mask: true
              });
              
              app.post('ApiMiaosha/makeReservation', {id: id}, function(res){
                // 隐藏加载动画
                wx.hideLoading();
                
                if(res.status == 1){
                  wx.showToast({
                    title: res.msg,
                    icon: 'success',
                    duration: 2000
                  });
                  that.getdata(false); // 重新加载数据以更新状态
                } else {
                  wx.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 2000
                  });
                }
              }, function() {
                // 请求失败时隐藏加载动画并显示错误提示
                wx.hideLoading();
                wx.showToast({
                  title: '网络错误，请稍后再试。',
                  icon: 'none',
                  duration: 2000
                });
              });
            }
          }
        });
      },
  
      // 日期格式化方法
      formatDate: function(timestamp) {
        var date = new Date(timestamp * 1000); // 假设 timestamp 是 UNIX 时间戳（秒）
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      // 添加倒计时格式化方法
      formatCountdown(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600)/ 60);
        const secs = seconds % 60;
        
        return `${hours}小时${minutes}分${secs}秒`;
      }
    },
  
    // 组件销毁时清除定时器
    beforeDestroy() {
      if (this.timer) {
        clearInterval(this.timer);
      }
    },
  
    // 页面隐藏时清除定时器
    onHide() {
      if (this.timer) {
        clearInterval(this.timer);
      }
    },
  
    // 页面显示时重新获取数据和启动定时器
    onShow() {
      this.getdata();
    }
  };
  </script>
  
  <style>
  .container{ width:100%;}
  .topsearch{width:94%;margin:10rpx 3%;}
  .topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
  .topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
  .topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
  .order-content{display:flex;flex-direction:column}
  .order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
  .order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}
  .order-box .head .f1{display:flex;align-items:center;color:#333}
  .order-box .head image{width:34rpx;height:34rpx;margin-right:4px}
  .order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }
  .order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }
  .order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }
  .order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }
  .order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }
  
  .order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}
  .order-box .content:last-child{ border-bottom: 0; }
  .order-box .content image{ width: 140rpx; height: 140rpx;}
  .order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}
  .order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
  .order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}
  .order-box .detail .t3 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12rpx;
    font-size: 28rpx;
  }
  
  .order-box .detail .x1 {
    flex: 1;
    color: #666;
  }
  
  .order-box .detail .x2 {
    width: auto;
    padding: 10rpx 20rpx;
    font-size: 30rpx;
    font-weight: bold;
    color: #fff;
    background-color: #ff4246;
    border-radius: 8rpx;
    cursor: pointer;
    text-align: center;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    transition: all 0.3s ease; /* 增加按钮的动画效果 */
  }
  
  .order-box .detail .x2:hover {
    background-color: #ff2e2e; /* 鼠标悬停时按钮变亮 */
  }
  
  .order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
  .order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
  
  .btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}
  .btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}
  
  .hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
  .hxqrbox .img{width:400rpx;height:400rpx}
  .hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
  .hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}
  </style>
  