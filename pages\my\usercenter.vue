<template>
<view class="container" :style="{backgroundColor:pageinfo.bgcolor}">
	<dp :pagecontent="pagecontent"></dp>
	<block v-if="xixie">
        <dp-xixie-mendian-log ></dp-xixie-mendian-log>
    </block>
	<view v-if="copyright!=''" class="copyright">{{copyright}}</view>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
		<popmsg ref="popmsg"></popmsg>
</view>
</template>
 
<script>
var app = getApp(); 
export default {
	data() {
	return {
			opt:{},
			loading:false,
            isload: false,
			pageinfo: [],
			pagecontent: [],
			copyright:'',
			xixie:false
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata(); 
	},
	onPullDownRefresh:function(e){
		this.getdata();
	},
	methods: {
		getdata:function(){
			var that = this;
			that.loading = true;
			app.get('ApiMy/usercenter',{},function (data){
				
				console.log(data)
				
				that.loading = false;
			  var pagecontent = data.pagecontent;
				that.pageinfo = data.pageinfo;
				that.pagecontent = data.pagecontent;
				that.copyright = data.copyright;
				uni.setNavigationBarTitle({
					title: data.pageinfo.title
				});
				if(data.xixie){
                    that.xixie = data.xixie;
                }
				
				if(data.need_bind_phone){
					that.showBindPhoneModal(data.bind_phone_url, data.bind_phone_mode);
				}
				
				if(data.need_invite_code){
					that.showInviteCodeModal(data.invite_code_url, data.invite_code_mode);
				}
				
				if(data.need_set_avatar){
					that.showSetAvatarModal(data.avatar_url, data.avatar_mode);
				}
				
				that.loaded();
			});
		},
		
		// 显示邀请码弹窗
		showInviteCodeModal: function(inviteUrl, inviteMode) {
			var that = this;
			inviteMode = inviteMode || 1;
			
			var today = new Date().toDateString();
			var lastPromptDate = uni.getStorageSync('invite_code_prompt_date');
			
			if (inviteMode !== 2 && lastPromptDate === today) {
				return;
			}

			var modalOptions = {
				title: '填写邀请码',
				content: '为了享受更多优惠，请填写邀请码',
				confirmText: '去填写'
			};
			
			if (inviteMode === 2) {
				modalOptions.showCancel = false;
				modalOptions.content = '您必须填写邀请码才能正常使用';
			} else {
				modalOptions.cancelText = '稍后';
			}
			
			uni.showModal({
				title: modalOptions.title,
				content: modalOptions.content,
				confirmText: modalOptions.confirmText,
				showCancel: modalOptions.showCancel !== false,
				cancelText: modalOptions.cancelText || '取消',
				success: function(res) {
					if (res.confirm) {
						uni.navigateTo({
							url: inviteUrl,
							fail: function(err) {
								app.error('页面跳转失败');
							}
						});
					} else if (res.cancel && inviteMode !== 2) {
						uni.setStorageSync('invite_code_prompt_date', today);
					}
				}
			});
		},
		
		// 显示设置头像昵称弹窗
		showSetAvatarModal: function(avatarUrl, avatarMode) {
			var that = this;
			avatarMode = avatarMode || 1;
			
			var today = new Date().toDateString();
			var lastPromptDate = uni.getStorageSync('avatar_prompt_date');
			
			if (avatarMode !== 2 && lastPromptDate === today) {
				return;
			}

			var modalOptions = {
				title: '设置头像昵称',
				content: '完善您的个人信息，获得更好的使用体验',
				confirmText: '去设置'
			};
			
			if (avatarMode === 2) {
				modalOptions.showCancel = false;
				modalOptions.content = '您必须设置头像昵称才能正常使用';
			} else {
				modalOptions.cancelText = '稍后';
			}
			
			uni.showModal({
				title: modalOptions.title,
				content: modalOptions.content,
				confirmText: modalOptions.confirmText,
				showCancel: modalOptions.showCancel !== false,
				cancelText: modalOptions.cancelText || '取消',
				success: function(res) {
					if (res.confirm) {
						uni.navigateTo({
							url: avatarUrl,
							fail: function(err) {
								app.error('页面跳转失败');
							}
						});
					} else if (res.cancel && avatarMode !== 2) {
						uni.setStorageSync('avatar_prompt_date', today);
					}
				}
			});
		},
		
		// 显示绑定手机号弹窗
		showBindPhoneModal: function(bindUrl, bindMode) {
			var that = this;
			bindMode = bindMode || 1;
			
			var today = new Date().toDateString();
			var lastPromptDate = uni.getStorageSync('bind_phone_prompt_date');
			
			if (bindMode !== 2 && lastPromptDate === today) {
				return;
			}
			

			var modalOptions = {
				title: '绑定手机号',
				content: '为了保障您的账户安全，请先绑定手机号',
				confirmText: '去绑定'
			};
			
			if (bindMode === 2) {
				modalOptions.showCancel = false;
				modalOptions.content = '为了保障您的账户安全，必须先绑定手机号';
			} else {
				modalOptions.cancelText = '稍后';
			}
			
			uni.showModal({
				title: modalOptions.title,
				content: modalOptions.content,
				confirmText: modalOptions.confirmText,
				showCancel: modalOptions.showCancel !== false,
				cancelText: modalOptions.cancelText || '取消',
			success: function(res) {
				if (res.confirm) {
					uni.navigateTo({
						url: bindUrl,
						fail: function(err) {
							app.error('页面跳转失败');
						}
					});
				} else if (res.cancel && bindMode !== 2) {
					uni.setStorageSync('bind_phone_prompt_date', today);
				}
						}
			});
		}
	}
}
</script>
<style>

</style>