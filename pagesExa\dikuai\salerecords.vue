<template>
	<view>
		<view class="it" v-for="(item, index) in list" :key="index">

			<view>

				<view>成猪ID:{{item.pig_id}}</view>

				<view>{{item.sale_time}}</view>

			</view>

			<view>

				<view>{{item.money}}余额，{{item.score}}积分，{{item.commission}}佣金</view>

			</view>

		</view>
		<nodata v-if="nodata"></nodata>
		
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				list: [],
				nodata: false
			}
		},
		onLoad(opt) {
			this.getData();
		},
		methods: {
			getData() {
				let that = this
				app.get('ApiPlot/getSales', {}, function(res) {

					that.list = res.data
					
					if(that.list.length == 0){
						that.nodata = true;
					}
				});

			}
		}
	}
</script>

<style>
	.it {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 20px;
		padding: 10px;
		border-bottom: 1px solid #aaa;
	}
</style>