<template>
	<view class="container">
		<view class="product-info-box">
			<view class="ing-box">
				
				<image  class="img" :src="product && product.pic"></image>
			</view>
			<view class="info">
					<view class="title-text">{{product.name}}</view>
				<view class="config-text">{{guigeString}}</view>
			</view>

		</view>
		<form @submit="topay">

			<view class="item-group">
				<view class="group-title">
					<view class="title-text">个人信息</view>
					<view class="title-tips">*请填写真实地址，精确到门牌号，如有虚假直接拒单</view>
				</view>

				<view class="form-item">
					<view class="item-label">姓名</view>
					<view class="item-control">
						<input :modal="formData.name" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">身份证号</view>
					<view class="item-control">
							<input :modal="formData.idCard" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">手机号码 </view>
					<view class="item-control">
						<input :modal="formData.phone" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">所在地区</view>
					<view class="item-control">
						<input :modal="formData.area" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">居住地址</view>
					<view class="item-control">
						<input :modal="formData.address" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">电子邮箱</view>
					<view class="item-control">
						<input :modal="formData.email" placeholder="请输入"/>
					</view>
				</view>

			</view>
			<view class="item-group">
				<view class="group-title">
					<view class="title-text">单位信息</view>
					<view class="title-tips">*请填写真实工作，核实到虚假工作直接拒单</view>
				</view>

				<view class="form-item">
					<view class="item-label">单位名称</view>
					<view class="item-control">
						<input :modal="formData.companyName" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">职位</view>
					<view class="item-control">
						<input :modal="formData.companyPosition" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">每月收入 </view>
					<view class="item-control">
						<input :modal="formData.salary" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">单位地址</view>
					<view class="item-control">
						<input :modal="formData.companyAddress" placeholder="请输入"/>
					</view>
				</view>
			</view>
			<view class="item-group">
				<view class="group-title">
					<view class="title-text">紧急联系人信息</view>
					<view class="title-tips">*请预留两名直系亲属联系电话，如有虚假直接拒单</view>
				</view>
				<view class="emergency-label">联系人1</view>
				<view class="form-item">
					<view class="item-label">姓名</view>
					<view class="item-control">
						<input :modal="formData.emergencyName1" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">关系</view>
					<view class="item-control">
						<input :modal="formData.emergencyRelationship1" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">手机号码 </view>
					<view class="item-control">
						<input :modal="formData.emergencyphone1" placeholder="请输入"/>
					</view>
				</view>
				<view class="emergency-label">联系人2</view>
				<view class="form-item">
					<view class="item-label">姓名</view>
					<view class="item-control">
						<input :modal="formData.emergencyName2" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">关系</view>
					<view class="item-control">
						<input :modal="formData.emergencyRelationship2" placeholder="请输入"/>
					</view>
				</view>
				<view class="form-item">
					<view class="item-label">手机号码 </view>
					<view class="item-control">
						<input :modal="formData.emergencyphone2" placeholder="请输入"/>
					</view>
				</view>
			</view>
			<view class="item-group">
				<view class="group-title">
					<view class="title-text">推广合影</view>
					<view class="title-tips">*请上传你与商铺店主的合影</view>
				</view>
				<view class="upload-box">+</view>
			</view>
			
			<button class="submit-btn" form-type="submit">提交信息</button>
		</form>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				ggselected:'',
				proid: null,
				product: {},
				guigeString:'',
				formData:{
					userName: '', // 姓名
					idCard: '', // 身份证
					phone: '',// 手机号码
					area: '',// 所在地区
					address: '', // 居住地址
					email: '', // 邮箱
					companyName: '', // 单位名称,
					companyPosition: '', // 职位
					salary: '', // 薪资
					companyAddress: '', // 单位地址
					emergencyName1: '', // 紧急联系人1
					emergencyphone1:'',// 紧急联系人1电话
					emergencyRelationship1:'', // 紧急联系人1关系
					emergencyName2: '', // 紧急联系人2
					emergencyphone2:'',// 紧急联系人2电话
					emergencyRelationship2:'', // 紧急联系人2关系
					imgs: ''
					
				}
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.ggselected = opt.guigeString
			this.proid = opt.prodid
			this.getProductData()
		},
		methods: {
			getProductData:function(){
				var that = this;
				 that.loading = true;
				app.get('ApiCycle/product', {id:that.proid}, function (res) {
					 that.loading = false;
					if(res.status==1){
						that.loading = false;
						that.productDetail = res
						that.product = res.product;
						
						let guigearr = []
						console.log('res.guigelist=====', res.guigelist)
						for(var i in res.guigelist) {
							guigearr.push(res.guigelist[i].name)
						}
						
						console.log('guigearr=====', guigearr)
						that.guigeString = guigearr.join('|')
					
					}else{
						app.alert(res.msg)
					}
				});
			},
			
			//提交并支付
			topay: function(e) {
				let params = {
					
				}
				app.post('ApiCycle/createOrder',params, function(data) {
					
				})
				
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f1f1f1;
		padding-bottom: 20px;

		.product-info-box {
			padding: 12px;
			border-radius: 9px;
			margin: 12px;
			background-color: #ffffff;
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
			.ing-box {
				width: 50px;
				height: 50px;
				margin-right: 12px;
				overflow: hidden;
				border-radius: 8px;
				.img {
					width: 100%;
					height: 100%;
				}
			}
			.info {
				display: flex;
				flex-direction: column;
				justify-content: flex-start;
				align-items: flex-start;
				.title-text {
					font-size:16px;
					font-weight:bold;
					margin-bottom: 12px;
				}
				.config-text {
					font-size: 14px;
					color: #ccc;
				}
			}
		}

		.item-group {
			background-color: #fff;
			margin: 12px;
			border-radius: 8px;
			padding: 12px;

			.group-title {
				margin-bottom: 10px;

				.title-text {
					font-size: 16px;
					font-weight: bold;
					margin-bottom: 10px;
				}

				.title-tips {
					font-size: 12px;
					color: #ff0000;
				}

			}

			.form-item {
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				align-items: center;
				fOnt-size: 16px;
				height: 50px;
				border-bottom: 1px solid #F1F1F1;
				.item-label {
					font-size: 14px;
					width: 100px;
				}
			}
			.upload-box {
				width: 80px;
				height: 80px;
				border-radius: 8px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				background-color: #ccc;
			}
			.emergency-label {
				font-size: 14px;
			}
			
		}
		.submit-btn {
			border-radius: 20px;
			background-color: #005bb5;
			color: #fff;
			margin: 12px;
			height: 50px;
		}

	}
</style>