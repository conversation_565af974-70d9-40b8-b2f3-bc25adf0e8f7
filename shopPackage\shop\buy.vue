<template>
	<view class="container">
		<block v-if="isload">
			<form @submit="topay">
				<view v-if="needaddress==0" class="address-add">
					<view class="linkitem">
						<text class="f1">联 系 人：</text>
						<input type="text" class="input" :value="linkman" placeholder="请输入您的姓名" @input="inputLinkman"
							placeholder-style="color:#626262;font-size:28rpx" />
					</view>
					<view class="linkitem">
						<text class="f1">联系电话：</text>
						<input type="text" class="input" :value="tel" placeholder="请输入您的手机号" @input="inputTel"
							placeholder-style="color:#626262;font-size:28rpx" />
					</view>
				</view>
				<view v-else class="address-add flex-y-center" @tap="goto"
					:data-url="'/pages/address/'+(address.id ? 'address' : 'addressadd')+'?fromPage=buy&type=' + (havetongcheng==1?'1':'0')">
					<view class="f1">
						<image class="img" src="/static/img/address.png" />
					</view>
					<view class="f2 flex1" v-if="address.id">
						<view style="font-weight:bold;color:#111111;font-size:30rpx">{{address.name}} {{address.tel}} <text v-if="address.company">{{address.company}}</text></view>
						<view style="font-size:24rpx">{{address.area}} {{address.address}}</view>
					</view>
					<view v-else class="f2 flex1">请选择收货地址</view>
					<image src="/static/img/arrowright.png" class="f3"></image>
				</view>
				<view v-for="(buydata, index) in allbuydata" :key="index" class="buydata">
					<view class="btitle">
						<image class="img" src="/static/img/ico-shop.png" />{{buydata.business.name}}
					</view>
					<view class="bcontent">
						<view class="product">
							<view v-for="(item, index2) in buydata.prodata" :key="index2">
								<view v-if="item.num > 0" class="item flex">
									<view class="img" @tap="goto" :data-url="'/shopPackage/shop/product?id=' + item.product.id">
										<image v-if="item.guige.pic" :src="item.guige.pic"></image>
										<image v-else :src="item.product.pic"></image>
									</view>
									<view class="info flex1">
										<view class="f1">{{item.product.name}}</view>
										<view class="f2">规格：{{item.guige.name}}</view>
										<view class="f3">
											<block v-if="order_change_price"><input type="number" :value="item.guige.sell_price" :data-price="item.guige.sell_price" :data-index="index" :data-index2="index2" class="inputPrice" @input="inputPrice"></block>
											<block v-else-if="item.guige.is_newcustom == 0"><text style="font-weight:bold;">￥{{item.guige.yh_price}}</text></block>
											<block v-else><text style="font-weight:bold;">￥{{item.guige.sell_price}}</text></block>
											<text style="padding-left:20rpx"> × {{item.num}}</text>
										</view>
									</view>
								</view>
								<view v-if="item.is_yh == 1" class="item flex">
									<view class="img" @tap="goto" :data-url="'/shopPackage/shop/product?id=' + item.product.id">
										<image v-if="item.guige.pic" :src="item.guige.pic"></image>
										<image v-else :src="item.product.pic"></image>
									</view>
									<view class="info flex1">
										<view class="f1">{{item.product.name}}</view>
										<view class="f2">规格：{{item.guige.name}}</view>
										<view class="f3">
											<block v-if="order_change_price"><input type="number" :value="item.guige.sell_price" :data-price="item.guige.sell_price" :data-index="index" :data-index2="index2" class="inputPrice" @input="inputPrice"></block>
											<block v-else><text style="font-weight:bold;">￥{{item.youhui.product_price}}(优惠价格)</text></block>
											<text style="padding-left:20rpx"> × {{item.youhui.new_num}}(优惠数量)</text>
										</view>
									</view>
								</view>
								<view class="glassinfo" v-if="item.product.glassrecord" @tap="showglass" :data-index="index" :data-index2="index2" :data-grid="item.product.glassrecord.id" :style="'background:rgba('+t('color1rgb')+',0.1)'">
									<view class="f1">
										视力档案
									</view>
									<view class="f2">
										<text>{{item.product.glassrecord.type==1?'近视':'远视'}}，右眼{{item.product.glassrecord.degress_right}}度，左眼{{item.product.glassrecord.degress_left}}度</text>
										<image src="../../static/img/arrowright.png"></image>
									</view>
								</view>
							</view>
						</view>
						<view class="freight">
							<view class="f1">配送方式</view>
							<view class="freight-ul">
								<view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;">
									<block v-for="(item, idx2) in buydata.freightList" :key="idx2">
										<view class="freight-li"
											:style="buydata.freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''"
											@tap="changeFreight" :data-bid="buydata.bid" :data-index="idx2">{{item.name}}
										</view>
									</block>
								</view>
							</view>
							<view class="freighttips"
								v-if="buydata.freightList[buydata.freightkey].minpriceset==1 && buydata.freightList[buydata.freightkey].minprice > 0 && buydata.freightList[buydata.freightkey].minprice*1 > buydata.product_price*1">
								满{{buydata.freightList[buydata.freightkey].minprice}}元起送，还差{{(buydata.freightList[buydata.freightkey].minprice - buydata.product_price).toFixed(2)}}元
							</view>
							<view class="freighttips" v-if="buydata.freightList[buydata.freightkey].isoutjuli==1">超出配送范围</view>
						</view>

						<view class="price" v-if="buydata.freightList[buydata.freightkey].pstimeset==1">
							<view class="f1">{{buydata.freightList[buydata.freightkey].pstype==1?'取货':'配送'}}时间</view>
							<view class="f2" @tap="choosePstime" :data-bid="buydata.bid">
								{{buydata.pstimetext==''?'请选择时间':buydata.pstimetext}}<text class="iconfont iconjiantou"
									style="color:#999;font-weight:normal"></text>
							</view>
						</view>
						<view class="storeitem" v-if="buydata.freightList[buydata.freightkey].pstype==1">
							<view class="panel">
								<view class="f1">取货地点</view>
								<view class="f2" @tap="openMendian" :data-bid="buydata.bid"
									:data-freightkey="buydata.freightkey"
									:data-storekey="buydata.freightList[buydata.freightkey].storekey"><text
										class="iconfont icondingwei"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}
								</view>
							</view>
							<block v-for="(item, idx) in buydata.freightList[buydata.freightkey].storedata" :key="idx">
								<view class="radio-item" @tap.stop="choosestore" :data-bid="buydata.bid" :data-index="idx" v-if="idx<5 || storeshowall==true">
									<view class="f1">{{item.name}} </view>
									<text style="color:#f50;">{{item.juli}}</text>
									<view class="radio"
										:style="buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''">
										<image class="radio-img" src="/static/img/checkd.png" />
									</view>
								</view>
							</block>
							<view v-if="storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5" class="storeviewmore" @tap="doStoreShowAll">- 查看更多 - </view>
						</view>
						
						<view class="storeitem" v-if="buydata.freightList[buydata.freightkey].pstype==5">
							<view class="panel">
								<view class="f1">配送门店</view>
								<view class="f2" @tap="openMendian" :data-bid="buydata.bid"
									:data-freightkey="buydata.freightkey"
									:data-storekey="buydata.freightList[buydata.freightkey].storekey"><text
										class="iconfont icondingwei"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}
								</view>
							</view>
							<block v-for="(item, idx) in buydata.freightList[buydata.freightkey].storedata" :key="idx">
								<view class="radio-item" @tap.stop="choosestore" :data-bid="buydata.bid" :data-index="idx" v-if="idx<5 || storeshowall==true">
									<view class="f1">{{item.name}} </view>
									<text style="color:#f50;">{{item.juli}}</text>
									<view class="radio"
										:style="buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''">
										<image class="radio-img" src="/static/img/checkd.png" />
									</view>
								</view>
							</block>
							<view v-if="storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5" class="storeviewmore" @tap="doStoreShowAll">- 查看更多 - </view>
						</view>

						<view class="price" v-if="buydata.freightList[buydata.freightkey].pstype==11">
							<view class="f1">选择物流</view>
							<view class="f2" @tap="showType11List" :data-bid="buydata.bid">
								<text>{{buydata.type11key?buydata.freightList[buydata.freightkey].type11pricedata[buydata.type11key-1].name:'请选择'}}</text><text
									class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</view>
						</view>

						<view class="price">
							<text class="f1">订单金额</text>
							<text class="f2">¥{{buydata.product_price}}</text>
						</view>
						<view class="price" v-if="buydata.leveldk_money>0">
							<text class="f1">{{t('会员')}}折扣({{userinfo.discount}}折)</text>
							<text class="f2">-¥{{buydata.leveldk_money}}</text>
						</view>
						<view class="price" v-if="buydata.manjian_money>0">
							<text class="f1">满减活动</text>
							<text class="f2">-¥{{buydata.manjian_money}}</text>
						</view>
						<view class="price">
							<text class="f1">{{buydata.freightList[buydata.freightkey].freight_price_txt || '运费'}}<text v-if="buydata.freightList[buydata.freightkey].pstype!=1 && buydata.freightList[buydata.freightkey].freeset==1" style="color:#aaa;font-size:24rpx;">（满{{buydata.freightList[buydata.freightkey].free_price}}元包邮）</text></text>
							<text class="f2">+¥{{buydata.freightList[buydata.freightkey].freight_price}}</text>
						</view>
						<!-- <view class="price"  v-if="buydata.diy_amount>0">
													<text class="f1">框价格</text>
													<text class="f2">¥{{buydata.diy_amount}}</text>
												</view> -->

						<view class="price">
							<view class="f1">{{t('优惠券')}}</view>
							<view v-if="buydata.couponCount > 0" class="f2" @tap="showCouponList" :data-bid="buydata.bid">
								<block v-if="(buydata.coupons).length>0">
									<text class="couponname" :style="{background:t('color1')}" v-for="(item,index) in buydata.coupons">{{item.couponname}}</text>
								</block>
								<block v-else>
									<text class="couponname" :style="{background:t('color1')}">{{buydata.couponCount+'张可用'}}</text>
								</block>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</view>
							<text class="f2" v-else style="color:#999">无可用{{t('优惠券')}}</text>
						</view>
						<view class="price" v-if="buydata.cuxiaoCount > 0">
							<view class="f1">促销活动</view>
							<view class="f2" @tap="showCuxiaoList" :data-bid="buydata.bid">
								<block v-if="buydata.cuxiaonameArr && buydata.cuxiaonameArr.length > 0">
									<view :style="{background:t('color1')}" class="redBg" v-for="(item,index) in buydata.cuxiaonameArr" :key="index">{{item}}</view>
								</block>
								<block v-else>
									<text :style="{background:t('color1')}" class="redBg">{{buydata.cuxiaoname?buydata.cuxiaoname:buydata.cuxiaoCount+'个可用'}}</text>
								</block>
							</view>
							<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
						</view>
						<view class="price" v-if="buydata.business.invoice > 0">
							<view class="f1">发票</view>
							<view class="f2" @tap="showInvoice" :data-url="'/shopPackage/shop/invoice?bid=' + buydata.bid + '&prodata=' + opt.prodata" :data-bid="buydata.bid" :data-index="index">
								<text
									style="font-size:24rpx" v-if="buydata.tempInvoice && buydata.tempInvoice.invoice_name">
									<text v-if="buydata.tempInvoice && buydata.tempInvoice.name_type == 1">个人 - </text>
									<text v-if="buydata.tempInvoice && buydata.tempInvoice.name_type == 2">公司 - </text>
									{{buydata.tempInvoice.invoice_name}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</view>
						</view>
						<view class="price" v-if="buydata.business.invoice > 0 && buydata.business.invoice_rate > 0 && buydata.tempInvoice && buydata.tempInvoice.invoice_name">
							<text class="f1">发票费用</text>
							<text class="f2">¥{{buydata.invoice_price}}</text>
						</view>

						<view style="display:none">{{test}}</view>
						<view class="form-item" v-for="(item,idx) in buydata.freightList[buydata.freightkey].formdata" :key="item.id">
							<view class="label">{{item.val1}}<text v-if="item.val3==1" style="color:red"> *</text></view>
							<block v-if="item.key=='input'">
								<input type="text" :name="'form'+buydata.bid+'_'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx"/>
							</block>
							<block v-if="item.key=='textarea'">
								<textarea :name="'form'+buydata.bid+'_'+idx" class='textarea' :placeholder="item.val2" placeholder-style="font-size:28rpx"/>
							</block>
							<block v-if="item.key=='radio'">
								<radio-group class="radio-group" :name="'form'+buydata.bid+'_'+idx">
									<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
										<radio class="radio" :value="item1"/>{{item1}}
									</label>
								</radio-group>
							</block>
							<block v-if="item.key=='checkbox'">
								<checkbox-group :name="'form'+buydata.bid+'_'+idx" class="checkbox-group">
									<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
										<checkbox class="checkbox" :value="item1"/>{{item1}}
									</label>
								</checkbox-group>
							</block>
							<block v-if="item.key=='selector'">
								<picker class="picker" mode="selector" :name="'form'+buydata.bid+'_'+idx" value="" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx] || buydata.editorFormdata[idx]===0"> {{item.val2[buydata.editorFormdata[idx]]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='time'">
								<picker class="picker" mode="time" :name="'form'+buydata.bid+'_'+idx" value="" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx]">{{buydata.editorFormdata[idx]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='date'">
								<picker class="picker" mode="date" :name="'form'+buydata.bid+'_'+idx" value="" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-bid="buydata.bid" :data-idx="idx">
									<view v-if="buydata.editorFormdata[idx]">{{buydata.editorFormdata[idx]}}</view>
									<view v-else>请选择</view>
								</picker>
								<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
							</block>
							<block v-if="item.key=='upload'">
								<input type="text" style="display:none" :name="'form'+buydata.bid+'_'+idx" :value="buydata.editorFormdata[idx]"/>
								<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
									<view class="form-imgbox" v-if="buydata.editorFormdata[idx]">
										<view class="form-imgbox-img"><image class="image" :src="buydata.editorFormdata[idx]" @click="previewImage" :data-url="buydata.editorFormdata[idx]" mode="widthFix"/></view>
									</view>
									<view class="form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-bid="buydata.bid" :data-idx="idx"></view>
								</view>
							</block>
						</view>
						<view class="price" v-if="buydata.diy_amount>0">
							<text class="f1">框价格   x {{buydata.totalnum}}</text>
							<text class="f2">¥{{buydata.diy_amount}}</text>
					</view>
						<view class="price" v-if="totalFreight">
						  <text class="f1">配送费</text>
						  <text class="f2">¥{{totalFreight}}</text>
				</view>
					</view>
				</view>
				
				<view class="scoredk" v-if="buy_selectmember">
					<view class="price">
						<view class="f1">选择会员</view>
						<view class="f2" @tap="showMemberList">
							<text>{{checkMem.id?checkMem.nickname:'请选择'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
						</view>
					</view>
				</view>
				<view class="scoredk"
					v-if="userinfo.score2money>0 && (userinfo.scoremaxtype==0 || (userinfo.scoremaxtype==1 && userinfo.scoredkmaxmoney>0))">
					<checkbox-group @change="scoredk" class="flex" style="width:100%">
						<view class="f1">
							<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text
									style="color:#e94745">{{userinfo.scoredk_money*1}}</text> 元</view>
							<view style="font-size:22rpx;color:#999"
								v-if="userinfo.scoremaxtype==0 && userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100">
								最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>
							<view style="font-size:22rpx;color:#999" v-else-if="userinfo.scoremaxtype==1">
								最多可抵扣{{userinfo.scoredkmaxmoney}}元</view>
						</view>
						<view class="f2">使用{{t('积分')}}抵扣
							<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
						</view>
					</checkbox-group>
				</view>
				
				<view class="scoredk"
					v-if="userinfo.score2moneyhei>0 && (userinfo.scoremaxtypehei==0 || (userinfo.scoremaxtypehei==1 && userinfo.scoredkmaxmoneyhei>0))">
					<checkbox-group @change="scoredkhei" class="flex" style="width:100%">
						<view class="f1">
							<view>{{userinfo.heiscore*1}} {{t('现金券')}}可抵扣 <text
									style="color:#e94745">{{userinfo.scorebdkyfhei*1}}</text> 元</view>
							<view style="font-size:22rpx;color:#999"
								v-if="userinfo.scoremaxtypehei==0 && userinfo.scoredkmaxpercenthei > 0 && userinfo.scoredkmaxpercenthei<100">
								最多可抵扣订单金额的{{userinfo.scoredkmaxpercenthei}}%</view>
							<view style="font-size:22rpx;color:#999" v-else-if="userinfo.scoremaxtypehei==1">
								最多可抵扣{{userinfo.scoredkmaxmoneyhei}}元</view>
						</view>
						<view class="f2">使用{{t('现金券')}}抵扣
							<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
						</view>
					</checkbox-group>
				</view>
				
				<view class="scoredk"
					v-if="userinfo.score2moneyyu>0 && (userinfo.scoremaxtypeyu==0 || (userinfo.scoremaxtypeyu==1 && userinfo.scoredkmaxmoneyyu>0))">
					<checkbox-group @change="scoredkyu" class="flex" style="width:100%">
						<view class="f1">
							<view>{{userinfo.yue*1}} {{t('余额')}}可抵扣 <text
									style="color:#e94745">{{userinfo.scoredkmaxpercentyu}}%</text> </view>
							<view style="font-size:22rpx;color:#999"
								v-if="userinfo.scoremaxtypeyu==0 && userinfo.scoredkmaxpercentyu > 0 && userinfo.scoredkmaxpercentyu<100">
								最多可抵扣订单金额的{{userinfo.scoredkmaxpercentyu}}%</view>
							<view style="font-size:22rpx;color:#999" v-else-if="userinfo.scoremaxtypeyu==1">
								最多可抵扣{{userinfo.scoredkmaxmoneyyu}}元</view>
						</view>
						<view class="f2">使用{{t('余额')}}抵扣
							<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
						</view>
					</checkbox-group>
				</view>
				
				
				<view class="scoredk"
					v-if="userinfo.score2moneyhuang>0 && (userinfo.scoremaxtypehuang==0 || (userinfo.scoremaxtypehuang==1 && userinfo.scoredkmaxmoneyhuang>0))">
					<checkbox-group @change="scoredkhuang" class="flex" style="width:100%">
						<view class="f1">
							<view>{{userinfo.scorehuang*1}} {{t('红包')}}可抵扣 <text
									style="color:#e94745">{{userinfo.scorebdkyfhuang*1}}</text> 元</view>
							<view style="font-size:22rpx;color:#999"
								v-if="userinfo.scoremaxtypehuang==0 && userinfo.scoredkmaxpercenthuang > 0 && userinfo.scoredkmaxpercenthuang<100">
								最多可抵扣订单金额的{{userinfo.scoredkmaxpercenthuang}}%</view>
							<view style="font-size:22rpx;color:#999" v-else-if="userinfo.scoremaxtypehuang==1">
								最多可抵扣{{userinfo.scoredkmaxmoneyhuang}}元</view>
						</view>
						<view class="f2">使用{{t('红包')}}抵扣
							<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
						</view>
					</checkbox-group>
				</view>
				
				<view style="width: 100%; height:110rpx;"></view>
				<view class="footer flex notabbarbot">
					<view class="text1 flex1">总计：
						<text style="font-weight:bold;font-size:36rpx">￥{{alltotalprice}}</text>
					</view>
					<button class="op" form-type="submit" :style="{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" :disabled="submitDisabled">
						提交订单</button>
				</view>
			</form>

			<view v-if="invoiceShow" class="popup__container">
				<view class="popup__overlay" @tap.stop="handleClickMask"></view>
				<view class="popup__modal">
					<view class="popup__title">
						<text class="popup__title-text">请填写开票信息</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx"
							@tap.stop="handleClickMask" />
					</view>
					<view class="popup__content invoiceBox">
						<form @submit="invoiceFormSubmit" @reset="formReset" report-submit="true">
							<view class="orderinfo">
								<view class="item">
									<text class="t1">发票类型</text>
									<view class="t2">
											<radio-group class="radio-group" @change="changeOrderType" name="invoice_type">
											<label class="radio" v-if="inArray(1,invoice_type)">
												<radio value="1" :checked="invoice_type_select == 1 ? true : false"></radio>普通发票
											</label>
											<label class="radio" v-if="inArray(2,invoice_type)">
												<radio value="2" :checked="invoice_type_select == 2 ? true : false"></radio>增值税专用发票
											</label>
											</radio-group>
									 </view>
								</view>
								<view class="item">
									<text class="t1">抬头类型</text>
									<view class="t2">
										<block v-if="inputDisabled">
											<text v-if="invoice && invoice.name_type == 1">个人</text>
											<text v-if="invoice && invoice.name_type == 2">公司</text>
										</block>
										<block v-else>
											<radio-group class="radio-group" @change="changeNameType" name="name_type">
											<label class="radio">
												<radio value="1" :checked="name_type_select == 1 ? true : false" :disabled="name_type_personal_disabled ? true : false"></radio>个人
											</label>
											<label class="radio">
												<radio value="2" :checked="name_type_select == 2 ? true : false"></radio>公司
											</label>
											</radio-group>
										</block>
									</view>
								</view>
								<view class="item">
									<text class="t1">抬头名称</text>
									<input class="t2" type="text" placeholder="抬头名称" placeholder-style="font-size:28rpx;color:#BBBBBB" name="invoice_name" :disabled="inputDisabled" :value="invoice ? invoice.invoice_name : ''" ></input>
								</view>
								<view class="item" v-if="name_type_select == 2">
									<text class="t1">公司税号</text>
									<input class="t2" type="text" placeholder="公司税号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tax_no" :disabled="inputDisabled" :value="invoice ? invoice.tax_no : ''"></input>
								</view>
								<view class="item" v-if="invoice_type_select == 2">
									<text class="t1">注册地址</text>
									<input class="t2" type="text" placeholder="注册地址" placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" :disabled="inputDisabled" :value="invoice ? invoice.address : ''"></input>
								</view>
								<view class="item" v-if="invoice_type_select == 2">
									<text class="t1">注册电话</text>
									<input class="t2" type="text" placeholder="注册电话" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" :disabled="inputDisabled" :value="invoice ? invoice.tel : ''"></input>
								</view>
								<view class="item" v-if="invoice_type_select == 2">
									<text class="t1">开户银行</text>
									<input class="t2" type="text" placeholder="开户银行" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_name" :disabled="inputDisabled" :value="invoice ? invoice.bank_name : ''"></input>
								</view>
								<view class="item" v-if="invoice_type_select == 2">
									<text class="t1">银行账号</text>
									<input class="t2" type="text" placeholder="银行账号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_account" :disabled="inputDisabled" :value="invoice ? invoice.bank_account : ''"></input>
								</view>
								<view class="item">
									<text class="t1">手机号</text>
									<input class="t2" type="text" placeholder="接收电子发票手机号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="mobile" :disabled="inputDisabled" :value="invoice ? invoice.mobile : ''"></input>
								</view>
								<view class="item">
									<text class="t1">邮箱</text>
									<input class="t2" type="text" placeholder="接收电子发票邮箱" placeholder-style="font-size:28rpx;color:#BBBBBB" name="email" :disabled="inputDisabled" :value="invoice ? invoice.email : ''"></input>
								</view>
							</view>
							<button class="btn" form-type="submit" :style="{background:t('color1')}">确定</button>
							<view style="padding-top:30rpx"></view>
						</form>
					</view>
				</view>
			</view>
			
			<view v-if="couponvisible" class="popup__container">
				<view class="popup__overlay" @tap.stop="handleClickMask"></view>
				<view class="popup__modal">
					<view class="popup__title">
						<text class="popup__title-text">请选择{{t('优惠券')}}</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx"
							@tap.stop="handleClickMask" />
					</view>
					<view class="popup__content">
						<couponlist :couponlist="allbuydata[bid].couponList" :choosecoupon="true"
							:selectedrids="allbuydata[bid].couponrids" :bid="bid" @chooseCoupon="chooseCoupon">
						</couponlist>
					</view>
				</view>
			</view>
			<view v-if="pstimeDialogShow" class="popup__container">
				<view class="popup__overlay" @tap.stop="hidePstimeDialog"></view>
				<view class="popup__modal">
					<view class="popup__title">
						<text
							class="popup__title-text">请选择{{allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstype==1?'取货':'配送'}}时间</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx"
							@tap.stop="hidePstimeDialog" />
					</view>
					<view class="popup__content">
						<view class="pstime-item"
							v-for="(item, index) in allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstimeArr"
							:key="index" @tap="pstimeRadioChange" :data-index="index">
							<view class="flex1">{{item.title}}</view>
							<view class="radio"
								:style="allbuydata[nowbid].freight_time==item.value ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<view v-if="cuxiaovisible" class="popup__container">
				<view class="popup__overlay" @tap.stop="handleClickMask"></view>
				<view class="popup__modal">
					<view class="popup__title">
						<text class="popup__title-text">优惠促销</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx"
							@tap.stop="handleClickMask" />
					</view>
					<view class="popup__content">
						<view class="cuxiao-desc">
							<block v-if="multi_promotion">
								<view class="cuxiao-item" @tap="changecxMulti" data-id="0">
									<view class="type-name"><text style="color:#333">不使用促销</text></view>
									<view class="radio" :style="cxid === 0 ? 'background:'+t('color1')+';border:0' : ''">
										<image class="radio-img" src="/static/img/checkd.png" />
									</view>
								</view>
								<view v-for="(item, index) in allbuydata[bid].cuxiaolist" :key="index" class="cuxiao-item"
									@tap="changecxMulti" :data-id="item.id" :data-index="index">
									<view class="type-name flex-col">
										<view>
											<text style="border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px">{{item.tip}}</text>
											<text style="color:#333;padding-left:20rpx">{{item.name}}</text>
										</view>
										<view v-if="item.give_jifen > 0 || item.give_money > 0 || item.give_cash_coupon > 0 || item.give_yellow_points > 0" style="margin-top:8rpx;font-size:22rpx;color:#FF6600;padding:8rpx 16rpx;background:#FFF8EE;border-radius:8rpx;">
											<text v-if="item.give_jifen > 0">赠送积分:{{item.give_jifen}} </text>
											<text v-if="item.give_money > 0">赠送余额:{{item.give_money}} </text>
											<text v-if="item.give_cash_coupon > 0">赠送现金券:{{item.give_cash_coupon}} </text>
											<text v-if="item.give_yellow_points > 0">赠送红包:{{item.give_yellow_points}}</text>
										</view>
									</view>
									<view class="radio" :style="cxids.indexOf(item.id) !== -1 ? 'background:'+t('color1')+';border:0' : ''">
										<image class="radio-img" src="/static/img/checkd.png" />
									</view>
								</view>
							</block>
							<block v-else>
								<view class="cuxiao-item" @tap="changecx" data-id="0">
									<view class="type-name"><text style="color:#333">不使用促销</text></view>
									<view class="radio" :style="cxid == 0 ? 'background:'+t('color1')+';border:0' : ''">
										<image class="radio-img" src="/static/img/checkd.png" />
									</view>
								</view>
								<view v-for="(item, index) in allbuydata[bid].cuxiaolist" :key="index" class="cuxiao-item"
									@tap="changecx" :data-id="item.id" :data-index="index">
									<view class="type-name flex-col">
										<view>
											<text style="border-radius:4px;border:1px solid #f05423;color: #ff550f;font-size:20rpx;padding:2px 5px">{{item.tip}}</text>
											<text style="color:#333;padding-left:20rpx">{{item.name}}</text>
										</view>
										<view v-if="item.give_jifen > 0 || item.give_money > 0 || item.give_cash_coupon > 0 || item.give_yellow_points > 0" style="margin-top:8rpx;font-size:22rpx;color:#FF6600;padding:8rpx 16rpx;background:#FFF8EE;border-radius:8rpx;">
											<text v-if="item.give_jifen > 0">赠送积分:{{item.give_jifen}} </text>
											<text v-if="item.give_money > 0">赠送余额:{{item.give_money}} </text>
											<text v-if="item.give_cash_coupon > 0">赠送现金券:{{item.give_cash_coupon}} </text>
											<text v-if="item.give_yellow_points > 0">赠送红包:{{item.give_yellow_points}}</text>
										</view>
									</view>
									<view class="radio" :style="cxid==item.id ? 'background:'+t('color1')+';border:0' : ''">
										<image class="radio-img" src="/static/img/checkd.png" />
									</view>
								</view>
							</block>
						</view>

						<view id="cxproinfo" v-if="cuxiaoinfo.product" style="padding:0 40rpx">
							<view class="product">
								<view class="item flex" style="background:#f5f5f5">
									<view class="img" @tap="goto" :data-url="'/shopPackage/shop/product?id=' + cuxiaoinfo.product.id">
										<image :src="cuxiaoinfo.product.pic"></image>
									</view>
									<view class="info flex1">
										<view class="f1">{{cuxiaoinfo.product.name}}</view>
										<view class="f2">规格：{{cuxiaoinfo.guige.name}}</view>
										<view class="f3">
											<text style="font-weight:bold;">￥{{cuxiaoinfo.guige.sell_price}}</text>
											<text style="padding-left:20rpx"> × 1</text>
										</view>
									</view>
								</view>
							</view>
							<view v-if="cuxiaoinfo.give_jifen > 0 || cuxiaoinfo.give_money > 0 || cuxiaoinfo.give_cash_coupon > 0 || cuxiaoinfo.give_yellow_points > 0" style="margin-top:16rpx;font-size:24rpx;color:#FF6600;padding:12rpx 20rpx;background:#FFF8EE;border-radius:8rpx;">
								<text v-if="cuxiaoinfo.give_jifen > 0">赠送积分:{{cuxiaoinfo.give_jifen}} </text>
								<text v-if="cuxiaoinfo.give_money > 0">赠送余额:{{cuxiaoinfo.give_money}} </text>
								<text v-if="cuxiaoinfo.give_cash_coupon > 0">赠送现金券:{{cuxiaoinfo.give_cash_coupon}} </text>
								<text v-if="cuxiaoinfo.give_yellow_points > 0">赠送红包:{{cuxiaoinfo.give_yellow_points}}</text>
							</view>
						</view>

						<view v-if="cuxiaoList && cuxiaoList.info && cuxiaoList.info.length > 0" style="padding:0 40rpx">
							<view v-for="(item, idx) in cuxiaoList.info" :key="idx" class="cuxiao-item-detail">
								<view class="cuxiao-item-title" style="margin:20rpx 0 10rpx;font-weight:bold;color:#333;">{{item.name}}</view>
								<view v-if="item.product" class="product">
									<view class="item flex" style="background:#f5f5f5">
										<view class="img" @tap="goto" :data-url="'/shopPackage/shop/product?id=' + item.product.id">
											<image :src="item.product.pic"></image>
										</view>
										<view class="info flex1">
											<view class="f1">{{item.product.name}}</view>
											<view class="f2" v-if="item.guige">规格：{{item.guige.name}}</view>
											<view class="f3" v-if="item.guige">
												<text style="font-weight:bold;">￥{{item.guige.sell_price}}</text>
												<text style="padding-left:20rpx"> × 1</text>
											</view>
										</view>
									</view>
								</view>
								<view v-if="item.give_jifen > 0 || item.give_money > 0 || item.give_cash_coupon > 0 || item.give_yellow_points > 0" 
									style="margin-top:10rpx;font-size:24rpx;color:#FF6600;padding:12rpx 20rpx;background:#FFF8EE;border-radius:8rpx;">
									<text v-if="item.give_jifen > 0">赠送积分:{{item.give_jifen}} </text>
									<text v-if="item.give_money > 0">赠送余额:{{item.give_money}} </text>
									<text v-if="item.give_cash_coupon > 0">赠送现金券:{{item.give_cash_coupon}} </text>
									<text v-if="item.give_yellow_points > 0">赠送红包:{{item.give_yellow_points}}</text>
								</view>
							</view>
						</view>
						<view style="width:100%; height:120rpx;"></view>
						<view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff">
							<view
								style="width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;"
								:style="{background:t('color1')}" @tap="chooseCuxiao">确 定</view>
						</view>
					</view>
				</view>
			</view>

			<view v-if="type11visible" class="popup__container">
				<view class="popup__overlay" @tap.stop="handleClickMask"></view>
				<view class="popup__modal">
					<view class="popup__title">
						<text class="popup__title-text">选择物流</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx"
							@tap.stop="handleClickMask" />
					</view>
					<view class="popup__content">
						<view class="cuxiao-desc">
							<view
								v-for="(item, index) in allbuydata[bid].freightList[allbuydata[bid].freightkey].type11pricedata"
								:key="index" @tap="changetype11" :data-index="index" style="padding:0 30rpx 20rpx 40rpx"
								v-if="address.id && address.province==item.province && address.city==item.city && address.district==item.area">
								<view class="cuxiao-item" style="padding:0">
									<view class="type-name"><text
											style="color:#333;font-weight:bold;">{{item.name}}</text></view>
									<view class="radio"
										:style="type11key==index ? 'background:'+t('color1')+';border:0' : ''">
										<image class="radio-img" src="/static/img/checkd.png" />
									</view>
								</view>
								<view style="margin-left:20rpx">发货: {{item.send_address}} - {{item.send_tel}}</view>
								<view style="margin-left:20rpx">收货: {{item.receive_address}} - {{item.receive_tel}}
								</view>
							</view>
						</view>
						<view style="width:100%; height:120rpx;"></view>
						<view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff">
							<view
								style="width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;"
								:style="{background:t('color1')}" @tap="chooseType11">确 定</view>
						</view>
					</view>
				</view>
			</view>
			
			<view v-if="membervisible" class="popup__container">
				<view class="popup__overlay" @tap.stop="handleClickMask"></view>
				<view class="popup__modal" style="height: 1100rpx;">
					<view class="popup__title">
						<text class="popup__title-text">请选择指定会员</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx;"
							@tap.stop="handleClickMask" />
					</view>
					<view class="popup__content">
						<view class="member_search">
							<view style="width:130rpx;color:#333;flex-shrink:0">选择地区</view>
							<uni-data-picker :localdata="items" :border="false" :placeholder="regiondata || '请选择省市区'" @change="regionchange2" class="flex1" style="overflow:hidden"></uni-data-picker>
							<view class="searchMemberButton" @click="memberSearch" style="flex-shrink:0">检索用户</view>
						</view>
						<view class="memberlist">
							<view v-for="(item2,i) in memberList" :key="i" class="memberitem" @tap="checkMember" :data-info="item2">
								<image :src="item2.headimg" @tap.stop="showmemberinfo" :data-mid="item2.id"/>
								<view class="flex-col">
									<view class="t1" @tap.stop="showmemberinfo" :data-mid="item2.id">{{item2.nickname}}</view>
									<view>{{item2.province}} {{item2.city}} {{item2.area}}</view>
								</view>
								<view class="flex1"></view>
								<view class="radio" :style="checkMem.id==item2.id ? 'background:'+t('color1')+';border:0' : ''">
									<image class="radio-img" src="/static/img/checkd.png" />
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>


			<view v-if="memberinfovisible" class="popup__container">
				<view class="popup__overlay" @tap.stop="memberinfoClickMask"></view>
				<view class="popup__modal" style="height: 1100rpx;">
					<view class="popup__title">
						<text class="popup__title-text">查看资料</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx;"
							@tap.stop="memberinfoClickMask" />
					</view>
					<view class="popup__content">
						<view class="orderinfo">
							<view class="item" v-for="item in selectmemberinfo" :key="index">
								<text class="t1">{{item[0]}}</text>
								<view class="t2" v-if="item[2]=='upload'"><image :src="item[1]" style="width:400rpx;height:auto" mode="widthFix" @tap="previewImage" :data-url="item[1]"/></view>
								<text class="t2" v-else>{{item[1]}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 眼镜档案 -->
			<view v-if="isshowglass" class="popup__container glass_popup">
				<view class="popup__overlay" @tap.stop="hideglass"></view>
				<view class="popup__modal" style="height: 1100rpx;">
					<view class="popup__title">
						<text class="popup__title-text">视力档案</text>
						<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx;"
							@tap.stop="hideglass" />
					</view>
					<view class="popup__content">
						<radio-group @change="chooseglass">
						<block v-for="(item,index) in glassrecordlist" :key="index">
						<label>
							<view class="glassitem" :class="grid==item.id?'on':''">
								<view class="radio"><radio :color="t('color1')" :checked="grid==item.id?true:false" :value="''+index"></radio></view>
								<view class="gcontent">
									<view class="glassrow">
										<view class="glasscol">
											<view class="num">{{item.type==1?'近视':'远视'}}</view>
											<view class="txt">{{item.is_ats==1?'散光':''}}</view>
										</view>
										<view class="glasscol">
											<view class="num">{{item.degress_right}}</view>
											<view class="txt">右眼</view>
										</view>
										<view class="glasscol">
											<view class="num">{{item.degress_left}}</view>
											<view class="txt">左眼</view>
										</view>
										<view class="glasscol">
											<view class="num">{{item.ipd}}mm</view>
											<view class="txt">瞳距</view>
										</view>
									</view>
									<block v-if="item.is_ats==1">
										<view class="glassrow bt">
											<view class="glasscol">
												<view class="num">{{item.ats_right}}</view>
												<view class="txt">柱镜右眼</view>
											</view>
											<view class="glasscol">
												<view class="num">{{item.ats_left}}</view>
												<view class="txt">柱镜左眼</view>
											</view>
											<view class="glasscol">
												<view class="num">{{item.ats_zright}}</view>
												<view class="txt">轴位右眼</view>
											</view>
											<view class="glasscol">
												<view class="num">{{item.ats_zleft}}</view>
												<view class="txt">轴位左眼</view>
											</view>
										</view>
									</block>
								</view>
								
							</view>
						</label>
						</block>
						</radio-group>
					</view>
				</view>
			</view>
			<!-- 眼镜档案 -->

		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				pre_url:app.globalData.pre_url,
				test:'test',
				havetongcheng: 0,
				address: [],
				memberList: [],
				checkMem:{},
				usescore: 0,
				scoredk_money: 0,
				totalprice: '0.00',
				couponvisible: false,
				cuxiaovisible: false,
				membervisible: false,
				memberinfovisible:false,
				selectmemberinfo:{},
				bid: 0,
				nowbid: 0,
				needaddress: 1,
				linkman: '',
				singleFreight: 0,
				totalFreight: 0,
				tel: '',
				userinfo: {},
				pstimeDialogShow: false,
				pstimeIndex: -1,
				manjian_money: 0,
				cxid: 0,
				cxids: [],
				latitude: "",
				longitude: "",
				allbuydata: {},
				allbuydatawww: {},
				alltotalprice: "",
				cuxiaoinfo: false,
				cuxiaoList: {},
				type11visible: false,
				type11key: -1,
				regiondata: '',
				items: [],
				editorFormdata:[],
				buy_selectmember:false,
				multi_promotion:0,
				storeshowall:false,
				order_change_price:false,
				invoiceShow:false,
				invoice:{},
				invoice_type:[],
				invoice_type_select:1,
				name_type_select:1,
				name_type_personal_disabled:false,
				inputDisabled:false,
				submitDisabled:false,
				pstype3needAddress:false,
				isshowglass:false,
				glassrecordlist:[],
				grid:0,
				curindex:-1,
				curindex2:-1,
				is_yh:0,//是否存在优惠
				yh_prices:0,
				yh_nums:0,
				newArr:[],
			};
		},

		onLoad: function(opt) {
			
			this.opt = app.getopts(opt);
			
			this.getdata();
			this.newArr = [];
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			getdata: function() {
				var that = this;
		
				that.loading = true;
				app.get('ApiShop/buy', {
					prodata: that.opt.prodata
				}, function(res) {
					// console.log('返回信息');
					// console.log(res);
					that.loading = false;
					if (res.status == 0) {
						if (res.msg) {
							app.alert(res.msg, function() {
								if (res.url) {
									app.goto(res.url);
								} else {
									app.goback();
								}
							});
						} else if (res.url) {
							app.goto(res.url);
						} else {
							app.alert('您没有权限购买该订单');
						}
						return;
					}
					that.havetongcheng = res.havetongcheng;
					that.address = res.address;
					that.linkman = res.linkman;
					that.singleFreight = res.single_freight
					that.totalFreight = res.total_freight
					that.tel = res.tel;
					that.userinfo = res.userinfo;
					that.buy_selectmember = res.buy_selectmember;
					that.order_change_price = res.order_change_price;
					that.pstype3needAddress = res.pstype3needAddress;
					if(that.buy_selectmember){
						uni.request({
							url: app.globalData.pre_url+'/static/area2.json',
							data: {},
							method: 'GET',
							header: { 'content-type': 'application/json' },
							success: function(res2) {
								that.items = res2.data
							}
						})
					}
					that.allbuydata = res.allbuydata;
					that.allbuydatawww = JSON.parse(JSON.stringify(res.allbuydata));
					// console.log(that.allbuydata);
					// console.log(res.allbuydata);

                     console.log( res.allbuydata)
       // 初始化 newArr
                    that.newArr = [];
                    that.is_yh = 0;

                    // 确保 allbuydata 是数组
                    if (Array.isArray(res.allbuydata)) {
                        res.allbuydata.forEach(function(item, index) {
                            if (item && item.prodata) {
                                let obj = item.prodata;
                                for (let key in obj) {
                                    if (obj[key].yh_danshu == 1 && that.newArr.length == 0) {
                                        console.log(obj[key]);
                                        that.is_yh = 1;
                                        that.newArr.push({
                                            "product_id": obj[key].id,
                                            "is_yh": obj[key].yh_danshu,
                                            "yh_num": obj[key].yh_danshu,
                                            "yh_price": obj[key].yh_price
                                        });
                                    }
                                }
                            } else {
                                console.warn('res.allbuydata[' + index + '] 未定义或没有 prodata');
                            }
                        });
                    } else {
                        console.error('res.allbuydata 不是数组或为空');
                    }
					// console.log('222');
					console.log(that.newArr);
					console.log('结束');
					that.needLocation = res.needLocation;
					that.scorebdkyf = res.scorebdkyf;
					that.scorebdkyfheihei = res.scorebdkyfheihei;
					that.multi_promotion = res.multi_promotion;
					that.calculatePrice();
					that.loaded();
					
					// var allbuydata = that.allbuydata;
					// for (var i in allbuydata) {
					// 	allbuydata[i].tempInvoice = uni.getStorageSync('temp_invoice_' + allbuydata[i].bid);
					// }
					// that.allbuydata = allbuydata;
					

					if (res.needLocation == 1) {
						app.getLocation(function(res) {
							var latitude = res.latitude;
							var longitude = res.longitude;
							that.latitude = latitude;
							that.longitude = longitude;
							var allbuydata = that.allbuydata;
							for (var i in allbuydata) {
								var freightList = allbuydata[i].freightList;
								for (var j in freightList) {
									if (freightList[j].pstype == 1 || freightList[j].pstype == 5) {
										var storedata = freightList[j].storedata;
										if (storedata) {
											for (var x in storedata) {
												if (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {
													var juli = that.getDistance(latitude, longitude,storedata[x].latitude, storedata[x].longitude);
													storedata[x].juli = juli;
												}
											}
											storedata.sort(function(a, b) {
												return a["juli"] - b["juli"];
											});
											for (var x in storedata) {
												if (storedata[x].juli) {
													storedata[x].juli = storedata[x].juli + '千米';
												}
											}
											console.log(storedata);
											allbuydata[i].freightList[j].storedata = storedata;
										}
									}
								}
							}
							that.allbuydata = allbuydata;
						});
					}
				});
			},
			//积分抵扣
			scoredk: function(e) {
				var usescore = e.detail.value[0];
				if (!usescore) usescore = 0;
				this.usescore = usescore;
				this.calculatePrice();
			},
			//黑积分抵扣
			scoredkhei: function(e) {
				var usescorehei = e.detail.value[0];
				if (!usescorehei) usescorehei = 0;
				this.usescorehei = usescorehei;
				console.log(this.usescorehei,888)
				this.calculatePrice();
			},
			//黄积分抵扣
			scoredkhuang: function(e) {
				var usescorehuang = e.detail.value[0];
				if (!usescorehuang) usescorehuang = 0;
				this.usescorehuang = usescorehuang;
				console.log(this.usescorehuang,999)
				this.calculatePrice();
			},
			
			//余额抵扣
			scoredkyu: function(e) {
				var usescoreyu = e.detail.value[0];
				if (!usescoreyu) usescoreyu = 0;
				this.usescoreyu = usescoreyu;
				this.calculatePrice();
			},
			inputLinkman: function(e) {
				this.linkman = e.detail.value;
			},
			inputTel: function(e) {
				this.tel = e.detail.value;
			},
			inputfield: function(e) {
				var bid = e.currentTarget.dataset.bid;
				var field = e.currentTarget.dataset.field;
				allbuydata2[bid][field] = e.detail.value;
				this.allbuydata2 = allbuydata2;
			},
			//选择收货地址
			chooseAddress: function() {
				app.goto('/pages/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));
			},
			inputPrice: function(e) {
				var that = this;
				var index = e.currentTarget.dataset.index;
				var index2 = e.currentTarget.dataset.index2;
				var allbuydata = that.allbuydata;
				var allbuydatawww = that.allbuydatawww;
				var oldprice = allbuydatawww[index]['prodata'][index2].guige.sell_price;
				if(e.detail.value == '' || parseFloat(e.detail.value) < parseFloat(oldprice)) {
					that.submitDisabled = true;
					app.error('不能小于原价:'+oldprice);
					return;
				}
				that.submitDisabled = false;
				allbuydata[index]['prodata'][index2].guige.sell_price = e.detail.value;
				allbuydata[index]['product_price'] = (e.detail.value * allbuydata[index]['prodata'][index2].num).toFixed(2);
				// allbuydata[index].prodatastr = allbuydata[index].prodatastr
				that.allbuydata = allbuydata;
				console.log(allbuydata[index]);
				that.calculatePrice();
			},
			//计算价格
			calculatePrice: function() {
				var that = this;
				var address = that.address;
				var allbuydata = that.allbuydata;
				var alltotalprice = 0;
				var allfreight_price = 0;
				var needaddress = 0;
				// console.log(allbuydata)
				for (var k in allbuydata) {
					var product_price = parseFloat(allbuydata[k].product_price);
					var diy_amount = parseFloat(allbuydata[k].diy_amount);
					var leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣
					var manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动
					var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 
					var cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动 
					var invoice_money = parseFloat(allbuydata[k].invoice_money); //+发票 
					//var diy_amount = parseFloat(allbuydata[k].diy_amount); //包装价格
					//算运费
					var freightdata = allbuydata[k].freightList[allbuydata[k].freightkey];
					var freight_price = freightdata['freight_price'];
					if (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {
						needaddress = 1;
					}
					if(that.pstype3needAddress && (freightdata.pstype == 3 || freightdata.pstype == 4 || freightdata.pstype == 5)) {
						needaddress = 1;
					}
					if (allbuydata[k].coupontype == 4) {
						freight_price = 0;
					}
					//var totalprice = product_price - leveldk_money - manjian_money - coupon_money + cuxiao_money;
					var totalprice = product_price - leveldk_money - manjian_money - coupon_money + cuxiao_money + diy_amount;
					if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费

					totalprice = totalprice + freight_price;
					allbuydata[k].freight_price = freight_price.toFixed(2);
					if(allbuydata[k].business.invoice && allbuydata[k].business.invoice_rate > 0 && allbuydata[k].tempInvoice){
						var invoice_money = totalprice * parseFloat(allbuydata[k].business.invoice_rate) / 100;
						allbuydata[k].invoice_money = invoice_money.toFixed(2);
						totalprice = totalprice + invoice_money;
					}
					console.log('invoice_money');
					console.log(invoice_money);
					
					allbuydata[k].totalprice = totalprice.toFixed(2);
					alltotalprice += totalprice;
					alltotalprice += that.totalFreight;
					allfreight_price += freight_price;
				}
				that.needaddress = needaddress;
				if (that.usescore) {
					var scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣
				} else {
					var scoredk_money = 0;
				}
				if (that.usescorehei) {
					var scorebdkyfhei = parseFloat(that.userinfo.scorebdkyfhei); //-积分抵扣
				} else {
					var scorebdkyfhei = 0;
				}
				
				if (that.usescorehuang) {
					var scorebdkyfhuang = parseFloat(that.userinfo.scorebdkyfhuang); //-积分抵扣
				} else {
					var scorebdkyfhuang = 0;
				}
				if (that.usescoreyu) {
					var scorebdkyfyu = parseFloat(that.userinfo.scorebdkyfyu); //-余额抵扣
				} else {
					var scorebdkyfyu = 0;
				}
				
				
				var oldalltotalprice = alltotalprice;
				alltotalprice = alltotalprice - scoredk_money;
				// alltotalprice = alltotalprice - scorebdkyfhei;
				if (alltotalprice < 0) alltotalprice = 0;

				if (that.scorebdkyf == '1' && scoredk_money > 0 && alltotalprice < allfreight_price) {
					//积分不抵扣运费
					alltotalprice = allfreight_price;
					scoredk_money = oldalltotalprice - allfreight_price;
				}
				if (that.scorebdkyfhei == '1' && scorebdkyfhei > 0 && alltotalprice < allfreight_price) {
					//黑积分不抵扣运费
					alltotalprice = allfreight_price;
					scorebdkyfhei = oldalltotalprice - allfreight_price;
				}
				if (that.scorebdkyfhuang == '1' && scorebdkyfhuang > 0 && alltotalprice < allfreight_price) {
					//黄积分不抵扣运费
					alltotalprice = allfreight_price;
					scorebdkyfhuang = oldalltotalprice - allfreight_price;
				}
				if (that.scorebdkyfyu == '1' && scorebdkyfyu > 0 && alltotalprice < allfreight_price) {
					//黑积分不抵扣运费
					alltotalprice = allfreight_price;
					scorebdkyfyu = oldalltotalprice - allfreight_price;
				}
				var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例
				var scoremaxtype = parseInt(that.userinfo.scoremaxtype);
				var scoredkmaxmoney = parseFloat(that.userinfo.scoredkmaxmoney);
				
				var scoredkmaxpercenthei = parseFloat(that.userinfo.scoredkmaxpercenthei); //最大抵扣比例
				var scoremaxtypehei = parseInt(that.userinfo.scoremaxtypehei);
				var scoredkmaxmoneyhei = parseFloat(that.userinfo.scoredkmaxmoneyhei);
				
				var scoredkmaxpercenthuang = parseFloat(that.userinfo.scoredkmaxpercenthuang); //最大抵扣比例
				var scoremaxtypehuang = parseInt(that.userinfo.scoremaxtypehuang);
				var scoredkmaxmoneyhuang = parseFloat(that.userinfo.scoredkmaxmoneyhuang);
				
				var scoredkmaxpercentyu = parseFloat(that.userinfo.scoredkmaxpercentyu); //最大抵扣比例
				var scoremaxtypeyu = parseInt(that.userinfo.scoremaxtypeyu);
				var scoredkmaxmoneyyu = parseFloat(that.userinfo.scoredkmaxmoneyyu);
				

				if (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 &&
					scoredk_money > oldalltotalprice * scoredkmaxpercent * 0.01) {
					scoredk_money = oldalltotalprice * scoredkmaxpercent * 0.01;
					alltotalprice = oldalltotalprice - scoredk_money;
					// oldalltotalprice = oldalltotalprice - scoredk_money;
					
				} else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {
					
					scoredk_money = scoredkmaxmoney;
					alltotalprice = oldalltotalprice - scoredk_money;
					// oldalltotalprice = oldalltotalprice - scoredk_money;
					
				}
				if (scoremaxtypehei == 0 && scorebdkyfhei > 0 && scoredkmaxpercenthei > 0 && scoredkmaxpercenthei < 100 &&
					scorebdkyfhei > oldalltotalprice * scoredkmaxpercenthei * 0.01) {
					scorebdkyfhei = alltotalprice * scoredkmaxpercenthei * 0.01;
					alltotalprice = alltotalprice - scorebdkyfhei;
					// oldalltotalprice = oldalltotalprice - scorebdkyfhei;
				} else if (scoremaxtypehei == 1 && scorebdkyfhei > scoredkmaxmoneyhei) {
					scorebdkyfhei = scoredkmaxmoneyhei;
					alltotalprice = alltotalprice - scorebdkyfhei;
					// oldalltotalprice = oldalltotalprice - scorebdkyfhei;
				}
				
				//黄积分
				if (scoremaxtypehuang == 0 && scorebdkyfhuang > 0 && scoredkmaxpercenthuang > 0 && scoredkmaxpercenthuang < 100 &&
					scorebdkyfhuang > oldalltotalprice * scoredkmaxpercenthuang * 0.01) {
					scorebdkyfhuang = alltotalprice * scoredkmaxpercenthuang * 0.01;
					alltotalprice = alltotalprice - scorebdkyfhuang;
					// oldalltotalprice = oldalltotalprice - scorebdkyfhuang;
				} else if (scoremaxtypehuang == 1 && scorebdkyfhuang > scoredkmaxmoneyhuang) {
					scorebdkyfhuang = scoredkmaxmoneyhuang;
					alltotalprice = alltotalprice - scorebdkyfhuang;
					// oldalltotalprice = oldalltotalprice - scorebdkyfhuang;
				}
				
				
				if (scoremaxtypeyu == 0 && scorebdkyfyu > 0 && scoredkmaxpercentyu > 0 && scoredkmaxpercentyu < 100 &&
					scorebdkyfyu > oldalltotalprice * scoredkmaxpercentyu * 0.01) {
					scorebdkyfyu = alltotalprice * scoredkmaxpercentyu * 0.01;
					alltotalprice = alltotalprice - scorebdkyfyu;
				} else if (scoremaxtypeyu == 1 && scorebdkyfyu > scoredkmaxmoneyyu) {
					scorebdkyfyu = scoredkmaxmoneyyu;
					alltotalprice = alltotalprice - scorebdkyfyu;
				}

				if (alltotalprice < 0) alltotalprice = 0;
				alltotalprice = alltotalprice.toFixed(2);
				that.alltotalprice = alltotalprice;
				that.allbuydata = allbuydata;
			},
			changeFreight: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = e.currentTarget.dataset.bid;
				var index = e.currentTarget.dataset.index;
				var freightList = allbuydata[bid].freightList;
				if(freightList[index].pstype==1 && freightList[index].storedata.length < 1) {
					app.error('无可自提门店');return;
				}
				if(freightList[index].pstype==5 && freightList[index].storedata.length < 1) {
					app.error('无可配送门店');return;
				}
				allbuydata[bid].freightkey = index;
				that.allbuydata = allbuydata;
				that.calculatePrice();
				that.allbuydata[bid].editorFormdata = [];
			},
			chooseFreight: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = e.currentTarget.dataset.bid;
				// console.log(bid);
				// console.log(allbuydata);
				var freightList = allbuydata[bid].freightList;
				var itemlist = [];

				for (var i = 0; i < freightList.length; i++) {
					itemlist.push(freightList[i].name);
				}

				uni.showActionSheet({
					itemList: itemlist,
					success: function(res) {
						if (res.tapIndex >= 0) {
							allbuydata[bid].freightkey = res.tapIndex;
							that.allbuydata = allbuydata;
							that.calculatePrice();
						}
					}
				});
			},
			choosePstime: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = e.currentTarget.dataset.bid;
				var freightkey = allbuydata[bid].freightkey;
				var freightList = allbuydata[bid].freightList;
				var freight = freightList[freightkey];
				var pstimeArr = freightList[freightkey].pstimeArr;
				var itemlist = [];
				for (var i = 0; i < pstimeArr.length; i++) {
					itemlist.push(pstimeArr[i].title);
				}
				if (itemlist.length == 0) {
					app.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');
					return;
				}
				that.nowbid = bid;
				that.pstimeDialogShow = true;
				that.pstimeIndex = -1;
			},
			pstimeRadioChange: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var pstimeIndex = e.currentTarget.dataset.index;
				// console.log(pstimeIndex)
				var nowbid = that.nowbid;
				var freightkey = allbuydata[nowbid].freightkey;
				var freightList = allbuydata[nowbid].freightList;
				var freight = freightList[freightkey];
				var pstimeArr = freightList[freightkey].pstimeArr;
				var choosepstime = pstimeArr[pstimeIndex];
				allbuydata[nowbid].pstimetext = choosepstime.title;
				allbuydata[nowbid].freight_time = choosepstime.value;
				that.allbuydata = allbuydata
				that.pstimeDialogShow = false;
			},
			hidePstimeDialog: function() {
				this.pstimeDialogShow = false;
			},
			chooseCoupon: function(e) {
				var allbuydata = this.allbuydata;
				var bid = e.bid;
				var couponrid = e.rid;
				var couponkey = e.key;
				var oldcoupons = allbuydata[bid].coupons;
				var oldcouponrids = allbuydata[bid].couponrids;
				var couponList = allbuydata[bid].couponList;
				if (app.inArray(couponrid,oldcouponrids)) {
					var coupons = [];
					var couponrids = [];
					for(var i in oldcoupons){
						if(oldcoupons[i].id != couponrid){
							coupons.push(oldcoupons[i]);
							couponrids.push(oldcoupons[i].id);
						}
					}
				} else {
					coupons = oldcoupons;
					couponrids = oldcouponrids;
					console.log(allbuydata[bid].coupon_peruselimit + '---' + oldcouponrids.length);
					if(allbuydata[bid].coupon_peruselimit > oldcouponrids.length){
						console.log('xxxx');
						coupons.push(couponList[couponkey]);
						couponrids.push(couponList[couponkey].id);
					}else{
						if(allbuydata[bid].coupon_peruselimit > 1){
							app.error('最多只能选用'+allbuydata[bid].coupon_peruselimit+'张');
							return;
						}else{
							coupons = [couponList[couponkey]];
							couponrids = [couponrid];
						}
					}
				}
				console.log(coupons);
				console.log(couponrids);
				allbuydata[bid].coupons = coupons;
				allbuydata[bid].couponrids = couponrids;
				var coupon_money = 0;
				var coupontype = 1;
				for(var i in coupons){
					if(coupons[i]['type'] == 4){
						coupontype = 4;
					}else if(coupons[i]['type'] == 10){
						coupon_money += coupons[i]['thistotalprice'] * (100-coupons[i]['discount']) * 0.01;
					}else{
						coupon_money += coupons[i]['money']
					}
				}
				allbuydata[bid].coupontype = coupontype;
				allbuydata[bid].coupon_money = coupon_money;
				this.allbuydata = allbuydata;
				this.couponvisible = false;
				this.calculatePrice();
			},
			choosestore: function(e) {
				var bid = e.currentTarget.dataset.bid;
				var storekey = e.currentTarget.dataset.index;
				var allbuydata = this.allbuydata;
				var buydata = allbuydata[bid];
				var freightkey = buydata.freightkey
				allbuydata[bid].freightList[freightkey].storekey = storekey
				this.allbuydata = allbuydata;
			},
			//提交并支付
			topay: function(e) {
				var that = this;
				var needaddress = that.needaddress;
				var addressid = this.address.id;
				var checkmemid = this.checkMem.id;
				var linkman = this.linkman;
				var tel = this.tel;
				var usescore = this.usescore;
				var usescorehei = this.usescorehei;
				var usescoreyu = this.usescoreyu;
				var usescorehuang = this.usescorehuang;
				var frompage = that.opt.frompage ? that.opt.frompage : '';
				var allbuydata = that.allbuydata;
				var is_yh = that.is_yh;
				var yh_prices = that.yh_prices;
				var yh_nums = that.yh_nums;
				var newarr = that.newArr;
				
				if (needaddress == 0) addressid = 0;

				if (needaddress == 1 && addressid == undefined) {
					app.error('请选择收货地址');
					return;
				}
				var buydata = [];
				for (var i in allbuydata) {
					var freightkey = allbuydata[i].freightkey;
					if (allbuydata[i].freightList[freightkey].pstimeset == 1 && allbuydata[i].freight_time == '') {
						app.error('请选择' + (allbuydata[i].freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间');
						return;
					}
					if (allbuydata[i].freightList[freightkey].pstype == 1 || allbuydata[i].freightList[freightkey].pstype == 5) {
						var storekey = allbuydata[i].freightList[freightkey].storekey;
						var storeid = allbuydata[i].freightList[freightkey].storedata[storekey].id;
					} else {
						var storeid = 0;
					}
					if (allbuydata[i].freightList[freightkey].pstype == 11) {
						var type11key = allbuydata[i].type11key;
						if (type11key == 0 || !type11key) {
							app.error('请选择物流');
							return;
						}
						type11key = type11key - 1;
					} else {
						var type11key = 0
					}

					var formdata_fields = allbuydata[i].freightList[freightkey].formdata;
					var formdata = e.detail.value;
					var newformdata = {};
					for (var j = 0; j < formdata_fields.length;j++){
						var thisfield = 'form'+allbuydata[i].bid + '_' + j;
						if (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length==0)){
								app.alert(formdata_fields[j].val1+' 必填');return;
						}
						if (formdata_fields[j].key == 'selector') {
								formdata[thisfield] = formdata_fields[j].val2[formdata[thisfield]]
						}
						if(j > 0 && formdata_fields[j].val1 == '确认账号' && formdata_fields[j-1].val1 == '充值账号' && formdata[thisfield] != formdata['form'+allbuydata[i].bid + '_' + (j-1)]){
							app.alert('两次输入账号不一致');return;
						}
						newformdata['form'+j] = formdata[thisfield];
					}

					var couponrid = (allbuydata[i].couponrids).join(',');
					
					var buydatatemp = {
						bid: allbuydata[i].bid,
						prodata: allbuydata[i].prodatastr,
						cuxiaoid: allbuydata[i].cuxiaoid,
						couponrid: couponrid,
						freight_id: allbuydata[i].freightList[freightkey].id,
						freight_time: allbuydata[i].freight_time,
						storeid: storeid,
						formdata:newformdata,
						type11key: type11key,
					};
					
					if(that.order_change_price) {
						buydatatemp.prodataList = allbuydata[i].prodata;
					}
					if(allbuydata[i].business.invoice) {
						buydatatemp.invoice = allbuydata[i].tempInvoice;
					}
					buydata.push(buydatatemp);
				}
				// console.log(buydata);return;
				app.showLoading('提交中');
				app.post('ApiShop/createOrder', {
					frompage: frompage,
					buydata: buydata,
					addressid: addressid,
					linkman: linkman,
					tel: tel,
					checkmemid:checkmemid,
					usescore: usescore,
					usescorehei: usescorehei,
					usescoreyu:usescoreyu,
					usescorehuang:usescorehuang,
					is_yh:is_yh,
					yh_prices:yh_prices,
					yh_nums:yh_nums,
					newarr:newarr,
				}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						//that.showsuccess(res.data.msg);
						app.error(res.msg);
						return;
					}
					//app.error('订单编号：' +res.payorderid);
					if(res.payorderid)
					app.goto('/pages/pay/pay?id=' + res.payorderid);
				});
			},
			showCouponList: function(e) {
				this.couponvisible = true;
				this.bid = e.currentTarget.dataset.bid;
			},
			showInvoice: function(e) {
				this.invoiceShow = true;
				this.bid = e.currentTarget.dataset.bid;
				let index = e.currentTarget.dataset.index;
				this.invoice_type = this.allbuydata[index].business.invoice_type;
				this.invoice = this.allbuydata[index].tempInvoice;
			},
			changeOrderType: function(e) {
				var that = this;
				var value = e.detail.value;
				if(value == 2) {
					that.name_type_select = 2;
					that.name_type_personal_disabled = true;
				} else {
					that.name_type_personal_disabled = false;
				}
				that.invoice_type_select = value;
			},
			changeNameType: function(e) {
				var that = this;
				var value = e.detail.value;
				that.name_type_select = value;
			},
			invoiceFormSubmit: function (e) {
			  var that = this;
				var formdata = e.detail.value;
				if(formdata.invoice_name == '') {
					app.error('请填写抬头名称');
					return;
				}
				if((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {
					///^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/
					app.error('请填写公司税号');
					return;
				}
				if(formdata.invoice_type == 2) {
					if(formdata.address == '') {
						app.error('请填写注册地址');
						return;
					}
					if(formdata.tel == '') {
						app.error('请填写注册电话');
						return;
					}
					if(formdata.bank_name == '') {
						app.error('请填写开户银行');
						return;
					}
					if(formdata.bank_account == '') {
						app.error('请填写银行账号');
						return;
					}
				}
				if (formdata.mobile != '') {
					if(!/^1[3456789]\d{9}$/.test(formdata.mobile)){
						app.error("手机号码有误，请重填");
						return;
					}
				}
				if (formdata.email != '') {
					if(!/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/.test(formdata.email)){
						app.error("邮箱有误，请重填");
						return;
					}
				}
				if(formdata.mobile == '' && formdata.email == '') {
					app.error("手机号和邮箱请填写其中一个");
					return;
				}
				// console.log(formdata);
				var allbuydata = that.allbuydata;
				for (var i in allbuydata) {
					if(allbuydata[i].bid == that.bid)
						allbuydata[i].tempInvoice = formdata;
				}
				that.allbuydata = allbuydata;
				that.invoiceShow = false;
					// that.loading = true;
					// uni.setStorageSync('temp_invoice_' + that.opt.bid, formdata);
				that.calculatePrice();
			},
			handleClickMask: function() {
				this.couponvisible = false;
				this.cuxiaovisible = false;
				this.type11visible = false;
				this.membervisible = false;
				this.invoiceShow = false;
			},
			showCuxiaoList: function(e) {
				this.cuxiaovisible = true;
				this.bid = e.currentTarget.dataset.bid;
			},
			changecx: function(e) {
				var that = this;
				var cxid = e.currentTarget.dataset.id;
				var cxindex = e.currentTarget.dataset.index;
				console.log(cxid);
				that.cxid = cxid;
				if (cxid == 0) {
					that.cuxiaoinfo = false;
					return;
				}
				var cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];
				console.log(cuxiaoinfo.cuxiaomoney)
				app.post("ApiShop/getcuxiaoinfo", {
					id: cxid
				}, function(res) {
					if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {
						res.cuxiaomoney = cuxiaoinfo.cuxiaomoney
					}
					// 添加赠送信息
					if (cuxiaoinfo.give_jifen > 0) {
						res.give_jifen = cuxiaoinfo.give_jifen;
					}
					if (cuxiaoinfo.give_money > 0) {
						res.give_money = cuxiaoinfo.give_money;
					}
					if (cuxiaoinfo.give_cash_coupon > 0) {
						res.give_cash_coupon = cuxiaoinfo.give_cash_coupon;
					}
					if (cuxiaoinfo.give_yellow_points > 0) {
						res.give_yellow_points = cuxiaoinfo.give_yellow_points;
					}
					that.cuxiaoinfo = res;
				});
			},
			changecxMulti: function(e) {
				var that = this;
				var cxid = e.currentTarget.dataset.id;
				var cxindex = e.currentTarget.dataset.index;
				that.cuxiaoList.length = 0;
				console.log('cxid:'+cxid);
				if (cxid == 0) {
					that.cuxiaoinfo = false;
					that.cxids.length = 0;
					that.cxid = 0;
					return;
				}
				var index = that.cxids.indexOf(cxid);
				if(index === -1){
					that.cxids.push(cxid);
				} else {
					that.cxids.splice(index);
				}
				if(that.cxids.length == 0) {
					that.cxid = 0;
					that.cuxiaoinfo = false;
					return;
				}
				that.cxid = '';
				var cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];
				console.log(cuxiaoinfo.cuxiaomoney)
				app.showLoading();
				app.post("ApiShop/getcuxiaoinfo", {
					id: that.cxids
				}, function(res) {
					// if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {
					// 	res.cuxiaomoney = cuxiaoinfo.cuxiaomoney
					// }
					// 添加赠送信息
					if (res.info && res.info.length > 0) {
						for (var i = 0; i < res.info.length; i++) {
							var item = that.allbuydata[that.bid].cuxiaolist.find(c => c.id == res.info[i].id);
							if (item) {
								if (item.give_jifen > 0) {
									res.info[i].give_jifen = item.give_jifen;
								}
								if (item.give_money > 0) {
									res.info[i].give_money = item.give_money;
								}
								if (item.give_cash_coupon > 0) {
									res.info[i].give_cash_coupon = item.give_cash_coupon;
								}
								if (item.give_yellow_points > 0) {
									res.info[i].give_yellow_points = item.give_yellow_points;
								}
							}
						}
					}
					app.showLoading(false);
					that.cuxiaoList = res;
				});
			},
			chooseCuxiao: function() {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = that.bid;
				var cxid = that.cxid;
				var cxids = that.cxids;
				console.log(cxid == 0)
				if (cxid == 0 || cxid == '') {
					allbuydata[bid].cuxiaoid = '';
					allbuydata[bid].cuxiao_money = 0;
					allbuydata[bid].cuxiaoname = '不使用促销';
					allbuydata[bid].cuxiaonameArr = [];
				} else {
					allbuydata[bid].cuxiaoid = [];
					allbuydata[bid].cuxiao_money = 0;
					allbuydata[bid].cuxiaotype = [];
					allbuydata[bid].cuxiaonameArr = [];
					console.log(that.cuxiaoList.info)
					if(that.cuxiaoList.info && that.cuxiaoList.info.length > 0) {
						for (var i in that.cuxiaoList.info) {
							var cxtype = that.cuxiaoList.info[i].type;
							console.log(cxtype);
							if (cxtype == 1 || cxtype == 6) {
								//满额立减 满件立减
								allbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'] * -1;
							} else if (cxtype == 2) {
								//满额赠送
								allbuydata[bid].cuxiao_money += 0;
							} else if (cxtype == 3) {
								//加价换购  27.8+15.964+41.4
								allbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'];
							} else if (cxtype == 4 || cxtype == 5) {
								//满额打折 满件打折
								var cuxiaoMoney = 0;
								for ( var y in that.allbuydata[bid].cuxiaolist) {
									if(that.cuxiaoList.info[i].id == that.allbuydata[bid].cuxiaolist[y].id) {
										cuxiaoMoney = that.allbuydata[bid].cuxiaolist[y].cuxiaomoney;
									}
								}
								console.log('cuxiaoMoney');
								console.log(cuxiaoMoney);
								allbuydata[bid].cuxiao_money += cuxiaoMoney * -1
							}
							allbuydata[bid].cuxiaoid.push(that.cuxiaoList.info[i].id);
							allbuydata[bid].cuxiaotype.push(cxtype);
							allbuydata[bid].cuxiaonameArr.push(that.cuxiaoList.info[i]['name']);
						}
						console.log('allbuydata[bid]');
						console.log(allbuydata[bid]);
					} else {
						var cxtype = that.cuxiaoinfo.info.type;
						console.log(cxtype);
						if (cxtype == 1 || cxtype == 6) {
							//满额立减 满件立减
							allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'] * -1;
						} else if (cxtype == 2) {
							//满额赠送
							allbuydata[bid].cuxiao_money = 0;
						} else if (cxtype == 3) {
							//加价换购  27.8+15.964+41.4
							allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'];
						} else if (cxtype == 4 || cxtype == 5) {
							//var product_price = parseFloat(allbuydata[bid].product_price);
							//var leveldk_money = parseFloat(allbuydata[bid].leveldk_money); //会员折扣
							//var manjian_money = parseFloat(allbuydata[bid].manjian_money); //满减活动
							//满额打折 满件打折
							//allbuydata[bid].cuxiao_money = (1 - that.cuxiaoinfo.info['zhekou'] * 0.1) * (product_price - leveldk_money - manjian_money) * -1;
							allbuydata[bid].cuxiao_money = that.cuxiaoinfo.cuxiaomoney * -1
						}
						allbuydata[bid].cuxiaoid = cxid;
						allbuydata[bid].cuxiaotype = cxtype;
						allbuydata[bid].cuxiaoname = that.cuxiaoinfo.info['name'];
					}
				}
				this.allbuydata = allbuydata;
				this.cuxiaovisible = false;
				this.calculatePrice();
			},
			showType11List: function(e) {
				this.type11visible = true;
				this.bid = e.currentTarget.dataset.bid;
			},
			changetype11: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = that.bid;
				that.type11key = e.currentTarget.dataset.index;
				// console.log(that.type11key)
			},
			chooseType11: function(e) {
				var that = this;
				var allbuydata = that.allbuydata;
				var bid = that.bid;
				var type11key = that.type11key;
				if (type11key == -1) {
					app.error('请选择物流');
					return;
				}
				allbuydata[bid].type11key = type11key + 1;
				// console.log(allbuydata[bid].type11key)
				var freightkey = allbuydata[bid].freightkey;
				var freightList = allbuydata[bid].freightList;
				var freight_price = parseFloat(freightList[freightkey].type11pricedata[type11key].price);
				var product_price = parseFloat(allbuydata[bid].product_price);
				// console.log(freightList[freightkey].freeset);
				// console.log(parseFloat(freightList[freightkey].free_price));
				// console.log(product_price);
				if (freightList[freightkey].freeset == 1 && parseFloat(freightList[freightkey].free_price) <=
					product_price) {
					freight_price = 0;
				}
				allbuydata[bid].freightList[freightkey].freight_price = freight_price;

				this.allbuydata = allbuydata;
				this.type11visible = false;
				this.calculatePrice();
			},
			openMendian: function(e) {
				var allbuydata = this.allbuydata
				var bid = e.currentTarget.dataset.bid;
				var freightkey = e.currentTarget.dataset.freightkey;
				var storekey = e.currentTarget.dataset.storekey;
				var frightinfo = allbuydata[bid].freightList[freightkey]
				var storeinfo = frightinfo.storedata[storekey];
				// console.log(storeinfo)
				app.goto('mendian?id=' + storeinfo.id);
			},
			openLocation: function(e) {
				var allbuydata = this.allbuydata
				var bid = e.currentTarget.dataset.bid;
				var freightkey = e.currentTarget.dataset.freightkey;
				var storekey = e.currentTarget.dataset.storekey;
				var frightinfo = allbuydata[bid].freightList[freightkey]
				var storeinfo = frightinfo.storedata[storekey];
				// console.log(storeinfo)
				var latitude = parseFloat(storeinfo.latitude);
				var longitude = parseFloat(storeinfo.longitude);
				var address = storeinfo.name;
				uni.openLocation({
					latitude: latitude,
					longitude: longitude,
					name: address,
					scale: 13
				})
			},
			editorChooseImage: function (e) {
				var that = this;
				var bid = e.currentTarget.dataset.bid;
				var idx = e.currentTarget.dataset.idx;
				var editorFormdata = that.allbuydata[bid].editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				app.chooseImage(function(data){
					editorFormdata[idx] = data[0];
					// console.log(editorFormdata)
					that.allbuydata[bid].editorFormdata = editorFormdata
					that.test = Math.random();
				})
			},
			editorBindPickerChange:function(e){
				var that = this;
				var bid = e.currentTarget.dataset.bid;
				var idx = e.currentTarget.dataset.idx;
				var val = e.detail.value;
				var editorFormdata = that.allbuydata[bid].editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = val;
				// console.log(editorFormdata)
				that.allbuydata[bid].editorFormdata = editorFormdata;
				that.test = Math.random();
			},

			showMemberList: function(e) {
				this.membervisible = true;
			},
			regionchange2: function(e) {
				const value = e.detail.value
				// console.log(value[0].text + ',' + value[1].text + ',' + value[2].text);
				this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text
			},
			memberSearch: function() {
				var that = this;
				// console.log(that.regiondata)
				app.post('ApiShop/memberSearch', {
					diqu: that.regiondata
				}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					var data = res.memberList;
					that.memberList = data;
				});
			},
			checkMember: function(e) {
				var that = this;
				that.checkMem = e.currentTarget.dataset.info;
				this.membervisible = false;
			},
			showmemberinfo:function(e){
				var that = this;
				var mid = e.currentTarget.dataset.mid;
				app.showLoading('提交中');
				app.post('ApiShop/getmemberuplvinfo',{mid:mid}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					that.selectmemberinfo = res.info;
					that.memberinfovisible = true;
				});
			},
			memberinfoClickMask:function(){
				this.memberinfovisible = false;
			},
			doStoreShowAll:function(){
				this.storeshowall = true;
			},
			showglass:function(e){
				var that = this
				var grid = e.currentTarget.dataset.grid;
				var index = e.currentTarget.dataset.index;
				var index2 = e.currentTarget.dataset.index2;
				console.log(grid)
				console.log(index)
				console.log(index2)
				// console.log(that.glassrecordlist)
				if(that.glassrecordlist.length<1){
					//没有数据 就重新请求
					that.loading;
					app.post('ApiGlass/myrecord', {pagenum:1,listrow:100}, function (res) {
						that.loading = false;
					  var datalist = res.data;
						console.log(datalist)
						that.glassrecordlist = datalist;
						// console.log(that.glassrecordlist);
						// if(datalist.length>0){
						// 	// 
						// 	that.isshowglass = true
						// }else{
						// 	app.error('无可用的视力档案')
						// }
						that.isshowglass = true
					});
					
				}else{
					that.isshowglass = true
				}
				that.curindex = index
				that.curindex2 = index2
				that.grid = grid
			},
			hideglass:function(e){
				var that = this
				that.isshowglass = false;
			},
			chooseglass:function(e){
				var that = this;
				var gindex = e.detail.value;
				var allbuydata = that.allbuydata;
				var grid = that.grid;
				var index = that.curindex;
				var index2 = that.curindex2;
				console.log(gindex+'-'+that.curindex+'-'+that.curindex2)
				var glassrecordlist = that.glassrecordlist;
				var product = allbuydata[index]['prodata'][index2].product
				var sid = glassrecordlist[gindex].id
				if(grid==sid){
					product.glassrecord = {};
					that.grid = 0;
				}else{
					product.glassrecord = glassrecordlist[gindex]
					that.grid = glassrecordlist[gindex].id
				}
				
				that.allbuydata[index]['prodata'][index2]['product'] = product;
				that.isshowglass = false;
			}
		}
	}
</script>

<style>
	.redBg{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx; width: auto; display: inline-block; margin-top: 4rpx;}
.address-add {width: 94%;margin: 20rpx 3%;background: #fff;border-radius: 20rpx;padding: 20rpx 3%;min-height: 140rpx;}
.address-add .f1 {margin-right: 20rpx}
.address-add .f1 .img {width: 66rpx;height: 66rpx;}
.address-add .f2 {color: #666;}
.address-add .f3 {width: 26rpx;height: 26rpx;}
.linkitem {width: 100%;padding: 1px 0;background: #fff;display: flex;align-items: center}.cf3 {width: 200rpx;height: 26rpx;display: block;
    text-align: right;}
.linkitem .f1 {width: 160rpx;color: #111111}
.linkitem .input {height: 50rpx;padding-left: 10rpx;color: #222222;font-weight: bold;font-size: 28rpx;flex: 1}
.buydata {width: 94%;margin: 0 3%;background: #fff;margin-bottom: 20rpx;border-radius: 20rpx;}
.btitle {width: 100%;padding: 20rpx 20rpx;display: flex;align-items: center;color: #111111;font-weight: bold;font-size: 30rpx}
.btitle .img {width: 34rpx;height: 34rpx;margin-right: 10rpx}
.bcontent {width: 100%;padding: 0 20rpx}
.product {width: 100%;border-bottom: 1px solid #f4f4f4}
.product .item {width: 100%;padding: 20rpx 0;background: #fff;border-bottom: 1px #ededed dashed;}
.product .item:last-child {border: none}
.product .info {padding-left: 20rpx;}
.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
.product .info .f2 {color: #999999;font-size: 24rpx}
.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}
.product image {width: 140rpx;height: 140rpx}
.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}
.freight .f1 {color: #333;margin-bottom: 10rpx}
.freight .f2 {color: #111111;text-align: right;flex: 1}
.freight .f3 {width: 24rpx;height: 28rpx;}
.freighttips {color: red;font-size: 24rpx;}
.freight-ul {width: 100%;display: flex;}
.freight-li {flex-shrink: 0;display: flex;background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;text-align: center;height: 48rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0}
.inputPrice {border: 1px solid #ddd; width: 200rpx; height: 40rpx; border-radius: 10rpx; padding: 0 4rpx;}

.price {width: 100%;padding: 20rpx 0;background: #fff;display: flex;align-items: center}
.price .f1 {color: #333}
.price .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}
.price .f3 {width: 24rpx;height: 24rpx;}
.price .couponname{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;display:inline-block;margin:2rpx 0 2rpx 10rpx}
.scoredk {width: 94%;margin: 0 3%;margin-bottom: 20rpx;border-radius: 20rpx;padding: 24rpx 20rpx;background: #fff;display: flex;align-items: center}
.scoredk .f1 {color: #333333}
.scoredk .f2 {color: #999999;text-align: right;flex: 1}
.remark {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center}
.remark .f1 {color: #333;width: 200rpx}
.remark input {border: 0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right}
.footer {width: 96%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 2%;display: flex;align-items: center;z-index: 8;box-sizing:content-box}
.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}
.footer .text1 text {color: #e94745;font-size: 32rpx;}
.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}
.footer .op[disabled] { background: #aaa !important; color: #666;}
.storeitem {width: 100%;padding: 20rpx 0;display: flex;flex-direction: column;color: #333}
.storeitem .panel {width: 100%;height: 60rpx;line-height: 60rpx;font-size: 28rpx;color: #333;margin-bottom: 10rpx;display: flex}
.storeitem .panel .f1 {color: #333}
.storeitem .panel .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}
.storeitem .radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;border-bottom: 0 solid #eee;padding: 8rpx 20rpx;}
.storeitem .radio-item:last-child {border: 0}
.storeitem .radio-item .f1 {color: #666;flex: 1}
.storeitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left: 30rpx}
.storeitem .radio .radio-img {width: 100%;height: 100%}
.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}
.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.pstime-item .radio .radio-img {width: 100%;height: 100%}
.cuxiao-desc {width: 100%}
.cuxiao-item {display: flex;padding: 0 40rpx 20rpx 40rpx;}
.cuxiao-item .type-name {font-size: 28rpx;color: #49aa34;margin-bottom: 10rpx;flex: 1}
.cuxiao-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.cuxiao-item .radio .radio-img {width: 100%;height: 100%}

.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}
.form-item .label {color: #333;width: 200rpx;flex-shrink:0}
.form-item .radio{transform:scale(.7);}
.form-item .checkbox{transform:scale(.7);}
.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}
.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}
.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}
.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .radio2{display:flex;align-items:center;}
.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}
.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}

.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.form-imgbox-close .image{width:100%;height:100%}
.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.form-imgbox-img>.image{max-width:100%;}
.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.form-uploadbtn{position:relative;height:180rpx;width:180rpx}

.member_search{width:100%;padding:0 40rpx;display:flex;align-items:center}
.searchMemberButton{height:60rpx;background-color: #007AFF;border-radius: 10rpx;width: 160rpx;line-height: 60rpx;color: #fff;text-align: center;font-size: 28rpx;display: block;}
.memberlist{width:100%;padding:0 40rpx;height: auto;margin:20rpx auto;}
.memberitem{display:flex;align-items:center;border-bottom:1px solid #f5f5f5;padding:20rpx 0}
.memberitem image{display: block;height:100rpx;width:100rpx;margin-right:20rpx;}
.memberitem .t1{color:#333;font-weight:bold}
.memberitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}
.memberitem .radio .radio-img {width: 100%;height: 100%}

.checkMem{ display: inline-block; }
.checkMem p{ height: 30px; width: 100%; display: inline-block; }
.placeholder{  font-size: 26rpx;line-height: 80rpx;}
.selected-item span{ font-size: 26rpx !important;}
.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;flex-shrink:0}
.orderinfo .item .t2{flex:1;text-align:right}
.orderinfo .item .red{color:red}

.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}

.btn{ height:80rpx;line-height: 80rpx;width:90%;margin:0 auto;border-radius:40rpx;margin-top:40rpx;color: #fff;font-size: 28rpx;font-weight:bold}
.invoiceBox .radio radio{transform: scale(0.8);}
.invoiceBox .radio:nth-child(2) { margin-left: 30rpx;}
.glassinfo{color: #333; padding:10rpx; border-radius: 10rpx;display: flex;justify-content: space-between;align-items: center;background: #f4f4f4;margin-top: 10rpx;}
.glassinfo .f2{display: flex;justify-content: flex-end;}
.glassinfo .f2 image{width: 32rpx;height: 36rpx;padding-top: 4rpx;}
.glassinfo .f1{font-weight: bold;}

.glass_popup{
	
}
.glass_popup .popup__title{padding: 30rpx 0 0 0;}
.glassitem{background:#eeeeee;border-radius: 10rpx;width: 94%;margin: 20rpx 3%;display: flex;align-items: center;padding: 20rpx 0;}
.glassitem.on{background: #ffe6c8;}
.glassitem .radio{width: 80rpx;flex-shrink: 0;text-align: center;}
.glassitem .gcontent{flex:1;padding: 0 20rpx;}
.glassrow{display: flex;padding: 10rpx 0;align-items: center;}
.glassrow .glasscol{min-width: 25%;text-align: center;}
.glassitem .bt{border-top:1px solid #e3e3e3}
</style>
