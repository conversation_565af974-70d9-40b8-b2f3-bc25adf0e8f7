<template>
<view class="container">
	<block v-if="isload">
		<view class="content">
			<view class="info-item">
				<view class="t1">买入方</view>
				<view class="t2">{{sellname.sellname}}</view>
			</view>
			<view class="info-item">
				<view class="t1">联系方式</view>
				<view class="t2">{{sellname.tel}}</view>
			</view>
		</view>
		<view class="content">
			<view class="info-item">
				<view class="t1">支付凭证</view>
			</view>
			<view>
				<image :src="payorder.paypics"/>
			</view>
		</view>
		<form @submit="formSubmit" @reset="formReset">
		<view class="content">
			<view class="form-item">
					<text class="label">审核</text>
					<radio-group class="radio-group" name="shenhe_status">
					<label class="radio">
						<radio value="1"></radio>审核通过
					</label>
					<label class="radio">
						<radio value="2"></radio>审核驳回
					</label>
					</radio-group>
			</view>
			<view class="form-item">
				<input type="text" class="input" placeholder="请输入审核信息" placeholder-style="color:#BBBBBB;font-size:28rpx" name="check_remark" :value="payorder.check_remark"></input>
			</view>
			<button class="set-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">提 交</button>
		</view>
		</form>
		
		<!-- #ifdef APP-PLUS -->
		<view class="content">
			<view class="info-item" @tap="delaccount">
				<view class="t1">注销账号</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<!-- #endif -->
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
	  sellname:{},
			menuindex:-1,
			
			data:{},
			payorder:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMiaosha/shenhe', {id:that.opt.id}, function (data) {
				that.loading = false;
				if (data.status == 0) {
					app.error(data.msg);
					return;
				}
				that.data = data.data;
				that.sellname = data.sellname;
				that.payorder = data.payorder;
				that.loaded();
			});
		},
		
		formSubmit: function (e) {
			var that = this;
		  var formdata = e.detail.value;
				var check_remark = formdata.check_remark
				var shenhe_status = formdata.shenhe_status
		  if (check_remark == '') {
		    app.alert('请输入审核信息');return;
		  }
		  if (check_remark == '') {
		    app.alert('请审核');return;
		  }
				app.showLoading('提交中');
		  app.post("ApiMiaosha/saveshenhe", {check_remark:check_remark,shenhe_status:shenhe_status,id:that.opt.id}, function (data) {
					app.showLoading(false);
		    if (data.status == 1) {
		      app.success(data.msg);
		      setTimeout(function () {
		        app.goback(true);
		      }, 1000);
		    } else {
		      app.error(data.msg);
		    }
		  });
		  }
  }
};
</script>
<style>
.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}

.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;}
.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;line-height:98rpx;}
.form-item:last-child{border:0}
.form-item .label{color: #000;width:200rpx;}
.form-item .input{flex:1;color: #000;}
.form-item .radio{height: 60rpx;line-height: 60rpx;color: #666;margin-right:30rpx}
.form-item .radio radio{transform: scale(0.8);}
.set-btn{width: 90%;margin:60rpx 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
</style>