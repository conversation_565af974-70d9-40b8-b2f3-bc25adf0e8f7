<template>
<view class="dp-form" :style="{
	color:params.color,
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	fontSize:(params.fontsize*2)+'rpx'
}">
	<form @submit="editorFormSubmit" :data-formcontent="data.content" :data-tourl="params.hrefurl" :data-formid="data.id">
		<view style="display:none">{{test}}</view>
		
		<view :class="params.style==1?'dp-form-item':'dp-form-item2'" v-for="(item,idx) in data.content" :style="{borderColor:params.linecolor}" :key="item.id">
			<block v-if="item.key=='separate'">
				<view class="dp-form-separate">{{item.val1}}</view>
			</block>
			<view v-if="item.key!='separate'" class="label">{{item.val1}}<text v-if="item.val3==1&&params.showmuststar" style="color:red"> *</text></view>
			<block v-if="item.key=='input'">
				<block v-if="params.style==1">
					<text v-if="item.val5" style="margin-right:10rpx">{{item.val5}}</text>
					<!-- 授权不可编辑S -->
					<block v-if="item.val4==2 && item.val6==1 && (platform =='mp' || platform == 'wx')">
						<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" disabled="true" :name="'form'+idx" class="input disabled" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':'#efefef'}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
						<button class="authtel" :style="{backgroundColor:params.btnbgcolor,color:params.btncolor}"  open-type="getPhoneNumber" type="primary" @getphonenumber="getPhoneNumber" :data-idx="idx">获取手机号码</button>
					</block>
					<!-- 授权不可编辑E -->
					<block v-else>
						<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" readonly :name="'form'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
					</block>
				</block>
				
				<view v-if="params.style==2" class="value">
					<text v-if="item.val5" style="margin-right:10rpx">{{item.val5}}</text>
					<block v-if="item.val4==2 && item.val6==1 && (platform =='mp' || platform == 'wx')">
						<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" disabled="true" :name="'form'+idx" class="input disabled" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':'#efefef'}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
						<button class="authtel" :style="{backgroundColor:params.btnbgcolor,color:params.btncolor}"  open-type="getPhoneNumber" type="primary" @getphonenumber="getPhoneNumber" :data-idx="idx">获取手机号码</button>
					</block>
					<block v-else>
						<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" readonly :name="'form'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
					</block>
				</view>
			</block>
			<block v-if="item.key=='textarea'">
				<textarea :name="'form'+idx" class='textarea' :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
			</block>
			<block v-if="item.key=='radio'">
				<radio-group class="flex" :name="'form'+idx" style="flex-wrap:wrap" @change="setfield" :data-formidx="'form'+idx">
					<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
							<radio class="radio" :value="item1" :checked="formdata['form'+idx] && formdata['form'+idx]==item1 ? true : false"/>{{item1}}
					</label>
				</radio-group>
			</block>
			<block v-if="item.key=='checkbox'">
				<checkbox-group :name="'form'+idx" class="flex" style="flex-wrap:wrap" @change="setfield" :data-formidx="'form'+idx">
					<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
						<checkbox class="checkbox" :value="item1" :checked="formdata['form'+idx] && inArray(item1,formdata['form'+idx]) ? true : false"/>{{item1}}
					</label>
				</checkbox-group>
			</block>
			<block v-if="item.key=='selector'">
				<picker class="picker" mode="selector" :name="'form'+idx" :value="editorFormdata[idx]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view v-if="editorFormdata[idx] || editorFormdata[idx]===0"> {{item.val2[editorFormdata[idx]]}}</view>
					<view v-else>请选择</view>
				</picker>
			</block>
			<block v-if="item.key=='time'">
				<picker class="picker" mode="time" :name="'form'+idx" :value="formdata['form'+idx]" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view>
					<view v-else>请选择</view>
				</picker>
			</block>
			<block v-if="item.key=='date'">
				<picker class="picker" mode="date" :name="'form'+idx" :value="formdata['form'+idx]" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view>
					<view v-else>请选择</view>
				</picker>
			</block>

			<block v-if="item.key=='region'">
				<uni-data-picker :localdata="items" popup-title="请选择省市区" :placeholder="formdata['form'+idx] || '请选择省市区'" @change="onchange" :data-formidx="'form'+idx"></uni-data-picker>
				<!-- <picker class="picker" mode="region" :name="'form'+idx" value="" @change="editorBindPickerChange" :data-idx="idx">
					<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view> 
					<view v-else>请选择省市区</view>
				</picker> -->
				<input type="text" style="display:none" :name="'form'+idx" :value="regiondata ? regiondata : formdata['form'+idx]"/>
			</block>
			<block v-if="item.key=='upload'">
				<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
				<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
					<view class="dp-form-imgbox" v-if="editorFormdata[idx]">
						<view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx"><image src="/static/img/ico-del.png" class="image"></image></view>
						<view class="dp-form-imgbox-img"><image class="image" :src="editorFormdata[idx]" @click="previewImage" :data-url="editorFormdata[idx]" mode="widthFix" :data-idx="idx"/></view>
					</view>
					<view v-else class="dp-form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-idx="idx" :data-formidx="'form'+idx"></view>
				</view>
			</block>
            <!-- #ifdef H5 || MP-WEIXIN -->
            <block v-if="item.key=='upload_file'">
            	<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
            	<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
            		<view class="dp-form-imgbox" v-if="editorFormdata[idx]">
            			<view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx">
                            <image src="/static/img/ico-del.png" class="image"></image>
                        </view>
            			<view  style="overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;width: 530rpx;" @tap="download" :data-file="editorFormdata[idx]" >
                            {{editorFormdata[idx]}}
                        </view>
            		</view>
            		<view v-else class="dp-form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="chooseFile" :data-idx="idx" :data-formidx="'form'+idx"></view>
            	</view>
            </block>
            <!-- #endif -->
		</view>
		<block v-if="data.payset==1">
			<view class="dp-form-item" v-if="!data.is_other_fee || data.is_other_fee==0">
				<text class="label">支付金额：</text>
				<input type="text" class="input" name="price" :value='data.price' v-if="data.priceedit==1" @input="setfield" data-formidx="price"/>
				<text v-if="data.priceedit==0">{{data.price}}</text>
				<text style="padding-left:10rpx">元</text>
			</view>
			<block  v-if="data.is_other_fee==1">
			<view class="dp-form-item" style="border: none;">
				<view class="dp-form-label">费用明细</view>
				<view class="dp-form-feelist">
				<checkbox-group name="feelist">
					<view class="dp-fee-item" v-for="(item,index) in data.fee_items" :key="index">
						<view class="dp-fee-name">{{item.name}}</view>
						<view class="dp-fee-money">￥{{item.money}}</view>
						<view class="dp-fee-check"><checkbox @click="feeChange" :data-index="index" :value="''+index" :checked="item.checked?true:false" :color="t('color1')" style="transform: scale(0.7);"></checkbox></view>
					</view>
				</checkbox-group>
				<view class="dp-fee-item sum">
					<view class="dp-fee-name">合计</view>
					<view class="dp-fee-money">￥<text>{{feetotal}}</text></view>
					<view class="dp-fee-check"></view>
				</view>
				</view>
			</view>
			</block>
		</block>
		<button @tap="editorFormSubmit" v-if="data != ''" class="dp-form-btn" :style="{backgroundColor:params.btnbgcolor,border:'1px solid '+params.btnbordercolor,fontSize:(params.btnfontsize*2)+'rpx',color:params.btncolor,width:(params.btnwidth*2.2)+'rpx',height:(params.btnheight*2.2)+'rpx',lineHeight:(params.btnheight*2.2)+'rpx',borderRadius:(params.btnradius*2.2)+'rpx'}" :data-formcontent="data.content" :data-tourl="params.hrefurl" :data-formid="data.id">{{params.btntext}}</button>
	</form>
</view>
</template>
<script>
	var app = getApp();
	export default {
		data(){
			return {
				pre_url:getApp().globalData.pre_url,
				editorFormdata:[],
				test:'test',
				regiondata:'',
				items: [],
				tmplids: [],
				submitDisabled:false,
				formdata:{},
				formvaldata:{},
				authphone:'',
				platform:'',
				feetotal:0
			}
		},
		props: {
			params:{},
			data:{},
			latitude:'',
			longitude:'',
		},
		mounted:function(){
			var that = this;
			that.platform = app.getplatform();
			app.get('ApiIndex/getCustom',{}, function (customs) {
				var url = app.globalData.pre_url+'/static/area.json';
				if(customs.data.includes('plug_zhiming')) {
					url = app.globalData.pre_url+'/static/area_gaoxin.json';
				}
				uni.request({
					url: app.globalData.pre_url+'/static/area.json',
					data: {},
					method: 'GET',
					header: { 'content-type': 'application/json' },
					success: function(res2) {
						that.items = res2.data
					}
				});
			});
			that.checkPayMoney()

			var pages = getCurrentPages(); //获取加载的页面
			var currentPage = pages[pages.length - 1]; //获取当前页面的对象
			var thispath = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url 
			var opts = currentPage.$vm.opt;
			console.log(opts)
			var fromrecord = 0;
			if(opts && opts.fromrecord){
				fromrecord = opts.fromrecord;
			}
			app.get('ApiElectricityForm/getlastformdata',{formid:that.data.id,fromrecord:fromrecord}, function (res) {
				if(res && res.status == 1 && res.data){
					var formcontent = that.data.content;
					var editorFormdata = [];
					var formvaldata = {};
					formvaldata.price = that.data.price
					for(var i in formcontent){
						var thisval = res.data['form'+i];
						if (formcontent[i].key == 'region') {
							that.regiondata = thisval;
						}
						if (formcontent[i].key == 'selector') {
							for(var j in formcontent[i].val2){
								if(formcontent[i].val2[j] == res.data['form'+i]){
									thisval = j;
								}
							}
						}
						if (formcontent[i].key == 'checkbox') {
							if(res.data['form'+i]){
								res.data['form'+i] = (res.data['form'+i]).split(',');
							}else{
								res.data['form'+i] = [];
							}
						}
						editorFormdata.push(thisval);
						formvaldata['form'+i] = thisval;
					}
					that.editorFormdata = editorFormdata;
					that.formvaldata = formvaldata;
					that.formdata = res.data;
				}else{
					var formvaldata = {};
					formvaldata.price = that.data.price;
					that.formvaldata = formvaldata;
				}
			})
		},
		methods:{
			onchange(e) {
				console.log(e) 
        const value = e.detail.value
				console.log(value[0].text + ',' + value[1].text + ',' + value[2].text)
				this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;
      },
			setfield:function(e){
				var field = e.currentTarget.dataset.formidx;
				var value = e.detail.value;
				this.formvaldata[field] = value;
			},
			editorFormSubmit:function(e){
				var that = this;
				if(this.submitDisabled) return ;
				//console.log('form发生了submit事件，携带数据为：', e.detail.value)
				var subdata = e.detail.value;
				var subdata = this.formvaldata;
				console.log(subdata)
				var formcontent = e.currentTarget.dataset.formcontent;
				var formid = e.currentTarget.dataset.formid;
				var tourl = e.currentTarget.dataset.tourl;
				var formdata = new Array();
				for (var i = 0; i < formcontent.length;i++){
					//console.log(subdata['form' + i]);
					if (formcontent[i].key == 'region') {
							subdata['form' + i] = that.regiondata;
					}
					if (formcontent[i].key!='separate' && formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === null || subdata['form' + i] === undefined || subdata['form' + i].length==0)){
							app.alert(formcontent[i].val1+' 必填'+formcontent[i].val2);return;
					}
					if (formcontent[i].key =='switch'){
							if (subdata['form' + i]==false){
									subdata['form' + i] = '否'
							}else{
									subdata['form' + i] = '是'
							}
					}
					if (formcontent[i].key == 'selector') {
							subdata['form' + i] = formcontent[i].val2[subdata['form' + i]]
					}
					if (formcontent[i].key == 'input' && formcontent[i].val4 && subdata['form' + i]!==''){
						if(formcontent[i].val4 == '2'){ //手机号
							if (!/^1[3456789]\d{9}$/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
						if(formcontent[i].val4 == '3'){ //身份证号
							if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
						if(formcontent[i].val4 == '4'){ //邮箱
							if (!/^(.+)@(.+)$/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
					}
					formdata.push(subdata['form' + i])
				}
				
				//范围
				if(that.data.fanwei == 1 && (that.latitude == '' || that.longitude == '')) {
					app.alert('请定位您的位置或者刷新重试');return;
				}
				var feedata = [];
				if(that.data.payset==1 && that.data.is_other_fee==1){
					var feeitems = that.data.fee_items
					var feenum = 0;
					var feetotal  = 0;
					for(let i in feeitems){
						if(feeitems[i].checked){
							feenum++;
							feetotal = feetotal + parseFloat(feeitems[i].money);
							feedata.push(feeitems[i])
						}
					}
					if(feenum<1){
						app.error('请选择费用明细');
						return;
					}else{
						feetotal = feetotal.toFixed(2);
						formdata.price = feetotal;
						subdata.price = feetotal;
					}
					
				}
				//console.log(formdata);
				that.submitDisabled = true;
				app.showLoading('提交中');

				var pages = getCurrentPages(); //获取加载的页面
				var currentPage = pages[pages.length - 1]; //获取当前页面的对象
				var thispath = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url 
				var opts = currentPage.$vm.opt;

				var posturl = 'ApiElectricityForm/formsubmit';
				if(that.params.isquery == '1'){
					posturl = 'ApiElectricityForm/formquery';
				}
                
                var edit_id = 0;
                if(opts && opts.fromrecord && opts.type){
                	if(opts.type == 'edit'){
                        edit_id = opts.fromrecord;
                    }
                }
                
				app.post(posturl, {formid:formid,formdata:subdata,price:subdata.price,fromurl:thispath+'?id='+opts.id,latitude:that.latitude,longitude:that.longitude,edit_id:edit_id,feedata:feedata},function(data){
					that.tmplids = data.tmplids;
					app.showLoading(false);
					if (data.status == 0) {
						//that.showsuccess(res.data.msg);
						setTimeout(function () {
							app.error(data.msg);
						}, 100)
						that.submitDisabled = false;
						return;
					}else if(data.status == 1) { //无需付款
						if(that.params.isquery == '1'){
							that.submitDisabled = false;
							app.goto(data.tourl);return;
						}
						that.subscribeMessage(function () {
							setTimeout(function () {
								app.success(data.msg);
							}, 100)
							setTimeout(function () {
								app.goto(tourl);
							}, 1000)
						});
						return;
					}else if(data.status==2){
						that.subscribeMessage(function () {
							setTimeout(function () {
								app.goto('/pages/pay/pay?id='+data.payorderid+'&tourl='+tourl);
							}, 100);
						});
					}
					that.submitDisabled = false;
				});
			},
			editorChooseImage: function (e) {
				var that = this;
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				app.chooseImage(function(data){
					editorFormdata[idx] = data[0];
					console.log(editorFormdata)
					that.editorFormdata = editorFormdata
					that.test = Math.random();

					var field = e.currentTarget.dataset.formidx;
					that.formvaldata[field] = data[0];

				})
			},
			removeimg:function(e){
				var that = this;
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var field = e.currentTarget.dataset.formidx;
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = '';
				that.editorFormdata = editorFormdata
				that.test = Math.random();
				that.formvaldata[field] = '';
			},
			editorBindPickerChange:function(e){
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var val = e.detail.value;
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = val;
				console.log(editorFormdata)
				this.editorFormdata = editorFormdata
				this.test = Math.random();

				var field = e.currentTarget.dataset.formidx;
				this.formvaldata[field] = val;
			},
			getPhoneNumber: function (e) {
				var that = this
				var idx = e.currentTarget.dataset.idx;
				var field = 'form'+idx;
				if(that.authphone){
					that.test = Math.random()
					that.formdata['form'+idx] = that.authphone;
					that.formvaldata[field] = that.authphone;
					return true;
				}
				if(e.detail.errMsg == "getPhoneNumber:fail user deny"){
					app.error('请同意授权获取手机号');return;
				}
				if(!e.detail.iv || !e.detail.encryptedData){
					app.error('请同意授权获取手机号');return;
				}
				wx.login({success (res1){
					console.log('res1')
					console.log(res1);
					var code = res1.code;
					//用户允许授权
					app.post('ApiIndex/authphone',{ iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid},function(res2){
						if (res2.status == 1) {
							that.authphone = res2.tel;
							that.test = Math.random()
							that.formdata['form'+idx] = that.authphone;
							that.formvaldata[field] = that.authphone;
						} else {
							app.error(res2.msg);
						}
						return;
					})
				}});
			},
            download:function(e){
                var that = this;
                var file = e.currentTarget.dataset.file;
                // #ifdef H5
                    window.location.href= file;
                // #endif
                
                // #ifdef MP-WEIXIN
                uni.downloadFile({
                	url: file, 
                	success: (res) => {
                        var filePath = res.tempFilePath;
                		if (res.statusCode === 200) {
                			uni.openDocument({
                              filePath: filePath,
                              showMenu: true,
                              success: function (res) {
                                console.log('打开文档成功');
                              }
                            });
                		}
                	}
                });
                // #endif
            },
            chooseFile:function(e){
                var that = this;
                var idx = e.currentTarget.dataset.idx;
                var field = e.currentTarget.dataset.formidx;
                
                var editorFormdata = this.editorFormdata;
                if(!editorFormdata) editorFormdata = [];
                
                var up_url = app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id;
                
                // #ifdef H5
                uni.chooseFile({
                    count: 1, //默认100
                    extension:['.zip','.exe','.js'],
                    success: function (res) {
                        console.log(res);
                        const tempFilePaths = res.tempFiles;
                        
                        //for (var i = 0; i < tempFilePaths.length; i++) {
                        	app.showLoading('上传中');
                            console.log(tempFilePaths[0]);
                        	uni.uploadFile({
                        		url: up_url,
                        		filePath: tempFilePaths[0]['path'],
                        		name: 'file',
                        		success: function(res) {
                        			app.showLoading(false);
                        			var data = JSON.parse(res.data);
                        			if (data.status == 1) {
                                        that.formvaldata[field] = data.url;
                                        
                                        editorFormdata[idx] = data.url;
                                        that.editorFormdata = editorFormdata;
                                        that.$set(that.editorFormdata, idx,data.url)
                        			} else {
                        				app.alert(data.msg);
                        			}
                        		},
                        		fail: function(res) {
                        			app.showLoading(false);
                        			app.alert(res.errMsg);
                        		}
                        	});
                        //}
                    }
                });
                // #endif
                // #ifdef MP-WEIXIN
                    wx.chooseMessageFile({
                      count: 1,
                      type: 'file',
                      success (res) {
                        // tempFilePath可以作为 img 标签的 src 属性显示图片
                        const tempFilePaths = res.tempFiles
                        console.log(tempFilePaths);
                        
                       
                        //for (var i = 0; i < tempFilePaths.length; i++) {
                        	app.showLoading('上传中');
                            console.log(tempFilePaths[0]);
                        	uni.uploadFile({
                        		url: up_url,
                        		filePath: tempFilePaths[0]['path'],
                        		name: 'file',
                        		success: function(res) {
                        			app.showLoading(false);
                        			var data = JSON.parse(res.data);
                        			if (data.status == 1) {
                                        that.formvaldata[field] = data.url;
                                        
                                        editorFormdata[idx] = data.url;
                                        that.editorFormdata = editorFormdata;
                                        that.$set(that.editorFormdata, idx,data.url)
                        			} else {
                        				app.alert(data.msg);
                        			}
                        		},
                        		fail: function(res) {
                        			app.showLoading(false);
                        			app.alert(res.errMsg);
                        		}
                        	});
                        //}
                      },
                      complete(res){
                          console.log(res)
                      }
                    })
                // #endif
            },
						checkPayMoney:function(){
							var that = this
							var data = that.data
							var feetotal = 0;
							if(data && data.is_other_fee){
								var feeitmes = data.fee_items;
								for(let i in feeitmes){
									feetotal += parseFloat(feeitmes[i].money)
									feeitmes[i]['checked'] = true
								}
								that.data.fee_items = feeitmes
								that.feetotal = feetotal.toFixed(2)
							}
						},
						feeChange:function(e){
							var that = this;
							var index = e.currentTarget.dataset.index
							var feeitems = that.data.fee_items
							if(feeitems[index].checked){
								feeitems[index].checked = false
							}else{
								feeitems[index].checked = true
							}
							var feetotal = 0;
							for(let i in feeitems){
								if(feeitems[i].checked){
									feetotal = feetotal + parseFloat(feeitems[i].money)
								}
							}
							that.feetotal = feetotal.toFixed(2);
							that.data.fee_items = feeitems
						}
		}
	}
</script>
<style>
.dp-form{height: auto; position: relative;overflow: hidden; padding: 10rpx 0px; background: #fff;}
.dp-form .radio{transform:scale(.7);}
.dp-form .checkbox{transform:scale(.7);}
.dp-form-item{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;align-items: center;}
.dp-form-item:last-child{border:0}
.dp-form-item .label{line-height: 70rpx;width:140rpx;margin-right: 10px;flex-shrink:0;text-align: right;}
.dp-form-item .input{height: 70rpx;line-height: 70rpx;overflow: hidden;flex:1;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff;flex: 1;}
.dp-form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}
.dp-form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.dp-form-item .radio2{display:flex;align-items:center;}
.dp-form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.dp-form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item .layui-form-switch{}
.dp-form-item .picker{height: 70rpx;line-height:70rpx;flex:1;}

.dp-form-item2{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;flex-direction:column;align-items: flex-start;}
.dp-form-item2:last-child{border:0}
.dp-form-item2 .label{height:70rpx;line-height: 70rpx;width:100%;margin-right: 10px;}
.dp-form-item2 .value{display: flex;justify-content: flex-start;width: 100%;flex: 1;}
.dp-form-item2 .input{height: 70rpx;line-height: 70rpx;overflow: hidden;width:100%;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff;flex: 1;}
.dp-form-item2 .textarea{height:180rpx;line-height:40rpx;overflow: hidden;width:100%;border:1px solid #eee;border-radius:2px;padding:8rpx}
.dp-form-item2 .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}
.dp-form-item2 .radio2{display:flex;align-items:center;}
.dp-form-item2 .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item2 .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}
.dp-form-item2 .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item2 .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item2 .layui-form-switch{}
.dp-form-item2 .picker{height: 70rpx;line-height:70rpx;flex:1;width:100%;}
.dp-form-btn{margin: 0 auto;background: #ff4f4f;color: #fff;margin-top: 15px;margin-bottom:10px;text-align:center}
.flex-y-center {margin-right: 20rpx;}

.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff;z-index:9;border-radius:50%}
.dp-form-imgbox-close .image{width:100%;height:100%}
.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.dp-form-imgbox-img>.image{max-width:100%;}
.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}
.dp-form-separate{width: 100%;padding: 20rpx;text-align: center;padding: 20rpx;font-size: 36rpx;font-weight: 500;color: #454545;}
.authtel{border-radius: 10rpx; line-height: 68rpx;margin-left: 10rpx;padding: 0 10rpx;}
.input.disabled{background: #EFEFEF;}
.dp-form-feelist{flex: 1;padding-left: 50rpx;}
.dp-fee-item{display: flex;justify-content: flex-start;align-items: center;color: #a3a3a3;font-size: 24rpx;}
.dp-fee-item.sum{color: #222222;font-weight: bold;font-size: 28rpx;padding-top: 10rpx;}
.dp-form-label{flex-shrink: 0;}
.dp-fee-name{flex: 1;width: 60%;}
.dp-fee-money{width: 30%;flex-shrink: 0;}
.dp-fee-check{width: 10%;flex-shrink: 0;}

</style>