-- 团长管理团列表操作功能数据库更新脚本

-- 检查并添加is_hidden字段
ALTER TABLE `daihuotuan` 
ADD COLUMN IF NOT EXISTS `is_hidden` tinyint(1) NOT NULL DEFAULT 0 COMMENT '隐藏状态：0显示，1隐藏';

-- 确保status字段存在
ALTER TABLE `daihuotuan` 
MODIFY COLUMN IF EXISTS `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '上架状态：0未上架，1已上架';

-- 如果status字段不存在，则添加
ALTER TABLE `daihuotuan` 
ADD COLUMN IF NOT EXISTS `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '上架状态：0未上架，1已上架';

-- 确保create_time字段存在
ALTER TABLE `daihuotuan` 
MODIFY COLUMN IF EXISTS `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间';

-- 如果create_time字段不存在，则添加
ALTER TABLE `daihuotuan` 
ADD COLUMN IF NOT EXISTS `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间';

-- 更新现有记录，设置create_time
UPDATE `daihuotuan` SET `create_time` = UNIX_TIMESTAMP() WHERE `create_time` = 0;

-- 添加索引以提高查询性能
ALTER TABLE `daihuotuan` 
ADD INDEX IF NOT EXISTS `idx_status` (`status`),
ADD INDEX IF NOT EXISTS `idx_is_hidden` (`is_hidden`),
ADD INDEX IF NOT EXISTS `idx_create_time` (`create_time`); 