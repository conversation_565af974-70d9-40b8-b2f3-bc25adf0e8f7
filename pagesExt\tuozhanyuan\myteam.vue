<template>
<view class="container">
	<block v-if="isload">
		<view class="search-container">
			<view class="topsearch flex-y-center">
				<view class="f1 flex-y-center">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input :value="keyword" placeholder="搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" @confirm="search"></input>
				</view>
			</view>
		
		</view>
		<!-- <uni-drawer ref="showRight" mode="right" @change="change($event,'showRight')" :width="280">
			<view class="filter-scroll-view">
				<scroll-view class="filter-scroll-view-box" scroll-y="true">
					<view class="search-filter">
						<view class="filter-title">筛选</view>
						<view class="filter-content-title">商家分类</view>
						<view class="search-filter-content">
							<view class="filter-item" :style="catchecid==oldcid?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''" @tap.stop="cateClick" :data-cid="oldcid">全部</view>
							<block v-for="(item, index) in clist" :key="index">
								<view class="filter-item" :style="catchecid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''" @tap.stop="cateClick" :data-cid="item.id">{{item.name}}</view>
							</block>
						</view>
						<view class="search-filter-btn">
							<view class="btn" @tap="filterReset">重置</view>
							<view class="btn2" :style="{background:t('color1')}" @tap="filterConfirm">确定</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</uni-drawer> -->
		<view class="ind_business">
			<view class="ind_buslist" id="datalist">
				<block v-for="(item, index) in datalist" :key="index">
				<view>
					<view class="label">
					
						<text class="t1">{{item.nickname}} </text>
						<text class="t2">{{item.tuozhancate}}</text>
					</view>
					  <view class="divider"></view> 
					<!-- <view class="ind_busbox flex1 flex-row">
						<view class="ind_buspic flex0"><image :src="item.headimg"></image></view>
						<view class="flex1">
							<view class="bus_title">{{item.nickname}}</view>
						    <view class="bus_title" v-if="item.tuozhancate">拓展分类-{{item.tuozhancate}}</view>
							<view class="bus_address" v-if="item.tel" @tap.stop="phone" :data-phone="item.tel"><image src="/static/img/b_tel.png" style="width:26rpx;height:26rpx;margin-right:10rpx"/><text class="x1">联系电话：{{item.tel}}</text></view>
							
						</view>
					</view> -->
					<block v-if="showtype == 0">
					<!-- <scroll-view scroll-x="true" style="width: 510rpx;"> -->
				<view class="prolist" style="margin-top: -10px; background-color: white; padding: 20rpx; border-radius: 10rpx; box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);">
				  <view class="product2">
				    <view class="f2 hah" style="text-align: center; margin-bottom: 10rpx;">{{item.tuozhanedu}}</view>
				    <view class="f2" style="text-align: center;">额度</view>
				  </view>
				  <view class="product2">
				    <button class="action-button" @tap="tomoney" :data-xiajiid="item.id">额度充值</button>
				  </view>
				  <view class="product2">
				    <button class="action-button" @tap="changeClistDialog" :data-xiajiid="item.id">设置拓展员分类</button>
				  </view>
				</view>

				
					</block>
				</view>
				</block>
				<nomore v-if="nomore"></nomore>
				<nodata v-if="nodata"></nodata>
			</view>
		</view>
		<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange" :menuindex="menuindex"></buydialog>
	</block>
	<uni-popup id="dialogInput" ref="dialogInput" type="dialog">
		<uni-popup-dialog mode="input" title="额度充值" value="" placeholder="请输入转入额度" @confirm="tomonenyconfirm"></uni-popup-dialog>
	</uni-popup>
	<view class="popup__container" v-if="clistshow">
		<view class="popup__overlay" @tap.stop="changeClistDialog"></view>
		<view class="popup__modal">
			<view class="popup__title">
				<text class="popup__title-text">请选择拓展员分类</text>
				<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="changeClistDialog"/>
			</view>
			<view class="popup__content">
				<block v-for="(item, index) in clist" :key="item.id">
					<view class="clist-item" @tap="cidsChange" :data-id="item.id">
						<view class="flex1">{{item.name}}-{{item.id}}</view>
						<view class="radio" :style="inArray(item.id,cids) ? 'background:'+t('color1')+';border:0' : ''">
							<image class="radio-img" src="/static/img/checkd.png"/></view>
					</view>
				</block>
			</view>
			<view class="uni-dialog-button-group">
				<view class="uni-dialog-button" @tap.stop="changeClistDialog">
					<text class="uni-dialog-button-text">取消</text>
				</view>
				<view class="uni-dialog-button uni-border-left" @click="dialogDetailtxtConfirm">
					<text class="uni-dialog-button-text uni-button-color">确定</text>
				</view>
			</view>
			<view class="uni-popup-dialog__close" @tap.stop="changeClistDialog">
				<span class="uni-popup-dialog__close-icon "></span>
			</view>
		</view>
	</view>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			userinfo:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			pre_url:app.globalData.pre_url,
      field: 'juli',
			order:'asc',
      oldcid: "",
      catchecid: "",
      longitude: '',
      latitude: '',
			clist:[],
      datalist: [],
      pagenum: 1,
      keyword: '',
      cid: '',
      nomore: false,
      nodata: false,
      types: "",
      showfilter: "",
			showtype:0,
			buydialogShow:false,
			proid:0,
			xiajiid1:0,
			xiajiid:0,
			clistshow:false,
			cids:[],
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.oldcid = this.opt.cid;
		this.catchecid = this.opt.cid;
		this.cid = this.opt.cid;
		this.mingxiid = this.opt.mingxiid;
        if(this.opt.keyword) {
        	this.keyword = this.opt.keyword;
        }
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getDataList(true);
    }
  },
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiBusiness/mylist', {}, function (res) {
				that.loading = false;
				
				that.showtype = res.showtype || 0;
				that.loaded();
			});
			app.getLocation(function (res) {
				var latitude = res.latitude;
				var longitude = res.longitude;
				that.longitude = longitude;
				that.latitude = latitude;
				that.getDataList();
			},
			function () {
				that.getDataList();
			});
		},
    getDataList: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var latitude = that.latitude;
      var longitude = that.longitude;
      var keyword = that.keyword;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
      app.post('ApiYihuo/myshiyebu', {mingxiid:that.mingxiid,pagenum: pagenum,cid: that.cid,field: that.field,order: that.order,longitude: longitude,latitude: latitude,keyword: keyword}, function (res) {
        that.loading = false;
				uni.stopPullDownRefresh();
	    if(res.status == 0)
		{
			app.alert(res.msg, function () {
				app.goto('/pages/my/usercenter', 'redirect');
			})
			return;
		}
		that.userinfo = res.userinfo;
		that.clist = res.clist;
        var data = res.data;
        if (pagenum == 1) {
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
	tomoney:function(e)
	{
		var xiajiid = e.currentTarget.dataset.xiajiid
		this.xiajiid = xiajiid;
		this.$refs.dialogInput.open()
	},
	tomonenyconfirm: function (done, val) {
		
			// console.log(val)
	  var that = this;
	  var money = val;
	  if (money == '' || parseFloat(money) <= 0) {
	    app.alert('请输入转入额度');
	    return;
	  }
	  if (parseFloat(money) > this.userinfo.tuozhanedu) {
	    app.alert('可转入' + that.t('额度') + '不足');
	    return;
	  }
	  done();
			app.showLoading('提交中');
	  app.post('ApiYihuo/commission2money', {money: money,xiajiid:that.xiajiid}, function (data) {
				app.showLoading(false);
	    if (data.status == 0) {
	      app.error(data.msg);
	    } else {
	      that.hiddenmodalput = true;
	      app.success(data.msg);
	      setTimeout(function () {
	        that.getdata();
	      }, 1000);
	    }
	  });
	},
	changeClistDialog:function(e){
		this.clistshow = !this.clistshow
		var xiajiid = e.currentTarget.dataset.xiajiid
		this.xiajiid1 = xiajiid;
	},
	cidsChange:function(e){
		var clist = this.clist;
		var cids = this.cids;
		var cid = e.currentTarget.dataset.id;
		var newcids = [];
		var ischecked = false;
		for(var i in cids){
			if(cids[i] != cid){
				newcids.push(cids[i]);
			}else{
				ischecked = true;
			}
		}
		if(ischecked==false){
			if(newcids.length >= 1){
				app.error('最多只能选择一个分类');return;
			}
			newcids.push(cid);
		}
		this.cids = newcids;
		this.getcnames();
	},
	getcnames:function(){
		var cateArr = this.cateArr;
		var cids = this.cids;
		var cnames = [];
		for(var i in cids){
			cnames.push(cateArr[cids[i]]);
		}
		this.cnames = cnames.join(',');
	},
	dialogDetailtxtConfirm:function(e)
	{
		  var that = this;
		var cids = that.cids;
		if (cids == '' || parseFloat(cids) <= 0) {
		  app.alert('请选择分类');
		  return;
		}
		// done();
					app.showLoading('提交中');
		app.post('ApiYihuo/setcids', {cids: cids,xiajiid:that.xiajiid1}, function (data) {
						app.showLoading(false);
		  if (data.status == 0) {
		    app.error(data.msg);
		  } else {
		    that.hiddenmodalput = true;
		    app.success(data.msg);
		    setTimeout(function () {
		      that.getdata();
		    }, 1000);
		  }
		});
	},
	
		// 打开窗口
		showDrawer(e) {
			console.log(e)
			this.$refs[e].open()
		},
		// 关闭窗口
		closeDrawer(e) {
			this.$refs[e].close()
		},
		// 抽屉状态发生变化触发
		change(e, type) {
			console.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));
			this[type] = e
		},
    cateClick: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
      that.catchecid = cid
    },
		filterConfirm(){
			this.cid = this.catchecid;
			this.gid = this.catchegid;
			this.getDataList();
			this.$refs['showRight'].close()
		},
		filterReset(){
			this.catchecid = this.oldcid;
			this.catchegid = '';
		},
    filterClick: function () {
      this.showfilter = !this.showfilter
    },
    changetab: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
      that.cid = cid
      that.pagenum = 1;
      that.datalist = [];
      that.getDataList();
    },
    search: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
			that.pagenum = 1;
      that.datalist = [];
      that.getDataList();
    },
    sortClick: function (e) {
      var that = this;
      var t = e.currentTarget.dataset;
      that.field = t.field;
      that.order = t.order;
      that.getDataList();
    },
    filterClick: function (e) {
      var that = this;
      var types = e.currentTarget.dataset.types;
      that.types = types;
    },
		openLocation:function(e){
			//console.log(e)
			var latitude = parseFloat(e.currentTarget.dataset.latitude)
			var longitude = parseFloat(e.currentTarget.dataset.longitude)
			var address = e.currentTarget.dataset.address
			uni.openLocation({
			 latitude:latitude,
			 longitude:longitude,
			 name:address,
			 scale: 13
		 })		
		},
		phone:function(e) {
			var phone = e.currentTarget.dataset.phone;
			uni.makePhoneCall({
				phoneNumber: phone,
				fail: function () {
				}
			});
		},
		buydialogChange: function (e) {
			if(!this.buydialogShow){
				this.proid = e.currentTarget.dataset.proid
			}
			this.buydialogShow = !this.buydialogShow;
			console.log(this.buydialogShow);
		},
  }
};
</script>
<style>
.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}
.topsearch{width:100%;padding:16rpx 20rpx;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.topsearch .search-btn{display:flex;align-items:center;color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}
.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0}
.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:28rpx;font-weight:bold;color:#323232}
.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}

.filter-scroll-view{margin-top:var(--window-top)}
.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}
.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}
.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:60rpx 0 30rpx 20rpx;}
.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}
.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}
.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}
.search-filter button .icon{margin-top:6rpx;height:54rpx;}
.search-filter-btn{display:flex;padding:30rpx 30rpx;justify-content: space-between}
.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}
.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}

.ind_business {width: 100%;margin-top: 110rpx;font-size:26rpx;padding:0 24rpx}
.ind_business .ind_busbox{ width:100%;background: #fff;padding:20rpx;overflow: hidden; margin-bottom:20rpx;border-radius:8rpx;position:relative}
.ind_business .ind_buspic{ width:120rpx;height:120rpx; margin-right: 28rpx; }
.ind_business .ind_buspic image{ width: 100%;height:100%;border-radius: 8rpx;object-fit: cover;}
.ind_business .bus_title{ font-size: 30rpx; color: #222;font-weight:bold;line-height:46rpx}
.ind_business .bus_score{font-size: 24rpx;color:#FC5648;display:flex;align-items:center}
.ind_business .bus_score .img{width:24rpx;height:24rpx;margin-right:10rpx}
.ind_business .bus_score .txt{margin-left:20rpx}
.ind_business .indsale_box{ display: flex}
.ind_business .bus_sales{ font-size: 24rpx; color:#999;position:absolute;top:20rpx;right:28rpx}

.ind_business .bus_address{color:#999;font-size: 22rpx;height:36rpx;line-height: 36rpx;margin-top:6rpx;display:flex;align-items:center;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.ind_business .bus_address .x2{padding-left:20rpx}
.ind_business .prolist{white-space: nowrap;margin-top:16rpx; margin-bottom: 10rpx;}
.ind_business .prolist .product{width:158rpx;height:100rpx;overflow:hidden;display:inline-flex;flex-direction:column;align-items:center;margin-right:24rpx}
.ind_business .prolist .product .f1{width:158rpx;height:100rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist .product .f2{font-size:28rpx;color:#666;font-weight:bold;margin-top:4rpx}
.ind_business .prolist .product .hah{color:#fe2b2e;}
.ind_business .prolist2{margin-top:16rpx; margin-bottom: 10rpx;}
.ind_business .prolist2 .product{width:158rpx;overflow:hidden;display:inline-flex;flex-direction:column;margin-right:10rpx;position:relative;min-height:200rpx;padding-bottom:20rpx}
.ind_business .prolist2 .product .f1{width:118rpx;height:118rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist2 .product .f2{font-size:28rpx;color:#FC5648;font-weight:bold;margin-top:4rpx;}
.ind_business .prolist2 .product .f3{font-size:22rpx;font-weight:normal;color: #aaa;text-decoration: line-through;}
.ind_business .prolist2 .product .f4{font-size:20rpx;font-weight:normal;color: #888;}

.ind_business .prolist2 .product .f5{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;top:140rpx;right:0rpx;text-align:center;}
.ind_business .prolist2 .product .f5 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}
.ind_business .prolist2 .product .f5 .img{width:100%;height:100%}
.ind_business .prolist .product2{width:31%;height:100rpx;overflow:hidden;display:inline-flex;flex-direction:column;align-items:center;margin-right:24rpx}
.ind_business .prolist .product2 .f1{width:158rpx;height:100rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist .product2 .f2{font-size:28rpx;color:#666;margin-top:4rpx}
.ind_business .prolist .product2 .hah{color:#fe2b2e;}
.ind_business .prolist2{margin-top:16rpx; margin-bottom: 10rpx;}
.ind_business .prolist2 .product{width:158rpx;overflow:hidden;display:inline-flex;flex-direction:column;margin-right:10rpx;position:relative;min-height:200rpx;padding-bottom:20rpx}
.ind_business .prolist2 .product .f1{width:118rpx;height:118rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist2 .product .f2{font-size:28rpx;color:#FC5648;font-weight:bold;margin-top:4rpx;}
.ind_business .prolist2 .product .f3{font-size:22rpx;font-weight:normal;color: #aaa;text-decoration: line-through;}
.ind_business .prolist2 .product .f4{font-size:20rpx;font-weight:normal;color: #888;}

.ind_business .prolist2 .product .f5{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;top:140rpx;right:0rpx;text-align:center;}
.ind_business .prolist2 .product .f5 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}
.ind_business .prolist2 .product .f5 .img{width:100%;height:100%}
.ind_buslist {border-radius:16rpx;}
.ind_buslist .label{   height: 50px;  display: flex; justify-content: center; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);display:flex;width: 100%;padding: 16rpx;color: #333;margin-top: 20rpx;background-color: #ffffff;}
.ind_buslist .label .t1{flex:1;font-size: 18px;font-weight: bold;}
.ind_buslist .label .t2{ width:300rpx;text-align:right;font-weight: bold;}
.ind_buslist .divider {height: 1px;background-color: #eee;margin: 0px 0;}

.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}
.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}
.radio .radio-img{width:100%;height:100%;display:block}
.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}
.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}
.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}
.uni-dialog-button-text {font-size: 14px;}
.uni-button-color {color: #007aff;}
.content .label{display:flex;width: 100%;padding: 16rpx;color: #333; }
.content .label .t1{flex:1}
.content .label .t2{ width:300rpx;text-align:right}
  .prolist {
    margin-top: -10px;
    background-color: white;
    padding: 20rpx;
    border-radius: 10rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  }

  .product2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .f2 {
    font-size: 28rpx;
    margin: 5rpx 0;
  }

  .action-button {
    width: 100%;
    padding: 10rpx;
    background-color: #ff6f61;
    color: white;
    border: none;
    border-radius: 5rpx;
    text-align: center;
    font-size: 24rpx;
    cursor: pointer;
  }

  .action-button:hover {
    background-color: #e65c55;
  }

  .hah {
    text-align: center;
  }

.section {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  padding: 20px;
  transition: box-shadow 0.3s ease-in-out;
}

.section:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}



.icon-container {
  display: flex;
  justify-content: space-between;
  margin-top: 0px;
  background-color: #ffffff;
}

.icon-item {
  text-align: center;
  flex: 1;
}

.icon {
  font-size: 28px;
  display: block;
  margin-bottom: 8px;
  color: #007bff;
}

/* 图片样式，可以根据需要调整大小 */
.icon-image {
  width: 30px; /* 请根据实际需求调整图片宽度 */
  height: 30px; /* 保持宽高比 */
  margin-bottom: -5px; /* 图片和文字间的间距 */
   margin-top: 10px;
}

</style>