<template>
    <view>
        <block v-if="isload">
            <form @submit="formsubmit">

                <view class="st_box">
                    <!-- #ifndef MP-WEIXIN -->
                    <view class="st_title flex-y-center"
                        style="position: sticky;top: 0rpx;background-color: #fff;z-index: 3;justify-content: space-between;">
                        <view @tap="goback" style="width:100rpx">
                            <image :src="pre_url+'/static/img/goback.jpg'"></image>
                        </view>
						<!-- <view style="margin-right:40%">发布笔记</view> -->
                        <view>
                            <image :src="pre_url+'/static/icon/warning.png'" style="width: 40rpx;" mode="widthFix"></image>
                        </view>
                    </view>
                    <!-- #endif -->
                    
                    <!-- 课程信息展示区域 -->
                    <view class="course-info" v-if="courseInfo && courseInfo.id">
                        <view class="course-header">
                            <view class="course-image">
                                <image :src="courseInfo.pic" mode="aspectFill"></image>
                            </view>
                            <view class="course-details">
                                <view class="course-title">{{courseInfo.name}}</view>
                                <view class="course-desc">{{courseInfo.count}}节课 | 已有{{courseInfo.join_num}}人学习</view>
                            </view>
                        </view>
                    </view>
                    
                    <view class="st_form">
                        <view class="flex" style="flex-wrap:wrap;">
                            <view v-for="(item, index) in pics" :key="index" class="layui-imgbox">
                                <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pics">
                                    <image :src="pre_url+'/static/img/ico-del.png'"></image>
                                </view>
                                <view class="layui-imgbox-img" v-if="item.type == 'img'">
                                    <image :src="item.url" @tap="previewImage" :data-url="item" mode="widthFix"></image>
                                </view>
                                <view class="layui-imgbox-img" v-if="item.type == 'video'">
                                    <image :src="pre_url+'/static/img/uploadvideo.png'"
                                        style="width:200rpx;height:200rpx;background:#eee;"
                                        @click="previewVideo(item.url)"></image>
                                </view>
                            </view>

                            <!-- 上传按钮，根据 shipincheck 的值决定是否显示视频上传选项 -->
                            <view class="uploadbtn"
                                :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 75rpx;background-size:50rpx 50rpx;background-color:#F3F3F3;'"
                                @click="openSelect" v-if="pics.length < 9">
                            </view>
                        </view>

                        <view style="margin: 20rpx 0;">
                            <input placeholder="填写标题会有更多赞哦~" name="title" style="height: 100rpx;"></input>
                        </view>

                        <view style="border-bottom: 1px solid #EEEEEE;padding-bottom: 20rpx;">
                            <textarea placeholder="添加正文" class="feedback-textarea" name="content"
                                maxlength="-1"></textarea>
							<view>
								<!-- 标签 -->
                            <template>
                                <view>
                                    <scroll-view scroll-x="true" style="white-space: nowrap; width: 100%;">
                                        <view class="tag-box" :style="{
                                            'display': 'inline-block',
                                            'color': cindex === index ? '#fff' : '#999999',
                                            'background': cindex === index ? t('color1') : 'transparent'
                                        }" @click="cateChange(index)" v-for="(item, index) in cateArr" :key="index">
                                            {{ item }}
                                        </view>
                                    </scroll-view>
                                </view>
                            </template>
							</view>
                        </view>
                    </view>

                    <input type="text" hidden="true" name="pics" :value="pics" maxlength="-1"></input>
                    <input type="text" hidden="true" name="kcid" :value="kcid" maxlength="-1"></input>
                    <input type="text" hidden="true" name="type" :value="type" maxlength="-1"></input>

                    <!-- 选择商品 -->
                    <view class="picker-container" v-if="bind_product == 1">
                        <view class="picker-label" @tap="openShop">
                            <image :src="pre_url+'/static/img/shortvideo_cart.png'" style="width:26rpx;margin-right: 10rpx;"
                                mode="widthFix"></image>
                            {{selectedObj.name ? selectedObj.name : '选择商品' }}
                        </view>
                        <view class="picker-text" @tap="selectProduct">
                            {{ selectedObj.selectedItems.length > 0 ? `${selectedObj.selectedItems.length}件商品` : '请选择商品' }}
                        </view>
                        <image :src="pre_url+'/static/icon/right.png'" style="width:26rpx" mode="widthFix" @tap="selectProduct"></image>
                    </view>
                    
                    <!-- 选择课程 -->
                    <view class="picker-container" v-if="bind_kecheng == 1 && !courseInfo">
                        <view class="picker-label">
                            <image :src="pre_url+'/static/icon/kecheng.png'" style="width:26rpx;margin-right: 10rpx;"
                                mode="widthFix"></image>
                            选择课程
                        </view>
                        <view class="picker-text">
                            请选择课程
                        </view>
                        <image :src="pre_url+'/static/icon/right.png'" style="width:26rpx" mode="widthFix"></image>
                    </view>
                </view>

                <view class="st_title flex-y-center" :style="{ paddingBottom: (safeBottomHeight + 80) + 'rpx' }">
                    <view style="color: #878787;font-size: 12rpx;text-align: center;margin-right: 20rpx;width: 80rpx;">
                        <view class="flex-y-center tag-box"
                            style="width: 70rpx; height: 70rpx;padding: 0rpx;line-height: 70rpx;border-radius: 50rpx;margin: 0;">
                            <image :src="pre_url+'/static/icon/draft.png'" mode="widthFix" style="width: 34rpx;margin: 0 auto;">
                            </image>
                        </view>
                        <view style="margin-top: 10rpx;">
                            首页
                        </view>
                    </view>
                    <button form-type="submit" :style="'background:'+t('color1')">发布笔记</button>
                </view>

                <view style="width:100%;height:50rpx"></view>
            </form>

            <!-- 视频预览 兼容H5-->
            <uni-popup ref="videoShow" :mask-click="false">
                <view class="viewVideo">
                    <!-- 兼容H5 -->
                    <view class="close" @click="fullscreenchange"></view>
                    <video id="myVideo" :src="cVideo" object-fit='contain' autoplay="false" :controls="true"
                        show-fullscreen-btn="false" play-btn-position="center" show-loading='true'
                        @fullscreenchange="fullscreenchange"></video>
                </view>
            </uni-popup>
			
			<!-- 商品购物 -->
			<uni-popup ref="shopShow" type="bottom">
				<view class="viewShop">
					<view style="text-align: center;font-weight: bold;margin-bottom: 20rpx;position: sticky;top: 0;">
						文中提到的商品（{{selectedObj.matchedData.length}}）
					</view>
			
					<view class="cart-container">
						<scroll-view :scroll-top="0" scroll-y="true" style="height: 50vh;">
							<view class="cart-item" v-for="(item, index) in selectedObj.matchedData" :key="index" @click="goto" :data-url="'/shopPackage/shop/product?id='+item.id">
								<view class="image-box">
									<image class="item-image" :src="item.pic" mode="heightFix"></image>
								</view>
			
								<view class="item-info">
									<text class="item-name">{{ item.name }}</text>
									<view style="display: flex;justify-content: space-between;width: 100%;">
										<text class="item-price">
											<span>￥</span>
											<span style="font-size: 34rpx;font-weight: bold;">{{ item.sell_price }}</span>
										</text>
									</view>
									
								</view>
								
							</view>
						</scroll-view>
			
						<view style="position: sticky;bottom: 0;
						height: 100rpx;display: flex;justify-content: space-between;
						align-items: center;padding: 0 20rpx;">
							<view @tap="shopAllSelectedClick" style="visibility: hidden;">
								<checkbox :checked="shopAllSelected" style="transform:scale(0.8)"></checkbox>
								全选
							</view>
							
							<view class="shopButtonActive" @click="closePop">
								点击关闭
							</view>
						</view>
					</view>
				</view>
			</uni-popup>

        </block>
        <loading v-if="loading"></loading>
        <dp-tabbar :opt="opt"></dp-tabbar>
        <popmsg ref="popmsg"></popmsg>
    </view>
</template>

<script>
    var app = getApp();

    export default {
        data() {
            return {
                opt: {},
                loading: false,
                isload: false,
                menuindex: -1,
                selectedObj: {
                    name: '',
                    selectedItems: [] ,// ids
					matchedData: [] // 商品列表
                }, // 用于存储已选择的商品
                pre_url: app.globalData.pre_url,
                datalist: [],
                content_pic: [],
                pagenum: 1,
                cateArr: [],
                cindex: -1,
                pics: [],
                need_call: false,
                safeBottomHeight: 100, // 初始值
                product: '',
                cVideo: '',
                shipincheck: 0, // 默认为0，后面通过接口获取真实值
				shopAllSelected: false,
                bind_product: 0, // 绑定商品的开关，默认为0
                bind_kecheng: 0, // 绑定课程的开关，默认为0
                courseInfo: null, // 课程信息
                kcid: '', // 课程ID
                type: '' // 类型标识
            };
        },

        onLoad: function(opt) {
            const eventChannel = this.getOpenerEventChannel();
            if (JSON.stringify(eventChannel) !== '{}') {
                const that = this;
                eventChannel.on('acceptDataFromOpenerPage', function(val) {
                    that.pics = val.data.map(item => {
                        return {
                            url: item.url,
                            type: 'img'
                        };
                    });
                });
            }
            const systemInfo = wx.getSystemInfoSync();
            this.safeBottomHeight = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0;
            this.opt = app.getopts(opt);
            
            // 获取课程ID和类型
            if(this.opt.kcid) {
                this.kcid = this.opt.kcid;
                this.type = this.opt.type || '';
                this.getCourseInfo();
            }
            
            this.getdata();
            this.getDetail();
        },
        onPullDownRefresh: function() {
            this.getdata();
        },
		computed: {
			isAnyItemSelected() {
				return this.selectedObj.matchedData.some(item => item.selected);
			}
		},
        methods: {
            // 获取课程信息
            getCourseInfo() {
                if (!this.kcid) return;
                
                const that = this;
                that.loading = true;
                app.get('ApiKecheng/detail', {id: that.kcid}, function(res) {
                    that.loading = false;
                    if (res.status == 0) {
                        app.alert(res.msg);
                        return;
                    }
                    that.courseInfo = res.product;
                });
            },
            
            async getDetail() {
                let that = this;
                // 判断商品ID是否存在并且不为空
                if (that.opt.goodid) {
                    let arr = that.opt.goodid.split(',')
                    let names = []
                    that.selectedObj.selectedItems = that.opt.goodid.split(',')
                    for (let item of arr) {
                        const name = await that.getProduct(item)
                        names.push(name)
                    }
                    that.selectedObj.name = names.join(',')
                } else {
                    console.log("商品ID为空，无法加载商品详情");
                }
            },
            getProduct(id) {
                return new Promise((resolve, reject) => {
                    app.get('ApiShop/product', {
                        id: id
                    }, function(res) {
                        // 如果返回的商品信息为空，则不进行赋值操作
                        if (res && res.product) {
                            resolve(res.product.name)
                        } else {
                            resolve('')
                        }
                    });
                });
            },
			
			closePop(){
				this.$refs.shopShow.close()
				
			},

            selectProduct() {
                const that = this;
                uni.navigateTo({
                    url: '/daihuobiji/detail/selectgoods',
                    events: {
                        selectedGoods: function(data) {
                            // 接收商品选择页面传回的数据
                            that.selectedObj.name = data.matchedData.map(item => item.name).join(',');
                            that.selectedObj.selectedItems = data.selectedItems;
							that.selectedObj.matchedData = data.matchedData
                        }
                    },
                    success: function(res) {
                        // 可在此向商品选择页面传递数据
                    }
                });
            },

            getdata: function() {
                var that = this;
                that.loading = true;
                app.get('Apidaihuobiji/fatie', {}, function(res) {
                    that.loading = false;
                    if (res.status == 0) {
                        app.alert(res.msg);
                        return;
                    }
                    that.clist = res.clist;
                    // 保存 shipincheck 的值
                    that.shipincheck = res.sysset.shipincheck;
                    // 保存绑定商品和绑定课程的设置
                    that.bind_product = res.sysset.bind_product;
                    that.bind_kecheng = res.sysset.bind_kecheng;

                    var clist = res.clist;
                    if (clist.length > 0) {
                        var cateArr = [];
                        for (var i in clist) {
                            cateArr.push(clist[i].name);
                        }
                        that.cindex = 0; // 默认选中第一个分类
                    } else {
                        cateArr = false;
                    }
                    that.cateArr = cateArr;
                    if (res.need_call) {
                        that.need_call = true;
                    }
                    that.loaded();
                });
            },

            cateChange: function(e) {
                this.cindex = e;
            },
            formsubmit: async function(e) {
                var that = this;
                var clist = that.clist;
                if (clist.length > 0) {
                    if (that.cindex == -1) {
                        app.error('请选择分类');
                        return false;
                    }
                    var cid = clist[that.cindex].id;
                } else {
                    var cid = 0;
                }
                var formdata = e.detail.value;
                var content = formdata.content;
                var pics = [];
                var video = this.pics.filter(item => item.type === 'video');
                var mobile = formdata.mobile;
                var title = formdata.title;
                var kcid = formdata.kcid;
                var type = formdata.type;
                
                if (content == '' && pics == '') {
                    app.error('请输入内容');
                    return false;
                }
                for (var i = 0; i < this.pics.length; i++) {
                    if (this.pics[i].type == 'img') {
                        app.showLoading('上传中');
                        const res = await uni.uploadFile({
                            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app
                                .globalData.aid + '/platform/' + app.globalData.platform +
                                '/session_id/' +
                                app.globalData.session_id,
                            filePath: this.pics[i].url,
                            name: 'file'
                        });
                        app.showLoading(false);
                        let data = null;
                        if (Array.isArray(res)) {
                            data = res[1];
                        } else {
                            data = res;
                        }
                        data = JSON.parse(data.data);

                        if (data.status == 1) {
                            pics.push(data.url);
                        } else {
                            app.alert(data.msg);
                        }
                    }
                }

                pics = pics.join(',');
                video = video.map(item => item.url).join(',');
                const goodid = that.selectedObj.selectedItems.join(',');
                app.post('Apidaihuobiji/fatie', {
                    cid: cid,
                    pics: pics,
                    content: content,
                    video: video,
                    mobile: mobile,
                    title: title,
                    goodid: goodid,
                    kcid: kcid,
                    type: type
                }, function(res) {
                    app.showLoading(false);
                    if (res.status == 1) {
                        app.success(res.msg);
                        setTimeout(function() {
                            uni.navigateTo({
                                url: '/daihuobiji/detail/userbiji',
                            });
                        }, 1000);
                    } else {
                        app.error(res.msg);
                    }
                });
            },

            // 上传图片
            uploadimg: function() {
                this.goback();
            },

            // 上传视频
            uploadvideo: function() {
                var that = this;
                uni.chooseVideo({
                    sourceType: ['album', 'camera'],
                    maxDuration: 60,
                    success: function(res) {
                        var tempFilePath = res.tempFilePath;
                        app.showLoading('上传中');
                        uni.uploadFile({
                            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app
                                .globalData.aid + '/platform/' + app.globalData.platform +
                                '/session_id/' + app.globalData.session_id,
                            filePath: tempFilePath,
                            name: 'file',
                            success: function(res) {
                                app.showLoading(false);
                                var data = JSON.parse(res.data);

                                if (data.status == 1) {
                                    that.pics.push({
                                        url: data.url,
                                        type: 'video'
                                    });
                                } else {
                                    app.alert(data.msg);
                                }
                            },
                            fail: function(res) {
                                app.showLoading(false);
                                app.alert(res.errMsg);
                            }
                        });
                    },
                    fail: function(res) {
                        console.log(res);
                    }
                });
            },

            removeimg: function(e) {
                var that = this;
                var index = e.currentTarget.dataset.index;
                var field = e.currentTarget.dataset.field;
                var pics = that[field];
                pics.splice(index, 1);
            },

            openSelect: function() {
                const that = this;
                // 根据 shipincheck 值决定是否显示视频选项
                if (this.shipincheck == 1) {
                    uni.showActionSheet({
                        itemList: ['图片', '视频'],
                        success: function(res) {
                            if (res.tapIndex == 0) {
                                // 上传图片
                                uni.navigateTo({
                                    url: '/daihuobiji/detail/newFatie',
                                });
                            } else {
                                // 上传视频
                                that.uploadvideo();
                            }
                        },
                        fail: function(res) {
                            console.log(res.errMsg);
                        }
                    });
                } else {
                    // 只上传图片
                    uni.navigateTo({
                        url: '/daihuobiji/detail/newFatie',
                    });
                }
            },

            // 关闭视频弹窗
            close() {
                this.$refs.videoShow.close();
            },
            // 点击视频封面预览视频
            previewVideo(url) {
                this.cVideo = url;
                this.$refs.videoShow.open('top');
                this.videoContext = uni.createVideoContext('myVideo', this);
                this.videoContext.play();
            },
            // 视频满屏点击关闭
            fullscreenchange() {
                this.cVideo = '';
                this.videoContext.pause();
                this.close();
            },
			// 打开购物车
			openShop() {
				this.$refs.shopShow.open('top')
			},
			// 全选
			shopAllSelectedClick() {
				this.shopAllSelected = !this.shopAllSelected
				this.selectedObj.matchedData.forEach(item => {
					item.selected = this.shopAllSelected
				})
			},
			toggleSelection(item) {
				this.$set(item, 'selected', !item.selected); // 使用 $set 确保响应性
				this.shopAllSelected = this.selectedObj.matchedData.every(item => item.selected)
			},
			shop() {
				if(!this.isAnyItemSelected){
					app.success('请选择商品');
					return;
				}
				
				this.selectedObj.matchedData.forEach(async item =>{
					await this.addCart(item.id)
				})
				app.success('添加成功');
				this.$refs.shopShow.close()
			},
			async addCartOne(item){
				this.addCart(item.id)
				app.success('添加成功');
			},
			addCart(proid){
				return new Promise((resolve, reject) => {
					app.post('ApiShop/addcart', {
						proid,
						num: 1
					}, function(res) {
						resolve('添加成功')
					});
				});
			}
        }
    };
</script>

<style>
    page {
        background: #ffffff
    }

    .st_box {
        padding: 20rpx 0;
        padding-top: 0;
    }

    .st_title {
        display: flex;
        justify-content: space-between;
        padding: 24rpx;
    }

    .st_title1 {
        display: flex;
        justify-content: space-between;
        padding: 24rpx;
        border-bottom: 1px solid #D0D0D0
    }

    .st_title image {
        width: 18rpx;
        height: 32rpx
    }

    .st_title text {
        color: #242424;
        font-size: 36rpx
    }

    .picker-container {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eeeeee;
        padding: 40rpx 0;
    }

    .picker-label {
        font-size: 26rpx;
        color: #000000;
        flex: 1;
        display: flex;
        align-items: center;
    }

    .picker {
        flex: 2;
        text-align: right;
    }

    .picker-text {
        font-size: 26rpx;
        color: #333;
    }

    .st_title button {
        background: rgb(255, 36, 66);
        border-radius: 70rpx;
        line-height: 40rpx;
        border: none;
        padding: 0 30rpx;
        color: #fff;
        font-size: 28rpx;
        text-align: center;
        width: 90%;
        display: flex;
        height: 80rpx;
        justify-content: center;
        align-items: center;
    }

    .st_form {
        background: #ffffff;
        margin: 10px;
    }

    .st_form input {
        width: 100%;
        height: 120rpx;
        border: none;
        border-bottom: 1px solid #EEEEEE;
    }

    .st_form input::-webkit-input-placeholder {
        color: rgb(214, 214, 214);
        font-size: 24rpx;
        font-weight: 300;
    }

    .st_form textarea {
        font-size: 28rpx;
        width: 100%;
        font-weight: 350;
        white-space: pre-wrap;
        min-height: 200rpx;
        padding: 20rpx 0;
        padding-top: 10rpx;
        border: none;
    }

    .layui-imgbox {
        margin-right: 16rpx;
        margin-bottom: 10rpx;
        font-size: 24rpx;
        position: relative;
    }

    .layui-imgbox-close {
        position: absolute;
        display: block;
        width: 32rpx;
        height: 32rpx;
        right: -16rpx;
        top: -16rpx;
        z-index: 90;
        color: #999;
        font-size: 32rpx;
        background: #fff
    }

    .layui-imgbox-close image {
        width: 100%;
        height: 100%
    }

    .layui-imgbox-img {
        display: block;
        width: 200rpx;
        height: 200rpx;
        padding: 2px;
        border: #d3d3d3 1px solid;
        background-color: #f6f6f6;
        overflow: hidden;
        border-radius: 20rpx;
    }

    .layui-imgbox-img>image {
        max-width: 100%;
    }

    .uploadbtn {
        position: relative;
        height: 200rpx;
        width: 200rpx;
        border-radius: 20rpx;
    }

    .feedback-textarea {
        font-size: 28rpx;
        width: 100%;
        font-weight: 350;
        white-space: pre-wrap;
        min-height: 200rpx;
        padding: 20rpx 0;
        padding-top: 10rpx;
        border: none;
    }

    .tag-box {
        background-color: #ededed;
        padding: 10rpx 20rpx;
        border-radius: 30rpx;
        margin-right: 15rpx;
    }

    .icon-box {
        width: 50rpx;
        height: 50rpx;
        text-align: center;
        line-height: 50rpx;
        background-color: #ededed;
        border-radius: 50rpx;
    }

    .checkbox-box ::v-deep .uni-checkbox-input {
        border-radius: 30rpx;
        margin: 0;
    }
    
    /* 课程信息卡片样式 */
    .course-info {
        margin: 20rpx;
        background-color: #f9f9f9;
        border-radius: 12rpx;
        padding: 20rpx;
        box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
    }
    
    .course-header {
        display: flex;
        align-items: center;
    }
    
    .course-image {
        width: 160rpx;
        height: 120rpx;
        border-radius: 8rpx;
        overflow: hidden;
        margin-right: 20rpx;
    }
    
    .course-image image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .course-details {
        flex: 1;
    }
    
    .course-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
        line-height: 1.4;
    }
    
    .course-desc {
        font-size: 24rpx;
        color: #999;
    }
</style>
<style>
	.viewShop {
		height: 60vh;
		width: 100%;
		border-radius: 20rpx 20rpx 0 0;
		background-color: #fff;
		padding-top: 20rpx;
	}
	.cart-container {
		display: flex;
		flex-direction: column;
	}

	.cart-item {
		display: flex;
		align-items: center;
		padding: 10px;
	}

	.item-selector {
		margin-right: 10px;
	}

	.image-box {
		background-color: #f4f4f4;
		border-radius: 10rpx;
		width: 130rpx;
		height: 130rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.item-image {
		height: 70%;
	}

	.item-info {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 130rpx;
		width: 67%;
	}

	.item-name {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}

	.item-price {
		font-size: 14px;
		color: #e65602;
	}

	.item-quantity {
		display: flex;
		align-items: center;
		margin-left: auto;
	}

	.item-quantity button {
		width: 30px;
		height: 30px;
		border: 1px solid #ddd;
		background-color: #fff;
		cursor: pointer;
	}

	.item-quantity text {
		margin: 0 5px;
	}

	.shopButton {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #a0a0a0;
		background-color: #ddd;
		font-size: 20rpx;
	}

	.shopButtonActive {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #fff;
		background-color: #eb8200;
		font-size: 20rpx;
	}
</style>