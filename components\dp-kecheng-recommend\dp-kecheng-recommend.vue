<template>
<view class="dp-kecheng-recommend">
	<!-- 标题栏 -->
	<view class="header" :style="dynamicHeaderStyle">
		<view class="title" :style="dynamicTitleTextStyle">
			<view class="title-content-area">
				<view style="display: flex; align-items: center;">
					<view class="configurable-title-left-border"
						  v-if="params.showTitleLeftBorder === '1'"
						  :style="dynamicConfigurableTitleLeftBorderStyle">
					</view>
					<text>{{ headerTitleText }}</text>
				</view>
				<view class="configurable-title-bottom-border"
					  v-if="params.showTitleBottomBorder === '1'"
					  :style="dynamicConfigurableTitleBottomBorderStyle">
				</view>
			</view>
		</view>
		<view class="refresh" @click="refreshData" :style="dynamicRefreshTextStyle">
			<view class="refresh-container">
				<text>{{ headerRefreshText }}</text>
				<text class="iconfont icon-refresh">⟳</text>
			</view>
		</view>
	</view>

	<!-- Large Item: First item from data -->
	<view v-if="largeItemData" class="large-item item-card" @click="gotoProduct(largeItemData)">
		<view class="product-pic-large"  style="padding-bottom: 50%;">
			<image class="image-large" :src="largeItemData.pic" mode="aspectFill"/>
			<view class="category-tag" style="background: #0000008e;display: flex;justify-content: space-between;padding:16rpx 20rpx;align-items: center;">
				<text style="font-size: 28rpx;font-weight: normal;width: 70%;">{{largeItemData.name}}</text>

				<view style="display: flex;align-items: center;background: #216BF9; padding: 5px 10px;border-radius: 20px;height: 60rpx;" @click.stop="goToTrialStudy(largeItemData)">
					<view style="width: 18px;height: 18px;background: #fff;border-radius: 50px;margin-right: 5px;">
						<image style="width: 100%;height: 100%;padding: 3px;" src="/static/img/bf.png"></image>
					</view>
					<text style="font-size: 28rpx;font-weight: normal;">试学</text>
				</view>
			</view>
			<!-- <view class="kctype-overlay">
				<view v-if="largeItemData.kctype === 1">图文</view>
				<view v-else-if="largeItemData.kctype === 2">音频</view>
				<view v-else-if="largeItemData.kctype === 3">视频</view>
				<view v-else>综合</view>
			</view> -->
		</view>
	</view>

	<!-- Small Items: Next two items from data -->
	<view class="small-items-container">
		<view v-for="(item, index) in smallItemsData" :key="item[idfield] || index" class="small-item item-card" @click="handleSmallItemClick(index)">
			<view class="product-pic-small">
				<image class="image-small" :src="item.pic" mode="aspectFill"/>
				<!-- <view class="kctype-overlay-small">
					<view v-if="item.kctype === 1">图文</view>
					<view v-else-if="item.kctype === 2">音频</view>
					<view v-else-if="item.kctype === 3">视频</view>
					<view v-else>综合</view>
				</view> -->
			</view>
			<view class="product-info-small">
				<view class="name-small" v-if="params.showname == '1'">{{item.name}}</view>
				
				<view class="details-small" style="flex-direction: initial;align-items: center;justify-content: space-between;">
					<view class="price-small" v-if="params.showprice != '0'">
						<text class="t1" :style="{color:t('color1')}"><text style="font-size:20rpx" v-if="item.price>0">￥</text>{{item.price==0?'免费':item.price}}</text>
						<text class="t2" v-if="params.showprice == '1' && item.market_price*1 > item.price*1"><text style="font-size:20rpx">￥</text>{{item.market_price}}</text>
					</view>
					<view class="sales-small" v-if="params.showsales == '1'">
						共{{item.count}}节<block v-if="sysset && sysset.show_join_num == 1"><text style="margin: 0 4rpx;">|</text>{{item.join_num}}人已学习</block>
					</view>
					
					    
					<view style="display: flex;align-items: center;border: 1px solid #e1e1e1; padding: 3px 6px;border-radius: 20px;" @click.stop="goToTrialStudy(item, index)">
						<image style="width: 20px;height: 20px;margin-right: 5px;" src="/static/img/bf2.png"></image>
						<text>试学</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</view>
</template>

<script>
var app = getApp();

export default {
	props: {
		params: {
			type: Object,
			default: () => ({})
		},
		data: {
			type: Array,
			default: () => []
		},
		idfield: {
			type: String,
			default: 'proid' // 根据您的数据结构，默认为 proid
		},
		menuindex: {
			default: -1
		},
		sysset: {
			type: Object,
			default: () => ({})
		},
		saleimg: {
			type: String,
			default: ''
		},
		// showname, showprice, showsales 已经通过 params 传递
	},
	computed: {
		headerTitleText() {
			return this.params.titleText || '热门课程';
		},
		headerRefreshText() {
			return this.params.refreshText || '换一换';
		},
		dynamicHeaderStyle() {
			const styles = {};
			const params = this.params;
			if (params.titleBgType === 'solid' && params.titleBgSolid) {
				styles.background = params.titleBgSolid;
				styles.padding = '16rpx 20rpx'; 
				styles.borderRadius = '12rpx 12rpx 0 0';
			} else if (params.titleBgType === 'gradient' && params.titleBgGradientStart && params.titleBgGradientEnd) {
				const direction = params.titleBgGradientDirection || 'to right';
				styles.background = `linear-gradient(${direction}, ${params.titleBgGradientStart}, ${params.titleBgGradientEnd})`;
				styles.padding = '16rpx 20rpx';
				styles.borderRadius = '12rpx 12rpx 0 0';
			} else if (params.titleBgType === 'none') {
				styles.background = 'transparent';
				styles.padding = '0';
				styles.borderRadius = '0';
			}
			// If none of the above, styles remains empty, and CSS defaults for .header apply.
			return styles;
		},
		dynamicTitleTextStyle() {
			const styles = {};
			if (this.params.titleTextColor) {
				styles.color = this.params.titleTextColor;
			}
			return styles;
		},
		dynamicRefreshTextStyle() {
			const styles = {};
			if (this.params.refreshTextColor) {
				styles.color = this.params.refreshTextColor;
			}
			// If not set, CSS default for .refresh applies
			return styles;
		},
		dynamicConfigurableTitleLeftBorderStyle() {
			return {
				backgroundColor: this.params.titleLeftBorderColor || '#FF8C00',
			};
		},
		dynamicConfigurableTitleBottomBorderStyle() {
			return {
				backgroundColor: this.params.titleBottomBorderColor || '#FF8C00',
			};
		},
		largeItemData() {
			return this.data && this.data.length > 0 ? this.data[0] : null;
		},
		smallItemsData() {
			return this.data && this.data.length > 1 ? this.data.slice(1, 3) : []; // 取第2和第3个元素
		}
	},
	methods: {
		handleSmallItemClick(index) {
			const item = this.smallItemsData[index];
			console.log('2025-01-14 22:55:53,565-INFO-[dp-kecheng-recommend][handleSmallItemClick_001] - Index:', index, 'Resolved Item:', JSON.stringify(item));
			if (item) {
				this.gotoProduct(item);
			} else {
				console.error('2025-01-14 22:55:53,566-ERROR-[dp-kecheng-recommend][handleSmallItemClick_002] - Item not found at index:', index, 'Current smallItemsData:', JSON.stringify(this.smallItemsData));
			}
		},
		gotoProduct(item) {
			if (item && item[this.idfield]) {
				const itemId = item[this.idfield];
				const targetUrl = '/activity/kecheng/product?id=' + itemId;
				console.log('2025-01-14 22:55:53,567-INFO-[dp-kecheng-recommend][gotoProduct_001] - Navigating to product detail. Item ID:', itemId, 'URL:', targetUrl);
				uni.navigateTo({
					url: targetUrl,
					success: function(res) {
						console.log('2025-01-14 22:55:53,568-INFO-[dp-kecheng-recommend][gotoProduct_002] - Navigation success. URL:', targetUrl, res);
					},
					fail: function(err) {
						console.error('2025-01-14 22:55:53,569-ERROR-[dp-kecheng-recommend][gotoProduct_003] - Navigation failed. URL:', targetUrl, 'Error:', err);
						// uni.showToast({ title: '页面跳转失败: ' + (err.errMsg || '未知错误'), icon: 'none', duration: 3000 });
					}
				});
			} else {
				console.warn('2025-01-14 22:55:53,570-WARN-[dp-kecheng-recommend][gotoProduct_004] - invalid item or idfield. Item keys:', item ? Object.keys(item) : 'null item', 'idfield:', this.idfield, 'ID Value:', item ? item[this.idfield] : 'N/A');
			}
		},
		/**
		 * 处理试学功能：获取课程的试学章节并跳转
		 * @param {Object} item - 课程数据对象
		 * @param {Number} index - 索引（用于小图按钮）
		 */
		goToTrialStudy(item, index) {
			// 添加明显的调试信息，确保方法被调用
			console.log('2025-01-14 22:55:53,571-INFO-[dp-kecheng-recommend][goToTrialStudy_001] - ===== 试学按钮被点击了！=====');
			console.log('2025-01-14 22:55:53,572-INFO-[dp-kecheng-recommend][goToTrialStudy_002] - 传入参数 item:', JSON.stringify(item));
			console.log('2025-01-14 22:55:53,573-INFO-[dp-kecheng-recommend][goToTrialStudy_003] - 传入参数 index:', index);
			console.log('2025-01-14 22:55:53,574-INFO-[dp-kecheng-recommend][goToTrialStudy_004] - 当前 idfield:', this.idfield);
			console.log('2025-01-14 22:55:53,575-INFO-[dp-kecheng-recommend][goToTrialStudy_005] - 当前 smallItemsData:', JSON.stringify(this.smallItemsData));
			
			// 显示用户友好的提示
			uni.showToast({
				title: '试学按钮被点击',
				icon: 'none',
				duration: 2000
			});
			
			// 如果 item 无效，尝试从 smallItemsData 中获取
			if (!item && typeof index === 'number' && index >= 0) {
				item = this.smallItemsData[index];
				console.log('2025-01-14 22:55:53,576-INFO-[dp-kecheng-recommend][goToTrialStudy_006] - 从 smallItemsData 重新获取 item:', JSON.stringify(item));
			}
			
			if (!item || !item[this.idfield]) {
				console.error('2025-01-14 22:55:53,577-ERROR-[dp-kecheng-recommend][goToTrialStudy_007] - Invalid item for trial study. Item:', JSON.stringify(item), 'idfield:', this.idfield);
				console.error('2025-01-14 22:55:53,578-ERROR-[dp-kecheng-recommend][goToTrialStudy_008] - Available data:', JSON.stringify({
					largeItemData: this.largeItemData,
					smallItemsData: this.smallItemsData,
					allData: this.data
				}));
				uni.showToast({ 
					title: '课程信息异常', 
					icon: 'none', 
					duration: 2000 
				});
				return;
			}

			const courseId = item[this.idfield];
			console.log('2025-01-14 22:55:53,579-INFO-[dp-kecheng-recommend][goToTrialStudy_009] - Starting trial study for course ID:', courseId);
			
			// 显示加载提示
			uni.showLoading({
				title: '获取试学章节中...'
			});

			// 获取课程章节列表，查找试学章节
			app.post('ApiKecheng/getmululist', {
				pagenum: 1,
				id: courseId
			}, (res) => {
				uni.hideLoading();
				console.log('2025-01-14 22:55:53,580-INFO-[dp-kecheng-recommend][goToTrialStudy_010] - Course chapters response:', res);
				
				if (res.status === 0) {
					console.error('2025-01-14 22:55:53,581-ERROR-[dp-kecheng-recommend][goToTrialStudy_011] - Failed to get course chapters:', res.msg);
					uni.showToast({ 
						title: res.msg || '获取章节信息失败', 
						icon: 'none', 
						duration: 2000 
					});
					return;
				}

				const chapters = res.data || [];
				console.log('2025-01-14 22:55:53,582-INFO-[dp-kecheng-recommend][goToTrialStudy_012] - Found chapters:', chapters.length);
				
				// 查找第一个可以试学的章节 (ismianfei === 1)
				const trialChapter = chapters.find(chapter => chapter.ismianfei === 1);
				
				if (trialChapter) {
					console.log('2025-01-14 22:55:53,583-INFO-[dp-kecheng-recommend][goToTrialStudy_013] - Found trial chapter:', trialChapter.name, 'ID:', trialChapter.id);
					const trialUrl = `/activity/kecheng/mldetail?id=${trialChapter.id}&kcid=${courseId}`;
					
					uni.navigateTo({
						url: trialUrl,
						success: function(res) {
							console.log('2025-01-14 22:55:53,584-INFO-[dp-kecheng-recommend][goToTrialStudy_014] - Trial navigation success. URL:', trialUrl);
						},
						fail: function(err) {
							console.error('2025-01-14 22:55:53,585-ERROR-[dp-kecheng-recommend][goToTrialStudy_015] - Trial navigation failed. URL:', trialUrl, 'Error:', err);
							uni.showToast({ 
								title: '跳转试学失败', 
								icon: 'none', 
								duration: 2000 
							});
						}
					});
				} else {
					console.warn('2025-01-14 22:55:53,586-WARN-[dp-kecheng-recommend][goToTrialStudy_016] - No trial chapter found for course:', courseId);
					// 如果没有试学章节，提示用户或跳转到课程详情页
					uni.showModal({
						title: '提示',
						content: '该课程暂无试学章节，是否查看课程详情？',
						success: (res) => {
							if (res.confirm) {
								console.log('2025-01-14 22:55:53,587-INFO-[dp-kecheng-recommend][goToTrialStudy_017] - User chose to view course details');
								this.gotoProduct(item);
							}
						}
					});
				}
			}, (error) => {
				uni.hideLoading();
				console.error('2025-01-14 22:55:53,588-ERROR-[dp-kecheng-recommend][goToTrialStudy_018] - Network error getting chapters:', error);
				uni.showToast({ 
					title: '网络异常，请稍后重试', 
					icon: 'none', 
					duration: 2000 
				});
			});
		},
		refreshData() {
			// 通知父组件刷新数据
			console.log('2025-01-14 22:55:53,582-INFO-[dp-kecheng-recommend][refreshData_001] - 触发换一换事件');
			
			// 在不同环境下使用不同的刷新策略
			// #ifdef MP-WEIXIN
			// 小程序环境，使用更简单的事件参数格式
			console.log('2025-01-14 22:55:53,583-INFO-[dp-kecheng-recommend][refreshData_002] - 小程序环境：触发刷新');
			this.$emit('getdata', {refreshType: 'recommend'});
			// #endif
			
			// #ifndef MP-WEIXIN
			// 其他环境
			this.$emit('getdata');
			// #endif
		},
		// t() 方法通常是全局混入的，用于获取主题颜色等
		// 如果您的项目中有全局的 this.t 方法，这里不需要额外定义
		// 若没有，您可能需要从 appropriate store/mixin 引入
		t(key) {
			// 这是一个示例实现, 请根据您项目的实际情况调整
			if (key === 'color1') return this.params.color1 || '#FF5000'; // 默认颜色
			return ''
		}
	}
}
</script>

<style scoped>
.dp-kecheng-recommend {
	display: flex;
	flex-direction: column;
	width: 100%;
/* 	background-color: #F7F7F8; */
	border-radius: 24rpx;
	padding: 0 0 20rpx 0;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
	overflow: hidden;
	background-image: linear-gradient(to bottom, #D7EEFE, #fff);
} 

/* 标题栏样式 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	/* background: linear-gradient(to bottom, #FFE8B8, #FFF9E8); */
	padding: 24rpx 24rpx 16rpx;
	position: relative;
}

.title {
	font-size: 32rpx;
	font-weight: bold;
	color: #563B00;
	display: flex;
	align-items: center;
}

.title-content-area {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.configurable-title-left-border {
	width: 4px;
	height: 1em;
	margin-right: 8px;
	display: inline-block;
}

.configurable-title-bottom-border {
	height: 3px;
	width: 25px;
	margin-top: 4px;
}

.refresh {
	display: flex;
	align-items: center;
	font-size: 26rpx;
}

.refresh-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	background-color: transparent;
	border: 1px solid rgba(74, 122, 255, 0.3);
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	transition: all 0.2s ease;
}

.refresh-container:active {
	transform: scale(0.95);
	box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.refresh-container text {
	color: #4A7AFF;
	font-size: 24rpx;
	font-weight: 500;
}

.refresh-container .icon-refresh {
	margin-left: 8rpx;
	font-size: 24rpx;
	font-weight: bold;
}

.item-card {
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	margin: 0 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

/* Large Item Styles */
.large-item {
	margin-top: 20rpx;
	margin-bottom: 10rpx;
}

.large-item .product-pic-large {
	width: 100%;
	height: 0;
	padding-bottom: 60%;
	position: relative;
	overflow: hidden;
	border-radius: 16rpx 16rpx 0 0;
}

.large-item .image-large {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.category-tag {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
	padding: 40rpx 20rpx 20rpx;
}

.category-tag text {
	color: #fff;
	font-size: 34rpx;
	font-weight: bold;
	text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.kctype-overlay {
	position: absolute;
	right: 16rpx;
	bottom: 16rpx;
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	width: 70rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 40rpx;
}

/* Small Items Styles */
.small-items-container {
	display: flex;
	flex-direction: column;
	padding: 0;
}

.small-item {
	display: flex;
	align-items: center;
	padding: 16rpx 20rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.small-item:last-child {
	border-bottom: none;
}

.small-item .product-pic-small {
	width: 270rpx;
	height: 160rpx;
	position: relative;
	border-radius: 20rpx;
	overflow: hidden;
	margin-right: 20rpx;
	flex-shrink: 0;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);
}

.small-item .image-small {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.kctype-overlay-small {
	position: absolute;
	right: 4rpx;
	bottom: 4rpx;
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
	font-size: 20rpx;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 1.5;
}

.small-item .product-info-small {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-height: 160rpx;
	padding: 6rpx 0;
	overflow: hidden;
}

.small-item .name-small {
	color: #333;
	font-size: 30rpx;
	font-weight: 500;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 30rpx;
}

.small-item .details-small {
	margin-top: auto;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
}

.small-item .price-small {
	display: flex;
	align-items: baseline;
	margin-bottom: 4rpx;
	flex-wrap: wrap;
}

.small-item .price-small .t1 {
	font-size: 32rpx;
	font-weight: bold;
	margin-right: 6rpx;
	color: #FF5000;
}

.small-item .price-small .t2 {
	font-size: 22rpx;
	color: #999;
	text-decoration: line-through;
}

.small-item .sales-small {
	font-size: 24rpx;
	color: #999;
	line-height: 1.3;
}
</style> 