<template>
	<view class="box">
		<!-- 导航栏 -->
		<view class="flex">
			<view>
				<image src="@/static/img/arrow-left.png" style="height: 30rpx;" mode="heightFix"  @tap="goback"></image>
			</view>
			<view class="nextButton" @click="goToFatie">
				下一步
			</view>
		</view>
		<jack-fileupload ref="jackFileupload" />
	</view>
</template>

<script>
	import jackFileupload from './components/jack-filepicker/components/jack-fileupload/jack-fileupload.vue'
	var app = getApp();

	export default {
		components: {
			jackFileupload 
		},
		data() {
			return {

			};
		},

		onLoad: function(opt) {

		},
		onPullDownRefresh: function() {

		},
		methods: {
			goToFatie() {
				if (this.$refs.jackFileupload.images.length == 0) {
					uni.showToast({
						title: '请选择图片',
						duration: 2000,
						icon: 'none'
					});
					return;
				}
				const that = this
				uni.navigateTo({
					url: '/daihuobiji/detail/fatie',
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						acceptDataFromOpenedPage: function(data) {
							console.log(data)
						}
					},
					success: function(res) {
						that.$nextTick(() =>{
							let data = that.$refs.jackFileupload.images
							// 通过eventChannel向被打开页面传送数据
							res.eventChannel.emit('acceptDataFromOpenerPage', {
								data
							})
						})
						
					}
				})
				console.log('this.$refs.jackFileupload.images', this.$refs.jackFileupload.images)
			}
			// @tap="goto" :data-url="'detail/fatie'"
		}
	};
</script>
<style scoped>
	.box {
		background: #000000;
		height: 100vh;
		width: 100vw;
	}

	.nextButton {
		width: 140rpx;
		text-align: center;
		border-radius: 30rpx;
		color: #fff;
		background-color: #ff2600;
		padding: 10rpx 0;
		font-size: 24rpx;
	}

	.flex {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 30rpx;
	}
</style>