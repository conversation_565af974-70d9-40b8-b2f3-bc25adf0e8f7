<template>
	<view class="nav-bar" :style="{height: navBarHeight + 'rpx', paddingTop:statusBarHeight+ 'rpx',paddingbottom:statusBarHeight+ 'rpx' }">
		<view class="left" >
			<image class="back-icon" src="../../static/img/daihuobiji_back.png" @click="onBack"></image>
			<image
			  class="user-avatar"
			  :src="userInfo.headimg"
			  @tap="goto(userInfo.mid)"
			></image>

			<text class="nav-text">{{ userInfo.nickname }}</text>
		</view>

		<view class="right">
			<view class="button-group">
				<view class="nav-button follow-status" hover-class="button-hover">
					<text @tap="guangzhu()">{{ followStatus ? '已关注' : '关注' }}</text>
				</view>
				<!-- <view class="nav-button shop-button" 
					  v-if="currentShopId > 0" 
					  hover-class="button-hover">
					<text @tap="gotoShop()">TA的店铺</text>
				</view> -->
				<!-- <view class="nav-button team-button" 
				v-if="currentLeaderId > 0" 
				hover-class="button-hover">
				<text @tap="gotoTeam()">带货团</text>
			</view> -->
			</view>
		</view>
		
		<view style="width: 100px;" v-if="weixin"></view>
		
	</view>
</template>

<script>
	var app = getApp();
	export default {
		props: {
			userInfo: {
				type: Object,
				required: true,
				default: () => ({
					headimg: '',
					nickname: '',
					mid: '',
					shop_id: '',
					leader_id: '',
					followStatus: false
				})
			},
			noteId: {
				type: String,
				required: true
			}
		},
		data() {
			return {
				navBarHeight: 140,
				statusBarHeight:0,
				followStatus: false,
				weixin : false,
				currentLeaderId: 0,
				currentShopId: 0
			}
		},
		mounted() {
			console.log('userInfo:', this.userInfo); // 检查初始数据
			this.isguangzhu();
			
			// #ifdef MP-WEIXIN  
                this.weixin = true
				this.statusBarHeight = 80
				this.navBarHeight = 200
			// #endif
		},
		onShow() {
		    // 页面每次显示时重新加载数据，避免缓存问题
		    this.getdata();
			
		},

		methods: {
			onBack() {
				uni.navigateBack();
			},
			goto(mid) {
			    if (!mid) {
			        console.error('mid is missing');
			        return;
			    }
			    
			    const url = `/daihuobiji/detail/userbijishe?mid=${mid}`;
			    uni.navigateTo({
			        url: url,
			        fail: (err) => {
			            console.error('Navigation failed:', err);
			        }
			    });
			},

			isguangzhu() {
				var that = this;
				app.post('Apidaihuobiji/getNoteDetail', {
					usid: that.userInfo.mid,
					node_id: that.noteId,  
				}, function(res) {
					if (res.status === 1 && res.data) {
						that.followStatus = res.data.is_followed === 1;
						that.currentLeaderId = Number(res.data.leader_id) || 0;
						that.currentShopId = Number(res.data.shop_id) || 0;
						
						// 更新userInfo
						if(res.data.shop_id !== undefined) {
							that.$set(that.userInfo, 'shop_id', res.data.shop_id);
						}
						if(res.data.leader_id !== undefined) {
							that.$set(that.userInfo, 'leader_id', res.data.leader_id);
						}
					}
				});
			},

			guangzhu() {
				var that = this;
				const action = that.followStatus ? 'unfollow' : 'follow'; // 判断当前状态

				app.post('Apidaihuobiji/followUser', {
					node_id: that.noteId,
					guanzhuid: that.userInfo.mid,
					usid: that.userInfo.mid,
					action: action // 添加操作类型
				}, function(res) {
					if (res.status === 1) {
						that.followStatus = !that.followStatus; // 切换关注状态
					} else {
						console.error('Error:', res.message || '操作失败');
					}
				});
			},

			gotoShop() {
				if(!this.currentShopId) {
					console.log('没有店铺ID');
					return;
				}
				
				uni.navigateTo({
					url: `/pagesExt/business/index?id=${this.currentShopId}`,
					fail: (err) => {
						console.error('跳转店铺失败:', err);
					}
				});
			},

			gotoTeam() {
				if(!this.currentLeaderId) {
					console.log('没有团长ID');
					return;
				}
				
				uni.navigateTo({
					url: `/daihuobiji/kuaituan/tuanzhangtuanlist?bid=${this.currentLeaderId}`,
					fail: (err) => {
						console.error('跳转团长页面失败:', err);
					}
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.nav-bar {
		display: flex;
		flex-direction: row;
		
		justify-content: space-between;
		align-items: center;
		background-color: #fff;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		padding: 0 16px;
		color: #333333;

		.left {
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;

			.back-icon {
				width: 44rpx;
				height: 44rpx;
				margin-right: 12rpx;
			}

			.user-avatar {
				width: 68rpx;
				height: 68rpx;
				border-radius: 100rpx;
				border: 1rpx solid #F6F6F6;
				background-color: #ccc;
				margin-right: 12rpx;
			}

			.nav-text {
				height: 37rpx;
				font-size: 28rpx;
				color: #333333;
				line-height: 33rpx;
			}
		}

		.right {
			.button-group {
				display: flex;
				flex-direction: row;
				align-items: center;
				gap: 12rpx; // 增加按钮间距
			}
			
			.nav-button {
				min-width: 120rpx; // 设置最小宽度
				height: 56rpx;
				line-height: 54rpx;
				text-align: center;
				border-radius: 31rpx;
				border: 1rpx solid #E3582F;
				font-size: 24rpx;
				color: #E3582F;
				background-color: #fff;
				padding: 0 20rpx; // 添加水平内边距
				transition: all 0.2s ease; // 添加过渡效果
				
				&.shop-button, &.team-button {
					background-color: #E3582F;
					color: #fff;
				}
				
				// 按钮点击效果
				&.button-hover {
					transform: scale(0.95);
					opacity: 0.9;
				}
			}
		}
	}

	// 添加按钮点击波纹效果
	@keyframes ripple {
		0% {
			transform: scale(1);
			opacity: 0.4;
		}
		100% {
			transform: scale(2);
			opacity: 0;
		}
	}

	.nav-button {
		position: relative;
		overflow: hidden;
		
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 100%;
			height: 100%;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			transform: translate(-50%, -50%) scale(0);
			opacity: 0;
		}
		
		&:active::after {
			animation: ripple 0.6s ease-out;
		}
	}
</style>
