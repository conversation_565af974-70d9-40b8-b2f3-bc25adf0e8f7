<template>
  <view>
    <block v-if="isload">
      <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==2">
        <parse :content="bset.verify_reject || '审核未通过：'"/>{{info.reason}}，请修改后重新提交
      </view>
      <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==0">
        <parse :content="bset.verify_notice || '您的入驻申请已提交成功，请耐心等待审核，平台将于7个工作日内联系您核实信息，请留意来电'"/>
      </view>
      <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==1">
        <parse :content="bset.verify_success || '恭喜您审核通过！'"/>
      </view>
      <view style="color:red;padding:10rpx 30rpx;margin-top:20rpx">
        <parse :content="bset.verify_normal || '温馨提示：审核通过后需缴纳保证金可完成入驻'"/>
      </view>
  
      <form @submit="subform">
        <view class="apply_box">
          
          <view class="apply_item">
            <view>联系人姓名<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="linkman" :value="info.linkman" placeholder="请填写姓名"></input></view>
          </view>
          <view class="apply_item">
            <view>联系人电话<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="linktel" :value="info.linktel" placeholder="请填写手机号码"></input></view>
          </view>
          <!-- 邀请码输入框：根据后台配置决定是否显示 -->
          <view class="apply_item" v-if="registerConfig.register_need_invite_code == 1">
            <view>
              {{registerConfig.register_need_invite_code == 1 ? '邀请码' : '拓展人邀请码'}}
              <text style="color:red" v-if="registerConfig.register_need_invite_code == 1"> *</text>
            </view>
            <view class="flex-y-center">
              <input type="text" name="tuozhanid" :value="info.tuozhanid" :placeholder="registerConfig.register_need_invite_code == 1 ? '请填写邀请码' : '请填写拓展员邀请码'"></input>
            </view>
          </view>
        </view>
        
        <view class="apply_box">
          <view class="apply_item">
            <view>商家名称<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="name" :value="info.name" placeholder="请输入商家名称"></input></view>
          </view>
          <view class="apply_item">
            <view>商家描述<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="desc" :value="info.desc" placeholder="请输入商家描述"></input></view>
          </view>
          <view class="apply_item" v-if="registerConfig.simple_register_mode != 1">
            <view>主营类目<text style="color:red"> *</text></view>
            <view>
              <view class="search-picker" @tap="showCateSearch">
                <view class="picker">{{cateArr[cindex] || '请选择主营类目'}}</view>
              </view>
            </view>
          </view>
          
          <!-- 类目搜索弹窗 -->
          <view v-if="showCateSearchPopup" class="cate-search-mask" @tap="hideCateSearch">
            <view class="cate-search-popup" @tap.stop>
              <view class="cate-search-header">
                <view class="cate-search-input">
                  <input type="text" v-model="cateSearchKeyword" placeholder="搜索类目" @input="searchCate"/>
                </view>
                <view class="cate-search-close" @tap="hideCateSearch">关闭</view>
              </view>
              <view class="cate-search-list">
                <view 
                  v-for="(item, index) in filteredCateList" 
                  :key="index" 
                  class="cate-search-item"
                  @tap="selectCate(index)"
                >
                  {{item.name}}
                </view>
                <view v-if="filteredCateList.length === 0" class="cate-no-result">
                  无匹配结果
                </view>
              </view>
            </view>
          </view>
            <!-- 费率选择 -->
        <view class="apply_item" v-if="registerConfig.register_need_select_rate == 1 || registerConfig.register_need_fill_rate == 1">
          <view>商户费率<text style="color:red"> *</text></view>
          
          <!-- 费率套餐选择 -->
          <view v-if="registerConfig.register_need_select_rate == 1">
            <picker @change="rateChange" :value="rateIndex" :range="rateArr">
              <view class="picker">{{ rateArr[rateIndex] || '请选择费率套餐' }}</view>
            </picker>
          </view>
          
          <!-- 自定义费率填写 -->
          <view v-if="registerConfig.register_need_fill_rate == 1" class="flex-y-center">
            <input 
              type="number" 
              name="custom_rate" 
              v-model="info.custom_rate" 
              :placeholder="'请填写费率 (' + registerConfig.rate_min + '%-' + registerConfig.rate_max + '%)'"
              @input="validateCustomRate"
              @blur="validateCustomRateOnBlur"
            ></input>
          </view>
          
          <!-- 费率说明提示 -->
          <!-- <view v-if="registerConfig.register_need_fill_rate == 1" class="rate-tips">
            <text class="rate-tip-text">费率范围：{{registerConfig.rate_min}}% - {{registerConfig.rate_max}}%</text>
            <text v-if="customRateError" class="rate-error">{{customRateError}}</text>
          </view> -->
        </view>
  
          <view class="apply_item">
            <view>店铺坐标<text style="color:red"> </text></view>
            <view class="flex-y-center"><input type="text" disabled placeholder="请选择坐标" name="zuobiao" :value="latitude ? latitude+','+longitude:''" @tap="locationSelect"></input></view>
          </view>
          <view class="apply_item">
            <view>店铺地址<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="address" :value="address" placeholder="请输入商家详细地址"></input></view>
          </view>
          <input type="text" hidden="true" name="latitude" :value="latitude"></input>
          <input type="text" hidden="true" name="longitude" :value="longitude"></input>
          <view class="apply_item">
            <view>客服电话<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="tel" :value="info.tel" placeholder="请填写客服电话"></input></view>
          </view>
          <view class="apply_item" style="line-height:50rpx"><textarea name="content" placeholder="请输入商家简介" :value="info.content"></textarea></view>
        </view>
        <view class="apply_box">
          <view class="apply_item" style="border-bottom:0"><text>商家主图<text style="color:red"> *</text></text></view>
          <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
            <view v-for="(item, index) in pic" :key="index" class="layui-imgbox">
              <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pic"><image src="/static/img/ico-del.png"></image></view>
              <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
            </view>
            <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pic" v-if="pic.length==0"></view>
          </view>
          <input type="text" hidden="true" name="pic" :value="pic.join(',')" maxlength="-1"></input>
        </view>
        <view class="apply_box" v-if="registerConfig.register_need_product_images == 1">
          <view class="apply_item" style="border-bottom:0"><text>主要产品图片(3-5张)<text style="color:red"> *</text></text></view>
          <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
            <view v-for="(item, index) in pics" :key="index" class="layui-imgbox">
              <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pics"><image src="/static/img/ico-del.png"></image></view>
              <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
            </view>
            <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pics" v-if="pics.length<5"></view>
          </view>
          <input type="text" hidden="true" name="pics" :value="pics.join(',')" maxlength="-1"></input>
        </view>
        <view class="apply_box" v-if="registerConfig.register_need_certificates == 1">
          <view class="apply_item" style="border-bottom:0"><text>身份证，营业执照，及相关资质<text style="color:red"> *</text></text></view>
          <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
            <view v-for="(item, index) in zhengming" :key="index" class="layui-imgbox">
              <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="zhengming"><image src="/static/img/ico-del.png"></image></view>
              <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
            </view>
            <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="zhengming"></view>
          </view>
          <input type="text" hidden="true" name="zhengming" :value="zhengming.join(',')" maxlength="-1"></input>
        </view>
        
          
          <!-- 新增提现方式部分 -->
          <view class="apply_box" v-if="registerConfig.register_need_withdraw_method == 1 && availableWithdrawMethods && availableWithdrawMethods.length > 0">
            <view class="apply_item">
              <view>提现方式<text style="color:red"> *</text></view>
              <view>
                <picker @change="withdrawMethodChange" :value="withdrawMethodIndex" :range="availableWithdrawMethods">
                  <view class="picker">{{ availableWithdrawMethods[withdrawMethodIndex] || '请选择提现方式' }}</view>
                </picker>
              </view>
            </view>
    
            <!-- 支付宝相关字段 -->
            <view v-if="selectedWithdrawMethod === '支付宝'" class="apply_item">
              <view>支付宝姓名<text style="color:red"> *</text></view>
              <view class="flex-y-center"><input type="text" name="alipayName" v-model="info.bankcarduser" placeholder="请输入支付宝姓名"></input></view>
            </view>
            <view v-if="selectedWithdrawMethod === '支付宝'" class="apply_item">
              <view>支付宝账号<text style="color:red"> *</text></view>
              <view class="flex-y-center"><input type="text" name="alipayAccount" v-model="info.aliaccount" placeholder="请输入支付宝账号"></input></view>
            </view>
    
            <!-- 银行卡相关字段 -->
            <view v-if="selectedWithdrawMethod === '银行卡'" class="apply_item">
              <view>持卡人<text style="color:red"> *</text></view>
              <view class="flex-y-center"><input type="text" name="bankcarduser" v-model="info.bankcarduser" placeholder="请输入持卡人姓名"></input></view>
            </view>
            <view v-if="selectedWithdrawMethod === '银行卡'" class="apply_item">
              <view>银行卡账号<text style="color:red"> *</text></view>
              <view class="flex-y-center"><input type="text" name="bankCardAccount" v-model="info.bankCardAccount" placeholder="请输入银行卡账号"></input></view>
            </view>
            <view v-if="selectedWithdrawMethod === '银行卡'" class="apply_item">
              <view>开户行<text style="color:red"> *</text></view>
              <view class="flex-y-center"><input type="text" name="bankName" v-model="info.bankname" placeholder="请输入开户行"></input></view>
            </view>
    
            <!-- 微信钱包相关字段 -->
            <view v-if="selectedWithdrawMethod === '微信钱包'" class="apply_item">
              <view>微信号<text style="color:red"> *</text></view>
              <view class="flex-y-center"><input type="text" name="weixinAccount" v-model="info.weixin" placeholder="请输入微信号"></input></view>
            </view>
          </view>
        <view class="apply_box">
          <view class="apply_item">
            <text>登录账号<text style="color:red"> *</text></text>
            <view class="flex-y-center"><input type="text" name="un" :value="info.un" placeholder="请填写登录账号" autocomplete="off"></input></view>
          </view>
          <view class="apply_item">
            <text>登录密码<text style="color:red"> *</text></text>
            <view class="flex-y-center"><input type="password" name="pwd" :value="info.pwd" placeholder="请填写登录密码" autocomplete="off"></input></view>
          </view>
          <view class="apply_item">
            <text>确认密码<text style="color:red"> *</text></text>
            <view class="flex-y-center"><input type="password" name="repwd" :value="info.repwd" placeholder="请再次填写密码"></input></view>
          </view>
        </view>
        <block v-if="bset.xieyi_show==1">
        <view class="flex-y-center" style="margin-left:20rpx;color:#999" v-if="!info.id || info.status==2">
          <checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox value="1" :checked="isagree"></checkbox>阅读并同意</label></checkbox-group>
          <text style="color:#666" @tap="showxieyiFun">《商户入驻协议》</text>
        </view>
        </block>
        <view style="padding:30rpx 0"><button v-if="!info.id || info.status==2" form-type="submit" class="set-btn" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">提交申请</button>
  </view>
      </form>
      <!-- 底部返回按钮 -->
         <view class="content">
           <view class="back-button" @tap="goBack">
             <text class="t1">返回</text>
           </view>
         </view>
      <view id="xieyi" :hidden="!showxieyi" style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)">
        <view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px">
          <view style="overflow:scroll;height:100%;">
            <parse :content="bset.xieyi"/>
          </view>
          <view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center" @tap="hidexieyi">已阅读并同意</view>
        </view>
      </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
  </template>
  <script>
  var app = getApp();
  
  export default {
    data() {
      return {
        opt: {},
        loading: false,
        isload: false,
        menuindex: -1,
        pre_url: app.globalData.pre_url,
        datalist: [],
        pagenum: 1,
        cateArr: [],
        cindex: 0,
        rateArr: [], // 商户费率名称数组
        rateIndex: 0, // 当前选中的费率索引
        selectedRate: null, // 当前选中的费率对象
        isagree: false,
        showxieyi: false,
        pic: [],
        pics: [],
        zhengming: [],
        info: {
          custom_rate: '',
          invite_code: '',
          tuozhanid: ''
        },
        bset: {},
        latitude: '',
        longitude: '',
        address: '',
        withdrawMethods: [], // 改为动态数组，根据后台配置
        withdrawMethodIndex: 0,
        selectedWithdrawMethod: '',
        showCateSearchPopup: false,
        cateSearchKeyword: '',
        filteredCateList: [],
        availableWithdrawMethods: [], // 新增：存储后台返回的可用提现方式
        // 入驻模式配置
        registerConfig: {
          simple_register_mode: 0,
          register_need_audit: 1,
          register_need_invite_code: 0,
          register_need_select_rate: 0,
          register_need_fill_rate: 0,
          rate_min: 0,
          rate_max: 100,
          register_need_product_images: 0,
          register_need_certificates: 1,
          register_need_withdraw_method: 1
        },
        customRateError: '' // 新增：自定义费率验证错误提示
      };
    },
  
    onLoad: function (opt) {
      this.opt = app.getopts(opt);
      this.getdata();
    },
    onPullDownRefresh: function () {
      this.getdata();
    },
    methods: {
      getdata: function () {
        var that = this;
        that.loading = true;
        app.get('ApiBusiness/apply', {}, function (res) {
          that.loading = false;
          if (res.status == 2) {
            app.alert(res.msg, function () {
              app.goto('/admin/index/index', 'redirect');
            });
            return;
          }
          uni.setNavigationBarTitle({
            title: res.title
          });
          var clist = res.clist;
          var cateArr = [];
          for (var i in clist) {
            cateArr.push(clist[i].name);
          }
          // 处理费率数据，模仿分类的处理方式
          var feilv = res.feilv || [];
          var rateArr = [];
          for (var i in feilv) {
            rateArr.push(feilv[i].name);
          }
          that.feilv = feilv;
          that.rateArr = rateArr;
          
          // 如果有现有的费率数据，设置对应的索引
          if (res.info && res.info.rate_id) {
            for (var j = 0; j < feilv.length; j++) {
              if (feilv[j].id == res.info.rate_id) {
                that.rateIndex = j;
                that.selectedRate = feilv[j];
                break;
              }
            }
          }
          
          var pics = res.info ? res.info.pics : '';
          if (pics) {
            pics = pics.split(',');
          } else {
            pics = [];
          }
          var zhengming = res.info ? res.info.zhengming : '';
          if (zhengming) {
            zhengming = zhengming.split(',');
          } else {
            zhengming = [];
          }
          that.clist = res.clist;
          that.bset = res.bset;
          that.info = res.info;
          that.address = res.info.address;
          that.latitude = res.info.latitude;
          that.longitude = res.info.longitude;
          that.cateArr = cateArr;
          that.pic = res.info.logo ? [res.info.logo] : [];
          that.pics = pics;
          that.zhengming = zhengming;
          that.availableWithdrawMethods = res.availableWithdrawMethods || [];
          
          // 获取入驻模式配置
          if(res.registerConfig) {
            that.registerConfig = Object.assign(that.registerConfig, res.registerConfig);
          }
          
          // 如果有可用提现方式，自动选中第一个
          if (that.availableWithdrawMethods.length > 0) {
            that.selectedWithdrawMethod = that.availableWithdrawMethods[0];
            that.withdrawMethodIndex = 0;
          }
          
          // 初始化自定义费率验证（如果有现有数据）
          if (that.registerConfig.register_need_fill_rate == 1 && that.info.custom_rate) {
            that.validateCustomRateValue(that.info.custom_rate);
          }
          
          that.loaded();
        });
      },
      cateChange: function (e) {
        this.cindex = e.detail.value;
      },
      // 费率选择事件处理
      rateChange: function (e) {
        this.rateIndex = e.detail.value;
        this.selectedRate = this.feilv[this.rateIndex]; // 更新选中的费率对象
      },
      withdrawMethodChange: function (e) {
        this.withdrawMethodIndex = e.detail.value;
        this.selectedWithdrawMethod = this.availableWithdrawMethods[this.withdrawMethodIndex];
      },
      locationSelect: function () {
        var that = this;
        uni.chooseLocation({
          success: function (res) {
            that.info.address = res.name;
            that.info.latitude = res.latitude;
            that.info.longitude = res.longitude;
            that.address = res.name;
            that.latitude = res.latitude;
            that.longitude = res.longitude;
          }
        });
      },
      subform: function (e) {
        var that = this;
        var info = e.detail.value;
      
        // 添加 id 到表单数据中
        if (that.info && that.info.id) {
          info.id = that.info.id;  // 将已有的 id 添加到表单数据中
        }
      
        // 验证所有必填项
        if (info.linkman == '') {
          app.error('请填写联系人姓名');
          return false;
        }
        if (info.linktel == '') {
          app.error('请填写联系人电话');
          return false;
        }
        if (info.tel == '') {
          app.error('请填写客服电话');
          return false;
        }
        if (info.name == '') {
          app.error('请填写商家名称');
          return false;
        }
        // 简单入驻模式下不需要验证主营类目
        if (that.registerConfig.simple_register_mode != 1 && that.cindex < 0) {
          app.error('请选择主营类目');
          return false;
        }
        if (info.address == '') {
          app.error('请填写店铺地址');
          return false;
        }
        if (info.pic == '') {
          app.error('请上传商家主图');
          return false;
        }
        
        // 根据配置验证邀请码
        if (that.registerConfig.register_need_invite_code == 1) {
          if (!info.tuozhanid || info.tuozhanid == '') {
            app.error('请填写邀请码');
            return false;
          }
        }
        
        // 根据配置验证产品图片
        if (that.registerConfig.register_need_product_images == 1 && info.pics == '') {
          app.error('请上传商家照片');
          return false;
        }
        
        // 根据配置验证证明材料
        if (that.registerConfig.register_need_certificates == 1 && info.zhengming == '') {
          app.error('请上传证明材料');
          return false;
        }
        if (info.un == '') {
          app.error('请填写登录账号');
          return false;
        }
        if (info.pwd == '') {
          app.error('请填写登录密码');
          return false;
        }
        if (info.pwd.length < 6) {
          app.error('密码不能小于6位');
          return false;
        }
        if (info.repwd != info.pwd) {
          app.error('两次输入的密码不一致');
          return false;
        }
      
        // 根据配置验证费率
        if (that.registerConfig.register_need_select_rate == 1 || that.registerConfig.register_need_fill_rate == 1) {
          // 验证费率套餐选择
          if (that.registerConfig.register_need_select_rate == 1) {
            if (that.rateIndex < 0 || !that.feilv[that.rateIndex] || !that.feilv[that.rateIndex].id) {
              app.error('请选择商户费率套餐');
              return false;
            }
          }
          
          // 验证自定义费率填写
          if (that.registerConfig.register_need_fill_rate == 1) {
            if (!info.custom_rate || info.custom_rate == '' || info.custom_rate == '0') {
              app.error('请填写商户费率');
              return false;
            }
            
            var rate = parseFloat(info.custom_rate);
            if (isNaN(rate)) {
              app.error('费率必须为有效数字');
              return false;
            }
            
            if (rate < that.registerConfig.rate_min || rate > that.registerConfig.rate_max) {
              app.error('费率必须在 ' + that.registerConfig.rate_min + '% - ' + that.registerConfig.rate_max + '% 范围内');
              return false;
            }
            
            // 如果有实时验证错误，阻止提交
            if (that.customRateError) {
              app.error(that.customRateError);
              return false;
            }
          }
        }
        
        // 根据配置验证提现方式（只有当需要提现方式且有可用提现方式时才验证）
        if (that.registerConfig.register_need_withdraw_method == 1 && that.availableWithdrawMethods && that.availableWithdrawMethods.length > 0) {
          if (that.selectedWithdrawMethod === '支付宝') {
            if (!info.alipayName || !info.alipayAccount) {
              app.error('请填写支付宝相关信息');
              return false;
            }
          } else if (that.selectedWithdrawMethod === '银行卡') {
            if (!info.bankcarduser || !info.bankCardAccount || !info.bankName) {
              app.error('请填写银行卡相关信息');
              return false;
            }
          } else if (that.selectedWithdrawMethod === '微信钱包') {
            if (!info.weixin) {
              app.error('请填写微信号');
              return false;
            }
          }
        }
      
        // 赋值地址、纬度和经度
        info.address = that.address;
        info.latitude = that.latitude;
        info.longitude = that.longitude;
        
        // 根据配置设置主营类目
        if (that.registerConfig.simple_register_mode != 1) {
          info.cid = that.clist[that.cindex] ? that.clist[that.cindex].id : '';
        }
      
        // 根据配置设置费率相关字段
        if (that.registerConfig.register_need_select_rate == 1) {
          info.rate_id = that.feilv[that.rateIndex] ? that.feilv[that.rateIndex].id : null;
        }
        if (that.registerConfig.register_need_fill_rate == 1) {
          info.custom_rate = info.custom_rate;
        }
      
        // 根据配置设置提现方式（只有当需要提现方式且有可用提现方式时才设置）
        if (that.registerConfig.register_need_withdraw_method == 1 && that.availableWithdrawMethods && that.availableWithdrawMethods.length > 0) {
          info.withdrawMethod = that.selectedWithdrawMethod;
          if (that.selectedWithdrawMethod === '支付宝') {
            info.alipayName = that.info.alipayName;
            info.alipayAccount = that.info.alipayAccount;
          } else if (that.selectedWithdrawMethod === '银行卡') {
            info.bankcarduser = that.info.bankcarduser;
            info.bankCardAccount = that.info.bankCardAccount;
            info.bankName = that.info.bankName;
          } else if (that.selectedWithdrawMethod === '微信钱包') {
            info.weixin = that.info.weixin;
          }
        }
      
        // 检查是否同意协议（如果需要）
        if (that.bset.xieyi_show == 1 && !that.isagree) {
          app.error('请先阅读并同意商户入驻协议');
          return false;
        }
      
        // 提交表单数据到服务器
        app.showLoading('提交中');
        app.post("ApiBusiness/apply", { info: info }, function (res) {
          app.showLoading(false);
          if (res.status == 1) {
            app.success(res.msg);
            setTimeout(function () {
              if (res.after_register_url) {
                app.goto(res.after_register_url);
              } else {
                app.goto(app.globalData.indexurl);
              }
            }, 1500);
          } else {
            app.error(res.msg);
          }
        });
      },
  
      isagreeChange: function (e) {
        var val = e.detail.value;
        this.isagree = val.length > 0;
      },
      showxieyiFun: function () {
        this.showxieyi = true;
      },
      hidexieyi: function () {
        this.showxieyi = false;
        this.isagree = true;
      },
      uploadimg: function (e) {
        var that = this;
        var field = e.currentTarget.dataset.field;
        var pics = that[field];
        if (!pics) pics = [];
        app.chooseImage(function (urls) {
          for (var i = 0; i < urls.length; i++) {
            pics.push(urls[i]);
          }
          if (field == 'pic') that.pic = pics;
          if (field == 'pics') that.pics = pics;
          if (field == 'zhengming') that.zhengming = pics;
        }, 1);
      },
    // 返回功能
      goBack: function () {
        uni.navigateBack({
          delta: 1
        });
      },
      removeimg: function (e) {
        var that = this;
        var index = e.currentTarget.dataset.index;
        var field = e.currentTarget.dataset.field;
        if (field == 'pic') {
          var pics = that.pic;
          pics.splice(index, 1);
          that.pic = pics;
        } else if (field == 'pics') {
          var pics = that.pics;
          pics.splice(index, 1);
          that.pics = pics;
        } else if (field == 'zhengming') {
          var pics = that.zhengming;
          pics.splice(index, 1);
          that.zhengming = pics;
        }
      },
      showCateSearch: function () {
        this.filteredCateList = this.clist || [];
        this.cateSearchKeyword = '';
        this.showCateSearchPopup = true;
      },
      hideCateSearch: function () {
        this.showCateSearchPopup = false;
      },
      searchCate: function () {
        var that = this;
        var keyword = that.cateSearchKeyword.toLowerCase();
        that.filteredCateList = (that.clist || []).filter(function (item) {
          return item.name.toLowerCase().indexOf(keyword) !== -1;
        });
      },
      selectCate: function (index) {
        var item = this.filteredCateList[index];
        var originalIndex = this.clist.findIndex(function(cateItem) {
          return cateItem.id === item.id;
        });
        if (originalIndex !== -1) {
          this.cindex = originalIndex;
        }
        this.hideCateSearch();
      },
             // 通用费率验证方法
       validateCustomRateValue: function (value) {
         var that = this;
         var rate = parseFloat(value);
         if (!value || value === '' || value === '0') {
           that.customRateError = '';
           return false;
         }
         if (isNaN(rate)) {
           that.customRateError = '费率必须为有效数字';
           return false;
         }
         if (rate < that.registerConfig.rate_min || rate > that.registerConfig.rate_max) {
           that.customRateError = '费率必须在 ' + that.registerConfig.rate_min + '% - ' + that.registerConfig.rate_max + '% 范围内';
           return false;
         }
         that.customRateError = '';
         return true;
       },
       // 自定义费率实时验证
       validateCustomRate: function (e) {
         this.validateCustomRateValue(e.detail.value);
       },
       // 自定义费率失去焦点验证
       validateCustomRateOnBlur: function () {
         this.validateCustomRateValue(this.info.custom_rate);
       }
    }
  };
  </script>
  
  <style>
  radio{transform: scale(0.6);}
  checkbox{transform: scale(0.6);}
  .apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
  .apply_title { background: #fff}
  .apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}
  .apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}
  
  .apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }
  .apply_box .apply_item:last-child{ border:none}
  .apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}
  .apply_item input::placeholder{ color:#999999}
  .apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}
  .apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }
  .apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }
  .set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
  
  .layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
  .layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}
  .layui-imgbox-close image{width:100%;height:100%}
  .layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
  .layui-imgbox-img>image{max-width:100%;}
  .layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
  .uploadbtn{position:relative;height:200rpx;width:200rpx}
  
  /* 返回按钮样式 */
  .back-button {
    width: 90%;
    background: #b60000;
    color: #fff;
    text-align: center;
    height: 96rpx;
    line-height: 96rpx;
    border-radius: 50px;
    margin-top: 0rpx;
    margin: auto;
  }
  
  .back-button .t1 {
    font-size: 30rpx;
    color: #fff;
  }

  /* 类目搜索样式 */
  .search-picker {
    text-align: right;
  }

  .cate-search-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cate-search-popup {
    position: relative;
    width: 80%;
    max-height: 70%;
    background-color: #fff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    animation: popup 0.3s ease;
  }

  @keyframes popup {
    from {
      transform: scale(0.8);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  .cate-search-header {
    display: flex;
    padding: 20rpx;
    border-bottom: 1rpx solid #eee;
    background-color: #fff;
  }
  .cate-search-input {
    flex: 1;
    background: #f5f5f5;
    border-radius: 36rpx;
    padding: 10rpx 20rpx;
    margin-right: 20rpx;
  }
  .cate-search-input input {
    height: 60rpx;
    width: 100%;
  }
  .cate-search-close {
    width: 100rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    color: #666;
  }
  .cate-search-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 30rpx;
    max-height: 600rpx;
  }
  .cate-search-item {
    padding: 30rpx 0;
    border-bottom: 1rpx solid #eee;
    font-size: 30rpx;
  }
  .cate-no-result {
    padding: 50rpx 0;
    text-align: center;
    color: #999;
  }

  /* 费率选择样式 */
  .rate-tips {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #999;
    padding-left: 10rpx;
  }

  .rate-tip-text {
    margin-right: 20rpx;
  }

  .rate-error {
    color: #f00;
    font-size: 24rpx;
  }
  </style>
  