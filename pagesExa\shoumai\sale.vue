<template>
<view class="container">
	<block v-if="isload">
	<form @submit="formSubmit">
		<!-- 资产类型选择 -->
		<view class="asset-type-selector">
			<view class="selector-title">選擇售賣資產類型</view>
			<view class="selector-content">
				<view 
					v-for="(asset, index) in availableAssets" 
					:key="asset.type" 
					class="asset-item" 
					:class="{active: selectedAssetType === asset.type}"
					@tap="selectAssetType(asset.type, index)">
					<text>{{asset.name}}</text>
					<text class="asset-amount">{{asset.amount}}</text>
				</view>
			</view>
		</view>
		
		<!-- 资产余额显示 -->
		<view class="mymoney" :style="{background:t('color1')}" v-if="selectedAsset">
			<view class="f1">{{selectedAsset.name}}</view>
			<view class="f2">{{selectedAsset.amount}}</view>
		</view>
		
		<!-- 售卖说明 -->
		<view class="notice-box">
			<view class="notice-title">售卖說明</view>
			<view class="notice-content">
				<text v-if="selectedAsset">1. {{selectedAsset.name}}售卖折扣率范围: {{selectedAsset.min_discount}} - {{selectedAsset.max_discount}}</text>
				<text>2. 您的資產將以折扣價格出售給其他用戶</text>
				<text>3. 買家將直接向您支付相應的金額</text>
				<text>4. 交易成功前您的資產將被凍結</text>
				<text>5. 請務必先設置錢包信息才能進行售賣</text>
			</view>
		</view>
		
		<view class="content2">
			<!-- 售卖金额 -->
			<view class="item2"><view class="f1">出售{{selectedAsset ? selectedAsset.name : '資產'}}</view></view>
			<view class="item3">
				<view class="f2">
					<input class="input" type="digit" name="amount" v-model="amount" placeholder="請輸入金額" placeholder-style="color:#999;font-size:40rpx" @input="moneyinput"></input>
				</view>
			</view>
			
			<!-- 折扣率设置 -->
			<view class="discount-section" v-if="selectedAsset">
				<view class="discount-title">折扣率設置</view>
				<view class="discount-slider">
					<slider 
						:min="selectedAsset.min_discount * 100" 
						:max="selectedAsset.max_discount * 100" 
						:value="discount * 100" 
						show-value 
						@change="discountChange"
						activeColor="#533CD7"
						backgroundColor="#eee"
					/>
				</view>
				<view class="discount-value">
					<text>当前折扣率: {{(discount * 100).toFixed(0)}}%</text>
					<text v-if="amount > 0">折扣后金额: {{(amount * discount).toFixed(2)}} 元</text>
				</view>
			</view>
			
			<!-- 最低售卖限制提示 -->
			<view class="item4" v-if="amount > 0 && settings && settings.sale_fee > 0">
				<text>手续费率: {{settings.sale_fee * 100}}%, 手续费: {{(amount * discount * settings.sale_fee).toFixed(2)}} 元</text>
			</view>
			<view class="item4" v-if="amount > 0 && settings && settings.min_amount > 0">
				<text style="color:#ff6b6b">最低售卖金额: {{settings.min_amount}} 元</text>
			</view>
		</view>
		
		<!-- 提交按钮 -->
		<button class="btn" :style="{background:t('color1')}" @tap="formSubmit">確 認 售 出</button>
		
		<!-- 钱包设置链接 -->
		<view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center" @tap="goto" data-url="/pagesExa/shoumai/walletsite">
			<text style="margin-right:10rpx">{{ userinfo.set_wallet == 0 ? '請先設定USTD帳戶' : '修改USTD帳戶' }}</text>
			<image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx"/>
		</view>
	</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
		opt:{},
		loading: false,
      	isload: false,
		menuindex: -1,
		
		// 用户信息
      	userinfo: [],
		
		// 设置信息
		settings: null,
		
		// 可用资产类型
		availableAssets: [],
		selectedAssetType: "",
		selectedAsset: null,
		
		// 金额和折扣
		amount: "",
		discount: 0.9, // 默认折扣
    };
  },

  onLoad: function (opt) {
	this.opt = app.getopts(opt);
	this.getdata();
  },
  
  onPullDownRefresh: function () {
	this.getdata();
	uni.stopPullDownRefresh();
  },
  
  methods: {
	// 获取数据
	getdata: function () {
		var that = this;
		that.loading = true;
		
		// 获取售卖设置信息
		app.get('ApiShoumai/getSaleSettings', {}, function (res) {
			that.loading = false;

			if (!res.status) {
				app.error(res.msg || '获取设置失败');
				return;
			}
			
			// 检查钱包设置
			if (res.data.user_info.set_wallet === 0) {
				app.error('請先設置錢包信息');
				setTimeout(function() {
					app.goto("/pagesExa/shoumai/walletsite")
				}, 1000);
				return;
			}

			// 设置标题
			uni.setNavigationBarTitle({
				title: '资产售卖'
			});
			
			// 保存设置和用户信息
			that.settings = res.data.settings;
			that.userinfo = res.data.user_info;
			
			// 处理可用资产类型
			that.processAvailableAssets(res.data);
			
			that.isload = true;
		});
	},
	
	// 处理可用资产类型
	processAvailableAssets: function(data) {
		var assets = data.assets || [];
		var settings = data.settings;
		
		// 过滤可用资产类型
		var availableAssets = [];
		
		if (settings.enable_money === 1 && assets.find(a => a.type === 'money')) {
			availableAssets.push(assets.find(a => a.type === 'money'));
		}
		
		if (settings.enable_score === 1 && assets.find(a => a.type === 'score')) {
			availableAssets.push(assets.find(a => a.type === 'score'));
		}
		
		if (settings.enable_commission === 1 && assets.find(a => a.type === 'commission')) {
			availableAssets.push(assets.find(a => a.type === 'commission'));
		}
		
		if (settings.enable_heiscore === 1 && assets.find(a => a.type === 'heiscore')) {
			availableAssets.push(assets.find(a => a.type === 'heiscore'));
		}
		
		if (settings.enable_contribution === 1 && assets.find(a => a.type === 'contribution')) {
			availableAssets.push(assets.find(a => a.type === 'contribution'));
		}
		
		if (settings.enable_score_huangjin === 1 && assets.find(a => a.type === 'score_huangjin')) {
			availableAssets.push(assets.find(a => a.type === 'score_huangjin'));
		}
		
		this.availableAssets = availableAssets;
		
		// 如果有可用资产，默认选择第一个
		if (availableAssets.length > 0) {
			this.selectAssetType(availableAssets[0].type, 0);
		}
	},
	
	// 选择资产类型
	selectAssetType: function(type, index) {
		this.selectedAssetType = type;
		this.selectedAsset = this.availableAssets[index];
		
		// 设置折扣默认值为中间值
		if (this.selectedAsset) {
			var minDiscount = parseFloat(this.selectedAsset.min_discount);
			var maxDiscount = parseFloat(this.selectedAsset.max_discount);
			this.discount = ((minDiscount + maxDiscount) / 2).toFixed(2);
		}
		
		// 清空金额
		this.amount = "";
	},
	
	// 折扣滑块变化处理
	discountChange: function(e) {
		this.discount = (e.detail.value / 100).toFixed(2);
	},
		
    // 金额输入处理
    moneyinput: function (e) {
		if (!this.selectedAsset) {
			app.error('請先選擇資產類型');
			return;
		}
		
		var assetAmount = parseFloat(this.selectedAsset.amount);
		var amount = parseFloat(e.detail.value);
      
		// 清空金额时的处理
		if (e.detail.value === '' || isNaN(amount)) {
			this.amount = '';
			return;
		}
      
		// 金额验证
		if (amount <= 0) {
			app.error('金額必須大於0');
			this.amount = '';
			return;
		} else if (amount > assetAmount) {
			app.error(this.selectedAsset.name + '餘額不足');
			this.amount = assetAmount.toString();
			return;
		}
	  
		this.amount = amount.toString();
    },
	
    // 表单提交
    formSubmit: function () {
		var that = this;
		
		// 验证资产类型选择
		if (!that.selectedAsset) {
			app.error('請選擇要售賣的資產類型');
			return;
		}
		
		var assetAmount = parseFloat(that.selectedAsset.amount);
		var amount = parseFloat(that.amount);
		var discount = parseFloat(that.discount);
      
		// 验证输入
		if (isNaN(amount) || amount <= 0) {
			app.error('金額必須大於0');
			return;
		}
      
		// 验证最低售卖金额
		if (that.settings.min_amount > 0 && amount < that.settings.min_amount) {
			app.error('金額必須大於' + that.settings.min_amount);
			return;
		}

		// 验证资产余额
		if (amount > assetAmount) {
			app.error(that.selectedAsset.name + '餘額不足');
			return;
		}
		
		// 验证折扣率范围
		var minDiscount = parseFloat(that.selectedAsset.min_discount);
		var maxDiscount = parseFloat(that.selectedAsset.max_discount);
		
		if (discount < minDiscount || discount > maxDiscount) {
			app.error('折扣率必須在' + (minDiscount * 100) + '%-' + (maxDiscount * 100) + '%之間');
			return;
		}
			
		// 二次确认
		var discountedPrice = (amount * discount).toFixed(2);
		app.confirm('確認出售' + amount + that.selectedAsset.name + '嗎？折扣后約為' + discountedPrice + '元', function() {
			app.showLoading('提交中');
			
			// 提交售卖信息
			app.post('ApiShoumai/postSale', {
				amount: amount,
				discount: discount,
				asset_type: that.selectedAssetType
			}, function (data) {
				app.showLoading(false);
				
				if (data.status == 0) {
					app.error(data.msg);
					return;
				} else {
					app.success(data.msg);
					setTimeout(function () {
						app.goto('/pagesExa/shoumai/index?my=1');
					}, 1000);
				}
			});
		});
    }
  }
};
</script>
<style>
.container{display:flex;flex-direction:column}

/* 资产类型选择器样式 */
.asset-type-selector {
	width: 94%;
	margin: 20rpx 3%;
	background: #fff;
	border-radius: 10rpx;
	padding: 20rpx;
}
.selector-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}
.selector-content {
	display: flex;
	flex-wrap: wrap;
}
.asset-item {
	width: calc(33.33% - 20rpx);
	margin: 10rpx;
	background: #f5f5f5;
	border-radius: 10rpx;
	padding: 20rpx;
	text-align: center;
	display: flex;
	flex-direction: column;
}
.asset-item.active {
	background: #533CD7;
	color: #fff;
}
.asset-amount {
	font-size: 24rpx;
	margin-top: 10rpx;
}

.mymoney{width:94%;margin:20rpx 3%;border-radius: 10rpx 56rpx 10rpx 10rpx;position:relative;display:flex;flex-direction:column;padding:70rpx 0}
.mymoney .f1{margin:0 0 0 60rpx;color:rgba(255,255,255,0.8);font-size:24rpx;}
.mymoney .f2{margin:20rpx 0 0 60rpx;color:#fff;font-size:64rpx;font-weight:bold}
.mymoney .f3{height:56rpx;padding:0 10rpx 0 20rpx;border-radius: 28rpx 0px 0px 28rpx;background:rgba(255,255,255,0.2);font-size:20rpx;font-weight:bold;color:#fff;display:flex;align-items:center;position:absolute;top:94rpx;right:0}

.notice-box {
	width: 94%;
	margin: 10rpx 3% 20rpx 3%;
	background: #f8f8fc;
	border-radius: 10rpx;
	padding: 20rpx;
	border-left: 4px solid #533CD7;
}
.notice-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}
.notice-content {
	display: flex;
	flex-direction: column;
}
.notice-content text {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
	line-height: 1.5;
}

/* 折扣率设置样式 */
.discount-section {
	width: 94%;
	margin: 0 3%;
	padding: 20rpx 0;
	border-top: 1px solid #F0F0F0;
}
.discount-title {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 20rpx;
}
.discount-slider {
	padding: 0 20rpx;
}
.discount-value {
	display: flex;
	justify-content: space-between;
	font-size: 24rpx;
	color: #666;
	margin-top: 10rpx;
	padding: 0 20rpx;
}

.content2{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff}
.content2 .item1{display:flex;width:100%;border-bottom:1px solid #F0F0F0;padding:0 30rpx}
.content2 .item1 .f1{flex:1;font-size:32rpx;color:#333333;font-weight:bold;height:120rpx;line-height:120rpx}
.content2 .item1 .f2{color:#FC4343;font-size:44rpx;font-weight:bold;height:120rpx;line-height:120rpx}

.content2 .item2{display:flex;width:100%;padding:0 30rpx;padding-top:10rpx}
.content2 .item2 .f1{height:80rpx;line-height:80rpx;color:#999999;font-size:28rpx}

.content2 .item3{display:flex;width:100%;padding:0 30rpx;padding-bottom:20rpx}
.content2 .item3 .f1{height:100rpx;line-height:100rpx;font-size:60rpx;color:#333333;font-weight:bold;margin-right:20rpx}
.content2 .item3 .f2{display:flex;align-items:center;font-size:60rpx;color:#333333;font-weight:bold}
.content2 .item3 .f2 .input{font-size:60rpx;height:100rpx;line-height:100rpx;}
.content2 .item4{display:flex;width:94%;margin:0 3%;border-top:1px solid #F0F0F0;height:100rpx;line-height:100rpx;color:#8C8C8C;font-size:28rpx}

.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}
</style>