<template>
	<view class="container" :style="{backgroundColor:'pageinfo.bgcolor'}">
		<view class="header">
			{{tit}}
			<view class="pickerview">
				<uni-datetime-picker v-model="date_time" type="date" @change="clickTime">
					<view style="display: flex; align-items: center;">
						<uni-icons type="calendar" size="20" color="#999999"></uni-icons>
						{{date_time || '请选择你要运动的时间'}}
					</view>
					
				</uni-datetime-picker>
			</view>
		</view>
		<view style="margin: 50rpx 20rpx 0; background: #f5f5f5; border-radius: 70rpx 70rpx 0 0;">
			<view class="tabs">
				<uni-data-picker popup-title="请选择" :localdata="list1" v-model="list1_id" class="tabs_item"
					@change="onchange1">{{list1_name}}∨</uni-data-picker>
				<uni-data-picker popup-title="请选择" :localdata="list2" v-model="list2_id" class="tabs_item"
					@change="onchange2">{{list2_name}}∨</uni-data-picker>
				<uni-data-picker popup-title="请选择" :localdata="list3" v-model="list3_id" class="tabs_item"
					@change="onchange3">{{list3_name}}∨</uni-data-picker>
			</view>
			<scroll-view scroll-y="true" class="scroll-Y" @scrolltoupper="upper">
				<view class="item" v-for="item,index in list" :key="index" @tap="goItem(index)">
					<img :src="item.logo"
						class="itemImg" />
					<view class="itemMain">
						<view class="name">{{item.title}}</view>
						<view class="info">
							<view class="tag">{{item.score}}</view>
							5599人预定.{{item.visit}}浏览
						</view>
						<view class="address">
							<view class="detail">{{tit}}/{{item.district}}</view>
							<view class="distance">{{item.distance}}</view>
						</view>
						<view class="other">
							<view class="otherTag" v-for="ite,ind in item.services" :key="ind" >
								{{ite}}
							</view>
						</view>
<!-- 						<view class="tagInfo">
							<view class="tag_icon">
								<u-icon name="gift" color="#d62828" size="18"></u-icon>
							</view>
							全民健身补贴
						</view> -->
					</view>
				</view>
			</scroll-view>
		</view>

	</view>
</template>
<script>
import list from '../../uni_modules/uview-ui/libs/config/props/list';
	var app = getApp();
	export default {
		data() {
			return {
				id: '',
				tit : '',
				pageinfo: [],
				list1: [],
				list2: [{
						text: "推荐排序",
						value: "",
					},
					{
						text: "二年级",
						value: "2-0"
					},
					{
						text: "三年级",
						value: "3-0"
					}
				],
				list3: [],
				list1_id: '',
				list1_name: '全部区域',
				list2_id: '',
				list2_name: '推荐排序',
				list3_id: '',
				list3_name: '筛选',
				date_time: '',
				
				list : []   
			}
		},
		onLoad: function(opt) {
			// this.opt = app.getopts(opt);
			
			this.tit = opt.name;
			this.id = opt.id;
			
			var sysinfo = uni.getSystemInfoSync();

			this.statusBarHeight = sysinfo.statusBarHeight;
			this.screenWidth = sysinfo.screenWidth;
			// var cachearea = app.getCache('user_current_area');
			// var cachelongitude = app.getCache('user_current_longitude');
			// var cachelatitude = app.getCache('user_current_latitude');
			// if (cachearea || cachearea != 1) {
			// 	this.area = cachearea
			// 	this.latitude = cachelatitude
			// 	this.longitude = cachelongitude
			// }
			 this.getdata();

		},
		onPullDownRefresh: function(e) {
			this.getdata();
		},
		onPageScroll: function(e) {
			uni.$emit('onPageScroll', e);
		},
		methods: {
			onchange1(e) {
				this.list1_id = e.detail.value[0].value
				this.list1_name = e.detail.value[0].text
			},
			onchange2(e) {
				this.list2_id = e.detail.value[0].value
				this.list2_name = e.detail.value[0].text
			},
			onchange3(e) {
				this.list3_id = e.detail.value[0].value
				this.list3_name = e.detail.value[0].text
			},
			upper() {

			},
			clickTime(e){
				this.date_time=e
			},
			getdata: function() {
				var that = this;
				
				
				var obj ={
					// city : '广州市',					
					// district : '天河区',
					cid : this.id,
					// // sid :  3,
					// longitude :'113.36427211761475',
					// latitude : '23.12260064832429',  
					// // pagenum : 1,
					// day : '2024-03-08',
					// time : '火车站',  
					//hour : '1',
					// distance : 1,
					// hprice : 1,
					// lprice : 1
					
				}  
		
				that.loading = true;
				
				app.get('/ApiVenues/getVenuesLsit', obj, function(res) {
				    
					console.log(res)
					
					if(res.status == 1){
						
						 let dis = [];
						 
						 res.data.short_list.district.filter(item=>{
							 let obj ={
								 text : item.name,
								 value : item.id
							 }
							 
							 dis.push(obj);
						 })
						 
						 that.list1 = dis;
						  
		
						 let arrs = [];
						 
						 res.data.short_list.service.filter(item=>{
							 let obj ={
								 text : item.name,
								 value : item.id
							 }
							 
							 arrs.push(obj);
						 })
						 
						that.list3 = arrs;
						
						
						res.data.list.filter((item,k)=>{
							
							let ss = item.service_id.split(",");
							
							ss.filter((o,i)=>{
								
								that.list3.filter((m,n)=>{
									
									if(o == m.value){
										ss[i] = m.text;
									}
								})
								
							})
							
							res.data.list[k].services = ss;
						})
						
						
						that.list = res.data.list;

						//console.log(that.list)
						
					}   	   
			
				});
				
			},
			
			goItem(ind){
				
				let item = this.list[ind];
				
				uni.navigateTo({
					url:'../../pagesB/dingchang/dingchangdetail?id='+item.id+'&services='+item.services.toString()
				})
			},
			
			
			showsubqrcode: function() {
				this.$refs.qrcodeDialog.open();
			},
			closesubqrcode: function() {
				this.$refs.qrcodeDialog.close();
			},
			changePopupAddress: function(status) {
				this.xdata.popup_address = status;
			},
			setMendianData: function(data) {
				this.mendian_data = data;
			},
			toChoseAddress() {
				uni.navigateTo({
					url: '/pages/choseAddress/choseAddress'
				})
			},
		}
	}
</script>
<style>
	page {
		background: linear-gradient(to right, #b1bcf5, #e5e7f5);
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 50rpx 24rpx 24rpx;
		box-sizing: border-box;
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
	}

	.pickerview {
		width: 550rpx;
		height: 80rpx;
		border: 1rpx solid #E5E8EF;
		border-radius: 200rpx;
		line-height: 80rpx;
		text-indent: 20rpx;
		font-size: 26rpx;
		color: #999999;
		background: #e1e6f7;
	}

	.tabs {
		display: flex;
		height: 80rpx;
		align-items: center;
		background: #ffffff;
		border-radius: 70rpx 70rpx 0 0;
	}

	.tabs_item {
		font-size: 32rpx;
		color: #333333;
		flex: 1;
		display: flex;
		justify-content: center;
	}

	.scroll-Y {
		height: calc(100vh - 300rpx);
		padding: 24rpx;
		box-sizing: border-box;
	}

	.item {
		background: #ffffff;
		padding: 16rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: flex-start;
	}

	.itemImg {
		width: 160rpx;
		height: 120rpx;
	}

	.itemMain {
		flex: 1;
		padding-left: 16rpx;
	}

	.name {
		height: 48rpx;
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 12rpx;
	}

	.info {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 12rpx;
	}

	.tag {
		border-radius: 32rpx 0 24rpx 32rpx;
		background: linear-gradient(to right, #8ca3fa, #6b63ec);
		color: #ffffff;
		padding: 0 10rpx;
		height: 40rpx;
		font-size: 28rpx;
		font-weight: bold;
		margin-right: 15rpx;
	}

	.address {
		display: flex;
		align-items: flex-start;
		margin-bottom: 12rpx;
	}

	.detail {
		width: 70%;
		font-size: 24rpx;
		color: #666666;
	}

	.distance {
		padding-left: 10rpx;
		box-sizing: border-box;
		font-size: 24rpx;
		color: #666666;
		flex: 1;
		text-align: right;
	}

	.other {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #E5E8EF;
	}

	.otherTag {
		padding: 0 8rpx;
		border: 1rpx solid #E5E8EF;
		font-size: 24rpx;
		color: #666666;
		height: 32rpx;
		margin-right: 10rpx;
	}

	.tagInfo {
		line-height: 50rpx;
		font-size: 26rpx;
		color: #666666;
		margin-top: 15rpx;
		display: flex;
		align-items: center;
	}
	.tag_icon {
		background: #efd599;
		height: 100%;
		width: 46rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 9rpx 0 18rpx 9rpx;
	}
</style>