<template>
<view class="container">
	<block v-if="isload">
		<block v-if="cartlist.length>0">
			<view class="cartmain">
				<block v-for="(item, index) in cartlist" :key="item.id">
					<view class="item">
						<view class="content flex-y-center">
							<view @tap.stop="changeradio" :data-index="index" class="radio" :style="item.checked ? 'background:'+t('color1')+';border:0' : ''"><image class="radio-img" src="/static/img/checkd.png"/></view>
							<image :src="item.product.pic" class="img" @tap="gotoFun" :data-url="'/shopPackage/shop/product?id=' + item.product.id"/>
							<view class="detail">
								<view class="title"><text>{{item.product.name}}</text></view>
								<view class="desc"><text>价值：￥{{item.product.sell_price}}</text></view>
								<view class="buynum flex flex-y-center">
									<view class="price flex1" :style="{color:t('color1')}"><text v-if="item.product.money_price>0">￥{{item.product.money_price}}+</text>{{item.product.score_price}}{{t('积分')}}</view>
									<view class="f2 flex flex-y-center">
										<view class="minus flex-x-center" @tap="gwcminus" :data-index="index" :data-cartid="item.id" :data-num="item.num">-</view>
										<input class="flex-x-center" type="number" :value="item.num" @blur="gwcinput" :data-max="item.product.stock" :data-index="index" :data-cartid="item.id" :data-num="item.num"></input>
										<view class="plus flex-x-center" @tap="gwcplus" :data-index="index" :data-max="item.product.stock" :data-num="item.num" :data-cartid="item.id">+</view>
									</view>
								</view>
							</view>
							<view class="prodel" @tap="cartdelete" :data-cartid="item.id"><image src="/static/img/del.png"/></view>
						</view>
					</view>
				</block>
			</view>
			
			<view style="height:auto;position:relative">
				<view style="width:100%;height:110rpx"></view>
				<view class="footer flex" :class="menuindex>-1?'tabbarbot':'notabbarbot'">
					<view class="text1">合计：</view>
					<view class="text2" :style="{color:t('color1')}"><text v-if="totalmoney>0">￥{{totalmoney}}+</text><text>{{totalscore}}{{t('积分')}}</text></view>
					<view class="flex1"></view>
					<view class="op" @tap="toOrder" :style="{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">去结算</view>
				</view>
			</view>

		</block>
		<block v-else>
			<view class="data-empty">
				<image :src="pre_url+'/static/img/cartnull.png'" class="data-empty-img" style="width:120rpx;height:120rpx"/>
				<view class="data-empty-text" style="margin-top:20rpx;font-size:24rpx">购物车空空如也~</view>
				<button style="width:400rpx;border:1px solid #ff6801;border-radius:6rpx;background:#ff6801;margin-top:20px;color:#fff" @tap="goto" data-url="index">去选购</button>
			</view>
		</block>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			initdata:{},
			pre_url:app.globalData.pre_url,

			cartlist:[],
			totalscore:0,
      totalmoney: '0.00',
      selectedcount: 0,
    };
  },
  
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
  },
	onShow:function(){
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
    getdata: function (){
      var that = this;
			that.loading = true;
      app.get('ApiScoreshop/cart', {}, function (res) {
				that.loading = false;
        that.cartlist = res.cartlist;
        var cartlist = res.cartlist;
        for (var i in cartlist) {
					cartlist[i].checked = true;
        }
        that.calculate();
				that.loaded();
      });
    },
    changeradio: function (e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
			var cartlist = that.cartlist;
      var checked = cartlist[index].checked;
			if(checked){
				cartlist[index].checked = false;
			}else{
				cartlist[index].checked = true;
			}
			that.cartlist = cartlist;
      that.calculate();
    },
    calculate: function () {
      var that = this;
      var cartlist = that.cartlist;
      var totalmoney = 0.00;
      var totalscore = 0.00;
      var selectedcount = 0;
      for (var i in cartlist) {
				if (cartlist[i].checked) {
					var thiscart = cartlist[i];
					totalmoney += thiscart.product.money_price * thiscart.num;
					totalscore += thiscart.product.score_price * thiscart.num;
					selectedcount += thiscart.num;
				}
      }
      that.totalmoney = totalmoney.toFixed(2);
      that.totalscore = totalscore;
      that.selectedcount = selectedcount;
    },
    checkpro: function (e) {
      var that = this;
      var ids = e.detail.value;
      that.ids = ids;
      that.calculate();
    },
    cartdelete: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.cartid;
      app.confirm('确定要从购物车移除吗?', function () {
        app.post('ApiScoreshop/cartdelete', {id: id}, function (data) {
          app.success('操作成功');
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    toOrder: function () {
      var that = this;
      var cartlist = that.cartlist;
      var prodata = [];
      for (var i in cartlist) {
				if (cartlist[i].checked) {
					prodata.push(cartlist[i].proid + ',' + cartlist[i].num);
				}
      }
      if (prodata.length == 0) {
        app.error('请先选择产品');
        return;
      }
      app.goto('buy?prodata=' + prodata.join('-'));
    },
    //加
    gwcplus: function (e) {
      var index = parseInt(e.currentTarget.dataset.index);
      var maxnum = parseInt(e.currentTarget.dataset.max);
      var cartid = e.currentTarget.dataset.cartid;
      var num = parseInt(e.currentTarget.dataset.num);
      if (num >= maxnum) {
        app.error('库存不足');
        return;
      }
			var cartlist = this.cartlist;
			cartlist[index].num++;
			this.cartlist = cartlist
			this.calculate();
      var that = this;
      app.post('ApiScoreshop/cartChangenum', {id: cartid,num: num + 1}, function (data) {
        if (data.status == 1) {
          //that.getdata();
        } else {
          app.error(data.msg);
        }
      });
    },
    //减
    gwcminus: function (e) {
      var index = parseInt(e.currentTarget.dataset.index);
      var maxnum = parseInt(e.currentTarget.dataset.max);
      var cartid = e.currentTarget.dataset.cartid;
      var num = parseInt(e.currentTarget.dataset.num);
      if (num == 1) return;
			var cartlist = this.cartlist;
			cartlist[index].num--;
			this.cartlist = cartlist
			this.calculate();
      var that = this;
      app.post('ApiScoreshop/cartChangenum', {id: cartid,num: num - 1}, function (data) {
        if (data.status == 1) {
          //that.getdata();
        } else {
          app.error(data.msg);
        }
      });
    },
    //输入
    gwcinput: function (e) {
      var index = parseInt(e.currentTarget.dataset.index);
      var maxnum = parseInt(e.currentTarget.dataset.max);
      var cartid = e.currentTarget.dataset.cartid;
      var num = e.currentTarget.dataset.num;
      var newnum = parseInt(e.detail.value);
      console.log(num + '--' + newnum);
      if (num == newnum) return;

      if (newnum > maxnum) {
        app.error('库存不足');
        return;
      }
			var cartlist = this.cartlist;
			cartlist[index].num = newnum;
			this.cartlist = cartlist
			this.calculate();

      var that = this;
      app.post('ApiScoreshop/cartChangenum', {id: cartid,num: newnum}, function (data) {
        if (data.status == 1) {
          //that.getdata();
        } else {
          app.error(data.msg);
        }
      });
    },
		addcart:function(){
			this.getdata();
		}
  }
};
</script>
<style>
.cartmain .item {width: 94%;margin:20rpx 3%;background: #fff;border-radius:20rpx;padding:30rpx 3%;}

.cartmain .item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}
.cartmain .item .radio .radio-img{width:100%;height:100%}

.cartmain .item .content {width:100%;margin:0;position: relative;}
.cartmain .item .content .check {width:70rpx;margin-top: 30rpx;}
.cartmain .item .content .img {width: 140rpx;height: 140rpx;}
.cartmain .item .content .detail {flex:1;margin-left:16rpx}
.cartmain .item .content .detail .title {padding-right:40rpx;color: #222222;font-weight:bold;font-size:26rpx;line-height:34rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:68rpx}
.cartmain .item .content .detail .desc {height: 46rpx;line-height: 46rpx;color: #666;overflow: hidden;font-size: 24rpx;}
.cartmain .item .content .prodel{width:24rpx;height:24rpx;position:absolute;top:0;right:0}
.cartmain .item .content .prodel image{width:100%;height:100%}

.cartmain .item .buynum .price{height: 46rpx;display:flex;align-items:center;font-size:26rpx;font-weight:bold}
.cartmain .item .buynum .f2{ border:1px solid #f1f1f1}
.cartmain .item .buynum .f2 input{flex:1;width:70rpx;border-left:1px solid #f1f1f1;border-right:1px solid #f1f1f1;text-align:center;color:#333;font-size:26rpx}
.cartmain .item .buynum .f2 .plus{width:50rpx;}
.cartmain .item .buynum .f2 .minus{width:50rpx;}

.cartmain .item .bottom{width: 94%;margin: 0 3%;border-top: 1px #e5e5e5 solid;padding: 10rpx 0px;overflow: hidden;color: #ccc;display:flex;align-items:center;justify-content:flex-end}
.cartmain .item .bottom .f1{display:flex;align-items:center;color:#333}
.cartmain .item .bottom .f1 image{width:40rpx;height:40rpx;border-radius:4px;margin-right:4px}
.cartmain .item .bottom .op {border: 1px #ff4246 solid;border-radius: 10rpx;color: #ff4246;padding: 0 10rpx;height: 46rpx;line-height: 46rpx;margin-left: 10rpx;}

.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}
.footer .text0{color:#666666;font-size:24rpx;}
.footer .text1 {height: 110rpx;line-height: 110rpx;color:#444;font-weight:bold;font-size:24rpx;margin-right:10rpx}
.footer .text2 {color: #F64D00;font-size: 30rpx;font-weight:bold}
.footer .op{width: 216rpx;height: 80rpx;line-height:80rpx;border-radius: 6rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center;margin-left:30rpx}

.data-empty {width: 100%; text-align: center; padding-top:100rpx;padding-bottom:100rpx}
.data-empty-img{ width: 300rpx; height: 300rpx; display: inline-block; }
.data-empty-text{ display: block; text-align: center; color: #999999; font-size:32rpx; width: 100%; margin-top: 30rpx; }
</style>