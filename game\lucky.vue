<template>
	<view class="container">
		<view class="newgame1">
			<view class="title">
				<view class="title-text" @tap="onBack">返回</view>
				<view class="title-text" @tap="goToOrder">订单</view>
			</view>
			<view class="tiao">
				<view class="tiao-text-wrap">
					中奖概率：
					<text class="tiao-num">{{ getProbability() }}%</text>
				</view>
			</view>
			<view class="goods_box">
				<view class="goods_box_header">
					<image class="goods-img" :src="productInfo.pic" />
				</view>
				<view class="goods_text">需要{{ price }}元</view>
				<view class="goods_text">{{ price }}元就能抽奖，算法绝对公平</view>
			</view>
			<!-- 		<view class="yuezhifu">
				<view class="yuezhifu-l">
					<image class="icon" src="@/game/static/game/lucky/icon1.png" />
					<text class="yuezhifu-l_title">体力</text>
					<text class="yuezhifu-l_num">¥0.88</text>
				</view>
				<view class="yuezhifu-r">
					(余量：
					<text>1</text>
					)
				</view>
			</view>
			<view class="yuezhifu">
				<view class="yuezhifu-l">
					<image class="icon" src="@/game/static/game/lucky/icon2.png" />
					<text class="yuezhifu-l_title">积分</text>
					<text class="yuezhifu-l_num">88</text>
				</view>
				<view class="yuezhifu-r">
					(余量：
					<text></text>
					)
				</view>
			</view> -->
			<view class="btn">
				<view class="btn-wrap btn-wrap-l">
					<view class="icon-reduce" @tap="onReduce">-</view>
					<text>
						<text>{{ time }}</text>
						次
					</text>
					<view @tap="onAdd">+</view>
				</view>
				<view class="btn-wrap btn-wrap-r" @click="onSubmit">马上试一把</view>
			</view>
			<view class="tips">批量抽奖，节省时间且更容易中奖哟~</view>
		</view>
		<!-- 中奖提示 -->
		<uni-popup ref="popupResult">
			<view class="pri_box">
				<view class="pri_box_header">
					<text>恭喜中奖</text>
					<view class="close_wrap" @tap="onCloseResultModal">
						<uni-icons type="closeempty" size="24" color="#fff"></uni-icons>
					</view>
				</view>
				<view class="goods_box">
					<view class="goods_box_header">
						<image class="goods-img" :src="productInfo.pic" />
					</view>
					<view class="goods_text">{{ productInfo.name }}</view>
				</view>
				<button class="btn-view" @click="goToOrder">去查看</button>
			</view>
		</uni-popup>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {
				amount: 0
			},
			loading: false,
			id: null,
			// 商品信息
			productInfo: {
				name: '',
				pic: '',
				sell_price: 0
			},
			price: 0,
			time: 1
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.price = this.opt.amount;
		this.getdata();

		if (this.opt.oid) {
			this.loading = true;
			this.getResult();
		}
	},
	methods: {
		onBack() {
			uni.redirectTo({ url: `/shopPackage/shop/product?id=${this.opt.id}` });
		},
		goToOrder() {
			app.goto('/game/order');
		},
		/**
		 * 商品信息
		 */
		getdata: function () {
			let that = this;
			let id = this.opt.id || 0;
			that.loading = true;
			app.get(
				'ApiShop/product',
				{
					id: id
				},
				function (res) {
					that.loading = false;
					if (res.status == 0) {
						app.alert(res.msg);
						return;
					}
					that.productInfo = res.product;
				}
			);
		},
		getProbability() {
			if (this.opt.amount && this.productInfo.sell_price) {
				let num = this.opt.amount / this.productInfo.sell_price;
				return ((num && num * 100) || 0).toFixed(2);
			} else {
				return (0).toFixed(2);
			}
		},
		onReduce() {
			if (this.time > 1) {
				this.time--;
				// this.price = (this.time * this.opt.amount).toFixed(2)
			}
		},
		onAdd() {
			this.time++;
			// this.price = (this.time * this.opt.amount).toFixed(2)
		},
		onCloseResultModal() {
			this.$refs.popupResult.close();
		},
		onSubmit() {
			let that = this;
			that.loading = true;
			const params = {
				sid: that.opt.gameId,
				pid: that.opt.id,
				amount: that.opt.amount,
				frequency: that.time,
				tourl: app._fullurl()
			};
			app.post('ApiSweepstakes/postRaffle', params, function (res) {
				switch (res.status) {
					case 1:
						if (res.is_lottery === 1) {
							app.alert('恭喜你，中奖了');
						} else {
							app.alert('未中奖，请再接再厉');
						}
						break;
					case 2:
						app.confirm(res.msg || '出错了', function () {
							app.goto(res.pathUrl);
						});
						break;
					case 3:
						app.goto(res.pathUrl);
						break;
					default:
						app.alert(res.msg || '出错了');
						break;
				}
			});
		},
		getResult() {
			let that = this;
			const params = {
				oid: this.opt.oid
			};
			app.post('ApiSweepstakes/getResult', params, function (res) {
				if (res.status === 1) {
					if (res.is_lottery === 1) {
						app.alert('恭喜你，中奖了', function () {
							app.goto(app._delOrderId());
						});
					} else {
						app.alert('未中奖，请再接再厉', function () {
							app.goto(app._delOrderId());
						});
					}
				} else {
					app.alert(res.msg, function () {
						app.goto(app._delOrderId());
					});
				}
			});
		}
	}
};
</script>

<style scoped>
.container {
	height: 100vh;
	overflow: hidden;
}

.newgame,
.newgame1,
.newgame2 {
	height: 100%;
	background: url(../game/static/game/lucky/bg1.png) no-repeat;
	background-size: 100% 100%;
}

.newgame1 .title,
.newgame2 .title,
.newgame .title {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	padding: 25px 15px;
	box-sizing: border-box;
}

.title .title-text {
	width: 51px;
	height: 30px;
	border-radius: 30px;
	text-align: center;
	line-height: 30px;
	background: #aeb8ff;
	color: #6b52d5;
	font-size: 13px;
}

.newgame1 .tiao {
	height: 160px;
	padding: 110px 40px 0;
	box-sizing: border-box;
	background: url(../game/static/game/lucky/tiao_box.png) no-repeat;
	background-size: 100% 100%;
}

.newgame1 .tiao .tiao-text-wrap {
	height: 48px;
	text-align: center;
	color: #fff;
	line-height: 40px;
	font-size: 16px;
}

.newgame1 .tiao .tiao-text-wrap .tiao-num {
	color: #fae564;
}

.newgame1 .goods_box {
	width: 230px;
	height: 210px;
	background: #fff;
	border: 3px solid #683afa;
	border-radius: 3px;
	text-align: center;
	margin: 30px auto 0;
}

.newgame1 .goods_box .goods_box_header {
	width: 180px;
	height: 108px;
	background: url(../game/static/game/lucky/goods_bg.png) no-repeat;
	background-size: 100% 100%;
	margin: 22px auto 0;
}

.goods-img {
	width: 50px;
	height: 50px;
	margin: 21px auto 0;
}

.newgame1 .goods_box .goods_text {
	font-size: 14px;
	color: #212121;
	line-height: 25px;
}

/* 分割线 */

.newgame1 .yuezhifu {
	display: flex;
	justify-content: space-between;
	padding: 0 20px;
	color: #fff;
	margin-top: 20px;
}

.newgame1 .yuezhifu .yuezhifu-l_title {
	margin: 0 6px;
	font-size: 16px;
}

.newgame1 .yuezhifu .yuezhifu-l_num {
	color: #c4ff0e;
	font-weight: bold;
	font-size: 16px;
}

.newgame1 .yuezhifu .icon {
	width: 20px;
	height: 20px;
	position: relative;
	top: 5px;
}

.newgame1 .yuezhifu .yuezhifu-r {
	color: #0bff46;
	font-size: 16px;
}

.newgame1 .btn {
	margin: 20px auto 10px;
	height: 54px;
	width: 264px;
	border-radius: 54px;
	display: flex;
	justify-content: space-between;
	overflow: hidden;
}

.newgame1 .btn .btn-wrap {
	width: 50%;
	font-size: 18px;
	line-height: 54px;
	text-align: center;
}

.newgame1 .btn > view:first-child,
.newgame2 .btn > view:first-child,
.newgame .btn > view:first-child {
	background: #fff;
	color: #6987ff;
	font-size: 28px;
}

.newgame1 .btn > view:first-child text:nth-child(2),
.newgame2 .btn > view:first-child text:nth-child(2),
.newgame .btn > view:first-child text:nth-child(2) {
	font-size: 15px;
	margin: 0 5px;
}

.newgame1 .btn > view:first-child text:nth-child(2) text,
.newgame2 .btn > view:first-child text:nth-child(2) text,
.newgame .btn > view:first-child text:nth-child(2) text {
	font-size: 24px;
}

.newgame1 .btn > view:first-child > text:first-child,
.newgame1 .btn > view:first-child > text:last-child,
.newgame2 .btn > view:first-child > text:first-child,
.newgame2 .btn > view:first-child > text:last-child,
.newgame .btn > view:first-child > text:first-child,
.newgame .btn > view:first-child > text:last-child {
	font-size: 28px;
}

.newgame1 .btn > view:last-child,
.newgame2 .btn > view:last-child,
.newgame .btn > view:last-child {
	background: -webkit-linear-gradient(bottom, #ae61fd, #777fff);
	background: linear-gradient(0deg, #ae61fd, #777fff);
	color: #fff;
}

.newgame1 .tips,
.newgame2 .tips,
.newgame .tips {
	padding-bottom: 20px;
	color: #fff;
	font-size: 13px;
	text-align: center;
}

.btn-wrap-l {
	padding: 0 10px;
	display: flex;
	justify-content: space-around;
	align-items: center;
	box-sizing: border-box;
}

/* 弹窗 */
.pri_box {
	width: 240px;
	height: 250px;
	margin: 180px auto 0;
	background-color: #fff;
	text-align: center;
}

.pri_box_header {
	height: 60px;
	background: url(../game/static/game/happy/box_header_bg.png) no-repeat;
	background-size: 100% 100%;
	line-height: 60px;
	color: #fff;
	font-size: 20px;
	font-weight: 700;
	position: relative;
}

.btn-confirm {
	width: 150px;
	height: 32px;
	text-align: center;
	margin: 36px auto 0;
	background: url(../game/static/game/happy/btn-confirm.png) no-repeat;
	background-size: 100% 100%;
	color: #fff;
	line-height: 32px;
	font-size: 14px;
}

.pri_box_content {
	padding: 20px;
	display: flex;
	flex-direction: column;
}

.input-class {
	background: #efefef;
	height: 32px;
	border-radius: 3px;
	border: none;
	outline: none;
	padding-left: 5px;
	color: #f85e06;
	font-weight: 700;
	text-align: left;
}

.lucky_text {
	text-align: left;
	margin-bottom: 10px;
	font-size: 13px;
}

.lucky_tips {
	margin-top: 3px;
	text-align: center;
	font-size: 12px;
	color: #aaa5a5;
}

.close_wrap {
	position: absolute;
	top: 15px;
	right: 5px;
	line-height: 0;
}
.btn-view {
	margin: 20px;
	background: linear-gradient(0deg, #ae61fd, #777fff);
	color: #fff;
	border-radius: 20px;
}
</style>
