<template>
<view class="container">
	<block v-if="isload">
		<!-- 头部信息 -->
		<view class="header-container">
			<view class="user-info">
				<image class="avatar" :src="myInfo.headimg || '/static/image/default-avatar.png'" mode="aspectFill"></image>
				<view class="user-detail">
					<view class="username">{{myInfo.nickname || '用户'}}</view>
					<view class="points-info">
						<text class="points-label">当前分红点：</text>
						<text class="points-value">{{myInfo.current_points}}</text>
						<text class="points-unit">个</text>
					</view>
				</view>
			</view>
			<view class="ranking-info" v-if="rankingInfo.my_ranking > 0">
				<text class="ranking-text">{{rankingInfo.ranking_text}}</text>
			</view>
		</view>

		<!-- 统计卡片 -->
		<view class="stats-container">
			<view class="stat-card">
				<view class="stat-value">{{myInfo.stats.today_new}}</view>
				<view class="stat-label">今日新增</view>
			</view>
			<view class="stat-card">
				<view class="stat-value">{{myInfo.stats.month_new}}</view>
				<view class="stat-label">本月新增</view>
			</view>
			<view class="stat-card">
				<view class="stat-value">{{myInfo.stats.total_earned}}</view>
				<view class="stat-label">累计获得</view>
			</view>
			<view class="stat-card">
				<view class="stat-value">{{myInfo.stats.total_used}}</view>
				<view class="stat-label">累计使用</view>
			</view>
		</view>

		<!-- 功能说明 -->
		<view class="description-container">
			<view class="description-title">
				<text class="icon">📊</text>
				<text>分红点说明</text>
			</view>
			<view class="description-content">
				<text>{{myInfo.settings.description}}</text>
			</view>
			<view class="description-rules">
				<view class="rule-item">• 每{{myInfo.settings.reward_amount}}元小区业绩奖励{{myInfo.settings.reward_points}}个分红点</view>
				<view class="rule-item">• 业绩计算方式：{{myInfo.settings.calc_type_text}}</view>
				<view class="rule-item">• 触发时机：{{myInfo.settings.trigger_type_text}}</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-container">
			<view class="menu-item" @click="toPage('logs')">
				<view class="menu-icon">📝</view>
				<view class="menu-content">
					<view class="menu-title">变动记录</view>
					<view class="menu-desc">查看分红点增减记录</view>
				</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @click="toPage('rewards')">
				<view class="menu-icon">🎁</view>
				<view class="menu-content">
					<view class="menu-title">奖励记录</view>
					<view class="menu-desc">查看分红点奖励记录</view>
				</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @click="toPage('ranking')">
				<view class="menu-icon">🏆</view>
				<view class="menu-content">
					<view class="menu-title">排行榜</view>
					<view class="menu-desc">查看分红点排行榜</view>
				</view>
				<view class="menu-arrow">></view>
			</view>
		</view>

		<!-- 平台统计 -->
		<view class="platform-stats" v-if="platformStats">
			<view class="platform-title">平台统计</view>
			<view class="platform-data">
				<view class="platform-item">
					<text class="platform-label">总分红点数</text>
					<text class="platform-value">{{platformStats.total_points}}</text>
				</view>
				<view class="platform-item">
					<text class="platform-label">持有人数</text>
					<text class="platform-value">{{platformStats.holder_count}}</text>
				</view>
				<view class="platform-item">
					<text class="platform-label">今日新增</text>
					<text class="platform-value">{{platformStats.today_new}}</text>
				</view>
				<view class="platform-item">
					<text class="platform-label">本月新增</text>
					<text class="platform-value">{{platformStats.month_new}}</text>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			menuindex: -1,
			myInfo: {
				current_points: 0,
				nickname: '',
				headimg: '',
				settings: {
					description: '',
					reward_amount: 0,
					reward_points: 0,
					calc_type_text: '',
					trigger_type_text: ''
				},
				stats: {
					today_new: 0,
					month_new: 0,
					total_earned: 0,
					total_used: 0
				}
			},
			rankingInfo: {
				my_ranking: 0,
				ranking_text: ''
			},
			platformStats: null
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	
	onPullDownRefresh: function () {
		this.getdata();
	},

	methods: {
		getdata: function () {
			this.loaded();
			this.getMyInfo();
			this.getMyRanking();
			this.getPlatformStats();
		},

		// 获取我的分红点信息
		getMyInfo: function () {
			var that = this;
			that.loading = true;
			
			app.post('ApiFenhongdian/getMyFenhongdian', {}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 1) {
					that.myInfo = res.data;
				} else if (res.status == -1) {
					// 需要登录
					app.gotoLogin();
				} else {
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},

		// 获取我的排名
		getMyRanking: function () {
			var that = this;
			
			app.post('ApiFenhongdian/getMyRanking', {}, function (res) {
				if (res.status == 1) {
					that.rankingInfo = res.data;
				}
			});
		},

		// 获取平台统计
		getPlatformStats: function () {
			var that = this;
			
			app.post('ApiFenhongdian/getStats', {}, function (res) {
				if (res.status == 1) {
					that.platformStats = res.data;
				}
			});
		},

		// 页面跳转
		toPage: function (type) {
			var url = '';
			switch (type) {
				case 'logs':
					url = '/pagesExt/fenhongdian/logs';
					break;
				case 'rewards':
					url = '/pagesExt/fenhongdian/rewards';
					break;
				case 'ranking':
					url = '/pagesExt/fenhongdian/ranking';
					break;
			}
			if (url) {
				app.goto(url);
			}
		},

		loaded: function () {
			this.isload = true;
		},

		getmenuindex: function (e) {
			this.menuindex = e;
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}

/* 头部信息 */
.header-container{
	background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
	padding: 40rpx 30rpx 30rpx;
	margin-bottom: 20rpx;
	position: relative;
}
.user-info{
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.avatar{
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	border: 4rpx solid rgba(255,255,255,0.3);
	margin-right: 30rpx;
}
.user-detail{
	flex: 1;
}
.username{
	color: #fff;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}
.points-info{
	display: flex;
	align-items: baseline;
}
.points-label{
	color: rgba(255,255,255,0.8);
	font-size: 26rpx;
}
.points-value{
	color: #fff;
	font-size: 48rpx;
	font-weight: bold;
	margin: 0 8rpx;
}
.points-unit{
	color: rgba(255,255,255,0.8);
	font-size: 24rpx;
}
.ranking-info{
	text-align: center;
}
.ranking-text{
	color: #fff;
	font-size: 28rpx;
	background: rgba(255,255,255,0.2);
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
}

/* 统计卡片 */
.stats-container{
	display: flex;
	padding: 0 30rpx 30rpx;
	margin-bottom: 20rpx;
}
.stat-card{
	flex: 1;
	background: #fff;
	padding: 30rpx 20rpx;
	margin-right: 20rpx;
	border-radius: 16rpx;
	text-align: center;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}
.stat-card:last-child{
	margin-right: 0;
}
.stat-value{
	color: #FF6B35;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}
.stat-label{
	color: #666;
	font-size: 24rpx;
}

/* 功能说明 */
.description-container{
	background: #fff;
	margin: 0 30rpx 30rpx;
	padding: 30rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}
.description-title{
	display: flex;
	align-items: center;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}
.description-title .icon{
	font-size: 36rpx;
	margin-right: 12rpx;
}
.description-content{
	color: #666;
	font-size: 28rpx;
	line-height: 1.6;
	margin-bottom: 20rpx;
}
.description-rules{
	background: #f8f8f8;
	padding: 20rpx;
	border-radius: 12rpx;
}
.rule-item{
	color: #666;
	font-size: 26rpx;
	line-height: 1.6;
	margin-bottom: 8rpx;
}
.rule-item:last-child{
	margin-bottom: 0;
}

/* 功能菜单 */
.menu-container{
	background: #fff;
	margin: 0 30rpx 30rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}
.menu-item{
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f5f5f5;
}
.menu-item:last-child{
	border-bottom: none;
}
.menu-icon{
	font-size: 40rpx;
	margin-right: 24rpx;
}
.menu-content{
	flex: 1;
}
.menu-title{
	color: #333;
	font-size: 30rpx;
	font-weight: bold;
	margin-bottom: 6rpx;
}
.menu-desc{
	color: #999;
	font-size: 24rpx;
}
.menu-arrow{
	color: #ccc;
	font-size: 28rpx;
}

/* 平台统计 */
.platform-stats{
	background: #fff;
	margin: 0 30rpx 30rpx;
	padding: 30rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}
.platform-title{
	color: #333;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	text-align: center;
}
.platform-data{
	display: flex;
	flex-wrap: wrap;
}
.platform-item{
	width: 50%;
	display: flex;
	justify-content: space-between;
	padding: 12rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}
.platform-item:nth-child(odd){
	border-right: 1rpx solid #f5f5f5;
	padding-right: 20rpx;
	margin-right: 20rpx;
}
.platform-item:nth-child(n+3){
	border-bottom: none;
}
.platform-label{
	color: #666;
	font-size: 26rpx;
}
.platform-value{
	color: #FF6B35;
	font-size: 28rpx;
	font-weight: bold;
}
</style> 