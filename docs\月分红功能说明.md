# 月分红功能说明文档

## 功能概述

月分红功能是一个允许平台定期向符合条件的会员分配奖金的系统。该功能包括以下主要部分：

1. 分红活动列表页：展示所有分红活动，包括活动名称、奖金池金额、分红比例等信息
2. 分红活动详情页：展示单个分红活动的详细信息，包括分红规则、参与会员、分红记录等
3. 分红明细列表：展示分红记录的详细信息

## 页面结构

### 1. 活动列表页 (`pagesExa/yuefenhong/index.vue`)

- **顶部搜索和筛选区**
  - 活动标题搜索框
  - 时间范围选择器
  - 分红状态筛选下拉框
  
- **数据统计区**
  - 总活动数量
  - 总分红金额
  - 总参与人数
  
- **活动列表区**
  - 支持网格/列表视图切换
  - 活动卡片展示
    - 活动标题和状态
    - 分红比例
    - 奖金池金额
    - 参与人数
    - 创建时间
    - 操作按钮（查看详情、分享）

### 2. 活动详情页 (`pagesExa/yuefenhong/detail.vue`)

- **顶部活动信息**
  - 活动标题和状态
  - 分红比例、奖金池比例
  - 创建时间
  - 分红规则说明
  
- **中部数据展示**
  - 数据概览
    - 当前奖金池金额
    - 总分红金额
    - 待分红金额
    - 参与人数
  - 数据图表
    - 奖金池趋势图
    - 分红比例饼图
  
- **底部会员列表**
  - 分红明细表格
  - 分页控制
  - 导出数据功能
  
- **管理员功能**
  - 手动触发分红按钮
  - 分红确认弹窗

## API 接口

### 1. 获取分红活动列表

```
GET /apiyuefenhong/getList
```

### 2. 获取分红活动详情

```
GET /api/yuefenhong/getDetail
```

### 3. 获取分红明细列表

```
GET /api/yuefenhong/getDetailList
```

### 4. 获取参与人数（待实现）

```
GET /api/yuefenhong/getParticipantCount
```

### 5. 检查管理员权限（待实现）

```
GET /api/yuefenhong/checkAdminPermission
```

### 6. 触发分红操作（待实现）

```
POST /api/yuefenhong/triggerDividend
```

## 数据结构

### 活动列表项

```json
{
  "id": 1,
  "title": "月度分红活动",
  "fenhong_ratio": "10%",
  "pool_ratio": "5%",
  "level_names": "黄金会员,钻石会员",
  "yeji_level_names": "普通会员,黄金会员",
  "pool_amount": 5000.00,
  "total_amount": 100000.00,
  "createtime": "2024-01-20 12:00:00",
  "status": 1
}
```

### 分红明细项

```json
{
  "id": 1,
  "member_name": "张三",
  "dividend_amount": 100.00,
  "deducted_contribution": 100.00,
  "dividend_time": "2024-01-20 12:00:00",
  "remark": "月分红收益",
  "pool_amount": 5000.00
}
```

## 使用说明

1. 在首页或相关入口点击"月分红"进入活动列表页
2. 在活动列表页可以查看所有分红活动，并可以通过搜索和筛选找到特定活动
3. 点击活动卡片或"查看详情"按钮进入活动详情页
4. 在活动详情页可以查看活动的详细信息、数据统计和分红明细
5. 管理员可以在活动详情页点击"手动触发分红"按钮进行分红操作

## 注意事项

1. 分红操作不可撤销，请谨慎操作
2. 分红金额将根据设定的规则自动计算
3. 分红后，奖金池金额将被清零
4. 分红记录将永久保存，可随时查看

## 后续优化计划

1. 添加图表可视化展示
2. 完善导出数据功能
3. 增加自动分红定时任务
4. 增加分红通知功能
5. 优化移动端适配 