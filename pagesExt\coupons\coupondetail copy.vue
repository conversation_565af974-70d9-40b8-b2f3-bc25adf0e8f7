<template>
<view class="container">
	<block v-if="isload">
		<view class="couponbg" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"></view>
		<view class="orderinfo">
			
			<block v-if="coupon.id">
				<view class="item flex-col">
					<view class="t2">
						<view class="guize_txt">
							<parse :content="content" />
						</view>
					</view>	
				</view>
				
				<view class="topitem">
					<view class="f1" :style="{color:t('color1')}" v-if="coupon.type==0"><text class="t0">￥</text><text class="t1">{{coupon.money}}</text></view>
					<view class="f1" :style="{color:t('color1')}" v-if="coupon.type==1">商品券</view>
					<view class="f1" :style="{color:t('color1')}" v-if="coupon.type==2"><text class="t1">{{coupon.score}}</text><text class="t2">积分</text></view>
					<view class="f1" :style="{color:t('color1')}" v-if="coupon.type==3">礼品卡</view>
					<view class="f2">
						<view class="t1">{{coupon.name}}</view>
					</view>
				</view>
				<view class="item">
					<text class="t1">类型</text>
					<text class="t2" v-if="coupon.type==0">余额券<text v-if="coupon.isgive == 1 || coupon.isgive == 2">（可赠送）</text></text>
					<text class="t2" v-if="coupon.type==1">商品券<text v-if="coupon.isgive == 1 || coupon.isgive == 2 ">（可赠送）</text></text>
					<text class="t2" v-if="coupon.type==2">积分券<text v-if="coupon.isgive == 1 || coupon.isgive == 2">（可赠送）</text></text>
					<text class="t2" v-if="coupon.type==3">礼品卡<text v-if="coupon.isgive == 1 || coupon.isgive == 2">（可赠送）</text></text>
			    </view>
				
				<view class="item"  v-if="record.id && send==0 ">
					<text class="t1">兑换码</text>
					<text class="t2">{{record.code}}</text>
				</view>
				
				<view class="item"  v-if="record.id && record.qr && send==0 ">
					<text class="t1">二维码</text>
					<text class="t2"><image :src="record.qr" class="img"/></text>
				</view>
				
				
				<view class="item"  v-if="record.id && send==0 ">
					<text class="t1">领取时间</text>
					<text class="t2">{{record.createtime}}</text>
				</view>
				<block v-if="record.status==1">
				<view class="item">
					<text class="t1">使用时间</text>
					<text class="t2">{{record.usetime}}</text>
				</view>
				</block>
				
				<view class="item flex-col">
					<text class="t1">有效期</text>
					<text class="t2">{{coupon.starttimes}} 至 {{coupon.endtimes}}</text>
				</view>
				<view class="item flex-col" v-if="coupon.type==1 && coupon.prodatalist.length > 0">
					<text class="t1">包含</text>
					<view class="t2" v-for="(item, index) in coupon.prodatalist" :key="item.id" >{{item.name}} ￥{{item.sell_price}} X {{item.buynum}}</view>
				</view>
				<view class="item flex-col" v-if="coupon.type==3 && coupon.couponList.length > 0">
					<text class="t1">包含</text>
					<view class="t2" v-for="(item, index) in coupon.couponList" :key="item.id" >{{item.name}}</view>
				</view>
				
			</block>
		</view>
		
		<block v-if="mid == record.mid">
			<!-- 自用+转赠 -->
			<block v-if="coupon.isgive == 1 ||  coupon.isgive == 0 || (coupon.isgive==2 && record.from_mid) ">
				<block v-if="record.id &&  record.status==2">
					<view class="btn-add" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"  @tap.stop="goto" :data-url="'dh?cpid=' + record.hid+('&id='+record.id)">去使用</view>
				</block>
				<block v-else>
					<view  class="btn-add"  :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="coupon.id">已使用</view>
				</block>
			</block>
			<block v-if="record.id && record.status==2  && (coupon.isgive == 1 || coupon.isgive == 2)">
				<view class="btn-add" @tap="shareapp" v-if="getplatform() == 'app'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">转赠好友</view>
				<view class="btn-add" @tap="sharemp" v-else-if="getplatform() == 'mp'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">转赠好友</view>
				<view class="btn-add" @tap="sharemp" v-else-if="getplatform() == 'h5'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">转赠好友</view>
				<button class="btn-add" open-type="share" v-else :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">转赠好友</button>
			</block>
		</block>
		<block v-else>
			
			<view v-if="record.status==1" class="btn-add" style="background:#9d9d9d">已使用</view>
			<view v-else-if="record.status==2 && isrec==1" class="btn-add" style="background:#9d9d9d">已抢光</view>
			<view v-else class="btn-add" @tap="getcoupon" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id" :data-send="send" :data-isrec="isrec">立即领取</view>
		</block>
		
		<view class='text-center' @tap="goto" data-url='/pagesExt/coupons/couponlist' style="margin-top: 40rpx; line-height: 60rpx;"><text>返回</text></view>

	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
            isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,

			textset:{},
			record:{},
			coupon:{},
			shareTitle:'',
			sharePic:'',
			shareDesc:'',
			shareLink:'',
			mid:0,
			content: '',
			send: 0,
			isrec: 0
		}
  },
	
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});
		var query = (sharewxdata.path).split('?')[1]+'&seetype=circle';
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			
			app.get('ApiLipin2/coupondetail', {rid: that.opt.rid,id: that.opt.id,send: that.opt.send,isrec: that.opt.isrec}, function (res) {
				that.loading = false;
				that.textset = app.globalData.textset;
				uni.setNavigationBarTitle({
					title: that.t('礼品卡') + '详情'
				});
				if(!res.coupon.id) {
					app.alert(that.t('礼品卡')+'不存在');return;
				}
				
				that.mid = app.globalData.mid;
				that.send = res.send;
				that.isrec = res.isrec;
				that.record = res.record;
				that.coupon = res.coupon;
				
				
				that.shareTitle = that.coupon.shareTitle;
				that.shareDesc = that.coupon.shareDesc;
				if(that.coupon.sharePic){
					that.sharePic = that.coupon.sharePic;
				}else{
					that.sharePic = app.globalData.initdata.logo;
				}
				that.content = that.coupon.content;
				
				that.shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pagesExt/coupons/coupondetail?scene=id_'+that.coupon.id+'-pid_' + app.globalData.mid+'-rid_' + that.record.id+'-send_1';
				
				
				that.loaded({title:that.shareTitle,pic:that.sharePic,desc:that.shareDesc,link:that.shareLink});
			});
		},

		getcoupon:function(e){
			var that = this;
			var couponinfo = that.coupon;
			if (app.globalData.platform == 'wx' && couponinfo.rewardedvideoad && wx.createRewardedVideoAd) {
				app.showLoading();
				if(!app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad]){
					app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = wx.createRewardedVideoAd({ adUnitId: couponinfo.rewardedvideoad});
				}
				var rewardedVideoAd = app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad];
				rewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});
				rewardedVideoAd.onError((err) => {
					app.showLoading(false);
					app.alert(err.errMsg);
					console.log('onError event emit', err)
					rewardedVideoAd.offLoad()
					rewardedVideoAd.offClose();
				});
				rewardedVideoAd.onClose(res => {
					app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = null;
					if (res && res.isEnded) {
						//app.alert('播放结束 发放奖励');
						that.getcouponconfirm(e);
					} else {
						console.log('播放中途退出，不下发奖励');
					}
					rewardedVideoAd.offLoad()
					rewardedVideoAd.offClose();
				});
			}else{
				that.getcouponconfirm(e);
			}
		},
        getcouponconfirm: function (e) {
			var that = this;
			var datalist = that.datalist;
			var id = e.currentTarget.dataset.id;
			var send = e.currentTarget.dataset.send;
			var key = e.currentTarget.dataset.key;
			
				app.showLoading('领取中');
				app.post('ApiLipin2/getcoupon', {id: id,send:send}, function (data) {
					app.showLoading(false);
					if (data.status == 0) {
						app.error(data.msg);
					} else {
						app.success(data.msg);
						setTimeout(function(){
							app.goto('mycoupon');
						},1000)
					}
				});
			
    },
	
		receiveCoupon:function(e){
			var that = this;
			var couponinfo = that.coupon;
			if (app.globalData.platform == 'wx' && couponinfo.rewardedvideoad && wx.createRewardedVideoAd) {
				app.showLoading();
				if(!app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad]){
					app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = wx.createRewardedVideoAd({ adUnitId: couponinfo.rewardedvideoad});
				}
				var rewardedVideoAd = app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad];
				rewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});
				rewardedVideoAd.onError((err) => {
					app.showLoading(false);
					app.alert(err.errMsg);
					console.log('onError event emit', err)
					rewardedVideoAd.offLoad()
					rewardedVideoAd.offClose();
				});
				rewardedVideoAd.onClose(res => {
					app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = null;
					if (res && res.isEnded) {
						//app.alert('播放结束 发放奖励');
						that.receiveCouponConfirm(e);
					} else {
						console.log('播放中途退出，不下发奖励');
					}
					rewardedVideoAd.offLoad()
					rewardedVideoAd.offClose();
				});
			}else{
				that.receiveCouponConfirm(e);
			}
		}
,
		receiveCouponConfirm:function(e){
			var that = this;
			var datalist = that.datalist;
			var rid = that.record.id;
			var id = that.coupon.id;
			var send = that.send;
			app.showLoading('领取中');
			app.post('ApiLipin2/receiveLipin', {id: id,rid:rid,send:send}, function (data) {
				app.showLoading(false);
				if (data.status == 0) {
					app.error(data.msg);
				} else {
					app.success(data.msg);
					that.getdata();
				}
			});
		},
		
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			that.sharetypevisible = false;
			uni.showActionSheet({
		    itemList: ['发送给微信好友', '分享到微信朋友圈'],
		    success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.shareTitle;
						sharedata.summary = that.shareDesc;
						sharedata.href = that.shareLink;
						sharedata.imageUrl = that.sharePic;
						
						uni.share(sharedata);
					}
		    }
		  });
		},
  }
};
</script>
<style>
.container{display:flex;flex-direction:column; padding-bottom: 30rpx;}
.couponbg{width:100%;height:500rpx;}
.orderinfo{ width:94%;margin: -400rpx 3% 20rpx 3%;border-radius:8px;padding:14rpx 3%;background: #FFF;color:#333;}
.orderinfo .topitem{display:flex;padding:60rpx 40rpx;align-items:center;border-bottom:2px dashed #E5E5E5;position:relative}
.orderinfo .topitem .f1{font-size:50rpx;font-weight:bold;}
.orderinfo .topitem .f1 .t1{font-size:60rpx;}
.orderinfo .topitem .f1 .t2{font-size:40rpx;}
.orderinfo .topitem .f2{margin-left:40rpx}
.orderinfo .topitem .f2 .t1{font-size:36rpx;color:#2B2B2B;font-weight:bold;height:50rpx;line-height:50rpx}
.orderinfo .topitem .f2 .t2{font-size:24rpx;color:#999999;height:50rpx;line-height:50rpx}
.orderinfo .item{display:flex;flex-direction:column;width:100%;padding:0 40rpx;margin-top:40rpx}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;color:#2B2B2B;font-weight:bold;font-size:30rpx;height:60rpx;line-height:60rpx}
.orderinfo .item .t2{color:#2B2B2B;font-size:24rpx;height:auto;line-height:40rpx;white-space:pre-wrap;}
.orderinfo .item .red{color:red}

.text-center { text-align: center;}
.btn-add{width:90%;margin:30rpx 5%;height:96rpx; line-height:96rpx; text-align:center;color: #fff;font-size:30rpx;font-weight:bold;border-radius:48rpx;}
</style>