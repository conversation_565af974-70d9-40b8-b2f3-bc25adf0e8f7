<template>
    <view class="container">
        <view class="content">
            <view class="info-item">
                <view class="t1">售賣人：</view>
                <view class="t2">{{ selectedItem.nickname }}</view>
            </view>
            <view class="info-item" @tap="copyAddressText">
                <view class="t1">通道：</view>
                <view class="t2">{{ selectedItem.corridor }}</view>
            </view>
            <view class="info-item" @tap="copyAddressText">
                <view class="t1">收款地址：</view>
                <view class="t2">{{ selectedItem.wallet_address }}</view>
            </view>
            <view class="info-item" style="height:136rpx;line-height:136rpx" :data-url="selectedItem.wallet_receiving_code" @tap="previewImage" >
                <view class="t1" style="flex:1;">收款二維碼：</view>
                <image :src="selectedItem.wallet_receiving_code" style="width:88rpx;height:88rpx;"></image>
                <!-- <image class="t3" src="/static/img/search_ico.png"/> -->
            </view>
        </view>
        <view class="content">
            <view class="info-item">
                <view class="t1">創建時間：</view>
                <view class="t2">{{ selectedItem.createtime }}</view>
            </view>
            <view class="info-item">
                <view class="t1">買入數量：</view>
                <view class="t2">{{ selectedItem.commission }}</view>
            </view>
        </view>
        <view class="content">
          <view class="info-item">
            <h3 style="margin-top: 8rpx;">應付：{{ parseFloat(selectedItem.ratio * selectedItem.commission) }} 元</h3>
          </view>
            <view class="info-item" style="height: auto;">
                <view class="form-item4 flex-col" style="margin-top: 10px;">
                    <view class="label">
                        <h4>上傳支付憑證</h4>
                    </view>
                    <view id="content_picpreview" class="flex" style="flex-wrap:wrap;padding-top:20rpx">
                        <view v-for="(item, index) in content_pic" :key="index" class="layui-imgbox">
                            <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="content_pic">
                                <image src="/static/img/ico-del.png"></image>
                            </view>
                            <view class="layui-imgbox-img">
                                <image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image>
                            </view>
                        </view>
                        <view class="uploadbtn" :style="'background:url('+pre_url+'static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="content_pic" v-if="content_pic.length<2"></view>
                    </view>
                    <text class="info" style="color:#9e9e9e">請務必確認上傳支付憑證的有效性與真實性，支付金額必須與訂單應付金額一致。</text>
                </view>
            </view>
        </view>
        <view style="width:94%;margin: 2rpx 3%;">
          <button class="buy-button" @click="postVoucher(selectedItem)">提交</button>
        </view>

        <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
    </view>
</template>

<script>
var app = getApp();
export default {
    data() {
        return {
            opt: {},
            ratio: 1,
            // 
            selectedItem: [],
            id: '',
            pre_url:app.globalData.pre_url,
            content_pic: [],
			//
			address_text: '',
        }
    },
    onLoad: function(opt) {
        this.opt = app.getopts(opt);
        if (this.opt.id)
            this.id = opt.id
        this.getdata();
    },
    onPullDownRefresh: function() {
        this.getdata();
    },
    methods: {
        getdata: function(loadmore) {
            var that = this;
            that.loading = true;
            app.get('ApiShoumai/postVoucher', { id: that.id }, function(res) {
                that.loading = false;
                if(res.status == 0){
                  app.goback()
                }
                that.selectedItem = res.data
				that.address_text = res.data.wallet_address
            });
        },
        postVoucher(item){
          var that = this;
          var content_pic = that.content_pic;
          if(content_pic.length <= 0){
            app.alert('請上傳支付憑證');
            return;
          }

          app.showLoading('处理中');
          app.post('ApiShoumai/postVoucher', {id: item.id, content_pic: content_pic.join(',')}, function (data) {
            app.showLoading(false);
            if (data.status == 0) {
                app.error(data.msg);
            } else {
                app.success(data.msg);
                // that.content_pic = [];
                app.goto("/pages/shoumai/index")
            }
          });  
        },
        uploadimg:function(e){
          var that = this;
          var field= e.currentTarget.dataset.field
          var pics = that[field]
          if(!pics) pics = [];
          app.chooseImage(function(urls){
            for(var i=0;i<urls.length;i++){
              pics.push(urls[i]);
            }
            if(field == 'pic') that.pic = pics;
            if(field == 'pics') that.pics = pics;
            if(field == 'zhengming') that.zhengming = pics;
          },1)
        },
		copyAddressText(){
			// #ifdef H5
			this.$copyText(this.address_text).then(
				res => {
					uni.showToast({
						title: '复制成功',
					})
				}
			)
			// #endif
			// #ifndef H5
			uni.setClipboardData({
				data: this.address_text,
				success: () => {
					uni.showToast({
						title: '复制成功',
					})
				},
			})
			// #endif
		}
        
    },
};
</script>

<style scoped>

.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:80rpx;line-height:80rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:80rpx;line-height:80rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}

.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.layui-imgbox-close image{width:100%;height:100%}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}

.info {
  margin-bottom: 0.75rem;
  text-align: left;
  line-height: 1.5;
  color: #333;
}

.input-field {
  box-sizing: border-box; /* 确保padding不会增加输入框的总宽高 */
  width: 100%; /* 宽度为100%，根据父容器调整 */
  padding: 0px 15px; /* 适度的内边距，确保文本和边框之间有空间 */
  margin-top: 1rem; /* 与上方元素的间距 */
  margin-bottom: 1rem; /* 与上方元素的间距 */
  border: 1px solid #0587f0; /* 边框 */
  border-radius: 4px; /* 圆角边框 */
  font-size: 1.3rem; /* 文本大小 */
  line-height: 1.5; /* 行高 */
  color: #333; /* 文本颜色 */
}

.input-field::placeholder {
  color: #888; /* 占位符颜色 */
}

.input-field:focus {
  border-color: #405d85; /* 聚焦时的边框颜色 */
  outline: none; /* 移除聚焦时的默认轮廓线 */
}


.buy-button {
  width: 100%;
  padding: 0rem 0;
  margin-top: 1.5rem;
  background-color: #0587f0;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.125rem;
  transition: background-color 0.2s ease;
}

.buy-button:hover {
  background-color: #405d85;
}

.buy-button:active {
  transform: scale(0.98); /* 按钮点击时的小幅缩放 */
}


/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  border-radius: 4px;
  overflow: hidden;
  height: 100vh;
}

/* 分段控制器样式 */
.segment-control {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 50px;
  background-color: #f5f5f5;
}

</style>
