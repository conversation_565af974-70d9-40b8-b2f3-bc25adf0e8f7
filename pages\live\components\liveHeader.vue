<template>
	<view class="live-header">
		<view class="header-content">
			<!-- 主播信息 -->
			<view class="anchor-info">
				<image class="avatar" :src="anchorAvatar" mode="aspectFill"></image>
				<view class="info">
					<text class="nickname">{{anchorNickname}}</text>
					<view class="status-wrap">
						<text class="status" :class="{'offline': !data.online}">
							{{data.online ? '直播中' : '未开播'}}
						</text>
						<text class="fans" v-if="fansCount">{{fansCount}}粉丝</text>
					</view>
				</view>
			</view>
			
			<!-- 关注按钮 -->
			<view class="follow-btn" :class="{'followed': isFollowed}" @tap="handleFollow">
				<text>{{isFollowed ? '已关注' : '关注'}}</text>
			</view>
		</view>
		
		<!-- 分享按钮 -->
		<view class="share-btn" @tap="handleShare">
			<text class="iconfont icon-share"></text>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: () => ({
				anchor: {
					headimg: '',
					nickname: '主播昵称'
				},
				online: 0,
				fans_count: 0
			})
		}
	},
	
	data() {
		return {
			isFollowed: false,
			defaultAvatar: '/static/images/default-avatar.png'
		}
	},
	
	computed: {
		anchorAvatar() {
			return this.data.anchor?.headimg || this.defaultAvatar
		},
		
		anchorNickname() {
			return this.data.anchor?.nickname || '主播昵称'
		},
		
		fansCount() {
			return this.data.fans_count || 0
		}
	},
	
	methods: {
		handleFollow() {
			this.isFollowed = !this.isFollowed
			uni.showToast({
				title: this.isFollowed ? '已关注' : '已取消关注',
				icon: 'none'
			})
		},
		
		handleShare() {
			this.$emit('onShare')
		}
	}
}
</script>

<style lang="scss">
.live-header {
	position: relative;
	padding: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	
	.header-content {
		display: flex;
		align-items: center;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 100rpx;
		padding: 10rpx;
	}
	
	.anchor-info {
		display: flex;
		align-items: center;
		
		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			margin-right: 20rpx;
		}
		
		.info {
			.nickname {
				color: #fff;
				font-size: 28rpx;
				font-weight: 500;
			}
			
			.status-wrap {
				display: flex;
				align-items: center;
				margin-top: 4rpx;
				
				.status {
					color: #00FF00;
					font-size: 24rpx;
					margin-right: 20rpx;
					
					&.offline {
						color: #FF4656;
					}
				}
				
				.fans {
					color: rgba(255, 255, 255, 0.8);
					font-size: 24rpx;
				}
			}
		}
	}
	
	.follow-btn {
		background: linear-gradient(to right, #FF4656, #FF8C9A);
		border-radius: 100rpx;
		padding: 10rpx 30rpx;
		margin-left: 20rpx;
		
		&.followed {
			background: rgba(255, 255, 255, 0.2);
		}
		
		text {
			color: #fff;
			font-size: 24rpx;
		}
	}
	
	.share-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		.iconfont {
			color: #fff;
			font-size: 40rpx;
		}
	}
}
</style> 