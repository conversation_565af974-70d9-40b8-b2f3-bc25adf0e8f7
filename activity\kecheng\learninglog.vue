<template>
<view class="container">
	<block v-if="isload">
		<view class="user-profile">
			<view class="user-info">
				<view class="avatar-container">
					<image class="user-avatar" :src="userInfo.headimg || pre_url+'/static/img/default_avatar.png'" mode="aspectFill"></image>
				</view>
				<view class="user-text">
					<view class="user-name">{{ userInfo.nickname || '微信昵称' }}</view>
					<view class="user-welcome">欢迎回来，继续学习吧</view>
				</view>
			</view>
			<view class="page-title">学习中心</view>
		</view>
		
		<view class="function-buttons">
			<view class="function-button" @tap="navTo('/activity/kecheng/orderlist')">
				<image :src="pre_url+'/static/img/icon_bought.png'" mode="aspectFit"></image>
				<text>已购</text>
			</view>
			<view class="function-button" @tap="navTo('/pagesExa/my/favorite')">
				<image :src="pre_url+'/static/img/icon_collect.png'" mode="aspectFit"></image>
				<text>收藏</text>
			</view>
			<view class="function-button" @tap="navTo('/activity/kecheng/learningbiji')">
				<image :src="pre_url+'/static/img/icon_notes.png'" mode="aspectFit"></image>
				<text>笔记</text>
			</view>
			<view class="function-button" @tap="navTo('/pagesExb/training/index')">
				<image :src="pre_url+'/static/img/icon_training.png'" mode="aspectFit"></image>
				<text>训练营</text>
			</view>
		</view>
		
		<view class="section-title">
			<text>学习历史</text>
			<!-- <text class="section-subtitle">(备注：上下滑动时仅此段文字以下滑动，以上部分随动吸顶)</text> -->
		</view> 
		
		<!-- 添加筛选tabs -->
		<view class="filter-tabs">
			<view class="tab-item" :class="{'active': filterType === 'all'}" @tap="changeFilter('all')">
				<text>全部</text>
				<text class="count" v-if="counts.all > 0">({{ counts.all }})</text>
			</view>
			<view class="tab-item" :class="{'active': filterType === 'purchased'}" @tap="changeFilter('purchased')">
				<text>已购买</text>
				<text class="count" v-if="counts.purchased > 0">({{ counts.purchased }})</text>
			</view>
			<view class="tab-item" :class="{'active': filterType === 'unpurchased'}" @tap="changeFilter('unpurchased')">
				<text>未购买</text>
				<text class="count" v-if="counts.unpurchased > 0">({{ counts.unpurchased }})</text>
			</view>
		</view>
		
		<view class="learning-log-container">
			<block v-if="filteredDatalist && filteredDatalist.length > 0">
				<view class="log-item" v-for="(item, index) in filteredDatalist" :key="index" @tap.stop="gotoCourseOrDetail(item)">
					<!-- 丝带状态标识 -->
					<!-- <view class="ribbon-badge" v-if="item.is_purchased === 0">
						<view class="ribbon unpaid-ribbon">
							<text>未购买</text>
						</view>
					</view>
					<view class="ribbon-badge" v-else>
						<view class="ribbon paid-ribbon">
							<text>已购买</text>
						</view>
					</view> -->
					
					<view class="course-info">
						<view class="course-pic-container" :style="'background: conic-gradient(#1484fa 0deg '+ item.deg + 'deg'+ ',#eee '+item.deg+'deg '+item.degc+'deg);'">
							<image class="course-pic" :src="item.course_pic" mode="aspectFill"></image>
							
							
 							<!--<view class="progress-overlay">
								<text class="progress-text">{{ item.overall_progress_percent }}%</text>
							</view>
							<view class="progress-border" :style="{'--progress-percent': item.overall_progress_percent + '%'}">
								<view class="bottom-border"></view>
								<view class="left-border"></view>
							</view> -->
						</view>
						
						<text class="pl">{{ item.overall_progress_percent }}%</text>
						
						<view class="course-details">
							<view class="course-name-container">
								<view class="course-name">{{ item.course_name }}</view>
								<!-- 价格显示区域 -->
								<view class="price-display" v-if="item.is_purchased === 0">
									<!-- 免费课程 -->
									<view class="free-price" v-if="item.course_price == 0">
										<text>会员免费</text>
									</view>
									<!-- 付费课程 -->
									<template v-else>
										<view class="current-price">
											<text>￥{{ item.course_price }}</text>
										</view>
										<view class="member-tag" v-if="item.is_member_price">
											<text>会员价</text>
										</view>
									</template>
								</view>
							</view>
							<view class="course-stats">
								<text>总 {{ item.total_chapters }} 讲 | 已学 {{ item.completed_chapters }} 讲</text>
							</view>
						</view>
					</view>
					<view class="chapter-toggle" @tap.stop="toggleChapter(index)" :class="{'active': item.showChapter}">
						<text>{{ item.showChapter ? '收起章节' : '查看章节' }}</text>
						<text class="toggle-icon" :class="{'rotate': item.showChapter}">›</text>
					</view>
					<view class="target-chapter-info" v-if="item.showChapter && item.target_chapter_id">
						<view class="chapter-title">应学章节：{{ item.target_chapter_name }}</view>
						<view class="chapter-meta">
							<text v-if="item.target_chapter_type === 1">类型：图文</text>
							<text v-if="item.target_chapter_type === 2">类型：音频</text>
							<text v-if="item.target_chapter_type === 3">类型：视频</text>
							<text v-if="item.target_chapter_duration && (item.target_chapter_type === 2 || item.target_chapter_type === 3)"> | 时长：{{ formatDuration(item.target_chapter_duration) }}</text>
						</view>
						<view class="chapter-progress">
							<text>进度：</text>
							<block v-if="item.target_chapter_log_status === 1">
								<text class="status-completed">已学完</text>
							</block>
							<block v-else>
								<text v-if="item.target_chapter_type === 1">{{ item.target_chapter_log_jindu === '已学完' ? '已学完' : '未学习' }}</text>
								<text v-else>{{ item.target_chapter_log_jindu }}%</text>
								<text v-if="item.target_chapter_current_time > 0 && (item.target_chapter_type === 2 || item.target_chapter_type === 3)"> (已播：{{ formatDuration(item.target_chapter_current_time) }})</text>
							</block>
						</view>
					</view>
					<view class="target-chapter-info" v-else-if="item.showChapter">
						<view class="chapter-title">暂无学习章节</view>
					</view>
				</view>
			</block>
			
			<nodata :text="getNoDataText()" v-if="showNoData">
				<image slot="img" :src="pre_url+'/static/img/nodata.png'" mode="aspectFit"></image>
			</nodata>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      nomore: false,
      nodata: false,
      pagenum: 1,
      pernum: 10, //每页显示条目数
      datalist: [],
      userInfo: {},
      pre_url: app.globalData.pre_url,
      filterType: 'all',
      counts: {
        all: 0,
        purchased: 0,
        unpurchased: 0
      }
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
		this.getTabbarMenu();
    this.getLearningProgress(true);
    this.getUserInfo(); // 获取用户信息
  },
  onPullDownRefresh: function () {
    this.getLearningProgress(true);
  },
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getLearningProgress(false);
    }
  },
  computed: {
    filteredDatalist: function() {
      if (this.filterType === 'all') {
        return this.datalist;
      } else if (this.filterType === 'purchased') {
        return this.datalist.filter(item => item.is_purchased === 1);
      } else if (this.filterType === 'unpurchased') {
        return this.datalist.filter(item => item.is_purchased === 0);
      }
      return this.datalist;
    },
    showNoData: function() {
      return this.isload && (!this.filteredDatalist || this.filteredDatalist.length === 0);
    }
  },
  methods: {
		getTabbarMenu: function() {
			var currentRoute = '/' + this.__route__;
			var tarbar = app.globalData.tarbar;
			if (tarbar && tarbar.list) {
				for (var i = 0; i < tarbar.list.length; i++) {
					if (tarbar.list[i].pagePath == currentRoute) {
						this.menuindex = i;
						// uni.setNavigationBarTitle({
						// 	title: tarbar.list[i].text
						// });
					}
				}
			}
			// 自定义导航栏 隐藏原生tabbar需要设置，否则点击返回会有问题
			if(this.opt && this.opt.hidettabbar == 1){
				uni.hideTabBar();
			}
		},
    getLearningProgress: function (isRefresh) {
      var that = this;

      if (isRefresh) {
        that.pagenum = 1;
        that.datalist = [];
      }

      that.loading = true;
      that.nodata = false;
      that.nomore = false;
      
      app.post('ApiKecheng/getAllLearningProgress', {
        pagenum: that.pagenum,
        pernum: that.pernum
      }, function (res) {
        that.loading = false;
        that.isload = true;
        uni.stopPullDownRefresh();

        if (res.status == 1) {
          var data = res.data;
          for(var i = 0; i < data.length; i++) {
            data[i].showChapter = false;
            if(data[i].is_purchased === 0) {
              data[i].needPurchase = true;
            }
          }
		  
		  data.filter(item=>{
			   item.deg = parseInt(item.overall_progress_percent/100*360)
			   item.degc = 360 - item.deg
		  })
		  
          if (that.pagenum == 1) {
            that.datalist = data;
            if (data.length == 0) {
              that.nodata = true;
            }
            
            // 计算各类数量
            that.calculateCounts();
          } else {
            that.datalist = that.datalist.concat(data);
            // 重新计算数量
            that.calculateCounts();
          }

          if (data.length < that.pernum) {
            that.nomore = true;
          }
        } else if (res.status == -1) {
					app.showModal(res.msg, function () {
						app.goto('/pages/login/login');
					});
				} else {
          app.alert(res.msg);
        }
      });
    },
		gotoCourseOrDetail: function(item) {
			// 如果有当前学习章节，直接跳转到该章节
			if (item.target_chapter_id) {
				app.goto('/activity/kecheng/mldetail?kcid=' + item.kcid + '&id=' + item.target_chapter_id);
			} else if (item.is_purchased === 0) {
				// 未购买且无学习章节，跳转到课程详情页
				app.goto('/activity/kecheng/detail?id=' + item.kcid);
			} else {
				// 已购买但无章节，跳转到课程详情页
				app.goto('/activity/kecheng/detail?id=' + item.kcid);
			}
		},
    formatDuration: function(seconds) {
      if (!seconds || seconds <= 0) return '00:00';
      seconds = Math.floor(seconds);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      const formattedMinutes = String(minutes).padStart(2, '0');
      const formattedSeconds = String(remainingSeconds).padStart(2, '0');
      return `${formattedMinutes}:${formattedSeconds}`;
    },
		getmenuindex: function getmenuindex(e) {
			this.menuindex = e;
		},
    toggleChapter: function(index) {
      this.datalist[index].showChapter = !this.datalist[index].showChapter;
    },
    navTo: function(url) {
      app.goto(url);
    },
    goto: function(e) {
      const url = e.currentTarget.dataset.url;
      if (url) {
        app.goto('/activity/kecheng/' + url);
      }
    },
    getUserInfo: function() {
      var that = this;
      app.get('ApiMy/getCurrentUserInfo', {}, function(res) {
        if(res.status == 1) {
          that.userInfo = res.data;
        }
      });
    },
    getProgressBgStyle: function(percent) {
      const hue = ((1 - percent / 100) * 120).toString(10);
      return `hsl(${hue}, 100%, 50%)`;
    },
    changeFilter: function(type) {
      this.filterType = type;
      this.getLearningProgress(true);
    },
    calculateCounts: function() {
      var that = this;
      that.counts.all = that.datalist.length;
      that.counts.purchased = that.datalist.filter(item => item.is_purchased === 1).length;
      that.counts.unpurchased = that.datalist.filter(item => item.is_purchased === 0).length;
    },
    getNoDataText: function() {
      if (this.filterType === 'all') {
        return '暂无学习记录';
      } else if (this.filterType === 'purchased') {
        return '暂无已购买的学习记录';
      } else if (this.filterType === 'unpurchased') {
        return '暂无未购买的学习记录';
      }
      return '暂无学习记录';
    }
  }
};
</script>
<style>
.container {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  background-color: #f7f9fd;
  min-height: 100vh;
}

/* 用户信息区：增强视觉层次感 */
.user-profile {
  padding: 40rpx 30rpx 30rpx 30rpx;
  background: linear-gradient(120deg, #f0f8ff 0%, #ffffff 100%);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(16,174,255,0.1);
  position: relative;
  z-index: 3;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.user-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.avatar-container {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  position: relative;
  box-shadow: 0 6rpx 16rpx rgba(16,174,255,0.15);
}

.avatar-container::after {
  content: '';
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  right: -3rpx;
  bottom: -3rpx;
  border-radius: 50%;
  border: 3rpx solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(to right, #10aeff, #1484fa) border-box;
  z-index: -1;
}

.user-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.user-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  font-size: 34rpx;
  font-weight: 700;
  color: #222;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
}

.user-welcome {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
  margin-top: 4rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #10aeff;
  background: linear-gradient(90deg, #10aeff, #1484fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(16,174,255,0.07);
}

/* 功能按钮：添加渐变色彩和发光描边 */
.function-buttons {
  display: flex;
  justify-content: space-between;
  padding: 36rpx 30rpx 26rpx 30rpx;
  background: #fff;
  border-radius: 0 0 36rpx 36rpx;
  box-shadow: 0 10rpx 30rpx rgba(16,174,255,0.08);
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}

.function-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 22%;
  padding: 0;
  background: none;
  transition: transform 0.2s ease;
}

.function-button:active {
  transform: translateY(4rpx);
}

.function-button:nth-child(1) {
  position: relative;
}

.function-button:nth-child(1)::after {
  content: '';
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  border-radius: 50%;
  background: radial-gradient(circle at center, rgba(16,174,255,0.15) 0%, rgba(16,174,255,0) 70%);
  z-index: -1;
  opacity: 0.8;
}

.function-button image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
}

.function-button text {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  letter-spacing: 1rpx;
}

/* 区域标题：增强对比度 */
.section-title {
  padding: 32rpx 30rpx 10rpx 30rpx;
  display: flex;
  align-items: center;
  background: transparent;
  margin-bottom: 6rpx;
  position: relative;
}

.section-title text:first-child {
  font-size: 32rpx;
  font-weight: 700;
  color: #222;
  position: relative;
  padding-left: 18rpx;
}

.section-title text:first-child::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #10aeff, #1484fa);
  border-radius: 4rpx;
}

.section-subtitle {
  font-size: 20rpx;
  color: #aaa;
  font-weight: normal;
  margin-left: 18rpx;
}

.learning-log-container {
  padding: 0 16rpx 30rpx;
}

/* 课程卡片：优化层次感和交互体验 */
.log-item {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  padding: 22rpx 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(16,174,255,0.08), 0 2rpx 4rpx rgba(0,0,0,0.03);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(16,174,255,0.1);
}

.log-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(16,174,255,0.05);
}

/* 丝带状态标识 */
.ribbon-badge {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}

.ribbon {
  position: relative;
  width: 90rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  font-weight: 700;
  letter-spacing: 0.5rpx;
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.3);
  clip-path: polygon(0 0, 100% 100%, 100% 0);
}

.ribbon text {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  transform: rotate(45deg);
  transform-origin: center;
  white-space: nowrap;
  font-size: 16rpx;
}

.unpaid-ribbon {
  background: #ff6b6b;
}

.paid-ribbon {
  background: #57DD43;
}

/* 课程信息：调整信息层级 */
.course-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.course-pic-container {
  position: relative;
  width: 260rpx;
  height: 150rpx;
  margin-right: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 3px;
}

.course-pic {
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  border-radius: 16rpx;
}

.progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 16rpx;
}

.progress-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.7);
}

.progress-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  pointer-events: none;
}

/* 上边进度条 */
.progress-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: calc(var(--progress-percent, 0%));
  max-width: 100%;
  height: 10rpx;
  background: linear-gradient(to right, #1484fa, #10aeff);
  box-shadow: 0 0 15rpx rgba(16, 174, 255, 0.8);
  border-radius: 4rpx;
}

/* 右边进度条 */
.progress-border::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: calc(var(--progress-percent, 0%));
  max-height: 100%;
  width: 10rpx;
  background: linear-gradient(to bottom, #10aeff, #1484fa);
  box-shadow: 0 0 15rpx rgba(16, 174, 255, 0.8);
  border-radius: 4rpx;
}

/* 下边进度条 */
.progress-border .bottom-border {
  position: absolute;
  bottom: 0;
  right: 0;
  width: calc(var(--progress-percent, 0%));
  max-width: 100%;
  height: 10rpx;
  background: linear-gradient(to left, #1484fa, #10aeff);
  box-shadow: 0 0 15rpx rgba(16, 174, 255, 0.8);
  border-radius: 4rpx;
}

/* 左边进度条 */
.progress-border .left-border {
  position: absolute;
  bottom: 0;
  left: 0;
  height: calc(var(--progress-percent, 0%));
  max-height: 100%;
  width: 10rpx;
  background: linear-gradient(to top, #10aeff, #1484fa);
  box-shadow: 0 0 15rpx rgba(16, 174, 255, 0.8);
  border-radius: 4rpx;
}

.course-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 150rpx;
  min-width: 0; /* 确保flex子项能够正确缩小 */
}

.course-name-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.course-name {
  font-size: 30rpx;
  font-weight: 700;
  color: #222;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  margin-bottom: 10rpx;
  letter-spacing: 0.5rpx;
  flex: 1;
  margin-right: 16rpx;
}

/* 价格显示区域 */
.price-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  min-width: 120rpx;
}

.current-price {
  font-size: 28rpx;
  font-weight: 700;
  color: #ff6b6b;
  margin-bottom: 4rpx;
}

.current-price text {
  background: linear-gradient(to right, #ff6b6b, #ff3b3b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.free-price {
  font-size: 28rpx;
  font-weight: 700;
  margin-bottom: 4rpx;
}

.free-price text {
  background: linear-gradient(to right, #07c160, #05a64a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
  margin-bottom: 4rpx;
}

.member-tag {
  background: linear-gradient(135deg, rgba(16,174,255,0.1) 0%, rgba(20,132,250,0.1) 100%);
  border: 1rpx solid rgba(16,174,255,0.2);
  border-radius: 10rpx;
  padding: 2rpx 8rpx;
  font-size: 18rpx;
  font-weight: 600;
}

.member-tag text {
  background: linear-gradient(to right, #10aeff, #1484fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.course-stats {
  font-size: 22rpx;
  color: #666;
  display: flex;
  align-items: center;
  margin-top: auto;
}

/* 章节展开/折叠：增强交互引导 */
.chapter-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0 4rpx 0;
  font-size: 26rpx;
  position: relative;
  border-top: 1rpx dashed rgba(16,174,255,0.2);
  margin-top: 8rpx;
  transition: all 0.2s ease;
}

.chapter-toggle text:first-child {
  font-weight: 600;
  background: linear-gradient(90deg, #10aeff, #1484fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.chapter-toggle.active {
  border-bottom: none;
}

.toggle-icon {
  margin-left: 10rpx;
  font-size: 30rpx;
  font-weight: bold;
  transform: rotate(90deg);
  display: inline-block;
  transition: transform 0.3s ease;
  color: #10aeff;
}

.toggle-icon.rotate {
  transform: rotate(-90deg);
}

/* 章节信息：优化信息层级 */
.target-chapter-info {
  margin-top: 0;
  padding: 20rpx 0 4rpx 0;
  border-top: 1rpx dashed rgba(16,174,255,0.2);
  position: relative;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chapter-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 600;
  position: relative;
  padding-left: 18rpx;
}

.chapter-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 20rpx;
  background: linear-gradient(to bottom, #10aeff, #1484fa);
  border-radius: 4rpx;
}

.chapter-meta {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.chapter-meta text {
  margin-right: 14rpx;
  margin-bottom: 8rpx;
  background: linear-gradient(to right, #f3f8ff, #eaf6ff);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  line-height: 1.5;
  border: 1rpx solid rgba(16,174,255,0.1);
}

.chapter-progress {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.chapter-progress text:first-child {
  color: #888;
  margin-right: 8rpx;
}

.chapter-progress .status-completed {
  color: #07c160;
  font-weight: 700;
  position: relative;
  padding-left: 8rpx;
  text-shadow: 0 1rpx 0 rgba(0,0,0,0.05);
}

.chapter-progress .status-completed::before {
  content: '✓';
  margin-right: 4rpx;
  font-size: 24rpx;
}

/* 高亮进度百分比显示 */
.chapter-progress text:nth-child(2):not(.status-completed) {
  color: #10aeff;
  font-weight: 700;
  text-shadow: 0 1rpx 0 rgba(0,0,0,0.05);
}

/* 已播放时间显示 */
.chapter-progress text:nth-child(3) {
  margin-left: 8rpx;
  margin-top: 6rpx;
  background: linear-gradient(to right, #f1f7ff, #e6f3ff);
  border-radius: 16rpx;
  padding: 2rpx 14rpx;
  font-size: 22rpx;
  color: #1484fa;
  border: 1rpx solid rgba(16,174,255,0.15);
  box-shadow: 0 2rpx 4rpx rgba(16,174,255,0.1);
}

/* 空数据和加载更多提示 */
.nodata {
  width: 100%;
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.nodata image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
  filter: drop-shadow(0 4rpx 8rpx rgba(0,0,0,0.1));
}

.nomore {
  width: 100%;
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
  position: relative;
}

.nomore::before, .nomore::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 20%;
  height: 1rpx;
  background: linear-gradient(to right, rgba(229,229,229,0), rgba(229,229,229,1), rgba(229,229,229,0));
}

.nomore::before {
  left: 15%;
}

.nomore::after {
  right: 15%;
}

.pl{
position: absolute;
    top: 0px;
    left: 17%;
    olor: #000;
    font-weight: bold;
    z-index: 10;
    background: #ffffff;
    padding: 0 5px;
    font-size: 10px;
}

/* 添加筛选tabs样式 */
.filter-tabs {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: #fff;
  border-radius: 20rpx;
  margin: 0 16rpx 20rpx 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(16,174,255,0.08);
  border: 1rpx solid rgba(16,174,255,0.1);
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  position: relative;
  transition: all 0.3s ease;
  flex: 1;
  margin: 0 4rpx;
}

.tab-item.active {
  background: linear-gradient(135deg, rgba(16,174,255,0.1) 0%, rgba(20,132,250,0.1) 100%);
  border: 1rpx solid rgba(16,174,255,0.2);
  box-shadow: 0 2rpx 12rpx rgba(16,174,255,0.15);
}

.tab-item.active::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 4rpx;
  background: linear-gradient(to right, #10aeff, #1484fa);
  border-radius: 2rpx;
}

.tab-item text:first-child {
  font-size: 26rpx;
  font-weight: 600;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item.active text:first-child {
  background: linear-gradient(to right, #10aeff, #1484fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.tab-item .count {
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
  padding: 2rpx 8rpx;
  background: rgba(0,0,0,0.05);
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.tab-item.active .count {
  background: linear-gradient(135deg, rgba(16,174,255,0.15) 0%, rgba(20,132,250,0.15) 100%);
  color: #1484fa;
  font-weight: 600;
}

</style> 