# 文件管理功能说明

## 功能描述

文件管理功能模块提供了文件的上传、下载、查看、分类等功能，便于用户进行文件的存储和分享。

## 页面说明

### 1. 文件列表页（pagesExa/filem/filemlist.vue）

- 显示文件列表，包括名称、描述、大小、上传时间等信息
- 提供分类筛选功能
- 提供搜索功能
- 支持下拉刷新和上拉加载更多
- 点击文件项可跳转至文件详情页

### 2. 文件详情页（pagesExa/filem/detail.vue）

- 显示文件的详细信息，包括名称、描述、大小、格式、上传者、查看次数、下载次数等
- 提供文件预览功能（支持图片、PDF类型）
- 提供文件下载功能
- 支持文件分享

## 文件预览功能

本模块支持以下类型文件的预览：

1. **图片预览**：支持jpg、jpeg、png、gif、bmp、webp等常见图片格式
2. **PDF预览**：通过PDF.js实现，支持PDF文件在线浏览、翻页等功能
3. **文本预览**：支持txt、log、md、json、xml、html、css、js等文本文件（需服务端支持）

### PDF预览功能详细说明

系统现已全面升级PDF文件的在线预览功能，具体实现如下：

1. **文件详情页预览**：
   - 在文件详情页面，系统会自动识别PDF文件
   - 提供简化版PDF预览，显示PDF图标和文件基本信息
   - 提供"预览"按钮，点击后跳转到专门的PDF预览页面

2. **专业PDF预览页面**：
   - 使用PDF.js实现完整的PDF预览功能
   - 支持页面缩放、页面跳转、文档搜索、全屏查看等高级功能
   - 适配H5环境，提供接近原生的PDF阅读体验

3. **技术实现**：
   - 前端使用PDF.js库实现PDF渲染
   - 通过web-view组件嵌入PDF.js查看器
   - 支持跨域PDF文件加载
   - 文件路径自动识别，即使接口未返回文件类型也能正确识别PDF文件

4. **平台兼容性**：
   - H5环境：完整支持所有PDF预览功能
   - 小程序：提供简化版预览，点击预览按钮后跳转到H5页面查看完整PDF
   - APP：支持完整PDF预览功能

### 使用说明
1. 在文件列表中点击PDF文件，进入文件详情页
2. 在文件详情页点击"预览"按钮，进入PDF预览页面
3. 在PDF预览页面可以使用PDF.js提供的各种功能查看PDF内容：
   - 使用工具栏上的"+"和"-"按钮或滚轮进行缩放
   - 使用页码输入框或翻页按钮进行页面跳转
   - 使用搜索框搜索PDF内容
   - 使用全屏按钮进入全屏模式
4. 如需下载PDF文件，可以返回文件详情页点击"下载"按钮

### 注意事项
- PDF预览功能需要网络连接
- 大型PDF文件可能需要较长加载时间
- 跨域PDF文件需要服务器支持CORS
- 文件URL必须是有效的PDF文件链接

## 接口说明

本功能使用以下接口：

### 1. 获取文件列表

- 接口地址：`ApiFilem/getfilemlist`
- 请求方式：GET
- 主要参数：
  - pagenum：页码
  - cid：分类ID
  - keyword：搜索关键词
  - filetype：文件类型
  - showprivate：是否显示私有文件

### 2. 获取文件详情

- 接口地址：`ApiFilem/detail`
- 请求方式：GET
- 主要参数：
  - id：文件ID

### 3. 下载文件

- 接口地址：`ApiFilem/download`
- 请求方式：GET
- 主要参数：
  - id：文件ID

### 4. 获取文件分类

- 接口地址：`ApiFilem/getcategory`
- 请求方式：GET
- 主要参数：
  - bid：商户ID (可选)

## 技术实现说明

1. **PDF预览功能**：使用PDF.js实现，将其放在分包中以减少主包体积
   - PDF.js核心文件：`static/pdfjs/build/pdf.min.js`
   - PDF.js工作线程：`static/pdfjs/build/pdf.worker.min.js`
   - PDF.js查看器：`static/pdfjs/web/viewer.html`
   - PDF预览页面：`pagesExa/filem/pdf-viewer-page.vue`

2. **文件类型图标**：存放在`static/img/filetypes/`目录下，根据文件类型自动显示对应图标
   - 系统会根据文件扩展名自动识别文件类型
   - 如果接口未返回文件类型，系统会从文件路径中提取扩展名

## 注意事项

1. PDF预览功能在不同平台上表现可能有差异：
   - 小程序环境：通过Canvas渲染PDF
   - H5环境：直接使用PDF.js渲染
   - APP环境：使用自定义Canvas渲染
2. 文件下载功能在不同环境（小程序、H5、APP）有不同的实现方式
3. 私有文件需要登录后才能查看和下载

## 后续优化方向

1. 增加更多文件类型的在线预览功能（如Word、Excel、PPT等）
2. 优化PDF预览性能，支持移动端手势操作（如双指缩放、滑动翻页等）
3. 增加文件上传功能
4. 增加文件收藏和分享功能
5. 增加文件权限管理功能
6. 增加PDF注释和标记功能
7. 支持离线查看已缓存的PDF文件
8. 增加文件分类和搜索功能
9. 优化文件列表加载性能
10. 增加文件分享和协作功能 