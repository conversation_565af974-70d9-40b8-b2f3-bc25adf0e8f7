<template>
	<view class="waterfalls-box" :style="{ height: height + 'px' }">
		<view v-for="(item, index) of list" class="waterfalls-list" :key="getItemKey(item)"
			:id="'waterfalls-list-id-' + getItemId(item)" :ref="'waterfalls-list-id-' + getItemId(item)" :style="{
        '--offset': offset + 'px',
        '--cols': cols,
				'background': probgcolor,
        top: allPositionArr[index] ? allPositionArr[index].top : 0,
        left: allPositionArr[index] ? allPositionArr[index].left : 0,
      }" @click="gotoDetail(item)">
			
			<!-- 加载中遮罩层 -->
			<view class="loading-mask" v-if="!loadedItems[index]">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			
			<!-- 闪光效果 -->
			<view class="shine-effect" v-if="shiningItems[index]"></view>
			
			<!-- 商品内容 -->
			<template v-if="!item.content_type">
				<image class="waterfalls-list-image" mode="widthFix" :style="imageStyle" :src="item.pic || ' '"
					@load="imageLoadHandle(index)" @error="imageLoadHandle(index)" />
				<image class="saleimg" :src="saleimg" v-if="saleimg!=''" mode="widthFix" />
				<view class="product-info">
					<view class="p1" v-if="showname == 1">{{item.name}}</view>
					<view class="binfo flex-y-center" v-if="showbname && item.binfo">
						<image :src="item.binfo.logo" class="t1"></image><text class="t2">{{item.binfo.name}}</text>
					</view>
					<view class="p2">
						<view class="p2-1" v-if="showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)">
							<text class="t1" style="color:#f5222d"><text
									style="font-size:24rpx">￥</text>{{item.sell_price}}</text>
							<text class="t2"
								v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
							<text class="t3" v-if="item.juli" style="color:#888;">{{item.juli}}</text>
						</view>
						<view class="p2-1" v-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0"
							style="height: 50rpx;line-height: 44rpx;">
							<text class="t1" style="color:#f5222d">询价</text>
							<block v-if="item.xunjia_text && item.price_type == 1">
								<view class="lianxi" style="background:#f5222d" @tap.stop="showLinkChange"
									:data-lx_name="item.lx_name" :data-lx_bid="item.lx_bid"
									:data-lx_bname="item.lx_bname" :data-lx_tel="item.lx_tel" data-btntype="2">
									{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>
							</block>
						</view>
					</view>
					<view class="p1" v-if="item.merchant_name"
						style="color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal;">
						<text>{{item.merchant_name}}</text></view>
					<view class="p1" v-if="item.main_business"
						style="color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;">
						<text>{{item.main_business}}</text></view>
					<view class="p3" v-if="(showsales=='1' && item.sales>0) || showstock=='1'">
						<text v-if="showsales=='1' && item.sales>0">已售{{item.sales}}</text>
						<text v-if="(showsales=='1' && item.sales>0) && showstock=='1'"
							style="padding:0 4px;font-size:22rpx">|</text>
						<text v-if="showstock=='1'">仅剩{{item.stock}}</text>
					</view>
					<view v-if="(showsales !='1' ||  item.sales<=0) && item.main_business" style="height: 44rpx;">
					</view>

					<view class="p4" style="background:rgba(245, 34, 45, 0.1);color:#f5222d"
						v-if="showcart==1 && !item.price_type" @click.stop="buydialogChange"
						:data-proid="item[idfield]"><text class="iconfont icon_gouwuche"></text></view>
					<view class="p4" style="background:rgba(245, 34, 45, 0.1);color:#f5222d"
						v-if="showcart==2 && !item.price_type" @click.stop="buydialogChange"
						:data-proid="item[idfield]">
						<image :src="cartimg" class="img" />
					</view>
				</view>
				<view v-if="showcoupon==1 && item.couponlist && (item.couponlist).length>0" class="couponitem">
					<view class="f1">
						<view v-for="(coupon, index2) in item.couponlist" :key="index2" class="t"
							style="background:rgba(245, 34, 45, 0.1);color:#f5222d">
							<text v-if="coupon.minprice > 0">满{{coupon.minprice}}减{{coupon.money}}</text>
							<text v-if="coupon.minprice == 0">{{coupon.money}}元无门槛</text>
						</view>
					</view>
				</view>
			</template>
			
			<!-- 文章内容 -->
			<template v-else-if="item.content_type === 'article'">
				<view class="article-container">
					<image class="article-image" mode="widthFix" :src="item.cover_img || item.coverimg || ' '"
						@load="imageLoadHandle(index)" @error="imageLoadHandle(index)" />
					<view class="article-tag">文章</view>
					<view class="article-info">
						<view class="article-title">{{item.title}}</view>
						<view class="article-desc" v-if="item.summary">{{item.summary}}</view>
						<view class="article-meta">
							<text class="article-date">{{formatDate(item.publish_time || item.created_at)}}</text>
							<text class="article-views">
								<text class="iconfont icon_chakan"></text>
								{{item.view_num || 0}}
							</text>
						</view>
					</view>
				</view>
			</template>
			
			<!-- 视频内容 -->
			<template v-else-if="item.content_type === 'video'">
				<view class="video-container">
					<view class="video-image-wrap">
						<image class="video-image" mode="widthFix" :src="item.coverimg || ' '"
							@load="imageLoadHandle(index)" @error="imageLoadHandle(index)" />
						<view class="video-play-icon">
							<text class="iconfont icon_bofang"></text>
						</view>
					</view>
					<view class="video-tag">视频</view>
					<view class="video-info">
						<view class="video-title">{{item.title || '精彩短视频'}}</view>
						<view class="video-meta">
							<view class="video-user" v-if="item.username">
								<image class="video-user-avatar" :src="item.logo || '/static/img/default-avatar.png'"></image>
								<text class="video-user-name">{{item.username}}</text>
							</view>
							<view class="video-counts">
								<text class="video-view-count">
									<text class="iconfont icon_bofang1"></text>
									{{item.view_num || 0}}
								</text>
								<text class="video-like-count">
									<text class="iconfont icon_dianzan"></text>
									{{item.zan_num || 0}}
								</text>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>
		
		<!-- 弹窗组件 -->
		<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange"
			:menuindex="menuindex"></buydialog>
		<view class="posterDialog linkDialog" v-if="showLinkStatus">
			<view class="main">
				<view class="close" @tap="showLinkChange">
					<image class="img" src="/static/img/close.png" />
				</view>
				<view class="content">
					<view class="title">{{lx_name}}</view>
					<view class="row" v-if="lx_bid > 0">
						<view class="f1">店铺名称</view>
						<view class="f2" @tap="goto" :data-url="'/pagesExt/business/index?id='+lx_bid">{{lx_bname}}
							<image src="/static/img/arrowright.png" class="image" />
						</view>
					</view>
					<view class="row" v-if="lx_tel">
						<view class="f1">联系电话</view>
						<view class="f2" @tap="goto" :data-url="'tel::'+lx_tel" style="color:#f5222d">{{lx_tel}}
							<image src="/static/img/copy.png" class="copyicon" @tap.stop="copy" :data-text="lx_tel">
							</image>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				required: true
			},
			// offset 间距，单位为 px
			offset: {
				type: Number,
				default: 8
			},
			// 列表渲染的 key 的键名，值必须唯一，默认为 id
			idfield: {
				type: String,
				default: "id"
			},
			// 图片 src 的键名
			imageSrcKey: {
				type: String,
				default: "pic"
			},
			// 列数
			cols: {
				type: Number,
				default: 2,
				validator: (num) => num >= 2
			},
			imageStyle: {
				type: Object
			},
			showstyle: {
				default: 2
			},
			menuindex: {
				default: -1
			},
			saleimg: {
				default: ''
			},
			showname: {
				default: 1
			},
			namecolor: {
				default: '#333'
			},
			showprice: {
				default: '1'
			},
			showsales: {
				default: '1'
			},
			showcart: {
				default: '1'
			},
			cartimg: {
				default: '/static/imgsrc/cart.svg'
			},
			showstock: {
				default: '0'
			},
			showbname: {
				default: '0'
			},
			showcoupon: {
				default: '0'
			},
			probgcolor:{default:'#fff'}
		},
		data() {
			return {
				topArr: [], // left, right 多个时依次表示第几列的数据
				allPositionArr: [], // 保存所有的位置信息
				allHeightArr: [], // 保存所有的 height 信息
				height: 0, // 外层包裹高度
				oldNum: 0,
				num: 0,
				buydialogShow: false,
				proid: 0,
				showLinkStatus: false,
				lx_name: '',
				lx_bid: '',
				lx_bname: '',
				lx_tel: '',
				forceRefresh: false, // 用于强制刷新布局的标志
				loadedItems: {}, // 用于跟踪每个商品是否已加载完成
				shiningItems: {}, // 用于跟踪每个商品是否正在显示闪光效果
			};
		},
		created() {
			this.refresh();
		},
		watch: {
			// 监听list变化，当数据更新时重新刷新布局
			list: {
				handler(newVal, oldVal) {
					console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-waterfall][list_watch_001] 混合瀑布流数据变化，重新计算布局');
					
					// 如果是首次加载或完全刷新数据，则执行完整刷新
					if (!oldVal || oldVal.length === 0 || newVal.length <= oldVal.length) {
						this.refresh();
						// 重置所有加载状态
						this.resetLoadingStates(newVal.length);
					} else {
						// 如果是追加数据，只处理新增部分，保持已有元素位置不变
						this.appendRefresh(oldVal.length);
						// 只为新增加的项目初始化加载状态
						this.initLoadingStates(oldVal.length, newVal.length);
					}
					
					// 允许DOM更新后再执行计算
					this.$nextTick(() => {
						if (newVal.length > 0) {
							// 只有在完全刷新时重置所有计算数据
							if (!oldVal || oldVal.length === 0) {
								this.allHeightArr = [];
								this.allPositionArr = [];
								this.num = 0;
							}
						}
					});
				},
				deep: true
			}
		},
		methods: {
			// 重置所有加载状态
			resetLoadingStates(length) {
				// 初始化所有项目为未加载状态
				const loadedItems = {};
				const shiningItems = {};
				for (let i = 0; i < length; i++) {
					loadedItems[i] = false;
					shiningItems[i] = false;
				}
				this.loadedItems = loadedItems;
				this.shiningItems = shiningItems;
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-waterfall][resetLoadingStates_001] 重置所有混合内容加载状态');
			},
			
			// 初始化新增项目的加载状态
			initLoadingStates(startIndex, endIndex) {
				for (let i = startIndex; i < endIndex; i++) {
					this.$set(this.loadedItems, i, false);
					this.$set(this.shiningItems, i, false);
				}
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-waterfall][initLoadingStates_001] 初始化新增混合内容加载状态');
			},
			
			// 显示闪光效果
			showShineEffect(index) {
				// 设置闪光效果为可见
				this.$set(this.shiningItems, index, true);
				
				// 2秒后隐藏闪光效果
				setTimeout(() => {
					this.$set(this.shiningItems, index, false);
				}, 2000);
				
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-waterfall][showShineEffect_001] 显示混合内容闪光效果:', index);
			},
			
			// 获取项目的唯一键
			getItemKey(item) {
				if (item.content_type === 'article') {
					return 'article_' + (item.article_id || item.id || Date.now());
				} else if (item.content_type === 'video') {
					return 'video_' + (item.videoId || item.id || Date.now());
				} else {
					return 'product_' + (item[this.idfield] || Date.now());
				}
			},
			
			// 获取项目ID用于DOM引用
			getItemId(item) {
				if (item.content_type === 'article') {
					return 'article_' + (item.article_id || item.id || Date.now());
				} else if (item.content_type === 'video') {
					return 'video_' + (item.videoId || item.id || Date.now());
				} else {
					return item[this.idfield] || Date.now();
				}
			},
			
			// 格式化日期
			formatDate(timestamp) {
				if (!timestamp) return '';
				const date = new Date(parseInt(timestamp) * 1000);
				return date.getFullYear() + '-' + 
					('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
					('0' + date.getDate()).slice(-2);
			},
			
			// 页面跳转
			gotoDetail(item) {
				let url = '';
				if (item.content_type === 'article') {
					url = '/pages/article/detail?id=' + (item.article_id || item.id);
				} else if (item.content_type === 'video') {
					url = '/activity/shortvideo/detail?id=' + (item.videoId || item.id);
				} else {
					url = '/shopPackage/shop/product?id=' + item[this.idfield];
				}
				
				this.goto({
					currentTarget: {
						dataset: {
							url: url
						}
					}
				});
			},
			
			buydialogChange: function(e) {
				if (!this.buydialogShow) {
					this.proid = e.currentTarget.dataset.proid
				}
				this.buydialogShow = !this.buydialogShow;
			},
			
			addcart: function() {
				this.$emit('addcart');
			},
			
			showLinkChange: function(e) {
				var that = this;
				that.showLinkStatus = !that.showLinkStatus;
				if (e.currentTarget.dataset) {
					that.lx_name = e.currentTarget.dataset.lx_name;
					that.lx_bid = e.currentTarget.dataset.lx_bid;
					that.lx_bname = e.currentTarget.dataset.lx_bname;
					that.lx_tel = e.currentTarget.dataset.lx_tel;
				}
			},
			
			copy(e) {
				uni.setClipboardData({
					data: e.currentTarget.dataset.text,
					success: () => {
						uni.showToast({
							title: '复制成功'
						});
					}
				});
			},
			
			goto(e) {
				let url = e.currentTarget.dataset.url;
				if (!url) return;
				
				if (url.startsWith('tel::')) {
					// 拨打电话
					uni.makePhoneCall({
						phoneNumber: url.replace('tel::', '')
					});
				} else {
					// 页面跳转
					uni.navigateTo({
						url: url
					});
				}
			},

			imageLoadHandle(index) {
				// 如果已经计算过这个索引并且不是强制刷新，不再重复计算
				if (this.allHeightArr[index] && this.allPositionArr[index] && !this.forceRefresh) {
					return;
				}
				
				const item = this.list[index];
				const id = "waterfalls-list-id-" + this.getItemId(item);
				const query = uni.createSelectorQuery().in(this);
				
				query
					.select("#" + id)
					.fields({
						size: true
					}, (data) => {
						if (!data) {
							console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-waterfall][imageLoadHandle_001] ID不存在:', id);
							return;
						}
						
						if (!data.width || !data.height) {
							console.error('2025-06-10 20:55:53,565-ERROR-[dp-product-mixed-waterfall][imageLoadHandle_002] 无法获取元素尺寸:', id);
							
							// 如果获取不到尺寸，使用占位尺寸，防止布局错乱
							this.allHeightArr[index] = 300; 
							this.calePosition();
							return;
						}

						// 保存加载后的 item 高度
						this.allHeightArr[index] = data.height;
						
						// 图片加载完成，标记为已加载
						this.$set(this.loadedItems, index, true);
						
						// 显示闪光效果
						this.showShineEffect(index);
						
						// 如果列数发生变化时，需要重新布局
						// 计算出自动排版的位置信息并进行绘制
						this.calePosition();
					})
					.exec();
			},
			
			calePosition() {
				this.num += 1;
				
				// 如果高度数组为空，或者数量小于列表长度，说明还有图片没有加载完成
				if (!this.allHeightArr.length || this.allHeightArr.length < this.list.length) {
					return;
				}
				
				// 计算瀑布流布局位置
				const positionArr = [];
				if (this.cols === 2) {
					// 初始化高度数组
					let leftH = 0;
					let rightH = 0;
					const w = uni.upx2px(750) / 2;
					const offset = this.offset || 0;

					this.allHeightArr.forEach((h, i) => {
						// 判断左右两侧哪个高度更低，将元素放在高度更低的一侧
						if (leftH <= rightH) {
							positionArr[i] = {
								left: "0px",
								top: leftH + "px"
							};
							leftH += h + offset;
						} else {
							positionArr[i] = {
								left: w + "px",
								top: rightH + "px"
							};
							rightH += h + offset;
						}
					});

					// 设置容器高度为左右两列中较高的一列高度
					this.height = Math.max(leftH, rightH);
				} else if (this.cols >= 3) {
					// 多列瀑布流的实现（3列或更多）
					const colsHeightArr = new Array(this.cols).fill(0); // 记录每列的高度
					const containerWidth = uni.upx2px(750);
					const columnWidth = containerWidth / this.cols;
					const offset = this.offset || 0;

					this.allHeightArr.forEach((h, i) => {
						// 找到高度最小的列
						let minHeightIndex = 0;
						for (let j = 1; j < this.cols; j++) {
							if (colsHeightArr[j] < colsHeightArr[minHeightIndex]) {
								minHeightIndex = j;
							}
						}

						// 将元素放在高度最小的列
						positionArr[i] = {
							left: (minHeightIndex * columnWidth) + "px",
							top: colsHeightArr[minHeightIndex] + "px"
						};

						// 更新该列的高度
						colsHeightArr[minHeightIndex] += h + offset;
					});

					// 设置容器高度为各列中最高的列的高度
					this.height = Math.max(...colsHeightArr);
				}

				// 更新位置数组
				this.allPositionArr = positionArr;
				
				// 通知父组件图片加载完成
				if(this.list.length > 0 && this.allHeightArr.length === this.list.length) {
					console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-waterfall][calePosition_001] 混合瀑布流布局计算完成');
					this.$emit('image-load');
				}
			},
			
			// 添加追加刷新方法，只对新增项目计算位置
			appendRefresh(startIndex) {
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-waterfall][appendRefresh_001] 混合瀑布流追加刷新，起始索引:', startIndex);
				
				// 使用现有的topArr继续计算，不重置已有元素的位置
				// 检查topArr是否已经初始化
				if (this.topArr.length === 0) {
					let arr = [];
					for (let i = 0; i < this.cols; i++) {
						arr.push(0);
					}
					this.topArr = arr;
				}
				
				// 仅处理新增的部分
				this.forceRefresh = true;
				
				// 设置一个标志，300ms后取消强制刷新状态
				setTimeout(() => {
					this.forceRefresh = false;
					console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-waterfall][appendRefresh_002] 混合瀑布流追加刷新完成');
				}, 300);
			},
			
			// 刷新瀑布流
			refresh() {
				this.topArr = [];
				this.forceRefresh = true;
				
				for (let i = 0; i < this.cols; i++) {
					this.topArr[i] = 0;
				}
				
				// 重置所有加载状态
				this.resetLoadingStates(this.list.length);
				
				this.$nextTick(() => {
					setTimeout(() => {
						this.forceRefresh = false;
					}, 500);
				});
			}
		}
	}
</script>

<style>
.waterfalls-box {
	position: relative;
	width: 100%;
}

.waterfalls-list {
	width: calc(100% / var(--cols) - var(--offset));
	transform: translateZ(0);
	position: absolute;
	border-radius: 12rpx;
	overflow: hidden;
	margin-bottom: var(--offset);
	background-color: #fff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.waterfalls-list-image {
	width: 100%;
	transform: translateZ(0);
}

.saleimg {
	position: absolute;
	top: 0;
	left: 0;
	width: 80rpx;
	height: 80rpx;
	z-index: 6;
}

/* 加载中遮罩层样式 */
.loading-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.8);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	z-index: 10;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f0f0f0;
	border-top: 4rpx solid #f5222d;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.loading-text {
	margin-top: 20rpx;
	font-size: 24rpx;
	color: #666;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 闪光效果样式 */
.shine-effect {
	position: absolute;
	top: 0;
	left: -150%;
	width: 100%;
	height: 100%;
	background: linear-gradient(
		to right, 
		rgba(255, 255, 255, 0) 0%,
		rgba(255, 255, 255, 0.3) 50%,
		rgba(255, 255, 255, 0) 100%
	);
	transform: skewX(-25deg);
	z-index: 5;
	animation: shine 2s ease-in-out;
}

@keyframes shine {
	0% { left: -150%; }
	100% { left: 150%; }
}

/* 商品样式 */
.product-info {
	padding: 16rpx;
}

.product-info .p1 {
	font-size: 28rpx;
	color: #333;
	line-height: 1.3;
	margin-bottom: 12rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.binfo {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.binfo .t1 {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	margin-right: 8rpx;
}

.binfo .t2 {
	font-size: 24rpx;
	color: #666;
}

.p2 {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.p2-1 {
	display: flex;
	align-items: center;
}

.p2-1 .t1 {
	font-size: 30rpx;
	font-weight: bold;
}

.p2-1 .t2 {
	font-size: 24rpx;
	color: #999;
	text-decoration: line-through;
	margin-left: 8rpx;
}

.p2-1 .t3 {
	font-size: 24rpx;
	margin-left: 8rpx;
}

.p2-1 .lianxi {
	font-size: 24rpx;
	color: #fff;
	padding: 0 12rpx;
	height: 44rpx;
	line-height: 44rpx;
	border-radius: 22rpx;
	margin-left: 16rpx;
}

.p3 {
	font-size: 24rpx;
	color: #999;
	margin-top: 12rpx;
}

.p4 {
	position: absolute;
	right: 16rpx;
	bottom: 16rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
}

.p4 .img {
	width: 38rpx;
	height: 38rpx;
}

.couponitem {
	margin-top: 12rpx;
	padding: 0 16rpx 16rpx;
}

.couponitem .f1 {
	display: flex;
	flex-wrap: wrap;
}

.couponitem .f1 .t {
	font-size: 22rpx;
	padding: 4rpx 8rpx;
	border-radius: 4rpx;
	margin-right: 8rpx;
	margin-bottom: 8rpx;
}

/* 文章样式 */
.article-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	position: relative;
}

.article-image {
	width: 100%;
	height: auto;
	background-color: #f5f5f5;
	transform: translateZ(0);
}

.article-tag {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
}

.article-info {
	padding: 16rpx;
}

.article-title {
	font-size: 28rpx;
	font-weight: bold;
	line-height: 1.4;
	color: #333;
	margin-bottom: 12rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.article-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 12rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.article-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 24rpx;
	color: #999;
}

.article-views {
	display: flex;
	align-items: center;
}

.article-views .iconfont {
	margin-right: 4rpx;
	font-size: 24rpx;
}

/* 视频样式 */
.video-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	position: relative;
}

.video-image-wrap {
	width: 100%;
	position: relative;
}

.video-image {
	width: 100%;
	height: auto;
	background-color: #f5f5f5;
	transform: translateZ(0);
}

.video-play-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 80rpx;
	height: 80rpx;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 36rpx;
}

.video-tag {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
}

.video-info {
	padding: 16rpx;
}

.video-title {
	font-size: 28rpx;
	font-weight: bold;
	line-height: 1.4;
	color: #333;
	margin-bottom: 12rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.video-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.video-user {
	display: flex;
	align-items: center;
}

.video-user-avatar {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	margin-right: 8rpx;
}

.video-user-name {
	font-size: 24rpx;
	color: #666;
	max-width: 120rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.video-counts {
	display: flex;
	align-items: center;
}

.video-view-count, .video-like-count {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #999;
}

.video-view-count {
	margin-right: 16rpx;
}

.video-view-count .iconfont, .video-like-count .iconfont {
	margin-right: 4rpx;
	font-size: 24rpx;
}

/* 弹窗样式 */
.posterDialog {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.posterDialog .main {
	width: 600rpx;
	background-color: #fff;
	border-radius: 12rpx;
	position: relative;
}

.posterDialog .close {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.posterDialog .close .img {
	width: 32rpx;
	height: 32rpx;
}

.posterDialog .content {
	padding: 40rpx;
}

.posterDialog .title {
	font-size: 32rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 30rpx;
}

.posterDialog .row {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
	padding-bottom: 20rpx;
}

.posterDialog .row .f1 {
	width: 160rpx;
	font-size: 28rpx;
	color: #666;
}

.posterDialog .row .f2 {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	display: flex;
	align-items: center;
}

.posterDialog .row .f2 .image {
	width: 24rpx;
	height: 24rpx;
	margin-left: 8rpx;
}

.posterDialog .row .f2 .copyicon {
	width: 32rpx;
	height: 32rpx;
	margin-left: 12rpx;
}

.bg-desc {
	position: absolute;
	top: 0;
	left: 0;
	font-size: 24rpx;
	color: #fff;
	padding: 0 20rpx;
	height: 40rpx;
	line-height: 40rpx;
	border-radius: 0 0 40rpx 0;
}
</style> 