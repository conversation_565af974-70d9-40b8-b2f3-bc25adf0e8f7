<template>
<view class="container">
	<block v-if="isload">
		<view class="header-container">
			<view class="activity-info">
				<view class="activity-name">{{config.name}}</view>
				<view class="activity-desc">{{config.copy_mode}}复制模式 · 财富奖励{{config.wealth_reward_amount}}元</view>
			</view>
			<view class="my-position-btn" @click="toMyPosition" style="background: #FF5722">我的点位</view>
		</view>
		
		<!-- 新增：测试功能快捷入口 -->
		<view class="test-actions" v-if="config.id">
			<view class="test-buttons">
				<view class="test-btn" @click="showAddPositionModal" style="background: linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%)">
					<text>?? 快速增加点位</text>
				</view>
				<view class="test-btn outline" @click="toPositionTree">
					<text>?? 查看排单树</text>
				</view>
			</view>
		</view>
		
		<view class="content-container">
			<scroll-view class="product-box" scroll-y="true" @scrolltolower="scrolltolower">
				<view class="product-itemlist">
					<view class="item" v-for="(item,index) in datalist" :key="item.id" @click="toProductDetail" :data-id="item.id">
						<view class="product-pic">
							<image class="image" :src="item.thumb" mode="widthFix"/>
						</view>
						<view class="product-info">
							<view class="p1"><text>{{item.name}}</text></view>
							<view class="p2">
								<view class="t1" style="color: #FF5722">¥{{item.price}}</view>
								<text class="t2" v-if="item.original_price*1 > item.price*1">¥{{item.original_price}}</text>
							</view>
							<view class="p3">
								<view class="p3-1">{{item.sales_text}}</view>
								<view class="p3-2">{{item.stock_text}}</view>
							</view>
							<view class="category-tag" v-if="item.category_name">{{item.category_name}}</view>
						</view>
						<view class="buy-btn" @click.stop="buyProduct" :data-id="item.id" style="background: linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%)">购买</view>
					</view>
				</view>
				<nomore text="没有更多商品了" v-if="nomore"></nomore>
				<nodata text="暂无参与商品" v-if="nodata"></nodata>
				<view style="width:100%;height:100rpx"></view>
			</scroll-view>
		</view>
		
		<!-- 新增：手动添加点位弹窗 -->
		<view class="modal-overlay" v-if="showAddModal" @click="hideAddPositionModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">快速增加点位</text>
					<text class="modal-close" @click="hideAddPositionModal">×</text>
				</view>
				<view class="modal-body">
					<view class="activity-info-modal">
						<text class="info-title">{{config.name}}</text>
						<text class="info-desc">{{config.copy_mode}}复制 · 奖励{{config.wealth_reward_amount}}元</text>
					</view>
					<view class="form-item">
						<text class="form-label">父点位ID（可选）：</text>
						<input type="number" class="form-input" placeholder="不填则创建根点位" v-model="addForm.parent_id"/>
					</view>
					<view class="form-item">
						<text class="form-label">订单ID（可选）：</text>
						<input type="number" class="form-input" placeholder="关联的订单ID" v-model="addForm.order_id"/>
					</view>
					<view class="form-tips">
						<text>?? 测试功能说明：</text>
						<text>• 此功能仅用于测试排单系统</text>
						<text>• 不填父点位ID将创建根点位</text>
						<text>• 系统自动计算层级和位置</text>
						<text>• 财富点位根据配置自动判断</text>
					</view>
				</view>
				<view class="modal-footer">
					<view class="modal-btn cancel" @click="hideAddPositionModal">取消</view>
					<view class="modal-btn confirm" @click="confirmAddPosition" style="background: #FF5722">立即添加</view>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			pagenum: 1,
			nomore: false,
			nodata: false,
			datalist: [],
			config_id: 0,
			config: {},
			// 新增：添加点位相关数据
			showAddModal: false,
			addForm: {
				parent_id: '',
				order_id: ''
			}
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.config_id = opt.config_id || 0;
		if(!this.config_id){
			uni.showToast({title: '活动参数错误', icon: 'none'});
			return;
		}
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	methods: {
		getdata:function(){
			var that = this;
			that.pagenum = 1;
			that.datalist = [];
			that.loaded();
			that.getdatalist();
		},
		getdatalist: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}

			var that = this;
			var pagenum = that.pagenum;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
			app.post('ApiPaidan/getActivityProducts', {
				config_id: that.config_id,
				page: pagenum, 
				limit: 10
			}, function (res) { 
				that.loading = false;
				uni.stopPullDownRefresh();

				if(res.code == 1){
					var data = res.data;
					that.config = data.config;
					
					var products = data.list;
					if (products.length == 0) {
						if(pagenum == 1){
							that.nodata = true;
						}else{
							that.nomore = true;
						}
					}
					var datalist = that.datalist;
					var newdata = datalist.concat(products);
					that.datalist = newdata;
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},

		scrolltolower: function () {
			if (!this.nomore) {
				this.pagenum = this.pagenum + 1;    
				this.getdatalist(true);
			}
		},
		
		toProductDetail(e){
			var id = e.currentTarget.dataset.id;
			app.goto('/shopPackage/shop/product?id='+id);
		},
		
		buyProduct(e){
			var id = e.currentTarget.dataset.id;
			app.goto('/shopPackage/shop/product?id='+id);
		},
		
		toMyPosition(){
			app.goto('/pagesB/paidan/my-position?config_id='+this.config_id);
		},
		
		// 新增：显示添加点位弹窗
		showAddPositionModal: function(){
			this.showAddModal = true;
			this.addForm = {
				parent_id: '',
				order_id: ''
			};
		},
		
		// 新增：隐藏添加点位弹窗
		hideAddPositionModal: function(){
			this.showAddModal = false;
		},
		
		// 新增：确认添加点位
		confirmAddPosition: function(){
			var that = this;
			
			// 参数验证
			var params = {
				config_id: that.config_id
			};
			
			if(that.addForm.parent_id){
				params.parent_id = parseInt(that.addForm.parent_id);
			}
			
			if(that.addForm.order_id){
				params.order_id = parseInt(that.addForm.order_id);
			}
			
			that.loading = true;
			
			app.post('ApiPaidan/addPosition', params, function (res) {
				that.loading = false;
				
				if(res.code == 1){
					that.$refs.popmsg.show({type: 'success', msg: res.msg});
					that.hideAddPositionModal();
					// 可以选择跳转到我的点位页面查看
					setTimeout(function(){
						that.toMyPosition();
					}, 1500);
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		// 新增：跳转到排单树页面
		toPositionTree: function(){
			app.goto('/pagesB/paidan/position-tree?config_id=' + this.config_id);
		},
		
		loaded: function () {
			this.isload = true;
		},
		
		getmenuindex: function (e) {
			this.menuindex = e;
		},
		
		// 获取颜色样式，避免undefined错误
		getColorStyle(colorType, styleType = 'color') {
			try {
				if (app.globalData && app.globalData.initdata && app.globalData.initdata[colorType]) {
					const colorValue = app.globalData.initdata[colorType];
					if (styleType === 'background') {
						return {background: colorValue};
					} else {
						return {color: colorValue};
					}
				}
			} catch (e) {
				console.log('获取颜色失败:', e);
			}
			// 默认颜色
			const defaultColor = '#007aff';
			return styleType === 'background' ? {background: defaultColor} : {color: defaultColor};
		},
		
		// 获取渐变样式，避免undefined错误
		getGradientStyle() {
			try {
				if (app.globalData && app.globalData.initdata && app.globalData.initdata.color1 && app.globalData.initdata.color1rgb) {
					const color1 = app.globalData.initdata.color1;
					const color1rgb = app.globalData.initdata.color1rgb;
					if (color1rgb.red !== undefined && color1rgb.green !== undefined && color1rgb.blue !== undefined) {
						return {
							background: `linear-gradient(90deg, ${color1} 0%, rgba(${color1rgb.red},${color1rgb.green},${color1rgb.blue},0.8) 100%)`
						};
					}
				}
			} catch (e) {
				console.log('获取渐变样式失败:', e);
			}
			return {background: 'linear-gradient(90deg, #007aff 0%, rgba(0,122,255,0.8) 100%)'}; // 默认渐变
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}
.header-container {width: 100%;padding: 30rpx;background-color: #fff;border-bottom:1px solid #f5f5f5;display:flex;align-items:center;}
.activity-info{flex:1;}
.activity-name{font-size:32rpx;font-weight:bold;color:#333;margin-bottom:8rpx;}
.activity-desc{font-size:24rpx;color:#666;}
.my-position-btn{padding:16rpx 32rpx;color:#fff;font-size:26rpx;border-radius:30rpx;text-align:center;}

/* 新增：测试功能样式 */
.test-actions{width:100%;padding:20rpx 30rpx;background-color:#fff;border-bottom:1px solid #f5f5f5;}
.test-buttons{display:flex;justify-content:space-between;}
.test-btn{flex:1;height:70rpx;line-height:70rpx;text-align:center;color:#fff;font-size:24rpx;border-radius:35rpx;margin:0 10rpx;}
.test-btn:first-child{margin-left:0;}
.test-btn:last-child{margin-right:0;}
.test-btn.outline{background:#fff;color:#333;border:2rpx solid #ddd;}

.content-container{flex:1;height:100%;display:flex;overflow: hidden;}
.product-box{width: 100%;height:100%;padding:20rpx;}
.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px;}
.product-itemlist .item{width:100%;display: flex;position: relative;margin-bottom: 20rpx;background: #fff;padding:20rpx;border-radius:16rpx;align-items:center;}
.product-itemlist .product-pic {width: 160rpx;height:160rpx;overflow:hidden;background: #ffffff;border-radius:12rpx;margin-right:20rpx;}
.product-itemlist .product-pic .image{width: 100%;height:100%;object-fit:cover;}
.product-itemlist .product-info {flex:1;padding-right:20rpx;position: relative;}
.product-itemlist .product-info .p1 {color:#333;font-weight:bold;font-size:28rpx;line-height:40rpx;margin-bottom:12rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:80rpx}
.product-itemlist .product-info .p2{margin-bottom:12rpx;height:36rpx;line-height:36rpx;overflow:hidden;display:flex;align-items:center;}
.product-itemlist .product-info .p2 .t1{font-size:32rpx;font-weight:bold;}
.product-itemlist .product-info .p2 .t2 {margin-left:16rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;}
.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-bottom:12rpx}
.product-itemlist .product-info .p3-1{font-size:22rpx;color:#999;margin-right:20rpx;}
.product-itemlist .product-info .p3-2{font-size:22rpx;color:#999;}
.category-tag{display:inline-block;padding:4rpx 12rpx;background:#f0f0f0;color:#666;font-size:20rpx;border-radius:12rpx;}
.buy-btn{width:120rpx;height:60rpx;line-height:60rpx;text-align:center;color:#fff;font-size:24rpx;border-radius:30rpx;}

/* 新增：弹窗样式 */
.modal-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:9999;}
.modal-content{width:600rpx;background:#fff;border-radius:20rpx;overflow:hidden;}
.modal-header{display:flex;justify-content:space-between;align-items:center;padding:30rpx;border-bottom:1px solid #f5f5f5;}
.modal-title{font-size:32rpx;font-weight:bold;color:#333;}
.modal-close{font-size:40rpx;color:#999;width:40rpx;height:40rpx;text-align:center;line-height:32rpx;}
.modal-body{padding:30rpx;}
.activity-info-modal{background:#f8f9fa;padding:20rpx;border-radius:12rpx;margin-bottom:30rpx;text-align:center;}
.info-title{display:block;font-size:28rpx;font-weight:bold;color:#333;margin-bottom:8rpx;}
.info-desc{font-size:22rpx;color:#666;}
.form-item{margin-bottom:30rpx;}
.form-label{display:block;font-size:26rpx;color:#333;margin-bottom:16rpx;}
.form-input{width:100%;height:80rpx;padding:0 20rpx;background:#f8f8f8;border-radius:12rpx;font-size:26rpx;border:none;}
.form-tips{background:#f0f8ff;padding:20rpx;border-radius:12rpx;margin-top:20rpx;}
.form-tips text{display:block;font-size:22rpx;color:#666;margin-bottom:8rpx;line-height:1.5;}
.form-tips text:first-child{font-weight:bold;color:#007aff;}
.form-tips text:last-child{margin-bottom:0;}
.modal-footer{display:flex;padding:30rpx;border-top:1px solid #f5f5f5;}
.modal-btn{flex:1;height:80rpx;line-height:80rpx;text-align:center;font-size:28rpx;border-radius:40rpx;margin:0 10rpx;}
.modal-btn:first-child{margin-left:0;}
.modal-btn:last-child{margin-right:0;}
.modal-btn.cancel{background:#f8f8f8;color:#666;}
.modal-btn.confirm{color:#fff;}
</style>