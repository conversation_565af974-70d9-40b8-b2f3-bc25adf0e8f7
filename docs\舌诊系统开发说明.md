# 舌诊UniApp系统开发说明

## 项目概述

本项目是一个基于UniApp的静态舌诊页面系统，包含引导介绍、照片拍摄、分析处理和结果展示四个核心功能模块。系统采用中医舌诊理论，结合现代AI技术理念，为用户提供便捷的健康检测服务。

## 系统架构

### 页面结构
```
pagesB/shezhen/
├── guide.vue       # 舌诊引导介绍页
├── camera.vue      # 舌诊拍摄页面
├── analysis.vue    # 分析处理页面
└── result.vue      # 结果展示页面
```

### 路由配置
在`pages.json`中配置的路由信息：
```json
{
  "root": "pagesB",
  "pages": [
    {
      "path": "shezhen/guide",
      "style": {
        "navigationBarTitleText": "舌诊指南"
      }
    },
    {
      "path": "shezhen/camera",
      "style": {
        "navigationBarTitleText": "舌诊拍摄"
      }
    },
    {
      "path": "shezhen/analysis",
      "style": {
        "navigationBarTitleText": "分析中"
      }
    },
    {
      "path": "shezhen/result",
      "style": {
        "navigationBarTitleText": "舌诊结果"
      }
    }
  ]
}
```

## 功能模块详解

### 1. 引导介绍页 (guide.vue)

#### 功能特点
- **中医舌诊科普**：详细介绍舌诊的基本原理和应用价值
- **AI技术说明**：说明AI舌诊的技术优势和准确性
- **拍摄指导**：提供详细的拍摄步骤和注意事项
- **健康提醒**：明确说明系统的参考性质和医疗建议

#### 技术实现
- 响应式布局设计，适配多种屏幕尺寸
- 渐变背景和卡片式布局，提升视觉效果
- 图标与文字结合，增强用户理解
- 统一的按钮样式和交互反馈

#### 关键代码片段
```vue
<view class="guide-cards">
  <view class="guide-card">
    <image class="card-image" src="/static/images/tongue-diagnosis.png"></image>
    <text class="card-title">什么是舌诊？</text>
    <text class="card-content">中医舌诊通过观察舌质、舌苔的颜色、形态、厚薄等特征，判断人体脏腑功能和气血盛衰，是中医四诊中的重要组成部分。</text>
  </view>
</view>
```

### 2. 拍摄页面 (camera.vue)

#### 功能特点
- **相机预览**：实时显示相机画面，支持前后摄像头切换
- **拍摄引导**：提供舌头定位框架和拍摄提示
- **图片预览**：拍摄完成后可预览图片质量
- **重拍功能**：支持重新拍摄直到满意
- **闪光灯控制**：根据环境光线自动或手动控制闪光灯

#### 技术实现
- 使用UniApp的camera组件实现相机功能
- 实现拍摄状态管理和错误处理
- 图片质量检测和用户引导
- 加载状态和进度提示

#### 关键代码片段
```vue
<camera 
  :device-position="cameraPosition"
  :flash="flashMode"
  @initdone="onCameraInit"
  @error="onCameraError"
  class="camera-preview"
></camera>
```

### 3. 分析页面 (analysis.vue)

#### 功能特点
- **分析进度展示**：实时显示AI分析的各个阶段
- **图片展示**：显示待分析的舌诊图片
- **动画效果**：扫描线动画和分析区域标记
- **日志记录**：详细记录分析过程的各个步骤
- **预计时间**：显示分析完成的预计时间

#### 技术实现
- 模拟AI分析流程，包含多个分析阶段
- CSS动画实现扫描效果和进度条
- 定时器控制分析进度更新
- 状态管理确保流程顺序执行

#### 关键代码片段
```vue
<view class="scanning-line" v-if="currentStep >= 1"></view>
<view class="analysis-region" 
  v-for="(region, index) in analysisRegions" 
  :key="index"
  :style="{ left: region.x + 'rpx', top: region.y + 'rpx' }"
></view>
```

### 4. 结果页面 (result.vue)

#### 功能特点
- **综合评分**：显示健康评分和可视化圆环进度
- **诊断结果**：展示舌质、舌苔、体质类型等核心信息
- **详细报告**：分标签页展示舌象特征、健康状况、中医理论
- **调理建议**：提供饮食、运动、情志、中医等多维度建议
- **报告操作**：支持保存、分享、重新检测等功能

#### 技术实现
- 标签页切换和内容展示
- 动态数据渲染和状态管理
- 可视化图表和进度条
- 分享功能和本地存储

#### 关键代码片段
```vue
<view class="score-progress" 
  :style="{ transform: `rotate(${(resultData.score / 100) * 360}deg)` }">
</view>
```

## 数据结构设计

### 结果数据结构
```javascript
resultData: {
  image: '',              // 舌诊图片路径
  tongueQuality: '淡红',   // 舌质类型
  tongueCoating: '薄白',   // 舌苔类型
  constitution: '气虚质',  // 体质类型
  score: 85,              // 健康评分
  suggestions: []         // 调理建议
}
```

### 特征分析数据
```javascript
tongueFeatures: [
  {
    name: '舌体颜色',
    value: '淡红色',
    status: 'normal',      // normal/attention/warning
    description: '舌质淡红，表示气血充足，脏腑功能正常'
  }
]
```

### 健康指标数据
```javascript
healthIndicators: [
  {
    name: '气血充盈度',
    level: 'good',         // good/normal/attention
    levelText: '良好',
    percentage: 85,        // 百分比进度
    description: '气血运行通畅，精神状态良好'
  }
]
```

## 样式设计规范

### 色彩方案
- **主色调**：渐变蓝紫色 `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **辅助色**：白色背景，灰色文字
- **状态色**：
  - 正常：绿色 `#4caf50`
  - 注意：橙色 `#ff9800`
  - 警告：红色 `#f44336`

### 布局规范
- **间距单位**：使用rpx为单位，标准间距为20rpx的倍数
- **圆角大小**：卡片15-20rpx，按钮40rpx，小元素10rpx
- **阴影效果**：统一使用 `box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1)`

### 字体规范
- **标题**：32rpx，粗体
- **副标题**：28rpx，粗体
- **正文**：26rpx，常规
- **说明文字**：24rpx，常规
- **小字**：22rpx，常规

## 日志系统

采用结构化日志格式：`时间戳-级别-[文件名]-[函数名_序号]`

### 日志级别
- **INFO**：一般信息记录
- **ERROR**：错误信息记录
- **DEBUG**：调试信息记录

### 日志示例
```javascript
console.log('2025-01-26 10:15:00,001-INFO-[guide][startDiagnosis_001] 开始舌诊流程');
console.error('2025-01-26 10:15:00,002-ERROR-[camera][takePhoto_003] 拍照失败:', error);
```

## 开发指南

### 环境要求
- Node.js 14+
- HBuilderX 3.0+
- UniApp框架

### 本地开发
1. 克隆项目到本地
2. 在HBuilderX中打开项目
3. 运行到小程序或H5平台进行测试

### 页面跳转
```javascript
// 跳转到引导页
uni.navigateTo({
  url: '/pagesB/shezhen/guide'
});

// 跳转到结果页（携带数据）
uni.navigateTo({
  url: `/pagesB/shezhen/result?data=${encodeURIComponent(JSON.stringify(resultData))}`
});
```

### 数据传递
页面间通过URL参数传递数据，复杂数据使用JSON序列化：
```javascript
// 发送数据
const data = { score: 85, type: '气虚质' };
const url = `/pagesB/shezhen/result?data=${encodeURIComponent(JSON.stringify(data))}`;

// 接收数据
onLoad(options) {
  if (options.data) {
    this.resultData = JSON.parse(decodeURIComponent(options.data));
  }
}
```

## 扩展功能建议

### 1. 数据持久化
- 使用uni.setStorage保存历史记录
- 实现用户登录和数据同步
- 添加报告导出功能

### 2. AI集成
- 对接真实的AI舌诊算法
- 实现图片上传和分析API
- 添加分析准确度评估

### 3. 用户体验优化
- 添加引导动画和过渡效果
- 实现多语言支持
- 优化图片压缩和加载

### 4. 健康管理
- 添加健康建议推送
- 实现体质调理计划
- 集成健康数据监控

## 注意事项

1. **医疗声明**：系统仅供健康参考，不能替代专业医疗诊断
2. **隐私保护**：确保用户图片和健康数据的安全性
3. **性能优化**：注意图片大小和页面加载性能
4. **兼容性**：测试多平台和设备的兼容性
5. **用户引导**：提供清晰的使用说明和错误提示

## 更新日志

### v1.0.0 (2025-01-26)
- 完成舌诊系统基础功能开发
- 实现四个核心页面
- 添加完整的样式设计
- 完善日志系统和错误处理
- 编写详细的开发文档

---

*本文档将随着项目发展持续更新，请关注最新版本。* 