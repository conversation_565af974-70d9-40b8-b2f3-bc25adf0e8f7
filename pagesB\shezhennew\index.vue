<template>
<view class="page">
	<!-- 自定义导航栏 -->
	<view class="custom-navbar">
		<view class="navbar-left" @tap="goBack">
			<text class="back-arrow">‹</text>
		</view>
		<view class="navbar-center">
			<text class="navbar-title">AI在线舌诊</text>
		</view>
		<view class="navbar-right"></view>
	</view>

	<!-- 主要内容区域 -->
	<view class="content-container">
		<!-- 拍摄提示区域 -->
		<view class="notice-section">
			<view class="notice-header">
				<image class="notice-icon" :src="pre_url+'/static/img/shezhen-notice-icon.png'"/>
				<text class="notice-title">请拍摄请注意：</text>
			</view>
			<text class="notice-desc">
				请自然伸出舌头，在框内完整的露出舌头，并点击底部拍摄按钮
			</text>
		</view>

		<!-- 拍摄要求图标 -->
		<view class="requirements-list">
			<view class="requirement-item">
				<image class="requirement-icon" :src="pre_url+'/static/img/shezhen-light-icon.png'"/>
				<text class="requirement-text">光线充足</text>
			</view>
			<view class="requirement-item">
				<image class="requirement-icon" :src="pre_url+'/static/img/shezhen-flat-icon.png'"/>
				<text class="requirement-text">舌面平展</text>
			</view>
			<view class="requirement-item">
				<image class="requirement-icon" :src="pre_url+'/static/img/shezhen-face-icon.png'"/>
				<text class="requirement-text">正对手机</text>
			</view>
		</view>

		<!-- 舌头拍摄框架 -->
		<view class="camera-preview">
			<image class="tongue-frame" :src="pre_url+'/static/img/shezhen-tongue-frame.png'"/>
		</view>
	</view>

	<!-- 底部拍摄控制栏 -->
	<view class="camera-controls">
		<image class="control-btn" :src="pre_url+'/static/img/shezhen-gallery-icon.png'" @tap="chooseFromGallery"/>
		<image class="shoot-btn" :src="pre_url+'/static/img/shezhen-camera-btn.png'" @tap="takePhoto"/>
		<image class="control-btn" :src="pre_url+'/static/img/shezhen-switch-camera.png'" @tap="switchCamera"/>
	</view>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			pre_url: '',
			cameraPosition: 'back' // 摄像头位置：front前置，back后置
		};
	},

	onLoad: function() {
		this.pre_url = app.globalData.pre_url;
		uni.setNavigationBarTitle({
			title: 'AI在线舌诊'
		});
	},
	
	methods: {
		// 拍照
		takePhoto: function() {
			var that = this;
			uni.chooseImage({
				count: 1,
				sourceType: ['camera'],
				success: function(res) {
					var tempFilePaths = res.tempFilePaths;
					that.uploadImage(tempFilePaths[0]);
				},
				fail: function() {
					uni.showToast({
						title: '拍照失败',
						icon: 'none'
					});
				}
			});
		},
		
		// 从相册选择
		chooseFromGallery: function() {
			var that = this;
			uni.chooseImage({
				count: 1,
				sourceType: ['album'],
				success: function(res) {
					var tempFilePaths = res.tempFilePaths;
					that.uploadImage(tempFilePaths[0]);
				}
			});
		},
		
		// 切换摄像头
		switchCamera: function() {
			this.cameraPosition = this.cameraPosition === 'back' ? 'front' : 'back';
			uni.showToast({
				title: '已切换摄像头',
				icon: 'none'
			});
		},
		
		// 上传图片
		uploadImage: function(imagePath) {
			var that = this;
			uni.showLoading({
				title: '上传中...'
			});
			
			uni.uploadFile({
				url: app.globalData.pre_url + '/api/upload',
				filePath: imagePath,
				name: 'file',
				success: function(uploadRes) {
					uni.hideLoading();
					var data = JSON.parse(uploadRes.data);
					if (data.code === 1) {
						that.analyzeImage(data.data.url);
					} else {
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
					}
				},
				fail: function() {
					uni.hideLoading();
					uni.showToast({
						title: '上传失败',
						icon: 'none'
					});
				}
			});
		},
		
		// 分析舌诊图片
		analyzeImage: function(imageUrl) {
			var that = this;
			uni.hideLoading();

			// 跳转到分析页面
			uni.navigateTo({
				url: '/pagesB/shezhennew/analysis?imageUrl=' + encodeURIComponent(imageUrl)
			});
		},
		
		// 返回
		goBack: function() {
			uni.navigateBack();
		}
	}
};
</script>

<style>
.page {
	background-color: #ffffff;
	width: 100%;
	min-height: 100vh;
	position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 32rpx;
	background-color: #ffffff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	border-bottom: 1rpx solid #f0f0f0;
}

.navbar-left, .navbar-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-arrow {
	font-size: 48rpx;
	color: #333333;
	font-weight: 300;
}

.navbar-center {
	flex: 1;
	text-align: center;
}

.navbar-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

/* 主要内容区域 */
.content-container {
	padding-top: 88rpx;
	padding-bottom: 160rpx;
}

/* 拍摄提示区域 */
.notice-section {
	background-color: #f8f9fa;
	margin: 32rpx;
	padding: 40rpx;
	border-radius: 16rpx;
}

.notice-header {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.notice-icon {
	width: 64rpx;
	height: 64rpx;
	margin-right: 16rpx;
}

.notice-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.notice-desc {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.6;
	text-align: left;
}

/* 拍摄要求列表 */
.requirements-list {
	display: flex;
	justify-content: space-around;
	margin: 32rpx;
	padding: 32rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	border: 1rpx solid #f0f0f0;
}

.requirement-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.requirement-icon {
	width: 72rpx;
	height: 72rpx;
	margin-bottom: 16rpx;
}

.requirement-text {
	font-size: 24rpx;
	color: #999999;
	text-align: center;
}

/* 相机预览区域 */
.camera-preview {
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 40rpx 32rpx;
	padding: 60rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	border: 1rpx solid #f0f0f0;
}

.tongue-frame {
	width: 400rpx;
	height: 360rpx;
	border-radius: 12rpx;
}



/* 底部拍摄控制栏 */
.camera-controls {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 200rpx;
	background-color: #1e2639;
	padding: 0 56rpx;
}

.control-btn {
	width: 58rpx;
	height: 44rpx;
}

.control-btn:first-child {
	margin-right: 200rpx;
}

.shoot-btn {
	width: 120rpx;
	height: 120rpx;
}

.control-btn:last-child {
	width: 56rpx;
	height: 44rpx;
	margin-left: 200rpx;
}
</style>
