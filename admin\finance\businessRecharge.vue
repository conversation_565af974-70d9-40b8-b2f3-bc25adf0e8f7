<template>
<view>
	<block v-if="isload">
		<view class="content">
			<view class="form">
				<view class="group">
					<view class="title">充值金额</view>
					<view class="input">
						<input type="digit" v-model="amount" placeholder="请输入充值金额" placeholder-style="color:#C0C4CC" />
					</view>
				</view>
				<view class="group">
					<view class="title">支付方式</view>
					<view class="pay-type-list">
						<view 
							v-for="(item, index) in payTypes" 
							:key="index" 
							class="pay-type-item" 
							:class="{'active': pay_type === item.value}"
							@tap="selectPayType(item.value)"
						>
							<image class="pay-type-icon" :src="pre_url+'/static/img/'+item.icon"></image>
							<text class="pay-type-name">{{item.name}}</text>
						</view>
					</view>
				</view>
				<view class="group">
					<view class="title">支付凭证</view>
					<view class="upload-box">
						<view class="upload-item" v-if="pay_evidence" @tap="previewImage">
							<image class="uploaded-img" :src="pay_evidence" mode="aspectFill"></image>
							<view class="delete-btn" @tap.stop="removeImage">×</view>
						</view>
						<view v-else class="upload-btn" @tap="chooseImage">
							<image src="/static/img/add.png" class="add-icon"></image>
							<text>上传凭证</text>
						</view>
					</view>
				</view>
				<view class="group">
					<view class="title">备注说明(选填)</view>
					<view class="input">
						<textarea v-model="remark" placeholder="请输入备注说明" placeholder-style="color:#C0C4CC"></textarea>
					</view>
				</view>
			</view>
			
			<view class="tips">
				<view class="tip-item">1. 请确保填写的信息准确无误</view>
				<view class="tip-item">2. 上传真实有效的支付凭证</view>
				<view class="tip-item">3. 充值申请提交后将等待平台审核</view>
			</view>
			
			<view class="btn-submit" @tap="submitRecharge">提交充值申请</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
			loading: false,
      isload: false,
			pre_url: app.globalData.pre_url,
			
			// 表单数据
			amount: '', // 充值金额
			pay_type: 'wxpay', // 支付方式
			pay_evidence: '', // 支付凭证
			remark: '', // 备注
			
			// 支付方式选项
			payTypes: [
				{name: '微信转账', value: 'wxpay', icon: 'wxpay.png'},
				{name: '支付宝转账', value: 'alipay', icon: 'alipay.png'},
				{name: '银行转账', value: 'bank', icon: 'bank.png'}
			]
    };
  },
  
  onLoad: function () {
		this.loaded();
  },
	
  methods: {
		// 选择支付方式
		selectPayType(type) {
			this.pay_type = type;
		},
		
		// 选择图片
		chooseImage() {
			var that = this;
			app.chooseImage(function(urls) {
				if (urls && urls.length > 0) {
					that.pay_evidence = urls[0];
				}
			}, 1);
		},
		
		// 预览图片
		previewImage() {
			if (this.pay_evidence) {
				uni.previewImage({
					urls: [this.pay_evidence]
				});
			}
		},
		
		// 删除图片
		removeImage(e) {
			e.stopPropagation();
			this.pay_evidence = '';
		},
		
		// 提交充值申请
		submitRecharge() {
			var that = this;
			
			// 表单验证
			if (!that.amount || that.amount <= 0) {
				app.alert('请输入正确的充值金额');
				return;
			}
			
			if (!that.pay_evidence) {
				app.alert('请上传支付凭证');
				return;
			}
			
			// 构建请求参数
			const params = {
				amount: that.amount,
				pay_type: that.pay_type,
				pay_evidence: that.pay_evidence,
				remark: that.remark
			};
			
			that.loading = true;
			app.post('ApiAdminFinance/businessRecharge', params, function(res) {
				that.loading = false;
				if (res.status === 1) {
					app.alert('充值申请提交成功', function() {
						// 跳转到充值记录页面
						uni.redirectTo({
							url: 'businessRechargeLog'
						});
					});
				} else {
					app.alert(res.msg || '提交失败');
				}
			});
		}
  }
};
</script>

<style>
@import "../common.css";

.content {
	width: 94%;
	margin: 20rpx auto;
	border-radius: 16rpx;
	background-color: #fff;
	padding: 30rpx;
}

.form {
	width: 100%;
}

.group {
	margin-bottom: 40rpx;
}

.title {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 20rpx;
	font-weight: bold;
}

.input {
	width: 100%;
	border-bottom: 1px solid #eee;
	padding-bottom: 10rpx;
}

.input input, .input textarea {
	width: 100%;
	font-size: 30rpx;
	color: #333;
}

.input textarea {
	height: 120rpx;
}

.pay-type-list {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.pay-type-item {
	width: 30%;
	height: 100rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
	border: 1px solid #eee;
	border-radius: 10rpx;
	padding: 10rpx;
}

.pay-type-item.active {
	border-color: #FC5648;
	background-color: rgba(252, 86, 72, 0.05);
}

.pay-type-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 10rpx;
}

.pay-type-name {
	font-size: 24rpx;
	color: #333;
}

.upload-box {
	display: flex;
	flex-wrap: wrap;
}

.upload-btn, .upload-item {
	width: 200rpx;
	height: 200rpx;
	border: 1px dashed #ddd;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
}

.add-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
}

.uploaded-img {
	width: 100%;
	height: 100%;
}

.delete-btn {
	position: absolute;
	top: -20rpx;
	right: -20rpx;
	width: 40rpx;
	height: 40rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
}

.tips {
	margin-top: 40rpx;
	padding: 20rpx;
	background-color: #f8f8f8;
	border-radius: 10rpx;
}

.tip-item {
	font-size: 24rpx;
	color: #999;
	line-height: 1.8;
}

.btn-submit {
	width: 90%;
	height: 90rpx;
	background-color: #FC5648;
	color: #fff;
	font-size: 32rpx;
	border-radius: 45rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 60rpx auto 30rpx;
}
</style> 