# 鲸犀商城混合模式组件使用指南

## 一、混合模式概述

混合模式是鲸犀商城中的高级布局方式，可以将商品、文章和短视频内容混合展示在同一个组件中，形成丰富多样的内容流，提升用户体验和页面吸引力。

### 主要特点

- **内容整合**：将商品、文章、短视频等多种内容类型混合展示
- **灵活布局**：支持双排或瀑布流布局方式
- **混合策略**：支持随机插入或固定位置插入两种混合方式
- **丰富自定义**：可设置标题、副标题、更多链接等组件属性

## 二、混合模式设置指南

### 1. 基础设置

在页面设计器中添加"商城商品"组件后，在样式选择中选择混合模式：

- 双排混合：选择"mixed-row"
- 瀑布流混合：选择"mixed-waterfall"

### 2. 混合方式

混合模式支持两种混合方式：

- **随机插入**：将文章/视频随机插入到商品列表中，由系统根据设置的频率自动分配位置
- **固定位置**：在指定的位置插入文章/视频，可精确控制混合内容的位置

### 3. 布局方式

混合模式支持两种布局样式：

- **双排（mixed-row）**：内容以双排形式展示，适合商品展示
- **瀑布流（mixed-waterfall）**：内容以瀑布流形式展示，适合多样化内容和不同尺寸的图片

## 三、文章混合设置

### 1. 启用文章混合

1. 勾选"混合文章"启用文章混合功能
2. 选择文章来源方式：
   - **手动选择**：手动添加文章到混合内容中
   - **选择分类**：根据文章分类自动加载文章

### 2. 文章分类设置

文章分类设置是根据系统中已有的文章分类进行选择：

1. 选择所需的文章分类
2. 系统会自动加载该分类下的文章
3. 可以设置文章排序方式和显示数量

> **注意**：文章分类参数使用`category`字段，确保前后端一致

### 3. 文章排序选项

- **按排序**：按管理后台设置的排序值
- **发布时间↓**：按发布时间降序排列
- **发布时间↑**：按发布时间升序排列
- **阅读量**：按阅读量排序

## 四、短视频混合设置

### 1. 启用短视频混合

1. 勾选"混合短视频"启用短视频混合功能
2. 选择视频来源方式：
   - **手动选择**：手动添加短视频
   - **分组分类**：根据视频分类自动加载

### 2. 视频分类设置

可按视频分类自动加载视频内容：

1. 选择所需的视频分类
2. 设置视频数量
3. 若为平台账号可选择商家筛选

## 五、混合策略设置

### 1. 随机插入策略

当选择"随机插入"混合方式时：

- 可设置插入频率：
  - **低**：每7个商品插入一个混合内容
  - **中**：每5个商品插入一个混合内容
  - **高**：每3个商品插入一个混合内容
- 系统会根据频率设置随机插入文章或视频

### 2. 固定位置策略

当选择"固定位置"混合方式时：

- 在"插入位置"中设置具体位置，如：3,6,9
- 系统会在第3、6、9个位置插入文章或视频
- 多个位置用英文逗号分隔

## 六、前端显示效果

### 1. 双排布局效果

双排布局会将内容以两列形式排列，适合等宽内容展示：

```
┌─────────┐ ┌─────────┐
│ 商品1   │ │ 文章1   │
└─────────┘ └─────────┘
┌─────────┐ ┌─────────┐
│ 商品2   │ │ 视频1   │
└─────────┘ └─────────┘
┌─────────┐ ┌─────────┐
│ 商品3   │ │ 商品4   │
└─────────┘ └─────────┘
```

### 2. 瀑布流布局效果

瀑布流根据内容高度自动排列，适合高度不一的内容：

```
┌─────────┐ ┌─────────┐
│ 商品1   │ │ 文章1   │
└─────────┘ │         │
┌─────────┐ │         │
│ 商品2   │ └─────────┘
│         │ ┌─────────┐
└─────────┘ │ 视频1   │
┌─────────┐ │         │
│ 商品3   │ │         │
└─────────┘ └─────────┘
```

## 七、高级设置

### 1. 组件标题设置

可设置组件的标题相关内容：

- **主标题**：设置主标题文字和颜色
- **副标题**：设置副标题文字和颜色
- **更多文字**：设置"更多"链接的文字和颜色
- **更多链接**：设置"更多"链接的跳转地址

### 2. 商品显示设置

可自定义商品信息显示方式：

- **商品名称**：是否显示
- **商品价格**：显示方式（原价+销售价/仅销售价/不显示）
- **商品销量**：是否显示
- **购买按钮**：是否显示及样式

## 八、组件API参数说明

### 1. dp-product 组件参数

| 参数名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| params | Object | {} | 组件配置参数 |
| data | Array | [] | 商品数据 |
| mixedArticles | Array | [] | 文章数据 |
| mixedVideos | Array | [] | 短视频数据 |
| mixedMode | String | 'random' | 混合模式：'random'(随机插入)或'fixed'(固定位置) |
| mixedFrequency | String | 'medium' | 插入频率：'low'(低)、'medium'(中)、'high'(高) |
| mixedPositions | String | '3,6,9' | 固定位置插入时的位置，用逗号分隔 |

### 2. 混合行布局 dp-product-mixed-row 组件参数

| 参数名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| list | Array | - | 混合数据列表（必填） |
| saleimg | String | '' | 促销图标 |
| showname | Number | 1 | 是否显示名称：1(显示)、0(不显示) |
| showprice | String | '1' | 价格显示方式：'0'(不显示)、'1'(显示原价和售价)、'2'(仅显示售价) |
| showsales | String | '1' | 是否显示销量：'1'(显示)、'0'(不显示) |
| showcart | String | '1' | 是否显示购物车：'0'(不显示)、'1'(图标)、'2'(自定义图标) |
| params | Object | {} | 配置参数，包含标题等配置项 |

### 3. 混合瀑布流 dp-product-mixed-waterfall 组件参数

| 参数名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| list | Array | - | 混合数据列表（必填） |
| offset | Number | 8 | 间距(px) |
| cols | Number | 2 | 列数 |
| saleimg | String | '' | 促销图标 |
| showname | Number | 1 | 是否显示名称 |
| showprice | String | '1' | 价格显示方式 |
| showsales | String | '1' | 是否显示销量 |
| showcart | String | '1' | 是否显示购物车 |
| probgcolor | String | '#fff' | 商品背景色 |

## 九、实现原理

混合模式组件是基于现有商品组件扩展实现的，主要通过以下步骤：

1. **数据准备**：将商品数据、文章数据和视频数据合并处理
2. **内容标记**：为不同类型的内容添加类型标记（content_type）
3. **混合排序**：根据混合策略（随机/固定）对内容进行排序
4. **渲染展示**：根据内容类型使用不同的模板渲染内容

### 混合数据处理流程：

```
┌───────────┐    ┌───────────┐    ┌───────────┐
│  商品数据  │    │  文章数据  │    │  视频数据  │
└─────┬─────┘    └─────┬─────┘    └─────┬─────┘
      │                │                │
      └────────┬───────┴────────┬──────┘
               │                │
       ┌───────▼────────┐      ┌▼──────────────┐
       │  添加类型标记   │      │ 根据混合模式   │
       │ (content_type) │      │ 排序并插入     │
       └───────┬────────┘      └┬──────────────┘
               │                │
               └────────┬───────┘
                        │
                ┌───────▼────────┐
                │   渲染不同     │
                │  类型的内容    │
                └────────────────┘
```

## 十、常见问题解答

### 1. 混合模式数据不显示

问题：设置完混合模式后，前端不显示数据

解决方案：
- 点击"重新处理混合数据"按钮刷新数据
- 确认已正确选择文章分类或视频分类
- 检查分类下是否有可用内容
- 查看控制台是否有报错信息

### 2. 文章分类不生效

问题：选择文章分类后不加载文章

解决方案：
- 确认分类ID是否正确
- 检查该分类下是否有文章
- 确认文章状态是否为已发布

### 3. 混合排序问题

问题：混合内容排序不符合预期

解决方案：
- 随机插入模式下，每次刷新页面排序可能不同
- 固定位置模式下，检查位置设置是否正确
- 如需固定顺序，建议使用手动选择模式

### 4. 瀑布流图片加载问题

问题：瀑布流模式下图片排列混乱

解决方案：
- 确保图片有正确的尺寸信息
- 查看是否有图片加载失败
- 检查网络连接是否稳定

## 十一、最佳实践建议

1. **内容匹配**：选择与商品相关的文章和视频进行混合，提高内容关联性
2. **数量控制**：建议总内容量不超过20个，避免页面过长影响加载速度
3. **比例控制**：商品、文章、视频比例建议为6:3:1，保持内容多样性的同时突出商品展示
4. **图片质量**：确保所有图片清晰度一致，提升整体视觉效果
5. **定期更新**：定期更新内容，保持页面新鲜度
6. **优先使用固定位置**：为了保证更好的用户体验，建议使用固定位置混合模式，避免用户每次访问看到不同的内容排序

## 十二、性能优化

为确保混合模式组件的流畅运行，特别是在数据量较大时，可以采取以下优化措施：

1. **图片优化**：使用适当分辨率的图片，避免过大图片影响加载速度
2. **分页加载**：启用分页加载功能，避免一次性加载过多数据
3. **懒加载**：使用懒加载技术，仅加载可视区域内的图片
4. **预加载**：预加载下一页数据，提升翻页体验
5. **缓存策略**：缓存已加载的内容，减少重复请求

---

更多高级功能和API使用说明，请参考鲸犀商城开发者文档。 