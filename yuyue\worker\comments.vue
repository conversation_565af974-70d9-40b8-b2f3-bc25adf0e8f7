<template>
<view class="container">
	<view class="avg-score" v-if="avg_score">
		<view class="score-title">平均评分</view>
		<view class="score-value">{{avg_score}}</view>
		<view class="stars">
			<image class="star" v-for="(item,index) in 5" :key="index" :src="'/static/img/star' + (avg_score>=item?'2':'') + '.png'"/>
		</view>
	</view>
	<view class="comment">
		<view v-for="(item, index) in datalist" :key="index" class="item">
			<view class="f1">
				<image class="t1" :src="item.headimg"/>
				<view class="t2">{{item.nickname}}</view>
				<view class="flex1"></view>
				<view class="t3">
					<text class="comment-tag" :class="{
						'good': item.score >= 4,
						'middle': item.score == 3,
						'bad': item.score <= 2
					}">{{item.score >= 4 ? '好评' : (item.score == 3 ? '中评' : '差评')}}</text>
					<image class="img" v-for="(item2,index2) in [0,1,2,3,4]" :key="index2"  :src="'/static/img/star' + (item.score>item2?'2':'') + '.png'"/>
				</view>
			</view>
			<view style="color:#777;font-size:22rpx;">{{item.createtime}}</view>
			<view class="f2">
				<text class="t1">{{item.content}}</text>
				<view class="t2">
					<block v-if="item.content_pic && item.content_pic.length>0">
						<block v-for="(itemp, index) in item.content_pic" :key="index">
							<view @tap="previewImage" :data-url="itemp" :data-urls="item.content_pic">
								<image :src="itemp" mode="widthFix"/>
							</view>
						</block>
					</block>
				</view>
				<view class="t4" v-if="item.ordernum">
					<text>订单号：{{item.ordernum}}</text>
				</view>
				<view class="t4" v-if="item.title">
					<text>项目：{{item.title}}</text>
				</view>
			</view>
		</view>
  </view>
	
	<nomore v-if="nomore"></nomore>
	<nodata v-if="nodata" text="暂无评价~"></nodata>
	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
      datalist: [],
      pagenum: 1,
      pernum: 10, // 每页条数
      nomore: false,
      nodata: false,
			avg_score: null
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var pernum = that.pernum;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
      
      // 根据筛选类型设置评分范围参数
      var params = {
        pagenum: pagenum, 
        pernum: pernum
      };
      
      app.post('ApiYuyueWorker/getComments', params, function (res) {
				that.loading = false;
				console.log("评论数据返回:", res); // 添加日志查看返回数据
				if(res.status == 1){
          // 正确获取list列表
          var data = res.data && res.data.list ? res.data.list : [];
          // 获取总数
          var total = res.data && res.data.total_count ? parseInt(res.data.total_count) : 0;
          // 获取平均评分
          that.avg_score = res.data && res.data.avg_score ? res.data.avg_score : null;
          
          if (pagenum == 1) {
            that.datalist = data;
            if (data.length == 0) {
              that.nodata = true;
            }
            that.loaded();
          } else {
            if (data.length == 0) {
              that.nomore = true;
            } else {
              var datalist = that.datalist;
              var newdata = datalist.concat(data);
              that.datalist = newdata;
              
              // 判断是否已加载全部数据
              if (newdata.length >= total) {
                that.nomore = true;
              }
            }
          }
				} else {
          app.toast(res.msg || '加载失败');
          that.nodata = true;
				}
      });
    },
    
    // 图片预览
    previewImage: function(e) {
      var url = e.currentTarget.dataset.url;
      var urls = e.currentTarget.dataset.urls;
      uni.previewImage({
        current: url,
        urls: urls
      });
    },
    
    // 页面加载完成
    loaded: function(){
      this.isload = true;
      uni.stopPullDownRefresh();
    }
  }
};
</script>
<style>
.container{background:#f8f8f8; min-height: 100vh;}
.comment{display:flex;flex-direction:column;padding:10rpx 0;}
.comment .item{background-color:#fff;padding:20rpx;display:flex;flex-direction:column;margin-bottom:20rpx;}
.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}
.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}
.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}
.comment .item .f1 .flex1{flex:1;}
.comment .item .f1 .t3{text-align:right;}
.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}
.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}
.comment .item .f2 .t1{color:#333;font-size:28rpx;line-height:1.5;}
.comment .item .f2 .t2{display:flex;width:100%;flex-wrap:wrap;margin-top:10rpx;}
.comment .item .f2 .t2 image{width:160rpx;height:160rpx;margin:10rpx;border-radius:8rpx;}
.comment .item .f2 .t4{color:#aaa;font-size:24rpx;margin-top:10rpx;}

.avg-score{display:flex;flex-direction:column;align-items:center;background:#fff;padding:30rpx;margin-bottom:20rpx;}
.avg-score .score-title{font-size:28rpx;color:#666;}
.avg-score .score-value{font-size:48rpx;color:#333;font-weight:bold;margin:10rpx 0;}
.avg-score .stars{display:flex;margin-top:10rpx;}
.avg-score .stars .star{width:32rpx;height:32rpx;margin:0 5rpx;}

.comment-tag {
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 5rpx;
  margin-right: 10rpx;
}

.good {
  background-color: #06A051;
  color: #fff;
}

.middle {
  background-color: #FFA500;
  color: #fff;
}

.bad {
  background-color: #FF0000;
  color: #fff;
}
</style> 