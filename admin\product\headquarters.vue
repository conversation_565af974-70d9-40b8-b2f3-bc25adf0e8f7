<template>
  <view class="container">
    <view class="header-title">
      <text>总部商品同步</text>
    </view>
    
    <view class="search-box">
      <view class="search-bar">
        <input type="text" v-model="keyword" placeholder="搜索商品名称" @confirm="searchProducts"/>
        <view class="search-btn" @tap="searchProducts">
          <image src="/static/img/search.png" class="search-icon"></image>
        </view>
      </view>
      
      <view class="filter-box">
        <view class="filter-item">
          <picker @change="onCategoryChange" :value="categoryIndex" :range="categoryOptions" range-key="name">
            <view class="picker-content">
              <text>分类: {{categoryIndex > 0 ? categoryOptions[categoryIndex].name : '全部'}}</text>
              <image src="/static/img/dropdown.png"></image>
            </view>
          </picker>
        </view>
        
        <view class="filter-item">
          <picker @change="onGroupChange" :value="groupIndex" :range="groupOptions" range-key="name">
            <view class="picker-content">
              <text>分组: {{groupIndex > 0 ? groupOptions[groupIndex].name : '全部'}}</text>
              <image src="/static/img/dropdown.png"></image>
            </view>
          </picker>
        </view>
        
        <view class="filter-item">
          <picker @change="onStatusChange" :value="statusIndex" :range="statusOptions" range-key="name">
            <view class="picker-content">
              <text>状态: {{statusOptions[statusIndex].name}}</text>
              <image src="/static/img/dropdown.png"></image>
            </view>
          </picker>
        </view>
      </view>
    </view>
    
    <view class="product-list">
      <view v-if="productList.length === 0 && !loading" class="empty-tip">
        <image src="/static/img/empty.png" class="empty-img"></image>
        <text>暂无商品</text>
      </view>
      
      <view v-for="(item, index) in productList" :key="item.id" class="product-item" :class="{'imported': item.is_imported}">
        <view class="checkbox-wrapper" @tap="toggleSelect(item.id)" :class="{'disabled': item.is_imported}">
          <view class="custom-checkbox" :class="{'checked': selectedProducts.includes(item.id), 'disabled': item.is_imported}">
            <view v-if="selectedProducts.includes(item.id)" class="checkbox-inner"></view>
          </view>
        </view>
        
        <view class="product-info">
          <image :src="item.pic" class="product-image" mode="aspectFill"></image>
          <view class="product-detail">
            <view class="product-name">{{item.name}}</view>
            <view class="product-category">分类: {{item.cname}}</view>
            <view class="product-price">
              <text class="sell-price">￥{{item.sell_price}}</text>
              <text class="market-price">￥{{item.market_price}}</text>
            </view>
            <view class="product-status">
              <text :class="['status-tag', item.status == 1 ? 'status-on' : 'status-off']">
                {{item.status == 1 ? '上架' : '下架'}}
              </text>
              <text v-if="item.is_imported" class="imported-tag">已导入</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view v-if="productList.length > 0" class="pagination">
      <view class="btn prev" @tap="prevPage" :class="{disabled: pagenum <= 1}">上一页</view>
      <text class="page-info">{{pagenum}}/{{Math.ceil(total/pernum)}}</text>
      <view class="btn next" @tap="nextPage" :class="{disabled: pagenum >= Math.ceil(total/pernum)}">下一页</view>
    </view>
    
    <view class="footer-action" :class="{'has-selected': selectedProducts.length > 0}">
      <view class="select-all" @tap="toggleSelectAll">
        <view class="custom-checkbox" :class="{'checked': isAllSelected}">
          <view v-if="isAllSelected" class="checkbox-inner"></view>
        </view>
        <text>全选</text>
      </view>
      <view class="selected-count">已选择 <text class="count-num">{{selectedProducts.length}}</text> 个商品</view>
      <view class="import-btn" @tap="importProducts" :class="{'disabled': selectedProducts.length === 0}">导入选中商品</view>
    </view>
    
    <!-- 加载中提示 -->
    <view class="loading-overlay" v-if="loading">
      <view class="loading-content">
        <image src="/static/img/loading.gif" class="loading-icon"></image>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
    
    <!-- 导入成功弹窗 -->
    <view class="success-popup" v-if="showSuccessPopup">
      <view class="success-content">
        <image src="/static/img/success.png" class="success-icon"></image>
        <text class="success-title">导入成功</text>
        <text class="success-message">{{successMsg}}</text>
        <view class="success-btn" @tap="closeSuccessPopup">确定</view>
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      loading: false,
      keyword: '',
      categoryOptions: [{id: 0, name: '全部'}],
      groupOptions: [{id: 0, name: '全部'}],
      statusOptions: [
        {id: '', name: '全部'},
        {id: '1', name: '上架'},
        {id: '0', name: '下架'}
      ],
      categoryIndex: 0,
      groupIndex: 0,
      statusIndex: 0,
      
      productList: [],
      pagenum: 1,
      pernum: 10,
      total: 0,
      
      selectedProducts: [],
      isAllSelected: false,
      
      // 成功提示弹窗相关
      showSuccessPopup: false,
      successMsg: ''
    }
  },
  
  onLoad() {
    this.getCategories();
    this.getProductList();
  },
  
  onPullDownRefresh() {
    this.pagenum = 1;
    this.getProductList();
  },
  
  methods: {
    // 获取分类和分组
    getCategories() {
      const that = this;
      that.loading = true;
      
      app.post('ApiAdminProduct/getProductCategories', {}, function(res) {
        that.loading = false;
        if (res.status === 1) {
          // 处理分类数据
          const categories = [{id: 0, name: '全部'}];
          res.data.categories.forEach(category => {
            categories.push({id: category.id, name: category.name});
            if (category.child && category.child.length > 0) {
              category.child.forEach(subCategory => {
                categories.push({id: subCategory.id, name: `-- ${subCategory.name}`});
              });
            }
          });
          that.categoryOptions = categories;
          
          // 处理分组数据
          const groups = [{id: 0, name: '全部'}];
          res.data.groups.forEach(group => {
            groups.push({id: group.id, name: group.name});
          });
          that.groupOptions = groups;
        } else {
          app.toast(res.msg);
        }
      });
    },
    
    // 获取总部商品列表
    getProductList() {
      const that = this;
      that.loading = true;
      
      const params = {
        pagenum: that.pagenum,
        pernum: that.pernum,
        keyword: that.keyword,
        cid: that.categoryOptions[that.categoryIndex].id,
        gid: that.groupOptions[that.groupIndex].id,
        status: that.statusOptions[that.statusIndex].id
      };
      
      app.post('ApiAdminProduct/getHeadquartersProducts', params, function(res) {
        that.loading = false;
        uni.stopPullDownRefresh();
        
        if (res.status === 1) {
          that.productList = res.data;
          that.total = res.count;
          
          // 检查当前选中的商品是否还在列表中
          that.selectedProducts = that.selectedProducts.filter(id => 
            that.productList.some(product => product.id === id && !product.is_imported)
          );
          
          that.updateSelectAllStatus();
        } else {
          app.toast(res.msg);
        }
      });
    },
    
    // 导入选中的商品
    importProducts() {
      if (this.selectedProducts.length === 0) {
        app.toast('请选择要导入的商品');
        return;
      }
      
      const that = this;
      that.loading = true;
      
      app.post('ApiAdminProduct/importHeadquartersProducts', {
        productIds: that.selectedProducts
      }, function(res) {
        that.loading = false;
        
        if (res.status === 1) {
          // 设置成功信息并显示弹窗
          that.successMsg = res.msg;
          that.showSuccessPopup = true;
          
          that.selectedProducts = [];
          that.getProductList(); // 刷新列表
        } else {
          app.toast(res.msg);
        }
      });
    },
    
    // 关闭成功提示弹窗
    closeSuccessPopup() {
      this.showSuccessPopup = false;
    },
    
    // 搜索商品
    searchProducts() {
      this.pagenum = 1;
      this.getProductList();
    },
    
    // 分类选择
    onCategoryChange(e) {
      this.categoryIndex = e.detail.value;
      this.pagenum = 1;
      this.getProductList();
    },
    
    // 分组选择
    onGroupChange(e) {
      this.groupIndex = e.detail.value;
      this.pagenum = 1;
      this.getProductList();
    },
    
    // 状态选择
    onStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.pagenum = 1;
      this.getProductList();
    },
    
    // 上一页
    prevPage() {
      if (this.pagenum > 1) {
        this.pagenum--;
        this.getProductList();
      }
    },
    
    // 下一页
    nextPage() {
      if (this.pagenum < Math.ceil(this.total / this.pernum)) {
        this.pagenum++;
        this.getProductList();
      }
    },
    
    // 选择/取消选择单个商品
    toggleSelect(id) {
      // 检查商品是否已导入
      const product = this.productList.find(item => item.id === id);
      if (product && product.is_imported) {
        return; // 已导入的商品不允许选择
      }
      
      const index = this.selectedProducts.indexOf(id);
      if (index === -1) {
        this.selectedProducts.push(id);
      } else {
        this.selectedProducts.splice(index, 1);
      }
      
      this.updateSelectAllStatus();
    },
    
    // 选择/取消选择所有商品
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedProducts = [];
      } else {
        this.selectedProducts = this.productList
          .filter(item => !item.is_imported)
          .map(item => item.id);
      }
      
      this.updateSelectAllStatus();
    },
    
    // 更新全选状态
    updateSelectAllStatus() {
      const availableProducts = this.productList.filter(item => !item.is_imported);
      this.isAllSelected = 
        availableProducts.length > 0 && 
        this.selectedProducts.length === availableProducts.length;
    }
  }
}
</script>

<style>
.container {
  padding: 0 0 120rpx 0;
  background-color: #f7f7f7;
  min-height: 100vh;
  position: relative;
}

.header-title {
  background-color: #fff;
  padding: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-box {
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-bar {
  display: flex;
  align-items: center;
  border: 1px solid #eee;
  border-radius: 40rpx;
  padding: 0 20rpx;
  overflow: hidden;
  background-color: #f9f9f9;
}

.search-bar input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #FC5648 0%, rgba(252, 86, 72, 0.8) 100%);
  border-radius: 50%;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
}

.filter-box {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.filter-item {
  width: 50%;
  padding: 10rpx 0;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f9f9f9;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.picker-content image {
  width: 24rpx;
  height: 24rpx;
}

.product-list {
  margin: 20rpx;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.empty-img {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-tip text {
  color: #999;
  font-size: 28rpx;
}

.product-item {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.product-item.imported {
  opacity: 0.7;
}

.product-item.imported::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  pointer-events: none;
}

.checkbox-wrapper {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-wrapper.disabled {
  opacity: 0.5;
}

.custom-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.custom-checkbox.checked {
  border-color: #FC5648;
  background-color: #FC5648;
}

.custom-checkbox.disabled {
  background-color: #f5f5f5;
  border-color: #ddd;
}

.checkbox-inner {
  width: 16rpx;
  height: 16rpx;
  background-color: #fff;
  border-radius: 50%;
}

.product-info {
  flex: 1;
  display: flex;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.product-detail {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  font-weight: bold;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.product-category {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.product-price {
  margin-bottom: 10rpx;
}

.sell-price {
  font-size: 32rpx;
  color: #FC5648;
  font-weight: bold;
}

.market-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.product-status {
  display: flex;
  align-items: center;
}

.status-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.status-on {
  background-color: #e6f7ee;
  color: #52c41a;
}

.status-off {
  background-color: #f5f5f5;
  color: #999;
}

.imported-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: #f0f8ff;
  color: #1890ff;
  border-radius: 6rpx;
  margin-left: 10rpx;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30rpx 0;
}

.btn {
  padding: 10rpx 30rpx;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #333;
}

.btn.disabled {
  color: #999;
  background-color: #f5f5f5;
}

.page-info {
  margin: 0 20rpx;
  font-size: 26rpx;
  color: #666;
}

.footer-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  border-top: 1px solid #eee;
  z-index: 10;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.3s;
}

.footer-action.has-selected {
  transform: translateY(0);
}

.select-all {
  display: flex;
  align-items: center;
}

.select-all text {
  margin-left: 10rpx;
  font-size: 26rpx;
  color: #333;
}

.selected-count {
  flex: 1;
  margin-left: 30rpx;
  font-size: 26rpx;
  color: #666;
}

.count-num {
  color: #FC5648;
  font-weight: bold;
  font-size: 30rpx;
}

.import-btn {
  width: 240rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  color: #fff;
  background: linear-gradient(90deg, #FC5648 0%, rgba(252, 86, 72, 0.8) 100%);
  border-radius: 35rpx;
  font-size: 28rpx;
  box-shadow: 0 5rpx 10rpx rgba(252, 86, 72, 0.2);
  transition: all 0.3s;
}

.import-btn.disabled {
  opacity: 0.6;
  background: linear-gradient(90deg, #ccc 0%, #ddd 100%);
  box-shadow: none;
}

.import-btn:active {
  transform: scale(0.95);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-content {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 成功弹窗 */
.success-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.success-content {
  width: 560rpx;
  background-color: #fff;
  padding: 50rpx 40rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeInUp 0.3s;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.success-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.success-btn {
  width: 400rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(90deg, #FC5648 0%, rgba(252, 86, 72, 0.8) 100%);
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 