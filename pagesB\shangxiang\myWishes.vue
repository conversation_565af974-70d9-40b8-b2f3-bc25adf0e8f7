<template>
	<view class="container">
		<block v-if="isload">
			<!-- 统计数据 -->
			<view class="stats-section">
				<view class="stats-item">
					<text class="stats-number" style="color: #FFD700">{{statistics.total_count || 0}}</text>
					<text class="stats-label">总次数</text>
				</view>
				<view class="stats-item">
					<text class="stats-number" style="color: #FFD700">{{statistics.total_amount || 0}}</text>
					<text class="stats-label">总金额</text>
				</view>
				<view class="stats-item">
					<text class="stats-number" style="color: #FFD700">{{statistics.wish_count || 0}}</text>
					<text class="stats-label">许愿次数</text>
				</view>
			</view>
			
			<!-- 类型筛选 -->
			<view class="filter-tabs">
				<view v-for="(tab, index) in filterTabs" 
					  :key="index" 
					  class="filter-tab" 
					  :class="{active: currentTab === index}"
					  @tap="switchTab" 
					  :data-index="index">
					<text>{{tab.name}}</text>
				</view>
			</view>
			
			<!-- 我的记录列表 -->
			<view class="my-wishes-list">
				<view v-for="(item, index) in datalist" :key="index" class="wish-record">
					<view class="record-header">
						<text class="type-badge" :style="{backgroundColor: getTypeColor(item.type)}">{{getTypeName(item.type)}}</text>
						<text class="amount" style="color: #FFD700">￥{{item.amount}}</text>
						<text class="time">{{formatTime(item.create_time)}}</text>
					</view>
					
					<view class="record-content" v-if="item.wish_content">
						<text>{{item.wish_content}}</text>
					</view>
					
					<!-- <view class="record-footer">
						<view class="actions">
							<button class="action-btn edit-btn" 
									v-if="item.type === 'xuyuan'"
									@tap="editWish" 
									:data-id="item.id" 
									:data-content="item.wish_content">编辑</button>
						</view>
					</view> -->
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-if="!loading && datalist.length === 0" class="empty-state">
				<image class="empty-icon" :src="pre_url+'/static/img/empty-my-wishes.png'"/>
				<text class="empty-text">暂无记录</text>
			</view>
			
			<!-- 编辑许愿弹窗 -->
			<view v-if="showEditModal" class="modal-overlay" @tap="closeEditModal">
				<view class="modal-content" @tap.stop>
					<view class="modal-header">
						<text class="modal-title">编辑许愿内容</text>
						<text class="close-btn" @tap="closeEditModal">×</text>
					</view>
					
					<view class="modal-body">
						<textarea class="edit-textarea" 
								  placeholder="请输入许愿内容" 
								  v-model="editContent" 
								  maxlength="200"></textarea>
						<text class="char-count">{{editContent.length}}/200</text>
					</view>
					
					<view class="modal-footer">
						<button class="cancel-btn" @tap="closeEditModal">取消</button>
						<button class="confirm-btn" 
								style="background: linear-gradient(90deg, #FFD700 0%, rgba(255, 215, 0, 0.8) 100%)"
								@tap="saveEdit">保存</button>
					</view>
				</view>
			</view>
		</block>
		
		<nodata v-if="nodata"></nodata>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			isload: false,
			nodata: false,
			statistics: {},
			datalist: [],
			filterTabs: [
				{name: '全部', type: ''},
				{name: '供花', type: 'gonghua'},
				{name: '供果', type: 'gongguo'},
				{name: '上香', type: 'shangxiang'},
				{name: '许愿', type: 'xuyuan'}
			],
			currentTab: 0,
			page: 1,
			limit: 20,
			hasMore: true,
			loading: false,
			loadingMore: false,
			showEditModal: false,
			editContent: '',
			editId: 0,
			pre_url: '',
			textset: {}
		}
	},
	
	onLoad: function(opt) {
		this.opt = app.getopts(opt);
		this.pre_url = app.globalData.pre_url;
		this.getStatistics();
		this.getdata();
	},
	
	onPullDownRefresh: function() {
		this.page = 1;
		this.hasMore = true;
		this.getStatistics();
		this.getdata();
	},
	
	onReachBottom: function() {
		if (this.hasMore && !this.loading) {
			this.page++;
			this.getdata();
		}
	},
	
	methods: {
		// 获取统计数据
		getStatistics() {
			var that = this;
			app.post('ApiShangxiang/getMyStatistics', {}, function(res) {
				if (res.code === 1) {
					that.statistics = res.data;
				}
			});
		},
		
		// 获取数据
		getdata() {
			var that = this;
			if (that.loading) return;
			that.loading = true;
			
			const params = {
				page: that.page,
				limit: that.limit,
				type: that.filterTabs[that.currentTab].type
			};
			
			app.post('ApiShangxiang/getMyWishList', params, function(res) {
				that.loading = false;
				if (res.code === 1) {
					const newList = res.data.list || [];
					if (that.page === 1) {
						that.datalist = newList;
					} else {
						that.datalist = that.datalist.concat(newList);
					}
					that.hasMore = newList.length >= that.limit;
					that.loaded();
				} else {
					app.error(res.msg || '获取记录失败');
				}
				uni.stopPullDownRefresh();
			});
		},
		
		// 切换标签
		switchTab(e) {
			const index = e.currentTarget.dataset.index;
			this.currentTab = index;
			this.page = 1;
			this.hasMore = true;
			this.getdata();
		},
		
		// 编辑许愿
		editWish(e) {
			const id = e.currentTarget.dataset.id;
			const content = e.currentTarget.dataset.content;
			this.editId = id;
			this.editContent = content || '';
			this.showEditModal = true;
		},
		
		// 关闭编辑弹窗
		closeEditModal() {
			this.showEditModal = false;
			this.editContent = '';
			this.editId = 0;
		},
		
		// 保存编辑
		saveEdit() {
			var that = this;
			if (!that.editContent.trim()) {
				app.error('请输入许愿内容');
				return;
			}
			
			that.loading = true;
			app.post('ApiShangxiang/updateWish', {
				id: that.editId,
				wish_content: that.editContent
			}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					app.success('修改成功');
					that.closeEditModal();
					// 更新列表中的数据
					const item = that.datalist.find(item => item.id === that.editId);
					if (item) {
						item.wish_content = that.editContent;
					}
				} else {
					app.error(res.msg || '修改失败');
				}
			});
		},
		
		// 获取类型名称
		getTypeName(type) {
			switch (type) {
				case 'gonghua':
					return '供花';
				case 'gongguo':
					return '供果';
				case 'shangxiang':
					return '上香';
				case 'xuyuan':
					return '许愿';
				default:
					return '';
			}
		},
		
		// 获取类型颜色
		getTypeColor(type) {
			switch (type) {
				case 'gonghua':
					return '#B8860B';
				case 'gongguo':
					return '#DAA520';
				case 'shangxiang':
					return '#FFD700';
				case 'xuyuan':
					return '#FFA500';
				default:
					return '#8B7355';
			}
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return '';
			const date = new Date(time * 1000); // 假设time是时间戳（秒）
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			
			const now = new Date();
			const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
			const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
			const dateOnly = new Date(year, date.getMonth(), date.getDate());
			
			if (dateOnly.getTime() === today.getTime()) {
				return `今天 ${hours}:${minutes}`;
			} else if (dateOnly.getTime() === yesterday.getTime()) {
				return `昨天 ${hours}:${minutes}`;
			} else if (year === now.getFullYear()) {
				return `${month}-${day} ${hours}:${minutes}`;
			} else {
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			}
		},
		
		// 页面加载完成
		loaded() {
			var that = this;
			that.isload = true;
			that.textset = app.globalData.textset;
			uni.setNavigationBarTitle({
				title: '我的许愿记录'
			});
		}
	}
}
</script>

<style>
.container {
	background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
	min-height: 100vh;
}

.stats-section {
	display: flex;
	background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
	margin: 20rpx;
	border-radius: 15rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(255, 215, 0, 0.15);
	border: 1rpx solid rgba(255, 215, 0, 0.2);
}

.stats-item {
	flex: 1;
	text-align: center;
}

.stats-number {
	font-size: 36rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #C0C0C0;
	display: block;
}

.filter-tabs {
	display: flex;
	background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
	margin: 0 20rpx 20rpx;
	border-radius: 15rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 32rpx rgba(255, 215, 0, 0.15);
	border: 1rpx solid rgba(255, 215, 0, 0.2);
}

.filter-tab {
	flex: 1;
	text-align: center;
	padding: 15rpx 0;
	font-size: 26rpx;
	color: #C0C0C0;
	border-radius: 8rpx;
	transition: all 0.3s ease;
}

.filter-tab.active {
	color: #FFD700;
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
	font-weight: bold;
	border: 1rpx solid rgba(255, 215, 0, 0.3);
}

.my-wishes-list {
	padding: 0 20rpx 40rpx;
}

.wish-record {
	background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
	border-radius: 15rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 8rpx 32rpx rgba(255, 215, 0, 0.1);
	border: 1rpx solid rgba(255, 215, 0, 0.15);
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.type-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	color: #fff;
	font-size: 22rpx;
	margin-right: 20rpx;
}

.amount {
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 20rpx;
}

.time {
	font-size: 22rpx;
	color: #A0A0A0;
}

.record-content {
	padding: 16rpx;
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 215, 0, 0.02) 100%);
	border-radius: 10rpx;
	margin-bottom: 16rpx;
	font-size: 26rpx;
	line-height: 1.5;
	color: #E0E0E0;
	border: 1rpx solid rgba(255, 215, 0, 0.1);
}

.record-footer {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding-top: 10rpx;
}

.actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	border: none;
	background: none;
}

.edit-btn {
	color: #FFD700;
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
	border: 1rpx solid rgba(255, 215, 0, 0.3);
}

.empty-state {
	text-align: center;
	padding: 100rpx 0;
}

.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #A0A0A0;
	display: block;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-content {
	background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
	border-radius: 20rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	border: 2rpx solid rgba(255, 215, 0, 0.3);
	box-shadow: 0 16rpx 64rpx rgba(255, 215, 0, 0.2);
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid rgba(255, 215, 0, 0.2);
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #FFD700;
}

.close-btn {
	font-size: 40rpx;
	color: #C0C0C0;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-body {
	padding: 30rpx;
}

.edit-textarea {
	width: 100%;
	height: 200rpx;
	border: 1rpx solid rgba(255, 215, 0, 0.3);
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 26rpx;
	line-height: 1.5;
	resize: none;
	box-sizing: border-box;
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 215, 0, 0.02) 100%);
	color: #E0E0E0;
}

.char-count {
	font-size: 22rpx;
	color: #A0A0A0;
	text-align: right;
	margin-top: 10rpx;
	display: block;
}

.modal-footer {
	display: flex;
	padding: 30rpx;
	border-top: 1rpx solid rgba(255, 215, 0, 0.2);
	gap: 20rpx;
}

.cancel-btn {
	flex: 1;
	padding: 24rpx 0;
	border: 1rpx solid rgba(255, 215, 0, 0.3);
	border-radius: 10rpx;
	background: transparent;
	color: #C0C0C0;
	font-size: 28rpx;
	text-align: center;
}

.confirm-btn {
	flex: 1;
	padding: 24rpx 0;
	border: none;
	border-radius: 10rpx;
	color: #1a1a1a;
	font-size: 28rpx;
	text-align: center;
	font-weight: bold;
}
</style>
