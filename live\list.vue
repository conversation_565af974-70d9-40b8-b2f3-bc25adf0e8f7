<template>
	
	<view>
		
		<view style="padding: 20px;border-bottom: 1px solid #ccc;" v-for="(item, index) in slist" :key="index" @click="goItem(item)">
			{{item.name}}
		</view>
		
	</view>

	
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				opt: {},
				slist : []
			};
		},
		onLoad: function (opt) {
			let that = this;
			that.opt = app.getopts(opt);
			var sysinfo = uni.getSystemInfoSync();
			that.getdata();
		},
	
		onPullDownRefresh: function (e) {
			this.getdata();
		},
		onPageScroll: function (e) {
			uni.$emit('onPageScroll', e);
		},
		methods: {
			
			goItem(item){
				uni.navigateTo({
					url:'./live?id='+item.id
				})
			},
			
			getdata() {
				let that = this
				app.get('ApiLiveWeb/getList', {pagenum: 1}, function (data) {
					console.log(data);

					if (data.status == 1) {
                         that.slist = data.data;
					} else {
						if (data.msg) {
							app.alert(data.msg, function () {
								if (data.url) app.goto(data.url);
							});
						} else if (data.url) {
							app.goto(data.url);
						} else {
							app.alert('您无查看权限');
						}
					}
				});
				
			}
		}
		
	}
	
</script>

<style>
</style>