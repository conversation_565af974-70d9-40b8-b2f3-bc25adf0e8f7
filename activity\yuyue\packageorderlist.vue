<template>
	<view class="container">
		<!-- Tab切换 -->
		<view class="tabs">
			<view v-for="(tab, index) in tabs" :key="index"
				class="tab-item" :class="{active: currentTab === index}"
				:style="currentTab === index && themeColor ? 'color:' + themeColor + ';border-bottom-color:' + themeColor : ''"
				@tap="switchTab(index)">
				{{ tab.name }}
			</view>
		</view>

		<!-- 订单列表 -->
		<view class="order-list">
			<view v-if="orderList.length > 0">
				<view class="order-item" v-for="(order, index) in orderList" :key="index" @tap="gotoDetail(order.id)">
					<view class="item-header">
						<text class="order-time">{{ order.createtime }}</text>
						<text class="order-status" :style="{color: getStatusColor(order.status)}">{{ order.status_text }}</text>
					</view>
					<view class="item-content">
						<image class="package-pic-list" :src="order.package_pic" mode="aspectFill"></image>
						<view class="package-info-list">
							<view class="package-name-list">{{ order.package_name }}</view>
							<view class="package-services">
								<text>总次数: {{ order.total_services }}</text>
								<text class="remain">剩余: {{ order.remain_services }}</text>
							</view>
							<view class="package-expire" v-if="order.expires_time_format">有效期至: {{ order.expires_time_format }}</view>
						</view>
					</view>
					<view class="item-footer">
						<view class="total-price-list">实付: <text :style="{color: themeColor}">￥{{ order.total_price }}</text></view>
						<view class="item-actions">
							<button v-if="order.status === 0" class="action-btn plain" @tap.stop="cancelOrder(order.id, index)">取消订单</button>
							<button v-if="order.status === 0" class="action-btn primary" :style="{background: themeColor}" @tap.stop="gotoPay(order.payorderid, order.id)">去支付</button>
							<button v-if="canUse(order)" class="action-btn primary" :style="{background: themeColor}" @tap.stop="gotoDetail(order.id)">去使用</button>
							<button v-if="canRefund(order)" class="action-btn plain" @tap.stop="applyRefund(order.id)">申请退款</button>
							<!-- <button v-if="order.status === 3" class="action-btn plain disabled">已完成</button>
							<button v-if="order.status === 4" class="action-btn plain disabled">退款中</button> -->
							<button v-if="order.status === 2 || order.status > 4" class="action-btn plain" @tap.stop="deleteOrder(order.id, index)">删除</button>
						</view>
					</view>
				</view>
				<view class="loading-tip" v-if="loading">加载中...</view>
				<view class="nodata-tip" v-if="nodata">没有更多订单了~</view>
			</view>
			<view class="empty-tip list-empty" v-else-if="!loading">
				<image src="/static/img/nodata.png" class="empty-img" mode="widthFix"></image>
				<text>暂无相关订单</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		// 避免直接引用可能未初始化的全局app对象
		return {
			themeColor: '', // 主题色，将在onLoad中正确初始化
			tabs: [
				{ name: '全部', status: -1 },
				{ name: '待支付', status: 0 },
				{ name: '可使用', status: 1 }, // 接口定义1为已支付，包含多种子状态，前端统称可使用
				{ name: '已用完', status: 100 }, // 自定义状态码，用于前端标识已用完的套餐
				{ name: '已失效', status: 2 } // 接口定义2为已取消/已失效/已退款等终结状态
			],
			currentTab: 0,
			status: -1, // 默认请求全部
			orderList: [],
			page: 1,
			limit: 10,
			total: 0,
			loading: false,
			nodata: false,
			triggered: false, // 下拉刷新状态
			$baseUrl: '' // 添加 $baseUrl 属性
		}
	},
	onLoad(options) {
		// 初始化全局app对象
		const app = getApp();
		if (app && app.globalData && app.globalData.config && app.globalData.config.t) {
			this.themeColor = app.globalData.config.t('color1');
		} else {
			// 如果未能获取主题色，设置一个默认值
			this.themeColor = '#2979ff';
			console.warn('2025-01-03 22:55:53,565-INFO-[packageorderlist][onLoad_001] 无法获取主题色配置，使用默认值');
		}
		
		// 根据传入的 status 定位到对应的 tab
		let initialStatus = -1;
		if (options && options.status !== undefined) {
			initialStatus = parseInt(options.status);
		}
		
		// 使用已在data初始化的tabs数组
		const initialTabIndex = this.tabs.findIndex(tab => tab.status === initialStatus);
		this.currentTab = initialTabIndex >= 0 ? initialTabIndex : 0;
		this.status = this.tabs[this.currentTab].status;

		this.getOrderList(true);
		uni.setNavigationBarTitle({
			title: '我的套餐订单'
		});
		
		console.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][onLoad_002] 组件初始化完成，当前状态:', this.status);
	},
	// onShow() {
	// 	// 如果需要每次进入都刷新
	// 	this.getOrderList(true);
	// },
	onReachBottom() {
		if (this.loading || this.nodata) {
			return;
		}
		this.page++;
		this.getOrderList();
	},
	// uni-app下拉刷新
	onPullDownRefresh(){
		if(this.triggered) return;
		this.triggered = true;
		this.page = 1;
		this.nodata = false;
		this.getOrderList(true, () => {
			uni.stopPullDownRefresh();
			this.triggered = false;
		});
	},
	methods: {
		switchTab(index) {
			if (this.currentTab === index) {
				return;
			}
			this.currentTab = index;
			this.status = this.tabs[index].status;
			this.page = 1;
			this.nodata = false;
			this.orderList = []; // 清空列表
			this.getOrderList(true);
		},
		getOrderList(reload = false, callback) {
			var that = this;
			if (that.loading || (that.nodata && !reload)) {
				if (callback) callback();
				return;
			}
			that.loading = true;
			if (reload) {
				that.page = 1;
				that.orderList = [];
				that.nodata = false;
			}

			const app = getApp();
			if (!app || !app.post) {
				console.error('2025-01-03 22:55:53,565-ERROR-[packageorderlist][getOrderList_001] app对象未初始化或post方法不存在');
				that.loading = false;
				if (callback) callback();
				return;
			}

			// 针对"已用完"特殊状态，需要请求已支付的订单后再过滤
			const requestStatus = that.status === 100 ? 1 : that.status;
			
			app.post('ApiYuyuePackage/getUserPackages', {
				page: that.page,
				limit: that.limit,
				status: requestStatus
			}, function(res) {
				that.loading = false;
				if (res.status == 1) {
					const data = res.data || [];
					
					// 处理每条订单数据，修复过期时间计算问题
					const processedData = data.map(order => {
						// 对已支付状态的订单(status=1)处理
						if (order.status === 1) {
							// 获取当前时间戳（秒）
							const now = Math.floor(Date.now()/1000);
							
							// 检查expires_time是否明显错误（为0，或者小于now但订单状态是1，或者日期太早如1971年）
							const isExpireTimeInvalid = 
								order.expires_time === 0 || 
								(order.expires_time < now && order.expires_time < 1672531200); // 2023-01-01的时间戳
							
							// 支付后但expires_time无效的情况，说明后端未设置过期时间或计算错误
							if (isExpireTimeInvalid && order.pay_time) {
								console.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_004] 订单' + order.id + '检测到无效的过期时间:', order.expires_time);
								
								// 从支付时间计算过期时间
								const payTimeStamp = new Date(order.pay_time.replace(/-/g, '/')).getTime();
								const validDays = order.valid_days || 365; // 默认365天
								order.expires_time = Math.floor(payTimeStamp/1000) + (validDays * 86400);
								
								console.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_005] 订单' + order.id + '自动计算新的过期时间:', {
									payTime: order.pay_time, 
									payTimeStamp: Math.floor(payTimeStamp/1000),
									validDays: validDays,
									newExpiresTime: order.expires_time,
									currentTime: now
								});
							}
							
							// 格式化过期时间显示(独立于前面的逻辑，确保始终有格式化显示)
							if (order.expires_time > 0) {
								const expireDate = new Date(order.expires_time * 1000);
								order.expires_time_format = expireDate.getFullYear() + '-' + 
									String(expireDate.getMonth() + 1).padStart(2, '0') + '-' + 
									String(expireDate.getDate()).padStart(2, '0');
							}
							
							// 重新判断状态文本
							if (order.expires_time > 0 && now > order.expires_time) {
								order.status_text = '已过期';
								console.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_006] 订单' + order.id + '已过期，当前时间:', now, '过期时间:', order.expires_time);
							} else if (order.remain_services <= 0) {
								order.status_text = '已用完';
							} else {
								order.status_text = '可使用';
							}
						}
						
						// 修复图片URL
						if (order.package_pic && order.package_pic.indexOf('https://localhost') === 0) {
							order.package_pic = order.package_pic.replace('https://localhost', '');
						}
						
						// 双重URL问题修复 (添加这一段以与详情页保持一致)
						if (order.package_pic && order.package_pic.indexOf('https://localhosthttps://') === 0) {
							order.package_pic = order.package_pic.replace('https://localhost', '');
						}
						
						return order;
					});
					
					// 根据不同标签进行数据过滤
					let filteredData = processedData;
					
					// "已用完"标签：只保留已用完的订单
					if (that.status === 100) {
						filteredData = processedData.filter(order => 
							order.status === 1 && 
							(order.remain_services <= 0 || order.status_text === '已用完')
						);
						console.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_007] 过滤已用完套餐，原数量:', processedData.length, '过滤后数量:', filteredData.length);
					}
					// "可使用"标签：只保留真正可用的订单（剩余次数>0且未过期）
					else if (that.status === 1) {
						filteredData = processedData.filter(order => 
							order.status === 1 && 
							order.remain_services > 0 && 
							order.status_text === '可使用'
						);
						console.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_008] 过滤真正可使用套餐，原数量:', processedData.length, '过滤后数量:', filteredData.length);
					}
					
					if (that.page === 1) {
						that.orderList = filteredData;
					} else {
						that.orderList = that.orderList.concat(filteredData);
					}
					// 对于需要前端过滤的状态（已用完、可使用），total应该是过滤后的总数
					if (that.status === 100 || that.status === 1) {
						that.total = filteredData.length;
						if (filteredData.length < that.limit) {
							that.nodata = true;
						}
					} else {
						that.total = res.total || 0;
						if (data.length < that.limit || that.orderList.length >= that.total) {
							that.nodata = true;
						}
					}
					
					console.log('2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_003] 订单列表数据处理完成', processedData);
				} else {
					app.error(res.msg || '获取订单列表失败');
					if (that.page > 1) that.page--;
				}
				if (callback) callback();
			}, function() {
				that.loading = false;
				if (that.page > 1) that.page--;
				if (callback) callback();
				app.error('请求失败');
			});
		},
		gotoDetail(orderId) {
			if (!orderId) return;
			const app = getApp();
			app.goto('/yuyue/packageorderdetail?order_id=' + orderId);
		},
		gotoPay(payorderid, orderid){
			if(!payorderid) {
				const app = getApp();
				app.error('支付单号不存在');
				return;
			}
			const app = getApp();
			app.payorder({
				payorderid: payorderid,
				orderid: orderid,
				type: 'yuyue_package',
				success: () => {
					// 支付成功，刷新当前列表
					app.success('支付成功');
					this.getOrderList(true);
				},
				fail: () => {
					app.error('支付失败或取消');
				}
			});
		},
		cancelOrder(orderId, index){
			var that = this;
			const app = getApp();
			app.confirm('确定要取消该订单吗？', function(){
				app.showLoading('处理中...');
				app.post('ApiYuyuePackage/cancelOrder', {order_id: orderId}, function(res){
					app.showLoading(false);
					if(res.status == 1){
						app.success('取消成功');
						// 刷新列表或直接修改状态
						if (that.status === -1 || that.status === 0) {
							that.getOrderList(true);
						} else {
							that.orderList.splice(index, 1);
						}
					}else{
						app.error(res.msg || '取消失败');
					}
				});
			});
		},
		deleteOrder(orderId, index){
			// 注意：接口文档未提供删除接口，这里假设有 ApiYuyuePackage/deleteOrder
			var that = this;
			const app = getApp();
			app.confirm('确定要删除该订单吗？删除后不可恢复。', function(){
				app.showLoading('处理中...');
				app.post('ApiYuyuePackage/deleteOrder', {order_id: orderId}, function(res){
					app.showLoading(false);
					if(res.status == 1){
						app.success('删除成功');
						that.orderList.splice(index, 1);
						if(that.orderList.length === 0 && that.page > 1) that.page--; // 如果删空了且不在第一页，尝试回退页码
					}else{
						app.error(res.msg || '删除失败');
					}
				});
			});
		},
		applyRefund(orderId){
			// 跳转到退款申请页或直接在此处理
			// 接口文档提供了获取退款金额和提交退款的接口
			// 为简化，这里直接跳转到详情页，在详情页处理退款
			this.gotoDetail(orderId);
			const app = getApp();
			app.toast('请在订单详情页申请退款');
		},
		canUse(order){
			// 必须是已支付状态(status=1)、有剩余次数、未过期
			// 注意：我们现在接收整个order对象作为参数，而不仅仅是status
			return order.status === 1 
				&& order.remain_services > 0 
				&& order.status_text !== '已用完'
				&& order.status_text !== '已过期';
		},
		canRefund(order){
			// 必须是已支付状态(status=1)
			// 并且状态文本不是 '已用完' 和 '已过期'
			// 增加对 order.refund_status 的判断，如果已经申请过退款，则不显示
			return order.status === 1 
				&& order.status_text !== '已用完' 
				&& order.status_text !== '已过期'
				&& (!order.refund_status || order.refund_status === 0); // 未申请退款或申请状态为0
		},
		getStatusColor(status){
			switch(status){
				case 0: return '#FF9900'; // 待支付 - 橙色
				case 1: return this.themeColor || '#2979ff'; // 可使用 - 主题色
				case 2: return '#999999'; // 已取消/失效 - 灰色
				case 3: return '#4CAF50'; // 已完成(如果后端有此状态) - 绿色
				case 4: return '#FF9900'; // 退款中(如果后端有此状态) - 橙色
				case 5: return '#999999'; // 已退款(如果后端有此状态) - 灰色
				default: return '#666666';
			}
		},
		// 格式化时间戳为可读时间
		formatTime(timestamp) {
			if (!timestamp) return '';
			const date = new Date(timestamp * 1000);
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			const hours = date.getHours().toString().padStart(2, '0');
			const minutes = date.getMinutes().toString().padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		}
	}
}
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

/* Tabs */
.tabs {
	display: flex;
	background-color: #fff;
	height: 80rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	position: sticky;
	top: 0; /* 吸顶效果 */
	/* #ifdef H5 */
	top: var(--window-top); /* H5需要考虑顶部导航栏 */
	/* #endif */
	z-index: 99;
}

.tab-item {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #666;
	position: relative;
	border-bottom: 4rpx solid transparent; /* 底部边框占位 */
	transition: color 0.3s, border-bottom-color 0.3s;
}

.tab-item.active {
	color: #333; /* 激活时文字颜色加深 */
	font-weight: bold;
	border-bottom-width: 4rpx;
	border-bottom-style: solid;
}

/* Order List */
.order-list {
	flex: 1;
	padding: 20rpx;
}

.order-item {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 25rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding-bottom: 15rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.order-time {
	font-size: 24rpx;
	color: #999;
}

.order-status {
	font-size: 26rpx;
	font-weight: bold;
}

.item-content {
	display: flex;
	margin-bottom: 20rpx;
}

.package-pic-list {
	width: 140rpx;
	height: 140rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.package-info-list {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	overflow: hidden;
}

.package-name-list {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.package-services {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
}
.package-services .remain {
	margin-left: 20rpx;
	color: #ff9900;
	font-weight: bold;
}

.package-expire {
	font-size: 22rpx;
	color: #999;
}

.item-footer {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	margin-top: 15rpx;
	padding-top: 15rpx;
	border-top: 1rpx solid #f0f0f0;
}

.total-price-list {
	font-size: 26rpx;
	color: #333;
	margin-bottom: 15rpx;
}
.total-price-list text {
	font-size: 30rpx;
	font-weight: bold;
}

.item-actions {
	display: flex;
	gap: 15rpx; /* 按钮间距 */
}

.action-btn {
	padding: 0 24rpx;
	height: 56rpx;
	line-height: 56rpx;
	font-size: 24rpx;
	border-radius: 28rpx;
	margin: 0; /* 去除默认边距 */
	border: 1rpx solid #ccc;
	background-color: #fff;
	color: #666;
}
button::after {
    border: none;
}

.action-btn.primary {
	color: #fff;
	border: none;
}

.action-btn.plain {
	/* 可自定义朴素按钮样式 */
}
.action-btn.disabled {
    background-color: #f5f5f5;
    color: #ccc;
    border-color: #eee;
}

/* 加载和空状态 */
.loading-tip, .nodata-tip {
	text-align: center;
	color: #999;
	font-size: 24rpx;
	padding: 20rpx 0;
}

.empty-tip.list-empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding-top: 150rpx;
	flex: 1; /* 占据剩余空间 */
}

.empty-img {
	width: 200rpx;
	margin-bottom: 20rpx;
}
.empty-tip text {
	color: #999;
	font-size: 26rpx;
}
</style> 