<template>
  <view class="comprehensive-selection">
    <!-- 顶部导航 -->
    <view class="nav-header">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="nav-title">综合诊疗</view>
      <view class="nav-right"></view>
    </view>

    <!-- 页面内容 -->
    <scroll-view scroll-y class="content-area">
      <!-- 功能介绍 -->
      <view class="intro-section">
        <view class="intro-card">
          <view class="intro-header">
            <view class="intro-icon">
              <image src="/static/images/diagnosis/comprehensive.png" mode="aspectFit"></image>
            </view>
            <view class="intro-text">
              <text class="intro-title">综合诊疗</text>
              <text class="intro-desc">多维度分析，更准确的健康评估</text>
            </view>
          </view>
          
          <view class="intro-features">
            <text class="feature-item">🎯 舌诊 + 面诊 + 舌下诊疗</text>
            <text class="feature-item">📊 AI智能综合分析</text>
            <text class="feature-item">💡 精准个性化建议</text>
          </view>
        </view>
      </view>

      <!-- 诊疗方式选择 -->
      <view class="selection-section">
        <view class="section-header">
          <text class="section-title">选择诊疗方式</text>
          <text class="section-desc">建议选择多种方式以获得更准确的结果</text>
        </view>
        
        <view class="diagnosis-options">
          <!-- 舌诊选项 -->
          <view 
            class="option-card" 
            :class="{ active: selectedTypes.includes('tongue') }"
            @click="toggleDiagnosisType('tongue')"
          >
            <view class="option-header">
              <view class="option-icon">
                <image src="/static/images/diagnosis/tongue-icon.png" mode="aspectFit"></image>
              </view>
              <view class="option-info">
                <text class="option-title">舌诊</text>
                <text class="option-desc">通过舌头特征分析体质</text>
              </view>
              <view class="option-check">
                <text class="check-icon" :class="{ checked: selectedTypes.includes('tongue') }">
                  {{ selectedTypes.includes('tongue') ? '✓' : '' }}
                </text>
              </view>
            </view>
            
            <view class="option-features">
              <text class="feature-tag">舌色分析</text>
              <text class="feature-tag">舌苔检测</text>
              <text class="feature-tag">体质判断</text>
            </view>
            
            <view class="option-status" v-if="tongueStatus">
              <text class="status-text" :class="tongueStatus.class">{{ tongueStatus.text }}</text>
            </view>
          </view>

          <!-- 面诊选项 -->
          <view 
            class="option-card" 
            :class="{ active: selectedTypes.includes('face') }"
            @click="toggleDiagnosisType('face')"
          >
            <view class="option-header">
              <view class="option-icon">
                <image src="/static/images/diagnosis/face-icon.png" mode="aspectFit"></image>
              </view>
              <view class="option-info">
                <text class="option-title">面诊</text>
                <text class="option-desc">通过面部特征分析健康</text>
              </view>
              <view class="option-check">
                <text class="check-icon" :class="{ checked: selectedTypes.includes('face') }">
                  {{ selectedTypes.includes('face') ? '✓' : '' }}
                </text>
              </view>
            </view>
            
            <view class="option-features">
              <text class="feature-tag">面色分析</text>
              <text class="feature-tag">五官检测</text>
              <text class="feature-tag">气血评估</text>
            </view>
            
            <view class="option-status" v-if="faceStatus">
              <text class="status-text" :class="faceStatus.class">{{ faceStatus.text }}</text>
            </view>
          </view>

          <!-- 舌下诊疗选项 -->
          <view 
            class="option-card" 
            :class="{ active: selectedTypes.includes('sublingual') }"
            @click="toggleDiagnosisType('sublingual')"
          >
            <view class="option-header">
              <view class="option-icon">
                <image src="/static/images/diagnosis/sublingual-icon.png" mode="aspectFit"></image>
              </view>
              <view class="option-info">
                <text class="option-title">舌下诊疗</text>
                <text class="option-desc">通过舌下脉络分析循环</text>
              </view>
              <view class="option-check">
                <text class="check-icon" :class="{ checked: selectedTypes.includes('sublingual') }">
                  {{ selectedTypes.includes('sublingual') ? '✓' : '' }}
                </text>
              </view>
            </view>
            
            <view class="option-features">
              <text class="feature-tag">脉络分析</text>
              <text class="feature-tag">血液循环</text>
              <text class="feature-tag">经络评估</text>
            </view>
            
            <view class="option-status" v-if="sublingualStatus">
              <text class="status-text" :class="sublingualStatus.class">{{ sublingualStatus.text }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 权重配置 -->
      <view class="weight-section" v-if="selectedTypes.length > 1">
        <view class="section-header">
          <text class="section-title">分析权重配置</text>
          <text class="section-desc">调整不同诊疗方式在综合分析中的权重</text>
        </view>
        
        <view class="weight-sliders">
          <view class="weight-item" v-if="selectedTypes.includes('tongue')">
            <view class="weight-label">
              <text class="label-text">舌诊权重</text>
              <text class="label-value">{{ weights.tongue }}%</text>
            </view>
            <slider 
              :value="weights.tongue" 
              @change="onWeightChange('tongue', $event)"
              min="10" 
              max="80" 
              step="5"
              activeColor="#4facfe"
              class="weight-slider"
            />
          </view>
          
          <view class="weight-item" v-if="selectedTypes.includes('face')">
            <view class="weight-label">
              <text class="label-text">面诊权重</text>
              <text class="label-value">{{ weights.face }}%</text>
            </view>
            <slider 
              :value="weights.face" 
              @change="onWeightChange('face', $event)"
              min="10" 
              max="80" 
              step="5"
              activeColor="#4facfe"
              class="weight-slider"
            />
          </view>
          
          <view class="weight-item" v-if="selectedTypes.includes('sublingual')">
            <view class="weight-label">
              <text class="label-text">舌下权重</text>
              <text class="label-value">{{ weights.sublingual }}%</text>
            </view>
            <slider 
              :value="weights.sublingual" 
              @change="onWeightChange('sublingual', $event)"
              min="10" 
              max="80" 
              step="5"
              activeColor="#4facfe"
              class="weight-slider"
            />
          </view>
        </view>
        
        <view class="weight-total">
          <text class="total-label">总权重：</text>
          <text class="total-value" :class="{ invalid: totalWeight !== 100 }">{{ totalWeight }}%</text>
        </view>
      </view>

      <!-- 价格信息 */
      <view class="price-section">
        <view class="section-header">
          <text class="section-title">费用说明</text>
        </view>
        
        <view class="price-card">
          <view class="price-breakdown">
            <view class="price-item" v-if="selectedTypes.includes('tongue')">
              <text class="price-label">舌诊</text>
              <text class="price-value">¥{{ pricing.tongue }}</text>
            </view>
            <view class="price-item" v-if="selectedTypes.includes('face')">
              <text class="price-label">面诊</text>
              <text class="price-value">¥{{ pricing.face }}</text>
            </view>
            <view class="price-item" v-if="selectedTypes.includes('sublingual')">
              <text class="price-label">舌下诊疗</text>
              <text class="price-value">¥{{ pricing.sublingual }}</text>
            </view>
          </view>
          
          <view class="price-total">
            <text class="total-label">综合诊疗总价：</text>
            <text class="total-price">¥{{ totalPrice }}</text>
          </view>
          
          <view class="discount-info" v-if="discount > 0">
            <text class="discount-text">综合诊疗优惠：-¥{{ discount }}</text>
            <text class="final-price">实付：¥{{ finalPrice }}</text>
          </view>
          
          <view class="vip-info" v-if="config.can_use_free">
            <text class="vip-badge">VIP免费</text>
            <text class="vip-desc">您可以免费使用综合诊疗功能</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作 -->
    <view class="bottom-actions">
      <view class="action-info">
        <text class="selected-count">已选择 {{ selectedTypes.length }} 种诊疗方式</text>
        <text class="final-price-text" v-if="selectedTypes.length > 0">¥{{ finalPrice }}</text>
      </view>
      
      <button 
        class="start-button" 
        @click="startComprehensiveDiagnosis"
        :disabled="!canStart"
      >
        开始综合诊疗
      </button>
    </view>
  </view>
</template>

<script>
const app = getApp();

export default {
  data() {
    return {
      selectedTypes: [],
      weights: {
        tongue: 40,
        face: 40,
        sublingual: 20
      },
      pricing: {
        tongue: 10,
        face: 10,
        sublingual: 8
      },
      config: {
        can_use_free: false,
        comprehensive_discount: 0.15 // 15%折扣
      },
      
      // 状态信息
      tongueStatus: null,
      faceStatus: null,
      sublingualStatus: null
    }
  },

  computed: {
    totalWeight() {
      let total = 0;
      this.selectedTypes.forEach(type => {
        total += this.weights[type] || 0;
      });
      return total;
    },
    
    totalPrice() {
      let total = 0;
      this.selectedTypes.forEach(type => {
        total += this.pricing[type] || 0;
      });
      return total;
    },
    
    discount() {
      if (this.selectedTypes.length > 1) {
        return Math.round(this.totalPrice * this.config.comprehensive_discount * 100) / 100;
      }
      return 0;
    },
    
    finalPrice() {
      if (this.config.can_use_free) {
        return 0;
      }
      return Math.round((this.totalPrice - this.discount) * 100) / 100;
    },
    
    canStart() {
      return this.selectedTypes.length > 0 && this.totalWeight === 100;
    }
  },

  onLoad() {
    this.getConfig();
    this.checkPreviousResults();
  },

  methods: {
    // 获取配置
    async getConfig() {
      try {
        const res = await app.get('ApiDiagnosis/getConfig', {
          diagnosis_type: 'comprehensive'
        });
        
        if (res.code === 1) {
          this.config = { ...this.config, ...res.data };
          
          // 更新价格
          if (res.data.pricing) {
            this.pricing = { ...this.pricing, ...res.data.pricing };
          }
        }
      } catch (error) {
        console.error('获取配置失败:', error);
      }
    },

    // 检查之前的诊疗结果
    checkPreviousResults() {
      // 检查是否有之前的舌诊结果
      const tongueResult = uni.getStorageSync('recent_tongue_result');
      if (tongueResult && this.isRecentResult(tongueResult.createTime)) {
        this.tongueStatus = { text: '有近期结果', class: 'success' };
      }
      
      // 检查是否有之前的面诊结果
      const faceResult = uni.getStorageSync('recent_face_result');
      if (faceResult && this.isRecentResult(faceResult.createTime)) {
        this.faceStatus = { text: '有近期结果', class: 'success' };
      }
    },

    // 判断是否为近期结果（24小时内）
    isRecentResult(createTime) {
      const now = Date.now();
      const resultTime = new Date(createTime).getTime();
      return (now - resultTime) < 24 * 60 * 60 * 1000;
    },

    // 切换诊疗类型选择
    toggleDiagnosisType(type) {
      const index = this.selectedTypes.indexOf(type);
      if (index > -1) {
        this.selectedTypes.splice(index, 1);
      } else {
        this.selectedTypes.push(type);
      }
      
      // 重新平衡权重
      this.rebalanceWeights();
    },

    // 重新平衡权重
    rebalanceWeights() {
      const count = this.selectedTypes.length;
      if (count === 0) return;
      
      const baseWeight = Math.floor(100 / count);
      const remainder = 100 - (baseWeight * count);
      
      this.selectedTypes.forEach((type, index) => {
        this.weights[type] = baseWeight + (index < remainder ? 1 : 0);
      });
    },

    // 权重改变
    onWeightChange(type, event) {
      const newValue = event.detail.value;
      const oldValue = this.weights[type];
      const diff = newValue - oldValue;
      
      // 更新当前权重
      this.weights[type] = newValue;
      
      // 调整其他权重
      const otherTypes = this.selectedTypes.filter(t => t !== type);
      if (otherTypes.length > 0) {
        const avgAdjust = -diff / otherTypes.length;
        otherTypes.forEach(t => {
          this.weights[t] = Math.max(10, Math.min(80, this.weights[t] + avgAdjust));
        });
      }
      
      // 确保总权重为100
      this.normalizeWeights();
    },

    // 标准化权重
    normalizeWeights() {
      const total = this.totalWeight;
      if (total === 100) return;
      
      const factor = 100 / total;
      this.selectedTypes.forEach(type => {
        this.weights[type] = Math.round(this.weights[type] * factor);
      });
      
      // 处理舍入误差
      const newTotal = this.totalWeight;
      if (newTotal !== 100) {
        const diff = 100 - newTotal;
        const firstType = this.selectedTypes[0];
        if (firstType) {
          this.weights[firstType] += diff;
        }
      }
    },

    // 开始综合诊疗
    startComprehensiveDiagnosis() {
      if (!this.canStart) {
        if (this.selectedTypes.length === 0) {
          uni.showToast({
            title: '请至少选择一种诊疗方式',
            icon: 'none'
          });
        } else if (this.totalWeight !== 100) {
          uni.showToast({
            title: '请调整权重总和为100%',
            icon: 'none'
          });
        }
        return;
      }

      // 保存选择和配置
      const comprehensiveConfig = {
        selectedTypes: this.selectedTypes,
        weights: this.weights,
        pricing: this.pricing,
        totalPrice: this.totalPrice,
        finalPrice: this.finalPrice,
        canUseFree: this.config.can_use_free
      };
      
      uni.setStorageSync('comprehensive_config', comprehensiveConfig);
      
      // 跳转到拍摄流程
      uni.navigateTo({
        url: '/pagesB/diagnosis/comprehensive/multi-capture'
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss" scoped>
.comprehensive-selection {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44rpx 32rpx 20rpx;
}

.nav-back {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  
  .icon-back {
    font-size: 32rpx;
    color: #fff;
  }
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.nav-right {
  width: 64rpx;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 0 32rpx 200rpx;
  max-height: calc(100vh - 200rpx);
}

/* 介绍部分 */
.intro-section {
  margin-bottom: 32rpx;
}

.intro-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.intro-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  
  image {
    width: 100%;
    height: 100%;
  }
}

.intro-text {
  flex: 1;
}

.intro-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.intro-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.intro-features {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.feature-item {
  font-size: 24rpx;
  color: #555;
  line-height: 1.4;
}

/* 选择部分 */
.selection-section,
.weight-section,
.price-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.section-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 诊疗选项 */
.diagnosis-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-card {
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
  
  &.active {
    border-color: #4facfe;
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
  }
}

.option-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.option-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  
  image {
    width: 100%;
    height: 100%;
  }
}

.option-info {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.option-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.option-check {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.check-icon {
  font-size: 24rpx;
  color: #fff;
  font-weight: 600;
  
  &.checked {
    .option-check {
      background: #4facfe;
      border-color: #4facfe;
    }
  }
}

.option-card.active .option-check {
  background: #4facfe;
  border-color: #4facfe;
}

.option-features {
  display: flex;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.feature-tag {
  background: #f0f0f0;
  color: #666;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.option-card.active .feature-tag {
  background: rgba(79, 172, 254, 0.2);
  color: #4facfe;
}

.option-status {
  .status-text {
    font-size: 22rpx;
    
    &.success {
      color: #52c41a;
    }
  }
}

/* 权重配置 */
.weight-sliders {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.weight-item {
  .weight-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
  }
  
  .label-text {
    font-size: 26rpx;
    color: #333;
  }
  
  .label-value {
    font-size: 24rpx;
    color: #4facfe;
    font-weight: 600;
  }
}

.weight-slider {
  width: 100%;
}

.weight-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-top: 1rpx solid #eee;
}

.total-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.total-value {
  font-size: 28rpx;
  font-weight: 600;
  
  &.invalid {
    color: #ff4d4f;
  }
}

/* 价格信息 */
.price-card {
  .price-breakdown {
    margin-bottom: 16rpx;
  }
  
  .price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .price-label {
    font-size: 26rpx;
    color: #666;
  }
  
  .price-value {
    font-size: 26rpx;
    color: #333;
  }
}

.price-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-top: 1rpx solid #eee;
  margin-bottom: 16rpx;
}

.total-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.total-price {
  font-size: 32rpx;
  color: #4facfe;
  font-weight: 600;
}

.discount-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f6ffed;
  padding: 16rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.discount-text {
  font-size: 24rpx;
  color: #52c41a;
}

.final-price {
  font-size: 28rpx;
  color: #52c41a;
  font-weight: 600;
}

.vip-info {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
  padding: 16rpx;
  border-radius: 12rpx;
}

.vip-badge {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  margin-right: 16rpx;
}

.vip-desc {
  font-size: 24rpx;
  color: #4facfe;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.selected-count {
  font-size: 24rpx;
  color: #666;
}

.final-price-text {
  font-size: 28rpx;
  color: #4facfe;
  font-weight: 600;
}

.start-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  
  &:disabled {
    background: #ccc;
    color: #999;
  }
}
</style>