<template>
	<view>
		<block v-if="isload">
			<block v-if="partJobVo.template && partJobVo.template.templateId === 2">
				<view>
					<image lazyLoad class="allmessimage" mode="widthFix" :src="partJobVo.qualityBackground"></image>
					<view class="resourse">
						<parser :html="partJobVo.targetUrlContent"></parser>
					</view>
				</view>
				<apply-button :agreementVo="agreementVo" @cancel="agreementCancelHandle" @initData="initData"
					@loginSuccess="onLoginSuccess" @promptly="confirmReport" :collectImg="collectImg" :hasToken="hasToken"
					:partJobVo="partJobVo" v-if="flag" :style="{backgroundColor: t('color1')}"></apply-button>
			</block>
			<block v-else>
				<view class="container" v-if="!partJobVo.template || partJobVo.template.templateId !== 3">
					<view class="education-wrap">
						<education :partJobVo="partJobVo" :chosenList="chosenList" :tabCurrent="tabCurrent"
							:hasEyeAuth="hasEyeAuth" :isShowAll="isShowAll" :isInfoShowBtn="isInfoShowBtn"
							:isComputedInfo="isComputedInfo" :agreementVo="agreementVo" @clickCompany="goToCompany"></education>
						<job-list @tabChange="anchorTabHandle" :isTabList="true" :recommendList="moduleList"
							:tabCurrent="tabCurrent" v-if="moduleList && moduleList.length > 0"></job-list>
					</view>
				</view>
				<apply-button 
				:agreementVo="agreementVo" 
				:baseInfo="jobDetailJson.baseInfo"
				:collectImg="collectImg" 
				:hasToken="hasToken" 
				:partJobVo="partJobVo"
				@cancel="agreementCancelHandle"
				@collect="handleCollect"
				@initData="initData" 
				@loginSuccess="onLoginSuccess"
				@pay="showPayDialog" 
				@promptly="confirmReport"
				:style="{backgroundColor: t('color1')}"></apply-button>
			</block>
		</block>
		<report-dialog :agreementVo="agreementVo" :authorizedKey="authorizedKey" @close="closeReportDialog"
			@payCancel="onPayCancel" @paySuccess="onPaySuccess" :famousJobList="famousList"
			:fromRecommend="fromRecommend" :hasLatitude="hasLatitude" :isDirect="isDirect" :isPartDetails="true"
			:partJobId="partJobId" :partJobVo="partJobVo" :recommendTips="recommendTips"
			:reportDialogText="reportDialogText" :shareUserId="shareUserId" :source="source" :visible="visible"
			v-if="visible"></report-dialog>
		<apply-sure-modal @onCancelBtn="onCancelBtn" @onSureBtn="onSureBtn" :locationTownName="locationTownName"
			:nowTownName="nowTownName" v-if="isShowApplySure"></apply-sure-modal>
	</view>
</template>

<script>
	var app = getApp();
	import applyButton from './components/applyButton/applyButton';
	import simpleModel from './components/simpleModel/simpleModel';
	import navigationBar from './components/navigationBar/index';
	import applySureModal from './components/applySureModal/index';
	import jobList from './components/jobList/index';
	import reportDialog from './components/reportDialog/index';
	import regularItem from './components/regularItem/index';
	// 页面模板
	import education from './templates/education/education.vue';

	export default {
		components: { 
			jobList,
			applySureModal,
			applyButton,
			education,
			reportDialog,
			simpleModel,
			navigationBar,
			regularItem
		},
		data() {
			return {
				opt:{},
				loading:false,
				isload: false,
				partJobId: '',
				visible: false,
				moduleList: [],
				partJobVo: {
					id: 0,
					aid: 0,
					company_id: 0,
					title: '',
					salary: '',
					province: '',
					city: '',
					district: '',
					address: '',
					education: '',
					experience: '',
					description: '',
					requirement: '',
					status: 1,
					views: 0,
					create_time: '',
					buttonStatus: 6,
					hasApply: false,
					company: {
						id: 0,
						name: '',
						logo: '',
						introduction: '',
						scale: '',
						nature: '',
						industry: ''
					},
					// 兼容原有模板显示
					template: {
						templateId: 1
					},
					labelList: {
						descLabels: []
					}
				},
				hasToken: false,
				authorizedKey: '',
				reportDialogText: '',
				shareUserId: '',
				diploma: {
					0: '学历不限',
					2: '限高中以上',
					3: '限大专以上',
					4: '限本科以上',
					6: '限硕士以上',
					7: '限博士以上'
				},
				flag: true,
				showAll: false,
				showRemind: false,
				remainingApplyCount: '',
				userRemark: '',
				isSupportLifestyle: false,
				positionId: '',
				source: '',
				famousList: [],
				recommendTips: '',
				isShowApplySure: false,
				locationTownName: '',
				nowTownName: '',
				nowTownId: '',
				duration: '',
				routeType: '公交',
				routeMethods: [],
				current: 0,
				navList: [{
					text: '详情',
					type: 1
				}],
				navCurrent: 0,
				navTop: 148,
				navHeight: 40,
				isFixed: false,
				reportSwiper: [],
				computedFlag: false,
				qtbPrdUrl: '',
				healthVisible: false,
				moduleList: [],
				recommendAllList: [],
				tuanContentPrompt: [],
				copyVisible: false,
				guideVisible: false,
				tabCurrent: 0,
				complaintPhotos: [],
				personalImageList: [],
				positionTags: [],
				hasLatitude: false,
				isInfoShowBtn: true,
				isComputedInfo: false,
				remindType: 0,
				hasEyeAuth: false,
				isShowAll: false,
				jobRequireList: [],
				bannerList: [],
				bannerCurrent: 0,
				fromRecommend: false,
				rate: 0,
				videoAutoplay: false,
				swiperAutoplay: true,
				gateWay: {},
				countdownDesc: '00:00:00',
				payVisible: false,
				sendData: {},
				applyId: '',
				successVisible: false,
				finishCount: 0,
				deg: 0,
				point: 0,
				redpacketVisible: false,
				guideReportVisible: false,
				scrollFlag: false,
				toastVisible: false,
				browserSeconds: 10,
				rewardMoney: 0,
				actId: '',
				poinitTime: 10,
				coundownTimer: 10,
				guideReportDialogVisible: false,
				jobDetailJson: {
					baseInfo: {}
				},
				descMore: false,
				companyDescMore: false,
				classMore: false,
				busLine: '',
				chosenList: [],
				isOnlineCourse: false,
				contactDialogShow: false,
				contactInfo: {},
				contactInfoTime: '',
				onlineCourseIds: '10465,10467,10468,10469,10470,10471,10472',
				agreementVo: {},
				agreementVisible: false,
				isDirect: false,
				collectImg: 'iconcollect'
			};
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			t(text) {
				return getApp().globalData.initdata.textset[text] || text;
			},
			getdata: function() {
				var that = this;
				var id = that.opt.id;
				that.loading = true;
				app.get('ApiZhaopin/getPositionDetail', {
					id: id
				}, function(res) {
					that.loading = false;
					if(res.status == 1) {
						// 更新职位详情数据
						const apiData = res.data;
						
						// 先设置isload为true
						that.isload = true;
						
						// 设置职位ID并获取推荐列表
						that.partJobId = String(apiData.id);
						
						// 获取推荐列表
						that.getRecommendList();
						
						// 设置收藏状态
						that.collectImg = apiData.is_favorite === 1 ? 'iconcollect_fill' : 'iconcollect';
						console.log('初始化收藏状态:', that.collectImg);
						
						// 更新职位详情
						that.partJobVo = {
							...apiData,
							// 基础信息
							title: apiData.title,
							salary: apiData.salary,
							description: apiData.description,
							requirement: apiData.requirement,
							views: apiData.views,
							create_time: apiData.create_time,
							
							// 佣金信息
							commission_detail: apiData.commission_detail || null,
							
							// 地址信息
							address: apiData.address,
							province: apiData.province,
							city: apiData.city,
							district: apiData.district,
							
							// 公司信息
							company: {
								id: apiData.company_id || 0,
								name: apiData.company_name || '',
								logo: apiData.company_logo || 'https://qiniu-image.qtshe.com/company_default_5.4.png',
								introduction: apiData.benefits || '',
								scale: apiData.scale || '',
								nature: apiData.nature ? (apiData.nature === 1 ? '民营' : apiData.nature === 2 ? '国企' : apiData.nature === 3 ? '合资' : apiData.nature === 4 ? '外资' : '其他') : '',
								industry: ''
							},
							
							// 兼容原有模板显示
							template: apiData.template || {
								templateId: 1
							},
							buttonStatus: apiData.buttonStatus || 6,
							hasApply: apiData.hasApply || false,
							jobFeeVO: apiData.jobFeeVO || {
								feeRushPrice: 0,
								rushStatus: 0
							},
							labelList: {
								descLabels: [
									{ labelName: apiData.education },
									{ labelName: apiData.experience },
									{ labelName: apiData.scale || '' },
									{ labelName: apiData.nature ? (apiData.nature === 1 ? '民营' : apiData.nature === 2 ? '国企' : apiData.nature === 3 ? '合资' : apiData.nature === 4 ? '外资' : '其他') : '' },
									// 添加formatted_options中的标签
									...(apiData.formatted_options ? Object.entries(apiData.formatted_options).map(([type, tags]) => 
										tags.map(tag => ({ labelName: tag, labelType: type }))
									).flat() : [])
								].filter(label => label.labelName) // 过滤掉空标签
							}
						};
						
						// 更新其他必要数据
						that.jobDetailJson = {
							baseInfo: apiData
						};
						
						// 初始化famousList
						that.famousList = apiData.famousList || [];
						
						// 设置页面标题
						uni.setNavigationBarTitle({
							title: apiData.title || '职位详情'
						});
						
						console.log('职位数据初始化完成:', that.partJobVo);
					} else {
						app.alert(res.msg || '获取数据失败');
					}
				});
			},
			agreementCancelHandle() {
				// 处理协议取消
			},
			onLoginSuccess() {
				// 处理登录成功
			},
			confirmReport() {
				console.log('点击报名按钮');
				console.log('当前职位信息:', this.partJobVo);
				
				// 检查简历完善状态
				app.get('ApiZhaopin/getResumeDetail', {}, (res) => {
					if (res.status === 0 && res.need_resume) {
						uni.showModal({
							title: '提示',
							content: res.msg || '请先创建简历',
							confirmText: '去创建',
							cancelText: '取消',
							success: function(result) {
								if (result.confirm) {
									app.goto(res.url || '/zhaopin/resume');
								}
							}
						});
						return;
					}
					
					if (res.status == 1) {
						try {
							// 如果简历已完善，继续报名流程
							this.partJobId = String(this.partJobVo.id);
							console.log('设置职位ID:', this.partJobId);
							
							// 设置工作时间和地点信息
							this.partJobVo.jobTime = `${this.partJobVo.work_time_start || ''} - ${this.partJobVo.work_time_end || ''}`.trim();
							this.partJobVo.jobDateDesc = this.partJobVo.work_time_type || '';
							this.partJobVo.addressDetail = this.partJobVo.work_address || this.partJobVo.address || '';
							
							// 设置任职要求 - 安全处理
							if (this.partJobVo.requirement) {
								let requirementText = this.partJobVo.requirement || '';
								// 移除所有HTML标签
								requirementText = requirementText.replace(/<\/?[^>]+(>|$)/g, '');
								// 移除多余空白字符
								requirementText = requirementText.replace(/\s+/g, ' ').trim();
								
								if (requirementText) {
									this.partJobVo.requireList = this.partJobVo.requireList || [];
									if (!this.partJobVo.requireList.includes(requirementText)) {
										this.partJobVo.requireList.push(requirementText);
									}
								}
							}
							
							this.visible = true;
							console.log('弹窗显示状态:', this.visible);
						} catch (error) {
							console.error('处理报名信息时出错:', error);
							app.error('处理报名信息时出错，请稍后重试');
						}
					} else {
						app.error(res.msg || '获取简历信息失败');
					}
				});
			},
			closeReportDialog() {
				this.visible = false;
				// 重新获取数据，更新职位状态
				this.getdata();
			},
			onPayCancel() {
				// 处理支付取消
			},
			onPaySuccess() {
				// 处理支付成功
			},
			showPayDialog() {
				// 显示支付弹窗
			},
			onCancelBtn() {
				this.isShowApplySure = false;
			},
			onSureBtn() {
				// 处理确认按钮
			},
			anchorTabHandle(e) {
				console.log('切换tab:', e);
				if(e && e.detail) {
					const index = e.detail.target.dataset.i;
					this.tabCurrent = parseInt(index);
					// 切换tab时重新获取推荐列表
					this.getRecommendList();
				}
			},
			getRecommendList(tabIndex) {
				const that = this;
				// 获取当前位置
				uni.getLocation({
					type: 'gcj02',
					success: function(res) {
						// 调用推荐列表接口
						app.get('ApiZhaopin/getRecommendList', {
							position_id: that.partJobId || '',
							latitude: res.latitude,
							longitude: res.longitude,
							limit: 10
						}, function(res) {
							if(res.status == 1) {
								console.log('获取推荐列表成功:', res.data);
								that.moduleList = res.data.recommendList || [];
							} else {
								console.log('获取推荐列表失败:', res.msg);
								that.moduleList = [];
							}
						});
					},
					fail: function() {
						// 获取位置失败，仍然调用接口但不传入位置信息
						app.get('ApiZhaopin/getRecommendList', {
							position_id: that.partJobId || '',
							limit: 10
						}, function(res) {
							if(res.status == 1) {
								console.log('获取推荐列表成功:', res.data);
								that.moduleList = res.data.recommendList || [];
							} else {
								console.log('获取推荐列表失败:', res.msg);
								that.moduleList = [];
							}
						});
					}
				});
			},
			handleCollect() {
				console.log('handleCollect被调用');
				console.log('当前职位ID:', this.partJobId);
				console.log('当前收藏状态:', this.collectImg);
				
				const isCollected = this.collectImg === 'iconcollect_fill';
				
				app.post('apiZhaopin/favoritePosition', {
					position_id: this.partJobId
				}, (res) => {
					console.log('收藏接口返回:', res);
					if (res.status === 1) {
						// 根据接口返回的is_favorite来设置图标
						this.collectImg = res.data.is_favorite === 1 ? 'iconcollect_fill' : 'iconcollect';
						uni.showToast({
							title: res.msg,
							icon: 'none'
						});
					} else {
						uni.showToast({
							title: res.msg || '操作失败',
							icon: 'none'
						});
					}
				});
			},
			goToCompany() {
				console.log('点击跳转公司', this.partJobVo.company);
				console.log('hasEyeAuth:', this.hasEyeAuth);
				
				if (this.partJobVo.company && this.partJobVo.company.id) {
					const url = "/zhaopin/company?id=" + this.partJobVo.company.id;
					console.log('跳转URL:', url);
					
					uni.navigateTo({
						url: url,
						fail: (err) => {
							console.error('跳转失败:', err);
							uni.redirectTo({
								url: url
							});
						}
					});
				} else {
					console.log('公司信息不完整，无法跳转');
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	 @import './partdetails.scss';
</style>
