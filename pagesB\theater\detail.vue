<template>
	<view class="container">
		<block v-if="isload">

			<scroll-view @scroll="scroll" :scrollIntoView="scrollToViewId" :scrollTop="scrollTop" :scroll-y="true"
				style="height:100%;overflow:scroll">

				<view id="scroll_view_tab0">

					<view class="swiper-container">
						<view class="swiper-item-view">
							<image class="img" :src="data.logo" mode="scaleToFill" />
						</view>
					</view>

					<view class="header">

						<block v-if="data.min_price > 0">
							<view class="price_share">
								<view class="price">
									<view class="f1" :style="{color:t('color1')}">
										<text style="font-size:36rpx">￥</text>{{data.min_price}}<text
											v-if="data.max_price!=data.min_price">-{{data.max_price}}</text>
									</view>
								</view>
								<view class="share" @tap="shareClick">
									<image class="img" :src="pre_url+'/static/img/share.png'" /><text class="txt">分享</text>
								</view>
							</view>

							<view class="title">{{data.title}}</view>

						</block>

					</view>
					<view class="choose" @tap="openMap">
						<view class="f1 flex1">
							<view> {{data.venues_info.title}} </view>
							<view style="color: #aaa;font-size: 22rpx;"> {{data.venues_info.city}}{{data.venues_info.district}}</view>
						</view>
						<image class="f2" :src="pre_url+'/static/img/map.png'" />
					</view>
					
					<!-- 推荐场次信息 -->
					<view class="recommend-show" v-if="nearestShow">
						<view class="recommend-title">
							<view class="icon"></view>
							<view class="text">推荐场次</view>
						</view>
						<view class="recommend-content">
							<view class="show-time">{{nearestShow.title}}</view>
							<view class="show-desc">最近可预订场次</view>
						</view>
					</view>

				</view>


				<view id="scroll_view_tab2">

					<view class="detail_title">
						<view class="t1"></view>
						<view class="t2"></view>
						<view class="t0">详情</view>
						<view class="t2"></view>
						<view class="t1"></view>
					</view>
					<view class="detail">
						<dp :pagecontent="pagecontent"></dp>
					</view>

				</view>



				<view style="width:100%;height:140rpx;"></view>

			</scroll-view>

			<view class="bottombar flex-row" :class="menuindex>-1?'tabbarbot':'notabbarbot'">
				<view class="f1">


					<view class="item" @tap="goto" data-url="/pagesB/theater/orderlist">
						<!--  data-url="/pages/index/index" -->
						<image class="img" :src="pre_url+'/static/img/shou.png'" />
						<view class="t1">首页</view>
					</view>

					<button class="item" open-type="contact">
						<image class="img" :src="pre_url+'/static/img/kefu.png'" />
						<view class="t1">客服</view>
					</button>


					<view class="item" @tap="addfavorite">
						<image class="img" :src="pre_url+'/static/img/shoucang.png'" />
						<view class="t1">{{isfavorite?'已收藏':'收藏'}}</view>
					</view>
					
					<view class="item" @tap="manualDateClick">
						<image class="img" :src="pre_url+'/static/img/date.png'" />
						<view class="t1">选择场次</view>
					</view>
				</view>


				<view class="op">

					<view style="flex:1;">

						<view class="tocart flex-x-center flex-y-center" :style="{background:t('color2')}"
							@click="dateClick">
							选座购买
						</view>

					</view>

				</view>


			</view>


			<view v-if="sharetypevisible" class="popup__container">
				<view class="popup__overlay" @tap.stop="handleClickMask"></view>
				<view class="popup__modal" style="height:320rpx;min-height:320rpx">

					<view class="popup__content">
						<view class="sharetypecontent">
							<view class="f1" @tap="shareapp" v-if="getplatform() == 'app'">
								<image class="img" :src="pre_url+'/static/img/weixin.png'" />
								<text class="t1">分享给好友</text>
							</view>
							<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'mp'">
								<image class="img" :src="pre_url+'/static/img/weixin.png'" />
								<text class="t1">分享给好友</text>
							</view>
							<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'h5'">
								<image class="img" :src="pre_url+'/static/img/weixin.png'" />
								<text class="t1">分享给好友</text>
							</view>
							<button class="f1" open-type="share" v-else>
								<image class="img" :src="pre_url+'/static/img/weixin.png'" />
								<text class="t1">分享给好友</text>
							</button>
							<view class="f2" @tap="showPoster">
								<image class="img" :src="pre_url+'/static/img/sharepic.png'" />
								<text class="t1">生成分享图片</text>
							</view>

							<view class="f1" @tap="shareScheme" v-if="getplatform() == 'wx' && xcx_scheme">
								<image class="img" :src="pre_url+'/static/img/weixin.png'" />
								<text class="t1">小程序链接</text>
							</view>

						</view>
					</view>
				</view>
			</view>

		</block>
		<loading v-if="loading"></loading>

		<uni-calendar ref="calendar" :insert="false" :selected="selected" @confirm="confirm"></uni-calendar>

		<uni-popup id="dialogShowCategory" ref="dialogShowCategory" type="bottom" :mask-click="true">

			<view class="view-main">

				<view class="view-title">选择场次<image class="icon" :src="pre_url+'/static/img/close.png'" @click="hideDialog()">
				</view>

				<view class="tit-t">选择日期</view>

				<view class="view-title">

					<view class="tit-date" :style="{background:t('color2')}">{{date}}</view>

					<view>

						<image class="icon2" :src="pre_url+'/static/img/date.png'" @click="dateClick()"></image>

					</view>

				</view>

				<view class="tit-t">场次时间</view>


				<scroll-view scroll-y style="height: 600rpx;">

					<view v-for="item,index in ccList" :key="index" @click="changeCC(index)">

						<view class="tit-time" :style="'color:'+t('color2')+';background:'+t('color2')+'33'"
							v-if="cci==index">{{item}}</view>

						<view class="tit-time" v-else>{{item}}</view>

					</view>

				</scroll-view>


				<view class="view-btn" :style="{background:t('color2')}" @click="goSeat()">去选座</view>

			</view>


		</uni-popup>

	</view>
</template>

<script>
	var app = getApp();
	var interval = null;

	export default {

		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				textset: {},
				buydialogShow: false,
				btntype: 1,
				isfavorite: false,
				current: 0,
				isplay: 0,
				showcuxiaodialog: false,
				showfuwudialog: false,
				business: "",
				product: [],
				cartnum: "",
				commentlist: "",
				commentcount: "",
				cuxiaolist: "",
				couponlist: "",
				fuwulist: [],
				pagecontent: "",
				shopset: {},
				sysset: {},
				title: "",
				bboglist: "",
				sharepic: "",
				sharetypevisible: false,
				showposter: false,
				posterpic: "",
				scrolltopshow: false,
				kfurl: '',
				showLinkStatus: false,
				showjiesheng: 0,
				tjdatalist: [],
				showtoptabbar: 0,
				toptabbar_show: 0,
				toptabbar_index: 0,
				scrollToViewId: "",
				scrollTop: 0,
				scrolltab0Height: 0,
				scrolltab1Height: 0,
				scrolltab2Height: 0,
				scrolltab3Height: 0,
				xcx_scheme: false,
				showScheme: false,
				schemeurl: '',

				data: '',
				ind: 0,
				list: [],
				selected: [],
				date: '',
				ccList: [],
				cci: 0,
				nearestShow: null,  // 添加最近场次信息
				pre_url: ''  // 添加云资源域名前缀

			};
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.pre_url = app.globalData.pre_url || '';  // 初始化云资源域名前缀
			this.getdata();

		},
		onShow: function(e) {
			uni.$emit('getglassrecord');
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onShareAppMessage: function() {
			return this._sharewx({
				title: this.product.sharetitle || this.product.name,
				pic: this.product.sharepic || this.product.pic
			});
		},
		onShareTimeline: function() {
			var sharewxdata = this._sharewx({
				title: this.product.sharetitle || this.product.name,
				pic: this.product.sharepic || this.product.pic
			});
			var query = (sharewxdata.path).split('?')[1];
			console.log(sharewxdata)
			console.log(query)
			return {
				title: sharewxdata.title,
				imageUrl: sharewxdata.imageUrl,
				query: query
			}
		},
		onUnload: function() {
			clearInterval(interval);
		},

		methods: {

			// 查找最近的演出场次
			findNearestShow() {
				const now = new Date();
				let nearestShow = null;
				let minTimeDiff = Infinity;
				
				// 遍历所有场次，找到最近的未来场次
				for (let item of this.data.seating_list) {
					const showTime = new Date(item.title.replace(' ', 'T'));
					const timeDiff = showTime.getTime() - now.getTime();
					
					// 只考虑未来的场次
					if (timeDiff > 0 && timeDiff < minTimeDiff) {
						minTimeDiff = timeDiff;
						nearestShow = item;
					}
				}
				
				return nearestShow;
			},

			dateClick() {
				// 自动选择最近的场次日期，但仍然打开日历让用户选择
				const nearestShow = this.findNearestShow();
				
				if (nearestShow) {
					// 设置默认日期为最近场次的日期
					this.date = nearestShow.title.substring(0, 10); // 提取日期部分 "2025-05-04"
					
					// 过滤出该日期的所有场次
					let arr = [];
					this.list.filter(item => {
						if (item.indexOf(this.date) >= 0) {
							arr.push(item);
						}
					});
					
					this.ccList = arr;
					this.cci = 0; // 默认选中第一个场次
					
					// 打开场次选择弹框
					this.$refs.dialogShowCategory.open('top');
				} else {
					// 如果没有找到未来的场次，显示提示信息
					uni.showToast({
						title: '暂无可选场次',
						icon: 'none',
						duration: 2000
					});
				}
			},

			// 手动选择日期的方法（保留原有功能）
			manualDateClick() {
				this.$refs.dialogShowCategory.close();
				this.$refs.calendar.open();
			},

			hideDialog() {
				this.$refs.dialogShowCategory.close();

			},
			confirm(e) {
				console.log(e);

				this.date = e.fulldate;

				let arr = [];
				this.list.filter(item => {

					if (item.indexOf(this.date) >= 0) {

						arr.push(item);
					}


				})

				this.ccList = arr;

				this.$refs.dialogShowCategory.open('top');
			},


			goSeat() {

				let date = this.ccList[this.cci];

				uni.navigateTo({
					url: '/pagesB/theater/dingchangrili?id=' + this.opt.id + '&date=' + date
				});


			},

			changeCC(ind) {
				this.cci = ind;
			},
			
			openMap(){ 
			   let that = this

			    uni.openLocation({
			      latitude: parseFloat(that.data.venues_info.lon), // 目标地点的纬度，浮点数，范围为-90~90
			      longitude:  parseFloat(that.data.venues_info.lat), // 目标地点的经度，浮点数，范围为-180~180
			      name: that.data.venues_info.title, // 位置名
			      success() {
			        console.log('地图打开成功');
			      },
			      fail(error) {
			        console.log('地图打开失败', error);
			      }
			    });
				
			},



			getdata: function() {
				var that = this;
				var id = this.opt.id || 0;


				app.get('ApiTheater/getEpisodeInfo', {
					id: id
				}, function(res) {
					that.loading = false;
					that.isload = true;

					if (res.status == 0) {
						app.alert("数据异常");
						return;
					}

					that.data = res.data;

					let arr = [];
					res.data.price_list.filter(item => {
						arr.push(item.money);
					});


					arr.sort((a, b) => a - b);

					if (arr.length > 0) {
						that.data.min_price = arr[0];
						that.data.max_price = arr[arr.length - 1];
					}

					let dates = [];
					that.data.seating_list.filter(item => {
						dates.push(item.title);
					})


					let sd = [];
					res.data.show_time.split(',').filter(item => {
						let obj = {
							date: item.substring(0, 10)
						}
						sd.push(obj);
					})
					that.selected = sd;

					that.list = dates;

					that.pagecontent = res.data.content;
					
					// 设置最近的场次信息
					that.$nextTick(() => {
						that.nearestShow = that.findNearestShow();
					});

					console.log(res);

					// 	that.textset = app.globalData.textset;
					// 	var product = res.product;
					// 	var pagecontent = JSON.parse(product.detail);
					// 	that.business = res.business;
					// 	that.product = product;
					// 	that.cartnum = res.cartnum;
					// 	that.commentlist = res.commentlist;
					// 	that.commentcount = res.commentcount;
					// 	that.cuxiaolist = res.cuxiaolist;
					// 	that.couponlist = res.couponlist;
					// 	that.fuwulist = res.fuwulist;
					// 	that.pagecontent = pagecontent;
					// 	that.shopset = res.shopset;
					// 	that.sysset = res.sysset;
					// 	that.title = product.name;
					// 	that.isfavorite = res.isfavorite;
					// 	that.showjiesheng = res.showjiesheng || 0;
					// 	that.tjdatalist = res.tjdatalist || [];
					// 	that.showtoptabbar = res.showtoptabbar || 0;
					// 	that.bboglist = res.bboglist;
					// 	that.sharepic = product.pics[0];
					// 	that.xcx_scheme = res.xcx_scheme
					// 	uni.setNavigationBarTitle({
					// 		title: product.name
					// 	});

					// 	that.kfurl = '/pages/kefu/index?bid='+product.bid;
					// 	if(app.globalData.initdata.kfurl != ''){
					// 		that.kfurl = app.globalData.initdata.kfurl;
					// 	}
					// 	if(that.business && that.business.kfurl){
					// 		that.kfurl = that.business.kfurl;
					// 	}
					// 	that.loaded({title:product.sharetitle || product.name,pic:product.sharepic || product.pic,desc:product.sharedesc || product.sellpoint});

					// 	setTimeout(function(){
					// 		let view0 = uni.createSelectorQuery().in(that).select('#scroll_view_tab0')
					// 		view0.fields({
					// 			size: true,//是否返回节点尺寸（width height）
					// 			rect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					// 			scrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					// 		}, (res) => {
					// 			console.log(res)
					// 			that.scrolltab0Height = res.height
					// 		}).exec();
					// 		let view1 = uni.createSelectorQuery().in(that).select('#scroll_view_tab1')
					// 		view1.fields({
					// 			size: true,//是否返回节点尺寸（width height）
					// 			rect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					// 			scrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					// 		}, (res) => {
					// 			console.log(res)
					// 			that.scrolltab1Height = res.height
					// 		}).exec();
					// 		let view2 = uni.createSelectorQuery().in(that).select('#scroll_view_tab2')
					// 		view2.fields({
					// 			size: true,//是否返回节点尺寸（width height）
					// 			rect: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					// 			scrollOffset: false,//是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					// 		}, (res) => {
					// 			console.log(res)
					// 			that.scrolltab2Height = res.height
					// 		}).exec();
					// 	},500)


				});
			},
			swiperChange: function(e) {
				var that = this;
				that.current = e.detail.current
			},
			payvideo: function() {
				this.isplay = 1;
				uni.createVideoContext('video').play();
			},
			parsevideo: function() {
				this.isplay = 0;
				uni.createVideoContext('video').stop();
			},
			buydialogChange: function(e) {
				if (!this.buydialogShow) {
					this.btntype = e.currentTarget.dataset.btntype
				}
				this.buydialogShow = !this.buydialogShow;
			},
			//收藏操作
			addfavorite: function() {
				var that = this;
				var proid = that.product.id;
				app.post('ApiShop/addfavorite', {
					proid: proid,
					type: 'shop'
				}, function(data) {
					if (data.status == 1) {
						that.isfavorite = !that.isfavorite;
					}
					app.success(data.msg);
				});
			},
			shareClick: function() {
				this.sharetypevisible = true;
			},
			handleClickMask: function() {
				this.sharetypevisible = false
			},
			showPoster: function() {
				var that = this;
				that.showposter = true;
				that.sharetypevisible = false;
				app.showLoading('生成海报中');
				app.post('ApiShop/getposter', {
					proid: that.product.id
				}, function(data) {
					app.showLoading(false);
					if (data.status == 0) {
						app.alert(data.msg);
					} else {
						that.posterpic = data.poster;
					}
				});
			},

			shareScheme: function() {
				var that = this;
				app.showLoading();
				app.post('ApiShop/getwxScheme', {
					proid: that.product.id
				}, function(data) {
					app.showLoading(false);
					if (data.status == 0) {
						app.alert(data.msg);
					} else {
						that.showScheme = true;
						that.schemeurl = data.openlink
					}
				});
			},

			schemeDialogClose: function() {
				this.showScheme = false;
			},

			posterDialogClose: function() {
				this.showposter = false;
			},
			showfuwudetail: function() {
				this.showfuwudialog = true;
			},
			hidefuwudetail: function() {
				this.showfuwudialog = false
			},
			showcuxiaodetail: function() {
				this.showcuxiaodialog = true;
			},
			hidecuxiaodetail: function() {
				this.showcuxiaodialog = false
			},
			getcoupon: function() {
				this.showcuxiaodialog = false;
				this.getdata();
			},
			onPageScroll: function(e) {
				//var that = this;
				//var scrollY = e.scrollTop;     
				//if (scrollY > 200) {
				//	that.scrolltopshow = true;
				//}
				//if(scrollY < 150) {
				//	that.scrolltopshow = false
				//}
				//if (scrollY > 100) {
				//	that.toptabbar_show = true;
				//}
				//if(scrollY < 50) {
				//	that.toptabbar_show = false
				//}
			},
			changetoptab: function(e) {
				var index = e.currentTarget.dataset.index;
				this.scrollToViewId = 'scroll_view_tab' + index;
				this.toptabbar_index = index;
				if (index == 0) this.scrollTop = 0;
				console.log(index);
			},
			scroll: function(e) {
				var scrollTop = e.detail.scrollTop;
				//console.log(e)
				var that = this;
				if (scrollTop > 200) {
					that.scrolltopshow = true;
				}
				if (scrollTop < 150) {
					that.scrolltopshow = false
				}
				if (scrollTop > 100) {
					that.toptabbar_show = true;
				}
				if (scrollTop < 50) {
					that.toptabbar_show = false
				}
				var height0 = that.scrolltab0Height;
				var height1 = that.scrolltab0Height + that.scrolltab1Height;
				var height2 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height;
				//var height3 = that.scrolltab0Height + that.scrolltab1Height + that.scrolltab2Height + that.scrolltab3Height;
				console.log('-----------------------');
				console.log(scrollTop);
				console.log(height2);
				if (scrollTop >= 0 && scrollTop < height0) {
					//this.scrollToViewId = 'scroll_view_tab0';
					this.toptabbar_index = 0;
				} else if (scrollTop >= height0 && scrollTop < height1) {
					//this.scrollToViewId = 'scroll_view_tab1';
					this.toptabbar_index = 1;
				} else if (scrollTop >= height1 && scrollTop < height2) {
					//this.scrollToViewId = 'scroll_view_tab2';
					this.toptabbar_index = 2;
				} else if (scrollTop >= height2) {
					//this.scrollToViewId = 'scroll_view_tab3';
					this.toptabbar_index = 3;
				}
			},
			sharemp: function() {
				app.error('点击右上角发送给好友或分享到朋友圈');
				this.sharetypevisible = false
			},
			shareapp: function() {
				var that = this;
				that.sharetypevisible = false;
				uni.showActionSheet({
					itemList: ['发送给微信好友', '分享到微信朋友圈'],
					success: function(res) {
						if (res.tapIndex >= 0) {
							var scene = 'WXSceneSession';
							if (res.tapIndex == 1) {
								scene = 'WXSenceTimeline';
							}
							var sharedata = {};
							sharedata.provider = 'weixin';
							sharedata.type = 0;
							sharedata.scene = scene;
							sharedata.title = that.product.sharetitle || that.product.name;
							sharedata.summary = that.product.sharedesc || that.product.sellpoint;
							sharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid +
								'.html#/pagesB/shop/product?scene=id_' + that.product.id + '-pid_' + app
								.globalData.mid;
							sharedata.imageUrl = that.product.pic;
							var sharelist = app.globalData.initdata.sharelist;
							if (sharelist) {
								for (var i = 0; i < sharelist.length; i++) {
									if (sharelist[i]['indexurl'] == '/pagesB/shop/product') {
										sharedata.title = sharelist[i].title;
										sharedata.summary = sharelist[i].desc;
										sharedata.imageUrl = sharelist[i].pic;
										if (sharelist[i].url) {
											var sharelink = sharelist[i].url;
											if (sharelink.indexOf('/') === 0) {
												sharelink = app.globalData.pre_url + '/h5/' + app
													.globalData.aid + '.html#' + sharelink;
											}
											if (app.globalData.mid > 0) {
												sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') +
													'pid=' + app.globalData.mid;
											}
											sharedata.href = sharelink;
										}
									}
								}
							}
							uni.share(sharedata);
						}
					}
				});
			},
			showsubqrcode: function() {
				this.$refs.qrcodeDialog.open();
			},
			closesubqrcode: function() {
				this.$refs.qrcodeDialog.close();
			},
			addcart: function(e) {
				console.log(e)
				this.cartnum = this.cartnum + e.num;
			},
			showgg1Dialog: function() {
				this.$refs.gg1Dialog.open();
			},
			closegg1Dialog: function() {
				this.$refs.gg1Dialog.close();
			},
			showgg2Dialog: function() {
				this.$refs.gg2Dialog.open();
			},
			closegg2Dialog: function() {
				this.$refs.gg2Dialog.close();
			},
		}

	};
</script>
<style>
	page {
		position: relative;
		width: 100%;
		height: 100%;
	}

	.container {
		height: 100%
	}

	.follow_topbar {
		height: 88rpx;
		width: 100%;
		max-width: 640px;
		background: rgba(0, 0, 0, 0.8);
		position: fixed;
		top: 0;
		z-index: 13;
	}

	.follow_topbar .headimg {
		height: 64rpx;
		width: 64rpx;
		margin: 6px;
		float: left;
	}

	.follow_topbar .headimg image {
		height: 64rpx;
		width: 64rpx;
	}

	.follow_topbar .info {
		height: 56rpx;
		padding: 16rpx 0;
	}

	.follow_topbar .info .i {
		height: 28rpx;
		line-height: 28rpx;
		color: #ccc;
		font-size: 24rpx;
	}

	.follow_topbar .info {
		height: 80rpx;
		float: left;
	}

	.follow_topbar .sub {
		height: 48rpx;
		width: auto;
		background: #FC4343;
		padding: 0 20rpx;
		margin: 20rpx 16rpx 20rpx 0;
		float: right;
		font-size: 24rpx;
		color: #fff;
		line-height: 52rpx;
		border-radius: 6rpx;
	}

	.qrcodebox {
		background: #fff;
		padding: 50rpx;
		position: relative;
		border-radius: 20rpx
	}

	.qrcodebox .img {
		width: 400rpx;
		height: 400rpx
	}

	.qrcodebox .txt {
		color: #666;
		margin-top: 20rpx;
		font-size: 26rpx;
		text-align: center
	}

	.qrcodebox .close {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		bottom: -100rpx;
		left: 50%;
		margin-left: -25rpx;
		border: 1px solid rgba(255, 255, 255, 0.5);
		border-radius: 50%;
		padding: 8rpx
	}

	.swiper-container {
		position: relative;
		height: 400rpx;
		overflow: hidden;
	}

	.swiper {
		width: 100%;
		height: 400rpx;
		overflow: hidden;
	}

	.swiper-item-view {
		width: 100%;
		height: 400rpx;
	}

	.swiper-item-view .img {
		width: 100%;
		height: 400rpx;
		overflow: hidden;
	}

	.imageCount {
		width: 100rpx;
		height: 50rpx;
		background-color: rgba(0, 0, 0, 0.3);
		border-radius: 40rpx;
		line-height: 50rpx;
		color: #fff;
		text-align: center;
		font-size: 26rpx;
		position: absolute;
		right: 13px;
		bottom: 20rpx;
	}

	.provideo {
		background: rgba(255, 255, 255, 0.7);
		width: 160rpx;
		height: 54rpx;
		padding: 0 20rpx 0 4rpx;
		border-radius: 27rpx;
		position: absolute;
		bottom: 30rpx;
		left: 50%;
		margin-left: -80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between
	}

	.provideo image {
		width: 50rpx;
		height: 50rpx;
	}

	.provideo .txt {
		flex: 1;
		text-align: center;
		padding-left: 10rpx;
		font-size: 24rpx;
		color: #333
	}

	.videobox {
		width: 100%;
		height: 750rpx;
		text-align: center;
		background: #000
	}

	.videobox .video {
		width: 100%;
		height: 650rpx;
	}

	.videobox .parsevideo {
		margin: 0 auto;
		margin-top: 20rpx;
		height: 40rpx;
		line-height: 40rpx;
		color: #333;
		background: #ccc;
		width: 140rpx;
		border-radius: 25rpx;
		font-size: 24rpx
	}

	.header {
		width: 100%;
		padding: 20rpx 3%;
		background: #fff;
	}

	.header .price_share {
		width: 100%;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-between
	}

	.header .price_share .price {
		display: flex;
		align-items: flex-end
	}

	.header .price_share .price .f1 {
		font-size: 50rpx;
		color: #51B539;
		font-weight: bold
	}

	.header .price_share .price .f2 {
		font-size: 26rpx;
		color: #C2C2C2;
		text-decoration: line-through;
		margin-left: 30rpx;
		padding-bottom: 5px
	}

	.header .price_share .share {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-width: 60rpx;
	}

	.header .price_share .share .img {
		width: 32rpx;
		height: 32rpx;
		margin-bottom: 2px
	}

	.header .price_share .share .txt {
		color: #333333;
		font-size: 20rpx
	}

	.header .title {
		color: #000000;
		font-size: 32rpx;
		line-height: 42rpx;
		font-weight: bold;
	}

	.header .price_share .title {
		display: flex;
		align-items: flex-end;
	}

	.header .sellpoint {
		font-size: 28rpx;
		color: #666;
		padding-top: 20rpx;
	}

	.header .sales_stock {
		display: flex;
		justify-content: space-between;
		height: 60rpx;
		line-height: 60rpx;
		margin-top: 30rpx;
		font-size: 24rpx;
		color: #777777
	}

	.header .commission {
		display: inline-block;
		margin-top: 20rpx;
		margin-bottom: 10rpx;
		border-radius: 10rpx;
		font-size: 20rpx;
		height: 44rpx;
		line-height: 44rpx;
		padding: 0 20rpx
	}

	.header .upsavemoney {
		display: flex;
		align-items: center;
		margin-top: 20rpx;
		margin-bottom: 10rpx;
		border-radius: 10rpx;
		font-size: 20rpx;
		height: 70rpx;
		padding: 0 20rpx
	}

	.choose {
		display: flex;
		align-items: center;
		width: 100%;
		background: #fff;
		margin-top: 20rpx;
		padding: 8px 3%;
		color: #000;
	}

	.choose .f0 {
		color: #555;
		font-weight: bold;
		height: 32rpx;
		font-size: 24rpx;
		padding-right: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center
	}

	.choose .f2 {
		width: 32rpx;
		height: 32rpx;
	}

	/* 推荐场次样式 */
	.recommend-show {
		background: #fff;
		margin-top: 20rpx;
		padding: 20rpx 3%;
		border-radius: 10rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.recommend-title {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.recommend-title .icon {
		width: 6rpx;
		height: 32rpx;
		background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
		border-radius: 3rpx;
		margin-right: 12rpx;
	}

	.recommend-title .text {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
	}

	.recommend-content {
		padding-left: 18rpx;
	}

	.recommend-content .show-time {
		font-size: 32rpx;
		color: #ff6b6b;
		font-weight: bold;
		margin-bottom: 8rpx;
	}

	.recommend-content .show-desc {
		font-size: 24rpx;
		color: #999;
	}

	.cuxiaodiv {
		background: #fff;
		margin-top: 20rpx;
		padding: 0 3%;
	}

	.fuwupoint {
		width: 100%;
		font-size: 24rpx;
		color: #333;
		height: 88rpx;
		line-height: 88rpx;
		padding: 12rpx 0;
		display: flex;
		align-items: center
	}

	.fuwupoint .f0 {
		color: #555;
		font-weight: bold;
		height: 32rpx;
		font-size: 24rpx;
		padding-right: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center
	}

	.fuwupoint .f1 {
		margin-right: 20rpx;
		flex: 1;
		display: flex;
		flex-wrap: nowrap;
		overflow: hidden
	}

	.fuwupoint .f1 .t {
		padding: 4rpx 20rpx 4rpx 0;
		color: #777;
		flex-shrink: 0
	}

	.fuwupoint .f1 .t:before {
		content: "";
		display: inline-block;
		vertical-align: middle;
		margin-top: -4rpx;
		margin-right: 10rpx;
		width: 24rpx;
		height: 24rpx;
		background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;
		background-size: 24rpx auto;
	}

	.fuwupoint .f2 {
		flex-shrink: 0;
		display: flex;
		align-items: center;
		width: 32rpx;
		height: 32rpx;
	}

	.fuwupoint .f2 .img {
		width: 32rpx;
		height: 32rpx;
	}

	.fuwudialog-content {
		font-size: 24rpx
	}

	.fuwudialog-content .f1 {
		color: #333;
		height: 80rpx;
		line-height: 80rpx;
		font-weight: bold
	}

	.fuwudialog-content .f1:before {
		content: "";
		display: inline-block;
		vertical-align: middle;
		margin-top: -4rpx;
		margin-right: 10rpx;
		width: 24rpx;
		height: 24rpx;
		background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;
		background-size: 24rpx auto;
	}

	.fuwudialog-content .f2 {
		color: #777
	}

	.cuxiaopoint {
		width: 100%;
		font-size: 24rpx;
		color: #333;
		height: 88rpx;
		line-height: 88rpx;
		padding: 12rpx 0;
		display: flex;
		align-items: center
	}

	.cuxiaopoint .f0 {
		color: #555;
		font-weight: bold;
		height: 32rpx;
		font-size: 24rpx;
		padding-right: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center
	}

	.cuxiaopoint .f1 {
		margin-right: 20rpx;
		flex: 1;
		display: flex;
		flex-wrap: nowrap;
		overflow: hidden
	}

	.cuxiaopoint .f1 .t {
		margin-left: 10rpx;
		border-radius: 3px;
		font-size: 24rpx;
		height: 40rpx;
		line-height: 40rpx;
		padding-right: 10rpx;
		flex-shrink: 0;
		overflow: hidden
	}

	.cuxiaopoint .f1 .t0 {
		display: inline-block;
		padding: 0 5px;
	}

	.cuxiaopoint .f1 .t1 {
		padding: 0 4px
	}

	.cuxiaopoint .f2 {
		flex-shrink: 0;
		display: flex;
		align-items: center;
		width: 32rpx;
		height: 32rpx;
	}

	.cuxiaopoint .f2 .img {
		width: 32rpx;
		height: 32rpx;
	}

	.cuxiaodiv .cuxiaoitem {
		border-bottom: 1px solid #E6E6E6;
	}

	.cuxiaodiv .cuxiaoitem:last-child {
		border-bottom: 0
	}

	.popup__container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		height: auto;
		z-index: 10;
		background: #fff
	}

	.popup__overlay {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		height: 100%;
		z-index: 11;
		opacity: 0.3;
		background: #000
	}

	.popup__modal {
		width: 100%;
		position: absolute;
		bottom: 0;
		color: #3d4145;
		overflow-x: hidden;
		overflow-y: hidden;
		opacity: 1;
		padding-bottom: 20rpx;
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
		z-index: 12;
		min-height: 600rpx;
		max-height: 1000rpx;
	}

	.popup__title {
		text-align: center;
		padding: 30rpx;
		position: relative;
		position: relative
	}

	.popup__title-text {
		font-size: 32rpx
	}

	.popup__close {
		position: absolute;
		top: 34rpx;
		right: 34rpx
	}

	.popup__content {
		width: 100%;
		max-height: 880rpx;
		overflow-y: scroll;
		padding: 20rpx 0;
	}

	.service-item {
		display: flex;
		padding: 0 40rpx 20rpx 40rpx;
	}

	.service-item .prefix {
		padding-top: 2px;
	}

	.service-item .suffix {
		padding-left: 10rpx;
	}

	.service-item .suffix .type-name {
		font-size: 28rpx;
		color: #49aa34;
		margin-bottom: 10rpx;
	}


	.shop {
		display: flex;
		align-items: center;
		width: 100%;
		background: #fff;
		margin-top: 20rpx;
		padding: 20rpx 3%;
		position: relative;
		min-height: 100rpx;
	}

	.shop .p1 {
		width: 90rpx;
		height: 90rpx;
		border-radius: 6rpx;
		flex-shrink: 0
	}

	.shop .p2 {
		padding-left: 10rpx
	}

	.shop .p2 .t1 {
		width: 100%;
		height: 40rpx;
		line-height: 40rpx;
		overflow: hidden;
		color: #111;
		font-weight: bold;
		font-size: 30rpx;
	}

	.shop .p2 .t2 {
		width: 100%;
		height: 30rpx;
		line-height: 30rpx;
		overflow: hidden;
		color: #999;
		font-size: 24rpx;
		margin-top: 8rpx
	}

	.shop .p4 {
		height: 64rpx;
		line-height: 64rpx;
		color: #FFFFFF;
		border-radius: 32rpx;
		margin-left: 20rpx;
		flex-shrink: 0;
		padding: 0 30rpx;
		font-size: 24rpx;
		font-weight: bold
	}

	.detail {
		min-height: 200rpx;
	}

	.detail_title {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 60rpx;
		margin-bottom: 30rpx
	}

	.detail_title .t0 {
		font-size: 28rpx;
		font-weight: bold;
		color: #222222;
		margin: 0 20rpx
	}

	.detail_title .t1 {
		width: 12rpx;
		height: 12rpx;
		background: rgba(253, 74, 70, 0.2);
		transform: rotate(45deg);
		margin: 0 4rpx;
		margin-top: 6rpx
	}

	.detail_title .t2 {
		width: 18rpx;
		height: 18rpx;
		background: rgba(253, 74, 70, 0.4);
		transform: rotate(45deg);
		margin: 0 4rpx
	}

	.commentbox {
		width: 100%;
		background: #fff;
		padding: 0 3%;
		margin-top: 20rpx
	}

	.commentbox .title {
		height: 90rpx;
		line-height: 90rpx;
		border-bottom: 1px solid #DDDDDD;
		display: flex
	}

	.commentbox .title .f1 {
		flex: 1;
		color: #111111;
		font-weight: bold;
		font-size: 30rpx
	}

	.commentbox .title .f2 {
		color: #333;
		font-weight: bold;
		font-size: 28rpx;
		display: flex;
		align-items: center
	}

	.commentbox .nocomment {
		height: 100rpx;
		line-height: 100rpx
	}

	.comment {
		display: flex;
		flex-direction: column;
		min-height: 200rpx;
	}

	.comment .item {
		background-color: #fff;
		padding: 10rpx 20rpx;
		display: flex;
		flex-direction: column;
	}

	.comment .item .f1 {
		display: flex;
		width: 100%;
		align-items: center;
		padding: 10rpx 0;
	}

	.comment .item .f1 .t1 {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
	}

	.comment .item .f1 .t2 {
		padding-left: 10rpx;
		color: #333;
		font-weight: bold;
		font-size: 30rpx;
	}

	.comment .item .f1 .t3 {
		text-align: right;
	}

	.comment .item .f1 .t3 .img {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10rpx
	}

	.comment .item .score {
		font-size: 24rpx;
		color: #f99716;
	}

	.comment .item .score image {
		width: 140rpx;
		height: 50rpx;
		vertical-align: middle;
		margin-bottom: 6rpx;
		margin-right: 6rpx;
	}

	.comment .item .f2 {
		display: flex;
		flex-direction: column;
		width: 100%;
		padding: 10rpx 0;
	}

	.comment .item .f2 .t1 {
		color: #333;
		font-size: 28rpx;
	}

	.comment .item .f2 .t2 {
		display: flex;
		width: 100%
	}

	.comment .item .f2 .t2 image {
		width: 100rpx;
		height: 100rpx;
		margin: 10rpx;
	}

	.comment .item .f2 .t3 {
		color: #aaa;
		font-size: 24rpx;
	}

	.comment .item .f3 {
		margin: 20rpx auto;
		padding: 0 30rpx;
		height: 60rpx;
		line-height: 60rpx;
		border: 1px solid #E6E6E6;
		border-radius: 30rpx;
		color: #111111;
		font-weight: bold;
		font-size: 26rpx
	}

	.bottombar {
		width: 94%;
		position: fixed;
		bottom: 0px;
		left: 0px;
		background: #fff;
		display: flex;
		height: 100rpx;
		padding: 0 4% 0 2%;
		align-items: center;
		box-sizing: content-box
	}

	.bottombar .f1 {
		flex: 1;
		display: flex;
		align-items: center;
		margin-right: 30rpx
	}

	.bottombar .f1 .item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 80rpx;
		position: relative
	}

	.bottombar .f1 .item .img {
		width: 44rpx;
		height: 44rpx
	}

	.bottombar .f1 .item .t1 {
		font-size: 18rpx;
		color: #222222;
		height: 30rpx;
		line-height: 30rpx;
		margin-top: 6rpx
	}

	.bottombar .op {
		width: 60%;
		border-radius: 36rpx;
		overflow: hidden;
		display: flex;
	}

	.bottombar .tocart {
		height: 72rpx;
		line-height: 72rpx;
		color: #fff;
		border-radius: 0px;
		border: none;
		font-size: 28rpx;
		font-weight: bold
	}

	.bottombar .tobuy {
		flex: 1;
		height: 72rpx;
		line-height: 72rpx;
		color: #fff;
		border-radius: 0px;
		border: none;
		font-size: 28rpx;
		font-weight: bold
	}

	.bottombar .cartnum {
		position: absolute;
		right: 4rpx;
		top: -4rpx;
		color: #fff;
		border-radius: 50%;
		width: 32rpx;
		height: 32rpx;
		line-height: 32rpx;
		text-align: center;
		font-size: 22rpx;
	}

	.bottombar .op2 {
		width: 60%;
		overflow: hidden;
		display: flex;
	}

	.bottombar .tocart2 {
		flex: 1;
		height: 80rpx;
		border-radius: 10rpx;
		color: #fff;
		background: #fa938a;
		font-size: 28rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-right: 10rpx;
	}

	.bottombar .tobuy2 {
		flex: 1;
		height: 80rpx;
		border-radius: 10rpx;
		color: #fff;
		background: #df2e24;
		font-size: 28rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center
	}


	.sharetypecontent {
		height: 250rpx;
		width: 710rpx;
		margin: 20rpx;
		display: flex;
		padding: 50rpx;
		align-items: flex-end
	}

	.sharetypecontent .f1 {
		color: #51c332;
		width: 50%;
		height: 150rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #fff;
		font-size: 28rpx;
		border: 0
	}

	.sharetypecontent button::after {
		border: 0
	}

	.sharetypecontent .f1 .img {
		width: 90rpx;
		height: 90rpx
	}

	.sharetypecontent .f2 {
		color: #51c332;
		width: 50%;
		display: flex;
		flex-direction: column;
		align-items: center
	}

	.sharetypecontent .f2 .img {
		width: 90rpx;
		height: 90rpx
	}

	.sharetypecontent .t1 {
		height: 60rpx;
		line-height: 60rpx;
		color: #666
	}

	.posterDialog {
		position: fixed;
		z-index: 9;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.8);
		top: var(--window-top);
		left: 0
	}

	.posterDialog .main {
		width: 80%;
		margin: 60rpx 10% 30rpx 10%;
		background: #fff;
		position: relative;
		border-radius: 20rpx
	}

	.posterDialog .close {
		position: absolute;
		padding: 20rpx;
		top: 0;
		right: 0
	}

	.posterDialog .close .img {
		width: 32rpx;
		height: 32rpx;
	}

	.posterDialog .content {
		width: 100%;
		padding: 70rpx 20rpx 30rpx 20rpx;
		color: #333;
		font-size: 30rpx;
		text-align: center
	}

	.posterDialog .content .img {
		width: 540rpx;
		height: 960rpx
	}

	.linkDialog {
		background: rgba(0, 0, 0, 0.4);
		z-index: 11;
	}

	.linkDialog .main {
		width: 90%;
		position: fixed;
		top: 50%;
		left: 50%;
		margin: 0;
		-webkit-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);
	}

	.linkDialog .title {
		font-weight: bold;
		margin-bottom: 30rpx;
	}

	.linkDialog .row {
		display: flex;
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 16rpx;
	}

	.linkDialog .row .f1 {
		width: 40%;
		text-align: left;
	}

	.linkDialog .row .f2 {
		width: 60%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: right;
		align-items: center;
	}

	.linkDialog .image {
		width: 28rpx;
		height: 28rpx;
		margin-left: 8rpx;
		margin-top: 2rpx;
	}

	.linkDialog .copyicon {
		width: 28rpx;
		height: 28rpx;
		margin-left: 8rpx;
		position: relative;
		top: 4rpx;
	}

	.paramitem {
		display: flex;
		border-bottom: 1px solid #f5f5f5;
		padding: 20rpx
	}

	.paramitem .f1 {
		width: 30%;
		color: #666
	}

	.paramitem .f2 {
		color: #333
	}

	.paramitem:last-child {
		border-bottom: 0
	}

	.xihuan {
		height: auto;
		overflow: hidden;
		display: flex;
		align-items: center;
		width: 100%;
		padding: 20rpx 160rpx;
		margin-top: 20rpx
	}

	.xihuan-line {
		height: auto;
		padding: 0;
		overflow: hidden;
		flex: 1;
		height: 0;
		border-top: 1px solid #eee
	}

	.xihuan-text {
		padding: 0 32rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center
	}

	.xihuan-text .txt {
		color: #111;
		font-size: 30rpx
	}

	.xihuan-text .img {
		text-align: center;
		width: 36rpx;
		height: 36rpx;
		margin-right: 12rpx
	}

	.prolist {
		width: 100%;
		height: auto;
		padding: 8rpx 20rpx;
	}

	.toptabbar_tab {
		display: flex;
		width: 100%;
		height: 90rpx;
		background: #fff;
		top: var(--window-top);
		z-index: 11;
		position: fixed;
		border-bottom: 1px solid #f3f3f3
	}

	.toptabbar_tab .item {
		flex: 1;
		font-size: 28rpx;
		text-align: center;
		color: #666;
		height: 90rpx;
		line-height: 90rpx;
		overflow: hidden;
		position: relative
	}

	.toptabbar_tab .item .after {
		display: none;
		position: absolute;
		left: 50%;
		margin-left: -16rpx;
		bottom: 10rpx;
		height: 3px;
		border-radius: 1.5px;
		width: 32rpx
	}

	.toptabbar_tab .on {
		color: #323233;
	}

	.toptabbar_tab .on .after {
		display: block
	}

	.scrolltop {
		position: fixed;
		bottom: 160rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		background: rgba(0, 0, 0, 0.4);
		color: #fff;
		border-radius: 50%;
		padding: 12rpx 10rpx 8rpx 10rpx;
		z-index: 9;
	}

	.scrolltop .image {
		width: 100%;
		height: 100%;
	}

	.ggdiaplog_close {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		bottom: -100rpx;
		left: 50%;
		margin-left: -25rpx;
		border: 1px solid rgba(255, 255, 255, 0.5);
		border-radius: 50%;
		padding: 8rpx
	}

	.schemeDialog {
		background: rgba(0, 0, 0, 0.4);
		z-index: 12;
	}

	.schemeDialog .main {
		position: absolute;
		top: 30%
	}

	.schemecon {
		padding: 40rpx 30rpx;
	}

	.copybtn {
		text-align: center;
		margin-top: 30rpx;
		padding: 15rpx 20rpx;
		border-radius: 50rpx;
		color: #fff
	}

	.huang_bz {
		margin: auto;
		padding-left: 6px;
	}

	.huang_nums {
		padding: 2px 5px;
		background: #97e29d;
		border-radius: 10px;
		color: #fff;
		font-size: 10px;
	}


	.view-main {
		padding: 28rpx;
		font-size: 28rpx;
		color: #333333;
		border-radius: 20rpx 20rpx 0 0;

		background: #fff;
	}

	.view-title {
		justify-content: space-between;
		align-items: center;
		display: flex;
		font-size: 30rpx;

	}

	.icon {
		width: 25rpx;
		height: 25rpx;
		margin: auto 0;
	}

	.icon2 {
		width: 45rpx;
		height: 45rpx;
		margin: auto 0;
	}

	.tit-t {
		padding: 50rpx 0 20rpx 0;
		font-size: 22rpx;
	}

	.tit-date {
		padding: 10px;
		background: red;
		color: #fff;
		border-radius: 10rpx;
	}

	.tit-time {
		margin: 10rpx 0;
		border: 1px solid #f1f1f1;
		text-align: center;
		padding: 10rpx;
		border-radius: 10rpx;
	}

	.view-btn {
		background: red;
		color: #fff;
		text-align: center;
		padding: 10px;
		border-radius: 50rpx;
	}
</style>