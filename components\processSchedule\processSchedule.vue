<template>
	<view class="schedule">
		<view v-for="(step, index) in steps" :key="index" :class="['schedule-list', { select: step.statusCondition }]">
			<view class="line-box">
				<view class="check">
					<view class="check-box"></view>
				</view>
				<view v-if="index < steps.length - 1" class="line"></view>
			</view>
			<view class="right-box">
				<view class="title info">{{ step.title }}</view>
				<view class="flex">
					<view style="width: 100%">
						<view class="info">
							状态：
							<view v-if="step.isStatusHidden">——</view>
							<view class="status-i" :class="step.statusClass" >
								{{ step.statusText }}
								<image v-if="step.statusClass" class="icon" :src="getStatusIcon(step.statusIcon)">
								</image>
							</view>
						</view>
						<view class="info tip" v-if="step.hasDownloadButton && index !== 2">{{ data.original_filename }}
						</view>
						<view class="info">备注：{{ step.remark }}</view> 
						<view class="info">时间：{{ step.timeStatus}}</view>
					</view>
					<button class="btn" v-if="step.hasDownloadButton && index === 1"
						@click="downloadFile">下载供电方案</button>
					<button class="btn" v-if="step.hasDownloadButton && index === 2" @click="applyVoltage">申请供电</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import siteinfo from '../../siteinfo.js';

	export default {
		name: 'ProcessSchedule',
		props: {
			status: {
				type: Number,
				default: 0
			},
			data: {
				type: Object,
				default: () => {}
			}
		},
		computed: {
			steps() {
				return [{
						title: '申请办电',
						statusCondition: this.isStatusConditionMet(0, 4),
						statusText: this.getStatusText(0, 4, '需材料补充', '已完成受理'),
						statusClass: this.getStatusClass(0, 4, 'time', 'check'),
						statusIcon: this.getStatusIconName(0, 'time-i', 'checked'),
						remark: this.getStatusText(0, 4, this.data.remark_status_0, this.data.remark_status_1),
						timeStatus: this.getStatusTime(0, 4, this.data.time_status_0, this.data.time_status_1),
						hasDownloadButton: false
					},
					{
						title: '供电方案推荐',
						statusCondition: this.isStatusConditionMet(2, 4),
						statusText: this.getStatusText(2, 4, '方案制定中', '方案发送'),
						statusClass: this.getStatusClass(2, 4, 'time', 'check'),
						statusIcon: this.getStatusIconName(2, 'time-i', 'checked'),
						remark: this.getStatusText(2, 4, this.data.remark_status_2, this.data.remark_status_3),
						timeStatus: this.getStatusTime(2, 4, this.data.time_status_2, this.data.time_status_3),
						hasDownloadButton: this.shouldShowDownloadButton(2, 4)
					},
					{
						title: '验收送电',
						statusCondition: this.isStatusConditionMet(3, 4),
						statusText: this.getAcceptanceStatus(3, 4, '施工中', '已送电', '——'),
						statusClass: this.getAcceptanceStatus(3, 4, 'time', 'check', ''),
						statusIcon: this.getAcceptanceStatus(3, 4, 'time-i', 'checked', ''),
						remark: this.getAcceptanceStatus(3, 4, this.data.remark_status_5,
							this.data.remark_status_6, '——'),
						timeStatus: this.getTimeStatus(3, 4, this.data.time_status_5, this.data.time_status_6, '——'),	
						hasDownloadButton: this.shouldShowDownloadButton(3, 4)
					}
				];
			}
		},
		methods: {
			getStatusTime(minStatus, conditionStatus, textIfMin, textIfCondition) {
				if (this.status === conditionStatus||this.status === -1) return this.status === -1 && minStatus === 0 ? '审核中' : '——';
				return this.status < minStatus ? '——' : this.status > minStatus ? textIfCondition : textIfMin;
			},
			
			getTimeStatus(minStatus, conditionStatus, textIfMin, textIfCondition, text) {
				if (this.status === 6) return textIfCondition;
				return this.status < minStatus || this.status === conditionStatus ? text : textIfMin;
			},
			
			
			getAcceptanceStatus(minStatus, conditionStatus, textIfMin, textIfCondition, text) {
				if (this.status === 6) return textIfCondition;
				return this.status < minStatus || this.status === conditionStatus ? text : textIfMin;
			},
			
			
			isStatusConditionMet(minStatus, excludedStatus) {
				return this.status >= minStatus && this.status !== excludedStatus;
			},
			getStatusText(minStatus, conditionStatus, textIfMin, textIfCondition) {
				if (this.status === conditionStatus||this.status === -1) return this.status === -1 && minStatus === 0 ? '审核中' : '——';
				return this.status < minStatus ? '——' : this.status > minStatus ? textIfCondition : textIfMin;
			},
			getStatusClass(minStatus, conditionStatus, classIfMin, classIfCondition) {
				
				if(this.status === -1&& minStatus === 0){
					return classIfMin
				}
				
				return this.status < minStatus || this.status === conditionStatus ? '' : this.status > minStatus ?
					classIfCondition : classIfMin;
			},
			getStatusIconName(minStatus, iconIfMin, iconIfCondition) {
				return this.status > minStatus ? iconIfCondition : iconIfMin;
			},
			getStatusIcon(iconName) {
				return require(`@/static/highVoltage/${iconName}.png`);
			},
			shouldShowDownloadButton(minStatus, excludedStatus) {
				return this.status > minStatus && this.status !== excludedStatus;
			},
			isWxBrowser() {
				// 判断是否H5微信环境，true为微信浏览器
				const ua = navigator.userAgent.toLowerCase()
				return ua.match(/MicroMessenger/i) == 'micromessenger' ? true : false
			},
			downloadFile() {
				const url = `${siteinfo.siteroot}/${this.data.filepath}`;
				console.log(url);
				if (this.isWxBrowser()) {
					uni.setClipboardData({
						data: url,
						showToast: false,
						success: function() {
							uni.showModal({
								title: '提示信息',
								showCancel: false,
								content: '已复制下载链接，请打开浏览器下载。',
								success: function(res) {
									console.log(res);
								}
							});
						}
					});
					return
				}
				// #ifdef H5
				this.downloadPDFForH5(url);
				// #endif
				// #ifdef MP-WEIXIN
				this.downloadPDFForWeChat(url);
				// #endif
			},
			downloadPDFForWeChat(url) {
				uni.downloadFile({
					url,
					success: (res) => {
						if (res.statusCode === 200) {
							uni.openDocument({
								filePath: res.tempFilePath,
								fileType: 'pdf',
								success: () => console.log('PDF 文件打开成功'),
								fail: (err) => console.error('文件打开失败', err)
							});
						}
					},
					fail: (err) => console.error('下载文件失败', err)
				});
			},
			downloadPDFForH5(url) {
				uni.downloadFile({
					url,
					success: (res) => {
						if (res.statusCode === 200) {
							fetch(res.tempFilePath)
								.then((response) => response.blob())
								.then((blob) => {
									const downloadUrl = window.URL.createObjectURL(blob);
									const link = document.createElement('a');
									link.href = downloadUrl;
									link.download = this.data.original_filename || 'file.pdf';
									document.body.appendChild(link);
									link.click();
									document.body.removeChild(link);
									window.URL.revokeObjectURL(downloadUrl);
								});
						}
					},
					fail: (err) => console.error('下载文件失败', err)
				});
			},
			applyVoltage() {
				this.$emit('apply');
			}
		}
	};
</script>

<style lang="scss">
	.schedule {
		padding: 16rpx 32rpx 28rpx 60rpx;

		.schedule-list {
			display: flex;

			.line-box {
				width: max-content;

				.check {
					width: 40rpx;
					height: 40rpx;
					border: 2rpx solid #adadad;
					border-radius: 50%;
					position: relative;

					.check-box {
						position: absolute;
						top: 0;
						bottom: 0;
						left: 0;
						right: 0;
						margin: auto;
						width: 20rpx;
						height: 20rpx;
						background-color: #adadad;
						border-radius: 50%;
					}
				}

				.line {
					margin: auto;
					width: 2rpx;
					height: 148rpx;
					border: 2rpx solid #adadad;
				}
			}

			&.select {

				.line-box .check,
				.line-box,
				.line-box .line {
					border-color: #508ff2;

					.check-box {
						background-color: #508ff2;
					}
				}

				.title {
					color: #333333;
				}

				.info:not(.tip) {
					color: #666666;
				}
			}
		}

		.right-box {
			width: -webkit-fill-available;
			margin-left: 27rpx;
			color: #bfbfbf;
			font-size: 24rpx;

			.title {
				line-height: 36rpx;
				font-weight: bold;
			}

			.info {
				display: flex;
				align-items: center;
				margin-bottom: 10rpx;

				&.tip {
					line-height: 36rpx;
					color: rgba(0, 122, 255, 1);
					font-size: 24rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 260rpx;
					display: block;
				}
			}

			.status-i {
				display: flex;
				align-items: center;
				line-height: 38rpx;

				.icon {
					margin-left: 10rpx;
					width: 32rpx;
					height: 32rpx;
				}

				&.time {
					color: #ff9502;
				}

				&.check {
					color: #20ac00;
				}
			}

			.flex {
				width: 100%;
				align-items: center;
				justify-content: space-between;
			}
		}

		.btn {
			flex: none;
			width: 244rpx;
			height: 60rpx;
			line-height: 60rpx;
			border-radius: 8rpx;
			background-color: #3d8b6c;
			color: #fff;
			font-size: 28rpx;
			text-align: center;
			font-family: Roboto;
			margin: 0;
		}
	}
</style>
