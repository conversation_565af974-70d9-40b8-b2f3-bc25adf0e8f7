<template>
<view class="container">
	<block v-if="isload">
		<!-- Swiper for product images -->
		<view class="swiper-container">
			<swiper class="swiper" :indicator-dots="false" :autoplay="true" :interval="5000" @change="swiperChange">
				<block v-for="(item, index) in product.pics_list" :key="index">
					<swiper-item class="swiper-item">
						<view class="swiper-item-view"><image class="img" :src="item" mode="widthFix" @tap="previewImage" :data-url="item" :data-urls="product.pics_list"/></view>
					</swiper-item>
				</block>
			</swiper>
			<view class="imageCount" v-if="product.pics_list.length > 1">{{current+1}}/{{product.pics_list.length}}</view>
		</view>

		<!-- Price and Cycle Info -->
		<view class="collage_title flex-bt">
			<text>
				<text class="price">￥{{product.sell_price}}<text style="font-size:24rpx;color:#999">/期</text></text>
				<text class="m-price" v-if="product.market_price > 0">￥{{product.market_price}}</text>
			</text>
			<view :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}" class="ps_title flex-y-center">共{{product.total_period}}期</view>
		</view>

		<!-- Product Header -->
		<view class="header">
			<view class="title">
				<view class="lef">
					<text>{{product.name}}</text>
				</view>
				<view class="share" @tap="shareClick">
					<image src="/static/img/share.png"></image>
					<text>分享</text>
				</view>
			</view>
			<view class="sales_stock">
				<view class="f2">已售:{{product.sales}}</view>
				<!-- 库存信息暂时不明确，周期服务可能没有传统库存 -->
				<!-- <view class="f2">库存:{{product.stock}}</view> -->
			</view>
		</view>

		<!-- Shop Info (if needed) -->
		<view class="shop" v-if="business && business.id">
			<image :src="business.logo" class="p1"/>
			<view class="p2 flex1">
				<view class="t1">{{business.name}}</view>
				<view class="t2">{{business.desc}}</view>
			</view>
			<button class="p4" :style="{background: 'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="goto" :data-url="'/pages/business/index?id='+business.id" data-opentype="reLaunch">进入店铺</button>
		</view>

		<!-- Product Description -->
		<view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">服务描述</view><view class="t2"></view><view class="t1"></view></view>
		<view class="detail">
			<dp :pagecontent="pagecontent"></dp>
		</view>

		<view style="width:100%;height:120rpx;"></view>

		<!-- Bottom Bar -->
		<view class="bottombar flex-row flex-xy-center" :class="menuindex>-1 ? 'tabbarbot' : 'notabbarbot'" v-if="product.status==1">
			<view class="cart flex-col flex-x-center flex-y-center" @tap="goto" :data-url="kfurl" v-if="kfurl!='contact::'">
				<image class="img" src="/static/img/kefu.png"/>
				<view class="t1">客服</view>
			</view>
			<button class="cart flex-col flex-x-center flex-y-center" v-else open-type="contact">
				<image class="img" src="/static/img/kefu.png"/>
				<view class="t1">客服</view>
			</button>
			<view class="favorite flex-col flex-x-center flex-y-center" @tap="addfavorite">
				<image class="img" :src="'/static/img/shoucang' + (isfavorite?'2':'') + '.png'"/>
				<view class="t1">{{isfavorite?'已收藏':'收藏'}}</view>
			</view>
			<view class="tobuy flex1" @tap="tobuy" :style="{background:t('color1')}">
				<text>立即购买</text>
			</view>
		</view>

		<!-- Share Options Popup -->
		<view v-if="sharetypevisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal" style="height:320rpx;min-height:320rpx">
				<view class="popup__content">
					<view class="sharetypecontent">
						<view class="f1" @tap="shareapp" v-if="getplatform() == 'app'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'mp'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'h5'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<button class="f1" open-type="share" v-else>
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</button>
						<view class="f2" @tap="showPoster">
							<image class="img" src="/static/img/sharepic.png"/>
							<text class="t1">生成分享图片</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- Poster Popup -->
		<view class="posterDialog" v-if="showposter">
			<view class="main">
				<view class="close" @tap="posterDialogClose"><image class="img" src="/static/img/close.png"/></view>
				<view class="content">
					<image class="img" :src="posterpic" mode="widthFix" @tap="previewImage" :data-url="posterpic"></image>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			isfavorite: false,
			product:{ pics_list: [] }, // Initialize with empty list
      pagecontent: [], // Initialize as empty array for dp component
			business:{},
      current: 0, // Swiper index
      sharetypevisible: false,
      showposter: false,
      posterpic: "",
			kfurl:'',
    };
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.product.name,pic:this.product.pic,path:'/tiantianshande/yuyue/cycle/productDetail?id=' + this.product.id});
	},
	onShareTimeline:function(){ // Share to Moments
		var sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic,path:'/tiantianshande/yuyue/cycle/productDetail?id=' + this.product.id});
		var query = (sharewxdata.path).split('?')[1];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  methods: {
		getdata: function () {
			var that = this;
			var id = that.opt.id;
			that.loading = true;
			// Use the correct API endpoint
			app.get('ApiPeriodicService/productDetail', {id: id}, function (res) {
				that.loading = false;
				if (res.status == 0) {
					app.alert(res.msg, function(){
						app.goback();
					});
					return;
				}
				
				// Ensure pics_list is always an array
				if (!res.data.pics_list) {
					res.data.pics_list = res.data.pic ? [res.data.pic] : [];
				}
				
				that.product = res.data;
				// Parse product detail if it's a JSON string
				try {
					that.pagecontent = res.data.detail ? JSON.parse(res.data.detail) : [];
				} catch (e) {
					that.pagecontent = []; 
					console.error("Failed to parse product detail JSON:", e);
				}
				that.business = res.business || {}; // Handle potential missing business data
				that.isfavorite = res.isfavorite || false; // Handle potential missing favorite status
				
				uni.setNavigationBarTitle({
					title: res.data.name
				});
				
				// Set up customer service URL
				that.kfurl = '/pagesExt/kefu/index?bid='+ (that.business.id || 0); // Default to platform if no business ID
				if(app.globalData.initdata.kfurl != ''){
					that.kfurl = app.globalData.initdata.kfurl;
				}
				if(that.business && that.business.kfurl){
					that.kfurl = that.business.kfurl;
				}
				
				that.loaded({title:res.data.name, desc:res.data.fuwupoint, pic:res.data.pic}); // Loaded callback
			});
		},
    swiperChange: function (e) {
      this.current = e.detail.current;
    },
    tobuy: function () {
      // Navigate to the buy page, passing the product ID
			// Use the new buy page path
      app.goto('/yuyue/cycle/buy?product_id=' + this.product.id);
    },
    addfavorite: function () {
      var that = this;
      var proid = that.product.id;
			app.showLoading('操作中');
			// Use the correct API for periodic service favorites
      app.post('ApiPeriodicService/addfavorite', {proid: proid, type: 'periodic_service'}, function (data) { 
				app.showLoading(false);
        if (data.status == 1) {
          that.isfavorite = !that.isfavorite;
        }
        app.success(data.msg);
      });
    },
    shareClick: function () {
      this.sharetypevisible = true;
    },
    handleClickMask: function () {
      this.sharetypevisible = false;
    },
    showPoster: function () {
      var that = this;
      that.showposter = true;
      that.sharetypevisible = false;
      app.showLoading('努力生成中');
			// Use the correct API for periodic service posters
			app.post('ApiPeriodicService/getposter', {proid: that.product.id}, function (data) {
				app.showLoading(false);
        if (data.status == 0) {
          app.alert(data.msg);
        } else {
          that.posterpic = data.poster;
        }
      });
    },
    posterDialogClose: function () {
      this.showposter = false;
    },
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			this.sharetypevisible = false;
		},
		shareapp:function(){
			var that = this;
			uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.product.name;
						sharedata.summary = that.product.fuwupoint || '';
						sharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/' + 'tiantianshande/yuyue/cycle/productDetail?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;
						sharedata.imageUrl = that.product.pic;
						// Customize share content based on global settings if needed
						// ... (similar logic as in the original file if required)
						uni.share(sharedata);
					}
        }
      });
		}
	}
}
</script>

<style>
/* Reuse styles from tiantianshande/pagesExt/cycle/product.vue */
.container { background-color: #f8f8f8; }
.swiper-container{position:relative}
.swiper {width: 100%;height: 750rpx;overflow: hidden;}
.swiper-item-view{width: 100%;height: 750rpx;}
.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}

.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}

.collage_title{width:100%;height:110rpx;display:flex;align-items:center;padding:0 30rpx;background-color: #fff;margin-bottom: 20rpx;}
.collage_title .price {font-size:50rpx;color:#FF5043;font-weight: 700;}
.m-price{margin-left: 10rpx;font-size: 24rpx;color: #aaa;text-decoration: line-through;}
.ps_title{height: 35rpx;background: #FFDED9;border-radius: 4rpx;padding: 0 10rpx;font-size: 20rpx;font-family: PingFang SC;color: #FF3143;margin-left: 20rpx;line-height: 35rpx;}

.header {width: 100%;padding: 0 3%;background: #fff; border-radius: 10rpx; margin: 0 3% 20rpx 3%; width: 94%; }
.header .title {padding: 20rpx 0px;line-height:44rpx;font-size:32rpx;display:flex; border-bottom: 1px solid #f5f5f5;}
.header .title .lef{display:flex;flex-direction:column;justify-content: center;flex:1;color:#222222;font-weight:bold}
.header .title .share{width:88rpx;height:88rpx;padding-left:20rpx;border-left:1px solid #f5f5f5;text-align:center;font-size:24rpx;color:#222;display:flex;flex-direction:column;align-items:center; justify-content: center;}
.header .title .share image{width:32rpx;height:32rpx;margin-bottom:4rpx}

.sales_stock{ display: flex; justify-content: space-between; height: 80rpx; line-height: 80rpx; font-size: 24rpx; color: #777777; padding: 0 10rpx;}

.shop{display:flex;align-items:center;width: 94%; background: #fff; margin: 0 3% 20rpx 3%; padding: 20rpx 3%;position: relative; min-height: 100rpx; border-radius: 10rpx;}
.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}
.shop .p2{padding-left:10rpx}
.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}
.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}
.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}

.detail{min-height:200rpx;background-color: #fff; padding: 20rpx; width: 94%; margin: 0 3%; border-radius: 10rpx;}

.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:40rpx;margin-bottom:30rpx}
.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}
.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}
.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}

.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff; padding: 10rpx 0; border-top: 1px solid #eee;}
.bottombar .favorite{width: 18%;color:#707070;font-size:26rpx}
.bottombar .favorite .img{ width:44rpx;height:44rpx}
.bottombar .favorite .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:2rpx}
.bottombar .cart{width: 18%;font-size:26rpx;color:#707070}
.bottombar .cart .img{ width:44rpx;height:44rpx}
.bottombar .cart .t1{font-size:24rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:2rpx}
.bottombar .tobuy{font-weight: bold;height: 80rpx;color: #fff; font-size:28rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin: 0 30rpx 0 15rpx;border-radius: 40rpx;}

/* Popups */
.popup__container { position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 100; }
.popup__overlay { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); }
.popup__modal { position: absolute; left: 0; right: 0; bottom: 0; background-color: #fff; border-top-left-radius: 20rpx; border-top-right-radius: 20rpx; padding-bottom: env(safe-area-inset-bottom); max-height: 80%; overflow-y: auto; }
.popup__title { display: flex; align-items: center; justify-content: space-between; padding: 30rpx; border-bottom: 1px solid #eee; }
.popup__title-text { font-size: 32rpx; font-weight: bold; color: #333; }
.popup__close { width: 36rpx; height: 36rpx; }
.popup__content { padding: 20rpx; }

.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx 0;display:flex;padding:50rpx;align-items:flex-end}
.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}
.sharetypecontent button::after{border:0}
.sharetypecontent .f1 .img{width:90rpx;height:90rpx}
.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}
.sharetypecontent .f2 .img{width:90rpx;height:90rpx}
.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}

.posterDialog{ position:fixed;z-index:999;width:100%;height:100%;background:rgba(0,0,0,0.8);top:0;left:0; display: flex; align-items: center; justify-content: center; }
.posterDialog .main{ width:80%; background:#fff;position:relative;border-radius:20rpx; max-height: 90%; overflow-y: auto;}
.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0; z-index: 10;}
.posterDialog .close .img{ width:40rpx;height:40rpx;}
.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}
.posterDialog .content .img{width:100%;height:auto}
</style> 