<template>
	<view class="daihuobiji-detail-page" :style="weixin ? 'padding-top:' + totalBarHeight + 'px;' : ''">
	<!-- 	<DetailNav v-if="detailInfo.id" :userInfo="detailInfo" :noteId="String(detailInfo.id)" />
	 -->
	<DetailNavw v-if="detailInfo.id" :userInfo="detailInfo" :noteId="String(detailInfo.id)" />


		<view class="cover-swipper">
			<swiper class="swiper" :style="{ height: swiperHeight + 'px' }" circular indicator-dots="#D9D9D9"
				:autoplay="false" :interval="interval" indicator-active-color="#E3582F" :duration="duration"
				@change="onSwiperChange">
				<swiper-item v-for="(item, index) in detailInfo.videoAndImg" :key="index">
					<view class="swiper-item">
						<image v-if="item.type == 'img'" :src="item.url" class="cover-img" mode="widthFix"
							@load="onImageLoad($event, index)" @tap="openImagePreview(index)"></image>
						<video :src="item.url" v-if="item.type == 'video'"></video>
					</view>
				</swiper-item>

			</swiper>
		</view>

		<view class="info-box">
			<text class="title-text">{{ detailInfo.title || '' }}</text>
			<rich-text class="title-text2" :nodes="detailInfo.content"></rich-text>
			<view class="create-time">{{ detailInfo.showtime }}</view>
		</view>

		<view class="comment-box">
			<view class="top-tips">共{{ datalist.length }}条评论</view>

			<view class="comment-input-box" style="margin: 15px 0;" @tap="goto"
				:data-url="'pinglun?type=0&id=' + detailInfo.id">
				<input class="comment-input" placeholder="请在此评论" />
			</view>

			<view class="plbox_content" style="padding-bottom: 50px;" id="datalist">
				<block v-for="(item, idx) in datalist" :key="idx">
					<view class="item1 flex">
						<view class="f1 flex0">
							<image :src="item.headimg"></image>
						</view>
						<view class="f2 flex-col">
							<text class="t1">{{ item.nickname }}</text>
							<text class="t11">{{ item.createtime }}</text>
							<view class="t2 plcontent">
								<parse :content="item.content" />
							</view>
							<block v-if="item.replylist.length > 0">
								<view class="relist">
									<block v-for="(hfitem, index) in item.replylist" :key="index">
										<view class="item2">
											<view class="f1">
												{{ hfitem.nickname }}
												<text class="t1">{{ hfitem.createtime }}</text>
												<text v-if="hfitem.mid == mid" class="phuifu"
													style="font-size:20rpx;margin-left:20rpx;font-weight:normal"
													@tap="delplreply" :data-id="hfitem.id">
													删除
												</text>
											</view>
											<view class="f2 plcontent">
												<parse :content="hfitem.content" />
											</view>
										</view>
									</block>
								</view>
							</block>
							<view class="t3 flex">
								<view class="flex1">
									<text class="phuifu" style="cursor:pointer" @tap="goto"
										:data-url="'pinglun?type=1&id=' + detailInfo.id + '&hfid=' + detailInfo.id">
										回复
									</text>
									<text v-if="item.mid == mid" class="phuifu" style="cursor:pointer;margin-left:20rpx"
										@tap="delpinglun" :data-id="item.id">
										删除
									</text>
								</view>
								<view class="flex-y-center pzan" @tap="pzan" :data-id="detailInfo.id" :data-index="idx">
									<image :src="pre_url+'/static/img/lt_like' + (item.iszan == 1 ? '2' : '') + '.png'"></image>
									{{ item.zan }}
								</view>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>

		<view class="footer-box">
			<view class="buy">
				<view class="good-img" style="margin-top: 0px;">
					<image style="width: 100%;height: 100%;border-radius: 50px;" :src="yindaologo"></image>
				</view>
				<view class="buy-text" @tap="openShop">
					<text class="text-tips">{{ linkname }}</text>
					<!-- <text class="price-text">￥{{price}}</text> -->
				</view>
			</view>
			<view class="footer-actions">
				<view class="action-item" @tap="goto" :data-url="'pinglun?type=0&id=' + detailInfo.id">
					<image class="action-item-img" :src="pre_url+'/static/img/daihuobiji_comment.png'"></image>
					<text class="action-item-label">评论</text>
				</view>
				<!-- 
                <view class="action-item" @tap="goto" :data-url="'pinglun?type=0&id=' + detailInfo.id">
                    <image class="action-item-img" src="../../static/img/daihuobiji_comment.png"></image>
                    <text class="action-item-label">收藏</text>
                </view> 
                -->
				<view class="action-item" @tap="zan" :data-id="detailInfo.id">
					<image class="action-item-img" :src="pre_url+'/static/img/lt_like' + (iszan ? '2' : '') + '.png'"></image>
					<text class="action-item-label">
						{{ detailInfo.zan }}
					</text>
				</view>
			</view>
		</view>



		<!-- 商品购物 -->
		<uni-popup ref="shopShow" type="bottom" style="z-index: 999;">
			<view class="viewShop">
				<view style="text-align: center;font-weight: bold;margin-bottom: 20rpx;position: sticky;top: 0;">
					文中提到的商品（{{matchedData.length}}）
				</view>

				<view class="cart-container">
					<scroll-view :scroll-top="0" scroll-y="true" style="height: 50vh;">
						<view class="cart-item" v-for="(item, index) in matchedData" :key="index"
							@click="toggleSelection(item)">
							<view class="item-selector">
								<checkbox :checked="item.selected" style="transform:scale(0.8)"></checkbox>
							</view>
							<view class="image-box">
								<image class="item-image" :src="item.pic" mode="heightFix"></image>
							</view>

							<view class="item-info">
								<text class="item-name">{{ item.name }}</text>
								<view style="display: flex;justify-content: space-between;width: 100%;">
									<text class="item-price">
										<span>￥</span>
										<span style="font-size: 34rpx;font-weight: bold;">{{ item.sell_price }}</span>
									</text>
									<view style="border-radius: 50%;background-color: #14c8ff;
									width: 50rpx;height: 50rpx;text-align: center;line-height: 60rpx;" @click.stop="addCartOne(item)">
										<image :src="pre_url+'/static/icon/icon2.png'" mode="widthFix" style="width: 36rpx;">
										</image>
									</view>
								</view>

							</view>

						</view>
					</scroll-view>

					<view style="position: sticky;bottom: 0;
					height: 100rpx;display: flex;justify-content: space-between;
					align-items: center;padding: 0 20rpx;">
						<view @tap="shopAllSelectedClick">
							<checkbox :checked="shopAllSelected" style="transform:scale(0.8)"></checkbox>
							全选
						</view>
						<view :class="isAnyItemSelected?'shopButtonActive':'shopButton'" @click="shop">
							一键加购
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

	</view>
</template>
<script>
	import DetailNavw from '../components/DetailNavw.vue'
	var app = getApp();

	export default {
		components: {
			DetailNavw
		},
		data() {
			return {
				opt: {},
				indicatorDots: true,
				autoplay: true,
				interval: 2000,
				duration: 500,
				detailInfo: {},
				datalist: [],
				price: 0,
				pre_url: app.globalData.pre_url,
				need_call: false,
				pagenum: 1,
				nomore: false,
				iszan: "",
				yindaologo: '',
				weixin: false,
				yindao_link: '',
				linkname: '',
				mid: "",
				plcount: 0,
				myscore: 0,
				nodata: false,
				imageHeights: [], // 存储每张图片的显示高度
				swiperHeight: 300, // Swiper 初始高度
				imagesLoaded: 0, // 已加载图片数量
				maxSwiperHeight: 500, // Swiper 最大高度
				showImagePreview: false, // 控制放大图片遮罩层的显示
				currentPreviewIndex: 0, // 当前预览的图片索引
				currentSwiperIndex: 0, // 当前 Swiper 的索引
				statusBarHeight: 0,
				navigationBarHeight: 0,
				totalBarHeight: 0, // 状态栏高度 + 导航栏高度
				matchedData: [],
				shopAllSelected: false
			}
		},
		computed: {
			isAnyItemSelected() {
				return this.matchedData.some(item => item.selected);
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();

			// #ifdef MP-WEIXIN
			this.weixin = true
			// 获取系统信息
			const systemInfo = wx.getSystemInfoSync();

			// 获取状态栏高度
			this.statusBarHeight = systemInfo.statusBarHeight;

			// 计算导航栏高度
			let platform = systemInfo.platform;
			let navBarHeight = 0;
			if (platform === 'android') {
				navBarHeight = 48;
			} else if (platform === 'ios') {
				navBarHeight = 44;
			} else {
				navBarHeight = 48; // 默认导航栏高度
			}
			this.navigationBarHeight = navBarHeight;

			// 计算总高度
			this.totalBarHeight = this.statusBarHeight + this.navigationBarHeight;

			// #endif
		},
		methods: {
			getdata: function(loadmore) {
				var that = this;
				var id = that.opt.id;
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
					this.nomore = false;
				}
				var pagenum = that.pagenum;
				app.get('Apidaihuobiji/detail', {
					id: id,
					pagenum: pagenum
				}, function(res) {

					that.loading = false;
					if (res.need_call) {
						that.need_call = true;
					}

					var data = res.datalist;
					if (pagenum === 1) {
						that.mid = res.mid;
						that.datalist = res.datalist;
						that.plcount = res.plcount;
						that.iszan = res.iszan;
						that.detailInfo = res.detail;
						console.log('that.detailInfo.video', that.detailInfo.video)
						that.detailInfo.video = that.detailInfo.video == '' ? [] : that.detailInfo.video.split(
							',')
						that.detailInfo.videoAndImg = []
						if (that.detailInfo.pics.length > 0) { // 图片
							that.detailInfo.pics.forEach(item => {
								that.detailInfo.videoAndImg.push({
									url: item,
									type: 'img'
								})
							})
						}
						if (that.detailInfo.video.length > 0) { // 视频
							that.detailInfo.video.forEach(item => {
								that.detailInfo.videoAndImg.push({
									url: item,
									type: 'video'
								})
							})
						}
						that.myscore = res.myscore;
						that.yindaologo = res.yindaologo;
						that.yindao_link = res.yindao_link;
						that.linkname = res.linkname;
						if (data.length === 0) {
							that.nodata = true;
						} else {
							that.nodata = false;
						}
						// 重置图片相关数据
						that.imageHeights = [];
						that.swiperHeight = 300; // 确保 Swiper 有初始高度
						that.imagesLoaded = 0;
					} else {
						if (data.length === 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}

					that.pagenum += 1;
				})
			},

			zan: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				app.post("Apidaihuobiji/zan", {
					id: id
				}, function(res) {
					that.iszan = res.type !== 0;
					that.detailInfo.zan = res.zancount;
				});
			},
			pzan: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				var index = e.currentTarget.dataset.index;
				var datalist = that.datalist;
				app.post("Apidaihuobiji/pzan", {
					id: id
				}, function(res) {
					datalist[index].iszan = res.type !== 0 ? 1 : 0;
					datalist[index].zan = res.zancount;
					that.datalist = datalist;
				});
			},

			delplreply: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				app.confirm('确定要删除吗?', function() {
					app.post("Apidaihuobiji/delplreply", {
						id: id
					}, function(res) {
						app.success(res.msg);
						setTimeout(function() {
							that.onLoad(that.opt);
						}, 1000);
					});
				});
			},

			delpinglun: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				app.confirm('确定要删除这条评论吗?', function() {
					app.post("Apidaihuobiji/delpinglun", {
						id: id
					}, function(res) {
						app.success(res.msg);
						setTimeout(function() {
							that.onLoad(that.opt);
						}, 1000);
					});
				});
			},




			onImageLoad: function(e, index) {
				// 获取图片尺寸
				var imgWidth = e.detail.width;
				var imgHeight = e.detail.height;

				// 防止宽高为零
				if (imgWidth === 0 || imgHeight === 0) {
					return;
				}

				// 计算显示高度
				var viewWidth = uni.getSystemInfoSync().windowWidth;
				var ratio = imgHeight / imgWidth;
				var viewHeight = viewWidth * ratio;

				// 如果图片高度超过最大高度，使用最大高度
				var displayHeight = viewHeight > this.maxSwiperHeight ? this.maxSwiperHeight : viewHeight;

				// 存储每张图片的显示高度
				this.$set(this.imageHeights, index, displayHeight);

				// 增加已加载图片数量
				this.imagesLoaded += 1;

				// 当所有图片加载完成后，计算 Swiper 的高度
				if (this.imagesLoaded === this.detailInfo.pics.length) {
					// 取所有图片显示高度中的最大值作为 Swiper 的高度
					this.swiperHeight = Math.max(...this.imageHeights);

					// 更新视图
					this.$forceUpdate();
				}
			},

			onSwiperChange: function(e) {
				this.currentSwiperIndex = e.detail.current;
			},

			openImagePreview(index) {
				// 调用 uni.previewImage 打开图片预览
				uni.previewImage({
					current: this.detailInfo.pics[index], // 当前预览的图片
					urls: this.detailInfo.pics, // 所有图片的 URL 列表
					indicator: 'number', // 预览图片时的指示器样式，可选 'default' 或 'number'
					loop: true, // 是否循环显示图片
				});
			},
			closeImagePreview: function() {
				this.showImagePreview = false;
			},

			// 打开购物车
			openShop() {
				console.log('this.detailInfo',this.detailInfo.productids)
				this.getShopList(this.detailInfo.productids)
				this.$refs.shopShow.open('top')
			},
			// 全选
			shopAllSelectedClick() {
				this.shopAllSelected = !this.shopAllSelected
				this.matchedData.forEach(item => {
					item.selected = this.shopAllSelected
				})
			},
			toggleSelection(item) {
				this.$set(item, 'selected', !item.selected); // 使用 $set 确保响应性
				this.shopAllSelected = this.matchedData.every(item => item.selected)
			},
			shop() {
				if (!this.isAnyItemSelected) {
					app.success('请选择商品');
					return;
				}

				this.matchedData.forEach(async item => {
					await this.addCart(item.id)
				})
				app.success('添加成功');
				this.$refs.shopShow.close()
			},
			async addCartOne(item) {
				this.addCart(item.id)
				app.success('添加成功');
			},
			addCart(proid) {
				return new Promise((resolve, reject) => {
					app.post('ApiShop/addcart', {
						proid,
						num: 1
					}, function(res) {
						resolve('添加成功')
					});
				});
			},

			// 获取商品列表
			getShopList(ids) {
				function product(id) {
					return new Promise((resolve, reject) => {
						app.post('ApiShop/product', {
							id
						}, function(res) {
							let obj = {
								pic: res.product.pic,
								name: res.product.name,
								sell_price: res.product.sell_price
							}
							resolve(obj)
						});
					});
				}
				let idsArr = ids.split(',')
				let shopList = []
				idsArr.forEach(async item => {
					const res = await product(item)
					shopList.push(res)
				})
				this.matchedData = shopList
			}
		}
	}
</script>




<style lang="scss" scoped>
	.daihuobiji-detail-page {
		width: 100vw;
		height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 100rpx;
		padding-top: 100rpx;

		/* 标题部分的样式 */
		.title-box {
			padding: 20rpx;
			text-align: center;
			background-color: #fff;
			margin-bottom: 20rpx;
		}

		.title-text {
			font-size: 40rpx;
			font-weight: bold;
			color: #333;
		}

		.title-box2 {
			padding: 20rpx;
			text-align: center;
			background-color: #fff;
			margin-bottom: 20rpx;
		}

		.title-text2 {
			font-size: 35rpx;
			font-weight: bold;
			color: #6d6d6d;
		}

		.cover-swipper {
			background-color: #FFFFFF;
			width: 100%;

			.swiper {
				width: 100%;
				background-color: #FFFFFF;
			}

			.swiper-item {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 100%;
				/* 继承 Swiper 高度 */
			}

			.cover-img {
				width: 100%;
				height: auto;
			}
		}

		/* 放大图片的遮罩层样式 */
		.image-preview-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.8);
			z-index: 1000;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.preview-image {
			max-width: 90%;
			max-height: 90%;
		}

		.close-button {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			width: 60rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			background-color: rgba(0, 0, 0, 0.6);
			color: #fff;
			font-size: 50rpx;
			border-radius: 30rpx;
			cursor: pointer;
		}


		.info-box {
			background-color: #fff;
			padding: 24rpx;

			.desc {
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 26rpx;
				color: #666666;
				line-height: 30rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.create-time {
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 22rpx;
				color: #999999;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin-top: 32rpx;
			}
		}

		.info-box {
			background-color: #fff;
			padding: 24rpx;

			.desc {
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 26rpx;
				color: #666666;
				line-height: 30rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.create-time {
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 22rpx;
				color: #999999;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin-top: 32rpx;
			}
		}

		.info-box {
			background-color: #fff;
			padding: 24rpx;

			.desc {
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 26rpx;
				color: #666666;
				line-height: 30rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.create-time {
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 22rpx;
				color: #999999;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin-top: 32rpx;
			}
		}

		.comment-box {
			padding: 24rpx;
			background-color: #FFFFFF;
			margin-top: 20rpx;
			box-sizing: border-box;

			.top-tips {
				height: 32rpx;
				font-family: MiSans VF, MiSans VF;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				line-height: 28rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin-bottom: 24rpx;
			}

			.comment-input-box {
				height: 66rpx;
				background: #F5F5F5;
				border-radius: 50rpx;
				box-sizing: border-box;
				display: flex;
				align-items: center;

				.comment-input {
					width: 100%;
					height: 100%;
					font-family: MiSans VF, MiSans VF;
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
					line-height: 28rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
					padding: 0rpx 24rpx;
					border: none;
					background: transparent;
				}
			}

			.plbox_content {
				padding-top: 20rpx;

				.plcontent {
					vertical-align: middle;
					color: #111;
					word-wrap: break-word;
					word-break: break-all;
				}

				.plcontent image {
					width: 44rpx;
					height: 44rpx;
					vertical-align: inherit;
				}

				.item1 {
					width: 100%;
					margin-bottom: 20rpx;
					display: flex;
					flex-direction: row;
				}

				.item1 .f1 {
					width: 80rpx;
					flex-shrink: 0;
				}

				.item1 .f1 image {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
					object-fit: cover;
				}

				.item1 .f2 {
					flex: 1;
					display: flex;
					flex-direction: column;
				}

				.item1 .f2 .t1 {
					color: #222;
					font-weight: bold;
					font-size: 24rpx;
				}

				.item1 .f2 .t11 {
					color: #999999;
					font-size: 20rpx;
				}

				.item1 .f2 .t2 {
					color: #000;
					margin: 10rpx 0;
					line-height: 60rpx;
					font-size: 24rpx;
				}

				.item1 .f2 .t3 {
					color: #999;
					font-size: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
				}

				.item1 .f2 .pzan {
					display: flex;
					align-items: center;
					cursor: pointer;

					image {
						width: 32rpx;
						height: 32rpx;
						margin-right: 16rpx;
					}
				}

				.item1 .f2 .phuifu {
					color: #507DAF;
					font-size: 24rpx;
					cursor: pointer;
				}

				.relist {
					width: 100%;
					background: #F6F5F8;
					padding: 4rpx 20rpx;
					margin-bottom: 20rpx;

					.item2 {
						font-size: 24rpx;
						margin-bottom: 10rpx;
						display: flex;
						flex-direction: column;
					}

					.item2 .f1 {
						font-weight: bold;
						color: #222;
						width: 100%;
						display: flex;
						align-items: center;
					}

					.item2 .f1 .t1 {
						font-weight: normal;
						color: #999999;
						font-size: 20rpx;
						padding-left: 20rpx;
					}
				}
			}
		}

		.footer-box {
			position: fixed;
			bottom: 0rpx;
			right: 0rpx;
			left: 0rpx;
			height: 100rpx;
			background: #FFFFFF;
			box-shadow: inset 0rpx 1rpx 0rpx 0rpx #EAEAEA;
			border-radius: 0rpx;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			padding: 10rpx 24rpx;
			box-sizing: border-box;
			z-index: 98;

			.buy {
				width: 280rpx;
				height: 100%;
				background: #F5F5F5;
				border-radius: 50rpx;
				padding: 0rpx 24rpx;
				display: flex;
				align-items: center;

				.good-img {
					width: 52rpx;
					height: 52rpx;
					margin-right: 7rpx;
					flex-shrink: 0;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 100%;
						height: 100%;
						border-radius: 50px;
						object-fit: cover;
					}
				}

				.buy-text {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: flex-start;
					font-family: MiSans VF, MiSans VF;
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
					line-height: 28rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;

					.text-tips {
						margin-bottom: 2rpx;
					}
				}
			}

			.footer-actions {
				width: calc(100% - 300rpx);
				display: flex;
				flex-direction: row;

				.action-item {
					display: flex;
					flex: 1;
					align-items: center;
					justify-content: center;
					flex-direction: column;

					.action-item-img {
						width: 44rpx;
						height: 44rpx;
						margin-bottom: 4rpx;
					}

					.action-item-label {
						font-family: MiSans VF, MiSans VF;
						font-weight: 400;
						font-size: 24rpx;
						color: #333333;
						line-height: 28rpx;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}
				}
			}
		}

		.covermy {
			position: fixed;
			z-index: 99999;
			bottom: 0;
			right: 0;
			width: 130rpx;
			height: 130rpx;
			box-sizing: content-box;
		}

		.covermy image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
</style>

<style>
	.viewShop {
		height: 60vh;
		width: 100%;
		border-radius: 20rpx 20rpx 0 0;
		background-color: #fff;
		padding-top: 20rpx;
	}
	.cart-container {
		display: flex;
		flex-direction: column;
	}

	.cart-item {
		display: flex;
		align-items: center;
		padding: 10px;
	}

	.item-selector {
		margin-right: 10px;
	}

	.image-box {
		background-color: #f4f4f4;
		border-radius: 10rpx;
		width: 130rpx;
		height: 130rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.item-image {
		height: 70%;
	}

	.item-info {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 130rpx;
		width: 67%;
	}

	.item-name {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}

	.item-price {
		font-size: 14px;
		color: #e65602;
	}

	.item-quantity {
		display: flex;
		align-items: center;
		margin-left: auto;
	}

	.item-quantity button {
		width: 30px;
		height: 30px;
		border: 1px solid #ddd;
		background-color: #fff;
		cursor: pointer;
	}

	.item-quantity text {
		margin: 0 5px;
	}

	.shopButton {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #a0a0a0;
		background-color: #ddd;
		font-size: 20rpx;
	}

	.shopButtonActive {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #fff;
		background-color: #eb8200;
		font-size: 20rpx;
	}
</style>