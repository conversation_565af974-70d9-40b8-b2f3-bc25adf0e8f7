<template>
<view class="container">
	<block v-if="isload">
		<view class="user-profile">
			<view class="user-info">
				<view class="avatar-container">
					<image class="user-avatar" :src="userInfo.headimg || pre_url+'/static/img/default_avatar.png'" mode="aspectFill"></image>
				</view>
				<view class="user-text">
					<view class="user-name">{{ userInfo.nickname || '微信昵称' }}</view>
					<view class="user-welcome">欢迎回来，继续学习吧</view>
				</view>
			</view>
			<view class="page-title">我的笔记</view>
		</view>
		
		<!-- 搜索和排序区域 -->
		<view class="search-sort-area">
			<view class="search-box">
				<input 
					type="text" 
					v-model="keyword" 
					placeholder="搜索笔记内容/课程/章节" 
					@confirm="onSearch"
					class="search-input"
				/>
				<text class="iconfont icon-sousuo search-icon" @tap="onSearch"></text>
			</view>
			<!-- <view class="sort-box">
				<view 
					:class="['sort-item', sortType === 'new' ? 'active' : '']" 
					@tap="changeSort('new')"
				>最新</view>
				<view 
					:class="['sort-item', sortType === 'hot' ? 'active' : '']" 
					@tap="changeSort('hot')"
				>最热</view>
			</view> -->
		</view>
		
		<!-- 笔记内容 -->
		<view class="notes-container">
			<view class="notes-list">
				<block v-if="allNotes && allNotes.length > 0">
					<view class="note-item" v-for="(note, index) in allNotes" :key="note.id" @tap="viewNoteDetail(note.id)">
						<view class="note-header">
							<view class="course-info">
								<image class="course-pic" :src="note.kecheng_pic" mode="aspectFill"></image>
								<view class="course-detail">
									<text class="course-name">{{note.kecheng_name}}</text>
									<text class="note-type">{{note.note_type_name}}</text>
								</view>
							</view>
							<view class="chapter-name" v-if="note.chapter_name">{{note.chapter_name}}</view>
						</view>
						
						<view class="note-content">
							<rich-text :nodes="note.content"></rich-text>
						</view>
						
						<view class="note-study-info">
							<text class="progress" v-if="note.kechengset && note.kechengset.notes_need_progress == 1">学习进度: {{note.study_progress}}%</text>
							<text class="time-point" v-if="note.note_time">时间点: {{note.note_time}}</text>
						</view>
						
						<view class="note-footer">
							<view class="note-time">{{ note.createtime_format }}</view>
							<view class="note-actions">
								<view class="action-btn edit" @tap.stop="editNote(note)">
									<text class="iconfont icon-bianji"></text>
									<text>编辑</text>
								</view>
								<view class="action-btn delete" @tap.stop="deleteNote(note.id)">
									<text class="iconfont icon-shanchu"></text>
									<text>删除</text>
								</view>
							</view>
						</view>
					</view>
				</block>
				<nomore text="没有更多笔记了" v-if="notesNomore"></nomore>
				<nodata text="暂无笔记记录" v-if="notesNodata">
					<image slot="img" :src="pre_url+'/static/img/nodata.png'" mode="aspectFit"></image>
				</nodata>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      nomore: false,
      nodata: false,
      pagenum: 1,
      pernum: 10, //每页显示条目数
      userInfo: {},
      pre_url: app.globalData.pre_url,
      
      // 笔记相关数据
      allNotes: [], // 所有笔记列表
      notesNomore: false, // 笔记是否加载完
      notesNodata: false, // 笔记是否为空
      notesPagenum: 1, // 笔记页码
      keyword: '', // 搜索关键词
      sortType: 'new', // 排序方式：new=最新，hot=最热
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getTabbarMenu();
    this.getUserInfo(); // 获取用户信息
    this.getAllNotes(true); // 获取所有笔记
  },
  onPullDownRefresh: function () {
    this.getAllNotes(true);
  },
  onReachBottom: function () {
    if (!this.notesNodata && !this.notesNomore) {
      this.notesPagenum = this.notesPagenum + 1;
      this.getAllNotes(false);
    }
  },
  methods: {
    getTabbarMenu: function() {
      var currentRoute = '/' + this.__route__;
      var tarbar = app.globalData.tarbar;
      if (tarbar && tarbar.list) {
        for (var i = 0; i < tarbar.list.length; i++) {
          if (tarbar.list[i].pagePath == currentRoute) {
            this.menuindex = i;
          }
        }
      }
      if(this.opt && this.opt.hidettabbar == 1){
        uni.hideTabBar();
      }
    },
    getUserInfo: function() {
      var that = this;
      app.get('ApiMy/getCurrentUserInfo', {}, function(res) {
        if(res.status == 1) {
          that.userInfo = res.data;
        }
      });
    },
    // 获取所有笔记
    getAllNotes: function(isRefresh) {
      var that = this;
      
      if(isRefresh) {
        that.notesPagenum = 1;
        that.allNotes = [];
      }
      
      that.loading = true;
      that.notesNodata = false;
      that.notesNomore = false;
      
      app.post('ApiKechengNotes/getAllNotes', {
        pagenum: that.notesPagenum,
        pernum: that.pernum,
        keyword: that.keyword,
        sort_type: that.sortType
      }, function(res) {
        that.loading = false;
        that.isload = true;
        uni.stopPullDownRefresh();
        
        if(res.status == 1) {
          var data = res.data.list || [];
          if(that.notesPagenum == 1) {
            that.allNotes = data;
            if(data.length == 0) {
              that.notesNodata = true;
            }
          } else {
            that.allNotes = that.allNotes.concat(data);
          }
          
          if(data.length < that.pernum) {
            that.notesNomore = true;
          }
        } else {
          app.error(res.msg);
        }
      });
    },
    // 编辑笔记
    editNote: function(note) {
      app.goto('mldetail?id=' + note.chapter_id + '&kcid=' + note.kcid + '&note_id=' + note.id);
    },
    // 查看笔记详情
    viewNoteDetail: function(noteId) {
      app.goto('bijixiangqing?id=' + noteId);
    },
    // 删除笔记
    deleteNote: function(noteId) {
      var that = this;
      app.confirm('确定要删除这条笔记吗？', function() {
        app.showLoading('删除中...');
        app.post('ApiKechengNotes/deleteNote', {
          id: noteId
        }, function(res) {
          app.showLoading(false);
          if(res.status == 1) {
            app.success(res.msg);
            // 从列表中移除被删除的笔记
            that.allNotes = that.allNotes.filter(note => note.id !== noteId);
            if(that.allNotes.length === 0) {
              that.notesNodata = true;
            }
          } else {
            app.error(res.msg);
          }
        });
      });
    },
    // 搜索笔记
    onSearch() {
      this.notesPagenum = 1;
      this.getAllNotes(true);
    },
    
    // 切换排序方式
    changeSort(type) {
      if(this.sortType === type) return;
      this.sortType = type;
      this.notesPagenum = 1;
      this.getAllNotes(true);
    },
  }
};
</script>
<style>
.container {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  background-color: #f7f9fd;
  min-height: 100vh;
}

/* 用户信息区：增强视觉层次感 */
.user-profile {
  padding: 40rpx 30rpx 30rpx 30rpx;
  background: linear-gradient(120deg, #f0f8ff 0%, #ffffff 100%);
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(16,174,255,0.1);
  position: relative;
  z-index: 3;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.user-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.avatar-container {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  position: relative;
  box-shadow: 0 6rpx 16rpx rgba(16,174,255,0.15);
}

.avatar-container::after {
  content: '';
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  right: -3rpx;
  bottom: -3rpx;
  border-radius: 50%;
  border: 3rpx solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(to right, #10aeff, #1484fa) border-box;
  z-index: -1;
}

.user-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.user-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  font-size: 34rpx;
  font-weight: 700;
  color: #222;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
}

.user-welcome {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
  margin-top: 4rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #10aeff;
  background: linear-gradient(90deg, #10aeff, #1484fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(16,174,255,0.07);
}

/* 笔记相关样式 */
.notes-container {
  padding: 0 20rpx;
}

.notes-list {
  margin-top: 20rpx;
}

.note-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
  border: 1rpx solid rgba(0,0,0,0.02);
  transition: all 0.3s;
}

.note-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 8rpx rgba(0,0,0,0.08);
}

.note-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.course-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.course-pic {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.course-detail {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.course-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.note-type {
  font-size: 20rpx;
  color: #10aeff;
  margin-top: 2rpx;
  font-weight: 500;
}

.chapter-name {
  font-size: 20rpx;
  color: #666;
  background: #f0f8ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 0;
}

.note-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  margin: 8rpx 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-height: 72rpx;
}

.note-study-info {
  display: flex;
  gap: 16rpx;
  margin: 8rpx 0;
  flex-wrap: wrap;
}

.progress, .time-point {
  font-size: 20rpx;
  color: #666;
  background: #f5f8fa;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(16,174,255,0.1);
}

.progress {
  color: #10aeff;
  background: rgba(16,174,255,0.08);
}

.note-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid rgba(0,0,0,0.04);
}

.note-time {
  font-size: 20rpx;
  color: #999;
  font-weight: 400;
}

.note-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  transition: all 0.3s;
  border: 1rpx solid transparent;
}

.action-btn .iconfont {
  font-size: 24rpx;
  margin-right: 4rpx;
}

.action-btn.edit {
  background: rgba(16,174,255,0.08);
  color: #10aeff;
  border-color: rgba(16,174,255,0.2);
}

.action-btn.delete {
  background: rgba(255,71,87,0.08);
  color: #ff4757;
  border-color: rgba(255,71,87,0.2);
}

.action-btn:active {
  transform: scale(0.92);
}

/* 空数据和加载更多提示 */
.nodata {
  width: 100%;
  text-align: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 26rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.nodata image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.nomore {
  width: 100%;
  text-align: center;
  padding: 24rpx 0;
  color: #999;
  font-size: 22rpx;
  position: relative;
}

.nomore::before, .nomore::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 25%;
  height: 1rpx;
  background: linear-gradient(to right, rgba(229,229,229,0), rgba(229,229,229,0.8), rgba(229,229,229,0));
}

.nomore::before {
  left: 10%;
}

.nomore::after {
  right: 10%;
}

/* 添加搜索和排序区域样式 */
.search-sort-area {
  padding: 16rpx 20rpx;
  background: #fff;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
  border-radius: 16rpx;
  margin: 0 20rpx 20rpx 20rpx;
}

.search-box {
  position: relative;
  margin-bottom: 16rpx;
}

.search-input {
  width: 100%;
  height: 68rpx;
  background: #f8fafc;
  border-radius: 34rpx;
  padding: 0 70rpx 0 28rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
  border: 1rpx solid rgba(0,0,0,0.06);
}

.search-input:focus {
  background: #fff;
  border-color: #10aeff;
  box-shadow: 0 0 0 4rpx rgba(16,174,255,0.1);
}

.search-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #666;
  transition: color 0.3s;
}

.search-icon:active {
  color: #10aeff;
}

.sort-box {
  display: flex;
  gap: 16rpx;
}

.sort-item {
  padding: 8rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  background: #f8fafc;
  border-radius: 24rpx;
  transition: all 0.3s;
  border: 1rpx solid rgba(0,0,0,0.06);
  font-weight: 500;
}

.sort-item.active {
  background: #10aeff;
  color: #fff;
  border-color: #10aeff;
  box-shadow: 0 2rpx 8rpx rgba(16,174,255,0.3);
}

.sort-item:active {
  transform: scale(0.95);
}
</style> 