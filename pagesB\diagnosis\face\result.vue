<template>
	<view class="face-complete">
		<!-- 医院标识 -->
		<view class="hospital-info">
			<text>国家重点实验室项目 | 国家重大专项</text>
		</view>

		<!-- 报告类型选择 -->
		<view class="report-tabs">
			<view class="tab-item">
				<text class="tab-text">舌象报告</text>
			</view>
			<view class="tab-item active">
				<text class="tab-text">面诊报告</text>
				<view class="tab-line"></view>
			</view>
		</view>

		<!-- 健康得分卡片 -->
		<view class="health-card" v-if="resultData.score">
			<view class="health-content">
				<view class="health-left">
					<image class="health-avatar" :src="resultData.face_image || '/static/images/default-face.png'"></image>
					<view class="health-symptoms">
						<view class="main-symptom">
							<text class="symptom-label">体质类型：</text>
							<text class="symptom-value">{{constitutionType}}</text>
						</view>
						<view class="sub-symptom" v-if="mainSymptom">
							<text class="symptom-label">主要特征：</text>
							<text class="symptom-value">{{mainSymptom}}</text>
						</view>
					</view>
				</view>
				<view class="health-right">
					<view class="score-circle">
						<view class="score-inner">
							<text class="score-number">{{resultData.score}}</text>
							<text class="score-text">健康得分</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 可能体征 -->
		<view class="symptoms-section">
			<text class="section-title">您可能有以下体征</text>
			<view class="symptoms-grid">
				<view class="symptom-tag" v-for="(symptom, index) in typicalSymptoms" :key="index">
					{{symptom}}
				</view>
				<view v-if="typicalSymptoms.length === 0" class="no-symptoms">
					<text>暂无典型症状数据</text>
				</view>
			</view>
		</view>

		<!-- 面部特征分析 -->
		<view class="tongue-analysis">
		

			<!-- 详细面部特征列表 -->
			<view class="tongue-features-detail">
				<view class="features-header">
					<text class="features-title">面部特征分析</text>
					<text class="features-subtitle">AI智能识别 · 专业解读</text>
				</view>
				<view class="features-grid">
					<view
						v-for="(feature, index) in faceFeatures"
						:key="index"
						class="feature-card"
						:class="feature.status === 'warning' ? 'abnormal-card' : 'normal-card'"
					>
						<view class="card-header">
							<view class="feature-badge" :class="feature.status === 'warning' ? 'abnormal-badge' : 'normal-badge'">
								{{ feature.statusText }}
							</view>
							<text class="feature-category">{{ feature.name }}</text>
						</view>
						<text class="feature-name">{{ feature.name }}</text>
						<text class="feature-description">{{ feature.description }}</text>
					</view>
				</view>
				<view v-if="faceFeatures.length === 0" class="no-features">
					<view class="empty-icon">🔍</view>
					<text class="empty-text">暂无面部特征数据</text>
					<text class="empty-hint">请重新拍摄或检查图片质量</text>
				</view>
			</view>
		</view>

		<!-- 体征异常 -->
		<view class="abnormal-section">
			<view class="abnormal-title">
				<text class="abnormal-count">您有{{abnormalFeatures.length}}项表征异常</text>
			</view>
			<view class="abnormal-list">
				<view class="abnormal-item" v-for="(feature, index) in abnormalFeatures" :key="index">
					<view class="abnormal-header">
						<text class="abnormal-icon">!</text>
						<text class="abnormal-name">{{feature.name}}：</text>
					</view>
					<text class="abnormal-desc">{{feature.description}}</text>
				</view>
				<view v-if="abnormalFeatures.length === 0" class="no-abnormal">
					<text>恭喜您！暂无异常特征发现</text>
				</view>
			</view>
		</view>

		<!-- 面诊体征论述 - 简约医疗风格 -->
		<view class="theory-section">
			<view class="theory-header-wrapper">
				<view class="theory-icon">📋</view>
				<text class="section-title">面诊体征论述</text>
				<text class="theory-subtitle">专业医学解读</text>
			</view>

			<!-- 体质分析卡片 -->
			<view class="theory-card constitution-card" v-if="healthSuggestions">
				<view class="card-header">
					<view class="header-left">
						<view class="theory-tag constitution-tag">体质分析</view>
						<text class="theory-title">{{constitutionType}}</text>
					</view>
					<view class="header-right">
						<view class="status-indicator active">
							<text class="status-dot">●</text>
							<text class="status-text">已完成</text>
						</view>
					</view>
				</view>
				<view class="card-content">
					<view class="content-wrapper">
						<text class="theory-description">{{healthSuggestions}}</text>
						<view class="medical-stamp">
							<image class="doctor-avatar-small" src="/static/images/doctor-avatar.png"></image>
							<text class="stamp-text">专业解读</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="!healthSuggestions" class="empty-theory-state">
				<view class="empty-icon">🔍</view>
				<text class="empty-title">暂无体征论述数据</text>
				<text class="empty-description">正在为您生成专业的医学解读报告</text>
				<view class="loading-dots">
					<view class="dot"></view>
					<view class="dot"></view>
					<view class="dot"></view>
				</view>
			</view>
		</view>

		<!-- 保养建议 -->
		<view class="tongue-analysis" v-if="careSuggestions && Object.keys(careSuggestions).length > 0">
			<!-- 饮食建议 -->
			<view class="care-category" v-if="careSuggestions.food && careSuggestions.food.length > 0">
				<view class="category-header">
					<view class="category-icon food-icon">🍽️</view>
					<text class="category-title">饮食调养</text>
					<view class="category-subtitle">科学膳食，合理营养</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.food" :key="index">
						<view class="item-header">
							<view class="item-tag" :class="item.title === '禁忌饮食' ? 'forbidden-tag' : 'recommend-tag'">
								{{item.title}}
							</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
					</view>
				</view>
			</view>

			<!-- 运动建议 -->
			<view class="care-category" v-if="careSuggestions.sport && careSuggestions.sport.length > 0">
				<view class="category-header">
					<view class="category-icon sport-icon">🏃</view>
					<text class="category-title">运动保健</text>
					<view class="category-subtitle">适度运动，强身健体</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.sport" :key="index">
						<view class="item-header">
							<view class="item-tag recommend-tag">{{item.title}}</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
					</view>
				</view>
			</view>

			<!-- 生活建议 -->
			<view class="care-category" v-if="careSuggestions.sleep && careSuggestions.sleep.length > 0">
				<view class="category-header">
					<view class="category-icon life-icon">🌱</view>
					<text class="category-title">生活调理</text>
					<view class="category-subtitle">规律作息，身心平衡</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.sleep" :key="index">
						<view class="item-header">
							<view class="item-tag life-tag">{{item.title}}</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
					</view>
				</view>
			</view>

			<!-- 治疗建议 -->
			<view class="care-category" v-if="careSuggestions.treatment && careSuggestions.treatment.length > 0">
				<view class="category-header">
					<view class="category-icon treatment-icon">⚕️</view>
					<text class="category-title">保健疗法</text>
					<view class="category-subtitle">中医保健，养生调理</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.treatment" :key="index">
						<view class="item-header">
							<view class="item-tag treatment-tag">{{item.title}}</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
						<view class="treatment-note" v-if="item.title === '艾灸保健'">
							<text class="note-warning">⚠️ 注意事项：请在专业指导下进行，注意防烫伤</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 专家提醒 -->
			<view class="care-reminder">
				<view class="reminder-header">
					<view class="reminder-icon">👨‍⚕️</view>
					<text class="reminder-title">专家提醒</text>
				</view>
				<text class="reminder-content">以上建议仅供参考，具体调理方案请咨询专业医师。坚持执行，循序渐进，必有成效。</text>
				<image class="doctor-avatar-reminder" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view>

		<!-- 推荐商品 - 新增模块 -->
		<view class="recommend-products-section" v-if="recommendProducts && recommendProducts.length > 0">
			<view class="recommend-header">
				<view class="recommend-icon-wrapper">
					<text class="recommend-icon">商品</text>
				</view>
				<view class="recommend-title-wrapper">
					<text class="section-title">{{recommendTitle || '根据您的面诊结果，为您推荐以下产品'}}</text>
					<text class="recommend-subtitle">专业推荐，精准调理</text>
				</view>
			</view>

			<view class="products-list">
				<view
					class="product-item"
					v-for="(product, index) in recommendProducts"
					:key="index"
					@click="navigateToProduct(product)"
				>
					<view class="product-image-wrapper">
						<image
							class="product-image"
							:src="product.pic || '/static/images/default-product.png'"
							mode="aspectFill"
						></image>
						<view class="product-type-badge" :class="product.type === 'course' ? 'course-badge' : 'goods-badge'">
							<text class="type-text">{{product.type === 'course' ? '课程' : '商品'}}</text>
						</view>
					</view>

					<view class="product-info-wrapper">
						<view class="product-title">{{product.name}}</view>

						<view class="product-price-wrapper" v-if="product.price || product.sell_price || product.market_price">
							<text class="price-symbol">¥</text>
							<text class="price-value">{{product.price || product.sell_price || product.market_price || '0.00'}}</text>
							<text class="price-unit" v-if="product.type === 'course'">/课程</text>
						</view>

						<!-- 免费课程显示 -->
						<view class="product-price-wrapper" v-else-if="product.type === 'course'">
							<text class="free-text">免费</text>
						</view>

						<view class="product-reason" v-if="product.recommend_reason">
							<text class="reason-label">推荐：</text>
							<text class="reason-text">{{product.recommend_reason}}</text>
						</view>

						<view class="product-sales" v-if="product.sales">
							<text class="sales-text">已售{{product.sales}}件</text>
						</view>
					</view>

					<!-- 购物车按钮独立放在右侧 -->
					<view class="product-cart-btn" @click.stop="addToCart(product)">
						<text class="iconfont icon_gouwuche cart-icon"></text>
					</view>
				</view>
			</view>

			<!-- 推荐说明 -->
			<view class="recommend-notice">
				<view class="notice-header">
					<view class="notice-icon">i</view>
					<text class="notice-title">推荐说明</text>
				</view>
				<text class="notice-content">以上推荐商品基于您的体质分析结果和健康得分，由专业团队精心挑选。建议根据实际情况选择使用，如有疑问请咨询专业医师。</text>
			</view>
		</view>

		<!-- 底部按钮 - 浮动样式 -->
		<view class="bottom-buttons">
			<view class="btn-secondary" @click="goBack">返回</view>
			<view class="btn-secondary" @click="retakePhoto">重新拍照</view>
			<view class="btn-primary" @click="shareReport">分享报告</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			recordId: '',
			resultData: {},
			activeTab: 'features',
			faceFeatures: [],
			healthAnalysis: [],
			careAdvice: [],
			recommendProducts: [],
			diagnosisCards: [],

			// 新增数据字段，与舌诊页面保持一致
			constitutionType: '平和体质',
			mainSymptom: '',
			healthSuggestions: '',
			typicalSymptoms: [],
			abnormalFeatures: [],
			careSuggestions: {},
			recommendTitle: '',

			// 显示设置
			displaySettings: {
				show_score: 1,
				show_symptoms: 1,
				show_tongue_analysis: 1,
				show_care_advice: 1,
				show_product_recommend: 1
			}
		}
	},
	computed: {
		currentDate() {
			const now = new Date();
			return now.getFullYear() + '-' +
				   String(now.getMonth() + 1).padStart(2, '0') + '-' +
				   String(now.getDate()).padStart(2, '0');
		},
		scoreLevelClass() {
			if (!this.resultData.score) return 'normal';
			const score = parseInt(this.resultData.score);
			if (score >= 80) return 'excellent';
			if (score >= 60) return 'good';
			if (score >= 40) return 'normal';
			return 'poor';
		},
		scoreLevelText() {
			if (!this.resultData.score) return '正常';
			const score = parseInt(this.resultData.score);
			if (score >= 80) return '优秀';
			if (score >= 60) return '良好';
			if (score >= 40) return '正常';
			return '需要关注';
		},

		// 显示的面部特征（优先显示异常的，最多6个）
		displayedFaceFeatures() {
			if (!this.faceFeatures || this.faceFeatures.length === 0) {
				return [];
			}

			// 优先选择异常特征
			const abnormalFeatures = this.faceFeatures.filter(f => f.status === 'warning');
			const normalFeatures = this.faceFeatures.filter(f => f.status === 'normal');

			// 最多显示6个，优先异常特征
			let displayed = [...abnormalFeatures];
			if (displayed.length < 6) {
				displayed = displayed.concat(normalFeatures.slice(0, 6 - displayed.length));
			} else {
				displayed = displayed.slice(0, 6);
			}

			// 预先计算每个特征的样式，避免在模板中调用函数
			return displayed.map((feature, index) => {
				// 计算偏移样式
				const offset = this.calculateOffsetForSamePosition(feature.name, index, displayed.length);

				// 返回带有预计算样式的特征对象
				return {
					...feature,
					markerStyle: {
						zIndex: (10 + index).toString(), // 确保层级正确
						...offset
					}
				};
			});
		}
	},
	onLoad(options) {
		if (options.recordId) {
			this.recordId = options.recordId;
			this.getAnalysisRecord();
		}
	},
	methods: {
		// 获取分析记录 - 2025-07-17 修改为使用新的面诊接口
		getAnalysisRecord() {
			console.log('2025-07-17 INFO-[face-result][getAnalysisRecord_001] 开始获取面诊分析记录');

			const app = getApp();

			uni.showLoading({
				title: '加载中...'
			});

			// 2025-07-17 使用新的面诊接口获取记录
			app.post('ApiFaceAnalysis/getRecord', {
				id: this.recordId
			}, (response) => {
				uni.hideLoading();
				console.log('2025-07-17 INFO-[face-result][getAnalysisRecord_002] 获取面诊记录结果:', response);

				if (response && response.code === 1) {
					console.log('2025-07-17 INFO-[face-result][getAnalysisRecord_003] 获取记录成功，开始解析数据');
					this.resultData = response.data;
					this.parseNewAnalysisResult();
				} else {
					console.error('2025-07-17 ERROR-[face-result][getAnalysisRecord_004] 获取记录失败:', response?.msg);
					uni.showToast({
						title: response?.msg || '获取记录失败',
						icon: 'none'
					});

					// 降级到模拟数据，确保页面能正常显示
					this.loadMockData();
				}
			}, (error) => {
				uni.hideLoading();
				console.error('2025-07-17 ERROR-[face-result][getAnalysisRecord_005] 获取记录接口调用失败:', error);

				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});

				// 降级到模拟数据，确保页面能正常显示
				this.loadMockData();
			});
		},

		// 新增：加载模拟数据作为降级处理
		loadMockData() {
			console.log('2025-01-26 12:00:00,015-INFO-[face-result][loadMockData_001] 加载模拟数据');

			// 设置模拟的面诊结果数据
			this.resultData = {
				face_image: '/static/img/default-face.png',
				score: 85,
				analysis_result: JSON.stringify({
					face_color_status: 'normal',
					face_color_text: '正常',
					face_color_desc: '面色红润，气血充足',
					eye_status: 'normal',
					eye_text: '有神',
					eye_desc: '眼神明亮，精神状态良好',
					lip_status: 'normal',
					lip_text: '正常',
					lip_desc: '唇色淡红，血液循环良好',
					luster_status: 'normal',
					luster_text: '正常',
					luster_desc: '面部有光泽，皮肤状态良好',
					qi_blood_level: 'normal',
					qi_blood_text: '正常',
					qi_blood_desc: '气血运行正常，面色红润有光泽',
					organ_level: 'normal',
					organ_text: '正常',
					organ_desc: '脏腑功能正常，面部特征反映良好',
					spirit_level: 'normal',
					spirit_text: '良好',
					spirit_desc: '精神状态良好，眼神有神',
					diet_advice: '保持均衡饮食，多吃新鲜蔬果，少食辛辣刺激食物',
					sleep_advice: '保持规律作息，早睡早起，保证充足睡眠',
					exercise_advice: '适当运动，增强体质，促进气血循环',
					emotion_advice: '保持心情愉悦，避免过度焦虑和压力',
					eye_feature: '正常',
					eye_feature_desc: '眼神明亮有神',
					eye_feature_status: 'normal',
					lip_feature: '正常',
					lip_feature_desc: '唇色淡红润泽',
					lip_feature_status: 'normal',
					face_luster: '正常',
					face_luster_desc: '面部有光泽',
					face_luster_status: 'normal'
				})
			};

			// 解析模拟数据
			this.parseAnalysisResult();
		},

		// 2025-07-17 新增：解析新面诊接口的分析结果
		parseNewAnalysisResult() {
			try {
				console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_001] 开始解析新面诊分析结果');
				console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_002] 原始数据:', this.resultData);

				// 从新接口数据中提取信息
				const analysisResult = this.resultData.analysis_result || {};
				let analysisData = {};

				// 处理 analysis_result 数据
				if (typeof analysisResult === 'string') {
					analysisData = JSON.parse(analysisResult);
				} else if (typeof analysisResult === 'object') {
					analysisData = analysisResult;
				}

				console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_003] 解析后的分析数据:', analysisData);

				// 检查是否有新接口的数据结构
				if (analysisData.raw_report_data) {
					console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_004] 发现新接口数据结构');
					this.parseNewApiData(analysisData.raw_report_data);
				} else if (analysisData.physique_name || analysisData.score) {
					console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_005] 发现API数据结构');
					this.parseNewApiData(analysisData);
				} else {
					console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_006] 使用旧版解析方法');
					this.parseAnalysisResult();
					return;
				}

				// 获取推荐商品
				this.getRecommendProducts();

			} catch (error) {
				console.error('2025-07-17 ERROR-[face-result][parseNewAnalysisResult_007] 解析新面诊结果失败:', error);
				// 回退到旧版解析方法
				this.parseAnalysisResult();
			}
		},

		// 2025-07-17 新增：解析新API数据
		parseNewApiData(apiData) {
			console.log('2025-07-17 INFO-[face-result][parseNewApiData_001] 开始解析新API数据:', apiData);

			// 更新基础信息
			if (apiData.score) {
				this.resultData.score = apiData.score;
			}

			// 解析面部特征 - 从 features 数组中提取面部特征
			if (apiData.features && Array.isArray(apiData.features)) {
				this.parseNewFaceFeatures(apiData.features);
			} else {
				this.parseDefaultFaceFeatures();
			}

			// 解析健康分析
			this.parseNewHealthAnalysis(apiData);

			// 解析调理建议 - 从 advices 对象中提取
			if (apiData.advices) {
				this.parseNewCareAdvice(apiData.advices);
			} else {
				this.parseDefaultCareAdvice();
			}

			// 解析诊断卡片
			this.parseNewDiagnosisCards(apiData);
		},

		// 2025-07-17 新增：解析新API的面部特征
		parseNewFaceFeatures(features) {
			console.log('2025-07-17 INFO-[face-result][parseNewFaceFeatures_001] 解析面部特征:', features);

			this.faceFeatures = [];

			features.forEach(feature => {
				if (feature.feature_category === '面部') {
					const status = feature.feature_situation === '正常' ? 'normal' :
								  feature.feature_situation === '异常' ? 'warning' : 'normal';

					this.faceFeatures.push({
						name: feature.feature_group || feature.feature_name,
						status: status,
						statusText: feature.feature_situation || '正常',
						description: feature.feature_interpret || '特征正常'
					});
				}
			});

			// 如果没有面部特征，使用默认数据
			if (this.faceFeatures.length === 0) {
				this.parseDefaultFaceFeatures();
			}
		},

		// 2025-07-17 新增：解析默认面部特征
		parseDefaultFaceFeatures() {
			this.faceFeatures = [
				{
					name: '面色',
					status: 'normal',
					statusText: '正常',
					description: '面色红润，气血充足'
				},
				{
					name: '眼神',
					status: 'normal',
					statusText: '有神',
					description: '眼神明亮，精神状态良好'
				},
				{
					name: '唇色',
					status: 'normal',
					statusText: '正常',
					description: '唇色淡红，血液循环良好'
				},
				{
					name: '面部光泽',
					status: 'normal',
					statusText: '正常',
					description: '面部有光泽，皮肤状态良好'
				}
			];
		},

		// 2025-07-17 新增：解析新API的健康分析
		parseNewHealthAnalysis(apiData) {
			const score = apiData.score || 85;
			const physiqueName = apiData.physique_name || '未知体质';
			const physiqueAnalysis = apiData.physique_analysis || '体质分析中...';
			const riskWarning = apiData.risk_warning || '请注意保持健康的生活方式';

			// 设置体质类型和健康建议，与舌诊页面保持一致
			this.constitutionType = physiqueName;
			this.healthSuggestions = physiqueAnalysis;

			// 解析典型症状
			if (apiData.typical_symptom) {
				this.typicalSymptoms = apiData.typical_symptom.split('；').filter(symptom => symptom.trim());
			}

			// 提取异常特征
			this.abnormalFeatures = this.faceFeatures.filter(feature =>
				feature.status === 'warning'
			).map(feature => ({
				name: feature.name,
				description: feature.description
			}));

			this.healthAnalysis = [
				{
					title: '体质类型',
					level: score >= 80 ? 'normal' : score >= 60 ? 'warning' : 'danger',
					levelText: physiqueName,
					content: physiqueAnalysis
				},
				{
					title: '健康评分',
					level: score >= 80 ? 'normal' : score >= 60 ? 'warning' : 'danger',
					levelText: score + '分',
					content: this.getScoreDescription(score)
				},
				{
					title: '风险提示',
					level: score >= 80 ? 'normal' : score >= 60 ? 'warning' : 'danger',
					levelText: score >= 80 ? '低风险' : score >= 60 ? '中等风险' : '高风险',
					content: riskWarning
				}
			];
		},

		// 2025-07-17 新增：解析新API的调理建议
		parseNewCareAdvice(advices) {
			console.log('2025-07-17 INFO-[face-result][parseNewCareAdvice_001] 解析调理建议:', advices);

			this.careAdvice = [];

			// 设置保养建议数据，与舌诊页面保持一致的数据结构
			this.careSuggestions = advices;

			// 饮食建议
			if (advices.food && Array.isArray(advices.food)) {
				const foodAdvice = advices.food.map(item => item.advice || item.title).join('；');
				this.careAdvice.push({
					icon: '🍎',
					title: '饮食调理',
					content: foodAdvice
				});
			}

			// 运动建议
			if (advices.sport && Array.isArray(advices.sport)) {
				const sportAdvice = advices.sport.map(item => item.advice || item.title).join('；');
				this.careAdvice.push({
					icon: '🏃',
					title: '运动调理',
					content: sportAdvice
				});
			}

			// 生活建议
			if (advices.sleep && Array.isArray(advices.sleep)) {
				const sleepAdvice = advices.sleep.map(item => item.advice || item.title).join('；');
				this.careAdvice.push({
					icon: '💤',
					title: '作息调理',
					content: sleepAdvice
				});
			}

			// 治疗建议
			if (advices.treatment && Array.isArray(advices.treatment)) {
				const treatmentAdvice = advices.treatment.map(item => item.advice || item.title).join('；');
				this.careAdvice.push({
					icon: '💊',
					title: '治疗建议',
					content: treatmentAdvice
				});
			}

			// 如果没有建议，使用默认数据
			if (this.careAdvice.length === 0) {
				this.parseDefaultCareAdvice();
			}
		},

		// 2025-07-17 新增：解析默认调理建议
		parseDefaultCareAdvice() {
			this.careAdvice = [
				{
					icon: '🍎',
					title: '饮食调理',
					content: '保持均衡饮食，多吃新鲜蔬果，少食辛辣刺激食物'
				},
				{
					icon: '💤',
					title: '作息调理',
					content: '保持规律作息，早睡早起，保证充足睡眠'
				},
				{
					icon: '🏃',
					title: '运动调理',
					content: '适当运动，增强体质，促进气血循环'
				},
				{
					icon: '😌',
					title: '情志调理',
					content: '保持心情愉悦，避免过度焦虑和压力'
				}
			];
		},

		// 2025-07-17 新增：解析新API的诊断卡片
		parseNewDiagnosisCards(apiData) {
			const features = apiData.features || [];
			this.diagnosisCards = [];

			// 从特征中提取关键信息
			const faceFeatures = features.filter(f => f.feature_category === '面部');

			if (faceFeatures.length > 0) {
				faceFeatures.slice(0, 3).forEach(feature => {
					const status = feature.feature_situation === '正常' ? 'normal' :
								  feature.feature_situation === '异常' ? 'warning' : 'normal';

					this.diagnosisCards.push({
						icon: this.getFeatureIcon(feature.feature_group),
						title: feature.feature_group || feature.feature_name,
						value: feature.feature_name,
						description: feature.feature_interpret || '特征正常',
						status: status
					});
				});
			} else {
				// 使用默认诊断卡片
				this.diagnosisCards = [
					{
						icon: '👁️',
						title: '眼部特征',
						value: '正常',
						description: '眼神明亮有神',
						status: 'normal'
					},
					{
						icon: '👄',
						title: '唇部特征',
						value: '正常',
						description: '唇色淡红润泽',
						status: 'normal'
					},
					{
						icon: '🌟',
						title: '面部光泽',
						value: '正常',
						description: '面部有光泽',
						status: 'normal'
					}
				];
			}
		},

		// 2025-07-17 新增：根据特征组获取图标
		getFeatureIcon(featureGroup) {
			const iconMap = {
				'面色': '🎨',
				'面部光泽': '🌟',
				'眼神': '👁️',
				'眉头紧皱': '😤',
				'眉毛浓淡': '👁️‍🗨️',
				'眼皮层数': '👁️',
				'唇型': '👄',
				'唇色': '💋'
			};
			return iconMap[featureGroup] || '📋';
		},

		// 2025-07-17 新增：根据评分获取描述
		getScoreDescription(score) {
			if (score >= 90) return '健康状况优秀，请继续保持良好的生活习惯';
			if (score >= 80) return '健康状况良好，建议适当调理以维持最佳状态';
			if (score >= 70) return '健康状况一般，建议加强调理和保养';
			if (score >= 60) return '健康状况偏差，建议及时调理改善';
			return '健康状况较差，建议咨询专业医师进行调理';
		},

		// 解析分析结果（保留旧版本兼容）
		parseAnalysisResult() {
			try {
				console.log('2025-01-26 12:00:00,016-INFO-[face-result][parseAnalysisResult_001] 开始解析分析结果');
				console.log('2025-01-26 12:00:00,017-INFO-[face-result][parseAnalysisResult_002] 原始数据:', this.resultData);

				let analysisData = {};

				// 处理不同格式的 analysis_result
				if (typeof this.resultData.analysis_result === 'string') {
					analysisData = JSON.parse(this.resultData.analysis_result || '{}');
				} else if (typeof this.resultData.analysis_result === 'object') {
					analysisData = this.resultData.analysis_result || {};
				}

				console.log('2025-01-26 12:00:00,018-INFO-[face-result][parseAnalysisResult_003] 解析后的分析数据:', analysisData);

				// 检查是否是API响应格式（包含data.data结构）
				if (analysisData.data && analysisData.data.data) {
					console.log('2025-01-26 12:00:00,019-INFO-[face-result][parseAnalysisResult_004] 检测到API响应格式，提取实际数据');
					const apiData = analysisData.data.data;
					console.log('2025-01-26 12:00:00,020-INFO-[face-result][parseAnalysisResult_005] API数据:', apiData);

					// 从API数据中提取面诊相关信息
					analysisData = this.extractFaceAnalysisFromApi(apiData);
				}

				// 如果还是没有有效数据，尝试从其他字段获取
				if (!analysisData || Object.keys(analysisData).length === 0) {
					console.log('2025-01-26 12:00:00,020-INFO-[face-result][parseAnalysisResult_005] 分析数据为空，使用默认数据');
					analysisData = this.getDefaultAnalysisData();
				}

				// 解析面部特征
				this.parseFaceFeatures(analysisData);

				// 解析健康分析
				this.parseHealthAnalysis(analysisData);

				// 解析调理建议
				this.parseCareAdvice(analysisData);

				// 解析诊断卡片
				this.parseDiagnosisCards(analysisData);

				// 获取推荐商品
				this.getRecommendProducts();

			} catch (error) {
				console.error('2025-01-26 12:00:00,021-ERROR-[face-result][parseAnalysisResult_006] 解析分析结果失败:', error);
				// 使用默认数据
				const defaultData = this.getDefaultAnalysisData();
				this.parseFaceFeatures(defaultData);
				this.parseHealthAnalysis(defaultData);
				this.parseCareAdvice(defaultData);
				this.parseDiagnosisCards(defaultData);
			}
		},

		// 从API数据中提取面诊分析信息
		extractFaceAnalysisFromApi(apiData) {
			console.log('2025-01-26 12:00:00,022-INFO-[face-result][extractFaceAnalysisFromApi_001] 开始提取面诊数据');

			// 初始化分析数据
			let faceAnalysis = this.getDefaultAnalysisData();

			try {
				// 检查是否有体质信息
				if (apiData.physique_name) {
					// 更新评分信息
					this.resultData.score = apiData.score || 85;

					// 根据体质类型设置面部特征分析
					const physiqueName = apiData.physique_name;
					const score = apiData.score || 85;

					// 根据体质和评分生成面诊分析
					faceAnalysis = this.generateFaceAnalysisFromPhysique(physiqueName, score);
				}

				// 检查是否有症状信息
				if (apiData.typical_symptom) {
					faceAnalysis.symptoms = apiData.typical_symptom;
				}

				// 检查是否有建议信息
				if (apiData.advices) {
					if (apiData.advices.food) {
						faceAnalysis.diet_advice = Array.isArray(apiData.advices.food)
							? apiData.advices.food.join('；')
							: apiData.advices.food;
					}
					if (apiData.advices.sport) {
						faceAnalysis.exercise_advice = Array.isArray(apiData.advices.sport)
							? apiData.advices.sport.join('；')
							: apiData.advices.sport;
					}
					if (apiData.advices.life) {
						faceAnalysis.sleep_advice = Array.isArray(apiData.advices.life)
							? apiData.advices.life.join('；')
							: apiData.advices.life;
					}
				}

				console.log('2025-01-26 12:00:00,023-INFO-[face-result][extractFaceAnalysisFromApi_002] 提取的面诊数据:', faceAnalysis);

			} catch (error) {
				console.error('2025-01-26 12:00:00,024-ERROR-[face-result][extractFaceAnalysisFromApi_003] 提取面诊数据失败:', error);
			}

			return faceAnalysis;
		},

		// 根据体质信息生成面诊分析
		generateFaceAnalysisFromPhysique(physiqueName, score) {
			const analysis = this.getDefaultAnalysisData();

			// 根据评分设置状态
			const getStatusByScore = (score) => {
				if (score >= 80) return { status: 'normal', text: '良好' };
				if (score >= 60) return { status: 'normal', text: '正常' };
				if (score >= 40) return { status: 'warning', text: '需关注' };
				return { status: 'danger', text: '需调理' };
			};

			const statusInfo = getStatusByScore(score);

			// 根据体质类型调整分析结果
			const physiqueAnalysis = {
				'平和质': {
					face_color_desc: '面色红润有光泽，气血充足，体质平和',
					eye_desc: '眼神明亮有神，精神状态良好',
					lip_desc: '唇色淡红润泽，血液循环良好'
				},
				'气虚质': {
					face_color_desc: '面色偏淡，可能存在气虚现象，需要补气调理',
					eye_desc: '眼神略显疲倦，精神状态一般，建议多休息',
					lip_desc: '唇色偏淡，可能气血不足'
				},
				'阳虚质': {
					face_color_desc: '面色偏白，阳气不足，需要温阳调理',
					eye_desc: '眼神缺乏神采，精神状态欠佳',
					lip_desc: '唇色偏淡，循环较差'
				},
				'阴虚质': {
					face_color_desc: '面色偏红，可能阴虚火旺，需要滋阴润燥',
					eye_desc: '眼神略显干涩，需要滋阴调理',
					lip_desc: '唇色偏红，可能内热较重'
				}
			};

			const physiqueInfo = physiqueAnalysis[physiqueName] || physiqueAnalysis['平和质'];

			// 更新分析结果
			analysis.face_color_status = statusInfo.status;
			analysis.face_color_text = statusInfo.text;
			analysis.face_color_desc = physiqueInfo.face_color_desc;

			analysis.eye_status = statusInfo.status;
			analysis.eye_text = statusInfo.text;
			analysis.eye_desc = physiqueInfo.eye_desc;

			analysis.lip_status = statusInfo.status;
			analysis.lip_text = statusInfo.text;
			analysis.lip_desc = physiqueInfo.lip_desc;

			// 设置健康分析
			analysis.qi_blood_text = statusInfo.text;
			analysis.qi_blood_desc = `根据面诊分析，您的体质类型为${physiqueName}，评分${score}分，${physiqueInfo.face_color_desc}`;

			return analysis;
		},

		// 获取默认分析数据
		getDefaultAnalysisData() {
			return {
				face_color_status: 'normal',
				face_color_text: '正常',
				face_color_desc: '面色红润，气血充足',
				eye_status: 'normal',
				eye_text: '有神',
				eye_desc: '眼神明亮，精神状态良好',
				lip_status: 'normal',
				lip_text: '正常',
				lip_desc: '唇色淡红，血液循环良好',
				luster_status: 'normal',
				luster_text: '正常',
				luster_desc: '面部有光泽，皮肤状态良好',
				qi_blood_level: 'normal',
				qi_blood_text: '正常',
				qi_blood_desc: '气血运行正常，面色红润有光泽',
				organ_level: 'normal',
				organ_text: '正常',
				organ_desc: '脏腑功能正常，面部特征反映良好',
				spirit_level: 'normal',
				spirit_text: '良好',
				spirit_desc: '精神状态良好，眼神有神',
				diet_advice: '保持均衡饮食，多吃新鲜蔬果，少食辛辣刺激食物',
				sleep_advice: '保持规律作息，早睡早起，保证充足睡眠',
				exercise_advice: '适当运动，增强体质，促进气血循环',
				emotion_advice: '保持心情愉悦，避免过度焦虑和压力',
				eye_feature: '正常',
				eye_feature_desc: '眼神明亮有神',
				eye_feature_status: 'normal',
				lip_feature: '正常',
				lip_feature_desc: '唇色淡红润泽',
				lip_feature_status: 'normal',
				face_luster: '正常',
				face_luster_desc: '面部有光泽',
				face_luster_status: 'normal'
			};
		},

		// 解析面部特征
		parseFaceFeatures(data) {
			this.faceFeatures = [
				{
					name: '面色',
					status: data.face_color_status || 'normal',
					statusText: data.face_color_text || '正常',
					description: data.face_color_desc || '面色红润，气血充足'
				},
				{
					name: '眼神',
					status: data.eye_status || 'normal',
					statusText: data.eye_text || '有神',
					description: data.eye_desc || '眼神明亮，精神状态良好'
				},
				{
					name: '唇色',
					status: data.lip_status || 'normal',
					statusText: data.lip_text || '正常',
					description: data.lip_desc || '唇色淡红，血液循环良好'
				},
				{
					name: '面部光泽',
					status: data.luster_status || 'normal',
					statusText: data.luster_text || '正常',
					description: data.luster_desc || '面部有光泽，皮肤状态良好'
				}
			];
		},

		// 解析健康分析
		parseHealthAnalysis(data) {
			this.healthAnalysis = [
				{
					title: '气血状况',
					level: data.qi_blood_level || 'normal',
					levelText: data.qi_blood_text || '正常',
					content: data.qi_blood_desc || '气血运行正常，面色红润有光泽'
				},
				{
					title: '脏腑功能',
					level: data.organ_level || 'normal',
					levelText: data.organ_text || '正常',
					content: data.organ_desc || '脏腑功能正常，面部特征反映良好'
				},
				{
					title: '精神状态',
					level: data.spirit_level || 'normal',
					levelText: data.spirit_text || '良好',
					content: data.spirit_desc || '精神状态良好，眼神有神'
				}
			];
		},

		// 解析调理建议
		parseCareAdvice(data) {
			this.careAdvice = [
				{
					icon: '🍎',
					title: '饮食调理',
					content: data.diet_advice || '保持均衡饮食，多吃新鲜蔬果，少食辛辣刺激食物'
				},
				{
					icon: '💤',
					title: '作息调理',
					content: data.sleep_advice || '保持规律作息，早睡早起，保证充足睡眠'
				},
				{
					icon: '🏃',
					title: '运动调理',
					content: data.exercise_advice || '适当运动，增强体质，促进气血循环'
				},
				{
					icon: '😌',
					title: '情志调理',
					content: data.emotion_advice || '保持心情愉悦，避免过度焦虑和压力'
				}
			];
		},

		// 解析诊断卡片
		parseDiagnosisCards(data) {
			this.diagnosisCards = [
				{
					icon: '👁️',
					title: '眼部特征',
					value: data.eye_feature || '正常',
					description: data.eye_feature_desc || '眼神明亮有神',
					status: data.eye_feature_status || 'normal'
				},
				{
					icon: '👄',
					title: '唇部特征',
					value: data.lip_feature || '正常',
					description: data.lip_feature_desc || '唇色淡红润泽',
					status: data.lip_feature_status || 'normal'
				},
				{
					icon: '🌟',
					title: '面部光泽',
					value: data.face_luster || '正常',
					description: data.face_luster_desc || '面部有光泽',
					status: data.face_luster_status || 'normal'
				}
			];
		},

		// 获取推荐商品 - 2025-07-17 修改为使用新的面诊接口
		getRecommendProducts() {
			const app = getApp();

			// 2025-07-17 使用新的面诊接口获取推荐商品
			app.post('ApiFaceAnalysis/getRecommendProducts', {
				record_id: this.recordId,
				diagnosis_type: 2 // 面诊
			}, (response) => {
				if (response && response.code === 1) {
					this.recommendProducts = response.data || [];
				}
			}, () => {
				console.log('2025-07-17 INFO-[face-result][getRecommendProducts] 获取推荐商品失败，使用空数组');
				this.recommendProducts = [];
			});
		},

		// 切换标签
		switchTab(tab) {
			this.activeTab = tab;
		},

		// 图片加载错误
		onImageError() {
			console.log('图片加载失败');
		},

		// 返回
		goBack() {
			uni.navigateBack();
		},

		// 分享结果
		shareResult() {
			uni.showToast({
				title: '分享功能开发中',
				icon: 'none'
			});
		},

		// 跳转到商品详情
		goToProduct(product) {
			uni.navigateTo({
				url: `/pages/product/detail?id=${product.id}`
			});
		},

		// 新增方法，与舌诊页面保持一致

		// 重新拍照
		retakePhoto() {
			console.log('用户点击重新拍照');
			uni.navigateTo({
				url: '/pagesB/diagnosis/face/guide'
			});
		},

		// 分享报告
		shareReport() {
			console.log('用户点击分享报告');
			uni.showToast({
				title: '分享功能开发中',
				icon: 'none'
			});
		},

		// 查看面诊图片
		viewFaceImage() {
			console.log('用户点击查看面诊图片');
			if (this.resultData.face_image) {
				uni.previewImage({
					urls: [this.resultData.face_image]
				});
			}
		},

		// 根据特征组获取标记样式位置类
		getFeaturePositionClass(featureName) {
			const positionClasses = {
				'面色': 'face-color',
				'眼神': 'eyes',
				'唇色': 'lips',
				'面部光泽': 'luster',
				'眉头紧皱': 'eyebrows',
				'眉毛浓淡': 'eyebrows',
				'眼皮层数': 'eyelids',
				'唇型': 'lips'
			};
			return positionClasses[featureName] || 'default';
		},

		// 计算相同位置标签的偏移量
		calculateOffsetForSamePosition(featureName, index, total) {
			// 定义容易重叠的特征组
			const centerGroups = ['面色', '面部光泽'];
			const eyeGroups = ['眼神', '眉头紧皱', '眉毛浓淡', '眼皮层数'];
			const lipGroups = ['唇色', '唇型'];

			if (centerGroups.includes(featureName)) {
				// 中心区域的标签呈环形分布
				const angle = (index * 360 / total) * Math.PI / 180;
				const radius = 25;
				return {
					marginTop: (Math.sin(angle) * radius) + 'rpx',
					marginLeft: (Math.cos(angle) * radius) + 'rpx'
				};
			} else if (eyeGroups.includes(featureName)) {
				// 眼部标签垂直分布
				const offset = (index - total / 2) * 30;
				return {
					marginTop: offset + 'rpx'
				};
			} else if (lipGroups.includes(featureName)) {
				// 唇部标签水平分布
				const offset = (index - total / 2) * 40;
				return {
					marginLeft: offset + 'rpx'
				};
			}

			return {};
		},

		// 导航到商品
		navigateToProduct(product) {
			if (product.type === 'course') {
				uni.navigateTo({
					url: `/pages/course/detail?id=${product.id}`
				});
			} else {
				uni.navigateTo({
					url: `/pages/product/detail?id=${product.id}`
				});
			}
		},

		// 添加到购物车
		addToCart(product) {
			uni.showToast({
				title: '已添加到购物车',
				icon: 'success'
			});
		}
	}
}
</script>

<style>
/* 面诊完成页面样式 - 与舌诊页面保持一致 */
.face-complete {
	min-height: 100vh;
	background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
	padding-bottom: 200rpx;
}

/* 医院标识 */
.hospital-info {
	text-align: center;
	padding: 30rpx 0;
	background: rgba(255, 255, 255, 0.9);
	font-size: 24rpx;
	color: #666;
	border-bottom: 1px solid #f0f0f0;
}

/* 报告类型选择 */
.report-tabs {
	display: flex;
	justify-content: center;
	padding: 30rpx 0;
	background: #fff;
	border-bottom: 1px solid #f0f0f0;
}

.tab-item {
	position: relative;
	margin: 0 40rpx;
	padding: 20rpx 0;
}

.tab-item.active .tab-text {
	color: #007aff;
	font-weight: 600;
}

.tab-line {
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 4rpx;
	background: #007aff;
	border-radius: 2rpx;
}

.tab-text {
	font-size: 28rpx;
	color: #666;
}

/* 健康得分卡片 */
.health-card {
	margin: 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
}

.health-content {
	display: flex;
	align-items: center;
	color: #fff;
}

.health-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.health-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 30rpx;
	border: 3px solid rgba(255, 255, 255, 0.3);
}

.health-symptoms {
	flex: 1;
}

.main-symptom, .sub-symptom {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.symptom-label {
	font-size: 26rpx;
	opacity: 0.8;
	margin-right: 10rpx;
}

.symptom-value {
	font-size: 28rpx;
	font-weight: 600;
}

.health-right {
	margin-left: 30rpx;
}

.score-circle {
	width: 140rpx;
	height: 140rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	border: 3px solid rgba(255, 255, 255, 0.3);
}

.score-inner {
	text-align: center;
}

.score-number {
	font-size: 48rpx;
	font-weight: 700;
	line-height: 1;
	display: block;
	margin-bottom: 8rpx;
}

.score-text {
	font-size: 22rpx;
	opacity: 0.9;
}

/* 可能体征 */
.symptoms-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.symptoms-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.symptom-tag {
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	color: #fff;
	padding: 15rpx 25rpx;
	border-radius: 25rpx;
	font-size: 26rpx;
	font-weight: 500;
	box-shadow: 0 4rpx 15rpx rgba(255, 154, 158, 0.3);
}

.no-symptoms {
	width: 100%;
	text-align: center;
	padding: 60rpx 0;
	color: #999;
	font-size: 28rpx;
}

/* 面部分析 */
.tongue-analysis {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.tongue-diagram {
	position: relative;
	margin: 30rpx 0;
}

.face-svg {
	position: relative;
	width: 600rpx;
	height: 520rpx;
	margin: 0 auto;
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
	/* 瓜子脸形状：额头较宽，下巴尖细 */
	border-radius: 45% 45% 50% 50% / 55% 55% 45% 45%;
	overflow: visible;
	/* 添加面部轮廓阴影 */
	box-shadow: inset 0 0 50rpx rgba(0,0,0,0.05);
}

.face-shape {
	width: 100%;
	height: 100%;
	position: relative;
	/* 瓜子脸形状：额头较宽，下巴尖细 */
	border-radius: 45% 45% 50% 50% / 55% 55% 45% 45%;
}

/* 绘制适合瓜子脸的面部特征 */
.face-shape::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		/* 左眉毛 - 调整位置适合瓜子脸 */
		linear-gradient(15deg, transparent 20%, #8b4513 24%, #6b4423 26%, transparent 30%),
		/* 右眉毛 */
		linear-gradient(-15deg, transparent 20%, #8b4513 24%, #6b4423 26%, transparent 30%),
		/* 左眼轮廓 - 位置稍微上移 */
		radial-gradient(ellipse 40rpx 25rpx at 35% 32%, rgba(0,0,0,0.1) 70%, transparent 71%),
		/* 右眼轮廓 */
		radial-gradient(ellipse 40rpx 25rpx at 65% 32%, rgba(0,0,0,0.1) 70%, transparent 71%),
		/* 左眼白 */
		radial-gradient(ellipse 32rpx 18rpx at 35% 32%, #fff 80%, transparent 81%),
		/* 右眼白 */
		radial-gradient(ellipse 32rpx 18rpx at 65% 32%, #fff 80%, transparent 81%),
		/* 左眼球 */
		radial-gradient(circle 12rpx at 35% 32%, #4a4a4a 70%, transparent 71%),
		/* 右眼球 */
		radial-gradient(circle 12rpx at 65% 32%, #4a4a4a 70%, transparent 71%),
		/* 左瞳孔 */
		radial-gradient(circle 6rpx at 35% 32%, #000 80%, transparent 81%),
		/* 右瞳孔 */
		radial-gradient(circle 6rpx at 65% 32%, #000 80%, transparent 81%),
		/* 鼻子轮廓 - 调整位置 */
		linear-gradient(to bottom, transparent 44%, rgba(0,0,0,0.08) 47%, rgba(0,0,0,0.12) 50%, transparent 54%),
		/* 嘴巴 - 位置下移适合瓜子脸 */
		radial-gradient(ellipse 60rpx 15rpx at 50% 68%, #d4567a 75%, transparent 76%);
	background-size:
		55rpx 12rpx, 55rpx 12rpx, 100% 100%, 100% 100%,
		100% 100%, 100% 100%, 100% 100%, 100% 100%,
		100% 100%, 100% 100%, 12rpx 25rpx, 100% 100%;
	background-position:
		30% 22%, 70% 22%, 0 0, 0 0,
		0 0, 0 0, 0 0, 0 0,
		0 0, 0 0, 50% 47%, 0 0;
	background-repeat: no-repeat;
	/* 瓜子脸形状 */
	border-radius: 45% 45% 50% 50% / 55% 55% 45% 45%;
}

/* 添加眼部高光和细节 - 适配瓜子脸 */
.face-shape::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		/* 左眼高光 - 调整位置 */
		radial-gradient(circle 4rpx at 33% 30%, rgba(255,255,255,0.8) 60%, transparent 61%),
		/* 右眼高光 */
		radial-gradient(circle 4rpx at 63% 30%, rgba(255,255,255,0.8) 60%, transparent 61%),
		/* 鼻梁高光 - 调整位置 */
		linear-gradient(to bottom, transparent 45%, rgba(255,255,255,0.3) 47%, rgba(255,255,255,0.1) 49%, transparent 51%),
		/* 面颊高光 - 增加立体感 */
		radial-gradient(ellipse 30rpx 20rpx at 25% 45%, rgba(255,255,255,0.1) 50%, transparent 51%),
		radial-gradient(ellipse 30rpx 20rpx at 75% 45%, rgba(255,255,255,0.1) 50%, transparent 51%);
	background-size:
		100% 100%, 100% 100%, 8rpx 20rpx, 100% 100%, 100% 100%;
	background-position:
		0 0, 0 0, 50% 47%, 0 0, 0 0;
	background-repeat: no-repeat;
	/* 瓜子脸形状 */
	border-radius: 45% 45% 50% 50% / 55% 55% 45% 45%;
}

.face-markers {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 10;
}

.marker {
	position: absolute;
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 100rpx;
	padding: 10rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 15rpx;
	box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.1);
	transform: translate(-50%, -50%);
	z-index: 20;
	backdrop-filter: blur(5rpx);
}

.marker.normal {
	border-left: 4rpx solid #4caf50;
}

.marker.abnormal {
	border-left: 4rpx solid #f44336;
}

.marker-icon {
	font-size: 24rpx;
	font-weight: 600;
	margin-bottom: 5rpx;
}

.marker.normal .marker-icon {
	color: #4caf50;
}

.marker.abnormal .marker-icon {
	color: #f44336;
}

.marker-text {
	font-size: 20rpx;
	color: #333;
	text-align: center;
	line-height: 1.2;
}

/* 面部特征位置定位 - 适配瓜子脸 */
.marker-face-color {
	top: 38%;
	left: 50%;
}

.marker-eyes {
	top: 32%;
	left: 50%;
}

.marker-lips {
	top: 68%;
	left: 50%;
}

.marker-luster {
	top: 45%;
	left: 50%;
}

.marker-eyebrows {
	top: 22%;
	left: 50%;
}

.marker-eyelids {
	top: 28%;
	left: 30%;
}

.marker-default {
	top: 50%;
	left: 85%;
}

.tongue-photo-btn {
	text-align: center;
	margin-top: 30rpx;
}

.photo-btn-text {
	color: #007aff;
	font-size: 28rpx;
	padding: 15rpx 30rpx;
	border: 2rpx solid #007aff;
	border-radius: 25rpx;
	display: inline-block;
}

/* 详细特征列表 */
.tongue-features-detail {
	margin-top: 40rpx;
}

.features-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.features-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.features-subtitle {
	font-size: 24rpx;
	color: #666;
}

.features-grid {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.feature-card {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
	border-left: 4rpx solid #e0e0e0;
}

.feature-card.normal-card {
	border-left-color: #4caf50;
	background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
}

.feature-card.abnormal-card {
	border-left-color: #f44336;
	background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.feature-badge {
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 500;
}

.normal-badge {
	background: #e8f5e8;
	color: #4caf50;
}

.abnormal-badge {
	background: #ffebee;
	color: #f44336;
}

.feature-category {
	font-size: 24rpx;
	color: #666;
}

.feature-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.feature-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.no-features {
	text-align: center;
	padding: 80rpx 0;
	color: #999;
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	margin-bottom: 10rpx;
	display: block;
}

.empty-hint {
	font-size: 24rpx;
	color: #ccc;
}

/* 体征异常 */
.abnormal-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.abnormal-title {
	margin-bottom: 30rpx;
}

.abnormal-count {
	font-size: 32rpx;
	font-weight: 600;
	color: #f44336;
}

.abnormal-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.abnormal-item {
	background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
	border-radius: 15rpx;
	padding: 25rpx;
	border-left: 4rpx solid #f44336;
}

.abnormal-header {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.abnormal-icon {
	width: 30rpx;
	height: 30rpx;
	background: #f44336;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	font-weight: 600;
	margin-right: 15rpx;
}

.abnormal-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #f44336;
}

.abnormal-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.no-abnormal {
	text-align: center;
	padding: 60rpx 0;
	color: #4caf50;
	font-size: 28rpx;
}

/* 体征论述 */
.theory-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.theory-header-wrapper {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.theory-icon {
	font-size: 40rpx;
	margin-right: 15rpx;
}

.theory-subtitle {
	font-size: 24rpx;
	color: #666;
	margin-left: 15rpx;
}

.theory-card {
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border: 1px solid #e9ecef;
}

.constitution-card {
	border-left: 4rpx solid #007aff;
}

.header-left {
	flex: 1;
}

.theory-tag {
	display: inline-block;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 500;
	margin-bottom: 10rpx;
}

.constitution-tag {
	background: linear-gradient(135deg, #007aff 0%, #0056b3 100%);
	color: #fff;
}

.theory-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.header-right {
	margin-left: 20rpx;
}

.status-indicator {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	background: #e8f5e8;
}

.status-dot {
	color: #4caf50;
	margin-right: 8rpx;
	font-size: 20rpx;
}

.status-text {
	font-size: 22rpx;
	color: #4caf50;
}

.card-content {
	margin-top: 20rpx;
}

.content-wrapper {
	position: relative;
}

.theory-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 20rpx;
}

.medical-stamp {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-top: 20rpx;
}

.doctor-avatar-small {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	margin-right: 10rpx;
}

.stamp-text {
	font-size: 22rpx;
	color: #007aff;
	font-weight: 500;
}

.empty-theory-state {
	text-align: center;
	padding: 80rpx 0;
	color: #999;
}

.empty-title {
	font-size: 28rpx;
	margin-bottom: 10rpx;
	display: block;
}

.empty-description {
	font-size: 24rpx;
	color: #ccc;
	margin-bottom: 30rpx;
}

.loading-dots {
	display: flex;
	justify-content: center;
	gap: 10rpx;
}

.dot {
	width: 12rpx;
	height: 12rpx;
	background: #ccc;
	border-radius: 50%;
	animation: loading 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
	0%, 80%, 100% { transform: scale(0); }
	40% { transform: scale(1); }
}

/* 保养建议 */
.care-category {
	margin-bottom: 40rpx;
}

.category-header {
	display: flex;
	align-items: center;
	margin-bottom: 25rpx;
	padding-bottom: 15rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.category-icon {
	font-size: 40rpx;
	margin-right: 15rpx;
}

.food-icon { color: #ff6b6b; }
.sport-icon { color: #4ecdc4; }
.life-icon { color: #45b7d1; }
.treatment-icon { color: #96ceb4; }

.category-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-right: 15rpx;
}

.category-subtitle {
	font-size: 24rpx;
	color: #666;
}

.suggestion-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.suggestion-item {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx;
}

.item-header {
	margin-bottom: 15rpx;
}

.item-tag {
	display: inline-block;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 500;
}

.recommend-tag {
	background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
	color: #fff;
}

.forbidden-tag {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
	color: #fff;
}

.life-tag {
	background: linear-gradient(135deg, #45b7d1 0%, #2980b9 100%);
	color: #fff;
}

.treatment-tag {
	background: linear-gradient(135deg, #96ceb4 0%, #85c1a3 100%);
	color: #fff;
}

.item-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

.treatment-note {
	margin-top: 15rpx;
	padding: 15rpx;
	background: #fff3cd;
	border-radius: 10rpx;
	border-left: 4rpx solid #ffc107;
}

.note-warning {
	font-size: 24rpx;
	color: #856404;
}

.care-reminder {
	margin-top: 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	padding: 30rpx;
	color: #fff;
	position: relative;
	overflow: hidden;
}

.reminder-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.reminder-icon {
	font-size: 40rpx;
	margin-right: 15rpx;
}

.reminder-title {
	font-size: 30rpx;
	font-weight: 600;
}

.reminder-content {
	font-size: 26rpx;
	line-height: 1.6;
	opacity: 0.9;
	margin-bottom: 20rpx;
}

.doctor-avatar-reminder {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	opacity: 0.3;
}

/* 推荐商品 */
.recommend-products-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.recommend-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.recommend-icon-wrapper {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.recommend-icon {
	font-size: 24rpx;
	color: #fff;
	font-weight: 600;
}

.recommend-title-wrapper {
	flex: 1;
}

.recommend-subtitle {
	font-size: 24rpx;
	color: #666;
	margin-top: 5rpx;
}

.products-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.product-item {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	position: relative;
}

.product-image-wrapper {
	position: relative;
	margin-right: 20rpx;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 10rpx;
}

.product-type-badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	padding: 4rpx 8rpx;
	border-radius: 10rpx;
	font-size: 20rpx;
}

.course-badge {
	background: #007aff;
	color: #fff;
}

.goods-badge {
	background: #ff6b6b;
	color: #fff;
}

.product-info-wrapper {
	flex: 1;
}

.product-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.product-price-wrapper {
	display: flex;
	align-items: baseline;
	margin-bottom: 10rpx;
}

.price-symbol {
	font-size: 24rpx;
	color: #ff4757;
	margin-right: 4rpx;
}

.price-value {
	font-size: 32rpx;
	font-weight: 600;
	color: #ff4757;
}

.price-unit {
	font-size: 22rpx;
	color: #999;
	margin-left: 4rpx;
}

.free-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #4caf50;
}

.product-reason {
	margin-bottom: 8rpx;
}

.reason-label {
	font-size: 22rpx;
	color: #666;
}

.reason-text {
	font-size: 22rpx;
	color: #007aff;
}

.product-sales {
	font-size: 22rpx;
	color: #999;
}

.product-cart-btn {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #007aff 0%, #0056b3 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 15rpx;
}

.cart-icon {
	color: #fff;
	font-size: 28rpx;
}

.recommend-notice {
	margin-top: 30rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx;
}

.notice-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.notice-icon {
	width: 30rpx;
	height: 30rpx;
	background: #007aff;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	font-weight: 600;
	margin-right: 15rpx;
}

.notice-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
}

.notice-content {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
}

/* 底部按钮 */
.bottom-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 30rpx;
	border-top: 1px solid #eee;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.btn-secondary {
	flex: 1;
	height: 88rpx;
	background: #f5f5f5;
	color: #666;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 600;
}

.btn-primary {
	flex: 1;
	height: 88rpx;
	background: linear-gradient(135deg, #007aff 0%, #0056b3 100%);
	color: #fff;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 15rpx rgba(0, 122, 255, 0.3);
}
</style>