# 鲸犀预约服务模块优化开发文档

## 一、需求分析

### 当前流程
目前预约服务流程是：用户选择项目 → 选择预约日期时间 → 选择服务人员 → 下单支付。

### 目标流程
改为：用户选择项目 → 下单支付 → 选择预约日期时间 → 后续分配/选择服务人员。

### 灵活配置需求
1. **系统层面灵活配置**：系统管理员可随时在后台调整预约流程模式，以适应不同季节或运营需求
2. **商品层面精细配置**：可为不同服务项目单独设置预约流程模式，部分商品走新流程，部分商品保持原流程

## 二、系统架构分析

### 相关模块和文件
1. **前端模块**：
   - 小程序预约下单页面
   - 预约日期时间选择页面（需修改）
   - 预约服务人员选择页面（需新增）
   - 我的订单页面（需修改）

2. **后端控制器**：
   - `app/controller/ApiYuyue.php` - 前端API接口
   - `app/controller/YuyueOrder.php` - 后台订单管理
   - `app/controller/YuyueWorker.php` - 服务人员管理
   - `app/controller/YuyueSet.php` - 设置管理
   - `app/controller/YuyueList.php` - 商品管理（需修改）

3. **服务类**：
   - `app/common/YuyueWorker.php` - 服务人员业务逻辑

4. **数据表**：
   - `yuyue_order` - 预约订单表
   - `yuyue_worker` - 服务人员表
   - `yuyue_worker_order` - 服务人员接单表
   - `yuyue_set` - 系统设置表
   - `yuyue_product` - 预约商品表（需修改）

## 三、功能改造方案

### 1. 数据表调整

#### 修改 `yuyue_order` 表
增加字段来标识订单的预约状态和服务人员分配状态：
```sql
ALTER TABLE `yuyue_order` 
ADD COLUMN `appointment_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '预约状态：0未预约时间，1已预约时间',
ADD COLUMN `worker_assign_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '服务人员分配状态：0未分配，1已分配';
```

#### 修改 `yuyue_set` 表
增加系统级配置项控制预约模式：
```sql
ALTER TABLE `yuyue_set` 
ADD COLUMN `order_flow_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单流程模式：0传统模式(先选时间再支付)，1新模式(先支付再选时间)',
ADD COLUMN `assign_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '服务人员分配模式：0下单时选择，1下单后分配',
ADD COLUMN `allow_product_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许商品单独设置流程模式：0否，1是';
```

#### 修改 `yuyue_product` 表
增加商品级配置项控制预约模式：
```sql
ALTER TABLE `yuyue_product` 
ADD COLUMN `order_flow_mode` tinyint(1) NOT NULL DEFAULT -1 COMMENT '订单流程模式：-1跟随系统设置，0传统模式，1新模式';
```

### 2. 业务流程修改

#### 下单流程
1. **原流程**：选择项目 → 选择日期时间 → 选择服务人员 → 下单支付
2. **新流程**：选择项目 → 下单支付 → 选择预约日期时间 → （订单状态为待分配服务人员）
3. **流程确定逻辑**：
   - 首先检查商品是否单独设置了流程模式（order_flow_mode > -1）
   - 如果商品有单独设置，则使用商品设置的流程模式
   - 如果商品没有单独设置，则使用系统全局设置的流程模式

#### 服务人员分配流程
新增两种分配方式：
1. **后台派单**：管理员在后台为订单指定服务人员
2. **用户自选**：用户在预约日期时间后自行选择服务人员

### 3. 接口改造

#### 修改下单接口 `app/controller/ApiYuyue.php`
```php
// 修改createOrder方法
public function createOrder(){
    // 获取商品信息
    $proid = input('post.proid/d');
    $product = Db::name('yuyue_product')->where('id', $proid)->find();
    if (!$product) return json(['status'=>0, 'msg'=>'商品不存在']);
    
    // ... 其他参数获取 ...
    
    // 判断订单流程模式 - 先检查商品设置，再检查系统设置
    $set = Db::name('yuyue_set')->where('aid', aid)->find();
    $order_flow_mode = $set['order_flow_mode'] ?? 0;
    
    // 如果商品单独设置了流程模式且允许商品单独设置
    if ($set['allow_product_mode'] == 1 && $product['order_flow_mode'] > -1) {
        $order_flow_mode = $product['order_flow_mode'];
    }
    
    // 记录日志
    \think\facade\Log::info('[ApiYuyue][createOrder_001] 下单流程模式: proid='.$proid.', order_flow_mode='.$order_flow_mode);
    
    if ($order_flow_mode == 0) {
        // 原有流程逻辑：需要时间和服务人员信息
        // ... 
    } else {
        // 新流程逻辑：只处理下单，不需要时间和服务人员信息
        $order_data = [
            'aid' => aid,
            'mid' => $this->mid,
            'proid' => $proid,
            // ... 其他订单字段 ...
            'appointment_status' => 0, // 未预约时间
            'worker_id' => 0, // 未分配服务人员
            'worker_assign_status' => 0,
            'status' => 1, // 支付后状态为待预约
        ];
    }
    
    // 创建订单并处理支付
    // ... 完成订单创建 ...
    
    return json(['status'=>1, 'msg'=>'下单成功', 'orderid'=>$orderid]);
}
```

#### 获取商品详情时返回流程模式
```php
// 修改product方法
public function product(){
    $proid = input('post.proid/d');
    // ... 已有代码 ...
    
    // 获取商品信息
    $product = Db::name('yuyue_product')->where('id', $proid)->find();
    
    // 获取系统设置
    $set = Db::name('yuyue_set')->where('aid', aid)->find();
    
    // 确定实际使用的流程模式
    $order_flow_mode = $set['order_flow_mode'] ?? 0;
    if ($set['allow_product_mode'] == 1 && $product['order_flow_mode'] > -1) {
        $order_flow_mode = $product['order_flow_mode'];
    }
    
    // 将流程模式添加到返回数据中
    $data['order_flow_mode'] = $order_flow_mode;
    
    return json(['status'=>1, 'data'=>$data]);
}
```

#### 其他接口保持不变
- 预约时间接口
- 获取可选服务人员接口
- 分配服务人员接口

### 4. 后台管理功能改造

#### 修改系统设置页面 `app/controller/YuyueSet.php`
扩展设置页面，增加订单流程模式选择项和商品个性化设置选项：
```php
// 修改set方法
public function set(){
    $info = Db::name('yuyue_set')->where('aid', aid)->where('bid', bid)->find();
    if (!$info) {
        Db::name('yuyue_set')->insert(['aid'=>aid, 'bid'=>bid]);
        $info = Db::name('yuyue_set')->where('aid', aid)->where('bid', bid)->find();
    }
    
    // ... 已有代码 ...
    
    // 初始化新增字段
    if (!isset($info['order_flow_mode'])) {
        $info['order_flow_mode'] = 0;
    }
    if (!isset($info['assign_mode'])) {
        $info['assign_mode'] = 0;
    }
    if (!isset($info['allow_product_mode'])) {
        $info['allow_product_mode'] = 0;
    }
    
    View::assign('info', $info);
    return View::fetch();
}
```

#### 修改商品编辑页面 `app/controller/YuyueList.php`
```php
// 修改edit方法
public function edit(){
    // ... 已有代码 ...
    
    // 获取全局设置
    $set = Db::name('yuyue_set')->where('aid', aid)->where('bid', bid)->find();
    $allow_product_mode = $set['allow_product_mode'] ?? 0;
    
    // 将全局设置传递给视图
    View::assign('allow_product_mode', $allow_product_mode);
    
    // ... 其他代码 ...
    
    return View::fetch();
}

// 修改save方法
public function save(){
    $info = input('post.info/a');
    
    // ... 已有代码 ...
    
    // 如果提交的数据包含自定义流程模式
    if (isset($info['order_flow_mode'])) {
        // 获取系统设置，确认是否允许商品单独设置
        $set = Db::name('yuyue_set')->where('aid', aid)->where('bid', bid)->find();
        if ($set && $set['allow_product_mode'] == 1) {
            // 允许，保留用户设置
        } else {
            // 不允许，使用系统默认值
            $info['order_flow_mode'] = -1;
        }
    }
    
    // ... 保存逻辑 ...
    
    return json(['status'=>1, 'msg'=>'操作成功', 'url'=>(string)url('index')]);
}
```

### 5. 前端界面改造

#### 修改系统设置页面
增加商品个性化流程设置开关：
```html
<div class="layui-form-item">
    <label class="layui-form-label">允许商品单独设置</label>
    <div class="layui-input-block">
        <input type="radio" name="info[allow_product_mode]" value="1" title="允许" {if $info.allow_product_mode==1}checked{/if}>
        <input type="radio" name="info[allow_product_mode]" value="0" title="不允许" {if $info.allow_product_mode==0}checked{/if}>
    </div>
    <div class="layui-form-mid layui-word-aux">启用后，可在商品编辑页面单独设置该商品的预约流程模式</div>
</div>
```

#### 修改商品编辑页面
在允许商品单独设置时，增加流程模式选择：
```html
{if $allow_product_mode == 1}
<div class="layui-form-item">
    <label class="layui-form-label">预约流程模式</label>
    <div class="layui-input-block">
        <input type="radio" name="info[order_flow_mode]" value="-1" title="跟随系统设置" {if $info.order_flow_mode==-1}checked{/if}>
        <input type="radio" name="info[order_flow_mode]" value="0" title="传统模式(先选时间再支付)" {if $info.order_flow_mode==0}checked{/if}>
        <input type="radio" name="info[order_flow_mode]" value="1" title="新模式(先支付再选时间)" {if $info.order_flow_mode==1}checked{/if}>
    </div>
</div>
{/if}
```

#### 前端页面流程适配
小程序端根据返回的商品流程模式参数，动态调整显示流程：
```javascript
// 在商品详情页加载时
onLoad: function(options) {
    // ... 获取商品详情 ...
    
    // 根据流程模式调整界面
    if (this.data.productInfo.order_flow_mode == 1) {
        // 新模式
        this.setData({
            needSelectDateBeforePay: false
        });
    } else {
        // 传统模式
        this.setData({
            needSelectDateBeforePay: true
        });
    }
}
```

## 四、实施步骤

### 第一阶段：数据库改造
1. 修改`yuyue_order`表，增加预约状态和服务人员分配状态字段
2. 修改`yuyue_set`表，增加订单流程模式、预约模式配置字段和商品单独设置开关
3. 修改`yuyue_product`表，增加商品级流程模式字段

### 第二阶段：后端接口开发
1. 修改下单接口，支持灵活的商品和系统流程模式判断
2. 开发预约时间选择接口
3. 开发服务人员分配相关接口
4. 修改后台订单管理功能，增加设置预约时间和派单功能
5. 修改系统设置，增加订单流程模式和商品单独设置选项
6. 修改商品管理，支持商品级别的流程模式设置

### 第三阶段：前端页面开发
1. 修改下单页面，根据商品流程模式适配下单流程
2. 开发预约时间选择页面
3. 开发服务人员选择页面
4. 修改订单详情页面，增加预约状态和服务人员分配状态显示
5. 修改系统设置页面，增加商品个性化设置开关
6. 修改商品编辑页面，增加流程模式选择项

### 第四阶段：测试与上线
1. 功能测试：验证不同模式组合下的完整预约流程
2. 兼容性测试：确保旧数据在新流程下正常运行
3. 性能测试：评估改造对系统性能的影响
4. 分批上线：先小范围测试，再全面推广

## 五、API接口文档

### 1. 创建订单接口
- **URL**: `/index.php/api/apiyuyue/createOrder`
- **方法**: POST
- **参数**:
  ```
  proid: 商品ID
  ...其他订单参数
  ```
- **返回**: 订单创建结果

### 2. 获取商品详情接口
- **URL**: `/index.php/api/apiyuyue/product`
- **方法**: POST
- **参数**:
  ```
  proid: 商品ID
  ```
- **返回**: 
  ```json
  {
    "status": 1,
    "data": {
      "id": 1,
      "name": "商品名称",
      "order_flow_mode": 1, // 流程模式：0传统，1新模式
      ...其他字段
    }
  }
  ```

### 3. 预约时间接口
- **URL**: `/index.php/api/apiyuyue/appointTime`
- **方法**: POST
- **参数**:
  ```
  orderid: 订单ID
  yy_date: 预约日期
  yy_time: 预约时间段
  ```
- **返回**: 预约结果

### 4. 获取可选服务人员接口
- **URL**: `/index.php/api/apiyuyue/getAvailableWorkers`
- **方法**: POST
- **参数**:
  ```
  orderid: 订单ID
  ```
- **返回**: 可选服务人员列表

### 5. 分配服务人员接口
- **URL**: `/index.php/api/apiyuyue/assignWorker`
- **方法**: POST
- **参数**:
  ```
  orderid: 订单ID
  worker_id: 服务人员ID
  ```
- **返回**: 分配结果

## 六、风险评估

1. **数据兼容性风险**：
   - 已有订单可能存在状态不一致问题
   - 老商品没有流程模式设置
   - 解决方案：增加数据迁移脚本，确保老数据兼容新流程

2. **用户体验风险**：
   - 不同商品使用不同流程可能导致用户困惑
   - 解决方案：增加流程引导，提供清晰的操作提示和说明

3. **系统复杂性风险**：
   - 增加灵活配置增加了系统复杂度和维护难度
   - 解决方案：完善文档，增加日志记录，方便问题排查

4. **系统性能风险**：
   - 额外的流程判断逻辑可能影响系统性能
   - 解决方案：优化查询，考虑使用缓存减少数据库查询

## 七、附录

### 相关表结构

#### yuyue_order 表
```sql
CREATE TABLE `yuyue_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT 0,
  `bid` int(11) NOT NULL DEFAULT 0,
  `mid` int(11) NOT NULL DEFAULT 0,
  `proid` int(11) NOT NULL DEFAULT 0,
  `worker_id` int(11) NOT NULL DEFAULT 0,
  `worker_orderid` int(11) NOT NULL DEFAULT 0,
  `appointment_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '预约状态',
  `worker_assign_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '服务人员分配状态',
  `yy_date` varchar(20) NOT NULL DEFAULT '',
  `yy_time` varchar(20) NOT NULL DEFAULT '',
  `status` tinyint(1) NOT NULL DEFAULT 0,
  ...其他字段
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### yuyue_set 表
```sql
CREATE TABLE `yuyue_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT 0,
  `bid` int(11) NOT NULL DEFAULT 0,
  `order_flow_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单流程模式',
  `assign_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '服务人员分配模式',
  `allow_product_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许商品单独设置流程模式',
  ...其他字段
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### yuyue_product 表
```sql
CREATE TABLE `yuyue_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT 0,
  `bid` int(11) NOT NULL DEFAULT 0,
  `name` varchar(100) NOT NULL DEFAULT '',
  `order_flow_mode` tinyint(1) NOT NULL DEFAULT -1 COMMENT '订单流程模式：-1跟随系统，0传统，1新模式',
  ...其他字段
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 服务人员奖励机制

为了更好地激励服务人员提供优质服务，系统还可以引入以下奖励机制：

1. **评价奖励**：根据用户评价星级或好评程度，给予服务人员不同比例的奖励
   - 5星/好评：获得100%提成
   - 4星/中评：获得80%提成
   - 3星及以下/差评：获得50%提成

2. **接单量奖励**：根据服务人员月接单量设置阶梯式奖励
   - 10单以下：基础提成
   - 10-30单：基础提成+5%
   - 30单以上：基础提成+10%

3. **服务速度奖励**：对于在规定时间内完成服务的人员给予额外奖励

### 用户端界面示例

#### 订单详情页-未预约状态
```
|--------------------------|
|      订单详情            |
|--------------------------|
| 订单号: 202304010001     |
| 预约项目: 家电维修       |
| 订单状态: 已支付待预约    |
|                          |
| [预约日期时间] <- 新增按钮 |
|--------------------------|
```

#### 预约日期时间页面
```
|--------------------------|
|    选择预约日期时间      |
|--------------------------|
| 2023年4月               |
| 一 二 三 四 五 六 日     |
|                1  2      |
| 3  4  5  6  7  8  9      |
| ...                      |
|                          |
| 可选时间段:              |
| ○ 09:00-10:00           |
| ○ 10:00-11:00           |
| ...                      |
|                          |
| [确认预约]               |
|--------------------------|
```

#### 服务人员选择页面
```
|--------------------------|
|      服务人员选择        |
|--------------------------|
| 筛选: [全部 v] [评分 v]  |
|                          |
| [张师傅]                 |
| 评分: ★★★★★ 5.0         |
| 已完成: 56单             |
| 擅长: 家电维修、管道疏通  |
|                          |
| [李师傅]                 |
| 评分: ★★★★☆ 4.8         |
| 已完成: 42单             |
| 擅长: 家电维修、电路检修  |
|                          |
| ...                      |
|--------------------------|
```

### 后台功能示例

#### 预约设置页面新增选项
```
|--------------------------|
|      预约系统设置        |
|--------------------------|
| 订单流程模式:            |
| ○ 传统模式(先选时间再支付)|
| ● 新模式(先支付再选时间)  |
|                          |
| 服务人员分配方式:        |
| ○ 仅后台派单             |
| ● 允许用户自选            |
| ○ 自动分配               |
|                          |
| 允许商品单独设置:        |
| ○ 不允许                 |
| ● 允许                   |
|                          |
| [保存]                   |
|--------------------------|
```

#### 商品编辑页面新增选项
```
|--------------------------|
|      商品编辑            |
|--------------------------|
| 商品名称: [家电维修]     |
| 价格: [100]              |
| ...                      |
|                          |
| 预约流程模式:            |
| ○ 跟随系统设置           |
| ○ 传统模式(先选时间再支付)|
| ● 新模式(先支付再选时间)  |
|                          |
| [保存]                   |
|--------------------------|
```

#### 订单管理页面新增功能
```
|--------------------------|
|      订单管理            |
|--------------------------|
| 订单号: 202304010001     |
| 客户: 张三               |
| 状态: 已支付待预约        |
|                          |
| [设置预约时间] <- 新增按钮 |
|--------------------------|
```

#### 预约时间设置弹窗
```
|--------------------------|
|      设置预约时间        |
|--------------------------|
| 订单信息:                |
| 订单号: 202304010001     |
| 预约项目: 家电维修       |
|                          |
| 选择日期: [2023-04-05]   |
| 选择时间段: [10:00-11:00]|
|                          |
| [确定]                   |
|--------------------------|
```

#### 服务人员分配弹窗
```
|--------------------------|
|      分配服务人员        |
|--------------------------|
| 订单信息:                |
| 订单号: 202304010001     |
| 预约项目: 家电维修       |
| 预约时间: 2023-04-05 10:00|
|                          |
| 选择服务人员:            |
| [张师傅 v]               |
|                          |
| [确定分配]               |
|--------------------------|
``` 