<template>
	<view>
		<view class="footer flex" style="width: 100%;">
			<view class="input">
		
				<input class="input" v-model="msg" @blur="sendMsg" style="color: #fff;"  placeholder="说点什么..."/>
			</view>
			<view class="tools flex">
				<view class="tool-item cart" @click="welcomeUser">
					<uni-icons type="cart-filled" size="22" color="#fff"></uni-icons>
				</view>
				<!-- <view class="tool-item gift" @click="openGift">
					<uni-icons type="gift-filled" size="22" color="#fff"></uni-icons>
				</view> -->
				<likeButton :throttle="100" :large="2" :duration="3000" :imgWidth="90" :imgHeight="90" :site="[105, 100]" :showImgs="likeImgs">
					<view class="tool-item like" @click="zan">
						<uni-icons type="hand-up-filled" size="22" color="#fff"></uni-icons>
					</view>
				</likeButton>
				<view class="tool-item" @click="clear">
					<uni-icons :type="isCleared ? 'refresh' : 'closeempty'" size="22" color="#fff"></uni-icons>
				</view>
			</view>
		</view>
		
		<uni-popup ref="giftPopup" :mask-click="false">
			<view class="gift-main flex">
				<view class="gift-item flex flex-column" @click="sendGift(0)">
					<image src="https://rdimg.rundejy.com/web/runde_admin/icon_奖杯-1.png" mode=""></image>
					<text>奖杯</text>
				</view>
				<view class="gift-item flex flex-column" @click="sendGift(1)">
					<image src="https://rdimg.rundejy.com/web/runde_admin/icon_鼓掌-1.png" mode=""></image>
					<text>鼓掌</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template> 

<script>
	var app = getApp();
	import likeButton from '@/components/like-button/like-button.vue'
	export default {
		components: { likeButton },
		props: {
			room_id: {
				type: [Number, String],
				required: true
			}
		},
		data() {
			return {
				likeImgs: [],
				msg : '',
				isCleared: false
			}
		},
		mounted() {
			this.likeImgs = [
				require('@/static/live/like/bg1.png'),
				require('@/static/live/like/bg2.png'),
				require('@/static/live/like/bg3.png'),
				require('@/static/live/like/bg4.png'),
				require('@/static/live/like/bg5.png'),
				require('@/static/live/like/bg6.png'),
			];
			
			// 模拟效果
			// setInterval(() => {
			// 	this.openGift();
			// 	this.welcomeUser();
			// }, 300)
		},
		methods: {
			openGift() {
				this.$emit('sendGift', { index: 1 });
				// this.$refs.giftPopup.open('bottom')
			},
			
			sendGift(index) {
				this.$refs.giftPopup.close();
				this.$emit('sendGift', { index });
			},
			
			welcomeUser() {
				this.$emit('welcomeUser');
			},
			
			
			sendMsg() {

				app.sendSocketMessage({
					type: 'live_web',
					content: this.msg,
					data:{
						aid: app.globalData.aid,
						mid: app.globalData.mid,
						room: this.room_id,
					}	
				});
				
				this.msg = '';
			},
			
			clear() {
				this.isCleared = !this.isCleared;
				console.log('Clear state:', this.isCleared);
				this.$emit('clear', this.isCleared);
			},
			zan(){
				app.sendSocketMessage({
					type: 'live_web',
					data:{
						aid: app.globalData.aid,
						mid: app.globalData.mid,
						room: this.room_id,
						live_type:'like'
					}	
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx;
		height: 100rpx;
		width: calc(100vw - 40rpx);
		z-index: 98;
		/* #ifdef MP-WEIXIN */
		padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
		padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
		height: calc(100rpx + constant(safe-area-inset-bottom)); /* iOS 11.0 */
		height: calc(100rpx + env(safe-area-inset-bottom)); /* iOS 11.2+ */
		/* #endif */
		
		.input {
			background: var(--live-background);
			color: #ccc;
			border-radius: 100rpx;
			font-size: 28rpx;
			height: 80rpx;
			line-height: 80rpx;
			padding-left: 20rpx;
			flex: 1;
			display: flex;
			align-items: center;
		}
		
		input:placeholder {
		  color: #909090; /* 提示字颜色 */
		}
		
		.tools {
			justify-content: space-around;
			display: flex;
			align-items: center;
			
			.tool-item {
				width: 80rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 100%;
				margin-left: 20rpx;
				color: #fff;
				background: var(--live-background);
				/* #ifdef MP-WEIXIN */
				margin-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
				margin-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
				/* #endif */
				
				&.cart {
					background: linear-gradient(to right, #E7B211, #E98824)!important;
				}
				
				&.gift {
					background: linear-gradient(to right, #E77DD2, #EA35AA)!important;
				}
				
				&.like {
					background: linear-gradient(to bottom right, #FF4857, #FF2C80)!important;
				}
			}
		}
	}
	
	.gift-main {
		height: 600rpx;
		background: rgba(0, 0, 0, 0.8);
		align-items: flex-start;
		flex-wrap: wrap;
		padding: 30rpx;
		justify-content: flex-start;
		
		.gift-item {
			width: 120rpx;
			border: 2rpx solid #000;
			margin-right: 16rpx;
			
			&:nth-child(5n) {
				margin-right: 0;
			}
			image {
				width: 100rpx;
				height: 100rpx;
			}
			text {
				font-size: 24rpx;
				color: #fff;
			}
		}
	}
	
	.flex-column {
	    flex-direction: column;
	}
	
	.flex {
	    display: flex;
	    justify-content: flex-start;
	    align-items: center;
	}
</style>
