<template>
<view class="container">
	<block v-if="isload">
		<!-- 顶部统计卡片 -->
		<view class="stats-container">
			<view class="stats-card">
				<view class="stats-item">
					<text class="stats-number">{{statistics.total_merchants}}</text>
					<text class="stats-label">总商户数</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">{{statistics.active_merchants}}</text>
					<text class="stats-label">活跃商户</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">{{statistics.today_new_merchants}}</text>
					<text class="stats-label">今日新增</text>
				</view>
			</view>
			<view class="amount-card">
				<view class="amount-item">
					<text class="amount-label">总交易额</text>
					<text class="amount-value">¥{{statistics.total_amount}}</text>
				</view>
				<view class="amount-item">
					<text class="amount-label">本月交易额</text>
					<text class="amount-value">¥{{statistics.month_amount}}</text>
				</view>
			</view>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-container">
			<view class="filter-row">
				<view class="search-box">
					<input 
						class="search-input" 
						type="text" 
						v-model="keyword" 
						placeholder="搜索商户名称" 
						@confirm="onSearch"
					/>
					<text class="iconfont iconsousuo search-icon" @tap="onSearch"></text>
				</view>
			</view>
			<view class="filter-row">
				<picker @change="onStatusChange" :value="statusIndex" :range="statusList" range-key="name">
					<view class="filter-item">
						<text>{{statusList[statusIndex].name}}</text>
						<text class="iconfont iconjiantou"></text>
					</view>
				</picker>
				<picker @change="onTypeChange" :value="typeIndex" :range="typeList" range-key="name">
					<view class="filter-item">
						<text>{{typeList[typeIndex].name}}</text>
						<text class="iconfont iconjiantou"></text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 商户列表 -->
		<view class="merchant-list">
			<view 
				class="merchant-item" 
				v-for="(item, index) in datalist" 
				:key="index"
				@tap="goto" 
				:data-url="'merchant_detail?merchant_id=' + item.id"
			>
				<!-- 商户头部信息 -->
				<view class="merchant-header">
					<view class="merchant-avatar">
						<image :src="item.logo || '/static/img/default_merchant.png'" class="avatar-img"></image>
					</view>
					<view class="merchant-info">
						<view class="merchant-name">{{item.name}}</view>
						<view class="merchant-category">{{item.category_name || '未分类'}}</view>
						<view class="merchant-address">
							<text class="iconfont iconweizhi"></text>
							<text class="address-text">{{item.address || '暂无地址'}}</text>
						</view>
					</view>
					<view class="merchant-status">
						<text class="status-badge" :style="{background: item.status_color}">
							{{item.status_text}}
						</text>
					</view>
				</view>

				<!-- 商户统计信息 -->
				<view class="merchant-stats">
					<view class="stat-item">
						<text class="stat-label">订单数</text>
						<text class="stat-value">{{item.order_count}}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">交易额</text>
						<text class="stat-value">¥{{item.order_amount}}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">商品数</text>
						<text class="stat-value">{{item.product_count}}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">余额</text>
						<text class="stat-value">¥{{item.money}}</text>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="merchant-actions">
					<view class="action-btn" @tap.stop="callMerchant" :data-phone="item.phone">
						<text class="iconfont icondianhua"></text>
						<text>电话</text>
					</view>
					
					<view class="action-btn" @tap.stop="gotoBusinessManage" :data-id="item.id">
						<text class="iconfont iconguanli"></text>
						<text>详情</text>
					</view>
					<view class="action-btn" @tap.stop="viewLocation" :data-longitude="item.longitude" :data-latitude="item.latitude" :data-name="item.name">
						<text class="iconfont iconweizhi"></text>
						<text>位置</text>
					</view>
				</view>
			</view>
		</view>
	</block>
	
	<!-- 空状态 -->
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	
	<!-- 底部导航 -->
	<dp-tabbar :opt="opt"></dp-tabbar>
	
	<!-- 消息提示 -->
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			nodata: false,
			nomore: false,
			page: 1,
			limit: 20,
			keyword: '',
			datalist: [],
			statistics: {
				total_merchants: 0,
				active_merchants: 0,
				today_new_merchants: 0,
				total_orders: 0,
				total_amount: '0.00',
				month_amount: '0.00'
			},
			// 状态筛选
			statusIndex: 0,
			statusList: [
				{key: '', name: '全部状态'},
				{key: '1', name: '正常营业'},
				{key: '0', name: '待审核'},
				{key: '2', name: '已暂停'},
				{key: '3', name: '已禁用'}
			],
			// 类型筛选
			typeIndex: 0,
			typeList: [
				{key: '', name: '全部类型'}
			]
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getMerchantTypes();
		this.getdata();
	},

	onPullDownRefresh: function () {
		this.page = 1;
		this.getdata();
	},

	onReachBottom: function () {
		if (!this.nomore) {
			this.page++;
			this.getdata(true);
		}
	},

	methods: {
		// 获取商户类型
		getMerchantTypes: function() {
			var that = this;
			app.get('ApiCityAgent/getMerchantTypes', {}, function (res) {
				if (res.status == 1 && res.types) {
					// 添加默认选项
					that.typeList = [{key: '', name: '全部类型'}];
					res.types.forEach(function(type) {
						that.typeList.push({
							key: type.id.toString(),
							name: type.name
						});
					});
				}
			});
		},
		
		// 获取数据
		getdata: function (isLoadMore = false) {
			var that = this;
			
			if (!isLoadMore) {
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
			}
			
			var params = {
				page: that.page,
				limit: that.limit
			};
			
			// 添加筛选条件
			if (that.statusIndex > 0) {
				params.status = that.statusList[that.statusIndex].key;
			}
			
			if (that.typeIndex > 0) {
				params.merchant_type = that.typeList[that.typeIndex].key;
			}
			
			if (that.keyword) {
				params.keyword = that.keyword;
			}
			
			app.get('ApiCityAgent/getMerchantList', params, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 0) {
					app.error(res.msg);
					return;
				}
				
				// 设置导航标题
				uni.setNavigationBarTitle({
					title: '商户管理'
				});
				
				// 更新统计数据
				if (res.statistics) {
					that.statistics = res.statistics;
				}
				
				// 更新列表数据
				if (isLoadMore) {
					that.datalist = that.datalist.concat(res.list);
				} else {
					that.datalist = res.list;
				}
				
				// 检查是否还有更多数据
				if (res.list.length < that.limit) {
					that.nomore = true;
				}
				
				// 检查是否为空
				if (that.datalist.length === 0) {
					that.nodata = true;
				}
				
				that.loaded();
			});
		},
		
		// 搜索
		onSearch: function () {
			this.page = 1;
			this.getdata();
		},
		
		// 状态筛选
		onStatusChange: function (e) {
			this.statusIndex = e.detail.value;
			this.page = 1;
			this.getdata();
		},
		
		// 类型筛选
		onTypeChange: function (e) {
			this.typeIndex = e.detail.value;
			this.page = 1;
			this.getdata();
		},
		
		// 拨打电话
		callMerchant: function (e) {
			var phone = e.currentTarget.dataset.phone;
			if (!phone) {
				app.error('该商户未设置联系电话');
				return;
			}
			
			uni.showModal({
				title: '拨打电话',
				content: '确认拨打电话：' + phone + '？',
				success: function (res) {
					if (res.confirm) {
						uni.makePhoneCall({
							phoneNumber: phone,
							fail: function() {
								app.error('拨打电话失败');
							}
						});
					}
				}
			});
		},
		
		// 查看位置
		viewLocation: function (e) {
			var longitude = parseFloat(e.currentTarget.dataset.longitude);
			var latitude = parseFloat(e.currentTarget.dataset.latitude);
			var name = e.currentTarget.dataset.name;
			
			if (!longitude || !latitude) {
				app.error('该商户未设置位置信息');
				return;
			}
			
			uni.openLocation({
				longitude: longitude,
				latitude: latitude,
				name: name,
				scale: 18,
				fail: function() {
					app.error('打开地图失败');
				}
			});
		},
		
		// 页面跳转
		goto: function (e) {
			var url = e.currentTarget.dataset.url;
			if (url) {
				app.goto('/pagesExt/cityagent/' + url);
			}
		},
		
		// 跳转到商户后台管理
		gotoBusinessManage: function (e) {
			var merchantId = e.currentTarget.dataset.id;
			if (merchantId) {
				// 跳转到商户后台管理页面
				app.goto('/pagesExt/business/index?id=' + merchantId);
			}
		},
		
		// 数据加载完成
		loaded: function () {
			this.isload = true;
		}
	}
};
</script>

<style>
.container {
	background: #f8f8f8;
	min-height: 100vh;
}

/* 统计卡片样式 */
.stats-container {
	padding: 20rpx;
}

.stats-card {
	display: flex;
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
	flex: 1;
	text-align: center;
}

.stats-number {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #4CAF50;
	margin-bottom: 10rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #666;
}

.amount-card {
	display: flex;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	padding: 30rpx;
	color: white;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.amount-item {
	flex: 1;
	text-align: center;
}

.amount-label {
	display: block;
	font-size: 24rpx;
	opacity: 0.9;
	margin-bottom: 10rpx;
}

.amount-value {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
}

/* 筛选条件样式 */
.filter-container {
	background: white;
	margin: 0 20rpx 20rpx;
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.filter-row {
	display: flex;
	margin-bottom: 20rpx;
}

.filter-row:last-child {
	margin-bottom: 0;
}

.search-box {
	flex: 1;
	position: relative;
	background: #f8f8f8;
	border-radius: 25rpx;
	padding: 0 20rpx;
	height: 70rpx;
	display: flex;
	align-items: center;
}

.search-input {
	flex: 1;
	height: 70rpx;
	line-height: 70rpx;
	font-size: 28rpx;
	color: #333;
}

.search-icon {
	font-size: 32rpx;
	color: #999;
	margin-left: 20rpx;
}

.filter-item {
	flex: 1;
	height: 70rpx;
	line-height: 70rpx;
	padding: 0 20rpx;
	background: #f8f8f8;
	border-radius: 10rpx;
	margin-right: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
	color: #333;
}

.filter-item:last-child {
	margin-right: 0;
}

/* 商户列表样式 */
.merchant-list {
	padding: 0 20rpx;
}

.merchant-item {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 商户头部信息 */
.merchant-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.merchant-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 15rpx;
	overflow: hidden;
	margin-right: 20rpx;
}

.avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.merchant-info {
	flex: 1;
}

.merchant-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.merchant-category {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.merchant-address {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #999;
}

.merchant-address .iconfont {
	font-size: 24rpx;
	margin-right: 10rpx;
}

.address-text {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.merchant-status {
	
}

.status-badge {
	font-size: 22rpx;
	color: white;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

/* 商户统计信息 */
.merchant-stats {
	display: flex;
	padding: 20rpx 0;
	border-top: 1rpx solid #f0f0f0;
	border-bottom: 1rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.stat-value {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

/* 操作按钮 */
.merchant-actions {
	display: flex;
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	margin-right: 20rpx;
	background: #f8f8f8;
	border-radius: 15rpx;
	font-size: 24rpx;
	color: #666;
}

.action-btn:last-child {
	margin-right: 0;
}

.action-btn .iconfont {
	font-size: 32rpx;
	margin-bottom: 10rpx;
	color: #4CAF50;
}

.action-btn:active {
	background: #e8f5e8;
}
</style> 