<template>
  <view class="container">
    <form @submit="saveTemplate">
      <!-- 配送方式 -->
      <view class="form-item">
        <text class="item-label">配送方式</text>
        <view class="radio-group">
          <label class="radio-item">
            <radio :checked="formData.pstype === 0" @tap="formData.pstype = 0" />
            <text>普通快递</text>
          </label>
          <label class="radio-item">
            <radio :checked="formData.pstype === 1" @tap="formData.pstype = 1" />
            <text>到店自提</text>
          </label>
          <label class="radio-item">
            <radio :checked="formData.pstype === 2" @tap="formData.pstype = 2" />
            <text>同城配送</text>
          </label>
        </view>
      </view>

      <!-- 模板名称 -->
      <view class="form-item">
        <text class="item-label">显示名称</text>
        <input v-model="formData.name" placeholder="请输入模板名称" class="input" />
      </view>

      <!-- 计价方式 -->
      <view class="form-item">
        <text class="item-label">计价方式</text>
        <view class="radio-group">
          <label class="radio-item">
            <radio :checked="formData.type === 1" @tap.stop="handleTypeChange(1)" />
            <text @tap.stop="handleTypeChange(1)">按重量(克)</text>
          </label>
          <label class="radio-item">
            <radio :checked="formData.type === 2" @tap.stop="handleTypeChange(2)" />
            <text @tap.stop="handleTypeChange(2)">按件数(个)</text>
          </label>
        </view>
      </view>

      <!-- 运费规则表格 -->
      <view class="rules-section">
        <view class="table-header">
          <text>区域</text>
          <text>是否配送</text>
          <text>{{formData.type === 1 ? '首重重量(克)' : '首件数量(个)'}}</text>
          <text>{{formData.type === 1 ? '首重费用(元)' : '首件费用(元)'}}</text>
          <text>{{formData.type === 1 ? '续重重量(克)' : '续件数量(个)'}}</text>
          <text>{{formData.type === 1 ? '续重费用(元)' : '续件费用(元)'}}</text>
          <text>操作</text>
        </view>

        <view class="table-body">
          <view class="table-row" v-for="(area, index) in formData.pricedata" :key="index">
            <view class="table-cell region-cell" 
                  :class="{'clickable': area.region !== '全国(默认运费)'}" 
                  @click="handleRegionSelect(index)">
              <text :class="{'default-region': area.region === '全国(默认运费)'}">{{getRegionText(area.region)}}</text>
              <text v-if="area.region !== '全国(默认运费)'" class="select-icon">选择</text>
            </view>
            <view class="table-cell">
              <switch :checked="area.is_delivery" @change="toggleDelivery(index)" />
            </view>
            <view class="table-cell">
              <input type="number" v-model="area.fristweight" />
            </view>
            <view class="table-cell">
              <input type="number" v-model="area.fristprice" />
            </view>
            <view class="table-cell">
              <input type="number" v-model="area.secondweight" />
            </view>
            <view class="table-cell">
              <input type="number" v-model="area.secondprice" />
            </view>
            <view class="table-cell">
              <text class="delete-btn" @tap="removeArea(index)" 
                    v-if="area.region !== '全国(默认运费)'">删除</text>
            </view>
          </view>
        </view>

        <!-- 添加区域按钮 -->
        <view class="add-area-btn" @tap="addArea" v-if="formData.pricedata.length < 20">
          <text class="icon">+</text>
          <text>添加区域</text>
        </view>
      </view>

      <!-- 满额包邮设置 -->
      <view class="form-item">
        <text class="item-label">满额包邮</text>
        <view class="switch-group">
          <switch :checked="formData.freeset === 1" @change="(e) => formData.freeset = e.detail.value ? 1 : 0" />
          <input 
            v-if="formData.freeset === 1"
            type="digit" 
            v-model="formData.free_price"
            class="price-input"
            placeholder="请输入包邮金额" 
          />
          <text v-if="formData.freeset === 1" class="unit">元</text>
        </view>
      </view>

      <!-- 满额起送 -->
      <view class="form-item">
        <text class="item-label">满额起送</text>
        <view class="switch-group">
          <switch :checked="formData.minpriceset === 1" @change="(e) => formData.minpriceset = e.detail.value ? 1 : 0" />
          <input 
            v-if="formData.minpriceset === 1"
            type="digit" 
            v-model="formData.minprice"
            class="price-input"
            placeholder="请输入起送金额" 
          />
          <text v-if="formData.minpriceset === 1" class="unit">元</text>
        </view>
      </view>

      <button form-type="submit" class="save-btn">保存</button>
    </form>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      id: '',
      formData: {
        id: '',
        pstype: 0,
        name: '普通快递',
        type: 1,
        status: 1,
        sort: 0,
        freeset: 0,
        free_price: 0,
        minpriceset: 0,
        minprice: 0,
        pricedata: [{
          region: '全国(默认运费)',
          is_delivery: true,
          fristweight: '1000',
          fristprice: '0',
          secondweight: '1000',
          secondprice: '0'
        }],
        formdata: []
      }
    }
  },

  onLoad(opt) {
    this.opt = app.getopts(opt);
    if(opt.id) {
      this.id = opt.id;
      this.getTemplateInfo();
    }
  },

  methods: {
    // 获取区域文本显示
    getRegionText(region) {
      if (!region) return '点击选择区域';
      if (region === '全国(默认运费)') return region;
      
      try {
        // 计算选择的省份数量
        const provinceCount = region.split(';').filter(r => r).length;
        return `已选${provinceCount}个省市`;
      } catch (e) {
        console.error('解析区域文本失败:', e);
        return '点击选择区域';
      }
    },

    // 切换是否配送
    toggleDelivery(index) {
      this.formData.pricedata[index].is_delivery = !this.formData.pricedata[index].is_delivery;
    },

    // 添加区域
    addArea() {
      if (this.formData.pricedata.length >= 20) {
        uni.showToast({
          title: '最多添加20个区域',
          icon: 'none'
        });
        return;
      }

      const hasDefault = this.formData.pricedata.some(
        item => item.region === '全国(默认运费)'
      );

      this.formData.pricedata.push({
        region: hasDefault ? '' : '全国(默认运费)',
        is_delivery: true,
        fristweight: '1000',
        fristprice: '0',
        secondweight: '1000',
        secondprice: '0'
      });
    },

    // 删除区域
    removeArea(index) {
      this.formData.pricedata.splice(index, 1);
    },

    // 处理区域选择点击
    handleRegionSelect(index) {
      const area = this.formData.pricedata[index];
      
      // 如果是默认运费，显示提示并返回
      if (area.region === '全国(默认运费)') {
        uni.showToast({
          title: '默认运费区域不可修改',
          icon: 'none'
        });
        return;
      }
      
      // 打开选择页面
      uni.navigateTo({
        url: './region-select',
        events: {
          onSelected: (regions) => {
            if (!Array.isArray(regions)) {
              console.error('接收到的regions不是数组:', regions);
              return;
            }

            try {
              // 将选中的区域按省份分组并处理
              const provinceMap = new Map();
              regions.forEach(region => {
                const provinceName = region.province.name;
                if (!provinceMap.has(provinceName)) {
                  provinceMap.set(provinceName, new Set());
                }
                // 如果是全选，则使用"全部地区"
                if (region.city.name === '全部地区') {
                  provinceMap.get(provinceName).add('全部地区');
                } else {
                  provinceMap.get(provinceName).add(region.city.name);
                }
              });

              // 转换为指定格式
              let formattedRegions = Array.from(provinceMap.entries()).map(([province, cities]) => {
                const cityList = Array.from(cities);
                return `${province}[${cityList.join(',')}]`;
              }).join(';');
              
              // 确保末尾有分号
              if (formattedRegions && !formattedRegions.endsWith(';')) {
                formattedRegions += ';';
              }
              
              // 更新区域数据
              this.$set(this.formData.pricedata[index], 'region', formattedRegions);
            } catch (error) {
              console.error('处理区域数据时出错:', error);
              uni.showToast({
                title: '处理区域数据失败',
                icon: 'none'
              });
            }
          }
        },
        success: (res) => {
          // 解析当前已选择的区域数据
          let currentRegions = [];
          try {
            if (area.region && area.region !== '全国(默认运费)') {
              // 将已保存的字符串格式转换为选择器需要的格式
              const regionPairs = area.region.split(';').filter(r => r);
              regionPairs.forEach(pair => {
                const [provinceName, areasStr] = pair.split('[');
                const areas = areasStr.replace(']', '').split(',');
                
                // 构造选择器需要的数据结构
                areas.forEach(areaName => {
                  currentRegions.push({
                    province: { name: provinceName },
                    city: { name: '全部地区' }, // 由于数据结构限制，这里使用默认值
                    area: { name: areaName },
                    name: `${provinceName}全部地区${areaName}`,
                    code: `${provinceName}_${areaName}` // 生成一个临时的唯一标识
                  });
                });
              });
            }
          } catch (e) {
            console.error('解析已选区域失败:', e);
          }
          
          // 传递当前选择的数据到选择页面
          res.eventChannel.emit('currentSelected', {
            selected: currentRegions
          });
        }
      });
    },

    // 保存模板
    saveTemplate() {
      if(!this.validateForm()) return;
      
      let that = this;
      // 构建提交数据对象
      const submitData = {
        info: {
          id: that.id || '',
          pstype: that.formData.pstype,
          name: that.formData.name,
          type: that.formData.type,
          storetype: 0,
          freeset: that.formData.freeset,
          free_price: that.formData.free_price,
          minprice: that.formData.minprice,
          sort: that.formData.sort,
          status: that.formData.status,
          pricedata: that.formData.pricedata.map(area => ({
            region: area.region,
            is_delivery: area.is_delivery ? 1 : 0,
            fristweight: area.fristweight,
            fristprice: area.fristprice,
            secondweight: area.secondweight,
            secondprice: area.secondprice
          }))
        }
      };

      app.post('ApiAdminProduct/freightSave', submitData, function(res) {
        if(res.status === 1) {
          app.success(res.msg);
          setTimeout(() => {
            uni.navigateBack({
              delta: 1
            });
          }, 1500);
        } else {
          app.error(res.msg);
        }
      });
    },

    // 获取模板信息
    getTemplateInfo() {
      let that = this;
      app.post('ApiAdminProduct/freightInfo', {
        id: that.id
      }, function(res) {
        if(res.info) {
          // 处理价格数据
          let pricedata = res.info.pricedata;
          try {
            pricedata = typeof pricedata === 'string' ? JSON.parse(pricedata) : pricedata;
          } catch(e) {
            pricedata = [{
              region: '全国(默认运费)',
              is_delivery: true,
              fristweight: '1000',
              fristprice: '0', 
              secondweight: '1000',
              secondprice: '0'
            }];
          }

          // 更新表单数据
          that.formData = {
            id: res.info.id || '',
            pstype: res.info.pstype || 0,
            name: res.info.name || '普通快递',
            type: res.info.type || 1,
            status: res.info.status || 1,
            sort: res.info.sort || 0,
            freeset: res.info.freeset || 0,
            free_price: res.info.free_price || 0,
            minpriceset: res.info.minpriceset || 0,
            minprice: res.info.minprice || 0,
            pricedata: pricedata
          };
        }
      });
    },

    // 获取区域列表
    getAreaList() {
      let that = this;
      uni.request({
        url: app.globalData.pre_url + '/static/area.json',
        method: 'GET',
        success: function(res) {
          that.areaList = res.data;
        }
      });
    },

    // 表单验证
    validateForm() {
      const { name, type, pricedata, freeset, free_price, minpriceset, minprice } = this.formData;
      
      if(!name) {
        app.error('请输入模板名称');
        return false;
      }

      if(!pricedata || pricedata.length === 0) {
        app.error('请设置运费规则');
        return false;
      }

      // 验证满额包邮
      if(freeset === 1) {
        if(!free_price || free_price <= 0) {
          app.error('请输入正确的包邮金额');
          return false;
        }
      }

      // 验证满额起送
      if(minpriceset === 1) {
        if(!minprice || minprice <= 0) {
          app.error('请输入正确的起送金额');
          return false;
        }
      }

      // 验证每区域的运费规则
      for(let area of pricedata) {
        if(area.is_delivery) {
          if(!area.fristweight || !area.fristprice) {
            app.error('请完善首重信息');
            return false;
          }
          if(!area.secondweight || !area.secondprice) {
            app.error('请完善续重信���');
            return false;
          }
        }
      }

      return true;
    },

    // 获取运费模板列表
    getdata(loadmore) {
      if(!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
      }

      let that = this;
      let pagenum = that.pagenum;
      that.nodata = false;
      that.nomore = false;
      that.loading = true;

      app.post('ApiAdminProduct/freightList', {
        keyword: that.keyword,
        pagenum: pagenum,
        st: that.st
      }, function(res) {
        that.loading = false;
        let data = res.datalist;
        
        if(pagenum == 1) {
          that.countall = res.countall;
          that.count0 = res.count0;
          that.count1 = res.count1;
          that.datalist = data;
          if(data.length == 0) {
            that.nodata = true;
          }
          that.loaded();
        } else {
          if(data.length == 0) {
            that.nomore = true;
          } else {
            let datalist = that.datalist;
            let newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },

    // 切换标签页
    changetab(st) {
      this.st = st;
      this.getdata();
    },

    // 搜索确认
    searchConfirm(e) {
      this.keyword = e.detail.value;
      this.getdata(false);
    },

    // 切换计价方式
    handleTypeChange(type) {
      this.formData.type = type;
      
      // 根据类型设置默认值
      this.formData.pricedata.forEach(area => {
        if (type === 1) { // 按重量
          area.fristweight = '1000';
          area.secondweight = '1000';
        } else { // 按件数
          area.fristweight = '1';
          area.secondweight = '1';
        }
      });
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
  background: #f7f8fa;
  min-height: 100vh;
}

/* 表单项样式优化 */
.form-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #fff;
  min-height: 80rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.02);
}

.item-label {
  width: 140rpx;
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

/* 单选按钮组样式 */
.radio-group {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}

.radio-item {
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
}

/* 自定义radio样式 */
.radio-item radio {
  transform: scale(0.8);
  margin-right: 6rpx;
}

.radio-item text {
  margin-left: 4rpx;
}

/* 选中状态的文字颜色 */
.radio-item.active text {
  color: #2d8cf0;
}

/* 区域选择器样式 */
.region-selector {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 28rpx;
  color: #333;
}

.region-selector .arrow {
  margin-left: 10rpx;
  color: #999;
}

/* 运费规则表格样式优化 */
.rules-section {
  background: #fff;
  margin: 12rpx 0;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.02);
}

.table-header {
  display: flex;
  background: #f8f8f8;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.table-header text {
  flex: 1;
  padding: 0 10rpx;
}

.table-body {
  font-size: 26rpx;
}

.table-row {
  display: flex;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #eee;
  align-items: center;
}

.table-cell {
  flex: 1;
  text-align: center;
  padding: 0 10rpx;
}

.table-cell input {
  width: 90%;
  height: 56rpx;
  border: 1rpx solid #eee;
  border-radius: 4rpx;
  text-align: center;
  margin: 0 auto;
  background: #f9f9f9;
  transition: all 0.3s;
}

.table-cell input:focus {
  border-color: #2d8cf0;
  background: #fff;
}

/* 删除按钮样式优化 */
.delete-btn {
  color: #ff4d4f;
  font-size: 26rpx;
  padding: 6rpx 12rpx;
  border: 1rpx solid #ff4d4f;
  border-radius: 4rpx;
  transition: all 0.3s;
}

.delete-btn:active {
  background: rgba(255, 77, 79, 0.1);
}

/* 相关样式优化 */
.switch-group {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-input {
  width: 180rpx;
  height: 56rpx;
  border: 1rpx solid #eee;
  border-radius: 4rpx;
  margin: 0 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  background: #f9f9f9;
  transition: all 0.3s;
}

.price-input:focus {
  border-color: #2d8cf0;
  background: #fff;
}

/* 保存按钮样式优化 */
.save-btn {
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  background: #2d8cf0;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin: 30rpx auto;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(45, 140, 240, 0.15);
  transition: all 0.3s;
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(45, 140, 240, 0.2);
}

/* 输入框样式 */
.input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 20rpx;
}

/* 添加区域按钮样式优化 */
.add-area-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  margin: 16rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  color: #2d8cf0;
  font-size: 28rpx;
  transition: all 0.3s;
}

.add-area-btn:active {
  background: #e8e8e8;
}

/* 提示文本样式 */
.tip-text {
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 30rpx;
  line-height: 1.5;
}

/* 必填记 */
.required::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4rpx;
}

/* 禁用状态 */
.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 表内容溢出处理 */
.table-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分割线 */
.divider {
  height: 20rpx;
  background: #f5f5f5;
}

/* 区选择单元格样式优化 */
.region-cell {
  position: relative;
  padding: 0 16rpx;
  min-height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.clickable {
  cursor: pointer;
  user-select: none;
}

.clickable:active {
  background-color: #f5f5f5;
}

.default-region {
  color: #666;
}

.select-icon {
  font-size: 24rpx;
  color: #2d8cf0;
  background: rgba(45, 140, 240, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-left: 8rpx;
}

/* 添加区域按钮样式 */
.add-area-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  margin: 16rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  color: #2d8cf0;
  font-size: 28rpx;
}

.add-area-btn .icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

/* 表格样式优化 */
.table-header {
  background: #f8f8f8;
  font-weight: bold;
}

.table-cell {
  flex: 1;
  min-width: 0; /* 止内容溢 */
  padding: 20rpx 10rpx;
  text-align: center;
  font-size: 26rpx;
}

.table-cell input {
  width: 90%;
  height: 60rpx;
  border: 1rpx solid #eee;
  border-radius: 4rpx;
  text-align: center;
  margin: 0 auto;
}

.delete-btn {
  color: #ff4d4f;
  font-size: 26rpx;
  padding: 4rpx 12rpx;
}

.delete-btn:active {
  opacity: 0.8;
}

/* 添加相关样式 */
.switch-group {
  flex: 1;
  display: flex;
  align-items: center;
}

.price-input {
  width: 200rpx;
  height: 60rpx;
  border: 1rpx solid #eee;
  border-radius: 4rpx;
  margin: 0 20rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.unit {
  font-size: 28rpx;
  color: #666;
}
</style>