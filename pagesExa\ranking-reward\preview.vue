<template>
	<view class="container">
		<!-- 规则信息 -->
		<view class="rule-header">
			<view class="rule-name">{{ruleInfo.name}}</view>
			<view class="rule-desc">
				<text class="desc-label">奖励模式：</text>
				<text v-if="ruleInfo.reward_mode === 1">根据自身消费</text>
				<text v-else-if="ruleInfo.reward_mode === 2">根据排名</text>
			</view>
			<view class="rule-desc">
				<text class="desc-label">排名类型：</text>
				<text>{{ruleInfo.rank_type_text}}</text>
			</view>
			<view class="rule-desc">
				<text class="desc-label">奖励比例：</text>
				<text>{{ruleInfo.total_reward_rate}}%</text>
			</view>
		</view>
		
		<!-- 我的排名信息 -->
		<view class="my-rank-info" v-if="hasRankInfo">
			<view class="rank-title">我的排名</view>
			<view class="rank-content">
				<view class="rank-num-box">
					<text class="rank-num">{{myRankInfo.rank || '--'}}</text>
					<text class="rank-label">当前排名</text>
				</view>
				<view class="rank-divider"></view>
				<view class="rank-num-box">
					<text class="rank-num">{{myRankInfo.count_num || 0}}</text>
					<text class="rank-label">{{ruleInfo.rank_type === 1 ? '下级人数' : '特定等级人数'}}</text>
				</view>
				<view class="rank-divider"></view>
				<view class="rank-num-box">
					<text class="rank-num reward-color">{{myRankInfo.reward_amount || '0.00'}}</text>
					<text class="rank-label">预计奖励</text>
				</view>
			</view>
		</view>
		
		<!-- 分红池信息 -->
		<view class="pool-info">
			<view class="pool-flex">
			<!-- 	<view class="pool-item">
					<view class="pool-label">本月消费总额</view>
					<view class="pool-value">¥{{total_consume || '0.00'}}</view>
				</view> -->
				<view class="pool-divider"></view>
				<view class="pool-item">
					<view class="pool-label">分红池总额</view>
					<view class="pool-value reward-color">¥{{pool_amount || '0.00'}}</view>
				</view>
			</view>
		</view>
		
		<!-- 周期选择器 -->
		<view class="period-selector">
			<picker mode="date" fields="month" :value="selectedDate" @change="periodChange">
				<view class="picker-box">
					<text>{{selectedPeriodText}}</text>
					<text class="picker-arrow">▼</text>
				</view>
			</picker>
		</view>
		
		<!-- 排名列表 -->
		<view class="rank-list">
			<view class="list-header">
				<text class="header-item rank-col">排名</text>
				<text class="header-item member-col">会员信息</text>
				<text class="header-item count-col">{{ruleInfo.rank_type === 1 ? '下级人数' : '特定等级人数'}}</text>
			<!-- 	<text class="header-item rate-col">奖励比例</text> -->
				<text class="header-item amount-col">奖励金额</text>
			</view>
			
			<view class="no-data" v-if="rankingList.length === 0">
				<text>暂无排名数据</text>
			</view>
			
			<view 
				class="list-item" 
				v-for="(item, index) in rankingList" 
				:key="index"
				:class="{'my-rank': item.is_me}"
			>
				<view class="item-col rank-col">
					<view :class="'rank-tag ' + (index < 3 ? 'rank-'+(index+1) : '')">{{item.rank}}</view>
				</view>
				<view class="item-col member-col">
					<view class="member-info">
						<image class="member-avatar" :src="item.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
						<text class="member-name">{{item.member_info}}</text>
					</view>
				</view>
				<view class="item-col count-col">{{item.count_num}}</view>
				<!-- <view class="item-col rate-col">{{item.reward_rate}}%</view> -->
				<view class="item-col amount-col reward-color">{{item.reward_amount}}</view>
			</view>
		</view>
		
		<!-- 分页器 -->
		<view class="pagination" v-if="totalPages > 1">
			<view 
				class="page-btn prev-btn" 
				:class="{'disabled': currentPage <= 1}"
				@click="prevPage"
			>上一页</view>
			<view class="page-info">{{currentPage}}/{{totalPages}}</view>
			<view 
				class="page-btn next-btn" 
				:class="{'disabled': currentPage >= totalPages}"
				@click="nextPage"
			>下一页</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			const now = new Date();
			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			
			return {
				ruleId: null,
				currentPage: 1,
				pageSize: 20,
				totalPages: 1,
				total: 0,
				
				ruleInfo: {
					id: 0,
					name: '',
					reward_mode: 2,
					total_reward_rate: 0,
					rank_type: 1,
					rank_type_text: ''
				},
				
				rankingList: [],
				myRankInfo: {},
				hasRankInfo: false,
				
				total_consume: '0.00',
				pool_amount: '0.00',
				
				selectedDate: `${year}-${month}`,
				selectedPeriod: `${year}${month}`,
				selectedPeriodText: `${year}年${month}月`,
				
				loading: false
			}
		},
		onLoad(options) {
			console.log(`${this.formatTime()}-INFO-[ranking-reward/preview][onLoad_001] 页面加载，参数：`, options);
			
			if (options.rule_id) {
				this.ruleId = parseInt(options.rule_id);
				this.initData();
			} else {
				uni.showToast({
					title: '参数错误',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		onPullDownRefresh() {
			this.initData(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			// 初始化数据
			initData(callback) {
				// 获取规则预览信息
				this.getRulePreview();
				// 获取我的排名信息
				this.getMyRankInfo();
				
				if (callback && typeof callback === 'function') {
					callback();
				}
			},
			
			// 获取规则预览信息
			getRulePreview() {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/preview][getRulePreview_001] 开始获取规则预览，规则ID：${this.ruleId}，周期：${this.selectedPeriod}`);
				
				this.loading = true;
				var that = this;
				
				app.post('ApiPaimingjiang/previewRanking', {
					rule_id: this.ruleId,
					period: this.selectedPeriod,
					pagenum: this.currentPage,
					pagesize: this.pageSize
				}, function(res) {
					that.loading = false;
					
					if (res.status === 1) {
						console.log(`${that.formatTime()}-INFO-[ranking-reward/preview][getRulePreview_002] 获取规则预览成功`);
						
						// 规则信息
						that.ruleInfo = res.data.rule_info;
						
						// 排名列表
						that.rankingList = res.data.ranking_list;
						
						// 分页信息
						that.total = res.data.total;
						that.totalPages = Math.ceil(that.total / that.pageSize);
						
						// 消费金额和分红池金额
						that.total_consume = res.data.total_consume;
						that.pool_amount = res.data.pool_amount;
					} else {
						console.log(`${that.formatTime()}-ERROR-[ranking-reward/preview][getRulePreview_003] 获取规则预览失败：${res.msg}`);
						uni.showToast({
							title: res.msg || '获取规则预览失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 获取我的排名信息
			getMyRankInfo() {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/preview][getMyRankInfo_001] 开始获取我的排名信息，规则ID：${this.ruleId}，周期：${this.selectedPeriod}`);
				
				var that = this;
				
				app.post('ApiPaimingjiang/getMyRankInfo', {
					rule_id: this.ruleId,
					period: this.selectedPeriod
				}, function(res) {
					if (res.status === 1) {
						console.log(`${that.formatTime()}-INFO-[ranking-reward/preview][getMyRankInfo_002] 获取我的排名信息成功`);
						
						if (res.data && res.data.rank) {
							that.myRankInfo = res.data;
							that.hasRankInfo = true;
						} else {
							that.hasRankInfo = false;
						}
					} else {
						console.log(`${that.formatTime()}-ERROR-[ranking-reward/preview][getMyRankInfo_003] 获取我的排名信息失败：${res.msg}`);
						that.hasRankInfo = false;
					}
				});
			},
			
			// 周期选择器变更
			periodChange(e) {
				const val = e.detail.value;
				if (!val) return;
				
				const [year, month] = val.split('-');
				
				this.selectedDate = val;
				this.selectedPeriod = `${year}${month}`;
				this.selectedPeriodText = `${year}年${month}月`;
				this.currentPage = 1;
				
				console.log(`${this.formatTime()}-INFO-[ranking-reward/preview][periodChange_001] 周期变更：${this.selectedPeriod}`);
				
				// 重新加载数据
				this.initData();
			},
			
			// 上一页
			prevPage() {
				if (this.currentPage <= 1) return;
				
				this.currentPage--;
				this.getRulePreview();
			},
			
			// 下一页
			nextPage() {
				if (this.currentPage >= this.totalPages) return;
				
				this.currentPage++;
				this.getRulePreview();
			},
			
			// 格式化时间，用于日志
			formatTime() {
				const date = new Date();
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				const seconds = date.getSeconds().toString().padStart(2, '0');
				const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${milliseconds}`;
			}
		}
	}
</script>

<style lang="scss">
.container {
	padding: 30rpx 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.reward-color {
	color: #FF6600 !important;
}

// 规则信息
.rule-header {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.rule-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	text-align: center;
}

.rule-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
	display: flex;
}

.desc-label {
	width: 160rpx;
}

// 我的排名信息
.my-rank-info {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.rank-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.rank-content {
	display: flex;
	justify-content: space-around;
	align-items: center;
}

.rank-num-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.rank-num {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.rank-label {
	font-size: 24rpx;
	color: #999;
}

.rank-divider {
	width: 2rpx;
	height: 80rpx;
	background-color: #EEEEEE;
}

// 分红池信息
.pool-info {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.pool-flex {
	display: flex;
	justify-content: space-around;
	align-items: center;
}

.pool-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.pool-label {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.pool-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.pool-divider {
	width: 2rpx;
	height: 60rpx;
	background-color: #EEEEEE;
}

// 周期选择器
.period-selector {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.picker-box {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
	color: #333;
}

.picker-arrow {
	font-size: 24rpx;
	color: #999;
}

// 排名列表
.rank-list {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
	overflow: hidden;
}

.list-header {
	display: flex;
	background-color: #F7F7F7;
	padding: 20rpx 0;
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
}

.header-item {
	text-align: center;
}

.list-item {
	display: flex;
	border-bottom: 1rpx solid #EEEEEE;
	padding: 20rpx 0;
	font-size: 26rpx;
	color: #333;
}

.list-item:last-child {
	border-bottom: none;
}

.my-rank {
	background-color: rgba(255, 102, 0, 0.05);
}

.item-col {
	display: flex;
	justify-content: center;
	align-items: center;
}

.rank-col {
	width: 15%;
}

.member-col {
	width: 35%;
}

.count-col {
	width: 15%;
}

.rate-col {
	width: 15%;
}

.amount-col {
	width: 20%;
}

.rank-tag {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	background-color: #F0F0F0;
	display: flex;
	justify-content: center;
	align-items: center;
	font-weight: bold;
}

.rank-1 {
	background-color: #FFD700;
	color: #FFFFFF;
}

.rank-2 {
	background-color: #C0C0C0;
	color: #FFFFFF;
}

.rank-3 {
	background-color: #CD7F32;
	color: #FFFFFF;
}

.member-info {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 0 10rpx;
}

.member-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	margin-right: 10rpx;
}

.member-name {
	font-size: 26rpx;
	color: #333;
	max-width: 180rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

// 分页器
.pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 30rpx;
	margin-bottom: 30rpx;
}

.page-btn {
	background-color: #FFFFFF;
	border: 1rpx solid #DDDDDD;
	border-radius: 8rpx;
	padding: 15rpx 30rpx;
	font-size: 28rpx;
	color: #333;
}

.disabled {
	color: #CCCCCC;
	background-color: #F5F5F5;
}

.page-info {
	margin: 0 30rpx;
	font-size: 28rpx;
	color: #666;
}

// 无数据提示
.no-data {
	text-align: center;
	padding: 50rpx 0;
	font-size: 28rpx;
	color: #999;
}
</style> 