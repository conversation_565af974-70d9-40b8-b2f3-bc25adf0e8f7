<template>
<view class="container">
    <!-- 顶部摘要信息 -->
    <view class="summary-card">
        <view class="summary-title">进货款信息</view>
        <view class="summary-content">
            <view class="summary-item">
                <text class="summary-label">当前余额</text>
                <text class="summary-value">￥{{ summary.balance || '0.00' }}</text>
            </view>
            <view class="summary-item">
                <text class="summary-label">本月充值</text>
                <text class="summary-value income">￥{{ summary.month_recharge || '0.00' }}</text>
            </view>
            <view class="summary-item">
                <text class="summary-label">本月消费</text>
                <text class="summary-value expense">￥{{ summary.month_consume || '0.00' }}</text>
            </view>
        </view>
    </view>
    
    <!-- 筛选条件 -->
    <view class="filter-section">
        <view class="date-range">
            <view class="date-picker" @tap="showDatePicker('start')">
                <text>开始日期：{{ filter.start_time || '请选择' }}</text>
            </view>
            <text class="date-separator">至</text>
            <view class="date-picker" @tap="showDatePicker('end')">
                <text>结束日期：{{ filter.end_time || '请选择' }}</text>
            </view>
        </view>
        <view class="type-filter">
            <view class="type-option" 
                  :class="{ active: filter.type === '' }" 
                  @tap="changeType('')">全部</view>
            <view class="type-option" 
                  :class="{ active: filter.type === 'income' }" 
                  @tap="changeType('income')">收入</view>
            <view class="type-option" 
                  :class="{ active: filter.type === 'expense' }" 
                  @tap="changeType('expense')">支出</view>
        </view>
        <view class="filter-actions">
            <view class="filter-btn reset" @tap="resetFilter">重置</view>
            <view class="filter-btn search" @tap="applyFilter">查询</view>
        </view>
    </view>
    
    <!-- 数据统计 -->
    <view class="statistics" v-if="statistics.total_income || statistics.total_expense">
        <view class="statistics-title">查询结果统计</view>
        <view class="statistics-content">
            <view class="stat-item">
                <text class="stat-label">收入总额</text>
                <text class="stat-value income">￥{{ statistics.total_income || '0.00' }}</text>
            </view>
            <view class="stat-item">
                <text class="stat-label">支出总额</text>
                <text class="stat-value expense">￥{{ statistics.total_expense || '0.00' }}</text>
            </view>
        </view>
    </view>
    
    <!-- 交易记录列表 -->
    <view class="transaction-list" v-if="transactionList.length > 0">
        <view class="transaction-item" v-for="(item, index) in transactionList" :key="item.id">
            <view class="transaction-header">
                <text class="transaction-time">{{ item.createtime_text }}</text>
                <text class="transaction-type" :class="item.type">{{ item.type === 'income' ? '收入' : '支出' }}</text>
            </view>
            <view class="transaction-body">
                <view class="transaction-amount" :class="item.type">
                    {{ item.type === 'income' ? '+' : '-' }}￥{{ item.money_abs }}
                </view>
                <view class="transaction-after">
                    <text class="after-label">余额：</text>
                    <text class="after-value">￥{{ item.after }}</text>
                </view>
            </view>
            <view class="transaction-remark">
                <text class="remark-label">备注：</text>
                <text class="remark-content">{{ item.remark || '无' }}</text>
            </view>
        </view>
    </view>
    
    <nodata v-if="nodata"></nodata>
    <nomore v-if="nomore"></nomore>
    <loading v-if="loading"></loading>
    <popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
    data() {
        // 获取当前年月
        var now = new Date();
        var year = now.getFullYear();
        var month = now.getMonth() + 1;
        month = month < 10 ? '0' + month : month;
        var currentMonth = year + '-' + month;
        
        // 获取当前日期
        var day = now.getDate();
        day = day < 10 ? '0' + day : day;
        var currentDate = year + '-' + month + '-' + day;
        
        // 获取本月第一天
        var firstDay = year + '-' + month + '-01';
        
        return {
            loading: false,
            summaryLoading: false, // 摘要数据加载状态
            listLoading: false, // 明细数据加载状态
            nodata: false,
            nomore: false,
            
            // 摘要数据
            summary: {
                balance: '0.00',
                month_recharge: '0.00',
                month_consume: '0.00'
            },
            
            // 筛选条件
            filter: {
                start_time: firstDay, // 默认设置为本月第一天
                end_time: currentDate, // 默认设置为当前日期
                type: '',
            },
            currentMonth: currentMonth,
            
            // 统计信息
            statistics: {
                total_income: '0.00',
                total_expense: '0.00'
            },
            
            // 交易记录
            transactionList: [],
            pagenum: 1,
            pernum: 20,
            
            // 日期选择器
            datePickerType: '',
            showDatePicker: false,
            
            // 自动加载控制
            autoLoadEnabled: true
        };
    },
    watch: {
        // 监听筛选条件变化，自动触发数据查询
        'filter.type': function(newVal, oldVal) {
            if (this.autoLoadEnabled && newVal !== oldVal) {
                console.log('2023-07-01 10:30:18-INFO-[balance.vue][watch_001]筛选类型变化，自动加载数据：', newVal);
                this.pagenum = 1;
                this.getTransactionList();
            }
        }
    },
    onLoad: function() {
        console.log('2023-07-01 10:30:19-INFO-[balance.vue][onLoad_001]页面加载，初始筛选条件：', this.filter);
        // 先获取摘要数据
        this.getSummaryData();
        // 获取交易记录（默认筛选条件）
        this.getTransactionList();
    },
    onPullDownRefresh: function() {
        this.pagenum = 1;
        this.transactionList = [];
        this.getSummaryData();
        this.getTransactionList(false, true);
    },
    onReachBottom: function() {
        if (!this.nodata && !this.nomore && !this.listLoading) {
            this.pagenum = this.pagenum + 1;
            this.getTransactionList(true);
        }
    },
    methods: {
        // 获取摘要数据
        getSummaryData: function() {
            var that = this;
            that.summaryLoading = true;
            
            app.post('ApiAdminFinance/balanceSummary', {
                date: that.currentMonth
            }, function(res) {
                console.log('2023-07-01 10:30:15-INFO-[balance.vue][getSummaryData_001]获取余额摘要：', res);
                
                if (res.status === 1) {
                    that.summary = res.data;
                } else {
                    app.error(res.msg || '获取余额摘要失败');
                }
                that.summaryLoading = false;
            });
        },
        
        // 获取交易记录
        getTransactionList: function(loadmore, isRefresh) {
            var that = this;
            
            if (that.listLoading && !isRefresh) {
                return;
            }
            
            if (!loadmore) {
                that.pagenum = 1;
                that.transactionList = [];
            }
            
            that.listLoading = true;
            that.loading = true;
            that.nodata = false;
            that.nomore = false;
            
            var params = {
                pagenum: that.pagenum,
                pernum: that.pernum,
                start_time: that.filter.start_time,
                end_time: that.filter.end_time,
                type: that.filter.type
            };
            
            console.log('2023-07-01 10:30:16-INFO-[balance.vue][getTransactionList_001]查询参数：', params);
            
            app.post('ApiAdminFinance/balanceLog', params, function(res) {
                that.listLoading = false;
                that.loading = false;
                
                if (isRefresh) {
                    uni.stopPullDownRefresh();
                }
                
                console.log('2023-07-01 10:30:17-INFO-[balance.vue][getTransactionList_002]获取交易记录：', res);
                
                if (res.status === 1) {
                    var data = res.data || [];
                    
                    // 更新统计信息
                    if (that.pagenum === 1) {
                        that.statistics = res.statistics || {
                            total_income: '0.00',
                            total_expense: '0.00'
                        };
                    }
                    
                    if (that.pagenum === 1 && data.length === 0) {
                        that.nodata = true;
                    } else if (data.length === 0) {
                        that.nomore = true;
                    } else {
                        if (loadmore) {
                            that.transactionList = that.transactionList.concat(data);
                        } else {
                            that.transactionList = data;
                        }
                    }
                } else {
                    app.error(res.msg || '获取交易记录失败');
                    that.nodata = true;
                }
            });
        },
        
        // 显示日期选择器
        showDatePicker: function(type) {
            var that = this;
            that.datePickerType = type;
            
            uni.showDatePicker({
                date: that.filter[type === 'start' ? 'start_time' : 'end_time'] || new Date().toISOString().split('T')[0],
                success: function(res) {
                    // 暂时禁用自动加载，避免连续多次调用
                    that.autoLoadEnabled = false;
                    
                    if (type === 'start') {
                        that.filter.start_time = res.date;
                    } else {
                        that.filter.end_time = res.date;
                    }
                    
                    // 日期变更后自动加载数据
                    setTimeout(function() {
                        that.autoLoadEnabled = true;
                        that.pagenum = 1;
                        that.getTransactionList();
                    }, 100);
                }
            });
        },
        
        // 切换交易类型筛选
        changeType: function(type) {
            // 如果类型相同则不重复加载
            if (this.filter.type === type) {
                return;
            }
            this.filter.type = type;
            // 注意：这里不需要手动调用getTransactionList，因为我们已经添加了watch监听器
        },
        
        // 重置筛选条件
        resetFilter: function() {
            // 暂时禁用自动加载，避免watch触发多次查询
            this.autoLoadEnabled = false;
            
            var now = new Date();
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            month = month < 10 ? '0' + month : month;
            var day = now.getDate();
            day = day < 10 ? '0' + day : day;
            var currentDate = year + '-' + month + '-' + day;
            var firstDay = year + '-' + month + '-01';
            
            this.filter = {
                start_time: firstDay,
                end_time: currentDate,
                type: ''
            };
            
            // 重置后自动加载数据
            setTimeout(() => {
                this.autoLoadEnabled = true;
                this.pagenum = 1;
                this.getTransactionList();
            }, 100);
        },
        
        // 应用筛选条件
        applyFilter: function() {
            this.pagenum = 1;
            this.getTransactionList();
        }
    }
};
</script>

<style>
.container {
    padding: 20rpx;
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* 摘要卡片样式 */
.summary-card {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.summary-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20rpx;
    border-left: 8rpx solid #4285f4;
    padding-left: 20rpx;
}

.summary-content {
    display: flex;
    justify-content: space-between;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.summary-label {
    font-size: 26rpx;
    color: #666666;
    margin-bottom: 10rpx;
}

.summary-value {
    font-size: 40rpx;
    font-weight: bold;
    color: #333333;
}

.summary-value.income {
    color: #4CAF50;
}

.summary-value.expense {
    color: #F44336;
}

/* 筛选区域样式 */
.filter-section {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.date-range {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
}

.date-picker {
    flex: 1;
    background-color: #f9f9f9;
    padding: 15rpx 20rpx;
    border-radius: 8rpx;
    font-size: 26rpx;
    color: #333333;
}

.date-separator {
    margin: 0 15rpx;
    color: #999999;
}

.type-filter {
    display: flex;
    margin-bottom: 20rpx;
}

.type-option {
    flex: 1;
    text-align: center;
    padding: 15rpx 0;
    font-size: 28rpx;
    color: #666666;
    background-color: #f9f9f9;
    margin: 0 10rpx;
    border-radius: 8rpx;
}

.type-option:first-child {
    margin-left: 0;
}

.type-option:last-child {
    margin-right: 0;
}

.type-option.active {
    background-color: #4285f4;
    color: #ffffff;
}

.filter-actions {
    display: flex;
    justify-content: space-between;
}

.filter-btn {
    width: 48%;
    text-align: center;
    padding: 15rpx 0;
    border-radius: 8rpx;
    font-size: 28rpx;
}

.filter-btn.reset {
    background-color: #f5f5f5;
    color: #666666;
}

.filter-btn.search {
    background-color: #4285f4;
    color: #ffffff;
}

/* 统计信息样式 */
.statistics {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.statistics-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20rpx;
}

.statistics-content {
    display: flex;
    justify-content: space-around;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 26rpx;
    color: #666666;
    margin-bottom: 10rpx;
}

.stat-value {
    font-size: 36rpx;
    font-weight: bold;
}

.stat-value.income {
    color: #4CAF50;
}

.stat-value.expense {
    color: #F44336;
}

/* 交易记录列表样式 */
.transaction-list {
    margin-bottom: 20rpx;
}

.transaction-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.transaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
    padding-bottom: 15rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.transaction-time {
    font-size: 26rpx;
    color: #999999;
}

.transaction-type {
    font-size: 26rpx;
    padding: 6rpx 15rpx;
    border-radius: 20rpx;
}

.transaction-type.income {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.transaction-type.expense {
    background-color: rgba(244, 67, 54, 0.1);
    color: #F44336;
}

.transaction-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
}

.transaction-amount {
    font-size: 36rpx;
    font-weight: bold;
}

.transaction-amount.income {
    color: #4CAF50;
}

.transaction-amount.expense {
    color: #F44336;
}

.transaction-after {
    display: flex;
    align-items: center;
}

.after-label {
    font-size: 24rpx;
    color: #999999;
}

.after-value {
    font-size: 24rpx;
    color: #333333;
}

.transaction-remark {
    display: flex;
    font-size: 24rpx;
    color: #666666;
}

.remark-label {
    color: #999999;
    margin-right: 10rpx;
}

.remark-content {
    flex: 1;
    word-break: break-all;
}
</style> 