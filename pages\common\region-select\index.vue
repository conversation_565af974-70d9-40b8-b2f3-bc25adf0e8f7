<template>
  <view class="container">
    <!-- 顶部搜索框 -->
    <view class="search-box">
      <input type="text" v-model="searchKey" placeholder="搜索地区" />
    </view>
    
    <!-- 已选择区域展示 -->
    <view class="selected-box" v-if="selectedRegions.length > 0">
      <view class="selected-title">已选择 {{selectedRegions.length}}</view>
      <view class="selected-list">
        <view class="selected-item" v-for="(item, index) in selectedRegions" :key="index">
          <text>{{item.name}}</text>
          <text class="delete-icon" @tap="removeRegion(item)">×</text>
        </view>
      </view>
    </view>

    <!-- 地区选择器 -->
    <view class="region-tabs">
      <view class="tab" 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="{active: currentTab === index}"
            @tap="switchTab(index)">
        <text>{{tab}}</text>
      </view>
    </view>

    <!-- 地区列表 -->
    <scroll-view scroll-y class="region-list">
      <block v-if="currentTab === 0">
        <!-- 省份列表 -->
        <view class="region-item" 
              v-for="province in filteredProvinces" 
              :key="province.id"
              @tap="selectProvince(province)">
          <view class="flex-row">
            <text>{{province.name}}</text>
            <text class="select-all" @tap.stop="selectAllInProvince(province)">全选</text>
          </view>
          <text class="check-icon" v-if="isProvinceFullySelected(province)">✓</text>
        </view>
      </block>
      
      <block v-if="currentTab === 1">
        <!-- 城市列表 -->
        <view class="region-item" 
              v-for="city in filteredCities" 
              :key="city.id"
              @tap="selectCity(city)">
          <text>{{city.name}}</text>
          <text class="check-icon" v-if="isCitySelected(city)">✓</text>
        </view>
      </block>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-btns">
      <button class="btn cancel" @tap="cancel">取消</button>
      <button class="btn confirm" @tap="confirm">确定</button>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      searchKey: '',
      selectedRegions: [],
      tabs: ['省份', '城市'],
      currentTab: 0,
      currentProvince: null,
      provinces: [],
      cities: []
    }
  },

  computed: {
    filteredProvinces() {
      if (!this.searchKey) return this.provinces;
      return this.provinces.filter(province => 
        province.name.includes(this.searchKey)
      );
    },
    
    filteredCities() {
      if (!this.currentProvince) return [];
      const cities = this.currentProvince.children || [];
      if (!this.searchKey) return cities;
      return cities.filter(city => 
        city.name.includes(this.searchKey)
      );
    }
  },

  onLoad(opt) {
    this.opt = app.getopts(opt);
    // 获取省份数据
    this.getProvinces();
    
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      eventChannel.on('currentSelected', (data) => {
        this.selectedRegions = data.selected || [];
      });
    }
  },

  methods: {
    // 获取省份数据
    getProvinces() {
      let that = this;
      app.post('ApiAdminProduct/getAreas', {
        pid: 0
      }, function(res) {
        if(res.status == 1) {
          that.provinces = res.data;
        } else {
          app.error(res.msg);
        }
      });
    },

    // 选择省份
    selectProvince(province) {
      this.currentProvince = province;
      this.currentTab = 1;
      
      // 获取城市数据
      this.getCities(province.id);
    },

    // 选择城市
    selectCity(city) {
      const regionData = {
        province: this.currentProvince,
        city: city,
        name: `${this.currentProvince.name}${city.name}`
      };

      // 检查是否已选择
      const index = this.selectedRegions.findIndex(
        item => item.province.id === this.currentProvince.id && item.city.id === city.id
      );

      if (index > -1) {
        this.selectedRegions.splice(index, 1);
      } else {
        this.selectedRegions.push(regionData);
      }
    },

    // 切换选项卡
    switchTab(index) {
      if (index === 1 && !this.currentProvince) {
        app.error('请先选择省份');
        return;
      }
      this.currentTab = index;
    },

    // 移除已选择的区域
    removeRegion(region) {
      const index = this.selectedRegions.findIndex(item => item.code === region.code);
      if (index > -1) {
        this.selectedRegions.splice(index, 1);
      }
    },

    // 取消选择
    cancel() {
      uni.navigateBack();
    },

    // 确认选择
    confirm() {
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.emit('onSelected', this.selectedRegions);
      }
      uni.navigateBack();
    },

    // 全选省份
    selectAllInProvince(province) {
      if (this.isProvinceFullySelected(province)) {
        // 如果已经全选，则移除该省份所有选择
        this.selectedRegions = this.selectedRegions.filter(
          item => item.province.id !== province.id
        );
      } else {
        // 添加全选数据
        this.selectedRegions.push({
          province: province,
          city: { name: '全部地区', id: 'all' },
          name: `${province.name}全部地区`
        });
      }
    },

    // 检查省份是否已全选
    isProvinceFullySelected(province) {
      return this.selectedRegions.some(
        item => item.province.id === province.id && item.city.name === '全部地区'
      );
    },

    // 检查城市是否已选择
    isCitySelected(city) {
      return this.selectedRegions.some(
        item => item.province.id === this.currentProvince.id && item.city.id === city.id
      );
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.search-box {
  padding: 20rpx;
  background: #fff;
}

.search-box input {
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.region-tabs {
  display: flex;
  background: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.tab.active {
  color: #2d8cf0;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #2d8cf0;
}

.region-list {
  flex: 1;
  background: #fff;
}

.region-item {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #eee;
}

.check-icon {
  color: #2d8cf0;
}

.selected-box {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.selected-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
}

.selected-item {
  background: #f5f5f5;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin: 10rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
}

.delete-icon {
  margin-left: 10rpx;
  color: #999;
  font-size: 32rpx;
}

.bottom-btns {
  padding: 20rpx;
  display: flex;
  background: #fff;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}

.cancel {
  background: #f5f5f5;
  color: #666;
}

.confirm {
  background: #2d8cf0;
  color: #fff;
}

.flex-row {
  display: flex;
  align-items: center;
  flex: 1;
}

.select-all {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #2d8cf0;
  padding: 4rpx 12rpx;
  border: 1rpx solid #2d8cf0;
  border-radius: 20rpx;
}
</style> 