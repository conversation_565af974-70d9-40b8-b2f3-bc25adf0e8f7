# 预约系统时间选择逻辑说明

## 数据结构

- **datelist**: 可选择的日期列表
  - 数据来源: `ApiYuyue/product` 接口返回
  - 格式: 包含年份、日期、星期等信息
  - 用途: 显示在时间选择器顶部的日期栏

- **timelist**: 特定日期的可用时间段列表
  - 数据来源: `ApiYuyue/isgetTime` 接口返回
  - 参数: 选择的日期(`date`)和产品ID(`proid`)
  - 格式: 包含时间段和状态(可用/不可用)
  - 用途: 显示在时间选择器下方的时间格子

## 选择流程

1. **服务选择优先原则**
   - 必须先选择服务，否则无法选择时间
   - 选择服务后，根据预约模式决定下一步操作

2. **时间选择弹窗**
   - 触发方式:
     - 服务选择后自动触发(常规模式)
     - 点击"服务时间"区域手动触发
   - 初始显示: 默认加载第一个日期的可用时间段

3. **日期选择行为**
   - 点击上方日期切换不同日期
   - 切换后自动请求该日期的可用时间段
   - 更新 `days` 变量存储选择的日期

4. **时间段选择行为**
   - 点击下方时间格子选择具体时间
   - 状态为0(灰色)的时间段不可选
   - 更新 `timeindex` 和 `selectDates` 变量

5. **确认选择操作**
   - 点击"确定"按钮完成选择
   - 检查是否已选择有效时间
   - 关闭弹窗，更新显示文本
   - 如已选服务，自动进入下一步

## 预约模式

- **常规模式** (order_flow_mode = 0)
  - 完整流程: 选择服务 → 选择时间 → 下单支付
  - URL参数: `prodata` + `yydate`(预约日期和时间)

- **先支付后预约模式** (order_flow_mode = 1)
  - 简化流程: 选择服务 → 下单支付 → 后续预约时间
  - URL参数: `prodata` + `order_flow_mode=1`

## 关键代码片段

```javascript
// 检查是否需要选择时间
if(this.order_flow_mode !== 1 && (!yydate || yydate==='请选择服务时间 ' || yydate==='请选择服务时间')) {
    this.chooseTime();
    return;
}

// 获取可用时间段
app.get('ApiYuyue/isgetTime', { date: that.nowdate, proid:this.opt.id}, function (res) {
    that.loading = false;
    that.timelist = res.data;
})

// 根据预约模式处理下单
if(flowMode !== 1){
    app.goto('/yuyue/buy?prodata=' + prodata +'&yydate='+yydate);
} else {
    app.goto('/yuyue/buy?prodata=' + prodata +'&order_flow_mode=1');
}
```

## 接口说明

1. **ApiYuyue/product**
   - 功能: 获取产品详情和可预约日期列表
   - 参数: 产品ID
   - 返回: 产品信息、可选日期列表、订单流程模式等

2. **ApiYuyue/isgetTime**
   - 功能: 获取特定日期的可用时间段
   - 参数: 日期和产品ID
   - 返回: 时间段列表及其可用状态

## 优化建议

1. 可添加日期范围限制，防止预约太远的未来日期
2. 考虑添加时间段容量显示，便于用户了解热门时段
3. 增加预约确认提醒，减少误操作
4. 优化先支付后预约模式的用户引导，明确说明后续预约步骤 