<template>
<view class="page">
	<!-- 第一部分：健康报告头部 -->
	<view class="section_49">
		<view class="group_112">
			<image class="thumbnail_12" :src="pre_url+'/static/img/report-back-arrow.png'" @tap="goBack"/>
			<text class="text_1">健康报告</text>
		</view>
		<view class="text-wrapper_105">
			<text class="text_2">舌诊报告</text>
			<text class="text_3">面诊报告</text>
		</view>
		<view class="box_3">
			<view class="image-text_44">
				<image class="thumbnail_1" :src="pre_url+'/static/img/report-warning-icon.png'"/>
				<text class="text-group_1">拍摄时，角度，光线与距离会影响诊断结果。</text>
			</view>
			<view class="box_4">
				<view class="group_113">
					<view class="block_58">
						<text class="paragraph_1">正<br/>常</text>
						<image class="image_1" :src="pre_url+'/static/img/report-tongue-normal.png'"/>
					</view>
					<text class="text_4">主题征：正常</text>
					<text class="text_5">副题征：正常</text>
				</view>
				<view class="group_3">
					<view class="text-group_67">
						<text class="text_6">90</text>
						<text class="text_7">健康得分</text>
					</view>
				</view>
			</view>
		</view>
		<view class="group_114">
			<text class="text_8">你可能有以下体征</text>
			<view class="image-text_45">
				<text class="text-group_3">点击症状解决</text>
				<view class="section_1"></view>
			</view>
		</view>
		<view class="group_115">
			<view class="text-wrapper_2">
				<text class="text_9">头身困重</text>
			</view>
			<view class="text-wrapper_3">
				<text class="text_10">头身困重</text>
			</view>
			<view class="text-wrapper_4">
				<text class="text_11">头身困重</text>
			</view>
		</view>
		<view class="group_116">
			<view class="text-wrapper_5">
				<text class="text_12">头身困重</text>
			</view>
			<view class="text-wrapper_6">
				<text class="text_13">头身困重</text>
			</view>
			<view class="text-wrapper_7">
				<text class="text_14">头身困重</text>
			</view>
		</view>
	</view>

	<!-- 第二部分：调理套餐推荐 -->
	<view class="group_4">
		<text class="text_15">为你专属定制调理套餐</text>
		<view class="box_104">
			<view class="box_10"></view>
			<view class="group_117">
				<view class="text-wrapper_8">
					<text class="text_16">未已佳调理推荐</text>
				</view>
				<text class="paragraph_2">祛湿轻体缓解套餐<br/>保持身体健康</text>
				<view class="box_105">
					<view class="text-wrapper_9">
						<text class="text_17">美白消肿</text>
					</view>
					<view class="text-wrapper_10">
						<text class="text_18">美白消肿</text>
					</view>
					<view class="text-wrapper_11">
						<text class="text_19">美白消肿</text>
					</view>
				</view>
				<view class="text-wrapper_106">
					<text class="text_20">已抢564件</text>
					<text class="text-group_4">立即抢购</text>
				</view>
			</view>
		</view>
	</view>

	<!-- 第三部分：舌象分析 -->
	<view class="section_50">
		<text class="text_21">舌象分析</text>
		<view class="group_118">
			<view class="group_119">
				<view class="image-text_46">
					<image class="label_1" :src="pre_url+'/static/img/report-analysis-icon1.png'"/>
					<text class="text-group_5">舌象分析</text>
				</view>
				<image class="label_5" :src="pre_url+'/static/img/report-analysis-icon1.png'"/>
				<text class="text_22">舌象分析</text>
			</view>
			<view class="block_3">
				<view class="box_16">
					<view class="box_106">
						<view class="block_4"></view>
					</view>
					<view class="box_107">
						<view class="block_5"></view>
					</view>
					<view class="box_108">
						<view class="box_17"></view>
					</view>
					<view class="box_109">
						<view class="box_18"></view>
					</view>
				</view>
			</view>
			<view class="group_120">
				<view class="image-text_47">
					<image class="label_2" :src="pre_url+'/static/img/report-analysis-icon2.png'"/>
					<text class="text-group_6">舌象分析</text>
				</view>
				<view class="image-text_48">
					<image class="label_3" :src="pre_url+'/static/img/report-analysis-icon2.png'"/>
					<text class="text-group_7">舌象分析</text>
				</view>
			</view>
		</view>
		<view class="group_121">
			<view class="image-wrapper_1">
				<image class="thumbnail_2" :src="pre_url+'/static/img/report-dot-icon.png'"/>
			</view>
			<text class="text_23">舌象分析</text>
		</view>
		<view class="text-wrapper_13">
			<text class="text_24">你有</text>
			<text class="text_25">4</text>
			<text class="text_26">项表征异常</text>
		</view>
		<view class="group_122">
			<view class="image-wrapper_2">
				<image class="thumbnail_3" :src="pre_url+'/static/img/report-dot-icon.png'"/>
			</view>
			<view class="text-wrapper_14">
				<text class="text_27">舌色淡白：</text>
				<text class="text_28">正常的舌色是淡红，出现淡</text>
			</view>
		</view>
		<text class="paragraph_3">白色说明您阳气不足，血液运行动力不足，血液不能充盈舌质，常见气血两亏或虚寒之证。</text>
		<view class="group_123">
			<view class="image-wrapper_3">
				<image class="thumbnail_4" :src="pre_url+'/static/img/report-dot-icon.png'"/>
			</view>
			<view class="text-wrapper_15">
				<text class="text_29">舌苔黄色：</text>
				<text class="text_30">正常的苔色是白苔，出现黄</text>
			</view>
		</view>
		<text class="text_31">苔说明体内热邪入侵或体内阳虚，且颜色越黄越严重。</text>
		<view class="group_124">
			<view class="image-wrapper_4">
				<image class="thumbnail_5" :src="pre_url+'/static/img/report-dot-icon.png'"/>
			</view>
			<view class="text-wrapper_16">
				<text class="text_32">齿痕:</text>
				<text class="text_33">正常是没有齿痕的，出现齿痕说</text>
			</view>
		</view>
		<text class="text_34">体内脾虚不能运化水湿，舌体胖大而受齿缘压迫出现齿痕。</text>
		<view class="group_125">
			<view class="image-wrapper_5">
				<image class="thumbnail_6" :src="pre_url+'/static/img/report-dot-icon.png'"/>
			</view>
			<view class="text-wrapper_17">
				<text class="text_35">厚苔:</text>
				<text class="text_36">正常苔质是薄苔，出现厚苔说明</text>
			</view>
		</view>
		<view class="text-group_68">
			<text class="text_37">胃气夹湿浊，痰浊，食积等邪气熏蒸舌面。</text>
			<text class="text_38">历史舌象</text>
		</view>

		<!-- 历史舌象记录 -->
		<view class="group_126">
			<view class="section_9">
				<view class="text-wrapper_18">
					<text class="text_39">未解锁</text>
				</view>
				<view class="text-wrapper_19">
					<text class="text_40">81</text>
					<text class="text_41">分</text>
				</view>
				<text class="text_42">水湿，内热</text>
				<text class="text_43">2025-04-02 15:02:11</text>
			</view>
			<view class="section_10">
				<view class="text-wrapper_20">
					<text class="text_44">未解锁</text>
				</view>
				<view class="text-wrapper_21">
					<text class="text_45">81</text>
					<text class="text_46">分</text>
				</view>
				<text class="text_47">水湿，内热</text>
				<text class="text_48">2025-04-02 15:02:11</text>
			</view>
		</view>

		<!-- 舌象体征论述 -->
		<image class="image_2" :src="pre_url+'/static/img/report-divider.png'"/>
		<text class="text_49">舌象体征论述</text>
		<view class="text-wrapper_107">
			<text class="text_50">水湿：</text>
			<text class="text_51">胃气夹湿浊，痰浊，食积等邪气熏蒸舌面</text>
		</view>
		<text class="text_52">胃气夹湿浊，痰浊，食积等邪气熏蒸舌面蒸</text>
		<view class="text-wrapper_23">
			<text class="text_53">胃气夹湿浊，痰浊，食积等邪气熏蒸舌面蒸舌面胃气夹湿浊，痰浊，食积等邪气熏蒸舌面蒸舌面邪气熏蒸舌面蒸舌邪气熏蒸</text>
		</view>

		<!-- 患病风险 -->
		<text class="text_54">患病风险</text>
		<view class="grid_4">
			<view class="image-text_49-0">
				<view class="section_39-0">
					<text class="text_55-0">50%</text>
				</view>
				<text class="text_58-0">肥胖</text>
			</view>
			<view class="image-text_49-1">
				<view class="section_39-1">
					<text class="text_55-1">50%</text>
				</view>
				<text class="text_58-1">肥胖</text>
			</view>
			<view class="image-text_49-2">
				<view class="section_39-2">
					<text class="text_55-2">50%</text>
				</view>
				<text class="text_58-2">肥胖</text>
			</view>
			<view class="image-text_49-3">
				<view class="section_39-3">
					<text class="text_55-3">50%</text>
				</view>
				<text class="text_58-3">肥胖</text>
			</view>
			<view class="image-text_49-4">
				<view class="section_39-4">
					<view class="text-wrapper_29-4">
						<text class="text_62-4">50%</text>
					</view>
				</view>
				<text class="text_58-4">肥胖</text>
			</view>
			<view class="image-text_49-5">
				<view class="section_39-5">
					<text class="text_55-5">50%</text>
				</view>
				<text class="text_58-5">肥胖</text>
			</view>
		</view>

		<!-- 需要警惕 -->
		<text class="text_67">需要警惕</text>
		<view class="group_127">
			<view class="block_7"></view>
			<text class="paragraph_4">肥胖：水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内</text>
		</view>
		<view class="group_128">
			<view class="group_13"></view>
			<text class="paragraph_5">肥胖：水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内</text>
		</view>
		<view class="group_129">
			<view class="block_8"></view>
			<text class="paragraph_6">肥胖：水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内</text>
		</view>
		<view class="group_130">
			<view class="group_14"></view>
			<text class="paragraph_7">肥胖：水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内</text>
		</view>
		<text class="text_68">注 ：以上概率为该体质人群的占比情况，仅供参考不代表你个人的身体情况</text>

		<!-- 健康天平 -->
		<text class="text_69">健康天平</text>
		<view class="group_131">
			<view class="text-wrapper_30">
				<text class="text_70">寒</text>
			</view>
			<view class="text-wrapper_31">
				<text class="text_71">虚</text>
			</view>
			<view class="text-wrapper_32">
				<text class="text_72">热</text>
			</view>
		</view>
		<text class="text_73">湿</text>
		<view class="group_132">
			<view class="group_15"></view>
			<text class="text_74">伴随痰湿</text>
		</view>
		<view class="section_21">
			<view class="block_9"></view>
			<text class="text_75">胃气夹湿浊，痰浊，食积等邪气熏蒸舌面蒸舌面胃气夹湿浊，痰浊，食积等邪气熏蒸舌面蒸舌面邪气熏蒸舌面蒸舌邪气熏蒸舌面蒸舌邪气熏蒸舌面蒸舌邪气熏蒸舌面蒸舌邪</text>
		</view>

		<!-- 调理原则 -->
		<text class="text_76">调理原则</text>
		<view class="group_133">
			<view class="group_16"></view>
			<text class="paragraph_8">肥胖：水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内</text>
		</view>
		<view class="group_134">
			<view class="group_17"></view>
			<text class="paragraph_9">肥胖：水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内水水湿群体内的水液异常停滞，导<br/>致体内</text>
		</view>

		<!-- 今日营养目标 -->
		<view class="group_135">
			<text class="text_77">今日营养目标</text>
			<view class="text-wrapper_33">
				<text class="text_78">会员专享</text>
			</view>
		</view>
		<view class="group_136">
			<image class="image_3" :src="pre_url+'/static/img/report-nutrition-chart.png'"/>
			<view class="group_137">
				<view class="group_21"></view>
				<view class="group_22"></view>
				<view class="group_23"></view>
			</view>
			<view class="text-group_69">
				<text class="text_79">碳水化物</text>
				<text class="text_80">蛋白质</text>
				<text class="text_81">脂肪</text>
			</view>
			<view class="text-group_70">
				<text class="text_82">62%</text>
				<text class="text_83">10%</text>
				<text class="text_84">28%</text>
			</view>
			<view class="text-group_71">
				<text class="text_85">356克</text>
				<text class="text_86">26克</text>
				<text class="text_87">77克</text>
			</view>
			<view class="text-group_72">
				<text class="text_88">2848</text>
				<text class="text_89">千卡</text>
			</view>
		</view>

		<!-- 今日专属膳方 -->
		<view class="group_138">
			<text class="text_90">今日专属膳方</text>
			<view class="image-text_50">
				<text class="text-group_13">调整膳方计划</text>
				<image class="thumbnail_7" :src="pre_url+'/static/img/report-arrow-right.png'"/>
			</view>
		</view>
		<view class="group_139">
			<view class="section_51">
				<view class="text-group_73">
					<text class="text_91">黄酒煮鸡</text>
					<text class="text_92">温中养血，驱寒通络</text>
				</view>
				<view class="group_140">
					<image class="thumbnail_13" :src="pre_url+'/static/img/report-dish-icon.png'"/>
					<text class="text_93">换道菜</text>
				</view>
			</view>
		</view>
	</view>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			pre_url: '',
			tongueImageUrl: '',
			reportData: null
		};
	},
	
	onLoad: function(opt) {
		this.pre_url = app.globalData.pre_url;
		this.tongueImageUrl = opt.imageUrl || '';
		
		if (opt.data) {
			try {
				this.reportData = JSON.parse(decodeURIComponent(opt.data));
			} catch (e) {
				console.error('解析报告数据失败:', e);
			}
		}
		
		uni.setNavigationBarTitle({
			title: '舌诊报告'
		});
	},
	
	methods: {
		// 返回
		goBack: function() {
			uni.navigateBack();
		},
		
		// 保存报告
		saveReport: function() {
			uni.showToast({
				title: '报告已保存',
				icon: 'success'
			});
		},
		
		// 分享报告
		shareReport: function() {
			uni.showToast({
				title: '分享功能开发中',
				icon: 'none'
			});
		}
	}
};
</script>

<style>
.page {
	background-color: rgba(255, 255, 255, 1);
	position: relative;
	width: 750rpx;
	min-height: 100vh;
	overflow: hidden;
}

.section_49 {
	width: 750rpx;
	height: 1074rpx;
}

.group_112 {
	width: 418rpx;
	height: 44rpx;
	margin: 192rpx 0 0 36rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.thumbnail_12 {
	width: 18rpx;
	height: 32rpx;
	margin-top: 6rpx;
}

.text_1 {
	width: 144rpx;
	height: 44rpx;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 36rpx;
	font-family: Inter-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 36rpx;
}

.text-wrapper_105 {
	width: 540rpx;
	height: 50rpx;
	margin: 48rpx 0 0 110rpx;
	display: flex;
	justify-content: space-between;
}

.text_2 {
	width: 160rpx;
	height: 50rpx;
	overflow-wrap: break-word;
	color: rgba(43, 193, 117, 1);
	font-size: 40rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 40rpx;
}

.text_3 {
	width: 160rpx;
	height: 50rpx;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 40rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 40rpx;
}

.box_3 {
	background-color: rgba(222, 255, 238, 1);
	border-radius: 16rpx;
	position: relative;
	width: 690rpx;
	height: 314rpx;
	margin: 94rpx 0 0 36rpx;
}

.image-text_44 {
	width: 456rpx;
	height: 28rpx;
	margin: 278rpx 0 0 46rpx;
	display: flex;
	justify-content: space-between;
}

.thumbnail_1 {
	width: 22rpx;
	height: 28rpx;
}

.text-group_1 {
	width: 408rpx;
	height: 26rpx;
	overflow-wrap: break-word;
	color: rgba(43, 193, 117, 1);
	font-size: 20rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 20rpx;
	margin-top: 2rpx;
}

.box_4 {
	background-image: linear-gradient(137deg, rgba(68, 207, 137, 1) 21.723096%, rgba(7, 171, 88, 1) 100%);
	border-radius: 16rpx;
	position: absolute;
	left: 0;
	top: -42rpx;
	width: 690rpx;
	height: 314rpx;
	display: flex;
}

.group_113 {
	width: 254rpx;
	height: 262rpx;
	margin: 18rpx 0 0 82rpx;
}

.block_58 {
	width: 254rpx;
	height: 196rpx;
	display: flex;
}

.paragraph_1 {
	width: 64rpx;
	height: 164rpx;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 64rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	margin-top: 16rpx;
}

.image_1 {
	width: 196rpx;
	height: 196rpx;
	margin-left: -6rpx;
}

.text_4 {
	width: 220rpx;
	height: 46rpx;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 36rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 36rpx;
	margin: -24rpx 0 0 2rpx;
}

.text_5 {
	width: 220rpx;
	height: 46rpx;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 36rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 36rpx;
	margin: -2rpx 0 0 2rpx;
}

.group_3 {
	background-color: rgba(255, 255, 255, 1);
	width: 218rpx;
	height: 218rpx;
	border: 12rpx solid rgba(255, 255, 255, 1);
	border-radius: 50%;
	margin: 48rpx 58rpx 0 78rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text-group_67 {
	position: relative;
	width: 112rpx;
	height: 126rpx;
	text-align: center;
}

.text_6 {
	width: 108rpx;
	height: 96rpx;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 80rpx;
	font-family: Inter-Bold;
	font-weight: 700;
	text-align: center;
	white-space: nowrap;
	line-height: 80rpx;
}

.text_7 {
	width: 112rpx;
	height: 30rpx;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: center;
	white-space: nowrap;
	line-height: 24rpx;
}

.group_114 {
	width: 678rpx;
	height: 50rpx;
	margin: 48rpx 0 0 36rpx;
	display: flex;
	justify-content: space-between;
}

.text_8 {
	width: 320rpx;
	height: 50rpx;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
}

.image-text_45 {
	width: 284rpx;
	height: 50rpx;
	display: flex;
	justify-content: space-between;
}

.text-group_3 {
	width: 240rpx;
	height: 40rpx;
	overflow-wrap: break-word;
	color: rgba(43, 193, 117, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.section_1 {
	width: 20rpx;
	height: 20rpx;
	background-color: rgba(43, 193, 117, 1);
	margin-top: 15rpx;
}

.group_115 {
	width: 678rpx;
	height: 80rpx;
	margin: 32rpx 0 0 36rpx;
	display: flex;
	justify-content: space-between;
}

.text-wrapper_2, .text-wrapper_3, .text-wrapper_4 {
	background-color: rgba(43, 193, 117, 1);
	border-radius: 40rpx;
	height: 80rpx;
	width: 200rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text_9, .text_10, .text_11 {
	color: rgba(255, 255, 255, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: center;
	white-space: nowrap;
	line-height: 28rpx;
}

.group_116 {
	width: 678rpx;
	height: 80rpx;
	margin: 24rpx 0 0 36rpx;
	display: flex;
	justify-content: space-between;
}

.text-wrapper_5, .text-wrapper_6, .text-wrapper_7 {
	background-color: rgba(43, 193, 117, 1);
	border-radius: 40rpx;
	height: 80rpx;
	width: 200rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text_12, .text_13, .text_14 {
	color: rgba(255, 255, 255, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: center;
	white-space: nowrap;
	line-height: 28rpx;
}

.group_4 {
	width: 750rpx;
	padding: 48rpx 36rpx;
}

.text_15 {
	width: 400rpx;
	height: 46rpx;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
	margin-bottom: 32rpx;
}

.box_104 {
	width: 678rpx;
	height: 240rpx;
	display: flex;
	justify-content: space-between;
}

.box_10 {
	background-color: rgba(43, 193, 117, 1);
	width: 200rpx;
	height: 240rpx;
	border-radius: 16rpx;
}

.group_117 {
	width: 450rpx;
	height: 240rpx;
	padding: 24rpx;
	background-color: rgba(248, 249, 250, 1);
	border-radius: 16rpx;
}

.text-wrapper_8 {
	background-color: rgba(43, 193, 117, 1);
	border-radius: 20rpx;
	height: 40rpx;
	width: 240rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;
}

.text_16 {
	color: rgba(255, 255, 255, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	line-height: 24rpx;
}

.paragraph_2 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	line-height: 1.4;
	margin-bottom: 16rpx;
}

.box_105 {
	width: 402rpx;
	height: 40rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.text-wrapper_9, .text-wrapper_10, .text-wrapper_11 {
	background-color: rgba(43, 193, 117, 1);
	border-radius: 20rpx;
	height: 40rpx;
	width: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text_17, .text_18, .text_19 {
	color: rgba(255, 255, 255, 1);
	font-size: 20rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	line-height: 20rpx;
}

.text-wrapper_106 {
	width: 402rpx;
	height: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.text_20 {
	color: rgba(0, 0, 0, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 24rpx;
}

.text-group_4 {
	color: rgba(43, 193, 117, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: right;
	white-space: nowrap;
	line-height: 28rpx;
}

/* 第三部分：舌象分析 */
.section_50 {
	width: 750rpx;
	padding: 48rpx 36rpx;
}

.text_21 {
	width: 160rpx;
	height: 50rpx;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
	margin-bottom: 32rpx;
}

.group_118 {
	width: 678rpx;
	height: 200rpx;
	display: flex;
	margin-bottom: 32rpx;
}

.group_119 {
	width: 200rpx;
	height: 200rpx;
	display: flex;
	flex-direction: column;
}

.image-text_46 {
	width: 200rpx;
	height: 80rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.label_1, .label_5 {
	width: 60rpx;
	height: 60rpx;
}

.text-group_5, .text_22 {
	color: rgba(0, 0, 0, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	line-height: 24rpx;
}

.block_3 {
	width: 200rpx;
	height: 200rpx;
	display: flex;
	flex-direction: column;
	margin: 0 39rpx;
}

.box_16 {
	width: 200rpx;
	height: 200rpx;
	display: flex;
	flex-direction: column;
}

.box_106, .box_107, .box_108, .box_109 {
	width: 200rpx;
	height: 50rpx;
	display: flex;
}

.block_4, .block_5, .box_17, .box_18 {
	width: 50rpx;
	height: 50rpx;
	background-color: rgba(43, 193, 117, 1);
	margin-right: 10rpx;
}

.group_120 {
	width: 200rpx;
	height: 200rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.image-text_47, .image-text_48 {
	width: 200rpx;
	height: 80rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.label_2, .label_3 {
	width: 60rpx;
	height: 60rpx;
}

.text-group_6, .text-group_7 {
	color: rgba(0, 0, 0, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	line-height: 24rpx;
}

.group_121 {
	width: 678rpx;
	height: 40rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.image-wrapper_1 {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.thumbnail_2 {
	width: 20rpx;
	height: 20rpx;
}

.text_23 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.text-wrapper_13 {
	width: 678rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.text_24, .text_26 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.text_25 {
	color: rgba(43, 193, 117, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
	margin: 0 8rpx;
}

.group_122, .group_123, .group_124, .group_125 {
	width: 678rpx;
	height: 40rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.image-wrapper_2, .image-wrapper_3, .image-wrapper_4, .image-wrapper_5 {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.thumbnail_3, .thumbnail_4, .thumbnail_5, .thumbnail_6 {
	width: 20rpx;
	height: 20rpx;
}

.text-wrapper_14, .text-wrapper_15, .text-wrapper_16, .text-wrapper_17 {
	width: 600rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
}

.text_27, .text_29, .text_32, .text_35 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.text_28, .text_30, .text_33, .text_36 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.paragraph_3, .text_31, .text_34 {
	width: 678rpx;
	color: rgba(0, 0, 0, 1);
	font-size: 26rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	line-height: 1.5;
	margin-bottom: 24rpx;
}

.text-group_68 {
	width: 678rpx;
	height: 80rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.text_37 {
	color: rgba(0, 0, 0, 1);
	font-size: 26rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	line-height: 1.5;
}

.text_38 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
}

/* 历史舌象记录 */
.group_126 {
	width: 678rpx;
	height: 200rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.section_9, .section_10 {
	width: 320rpx;
	height: 200rpx;
	background-color: rgba(248, 249, 250, 1);
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	flex-direction: column;
}

.text-wrapper_18, .text-wrapper_20 {
	background-color: rgba(43, 193, 117, 1);
	border-radius: 20rpx;
	height: 40rpx;
	width: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;
}

.text_39, .text_44 {
	color: rgba(255, 255, 255, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	line-height: 24rpx;
}

.text-wrapper_19, .text-wrapper_21 {
	width: 272rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.text_40, .text_45 {
	color: rgba(43, 193, 117, 1);
	font-size: 36rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 36rpx;
}

.text_41, .text_46 {
	color: rgba(0, 0, 0, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 24rpx;
	margin-left: 8rpx;
}

.text_42, .text_47 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
	margin-bottom: 8rpx;
}

.text_43, .text_48 {
	color: rgba(153, 153, 153, 1);
	font-size: 20rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 20rpx;
}

/* 舌象体征论述 */
.image_2 {
	width: 678rpx;
	height: 40rpx;
	margin: 32rpx 0;
}

.text_49 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
	margin-bottom: 24rpx;
}

.text-wrapper_107 {
	width: 678rpx;
	height: 40rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.text_50 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.text_51 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.text_52 {
	color: rgba(0, 0, 0, 1);
	font-size: 26rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	line-height: 1.5;
	margin-bottom: 16rpx;
}

.text-wrapper_23 {
	width: 678rpx;
	margin-bottom: 32rpx;
}

.text_53 {
	color: rgba(0, 0, 0, 1);
	font-size: 26rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	line-height: 1.5;
}

/* 患病风险 */
.text_54 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
	margin-bottom: 24rpx;
}

.grid_4 {
	width: 678rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-bottom: 32rpx;
}

.image-text_49-0, .image-text_49-1, .image-text_49-2, .image-text_49-3, .image-text_49-4, .image-text_49-5 {
	width: 210rpx;
	height: 120rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	background-color: rgba(248, 249, 250, 1);
	border-radius: 12rpx;
	padding: 16rpx;
}

.section_39-0, .section_39-1, .section_39-2, .section_39-3, .section_39-4, .section_39-5 {
	width: 100%;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text_55-0, .text_55-1, .text_55-2, .text_55-3, .text_55-5 {
	color: rgba(43, 193, 117, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: center;
	white-space: nowrap;
	line-height: 32rpx;
}

.text-wrapper_29-4 {
	width: 100%;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text_62-4 {
	color: rgba(43, 193, 117, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: center;
	white-space: nowrap;
	line-height: 32rpx;
}

.text_58-0, .text_58-1, .text_58-2, .text_58-3, .text_58-4, .text_58-5 {
	color: rgba(0, 0, 0, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	line-height: 24rpx;
}

/* 需要警惕 */
.text_67 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
	margin-bottom: 24rpx;
}

.group_127, .group_128, .group_129, .group_130 {
	width: 678rpx;
	height: 120rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.block_7, .block_8 {
	width: 120rpx;
	height: 120rpx;
	background-color: rgba(43, 193, 117, 1);
	border-radius: 12rpx;
}

.group_13, .group_14 {
	width: 120rpx;
	height: 120rpx;
	background-color: rgba(248, 249, 250, 1);
	border-radius: 12rpx;
}

.paragraph_4, .paragraph_5, .paragraph_6, .paragraph_7 {
	width: 520rpx;
	color: rgba(0, 0, 0, 1);
	font-size: 26rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	line-height: 1.5;
}

.text_68 {
	color: rgba(153, 153, 153, 1);
	font-size: 20rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	line-height: 1.4;
	margin-top: 32rpx;
}

/* 健康天平 */
.text_69 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
	margin: 48rpx 0 24rpx 0;
}

.group_131 {
	width: 678rpx;
	height: 80rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.text-wrapper_30, .text-wrapper_31, .text-wrapper_32 {
	background-color: rgba(43, 193, 117, 1);
	border-radius: 40rpx;
	height: 80rpx;
	width: 200rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text_70, .text_71, .text_72 {
	color: rgba(255, 255, 255, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: center;
	white-space: nowrap;
	line-height: 28rpx;
}

.text_73 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
	margin: 24rpx 0;
}

.group_132 {
	width: 678rpx;
	height: 80rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.group_15 {
	width: 120rpx;
	height: 80rpx;
	background-color: rgba(43, 193, 117, 1);
	border-radius: 12rpx;
}

.text_74 {
	color: rgba(0, 0, 0, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.section_21 {
	width: 678rpx;
	height: 120rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.block_9 {
	width: 120rpx;
	height: 120rpx;
	background-color: rgba(43, 193, 117, 1);
	border-radius: 12rpx;
}

.text_75 {
	width: 520rpx;
	color: rgba(0, 0, 0, 1);
	font-size: 26rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	line-height: 1.5;
}

/* 调理原则 */
.text_76 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
	margin: 32rpx 0 24rpx 0;
}

.group_133, .group_134 {
	width: 678rpx;
	height: 120rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.group_16, .group_17 {
	width: 120rpx;
	height: 120rpx;
	background-color: rgba(43, 193, 117, 1);
	border-radius: 12rpx;
}

.paragraph_8, .paragraph_9 {
	width: 520rpx;
	color: rgba(0, 0, 0, 1);
	font-size: 26rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	line-height: 1.5;
}

/* 今日营养目标 */
.group_135 {
	width: 678rpx;
	height: 50rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 32rpx 0 24rpx 0;
}

.text_77 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
}

.text-wrapper_33 {
	background-color: rgba(43, 193, 117, 1);
	border-radius: 25rpx;
	height: 50rpx;
	width: 160rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text_78 {
	color: rgba(255, 255, 255, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	line-height: 24rpx;
}

.group_136 {
	width: 678rpx;
	height: 200rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.image_3 {
	width: 160rpx;
	height: 160rpx;
}

.group_137 {
	width: 40rpx;
	height: 160rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.group_21, .group_22, .group_23 {
	width: 40rpx;
	height: 40rpx;
	background-color: rgba(43, 193, 117, 1);
	border-radius: 50%;
}

.text-group_69, .text-group_70, .text-group_71 {
	width: 120rpx;
	height: 160rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.text_79, .text_80, .text_81 {
	color: rgba(0, 0, 0, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 24rpx;
}

.text_82, .text_83, .text_84 {
	color: rgba(43, 193, 117, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.text_85, .text_86, .text_87 {
	color: rgba(0, 0, 0, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 24rpx;
}

.text-group_72 {
	width: 80rpx;
	height: 160rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.text_88 {
	color: rgba(43, 193, 117, 1);
	font-size: 36rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: center;
	white-space: nowrap;
	line-height: 36rpx;
}

.text_89 {
	color: rgba(0, 0, 0, 1);
	font-size: 20rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	line-height: 20rpx;
}

/* 今日专属膳方 */
.group_138 {
	width: 678rpx;
	height: 50rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 32rpx 0 24rpx 0;
}

.text_90 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
}

.image-text_50 {
	width: 240rpx;
	height: 50rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.text-group_13 {
	color: rgba(43, 193, 117, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 28rpx;
}

.thumbnail_7 {
	width: 20rpx;
	height: 20rpx;
}

.group_139 {
	width: 678rpx;
	height: 200rpx;
	margin-bottom: 48rpx;
}

.section_51 {
	width: 678rpx;
	height: 200rpx;
	background-color: rgba(248, 249, 250, 1);
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.text-group_73 {
	width: 630rpx;
	height: 80rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.text_91 {
	color: rgba(0, 0, 0, 1);
	font-size: 32rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 32rpx;
}

.text_92 {
	color: rgba(153, 153, 153, 1);
	font-size: 24rpx;
	font-family: Microsoft YaHei UI-Regular;
	font-weight: 400;
	text-align: left;
	white-space: nowrap;
	line-height: 24rpx;
}

.group_140 {
	width: 630rpx;
	height: 50rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.thumbnail_13 {
	width: 40rpx;
	height: 40rpx;
}

.text_93 {
	color: rgba(43, 193, 117, 1);
	font-size: 28rpx;
	font-family: Microsoft YaHei UI-Bold;
	font-weight: 700;
	text-align: right;
	white-space: nowrap;
	line-height: 28rpx;
}
</style>

/* 舌诊对比区域 */
.comparison-section {
	padding: 48rpx 36rpx 0 36rpx;
}

.comparison-header {
	margin-bottom: 32rpx;
}

.comparison-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #000000;
}

.tongue-comparison {
	display: flex;
	justify-content: space-between;
	gap: 32rpx;
}

.tongue-item {
	flex: 1;
	text-align: center;
}

.tongue-label {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 16rpx;
	display: block;
}

.tongue-image {
	width: 100%;
	height: 200rpx;
	border-radius: 12rpx;
	object-fit: cover;
}

/* 详细分析区域 */
.analysis-section {
	padding: 48rpx 36rpx 0 36rpx;
}

.analysis-header {
	margin-bottom: 32rpx;
}

.analysis-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #000000;
}

.analysis-items {
	display: flex;
	flex-direction: column;
	gap: 32rpx;
}

.analysis-item {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
}

.item-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.item-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.item-status {
	font-size: 24rpx;
	font-weight: 500;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.item-status.normal {
	background-color: #e8f5e8;
	color: #2bc175;
}

.item-status.warning {
	background-color: #fff3cd;
	color: #856404;
}

.item-status.danger {
	background-color: #f8d7da;
	color: #721c24;
}

.item-description {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.5;
}

/* 健康建议区域 */
.suggestions-section {
	padding: 48rpx 36rpx 0 36rpx;
}

.suggestions-header {
	margin-bottom: 32rpx;
}

.suggestions-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #000000;
}

.suggestions-content {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.suggestion-item {
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
}

.suggestion-number {
	width: 40rpx;
	height: 40rpx;
	background-color: #2bc175;
	color: #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
	flex-shrink: 0;
}

.suggestion-text {
	flex: 1;
	font-size: 26rpx;
	color: #666666;
	line-height: 1.5;
	margin-top: 8rpx;
}

/* 底部操作按钮 */
.action-section {
	padding: 48rpx 36rpx 80rpx 36rpx;
	display: flex;
	gap: 24rpx;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.primary {
	background-color: #2bc175;
}

.action-btn.secondary {
	border: 2rpx solid #2bc175;
	background-color: transparent;
}

.btn-text {
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary .btn-text {
	color: #ffffff;
}

.action-btn.secondary .btn-text {
	color: #2bc175;
}
</style>
