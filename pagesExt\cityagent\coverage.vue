<template>
<view class="container">
	<view class="header">
		<text class="title">区域管理</text>
		<text class="subtitle">管理您的代理覆盖区域</text>
	</view>
	
	<view class="content">
		<!-- 区域统计 -->
		<view class="stats-container">
			<view class="stats-item">
				<text class="stats-number">{{coverage_stats.province_count}}</text>
				<text class="stats-label">覆盖省份</text>
			</view>
			<view class="stats-item">
				<text class="stats-number">{{coverage_stats.city_count}}</text>
				<text class="stats-label">覆盖城市</text>
			</view>
			<view class="stats-item">
				<text class="stats-number">{{coverage_stats.district_count}}</text>
				<text class="stats-label">覆盖区县</text>
			</view>
			<view class="stats-item">
				<text class="stats-number">{{coverage_stats.orders_count}}</text>
				<text class="stats-label">区域订单</text>
			</view>
		</view>
		
		<!-- 省份列表 -->
		<view class="region-container" v-if="provinces.length > 0">
			<view class="region-header">
				<image src="/static/img/icon-province.png" class="region-icon"></image>
				<text class="region-title">覆盖省份</text>
			</view>
			
			<view class="province-list">
				<view class="province-item" 
					v-for="(province, index) in provinces" 
					:key="province.id"
					@tap="toggleProvince(index)">
					<view class="province-info">
						<text class="province-name">{{province.name}}</text>
						<text class="province-stats">{{province.city_count}}个城市 | {{province.orders}}笔订单</text>
					</view>
					<image :src="province.expanded ? '/static/img/arrow-up.png' : '/static/img/arrow-down.png'" 
						class="arrow-icon"></image>
				</view>
				
				<!-- 城市列表 -->
				<view class="city-container" v-if="provinces[expandedProvinceIndex] && provinces[expandedProvinceIndex].expanded">
					<view class="city-list">
						<view class="city-item" 
							v-for="city in provinces[expandedProvinceIndex].cities" 
							:key="city.id"
							@tap="viewCityDetail(city)">
							<view class="city-info">
								<text class="city-name">{{city.name}}</text>
								<text class="city-stats">{{city.district_count}}个区县 | {{city.orders}}笔订单</text>
							</view>
							<view class="city-revenue">
								<text class="revenue-amount">￥{{city.revenue}}</text>
								<text class="revenue-label">收益</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 直辖区县列表 -->
		<view class="region-container" v-if="districts.length > 0">
			<view class="region-header">
				<image src="/static/img/icon-district.png" class="region-icon"></image>
				<text class="region-title">直辖区县</text>
			</view>
			
			<view class="district-list">
				<view class="district-item" 
					v-for="district in districts" 
					:key="district.id"
					@tap="viewDistrictDetail(district)">
					<view class="district-info">
						<text class="district-name">{{district.name}}</text>
						<text class="district-stats">{{district.streets}}个街道 | {{district.orders}}笔订单</text>
					</view>
					<view class="district-revenue">
						<text class="revenue-amount">￥{{district.revenue}}</text>
						<text class="revenue-label">收益</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 申请扩展区域 -->
		<!-- <view class="action-container">
			<button class="action-btn primary" @tap="applyExpansion">
				<image src="/static/img/icon-expand.png" class="btn-icon"></image>
				<text>申请扩展区域</text>
			</button>
			
			<button class="action-btn secondary" @tap="viewApplications">
				<image src="/static/img/icon-application.png" class="btn-icon"></image>
				<text>查看申请记录</text>
			</button>
		</view> -->
		
		<!-- 区域业绩分析 -->
		<view class="analysis-container">
			<view class="analysis-header">
				<image src="/static/img/icon-analysis.png" class="analysis-icon"></image>
				<text class="analysis-title">区域业绩分析</text>
			</view>
			
			<view class="analysis-chart">
				<view class="chart-item" v-for="item in performance_data" :key="item.name">
					<view class="chart-bar">
						<view class="bar-fill" :style="{width: item.percentage + '%', background: getColor('color1') || '#4CAF50'}"></view>
					</view>
					<view class="chart-info">
						<text class="chart-name">{{item.name}}</text>
						<text class="chart-value">￥{{item.value}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="provinces.length === 0 && districts.length === 0">
			<image src="/static/img/empty-coverage.png" class="empty-icon"></image>
			<text class="empty-title">暂无覆盖区域</text>
			<text class="empty-desc">您还没有分配到任何代理区域</text>
			<button class="empty-btn" @tap="contactAdmin">联系管理员</button>
		</view>
	</view>
	
	<!-- 加载状态 -->
	<loading v-if="loading"></loading>
	
	<!-- 消息提示 -->
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			loading: false,
			provinces: [],
			districts: [],
			expandedProvinceIndex: -1,
			coverage_stats: {
				province_count: 0,
				city_count: 0,
				district_count: 0,
				orders_count: 0
			},
			performance_data: []
		};
	},
	
	onLoad() {
		this.loadCoverageData();
	},
	
	onPullDownRefresh() {
		this.loadCoverageData();
	},
	
	methods: {
		// 安全获取颜色值
		getColor: function(colorKey) {
			try {
				if (typeof this.t === 'function') {
					return this.t(colorKey);
				}
				return null;
			} catch (e) {
				console.log('获取颜色失败:', e);
				return null;
			}
		},
		
		// 加载覆盖区域数据
		loadCoverageData() {
			var that = this;
			that.loading = true;
			
			app.get('ApiCityAgent/getCoverageData', {}, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status === 1) {
					that.provinces = res.provinces || [];
					that.districts = res.districts || [];
					that.coverage_stats = res.coverage_stats || {};
					that.performance_data = res.performance_data || [];
					
					// 设置导航标题
					uni.setNavigationBarTitle({
						title: '区域管理'
					});
				} else {
					app.error(res.msg);
				}
			});
		},
		
		// 切换省份展开状态
		toggleProvince(index) {
			if (this.expandedProvinceIndex === index) {
				this.expandedProvinceIndex = -1;
				this.provinces[index].expanded = false;
			} else {
				// 关闭之前展开的省份
				if (this.expandedProvinceIndex >= 0) {
					this.provinces[this.expandedProvinceIndex].expanded = false;
				}
				
				this.expandedProvinceIndex = index;
				this.provinces[index].expanded = true;
				
				// 如果还没有加载城市数据，则加载
				if (!this.provinces[index].cities) {
					this.loadCityData(this.provinces[index].id, index);
				}
			}
		},
		
		// 加载城市数据
		loadCityData(provinceId, index) {
			var that = this;
			
			app.get('ApiCityAgent/getCityData', {province_id: provinceId}, function(res) {
				if (res.status === 1) {
					that.$set(that.provinces[index], 'cities', res.cities);
				}
			});
		},
		
		// 查看城市详情
		viewCityDetail(city) {
			app.goto('/pagesExt/cityagent/city-detail?city_id=' + city.id);
		},
		
		// 查看区县详情
		viewDistrictDetail(district) {
			app.goto('/pagesExt/cityagent/district-detail?district_id=' + district.id);
		},
		
		// 申请扩展区域
		applyExpansion() {
			app.goto('/pagesExt/cityagent/apply-expansion');
		},
		
		// 查看申请记录
		viewApplications() {
			app.goto('/pagesExt/cityagent/expansion-applications');
		},
		
		// 联系管理员
		contactAdmin() {
			uni.makePhoneCall({
				phoneNumber: '************',
				success: function () {
					console.log('拨号成功');
				},
				fail: function () {
					app.error('拨号失败');
				}
			});
		}
	}
};
</script>

<style>
.container {
	background: #f8f8f8;
	min-height: 100vh;
}

.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 30rpx;
	color: white;
	text-align: center;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 28rpx;
	opacity: 0.9;
}

.content {
	padding: 30rpx;
}

/* 统计卡片样式 */
.stats-container {
	display: flex;
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
	flex: 1;
	text-align: center;
}

.stats-number {
	display: block;
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #666;
}

/* 区域容器样式 */
.region-container {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.region-header {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.region-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 15rpx;
}

.region-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 省份列表样式 */
.province-list {
	padding: 0 30rpx;
}

.province-item {
	display: flex;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.province-item:last-child {
	border-bottom: none;
}

.province-info {
	flex: 1;
}

.province-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.province-stats {
	font-size: 24rpx;
	color: #666;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
}

/* 城市容器样式 */
.city-container {
	background: #f8f8f8;
	margin: 0 -30rpx;
	padding: 20rpx 30rpx;
}

.city-list {
	background: white;
	border-radius: 15rpx;
	padding: 20rpx;
}

.city-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.city-item:last-child {
	border-bottom: none;
}

.city-info {
	flex: 1;
}

.city-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 6rpx;
}

.city-stats {
	font-size: 22rpx;
	color: #666;
}

.city-revenue {
	text-align: right;
}

.revenue-amount {
	font-size: 28rpx;
	font-weight: bold;
	color: #4CAF50;
	display: block;
	margin-bottom: 4rpx;
}

.revenue-label {
	font-size: 20rpx;
	color: #999;
}

/* 区县列表样式 */
.district-list {
	padding: 0 30rpx;
}

.district-item {
	display: flex;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.district-item:last-child {
	border-bottom: none;
}

.district-info {
	flex: 1;
}

.district-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 6rpx;
}

.district-stats {
	font-size: 22rpx;
	color: #666;
}

.district-revenue {
	text-align: right;
}

/* 操作按钮样式 */
.action-container {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	border: none;
}

.action-btn.primary {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.action-btn.secondary {
	background: white;
	color: #666;
	border: 2rpx solid #e0e0e0;
}

.btn-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 10rpx;
}

/* 业绩分析样式 */
.analysis-container {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.analysis-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.analysis-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 15rpx;
}

.analysis-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.analysis-chart {
	
}

.chart-item {
	margin-bottom: 25rpx;
}

.chart-bar {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	margin-bottom: 10rpx;
	position: relative;
}

.bar-fill {
	height: 100%;
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.chart-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.chart-name {
	font-size: 26rpx;
	color: #333;
}

.chart-value {
	font-size: 26rpx;
	font-weight: bold;
	color: #4CAF50;
}

/* 空状态样式 */
.empty-state {
	text-align: center;
	padding: 80rpx 40rpx;
	background: white;
	border-radius: 20rpx;
}

.empty-icon {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
}

.empty-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 30rpx;
}

.empty-btn {
	width: 300rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	font-size: 28rpx;
	border-radius: 30rpx;
	border: none;
}
</style> 