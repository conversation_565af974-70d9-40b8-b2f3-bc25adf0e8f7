// 数据格式,数据中只需要包含以下字段和数据格式,开发者可以添加字段,比如id等等,不影响组件显示,
// 组件的返回结果是有菜单数组下标形式返回,
// 如果传入数据中有value,也会返回value,开发者可根据返回的下标获取所选中的菜单
/*
[
	{
		"name":"",	//字符串类型 选填项 菜单名称,如不填,则取第一个子菜单的name值,filter和radio类型则将设置为"筛选"
		"type":""	//字符串类型 必填项 可取值 hierarchy/hierarchy-column/filter/radio  hierarchy/hierarchy-column单或多层级菜单(最多三级); filter筛选多选菜单; radio筛选单选菜单
		"submenu":[	//对象数组类型 必填项 子菜单数据
			{
				"name":"",	//字符串类型 必填项 菜单名称
				"value":"",	//字符串类型 选填项 自定义内容,比如id等等,如果填写了,confirm返回的结果中将返回对应选中的value,若菜单无value字段则返回null,filter类型此字段无效果
				"submenu":[	//对象数组类型 必填项 子菜单数据
					{
						"name":"",	//字符串类型 必填项 菜单名称
						"value":"",	//字符串类型 选填项 自定义内容,比如id等等,如果填写了,confirm返回的结果中将返回对应选中的value,若菜单无value字段则返回null
						"submenu":[	//对象数组类型 必填项 子菜单数据 filter类型无效 
							{
								"name":"",	//字符串类型 必填项 菜单名称 hierarchy类型层级最多到此
								"value":"",	//字符串类型 选填项 自定义内容,比如id等等,如果填写了,confirm返回的结果中将返回对应选中的value,若菜单无value字段则返回null
							}
						]
					}
				]
			}
		]
	}
]
*/

//0.0.4版本起 返回结果将有两部分组成:
/*
{
	index:[],	//选中菜单的下标数组
	value:[]	//菜单中的valve,结构和下标结果数组一样,只是把下标替换成了value而已
}
*/
// 以下演示数据中,我故意把value设置成跟name一样,只是为了方便演示,使示例更加易懂,实际使用时候value应该是一个标识,给后台识别所用的.
// 数据较长，请仔细查看。
import area from './area.js'

export default [{
		"name": '城市',
		"type": 'hierarchy-column',
		"submenu": [{
			"name": '全部城市',
			"value": "全部城市",
			"submenu": [{
					"name": "全部城市",
					"value": "全部城市"
				}
			]
		},
		...area.provinces,
		]
	},
	{
		"name": '类型',
		"type": 'radio',
		"submenu": [{
				"name": "学校性质",
				"submenu": [
					{
						"name": "全部",
						"value": "全部"
					},
					{
						"name": "公办",
						"value": "公办"
					},
					{
						"name": "民办",
						"value": "民办"
					}
				]
			},
			{
				"name": "学历层次",
				"submenu": [
					{
						"name": "全部",
						"value": "全部"
					},
					{
						"name": "普通本科",
						"value": "普通本科"
					},
					{
						"name": "高职（专科）",
						"value": "高职（专科）"
					},{
						"name": "中专（高中）",
						"value": "中专（高中）"
					}
				]
			},
			{
				"name": "招生类型",
				"submenu": [
					// {
					// 	"name": "全部",
					// 	"value": "全部"
					// },
					{
						"name": "分类单招",
						"value": "分类单招"
					},
					{
						"name": "统一高考",
						"value": "统一高考"
					},
					{
						"name": "专升本",
						"value": "专升本"
					},
					{
						"name": "中外合作办学",
						"value": "中外合作办学"
					}
				]
			},
			{
				"name": "学校类型",
				"submenu": []
			}
		]
	}, {
		"name": '排序',
		"type": 'radio',
		"submenu": [{
			"name": "",
			"submenu": [
				// {
				// 	"name": "综合排序",
				// 	"value": "综合排序"
				// },
				{
					"name": "离我最近",
					"value": "离我最近"
				},
				{
					"name": "热度最高",
					"value": "热度最高"
				}
			]
		}]
	}
]