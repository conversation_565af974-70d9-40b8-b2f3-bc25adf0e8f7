<template>
<view class="container" @click="checkTapOutside">
	<block v-if="isload">
		<view class="topsearch flex-y-center">
			<!-- 城市选择按钮移到左边 -->
			<view class="city-select" @tap="gotoCity" :class="{'active': currentCity}">
				<text>{{currentCity || '选择城市'}}</text>
				<text class="city-icon">▼</text>
			</view>
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="输入姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm" @input="searchChange"></input>
			</view>
			<!-- 添加筛选按钮 -->
			<view class="filter-btn" @tap.stop="toggleFilterOptions">
				<text class="filter-text">筛选</text>
				<text class="filter-icon">▼</text>
			</view>
		</view>
		
		<!-- 筛选选项浮层 -->
		<view class="filter-options" v-if="showFilterOptions" @click.stop>
			<view class="filter-overlay" @tap="toggleFilterOptions"></view>
			<view class="filter-panel" @tap.stop>
				<view class="filter-title">排序方式</view>
				<view class="filter-list">
					<view :class="'filter-item ' + (field == 'juli' ? 'active' : '')" 
						@tap="sortClick" 
						data-field="juli" 
						data-order="asc"
						:style="field=='juli' ? 'color:'+t('color1')+';border-color:'+t('color1') : ''">
						<text>按距离排序</text>
						<text class="check-icon" v-if="field == 'juli'">✓</text>
					</view>
					<view :class="'filter-item ' + (field == 'comment_score' ? 'active' : '')" 
						@tap="sortClick" 
						data-field="comment_score" 
						data-order="desc"
						:style="field=='comment_score' ? 'color:'+t('color1')+';border-color:'+t('color1') : ''">
						<text>按评分排序</text>
						<text class="check-icon" v-if="field == 'comment_score'">✓</text>
					</view>
				</view>
				<view class="filter-btn-group">
					<view class="filter-reset" @tap="resetFilter">重置</view>
					<view class="filter-confirm" @tap="confirmFilter" :style="{background:t('color1')}">确定</view>
				</view>
			</view>
		</view>
		
		<view class="order-tab">
			<view class="order-tab2">
				<view :class="'item ' + (curTopIndex == -1 ? 'on' : '')" @tap="switchTopTab" :data-index="-1" :data-id="0">全部<view class="after" :style="{background:t('color1')}"></view></view>
				<block v-for="(item, index) in clist" :key="index">
					<view :class="'item ' + (curTopIndex == index ? 'on' : '')" @tap="switchTopTab" :data-index="index" :data-id="item.id">{{item.name}}<view class="after" :style="{background:t('color1')}"></view></view>
				</block>
			</view>
		</view>
		
		<!-- 位置获取失败时显示重试按钮 -->
		<view class="location-failed flex-center" v-if="locationFailed">
			<view class="retry-btn" @tap="retryLocation" :style="{borderColor:t('color1'), color:t('color1')}">
				重新获取位置
			</view>
		</view>
	
		<view class="content-list">
			<view v-for="(item, index) in datalist" :key="index" class="content flex" :data-id="item.id">
				<view class="f1" @click="goto" :data-url="'peodetail?id='+item.id" >
					<view class="headimg"><image :src="item.headimg" mode="aspectFill" /></view>
					<view class="text1">	
						<view class="name-type">
							<text class="t1">{{item.realname}} </text>
							<text class="t2" v-if="item.typename" >{{item.typename}} </text>
						</view>
						<view class="text2">{{item.jineng || '暂无技能描述'}}</view>
						<view v-if="item.jineng && item.jineng.includes('、')" class="tech-tags">
							<text class="tech-tag" v-for="(tag, tagIndex) in item.jineng.split('、').slice(0,3)" :key="tagIndex">{{tag}}</text>
						</view>
						<view class="text3">
							<text class="t4">服务<text> {{item.totalnum}}</text> 次</text> 
							<text class="t5">评分 <text>{{item.comment_score}}</text></text>
							<text class="t6" v-if="item.distance && item.distance != '未知'">距离 <text>{{item.distance}}</text></text>
						</view>
					</view>	
				</view>
				<view>
					<view class="yuyue"  @click="goto" :data-url="'/yuyue/peodetail?id='+item.id" :style="{background:t('color1')}">预约</view>
				</view>
			</view>
			<view class="no-data-placeholder" v-if="datalist.length === 0 && !loading && nodata">
				<image src="/static/img/empty.png" mode="aspectFit" class="empty-img"></image>
				<view class="empty-text">暂无相关技师</view>
			</view>
		</view>
		<nodata v-if="nodata && datalist.length === 0 && !loading"></nodata>
		<nomore v-if="nomore"></nomore>
	</block>
	<loading v-if="loading"></loading>
	<!-- 添加骨架屏加载效果 -->
	<view class="content-list skeleton-list" v-if="loading && datalist.length === 0">
		<view class="content skeleton-card" v-for="i in 3" :key="i">
			<view class="f1">
				<view class="skeleton headimg-skeleton"></view>
				<view class="text1">
					<view class="skeleton title-skeleton"></view>
					<view class="skeleton text-skeleton"></view>
					<view class="skeleton text-skeleton short"></view>
					<view class="skeleton-tags">
						<view class="skeleton tag-skeleton" v-for="j in 2" :key="j"></view>
					</view>
				</view>
			</view>
			<view class="skeleton btn-skeleton"></view>
		</view>
	</view>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
	  opt:{},
	  loading:false,
      isload: false,
	  menuindex:-1,
      keyword: '',
      datalist: [],
      type: "",
	  nodata:false,
	  curTopIndex: -1,
	  index:0,
	  curCid:0,
	  nomore: false,
	  pagenum: 1,
	  longitude: '',
      latitude: '',
	  field: '',
      order: '',
	  locationFailed: false,
	  // 添加城市相关变量
	  currentCity: '',
	  currentCityId: 0,
	  provinceId: 0,
	  cityId: 0,
	  districtId: 0,
	  // 添加筛选面板控制变量
	  showFilterOptions: false
    }
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.type = this.opt.type || '';
		
		// 重置地区筛选参数
		this.provinceId = 0;
		this.cityId = 0;
		this.districtId = 0;
		this.currentCity = '';
		this.currentCityId = 0;
		
		this.getdata();
		
		// 监听城市选择结果
		uni.$on('city', this.handleCitySelect);
		
		console.log('2023-06-15 11:05:23-INFO-[peolist][onLoad_001] 页面初始化完成');
  },
  onUnload: function() {
	// 取消监听，防止内存泄漏
	uni.$off('city', this.handleCitySelect);
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onReachBottom: function () {
	  if (!this.nodata && !this.nomore) {
	    this.pagenum = this.pagenum + 1;
	    this.getdatalist(true);
	  }
	},
  methods: {
		// 检查点击是否在筛选面板外部
		checkTapOutside: function(e) {
			// 使用setTimeout确保其他点击事件先执行
			setTimeout(() => {
				if (this.showFilterOptions) {
					console.log('2023-06-15 15:55:23-INFO-[peolist][checkTapOutside_001] 检测到外部点击，关闭筛选面板');
					this.showFilterOptions = false;
				}
			}, 10);
		},
		
		// 切换筛选选项面板
		toggleFilterOptions: function(e) {
			console.log('2023-06-15 15:55:23-INFO-[peolist][toggleFilterOptions_001] 切换筛选面板状态:', !this.showFilterOptions);
			if (e) {
				e.stopPropagation(); // 阻止事件冒泡
			}
			this.showFilterOptions = !this.showFilterOptions;
		},
		
		// 排序选择点击
		sortClick: function (e) {
			e.stopPropagation(); // 阻止事件冒泡
			var that = this;
			var field = e.currentTarget.dataset.field;
			var order = e.currentTarget.dataset.order;
			that.field = field;
			that.order = order;
			// 选择后不立即关闭筛选面板，等用户点击确定按钮
		},
		
		// 重置筛选
		resetFilter: function(e) {
			e.stopPropagation(); // 阻止事件冒泡
			this.field = '';
			this.order = '';
			// 重置后不立即获取数据，等用户点击确定按钮
		},
		
		// 确认筛选并关闭面板
		confirmFilter: function(e) {
			e.stopPropagation(); // 阻止事件冒泡
			this.showFilterOptions = false;
			this.getdatalist();
		},
		// 处理城市选择结果
		handleCitySelect: function(city) {
			console.log('2023-06-15 11:05:23-INFO-[peolist][handleCitySelect_001] 选择的城市：', city);
			if(city && city.id) {
				this.currentCityId = city.id;
				this.currentCity = city.name;
				
				// 更新逻辑，根据city对象的level属性更准确地设置地区ID
				if(city.level !== undefined) {
					if(city.level === 0) { // 省级
						this.provinceId = city.id;
						this.cityId = 0;
						this.districtId = 0;
					} else if(city.level === 1) { // 市级
						this.cityId = city.id;
						this.provinceId = city.parent_id || 0;
						this.districtId = 0;
					} else if(city.level === 2) { // 区级
						this.districtId = city.id;
						this.cityId = city.parent_id || 0;
						
						// 尝试获取省份ID，如果无法获取则设置为0
						if(city.province_id) {
							this.provinceId = city.province_id;
						} else {
							// 可能需要额外API调用获取省份ID，但此处简化处理
							this.provinceId = 0;
						}
					}
				} else if(city.parent_id) {
					// 如果有parent_id属性，可以判断其层级
					this.cityId = city.id;
					this.provinceId = city.parent_id;
					this.districtId = 0;
				} else if(city.first_letter) {
					// 如果有first_letter属性，说明是从字母索引列表选择的城市
					// 判断是否是省级城市（简单判断：城市名称包含"省"或"自治区"）
					let name = city.name || '';
					if(name.indexOf('省') > -1 || name.indexOf('自治区') > -1 || name.indexOf('市') === name.length-1 && name.length > 2) {
						this.provinceId = city.id;
						this.cityId = 0;
						this.districtId = 0;
					} else {
						// 否则可能是城市，这里设置为城市级别
						this.cityId = city.id;
						this.provinceId = 0; // 无法知道其所属省份ID
						this.districtId = 0;
					}
				}
				
				console.log('2023-06-15 11:05:23-INFO-[peolist][handleCitySelect_002] 设置的地区ID：', {
					provinceId: this.provinceId,
					cityId: this.cityId,
					districtId: this.districtId
				});
				
				// 重新获取数据
				this.getdatalist();
			}
		},
		
		// 跳转到城市选择页面
		gotoCity: function() {
			// 获取城市列表数据，使用系统配置的分区模式
			app.get('ApiArea/getActiveAreas', {
				// 不传入mode参数，让后端从系统配置中获取areamode
				with_children: true // 获取包含子级的数据
			}, (res) => {
				if(res.status === 1) {
					// 将数据传递给城市选择页面
					let data = encodeURIComponent(JSON.stringify({
						areas: res.data,
						area_config: {
							hotareas: '',
							hotareas_str: '',
							hotarea: 1,
							switcharearange: 0,
							switcharearangeareas_str: ''
						}
					}));
					
					uni.navigateTo({
						url: '/pages/index/city?data=' + data
					});
				} else {
					uni.showToast({
						title: '获取城市列表失败',
						icon: 'none'
					});
				}
			});
		},
		
		getdata:function(){
			var that = this;
			var nowcid = that.opt.cid;
			var bid = that.opt.bid || 0;
			if (!nowcid) nowcid = '';
			that.loading = true;
			
			// 显示下拉刷新动画
			if(uni.startPullDownRefresh) {
				uni.startPullDownRefresh();
			}
			
			app.get('ApiYuyue/peocategory', {cid: nowcid,bid:bid}, function (res) {
				that.loading = false;
				var data = res.data;
				that.clist = data;
				//that.curCid = data[0]['id'];
				if (nowcid) {
					for (var i = 0; i < data.length; i++) {
						if (data[i]['id'] == nowcid) {
							that.curTopIndex = i;
							that.curCid = nowcid;
							break;
						}
						var downcdata = data[i]['child'];
						var isget = 0;
						for (var j = 0; j < downcdata; j++) {
							if (downcdata[j]['id'] == nowcid) {
								that.curIndex = i;
								that.curIndex2 = j;
								that.curCid = nowcid;
								isget = 1;
								break;
							}
						}
						if (isget) break;
					}
				}
				that.loaded();
				
				app.getLocation(function (res) {
					console.log('位置获取成功：', res);
					var latitude = res.latitude;
					var longitude = res.longitude;
					that.longitude = longitude;
					that.latitude = latitude;
					that.getdatalist();
				},
				function (err) {
					console.log('位置获取失败：', err);
					that.locationFailed = true;
					that.getdatalist();
				});
			});
		},
		getdatalist: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
			var that = this;
			var pagenum = that.pagenum;
			var cid = that.curCid;
			var bid = that.opt.bid ? that.opt.bid : '';
			var order = that.order;
		    var keyword = that.keyword;
			var field = that.field; 
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
			var latitude = that.latitude;
			var longitude = that.longitude;
			
			console.log('2023-06-15 11:05:23-INFO-[peolist][getdatalist_001] 发送请求，地区ID：', {
				provinceId: that.provinceId,
				cityId: that.cityId,
				districtId: that.districtId
			});
			
			var params = {
				pagenum: pagenum,
				keyword: keyword,
				field: field,
				order: order,
				cid: cid,
				bid: bid,
				type: 'list',
				// 添加地区筛选参数，确保是数字类型
				province_id: parseInt(that.provinceId || 0),
				city_id: parseInt(that.cityId || 0),
				district_id: parseInt(that.districtId || 0)
			};
			
			// 只有当经纬度存在且没有定位失败标记时才添加位置信息
			if (longitude && latitude && !that.locationFailed) {
				params.longitude = longitude;
				params.latitude = latitude;
				console.log('发送位置信息到API：', longitude, latitude);
			}
			
			app.post('ApiYuyue/selectpeople', params, function (res) { 
				that.loading = false;
				var data = res.data;
				if (pagenum == 1) {
				  that.datalist = data;
				  if (data.length == 0) {
					that.nodata = true;
				  }
				} else {
				  if (data.length == 0) {
					that.nomore = true;
				  } else {
					var datalist = that.datalist;
					var newdata = datalist.concat(data);
					that.datalist = newdata;
				  }
				}
				
				if (data.length > 0 && data[0].distance) {
					console.log('技师列表已按距离排序，最近的技师距离：' + data[0].distance);
				}
				
				// 处理返回的数据，将未知距离统一格式化
				if (data.length > 0) {
					for (var i = 0; i < data.length; i++) {
						if (!data[i].distance || data[i].distance === '未知') {
							data[i].distance = '';
						}
					}
					if (that.field === 'juli' && that.locationFailed) {
						// 如果要按距离排序但获取位置失败，提示用户
						uni.showToast({
							title: '位置获取失败，无法按距离排序',
							icon: 'none',
							duration: 2000
						});
						that.field = ''; // 重置排序字段
					}
				}
				
				// 停止下拉刷新动画
				uni.stopPullDownRefresh();
			});
		},
		switchTopTab: function (e) {
		   var that = this;
		   var id = e.currentTarget.dataset.id;
		   var index = parseInt(e.currentTarget.dataset.index);
		   this.curTopIndex = index;
		   this.curIndex = -1;
		   this.curIndex2 = -1;
		   this.prolist = [];
		   this.nopro = 0;
		   this.curCid = id;
		   this.getdatalist();
		}, 
		searchChange: function (e) {
		  this.keyword = e.detail.value;
		},
		searchConfirm: function (e) {
		  var that = this;
		  var keyword = e.detail.value;
		  that.keyword = keyword;
		  that.getdata();
		},
		goto: function(e) {
			var url = e.currentTarget.dataset.url;
			uni.navigateTo({
				url: url
			});
		},
		loaded: function() {
			this.isload = true;
		},
		getmenuindex: function(e) {
			this.menuindex = e;
		},
		retryLocation: function() {
			var that = this;
			that.locationFailed = false;
			
			// 再次尝试获取位置
			app.getLocation(function (res) {
				console.log('位置获取成功：', res);
				var latitude = res.latitude;
				var longitude = res.longitude;
				that.longitude = longitude;
				that.latitude = latitude;
				that.getdatalist();
				
				// 显示获取成功提示
				uni.showToast({
					title: '位置获取成功',
					icon: 'success',
					duration: 2000
				});
			},
			function (err) {
				console.log('位置获取失败：', err);
				that.locationFailed = true;
				that.getdatalist();
				
				// 显示获取失败提示
				uni.showToast({
					title: '位置获取失败，请检查位置权限',
					icon: 'none',
					duration: 2000
				});
			});
		}
  }
};
</script>
<style>
.topsearch{width:94%;margin:16rpx 3%;background: #ffffff;border-radius: 16rpx;box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);padding: 8rpx 16rpx;display:flex;align-items:center;}
.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f7f7f7;flex:1;padding: 0 10rpx;margin: 0 10rpx;}
.topsearch .f1 .img{width:30rpx;height:30rpx;margin-left:20rpx}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

/* 城市选择器样式调整 */
.city-select {
	height: 70rpx;
	line-height: 70rpx;
	padding: 0 20rpx;
	margin-right: 15rpx;
	background: #f7f7f7;
	border-radius: 35rpx;
	font-size: 28rpx;
	color: #333;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
	max-width: 150rpx;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	position: relative;
	flex-shrink: 0;
}

/* 添加筛选按钮样式 */
.filter-btn {
	height: 70rpx;
	line-height: 70rpx;
	padding: 0 25rpx;
	background: #f7f7f7;
	border-radius: 35rpx;
	font-size: 28rpx;
	color: #333;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
	position: relative;
	flex-shrink: 0;
}

.filter-btn:active {
	opacity: 0.8;
	transform: scale(0.98);
	background: #f0f0f0;
}

.filter-text {
	margin-right: 6rpx;
}

.filter-icon {
	font-size: 20rpx;
	color: #7A83EC;
}

/* 筛选选项浮层样式 */
.filter-options {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	display: flex;
	flex-direction: column;
}

.filter-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: -1;
}

.filter-panel {
	position: absolute;
	top: 0;
	right: 0;
	width: 70%;
	height: 100%;
	background: #fff;
	padding: 50rpx 30rpx 30rpx;
	z-index: 1000;
	animation: slideInRight 0.3s ease;
	display: flex;
	flex-direction: column;
	box-shadow: -5rpx 0 15rpx rgba(0, 0, 0, 0.1);
}

@keyframes slideInRight {
	from {
		transform: translateX(100%);
	}
	to {
		transform: translateX(0);
	}
}

.filter-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 40rpx;
	margin-top: 40rpx;
	padding-left: 20rpx;
	border-left: 8rpx solid #7A83EC;
}

.filter-list {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 40rpx;
}

.filter-item {
	padding: 15rpx 25rpx;
	border: 1px solid #eee;
	border-radius: 35rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-width: 180rpx;
}

.filter-item.active {
	border-color: #7A83EC;
	color: #7A83EC;
	background: rgba(122, 131, 236, 0.06);
}

.check-icon {
	margin-left: 10rpx;
	font-weight: bold;
}

.city-select:active {
	opacity: 0.8;
	transform: scale(0.98);
	background: #f0f0f0;
}

.city-select:after {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(122, 131, 236, 0.06);
	border-radius: 35rpx;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.city-select:active:after {
	opacity: 1;
}

.city-icon {
	font-size: 20rpx;
	margin-left: 8rpx;
	color: #7A83EC;
}

.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx;box-shadow: 0 6rpx 15rpx rgba(0,0,0,0.03);}
.order-tab2{display:flex;width:auto;min-width:100%;padding: 5rpx 0;}
.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:84rpx; line-height:84rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;transition: all 0.3s ease;}
.order-tab2 .on{color:#222222;}
.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:3px;width:40rpx;transition: all 0.3s ease;}
.order-tab2 .on .after{display:block}


.content{
	width:94%;
	margin:25rpx 3%;
	background:#fff;
	border-radius:16rpx;
	padding:28rpx 30rpx; 
	justify-content: space-between;
	box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.05);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.content:after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background: linear-gradient(to right, #7A83EC, #9E8BF5);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.content:active:after {
	opacity: 1;
}

.content:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);
}

.content .f1{display:flex;align-items:center;flex: 1;}
.content .headimg{
	position: relative;
	overflow: hidden;
	border-radius: 12rpx;
}
.content .f1 image{ 
	width: 150rpx; 
	height: 150rpx; 
	border-radius: 12rpx;
	object-fit: cover;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
	transition: transform 0.5s ease;
}

.content:active .f1 image {
	transform: scale(1.05);
}

.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:32rpx;margin-left:20rpx;}
.content .f1 .t2{
	color:#7A83EC;
	background: rgba(122, 131, 236, 0.1); 
	margin-left: 16rpx; 
	padding:4rpx 20rpx; 
	font-size: 22rpx; 
	border-radius: 18rpx;
	font-weight: normal;
}

.name-type {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-bottom: 10rpx;
}

.text1{ margin-left: 30rpx; flex: 1; display: flex; flex-direction: column;}

.text2{ 
	color:#666666; 
	font-size: 26rpx;
	margin-top: 8rpx;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	max-height: 72rpx;
}
.text3{ 
	color:#999999; 
	font-size: 24rpx;
	margin-top: 16rpx;
	line-height: 1.4;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}
.text3 .t5{ margin-left: 24rpx;}
.text3 .t5 text{ color:#7A83EC; font-weight: 600;}
.text3 .t4 text{ color:#7A83EC; font-weight: 600;}
.text3 .t6{ margin-left: 24rpx;}
.text3 .t6 text{ color:#FF9900; font-weight: 600;}

.yuyue{ 
	background: linear-gradient(to right, #7A83EC, #9E8BF5); 
	height: 64rpx; 
	line-height: 64rpx; 
	padding: 0 25rpx; 
	color:#fff; 
	border-radius:32rpx; 
	min-width: 110rpx; 
	font-size: 28rpx; 
	text-align: center; 
	margin-top: 20rpx;
	transition: all 0.3s ease;
	box-shadow: 0 6rpx 15rpx rgba(122, 131, 236, 0.3);
	font-weight: 500;
	position: relative;
	overflow: hidden;
}

.yuyue:before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.1);
	transform: translateX(-100%);
	transition: transform 0.5s ease;
}

.yuyue:active:before {
	transform: translateX(0);
}

.yuyue:active {
	transform: scale(0.95) translateY(2rpx);
	box-shadow: 0 2rpx 6rpx rgba(122, 131, 236, 0.2);
}

.location-failed {
	margin: 20rpx 3%;
	padding: 30rpx 20rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.04);
}

.retry-btn {
	padding: 14rpx 36rpx;
	border: 1px solid #7A83EC;
	border-radius: 30rpx;
	font-size: 28rpx;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.retry-btn:active {
	transform: scale(0.98);
	opacity: 0.9;
	background: rgba(122, 131, 236, 0.06);
}

.flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.container {
	background: #f8f9fc;
	min-height: 100vh;
	padding-bottom: 50rpx;
}

.content-list {
	padding-bottom: 30rpx;
}

.no-data-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-img {
	width: 220rpx;
	height: 220rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	color: #999999;
	font-size: 30rpx;
}

/* 当前已选择城市的状态优化 */
.city-select.active {
	color: #7A83EC;
	font-weight: 500;
	background: rgba(122, 131, 236, 0.08);
	box-shadow: 0 2rpx 8rpx rgba(122, 131, 236, 0.1);
}

/* 添加技师标签样式 */
.tech-tags {
	display: flex;
	flex-wrap: wrap;
	margin-top: 10rpx;
	margin-bottom: 4rpx;
}

.tech-tag {
	background: rgba(255, 153, 0, 0.1);
	color: #FF9900;
	font-size: 22rpx;
	padding: 4rpx 16rpx;
	border-radius: 16rpx;
	margin-right: 12rpx;
	margin-bottom: 6rpx;
	white-space: nowrap;
}

/* 流式加载动画 */
@keyframes skeleton-loading {
	0% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0 50%;
	}
}

.skeleton {
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
	background-size: 400% 100%;
	animation: skeleton-loading 1.4s ease infinite;
	border-radius: 6rpx;
}

/* 列表项滑入动画 */
@keyframes slideInFromBottom {
	0% {
		opacity: 0;
		transform: translateY(30rpx);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.content {
	animation: slideInFromBottom 0.3s ease;
	animation-fill-mode: both;
}

/* 给每个列表项添加不同的延迟时间，形成瀑布流加载效果 */
.content:nth-child(2) { animation-delay: 0.05s; }
.content:nth-child(3) { animation-delay: 0.1s; }
.content:nth-child(4) { animation-delay: 0.15s; }
.content:nth-child(5) { animation-delay: 0.2s; }
.content:nth-child(6) { animation-delay: 0.25s; }
.content:nth-child(7) { animation-delay: 0.3s; }
.content:nth-child(8) { animation-delay: 0.35s; }
.content:nth-child(9) { animation-delay: 0.4s; }
.content:nth-child(10) { animation-delay: 0.45s; }

/* 骨架屏样式 */
.skeleton-card {
	box-shadow: none;
	margin: 16rpx 3%;
	padding: 24rpx 30rpx;
	animation: none;
}

.headimg-skeleton {
	width: 150rpx;
	height: 150rpx;
	border-radius: 12rpx;
}

.title-skeleton {
	width: 60%;
	height: 32rpx;
	margin-top: 6rpx;
	margin-bottom: 20rpx;
}

.text-skeleton {
	width: 90%;
	height: 24rpx;
	margin-top: 12rpx;
}

.text-skeleton.short {
	width: 60%;
}

.skeleton-tags {
	display: flex;
	margin-top: 20rpx;
}

.tag-skeleton {
	width: 100rpx;
	height: 30rpx;
	border-radius: 15rpx;
	margin-right: 12rpx;
}

.btn-skeleton {
	width: 110rpx;
	height: 64rpx;
	border-radius: 32rpx;
	margin-top: 20rpx;
	align-self: flex-end;
}

.filter-btn-group {
	display: flex;
	justify-content: space-between;
	border-top: 1px solid #f5f5f5;
	padding-top: 30rpx;
	margin-top: auto;
}

.filter-reset, .filter-confirm {
	width: 45%;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 40rpx;
	font-size: 28rpx;
	transition: all 0.3s ease;
}
</style>