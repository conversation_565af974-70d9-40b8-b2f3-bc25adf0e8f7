<template>
<view class="container">
	<block v-if="isload">
		<view class="header-container">
			<view class="config-selector">
				<picker @change="onConfigChange" :value="configIndex" :range="configOptions" range-key="name">
					<view class="picker-display">
						<text>{{selectedConfig.name || '请选择活动配置'}}</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<!-- 新增：快捷操作按钮 -->
			<view class="quick-actions" v-if="selectedConfig.id">
				<view class="action-btn" style="background: #FF5722" @tap="showAddPositionModal">
				?? 增加点位
			</view>
				<view class="action-btn outline" @tap="refreshTree">
					?? 刷新树图
				</view>
				<view class="action-btn" style="background:#ff9500;" @tap="showBatchAddModal">
					?? 批量测试
				</view>
				<view class="action-btn" style="background:#666;" @tap="showDebugInfo">
					?? 调试信息
				</view>
			</view>
			
			<view class="tree-legend">
				<view class="legend-item">
					<view class="legend-dot wealth"></view>
					<text>财富点位</text>
				</view>
				<view class="legend-item">
					<view class="legend-dot normal"></view>
					<text>普通点位</text>
				</view>
			</view>
		</view>
		
		<view class="tree-container" v-if="selectedConfig.id">
			<scroll-view class="tree-scroll" scroll-y="true" scroll-x="true">
				<view class="tree-content">
					<view class="tree-layer" v-for="(layer, layerIndex) in treeData" :key="layerIndex">
						<view class="layer-title">第{{layerIndex + 1}}层</view>
						<view class="layer-nodes">
							<view class="tree-node" 
								  v-for="(node, nodeIndex) in layer" 
								  :key="node.id"
								  :class="{wealth: node.is_wealth_position, expandable: node.has_children}"
								  @click="toggleNode(node)"
								  @longpress="showNodeActions(node)">
								<view class="node-avatar">
									<image :src="node.headimg || '/static/img/default-avatar.png'" class="avatar"/>
									<view class="wealth-badge" v-if="node.is_wealth_position">财</view>
								</view>
								<view class="node-info">
									<view class="node-name">{{node.nickname}}</view>
									<view class="node-position">{{node.position_text}}</view>
									<view class="node-queue">{{node.queue_text}}</view>
									<view class="node-relation">
										<text class="referrer-info">{{node.referrer_text}}</text>
										<text class="parent-info">{{node.parent_text}}</text>
									</view>
									<view class="node-time">{{node.createtime_text}}</view>
								</view>
								<view class="expand-icon" v-if="node.has_children">
									<text :class="{expanded: node.expanded}">▼</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 连接线 -->
					<view class="tree-lines">
						<view class="line" v-for="(line, index) in connectionLines" :key="index" :style="line.style"></view>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<view class="empty-state" v-else>
			<image src="/static/img/empty-tree.png" class="empty-icon"/>
			<view class="empty-text">请选择活动配置查看排单树</view>
		</view>
		
		<view class="tree-stats" v-if="treeStats.total > 0">
			<view class="stats-item">
				<text class="stats-label">总点位：</text>
				<text class="stats-value" style="color: #FF5722">{{treeStats.total}}</text>
			</view>
			<view class="stats-item">
				<text class="stats-label">财富点位：</text>
				<text class="stats-value" style="color: #FF5722">{{treeStats.wealth}}</text>
			</view>
			<view class="stats-item">
				<text class="stats-label">层数：</text>
				<text class="stats-value" style="color: #FF5722">{{treeStats.layers}}</text>
			</view>
		</view>
		
		<!-- 新增：添加点位弹窗 -->
		<view class="modal-overlay" v-if="showAddModal" @click="hideAddPositionModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">添加新点位</text>
					<text class="modal-close" @click="hideAddPositionModal">×</text>
				</view>
				<view class="modal-body">
					<view class="form-item">
						<text class="form-label">推荐人ID（可选）：</text>
						<input type="number" class="form-input" placeholder="真实推荐关系的用户ID" v-model="addForm.referrer_id"/>
					</view>
					<view class="form-item">
						<text class="form-label">父点位ID（可选）：</text>
						<input type="number" class="form-input" placeholder="不填则按公排算法自动分配" v-model="addForm.parent_id"/>
					</view>
					<view class="form-item">
						<text class="form-label">订单ID（可选）：</text>
						<input type="number" class="form-input" placeholder="关联的订单ID" v-model="addForm.order_id"/>
					</view>
					<view class="form-tips">
						<text>?? 提示：</text>
						<text>• 推荐人ID：记录真实的推荐关系</text>
						<text>• 父点位ID：不填将按公排算法自动分配</text>
						<text>• 系统会自动计算层级和排序位置</text>
						<text>• 财富点位根据活动配置自动判断</text>
						<text>• 树形图会显示"推荐人"和"排在XX下"信息</text>
					</view>
				</view>
				<view class="modal-footer">
					<view class="modal-btn cancel" @click="hideAddPositionModal">取消</view>
					<view class="modal-btn confirm" @click="confirmAddPosition" style="background: #FF5722">确认添加</view>
				</view>
			</view>
		</view>
		
		<!-- 新增：节点操作弹窗 -->
		<view class="modal-overlay" v-if="showNodeModal" @tap="hideNodeActions">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text>节点操作</text>
					<text class="close-btn" @tap="hideNodeActions">×</text>
				</view>
				<view class="modal-body" v-if="selectedNode">
					<view class="node-info">
						<text>用户：{{selectedNode.nickname}}</text>
						<text>{{selectedNode.layer_text}}{{selectedNode.position_text}}</text>
						<text>{{selectedNode.wealth_position_text}}</text>
					</view>
					<view class="action-buttons">
						<view class="action-button primary" @tap="addChildNode">
							<text>添加子点位</text>
						</view>
						<view class="action-button secondary" @tap="viewNodeDetail">
							<text>查看详情</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 新增：批量测试弹窗 -->
		<view class="modal-overlay" v-if="showBatchModal" @tap="hideBatchAddModal">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text>批量添加测试点位</text>
					<text class="close-btn" @tap="hideBatchAddModal">×</text>
				</view>
				<view class="modal-body">
					<view class="form-group">
						<text class="label">添加数量：</text>
						<input class="input" v-model="batchForm.count" type="number" placeholder="请输入数量（1-20）" />
					</view>
					<view class="form-tips">
						<text>⚠️ 此功能仅用于开发测试</text>
						<text>?? 将按公排算法自动分配层级和位置</text>
						<text>?? 财富点位将根据配置自动判断</text>
					</view>
					<view class="modal-buttons">
						<view class="modal-button secondary" @tap="hideBatchAddModal">
							<text>取消</text>
						</view>
						<view class="modal-button primary" @tap="confirmBatchAdd">
							<text>确认添加</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			configOptions: [],
			configIndex: 0,
			selectedConfig: {},
			treeData: [],
			connectionLines: [],
			treeStats: {
				total: 0,
				wealth: 0,
				layers: 0
			},
			// 新增：弹窗相关数据
			showAddModal: false,
			addForm: {
				referrer_id: '',
				parent_id: '',
				order_id: ''
			},
			showNodeModal: false,
			selectedNode: null,
			
			// 新增：批量测试相关
			showBatchModal: false,
			batchForm: {
				count: 5
			}
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		var config_id = opt.config_id || 0;
		this.getdata(config_id);
	},
	onPullDownRefresh: function () {
		this.getTreeData();
	},
	methods: {
		// 新增：获取颜色样式，带防御性检查
		getColorStyle: function(colorKey, styleType = 'color') {
			try {
				// 检查全局数据是否初始化
				if (!app.globalData || !app.globalData.initdata || !app.globalData.initdata.configs) {
					console.warn('全局数据未初始化，使用默认颜色');
					return styleType === 'color' ? { color: '#FF5722' } : { background: '#FF5722' };
				}
				
				// 安全地获取颜色
				var colorValue = app.globalData.initdata[colorKey] || '#FF5722';
				return styleType === 'color' ? { color: colorValue } : { background: colorValue };
			} catch (e) {
				console.error('获取颜色样式出错:', e);
				return styleType === 'color' ? { color: '#FF5722' } : { background: '#FF5722' };
			}
		},
		
		// 新增：获取渐变样式，带防御性检查
		getGradientStyle: function() {
			try {
				// 检查全局数据是否初始化
				if (!app.globalData || !app.globalData.initdata || !app.globalData.initdata.configs) {
					console.warn('全局数据未初始化，使用默认渐变');
					return {background: 'linear-gradient(90deg, #007aff 0%, rgba(0, 122, 255, 0.8) 100%)'};
				}
				
				var color1 = this.t('color1');
				var color1rgb = this.t('color1rgb');
				
				if (!color1 || !color1rgb) {
					console.warn('渐变颜色值获取失败，使用默认渐变');
					return {background: 'linear-gradient(90deg, #007aff 0%, rgba(0, 122, 255, 0.8) 100%)'};
				}
				
				return {background: 'linear-gradient(90deg,' + color1 + ' 0%,rgba(' + color1rgb + ',0.8) 100%)'};
			} catch (e) {
				console.error('获取渐变样式失败：', e);
				// 返回默认蓝色渐变
				return {background: 'linear-gradient(90deg, #007aff 0%, rgba(0, 122, 255, 0.8) 100%)'};
			}
		},
		
		getdata:function(preselectedConfigId){
			var that = this;
			that.loaded();
			that.getConfigs(preselectedConfigId);
		},
		
		getConfigs: function(preselectedConfigId){
			var that = this;
			app.post('ApiPaidan/getConfigs', {}, function (res) {
				if(res.code == 1){
					that.configOptions = res.data;
					
					// 如果有预选的配置ID，设置为选中状态
					if(preselectedConfigId > 0){
						for(var i = 0; i < that.configOptions.length; i++){
							if(that.configOptions[i].id == preselectedConfigId){
								that.configIndex = i;
								that.selectedConfig = that.configOptions[i];
								that.getTreeData();
								break;
							}
						}
					}
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		onConfigChange: function(e){
			var index = e.detail.value;
			this.configIndex = index;
			this.selectedConfig = this.configOptions[index];
			this.getTreeData();
		},
		
		getTreeData: function(){
			var that = this;
			
			if(!that.selectedConfig.id){
				that.$refs.popmsg.show({type: 'error', msg: '请选择活动配置'});
				return;
			}
			
			that.loading = true;
			
			app.post('ApiPaidan/getPositionTree', {
				config_id: that.selectedConfig.id
			}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if(res.code == 1){
					// 处理新的API响应格式
					if(res.data.flat && res.data.flat.length > 0){
						that.processTreeData(res.data.flat, res.data);
					} else {
						// 没有数据的情况
						that.treeData = [];
						that.treeStats = {
							total: 0,
							wealth: 0,
							layers: 0
						};
					}
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		processTreeData: function(flatData, apiData){
			// 使用API返回的统计数据
			var totalNodes = apiData.total_count || 0;
			var maxLayer = apiData.max_layer || 0;
			var wealthNodes = 0;
			
			// 计算财富点位数量
			flatData.forEach(function(node){
				if(node.is_wealth_position){
					wealthNodes++;
				}
			});
			
			// 将扁平数据转换为层级结构显示
			var layers = {};
			
			flatData.forEach(function(node){
				var layer = node.layer;
				if(!layers[layer]){
					layers[layer] = [];
				}
				layers[layer].push(node);
			});
			
			// 转换为数组格式，按层级排序
			var treeData = [];
			for(var i = 1; i <= maxLayer; i++){
				if(layers[i]){
					treeData.push(layers[i]);
				}
			}
			
			this.treeData = treeData;
			this.treeStats = {
				total: totalNodes,
				wealth: wealthNodes,
				layers: maxLayer
			};
			
			// 生成连接线
			this.generateConnectionLines();
		},
		
		generateConnectionLines: function(){
			// 这里可以根据需要生成连接线的样式
			// 由于实现复杂，这里暂时留空
			this.connectionLines = [];
		},
		
		toggleNode: function(node){
			if(!node.has_children){
				return;
			}
			
			// 这里可以实现展开/收起子节点的逻辑
			// 由于API设计，暂时只显示基础信息
			uni.showToast({
				title: '点击了' + node.nickname,
				icon: 'none'
			});
		},
		
		// 新增：显示添加点位弹窗
		showAddPositionModal: function(){
			this.showAddModal = true;
			this.addForm = {
				referrer_id: '',
				parent_id: '',
				order_id: ''
			};
		},
		
		// 新增：隐藏添加点位弹窗
		hideAddPositionModal: function(){
			this.showAddModal = false;
		},
		
		// 新增：确认添加点位
		confirmAddPosition: function(){
			var that = this;
			
			if(!that.selectedConfig.id){
				that.$refs.popmsg.show({type: 'error', msg: '请先选择活动配置'});
				return;
			}
			
			// 参数验证
			var params = {
				config_id: that.selectedConfig.id
			};
			
			if(that.addForm.referrer_id){
				params.referrer_id = parseInt(that.addForm.referrer_id);
			}
			
			if(that.addForm.parent_id){
				params.parent_id = parseInt(that.addForm.parent_id);
			}
			
			if(that.addForm.order_id){
				params.order_id = parseInt(that.addForm.order_id);
			}
			
			that.loading = true;
			
			app.post('ApiPaidan/addPosition', params, function (res) {
				that.loading = false;
				
				if(res.code == 1){
					var msg = res.msg;
					if(res.data.queue_number){
						msg += '，排队序号：' + res.data.queue_number;
					}
					if(res.data.referrer_nickname){
						msg += '，推荐人：' + res.data.referrer_nickname;
					}
					that.$refs.popmsg.show({type: 'success', msg: msg});
					that.hideAddPositionModal();
					that.getTreeData(); // 刷新树形图
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		// 新增：刷新树形图
		refreshTree: function(){
			this.getTreeData();
		},
		
		// 新增：显示节点操作
		showNodeActions: function(node){
			this.selectedNode = node;
			this.showNodeModal = true;
		},
		
		// 新增：隐藏节点操作
		hideNodeActions: function(){
			this.showNodeModal = false;
			this.selectedNode = null;
		},
		
		// 新增：在节点下添加子点位
		addChildNode: function(){
			this.addForm.referrer_id = '';
			this.addForm.parent_id = this.selectedNode.id;
			this.addForm.order_id = '';
			this.hideNodeActions();
			this.showAddModal = true;
		},
		
		// 新增：查看节点详情
		viewNodeDetail: function(){
			var node = this.selectedNode;
			uni.showModal({
				title: '点位详情',
				content: '用户：' + node.nickname + '\n' +
						'层级：' + node.layer_text + '\n' +
						'位置：' + node.position_text + '\n' +
						'类型：' + node.wealth_position_text + '\n' +
						'创建时间：' + node.createtime_text,
				showCancel: false
			});
			this.hideNodeActions();
		},
		
		// 新增：显示批量添加弹窗
		showBatchAddModal: function(){
			this.showBatchModal = true;
			this.batchForm.count = 5;
		},
		
		// 新增：隐藏批量添加弹窗
		hideBatchAddModal: function(){
			this.showBatchModal = false;
		},
		
		// 新增：批量添加测试点位
		confirmBatchAdd: function(){
			var that = this;
			
			if(!that.selectedConfig.id){
				that.$refs.popmsg.show({type: 'error', msg: '请先选择活动配置'});
				return;
			}
			
			var count = parseInt(that.batchForm.count);
			if(!count || count < 1 || count > 20){
				that.$refs.popmsg.show({type: 'error', msg: '请输入1-20之间的数量'});
				return;
			}
			
			that.loading = true;
			
			app.post('ApiPaidan/addTestPositions', {
				config_id: that.selectedConfig.id,
				count: count
			}, function (res) {
				that.loading = false;
				
				if(res.code == 1){
					var msg = '批量添加完成：成功' + res.data.success_count + '个，总计' + res.data.total_count + '个';
					if(res.data.errors && res.data.errors.length > 0){
						msg += '\n错误：' + res.data.errors.join(', ');
					}
					that.$refs.popmsg.show({type: 'success', msg: msg});
					that.hideBatchAddModal();
					that.getTreeData(); // 刷新树形图
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		// 新增：查看调试信息
		showDebugInfo: function(){
			var that = this;
			
			if(!that.selectedConfig.id){
				that.$refs.popmsg.show({type: 'error', msg: '请先选择活动配置'});
				return;
			}
			
			app.post('ApiPaidan/debugPositions', {
				config_id: that.selectedConfig.id
			}, function (res) {
				if(res.code == 1){
					var info = '调试信息：\n';
					info += '总点位数：' + res.data.total_count + '\n';
					info += 'AID：' + res.data.debug_info.aid + '\n';
					info += '配置ID：' + res.data.debug_info.config_id + '\n';
					info += '查询时间：' + res.data.debug_info.query_time + '\n\n';
					
					if(res.data.positions && res.data.positions.length > 0){
						info += '点位列表：\n';
						res.data.positions.forEach(function(pos, index){
							info += (index + 1) + '. ID:' + pos.id + ' ' + pos.nickname + ' ' + pos.layer_text + pos.position_text + '\n';
							info += '   ' + pos.queue_info + '\n';
							info += '   ' + pos.parent_info + '\n';
							info += '   ' + pos.referrer_info + '\n';
						});
					} else {
						info += '暂无点位数据';
					}
					
					uni.showModal({
						title: '调试信息',
						content: info,
						showCancel: false
					});
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		loaded: function () {
			this.isload = true;
		},
		
		getmenuindex: function (e) {
			this.menuindex = e;
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}
.header-container{width:100%;padding:30rpx;background-color:#fff;border-bottom:1px solid #f5f5f5;}
.config-selector{margin-bottom:20rpx;}
.picker-display{display:flex;justify-content:space-between;align-items:center;padding:20rpx;background:#f8f8f8;border-radius:12rpx;font-size:28rpx;color:#333;}
.picker-arrow{color:#999;}

/* 新增：快捷操作按钮样式 */
.quick-actions{display:flex;flex-wrap:wrap;margin-bottom:20rpx;}
.action-btn{width:48%;height:70rpx;line-height:70rpx;text-align:center;color:#fff;font-size:26rpx;border-radius:35rpx;margin-bottom:10rpx;}
.action-btn:nth-child(odd){margin-right:4%;}
.action-btn.outline{background:#fff;color:#333;border:2rpx solid #ddd;}

.tree-legend{display:flex;justify-content:center;}
.legend-item{display:flex;align-items:center;margin:0 20rpx;}
.legend-dot{width:20rpx;height:20rpx;border-radius:50%;margin-right:8rpx;}
.legend-dot.wealth{background-color:#ff6b35;}
.legend-dot.normal{background-color:#007aff;}
.tree-container{flex:1;height:100%;overflow:hidden;}
.tree-scroll{width:100%;height:100%;}
.tree-content{min-width:100%;padding:30rpx;position:relative;}
.tree-layer{margin-bottom:40rpx;}
.layer-title{font-size:24rpx;color:#666;text-align:center;margin-bottom:20rpx;}
.layer-nodes{display:flex;flex-wrap:wrap;justify-content:center;}
.tree-node{width:200rpx;background:#fff;margin:10rpx;padding:20rpx;border-radius:16rpx;text-align:center;position:relative;box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);}
.tree-node.wealth{border:2rpx solid #ff6b35;}
.tree-node.expandable{cursor:pointer;}
.node-avatar{position:relative;margin-bottom:12rpx;}
.avatar{width:60rpx;height:60rpx;border-radius:50%;}
.wealth-badge{position:absolute;top:-8rpx;right:-8rpx;width:24rpx;height:24rpx;background:#ff6b35;color:#fff;font-size:18rpx;border-radius:50%;line-height:24rpx;text-align:center;}
.node-info{}
.node-name{font-size:24rpx;color:#333;font-weight:bold;margin-bottom:8rpx;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.node-position{font-size:20rpx;color:#666;margin-bottom:4rpx;}
.node-queue{font-size:18rpx;color:#ff9500;font-weight:bold;margin-bottom:4rpx;}
.node-relation{margin-bottom:4rpx;}
.referrer-info{display:block;font-size:18rpx;color:#007aff;margin-bottom:2rpx;}
.parent-info{display:block;font-size:18rpx;color:#ff6b35;margin-bottom:2rpx;}
.node-time{font-size:18rpx;color:#999;}
.expand-icon{position:absolute;bottom:8rpx;right:8rpx;}
.expand-icon text{font-size:16rpx;color:#999;transition:transform 0.3s ease;}
.expand-icon text.expanded{transform:rotate(180deg);}
.empty-state{flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;}
.empty-icon{width:200rpx;height:200rpx;margin-bottom:30rpx;}
.empty-text{font-size:28rpx;color:#666;}
.tree-stats{width:100%;padding:20rpx 30rpx;background:#fff;border-top:1px solid #f5f5f5;display:flex;justify-content:space-around;}
.stats-item{text-align:center;}
.stats-label{font-size:24rpx;color:#666;}
.stats-value{font-size:28rpx;font-weight:bold;margin-left:8rpx;}
.tree-lines{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;z-index:1;}
.line{position:absolute;background:#ddd;}

/* 新增：弹窗样式 */
.modal-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:9999;}
.modal-content{width:600rpx;background:#fff;border-radius:20rpx;overflow:hidden;}
.modal-content.small{width:500rpx;}
.modal-header{display:flex;justify-content:space-between;align-items:center;padding:30rpx;border-bottom:1px solid #f5f5f5;}
.modal-title{font-size:32rpx;font-weight:bold;color:#333;}
.modal-close{font-size:40rpx;color:#999;width:40rpx;height:40rpx;text-align:center;line-height:32rpx;}
.modal-body{padding:30rpx;}
.form-item{margin-bottom:30rpx;}
.form-label{display:block;font-size:26rpx;color:#333;margin-bottom:16rpx;}
.form-input{width:100%;height:80rpx;padding:0 20rpx;background:#f8f8f8;border-radius:12rpx;font-size:26rpx;border:none;}
.form-tips{background:#f8f9fa;padding:20rpx;border-radius:12rpx;margin-top:20rpx;}
.form-tips text{display:block;font-size:22rpx;color:#666;margin-bottom:8rpx;line-height:1.5;}
.form-tips text:first-child{font-weight:bold;color:#333;}
.form-tips text:last-child{margin-bottom:0;}
.modal-footer{display:flex;padding:30rpx;border-top:1px solid #f5f5f5;}
.modal-btn{flex:1;height:80rpx;line-height:80rpx;text-align:center;font-size:28rpx;border-radius:40rpx;margin:0 10rpx;}
.modal-btn:first-child{margin-left:0;}
.modal-btn:last-child{margin-right:0;}
.modal-btn.cancel{background:#f8f8f8;color:#666;}
.modal-btn.confirm{color:#fff;}

/* 新增：节点详情样式 */
.node-detail{margin-bottom:30rpx;}
.detail-row{display:flex;justify-content:space-between;margin-bottom:16rpx;font-size:26rpx;}
.detail-row:last-child{margin-bottom:0;}
.label{color:#666;font-weight:normal;}
.value{color:#333;font-weight:bold;}
.action-list{}
.action-item{padding:20rpx 0;border-bottom:1px solid #f5f5f5;font-size:26rpx;color:#333;text-align:center;}
.action-item:last-child{border-bottom:none;}
.action-item:active{background:#f8f8f8;}
</style>