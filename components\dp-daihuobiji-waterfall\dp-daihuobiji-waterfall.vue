<template>
  <scroll-view
    class="waterfalls-container"
    scroll-y
    :scroll-with-animation="true"
    @scrolltolower="onScrollToLower"
    @scroll="onScroll"
    :style="{ height: containerHeight + 'px' }"
  >
    <view class="waterfalls-box" :style="{ height: height + 'px' }">
      <view 
        v-for="(item, index) of displayList"
        class="waterfalls-list"
        :class="{ 'loaded': allHeightArr[index] }"
        :key="item[idKey]"
        :id="'waterfalls-list-id-' + item[idKey]"
        :ref="'waterfalls-list-id-' + item[idKey]"
        :style="{
          '--offset': offset + 'px',
          '--cols': cols,
          top: allPositionArr[index] ? allPositionArr[index].top : 0,
          left: allPositionArr[index] ? allPositionArr[index].left : 0,
          width: columnWidth + 'px'
        }"
        @click="goto" 
        :data-url="'/daihuobiji/detail/index?id='+item['daihuobijiId']"
      >
        <image
          class="waterfalls-list-image"
          mode="widthFix"
          :style="imageStyle"
          :src="item[imageSrcKey] || ' '"
          @load="imageLoadHandle(index)"
          @error="imageLoadHandle(index)"
        >
          <view class="skeleton-loading" v-if="!allHeightArr[index]"></view>
        </image>
        <view class="article-waterfall-info">
          <rich-text class="p1" :nodes="item.title"></rich-text>
          <view class="p2">
            <view class="left">
              <image class="user-avatar" :src="item.headimg"></image>
              <text style="overflow:hidden" class="flex1">{{item.nickname}}</text>
            </view>
            <view class="right">
              <image class="like-icon" src="../../static/img/daihuobiji_like.png"></image>
              <text style="overflow:hidden">{{item.readcount}}</text>
            </view>
          </view>
        </view>
      </view>
      <view v-if="loading" class="loading-indicator">加载中...</view>
      <view v-if="noMore" class="no-more">没有更多数据了</view>
    </view>
  </scroll-view>
</template>

<script>
import { debounce } from 'lodash';

export default {
  props: {
    list: { type: Array, required: true },
    offset: { type: Number, default: 8 },
    idKey: { type: String, default: "daihuobijiId" },
    imageSrcKey: { type: String, default: "coverimg" },
    cols: { type: Number, default: 2 },
    imageStyle: { type: Object },
    showtime: { type: String, default: "1" },
    showreadcount: { type: String, default: "1" },
    loadCount: { type: Number, default: 10 },
    containerHeight: { type: Number, default: 600 },
    loadingDelay: { type: Number, default: 300 },
  },
  data() {
    return {
      topArr: [], // 每列的顶部位置
      allPositionArr: [], // 存储每个项目的位置
      allHeightArr: [], // 存储每个项目的高度
      height: 0, // 瀑布流容器的总高度
      currentLoad: 0, // 当前已加载的项目数量
      loading: false, // 是否正在加载中
      noMore: false, // 是否已无更多数据
      displayList: [], // 当前显示的项目列表
      columnWidth: 0, // 每列的宽度
      debouncedLoadMore: null,
      debouncedImageLoadHandle: null,
    };
  },
  watch: {
    list: {
      handler(newList) {
        if (newList && newList.length > 0) {
          setTimeout(() => {
            this.reset();
          }, 100);
        }
      },
      immediate: true
    }
  },
  created() {
    this.debouncedLoadMore = debounce(this.loadMore, 200);
    this.debouncedImageLoadHandle = debounce(this.imageLoadHandle, 50);
  },
  mounted() {
    setTimeout(() => {
      this.setColumnWidth();
    }, 100);
    uni.$on('resize', this.handleResize);
  },
  beforeDestroy() {
    uni.$off('resize', this.handleResize);
    if (this.debouncedLoadMore) this.debouncedLoadMore.cancel();
    if (this.debouncedImageLoadHandle) this.debouncedImageLoadHandle.cancel();
  },
  methods: {
    handleResize() {
      setTimeout(() => {
        this.setColumnWidth();
      }, 100);
    },
    setColumnWidth() {
      const query = uni.createSelectorQuery().in(this);
      query.select('.waterfalls-box').boundingClientRect(rect => {
        if (!rect) return;
        const totalOffset = this.offset * (this.cols - 1);
        this.columnWidth = (rect.width - totalOffset) / this.cols;
        this.resetLayout();
      }).exec();
    },
    resetLayout() {
      this.topArr = Array(this.cols).fill(0);
      this.allPositionArr = [];
      this.height = 0;
      this.$nextTick(() => {
        this.displayList.forEach((item, index) => {
          this.debouncedImageLoadHandle(index);
        });
      });
    },
    reset() {
      this.topArr = Array(this.cols).fill(0);
      this.allPositionArr = [];
      this.allHeightArr = [];
      this.height = 0;
      this.currentLoad = 0;
      this.noMore = false;
      this.displayList = [];
      
      this.$nextTick(() => {
        this.loadMore();
      });
    },
    async loadMore() {
      if (this.loading || this.noMore || !this.list || this.list.length === 0) return;
      
      this.loading = true;
      await new Promise(resolve => setTimeout(resolve, this.loadingDelay));

      const nextLoad = this.currentLoad + this.loadCount;
      const newItems = this.list.slice(this.currentLoad, nextLoad);
      
      if (newItems.length === 0) {
        this.noMore = true;
        this.loading = false;
        return;
      }

      this.displayList = [...this.displayList, ...newItems];
      this.currentLoad += newItems.length;
      
      await this.$nextTick();
      
      newItems.forEach((_, index) => {
        const globalIndex = this.currentLoad - newItems.length + index;
        let minCol = 0;
        let minHeight = this.topArr[0];
        for (let i = 1; i < this.cols; i++) {
          if (this.topArr[i] < minHeight) {
            minHeight = this.topArr[i];
            minCol = i;
          }
        }
        
        const position = {
          top: this.topArr[minCol] + "px",
          left: (this.columnWidth + this.offset) * minCol + "px"
        };
        this.$set(this.allPositionArr, globalIndex, position);
        this.topArr[minCol] += 200 + this.offset;
        this.height = Math.max(...this.topArr);
      });

      this.loading = false;
    },
    onScroll(event) {
      this.$emit('scroll', event);
    },
    onScrollToLower() {
      this.debouncedLoadMore();
    },
    imageLoadHandle(index) {
      const item = this.displayList[index];
      if (!item) return;

      const id = "waterfalls-list-id-" + item[this.idKey];
      const query = uni.createSelectorQuery().in(this);
      
      query.select("#" + id).fields({ size: true }, (data) => {
        if (!data) return;
        const itemHeight = Math.max(data.height, 200);
        this.$set(this.allHeightArr, index, itemHeight);
        this.recalculateLayout(index);
      }).exec();
    },
    recalculateLayout(changedIndex) {
      const currentPosition = this.allPositionArr[changedIndex];
      if (!currentPosition) return;
      
      const currentLeft = parseFloat(currentPosition.left);
      const currentCol = Math.round(currentLeft / (this.columnWidth + this.offset));
      
      this.topArr = Array(this.cols).fill(0);
      
      this.displayList.forEach((_, index) => {
        const height = this.allHeightArr[index];
        if (!height) return;
        
        let minCol = 0;
        let minHeight = this.topArr[0];
        for (let i = 1; i < this.cols; i++) {
          if (this.topArr[i] < minHeight) {
            minHeight = this.topArr[i];
            minCol = i;
          }
        }
        
        const position = {
          top: this.topArr[minCol] + "px",
          left: (this.columnWidth + this.offset) * minCol + "px"
        };
        
        this.$set(this.allPositionArr, index, position);
        this.topArr[minCol] += height + this.offset;
      });
      
      this.height = Math.max(...this.topArr);
    },
    goto(event) {
      const url = event.currentTarget.dataset.url;
      if (!url) return;
      
      uni.navigateTo({
        url,
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.waterfalls-container {
  position: relative;
  width: 100%;
}

.waterfalls-box {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.waterfalls-list {
  position: absolute;
  background-color: #fff;
  border-radius: 8rpx;
  transition: opacity 0.3s;
  opacity: 0;
  
  &.loaded {
    opacity: 1;
  }
  
  .waterfalls-list-image {
    width: 100%;
    will-change: transform;
    border-radius: 8rpx 8rpx 0 0;
    display: block;
    background-color: #f5f5f5;
    min-height: 200rpx;
    position: relative;
    
    .skeleton-loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
      background-size: 400% 100%;
      animation: skeleton-loading 1.4s ease infinite;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.article-waterfall-info {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  
  .p1 {
    color: #222;
    font-weight: bold;
    font-size: 28rpx;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  
  .p2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
    
    .left {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      
      .user-avatar {
        width: 32rpx;
        height: 32rpx;
        border-radius: 50%;
        margin-right: 10rpx;
        flex-shrink: 0;
      }
      
      .flex1 {
        flex: 1;
        min-width: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    
    .right {
      display: flex;
      align-items: center;
      margin-left: 20rpx;
      
      .like-icon {
        width: 24rpx;
        height: 24rpx;
        margin-right: 6rpx;
      }
    }
  }
}

.loading-indicator,
.no-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}
</style>
