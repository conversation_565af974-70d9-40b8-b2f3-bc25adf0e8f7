<template>
<view class="container">
	<block v-if="isload">
		<view class="content">
			<view class="info-item" style="height:136rpx;line-height:136rpx">
				<view class="t1" style="flex:1;">我的积分池详情</view>
			</view>
			
		</view>
		<view class="content">
			<view class="info-item" @tap="goto" data-url="jingtaiZong">
				<view class="t1">积分总额</view>
				<view class="t2">{{userinfo.zongscore}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>	
		</view>
		<view class="content">
			<view class="info-item" style="height:136rpx;line-height:136rpx">
				<view class="t1" style="flex:1;">积分自动转余额</view>
			</view>
		</view>
		<view class="content">
			<view class="info-item" @tap="goto" data-url="usercenter">
				<text class="t1">规则</text>
				<text class="t2">{{userinfo.guize}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="jingtaiA">
				<text class="t1">静态已转入余额</text>
				<text class="t2">{{userinfo.countA}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<!-- /-->
			<view class="info-item" @tap="goto" data-url="jingtaiB">
				<text class="t1">动态已转入余额</text>
				<text class="t2">{{userinfo.countB}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			userinfo:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/myjifenchi', {}, function (data) {
				that.loading = false;
				that.userinfo = data.userinfo;
				that.loaded();
			});
		}
  }
};
</script>
<style>
.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}


</style>