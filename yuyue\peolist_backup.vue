<template>
<view class="container">
	<block v-if="isload">
		<view class="topsearch flex-y-center">
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="输入姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm" @input="searchChange"></input>
			</view>
		</view>
		<view class="order-tab">
			<view class="order-tab2">
				<view :class="'item ' + (curTopIndex == -1 ? 'on' : '')" @tap="switchTopTab" :data-index="-1" :data-id="0">全部<view class="after" :style="{background:t('color1')}"></view></view>
				<block v-for="(item, index) in clist" :key="index">
					<view :class="'item ' + (curTopIndex == index ? 'on' : '')" @tap="switchTopTab" :data-index="index" :data-id="item.id">{{item.name}}<view class="after" :style="{background:t('color1')}"></view></view>
				</block>
			</view>
		</view>
		
		<!-- 添加排序选项 -->
		<view class="sort-bar flex" v-if="!locationFailed && (latitude && longitude)">
			<view :class="'sort-item ' + (field == 'juli' ? 'active' : '')" 
			      @tap="sortClick" 
			      data-field="juli" 
			      data-order="asc"
			      :style="field=='juli' ? 'color:'+t('color1') : ''">
				按距离排序
			</view>
			<view :class="'sort-item ' + (field == 'comment_score' ? 'active' : '')" 
			      @tap="sortClick" 
			      data-field="comment_score" 
			      data-order="desc"
			      :style="field=='comment_score' ? 'color:'+t('color1') : ''">
				按评分排序
			</view>
		</view>
		
		<!-- 位置获取失败时显示重试按钮 -->
		<view class="location-failed flex-center" v-if="locationFailed">
			<view class="retry-btn" @tap="retryLocation" :style="{borderColor:t('color1'), color:t('color1')}">
				重新获取位置
			</view>
		</view>
	
		<view class="content-list">
			<view v-for="(item, index) in datalist" :key="index" class="content flex" :data-id="item.id">
				<view class="f1" @click="goto" :data-url="'peodetail?id='+item.id" >
					<view class="headimg"><image :src="item.headimg" mode="aspectFill" /></view>
					<view class="text1">	
						<view class="name-type">
							<text class="t1">{{item.realname}} </text>
							<text class="t2" v-if="item.typename" >{{item.typename}} </text>
						</view>
						<view class="text2">{{item.jineng}}</view>
						<view class="text3">
							<text class="t4">服务<text> {{item.totalnum}}</text> 次</text> 
							<text class="t5">评分 <text>{{item.comment_score}}</text></text>
							<text class="t6" v-if="item.distance && item.distance != '未知'">距离 <text>{{item.distance}}</text></text>
						</view>
					</view>	
				</view>
				<view>
					<view class="yuyue"  @click="goto" :data-url="'/yuyue/peodetail?id='+item.id" :style="{background:t('color1')}">预约</view>
				</view>
			</view>
			<view class="no-data-placeholder" v-if="datalist.length === 0 && !loading && nodata">
				<image src="/static/img/empty.png" mode="aspectFit" class="empty-img"></image>
				<view class="empty-text">暂无相关技师</view>
			</view>
		</view>
		<nodata v-if="nodata && datalist.length === 0 && !loading"></nodata>
		<nomore v-if="nomore"></nomore>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
	  opt:{},
	  loading:false,
      isload: false,
	  menuindex:-1,
      keyword: '',
      datalist: [],
      type: "",
	  nodata:false,
	  curTopIndex: -1,
	  index:0,
	  curCid:0,
	  nomore: false,
	  pagenum: 1,
	  longitude: '',
      latitude: '',
	  field: '',
      order: '',
	  locationFailed: false,
    }
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.type = this.opt.type || '';
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onReachBottom: function () {
	  if (!this.nodata && !this.nomore) {
	    this.pagenum = this.pagenum + 1;
	    this.getdatalist(true);
	  }
	},
  methods: {
		getdata:function(){
			var that = this;
			var nowcid = that.opt.cid;
			var bid = that.opt.bid || 0;
			if (!nowcid) nowcid = '';
			that.loading = true;
			
			// 显示下拉刷新动画
			if(uni.startPullDownRefresh) {
				uni.startPullDownRefresh();
			}
			
			app.get('ApiYuyue/peocategory', {cid: nowcid,bid:bid}, function (res) {
				that.loading = false;
				var data = res.data;
				that.clist = data;
				//that.curCid = data[0]['id'];
				if (nowcid) {
					for (var i = 0; i < data.length; i++) {
						if (data[i]['id'] == nowcid) {
							that.curTopIndex = i;
							that.curCid = nowcid;
							break;
						}
						var downcdata = data[i]['child'];
						var isget = 0;
						for (var j = 0; j < downcdata; j++) {
							if (downcdata[j]['id'] == nowcid) {
								that.curIndex = i;
								that.curIndex2 = j;
								that.curCid = nowcid;
								isget = 1;
								break;
							}
						}
						if (isget) break;
					}
				}
				that.loaded();
				
				app.getLocation(function (res) {
					console.log('位置获取成功：', res);
					var latitude = res.latitude;
					var longitude = res.longitude;
					that.longitude = longitude;
					that.latitude = latitude;
					that.getdatalist();
				},
				function (err) {
					console.log('位置获取失败：', err);
					that.locationFailed = true;
					that.getdatalist();
				});
			});
		},
		getdatalist: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
			var that = this;
			var pagenum = that.pagenum;
			var cid = that.curCid;
			var bid = that.opt.bid ? that.opt.bid : '';
			var order = that.order;
		    var keyword = that.keyword;
			var field = that.field; 
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
			var latitude = that.latitude;
			var longitude = that.longitude;
			
			var params = {
				pagenum: pagenum,
				keyword: keyword,
				field: field,
				order: order,
				cid: cid,
				bid: bid,
				type: 'list'
			};
			
			// 只有当经纬度存在且没有定位失败标记时才添加位置信息
			if (longitude && latitude && !that.locationFailed) {
				params.longitude = longitude;
				params.latitude = latitude;
				console.log('发送位置信息到API：', longitude, latitude);
			}
			
			app.post('ApiYuyue/selectpeople', params, function (res) { 
				that.loading = false;
				var data = res.data;
				if (pagenum == 1) {
				  that.datalist = data;
				  if (data.length == 0) {
					that.nodata = true;
				  }
				} else {
				  if (data.length == 0) {
					that.nomore = true;
				  } else {
					var datalist = that.datalist;
					var newdata = datalist.concat(data);
					that.datalist = newdata;
				  }
				}
				
				if (data.length > 0 && data[0].distance) {
					console.log('技师列表已按距离排序，最近的技师距离：' + data[0].distance);
				}
				
				// 处理返回的数据，将未知距离统一格式化
				if (data.length > 0) {
					for (var i = 0; i < data.length; i++) {
						if (!data[i].distance || data[i].distance === '未知') {
							data[i].distance = '';
						}
					}
					if (that.field === 'juli' && that.locationFailed) {
						// 如果要按距离排序但获取位置失败，提示用户
						uni.showToast({
							title: '位置获取失败，无法按距离排序',
							icon: 'none',
							duration: 2000
						});
						that.field = ''; // 重置排序字段
					}
				}
				
				// 停止下拉刷新动画
				uni.stopPullDownRefresh();
			});
		},
		switchTopTab: function (e) {
		   var that = this;
		   var id = e.currentTarget.dataset.id;
		   var index = parseInt(e.currentTarget.dataset.index);
		   this.curTopIndex = index;
		   this.curIndex = -1;
		   this.curIndex2 = -1;
		   this.prolist = [];
		   this.nopro = 0;
		   this.curCid = id;
		   this.getdatalist();
		}, 
		searchChange: function (e) {
		  this.keyword = e.detail.value;
		},
		searchConfirm: function (e) {
		  var that = this;
		  var keyword = e.detail.value;
		  that.keyword = keyword;
		  that.getdata();
		},
		sortClick: function (e) {
			var that = this;
			var field = e.currentTarget.dataset.field;
			var order = e.currentTarget.dataset.order;
			that.field = field;
			that.order = order;
			that.getdatalist();
		},
		goto: function(e) {
			var url = e.currentTarget.dataset.url;
			uni.navigateTo({
				url: url
			});
		},
		loaded: function() {
			this.isload = true;
		},
		getmenuindex: function(e) {
			this.menuindex = e;
		},
		retryLocation: function() {
			var that = this;
			that.locationFailed = false;
			
			// 再次尝试获取位置
			app.getLocation(function (res) {
				console.log('位置获取成功：', res);
				var latitude = res.latitude;
				var longitude = res.longitude;
				that.longitude = longitude;
				that.latitude = latitude;
				that.getdatalist();
				
				// 显示获取成功提示
				uni.showToast({
					title: '位置获取成功',
					icon: 'success',
					duration: 2000
				});
			},
			function (err) {
				console.log('位置获取失败：', err);
				that.locationFailed = true;
				that.getdatalist();
				
				// 显示获取失败提示
				uni.showToast({
					title: '位置获取失败，请检查位置权限',
					icon: 'none',
					duration: 2000
				});
			});
		}
  }
};
</script>
<style>
.topsearch{width:94%;margin:16rpx 3%;background: #ffffff;border-radius: 10rpx;box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);}
.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f7f7f7;flex:1;padding: 0 10rpx;}
.topsearch .f1 .img{width:30rpx;height:30rpx;margin-left:20rpx}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}
.search-history {padding: 24rpx 34rpx;}
.search-history .search-history-title {color: #666;}
.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}
.search-history-list {padding: 24rpx 0 0 0;}
.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}


.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx;box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);}
.order-tab2{display:flex;width:auto;min-width:100%}
.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:84rpx; line-height:84rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;transition: all 0.3s ease;}
.order-tab2 .on{color:#222222;}
.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:3px;width:40rpx;transition: all 0.3s ease;}
.order-tab2 .on .after{display:block}


.content{
	width:94%;
	margin:20rpx 3%;
	background:#fff;
	border-radius:12rpx;
	padding:24rpx 30rpx; 
	justify-content: space-between;
	box-shadow: 0 2rpx 15rpx rgba(0,0,0,0.05);
	transition: all 0.3s ease;
}

.content:active {
	transform: translateY(2rpx);
	box-shadow: 0 1rpx 8rpx rgba(0,0,0,0.03);
}

.content .f1{display:flex;align-items:center}
.content .f1 image{ 
	width: 140rpx; 
	height: 140rpx; 
	border-radius: 12rpx;
	object-fit: cover;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:32rpx;margin-left:20rpx;}
.content .f1 .t2{
	color:#7A83EC;
	background: rgba(122, 131, 236, 0.1); 
	margin-left: 16rpx; 
	padding:4rpx 20rpx; 
	font-size: 22rpx; 
	border-radius: 18rpx;
	font-weight: normal;
}

.name-type {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-bottom: 4rpx;
}

.text1{ margin-left: 20rpx; flex: 1; display: flex; flex-direction: column;}

.text2{ 
	color:#999999; 
	font-size: 24rpx;
	margin-top: 6rpx;
	line-height: 1.4;
}
.text3{ 
	color:#999999; 
	font-size: 24rpx;
	margin-top: 12rpx;
	line-height: 1.4;
}
.text3 .t5{ margin-left: 20rpx;}
.text3 .t5 text{ color:#7A83EC; font-weight: 600;}
.text3 .t4 text{ color:#7A83EC; font-weight: 600;}
.text3 .t6{ margin-left: 20rpx;}
.text3 .t6 text{ color:#FF9900; font-weight: 600;}

.yuyue{ 
	background: #7A83EC; 
	height: 60rpx; 
	line-height: 60rpx; 
	padding: 0 20rpx; 
	color:#fff; 
	border-radius:30rpx; 
	width: 100rpx; 
	font-size: 26rpx; 
	text-align: center; 
	margin-top: 20rpx;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(122, 131, 236, 0.3);
}

.yuyue:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 6rpx rgba(122, 131, 236, 0.2);
}

.sort-bar {
	margin: 20rpx 3%;
	padding: 20rpx;
	background: #fff;
	border-radius: 10rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);
	display: flex;
}

.sort-item {
	padding: 12rpx 24rpx;
	margin-right: 20rpx;
	border: 1px solid #f5f5f5;
	border-radius: 30rpx;
	font-size: 26rpx;
	transition: all 0.3s ease;
}

.sort-item.active {
	border-color: #7A83EC;
	color: #7A83EC;
	background: rgba(122, 131, 236, 0.06);
}

.location-failed {
	margin: 20rpx 3%;
	padding: 30rpx 20rpx;
	background-color: #fff;
	border-radius: 10rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);
}

.retry-btn {
	padding: 14rpx 30rpx;
	border: 1px solid #7A83EC;
	border-radius: 30rpx;
	font-size: 26rpx;
	transition: all 0.3s ease;
}

.retry-btn:active {
	transform: scale(0.98);
	opacity: 0.9;
}

.flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.container {
	background: #f7f8fc;
	min-height: 100vh;
	padding-bottom: 30rpx;
}

.content-list {
	padding-bottom: 20rpx;
}

.no-data-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 0;
}

.empty-img {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	color: #999999;
	font-size: 28rpx;
}
</style>