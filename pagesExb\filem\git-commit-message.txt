升级PDF预览组件，实现类似官方PDF.js查看器的界面和功能

1. 界面升级:
   - 重新设计了工具栏和控制按钮
   - 添加了侧边栏，支持缩略图、大纲和附件视图
   - 添加了二级工具栏和弹出菜单
   - 实现了暗色主题，与官方PDF.js查看器风格一致

2. 功能增强:
   - 添加了页面缩放功能，支持多种预设缩放级别
   - 添加了页面旋转功能（顺时针和逆时针）
   - 添加了多种滚动模式（垂直、水平）
   - 添加了页面布局选项（单页、双页）
   - 实现了演示模式（全屏查看）
   - 增加了文档属性查看功能
   - 支持手形工具和文本选择工具

3. 性能优化:
   - 实现了多页渲染和页面缓存
   - 添加了相邻页面预加载功能
   - 优化了页面渲染逻辑和内存使用

4. 其他改进:
   - 在H5环境下支持打印功能
   - 改进了触控操作支持
   - 增强了错误处理和用户提示

修复PDF.js引用问题

1. 修改了PDF预览组件(pdf-preview.vue)：
   - 替换了直接使用源码的方式，改为使用预编译的PDF.js库
   - H5环境下通过CDN动态加载PDF.js库
   - 非H5环境使用本地PDF.js文件

2. 创建了目录结构：
   - 添加了pdfjs/build目录用于存放预编译文件
   - 创建了临时占位文件pdf.min.js和pdf.worker.min.js

3. 添加了PDF.js使用指南文档，详细说明了：
   - 配置方法
   - 文件获取方式
   - 使用示例
   - 常见问题解决方案 