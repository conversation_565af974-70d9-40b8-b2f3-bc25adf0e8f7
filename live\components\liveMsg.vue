<template>
	<view class="message">
		<scroll-view class="msg-scroll" :scroll-top="scrollTop" :class="{mask: msgMask}" @scroll="scrollEvent" :show-scrollbar="false" :scroll-with-animation="true" scroll-y>
			<view class="msg flex flex-column" id="msg-area">
				
				<view class="msg-container">
				
				<view class="msg-item system">
					<text class="system-text">平台提倡绿色直播，严禁未成年人直播或打赏，严禁涉政、涉恐、涉黄、聚集闹事、返现等内容，平台将会24小时巡查。请勿参与直播间非官方奖励活动/游戏，切勿私下交易，以防受骗。</text>
				</view>
				<view class="msg-item" v-for="(item, index) in messageList">
					<!-- 有昵称时显示用户消息 -->
					<template v-if="item.nickname">
						<text class="username">{{ item.nickname }}：</text>
						<text class="message-text">{{ item.message }}</text>
					</template>
					<!-- 没有昵称时显示为系统消息 -->
					<template v-else>
						<text class="system">{{ item.message }}</text>
					</template>
				</view>
				
				</view>
				
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				msgMask: null,
				messageList: [],
				cachedMessages: [],
				scrollTop: 0
			}
		},
		
		methods: {
			scrollEvent(e) {
				this.msgMask = e.detail.scrollTop > 0;
			},
			
			sendLiveMsg(msg) {
				console.log(msg)
				
				let data = msg.split('-')
				
				// 处理系统消息（进入、离开、点赞等）
				if(data[2] === 'like' || data[2] === 'out' || data[2] === 'join') {
					this.messageList.push({
						nickname: '', // 系统消息不显示昵称
						message: data[1]
					});
				} else {
					// 用户发送的普通消息
					this.messageList.push({
						nickname: data[0],
						message: data[1]
					});
				}     

				this.scrollToLower();
			},
			
			scrollToLower() {
			    setTimeout(() => {
			    	const query = uni.createSelectorQuery().in(this);
			    	query.select('#msg-area').boundingClientRect(data => {
			    		this.scrollTop = data.height;
			    	}).exec();
			    }, 50);
			},
			
			clearMessages() {
				this.cachedMessages = [...this.messageList];
				this.messageList = [];
			},
			
			restoreMessages() {
				if (this.cachedMessages.length > 0) {
					this.messageList = [...this.cachedMessages];
					this.scrollToLower();
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.message {
		position: absolute;
		bottom: 80rpx;
		left: 20rpx;
		right: 20rpx;
		width: calc(100% - 40rpx);
		/* #ifdef H5 */
		position: absolute;
		
		bottom: 80rpx;
		z-index: 99;
		/* #endif */
		/* #ifndef H5 */
		z-index: 10;
		/* #endif */

		.msg-scroll {
			height: 550rpx;
			width: 100%;
			/* #ifdef H5 */
			padding-bottom: 0;
			pointer-events: auto;
			margin-bottom: 20rpx;
			/* #endif */
			/* #ifdef MP-WEIXIN */
			padding-bottom: env(safe-area-inset-bottom);
			/* #endif */

			&.mask {
				-webkit-mask: -webkit-gradient(linear,left 30%,left top,from(#000),to(transparent));
			}
		}

		.msg-container {
			width: 100%;
			padding: 10rpx;
		}

		.msg-item {
			width: 100%;
			margin-bottom: 10rpx;
			color: #fff;
			font-size: 28rpx;
			padding: 10rpx 0;
			word-wrap: break-word;
			word-break: break-all;

			/* #ifdef MP-WEIXIN */
			display: block;
			box-sizing: border-box;
			/* #endif */

			.username {
				font-weight: 500;
				color: #77c4bc;
				padding-right: 10rpx;
			}

			.message-text {
				display: inline;
				word-wrap: break-word;
				word-break: break-all;
			}

			&.system {
				color: #77c4bc;
				font-size: 26rpx;
				
				.system-text {
					/* #ifdef MP-WEIXIN */
					display: block;
					white-space: normal;
					word-wrap: break-word;
					word-break: break-all;
					/* #endif */
				}
			}

			.system {
				color: #77c4bc;
				font-size: 26rpx;
			}
		}
	}

	.flex-column {
		flex-direction: column;
	}

	.flex {
		display: flex;
		justify-content: flex-start;
		align-items: flex-start;
	}

	/* #ifdef MP-WEIXIN */
	:deep(.msg-scroll) {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
	/* #endif */
</style>
