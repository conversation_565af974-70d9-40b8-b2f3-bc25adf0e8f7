<template>
    <view>
        <block v-if="isload">
            <view v-if="info.id && info.status==2" style="color:red;padding:10rpx 30rpx;margin-top:20rpx">
                <parse :content="bset.verify_reject || '审核未通过：'"/>{{info.reason}}，请修改后重新提交
            </view>
            <view v-if="info.id && info.status==0" style="color:red;padding:10rpx 30rpx;margin-top:20rpx">
                <parse :content="bset.verify_notice || '您的团长申请已提交成功，请耐心等待审核，平台将于7个工作日内联系您核实信息，请留意来电'"/>
            </view>
            <view v-if="info.id && info.status==1" style="color:red;padding:10rpx 30rpx;margin-top:20rpx">
                <parse :content="bset.verify_success || '恭喜您审核通过！'"/>
            </view>
            <view v-if="!info.id || info.status==2 || info.status==0" style="color:red;padding:10rpx 30rpx;margin-top:20rpx">
                <parse :content="bset.verify_normal || '温馨提示：审核通过后可完成入驻'"/>
            </view>
            <form @submit="subform">
              <!--  <view class="apply_box">
                    
                   <view class="apply_item">
                        <view>申请人姓名<text style="color:red"> *</text></view>
                        <view class="flex-y-center"><input type="text" name="linkman" :value="info.linkman" placeholder="请填写姓名"></input></view>
                    </view>
                   
                    <view class="apply_item">
                        <view>申请人电话<text style="color:red"> *</text></view>
                        <view class="flex-y-center"><input type="text" name="linktel" :value="info.linktel" placeholder="请填写手机号码"></input></view>
                    </view>
                </view> -->
                
                <view class="apply_box">
                    <view class="apply_item">
                        <view>团长名称<text style="color:red"> *</text></view>
                        <view class="flex-y-center"><input type="text" name="name" :value="info.name" placeholder="请输入团长名称"></input></view>
                    </view>
					<view class="apply_item">
					    <view>团长电话<text style="color:red"> *</text></view>
					    <view class="flex-y-center"><input type="text" name="tel" :value="info.tel" placeholder="请填写手机号码"></input></view>
					</view>
                   <!-- <view class="apply_item">
                        <view>团长描述<text style="color:red"> *</text></view>
                        <view class="flex-y-center"><input type="text" name="desc" :value="info.desc" placeholder="请输入团长描述"></input></view>
                    </view> -->
                    
                    <view class="apply_item">
                        <view>团长微信<text style="color:red"> *</text></view>
                        <view class="flex-y-center"><input type="text" name="wxid" :value="info.wxid" placeholder="请填写微信号"></input></view>
                    </view>
                
                </view>
				<view class="apply_box">
				    <view class="apply_item" style="border-bottom:0"><text>团长头像<text style="color:red"> *</text></text></view>
				    <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
				        <view v-for="(item, index) in pic" :key="index" class="layui-imgbox">
				            <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pic"><image src="/static/img/ico-del.png"></image></view>
				            <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
				        </view>
				        <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pic" v-if="pic.length==0"></view>
				    </view>
				    <input type="text" hidden="true" name="pic" :value="pic.join(',')" maxlength="-1"></input>
				</view>
                <view class="apply_box">
                    <view class="apply_item" style="border-bottom:0">
                        <text>申请条件<text style="color:red"> *</text></text>
                    </view>
                    
                    <checkbox-group @change="conditionChange">
                        <view class="condition-item">
                            <label class="flex-y-center">
                                <checkbox value="1" :checked="conditions.hasGroup"/>
                                <text>自有微信群50人以上</text>
                            </label>
                            <view class="group-proof" v-if="conditions.hasGroup">
                                <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
                                    <view v-for="(item, index) in groupPics" :key="index" class="layui-imgbox">
                                        <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="groupPics"><image src="/static/img/ico-del.png"></image></view>
                                        <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
                                    </view>
                                    <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="groupPics" v-if="groupPics.length<3"></view>
                                </view>
                            </view>
                        </view>
    
                        <view class="condition-item">
                            <label class="flex-y-center">
                                <checkbox value="2" :checked="conditions.hasOnlineShop"/>
                                <text>自有线上店铺</text>
                            </label>
                            <view v-if="conditions.hasOnlineShop">
                                <view class="apply_item">
                                    <view>店铺链接<text style="color:red"> *</text></view>
                                    <view class="flex-y-center">
                                        <input 
                                            type="text" 
                                            name="shopUrl" 
                                            :value="info.shop_url" 
                                            placeholder="请填写店铺链接"
                                            placeholder-class="placeholder-style"
                                        ></input>
                                    </view>
                                </view>
                            </view>
                        </view>
    
                        <view class="condition-item">
                            <label class="flex-y-center">
                                <checkbox value="3" :checked="conditions.hasOfflineShop"/>
                                <text>自有线下店铺</text>
                            </label>
                            <view v-if="conditions.hasOfflineShop">
                                <view class="apply_item">
                                    <view>店铺地址<text style="color:red"> *</text></view>
                                    <view class="flex-y-center">
                                        <input 
                                            type="text" 
                                            name="shopAddress" 
                                            :value="info.shop_address" 
                                            placeholder="请填写店铺地址"
                                            placeholder-class="placeholder-style"
                                        ></input>
                                    </view>
                                </view>
								 <view>上传营业执照<text style="color:red"> *</text>
                                <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
                                    <view v-for="(item, index) in licensePics" :key="index" class="layui-imgbox">
                                        <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="licensePics"><image src="/static/img/ico-del.png"></image></view>
                                        <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
                                    </view>
                                    <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="licensePics" v-if="licensePics.length<2"></view>
                                </view>
                            </view>
                        </view>  </view>
                    </checkbox-group>
                    <input type="text" hidden="true" name="groupPics" :value="groupPics.join(',')" maxlength="-1"></input>
                    <input type="text" hidden="true" name="licensePics" :value="licensePics.join(',')" maxlength="-1"></input>
                </view>
               
              <!--  <view class="apply_box">
                    <view class="apply_item" style="border-bottom:0"><text>团长照片(3-5张)<text style="color:red"> *</text></text></view>
                    <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
                        <view v-for="(item, index) in pics" :key="index" class="layui-imgbox">
                            <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pics"><image src="/static/img/ico-del.png"></image></view>
                            <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
                        </view>
                        <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pics" v-if="pics.length<5"></view>
                    </view>
                    <input type="text" hidden="true" name="pics" :value="pics.join(',')" maxlength="-1"></input>
                </view> -->
                
                <view class="apply_box">
                    <view class="apply_item">
                        <text>登录用户名<text style="color:red"> *</text></text>
                        <view class="flex-y-center"><input type="text" name="un" :value="info.un" placeholder="请填写登录账号" autocomplete="off"></input></view>
                    </view>
                    <view class="apply_item">
                        <text>登录密码<text style="color:red"> *</text></text>
                        <view class="flex-y-center"><input type="password" name="pwd" :value="info.pwd" placeholder="请填写登录密码" autocomplete="off"></input></view>
                    </view>
                    <view class="apply_item">
                        <text>确认密码<text style="color:red"> *</text></text>
                        <view class="flex-y-center"><input type="password" name="repwd" :value="info.repwd" placeholder="请再次填写密码"></input></view>
                    </view>
                </view>
                
                
                <block v-if="bset.xieyi_show==1">
                <view class="flex-y-center" style="margin-left:20rpx;color:#999" v-if="!info.id || info.status==2">
                    <checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox value="1" :checked="isagree"></checkbox>阅读并同意</label></checkbox-group>
                    <text style="color:#666" @tap="showxieyiFun">《团长入驻协议》</text>
                </view>
                </block>
                <view style="padding:30rpx 0"><button v-if="!info.id || info.status==2" form-type="submit" class="set-btn" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">提交申请</button>
    </view>
            </form>
            
            <view id="xieyi" :hidden="!showxieyi" style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)">
                <view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px">
                    <view style="overflow:scroll;height:100%;">
                        <parse :content="bset.xieyi"/>
                    </view>
                    <view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center" @tap="hidexieyi">已阅读并同意</view>
                </view>
            </view>
        </block>
        <loading v-if="loading"></loading>
        <dp-tabbar :opt="opt"></dp-tabbar>
        <popmsg ref="popmsg"></popmsg>
    </view>
    </template>
    
    <script>
    var app = getApp();
    
    export default {
      data() {
        return {
                opt:{},
                loading:false,
          isload: false,
                menuindex:-1,
                
                pre_url:app.globalData.pre_url,
          datalist: [],
          pagenum: 1,
          cateArr: [],
          cindex: 0,
          isagree: false,
          showxieyi: false,
                pic:[],
                pics:[],
                zhengming:[],
          info: {},
                bset:{},
          latitude: '',
          longitude: '',
          address:'',
          conditions: {
            hasGroup: false,
            hasOnlineShop: false,
            hasOfflineShop: false
          },
          groupPics: [],
          licensePics: [],
        };
      },
    
      onLoad: function (opt) {
            this.opt = app.getopts(opt);
            this.getdata();
      },
        onPullDownRefresh: function () {
            this.getdata();
        },
      methods: {
            getdata: function () {
                var that = this;
                that.loading = true;
                app.get('ApiTuanzhang/apply', {}, function (res) {
                    console.log('完整的info对象:', res.info);
                    console.log('status值:', res.info?.status);
                    console.log('status类型:', typeof res.info?.status);
                    
                    that.loading = false;
                    if (res.status == 2) {
                        app.alert(res.msg, function () {
                            app.goto('/pagesExa/tuanzhangadmin/index/index', 'redirect');
                        })
                        return;
                    }
                    uni.setNavigationBarTitle({
                        title: res.title
                    });
                    var clist = res.clist;
                    var cateArr = [];
                    for (var i in clist) {
                        cateArr.push(clist[i].name);
                    }
                    var pics = res.info ? res.info.pics : '';
                    if (pics) {
                        pics = pics.split(',');
                    } else {
                        pics = [];
                    }
                    var zhengming = res.info ? res.info.zhengming : '';
                    if (zhengming) {
                        zhengming = zhengming.split(',');
                    } else {
                        zhengming = [];
                    }
                    that.clist = res.clist
                    that.bset = res.bset
                    that.info = res.info
                    that.info.linktel = res.info.linktel;
                    that.address = res.info.address;
                    that.latitude = res.info.latitude;
                    that.longitude = res.info.longitude;
                    that.cateArr = cateArr;
                    that.pic = res.info.logo ? [res.info.logo] : [];
                    that.pics = pics;
                    that.zhengming = zhengming;
                    that.loaded();
                    
                    if(res.info && res.info.conditions) {
                        that.conditions = res.info.conditions;
                    }
                    
                    that.groupPics = res.info.group_pics ? res.info.group_pics.split(',') : [];
                    that.licensePics = res.info.license_pics ? res.info.license_pics.split(',') : [];
                    
                    that.info = res.info;
                    that.info.shopUrl = res.info.shop_url || '';
                    that.info.shopAddress = res.info.shop_address || '';
                });
            },
        cateChange: function (e) {
          this.cindex = e.detail.value;
        },
        locationSelect: function () {
          var that = this;
          uni.chooseLocation({
            success: function (res) {
              that.info.address = res.name;
                        that.info.latitude = res.latitude;
              that.info.longitude = res.longitude;
              that.address = res.name;
              that.latitude = res.latitude;
              that.longitude = res.longitude;
            }
          });
        },
        subform: function (e) {
          var that = this;
          var info = e.detail.value;
          // if (info.linkman == '') {
          //   app.error('请填写联系人姓名');
          //   return false;
          // }
          // if (info.linktel == '') {
          //   app.error('请填写联系人电话');
          //   return false;
          // }
          if (info.tel == '') {
            app.error('请填写客服电话');
            return false;
          }
          if (info.name == '') {
            app.error('请填写团长姓名');
            return false;
          }
          if (info.zuobiao == '') {
            //app.error('请选择店铺标');
            //return false;
          }
          // if (info.address == '') {
          //   app.error('请填写店铺地址');
          //   return false;
          // }
          if (info.pic == '') {
            app.error('请上传团长头像');
            return false;
          }
          // if (info.pics == '') {
          //   app.error('请上传团长照片');
          //   return false;
          // }
          if (info.zhengming == '') {//$.error('请上传证明材料');return false;
          }
          if (info.un == '') {
            app.error('请填写登录账号');
            return false;
          }
          if (info.pwd == '') {
            app.error('请填写登录密码');
            return false;
          }
          var pwd = info.pwd;
          if (pwd.length < 6) {
            app.error('密码不能小于6位');
            return false;
          }
          if (info.repwd != info.pwd) {
            app.error('两次输入密码不一致');
            return false;
          } //if(!/(^0?1[3|4|5|6|7|8|9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$)/.test(tel)){
          //	dialog('手机号格式错误');return false;
          //}
          info.address = that.address || '';
          info.latitude = that.latitude || '';
          info.longitude = that.longitude || '';
          if (that.bset && that.bset.xieyi_show == 1 && !that.isagree) {
            app.error('请先阅读并同意商户入驻协议');
            return false;
          }
          if (that.clist && that.clist[that.cindex]) {
            info.cid = that.clist[that.cindex].id;
          }
          if (that.info && that.info.id) {
            info.id = that.info.id;
          }
                app.showLoading('提交中');
          app.post("ApiTuanzhang/apply", {info: info}, function (res) {
                    app.showLoading(false);
            if (res.status == 1) {
              app.success(res.msg);
              setTimeout(function () {
                if (res.after_register_url) {
                  let jumpUrl = res.after_register_url;
                  if (!jumpUrl.startsWith('/')) {
                    jumpUrl = '/' + jumpUrl;
                  }
                  uni.redirectTo({
                    url: jumpUrl,
                    fail: function() {
                      uni.switchTab({
                        url: jumpUrl,
                        fail: function() {
                          app.goto(app.globalData.indexurl);
                        }
                      });
                    }
                  });
                } else {
                  app.goto(app.globalData.indexurl);
                }
              }, 1500);
            } else {
              app.error(res.msg);
            }
          });
        },
        isagreeChange: function (e) {
          console.log(e.detail.value);
          var val = e.detail.value;
          if (val.length > 0) {
            this.isagree = true;
          } else {
            this.isagree = false;
          }
        },
        showxieyiFun: function () {
          this.showxieyi = true;
        },
        hidexieyi: function () {
          this.showxieyi = false;
                this.isagree = true;
        },
            uploadimg:function(e){
                var that = this;
                var field= e.currentTarget.dataset.field
                var pics = that[field]
                if(!pics) pics = [];
                app.chooseImage(function(urls){
                    for(var i=0;i<urls.length;i++){
                        pics.push(urls[i]);
                    }
                    if(field == 'pic') that.pic = pics;
                    if(field == 'pics') that.pics = pics;
                    if(field == 'zhengming') that.zhengming = pics;
                },1)
            },
            removeimg:function(e){
                var that = this;
                var index= e.currentTarget.dataset.index
                var field= e.currentTarget.dataset.field
                if(field == 'pic'){
                    var pics = that.pic
                    pics.splice(index,1);
                    that.pic = pics;
                }else if(field == 'pics'){
                    var pics = that.pics
                    pics.splice(index,1);
                    that.pics = pics;
                }else if(field == 'zhengming'){
                    var pics = that.zhengming
                    pics.splice(index,1);
                    that.zhengming = pics;
                }
            },
        conditionChange(e) {
            const values = e.detail.value;
            this.conditions = {
                hasGroup: values.includes('1'),
                hasOnlineShop: values.includes('2'),
                hasOfflineShop: values.includes('3')
            };
        },
      }
    }
    </script>
    <style>
    radio{transform: scale(0.6);}
    checkbox{transform: scale(0.6);}
    .apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
    .apply_title { background: #fff}
    .apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}
    .apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}
    
    .apply_item {
      line-height: 100rpx; 
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #eee;
    }
    
    .apply_item input {
      width: 100%;
      height: 100rpx;
      border: none;
      color: #111;
      font-size: 28rpx;
      text-align: right;
    }
    
    .placeholder-style {
      color: #999999;
    }
    
    .set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
    
    .layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
    .layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}
    .layui-imgbox-close image{width:100%;height:100%}
    .layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
    .layui-imgbox-img>image{max-width:100%;}
    .layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
    .uploadbtn{position:relative;height:200rpx;width:200rpx}
    
    .condition-item {
      padding: 20rpx 0;
      border-bottom: 1px solid #eee;
    }
    
    .condition-item:last-child {
      border-bottom: none;
    }
    
    .group-proof {
      margin-top: 20rpx;
      padding-left: 40rpx;
    }
    </style>