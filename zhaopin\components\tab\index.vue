<template>
    <view class="tab-box">
        <scroll-view scrollX class="tabs-list">
            <view class="tabs-list">
                <view
                    @tap="handleTapTab"
                    :class="'ignoreT2 ptp_exposure tab-item ' + (item.key === '4' ? 'anchor' : '') + ' ' + (index === currentActive ? 'active' : '')"
                    :data-index="index"
                    data-ptpid="f6a7-18cd-9f87-3c05"
                    v-for="(item, index) in tabList"
                    :key="index"
                >
                    {{ item.value }}
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script>
export default {
    props: {
        tabList: {
            type: Array,
            default: () => []
        },
        current: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            currentActive: this.current
        };
    },
    methods: {
        handleTapTab(t) {
            const index = t.currentTarget.dataset.index;
            if (this.currentActive !== index) {
                this.currentActive = index;
                this.$emit('tabChange', {detail: {index}});
            }
        }
    },
    watch: {
        current: {
            handler(newVal) {
                this.currentActive = newVal;
            },
            immediate: true
        }
    }
};
</script>

<style lang="scss" scoped>
	@import './index.scss';
</style>
