<template>
<view class="container">
	<view class="search-bar">
		<view class="search-input">
			<image class="search-icon" src="/static/img/search_ico.png"></image>
			<input type="text" v-model="search.orderNo" placeholder="输入进货订单号搜索" @confirm="searchOrders" />
		</view>
		<view class="search-btn" @tap="searchOrders">搜索</view>
	</view>

	<view class="order-list" v-if="orderList.length > 0">
		<view class="order-item" v-for="(item, index) in orderList" :key="index" @tap="selectOrder(item)">
			<view class="order-header">
				<text class="order-no">订单号: {{item.order_no}}</text>
				<text class="order-status" :style="{color: getStatusColor(item.status)}">{{getStatusText(item.status)}}</text>
			</view>
			<view class="order-info">
				<view class="info-row">
					<text class="info-label">总金额:</text>
					<text class="info-value">￥{{item.total_price}}</text>
					<text class="info-label">下单时间:</text>
					<text class="info-value">{{item.create_time}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">商品数量:</text>
					<text class="info-value">{{item.product_count}}种</text>
					<text class="arrow-right">></text>
				</view>
			</view>
		</view>
	</view>
	
	<nodata v-if="orderList.length === 0 && !loading"></nodata>
	<loading v-if="loading"></loading>
	<nomore v-if="nomore"></nomore>
	
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      loading: false,
      nomore: false,
      pagenum: 1,
      orderList: [],
      search: {
        orderNo: ''
      }
    };
  },
  onLoad: function(opt) {
    this.opt = app.getopts(opt);
    this.getOrderList();
  },
  onReachBottom: function() {
    if (!this.nomore && !this.loading) {
      this.pagenum++;
      this.getOrderList(true);
    }
  },
  methods: {
    getOrderList: function(loadmore) {
      if (!loadmore) {
        this.pagenum = 1;
        this.orderList = [];
        this.nomore = false;
      }
      
      var that = this;
      that.loading = true;
      
      app.post('ApiAdminPurchase/getPurchaseOrdersForReturn', {
        pagenum: that.pagenum,
        order_no: that.search.orderNo || ''
      }, function(res) {
        that.loading = false;
        
        if (res.code === 0) {
          var data = res.data.list;
          
          if (that.pagenum === 1) {
            that.orderList = data;
            if (data.length === 0) {
              that.nomore = true;
            }
          } else {
            if (data.length === 0) {
              that.nomore = true;
            } else {
              that.orderList = that.orderList.concat(data);
            }
          }
        } else {
          app.error(res.msg);
        }
      });
    },
    searchOrders: function() {
      this.getOrderList();
    },
    getStatusText: function(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回',
        3: '已完成'
      };
      return statusMap[status] || '未知状态';
    },
    getStatusColor: function(status) {
      const colorMap = {
        0: '#FF9800', // 待审核 - 橙色
        1: '#4CAF50', // 已通过 - 绿色
        2: '#F44336', // 已驳回 - 红色
        3: '#2196F3'  // 已完成 - 蓝色
      };
      return colorMap[status] || '#999999';
    },
    selectOrder: function(order) {
      // 跳转到退货创建页面
      app.goto('create?purchase_id=' + order.id);
    }
  }
};
</script>

<style>
.container{ width:100%; }

.search-bar{ display:flex; align-items:center; padding:20rpx; background:#fff; }
.search-input{ flex:1; height:70rpx; display:flex; align-items:center; background:#f5f5f5; border-radius:35rpx; padding:0 20rpx; }
.search-icon{ width:28rpx; height:28rpx; margin-right:10rpx; }
.search-input input{ flex:1; height:100%; font-size:28rpx; }
.search-btn{ width:100rpx; height:70rpx; line-height:70rpx; text-align:center; font-size:28rpx; margin-left:20rpx; color:#1989fa; }

.order-list{ padding:10rpx 20rpx; }
.order-item{ background:#fff; border-radius:8rpx; margin-bottom:20rpx; padding:20rpx; box-shadow:0 2px 5px rgba(0,0,0,0.05); }
.order-header{ display:flex; justify-content:space-between; padding-bottom:15rpx; border-bottom:1px solid #f0f0f0; }
.order-no{ font-size:28rpx; color:#333; font-weight:bold; }
.order-status{ font-size:28rpx; }

.order-info{ padding:15rpx 0; }
.info-row{ display:flex; align-items:center; margin-bottom:10rpx; }
.info-label{ font-size:26rpx; color:#666; width:140rpx; }
.info-value{ font-size:26rpx; color:#333; flex:1; }
.arrow-right{ font-size:32rpx; color:#999; }
</style> 