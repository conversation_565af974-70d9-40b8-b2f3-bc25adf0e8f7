<template>
<view class="container">
	<block v-if="isload">
		<view class="summary-box">
			<view class="filter-bar">
				<text :class="['filter-item', !isDateFilter ? 'active' : '']" @tap="toggleDateFilter(false)">全部</text>
				<text :class="['filter-item', isDateFilter ? 'active' : '']" @tap="toggleDateFilter(true)">按月份</text>
				<picker v-if="isDateFilter" mode="date" fields="month" :value="currentDate" @change="dateChange" class="date-picker">
					<text class="picker-text">{{currentDate || '选择月份'}}</text>
				</picker>
			</view>
			<view class="summary-list">
				<view class="summary-item">
					<text class="label">已提现总额</text>
					<text class="value">{{summary.withdrawed || 0}}元</text>
				</view>
				<view class="summary-item">
					<text class="label">待提现(审核中)</text>
					<text class="value">{{summary.pending || 0}}元</text>
				</view>
				<view class="summary-item">
					<text class="label">待提现(已审核)</text>
					<text class="value">{{summary.approved || 0}}元</text>
				</view>
				<view class="summary-item">
					<text class="label">冻结金额</text>
					<text class="value">{{summary.frozen || 0}}元</text>
				</view>
			</view>
		</view>
		<view class="content">
			<view v-for="(item, index) in datalist" :key="index" class="item">
				<view class="f1">
						<text class="t1">提现金额：{{item.money}}元</text>
						<text class="t2">{{dateFormat(item.createtime,'Y-m-d H:i')}}</text>
				</view>
				<view class="f3">
						<text class="t1" v-if="item.status==0">审核中</text>
						<text class="t1" v-if="item.status==1">已审核</text>
						<text class="t2" v-if="item.status==2">已驳回</text>
						<text class="t1" v-if="item.status==3">已打款</text>
				</view>
			</view>
		</view>
		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			canwithdraw:false,
			textset:{},
      st: 0,
      datalist: [],
      pagenum: 1,
			nodata:false,
      nomore: false,
			currentDate: '',
			summary: {
				withdrawed: 0,
				pending: 0,
				approved: 0,
				frozen: 0
			},
			isDateFilter: false,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st || 0;
		const now = new Date();
		this.currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
		this.getdata();
		this.getSummary();
  },
	onPullDownRefresh: function () {
		this.getdata(true);
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var st = that.st;
			that.nodata = false;
			that.nomore = false;
			that.loading = true;
      app.post('ApiAdminFinance/bwithdrawlog', {st: st,pagenum: pagenum}, function (res) {
				that.loading = false;
        var data = res.data;
        if (pagenum == 1) {
					that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    getSummary() {
			const that = this;
			app.post('ApiAdminFinance/withdrawSummary', {
				date: that.isDateFilter ? that.currentDate : ''
			}, function(res) {
				if(res.error == 0) {
					that.summary = res.data;
				}
			});
		},
		toggleDateFilter(value) {
			this.isDateFilter = value;
			this.getSummary();
		},
		dateChange(e) {
			this.currentDate = e.detail.value;
			this.getSummary();
		},
		loaded() {
			this.isload = true;
		}
  }
};
</script>
<style>
.container{ 
	width:100%;
	display:flex;
	flex-direction:column;
	background: #f8f8f8;
	min-height: 100vh;
}
.content{ 
	width:94%;
	margin:0 3% 20rpx 3%;
}
.content .item{
	width:100%;
	background:#fff;
	margin:20rpx 0;
	padding:30rpx;
	border-radius:16rpx;
	display:flex;
	align-items:center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}
.content .item:last-child{
	border:0
}
.content .item .f1{
	width:500rpx;
	display:flex;
	flex-direction:column;
	gap: 12rpx;
}
.content .item .f1 .t1{
	color:#333;
	font-size:32rpx;
	word-break:break-all;
	overflow:hidden;
	text-overflow:ellipsis;
	font-weight: 500;
}
.content .item .f1 .t2{
	color:#999;
	font-size: 26rpx;
}
.content .item .f3{ 
	flex:1;
	width:200rpx;
	font-size:28rpx;
	text-align:right
}
.content .item .f3 .t1{
	color:#03bc01;
	background: rgba(3,188,1,0.1);
	padding: 8rpx 20rpx;
	border-radius: 24rpx;
}
.content .item .f3 .t2{
	color:#ff3b30;
	background: rgba(255,59,48,0.1);
	padding: 8rpx 20rpx;
	border-radius: 24rpx;
}

.data-empty{background:#fff}

.summary-box {
	width: 94%;
	margin: 20rpx 3%;
	background: linear-gradient(135deg, #ffffff 0%, #f9f9f9 100%);
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}
.filter-bar {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f5f5f5;
}
.filter-item {
	padding: 12rpx 32rpx;
	font-size: 28rpx;
	color: #666;
	background: #f5f5f5;
	border-radius: 32rpx;
	margin-right: 20rpx;
	transition: all 0.3s;
}
.filter-item.active {
	color: #fff;
	background: #03bc01;
	box-shadow: 0 4rpx 12rpx rgba(3,188,1,0.2);
}
.summary-list {
	padding: 10rpx 0;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
}
.summary-item {
	display: flex;
	flex-direction: column;
	padding: 20rpx;
	background: #f8f8f8;
	border-radius: 12rpx;
	transition: all 0.3s;
}
.summary-item:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.04);
}
.summary-item .label {
	color: #999;
	font-size: 26rpx;
	margin-bottom: 12rpx;
}
.summary-item .value {
	color: #03bc01;
	font-size: 36rpx;
	font-weight: 600;
}
.date-picker {
	flex: 1;
	text-align: right;
}
.picker-text {
	color: #666;
	font-size: 28rpx;
	padding: 12rpx 32rpx;
	background: #f5f5f5;
	border-radius: 32rpx;
	display: inline-flex;
	align-items: center;
}
.picker-text::after {
	content: '';
	display: inline-block;
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 10rpx 8rpx 0 8rpx;
	border-color: #999 transparent transparent transparent;
	margin-left: 12rpx;
}
</style>