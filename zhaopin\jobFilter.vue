<template>
  <view class="job-filter">
    <scroll-view scroll-y class="filter-content">
      <!-- 期望城市移到第一个位置 -->
      <view class="filter-section">
        <view class="section-title">
          <view class="title-line" :style="{backgroundColor: t('color1')}"></view>
          <text>期望城市</text>
        </view>
        <view class="location-row">
          <text class="location-hint">选择你想要工作的区域</text>
          <view class="location-selectors">
            <view class="selector-item">
              <picker @change="onProvinceChange" :value="provinceIndex" :range="provinceList" range-key="name">
                <view class="picker-content">
                  <text>{{currentProvinceName || '江苏省'}}</text>
                  <text class="arrow-down">▼</text>
                </view>
              </picker>
            </view>
            <view class="selector-item">
              <picker @change="onCityChange" :value="cityIndex" :range="cityList" range-key="name" :disabled="!currentProvince">
                <view class="picker-content">
                  <text>{{currentCityName || '苏州市'}}</text>
                  <text class="arrow-down">▼</text>
                </view>
              </picker>
            </view>
            <view class="selector-item">
              <picker @change="onDistrictChange" :value="districtIndex" :range="districtList" range-key="name" :disabled="!currentCity">
                <view class="picker-content">
                  <text>{{currentDistrictName || '常熟市'}}</text>
                  <text class="arrow-down">▼</text>
                </view>
              </picker>
            </view>
          </view>
          
          <view class="city-list" v-if="selectedCities.length > 0">
            <view 
              v-for="(city, index) in selectedCities" 
              :key="index"
              class="city-tag"
            >
              {{ city }}
              <text class="delete-icon" @tap="removeCity(index)">×</text>
            </view>
          </view>
          
          <view class="add-city-btn" @tap="addSelectedCity" :style="{
            backgroundColor: t('color1'),
            color: '#ffffff'
          }">
            <text class="add-icon" style="color: #ffffff;">+</text>
            <text>确定</text>
          </view>
        </view>
      </view>

      <!-- 工作形式和结算方式 -->
      <view class="filter-section">
        <view class="section-title">
          <view class="title-line" :style="{backgroundColor: t('color1')}"></view>
          <text>工作形式</text>
        </view>
        <view class="option-list">
          <view 
            v-for="(mode, index) in workModeOptions" 
            :key="index"
            class="option-item"
            :class="{ active: selectedWorkMode === mode.value }"
            @tap="selectWorkMode(mode.value)"
            :style="{
              background: selectedWorkMode === mode.value 
                ? t('color1')
                : '#f8f8f8',
              color: selectedWorkMode === mode.value ? '#fff' : '#666'
            }"
          >
            {{ mode.label }}
          </view>
        </view>
      </view>

      <view class="filter-section">
        <view class="section-title">
          <view class="title-line" :style="{backgroundColor: t('color1')}"></view>
          <text>结算方式</text>
        </view>
        <view class="option-list">
          <view 
            v-for="(payment, index) in paymentOptions" 
            :key="index"
            class="option-item"
            :class="{ active: selectedPayment === payment.value }"
            @tap="selectPayment(payment.value)"
            :style="{
              background: selectedPayment === payment.value 
                ? t('color1')
                : '#f8f8f8',
              color: selectedPayment === payment.value ? '#fff' : '#666'
            }"
          >
            {{ payment.label }}
          </view>
        </view>
      </view>

      <!-- 工作时间 -->
      <view class="filter-section">
        <view class="section-title">
          <view class="title-line" :style="{backgroundColor: t('color1')}"></view>
          <text>工作时间</text>
        </view>
        <view class="worktime-options">
          <view 
            v-for="(time, index) in workTimeOptions" 
            :key="index"
            class="worktime-item"
            :class="{ active: selectedWorkTime === time.value }"
            @tap="selectWorkTime(time.value)"
            :style="{
              background: selectedWorkTime === time.value 
                ? t('color1')
                : '#f8f8f8',
              color: selectedWorkTime === time.value ? '#fff' : '#666'
            }"
          >
            {{ time.label }}
          </view>
        </view>
      </view>

    

      <!-- 薪资范围 -->
      <view class="filter-section">
        <view class="section-title">
          <view class="title-line" :style="{backgroundColor: t('color1')}"></view>
          <text>薪资范围</text>
        </view>
        <view class="salary-selector">
          <view class="range-text">{{formatSalary(salaryRange[0])}} - {{formatSalary(salaryRange[1])}}</view>
          <view class="selector-group">
            <view class="selector-container">
              <picker 
                @change="(e) => handleSalaryPickerChange(e, 0)" 
                :value="salaryStartIndex" 
                :range="salaryStartOptions.map(val => formatSalary(val))"
              >
                <view class="selector-content">
                  <text>{{formatSalary(salaryRange[0])}}</text>
                  <text class="arrow-down">▼</text>
                </view>
              </picker>
            </view>
            <view class="selector-separator">至</view>
            <view class="selector-container">
              <picker 
                @change="(e) => handleSalaryPickerChange(e, 1)" 
                :value="salaryEndIndex" 
                :range="salaryEndOptions.map(val => formatSalary(val))"
              >
                <view class="selector-content">
                  <text>{{formatSalary(salaryRange[1])}}</text>
                  <text class="arrow-down">▼</text>
                </view>
              </picker>
            </view>
          </view>
        </view>
        <view class="salary-options">
          <view 
            v-for="(salary, index) in salaryOptions" 
            :key="index"
            class="salary-item"
            :class="{ active: selectedSalary === salary.value }"
            @tap="selectSalary(salary.value)"
            :style="{
              background: selectedSalary === salary.value 
                ? t('color1')
                : '#f8f8f8',
              color: selectedSalary === salary.value ? '#fff' : '#666'
            }"
          >
            {{ salary.label }}
          </view>
        </view>
      </view>


        <!-- 年龄移动到这里作为最后一项 -->
      <view class="filter-section">
        <view class="section-title">
          <view class="title-line" :style="{backgroundColor: t('color1')}"></view>
          <text>年龄</text>
        </view>
        <view class="location-row">
          <text class="location-hint">请输入您身份证上面的年龄</text>
        <view class="age-input-container">
          <view class="age-input-wrapper single-age">
            <input 
              type="number" 
              v-model="ageValue" 
              placeholder="请输入年龄" 
              class="age-input"
              maxlength="2"
            />
            <text class="age-label">岁</text>
          </view>
        </view>
      </view></view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="footer safe-area-bottom">
      <view class="btn-group">
        <button 
          class="next-btn full-width" 
          @tap="submitFilter"
          :style="{
            background: `linear-gradient(135deg, ${t('color1')}, ${t('color1')}dd)`
          }"
        >
          开始匹配
        </button>
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp()
export default {
  data() {
    return {
      isLoading: true,
      loadRetryCount: 0,
      selectedCities: [],
      areaList: [],
      // 省市区镇选择相关数据
      provinceList: [],
      cityList: [],
      districtList: [],
      provinceIndex: 0,
      cityIndex: 0,
      districtIndex: 0,
      currentProvince: null,
      currentCity: null,
      currentDistrict: null,
      currentProvinceName: '',
      currentCityName: '',
      currentDistrictName: '',
      showAreaPicker: false,
      salaryOptions: [
        { value: 1, label: '3千~5千', min: 3000, max: 5000 },
        { value: 2, label: '4千~6千', min: 4000, max: 6000 },
        { value: 3, label: '5千~7千', min: 5000, max: 7000 },
        { value: 4, label: '6千~8千', min: 6000, max: 8000 },
        { value: 5, label: '7千~9千', min: 7000, max: 9000 },
        { value: 6, label: '8千~1万', min: 8000, max: 10000 },
        { value: 7, label: '9千~1.1万', min: 9000, max: 11000 },
        { value: 8, label: '1万~1.2万', min: 10000, max: 12000 },
        { value: 9, label: '1.2万~1.5万', min: 12000, max: 15000 },
        { value: 10, label: '不限', min: 2000, max: 100000 }
      ],
      selectedSalary: null,
      salaryRange: [2000, 100000],
      salaryStartIndex: 0, // 默认2000元
      salaryEndIndex: 98, // 默认10万元
      salaryStartOptions: Array.from({ length: 99 }, (_, i) => i * 1000 + 2000).filter(val => val <= 100000), // 从2000元到10万元
      salaryEndOptions: Array.from({ length: 99 }, (_, i) => i * 1000 + 2000).filter(val => val <= 100000),
      workModeOptions: [
        { value: 1, label: '长期工' },
        { value: 2, label: '短期工' },
        { value: 3, label: '临时工' }
      ],
      selectedWorkMode: null,
      paymentOptions: [
        { value: 1, label: '月结' },
        { value: 2, label: '周结' },
        { value: 3, label: '日结' },
        { value: 4, label: '完工结' }
      ],
      selectedPayment: null,
      workTimeOptions: [
        { value: 1, label: '白班', start: 8, end: 17 },
        { value: 2, label: '夜班', start: 18, end: 8 },
        { value: 3, label: '两班倒', start: 0, end: 24 },
        { value: 4, label: '不限', start: 0, end: 24 }
      ],
      selectedWorkTime: null,
      workTimeRange: [9, 18],
      
      // 年龄相关数据 - 简化
      ageValue: '',
    }
  },

  watch: {
    jobCategories: {
      handler(newVal) {
        console.log('jobCategories数据变化：', newVal)
        if (newVal && newVal.length > 0) {
          this.isLoading = false
        }
      },
      immediate: true
    }
  },

  onLoad() {
    console.log('页面加载')
    this.getJobCategories()
    // 加载地区数据
    this.getAreaList()
  },

  onShow() {
    console.log('页面显示')
    if (this.jobCategories.length === 0 && !this.isLoading && this.loadRetryCount < 3) {
      console.log('数据为空，尝试重新加载')
      this.loadRetryCount++
      this.getJobCategories()
    }
  },

  onReady() {
    console.log('页面就绪')
    // 确保组件完全挂载后数据依然存在
    if (this.jobCategories.length === 0 && !this.isLoading) {
      console.log('页面就绪时数据为空，尝试重新加载')
      this.getJobCategories()
    }
  },

  methods: {
    getJobCategories() {
      const that = this
      this.isLoading = true
      
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
      
      console.log('开始获取职位类别数据')
      app.post('apiZhaopin/getTypeList', {
        pid: -1
      }, function(res) {
        uni.hideLoading()
        that.isLoading = false
        
        console.log('接口返回数据：', res)
        
        if (res.status === 1 && res.data && Array.isArray(res.data)) {
          const processedData = res.data.map(item => {
            console.log('处理类别项：', item)
            return {
              id: item.id,
              name: item.name,
              description: item.children?.length ? `包含${item.children.length}个细分职位` : '暂无子分类',
              icon: `/static/icons/job-${item.id}.png`,
              children: item.children || [],
              sort: item.sort || 0
            }
          }).sort((a, b) => b.sort - a.sort)
          
          that.jobCategories = processedData
          console.log('处理后的职位类型数据：', that.jobCategories)
          
          // 强制更新视图
          that.$forceUpdate()
          
          // 在下一个时间片段检查数据
          setTimeout(() => {
            if (that.jobCategories.length === 0) {
              console.error('数据加载异常：数据为空')
              uni.showToast({
                title: '数据加载异常，请重试',
                icon: 'none'
              })
            }
          }, 100)
        } else {
          console.error('获取职位类型失败：', res.msg)
          uni.showToast({
            title: res.msg || '获取职位类型失败',
            icon: 'none'
          })
        }
      })
    },

    // 省市区镇选择器相关方法
    onProvinceChange(e) {
      const index = e.detail.value;
      this.provinceIndex = index;
      this.currentProvince = this.provinceList[index];
      this.currentProvinceName = this.currentProvince.name;
      
      // 重置城市和区域
      this.cityList = this.currentProvince.children || [];
      this.cityIndex = 0;
      this.currentCity = null;
      this.currentCityName = '';
      this.districtList = [];
      this.districtIndex = 0;
      this.currentDistrict = null;
      this.currentDistrictName = '';
      
      if (this.cityList.length > 0) {
        this.currentCity = this.cityList[0];
        this.currentCityName = this.currentCity.name;
        this.districtList = this.currentCity.children || [];
        
        if (this.districtList.length > 0) {
          this.currentDistrict = this.districtList[0];
          this.currentDistrictName = this.currentDistrict.name;
        }
      }
    },
    
    onCityChange(e) {
      const index = e.detail.value;
      this.cityIndex = index;
      this.currentCity = this.cityList[index];
      this.currentCityName = this.currentCity.name;
      
      // 重置区域
      this.districtList = this.currentCity.children || [];
      this.districtIndex = 0;
      this.currentDistrict = null;
      this.currentDistrictName = '';
      
      if (this.districtList.length > 0) {
        this.currentDistrict = this.districtList[0];
        this.currentDistrictName = this.currentDistrict.name;
      }
    },
    
    onDistrictChange(e) {
      const index = e.detail.value;
      this.districtIndex = index;
      this.currentDistrict = this.districtList[index];
      this.currentDistrictName = this.currentDistrict.name;
    },
    
    addSelectedCity() {
      if (!this.currentProvince || !this.currentCity || !this.currentDistrict) return;
      
      let fullAddress = `${this.currentProvinceName}${this.currentCityName}${this.currentDistrictName}`;
      
      if (this.selectedCities.length >= 5) {
        uni.showToast({
          title: '最多选择5个城市',
          icon: 'none'
        });
        return;
      }
      
      if (!this.selectedCities.includes(fullAddress)) {
        this.selectedCities.push(fullAddress);
        uni.showToast({
          title: '添加成功',
          icon: 'none'
        });
      } else {
        uni.showToast({
          title: '该地址已添加',
          icon: 'none'
        });
      }
    },

    showCityPicker() {
      if (this.selectedCities.length >= 5) {
        uni.showToast({
          title: '最多选择5个城市',
          icon: 'none'
        });
        return;
      }

      // 如果还没有加载过地区数据，先加载
      if (this.provinceList.length === 0) {
        this.getAreaList();
      }
    },

    getAreaList() {
      const that = this;
      uni.showLoading({
        title: '加载中...',
        mask: true
      });
      
      app.get('apiZhaopin/getAreaList', {}, function(res) {
        uni.hideLoading();
        if (res.status === 1) {
          that.areaList = res.data;
          that.provinceList = res.data;
          
          // 设置默认值
          if (that.provinceList.length > 0) {
            that.currentProvince = that.provinceList[0];
            that.currentProvinceName = that.currentProvince.name;
            that.cityList = that.currentProvince.children || [];
            
            if (that.cityList.length > 0) {
              that.currentCity = that.cityList[0];
              that.currentCityName = that.currentCity.name;
              that.districtList = that.currentCity.children || [];
              
              if (that.districtList.length > 0) {
                that.currentDistrict = that.districtList[0];
                that.currentDistrictName = that.currentDistrict.name;
              }
            }
          }
        } else {
          uni.showToast({
            title: res.msg || '获取地区列表失败',
            icon: 'none'
          });
        }
      });
    },

    removeCity(index) {
      this.selectedCities.splice(index, 1)
    },

    selectSalary(value) {
      this.selectedSalary = value
      const option = this.salaryOptions.find(item => item.value === value)
      if (option) {
        this.salaryRange = [option.min, option.max]
        this.salaryStartIndex = this.salaryStartOptions.indexOf(option.min)
        this.salaryEndIndex = this.salaryEndOptions.indexOf(option.max)
      }
    },

    handleSalaryPickerChange(e, index) {
      const value = this.salaryStartOptions[e.detail.value];
      
      if (index === 0) { // 起始薪资
        this.salaryStartIndex = e.detail.value;
        if (value < this.salaryRange[1]) {
          this.salaryRange.splice(0, 1, value);
        } else {
          // 如果起始薪资大于等于结束薪资，调整结束薪资
          const newEndSalary = Math.min(value + 5000, 100000);
          this.salaryRange.splice(1, 1, newEndSalary);
          this.salaryEndIndex = this.salaryEndOptions.indexOf(newEndSalary);
        }
      } else if (index === 1) { // 结束薪资
        this.salaryEndIndex = e.detail.value;
        const endValue = this.salaryEndOptions[e.detail.value];
        if (endValue > this.salaryRange[0]) {
          this.salaryRange.splice(1, 1, endValue);
        } else {
          // 如果结束薪资小于等于起始薪资，调整起始薪资
          const newStartSalary = Math.max(endValue - 5000, 2000);
          this.salaryRange.splice(0, 1, newStartSalary);
          this.salaryStartIndex = this.salaryStartOptions.indexOf(newStartSalary);
        }
      }
      
      this.selectedSalary = null; // 清除预设选项的选中状态
    },

    selectWorkMode(value) {
      this.selectedWorkMode = value
    },

    selectPayment(value) {
      this.selectedPayment = value
    },

    selectWorkTime(value) {
      this.selectedWorkTime = value;
      const option = this.workTimeOptions.find(item => item.value === value);
      if (option) {
        this.workTimeRange = [option.start, option.end];
      }
    },

    validateAndSubmit() {
      if (this.selectedWorkMode === null || this.selectedPayment === null) {
        uni.showToast({
          title: '请完善工作要求信息',
          icon: 'none'
        })
        return false
      }
      
      if (this.selectedCities.length === 0) {
        uni.showToast({
          title: '请至少选择期望城市',
          icon: 'none'
        })
        return false
      }
      
      return true
    },

    submitFilter() {
      if (!this.validateAndSubmit()) return
      
      // 获取选中项的文字
      const workModeText = this.workModeOptions.find(item => item.value === this.selectedWorkMode)?.label || '';
      const paymentText = this.paymentOptions.find(item => item.value === this.selectedPayment)?.label || '';
      const salaryRangeText = this.selectedSalary 
        ? `${this.formatSalary(this.salaryRange[0])}-${this.formatSalary(this.salaryRange[1])}` 
        : '不限';
      const workTimeText = this.selectedWorkTime 
        ? this.workTimeOptions.find(item => item.value === this.selectedWorkTime)?.label 
        : '不限';
      
      // 添加年龄信息
      const ageRangeText = `${this.ageValue}岁`

      const filterData = {
        workMode: workModeText,
        payment: paymentText,
        cities: this.selectedCities,
        salary: salaryRangeText,
        workTime: workTimeText,
        age: ageRangeText
      }

      uni.showLoading({
        title: '匹配中...',
        mask: true
      })

      const that = this
      app.post('apiZhaopin/jobMatch', filterData, function(res) {
        uni.hideLoading()
        
        if (res.status === 1) {
          uni.setStorageSync('jobMatchFilterData', filterData)
          uni.setStorageSync('jobMatchResult', res)
          
          uni.navigateTo({
            url: '/zhaopin/jobMatch?from=filter'
          })
        } else {
          uni.showToast({
            title: res.msg || '匹配失败，请重试',
            icon: 'none'
          })
        }
      })
    },

    closeAreaPicker() {
      this.showAreaPicker = false
      this.currentProvince = null
      this.currentCity = null
      this.currentDistrict = null
    },

    formatSalary(value) {
      if (value >= 10000) {
        return (value / 10000).toFixed(1).replace(/\.0$/, '') + '万元';
      } else {
        return value + '元';
      }
    },

    handleAgeInputChange(index, value) {
      const ageValue = parseInt(value);
      if (!isNaN(ageValue)) {
        // 确保年龄在合理范围内
        const validAge = Math.min(Math.max(ageValue, 16), 70);
        this.ageRange.splice(index, 1, validAge);
        
        // 确保最小年龄小于最大年龄
        if (index === 0 && this.ageRange[0] > this.ageRange[1]) {
          this.ageRange.splice(1, 1, this.ageRange[0]);
        } else if (index === 1 && this.ageRange[1] < this.ageRange[0]) {
          this.ageRange.splice(0, 1, this.ageRange[1]);
        }
      }
    },
  }
}
</script>

<style lang="scss">
.job-filter {
  min-height: 100vh;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  
  .filter-content {
    flex: 1;
    height: calc(100vh - 120rpx);
    padding: 20rpx;
    box-sizing: border-box;
  }
  
  .filter-section {
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 8rpx;
    padding: 24rpx;
    border: 1rpx solid #eaeaea;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      position: relative;
      padding-left: 20rpx;
      
      .title-line {
        display: inline-block;
        position: relative;
        left: -10rpx;
        margin-right: 10rpx;
        width: 6rpx;
        height: 28rpx;
        border-radius: 3rpx;
        opacity: 0.9;
      }
    }
    
    .option-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      
      .option-item {
        padding: 16rpx 32rpx;
        background-color: #f5f5f5;
        border-radius: 4rpx;
        font-size: 28rpx;
        color: #333333;
        border: 1rpx solid #eaeaea;
        
        &:active {
          opacity: 0.8;
        }
        
        &.active {
          color: #ffffff;
        }
      }
    }
  }
  
  .age-selector,
  .salary-selector,
  .worktime-selector {
    padding: 16rpx 0;
    
    .range-text {
      text-align: center;
      font-size: 30rpx;
      color: #333333;
      margin-bottom: 24rpx;
      font-weight: 500;
    }
    
    .selector-group {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20rpx;
      
      .selector-container {
        flex: 1;
        max-width: 45%;
        
        .selector-content,
        .age-picker-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16rpx 24rpx;
          background-color: #f8f8f8;
          border-radius: 4rpx;
          font-size: 28rpx;
          color: #333333;
          border: 1rpx solid #eaeaea;
        }
        
        .arrow-down {
          margin-left: 8rpx;
          font-size: 22rpx;
          color: #999999;
        }
      }
      
      .selector-separator {
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #999999;
      }
    }
  }
  
  .salary-options,
  .age-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
    margin-top: 24rpx;
    
    .salary-item,
    .age-item {
      text-align: center;
      padding: 16rpx;
      background-color: #f5f5f5;
      border-radius: 4rpx;
      font-size: 28rpx;
      color: #333333;
      border: 1rpx solid #eaeaea;
      
      &:active {
        opacity: 0.8;
      }
      
      &.active {
        color: #ffffff;
      }
    }
  }
  
  .worktime-options {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-top: 16rpx;
    
    .worktime-item {
      padding: 12rpx 24rpx;
      border-radius: 4rpx;
      font-size: 26rpx;
      background-color: #f5f5f5;
      color: #333333;
      border: 1rpx solid #eaeaea;
      min-width: 140rpx;
      text-align: center;
      
      &:active {
        opacity: 0.8;
      }
      
      &.active {
        color: #ffffff;
      }
    }
  }
  
  .location-row {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    
    .location-hint {
      font-size: 26rpx;
      color: #999999;
      margin-bottom: 12rpx;
    }
    
    .location-selectors {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      margin-bottom: 16rpx;
      
      .selector-item {
        position: relative;
        flex: 1;
        min-width: 160rpx;
        
        .picker-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16rpx 24rpx;
          background-color: #ffffff;
          border-radius: 4rpx;
          font-size: 26rpx;
          color: #333333;
          border: 1rpx solid #ddd;
        }
        
        .arrow-down {
          margin-left: 8rpx;
          font-size: 20rpx;
          color: #999999;
        }
      }
    }
    
    .location-near {
      display: none; /* 隐藏定位附近按钮 */
    }
  }
  
  .city-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    
    .city-tag {
      display: flex;
      align-items: center;
      padding: 12rpx 24rpx;
      background-color: #f5f5f5;
      border-radius: 4rpx;
      font-size: 26rpx;
      color: #333333;
      border: 1rpx solid #eaeaea;
      
      .delete-icon {
        margin-left: 12rpx;
        font-size: 28rpx;
        color: #999999;
      }
    }
  }

  .footer {
    padding: 20rpx;
    background-color: #fff;
    border-top: 1rpx solid #eaeaea;
    
    &.safe-area-bottom {
      padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
      padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    }
    
    .btn-group {
      display: flex;
      
      button {
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 4rpx;
        font-size: 30rpx;
        font-weight: 500;
        
        &:active {
          opacity: 0.9;
        }
      }
      
      .next-btn {
        flex: 1;
        color: #fff;
      }
    }
  }

  .add-city-btn {
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx;
    background-color: #ffffff;
    border-radius: 4rpx;
    font-size: 26rpx;
    color: #333333;
    border: 1rpx solid #ddd;
    margin-bottom: 16rpx;
    width: fit-content;
    
    .add-icon {
      margin-right: 8rpx;
      font-size: 30rpx;
      color: #666;
    }
    
    &:active {
      opacity: 0.8;
    }
  }

  .age-input-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    
    .age-input-wrapper {
      display: flex;
      align-items: center;
      background-color: #f8f8f8;
      border-radius: 4rpx;
      padding: 0 20rpx;
      border: 1rpx solid #eaeaea;
      flex: 1;
      
      &.single-age {
        max-width: 60%;
        justify-content: center;
      }
      
      .age-input {
        height: 80rpx;
        flex: 1;
        font-size: 28rpx;
        text-align: center;
      }
      
      .age-label {
        font-size: 24rpx;
        color: #666;
        margin-left: 8rpx;
      }
    }
    
    .age-separator {
      display: none;
    }
  }
}

@media screen and (max-width: 375px) {
  .job-filter {
    .filter-content {
      padding: 16rpx;
    }
  }
}
</style> 