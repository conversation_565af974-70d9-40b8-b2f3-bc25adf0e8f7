# 文件管家 API 接口文档

## 一、接口说明

本文档描述鲸犀商城系统文件管家模块的前端API接口，供小程序、H5等前端应用调用。

### 基础URL


## 二、接口列表

### 1. 获取文件列表

**接口地址**：`ApiFilem/getfilemlist`

**请求方式**：GET

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| pagenum | int | 否 | 页码，默认1 |
| cid | int | 否 | 分类ID |
| bid | int | 否 | 商户ID，默认0 |
| keyword | string | 否 | 搜索关键词 |
| filetype | string | 否 | 文件类型筛选 |
| showprivate | int | 否 | 是否显示私有文件，1-显示（需要登录） |

**返回示例**：

```json
{
  "status": 1,
  "data": [
    {
      "id": 1,
      "name": "示例文档.docx",
      "subname": "这是一个示例文件",
      "filepath": "/upload/files/20230101/1234567890.docx",
      "filetype": "docx",
      "filesize": 1024,
      "filesize_text": "1024KB",
      "filetype_icon": "icon-docx",
      "uploader": "管理员",
      "viewcount": 10,
      "downloads": 5,
      "createtime": "2023-01-01",
      "sort": 100,
      "status": 1,
      "ispublic": 1
    }
  ],
  "title": "文件列表",
  "clist": [
    {
      "id": 1,
      "name": "文档",
      "pic": "",
      "sort": 100,
      "status": 1
    }
  ],
  "set": {
    "pagesize": 20,
    "allow_types": "jpg,jpeg,png,gif,doc,docx,xls,xlsx,pdf,txt,zip,rar"
  }
}
```

### 2. 获取文件详情

**接口地址**：`ApiFilem/detail`

**请求方式**：GET

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| id | int | 是 | 文件ID |

**返回示例**：

```json
{
  "status": 1,
  "detail": {
    "id": 1,
    "name": "示例文档.docx",
    "subname": "这是一个示例文件",
    "filepath": "/upload/files/20230101/1234567890.docx",
    "filetype": "docx",
    "filesize": 1024,
    "filesize_text": "1024KB",
    "filetype_icon": "icon-docx",
    "uploader": "管理员",
    "viewcount": 11,
    "downloads": 5,
    "createtime": "2023-01-01",
    "sort": 100,
    "status": 1,
    "ispublic": 1,
    "showname": 1,
    "showviewcount": 1,
    "showdownloads": 1,
    "showsendtime": 1,
    "showuploader": 1,
    "category": {
      "id": 1,
      "name": "文档",
      "pic": "",
      "sort": 100,
      "status": 1
    },
    "uploader_info": {
      "name": "管理员",
      "headimg": ""
    }
  }
}
```

### 3. 下载文件

**接口地址**：`ApiFilem/download`

**请求方式**：GET

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| id | int | 是 | 文件ID |

**返回示例**：

```json
{
  "status": 1,
  "url": "/upload/files/20230101/1234567890.docx",
  "filename": "1234567890.docx",
  "msg": "获取下载地址成功"
}
```

### 4. 获取文件分类

**接口地址**：`ApiFilem/getcategory`

**请求方式**：GET

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| bid | int | 否 | 商户ID，默认0 |

**返回示例**：

```json
{
  "status": 1,
  "data": [
    {
      "id": 1,
      "name": "文档",
      "pic": "",
      "sort": 100,
      "status": 1,
      "child": [
        {
          "id": 2,
          "name": "Word文档",
          "pic": "",
          "sort": 90,
          "status": 1
        },
        {
          "id": 3,
          "name": "Excel表格",
          "pic": "",
          "sort": 80,
          "status": 1
        }
      ]
    },
    {
      "id": 4,
      "name": "图片",
      "pic": "",
      "sort": 90,
      "status": 1,
      "child": []
    }
  ]
}
```

### 5. 搜索文件

**接口地址**：`ApiFilem/search`

**请求方式**：GET

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| keyword | string | 是 | 搜索关键词 |
| bid | int | 否 | 商户ID，默认0 |

**返回示例**：

```json
{
  "status": 1,
  "data": [
    {
      "id": 1,
      "name": "示例文档.docx",
      "subname": "这是一个示例文件",
      "filepath": "/upload/files/20230101/1234567890.docx",
      "filetype": "docx",
      "filesize": 1024,
      "filesize_text": "1024KB",
      "filetype_icon": "icon-docx",
      "uploader": "管理员",
      "viewcount": 10,
      "downloads": 5,
      "createtime": "2023-01-01",
      "sort": 100,
      "status": 1,
      "ispublic": 1
    }
  ]
}
```

## 三、错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 0 | 接口调用失败 |
| 1 | 接口调用成功 |

## 四、前端调用示例

```

## 五、文件管家数据字段说明

### 文件表(ddwx_filem)

| 字段名 | 类型 | 说明 |
| ----- | ---- | ---- |
| id | int | 文件ID |
| aid | int | 管理员ID |
| bid | int | 商户ID |
| cid | int | 分类ID |
| name | varchar | 文件名称 |
| subname | varchar | 文件描述 |
| filepath | varchar | 文件路径 |
| filetype | varchar | 文件类型 |
| filesize | int | 文件大小(KB) |
| uploader | varchar | 上传者名称 |
| viewcount | int | 查看次数 |
| downloads | int | 下载次数 |
| createtime | int | 创建时间戳 |
| sort | int | 排序序号 |
| status | tinyint | 状态(0-禁用,1-启用,2-驳回) |
| ispublic | tinyint | 是否公开(0-私有,1-公开) |
| showname | tinyint | 显示名称 |
| showviewcount | tinyint | 显示查看量 |
| showdownloads | tinyint | 显示下载量 |
| showsendtime | tinyint | 显示发布时间 |
| showuploader | tinyint | 显示上传者 |
| reason | varchar | 驳回原因 |

### 文件分类表(ddwx_filem_category)

| 字段名 | 类型 | 说明 |
| ----- | ---- | ---- |
| id | int | 分类ID |
| aid | int | 管理员ID |
| bid | int | 商户ID |
| pid | int | 父分类ID |
| name | varchar | 分类名称 |
| pic | varchar | 分类图片 |
| sort | int | 排序序号 |
| status | tinyint | 状态(0-禁用,1-启用) |

### 文件设置表(ddwx_filem_set)

| 字段名 | 类型 | 说明 |
| ----- | ---- | ---- |
| id | int | 设置ID |
| aid | int | 管理员ID |
| bid | int | 商户ID |
| pagesize | int | 每页显示数量 |
| is_check | tinyint | 是否需要审核 |
| allow_types | varchar | 允许上传的类型 |
| max_size | int | 最大上传大小(KB) | 