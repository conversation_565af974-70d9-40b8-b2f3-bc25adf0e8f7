<template>
	<view class="container" v-if="!loading && orderDetail.id">
		<!-- 订单状态与基本信息 -->
		<view class="status-section" :style="{background: 'linear-gradient(90deg,' + themeColor + ' 0%,rgba(' + themeColorRgb + ',0.8) 100%)'}">
			<view class="status-text">{{ orderDetail.status_text }}</view>
			<view class="status-desc" v-if="orderDetail.status === 0">请在指定时间内完成支付</view>
			<view class="status-desc" v-if="orderDetail.status === 1 && orderDetail.expires_time_format">有效期至: {{ orderDetail.expires_time_format }}</view>
			<!-- 根据订单状态显示不同提示 -->
			<view class="status-desc" v-if="orderDetail.status === 1 && orderDetail.status_text === '已过期'">此套餐已过期，无法使用</view>
			<view class="status-desc" v-if="orderDetail.status === 1 && !orderDetail.expires_time_format && orderDetail.status_text !== '已过期'">
				套餐有效期: {{ orderDetail.valid_days || 365 }}天
			</view>
		</view>

		<!-- 套餐包含项目 -->
		<view class="items-card" v-if="orderDetail.items && orderDetail.items.length > 0">
			<view class="card-title">套餐包含服务 (共 {{ orderDetail.items.length }} 项)</view>
			<view class="service-item-detail" v-for="(item, index) in orderDetail.items" :key="index">
				<image class="service-pic-detail" :src="fixImageUrl(item.service_pic || item.product_pic)" mode="aspectFill"></image>
				<view class="service-info-detail">
					<view class="service-name-detail">{{ item.service_name || item.product_name }}</view>
					<view class="service-times-detail">
						<text>总次数: {{ item.buy_times || item.total_num }}</text>
						<text class="remain-detail" :style="(item.remain_times || item.remain_num) <= 0 ? 'color:#ccc' : 'color:#ff9900'">剩余: {{ item.remain_times || item.remain_num }}</text>
					</view>
				</view>
				<button v-if="canUseItem(item)" class="use-btn" :style="{background: themeColor}" size="mini" @tap="gotoUseService(item.service_id || item.product_id)">去使用</button>
				<button v-else-if="orderDetail.status === 1 && (item.remain_times || item.remain_num) <= 0" class="use-btn disabled" size="mini">已用完</button>
				<button v-else-if="orderDetail.status === 1 && orderDetail.status_text === '已过期'" class="use-btn disabled" size="mini">已过期</button>
			</view>
		</view>

		<!-- 订单信息 -->
		<view class="info-card">
			<view class="card-title">订单信息</view>
			<view class="info-item">
				<text class="label">订单编号:</text>
				<text class="value">{{ orderDetail.ordernum || orderDetail.id }}</text>
				<button class="copy-btn" size="mini" @tap="copyText(orderDetail.ordernum || orderDetail.id)">复制</button>
			</view>
			<view class="info-item">
				<text class="label">下单时间:</text>
				<text class="value">{{ orderDetail.createtime }}</text>
			</view>
			<view class="info-item" v-if="orderDetail.pay_time">
				<text class="label">支付时间:</text>
				<text class="value">{{ orderDetail.pay_time }}</text>
			</view>
			<view class="info-item">
				<text class="label">联系方式:</text>
				<text class="value">{{ orderDetail.linkman }} {{ orderDetail.tel }}</text>
			</view>
			<view class="info-item">
				<text class="label">支付金额:</text>
				<text class="value" :style="{color: themeColor}">￥{{ orderDetail.total_price }}</text>
			</view>
			<view class="info-item" v-if="orderDetail.remark">
				<text class="label">订单备注:</text>
				<text class="value remark-value">{{ orderDetail.remark }}</text>
			</view>
		</view>

		<!-- 使用记录 -->
		<view class="records-card" v-if="orderDetail.use_records && orderDetail.use_records.length > 0">
			<view class="card-title">使用记录</view>
			<view class="record-item" v-for="(record, index) in orderDetail.use_records" :key="index">
				<view class="record-info">
					<view class="record-service-name">{{ record.product_name || '未知服务' }}</view>
					<view class="record-time">{{ record.use_time }}</view>
				</view>
				<view class="record-status">已使用</view>
			</view>
		</view>

		<!-- 退款信息 -->
		<view class="refund-card" v-if="orderDetail.refund_status && orderDetail.refund_status > 0">
			<view class="card-title">退款信息</view>
			<view class="info-item">
				<text class="label">退款状态:</text>
				<text class="value">{{ orderDetail.refund_status_text }}</text>
			</view>
			<view class="info-item" v-if="orderDetail.refund_reason">
				<text class="label">退款原因:</text>
				<text class="value">{{ orderDetail.refund_reason }}</text>
			</view>
			<view class="info-item" v-if="orderDetail.refund_time">
				<text class="label">申请时间:</text>
				<text class="value">{{ orderDetail.refund_time }}</text>
			</view>
			<view class="info-item" v-if="orderDetail.refund_check_time">
				<text class="label">审核时间:</text>
				<text class="value">{{ orderDetail.refund_check_time }}</text>
			</view>
			<view class="info-item" v-if="orderDetail.refund_check_reason">
				<text class="label">审核备注:</text>
				<text class="value">{{ orderDetail.refund_check_reason }}</text>
			</view>
			<view class="info-item" v-if="orderDetail.refund_money">
				<text class="label">退款金额:</text>
				<text class="value" :style="{color: themeColor}">￥{{ orderDetail.refund_money }}</text>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button v-if="orderDetail.status === 0" class="action-button plain" @tap="cancelOrder(orderDetail.id)">取消订单</button>
			<button v-if="orderDetail.status === 0" class="action-button primary" :style="{background: themeColor}" @tap="gotoPay(orderDetail.payorderid, orderDetail.id)">立即支付</button>
			<button v-if="canRefundDetail(orderDetail.status, orderDetail.refund_status)" class="action-button plain" @tap="applyRefund(orderDetail.id, orderDetail.total_price)">申请退款</button>
			<button v-if="canDelete(orderDetail.status)" class="action-button plain" @tap="deleteOrder(orderDetail.id)">删除订单</button>
		</view>

		<!-- 退款原因输入弹窗 -->
		<uni-popup ref="refundPopup" type="dialog">
			<uni-popup-dialog mode="input" title="申请退款" placeholder="请输入退款原因(选填)" :before-close="true" @confirm="confirmRefund" @close="closeRefundPopup"></uni-popup-dialog>
		</uni-popup>

	</view>
	<view class="loading-container" v-else-if="loading">
		<text>加载中...</text>
	</view>
	<view class="empty-container" v-else>
		<text>未找到订单信息</text>
	</view>
</template>

<script>
export default {
	data() {
		// 避免直接引用可能未初始化的全局app对象
		return {
			themeColor: '#2979ff', // 主题色默认值，将在onLoad中正确初始化
			themeColorRgb: '41,121,255', // 主题色RGB默认值
			orderId: null,
			orderDetail: {},
			loading: true,
			refunding: false, // 防止重复提交退款
			refundReason: '', // 存储退款原因
			refundAmount: 0 // 存储退款金额
		}
	},
	onLoad(options) {
		// 初始化全局app对象和主题色
		const app = getApp();
		if (app && app.globalData && app.globalData.config && app.globalData.config.t) {
			this.themeColor = app.globalData.config.t('color1');
			this.themeColorRgb = app.globalData.config.t('color1rgb');
			console.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][onLoad_001] 主题色初始化成功');
		} else {
			console.warn('2025-01-03 22:55:53,565-INFO-[packageorderdetail][onLoad_002] 无法获取主题色配置，使用默认值');
		}
		
		if (options && options.order_id) {
			this.orderId = options.order_id;
			this.getOrderDetail();
		} else {
			const app = getApp();
			if (app && app.error && app.goback) {
				app.error('缺少订单ID', function() {
					app.goback();
				});
			} else {
				console.error('2025-01-03 22:55:53,565-ERROR-[packageorderdetail][onLoad_003] app对象未初始化');
				uni.showToast({
					title: '缺少订单ID',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
			this.loading = false;
		}
		
		uni.setNavigationBarTitle({
			title: '订单详情'
		});
	},
	// onShow(){
	// 	// 如果需要返回刷新
	// 	if(this.orderId) this.getOrderDetail();
	// },
	methods: {
		getOrderDetail() {
			var that = this;
			that.loading = true;
			
			const app = getApp();
			if (!app || !app.post) {
				console.error('2025-01-03 22:55:53,565-ERROR-[packageorderdetail][getOrderDetail_001] app对象未初始化或post方法不存在');
				that.loading = false;
				uni.showToast({
					title: '获取订单详情失败',
					icon: 'none'
				});
				return;
			}
			
			app.post('ApiYuyuePackage/getPackageOrderDetail', { order_id: that.orderId }, function(res) {
				that.loading = false;
				if (res.status == 1 && res.data) {
					console.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_002] 获取订单数据成功', res.data);
					
					// 处理数据
					let data = res.data;
					
					// 修复过期时间计算问题
					if (data.status === 1) {
						// 获取当前时间戳（秒）
						const now = Math.floor(Date.now()/1000);
						
						// 检查expires_time是否明显错误（为0，或者小于now但订单状态是1，或者日期太早如1971年）
						const isExpireTimeInvalid = 
							data.expires_time === 0 || 
							(data.expires_time < now && data.expires_time < 1672531200); // 2023-01-01的时间戳
						
						// 支付后但expires_time无效的情况，说明后端未设置过期时间或计算错误，使用pay_time+valid_days计算
						if (isExpireTimeInvalid && data.pay_time) {
							console.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_003] 检测到无效的过期时间:', data.expires_time);
							
							// 从支付时间计算过期时间
							const payTimeStamp = new Date(data.pay_time.replace(/-/g, '/')).getTime();
							const validDays = data.valid_days || 365; // 默认365天
							data.expires_time = Math.floor(payTimeStamp/1000) + (validDays * 86400);
							
							console.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_004] 自动计算新的过期时间:', {
								payTime: data.pay_time, 
								payTimeStamp: Math.floor(payTimeStamp/1000),
								validDays: validDays,
								newExpiresTime: data.expires_time,
								currentTime: now
							});
						}
						
						// 格式化过期时间显示(独立于前面的逻辑，确保始终有格式化显示)
						if (data.expires_time > 0) {
							const expireDate = new Date(data.expires_time * 1000);
							data.expires_time_format = expireDate.getFullYear() + '-' + 
								String(expireDate.getMonth() + 1).padStart(2, '0') + '-' + 
								String(expireDate.getDate()).padStart(2, '0') + ' ' +
								String(expireDate.getHours()).padStart(2, '0') + ':' +
								String(expireDate.getMinutes()).padStart(2, '0') + ':' +
								String(expireDate.getSeconds()).padStart(2, '0');
						}
						
						// 重新判断状态文本
						if (data.expires_time > 0 && now > data.expires_time) {
							data.status_text = '已过期';
						} else if (data.remain_services <= 0) {
							data.status_text = '已用完';
						} else {
							data.status_text = '可使用';
						}
						
						console.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_005] 订单状态判断:', {
							expiresTime: data.expires_time,
							currentTime: now,
							remainServices: data.remain_services,
							statusText: data.status_text
						});
					}
					
					// 修复可能的图片路径问题
					if (data.package_pic && data.package_pic.indexOf('https://localhost') === 0) {
						data.package_pic = data.package_pic.replace('https://localhost', '');
					}
					
					// 双重URL问题修复
					if (data.package_pic && data.package_pic.indexOf('https://localhosthttps://') === 0) {
						data.package_pic = data.package_pic.replace('https://localhost', '');
					}
					
					// 处理服务项的字段名称映射
					if (data.items && data.items.length > 0) {
						data.items = data.items.map(item => {
							return {
								...item,
								// 将后端返回的字段名映射到组件中使用的字段名
								service_id: item.product_id,
								service_name: item.product_name || '未命名服务',
								service_pic: that.fixImageUrl(item.product_pic),
								buy_times: item.total_num || 0,
								remain_times: item.remain_num || 0
							};
						});
					}
					
					console.log('2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_006] 数据处理完成', data);
					that.orderDetail = data;
				} else {
					app.error(res.msg || '获取订单详情失败', function() {
						app.goback();
					});
				}
			}, function() {
				that.loading = false;
				app.error('请求失败', function() {
					app.goback();
				});
			});
		},
		
		// 修复图片URL
		fixImageUrl(url) {
			if (!url) return '/static/img/goods-default.png';
			
			// 修复双重URL问题
			if (url.indexOf('https://localhosthttps://') === 0) {
				url = url.replace('https://localhost', '');
			}
			
			// 修复localhost前缀
			if (url.indexOf('https://localhost') === 0) {
				url = url.replace('https://localhost', '');
			}
			
			return url;
		},

		// 判断服务项是否可用
		canUseItem(item){
			// 订单状态为可使用(1) 且 服务项剩余次数>0
			// 以及确认状态文本不是"已过期"
			const notExpired = this.orderDetail.status_text !== '已过期';
			return this.orderDetail.status === 1 && item.remain_times > 0 && notExpired;
		},

		// 去使用服务
		gotoUseService(productId) {
			var that = this;
			// 检查服务是否还有剩余次数
			const serviceItem = that.orderDetail.items.find(item => item.service_id === productId);
			if(!serviceItem || serviceItem.remain_times <= 0){
				const app = getApp();
				app.error('该服务已无剩余次数');
				return;
			}
			
			// 如果订单已过期，不允许使用
			if(that.orderDetail.status_text === '已过期'){
				const app = getApp();
				app.error('套餐已过期，无法使用');
				return;
			}

			const app = getApp();
			
			// 跳转到套餐预约页面
			app.goto('/yuyue/packageappoint?package_order_id=' + that.orderId + '&product_id=' + productId);
		},

		// 复制文本
		copyText(text) {
			const app = getApp();
			app.copy(text);
		},

		// 支付、取消、删除逻辑 (与列表页类似)
		gotoPay(payorderid, orderid){
			const app = getApp();
			if(!payorderid) {
				app.error('支付单号不存在');
				return;
			}
			app.payorder({
				payorderid: payorderid,
				orderid: orderid,
				type: 'yuyue_package',
				success: () => {
					app.success('支付成功');
					this.getOrderDetail(); // 刷新详情
				},
				fail: () => {
					app.error('支付失败或取消');
				}
			});
		},
		cancelOrder(orderId){
			var that = this;
			const app = getApp();
			app.confirm('确定要取消该订单吗？', function(){
				app.showLoading('处理中...');
				app.post('ApiYuyuePackage/cancelOrder', {order_id: orderId}, function(res){
					app.showLoading(false);
					if(res.status == 1){
						app.success('取消成功', function(){
							that.getOrderDetail(); // 刷新详情
						});
					}else{
						app.error(res.msg || '取消失败');
					}
				});
			});
		},
		deleteOrder(orderId){
			var that = this;
			const app = getApp();
			app.confirm('确定要删除该订单吗？删除后不可恢复。', function(){
				app.showLoading('处理中...');
				app.post('ApiYuyuePackage/deleteOrder', {order_id: orderId}, function(res){
					app.showLoading(false);
					if(res.status == 1){
						app.success('删除成功', function(){
							app.goback(); // 删除成功后返回上一页
						});
					}else{
						app.error(res.msg || '删除失败');
					}
				});
			});
		},

		// 判断是否可申请退款 (业务逻辑复杂，具体条件需后端确认)
		canRefundDetail(status, refund_status){
			// 修改判断逻辑，增加对状态文本的判断
			// 已支付(1)，未发起过退款(refund_status为0或null)，且不是已过期状态，且不是已用完状态
			return status === 1 
				&& (!refund_status || refund_status === 0) 
				&& this.orderDetail.status_text !== '已过期'
				&& this.orderDetail.status_text !== '已用完'; // 新增判断条件
		},
		// 判断是否可删除 (通常是已取消、已失效、已退款等终态)
		canDelete(status){
			// 增加对status_text的判断，如果是"已过期"也可以删除
			return status === 2 || status === 5 || this.orderDetail.status_text === '已过期'; 
		},

		// 申请退款
		applyRefund(orderId, amount) {
			// 检查是否已过期
			if(this.orderDetail.status_text === '已过期'){
				const app = getApp();
				app.error('套餐已过期，无法申请退款');
				return;
			}
			
			this.refundAmount = amount; // 存储需要退款的金额
			this.$refs.refundPopup.open();
		},
		closeRefundPopup(){
			this.$refs.refundPopup.close();
		},
		// 确认提交退款申请
		confirmRefund(done, value){
			this.refundReason = value || '用户申请退款'; // 获取输入的退款原因
			
			// 关闭输入框
			done();

			var that = this;
			if(that.refunding) return;
			that.refunding = true;
			
			const app = getApp();
			app.showLoading('提交中...');

			// 调用POST接口提交退款申请
			app.post('ApiYuyuePackage/refund', {
				orderid: that.orderId,
				money: that.refundAmount, // 使用之前获取或存储的金额
				reason: that.refundReason
			}, function(postRes){
				app.showLoading(false);
				that.refunding = false;
				if(postRes.status == 1){
					app.success('退款申请已提交', function(){
						that.getOrderDetail(); // 刷新订单详情
					});
				} else {
					app.error(postRes.msg || '提交失败');
				}
			}, function(){
				app.showLoading(false);
				that.refunding = false;
				app.error('请求失败');
			});
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	padding-bottom: 140rpx; /* 底部操作栏高度 */
}

/* 状态区域 */
.status-section {
	padding: 30rpx;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	color: #fff;
}

.status-text {
	font-size: 34rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.status-desc {
	font-size: 24rpx;
	opacity: 0.9;
}

/* 信息卡片 */
.info-card, .items-card, .records-card, .refund-card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 25rpx;
	margin-bottom: 20rpx;
}

.card-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 25rpx;
	padding-bottom: 15rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 10rpx;
}
.info-item:last-child{
	margin-bottom: 0;
}

.info-item .label {
	width: 150rpx;
	flex-shrink: 0;
	color: #999;
}

.info-item .value {
	flex: 1;
	color: #333;
}
.info-item .remark-value{
	white-space: pre-wrap; /* 保留换行 */
}

.copy-btn {
	margin-left: 15rpx;
	padding: 0 10rpx;
	font-size: 20rpx;
	height: 36rpx;
	line-height: 36rpx;
	background-color: #f0f0f0;
	color: #666;
	border: none;
}
button::after {
    border: none;
}

/* 服务项目 */
.service-item-detail {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx dashed #eee;
}
.service-item-detail:last-child {
	border-bottom: none;
}

.service-pic-detail {
	width: 100rpx;
	height: 100rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.service-info-detail {
	flex: 1;
	overflow: hidden;
}

.service-name-detail {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.service-times-detail {
	font-size: 24rpx;
	color: #999;
}
.service-times-detail .remain-detail {
	margin-left: 20rpx;
	font-weight: bold;
}

.use-btn {
	padding: 0 20rpx;
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	color: #fff;
	border-radius: 25rpx;
	margin-left: 20rpx;
	border: none;
	flex-shrink: 0;
}
.use-btn.disabled{
	background-color: #ccc !important;
	color: #fff;
}

/* 使用记录 */
.record-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1rpx dashed #eee;
}
.record-item:last-child {
	border-bottom: none;
}

.record-info {
	/* flex: 1; */
}

.record-service-name {
	font-size: 26rpx;
	color: #333;
	margin-bottom: 5rpx;
}

.record-time {
	font-size: 22rpx;
	color: #999;
}

.record-status {
	font-size: 24rpx;
	color: #4CAF50; /* 绿色表示已使用 */
	flex-shrink: 0;
}

/* 底部操作栏 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0 20rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	box-sizing: border-box;
	padding-bottom: env(safe-area-inset-bottom); /* iPhone X 适配 */
	height: calc(100rpx + env(safe-area-inset-bottom));
	gap: 15rpx; /* 按钮间距 */
}

.action-button {
	padding: 0 30rpx;
	height: 64rpx;
	line-height: 64rpx;
	font-size: 26rpx;
	border-radius: 32rpx;
	margin: 0;
	border: 1rpx solid #ccc;
	background-color: #fff;
	color: #666;
}
.action-button.primary {
	color: #fff;
	border: none;
}


/* 加载和空状态 */
.loading-container, .empty-container {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 80vh;
	color: #999;
	font-size: 28rpx;
}
</style> 