<template>

	<view class="wrap">

		<view class="top">

			<view class="tl"  @click="showTime()">

				<view>
					<view class="tit"  >{{title}}</view>
					<view class="date" >{{list[ind]}}</view>
				</view>

				<img src="/static/img/arrowright.png">

			</view>


			<scroll-view scroll-x>

				<view style="padding: 10rpx 20rpx;background: #fff;display: flex;">

					<view v-for="(tick, index) in ticketList" :key="index">

						<view :class="'tag_item '+(index== selectColor?'bk':'')" @click="selectTab(index)">
							<view class="tag_icon" :style="'background:'+tick.color"></view>
							<view class="tag_text">￥{{tick.money}}</view>
						</view>
					</view>

				</view>

			</scroll-view>

			<scroll-view scroll-x style="background: #fff;">

				<view style="padding: 10rpx 20rpx;display: flex;">

					<view v-for="(pack, index) in seat_data.packages_list" :key="index">

						<view :class="'tag_item '+(index == selectPage?'bk':'')" @click="selectPack(index)">

							<view style="display: flex;">
								<view v-for="v,y in  parseInt(pack.group_size)" :key="y" class="tag_icon"
									:style="'background:'+pack.color"></view>
							</view>          

							<view class="tag_text">
								￥{{ pack.fixed_price==0?Math.round(pack.money*pack.discount*parseInt(pack.group_size)*100)/100:pack.fixed_price}}
							</view>

							<view class="tp">套票</view>
						</view>

					</view>

				</view>

			</scroll-view>

		</view>
		
		
			<view class="xiao" v-if="showKuang&&platform!='ios'" > 
			
				<view style="transform:scale(0.22);transform-origin:0 0;">
					
					<view  v-for="item,index in cen_list" :key="index">
						 
						 <view style="display: flex;">
							 
							 <view>
						
								 <view  class="row" v-for="xItem,indx in item.yList" :key="indx">
									 
									 <view  v-for="yItem,indy in xItem" :key="indy" >
										 
										 <text class="item_s iconfont icon-danrenzuoweiyishou" :style="'color:'+yItem.color"
										 	v-if="yItem.on&&yItem.show==1" ></text>
										 <text :class="(yItem.color==ticke.color||!ticke.color?'item_s iconfont icon-danrenzuoweiyishou1 ':'item_s iconfont icon-danrenzuoweiyishou1 op')"
										 	:style="'color:'+yItem.color" v-else-if="!yItem.on&&yItem.show==1" ></text>
				
										 <text class="item_s iconfont icon-danrenzuoweiyishou1" v-else-if="yItem.show == 2"></text>
										 <text class="item_s iconfont icon-danrenzuoweiyishou1" :style="'color:#00000000'"
										 	v-else></text>
			
									 </view>	
							
								 </view>
							 
							</view> 
							
						 </view>
						
					</view>
					
				</view>
				
				<view class="kuang" :style="'width:'+k_w+'px;height:'+k_h+'px;'+'left:'+k_x+'px;'+'top:'+k_y+'px'"></view>
		
		 </view>


		<movable-area :style="'margin-top: 200px;width:'+screenWidth+'px'" scale-area>
 
			<movable-view class="max" direction="all" :style="'width:'+screenWidth+'px;height: 800px;'" scale  scale-min="1"
				scale-max="2.5" inertia="true"  @change="moveXy" @scale="moveScale" :scale-value="scaleValue" :x="x" :y="y" >

				<view class="main" @touchend="hideKuang()" @touchmove="onKuang()">

					<view style="border: 1px solid #000;display: flex;padding-bottom: 10px;" v-for="item,index in cen_list" :key="index">

						<view style="width: 100%;">

							<view class="wt-ll">

								<text class="wt" style="border: 1px solid #000;" v-if="index==0">舞台</text>
								<text class="wt" v-else>{{index+1}}楼</text>
								
							</view>

							<view style="display: flex;justify-content: center;">

								<view :style="index==0?'transform: scaleX(0.8);':'transform: scaleX(0.8);'">

									<view class="row" v-for="xItem,indx in item.yList" :key="indx">

										<view v-for="yItem,indy in xItem" :key="indy">

											<text class="item_s iconfont icon-danrenzuoweiyishou" :style="'color:'+yItem.color"
												v-if="yItem.on&&yItem.show==1" @click="selectOne(index,indx,indy)"></text>
											<text :class="(yItem.color==ticke.color||!ticke.color?'item_s iconfont icon-danrenzuoweiyishou1 ':'item_s iconfont icon-danrenzuoweiyishou1 op')"
												:style="'color:'+yItem.color" v-else-if="!yItem.on&&yItem.show==1"
												@click="selectOne(index,indx,indy)"></text>
												
												
											<text class="item_s iconfont icon-danrenzuoweiyishou1" v-else-if="yItem.show == 2"></text>
											<text class="item_s iconfont icon-danrenzuoweiyishou1" :style="'color:#f5f5f500'"
												v-else></text>
										</view>

									</view>

								</view>

							</view>
							

						</view>

					</view>
					
					<view :style="'width:15px;position:fixed;z-index:9;top:0px;'+'left:'+t_x+'px'" v-if="platform!='ios'">
						
						<view  v-for="item,index in cen_list" :key="index" >
							
							<view :class="'rll ' + (index==0?'brt':'') +' '+ (index ==cen_list.length-1?'brb':'')" >
							
								<view class="wt-ll"></view>
							
								<view class="row" v-for="xItem,indx in item.yList" :key="indx">
							
									<view class="lnum">{{indx+1}}</view>
							
								</view>
							
							</view> 
							
						</view>
						
					</view>


					<view class="line"></view>

				</view>


			</movable-view>

		</movable-area> 


		<view class="footer">
			<view class="footer_wrap">
				<view style="padding-bottom: 5px;">选择座位({{ selectCommit.length }})</view>
				<view class="tags_warp" v-if="selectCommit.length>0">
					<view class="select_it" v-for="(item, index) in selectCommit" :key="index">

						<view class="tag_icon" :style="'background:'+item.color"></view>

						<view>
							<view>{{item.cen}}楼</view>
							<view>{{item.y}}排{{item.x}}座</view>
						</view>
						
						<view style="padding: 20rpx;" @click="delSelect(index)">
							
							<image src="../../static/img/close.png" style="width: 10px;height: 10px"></image>
							
						</view>

	
					</view>

				</view>
				<view class="border"></view>
				<view class="footer_btn">
					<view class="btn_1" :style="'color:'+primary_color">
						<view v-if="selectCommit.length">
							￥{{total}}
						</view>
						<view v-else>
							￥ 0
						</view>
					</view>
					<view><button class="btn_2" type="primary" :style="'background:'+primary_color"
							@tap="submit()">提交订单</button></view>
				</view>
			</view>
		</view>


		<uni-popup id="dialogShowCategory" ref="dialogShowCategory" type="bottom" :mask-click="true">

			<view class="view-main">

				<view class="view-title">选择场次<image class="icon" src="../../static/img/close.png" @click="hideDialog()">
				</view>

				<scroll-view scroll-y style="height: 600rpx;padding-top: 30px;">

					<view v-for="item,index in list" :key="index" @click="bindPickerChange(index)">

						<view class="tit-time" :style="'color:'+t('color2')+';background:'+t('color2')+'33'"
							v-if="ind==index">{{item}}</view>

						<view class="tit-time" v-else>{{item}}</view>

					</view>

				</scroll-view>


				<view class="view-btn" :style="{background:t('color2')}" @click="goSeat()">确定选择</view>

			</view>

		</uni-popup>

	</view>

</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				id: '',
				seating_list: '',
				selectCommit: [],
				ticketList: [],
				primary_color: '',
				list: [],
				list_ids: [],
				ind: 0,
				title: '',
				selectColor: -1,
				selectPage: -1,
				ticke: '',

				total: 0,
				date: '',
				seat_data: '',
				cen_list: [],
				screenWidth: 0,

				showKuang: false,
				scaleValue: 1,

				k_w_n: 125, //红框固定长
				k_h_n: 80, //红框固定高
				k_w: 125, //红框长
				k_h: 80, //红框高,
				k_x: 0, //红框x坐标
				k_y: 0, //红框y坐标 

				ll_x: 10,
				moveX: 0,
				moveY: 0,

				scroll_x: 0,
				scroll_y: 0,
				x: 0,
				y: 0,
				
				t_x : 10,
				t_y : 0,
				t_scale : 1,
                platform: '' 
			}
		},
		onLoad: function(opt) {

			let that = this;

			that.id = opt.id;

			that.date = opt.date; 

			that.getdata();
			
			const systemInfo = uni.getSystemInfoSync();
			
			that.screenWidth = systemInfo.windowWidth;
			
			that.platform = systemInfo.platform;
			
			console.log(that.platform)
			
		},
		methods: {
			getdata: function() {
				var that = this;

				app.showLoading('加载中');

				app.get('ApiTheater/getEpisodeInfo', {
					id: that.id,
				}, function(data) {

					app.showLoading(false);


					if (data.status == 1) {

						that.title = data.data.title;

						that.seating_list = data.data.seating_list;

						let arr = [];
						let arr_id = [];

						data.data.seating_list.filter(item => {
							arr.push(item.title);
							arr_id.push(item.id);

							if (that.date == item.title) {
								that.seat_data = item;
							}
						})

						let cens = [];

						let yList = that.seat_data.yList;

						Object.keys(yList).forEach((key) => {

							yList[key].filter(o => {

								cens.push(o.floor);
							})

						});


						let unique = [...new Set(cens)];

						let cl = [];
						unique.filter(item => {
							let obj = {
								cen: item,
								xList: [],
								yList: [],
							}
							cl.push(obj);
						})


						cl.filter(m => {

							Object.keys(yList).forEach((key) => {

								if (yList[key][0].floor == m.cen) {

									m.yList.push(yList[key]);
								}

							});

						});

						that.cen_list = cl;

						that.list = arr;
						that.list_ids = arr_id;
						
						that.list.filter((item,k)=>{
							if(that.date == item){
								that.ind = k; 
							}
						})

						that.ticketList = data.data.price_list;

						that.primary_color = app.getCache('primary_color');

						that.scaleLayout(1);

					} else {
						if (data.msg) {
							app.alert(data.msg, function() {
								if (data.url) app.goto(data.url);
							});
						} else if (data.url) {
							app.goto(data.url);
						} else {
							app.alert('您无查看权限');
						}
					}
				});
			},
			
			moveXy(e) {
				
				let that = this
				
				if(that.platform!='ios'){
	
					that.scroll_x = -e.detail.x;
					that.scroll_y = -e.detail.y;

					 if(that.t_scale == 1){
						 that.k_x  = 0;
						 that.k_y  = 0;
					 }else{
						 that.k_x = that.scroll_x / (3*that.scaleValue);
						 that.k_y = that.scroll_y / (10*that.scaleValue);
						 
						 that.t_y =  e.detail.y;
						 
						 that.t_x =  10 - e.detail.x/that.t_scale;
					 }

                }

			},
			moveScale(e) {
				
				let that = this
				
				if(that.platform!='ios'){

					let scale = e.detail.scale;
						
					that.t_y = e.detail.y;
					that.t_scale = scale;

					if (scale == 1) {
						that.scaleValue = scale;
						that.x = 0;
						that.y = 0;
						that.t_y = 0;
					}
					
					let w = that.k_w;
					let h = that.k_h;

					h = that.k_h_n * (2 - scale);
					w = that.k_w_n * (2 - scale);

					if (h >= that.k_h_n) {
						h = that.k_h_n;
					} else if (h <= that.k_h_n / 2) {
						h = that.k_h_n / 2;
					}

					if (w >= that.k_w_n) {
						w = that.k_w_n
					} else if (w <= that.k_w_n / 2) {
						w = that.k_w_n / 2;
					}

					that.k_w = w;
					that.k_h = h;
				
				}

			},

			showTime() {
				this.$refs.dialogShowCategory.open('top');        
			},

			hideDialog() {
				this.$refs.dialogShowCategory.close();
			},

			scaleLayout(step) {
				let that = this
				that.scaleValue = step; // 增加或减少缩放值
			},


			selectTab(ind) {

				this.ticke = this.ticketList[ind];

				this.selectColor = ind;

				this.selectPage = -1;

				this.scaleLayout(1);

				//重置框
				this.k_h = this.k_h_n;
				this.k_w = this.k_w_n;
				this.k_x = 0;
				this.k_y = 0;
			},


			selectPack(ind) {

				this.ticke = this.seat_data.packages_list[ind];

				this.selectColor = -1;

				this.selectPage = ind;

				this.scaleLayout(1);

				//重置框
				this.k_h = this.k_h_n;
				this.k_w = this.k_w_n;
				this.k_x = 0;
				this.k_y = 0;
			},

			selectOne(index, indx, indy) {
				
				let that = this;

				console.log(index+'--'+ indx+'--'+indy)
      
				let obj = that.cen_list[index];
				
				let item = obj.yList[indx][indy];

	
				if (!item.color && item.show == 1) {
					return;
				} 

				if (that.scaleValue < 1.8) {

					that.scaleLayout(2.5);  
		
					if(that.platform!='ios'){
						that.x = -item.x * 10;
						that.y = -item.y * 15;
						
						that.t_y =  item.y * 20;
					}

				}


				if (item.color) {

					item.on = !item.on;

					if (item.on) {

						if (item.color_z) {

							item.color = item.color_z;
						} else {
							item.color_z = item.color;
						}

						item.color = '#aaa';

					} else {

						item.color = item.color_z;
					}
				}


				// that.cen_list[index] = obj;

				if (item.on) {

					that.selectCommit.push({
						price: parseInt(item.money),
						id: item.id,
						eid: obj.id,
						cen: item.floor,
						x_i: indx,
						y_i: indy,
						x: item.x,
						y: item.y,
						color: item.color_z
					})

				} else {

					that.selectCommit.filter((m, k) => { 

						if (m.x == item.x && m.y == item.y) {
							that.selectCommit.splice(k, 1);
						}

					})

				}
				
		
				that.getTotal();

			},

			getTotal() {
				let that = this

				let tickets = that.ticketList;

				tickets.filter((ticket, m) => {

					ticket.num = 0;

					that.selectCommit.filter((item, n) => {

						if (item.color == ticket.color) {

							ticket.num++;;
						}

					});

				});


				let money = 0;
				that.seat_data.packages_list.filter((item, k) => { //套票计算

					item.num = 0;

					tickets.filter((ticket, m) => {

						if (ticket.color == item.color) {

							let count = parseInt(ticket.num / item.group_size);

							if (count >= 1) {

								ticket.num = ticket.num - count * parseInt(item.group_size);

								if (item.fixed_price > 0) {
									money += parseFloat(item.fixed_price);

									item.total = Math.round((item.money * parseInt(item.group_size) -
										parseFloat(item.fixed_price)) * 100) / 100; //优惠金额

								} else {

									money += Math.round(item.money * item.discount * parseInt(item
										.group_size) * count * 100) / 100;

									item.total = Math.round((item.money * parseInt(item.group_size) - item
											.money * item.discount * parseInt(item.group_size) * count
											) * 100) / 100; //优惠金额

								}


								item.num = count;


							}

						}

					});

				});

				tickets.filter((ticket, m) => { //单票计算

					if (ticket.num > 0) {
						money += parseFloat(ticket.money*ticket.num);
					}

				});


				that.total = Math.round(money * 100) / 100;

			},


			delSelect(index) {

				let item = this.selectCommit[index];
				
				console.log(item);

				this.selectCommit.splice(index, 1);

				this.cen_list[item.cen-1].yList[item.x_i][item.y_i].on = false;

				this.cen_list[item.cen-1].yList[item.x_i][item.y_i].color = item.color;

				this.getTotal();

			},

			bindPickerChange(ind) {
				this.ind = ind
			},

			goSeat() {
				let that = this

				that.hideDialog();

				that.date = that.list[that.ind];

				that.getdata();
			},

			hideKuang() {
				this.showKuang = false
			},

			onKuang() {
				this.showKuang = true
			},

			submit() {

				var that = this;
				if (that.selectCommit.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '未选择场次'
					})
					return;
				}



				let list = [];
				that.selectCommit.filter(item => {
					list.push(item.id);
				})

				let book = {
					id: that.id,
					eid: that.list_ids[that.ind],
					eName: that.list[that.ind],
					sid: list.toString()
				}


				uni.navigateTo({
					url: '/pagesB/theater/dingchangPayment?book=' +
						JSON.stringify(book) + '&select=' + JSON.stringify(that.selectCommit) + '&title=' + that
						.title + '&money=' + that.total + '&packs=' + JSON.stringify(that.seat_data.packages_list)
				})

			}

		}
	}
</script>

<style scoped lang="scss">
	
	
	@font-face {
	  font-family: 'iconfont';  /* Project id 4610545 */
	  src: url('//at.alicdn.com/t/c/font_4610545_ok9ydpn36k.woff2?t=1720841573177') format('woff2'),
	       url('//at.alicdn.com/t/c/font_4610545_ok9ydpn36k.woff?t=1720841573177') format('woff'),
	       url('//at.alicdn.com/t/c/font_4610545_ok9ydpn36k.ttf?t=1720841573177') format('truetype');
	}
	
	.iconfont {
	  font-family: "iconfont" !important;
	  font-size: 16px;
	  font-style: normal;
	  -webkit-font-smoothing: antialiased;
	  -moz-osx-font-smoothing: grayscale;
	}
	
	
	.icon-danrenzuoweiyishou:before {
	  content: "\e600";
	}
	
	.icon-danrenzuoweiyishou1:before {
	  content: "\e601";
	}
	
	.wrap {
		padding-bottom: 200px;
		background: #f5f5f5;

	}

	.row {
		display: flex;
		height: 20rpx;

		// margin-bottom: 3px; 
	}

	.item {
		/*padding: 5px 13px;*/
		margin-left: 1px;
		margin-bottom: 1px;
		border-radius: 50%;
		border: 1rpx solid #ccc;
		display: -moz-inline-box;
		display: inline-block;
		width: 11px;
		// height: 8px;
		text-align: center;
		display: inline-block;
		vertical-align: top;
		color: #fff;
		font-size: 3px;
	}
	
	.item_s{
		margin-left: 1px;
		margin-bottom: 1px;
		display: -moz-inline-box;
		display: inline-block;
		text-align: center;
		display: inline-block;
		vertical-align: top;
	    color: #e9e9e9;
		font-size: 8px;
	}


	.item span:after {
		content: "";
		display: inline-block;
		width: 100%;
		height: 0px;
	}

	.item-m {
		/*padding: 5px 13px;*/
		margin-left: 1px;
		margin-bottom: 1px;
		border-radius: 50%;
		border: 1rpx solid #fff;
		display: -moz-inline-box;
		display: inline-block;
		width: 8px;
		height: 8px;
		text-align: center;
		display: inline-block;
		vertical-align: top;
		font-size: 5px;
	}

	.lnum {
		position: fixed;
		text-align: center;
		color: #fff;
		display: -moz-inline-box;
		display: inline-block;
		width: 15px;
		float: left;
		font-size: 6px;
	}

	.lls {
		background: #828282;
		color: #fff;
		padding: 0 5px;
		margin-left: 5px;
		border-radius: 50px;
	}

	.footer {
		position: fixed;
		bottom: 0;
		padding: 20rpx 0;
		overflow: hidden;
		width: 100%;
		background: white;
		z-index: 10;

		.footer_wrap {
			padding: 0 24rpx;
			color: #999999;
			font-size: 26rpx;
		}

		.tags_warp {
			display: flex;
			align-items: center;
			margin: 20rpx 0;
			min-height: 80rpx;
			white-space: nowrap;
			overflow: scroll;

			.exmap {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 22rpx;
				margin-right: 40rpx;
			}

			.select_it {
				border-radius: 10rpx;
				background: #f5f5f5;
				padding: 10rpx 0rpx 10rpx 20rpx;
				color: #000;
				margin-right: 10rpx;
				display: flex;
				align-items: center;
			}
		}

		.footer_btn {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 20rpx;

			.btn_1 {
				color: rgb(228, 69, 66);
				font-size: 56rpx;
				position: relative;

				.b_tag {
					position: absolute;
					top: -10rpx;
					right: 0;
					color: white;
					background: #d62828;
					border-radius: 10rpx 10rpx 10rpx 0;
					font-size: 20rpx;
					padding: 4rpx 10rpx;
					transform: translateX(100%);
					opacity: 0.8;
					white-space: nowrap;
				}
			}

			.btn_2 {
				border-radius: 50rpx;
				background-color: rgb(91, 115, 242);
				width: 200rpx;
			}
		}

		.border {
			height: 1rpx;
			background: #e6e6e6;
			transform: scaleY(0.5);
		}
	}

	.tag_item {
		position: relative;
		display: flex;
		align-items: center;
		margin-right: 20rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
		padding: 15rpx 10rpx;
		background: #f5f5f5;
		color: #aaa;
	}


	.tag_text {
		padding: 4rpx 10rpx;
		font-size: 28rpx;
	}

	.tag_icon {
		height: 10px;
		width: 10px;
		border-radius: 40px;
		margin: 0 2px;
	}

	.tl {
		padding: 10px 20px;
		display: flex;
		justify-content: space-between;
		background: #fff;
	}

	.tl img {
		width: 20px;
		height: 20px;
		margin: auto 0;
	}

	.tit {
		font-weight: bold;
		font-size: 30rpx;
	}

	.date {
		margin-top: 10rpx;
		color: #828282;
	}

	.wt {
		// border: 1px solid #000;
		padding: 0 40px;
	}

	.wt-ll {
		text-align: center;
		height: 30px;
		font-size: 10px;
	}
 
	.main {
	    padding: 0 5px;
	}

	.top {
		width: 100%;
		position: fixed;
		top: 0;
		z-index: 100;
	}

	.op {
		opacity: 0.3;
	}

	.xiao {
		position: fixed;
		right: 0;
		top: 125px;
		width: 125px;
		height: 80px;
		background: #000000B2;
		border-radius: 5px;
		z-index: 100;
	}

	.kuang {
		border: 1px solid red;
		position: absolute;
	}


	.rll {
		padding-bottom: 10px;
		background: #828282B2;
	}

	.brt {
		border-radius: 10px 10px 0 0;
	}

	.brb {
		border-radius: 0 0 10px 10px;
	}

	.bk {
		border: 1px solid;
	}

	.tp {
		font-size: 8px;
		position: absolute;
		right: 0;
		top: 0;
		background: #B39C7A;
		color: #fff;
		padding: 2px;
	}

	.view-main {
		padding: 28rpx;
		font-size: 28rpx;
		color: #333333;
		border-radius: 20rpx 20rpx 0 0;
		background: #fff;
	}

	.view-title {
		justify-content: space-between;
		align-items: center;
		display: flex;
		font-size: 30rpx;

	}

	.icon {
		width: 25rpx;
		height: 25rpx;
		margin: auto 0;
	}

	.icon2 {
		width: 45rpx;
		height: 45rpx;
		margin: auto 0;
	}

	.tit-t {
		padding: 50rpx 0 20rpx 0;
		font-size: 22rpx;
	}

	.tit-date {
		padding: 10px;
		background: red;
		color: #fff;
		border-radius: 10rpx;
	}

	.tit-time {
		margin: 10rpx 0;
		border: 1px solid #f1f1f1;
		text-align: center;
		padding: 10rpx;
		border-radius: 10rpx;
	}

	.view-btn {
		background: red;
		color: #fff;
		text-align: center;
		padding: 10px;
		border-radius: 50rpx;
	}
	
	.line {
		position: absolute;
		top: 15px;
		left: 50%;
		height: 600rpx;
		border: 1px dashed #ccc;
	}
</style>