<template>
	<view>
		<view class="container">
			<!-- 用户信息 -->
			<view class="user-info">
				<view class="user-balance">
					<text>余额：{{ balance }} 元</text>
				</view>
				<view class="user-points">
					<text>积分：{{ points }}</text>
				</view>
				<view class="user-commission">
					<text>佣金：{{ commission }} 元</text>
				</view>
			</view>
			<!-- 猪仔列表 -->
			<scroll-view class="piglet-list" scroll-y>
				<view class="piglet-item" v-for="(plot, index) in plots" :key="index">
					<template v-if="plot.pig">
						<image :src="getPigletImage(plot.pig)" class="piglet-image" />
						<view class="piglet-info">
							<view class="piglet-id">
								<text v-if="plot.pig.feed_days >= 10">成猪</text>
								<text v-else-if="plot.pig.feed_days >= 5">中猪</text>
								<text v-else>幼猪</text>
								ID：{{ plot.pig.pig_id }}
							</view>
							<text class="piglet-days">已连续喂养天数：{{ plot.pig.feed_days }}/10</text>
							<view class="action-buttons">
								<button v-if="!plot.pig.is_mature && plot.pig.status === 0" 
										@click="feedPiglet(plot.pig.pig_id)" 
										class="feed-button">喂养</button>
								<button v-if="plot.pig.is_mature && plot.pig.status === 0" 
										@click="sellPiglet(plot.pig.pig_id)" 
										class="sell-button">出售</button>
							</view>
						</view>
					</template>
				</view>
			</scroll-view>
			<!-- 查看出售记录按钮 -->
			<button @click="viewSaleRecords" class="records-button">查看出售记录</button>
		</view>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
		<dp-tabbar :opt="opt"></dp-tabbar>
	</view>
</template>
<script>
	var app = getApp();
	export default {
		components: {
			loading: () => import('@/components/loading/loading.vue'),
			dpTabbar: () => import('@/components/dp-tabbar/dp-tabbar.vue'),
			popmsg: () => import('@/components/popmsg/popmsg.vue')
		},
		data() {
			return {
				loading: false,
				opt: {
					selected: 1, // 当前选中的tab
					list: [
						{
							text: '菜单一',
							pagePath: '/pages/index/index'
						},
						{
							text: '菜单二',
							pagePath: '/pages/user/index'
						}
					]
				},
				balance: 0, // 用户余额
				points: 0, // 用户积分
				commission: 0, // 用户佣金
				plots: [], // 存储地块数据
				today: '', // 当前日期（格式：YYYY-MM-DD）
			};
		},

		onLoad() {
			this.today = this.getTodayDate();

			this.getMy();
			this.getData();
		},

		methods: {

			getData() {
				let that = this;
				app.get('ApiPlot/getPlots', {}, function(res) {
					console.log(res);
					if (res.status === 1 && res.data) {
						that.plots = res.data.filter(plot => plot.status === 2); // 只显示养殖中的地块
					}
				});
			},

			getMy() {
				let that = this
				app.get('ApiMy/usercenter', {}, function(data) {

					that.balance = data.pagecontent[0].data.userinfo.money;

					that.points = data.pagecontent[0].data.userinfo.score;

				});
			},

			// 获取当前日期
			getTodayDate() {
				const date = new Date();
				const year = date.getFullYear();
				const month = ('0' + (date.getMonth() + 1)).slice(-2);
				const day = ('0' + date.getDate()).slice(-2);
				return `${year}-${month}-${day}`;
			},

			// 喂养猪仔
			feedPiglet(pig_id) {

				let that = this
				app.post('ApiPlot/feedPig', {
					pig_id: pig_id
				}, function(res) {


					if (res.status == 1) {

						uni.showToast({
							title: res.message
						})

						that.getData();

					} else {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})

					}
				});


			},
			// 出售猪仔
			sellPiglet(pig_id) {

				let that = this
				app.post('ApiPlot/sellPig', {
					pig_id: pig_id
				}, function(res) {


					if (res.status == 1) {

						uni.showToast({
							title: res.message
						})

						that.getData();

					} else {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})

					}
				});
				
			},
			// 查看出售记录
			viewSaleRecords() {
				uni.navigateTo({
					url: './salerecords',
				});
			},
			// 获取猪仔图片
			getPigletImage(piglet) {
				// if (piglet.isMature) {
				//   return '../../static/farm/pig_mature.png';
				// } else if (piglet.daysFed >= 5) {
				//   return '../../static/farm/pig_growing.png';
				// } else {
				//   return '../../static/farm/pig_small.png';
				// }

				return '../../static/img2/unlocked-bg.png';
			},
		},
	};
</script>
<style scoped>
	.container {
		display: flex;
		flex-direction: column;
		padding: 20rpx;
		background-color: #f0f4f7;
		min-height: 100vh;
		padding-bottom: 120rpx; /* 为底部tabbar预留空间 */
	}

	.user-info {
		display: flex;
		justify-content: space-around;
		align-items: center;
		margin-bottom: 30rpx;
		background-color: #fff;
		padding: 20rpx;
		border-radius: 15rpx;
		box-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.user-info view {
		display: flex;
		align-items: center;
	}

	.icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}

	.user-info text {
		font-size: 30rpx;
		color: #333;
	}

	.piglet-list {
		flex: 1;
	}

	.piglet-item {
		display: flex;
		background-color: #fff;
		border-radius: 15rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.piglet-image {
		width: 150rpx;
		height: 150rpx;
		margin-right: 20rpx;
	}

	.piglet-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.piglet-id {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.piglet-days,
	.piglet-missed {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 5rpx;
	}

	.action-buttons {
		display: flex;
		margin-top: 10rpx;
	}

	.feed-button,
	.sell-button {
		flex: 1;
		height: 60rpx;
		font-size: 26rpx;
		border: none;
		border-radius: 30rpx;
		color: #fff;
	}

	.feed-button {
		background-color: #4caf50;
		margin-right: 10rpx;
	}

	.sell-button {
		background-color: #ff5722;
	}

	.records-button {
		width: 80%;
		height: 70rpx;
		font-size: 28rpx;
		color: #fff;
		background-color: #2196f3;
		border: none;
		border-radius: 35rpx;
		margin: 30rpx auto 0;
		box-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.dp-tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
</style>