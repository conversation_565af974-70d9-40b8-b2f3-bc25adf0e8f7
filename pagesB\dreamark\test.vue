<template>
	<view class="test-page">
		<view class="test-header">
			<text class="test-title">按钮测试页面</text>
		</view>
		
		<view class="test-content">
			<!-- 基础按钮测试 -->
			<button class="test-btn" @tap="testBasicClick" type="default">
				基础按钮测试
			</button>
			
			<!-- view按钮测试 -->
			<view class="test-view-btn" @tap="testViewClick">
				View按钮测试
			</view>
			
			<!-- 启动对话测试 -->
			<button class="start-btn" @tap="testStartDialogue" type="default">
				启动时空对话测试
			</button>
			
			<!-- 清空数据测试 -->
			<button class="clear-btn" @tap="testClearData" type="default">
				清空数据测试
			</button>
			
			<!-- 跳转测试 -->
			<button class="nav-btn" @tap="testNavigation" type="default">
				跳转测试
			</button>
		</view>
		
		<view class="test-log">
			<text class="log-title">测试日志:</text>
			<view v-for="(log, index) in testLogs" :key="index" class="log-item">
				<text class="log-text">{{log}}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			testLogs: []
		}
	},
	
	onLoad() {
		this.addLog('页面加载完成');
	},
	
	methods: {
		addLog(message) {
			const time = new Date().toLocaleTimeString();
			this.testLogs.unshift(`[${time}] ${message}`);
			console.log(`[测试] ${message}`);
		},
		
		testBasicClick() {
			this.addLog('基础按钮点击成功');
			uni.showToast({
				title: '基础按钮正常',
				icon: 'success'
			});
		},
		
		testViewClick() {
			this.addLog('View按钮点击成功');
			uni.showToast({
				title: 'View按钮正常',
				icon: 'success'
			});
		},
		
		testStartDialogue() {
			this.addLog('启动对话按钮点击成功');
			uni.showModal({
				title: '启动对话测试',
				content: '这是启动对话的测试，是否跳转到对话页面？',
				success: (res) => {
					if (res.confirm) {
						this.addLog('确认跳转到对话页面');
						uni.navigateTo({
							url: '/pagesB/dreamark/dialogue'
						});
					} else {
						this.addLog('取消跳转');
					}
				}
			});
		},
		
		testClearData() {
			this.addLog('清空数据按钮点击成功');
			uni.showModal({
				title: '清空数据测试',
				content: '这是清空数据的测试，是否执行清空？',
				success: (res) => {
					if (res.confirm) {
						this.addLog('执行清空数据');
						try {
							uni.removeStorageSync('user_dialogue_data');
							uni.showToast({
								title: '清空成功',
								icon: 'success'
							});
							this.addLog('数据清空成功');
						} catch (e) {
							this.addLog('数据清空失败: ' + e.message);
						}
					} else {
						this.addLog('取消清空');
					}
				}
			});
		},
		
		testNavigation() {
			this.addLog('跳转测试按钮点击成功');
			uni.showActionSheet({
				itemList: ['跳转到首页', '跳转到对话页', '跳转到拍摄页(旧)', '跳转到拍摄页(新)'],
				success: (res) => {
					const urls = [
						'/pagesB/dreamark/index',
						'/pagesB/dreamark/dialogue',
						'/pagesB/dreamark/camera',
						'/pagesB/dreamark/camera-new'
					];

					if (res.tapIndex < urls.length) {
						this.addLog(`跳转到: ${urls[res.tapIndex]}`);
						uni.navigateTo({
							url: urls[res.tapIndex]
						});
					}
				}
			});
		}
	}
}
</script>

<style>
page {
	background: #0a0a2a;
	color: #ffffff;
}

.test-page {
	padding: 40rpx;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.test-title {
	font-size: 36rpx;
	color: #00f7ff;
	font-weight: bold;
}

.test-content {
	margin-bottom: 40rpx;
}

.test-btn,
.start-btn,
.clear-btn,
.nav-btn {
	width: 100%;
	background: linear-gradient(45deg, #00f7ff, #bd00ff) !important;
	color: #fff !important;
	padding: 30rpx !important;
	border-radius: 20rpx !important;
	margin-bottom: 20rpx;
	font-size: 28rpx !important;
	border: none !important;
	text-align: center;
}

.test-btn::after,
.start-btn::after,
.clear-btn::after,
.nav-btn::after {
	border: none !important;
}

.test-view-btn {
	width: 100%;
	background: rgba(255, 165, 0, 0.2);
	border: 1px solid #ffa500;
	color: #ffa500;
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	font-size: 28rpx;
	text-align: center;
}

.clear-btn {
	background: linear-gradient(45deg, #ff4444, #ff6666) !important;
}

.nav-btn {
	background: linear-gradient(45deg, #44ff44, #66ff66) !important;
}

.test-log {
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	padding: 30rpx;
	max-height: 400rpx;
	overflow-y: auto;
}

.log-title {
	font-size: 24rpx;
	color: #00f7ff;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
}

.log-item {
	margin-bottom: 10rpx;
}

.log-text {
	font-size: 20rpx;
	color: #7df9ff;
	line-height: 1.4;
}
</style>
