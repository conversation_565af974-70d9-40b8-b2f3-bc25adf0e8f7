<template>
	<view class="container" :disableScroll="disableScroll" v-if="isLoaded">
		<view class="header" v-if="bannerList.length > 0">
			<swiper :autoplay="swiperAutoplay" @change="currentHandle" class="swiper" :current="current" :indicatorDots="false">
				<swiper-item
					:class="'header-swiper-item ptp_exposure_swiper_' + index"
					:id="'pid=2fm1-zmc2-29fj-1kfn-' + (index + 1)"
					key="url"
					v-for="(item, index) in bannerList"
					:key="index"
				>
					<video
						:autoplay="autoplay"
						@pause="videoPauseHandle"
						@play="videoPlayHandle"
						class="swiper-item-pic"
						id="video"
						:muted="true"
						:poster="item.poster"
						:src="item.url"
						v-if="item.type === 'video'"
					></video>

					<image class="swiper-item-pic" mode="aspectFill" :src="item.url" v-if="item.type === 'pic'"></image>

					<view class="header-swiper-bottom" v-if="item.type === 'pic'"></view>
				</swiper-item>
			</swiper>
			<view class="swiper-index" v-if="bannerList.length > 1">{{ current + 1 }}/{{ bannerList.length }}</view>
		</view>
		<view class="name">{{ companyName }}</view>
		<view class="info">
			<block v-if="companyData.style">
				<image class="info-image" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20201203_companyIcon.png"></image>
				<view>青团认证</view>
				<image class="info-icon" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20210106_icon.png" v-if="companyData.companyLabelType === 1"></image>
				<image class="info-icon2" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20210106_icon2.png" v-if="companyData.companyLabelType === 2"></image>
			</block>
			<block v-else>
				<image class="info-image" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20200821_authIcon.png"></image>
				<view>企业认证</view>
				<image class="info-icon" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20210106_icon.png" v-if="companyData.companyLabelType === 1"></image>
				<image class="info-icon2" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20210106_icon2.png" v-if="companyData.companyLabelType === 2"></image>
			</block>
		</view>
		<block v-if="companyData.style">
			<view class="title-box">
				<view>服务保障</view>
				<view class="title-box-tips">官方自营</view>
			</view>
			<image class="serve-image" mode="widthFix" :src="companyData.serviceUrl" v-if="companyData.serviceUrl"></image>
			<view class="title-box">
				<view>招聘顾问团队</view>
				<view class="title-box-tips">官方自营</view>
			</view>
			<view class="assist-box" v-if="companyData.team.length > 0">
				<scroll-view class="assist-list" :scrollX="true">
					<view class="assist-list-item" v-for="(item, index) in companyData.team" :key="index">
						<image class="assist-list-img ptp_exposure_static" :id="'pid=dj1k-dn1b-ug1b-vh72-' + (index + 1)" :src="item.headImg" v-if="item.headImg"></image>

						<image class="assist-list-icon" src="https://qiniu-image.qtshe.com/20201203_companyIcon3.png"></image>

						<view class="assist-list-title">{{ item.userName }}</view>

						<view class="assist-list-desc">{{ item.people }}</view>

						<view class="assist-list-rate"><rate :limit="5" :rate="item.rank"></rate></view>
					</view>
				</scroll-view>
			</view>
		</block>
		<view class="company-title">企业基本信息</view>
		<view class="content">
			<view class="part">
				<view class="part-title">企业类型</view>
				<view class="part-desc">{{ companyData.companyEyeCheckInfo.type === 1 ? '个体' : '公司' }}</view>
			</view>
			<view class="part">
				<view class="part-title">经营状态</view>
				<view class="part-desc">{{ companyData.companyEyeCheckInfo.regStatus || '--' }}</view>
			</view>
			<view class="part">
				<view class="part-title">成立日期</view>
				<view class="part-desc">{{ time }}</view>
			</view>
			<view class="part">
				<view class="part-title">注册地址</view>
				<view class="part-desc">{{ companyData.companyEyeCheckInfo.regLocation || '--' }}</view>
			</view>
			<view class="part">
				<view class="part-title">统一信用代码</view>
				<view class="part-desc">{{ companyData.companyEyeCheckInfo.creditCode || '--' }}</view>
			</view>
			<view class="part" v-if="isShowAll">
				<view class="part-title">组织机构代码</view>
				<view class="part-desc">{{ companyData.companyEyeCheckInfo.orgNumber || '--' }}</view>
			</view>
			<view class="part" v-if="isShowAll">
				<view class="part-title">经营范围</view>
				<view class="part-desc">{{ companyData.companyEyeCheckInfo.businessScope || '--' }}</view>
			</view>
			<view class="content-bottom">
				<view>以上信息由</view>
				<image mode="scaleToFill" src="https://qiniu-image.qtshe.com/20200822_companyIcon.png"></image>
				<view>提供</view>
			</view>
			<view @tap="showAllHandle" class="content-button ptp_exposure_static" data-ptpid="ff0d-1875-b777-e0c0" id="pid=ff0d-1875-b777-e0c0" v-if="!isShowAll">
				<text style="margin-right: 4rpx">展开全部</text>
				<text class="iconfont iconarrow_down"></text>
			</view>
			<view class="content-mask" v-if="!isShowAll"></view>
		</view>
		<move-drag
			@disable="disableScrollControl"
			@loadmore="loadmore"
			:isEnd="isEnd"
			:isLoading="isModuleLoading"
			:limit="400"
			:list="moduleList"
			ptpId="22df-1mag-1kfy-290f"
			ref="saveRef"
			:start="0"
			:startDirction="true"
			:title="'在招岗位 ' + totalCount"
		></move-drag>
	</view>
</template>

<script>
import moveDrag from './components/moveDrag/index';
import rate from './components/rate/index';

var app = getApp();
export default {
	components: {
		moveDrag,
		rate
	},
	data() {
		return {
			loading: false,
			isLoaded: false,
			opt: {},
			companyId: '',
			companyName: '',
			companyData: {},
			time: '--',
			pageNum: 1,
			pageSize: 10,
			jobList: [],
			current: 0,
			bannerList: [
				{
					url: 'https://qiniu-image.qtshe.com/20201210_companyBg.png',
					type: 'pic'
				}
			],
			assistList: [],
			isShowAll: false,
			autoplay: false,
			totalCount: 0,
			swiperAutoplay: true,
			scrollTop: 0,
			disableScroll: false,
			moduleList: [],
			isEnd: false,
			isModuleLoading: false
		};
	},
	onLoad: function(opt) {
		this.opt = app.getopts(opt);
		this.getCompanyDetail();
		this.getCompanyPositions();
	},
	onShow: function() {},
	onReachBottom: function() {},
	methods: {
		// 获取企业详情
		getCompanyDetail() {
			var that = this;
			that.loading = true;
			app.get('ApiZhaopin/getCompanyDetail', {
				id: that.opt.id
			}, function(res) {
				console.log('企业详情接口返回:', res);
				that.loading = false;
				if(res.status === 1) {
					const data = res.data;
					// 处理企业基本信息
					that.companyName = data.name;
					
					// 处理轮播图
					if(data.photos) {
						that.bannerList = data.photos.split(',').map(url => ({
							url: url,
							type: 'pic'
						}));
					} else {
						that.bannerList = [{
							url: data.logo || 'https://qiniu-image.qtshe.com/20201210_companyBg.png',
							type: 'pic'
						}];
					}

					that.companyData = {
						style: 0,
						serviceUrl: null,
						team: null,
						logo: data.logo || 'https://qiniu-image.qtshe.com/company_logo_default.png',
						name: data.name,
						companyEyeCheckInfo: {
							type: 1,
							regStatus: data.nature || '--',
							fromTime: new Date(data.create_time).getTime() || '--',
							regLocation: data.address || '--',
							creditCode: data.credit_code || '--',
							orgNumber: data.org_number || '--',
							businessScope: data.introduction || '--'
						},
						shareContentClassifys: {
							weixinTalk: `${data.position_count}个职位招聘中，点击查看更多招聘信息！`,
							weixinFriend: `【${data.name}】${data.position_count}个职位招聘中，点击查看更多招聘信息！`,
							sinaWb: `【${data.name}】${data.position_count}个职位招聘中，点击查看更多招聘信息！`,
							qqshare: `${data.position_count}个职位招聘中，点击查看更多招聘信息！`,
							qqtalk: `${data.position_count}个职位招聘中，点击查看更多招聘信息！`
						}
					};
					that.isLoaded = true;
				} else {
					uni.showToast({
						title: res.msg || '获取企业详情失败',
						icon: 'none'
					});
				}
			});
		},
		// 获取企业职位列表
		getCompanyPositions() {
			var that = this;
			that.isModuleLoading = true;
			app.get('ApiZhaopin/getCompanyPositions', {
				company_id: that.opt.id,
				page: that.pageNum,
				limit: that.pageSize
			}, function(res) {
				console.log('企业职位列表接口返回:', res);
				that.isModuleLoading = false;
				if(res.status === 1) {
					const data = res.data;
					// 处理职位列表
					const newList = data.list.map(item => ({
						addressDetail: item.work_address,
						partJobId: item.id,
						title: item.title,
						titleSimple: item.title,
						salary: item.salary,
						clearingForm: {
							key: '0',
							value: '其他'
						},
						welfare: Object.values(item.formatted_options || {}).flat().join(','),
						companyName: data.company.name,
						logo: data.company.logo,
						dataSource: 0,
						jobLineType: 1,
						category: 1,
						entryCount: item.views || 0,
						companyType: {
							key: '1',
							value: '企业'
						},
						brandName: data.company.name,
						companyLogo: data.company.logo,
						parentClassId: 10139,
						classId: 10465,
						c1: *********,
						c2: *********,
						c3: *********,
						labelList: {
							serviceLabels: [
								{
									labelId: 61,
									labelName: '企业认证',
									labelStyle: '{"id":10,"icon":"","color":"#72AAFA","borderColor":"#72AAFA","background":"#FFFFFF"}'
								}
							],
							descLabels: [
								{
									labelId: 19,
									labelName: '最新发布',
									labelStyle: '{"id":6,"icon":"","color":"#FA5555","borderColor":"#FEEEEE","background":"#FEEEEE"}'
								}
							]
						},
						listStyle: 3,
						jobIntroduction: [
							{
								title: '学历要求',
								introductionDesc: item.education
							},
							{
								title: '经验要求',
								introductionDesc: item.experience
							}
						],
						newJobIntroduction: item.description ? item.description.replace(/<[^>]+>/g,'').slice(0, 50) + '...' : '',
						ptpModParam: {
							dataSource: 0
						}
					}));

					if(that.pageNum === 1) {
						that.moduleList = newList;
					} else {
						that.moduleList = [...that.moduleList, ...newList];
					}

					that.totalCount = data.total;
					that.isEnd = that.moduleList.length >= data.total;
				} else {
					uni.showToast({
						title: res.msg || '获取职位列表失败',
						icon: 'none'
					});
				}
			});
		},
		loadmore: function() {
			if(!this.isEnd && !this.isModuleLoading) {
				this.pageNum++;
				this.getCompanyPositions();
			}
		},
		showAllHandle: function() {
			this.isShowAll = true;
		},
		currentHandle: function(t) {},
		getNetworkType: function() {},
		videoPlayHandle: function() {},
		videoPauseHandle: function() {},
		onShareAppMessage: function() {
			return {
				title: '青团社兼职',
				path: '/zhaopin/company?companyId=' + this.opt.id
			};
		},
		disableScrollControl: function(t) {
			this.disableScroll = t;
		},
		saveRef: function(t) {}
	}
};
</script>
<style lang="scss" scoped>
@import './company.scss';
</style>
