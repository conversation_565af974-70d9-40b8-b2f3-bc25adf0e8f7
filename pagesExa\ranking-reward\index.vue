<template>
	<view class="container">
		<!-- 头部统计 -->
		<view class="stats-container">
			<view class="stats-card">
				<view class="stats-title">累计获得奖励</view>
				<view class="stats-value">{{statsData.total_reward || '0.00'}}</view>
			</view>
			<view class="stats-flex">
				<view class="stats-item">
					<view class="stats-subtitle">已发放奖励</view>
					<view class="stats-subvalue">{{statsData.issued_reward || '0.00'}}</view>
				</view>
				<view class="stats-item">
					<view class="stats-subtitle">待发放奖励</view>
					<view class="stats-subvalue">{{statsData.pending_reward || '0.00'}}</view>
				</view>
			</view>
		</view>
		
		<!-- 奖励规则列表 -->
		<view class="rules-container">
			<view class="section-title">奖励规则</view>
			<view class="no-data" v-if="rulesList.length === 0">
				<text>暂无可用奖励规则</text>
			</view>
			<view class="rule-card" v-for="(item, index) in rulesList" :key="index" @click="goToRulePreview(item.id)">
				<view class="rule-top">
					<view class="rule-name">{{item.name}}</view>
					<view class="rule-tag">{{item.reward_mode_name}}</view>
				</view>
				<view class="rule-info">
					<view class="rule-item">
						<text class="rule-label">排名类型：</text>
						<text class="rule-value">{{item.rank_type_name}}</text>
					</view>
					<view class="rule-item">
						<text class="rule-label">奖励比例：</text>
						<text class="rule-value">{{item.total_reward_rate}}%</text>
					</view>
					<view class="rule-item">
						<text class="rule-label">奖励排名：</text>
						<text class="rule-value">前{{item.reward_top_num}}名</text>
					</view>
				</view>
				<view class="rule-button">查看详情</view>
			</view>
		</view>
		
		<!-- 最近奖励记录 -->
		<view class="records-container">
			<view class="section-title-row">
				<view class="section-title">最近奖励记录</view>
				<view class="view-more" @click="goToRecords">查看更多</view>
			</view>
			<view class="no-data" v-if="statsData.recent_records && statsData.recent_records.length === 0">
				<text>暂无奖励记录</text>
			</view>
			<view class="record-card" v-for="(item, index) in statsData.recent_records" :key="index">
				<view class="record-top">
					<view class="record-title">{{item.rule_name}}</view>
					<view class="record-status" :class="{'status-success': item.status === 1, 'status-pending': item.status === 0, 'status-rejected': item.status === 2}">
						{{item.status_text}}
					</view>
				</view>
				<view class="record-info">
					<view class="record-period">{{item.period_text}}</view>
					<view class="record-rank">排名：第{{item.rank}}名</view>
				</view>
				<view class="record-amount">+{{item.reward_amount}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				rulesList: [],
				statsData: {
					issued_reward: '0.00',
					pending_reward: '0.00',
					total_reward: '0.00',
					rules_with_ranking: 0,
					recent_records: []
				},
				loading: false
			}
		},
		onLoad() {
			this.initData();
		},
		onPullDownRefresh() {
			this.initData(() => {
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			// 初始化数据
			initData(callback) {
				// 获取统计数据
				this.getStatsData();
				// 获取规则列表
				this.getRulesList();
				
				if (callback && typeof callback === 'function') {
					callback();
				}
			},
			
			// 获取统计数据
			getStatsData() {
				const timestamp = new Date().getTime();
				console.log(`${this.formatTime()}-INFO-[ranking-reward/index][getStatsData_001] 开始获取排名奖励统计数据`);
				
				this.loading = true;
				var that = this;
				
				app.post('ApiPaimingjiang/getRankingStats', {}, function(res) {
					that.loading = false;
					
					if (res.status === 1) {
						console.log(`${that.formatTime()}-INFO-[ranking-reward/index][getStatsData_002] 获取排名奖励统计数据成功`);
						that.statsData = res.data;
					} else {
						console.log(`${that.formatTime()}-ERROR-[ranking-reward/index][getStatsData_003] 获取排名奖励统计数据失败：${res.msg}`);
						uni.showToast({
							title: res.msg || '获取统计数据失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 获取规则列表
			getRulesList() {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/index][getRulesList_001] 开始获取排名奖励规则列表`);
				
				this.loading = true;
				var that = this;
				
				app.post('ApiPaimingjiang/getRankingRules', {}, function(res) {
					that.loading = false;
					
					if (res.status === 1) {
						console.log(`${that.formatTime()}-INFO-[ranking-reward/index][getRulesList_002] 获取排名奖励规则列表成功，共${res.data.length}条数据`);
						that.rulesList = res.data;
					} else {
						console.log(`${that.formatTime()}-ERROR-[ranking-reward/index][getRulesList_003] 获取排名奖励规则列表失败：${res.msg}`);
						uni.showToast({
							title: res.msg || '获取规则列表失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 跳转到规则预览页
			goToRulePreview(ruleId) {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/index][goToRulePreview_001] 跳转到规则预览页，规则ID：${ruleId}`);
				uni.navigateTo({
					url: `/pagesExa/ranking-reward/preview?rule_id=${ruleId}`
				});
			},
			
			// 跳转到记录页
			goToRecords() {
				console.log(`${this.formatTime()}-INFO-[ranking-reward/index][goToRecords_001] 跳转到奖励记录页`);
				uni.navigateTo({
					url: '/pagesExa/ranking-reward/records'
				});
			},
			
			// 格式化时间，用于日志
			formatTime() {
				const date = new Date();
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				const seconds = date.getSeconds().toString().padStart(2, '0');
				const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${milliseconds}`;
			}
		}
	}
</script>

<style lang="scss">
.container {
	padding: 30rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 统计卡片
.stats-container {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.stats-card {
	text-align: center;
	margin-bottom: 20rpx;
}

.stats-title {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.stats-value {
	font-size: 48rpx;
	color: #FF6600;
	font-weight: bold;
}

.stats-flex {
	display: flex;
	justify-content: space-around;
	border-top: 1rpx solid #EEEEEE;
	padding-top: 20rpx;
}

.stats-item {
	text-align: center;
}

.stats-subtitle {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 6rpx;
}

.stats-subvalue {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

// 规则列表
.rules-container {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.section-title-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.view-more {
	font-size: 26rpx;
	color: #999;
}

.rule-card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.rule-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.rule-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.rule-tag {
	font-size: 24rpx;
	color: #FFFFFF;
	background-color: #FF6600;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}

.rule-info {
	margin-bottom: 20rpx;
}

.rule-item {
	display: flex;
	margin-bottom: 10rpx;
}

.rule-label {
	font-size: 28rpx;
	color: #666;
	width: 180rpx;
}

.rule-value {
	font-size: 28rpx;
	color: #333;
}

.rule-button {
	text-align: center;
	color: #FF6600;
	font-size: 28rpx;
	border: 1rpx solid #FF6600;
	border-radius: 30rpx;
	padding: 10rpx 0;
}

// 记录列表
.record-card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
	position: relative;
}

.record-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.record-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.record-status {
	font-size: 24rpx;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}

.status-pending {
	color: #FF9900;
	background-color: rgba(255, 153, 0, 0.1);
}

.status-success {
	color: #00CC66;
	background-color: rgba(0, 204, 102, 0.1);
}

.status-rejected {
	color: #FF3333;
	background-color: rgba(255, 51, 51, 0.1);
}

.record-info {
	display: flex;
	justify-content: space-between;
	font-size: 26rpx;
	color: #999;
}

.record-amount {
	position: absolute;
	right: 24rpx;
	bottom: 24rpx;
	font-size: 36rpx;
	color: #FF6600;
	font-weight: bold;
}

// 无数据提示
.no-data {
	text-align: center;
	padding: 50rpx 0;
	font-size: 28rpx;
	color: #999;
	background-color: #FFFFFF;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
}
</style> 