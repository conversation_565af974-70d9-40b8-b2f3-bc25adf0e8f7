<template>
	<view class="container">
		<block>
			<view class="view-show">
				<view class="search-container">
					<view class="search-box">
						<image class="img" src="/static/img/search_ico.png"></image>
						<input type="text" placeholder="输入专业名称搜索" v-model="keyword" @confirm="_request" />
					</view>
				</view>
				<view class="content"
					style="height:calc(100vh - 94rpx);overflow:hidden;display:flex;flex-direction: row !important;">
					<scroll-view class="nav_left" :scrollWithAnimation="animation" scroll-y="true"
						:class="menuindex>-1?'tabbarbot':'notabbarbot'">
						<block v-for="(item, index) in data" :key="index">
							<view class="nav_left_items" :class="index===currentActiveIndex?'active':''"
								@tap="clickRootItem(item)" :data-root-item-id="item.id" :data-root-item-index="index">
								<view class="before" :style="{background:t('color1')}"></view>{{item.zhuanye_name}}
							</view>
						</block>
					</scroll-view>

					<view class="nav_right">
						<view class="nav_right-content">
							<scroll-view @scroll="scroll" class="detail-list" :scrollIntoView="scrollToViewId"
								:scrollWithAnimation="animation" scroll-y="true"
								:class="menuindex>-1?'tabbarbot':'notabbarbot'">
								<view v-for="(detail, index) in details" :key="index" class="classification-detail-item"
									@tap.stop="goto" :data-url="'/pagesExa/daxuepage/specialityDetails?id='+detail.id">
									<view class="head" :data-id="detail.id" :id="'detail-' + detail.id">
										<view class="txt" style="margin-bottom: 30rpx;">{{detail.title}}</view>
										<view
											style="display: flex;justify-content: space-between;color: #bbbbbb;padding-bottom: 30rpx;border-bottom: 2rpx solid #bbbbbb;">
											<view>
												学制：{{detail.study_duration}}
											</view>
											<view class="">
												层次：{{detail.education_level}}
											</view>
										</view>
									</view>
								</view>
								<nodata v-if="nodata"></nodata>
							</scroll-view>
						</view>
					</view>

				</view>
			</view>
		</block>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		props: {
			daxueid: ''
		},
		data() {
			return {
				loading: false,
				menuindex: -1,
				keyword: '',
				data: [],
				oldData: [],
				currentActiveIndex: 0,
				animation: true,
				clist: "",
				bid: '',
				details: [],
				nodata:false
			};
		},
		created() {
			this.getdata();
		},
		onLoad: function(opt) {

		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiDaxue/getZhuanyeDetailByDaxueId', {
					daxueid: that.daxueid,
					search: that.keyword
				}, function(res) {
					that.loading = false;
					if (res.data.length > 0) {
						that.data = res.data;
						that.oldData = JSON.parse(JSON.stringify(res.data))
						that.details = that.data[0].details
					} else {
						that.data = []
						that.oldData = []
						that.details = []
						that.nodata = true
					}

				});
			},
			clickRootItem: function(t) {
				this.details = t.details
			},
			gotoCatproductPage: function(t) {
				var e = t.currentTarget.dataset;
				if (this.bid) {
					app.goto('/shopPackage/shop/prolist?bid=' + this.bid + '&cid2=' + e.id);
				} else {
					app.goto('/shopPackage/shop/prolist?cid=' + e.id);
				}
			},
			_request() {
				this.getdata()

				// if (this.keyword == '') {
				// 	this.data = this.oldData
				// 	this.details = arr[0].details
				// 	return;
				// }
				// this.details = []
				// this.data = []
				// const arr = this.oldData.filter(item => {
				// 	// 检查当前对象的 arr 数组中是否有任何对象的 name 属性等于关键词
				// 	return item.details.some(subItem => subItem.title === this.keyword);
				// })
				// console.log('arr', arr)
				// if (arr.length == 0) {
				// 	this.nodata = true
				// 	return;
				// }
				// this.data = arr
				// this.details = arr[0].details
			}
		}
	};
</script>
<style>
	page {
		position: relative;
		width: 100%;
		height: 100%;
	}

	button {
		border: 0 solid !important;
	}

	.container {
		height: 100%
	}

	.view-show {
		background-color: white;
		line-height: 1;
		width: 100%;
		height: 100%;
	}

	.search-container {
		width: 100%;
		height: 94rpx;
		padding: 16rpx 23rpx 14rpx 23rpx;
		background-color: #fff;
		position: relative;
		overflow: hidden;
		border-bottom: 1px solid #f5f5f5
	}

	.search-box {
		display: flex;
		align-items: center;
		height: 60rpx;
		border-radius: 30rpx;
		border: 0;
		background-color: #f7f7f7;
		flex: 1
	}

	.search-box .img {
		width: 24rpx;
		height: 24rpx;
		margin-right: 10rpx;
		margin-left: 30rpx
	}

	.search-box .search-text {
		font-size: 24rpx;
		color: #C2C2C2;
		width: 100%;
	}

	.nav_left {
		width: 25%;
		height: 100%;
		background: #ffffff;
		overflow-y: scroll;
	}

	.nav_left .nav_left_items {
		line-height: 50rpx;
		color: #666666;
		border-bottom: 0px solid #E6E6E6;
		font-size: 28rpx;
		position: relative;
		border-right: 0 solid #E6E6E6;
		padding: 25rpx 30rpx;
	}

	.nav_left .nav_left_items.active {
		background: #fff;
		color: #222222;
		font-size: 28rpx;
		font-weight: bold
	}

	.nav_left .nav_left_items .before {
		display: none;
		position: absolute;
		top: 50%;
		margin-top: -12rpx;
		left: 10rpx;
		height: 24rpx;
		border-radius: 4rpx;
		width: 8rpx
	}

	.nav_left .nav_left_items.active .before {
		display: block
	}

	.nav_right {
		width: 75%;
		height: 100%;
		display: flex;
		flex-direction: column;
		background: #f6f6f6;
		box-sizing: border-box;
		padding: 20rpx 20rpx 0 20rpx
	}

	.nav_right-content {
		background: #ffffff;
		padding: 20rpx;
		height: 100%;
		position: relative
	}

	.detail-list {
		height: 100%;
		overflow: scroll
	}

	.classification-detail-item {
		width: 100%;
		overflow: visible;
		background: #fff;
		margin-bottom: 30rpx;
	}

	.classification-detail-item .head {
		width: 100%;
	}

	.classification-detail-item .head .txt {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
	}

	.classification-detail-item .head .show-all {
		font-size: 22rpx;
		color: #949494;
		display: flex;
		align-items: center
	}

	.classification-detail-item .detail {
		width: 100%;
		display: flex;
		flex-wrap: wrap
	}

	.classification-detail-item .detail .detail-item {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 70rpx;
	}

	.classification-detail-item .detail .detail-item .img {
		width: 112rpx;
		height: 112rpx;
		margin-left: 24rpx
	}

	.classification-detail-item .detail .detail-item .txt {
		color: #333;
		font-size: 28rpx;
		margin-top: 20rpx;
		text-align: center;
		white-space: nowrap;
		word-break: break-all;
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}
</style>
