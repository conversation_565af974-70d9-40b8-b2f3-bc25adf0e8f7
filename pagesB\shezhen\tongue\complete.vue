<template>
	<view class="tongue-complete">
		<!-- 2025-01-03 22:55:53,565-INF0-[complete][init_001] 舌诊完整结果页面初始化 -->
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<view class="nav-title">健康报告</view>
			<view class="nav-right">
				<text class="menu-icon">•••</text>
				<text class="record-icon">●</text>
			</view>
		</view>
		
		<!-- 医院标识 -->
		<view class="hospital-info">
			<text>广东省中医院出品 | 国家重点实验室项目 | 国家重大专项</text>
		</view>
		
		<!-- 报告类型选择 -->
		<view class="report-tabs">
			<view class="tab-item active">
				<text class="tab-text">舌象报告</text>
				<view class="tab-line"></view>
			</view>
			<view class="tab-item">
				<text class="tab-text">足象报告</text>
			</view>
		</view>
		
		<!-- 健康得分卡片 -->
		<view class="health-card">
			<view class="health-content">
				<view class="health-left">
					<image class="health-avatar" src="/static/images/moisture-avatar.png"></image>
					<view class="health-symptoms">
						<view class="main-symptom">
							<text class="symptom-label">主体征：</text>
							<text class="symptom-value">水湿</text>
						</view>
						<view class="sub-symptom">
							<text class="symptom-label">副体征：</text>
							<text class="symptom-value">脾胃虚</text>
						</view>
					</view>
				</view>
				<view class="health-right">
					<view class="score-circle">
						<view class="score-inner">
							<text class="score-number">89</text>
							<text class="score-text">健康得分</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 智能问诊 -->
		<view class="ai-diagnosis">
			<view class="ai-left">
				<view class="ai-title">智能问诊</view>
				<text class="ai-subtitle">您好,请点击登录!</text>
				<text class="ai-description">补充信息，为您提供详尽的舌诊报告。</text>
			</view>
			<view class="ai-right">
				<view class="login-btn">去填写</view>
			</view>
		</view>
		
		<!-- 可能体征 -->
		<view class="symptoms-section">
			<text class="section-title">您可能有以下体征</text>
			<view class="symptoms-grid">
				<view class="symptom-tag">胸闷</view>
				<view class="symptom-tag">关节疼痛</view>
				<view class="symptom-tag">肥胖</view>
				<view class="symptom-tag">脸油头油多</view>
				<view class="symptom-tag">大便黏腻</view>
				<view class="symptom-tag">腹胀</view>
			</view>
		</view>
		
		<!-- 舌象分析 -->
		<view class="tongue-analysis">
			<text class="section-title">舌象分析</text>
			<view class="tongue-diagram">
				<image class="tongue-image" src="/static/images/tongue-diagram.png"></image>
				<view class="tongue-markers">
					<view class="marker good">
						<text class="marker-icon">✓</text>
						<text class="marker-text">舌色淡红</text>
					</view>
					<view class="marker bad top-right">
						<text class="marker-icon">!</text>
						<text class="marker-text">舌苔白色</text>
					</view>
					<view class="marker bad left">
						<text class="marker-icon">!</text>
						<text class="marker-text">有裂纹</text>
					</view>
					<view class="marker bad right">
						<text class="marker-icon">!</text>
						<text class="marker-text">有齿痕</text>
					</view>
					<view class="marker bad bottom">
						<text class="marker-icon">!</text>
						<text class="marker-text">有黏腻感</text>
					</view>
				</view>
				<view class="my-photo-btn">我的照片</view>
			</view>
		</view>
		
		<!-- 体征异常 -->
		<view class="abnormal-section">
			<view class="abnormal-title">
				<text class="abnormal-count">您有3项表征异常</text>
			</view>
			<view class="abnormal-list">
				<view class="abnormal-item">
					<view class="abnormal-header">
						<text class="abnormal-icon">!</text>
						<text class="abnormal-name">裂纹：</text>
					</view>
					<text class="abnormal-desc">极少数人天生出现裂纹属于正常情况，而其他人出现裂纹说明体内热盛伤阴，或血虚不润，或脾虚湿侵。</text>
				</view>
				<view class="abnormal-item">
					<view class="abnormal-header">
						<text class="abnormal-icon">!</text>
						<text class="abnormal-name">齿痕：</text>
					</view>
					<view class="abnormal-desc-container">
						<text class="abnormal-desc">正常是没有齿痕的，出现齿痕脾虚不能运化水湿，舌体胖大而受齿缘齿痕。</text>
						<image class="doctor-avatar" src="/static/images/doctor-avatar.png"></image>
					</view>
				</view>
				<view class="abnormal-item">
					<view class="abnormal-header">
						<text class="abnormal-icon">!</text>
						<text class="abnormal-name">厚苔：</text>
					</view>
					<text class="abnormal-desc">正常苔质是薄苔，出现厚苔说明胃气夹湿浊，疹浊，食积等邪气重甚苔厚</text>
				</view>
			</view>
		</view>
		
		<!-- 舌象体征论述 -->
		<view class="theory-section">
			<text class="section-title">舌象体征论述</text>
			<view class="theory-item">
				<view class="theory-header">
					<text class="theory-name water-wet">水湿：</text>
					<text class="theory-summary">中央生湿，湿生土，土生甘，甘生脾</text>
				</view>
				<view class="theory-content">
					<text class="theory-text">夫水湿者，乃脾胃之气所化也。脾胃壮则水湿化，脾胃弱则水湿停。故水湿之为病，皆因脾胃不足所致。其症多见体重肢肿，肌肤不泽，小便短少，大便溏泄，舌淡苔滑，脉沉缓等。治宜健脾利湿，以复其常。水湿之症，头身困重，四肢酸楚，食欲不振，腹胀便溏，舌淡苔滑，脉沉缓。或见面色萎黄，肌肤不泽，小便短少，体重肢肿。此皆因脾胃不足，运化失职，湿邪内生所致。</text>
				</view>
			</view>
			<view class="theory-item">
				<view class="theory-header">
					<text class="theory-name spleen-weak">脾胃虚：</text>
					<text class="theory-summary">脾胃为后天之本，气血生化之源。</text>
				</view>
				<view class="theory-subtitle">
					<text>脾胃者，仓廪之官，五味出焉</text>
				</view>
				<view class="theory-content">
					<text class="theory-text">百病皆由脾胃衰而生也。夫脾胃虚，则湿土之气潴于脘下，肾与膀胱受邪。膀胱壮寒，肾为阴火，二者偶弱，润泽之气虚，而煎熬之气胜，故小便黄赤而数，大便乃秘涩而不行。脾胃受伤，则水反为湿，谷反为满，精华之气不能输化于上，故合污下降而污利作矣。</text>
					<image class="doctor-avatar" src="/static/images/doctor-avatar.png"></image>
				</view>
			</view>
		</view>
		
		<!-- 患病风险 -->
		<view class="risk-section">
			<text class="section-title">患病风险</text>
			<view class="risk-grid">
				<view class="risk-item" v-for="(item, index) in riskData" :key="index">
					<!-- 2025-01-03 22:55:53,565-INF0-[complete][risk_001] 圆形进度条显示患病风险 -->
					<view class="risk-progress-circle">
						<view class="progress-ring" :style="riskProgressStyles[index]">
							<view class="progress-inner">
								<text class="progress-percentage">{{item.percentage}}%</text>
							</view>
						</view>
					</view>
					<text class="risk-name">{{item.name}}</text>
				</view>
			</view>
		</view>
		
		<!-- 需要警惕 -->
		<view class="warning-section">
			<text class="section-title">需要警惕</text>
			<view class="warning-list">
				<view class="warning-item" v-for="(item, index) in warningList" :key="index">
					<view class="warning-header">
						<text class="warning-bullet">•</text>
						<text class="warning-name">{{item.name}}：</text>
					</view>
					<text class="warning-desc">{{item.description}}</text>
					<image v-if="item.showDoctor" class="doctor-avatar-small" src="/static/images/doctor-avatar.png"></image>
				</view>
			</view>
		</view>
		
		<!-- 结果解读 -->
		<view class="result-section">
			<text class="section-title">结果解读</text>
			<view class="constitution-chart">
				<!-- 2025-01-03 22:55:53,565-INF0-[complete][constitution_001] 体质分析圆形图表 -->
				<view class="constitution-circle">
					<view class="constitution-inner">
						<!-- 中心体质显示 -->
						<view class="constitution-center">
							<text class="constitution-type">{{constitutionData.type}}</text>
							<view class="constitution-indicator" :style="{
								transform: `translate(${constitutionData.position.x}px, ${constitutionData.position.y}px)`
							}">
								<view class="indicator-dot"></view>
							</view>
						</view>
						
						<!-- 四个方向标签 -->
						<text class="direction-label top">实</text>
						<text class="direction-label right">热</text>
						<text class="direction-label bottom">虚</text>
						<text class="direction-label left">寒</text>
						
						<!-- 圆圈刻度线 -->
						<view class="scale-marks">
							<view class="scale-mark" v-for="(mark, index) in scaleMarks" :key="index" 
								:style="{
									transform: `rotate(${mark.angle}deg) translateY(-80px)`,
									opacity: mark.opacity
								}">
							</view>
						</view>
					</view>
				</view>
				
				<!-- 状态说明 -->
				<view class="constitution-status">
					<view class="status-indicator">
						<view class="status-dot" :style="{backgroundColor: constitutionData.statusColor}"></view>
						<text class="status-text">{{constitutionData.statusText}}</text>
					</view>
				</view>
			</view>
			
			<!-- 结果描述 -->
			<view class="result-description">
				<text class="result-text">{{constitutionData.description}}</text>
				<text class="result-text secondary">{{constitutionData.explanation}}</text>
			</view>
			
			<!-- 专家注释 -->
			<view class="result-note">
				<view class="note-content">
					<text class="note-text">{{constitutionData.note}}</text>
				</view>
				<image class="doctor-avatar-note" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view>
		
		<!-- VIP会员专享 -->
		<view class="vip-section">
			<view class="vip-header">
				<text class="vip-icon">👑</text>
				<text class="vip-text">VIP会员专享</text>
			</view>
			<view class="vip-card">
				<text class="vip-title">广东省中医院为您专属定制</text>
				<text class="vip-subtitle">健康调理方案</text>
			</view>
		</view>
		
		<!-- 报告解读 -->
		<view class="report-section">
			<view class="report-header">
				<text class="report-title">报告解读</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="symptoms-summary">
				<text class="summary-text">您的主症为</text>
				<text class="symptom-highlight water">水湿</text>
				<text class="summary-text">，副症为</text>
				<text class="symptom-highlight spleen">脾胃虚</text>
			</view>
			
			<view class="main-symptom-detail">
				<view class="symptom-label">主症【水湿】：</view>
				<text class="symptom-description">您体内水液代谢出现了异常，停滞在体内，也可以理解为人体新陈代谢速率变慢，导致体内的水分不能被正常的排出。</text>
			</view>
			
			<view class="view-detail-btn" @click="viewDetailContent">
				<text class="lock-icon">🔒</text>
				<text class="btn-text">查看体征解读内容</text>
			</view>
			
			<view class="doctor-section">
				<text class="doctor-title">名医为您解读体征</text>
				<image class="doctor-avatar" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view>
		
		<!-- 调理原则 -->
		<view class="principle-section">
			<view class="principle-header">
				<text class="principle-title">调理原则</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="principle-list">
				<view class="principle-item">
					<text class="principle-bullet">•</text>
					<text class="principle-text">日常饮食不要吃得过于油腻，油腻的食物会</text>
				</view>
				<view class="principle-item">
					<text class="principle-bullet">•</text>
					<text class="principle-text">每天积累运动要达到半小时以上，可以有效改善水湿体质，增强自己的体质。</text>
				</view>
			</view>
			
			<view class="view-principle-btn" @click="viewPrincipleContent">
				<text class="lock-icon">🔒</text>
				<text class="btn-text">查看病症调理原则</text>
			</view>
			
			<view class="custom-plan-text">
				<text>名医为您定制专属调理方案</text>
			</view>
		</view>
		
		<!-- 今日营养目标 -->
		<view class="nutrition-section">
			<view class="nutrition-header">
				<text class="nutrition-title">今日营养目标</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="chart-container">
				<view class="chart-left">
					<view class="pie-chart" :style="pieChartStyle">
						<view class="pie-center">
							<text class="calories-number">{{nutritionData.totalCalories}}</text>
							<text class="calories-unit">千卡</text>
						</view>
					</view>
				</view>
				
				<view class="chart-right">
					<view class="nutrition-item" v-for="(item, index) in nutritionData.nutritionItems" :key="index">
						<view class="nutrition-color" :style="{'background-color': item.color}"></view>
						<text class="nutrition-name">{{item.name}}</text>
						<text class="nutrition-percent">{{item.percent}}%</text>
						<text class="nutrition-amount">{{item.amount}}克</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 今日专属膳方 -->
		<view class="recipe-section">
			<view class="recipe-header">
				<text class="recipe-title">今日专属膳方</text>
				<text class="adjust-plan" @click="adjustRecipePlan">调整膳方计划 ></text>
			</view>
			
			<view class="recipe-card">
				<view class="recipe-content">
					<view class="recipe-info">
						<text class="recipe-name">黄酒煮鸡</text>
						<text class="recipe-effect">温中养血，散寒通络</text>
					</view>
					<view class="recipe-image-container">
						<image class="recipe-image" src="/static/images/huangjiu-chicken.png"></image>
					</view>
				</view>
			</view>
			
			<view class="doctor-recommendation">
				<image class="doctor-avatar-small" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="btn-secondary">返回</view>
			<view class="btn-secondary">重新拍照</view>
			<view class="btn-primary">分享</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 2025-01-03 22:55:53,565-INF0-[complete][data_001] 初始化完整页面数据
				riskData: [
					{ name: '肥胖', percentage: 45 },
					{ name: '痰疾', percentage: 38 },
					{ name: '关节炎', percentage: 21 },
					{ name: '脂溢性脱发', percentage: 33 },
					{ name: '高血糖、高血脂、高尿酸', percentage: 15 },
					{ name: '代谢性酸中毒', percentage: 7 }
				],
				warningList: [
					{
						name: '肥胖',
						description: '水湿人群体内的水液异常停滞，导致体内水液堆积而容易出现全身水肿，全身浮肿会使整个体形看起来很虚胖。',
						showDoctor: false
					},
					{
						name: '痰疾',
						description: '水湿人群体内的水液代谢异常，水液过多容易出现面部出油多，油脂堵塞毛孔，容易导致废物排泄障碍，皮肤而出现痤疮，即平时常说的痘痘。',
						showDoctor: true
					},
					{
						name: '关节炎',
						description: '水湿人群由于水液运化异常，水液',
						showDoctor: false
					}
				],
				// 2025-01-03 22:55:53,565-INF0-[complete][data_002] 营养目标数据
				nutritionData: {
					carbohydrate: { percent: 62, amount: 385 },
					protein: { percent: 10, amount: 62 },
					fat: { percent: 28, amount: 77 },
					totalCalories: 2484,
					nutritionItems: [
						{ name: '碳水化物', percent: 62, amount: 385, color: '#8884d8' },
						{ name: '蛋白质', percent: 10, amount: 62, color: '#82ca9d' },
						{ name: '脂肪', percent: 28, amount: 77, color: '#ffc658' }
					]
				},
				// 2025-01-03 22:55:53,565-INF0-[complete][data_003] 今日膳方数据
				recipeData: {
					name: '黄酒煮鸡',
					effect: '温中养血，散寒通络',
					image: '/static/images/huangjiu-chicken.png'
				},
				
				// 2025-01-03 22:55:53,565-INF0-[complete][data_004] 体质分析数据
				constitutionData: {
					type: '平和',
					position: { x: -20, y: 15 }, // 相对于中心的偏移位置
					statusColor: '#52c41a',
					statusText: '体质改善',
					description: '通过健康天平分析得出，您的身体偏寒且正气偏虚，处于痰湿状态。',
					explanation: '健康天平圆点越靠近中心身体越健康，越偏离中心健康状态越严重。',
					note: '注：「健康天平」由省中医院副院长杨志敏教授带领的健康辨识团队运用中医健康辨识体系，结合经络脏腑阴阳五行、气血津液等理论得出。'
				},
				
				// 2025-01-03 22:55:53,565-INF0-[complete][data_005] 圆形刻度标记数据
				scaleMarks: (() => {
					const marks = [];
					for (let i = 0; i < 24; i++) {
						const angle = i * 15; // 每15度一个刻度
						const isMainMark = i % 6 === 0; // 每90度为主刻度
						marks.push({
							angle: angle,
							opacity: isMainMark ? 0.3 : 0.1
						});
					}
					return marks;
				})()
			}
		},
		computed: {
			// 2025-01-03 22:55:53,565-INF0-[complete][computed_001] 动态计算饼状图样式
			pieChartStyle() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][computed_002] 生成饼状图样式');
				return {
					background: this.calculatePieChartGradient(this.nutritionData.nutritionItems)
				};
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][computed_003] 动态计算风险进度条样式
			riskProgressStyles() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][computed_004] 生成风险进度条样式');
				return this.riskData.map(item => {
					const angle = item.percentage * 3.6; // 将百分比转换为角度
					return {
						background: `conic-gradient(
							#4facfe 0deg ${angle}deg, 
							rgba(79, 172, 254, 0.1) ${angle}deg 360deg
						)`
					};
				});
			}
		},
		methods: {
			// 2025-01-03 22:55:53,565-INF0-[complete][goBack_001] 返回上一页功能
			goBack() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][goBack_001] 用户点击返回');
				uni.navigateBack();
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][viewDetail_001] 查看体征解读详情功能
			viewDetailContent() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][viewDetail_001] 用户点击查看体征解读');
				// 需要VIP权限才能查看详细内容
				uni.showToast({
					title: '需要VIP权限',
					icon: 'none'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][viewPrinciple_001] 查看调理原则详情功能
			viewPrincipleContent() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][viewPrinciple_001] 用户点击查看调理原则');
				// 需要VIP权限才能查看详细内容
				uni.showToast({
					title: '需要VIP权限',
					icon: 'none'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_001] 调整膳方计划
			adjustRecipePlan() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_002] 用户点击调整膳方计划');
				console.log('2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_003] 当前膳方:', this.recipeData);
				uni.navigateTo({
					url: '/pages/recipe/adjust'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_001] 显示详细健康报告
			onShowHealthReport() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_002] 导航到健康详细报告');
				uni.navigateTo({
					url: '/pages/tongue/detail'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_003] 分享功能
			onShare() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_004] 用户点击分享');
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_005] 查看营养详情
			onViewNutritionDetail() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_006] 导航到营养详情页面');
				uni.navigateTo({
					url: '/pages/nutrition/detail'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_007] 查看完整调理方案
			onViewCompleteRegulation() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_008] 导航到调理方案页面');
				uni.navigateTo({
					url: '/pages/tongue/plan'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_009] 开始舌诊拍摄
			onStartTongueDetection() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_010] 启动舌诊拍摄功能');
				uni.navigateTo({
					url: '/pages/tongue/camera'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_011] 计算饼状图角度
			calculatePieChartGradient(nutritionItems) {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_012] 计算饼状图渐变');
				let cumulativePercent = 0;
				const segments = nutritionItems.map(item => {
					const startPercent = cumulativePercent;
					const endPercent = cumulativePercent + item.percent;
					cumulativePercent = endPercent;
					return `${item.color} ${startPercent}% ${endPercent}%`;
				});
				return `conic-gradient(${segments.join(', ')})`;
			}
		},
		onLoad() {
			console.log('2025-01-03 22:55:53,565-INF0-[complete][onLoad_001] 舌诊完整页面加载完成');
		}
	}
</script>

<style scoped>
/* 2025-01-03 22:55:53,565-INF0-[complete][style_001] 舌诊完整页面样式定义 */

.tongue-complete {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 顶部导航样式 */
.top-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: white;
	position: relative;
}

.nav-left {
	display: flex;
	align-items: center;
}

.back-icon {
	font-size: 40rpx;
	color: #333;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.nav-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.menu-icon {
	font-size: 30rpx;
	color: #666;
}

.record-icon {
	font-size: 30rpx;
	color: #333;
}

/* 医院信息 */
.hospital-info {
	background-color: #e8f4ff;
	padding: 20rpx;
	text-align: center;
	font-size: 24rpx;
	color: #666;
}

/* 报告标签页 */
.report-tabs {
	display: flex;
	background-color: white;
	padding: 0 30rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 0;
	position: relative;
}

.tab-item.active .tab-text {
	color: #1890ff;
	font-weight: 500;
}

.tab-line {
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 6rpx;
	background-color: #1890ff;
	border-radius: 3rpx;
}

.tab-text {
	font-size: 32rpx;
	color: #333;
}

/* 健康得分卡片 */
.health-card {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	border-radius: 20rpx;
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(79, 172, 254, 0.3);
}

.health-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.health-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.health-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	padding: 10rpx;
}

.health-symptoms {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.main-symptom, .sub-symptom {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.symptom-label {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 400;
}

.symptom-value {
	font-size: 32rpx;
	color: white;
	font-weight: 600;
	background: rgba(255, 255, 255, 0.2);
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.health-right {
	display: flex;
	align-items: center;
}

.score-circle {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 20rpx rgba(79, 172, 254, 0.4);
	border: 3rpx solid rgba(255, 255, 255, 0.5);
}

.score-inner {
	text-align: center;
}

.score-number {
	font-size: 48rpx;
	font-weight: 700;
	color: #4facfe;
	display: block;
	line-height: 1;
}

.score-text {
	font-size: 24rpx;
	color: #4facfe;
	font-weight: 500;
	margin-top: 8rpx;
	display: block;
}

/* 智能问诊 */
.ai-diagnosis {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 30rpx;
	padding: 30rpx;
	background-color: white;
	border-radius: 15rpx;
}

.ai-left {
	flex: 1;
}

.ai-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.ai-subtitle {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 5rpx;
}

.ai-description {
	font-size: 24rpx;
	color: #999;
}

.login-btn {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
}

/* 可能体征 */
.symptoms-section {
	margin: 30rpx 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 30rpx;
}

.symptoms-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
	margin-top: 20rpx;
}

.symptom-tag {
	background: rgba(79, 172, 254, 0.08);
	color: #4facfe;
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
	border: 1rpx solid rgba(79, 172, 254, 0.2);
	backdrop-filter: blur(10rpx);
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.1);
}

/* 舌象分析 */
.tongue-analysis {
	margin: 30rpx;
}

.tongue-diagram {
	background-color: white;
	border-radius: 15rpx;
	padding: 40rpx;
	position: relative;
	text-align: center;
}

.tongue-image {
	width: 400rpx;
	height: 300rpx;
	border-radius: 15rpx;
	background-color: #ffb3ba;
}

.tongue-markers {
	position: absolute;
	top: 40rpx;
	left: 40rpx;
	right: 40rpx;
	bottom: 40rpx;
}

.marker {
	position: absolute;
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 24rpx;
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	background-color: white;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.marker.good {
	color: #52c41a;
	top: 10rpx;
	left: 10rpx;
}

.marker.bad {
	color: #ff4d4f;
}

.marker.top-right {
	top: 10rpx;
	right: 10rpx;
}

.marker.left {
	left: -20rpx;
	top: 50%;
	transform: translateY(-50%);
}

.marker.right {
	right: -20rpx;
	top: 50%;
	transform: translateY(-50%);
}

.marker.bottom {
	bottom: 10rpx;
	left: 50%;
	transform: translateX(-50%);
}

.marker-icon {
	font-size: 20rpx;
}

.my-photo-btn {
	position: absolute;
	bottom: 80rpx;
	right: 80rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: white;
	padding: 15rpx 25rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

/* 体征异常 */
.abnormal-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.abnormal-title {
	margin-bottom: 30rpx;
}

.abnormal-count {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.abnormal-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.abnormal-item {
	border-bottom: 1rpx solid #f0f0f0;
	padding-bottom: 30rpx;
}

.abnormal-item:last-child {
	border-bottom: none;
	padding-bottom: 0;
}

.abnormal-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.abnormal-icon {
	width: 32rpx;
	height: 32rpx;
	background-color: #ff4d4f;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
}

.abnormal-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #ff4d4f;
}

.abnormal-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.abnormal-desc-container {
	display: flex;
	align-items: flex-end;
	gap: 20rpx;
}

.doctor-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	flex-shrink: 0;
}

/* 舌象体征论述 */
.theory-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.theory-item {
	margin-bottom: 40rpx;
}

.theory-item:last-child {
	margin-bottom: 0;
}

.theory-header {
	display: flex;
	align-items: baseline;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.theory-name {
	font-size: 32rpx;
	font-weight: 500;
}

.theory-name.water-wet {
	color: #1890ff;
}

.theory-name.spleen-weak {
	color: #52c41a;
}

.theory-summary {
	font-size: 28rpx;
	color: #666;
}

.theory-subtitle {
	margin-bottom: 20rpx;
}

.theory-subtitle text {
	font-size: 28rpx;
	color: #333;
}

.theory-content {
	position: relative;
}

.theory-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	padding-right: 100rpx;
}

/* 患病风险 */
.risk-section {
	margin: 30rpx 20rpx;
}

.risk-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30rpx;
	margin-top: 30rpx;
	justify-items: center;
}

.risk-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
}

.risk-progress-circle {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	position: relative;
	padding: 6rpx;
	background: #ffffff;
	box-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.15);
}

.progress-ring {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	position: absolute;
	top: 0;
	left: 0;
	padding: 2rpx;
	box-sizing: border-box;
}

.progress-inner {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	width: 100rpx;
	height: 100rpx;
	background: #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.1);
}

.progress-percentage {
	font-size: 24rpx;
	font-weight: 600;
	color: #4facfe;
}

.risk-name {
	font-size: 24rpx;
	color: #333;
	text-align: center;
	font-weight: 500;
	line-height: 1.4;
	max-width: 120rpx;
}

/* 需要警惕 */
.warning-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.warning-list {
	margin-top: 30rpx;
}

.warning-item {
	margin-bottom: 30rpx;
	position: relative;
}

.warning-item:last-child {
	margin-bottom: 0;
}

.warning-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 10rpx;
}

.warning-bullet {
	color: #ff4d4f;
	font-size: 24rpx;
	font-weight: bold;
}

.warning-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.warning-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	padding-right: 100rpx;
}

.doctor-avatar-small {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 结果解读 */
.result-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.constitution-chart {
	margin: 40rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30rpx;
}

.constitution-circle {
	width: 400rpx;
	height: 400rpx;
	border: 4rpx solid #e6f4ff;
	border-radius: 50%;
	position: relative;
	background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
	box-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.12);
}

.constitution-inner {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
}

.constitution-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	background: linear-gradient(135deg, #ffffff 0%, #f8fdff 100%);
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.15);
	border: 2rpx solid #e6f4ff;
}

.constitution-type {
	font-size: 36rpx;
	font-weight: 600;
	color: #1890ff;
	margin-bottom: 5rpx;
}

.constitution-indicator {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.indicator-dot {
	position: absolute;
	width: 20rpx;
	height: 20rpx;
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	border-radius: 50%;
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
	animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
	}
	50% {
		transform: scale(1.2);
		box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.6);
	}
	100% {
		transform: scale(1);
		box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
	}
}

.direction-label {
	position: absolute;
	font-size: 28rpx;
	font-weight: 500;
	color: #1890ff;
	background: rgba(255, 255, 255, 0.9);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	border: 1rpx solid #e6f4ff;
	transform: translate(-50%, -50%);
}

.direction-label.top {
	top: 20rpx;
	left: 50%;
}

.direction-label.right {
	right: 20rpx;
	top: 50%;
	transform: translate(50%, -50%);
}

.direction-label.bottom {
	bottom: 20rpx;
	left: 50%;
	transform: translate(-50%, 50%);
}

.direction-label.left {
	left: 20rpx;
	top: 50%;
	transform: translate(-50%, -50%);
}

.scale-marks {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
}

.scale-mark {
	position: absolute;
	width: 2rpx;
	height: 20rpx;
	background: linear-gradient(to bottom, #1890ff, rgba(24, 144, 255, 0.3));
	top: 10rpx;
	left: 50%;
	transform-origin: 50% 190rpx;
	border-radius: 1rpx;
}

.constitution-status {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 10rpx;
	background: rgba(255, 255, 255, 0.9);
	padding: 12rpx 20rpx;
	border-radius: 25rpx;
	border: 1rpx solid #e6f4ff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.status-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);
}

.status-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #333;
}

.result-description {
	margin: 30rpx 0;
}

.result-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 15rpx;
}

.result-text.secondary {
	font-size: 24rpx;
	color: #999;
}

.result-note {
	background-color: #f8f9fa;
	padding: 30rpx;
	border-radius: 15rpx;
	position: relative;
}

.note-content {
	padding-right: 100rpx;
}

.note-text {
	font-size: 24rpx;
	color: #999;
	line-height: 1.6;
}

.doctor-avatar-note {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* VIP会员专享 */
.vip-section {
	margin: 30rpx;
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	border-radius: 15rpx;
	padding: 30rpx;
}

.vip-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.vip-icon {
	font-size: 32rpx;
}

.vip-text {
	font-size: 28rpx;
	color: #8b4513;
	font-weight: 500;
}

.vip-card {
	text-align: center;
}

.vip-title {
	font-size: 32rpx;
	color: #8b4513;
	margin-bottom: 10rpx;
	display: block;
}

.vip-subtitle {
	font-size: 48rpx;
	color: #8b4513;
	font-weight: bold;
}

/* 报告解读 */
.report-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.report-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.report-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.member-badge {
	background-color: #1890ff;
	color: white;
	padding: 5rpx 10rpx;
	border-radius: 15rpx;
	font-size: 24rpx;
}

.symptoms-summary {
	margin-bottom: 30rpx;
}

.summary-text {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
	margin-left: 10rpx;
}

.symptom-highlight {
	font-size: 28rpx;
	font-weight: 500;
	color: #1890ff;
}

.main-symptom-detail {
	margin-bottom: 30rpx;
}

.symptom-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.symptom-description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.view-detail-btn {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
	margin-bottom: 30rpx;
}

.doctor-section {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.doctor-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.doctor-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 调理原则 */
.principle-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.principle-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.principle-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.principle-list {
	margin-bottom: 30rpx;
}

.principle-item {
	margin-bottom: 15rpx;
}

.principle-bullet {
	color: #ff4d4f;
	font-size: 24rpx;
	font-weight: bold;
	margin-right: 10rpx;
}

.principle-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.view-principle-btn {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
	margin-bottom: 30rpx;
}

.custom-plan-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 今日营养目标 */
.nutrition-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.nutrition-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.nutrition-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.chart-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.chart-left {
	flex: 1;
}

.pie-chart {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	position: relative;
	margin-bottom: 20rpx;
}

.pie-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	background-color: white;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.calories-number {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
	color: #333;
}

.calories-unit {
	font-size: 20rpx;
	color: #666;
}

.chart-right {
	flex: 1;
}

.nutrition-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
}

.nutrition-color {
	width: 20rpx;
	height: 20rpx;
	border-radius: 4rpx;
	flex-shrink: 0;
}

.nutrition-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	flex: 1;
}

.nutrition-percent {
	font-size: 24rpx;
	color: #333;
	width: 60rpx;
	text-align: right;
}

.nutrition-amount {
	font-size: 24rpx;
	color: #666;
	width: 80rpx;
	text-align: right;
}

/* 今日专属膳方 */
.recipe-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.recipe-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.recipe-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.adjust-plan {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-left: auto;
}

.recipe-card {
	background-color: #f0f0f0;
	border-radius: 15rpx;
	padding: 30rpx;
}

.recipe-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.recipe-info {
	flex: 1;
}

.recipe-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.recipe-effect {
	font-size: 28rpx;
	color: #666;
}

.recipe-image-container {
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	overflow: hidden;
}

.recipe-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.doctor-recommendation {
	margin-top: 20rpx;
}

/* 底部按钮 */
.bottom-buttons {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	background-color: white;
	margin-top: 30rpx;
}

.btn-secondary {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	border: 2rpx solid #ddd;
	border-radius: 25rpx;
	font-size: 28rpx;
	color: #666;
}

.btn-primary {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	background-color: #1890ff;
	color: white;
	border-radius: 25rpx;
	font-size: 28rpx;
}
</style>