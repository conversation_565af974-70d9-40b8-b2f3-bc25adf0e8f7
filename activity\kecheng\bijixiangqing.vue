<template>
<view class="container">
	<block v-if="isload">
		<view class="note-detail-card">
			<!-- 课程信息 -->
			<view class="course-header">
				<view class="course-info">
					<image class="course-pic" :src="noteDetail.kecheng_pic" mode="aspectFill"></image>
					<view class="course-detail">
						<text class="course-name">{{noteDetail.kecheng_name}}</text>
						<text class="chapter-name" v-if="noteDetail.chapter_name">{{noteDetail.chapter_name}}</text>
					</view>
				</view>
			</view>
			
			<!-- 笔记内容 -->
			<view class="note-content">
				<rich-text :nodes="noteDetail.content"></rich-text>
			</view>
			
			<!-- 笔记信息 -->
			<view class="note-info">
				<view class="info-item" v-if="noteDetail.note_time">
					<text class="info-label">时间点:</text>
					<text class="info-value">{{noteDetail.note_time}}</text>
				</view>
				<view class="info-item" v-if="noteDetail.kechengset && noteDetail.kechengset.notes_need_progress == 1">
					<text class="info-label">学习进度:</text>
					<text class="info-value">{{noteDetail.study_progress}}%</text>
				</view>
				<view class="info-item">
					<text class="info-label">创建时间:</text>
					<text class="info-value">{{noteDetail.createtime_format}}</text>
				</view>
				<view class="info-item" v-if="noteDetail.updatetime && noteDetail.updatetime != noteDetail.createtime">
					<text class="info-label">更新时间:</text>
					<text class="info-value">{{noteDetail.updatetime_format}}</text>
				</view>
			</view>
			
			<!-- 用户信息 -->
			<view class="user-info">
				<image class="user-avatar" :src="noteDetail.headimg || pre_url+'/static/img/default_avatar.png'" mode="aspectFill"></image>
				<text class="user-nickname">{{noteDetail.nickname || '微信昵称'}}</text>
			</view>
			
			<!-- 操作按钮 -->
			<view class="note-actions">
				<view class="action-btn edit" @tap="editNote">
					<text class="iconfont icon-kecheng"></text>
					<text>前往课程页面</text>
				</view>
				<view class="action-btn delete" @tap="deleteNote">
					<text class="iconfont icon-shanchu"></text>
					<text>删除</text>
				</view>
				<!-- <view class="action-btn share" @tap="shareNote">
					<text class="iconfont icon-fenxiang"></text>
					<text>分享</text>
				</view> -->
				<view class="action-btn back" @tap="goBack">
					<text class="iconfont icon-fanhui"></text>
					<text>返回</text>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      loading: false,
      isload: false,
      noteId: 0,
      noteDetail: {},
      pre_url: app.globalData.pre_url,
    };
  },
  onLoad: function (options) {
    if (options.id) {
      this.noteId = options.id;
      this.getNoteDetail();
    } else {
      app.error('笔记ID不能为空');
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  onShareAppMessage: function () {
    return {
      title: '学习笔记: ' + (this.noteDetail.content_preview || '查看我的学习笔记'),
      path: '/tiantianshande/activity/kecheng/bijixiangqing?id=' + this.noteId,
      imageUrl: this.noteDetail.kecheng_pic || ''
    }
  },
  methods: {
    // 获取笔记详情
    getNoteDetail: function() {
      var that = this;
      that.loading = true;
      
      app.post('ApiKechengNotes/getNoteDetail', {
        id: that.noteId
      }, function(res) {
        that.loading = false;
        that.isload = true;
        
        if(res.status == 1) {
          that.noteDetail = res.data;
          
          // 生成预览内容（用于分享）
          if(that.noteDetail.content) {
            // 移除HTML标签，获取纯文本
            let plainText = that.noteDetail.content.replace(/<[^>]+>/g, '');
            // 截取前50个字符作为预览
            that.noteDetail.content_preview = plainText.substring(0, 50) + (plainText.length > 50 ? '...' : '');
          }
          
          // 设置页面标题
          uni.setNavigationBarTitle({
            title: '笔记详情'
          });
          
        } else {
          app.error(res.msg || '获取笔记详情失败');
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
    },
    
    // 编辑笔记
    editNote: function() {
      app.goto('mldetail?id=' + this.noteDetail.chapter_id + '&kcid=' + this.noteDetail.kcid + '&note_id=' + this.noteDetail.id);
    },
    
    // 删除笔记
    deleteNote: function() {
      var that = this;
      app.confirm('确定要删除这条笔记吗？', function() {
        app.showLoading('删除中...');
        app.post('ApiKechengNotes/deleteNote', {
          id: that.noteId
        }, function(res) {
          app.showLoading(false);
          if(res.status == 1) {
            app.success(res.msg);
            // 删除成功后返回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            app.error(res.msg);
          }
        });
      });
    },
    
    // 返回上一页
    goBack: function() {
      uni.navigateBack();
    },
    
    // 分享笔记
    shareNote: function() {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    }
  }
};
</script>

<style>
.container {
  padding: 24rpx;
  background-color: #f7f9fd;
  min-height: 100vh;
}

.note-detail-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);
  margin-bottom: 24rpx;
  position: relative;
  overflow: hidden;
}

.note-detail-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #10aeff, #1484fa);
}

/* 课程信息样式 */
.course-header {
  margin-bottom: 30rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
}

.course-info {
  display: flex;
  align-items: center;
}

.course-pic {
  width: 90rpx;
  height: 90rpx;
  border-radius: 12rpx;
  margin-right: 18rpx;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
  border: 1rpx solid rgba(0,0,0,0.05);
}

.course-detail {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.course-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.chapter-name {
  font-size: 24rpx;
  color: #666;
  background: rgba(16,174,255,0.08);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  display: inline-block;
  border: 1rpx solid rgba(16,174,255,0.2);
}

/* 笔记内容样式 */
.note-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  margin: 30rpx 0;
  padding: 30rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border-left: 6rpx solid #10aeff;
  min-height: 200rpx;
  box-shadow: inset 0 0 10rpx rgba(0,0,0,0.03);
}

/* 笔记信息样式 */
.note-info {
  margin: 30rpx 0;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
  background: #fff;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.info-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  margin: 30rpx 0;
  padding: 20rpx 0;
  border-top: 1rpx solid rgba(0,0,0,0.05);
}

.user-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.user-nickname {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

/* 操作按钮样式 */
.note-actions {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 30rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid rgba(0,0,0,0.05);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  transition: all 0.3s;
  border: 1rpx solid transparent;
}

.action-btn .iconfont {
  font-size: 28rpx;
  margin-right: 6rpx;
}

.action-btn.edit {
  background: rgba(16,174,255,0.08);
  color: #10aeff;
  border-color: rgba(16,174,255,0.2);
}

.action-btn.delete {
  background: rgba(255,71,87,0.08);
  color: #ff4757;
  border-color: rgba(255,71,87,0.2);
}

.action-btn.share {
  background: rgba(75,191,107,0.08);
  color: #4bbf6b;
  border-color: rgba(75,191,107,0.2);
}

.action-btn.back {
  background: rgba(108,117,125,0.08);
  color: #6c757d;
  border-color: rgba(108,117,125,0.2);
}

.action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}
</style> 