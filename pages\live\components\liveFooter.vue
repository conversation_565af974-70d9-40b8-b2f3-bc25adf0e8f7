<template>
	<view class="live-footer">
		<!-- 底部操作栏 -->
		<view class="footer-content">
			<!-- 消息输入框 -->
			<view class="input-box">
				<input 
					type="text"
					v-model="message"
					placeholder="说点什么..."
					placeholder-style="color: rgba(255, 255, 255, 0.5)"
					confirm-type="send"
					@confirm="handleSendMsg"
				/>
			</view>
			
			<!-- 操作按钮组 -->
			<view class="action-group">
				<!-- 礼物按钮 -->
				<view class="action-item" @tap="openGiftPanel">
					<text class="iconfont icon-gift"></text>
				</view>
				
				<!-- 点赞按钮 -->
				<view class="action-item" @tap="handleLike">
					<text class="iconfont icon-like"></text>
				</view>
			</view>
		</view>
		
		<!-- 礼物面板 -->
		<uni-popup ref="giftPopup" type="bottom">
			<view class="gift-panel">
				<view class="panel-header">
					<text class="title">选择礼物</text>
					<text class="close" @tap="closeGiftPanel">×</text>
				</view>
				
				<view class="gift-list">
					<view 
						class="gift-item"
						v-for="(item, index) in giftList"
						:key="index"
						@tap="handleSendGift(item)"
					>
						<image :src="item.image" mode="aspectFit"></image>
						<text class="gift-name">{{item.name}}</text>
						<text class="gift-price">{{item.price}}金币</text>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 点赞动画容器 -->
		<view class="like-animation-container" :style="{ opacity: likeAnimationOpacity }">
			<view 
				class="like-item"
				v-for="(item, index) in likeItems"
				:key="item.id"
				:style="item.style"
			>
				<text class="iconfont icon-like" :style="{ color: item.color }"></text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		room_id: {
			type: [Number, String],
			required: true
		}
	},
	
	data() {
		return {
			message: '',
			giftList: [
				{
					id: 1,
					name: '鲜花',
					price: 1,
					image: '/static/images/gifts/flower.png'
				},
				{
					id: 2,
					name: '蛋糕',
					price: 5,
					image: '/static/images/gifts/cake.png'
				},
				{
					id: 3,
					name: '钻石',
					price: 20,
					image: '/static/images/gifts/diamond.png'
				},
				{
					id: 4,
					name: '皇冠',
					price: 100,
					image: '/static/images/gifts/crown.png'
				}
			],
			likeItems: [],
			likeAnimationOpacity: 0,
			likeColors: ['#FF4656', '#FF8C9A', '#FFB6C1', '#FFC0CB']
		}
	},
	
	methods: {
		// 发送消息
		handleSendMsg() {
			if (!this.message.trim()) return
			
			this.$emit('sendMsg', this.message)
			this.message = ''
		},
		
		// 打开礼物面板
		openGiftPanel() {
			this.$refs.giftPopup.open()
		},
		
		// 关闭礼物面板
		closeGiftPanel() {
			this.$refs.giftPopup.close()
		},
		
		// 发送礼物
		handleSendGift(gift) {
			this.$emit('sendGift', gift)
			this.closeGiftPanel()
		},
		
		// 点赞动画
		handleLike() {
			this.$emit('sendLike')
			this.addLikeAnimation()
		},
		
		// 添加点赞动画
		addLikeAnimation() {
			const id = Date.now()
			const color = this.likeColors[Math.floor(Math.random() * this.likeColors.length)]
			
			const item = {
				id,
				color,
				style: {
					left: Math.random() * 100 + 'rpx',
					animation: `likeFloat 2s ease-out forwards`
				}
			}
			
			this.likeItems.push(item)
			this.likeAnimationOpacity = 1
			
			setTimeout(() => {
				const index = this.likeItems.findIndex(i => i.id === id)
				if (index > -1) {
					this.likeItems.splice(index, 1)
				}
				if (this.likeItems.length === 0) {
					this.likeAnimationOpacity = 0
				}
			}, 2000)
		}
	}
}
</script>

<style lang="scss">
.live-footer {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 100;
	
	.footer-content {
		display: flex;
		align-items: center;
		padding: 20rpx;
		background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
		
		.input-box {
			flex: 1;
			margin-right: 20rpx;
			
			input {
				background: rgba(255, 255, 255, 0.2);
				border-radius: 100rpx;
				height: 70rpx;
				padding: 0 30rpx;
				color: #fff;
				font-size: 28rpx;
			}
		}
		
		.action-group {
			display: flex;
			align-items: center;
			
			.action-item {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 20rpx;
				
				.iconfont {
					color: #fff;
					font-size: 40rpx;
				}
			}
		}
	}
	
	.gift-panel {
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding-bottom: env(safe-area-inset-bottom);
		
		.panel-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx;
			border-bottom: 1rpx solid #eee;
			
			.title {
				font-size: 28rpx;
				color: #333;
				font-weight: bold;
			}
			
			.close {
				font-size: 40rpx;
				color: #999;
				padding: 10rpx;
			}
		}
		
		.gift-list {
			display: flex;
			flex-wrap: wrap;
			padding: 20rpx;
			
			.gift-item {
				width: 25%;
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 20rpx;
				
				image {
					width: 80rpx;
					height: 80rpx;
					margin-bottom: 10rpx;
				}
				
				.gift-name {
					font-size: 24rpx;
					color: #333;
					margin-bottom: 4rpx;
				}
				
				.gift-price {
					font-size: 22rpx;
					color: #FF4656;
				}
			}
		}
	}
	
	.like-animation-container {
		position: fixed;
		right: 100rpx;
		bottom: 120rpx;
		width: 200rpx;
		height: 400rpx;
		pointer-events: none;
		transition: opacity 0.3s;
		
		.like-item {
			position: absolute;
			bottom: 0;
			
			.iconfont {
				font-size: 40rpx;
			}
		}
	}
}

@keyframes likeFloat {
	0% {
		transform: scale(1) translateY(0);
		opacity: 1;
	}
	100% {
		transform: scale(1.5) translateY(-400rpx);
		opacity: 0;
	}
}
</style> 