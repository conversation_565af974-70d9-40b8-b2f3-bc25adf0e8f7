{
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarBackgroundColor": "#FFFFFF",
		"navigationBarTitleText": "",
		"h5": {
			"titleNView": false
		},
		"app-plus": {
			"scrollIndicator": "none"
		}
	},
	"pages": [{
			"path": "pages/index/index",
			"style": {
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
				
			}
		},
		{
			"path": "pages/index/main",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		
		{
			"path": "pages/index/location",
			"style": {
				"navigationBarTitleText": "选择位置",
				"navigationBarBackgroundColor": "#ffffff",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/index/webView",
			"style": {
				"titleNView": false
			}
		},
		{
			"path": "pages/index/webView3",
			"style": {
				"titleNView": false
			}
		},
		{
			"path": "pages/index/webView2",
			"style": {
				"titleNView": false
			}
		},
		{
			"path": "pages/index/reg",
			"style": {
				"navigationBarTitleText": "用户注册",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/index/login",
			"style": {
				"navigationBarTitleText": "用户登录",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/index/city",
			"style": {
				"navigationBarTitleText": "多城市",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/index/getpwd",
			"style": {
				"navigationBarTitleText": "找回密码",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/index/bind",
			"style": {
				"navigationBarTitleText": "绑定管理员",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/address/address",
			"style": {
				"navigationBarTitleText": "收货地址",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/address/addressadd",
			"style": {
				"navigationBarTitleText": "编辑收货地址",
				"enablePullDownRefresh": false
			}
		},

		{
			"path": "pages/my/usercenter",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		
		{
			"path": "pages/pay/pay",
			"style": {
				"navigationBarTitleText": "收银台",
				"enablePullDownRefresh": true
			}
		}
	],
	"subPackages": [{
			"root": "shopPackage",
			"pages": [
				{
					"path": "shop/product",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/commentlist",
					"style": {
						"navigationBarTitleText": "商品评价",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/cart",
					"style": {
						"navigationBarTitleText": "购物车",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/prolist",
					"style": {
						"navigationBarTitleText": "商品列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/search",
					"style": {
						"navigationBarTitleText": "商品搜索",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/buy",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/mendian",
					"style": {
						"navigationBarTitleText": "门店",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/classify",
					"style": {
						"navigationBarTitleText": "商品分类",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/classify2",
					"style": {
						"navigationBarTitleText": "商品分类2",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/daigoulist",
					"style": {
						"navigationBarTitleText": "代购列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/daikebuy",
					"style": {
						"navigationBarTitleText": "代客购买",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/daigoupick",
					"style": {
						"navigationBarTitleText": "代购选择",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/productcopy",
					"style": {
						"navigationBarTitleText": "商品详情备份",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/fastbuy",
					"style": {
						"navigationBarTitleText": "快速购买",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/fastbuy2",
					"style": {
						"navigationBarTitleText": "快速购买2",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/category1",
					"style": {
						"navigationBarTitleText": "分类样式1",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/category2",
					"style": {
						"navigationBarTitleText": "分类样式2",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/category3",
					"style": {
						"navigationBarTitleText": "分类样式3",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/category4",
					"style": {
						"navigationBarTitleText": "分类样式4",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/product/product",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "activity",
			"pages": [{
					"path": "commission/index",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "commission/posterdengji",
					"style": {
						"navigationBarTitleText": "分享等级海报",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/teamchart",
					"style": {
						"navigationBarTitleText": "团队关系图",
						"enablePullDownRefresh": false
					}
				},
				
{
					"path": "commission/moneylog2",
					"style": {
						"navigationBarTitleText": "脱离回归记录",
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "commission/commissionlog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/ordergoodsinfo",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/commissionloghuang",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/commissionloglv",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/commissionlogxiaofeizhi",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/commissionlogMendian",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/orderMendian",
					"style": {
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "commission/withdraw",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/myteam",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/myteamnew",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/myteamnew2",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/myxingjilog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/buy",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/myyukucun",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/downorder",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/poster",
					"style": {
						"navigationBarTitleText": "分享海报",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/fhlog",
					"style": {
						"navigationBarTitleText": "分红记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/fhorder",
					"style": {
						"navigationBarTitleText": "分红订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/fenhong",
					"style": {
						"navigationBarTitleText": "股东分红",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/teamfenhong",
					"style": {
						"navigationBarTitleText": "团队分红",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/areafenhong",
					"style": {
						"navigationBarTitleText": "区域代理分红",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/orderYeji",
					"style": {
						"navigationBarTitleText": "业绩统计",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/myteamline",
					"style": {
						"navigationBarTitleText": "我的上下级",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/mysameline",
					"style": {
						"navigationBarTitleText": "同等级会员",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/commissionrecord",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/commissionranking",
					"style": {
						"navigationBarTitleText": "佣金排行榜",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/index",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/prolist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/product",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/cart",
					"style": {
						"navigationBarTitleText": "购物车",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/rank",
					"style": {
						"navigationBarTitleText": "积分排行",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/buy",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "scoreshop/orderlist",
					"style": {
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "scoreshop/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "collage/index",
					"style": {
						"navigationBarTitleText": "拼团商品列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/product",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/team",
					"style": {
						"navigationBarTitleText": "参团成员",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/buy",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "collage/orderlist",
					"style": {
						"navigationBarTitleText": "拼团订单列表",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "collage/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/comment",
					"style": {
						"navigationBarTitleText": "我要评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "collage/commentlist",
					"style": {
						"navigationBarTitleText": "商品评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "collage/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "seckill/index",
					"style": {
						"navigationBarTitleText": "秒杀列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "seckill/product",
					"style": {
						"navigationBarTitleText": "秒杀商品",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "seckill/buy",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "seckill/orderlist",
					"style": {
						"navigationBarTitleText": "秒杀订单列表",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "seckill/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "seckill/comment",
					"style": {
						"navigationBarTitleText": "我要评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "seckill/commentlist",
					"style": {
						"navigationBarTitleText": "商品评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "seckill/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuangou/prolist",
					"style": {
						"navigationBarTitleText": "团购列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuangou/product",
					"style": {
						"navigationBarTitleText": "团购商品",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuangou/buy",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuangou/orderlist",
					"style": {
						"navigationBarTitleText": "团购订单列表",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "tuangou/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuangou/comment",
					"style": {
						"navigationBarTitleText": "我要评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuangou/commentlist",
					"style": {
						"navigationBarTitleText": "商品评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuangou/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "xydzp/index",
					"style": {
						"navigationBarTitleText": "幸运大转盘",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "xydzp/myprize",
					"style": {
						"navigationBarTitleText": "我的奖品",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "choujiang/logistics",
					"style": {
						"navigationBarTitleText": "奖品物流",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "ggk/index",
					"style": {
						"navigationBarTitleText": "刮刮卡",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "ggk/myprize",
					"style": {
						"navigationBarTitleText": "我的奖品",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/index",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/ltlist",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/detail",
					"style": {
						"navigationBarTitleText": "动态详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/fatie",
					"style": {
						"navigationBarTitleText": "发帖",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/pinglun",
					"style": {
						"navigationBarTitleText": "评论",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/fatielog",
					"style": {
						"navigationBarTitleText": "发帖记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "luntan/focuslog",
					"style": {
						"navigationBarTitleText": "关注记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "peisong/dating",
					"style": {
						"navigationBarTitleText": "接单大厅",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisong/orderlist",
					"style": {
						"navigationBarTitleText": "我的配送单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisong/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisong/my",
					"style": {
						"navigationBarTitleText": "个人中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisong/moneylog",
					"style": {
						"navigationBarTitleText": "余额明细"
					}
				},
				{
					"path": "peisong/withdraw",
					"style": {
						"navigationBarTitleText": "余额提现"
					}
				},
				{
					"path": "peisong/setinfo",
					"style": {
						"navigationBarTitleText": "提现设置"
					}
				},
				{
					"path": "peisongdan/dating",
					"style": {
						"navigationBarTitleText": "接单大厅",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisongdan/orderlist",
					"style": {
						"navigationBarTitleText": "我的配送单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisongdan/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisongdan/my",
					"style": {
						"navigationBarTitleText": "个人中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisongdan/moneylog",
					"style": {
						"navigationBarTitleText": "余额明细"
					}
				},
				{
					"path": "peisongdan/withdraw",
					"style": {
						"navigationBarTitleText": "余额提现"
					}
				},
				{
					"path": "peisongdan/setinfo",
					"style": {
						"navigationBarTitleText": "提现设置"
					}
				},
				
				{
					"path": "shortvideo/index",
					"style": {
						"navigationBarTitleText": "短视频列表"
					}
				},
				{
					"path": "shortvideo/detail",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"disableScroll": true,
						"mp-toutiao": {
							"navigationStyle": "default"
						},
						"mp-alipay": {
							"transparentTitle": "always",
							"titlePenetrate": "YES"
						},
						"titleNView": false
					}
				},
				{
					"path": "shortvideo/uploadvideo",
					"style": {
						"navigationBarTitleText": "发布短视频"
					}
				},
				{
					"path": "shortvideo/myupload",
					"style": {
						"navigationBarTitleText": "我的发表记录"
					}
				},
				{
					"path": "kecheng/list",
					"style": {
						"navigationBarTitleText": "课程列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/learninglog",
					"style": {
						"navigationBarTitleText": "学习记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/bijixiangqing",
					"style": {
						"navigationBarTitleText": "笔记详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/wodezhanghu",
					"style": {
						"navigationBarTitleText": "我的账户",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/learningbiji",
					"style": {
						"navigationBarTitleText": "课程笔记",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/product",
					"style": {
						"navigationBarTitleText": "课程详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kecheng/mldetail",
					"style": {
						"navigationBarTitleText": "目录详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/orderlist",
					"style": {
						"navigationBarTitleText": "我的课程",
						"enablePullDownRefresh": false,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "kecheng/tiku",
					"style": {
						"navigationBarTitleText": "开始答题",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/complete",
					"style": {
						"navigationBarTitleText": "答题完成",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/recordlog",
					"style": {
						"navigationBarTitleText": "答题记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/error",
					"style": {
						"navigationBarTitleText": "错题回顾",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/category3",
					"style": {
						"navigationBarTitleText": "课程分类",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/category",
					"style": {
						"navigationBarTitleText": "课程分类",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/search",
					"style": {
						"navigationBarTitleText": "课程搜索",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "luckycollage/classify",
					"style": {
						"navigationBarTitleText": "幸运拼团分类",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/prolist",
					"style": {
						"navigationBarTitleText": "商品列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/product",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/team",
					"style": {
						"navigationBarTitleText": "参团成员",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/buy",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "luckycollage/orderlist",
					"style": {
						"navigationBarTitleText": "拼团订单列表",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "luckycollage/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/comment",
					"style": {
						"navigationBarTitleText": "我要评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "luckycollage/commentlist",
					"style": {
						"navigationBarTitleText": "商品评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "luckycollage/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/index",
					"style": {
						"navigationBarTitleText": "开团列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/product2",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "express/index",
					"style": {
						"navigationBarTitleText": "快递查询",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "express/mail",
					"style": {
						"navigationBarTitleText": "寄快递",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "express/addressadd",
					"style": {
						"navigationBarTitleText": "新增地址",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "express/address",
					"style": {
						"navigationBarTitleText": "地址薄",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "express/logistics",
					"style": {
						"navigationBarTitleText": "物流信息",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "express/kddetail",
					"style": {
						"navigationBarTitleText": "快递详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/index",
					"style": {
						"navigationBarTitleText": "工单提交",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/detail",
					"style": {
						"navigationBarTitleText": "工单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/record",
					"style": {
						"navigationBarTitleText": "工单列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "yuebao/yuebaolog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "yuebao/withdraw",
					"style": {
						"enablePullDownRefresh": true
					}
				}
				
			]
		},
		{
			"root": "pagesExt",
			"pages": [{
					"path": "order/orderlist",
					"style": {
						"navigationBarTitleText": "订单列表",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/batchInvoice",
					"style": {
						"navigationBarTitleText": "批量开票",
						"enablePullDownRefresh": true
					}
				},
				{
						"path": "order/orderzhangdan",
						"style": {
							"navigationBarTitleText": "订单列表",
							"enablePullDownRefresh": true,
							"titleNView": {
								"searchInput": {
									"placeholder": "输入关键字搜索",
									"borderRadius": "15px"
								},
								"buttons": [{
									"type": "home"
								}]
							}
						}
					},
				{
					"path": "jifenchi/jingtaiA",
					"style": {
						"navigationBarTitleText": "我的静态积分明细",
						"enablePullDownRefresh": true,
						"titleNView": {
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				
				{
					"path": "ranking/list",
					"style": {
						"navigationBarTitleText": "团队风云榜",
						"enablePullDownRefresh": true,
						"titleNView": {
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "ranking/listjituan",
					"style": {
						"navigationBarTitleText": "集团风云榜",
						"enablePullDownRefresh": true,
						"titleNView": {
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "jifenchi/jingtaiB",
					"style": {
						"navigationBarTitleText": "我的动态积分明细",
						"enablePullDownRefresh": true,
						"titleNView": {
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "jifenchi/jingtaiZong",
					"style": {
						"navigationBarTitleText": "我的积分池明细",
						"enablePullDownRefresh": true,
						"titleNView": {
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "jifenchi/myjifenchi",
					"style": {
						"navigationBarTitleText": "积分池",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "paidui/paidui",
					"style": {
						"navigationBarTitleText": "排队补贴",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "paidui/periods",
					"style": {
						"navigationBarTitleText": "分期管理",
						"enablePullDownRefresh": true
					}
				},
				// {
				//   "path": "paidui/ceshipaidui",
				//   "style": {
				//     "enablePullDownRefresh": true
				//   }
				// },
				{
					"path": "tuozhanyuan/mylist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/apply",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/tuozhanteam",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/myteam",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/tuozhanfeimingxi",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/index",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/tuozhancrm",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/shangjipull",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/shangjilist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/shangjidetail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/shangjixiugai",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/shagjigenjinjilu",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				

				// {
				//   "path": "tuozhanyuan/myteam",
				//   "style": {
				//     "enablePullDownRefresh": true
				//   }
				// },
				{
					"path": "tuozhanyuan/shiyebutongji",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/myshiyebu",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/index",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/bind",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/city-detail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/coverage",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/withdraw",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/withdrawlog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/income",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/merchant",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cityagent/merchant_detail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "maidan/maidanlog",
					"style": {
						"navigationBarTitleText": "买单付款记录",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入订单号搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "maidan/maidandetail",
					"style": {
						"navigationBarTitleText": "买单付款详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "maidan/pay",
					"style": {
						"navigationBarTitleText": "买单付款",
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "maidanpays/maidanlog",
					"style": {
						"navigationBarTitleText": "买单付款记录",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入订单号搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "maidanpays/maidandetail",
					"style": {
						"navigationBarTitleText": "买单付款详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "maidanpays/pay",
					"style": {
						"navigationBarTitleText": "买单付款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "article/artlist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "article/detail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "article/pinglun",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": true
					}
				},
			
				{
					"path": "zhuanzhang/zhuanzhang",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/zhuanzhangjishou",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/zhuanzhanggxz",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/zhuanzhuanghjf",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/heizhuanzhang",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/heizhuanzhangyue",
					"navigationBarTitleText": "链接转账",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/zhuanjifen",
					"navigationBarTitleText": "积分转账",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/zhuanjifen2",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/zhuanxiaofeizhi",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/zhuanxiaofeizhidetail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "zhuanzhang/score_to_commission",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/refundlist",
					"style": {
						"navigationBarTitleText": "退款订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},

				{
					"path": "order/refundDetail",
					"style": {
						"navigationBarTitleText": "退款详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/comment",
					"style": {
						"navigationBarTitleText": "我要评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "order/commentdp",
					"style": {
						"navigationBarTitleText": "店铺评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "order/commentps",
					"style": {
						"navigationBarTitleText": "评价配送员",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "order/detail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/logistics",
					"style": {
						"navigationBarTitleText": "查看物流",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/refundSelect",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/invoice",
					"style": {
						"navigationBarTitleText": "发票",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/index",
					"style": {
						"navigationBarTitleText": "商家详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/xinindex",
					"style": {
						"navigationBarTitleText": "新商家详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/sales-ranking",
					"style": {
						"navigationBarTitleText": "商家详情",
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "business/shoprecharge",
					"style": {
						"navigationBarTitleText": "商家充值",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/main",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/clist",
					"style": {
						"navigationBarTitleText": "商家列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/blist",
					"style": {
						"navigationBarTitleText": "商家列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/dlist",
					"style": {
						"navigationBarTitleText": "商家新列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/cityblist",
					"style": {
						"navigationBarTitleText": "城市商家列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/apply",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/applyduochengshi",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/zhunongapply",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/commentlist",
					"style": {
						"navigationBarTitleText": "商家评价",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/clist2",
					"style": {
						"navigationBarTitleText": "选择商家",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "business/maps",
					"style": {
						"navigationBarTitleText": "商家地图",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "lipin/index",
					"style": {
						"navigationBarTitleText": "兑换中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "lipin/prodh",
					"style": {
						"navigationBarTitleText": "兑换商品",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "lipin/dhlog",
					"style": {
						"navigationBarTitleText": "兑换记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "lipin2/index",
					"style": {
						"navigationBarTitleText": "兑换中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "lipin2/prodh",
					"style": {
						"navigationBarTitleText": "兑换商品",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "lipin2/dhlog",
					"style": {
						"navigationBarTitleText": "兑换记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "sign/index",
					"style": {
						"navigationBarTitleText": "签到",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "sign/signrecord",
					"style": {
						"navigationBarTitleText": "签到记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupon/record",
					"style": {
						"navigationBarTitleText": "使用记录"
					}
				},
				{
					"path": "coupons/record",
					"style": {
						"navigationBarTitleText": "使用记录"
					}
				},
				{
					"path": "coupons/couponlist",
					"style": {
						"navigationBarTitleText": "领卡中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupons/couponlist2",
					"style": {
						"navigationBarTitleText": "领卡中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupons/mycoupon",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupons/coupondetail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupons/dh",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupons/prodh",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupons/coupongive",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cycle/product",
					"style": {
						"navigationBarTitleText": "商品详情"
					}
				},
				{
					"path": "cycle/planDetail",
					"style": {
						"navigationBarTitleText": "计划详情"
					}
				},
				{
					"path": "cycle/buy",
					"style": {
						"navigationBarTitleText": "订单确认"
					}
				},
				{
					"path": "cycle/checkDate",
					"style": {
						"navigationBarTitleText": "选择日期"
					}
				},
				{
					"path": "cycle/planList",
					"style": {
						"navigationBarTitleText": "计划列表"
					}
				},
				{
					"path": "cycle/orderList",
					"style": {
						"navigationBarTitleText": "订单列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cycle/orderDetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationBarTextStyle": "white",
						"navigationBarBackgroundColor": "#FD4A46"
					}
				},
				{
					"path": "cycle/planWrite",
					"style": {
						"navigationBarTitleText": "填写计划"
					}
				},
				{
					"path": "cycle/logistics",
					"style": {
						"navigationBarTitleText": "物流信息"
					}
				},
				{
					"path": "cycle/refund",
					"style": {
						"navigationBarTitleText": "申请退款"
					}
				},
				{
					"path": "cycle/comment",
					"style": {
						"navigationBarTitleText": "评价"
					}
				},
				{
					"path": "cycle/commentps",
					"style": {
						"navigationBarTitleText": "评价配送员"
					}
				},
				{
					"path": "cycle/commentlist",
					"style": {
						"navigationBarTitleText": "评论列表"
					}
				},
				{
					"path": "cycle/prolist",
					"style": {
						"navigationBarTitleText": "周期购商品"
					}
				},
				{
					"path": "zuji/prolist",
					"style": {
						"navigationBarTitleText": "商品详情"
					}
				},
				{
					"path": "zuji/prolistzuji",
					"style": {
						"navigationBarTitleText": "商品详情"
					}
				},
				{
					"path": "zuji/buy",
					"style": {
						"navigationBarTitleText": "提交表单"
					}
				},
				{
					"path": "zuji/submitBuy",
					"style": {
						"navigationBarTitleText": "提交表单"
					}
				},

				{
					"path": "yueke/orderlist",
					"style": {
						"navigationBarTitleText": "订单列表",
						"enablePullDownRefresh": false,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "yueke/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yueke/workerlogin",
					"style": {
						"navigationBarTitleText": "教练登录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yueke/workerorderlist",
					"style": {
						"navigationBarTitleText": "预约记录",
						"enablePullDownRefresh": false,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "yueke/workerorderdetail",
					"style": {
						"navigationBarTitleText": "预约详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mingpian/index",
					"style": {
						"navigationBarTitleText": "名片详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mingpian/edit",
					"style": {
						"navigationBarTitleText": "名片编辑",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mingpian/favorite",
					"style": {
						"navigationBarTitleText": "我的名片夹",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mingpian/readlog",
					"style": {
						"navigationBarTitleText": "查看记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mingpian/favoritelog",
					"style": {
						"navigationBarTitleText": "收藏记录",
						"enablePullDownRefresh": false
					}
				}, {
					"path": "highVoltage/highVoltageApplication",
					"style": {
						"navigationBarTitleText": "高压办电",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "highVoltage/voltageApplicationForm",
					"style": {
						"navigationBarTitleText": "高压办电详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "highVoltage/voltageApply",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "highVoltage/electricityApply",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "kefu/index",
					"style": {
						"navigationBarTitleText": "在线咨询",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "electricityForm/recordList",
					"style": {
						"navigationBarTitleText": "我的提交",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "electricityForm/recordDetail",
					"style": {
						"navigationBarTitleText": "详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "electricityForm/recordReplyList",
					"style": {
						"navigationBarTitleText": "回复列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "levelreward/blist",
					"style": {
						"navigationBarTitleText": "等级推荐奖励列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "levelreward/index",
					"style": {
						"navigationBarTitleText": "奖励规则详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "bpv/index",
					"style": {
						"navigationBarTitleText": "兑换中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuozhanyuan/withdrawtuozhan",
					"style": {
						"enablePullDownRefresh": true
					}
				},

				// {
				//   "path": "tuozhanyuan/myteam",
				//   "style": {
				//     "enablePullDownRefresh": true
				//   }
				// },
				{
					"path": "equity_pool/index",
					"style": {
						"navigationBarTitleText": "股权池",
						"enablePullDownRefresh": true,
						"titleNView": {
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "equity_pool/myEquity",
					"style": {
						"navigationBarTitleText": "我的股权",
						"enablePullDownRefresh": true,
						"titleNView": {
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "equity_pool/ranking",
					"style": {
						"navigationBarTitleText": "股权排行榜",
						"enablePullDownRefresh": true,
						"titleNView": {
							"buttons": [{
								"type": "home"
							}]
						}
					}
				}
			]
		},
		{
			"root": "admin",
			"pages": [


				{
					"path": "index/login",
					"style": {
						"navigationBarTitleText": "管理员登录"
					}
				},
				
				{
					"path": "purchase/index",
					"style": {
						"navigationBarTitleText": "进货管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "purchase/detail",
					"style": {
						"navigationBarTitleText": "进货单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "purchase/create",
					"style": {
						"navigationBarTitleText": "新建进货单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "return/index",
					"style": {
						"navigationBarTitleText": "退货管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "return/detail",
					"style": {
						"navigationBarTitleText": "退货单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "return/selectorder",
					"style": {
						"navigationBarTitleText": "选择退货订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "return/create",
					"style": {
						"navigationBarTitleText": "申请退货",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "管理中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "product/headquarters",
					"style": {
						"navigationBarTitleText": "总部商品"
					}
				},
				{
					"path": "index/setpwd",
					"style": {
						"navigationBarTitleText": "修改密码"
					}
				},
				{
					"path": "index/setinfo",
					"style": {
						"navigationBarTitleText": "店铺设置"
					}
				},
				{
					"path": "index/recharge",
					"style": {
						"navigationBarTitleText": "店铺设置"
					}
				},
				{
					"path": "member/historys",
					"style": {
						"navigationBarTitleText": "历史订单"
					}
				},
				{
					"path": "hexiao/hexiao",
					"style": []
				},
				{
					"path": "hexiao/record",
					"style": {
						"navigationBarTitleText": "我的核销",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入用户昵称搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "finance/index",
					"style": {
						"navigationBarTitleText": "财务"
					}
				},
				{
					"path": "finance/commissionlog",
					"style": []
				},
				{
					"path": "finance/comwithdrawdetail",
					"style": []
				},
				{
					"path": "finance/comwithdrawlog",
					"style": []
				},
				{
					"path": "finance/moneylog",
					"style": []
				},
				{
					"path": "finance/rechargelog",
					"style": {
						"navigationBarTitleText": "充值记录"
					}
				},
				{
					"path": "finance/withdrawdetail",
					"style": []
				},
				{
					"path": "finance/withdrawlog",
					"style": []
				},
				{
					"path": "finance/bmoneylog",
					"style": {
						"navigationBarTitleText": "余额明细"
					}
				},
				{
					"path": "finance/bwithdraw",
					"style": {
						"navigationBarTitleText": "余额提现"
					}
				},
				{
					"path": "finance/bwithdrawlog",
					"style": {
						"navigationBarTitleText": "提现记录"
					}
				},
				{
					"path": "finance/txset",
					"style": {
						"navigationBarTitleText": "提现信息"
					}
				},
				{
					"path": "finance/yuebaowithdrawlog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "finance/yuebaowithdrawdetail",
					"style": []
				},
				{
					"path": "finance/yuebaolog",
					"style": []
				},
				{
					"path": "finance/mdmoneylog",
					"style": {
						"navigationBarTitleText": "余额明细"
					}
				},
				{
					"path": "finance/mdwithdraw",
					"style": {
						"navigationBarTitleText": "余额提现"
					}
				},
				{
					"path": "finance/mdwithdrawlog",
					"style": {
						"navigationBarTitleText": "提现记录"
					}
				},
				{
					"path": "finance/mdtxset",
					"style": {
						"navigationBarTitleText": "提现信息"
					}
				},
				{
					"path": "finance/waitreceivelog",
					"style": {
						"navigationBarTitleText": "待收款明细"
					}
				},
				
				{
					"path": "finance/balance",
					"style": {
						"navigationBarTitleText": "进货款明细",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "finance/businessRecharge",
					"style": {
						"navigationBarTitleText": "商家充值",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "finance/businessRechargeLog",
					"style": {
						"navigationBarTitleText": "充值记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "finance/businessRechargeDetail",
					"style": {
						"navigationBarTitleText": "充值详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "finance/balanceTransfer",
					"style": {
						"navigationBarTitleText": "余额转入进货款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kefu/index",
					"style": {
						"navigationBarTitleText": "消息列表"
					}
				},
				{
					"path": "kefu/message",
					"style": {
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "member/index",
					"style": []
				},
				{
					"path": "member/commissionlog",
					"style": []
				},
				{
					"path": "member/detail",
					"style": []
				},
				{
					"path": "member/history",
					"style": {
						"navigationBarTitleText": "足迹"
					}
				},
				{
					"path": "order/collageorder",
					"style": {
						"navigationBarTitleText": "拼团订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/collageorderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/cycleorder",
					"style": {
						"navigationBarTitleText": "周期购订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/cycleorderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/cycleplanlist",
					"style": {
						"navigationBarTitleText": "计划列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/cycleplandetail",
					"style": {
						"navigationBarTitleText": "计划详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/luckycollageorder",
					"style": {
						"navigationBarTitleText": "幸运拼团订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/luckycollageorderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/kanjiaorder",
					"style": {
						"navigationBarTitleText": "砍价订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/kanjiaorderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/seckillorder",
					"style": {
						"navigationBarTitleText": "秒杀订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/seckillorderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/yuyueorder",
					"style": {
						"navigationBarTitleText": "预约订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/yuyueorderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/scoreshoporder",
					"style": {
						"navigationBarTitleText": "积分兑换订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/scoreshoporderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/shoporder",
					"style": {
						"navigationBarTitleText": "商城订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/shoporderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/shopRefundOrder",
					"style": {
						"navigationBarTitleText": "退款订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/shopRefundOrderDetail",
					"style": {
						"navigationBarTitleText": "退款订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/tuangouorder",
					"style": {
						"navigationBarTitleText": "团购订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/tuangouorderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/maidanlog",
					"style": {
						"navigationBarTitleText": "买单记录",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入用户昵称或订单号搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/maidandetail",
					"style": {
						"navigationBarTitleText": "买单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/yuekeorder",
					"style": {
						"navigationBarTitleText": "约课记录",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "order/yuekeorderdetail",
					"style": {
						"navigationBarTitleText": "约课详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/record",
					"style": {
						"navigationBarTitleText": "处理工单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/add",
					"style": {
						"navigationBarTitleText": "提交工单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/formlog",
					"style": {
						"navigationBarTitleText": "工单记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/formdetail",
					"style": {
						"navigationBarTitleText": "工单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/myformdetail",
					"style": {
						"navigationBarTitleText": "商户工单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "form/formlog",
					"style": {
						"navigationBarTitleText": "表单提交记录",
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "form/formdetail",
					"style": {
						"navigationBarTitleText": "表单提交记录"
					}
				},
				{
					"path": "shortvideo/uploadvideo",
					"style": {
						"navigationBarTitleText": "发布短视频"
					}
				},
				{
					"path": "shortvideo/myupload",
					"style": {
						"navigationBarTitleText": "我的发表记录"
					}
				},
				{
					"path": "product/edit",
					"style": {
						"navigationBarTitleText": "商品设置"
					}
				},
				{
					"path": "product/index",
					"style": {
						"navigationBarTitleText": "商品管理",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				
				{
					"path": "daihuotuan/edit",
					"style": {
						"navigationBarTitleText": "带货团设置"
					}
				},
				{
					"path": "daihuotuan/selectgoods",
					"style": {
						"navigationBarTitleText": "带货团设置"
					}
				},
				{
					"path": "daihuotuan/index",
					"style": {
						"navigationBarTitleText": "带货团管理",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				
				
				{
					"path": "index/businessqr",
					"style": {
						"navigationBarTitleText": "推广码"
					}
				},
				{
					"path": "restaurant/product/edit",
					"style": {
						"navigationBarTitleText": "菜品设置"
					}
				},
				{
					"path": "restaurant/product/index",
					"style": {
						"navigationBarTitleText": "菜品管理",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "restaurant/category/edit",
					"style": {
						"navigationBarTitleText": "菜品分类设置"
					}
				},
				{
					"path": "restaurant/category/index",
					"style": {
						"navigationBarTitleText": "菜品分类管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableEdit",
					"style": {
						"navigationBarTitleText": "餐桌设置"
					}
				},
				{
					"path": "restaurant/table",
					"style": {
						"navigationBarTitleText": "餐桌管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableWaiter",
					"style": {
						"navigationBarTitleText": "餐桌管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableWaiterDetail",
					"style": {
						"navigationBarTitleText": "餐桌详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableWaiterPay",
					"style": {
						"navigationBarTitleText": "结算",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableCategoryEdit",
					"style": {
						"navigationBarTitleText": "餐桌分类设置"
					}
				},
				{
					"path": "restaurant/tableCategory",
					"style": {
						"navigationBarTitleText": "餐桌分类管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/takeawayorder",
					"style": {
						"navigationBarTitleText": "外卖订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "restaurant/takeawayorderdetail",
					"style": {
						"navigationBarTitleText": "外卖订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/shoporder",
					"style": {
						"navigationBarTitleText": "点餐订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "restaurant/shoporderdetail",
					"style": {
						"navigationBarTitleText": "点餐订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/shoporderEdit",
					"style": {
						"navigationBarTitleText": "点餐订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/bookingorder",
					"style": {
						"navigationBarTitleText": "预定订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入预订人姓名或手机号搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "restaurant/bookingorderdetail",
					"style": {
						"navigationBarTitleText": "预定订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/depositorder",
					"style": {
						"navigationBarTitleText": "寄存订单",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入寄存名称、姓名或手机号搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "restaurant/depositorderdetail",
					"style": {
						"navigationBarTitleText": "寄存订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/booking",
					"style": {
						"navigationBarTitleText": "添加预定",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/bookingTableList",
					"style": {
						"navigationBarTitleText": "选择餐桌",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/queue",
					"style": {
						"navigationBarTitleText": "排队叫号",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/queueCategory",
					"style": {
						"navigationBarTitleText": "排队管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/queueCategoryEdit",
					"style": {
						"navigationBarTitleText": "排队管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "freight/index",
					"style": {
						"navigationBarTitleText": "运费模板管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "freight/edit",
					"style": {
						"navigationBarTitleText": "编辑运费模板",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "freight/region-select",
					"style": {
						"navigationBarTitleText": "选择城市"
					}
				},
				{
					"path": "businessposter/index",
					"style": {
						"navigationBarTitleText": "分享海报",
						"enablePullDownRefresh": true
					}
				}
			]
		},

		{
			"root": "adminExt",
			"pages": [

				{"path": "index/login","style": {"navigationBarTitleText": "管理员登录"}},
				{"path": "index/index","style": {"navigationBarTitleText": "管理中心","enablePullDownRefresh": true,"navigationStyle": "custom"}},
				{"path": "index/setpage","style": {"navigationBarTitleText": "配置"}},
				{"path": "index/setnotice","style": {"navigationBarTitleText": "通知管理"}},
				{"path": "index/setpwd","style": {"navigationBarTitleText": "修改密码"}},
				{"path": "index/setinfo","style": {"navigationBarTitleText": "店铺设置"}},
				{"path": "index/recharge","style": {"navigationBarTitleText": "店铺设置"}},

				{"path": "hexiao/hexiao","style": {}},
				{"path": "hexiao/record","style": {"navigationBarTitleText": "我的核销","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入用户昵称搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "hexiao/recordgroup","custom_file":"hexiao_record_group","style": {"navigationBarTitleText": "我的核销","enablePullDownRefresh": true}},
				{"path": "finance/index","style": {"navigationBarTitleText": "财务","navigationStyle": "custom"}},
				{"path": "finance/commissionlog","style": {}},
				{"path": "finance/comwithdrawdetail","style": {}},
				{"path": "finance/comwithdrawlog","style": {}},
				{"path": "finance/moneylog","style": {}},
				{"path": "finance/scorelog","custom_file":"admin_m_show_scorelog","style": {"navigationBarTitleText": "我的核销","enablePullDownRefresh": true}},
				{"path": "finance/bscorelog","style": {}},
				{"path": "finance/rechargelog","style": {"navigationBarTitleText": "充值记录"}},
				{"path": "finance/withdrawdetail","style": {}},
				{"path": "finance/withdrawlog","style": {}},
				{"path": "finance/bmoneylog","style": {"navigationBarTitleText": "余额明细"}},
				{"path": "finance/bwithdraw","style": {"navigationBarTitleText": "余额提现"}},
				{"path": "finance/bwithdrawlog","style": {"navigationBarTitleText": "提现记录"}},
				{"path": "finance/txset","style": {"navigationBarTitleText": "提现信息"}},
				{"path": "finance/yuebaowithdrawlog","style": {"enablePullDownRefresh": true}},
				{"path": "finance/yuebaowithdrawdetail","style": {}},
				{"path": "finance/yuebaolog","style": {}},

				{"path": "finance/mdmoneylog","style": {"navigationBarTitleText": "余额明细"}},
				{"path": "finance/mdwithdraw","style": {"navigationBarTitleText": "余额提现"}},
				{"path": "finance/mdwithdrawlog","style": {"navigationBarTitleText": "提现记录"}},
				{"path": "finance/mdtxset","style": {"navigationBarTitleText": "提现信息"}},

				{"path": "couponmoney/record","custom_file":"business_canuseplatcoupon","style": {"navigationBarTitleText": "补贴券使用记录"}},
				{"path": "couponmoney/withdraw","custom_file":"business_canuseplatcoupon","style": {"navigationBarTitleText": "补贴券提现"}},
				{"path": "couponmoney/withdrawlog","custom_file":"business_canuseplatcoupon","style": {"navigationBarTitleText": "补贴券提现记录"}},
                
				{"path": "kefu/index","style": {"navigationBarTitleText": "消息列表"}},
				{"path": "kefu/message","style": {"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "member/index","style": {}},
				{"path": "member/detail","style": {}},
				{"path": "member/richinfo","style": {"navigationBarTitleText": "详细介绍"}},
				{"path": "member/history","style": {"navigationBarTitleText": "足迹"}},
				{"path": "member/code","custom_file":"member_code","style": {"navigationBarTitleText": "","enablePullDownRefresh": true}},
				{"path": "member/codebuy","custom_file":"member_code","style": {"navigationBarTitleText": "","enablePullDownRefresh": true}},
				{"path": "order/collageorder","style": {"navigationBarTitleText": "拼团订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/collageorderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				{"path": "order/cycleorder","style": {"navigationBarTitleText": "周期购订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/cycleorderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				{"path": "order/cycleplanlist","style": {"navigationBarTitleText": "计划列表","enablePullDownRefresh": true}},
				{"path": "order/cycleplandetail","style": {"navigationBarTitleText": "计划详情","enablePullDownRefresh": true}},
				{"path": "order/luckycollageorder","style": {"navigationBarTitleText": "幸运拼团订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/luckycollageorderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				{"path": "order/kanjiaorder","style": {"navigationBarTitleText": "砍价订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/kanjiaorderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				{"path": "order/seckillorder","style": {"navigationBarTitleText": "秒杀订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/seckillorderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},

				{"path": "order/yuyueorder","style": {"navigationBarTitleText": "预约订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/yuyueorderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				
				{"path": "order/scoreshoporder","style": {"navigationBarTitleText": "积分兑换订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/scoreshoporderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				{"path": "order/shoporder","style": {"navigationBarTitleText": "商城订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/shoporderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				{"path": "order/shopRefundOrder","style": {"navigationBarTitleText": "退款订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/shopRefundOrderDetail","style": {"navigationBarTitleText": "退款订单详情","enablePullDownRefresh": true}},
				{"path": "order/weightOrderFahuo","custom_file":"product_weight","style": {"navigationBarTitleText": "发货"}},

				{"path": "order/tuangouorder","style": {"navigationBarTitleText": "团购订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/tuangouorderdetail","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
	
				{"path": "order/yuekeorder","style": {"navigationBarTitleText": "约课记录","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/yuekeorderdetail","style": {"navigationBarTitleText": "约课详情","enablePullDownRefresh": true}},
				{"path": "order/dkorder","custom_file":"order_add_mobile","style": {"navigationBarTitleText": "代客下单","enablePullDownRefresh": false,"navigationStyle": "custom"}},
				{"path": "order/dkaddress","custom_file":"order_add_mobile","style": {"navigationBarTitleText": "地址","enablePullDownRefresh": false}},
				{"path": "order/dkaddressadd","custom_file":"order_add_mobile","style": {"navigationBarTitleText": "添加地址","enablePullDownRefresh": false}},
				{"path": "order/dkfastbuy","style": {"navigationBarTitleText": "选择商品","enablePullDownRefresh": false}},
				{"path": "order/dksearch","style": {"navigationBarTitleText": "商品搜索","enablePullDownRefresh": false}},
				{"path": "order/maidanlog"},//2.5.5
				{"path": "order/addmember","custom_file":"member_add","style": {"navigationBarTitleText": "添加会员","enablePullDownRefresh": false}},
		
				{"path": "workorder/category","custom_file":"workorder","style": {"navigationBarTitleText": "工单类型","enablePullDownRefresh": true}},
				{"path": "workorder/record","custom_file":"workorder","style": {"navigationBarTitleText": "处理工单","enablePullDownRefresh": true}},
				{"path": "workorder/formlog","custom_file":"workorder","style": {"navigationBarTitleText": "工单记录","enablePullDownRefresh": true}},
				{"path": "workorder/formdetail","custom_file":"workorder","style": {"navigationBarTitleText": "工单详情","enablePullDownRefresh": true}},
				{"path": "workorder/myformdetail","custom_file":"workorder","style": {"navigationBarTitleText": "商户工单详情","enablePullDownRefresh": true}},
				{"path": "workorder/jindu","custom_file":"workorder","style": {"navigationBarTitleText": "工单进度","enablePullDownRefresh": true}},	
				{"path": "workorder/updatecate","custom_file":"workorder","style": {"navigationBarTitleText": "修改工单分类","enablePullDownRefresh": true}},
				{"path": "yuyue/selectworker","custom_file":"yuyue_apply","style": {"navigationBarTitleText": "选择师傅派单","enablePullDownRefresh": true}},
				
				{"path": "form/formlog","style": {"navigationBarTitleText": "表单提交记录","titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "form/formdetail","style": {"navigationBarTitleText": "表单提交记录"}},
				
				{"path": "shortvideo/uploadvideo","style": {"navigationBarTitleText": "发布短视频"}},
				{"path": "shortvideo/myupload","style": {"navigationBarTitleText": "我的发表记录"}},

				{"path": "product/edit","style": {"navigationBarTitleText": "商品设置"}},
				{"path": "product/index","style": {"navigationBarTitleText": "商品管理","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"},{"type":"none","fontSrc":"/static/font/iconfont.ttf","text":"\ue609"}]}}},
				{"path": "product/category2/index","custom_file":"shop_categroy_business_mobile","style": {"navigationBarTitleText": "商家商品分类"}},
				{"path": "product/category2/edit","custom_file":"shop_categroy_business_mobile","style": {"navigationBarTitleText": "添加商家商品分类"}},
				{"path": "index/businessqr","style": {"navigationBarTitleText": "推广码"}},
				
				{"path": "scoreproduct/edit","custom_file":"scoreshop_product_mobileedit","style": {"navigationBarTitleText": "兑换商品设置"}},
				{"path": "scoreproduct/index","custom_file":"scoreshop_product_mobileedit","style": {"navigationBarTitleText": "兑换商品管理","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},

				{"path": "restaurant/product/edit","style": {"navigationBarTitleText": "菜品设置"}},
				{"path": "restaurant/product/index","style": {"navigationBarTitleText": "菜品管理","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "restaurant/category/edit","style": {"navigationBarTitleText": "菜品分类设置"}},
				{"path": "restaurant/category/index","style": {"navigationBarTitleText": "菜品分类管理","enablePullDownRefresh": true}},
				{"path": "restaurant/tableEdit","style": {"navigationBarTitleText": "餐桌设置"}},
				{"path": "restaurant/table","style": {"navigationBarTitleText": "餐桌管理","enablePullDownRefresh": true}},
				{"path": "restaurant/tableWaiter","style": {"navigationBarTitleText": "餐桌管理","enablePullDownRefresh": true}},
				{"path": "restaurant/tableWaiterDetail","style": {"navigationBarTitleText": "餐桌详情","enablePullDownRefresh": true}},
				{"path": "restaurant/tableWaiterPay","style": {"navigationBarTitleText": "结算","enablePullDownRefresh": true}},
				{"path": "restaurant/tableCategoryEdit","style": {"navigationBarTitleText": "餐桌分类设置"}},
				{"path": "restaurant/tableCategory","style": {"navigationBarTitleText": "餐桌分类管理","enablePullDownRefresh": true}},
				{"path": "restaurant/takeawayorder","style": {"navigationBarTitleText": "外卖订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "restaurant/takeawayorderdetail","style": {"navigationBarTitleText": "外卖订单","enablePullDownRefresh": true}},
				{"path": "restaurant/shoporder","style": {"navigationBarTitleText": "点餐订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "restaurant/shoporderdetail","style": {"navigationBarTitleText": "点餐订单","enablePullDownRefresh": true}},
				{"path": "restaurant/shoporderEdit","style": {"navigationBarTitleText": "点餐订单","enablePullDownRefresh": true}},
				{"path": "restaurant/bookingorder","style": {"navigationBarTitleText": "预定订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入预订人姓名或手机号搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "restaurant/bookingorderdetail","style": {"navigationBarTitleText": "预定订单","enablePullDownRefresh": true}},
				{"path": "restaurant/depositorder","style": {"navigationBarTitleText": "寄存订单","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入寄存名称、姓名或手机号搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "restaurant/depositorderdetail","style": {"navigationBarTitleText": "寄存订单","enablePullDownRefresh": true}},
				{"path": "restaurant/booking","style": {"navigationBarTitleText": "添加预定","enablePullDownRefresh": true}},
				{"path": "restaurant/bookingTableList","style": {"navigationBarTitleText": "选择餐桌","enablePullDownRefresh": true}},
				{"path": "restaurant/queue","style": {"navigationBarTitleText": "排队叫号","enablePullDownRefresh": true}},
				{"path": "restaurant/queueCategory","style": {"navigationBarTitleText": "排队管理","enablePullDownRefresh": true}},
				{"path": "restaurant/queueCategoryEdit","style": {"navigationBarTitleText": "排队管理","enablePullDownRefresh": true}},
				
				{"path": "health/record","custom_file":"health_assessment","style": {"navigationBarTitleText": "填写记录","enablePullDownRefresh": true}},
				{"path": "health/recordlog","custom_file":"health_assessment","style": {"navigationBarTitleText": "评测详情","enablePullDownRefresh": true}},
				{"path": "health/result","custom_file":"health_assessment","style": {"navigationBarTitleText": "评测结果","enablePullDownRefresh": true}},
				
				{"path": "yingxiao/queueFree","custom_file":"yx_queue_free","style": {"navigationBarTitleText": "排队记录","enablePullDownRefresh": true}},
				{"path": "product/editstock","custom_file":"product_sync_business","style": {"navigationBarTitleText": "修改库存"}},
				{"path": "business/index","custom_file":"business_sales_quota","style": {"navigationBarTitleText": "商家列表"}},
				// {"path": "order/maidanlog","style": {"navigationBarTitleText": "买单记录","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入用户昵称或订单号搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "order/maidandetail","style": {"navigationBarTitleText": "买单详情","enablePullDownRefresh": true}},
				// {"path": "order/maidanindex","style": {"navigationBarTitleText": "买单统计","enablePullDownRefresh": true}},
				{"path": "mendian/list","custom_file":"mendian_upgrade","style": {"navigationBarTitleText": "","enablePullDownRefresh": true}},
				{"path": "mendian/detail","custom_file":"mendian_upgrade","style": {"navigationBarTitleText": "","enablePullDownRefresh": true}},
				{"path": "mendian/withdrawlog","custom_file":"mendian_upgrade","style": {"navigationBarTitleText": "佣金提现","enablePullDownRefresh": true}},
				{"path": "mendian/withdrawdetail","custom_file":"mendian_upgrade","style": {"navigationBarTitleText": "佣金提现详情","enablePullDownRefresh": true}},
				{"path": "hotel/orderlist","custom_file":"hotel","style": {"navigationBarTitleText": "订单列表","enablePullDownRefresh": true}},
				{"path": "hotel/orderdetail","custom_file":"hotel","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				{"path": "hotel/refundyajin","custom_file":"hotel","style": {"navigationBarTitleText": "退押金","enablePullDownRefresh": true}},
				{"path": "hotel/refundyajinDetail","custom_file":"hotel","style": {"navigationBarTitleText": "退押金详情","enablePullDownRefresh": true}},
				{"path": "huodongbaoming/order","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "活动报名订单","enablePullDownRefresh": true}},
				{"path": "huodongbaoming/orderdetail","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": true}},
				{"path": "shop/shopstock","custom_file":"shop_add_stock_mobile","style": {"navigationBarTitleText": "录入库存","enablePullDownRefresh": true,"navigationStyle": "custom"}},
				{"path": "shop/shopstockgoods","custom_file":"shop_add_stock_mobile","style": {"navigationBarTitleText": "选择商品","enablePullDownRefresh": false}},
				{"path": "coupon/index","custom_file":"coupon_manage_mobile","style": {"enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "coupon/edit","custom_file":"coupon_manage_mobile","style": {"enablePullDownRefresh": false}},
				{"path": "coupon/prolist","custom_file":"coupon_manage_mobile","style": {"navigationBarTitleText": "服务商品","enablePullDownRefresh": false}},
				{"path": "coupon/restaurantList","custom_file":"coupon_manage_mobile","style": {"navigationBarTitleText": "餐饮优惠券列表","enablePullDownRefresh": false,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "coupon/category","custom_file":"coupon_manage_mobile","style": {"navigationBarTitleText": "商品分类","enablePullDownRefresh": false}},
				{"path": "bonuspoolgold/goldlog","custom_file":"bonus_pool_gold","style": {"navigationBarTitleText": "金币明细","enablePullDownRefresh": false}},
				{"path": "bonuspoolgold/goldwithdraw","custom_file":"bonus_pool_gold","style": {"navigationBarTitleText": "金币兑换","enablePullDownRefresh": false}},
				{"path": "finance/transfermendianmoney","custom_file":"mendian_money_transfer","style": {"navigationBarTitleText": "余额转账","enablePullDownRefresh": false}},
				{"path": "set/qrcodeShop","style": {"navigationBarTitleText": "店铺二维码","enablePullDownRefresh": false}},
				{"path": "queuefree/queueFreeSet","custom_file":"yx_queue_free_wxadmin_set","style": {"navigationBarTitleText": "排队免单设置","enablePullDownRefresh": false}}
			]
		},

		
		{
			"root": "live",
			"pages": [
				{
					"path": "live",
					"style": {
						
						"navigationBarTitleText": "直播间",
						"enablePullDownRefresh" : false,
						"navigationStyle": "custom"
					}
				},
				
				
				{
					"path": "list",
					"style": {
						"navigationBarTitleText" : "频道列表",
						"enablePullDownRefresh" : false
					}
				}
				
			]
		},	
         {
			"root": "restaurant",
			"pages": [{
					"path": "takeaway/blist",
					"style": {
						"navigationBarTitleText": "商家列表"
					}
				},
				{
					"path": "takeaway/index",
					"style": {
						"navigationBarTitleText": "商家详情"
					}
				},
				{
					"path": "takeaway/product",
					"style": {
						"navigationBarTitleText": "商品详情"
					}
				},
				{
					"path": "takeaway/commentlist",
					"style": {
						"navigationBarTitleText": "商品评价",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "takeaway/buy",
					"style": {
						"navigationBarTitleText": "订单确认"
					}
				},
				{
					"path": "takeaway/orderlist",
					"style": {
						"navigationBarTitleText": "外卖订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "takeaway/comment",
					"style": {
						"navigationBarTitleText": "我要评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "takeaway/commentdp",
					"style": {
						"navigationBarTitleText": "店铺评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "takeaway/commentps",
					"style": {
						"navigationBarTitleText": "评价配送员",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "takeaway/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "takeaway/logistics",
					"style": {
						"navigationBarTitleText": "查看物流",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "takeaway/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/index",
					"style": {
						"navigationBarTitleText": "点餐"
					}
				},
				{
					"path": "shop/search",
					"style": {
						"navigationBarTitleText": "搜索"
					}
				},
				{
					"path": "shop/product",
					"style": {
						"navigationBarTitleText": "商品详情"
					}
				},
				{
					"path": "shop/commentlist",
					"style": {
						"navigationBarTitleText": "商品评价",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/buy",
					"style": {
						"navigationBarTitleText": "订单确认"
					}
				},
				{
					"path": "shop/orderlist",
					"style": {
						"navigationBarTitleText": "点餐记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/comment",
					"style": {
						"navigationBarTitleText": "我要评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/commentdp",
					"style": {
						"navigationBarTitleText": "店铺评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/commentps",
					"style": {
						"navigationBarTitleText": "评价配送员",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/logistics",
					"style": {
						"navigationBarTitleText": "查看物流",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "booking/add",
					"style": {
						"navigationBarTitleText": "预定",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "booking/tableList",
					"style": {
						"navigationBarTitleText": "餐桌列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "booking/orderlist",
					"style": {
						"navigationBarTitleText": "预定列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "booking/detail",
					"style": {
						"navigationBarTitleText": "预定详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "queue/index",
					"style": {
						"navigationBarTitleText": "排队信息",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "queue/quhao",
					"style": {
						"navigationBarTitleText": "取号排队",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "queue/record",
					"style": {
						"navigationBarTitleText": "排队记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "deposit/orderlog",
					"style": {
						"navigationBarTitleText": "存取记录"
					}
				},
				{
					"path": "deposit/orderdetail",
					"style": {
						"navigationBarTitleText": "寄存"
					}
				},
				{
					"path": "deposit/add",
					"style": {
						"navigationBarTitleText": "添加寄存"
					}
				}
			]
		},
		{
			"root": "pagesExa",
			"pages": [
				
				{
					"path": "kaizhanghao/zhuzhanghao",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "danshujiang/index",
					"style": {
					  "navigationBarTitleText": "单数奖励",
					  "enablePullDownRefresh": true
					}
				},
				{
					"path": "tuandui/index",
					"style": {
					  "navigationBarTitleText": "团队业绩奖励",
					  "enablePullDownRefresh": true
					}
				},
				{
					"path": "tuandui/detail",
					"style": {
					  "navigationBarTitleText": "团队业绩奖励详情",
					  "enablePullDownRefresh": true
					}
				},
				{
					"path": "tuandui/records",
					"style": {
					  "navigationBarTitleText": "团队业绩奖励记录",
					  "enablePullDownRefresh": true
					}
				},
				{
					"path": "ranking-reward/index",
					"style": {
						"navigationBarTitleText": "排名奖励",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "ranking-reward/rules",
					"style": {
						"navigationBarTitleText": "排名奖励规则",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "ranking-reward/preview",
					"style": {
						"navigationBarTitleText": "排名奖励预览",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "ranking-reward/records",
					"style": {
						"navigationBarTitleText": "排名奖励记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "yuefenhong/index",
					"style": {
					  "navigationBarTitleText": "月分红活动",
					  "enablePullDownRefresh": true
					}
				},
				{
					"path": "yuefenhong/detail",
					"style": {
					  "navigationBarTitleText": "分红活动详情",
					  "enablePullDownRefresh": true
					}
				},
				{
					"path": "kaizhanghao/regzhuzhanghao",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "daike/daikebuy",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "daike/buy",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "daike/daikepay",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "daike/daigoulist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "daike/daigoulistadmin",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "daike/daigoupick",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "daike/address/address",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "daike/address/addressadd",
					"style": {
						"enablePullDownRefresh": true
					}
				},
			
				{
					"path": "dikuai/pigfarm",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "dikuai/farm",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "dikuai/salerecords",
					"style": {
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "出售记录"
					}
				},
				{
					"path": "shoumai/sale",
					"style": {
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "出售记录"
					}
				},
				{
					"path": "shoumai/index",
					"style": {
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "出售记录"
					}
				},
				{
					"path": "shoumai/voucher",
					"style": {
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "出售记录"
					}
				},
				{
					"path": "shoumai/walletsite",
					"style": {
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "出售记录"
					}
				},
				{
					"path": "my/setshoukuan",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/setavatar",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/setinvite",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/setusd",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/qianyue",
					"style": {
						
					}
				},
				{
					"path": "my/withdraw",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/renzhengzhongxin",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/levelinfo",
					"style": {
						"navigationBarTitleText": "等级说明",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "my/levelup",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/leveluppay",
					"style": {
						"navigationBarTitleText": "订单支付",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/scorelog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/scoreloghuang",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/favorite",
					"style": {
						"navigationBarTitleText": "我的收藏",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/history",
					"style": {
						"navigationBarTitleText": "我的足迹",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/paypwd",
					"style": {
						"navigationBarTitleText": "设置支付密码",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/set",
					"style": {
						"navigationBarTitleText": "个人设置",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/mydata",
					"style": {
						"navigationBarTitleText": "我的数据信息",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/setpwd",
					"style": {
						"navigationBarTitleText": "修改密码",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/setnickname",
					"style": {
						"navigationBarTitleText": "设置昵称",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/editaddress",
					"style": {
						"navigationBarTitleText": "设置地理位置",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setweixin",
					"style": {
						"navigationBarTitleText": "设置微信号",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/seteducation",
					"style": {
						"navigationBarTitleText": "设置学历",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setmaritalstatus",
					"style": {
						"navigationBarTitleText": "设置情感状态",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setprofession",
					"style": {
						"navigationBarTitleText": "设置职业",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setyonghujianjie",
					"style": {
						"navigationBarTitleText": "设置简介",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setrealname",
					"style": {
						"navigationBarTitleText": "设置姓名",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setidcard",
					"style": {
						"navigationBarTitleText": "认证身份证号码",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setidcardimg",
					"style": {
						"navigationBarTitleText": "认证身份证号码图片",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setidcardimgwx",
					"style": {
						"navigationBarTitleText": "微信人脸认证",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setidcardimgzfb",
					"style": {
						"navigationBarTitleText": "支付宝人脸认证",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/settel",
					"style": {
						"navigationBarTitleText": "设置手机号",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setsex",
					"style": {
						"navigationBarTitleText": "设置性别",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setbirthday",
					"style": {
						"navigationBarTitleText": "设置生日",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setaliaccount",
					"style": {
						"navigationBarTitleText": "设置支付宝账号",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setbankinfo",
					"style": {
						"navigationBarTitleText": "设置银行卡账号",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/duihuanzhanghao",
					"style": {
						"navigationBarTitleText": "拼团虚拟账号",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kecheng/list",
					"style": {
						"navigationBarTitleText": "开始答题",
						"enablePullDownRefresh": false
					}
				},
			
				{
					"path": "kecheng/mldetail",
					"style": {
						"navigationBarTitleText": "开始答题",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/product",
					"style": {
						"navigationBarTitleText": "开始答题",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/tiku",
					"style": {
						"navigationBarTitleText": "开始答题",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/orderlist",
					"style": {
						"navigationBarTitleText": "答题订单列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/complete",
					"style": {
						"navigationBarTitleText": "答题完成",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/recordlog",
					"style": {
						"navigationBarTitleText": "答题记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/error",
					"style": {
						"navigationBarTitleText": "错题回顾",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/category3",
					"style": {
						"navigationBarTitleText": "课程分类",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuanzhang/apply",
					"style": {
						"navigationBarTitleText": "团长入驻",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuanzhang/blist",
					"style": {
						"navigationBarTitleText": "团长列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuanzhang/index",
					"style": {
						"navigationBarTitleText": "团长首页",
						"enablePullDownRefresh": false
					}
				},
			{
				"path": "tuanzhang/buy",
				"style": {
					"navigationBarTitleText": "团长支付",
					"enablePullDownRefresh": false
				}
			},
			
			{
				"path": "tuanzhang/buy/pay",
				"style": {
					"navigationBarTitleText": "支付收银台",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "tuanzhang/product",
				"style": {
					"navigationBarTitleText": "商品详情",
					"enablePullDownRefresh": false
				}
			},
				{
					"path": "tuanzhangadmin/index/login",
					"style": {
						"navigationBarTitleText": "团长登录",
					"enablePullDownRefresh": false
				}
			},
				{
					"path": "tuanzhangadmin/index/mtuanlist",
					"style": {
						"navigationBarTitleText": "同步团列表",
					"enablePullDownRefresh": false
				}
			},
			{
					"path": "tuanzhangadmin/index/mytuanlist",
					"style": {
						"navigationBarTitleText": "我的团列表",
					"enablePullDownRefresh": false
				}
			},
			
				{
					"path": "tuanzhangadmin/index/tuanedit",
					"style": {
						"navigationBarTitleText": "编辑团",
					"enablePullDownRefresh": false
				}
			},
				{
					"path": "tuanzhangadmin/index/index",
					"style": {
						"navigationBarTitleText": "团长后台管理",
					"enablePullDownRefresh": false
				}
			},
				{
					"path": "tuanzhangadmin/finance/index",
					"style": {
						"navigationBarTitleText": "团长管理",
					"enablePullDownRefresh": false
				}
			},
			
			{
				"path": "tuanzhangadmin/finance/commissionlog",
				"style": []
			},
			{
				"path": "tuanzhangadmin/finance/comwithdrawdetail",
				"style": []
			},
			{
				"path": "tuanzhangadmin/finance/comwithdrawlog",
				"style": []
			},
			{
				"path": "tuanzhangadmin/finance/moneylog",
				"style": []
			},
			{
				"path": "tuanzhangadmin/finance/rechargelog",
				"style": {
					"navigationBarTitleText": "充值记录"
				}
			},
			{
				"path": "tuanzhangadmin/finance/withdrawdetail",
				"style": []
			},
			{
				"path": "tuanzhangadmin/finance/withdrawlog",
				"style": []
			},
			{
				"path": "tuanzhangadmin/finance/bmoneylog",
				"style": {
					"navigationBarTitleText": "余额明细"
				}
			},
			{
				"path": "tuanzhangadmin/finance/bwithdraw",
				"style": {
					"navigationBarTitleText": "余额提现"
				}
			},
			{
				"path": "tuanzhangadmin/finance/bwithdrawlog",
				"style": {
					"navigationBarTitleText": "提现记录"
				}
			},
			{
				"path": "tuanzhangadmin/finance/txset",
				"style": {
					"navigationBarTitleText": "提现信息"
				}
			},
			{
				"path": "tuanzhangadmin/finance/yuebaowithdrawlog",
				"style": {
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "tuanzhangadmin/finance/yuebaowithdrawdetail",
				"style": []
			},
			{
				"path": "tuanzhangadmin/finance/yuebaolog",
				"style": []
			},
			{
				"path": "tuanzhangadmin/finance/mdmoneylog",
				"style": {
					"navigationBarTitleText": "余额明细"
				}
			},
			{
					"path": "tuanzhangadmin/finance/mdwithdraw",
				"style": {
					"navigationBarTitleText": "余额提现"
				}
			},
			{
				"path": "tuanzhangadmin/finance/mdwithdrawlog",
				"style": {
					"navigationBarTitleText": "提现记录"
				}
			},
			{
				"path": "tuanzhangadmin/finance/mdtxset",
				"style": {
					"navigationBarTitleText": "提现信息"
				}
			},
			
				{
					"path": "tuanzhangadmin/index/setinfo",
					"style": {
						"navigationBarTitleText": "团长设置",
					"enablePullDownRefresh": false
				}
			},
			{
					"path": "tuanzhangadmin/index/setpwd",
					"style": {
						"navigationBarTitleText": "团长设置",
					"enablePullDownRefresh": false
				}
			},
				{
					"path": "tuanzhangadmin/index/artlistadmin",
					"style": {
						"navigationBarTitleText": "团长管理",
					"enablePullDownRefresh": false
				}
			},
				{
					"path": "tongxunlu/peolist",
					"style": {
						"navigationBarTitleText": "服务网点",
					"enablePullDownRefresh": false
				}
			},
				{
					"path": "tongxunlu/maps",
					"style": {
						"navigationBarTitleText": "服务网点地图",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "jianbanbaoming/index",
				"style": {
					"navigationBarTitleText": "活动详情"
				}
			},

			{
				"path": "jianbanbaoming/yuedetail",
				"style": {
					"navigationBarTitleText": "投票详情"
				}
			},

			{
				"path": "jianbanbaoming/baoming",
				"style": {
					"navigationBarTitleText": "我要报名"
				}
			},
			{
				"path": "jianbanbaoming/shuoming",
				"style": {
					"navigationBarTitleText": "活动说明"
				}
			},
			{
				"path": "jianbanbaoming/yuelist",
				"style": {
					"navigationBarTitleText": "活动说明"
				}
			}, {
				"path": "miaosha/index",
				"style": {
					"navigationBarTitleText": "秒杀列表",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "miaosha/product",
				"style": {
					"navigationBarTitleText": "秒杀商品详情",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/pay",
				"style": {
					"navigationBarTitleText": "秒杀确认支付",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/payshangchuan",
				"style": {
					"navigationBarTitleText": "秒杀上传凭证",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/lirun",
				"style": {
					"navigationBarTitleText": "买家仓库",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, 
			{
				"path": "miaosha/lishi",
				"style": {
					"navigationBarTitleText": "历史订单",
					"enablePullDownRefresh": true,
					"titleNView": {
			
						"buttons": [{
							"type": "home"
						}]
					}
				}
			},
			
			{
				"path": "miaosha/orderlist",
				"style": {
					"navigationBarTitleText": "买家仓库",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/changci",
				"style": {
					"navigationBarTitleText": "场次列表",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/fukuan",
				"style": {
					"navigationBarTitleText": "场次结算",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/pay2",
				"style": {
					"navigationBarTitleText": "场次结算",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/pay3",
				"style": {
					"navigationBarTitleText": "场次结算",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/weituo",
				"style": {
					"navigationBarTitleText": "委托上架",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/detail",
				"style": {
					"navigationBarTitleText": "详情",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "miaosha/detail2",
				"style": {
					"navigationBarTitleText": "详情2",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "miaosha/orderlist2",
				"style": {
					"navigationBarTitleText": "卖家仓库",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/buy",
				"style": {
					"navigationBarTitleText": "秒杀下单",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/buyweituo",
				"style": {
					"navigationBarTitleText": "委托上架",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/mymiaosha",
				"style": {
					"navigationBarTitleText": "我的秒杀列表",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/shenhe",
				"style": {
					"navigationBarTitleText": "我的秒杀审核",
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}
				}
			}, {
				"path": "miaosha/classify2",
				"style": {
					"navigationBarTitleText": "秒杀分类列表",
					"enablePullDownRefresh": true,
					"enablePullDownRefresh": true,
					"titleNView": {

						"buttons": [{
							"type": "home"
						}]
					}

				}
			},
			{
				"path": "caigougongying/index",
				"style": {
					"navigationBarTitleText": "采购供应",
					"enablePullDownRefresh": true
				}
			},
				{
				"path": "caigougongying/main",
				"style": {
					"navigationBarTitleText": "采购供应",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "caigougongying/fatie",
				"style": {
					"navigationBarTitleText": "发布供应",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "caigougongying/detail",
				"style": {
					"navigationBarTitleText": "供应详情",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "caigougongying/ltlist",
				"style": {
					"navigationBarTitleText": "供应列表",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "caigougongying/pinglun",
				"style": {
					"navigationBarTitleText": "评论列表",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "caigougongying/focuslog",
				"style": {
					"navigationBarTitleText": "关注记录",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "caigougongying/fatielog",
				"style": {
					"navigationBarTitleText": "发布记录",
					"enablePullDownRefresh": true
				}
			}
		]
	},
		{
			"root": "daihuobiji",
			"pages": [{
					"path": "detail/bijilist",
					"style": {
						"navigationBarTitleText": "笔记列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "detail/index",
					"style": {
						"navigationStyle": "custom"
						
					}
				},
				{
					"path": "detail/index2",
					"style": {
						"navigationStyle": "custom"
						
					}
				},
				{
					"path": "detail/selectgoods",
					"style": {
						
						"navigationBarTitleText": "商城"
						
					}
				},
				
				{
					"path": "detail/userbiji",
					"style": {
						"navigationStyle": "custom"

					}
				},
				{
					"path": "detail/userbijishe",
					"style": {
						"navigationStyle": "custom",
						"enablePullDownRefresh": true
				
					}
				},
				{
					"path": "detail/member/following",
					"style": {
						"navigationBarTitleText": "我的关注列表",
						  "navigationStyle": "default"
				
					}
				},
				{
					"path": "detail/member/follower",
					"style": {
						"navigationBarTitleText": "关注我的列表",
						
						  "navigationStyle": "default"
				
					}
				},
				{
					"path": "yuyue/yuelist",
					"style": {
						"navigationBarTitleText": "列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "yuyue/yuedetail",
					"style": {
						"navigationBarTitleText": "详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "detail/fatie",
					"style": {
						"navigationBarTitleText": "发笔记",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "detail/fabiji",
					"style": {
						"navigationBarTitleText": "记笔记",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "detail/fatieedit",
					"style": {
						"navigationBarTitleText": "发笔记",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "detail/newFatie",
					"style": {
						"navigationBarTitleText": "编辑图片",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "detail/pinglun",
					"style": {
						"navigationBarTitleText": "评论",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "detail/fatielog",
					"style": {
						"navigationBarTitleText": "发帖记录",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kuaituan/ditu",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "kuaituan/addtuan",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "kuaituan/selectgoods",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "kuaituan/addtuanadmin/mtuanlist",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "kuaituan/classify",
					"style": {
						"navigationBarTitleText": "商品列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kuaituan/tuanlist",
					"style": {
						"navigationBarTitleText": "九玖甄选",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kuaituan/tuanzhangtuanlist",
					"style": {
						"navigationBarTitleText": "团列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kuaituan/artlist",
					"style": {
						"navigationBarTitleText": "团列表",
						"enablePullDownRefresh": true
					}
				}, {
					"path": "kuaituan/detail",
					"style": {
						"navigationBarTitleText": "团详情",
						"enablePullDownRefresh": true
					}
				}, {
					"path": "kuaituan/tuanzhangdetail",
					"style": {
						"navigationBarTitleText": "团长团详情",
						"enablePullDownRefresh": true
					}
				}
			]
		},
		
			{
				"root": "zhaopin",
				"pages": [
				
					{
						"path": "index",
						"style": {
							"navigationBarTitleText": "招聘列表",
							"enablePullDownRefresh": true
						 
							
						}
					},
					{
						"path": "search",
						"style": {
						  "navigationBarTitleText": "职位搜索",
						  "enablePullDownRefresh": true,
						  "navigationBarBackgroundColor": "#ffffff",
						  "navigationBarTextStyle": "black"
						}
					  },
					{
						"path": "partdetails",
						"style": {
							"navigationBarTitleText": "",
							"enablePullDownRefresh": false
						}
					},
					{
						"path": "company",
						"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "公司主页"
						}
					
					},
					{
						"path": "resume",
						"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "完善简历"
						}
					
					},
					{
						"path": "jobMatch",
						"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "匹配结果"
						}
					
					},
					{
						"path": "jobFilter",
						"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "智能匹配"
						}
					
					},
					{
						"path": "myFavorites",
						"style": {
							"navigationBarTitleText": "我的收藏",
							"enablePullDownRefresh": false,
							"navigationBarBackgroundColor": "#ffffff",
							"navigationBarTextStyle": "black",
							"backgroundColor": "#f5f5f5"
						}
					},
					{
						"path": "myApply",
						"style": {
							"navigationBarTitleText": "我的投递",
							"enablePullDownRefresh": false,
							"navigationBarBackgroundColor": "#ffffff",
							"navigationBarTextStyle": "black",
							"backgroundColor": "#f5f5f5"
						}
					},
					{
						"path": "workingJob",
						"style": {
							"navigationBarTitleText": "我的工作",
							"enablePullDownRefresh": false,
							"navigationBarBackgroundColor": "#ffffff",
							"navigationBarTextStyle": "black",
							"backgroundColor": "#f5f5f5"
						}
					},
					{
						"path": "jobDetail",
						"style": {
							"navigationBarTitleText": "职位详情",
							"enablePullDownRefresh": true
						}
					}
				]
			},
			{
				"root": "yuyue",
				"pages": [
					
					{
						"path": "worker/index",
						"style": {
							"navigationBarTitleText": "管理员首页"
						}
					},
					{
						"path": "worker/promote",
						"style": {
							"navigationBarTitleText": "管理员首页"
						}
					},
					{
						"path": "selectpeople",
					"style": {
						"navigationBarTitleText": "人员列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "comments",
					"style": {
						"navigationBarTitleText": "客户评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "calendar",
					"style": {
						"navigationBarTitleText": "工作日历",
						"enablePullDownRefresh": false
					}
				},
				
				{
					"path": "product",
					"style": {
						"navigationBarTitleText": "服务详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "product2",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "prolist",
					"style": {
						"navigationBarTitleText": "服务列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "buy",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "buy2",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "orderlist",
					"style": {
						"navigationBarTitleText": "订单列表",
						"enablePullDownRefresh": false,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "logistics",
					"style": {
						"navigationBarTitleText": "查看进度",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "comment",
					"style": {
						"navigationBarTitleText": "评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "commentps",
					"style": {
						"navigationBarTitleText": "评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "commentlist",
					"style": {
						"navigationBarTitleText": "评价列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "peolist",
					"style": {
						"navigationBarTitleText": "服务网点",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "usercalendar",
					"style": {
						"navigationBarTitleText": "预约日历",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peolist2",
					"style": {
						"navigationBarTitleText": "师傅列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "peodetail",
					"style": {
						"navigationBarTitleText": "人员详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "peodetail2",
					"style": {
						"navigationBarTitleText": "师傅详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "jdorderlist",
					"style": {
						"navigationBarTitleText": "订单列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "jdorderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my",
					"style": {
						"navigationBarTitleText": "我的",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "dating",
					"style": {
						"navigationBarTitleText": "我的",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "moneylog",
					"style": {
						"navigationBarTitleText": "账单明细",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "search",
					"style": {
						"navigationBarTitleText": "搜索",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "setinfo",
					"style": {
						"navigationBarTitleText": "提现设置",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "withdraw",
					"style": {
						"navigationBarTitleText": "我的钱包",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "login",
					"style": {
						"navigationBarTitleText": "服务人员登录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "setpwd",
					"style": {
						"navigationBarTitleText": "修改密码",
						"enablePullDownRefresh": false
					}
				},
				
				{
					"path": "appoint",
					"style": {
						"navigationBarTitleText": "预约时间",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "selectworker",
					"style": {
						"navigationBarTitleText": "选择服务人员",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "packageorderlist", 
					"style": {
						"navigationBarTitleText": "我的套餐订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "packagelist", 
					"style": {
						"navigationBarTitleText": "服务套餐",
						"enablePullDownRefresh": true 
					}
				},
				{
					"path": "packagedetail", 
					"style": {
						"navigationBarTitleText": "套餐详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "packagebuy", 
					"style": {
						"navigationBarTitleText": "确认订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "packageorderdetail", 
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "apply", 
					"style": {
						"navigationBarTitleText": "师傅入驻",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "packageappoint", 
					"style": {
						"navigationBarTitleText": "预约服务",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cycle/productList", 
					"style": {
						"navigationBarTitleText": "周期服务列表",
						"enablePullDownRefresh": true 
					}
				},
				{
					"path": "cycle/productDetail", 
					"style": {
						"navigationBarTitleText": "服务详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cycle/buy", 
					"style": {
						"navigationBarTitleText": "确认订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cycle/orderList", 
					"style": {
						"navigationBarTitleText": "我的周期服务",
						"enablePullDownRefresh": true 
					}
				},
				{
					"path": "cycle/orderDetail", 
					"style": {
						"navigationBarTitleText": "服务单详情",
						"enablePullDownRefresh": true
					}
				}
			]
		},
	
		{
			"root": "pagesB",
			"pages": [
				{"path": "login/login","custom_file":"denglu","style": {"navigationBarTitleText": "登录"}},
				{"path": "login/reg","custom_file":"denglu","style": {"navigationBarTitleText": "注册"}},
				{"path": "huodongbaoming/prolist","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "活动列表"}},
				{"path": "huodongbaoming/prolist2","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "活动列表"}},
				{"path": "shezhennew/index","style": {"navigationStyle": "custom","navigationBarTitleText": "AI在线舌诊"}},
				{"path": "shezhennew/analysis","style": {"navigationStyle": "custom","navigationBarTitleText": "AI分析中"}},
				{"path": "shezhennew/report","style": {"navigationStyle": "custom","navigationBarTitleText": "舌诊报告"}},
				{"path": "huodongbaoming/product","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "活动详情"}},
				{"path": "huodongbaoming/proorderlist","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "报名列表"}},
				{"path": "huodongbaoming/buy","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "订单确认"}},
				{"path": "huodongbaoming/orderlist","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "订单列表","enablePullDownRefresh": false,"titleNView":{"searchInput":{"placeholder":"输入关键字搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "huodongbaoming/orderdetail","custom_file":"huodong_baoming","style": {"navigationBarTitleText": "订单详情","enablePullDownRefresh": false}},
				{"path": "maidan/pay","style": {"navigationBarTitleText": "买单付款"}},
				{"path": "maidan/maidanlog","style": {"navigationBarTitleText": "买单付款记录","enablePullDownRefresh": true,"titleNView":{"searchInput":{"placeholder":"输入订单号搜索","borderRadius":"15px"},"buttons":[{"type":"home"}]}}},
				{"path": "maidan/maidandetail","style": {"navigationBarTitleText": "买单付款详情","enablePullDownRefresh": true}},
				{"path": "shezhen/guide","style": {"navigationBarTitleText": "舌诊指南","enablePullDownRefresh": false}},
				{"path": "shezhen/camera","style": {"navigationBarTitleText": "舌诊拍摄","enablePullDownRefresh": false}},
				{"path": "shezhen/photo-upload","style": {"navigationBarTitleText": "拍照上传","enablePullDownRefresh": false,"navigationStyle": "custom"}},
				{"path": "shezhen/analysis","style": {"navigationBarTitleText": "分析中","enablePullDownRefresh": false}},
				{"path": "shezhen/result","style": {"navigationBarTitleText": "舌诊结果","enablePullDownRefresh": false}},
				{"path": "shezhen/history","style": {"navigationBarTitleText": "舌诊历史","enablePullDownRefresh": false}},
				{"path": "shezhen/complete","style": {"navigationBarTitleText": "舌诊完整结果","enablePullDownRefresh": false}},
				{"path": "diagnosis/face/guide-new","style": {"navigationBarTitleText": "面诊指南","enablePullDownRefresh": false}},
				{"path": "diagnosis/face/camera-new","style": {"navigationBarTitleText": "面诊拍摄","enablePullDownRefresh": false}},
				{"path": "diagnosis/comprehensive/guide-new","style": {"navigationBarTitleText": "综合诊疗指南","enablePullDownRefresh": false}},
				{"path": "diagnosis/face/index","style": {"navigationBarTitleText": "面诊分析","enablePullDownRefresh": false}},
				{"path": "diagnosis/face/result","style": {"navigationBarTitleText": "面诊结果","enablePullDownRefresh": false}},
				{"path": "diagnosis/comprehensive/index","style": {"navigationBarTitleText": "综合诊疗","enablePullDownRefresh": false}},
				{"path": "diagnosis/comprehensive/result","style": {"navigationBarTitleText": "综合诊疗结果","enablePullDownRefresh": false}},
				
				
				
				{"path": "paidan/activity-list","style": {"navigationBarTitleText": "排单活动","enablePullDownRefresh": true}},
				{"path": "paidan/activity-products","style": {"navigationBarTitleText": "活动商品","enablePullDownRefresh": true}},
				{"path": "paidan/my-position","style": {"navigationBarTitleText": "我的点位","enablePullDownRefresh": true}},
				{"path": "paidan/position-tree","style": {"navigationBarTitleText": "排单树","enablePullDownRefresh": true}},
				{"path": "paidan/config-list","style": {"navigationBarTitleText": "排单配置","enablePullDownRefresh": true}},
				{"path": "paidan/reward-records","style": {"navigationBarTitleText": "奖励记录","enablePullDownRefresh": true}},
				{"path": "paidan/statistics","style": {"navigationBarTitleText": "排单统计","enablePullDownRefresh": true}},
				
				{"path": "shangxiang/index","style": {"navigationBarTitleText": "上香供奉","enablePullDownRefresh": true}},
				{"path": "shangxiang/myWishes","style": {"navigationBarTitleText": "我的心愿","enablePullDownRefresh": true}},
				{"path": "shangxiang/wishList","style": {"navigationBarTitleText": "心愿列表","enablePullDownRefresh": true}},
				{"path": "fenhongdian/fenhongdian","style": {"navigationBarTitleText": "分红点","enablePullDownRefresh": true}},
				{"path": "theater/dingchangrili", "style": { "navigationBarTitleText": "订场"} },
				{ "path": "theater/dingchangPayment", "style": { "navigationBarTitleText": "" } },
				{ "path": "theater/hexiao", "style": { "navigationBarTitleText": "" } },
				{ "path": "theater/detail", "style": { "navigationBarTitleText": "" } },
				{ "path": "theater/orderlist", "style": { "navigationBarTitleText": "" } },
				{ "path": "theater/dingchangOrderDetail", "style": { "navigationBarTitleText": "订场详情" } },
				{ "path": "dingchang/dingchanglist", "style": { "enablePullDownRefresh": true, "navigationBarTitleText": "订场列表" } },
				{ "path": "dingchang/dingchangdetail", "style": { "navigationBarTitleText": "订场详情" } },
				{ "path": "dingchang/yuelist", "style": { "navigationBarTitleText": "约场列表" } },
				{ "path": "dingchang/yuedetail", "style": { "navigationBarTitleText": "" } },
				{ "path": "dingchang/dingchangOrder", "style": { "enablePullDownRefresh": true, "navigationBarTitleText": "我的订场" } },
				{ "path": "dingchang/content", "style": { "navigationBarTitleText": "" } },
				{ "path": "dingchang/dingchangOrderDetail", "style": { "navigationBarTitleText": "订场详情" } },
				{ "path": "dingchang/dingchangrili", "style": { "navigationBarTitleText": "订场日历" } },
				{ "path": "dingchang/dingchangPayment", "style": { "navigationBarTitleText": "" } },
				{ "path": "dingchang/dingchangToPayment", "style": { "navigationBarTitleText": "" } },
				{ "path": "dingchang/dingchangIndent", "style": { "navigationBarTitleText": "订单" } },
				{ "path": "dingchang/commentdp", "style": { "navigationBarTitleText": "评价" } },

				{"path": "dreamark/index", "style": {"navigationBarTitleText": "梦想方舟", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "dreamark/dialogue", "style": {"navigationBarTitleText": "时空对话", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "dreamark/camera", "style": {"navigationBarTitleText": "AI变脸预测", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "dreamark/camera-new", "style": {"navigationBarTitleText": "AI变脸预测(新版)", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "dreamark/voice-chat", "style": {"navigationBarTitleText": "语音对话", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "dreamark/ending", "style": {"navigationBarTitleText": "时空之旅完成", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "dreamark/test", "style": {"navigationBarTitleText": "按钮测试页面", "enablePullDownRefresh": false}},

				{"path": "coze/index", "style": {"navigationBarTitleText": "扣子AI助手", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "coze/chat", "style": {"navigationBarTitleText": "AI聊天", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "coze/workflow", "style": {"navigationBarTitleText": "智能工作流", "enablePullDownRefresh": false, "navigationStyle": "custom"}},
				{"path": "coze/workflow-logs", "style": {"navigationBarTitleText": "工作流记录", "enablePullDownRefresh": true, "navigationStyle": "custom"}},
				{"path": "coze/history", "style": {"navigationBarTitleText": "对话历史", "enablePullDownRefresh": true, "navigationStyle": "custom"}}


			]
		},
		{
			"root": "hotel",
			"pages": [  
				{"path": "index/index","custom_file":"hotel","style": {"custom_file":"hotel","enablePullDownRefresh": false}},
				{"path": "index/hotellist","custom_file":"hotel","style": {"custom_file":"hotel","enablePullDownRefresh": false}},
				{"path": "index/hoteldetails","custom_file":"hotel","style": {"custom_file":"hotel","enablePullDownRefresh": false,"navigationStyle": "custom",
				 "componentPlaceholder": {"calendar": "view"}}},
				{"path": "index/buy","custom_file":"hotel","style": {"navigationBarTitleText": "提交订单","custom_file":"hotel","enablePullDownRefresh": false,"navigationStyle": "custom"}},
			  {"path": "index/signature","custom_file":"hotel","style": {"navigationBarTitleText": "签字","custom_file":"hotel","enablePullDownRefresh": false}},
				{"path": "order/orderlist","custom_file":"hotel","style": {"navigationBarTitleText": "订单列表","custom_file":"hotel","enablePullDownRefresh": false}},
				{"path": "order/orderdetail","custom_file":"hotel","style": {"navigationBarTitleText": "订单详情","custom_file":"hotel","enablePullDownRefresh": false}},
				{"path": "order/comment","custom_file":"hotel","style": {"navigationBarTitleText": "订单评价","custom_file":"hotel","enablePullDownRefresh": false}},
				{"path": "order/refund","custom_file":"hotel","style": {"navigationBarTitleText": "申请退款","custom_file":"hotel","enablePullDownRefresh": false}},
				{"path": "order/commentlist","custom_file":"hotel","style": {"navigationBarTitleText": "评价列表","enablePullDownRefresh": true}}
				
			]
		},
		{
			"root": "pagesExb",
			"pages": [
				
				{
					"path": "filem/filemlist",
					"style": {
						"navigationBarTitleText": "文件管理",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "filem/detail",
					"style": {
						"navigationBarTitleText": "文件详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "filem/pdf-viewer-page",
					"style": {
						"navigationBarTitleText": "PDF预览",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				 {
								"path": "daxuepage/clist",
								"style": {
									"navigationBarTitleText": "院校列表",
									"enablePullDownRefresh": false
								}
							}, {
								"path": "daxuepage/blist",
								"style": {
									"navigationBarTitleText": "院校列表",
									"enablePullDownRefresh": false
								}
							},
							{
								"path": "daxuepage/zhuanye",
								"style": {
									"navigationBarTitleText": "专业",
									"enablePullDownRefresh": false
								}
							},
							{
								"path": "daxuepage/index",
								"style": {
									"navigationBarTitleText": "院校",
									"enablePullDownRefresh": false
								}
							},
							{
								"path": "daxuepage/specialityDetails",
								"style": {
									"navigationBarTitleText": "详情",
									"enablePullDownRefresh": false
								}
							},
							{
								"path": "daxuepage/fractionalLine",
								"style": {
									"navigationBarTitleText": "分数线查询",
									"enablePullDownRefresh": false
								}
							},
							{
								"path": "daxuepage/fractionalLineList",
								"style": {
									"navigationBarTitleText": "分数线查询列表",
									"enablePullDownRefresh": false
								}
							},
							{
								"path": "daxuepage/schoolBlurb",
								"style": {
									"navigationBarTitleText": "学校简介",
									"enablePullDownRefresh": false
								}
				
							},
							{
								"path": "daxuepage/articledetail",
								"style": {
									"navigationBarTitleText": "招生资料",
									"enablePullDownRefresh": false
								}
				
							},{
					"path": "kanjia/index",
					"style": {
						"navigationBarTitleText": "砍价商品列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/product",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/join",
					"style": {
						"navigationBarTitleText": "砍价详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/helplist",
					"style": {
						"navigationBarTitleText": "帮砍列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/buy",
					"style": {
						"navigationBarTitleText": "订单确认",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kanjia/orderlist",
					"style": {
						"navigationBarTitleText": "砍价订单列表",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "kanjia/orderdetail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/refund",
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupon/couponlist",
					"style": {
						"navigationBarTitleText": "领券中心",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupon/mycoupon",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupon/coupondetail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "coupon/coupongive",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "toupiao/index",
					"style": {
						"navigationBarTitleText": "活动详情"
					}
				},
				{
					"path": "toupiao/detail",
					"style": {
						"navigationBarTitleText": "投票详情"
					}
				},
				{
					"path": "toupiao/phb",
					"style": {
						"navigationBarTitleText": "排行榜"
					}
				},
				{
					"path": "toupiao/baoming",
					"style": {
						"navigationBarTitleText": "我要报名"
					}
				},
				{
					"path": "toupiao/shuoming",
					"style": {
						"navigationBarTitleText": "活动说明"
					}
				},
				{
					"path": "training/index",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "training/detail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "training/pinglun",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "message/pinglun",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "message/msglist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "message/detail",
					"style": {
						"enablePullDownRefresh": true
					}
				},


				{
					"path": "yunkucun/prolist",
					"style": {
						"navigationBarTitleText": "商品列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yunkucun/xiajiorderlist",
					"style": {
						"navigationBarTitleText": "下级订货信息",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "liandong/myteamjilu",
					"style": {
						"navigationBarTitleText": "脱离回归记录",
						"enablePullDownRefresh": true
					}
					},
				{
					"path": "form/formlog",
					"style": {
						"navigationBarTitleText": "提交记录",
						"enablePullDownRefresh": true,
						"titleNView": {
							"searchInput": {
								"placeholder": "输入关键字搜索",
								"borderRadius": "15px"
							},
							"buttons": [{
								"type": "home"
							}]
						}
					}
				},
				{
					"path": "form/formdetail",
					"style": {
						"navigationBarTitleText": "详细信息",
						"enablePullDownRefresh": true
					}
				},
				
				{
					"path": "money/moneylog",
					"style": {
						"navigationBarTitleText": "明细查询",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "money/cashcoupon",
					"style": {
						"navigationBarTitleText": "现金券",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "money/yuejiechongzhi",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "money/withdraw",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "money/recharge",
					"style": {
						"enablePullDownRefresh": true
					}
				},{
			"path": "shop/category1",
			"style": {
				"navigationBarTitleText": "商品分类",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "shop/category2",
			"style": {
				"navigationBarTitleText": "商品分类",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "shop/category3",
			"style": {
				"navigationBarTitleText": "商品分类",
				"enablePullDownRefresh": false
			}
		},
		{ 
			"path": "shop/category4",
			"style": {
				"navigationBarTitleText": "商品分类",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "shop/classify",
			"style": {
				"navigationBarTitleText": "分类商品",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "shop/prolist",
			"style": {
				"navigationBarTitleText": "商品列表",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "shop/classify2",
			"style": {
				"navigationBarTitleText": "分类商品",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "shop/fastbuy",
			"style": {
				"navigationBarTitleText": "快速购买",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "shop/fastbuy2",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "business/index",
			"style": {
				"navigationBarTitleText": "商家详情",
				"enablePullDownRefresh": true
			}
		},{
			"path": "order/orderlist",
			"style": {
				"navigationBarTitleText": "订单列表",
				"enablePullDownRefresh": true
			}
		},
	
		
		{
			"path": "sign/index",
			"style": {
				"navigationBarTitleText": "签到",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "index/city",
			"style": {
				"enablePullDownRefresh": false,
				"navigationBarTitleText": "城市选择"
			}
		}
				
			]
		}
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
	],
	"navigateToMiniProgramAppIdList": [
		"wxeb490c6f9b154ef9",
		"wx2b03c6e691cd7370"
	],
	"requiredPrivateInfos": ["chooseAddress", "chooseLocation", "getLocation"],
	"sitemapLocation": "sitemap.json",
	"permission": {
		"scope.userLocation": {
			"desc": "你的位置信息将用于获取距离信息"
		}
	},
	"condition": {
		"current": 0,
		"list": [{
			"name": "",
			"path": "",
			"query": ""
		}]
	},
	"mp-weixin": {
		"requiredPrivateInfos": [
			"getLocation",
			"chooseLocation"
		]
	}
}