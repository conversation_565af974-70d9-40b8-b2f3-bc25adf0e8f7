<template>
	<view class="dp-tab" :style="{
	margin:params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0',
	borderRadius:params.borderradius*2.2+'rpx'
}">
		<view v-if="needfixed==1" style="width:100%;height:90rpx;"></view>
		<view class="dsn-tab-box" :class="needfixed==1?'fixed':''"
			:style="{background:params.bgcolor,padding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx'}">
			<scroll-view scroll-x="true" style="overflow: visible !important" :scroll-into-view="'tab-item-' + tabindex" scroll-with-animation>
				<view class="dsn-tab-box-content">
					<view v-for="(item,index) in data" :key="index" class="dp-tab-item" @tap="changetab" 
						:id="'tab-item-' + index"
						:data-index="index"
						:class="{'active': tabindex==index}"
						:style="{
							fontSize:(tabindex==index?params.fontsize1*2.5+'rpx':params.fontsize1*2+'rpx'),
							color:(tabindex==index?params.color2:params.color1),
							minWidth: (params.max_width || 80) + 'px',
							flexBasis: 'auto'
						}">
						
						<view v-if="params.showpic == 1" class="tab-item-content">
							
							<image mode="heightFix" class="tab-item-icon" :src="item.imgurl" v-if="item.imgurl" ></image>
							
							<view class="tab-item-text">{{item.name}}</view>
							
						</view>
						
                        <view class="tab-item-content-alt" v-else>
							
							<image mode="widthFix" class="tab-item-image" :src="item.imgurl" v-if="item.imgurl"></image>
							
							<view v-else>{{item.name}}</view>
							
						</view>
						
						<view class="dp-tab-item-after" :style="{background:params.arrowcolor}"
							v-if="params.arrowshow==1 && tabindex==index"></view>
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="dp-tab-content"
			:style="{background:params.bgcolor2,padding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx',transform: contentTransform}"
			@touchstart="touchStart" 
			@touchmove="touchMove" 
			@touchend="touchEnd"
			@touchcancel="touchEnd"
			:class="{'swiping': isSwiping, 'animating': swipeAnimating}">
			<view class="swipe-indicator" v-if="showSwipeIndicator" :class="swipeDirection === 'left' ? 'right-indicator' : 'left-indicator'">
				<view class="indicator-arrow" :class="swipeDirection === 'left' ? 'right-arrow' : 'left-arrow'"></view>
			</view>
			<view class="dp-tab-content-inner">
				<transition name="tab-content-fade" mode="out-in">
					<view :key="tabindex" class="tab-content-wrapper">
						<block v-for="(setData, index) in pagecontent" :key="index">
							<block v-if="setData.temp=='notice'">
								<dp-notice :params="setData.params" :data="setData.data"></dp-notice>
							</block>
							<block v-if="setData.temp=='banner'">
								<dp-banner :params="setData.params" :data="setData.data"></dp-banner>
							</block>
							<block v-if="setData.temp=='search'">
								<dp-search :params="setData.params" :data="setData.data"></dp-search>
							</block>
							<block v-if="setData.temp=='text'">
								<dp-text :params="setData.params" :data="setData.data"></dp-text>
							</block>
							<block v-if="setData.temp=='title'">
								<dp-title :params="setData.params" :data="setData.data"></dp-title>
							</block>
							<block v-if="setData.temp=='dhlist'">
								<dp-dhlist :params="setData.params" :data="setData.data"></dp-dhlist>
							</block>
							<block v-if="setData.temp=='line'">
								<dp-line :params="setData.params" :data="setData.data"></dp-line>
							</block>
							<block v-if="setData.temp=='blank'">
								<dp-blank :params="setData.params" :data="setData.data"></dp-blank>
							</block>
							<block v-if="setData.temp=='menu'">
								<dp-menu :params="setData.params" :data="setData.data"></dp-menu>
							</block>
							<block v-if="setData.temp=='map'">
								<dp-map :params="setData.params" :data="setData.data"></dp-map>
							</block>
							<block v-if="setData.temp=='cube'">
								<dp-cube :params="setData.params" :data="setData.data"></dp-cube>
							</block>
							<block v-if="setData.temp=='picture'">
								<dp-picture :params="setData.params" :data="setData.data"></dp-picture>
							</block>
							<block v-if="setData.temp=='pictures'">
								<dp-pictures :params="setData.params" :data="setData.data"></dp-pictures>
							</block>
							<block v-if="setData.temp=='video'">
								<dp-video :params="setData.params" :data="setData.data"></dp-video>
							</block>
							<block v-if="setData.temp=='tab'">
								<dp-tab :params="setData.params" :data="setData.data" :tabid="setData.id"
									:menuindex="menuindex"></dp-tab>
							</block>
							<block v-if="setData.temp=='shop'">
								<dp-shop :params="setData.params" :data="setData.data" :shopinfo="setData.shopinfo"></dp-shop>
							</block>
							<block v-if="setData.temp=='product'">
								<dp-product :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-product>
							</block>
							<block v-if="setData.temp=='collage'">
								<dp-collage :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-collage>
							</block>
							<block v-if="setData.temp=='luckycollage'">
								<dp-luckycollage :params="setData.params" :data="setData.data"></dp-luckycollage>
							</block>

							<block v-if="setData.temp=='kanjia'">
								<dp-kanjia :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-kanjia>
							</block>
							<block v-if="setData.temp=='yuyue'">
								<dp-yuyue :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-yuyue>
							</block>
							<block v-if="setData.temp=='seckill'">
								<dp-seckill :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-seckill>
							</block>
							<block v-if="setData.temp=='scoreshop'">
								<dp-scoreshop :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-scoreshop>
							</block>
							<block v-if="setData.temp=='tuangou'">
								<dp-tuangou :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-tuangou>
							</block>
							<block v-if="setData.temp=='kecheng'">
								<dp-kecheng :params="setData.params" :data="setData.data" :menuindex="menuindex"></dp-kecheng>
							</block>
							<block v-if="setData.temp=='restaurant_product'">
								<dp-restaurant-product :params="setData.params" :data="setData.data"
									:menuindex="menuindex"></dp-restaurant-product>
							</block>
							<block v-if="setData.temp=='coupon'">
								<dp-coupon :params="setData.params" :data="setData.data"></dp-coupon>
							</block>
							<block v-if="setData.temp=='article'">
								<dp-article :params="setData.params" :data="setData.data"></dp-article>
							</block>
							<block v-if="setData.temp=='business'">
								<dp-business :params="setData.params" :data="setData.data"></dp-business>
							</block>
							<block v-if="setData.temp=='shortvideo'">
								<dp-shortvideo :params="setData.params" :data="setData.data"></dp-shortvideo>
							</block>
							<block v-if="setData.temp=='daihuobiji'">
								<dp-daihuobiji :params="setData.params" :data="setData.data"></dp-daihuobiji>
							</block>
							<block v-if="setData.temp=='liveroom'">
								<dp-liveroom :params="setData.params" :data="setData.data"></dp-liveroom>
							</block>
							<block v-if="setData.temp=='button'">
								<dp-button :params="setData.params" :data="setData.data"></dp-button>
							</block>
							<block v-if="setData.temp=='hotspot'">
								<dp-hotspot :params="setData.params" :data="setData.data"></dp-hotspot>
							</block>
							<block v-if="setData.temp=='cover'">
								<dp-cover :params="setData.params" :data="setData.data"></dp-cover>
							</block>
							<block v-if="setData.temp=='richtext'">
								<dp-richtext :params="setData.params" :data="setData.data" :content="setData.content"></dp-richtext>
							</block>
							<block v-if="setData.temp=='form'">
								<dp-form :params="setData.params" :data="setData.data" :content="setData.content"></dp-form>
							</block>
							<block v-if="setData.temp=='form-log'">
								<dp-form-log :params="setData.params" :data="setData.data"></dp-form-log>
							</block>
							<block v-if="setData.temp=='userinfo'">
								<dp-userinfo :params="setData.params" :data="setData.data" :content="setData.content"></dp-userinfo>
							</block>
							<block v-if="setData.temp=='wxad'">
								<dp-wxad :params="setData.params" :data="setData.data"></dp-wxad>
							</block>
							<block v-if="setData.temp=='jidian'">
								<dp-jidian :params="setData.params" :data="setData.data"></dp-jidian>
							</block>
							<block v-if="setData.temp=='zhaopin'">
								<dp-zhaopin :params="setData.params" :data="setData.data"></dp-zhaopin>
							</block>
							<block v-if="setData.temp=='qiuzhi'">
								<dp-qiuzhi :params="setData.params" :data="setData.data"></dp-qiuzhi>
							</block>
							<block v-if="setData.temp=='xixie'">
								<dp-xixie :params="setData.params" :data="setData.data" @getdata="getIndexdata"></dp-xixie>
							</block>
						</block>
					</view>
				</transition>
			</view>
		</view>
		<loading v-if="loading && !fastLoading" :class="{'fast-loading': fastLoading}"></loading>
	</view>
</template>
<script>
	var app = getApp();
	export default {
		data() {
			return {
				loading: false,
				loadingTimer: null, // 延迟显示loading的计时器
				fastLoading: true, // 是否是快速加载（不显示loading状态）
				tabindex: -1,
				pagecontent: [],
				latitude: '',
				longitude: '',
				tabtop: 0,
				needfixed: 0,
				windowWidth: 375, // 默认宽度
				touchStartX: 0,  // 触摸开始X坐标
				touchStartY: 0,  // 触摸开始Y坐标
				touchEndX: 0,    // 触摸结束X坐标
				touchEndY: 0,    // 触摸结束Y坐标
				touchMoveX: 0,   // 当前触摸X坐标
				swipeDirection: '', // 滑动方向
				swipeThreshold: 50, // 滑动阈值，大于这个距离才触发切换
				verticalThreshold: 80, // 垂直滑动阈值，小于这个距离才触发水平滑动
				isSwiping: false,  // 是否正在滑动
				showSwipeIndicator: false, // 是否显示滑动指示器
				swipeAnimating: false, // 正在执行滑动动画
				currentSwipeOffset: 0, // 当前滑动偏移量
				maxSwipeOffset: 100,   // 最大滑动偏移量
				tabChangeAnimationDuration: 300, // 标签切换动画持续时间（毫秒）
				contentCache: {}, // 缓存的标签内容
				isPreloading: false, // 是否正在预加载
				preloadedIndexes: new Set(), // 已预加载的标签索引
				loadingTimestamp: 0, // 加载开始的时间戳
				minLoadingTime: 500, // 最小加载显示时间（毫秒）
			}
		},
		props: {
			params: {},
			data: {},
			tabid: {
				default: ''
			},
			menuindex: {
				default: -1
			},
			enableSwipe: { // 是否启用滑动切换
				type: Boolean,
				default: true
			},
			swipeAnimationSpeed: { // 滑动动画速度 (1-10)
				type: Number,
				default: 5
			},
			preloadAdjacent: { // 是否预加载相邻标签内容
				type: Boolean,
				default: true
			},
			enableCache: { // 是否启用内容缓存
				type: Boolean,
				default: true
			},
			cacheLifetime: { // 缓存有效期（毫秒）
				type: Number,
				default: 300000 // 5分钟
			}
		},
		computed: {
			// 是否有下一个标签
			hasNextTab() {
				return this.tabindex < this.data.length - 1;
			},
			// 是否有上一个标签
			hasPrevTab() {
				return this.tabindex > 0;
			},
			// 内容区域滑动变换样式
			contentTransform() {
				if (this.isSwiping && !this.swipeAnimating) {
					return `translateX(${this.currentSwipeOffset}px)`;
				}
				return 'translateX(0)';
			},
			// 当前标签ID
			currentTabId() {
				if (this.tabindex >= 0 && this.data && this.data[this.tabindex]) {
					return this.data[this.tabindex].id;
				}
				return null;
			},
			// 缓存键
			cacheKey() {
				return `${this.tabid}_${this.currentTabId}`;
			}
		},
		mounted: function() {
			// 获取设备信息
			this.getDeviceInfo();
			
			this.changetab({
				currentTarget: {
					dataset: {
						index: 0
					}
				}
			}, true); // 初次加载，允许显示loading
			
			var that = this;
			if (this.params.fixedtop == 1) {
				setTimeout(function() {
					let view0 = uni.createSelectorQuery().in(that).select('.dsn-tab-box')
					view0.fields({
						size: false, //是否返回节点尺寸（width height）
						rect: true, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
						scrollOffset: false, //是否返回节点的 scrollLeft scrollTop，节点必须是 scroll-view 或者 viewport
					}, (res) => {
						console.log("2023-07-01 14:30:25-INFO-[dp-tab][getDeviceInfo_002] 获取tab位置：", res);
						that.tabtop = res.top;
					}).exec();
				}, 100);
				uni.$on('onPageScroll', function(data) {
					that.scrollTop = data.scrollTop;
					if (data.scrollTop > 0 && data.scrollTop >= that.tabtop) {
						that.needfixed = 1;
					} else {
						that.needfixed = 0;
					}
				});
			}
			
			// 监听屏幕旋转或尺寸变化
			uni.$on('windowResize', this.getDeviceInfo);
		},
		beforeDestroy() {
			// 移除事件监听
			uni.$off('windowResize', this.getDeviceInfo);
			uni.$off('onPageScroll');
			
			// 清除所有计时器
			if (this.loadingTimer) {
				clearTimeout(this.loadingTimer);
			}
			
			// 清理内存缓存
			this.contentCache = null;
		},
		methods: {
			getDeviceInfo() {
				// 获取设备信息，用于自适应
				try {
					const info = uni.getSystemInfoSync();
					console.log("2023-07-01 14:30:25-INFO-[dp-tab][getDeviceInfo_001] 设备信息:", info);
					this.windowWidth = info.windowWidth;
					// 根据设备宽度调整滑动阈值
					this.swipeThreshold = this.windowWidth * 0.15; // 屏幕宽度的15%
					this.maxSwipeOffset = this.windowWidth * 0.25; // 最大滑动距离为屏幕宽度的25%
					
					// 调整动画速度
					this.tabChangeAnimationDuration = 500 - (this.swipeAnimationSpeed * 30);
					// 确保动画时间在合理范围内
					this.tabChangeAnimationDuration = Math.min(Math.max(this.tabChangeAnimationDuration, 150), 500);
				} catch (e) {
					console.log("2023-07-01 14:30:25-ERROR-[dp-tab][getDeviceInfo_003] 获取设备信息失败:", e);
				}
			},
			// 触摸开始事件
			touchStart(e) {
				if (!this.enableSwipe || this.swipeAnimating || this.data.length <= 1) return;
				
				this.touchStartX = e.touches[0].clientX;
				this.touchStartY = e.touches[0].clientY;
				this.touchMoveX = this.touchStartX;
				this.isSwiping = true;
				this.currentSwipeOffset = 0;
				
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][touchStart_001] 触摸开始:", {
					x: this.touchStartX,
					y: this.touchStartY
				});
			},
			// 触摸移动事件
			touchMove(e) {
				if (!this.enableSwipe || !this.isSwiping || this.swipeAnimating || this.data.length <= 1) return;
				
				const currentX = e.touches[0].clientX;
				const currentY = e.touches[0].clientY;
				this.touchMoveX = currentX;
				
				// 计算水平移动距离
				const distanceX = currentX - this.touchStartX;
				// 计算垂直移动距离
				const distanceY = currentY - this.touchStartY;
				
				// 如果垂直滑动距离大于阈值，则认为是在上下滚动页面，不触发标签切换
				if (Math.abs(distanceY) > this.verticalThreshold) {
					this.isSwiping = false;
					this.showSwipeIndicator = false;
					this.currentSwipeOffset = 0;
					return;
				}
				
				// 根据水平移动距离判断滑动方向
				if (distanceX > 0) {
					// 向右滑动 (显示上一个标签)
					if (!this.hasPrevTab) {
						// 没有上一个标签时，添加阻尼效果
						this.currentSwipeOffset = Math.sqrt(distanceX) * 3;
					} else {
						// 限制最大滑动距离
						this.currentSwipeOffset = Math.min(distanceX, this.maxSwipeOffset);
						
						// 预加载上一个标签内容
						if (this.preloadAdjacent && !this.isPreloading && distanceX > this.swipeThreshold / 2) {
							this.preloadTabContent(this.tabindex - 1);
						}
					}
					this.swipeDirection = 'right';
				} else {
					// 向左滑动 (显示下一个标签)
					if (!this.hasNextTab) {
						// 没有下一个标签时，添加阻尼效果
						this.currentSwipeOffset = -Math.sqrt(Math.abs(distanceX)) * 3;
					} else {
						// 限制最大滑动距离
						this.currentSwipeOffset = Math.max(distanceX, -this.maxSwipeOffset);
						
						// 预加载下一个标签内容
						if (this.preloadAdjacent && !this.isPreloading && Math.abs(distanceX) > this.swipeThreshold / 2) {
							this.preloadTabContent(this.tabindex + 1);
						}
					}
					this.swipeDirection = 'left';
				}
				
				// 显示滑动指示器
				if (Math.abs(distanceX) > this.swipeThreshold / 2) {
					// 只有在有效方向上才显示指示器
					if ((distanceX > 0 && this.hasPrevTab) || (distanceX < 0 && this.hasNextTab)) {
						this.showSwipeIndicator = true;
					} else {
						this.showSwipeIndicator = false;
					}
				} else {
					this.showSwipeIndicator = false;
				}
				
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][touchMove_001] 触摸移动:", {
					distanceX,
					distanceY,
					direction: this.swipeDirection,
					offset: this.currentSwipeOffset
				});
			},
			// 触摸结束事件
			touchEnd(e) {
				if (!this.enableSwipe || !this.isSwiping || this.swipeAnimating || this.data.length <= 1) return;
				
				this.touchEndX = e.changedTouches ? e.changedTouches[0].clientX : this.touchMoveX;
				this.touchEndY = e.changedTouches ? e.changedTouches[0].clientY : 0;
				
				// 计算水平移动距离
				const distanceX = this.touchEndX - this.touchStartX;
				// 计算垂直移动距离
				const distanceY = this.touchEndY - this.touchStartY;
				
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][touchEnd_001] 触摸结束:", {
					distanceX,
					distanceY,
					threshold: this.swipeThreshold
				});
				
				// 平滑回到原位的动画
				this.swipeAnimating = true;
				
				// 如果垂直滑动距离大于阈值，则不触发标签切换
				if (Math.abs(distanceY) > this.verticalThreshold) {
					this.animateToOrigin();
					return;
				}
				
				// 如果水平滑动距离大于阈值，则触发标签切换
				if (Math.abs(distanceX) > this.swipeThreshold) {
					if (distanceX > 0 && this.hasPrevTab) {
						// 切换到上一个标签
						this.swipeToTab(this.tabindex - 1, 'right');
					} else if (distanceX < 0 && this.hasNextTab) {
						// 切换到下一个标签
						this.swipeToTab(this.tabindex + 1, 'left');
					} else {
						// 不满足切换条件，回到原位
						this.animateToOrigin();
					}
				} else {
					// 滑动距离不够，回到原位
					this.animateToOrigin();
				}
			},
			// 动画回到原位
			animateToOrigin() {
				this.swipeAnimating = true;
				
				// 使用CSS过渡动画回到原位
				this.currentSwipeOffset = 0;
				
				// 动画结束后重置状态
				setTimeout(() => {
					this.resetSwipeState();
				}, 300);
			},
			// 滑动切换到指定标签
			swipeToTab(index, direction) {
				this.swipeAnimating = true;
				
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][swipeToTab_001] 滑动切换到标签:", index, "方向:", direction);
				
				// 设置动画目标位置（完全滑出屏幕）
				this.currentSwipeOffset = direction === 'left' ? -this.windowWidth : this.windowWidth;
				
				// 震动反馈
				this.vibrateDevice();
				
				// 在动画过程中切换到新标签
				setTimeout(() => {
					// 使用无感加载模式
					this.changetab({
						currentTarget: {
							dataset: {
								index: index
							}
						}
					}, false); // 无感加载，不显示loading
					
					// 重置状态并允许新的滑动操作
					setTimeout(() => {
						this.resetSwipeState();
					}, this.tabChangeAnimationDuration);
				}, 50);
			},
			// 震动反馈
			vibrateDevice() {
				try {
					if (uni.vibrateShort) {
						uni.vibrateShort({
							success: function() {
								console.log("2023-07-01 14:30:25-INFO-[dp-tab][vibrateDevice_001] 震动反馈成功");
							},
							fail: function(err) {
								console.log("2023-07-01 14:30:25-ERROR-[dp-tab][vibrateDevice_002] 震动反馈失败:", err);
							}
						});
					}
				} catch (e) {
					console.log("2023-07-01 14:30:25-ERROR-[dp-tab][vibrateDevice_003] 震动API异常:", e);
				}
			},
			// 重置滑动状态
			resetSwipeState() {
				this.isSwiping = false;
				this.showSwipeIndicator = false;
				this.swipeDirection = '';
				this.currentSwipeOffset = 0;
				this.swipeAnimating = false;
			},
			changetab: function(e, showLoading = true) {
				var that = this
				var idx = e.currentTarget.dataset.index;
				
				if(that.tabindex == idx){
					return
				}
				
				// 清除已有的loading计时器
				if (this.loadingTimer) {
					clearTimeout(this.loadingTimer);
					this.loadingTimer = null;
				}
				
				that.tabindex = idx;
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][changetab_001] 切换tab:", idx, that.data[idx].id);
				
				// 设置是否快速加载（不显示loading）
				this.fastLoading = !showLoading;
				
				// 先从缓存获取数据
				const cacheKey = `${that.tabid}_${that.data[idx].id}`;
				const cachedData = this.getCachedContent(cacheKey);
				
				if (cachedData) {
					console.log("2023-07-01 14:30:25-INFO-[dp-tab][changetab_002] 使用缓存数据:", cacheKey);
					that.pagecontent = cachedData;
					uni.$emit('waterfall');
					
					// 在后台更新缓存
					if (this.preloadAdjacent && !this.isPreloading) {
						setTimeout(() => {
							this.fetchTabContent(idx, true);
						}, 1000);
					}
				} else {
					this.getdata(showLoading);
				}
				
				// 滚动到选中的tab
				this.$nextTick(() => {
					setTimeout(() => {
						const query = uni.createSelectorQuery().in(this);
						query.select('#tab-item-' + idx).boundingClientRect(data => {
							console.log("2023-07-01 14:30:25-INFO-[dp-tab][changetab_003] 当前tab位置:", data);
						}).exec();
					}, 100);
				});
				
				// 预加载相邻标签
				if (this.preloadAdjacent) {
					this.$nextTick(() => {
						// 延迟预加载，优先加载当前标签内容
						setTimeout(() => {
							this.preloadAdjacentTabs(idx);
						}, 1000);
					});
				}
			},
			// 预加载指定标签内容
			preloadTabContent(index) {
				// 避免重复预加载
				if (this.isPreloading || this.preloadedIndexes.has(index)) {
					return;
				}
				
				if (index >= 0 && index < this.data.length) {
					this.isPreloading = true;
					const preloadId = this.data[index].id;
					const cacheKey = `${this.tabid}_${preloadId}`;
					
					// 检查是否已有缓存
					if (!this.getCachedContent(cacheKey)) {
						console.log("2023-07-01 14:30:25-INFO-[dp-tab][preloadTabContent_001] 预加载标签内容:", index, preloadId);
						this.fetchTabContent(index, true);
					} else {
						console.log("2023-07-01 14:30:25-INFO-[dp-tab][preloadTabContent_002] 使用缓存内容，无需预加载:", index, preloadId);
						this.isPreloading = false;
					}
					
					// 标记为已预加载
					this.preloadedIndexes.add(index);
				}
			},
			// 预加载相邻标签内容
			preloadAdjacentTabs(currentIndex) {
				// 预加载下一个标签
				if (currentIndex < this.data.length - 1) {
					this.preloadTabContent(currentIndex + 1);
				}
				
				// 预加载上一个标签
				if (currentIndex > 0) {
					this.preloadTabContent(currentIndex - 1);
				}
			},
			// 获取缓存内容
			getCachedContent(key) {
				if (!this.enableCache) return null;
				
				const cachedItem = this.contentCache[key];
				if (!cachedItem) return null;
				
				// 检查缓存是否过期
				const now = Date.now();
				if (now - cachedItem.timestamp > this.cacheLifetime) {
					// 缓存已过期，删除
					delete this.contentCache[key];
					return null;
				}
				
				return cachedItem.data;
			},
			// 缓存内容
			cacheContent(key, data) {
				if (!this.enableCache) return;
				
				this.contentCache[key] = {
					data: data,
					timestamp: Date.now()
				};
				
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][cacheContent_001] 缓存内容:", key);
			},
			// 获取指定标签内容
			fetchTabContent(index, isPreload = false) {
				const tabId = this.data[index].id;
				const cacheKey = `${this.tabid}_${tabId}`;
				
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][fetchTabContent_001] 获取标签内容:", {
					index,
					tabid: this.tabid,
					tabindexid: tabId,
					isPreload
				});
				
				app.post('ApiIndex/gettabcontent', {
					tabid: this.tabid,
					tabindexid: tabId,
					latitude: this.latitude,
					longitude: this.longitude
				}, (res) => {
					// 缓存获取到的内容
					if (res && res.pagecontent) {
						this.cacheContent(cacheKey, res.pagecontent);
					}
					
					// 如果是预加载，标记为已完成
					if (isPreload) {
						this.isPreloading = false;
					}
					
					// 如果加载的是当前显示的标签，更新内容
					if (index === this.tabindex && !isPreload) {
						this.pagecontent = res.pagecontent;
						this.loading = false;
						uni.$emit('waterfall');
					}
					
					// 检查是否需要位置信息
					if (this.latitude === '' && this.longitude === '' && res.needlocation) {
						this.requestLocation();
					}
				});
			},
			getdata: function(showLoading = true) {
				var that = this;
				
				// 设置加载开始时间戳
				this.loadingTimestamp = Date.now();
				
				// 如果不需要显示loading，直接跳过
				if (!showLoading) {
					that.loading = false;
				} else {
					// 延迟显示loading，避免闪烁
					this.loadingTimer = setTimeout(() => {
						that.loading = true;
						that.fastLoading = false;
					}, 300); // 300ms后才显示loading
				}
				
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][getdata_001] 获取tab内容，参数:", {
					tabid: that.tabid,
					tabindexid: that.data[that.tabindex].id,
					showLoading
				});
				
				app.post('ApiIndex/gettabcontent', {
					tabid: that.tabid,
					tabindexid: that.data[that.tabindex].id,
					latitude: that.latitude,
					longitude: that.longitude
				}, function(res) {
					console.log("2023-07-01 14:30:25-INFO-[dp-tab][getdata_002] 获取tab内容结果:", res);
					
					// 计算加载耗时
					const loadingTime = Date.now() - that.loadingTimestamp;
					
					// 清除loading计时器
					if (that.loadingTimer) {
						clearTimeout(that.loadingTimer);
						that.loadingTimer = null;
					}
					
					// 如果加载时间过短，延迟隐藏loading，避免闪烁
					if (loadingTime < that.minLoadingTime && that.loading) {
						setTimeout(() => {
							that.loading = false;
							that.updateContent(res);
						}, that.minLoadingTime - loadingTime);
					} else {
						that.loading = false;
						that.updateContent(res);
					}
					
					// 缓存内容
					if (that.enableCache && res.pagecontent) {
						const cacheKey = `${that.tabid}_${that.data[that.tabindex].id}`;
						that.cacheContent(cacheKey, res.pagecontent);
					}
				})
			},
			// 更新内容
			updateContent(res) {
				this.pagecontent = res.pagecontent;
				uni.$emit('waterfall');
				
				// 检查是否需要获取位置
				if (this.latitude === '' && this.longitude === '' && res.needlocation) {
					this.requestLocation();
				}
			},
			// 请求位置信息
			requestLocation() {
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][requestLocation_001] 需要获取位置");
				app.getLocation((res) => {
					console.log("2023-07-01 14:30:25-INFO-[dp-tab][requestLocation_002] 获取位置成功:", res);
					this.latitude = res.latitude;
					this.longitude = res.longitude;
					this.refreshCurrentTab();
				}, (res2) => {
					console.log("2023-07-01 14:30:25-WARNING-[dp-tab][requestLocation_003] 获取位置失败:", res2);
				});
			},
			// 刷新当前标签
			refreshCurrentTab() {
				if (this.tabindex >= 0) {
					this.fetchTabContent(this.tabindex);
				}
			},
			getIndexdata: function() {
				console.log("2023-07-01 14:30:25-INFO-[dp-tab][getIndexdata_001] 触发父组件获取数据");
				this.$emit('getdata');
			},
		}
	}
</script>
<style>
	.dp-tab {
		height: auto;
		position: relative;
		background: #fff;
		overflow: hidden;
		width: 100%;
		box-sizing: border-box;
	}

	.dsn-tab-box {
		display: flex;
		width: 100%;
		height: auto;
		min-height: 90rpx;
		background: #fff;
		box-sizing: border-box;
	}

	.dsn-tab-box.fixed {
		position: fixed;
		top: 0;
		z-index: 9;
		left: 0;
		right: 0;
	}

	.dsn-tab-box-content {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		flex-wrap: nowrap;
		position: relative;
		width: 100%;
	}

	.dp-tab-item {
		flex-grow: 0;
		padding: 0 20rpx;
		flex-shrink: 0;
		font-size: 28rpx;
		text-align: center;
		color: #666;
		height: 90rpx;
		overflow: hidden;
		position: relative;
		transition: all 0.3s;
		white-space: nowrap;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.dp-tab-item.active {
		font-weight: bold;
		transform: scale(1.05);
	}
	
	.tab-item-content {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.tab-item-content-alt {
		line-height: 90rpx;
		width: 100%;
		text-align: center;
	}
	
	.tab-item-icon {
		height: 20px;
		margin-right: 3px;
		flex-shrink: 0;
	}
	
	.tab-item-image {
		width: 100%;
		max-height: 70rpx;
	}
	
	.tab-item-text {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.dp-tab-item-after {
		position: absolute;
		left: 50%;
		margin-left: -24rpx;
		bottom: 10rpx;
		height: 3px;
		border-radius: 1.5px;
		width: 48rpx;
		transition: all 0.3s;
	}

	.dp-tab-content {
		min-height: 100rpx;
		width: 100%;
		box-sizing: border-box;
		position: relative;
		transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
		overflow: hidden;
	}
	
	.dp-tab-content.swiping {
		transition: none;
		will-change: transform;
	}
	
	.dp-tab-content.animating {
		transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
		will-change: transform;
	}
	
	.dp-tab-content-inner {
		width: 100%;
		position: relative;
	}
	
	/* 滑动指示器样式 */
	.swipe-indicator {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		width: 40px;
		height: 40px;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
		pointer-events: none;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
		opacity: 0.8;
		transition: opacity 0.3s;
	}
	
	.indicator-arrow {
		width: 0;
		height: 0;
		border-style: solid;
	}
	
	.left-arrow {
		border-width: 8px 10px 8px 0;
		border-color: transparent #ffffff transparent transparent;
		margin-right: 3px;
	}
	
	.right-arrow {
		border-width: 8px 0 8px 10px;
		border-color: transparent transparent transparent #ffffff;
		margin-left: 3px;
	}
	
	.swipe-indicator.left-indicator {
		left: 20px;
	}
	
	.swipe-indicator.right-indicator {
		right: 20px;
	}
	
	/* 响应式样式 */
	@media screen and (max-width: 375px) {
		.dp-tab-item {
			font-size: 24rpx !important;
			padding: 0 10rpx;
		}
		
		.tab-item-icon {
			height: 16px;
		}
		
		.swipe-indicator {
			width: 36px;
			height: 36px;
		}
	}
	
	@media screen and (min-width: 768px) {
		.dp-tab-item {
			min-height: 100rpx;
		}
		
		.dsn-tab-box {
			min-height: 100rpx;
		}
		
		.swipe-indicator {
			width: 50px;
			height: 50px;
		}
		
		.indicator-arrow.left-arrow {
			border-width: 10px 12px 10px 0;
		}
		
		.indicator-arrow.right-arrow {
			border-width: 10px 0 10px 12px;
		}
	}
	
	/* 淡入淡出动画效果 */
	.tab-content-fade-enter-active, .tab-content-fade-leave-active {
		transition: opacity 0.3s ease;
	}
	.tab-content-fade-enter, .tab-content-fade-leave-to {
		opacity: 0;
	}
	
	/* 隐藏短时间的加载状态 */
	.fast-loading {
		opacity: 0;
		transition: opacity 0.3s;
	}
	.loading-visible {
		opacity: 1;
	}
</style>