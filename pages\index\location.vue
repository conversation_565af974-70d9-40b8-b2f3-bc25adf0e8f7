<template>
	<view class="location-page">
		<!-- 顶部搜索区域 -->
		<view class="search-section">
			<view class="search-box">
				<view class="search-input-box">
					<image :src="pre_url+'/static/img/search.png'" mode="aspectFit" class="search-icon"/>
					<input 
						type="text" 
						v-model="keyword" 
						placeholder="搜索地点、小区、写字楼" 
						@input="handleSearch"
						class="search-input"
					/>
					<view class="clear-btn" v-if="keyword" @tap="clearSearch">
						<image :src="pre_url+'/static/img/close.png'" mode="aspectFit" class="clear-icon"/>
					</view>
				</view>
			</view>
			
			<!-- 获取当前位置按钮 -->
			<view class="get-location-btn" @tap="getCurrentLocation" :class="{'loading': isLocating}" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
				<image 
					:src="pre_url+'/static/img/location.png'" 
					mode="aspectFit" 
					class="btn-icon"
					:class="{'rotating': isLocating}"
				/>
				<text>{{isLocating ? '正在获取位置...' : '重新定位'}}</text>
			</view>
		</view>

		<!-- 当前位置卡片 -->
		<view class="current-location" @tap="useCurrentLocation">
			<view class="location-left">
				<view class="location-icon-wrap">
					<image :src="pre_url+'/static/img/location.png'" mode="aspectFit" class="location-icon"/>
				</view>
				<view class="location-info">
					<text class="title">当前位置</text>
					<text class="address">{{currentAddress || '定位中...'}}</text>
				</view>
			</view>
			<view class="use-btn" :class="{'disabled': !locationInfo}" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
				<text>{{locationInfo ? '使用' : '定位中'}}</text>
				<image :src="pre_url+'/static/img/arrow-right.png'" mode="aspectFit" class="arrow-icon"/>
			</view>
		</view>

		<!-- 搜索历史 -->
		<view class="history-section" v-if="!keyword && !searchResults.length">
			<view class="section-header">
				<text class="title">搜索历史</text>
				<view class="clear-history" @tap="clearHistory">
					<image :src="pre_url+'/static/img/delete.png'" mode="aspectFit" class="delete-icon"/>
					<text>清除</text>
				</view>
			</view>
			<view class="history-list">
				<view 
					class="history-item" 
					v-for="(item, index) in searchHistory" 
					:key="index"
					@tap="useHistoryItem(item)"
				>
					<image :src="pre_url+'/static/img/time.png'" mode="aspectFit" class="time-icon"/>
					<text class="history-text">{{item.name}}</text>
				</view>
			</view>
		</view>

		<!-- 搜索结果列表 -->
		<scroll-view 
			scroll-y 
			class="location-list"
			@scrolltolower="loadMore"
			v-if="searchResults.length > 0"
		>
			<view 
				class="location-item" 
				v-for="(item, index) in searchResults" 
				:key="index"
				@tap="selectLocation(item)"
				hover-class="item-hover"
			>
				<view class="location-detail">
					<view class="name-wrap">
						<text class="name">{{item.name}}</text>
						<text class="tag" v-if="item.type">{{item.type}}</text>
					</view>
					<text class="address">{{item.address}}</text>
				</view>
				<view class="right-area">
					<text class="distance">{{item.distance}}</text>
					<image :src="pre_url+'/static/img/arrow-right.png'" mode="aspectFit" class="arrow-icon"/>
				</view>
			</view>
			
			<!-- 加载更多提示 -->
			<view class="load-more" v-if="hasMore">
				<view class="loading-dot" v-if="loading">
					<view class="dot"></view>
					<view class="dot"></view>
					<view class="dot"></view>
				</view>
				<text>{{loading ? '加载中...' : '上拉加载更多'}}</text>
			</view>
		</scroll-view>

		<!-- 无搜索结果 -->
		<view class="no-result" v-else-if="keyword && !loading">
			<image :src="pre_url+'/static/img/no-result.png'" mode="aspectFit" class="no-result-icon"/>
			<text class="main-text">未找到相关地址</text>
			<text class="sub-text">换个关键词试试</text>
		</view>

		<!-- 加载中 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<view class="loading-spinner">
					<view class="spinner-item" v-for="i in 12" :key="i"></view>
				</view>
				<text>加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			pre_url: app.globalData.pre_url,
			keyword: '',
			currentAddress: '',
			locationInfo: null,
			searchResults: [],
			loading: false,
			page: 1,
			latitude: '',
			longitude: '',
			hasMore: true,
			isLocating: false,
			searchHistory: []  // 添加搜索历史数组
		}
	},
	onLoad(options) {
		if (options.data) {
			const data = JSON.parse(decodeURIComponent(options.data));
			this.latitude = data.latitude;
			this.longitude = data.longitude;
			this.currentAddress = data.current_address;
			this.locationInfo = data.location_info;
		}
		// 如果没有位置信息，获取当前位置
		if (!this.latitude || !this.longitude) {
			this.getCurrentLocation();
		}
		
		// 加载搜索历史
		this.loadSearchHistory();
	},
	methods: {
		// 处理颜色
		t(name) {
			try {
				const colors = app.globalData.colors || {};
				return colors[name] || '';
			} catch (e) {
				console.error('获取颜色错误:', e);
				return '';
			}
		},
		
		// 获取当前位置
		getCurrentLocation() {
			this.isLocating = true;
			this.loading = true;
			app.getLocation((res) => {
				this.latitude = res.latitude;
				this.longitude = res.longitude;
				
				// 获取详细地址信息
				app.get('apiIndex/getLocation', {
					latitude: res.latitude,
					longitude: res.longitude
				}, (result) => {
					if(result.status === 1) {
						this.currentAddress = result.data.district + result.data.street;
						this.locationInfo = result.data;
					}
					this.loading = false;
					this.isLocating = false;
				});
			}, (err) => {
				console.log('获取位置失败：', err);
				this.loading = false;
				this.isLocating = false;
				uni.showToast({
					title: '获取位置失败',
					icon: 'none'
				});
			});
		},
		
		// 处理搜索输入
		handleSearch() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
			}
			this.searchTimer = setTimeout(() => {
				this.page = 1;
				this.searchResults = [];
				this.hasMore = true;
				this.searchLocation();
			}, 500);
		},
		
		// 搜索位置
		searchLocation() {
			if (!this.keyword || this.loading || !this.hasMore) return;
			
			this.loading = true;
			app.get('apiIndex/searchLocation', {
				keyword: this.keyword,
				latitude: this.latitude,
				longitude: this.longitude,
				page: this.page
			}, (res) => {
				if (res.status === 1) {
					if (this.page === 1) {
						this.searchResults = res.data;
					} else {
						this.searchResults = [...this.searchResults, ...res.data];
					}
					this.hasMore = res.data.length === 10; // 假设每页10条数据
				}
				this.loading = false;
			});
		},
		
		// 加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.searchLocation();
			}
		},
		
		// 清除搜索
		clearSearch() {
			this.keyword = '';
			this.searchResults = [];
		},
		
		// 加载搜索历史
		loadSearchHistory() {
			try {
				const history = uni.getStorageSync('location_search_history');
				if (history) {
					this.searchHistory = JSON.parse(history);
				}
			} catch (e) {
				console.error('加载搜索历史失败', e);
			}
		},
		
		// 添加到搜索历史
		addToHistory(item) {
			// 如果存在相同项，先移除
			this.searchHistory = this.searchHistory.filter(his => his.name !== item.name);
			
			// 添加到最前面
			this.searchHistory.unshift({
				name: item.name,
				latitude: item.latitude,
				longitude: item.longitude,
				address: item.address
			});
			
			// 最多保存10条记录
			if (this.searchHistory.length > 10) {
				this.searchHistory = this.searchHistory.slice(0, 10);
			}
			
			// 保存到本地
			uni.setStorageSync('location_search_history', JSON.stringify(this.searchHistory));
		},
		
		// 清除历史记录
		clearHistory() {
			uni.showModal({
				title: '提示',
				content: '确定要清除所有搜索历史吗？',
				success: (res) => {
					if (res.confirm) {
						this.searchHistory = [];
						uni.removeStorageSync('location_search_history');
					}
				}
			});
		},
		
		// 使用历史记录项
		useHistoryItem(item) {
			// 将历史记录项作为选择项使用
			this.selectLocation(item);
		},
		
		// 使用当前位置
		useCurrentLocation() {
			if (!this.locationInfo) {
				uni.showToast({
					title: '位置信息不完整',
					icon: 'none'
				});
				return;
			}
			
			// 触发选择位置事件
			uni.$emit('location_selected', {
				...this.locationInfo,
				latitude: this.latitude,
				longitude: this.longitude,
				display_name: this.currentAddress
			});
			
			uni.navigateBack();
		},
		
		// 选择位置
		selectLocation(item) {
			// 添加到历史记录
			this.addToHistory(item);
			
			// 触发选择位置事件
			uni.$emit('location_selected', {
				...item,
				latitude: item.latitude,
				longitude: item.longitude,
				display_name: item.name
			});
			
			uni.navigateBack();
		}
	}
}
</script>

<style>
page{background:#ffffff}
.container{width:100%;}

.location-page{
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: env(safe-area-inset-bottom);
}

.search-section{
	background-color: #fff;
	padding: 24rpx 30rpx;
	position: sticky;
	top: 0;
	z-index: 100;
	box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.03);
}

.search-box{
	margin-bottom: 24rpx;
}

.search-input-box{
	display: flex;
	align-items: center;
	background-color: #f8f9fc;
	border-radius: 40rpx;
	padding: 24rpx 32rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.search-input-box:active{
	background-color: #f0f2f7;
	border-color: rgba(0,122,255,0.1);
}

.search-icon{
	width: 36rpx;
	height: 36rpx;
	margin-right: 20rpx;
	opacity: 0.5;
}

.search-input{
	flex: 1;
	font-size: 30rpx;
	color: #333;
	font-weight: 400;
}

.search-input::placeholder{
	color: #999;
	font-weight: 300;
}

.clear-btn{
	padding: 12rpx;
	margin-right: -12rpx;
}

.clear-icon{
	width: 28rpx;
	height: 28rpx;
	opacity: 0.3;
	transition: opacity 0.3s;
}

.clear-icon:active{
	opacity: 0.5;
}

.get-location-btn{
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 24rpx;
	background: linear-gradient(to right, #f0f2f7, #f8f9fc);
	border-radius: 40rpx;
	font-size: 28rpx;
	color: #ffffff;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.get-location-btn:active{
	transform: scale(0.98);
	border-color: rgba(0,122,255,0.1);
}

.get-location-btn.loading{
	opacity: 0.7;
	background: #f0f2f7;
}

.btn-icon{
	width: 36rpx;
	height: 36rpx;
	margin-right: 12rpx;
	opacity: 0.6;
}

.btn-icon.rotating{
	animation: rotate 1.2s linear infinite;
}

.current-location{
	background-color: #fff;
	margin: 24rpx 24rpx 0;
	padding: 36rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-radius: 28rpx;
	box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.03);
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.current-location:active{
	transform: scale(0.985);
	border-color: rgba(0,122,255,0.1);
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.02);
}

.location-left{
	display: flex;
	align-items: center;
	flex: 1;
}

.location-icon-wrap{
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #f0f2f7, #f8f9fc);
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.location-icon{
	width: 44rpx;
	height: 44rpx;
	opacity: 0.6;
}

.location-info{
	flex: 1;
}

.location-info .title{
	font-size: 32rpx;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
	font-weight: 600;
}

.location-info .address{
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
}

.use-btn{
	display: flex;
	align-items: center;
	padding: 20rpx 36rpx;
	background: linear-gradient(135deg, #007AFF, #0056b3);
	border-radius: 40rpx;
	box-shadow: 0 6rpx 16rpx rgba(0,122,255,0.15);
	transition: all 0.3s ease;
}

.use-btn:active{
	transform: scale(0.95);
	box-shadow: 0 3rpx 8rpx rgba(0,122,255,0.1);
}

.use-btn text{
	color: #fff;
	font-size: 28rpx;
	font-weight: 500;
}

.use-btn .arrow-icon{
	width: 28rpx;
	height: 28rpx;
	margin-left: 8rpx;
	opacity: 0.9;
}

.use-btn.disabled{
	background: linear-gradient(135deg, #ccc, #999);
	box-shadow: none;
	opacity: 0.8;
}

.history-section{
	background-color: #fff;
	margin: 24rpx 24rpx 0;
	padding: 30rpx;
	border-radius: 28rpx;
	box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.03);
}

.section-header{
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.section-header .title{
	font-size: 30rpx;
	color: #333;
	font-weight: 600;
}

.clear-history{
	display: flex;
	align-items: center;
	padding: 12rpx;
}

.delete-icon{
	width: 32rpx;
	height: 32rpx;
	opacity: 0.4;
	margin-right: 8rpx;
}

.clear-history text{
	font-size: 26rpx;
	color: #999;
}

.history-list{
	display: flex;
	flex-wrap: wrap;
	margin: 0 -10rpx;
}

.history-item{
	display: flex;
	align-items: center;
	padding: 16rpx 24rpx;
	background: #f8f9fc;
	border-radius: 32rpx;
	margin: 10rpx;
	transition: all 0.3s ease;
}

.history-item:active{
	transform: scale(0.95);
	background: #f0f2f7;
}

.time-icon{
	width: 28rpx;
	height: 28rpx;
	opacity: 0.4;
	margin-right: 8rpx;
}

.history-text{
	font-size: 26rpx;
	color: #666;
}

.location-list{
	background-color: #fff;
	margin: 24rpx 24rpx 0;
	max-height: calc(100vh - 320rpx);
	border-radius: 28rpx;
	box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.03);
	overflow: hidden;
}

.location-item{
	padding: 36rpx 30rpx;
	display: flex;
	align-items: center;
	border-bottom: 1rpx solid rgba(0,0,0,0.03);
	transition: all 0.3s ease;
}

.location-item:last-child{
	border-bottom: none;
}

.location-item.item-hover{
	background-color: #f8f9fc;
}

.location-detail{
	flex: 1;
	padding-right: 24rpx;
}

.name-wrap{
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.name{
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-right: 12rpx;
}

.tag{
	font-size: 22rpx;
	color: #007AFF;
	background: rgba(0,122,255,0.1);
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
}

.address{
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
}

.right-area{
	display: flex;
	align-items: center;
}

.distance{
	font-size: 26rpx;
	color: #666;
	margin-right: 16rpx;
}

.arrow-icon{
	width: 28rpx;
	height: 28rpx;
	opacity: 0.3;
	transition: opacity 0.3s;
}

.location-item:active .arrow-icon{
	opacity: 0.5;
}

.load-more{
	text-align: center;
	padding: 30rpx 0;
	color: #999;
	font-size: 26rpx;
	background: linear-gradient(to bottom, transparent, rgba(248,249,252,0.8));
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-dot{
	display: flex;
	align-items: center;
	margin-right: 12rpx;
}

.dot{
	width: 12rpx;
	height: 12rpx;
	background: #999;
	border-radius: 50%;
	margin: 0 4rpx;
	opacity: 0.6;
	animation: dot-jump 1.4s infinite ease-in-out;
}

.dot:nth-child(2){
	animation-delay: 0.2s;
}

.dot:nth-child(3){
	animation-delay: 0.4s;
}

.no-result{
	padding: 160rpx 0;
	text-align: center;
	color: #999;
}

.no-result-icon{
	width: 280rpx;
	height: 280rpx;
	margin-bottom: 40rpx;
	opacity: 0.8;
}

.main-text{
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 16rpx;
}

.sub-text{
	font-size: 26rpx;
	color: #999;
	opacity: 0.8;
}

.loading-overlay{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(255,255,255,0.95);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	backdrop-filter: blur(8px);
	-webkit-backdrop-filter: blur(8px);
}

.loading-content{
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-spinner{
	position: relative;
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 24rpx;
}

.spinner-item{
	position: absolute;
	width: 8rpx;
	height: 24rpx;
	background: var(--color1);
	border-radius: 4rpx;
	animation: spinner-fade 1.2s linear infinite;
}

@keyframes dot-jump{
	0%, 80%, 100%{
		transform: translateY(0);
	}
	40%{
		transform: translateY(-8rpx);
	}
}

@keyframes spinner-fade{
	0%{
		opacity: 1;
	}
	100%{
		opacity: 0.15;
	}
}

@keyframes rotate{
	from{
		transform: rotate(0deg);
	}
	to{
		transform: rotate(360deg);
	}
}

::-webkit-scrollbar{
	width: 0;
	height: 0;
	background: transparent;
}

/* 添加spinner-item的12个状态 */
.spinner-item:nth-child(1){ transform: rotate(30deg); animation-delay: 0s; }
.spinner-item:nth-child(2){ transform: rotate(60deg); animation-delay: 0.1s; }
.spinner-item:nth-child(3){ transform: rotate(90deg); animation-delay: 0.2s; }
.spinner-item:nth-child(4){ transform: rotate(120deg); animation-delay: 0.3s; }
.spinner-item:nth-child(5){ transform: rotate(150deg); animation-delay: 0.4s; }
.spinner-item:nth-child(6){ transform: rotate(180deg); animation-delay: 0.5s; }
.spinner-item:nth-child(7){ transform: rotate(210deg); animation-delay: 0.6s; }
.spinner-item:nth-child(8){ transform: rotate(240deg); animation-delay: 0.7s; }
.spinner-item:nth-child(9){ transform: rotate(270deg); animation-delay: 0.8s; }
.spinner-item:nth-child(10){ transform: rotate(300deg); animation-delay: 0.9s; }
.spinner-item:nth-child(11){ transform: rotate(330deg); animation-delay: 1.0s; }
.spinner-item:nth-child(12){ transform: rotate(360deg); animation-delay: 1.1s; }
</style> 