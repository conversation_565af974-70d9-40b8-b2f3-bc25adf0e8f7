<template>
	<view class="plan-container">
		<!-- 2025-01-03 22:55:53,565-INF0-[plan][init_001] 健康调理方案页面初始化 -->
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<view class="nav-title">健康报告</view>
			<view class="nav-right">
				<text class="menu-icon">•••</text>
				<text class="record-icon">●</text>
			</view>
		</view>
		
		<!-- VIP会员专享头部 -->
		<view class="vip-header-section">
			<view class="vip-badge">
				<text class="vip-icon">👑</text>
				<text class="vip-text">VIP会员专享</text>
			</view>
			
			<view class="vip-title-card">
				<text class="hospital-name">广东省中医院为您专属定制</text>
				<text class="plan-title">健康调理方案</text>
			</view>
		</view>
		
		<!-- 报告解读 -->
		<view class="report-section">
			<view class="report-header">
				<text class="report-title">报告解读</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="symptoms-summary">
				<text class="summary-text">您的主症为</text>
				<text class="symptom-highlight water">水湿</text>
				<text class="summary-text">，副症为</text>
				<text class="symptom-highlight spleen">脾胃虚</text>
			</view>
			
			<view class="main-symptom-detail">
				<view class="symptom-label">主症【水湿】：</view>
				<text class="symptom-description">您体内水液代谢出现了异常，停滞在体内，也可以理解为人体新陈代谢速率变慢，导致体内的水分不能被正常的排出。</text>
			</view>
			
			<view class="view-detail-btn">
				<text class="lock-icon">🔒</text>
				<text class="btn-text">查看体征解读内容</text>
			</view>
			
			<view class="doctor-section">
				<text class="doctor-title">名医为您解读体征</text>
				<image class="doctor-avatar" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view>
		
		<!-- 调理原则 -->
		<view class="principle-section">
			<view class="principle-header">
				<text class="principle-title">调理原则</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="principle-list">
				<view class="principle-item">
					<text class="principle-bullet">•</text>
					<text class="principle-text">日常饮食不要吃得过于油腻，油腻的食物会</text>
				</view>
				<view class="principle-item">
					<text class="principle-bullet">•</text>
					<text class="principle-text">每天积累运动要达到半小时以上，可以有效改善水湿体质，增强自己的体质。</text>
				</view>
			</view>
			
			<view class="view-principle-btn">
				<text class="lock-icon">🔒</text>
				<text class="btn-text">查看病症调理原则</text>
			</view>
			
			<view class="custom-plan-text">
				<text>名医为您定制专属调理方案</text>
			</view>
		</view>
		
		<!-- 今日营养目标 -->
		<view class="nutrition-section">
			<view class="nutrition-header">
				<text class="nutrition-title">今日营养目标</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="nutrition-chart">
				<view class="chart-left">
					<view class="pie-chart">
						<!-- 饼图显示各营养素比例 -->
						<view class="pie-segment carb" :style="{transform: 'rotate(0deg)'}"></view>
						<view class="pie-segment protein" :style="{transform: 'rotate(223deg)'}"></view>
						<view class="pie-segment fat" :style="{transform: 'rotate(259deg)'}"></view>
					</view>
				</view>
				
				<view class="chart-right">
					<view class="nutrition-item">
						<view class="nutrition-color carb-color"></view>
						<text class="nutrition-name">碳水化物</text>
						<text class="nutrition-percent">62%</text>
						<text class="nutrition-amount">385克</text>
					</view>
					<view class="nutrition-item">
						<view class="nutrition-color protein-color"></view>
						<text class="nutrition-name">蛋白质</text>
						<text class="nutrition-percent">10%</text>
						<text class="nutrition-amount">62克</text>
					</view>
					<view class="nutrition-item">
						<view class="nutrition-color fat-color"></view>
						<text class="nutrition-name">脂肪</text>
						<text class="nutrition-percent">28%</text>
						<text class="nutrition-amount">77克</text>
					</view>
				</view>
				
				<view class="total-calories">
					<text class="calories-number">2484</text>
					<text class="calories-unit">千卡</text>
				</view>
			</view>
		</view>
		
		<!-- 今日专属膳方 -->
		<view class="recipe-section">
			<view class="recipe-header">
				<text class="recipe-title">今日专属膳方</text>
				<text class="adjust-plan">调整膳方计划 ></text>
			</view>
			
			<view class="recipe-card">
				<view class="recipe-content">
					<view class="recipe-info">
						<text class="recipe-name">黄酒煮鸡</text>
						<text class="recipe-effect">温中养血，散寒通络</text>
					</view>
					<view class="recipe-image-container">
						<image class="recipe-image" src="/static/images/huangjiu-chicken.png"></image>
					</view>
				</view>
			</view>
			
			<view class="doctor-recommendation">
				<image class="doctor-avatar-small" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="btn-secondary">返回</view>
			<view class="btn-secondary">重新拍照</view>
			<view class="btn-primary">分享</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 2025-01-03 22:55:53,565-INF0-[plan][data_001] 初始化调理方案页面数据
				nutritionData: {
					carbohydrate: { percent: 62, amount: 385 },
					protein: { percent: 10, amount: 62 },
					fat: { percent: 28, amount: 77 },
					totalCalories: 2484
				},
				todayRecipe: {
					name: '黄酒煮鸡',
					effect: '温中养血，散寒通络',
					image: '/static/images/huangjiu-chicken.png'
				}
			}
		},
		methods: {
			// 2025-01-03 22:55:53,565-INF0-[plan][goBack_001] 返回上一页功能
			goBack() {
				console.log('2025-01-03 22:55:53,565-INF0-[plan][goBack_001] 用户点击返回');
				uni.navigateBack();
			},
			
			// 2025-01-03 22:55:53,565-INF0-[plan][viewDetail_001] 查看详情功能
			viewDetailContent() {
				console.log('2025-01-03 22:55:53,565-INF0-[plan][viewDetail_001] 用户点击查看体征解读');
				// 需要VIP权限
			},
			
			// 2025-01-03 22:55:53,565-INF0-[plan][viewPrinciple_001] 查看调理原则功能
			viewPrincipleContent() {
				console.log('2025-01-03 22:55:53,565-INF0-[plan][viewPrinciple_001] 用户点击查看调理原则');
				// 需要VIP权限
			},
			
			// 2025-01-03 22:55:53,565-INF0-[plan][adjustPlan_001] 调整膳方计划功能
			adjustRecipePlan() {
				console.log('2025-01-03 22:55:53,565-INF0-[plan][adjustPlan_001] 用户点击调整膳方计划');
				// 调整膳方逻辑
			}
		},
		
		onLoad() {
			console.log('2025-01-03 22:55:53,565-INF0-[plan][onLoad_001] 健康调理方案页面加载完成');
		}
	}
</script>

<style scoped>
/* 2025-01-03 22:55:53,565-INF0-[plan][style_001] 健康调理方案页面样式定义 */

.plan-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 顶部导航样式 */
.top-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: white;
	position: relative;
}

.nav-left {
	display: flex;
	align-items: center;
}

.back-icon {
	font-size: 40rpx;
	color: #333;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.nav-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.menu-icon {
	font-size: 30rpx;
	color: #666;
}

.record-icon {
	font-size: 30rpx;
	color: #333;
}

/* VIP头部 */
.vip-header-section {
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	padding: 30rpx;
}

.vip-badge {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.vip-icon {
	font-size: 32rpx;
}

.vip-text {
	font-size: 28rpx;
	color: #8b4513;
	font-weight: 500;
}

.vip-title-card {
	text-align: center;
}

.hospital-name {
	font-size: 32rpx;
	color: #8b4513;
	margin-bottom: 10rpx;
	display: block;
}

.plan-title {
	font-size: 48rpx;
	color: #8b4513;
	font-weight: bold;
}

/* 报告解读 */
.report-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.report-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.report-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.member-badge {
	background-color: #ffd700;
	color: #8b4513;
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.symptoms-summary {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 30rpx;
	flex-wrap: wrap;
}

.summary-text {
	font-size: 28rpx;
	color: #333;
}

.symptom-highlight {
	font-size: 32rpx;
	font-weight: 500;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
}

.symptom-highlight.water {
	background-color: #e6f7ff;
	color: #1890ff;
}

.symptom-highlight.spleen {
	background-color: #f6ffed;
	color: #52c41a;
}

.main-symptom-detail {
	margin-bottom: 30rpx;
}

.symptom-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.symptom-description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.view-detail-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	background-color: #ffd700;
	padding: 25rpx;
	border-radius: 25rpx;
	margin-bottom: 30rpx;
}

.lock-icon {
	font-size: 24rpx;
}

.btn-text {
	font-size: 28rpx;
	color: #8b4513;
	font-weight: 500;
}

.doctor-section {
	text-align: center;
	position: relative;
}

.doctor-title {
	font-size: 28rpx;
	color: #8b4513;
	margin-bottom: 20rpx;
	display: block;
}

.doctor-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 调理原则 */
.principle-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.principle-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.principle-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.principle-list {
	margin-bottom: 30rpx;
}

.principle-item {
	display: flex;
	align-items: flex-start;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.principle-bullet {
	color: #1890ff;
	font-size: 24rpx;
	font-weight: bold;
	margin-top: 5rpx;
}

.principle-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	flex: 1;
}

.view-principle-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	background-color: #ffd700;
	padding: 25rpx;
	border-radius: 25rpx;
	margin-bottom: 30rpx;
}

.custom-plan-text {
	text-align: center;
}

.custom-plan-text text {
	font-size: 28rpx;
	color: #8b4513;
}

/* 营养目标 */
.nutrition-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.nutrition-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.nutrition-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.nutrition-chart {
	display: flex;
	align-items: center;
	gap: 40rpx;
	position: relative;
}

.chart-left {
	flex-shrink: 0;
}

.pie-chart {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	background: conic-gradient(
		#8884d8 0% 62%,
		#82ca9d 62% 72%,
		#ffc658 72% 100%
	);
	position: relative;
}

.chart-right {
	flex: 1;
}

.nutrition-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
}

.nutrition-color {
	width: 20rpx;
	height: 20rpx;
	border-radius: 4rpx;
}

.carb-color {
	background-color: #8884d8;
}

.protein-color {
	background-color: #82ca9d;
}

.fat-color {
	background-color: #ffc658;
}

.nutrition-name {
	font-size: 24rpx;
	color: #666;
	flex: 1;
}

.nutrition-percent {
	font-size: 24rpx;
	color: #333;
	width: 60rpx;
}

.nutrition-amount {
	font-size: 24rpx;
	color: #666;
	width: 80rpx;
}

.total-calories {
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	text-align: center;
}

.calories-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	display: block;
}

.calories-unit {
	font-size: 24rpx;
	color: #666;
}

/* 专属膳方 */
.recipe-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.recipe-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.recipe-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.adjust-plan {
	font-size: 24rpx;
	color: #1890ff;
}

.recipe-card {
	background-color: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.recipe-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.recipe-info {
	flex: 1;
}

.recipe-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
}

.recipe-effect {
	font-size: 24rpx;
	color: #666;
}

.recipe-image-container {
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	overflow: hidden;
}

.recipe-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.doctor-recommendation {
	text-align: right;
}

.doctor-avatar-small {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 底部按钮 */
.bottom-buttons {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	background-color: white;
	margin-top: 30rpx;
}

.btn-secondary {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	border: 2rpx solid #ddd;
	border-radius: 25rpx;
	font-size: 28rpx;
	color: #666;
}

.btn-primary {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	background-color: #1890ff;
	color: white;
	border-radius: 25rpx;
	font-size: 28rpx;
}
</style> 