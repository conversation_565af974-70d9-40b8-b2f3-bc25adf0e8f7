<template>
<view class="container">
	<block v-if="isload">
		<view class="content">
			<view class="info-item" style="height:136rpx;line-height:136rpx">
				<view class="t1" style="flex:1;">我的数据详情</view>
			</view>
		</view>
		<view class="content">
			<view class="info-item">
				<view>今日订单信息</view>
			</view>
			<view class="info-item">
				<view class="t1">成交订单数</view>
				<view class="t2">{{userinfo.ordercount}}单</view>
			</view>
			<view class="info-item">
				<view class="t1">成交额</view>
				<view class="t2">{{userinfo.ordersum}}元</view>
			</view>
		</view>
		<view class="content">
			<view class="info-item">
				<view>今日发放分红信息</view>
			</view>
			<view class="info-item">
				<text class="t1">分红总额</text>
				<text class="t2">{{userinfo.fenhong}}元</text>
			</view>
			<view class="info-item">
				<text class="t1">直推奖总额</text>
				<text class="t2">{{userinfo.zhituifenhong}}元</text>
			</view>
		</view>
		<view class="content">
			<view class="info-item">
				<view>等级下级信息</view>
			</view>
		</view>
		<view class="content" v-for="(item,index) in levearr">
			<view class="info-item">
				<text class="t1">{{item.name}}人数</text>
				<text class="t2">{{item.xiajicount}}人</text>
			</view>
			<view class="info-item">
				<text class="t1">直推奖励</text>
				<text class="t2">{{item.commission1str}}</text>
			</view>
			<view class="info-item">
				<text class="t1">分红点数</text>
				<text class="t2">{{item.fenhong}}%</text>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			userinfo:{},
			levearr:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/mydata', {}, function (data) {
				that.loading = false;
				that.userinfo = data.userinfo;
				that.levearr= data.levearr;
				that.loaded();
			});
		},
  }
};
</script>
<style>
.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}


</style>