<template>
	<view class="page-section page-section-gap map-container">
		<map style="width: 100%; height: 100vh;" :layer-style='5' :show-location='true' :latitude="latitude"
			:longitude="longitude" :markers="marker" :scale="scale" @markertap="markertap" @callouttap='callouttap'>
			<!-- 弹出层 -->
			<cover-view>
				<uni-popup ref="popup" type="bottom" border-radius="10px 10px 0 0">
						<div class="map-marker-popup-container" v-if="currentMerchant">
							<div class="oilStation-top">
								<div class="oilStation-top-icon">
									<div class="oilStation-top-iconBg">
										<!-- @tap.stop="preViewImage(currentMerchant.logo)" -->
										<image class="oilStation-top-iconBg-img" :src="currentMerchant.logo"
											mode="widthFix"></image>
									</div>
								</div>
								<div class="oilStation-top-name">
									<div class="oilStation-top-nameBox">
										<text class="oilStation-top-nameBox-name">{{ currentMerchant.name }}</text>
										<text class="oilStation-top-nameBox-address">{{ currentMerchant.address }}</text>
									</div>
								</div>
								<div class="oilStation-top-navigation" >
									<div class="oilStation-top-navigation-iconBg"  @click="navigateToMerchantPage">
										<text class="iconfont icondingwei"></text>
										<text class="oilStation-top-navigation-iconBg-span" >租机</text>
									</div>
								</div>
							</div>
				
				
							<div class="oilStation-bottom">
								<div class="oilStation-bottom-collectBox">
									<!-- <text class="oilStation-bottom-collectBox-collect">加收藏</text> -->
									<div class="oilStation-bottom-collectBox-score">
										<text class="iconfont iconstar"></text>
										<text class="oilStation-bottom-collectBox-score-span">4.9分</text>
									</div>
								</div>
								<div class="oilStation-bottom-lineUpBox">
									<div class="oilStation-bottom-lineUpBox-lineUp">
										<div class="oilStation-bottom-lineUpBox-headBox">
											<image class="oilStation-bottom-lineUpBox-headBox-medal" :src="currentMerchant.logo"
												mode="widthFix"></image>
											<image class="oilStation-bottom-lineUpBox-headBox-medal" :src="currentMerchant.logo"
												mode="widthFix"></image>
											<image class="oilStation-bottom-lineUpBox-headBox-medal" :src="currentMerchant.logo"
												mode="widthFix"></image>
										</div>
										<div class="oilStation-bottom-lineUpBox-lineUpNum">
											<text class="oilStation-bottom-lineUpBox-lineUpNum-num">1622</text>
											<text class="oilStation-bottom-lineUpBox-lineUpNum-txt">人正在租机</text>
										</div>
									</div>
								</div>
								<div class="oilStation-bottom-distanceBox">
									<div class="oilStation-bottom-distanceBox-distance">
										<text
											class="oilStation-bottom-distanceBox-num" v-if="currentMerchant && currentMerchant.juli">{{ setDistance(currentMerchant.juli) }}</text>
										<text class="oilStation-bottom-distanceBox-txt">km</text>
										<text class="iconfont icondingwei" style="font-size: 30rpx;"></text>
									</div>
								</div>
							</div>
				
				
							<!-- <div class="todayOilPrice">
								<div class="oil-title">
									<text class="oil-title-txt">今日油价</text>
								</div>
								<div class="todayOil-content">
									<div class="todayOil-priceByOilNum">
										<text class="todayOil-priceByOilNum-sheng shengActive">6.75/L</text>
										<text class="todayOil-priceByOilNum-oilNum">92#</text>
										<div class="myCarLabel">
											<text class="myCarLabel-txt">我的车辆</text>
										</div>
									</div>
									<div class="todayOil-priceByOilNum">
										<text class="todayOil-priceByOilNum-sheng">7.20/L</text>
										<text class="todayOil-priceByOilNum-oilNum"> 95#</text>
										<div class="myCarLabel">
											<text class="myCarLabel-txt">我的车辆</text>
										</div>
									</div>
									<div class="todayOil-priceByOilNum">
										<text class="todayOil-priceByOilNum-sheng">7.84/L</text>
										<text class="todayOil-priceByOilNum-oilNum">98#</text>
									</div>
									<div class="todayOil-priceByOilNum">
										<text class="todayOil-priceByOilNum-sheng">8.72/L</text>
										<text class="todayOil-priceByOilNum-oilNum">101#</text>
									</div>
								</div>
							</div> -->
						</div>
					</uni-popup>
				
			</cover-view>
		</map>

	</view>
</template>
<script>
	var app = getApp();
	export default {
		data() {
			return {
				latitude: 23.106574, //纬度
				longitude: 113.324587,   
				scale: 12, //缩放级别
				bottomData: false,
				marker: [],
				currentMerchant: null, // 当前点击商家
				merchantList: [], // 附近商家列表
			}
		},
		onLoad() {
			this.getLocation()
		},
		computed: {
			coverbottom() {
				let data = '100rpx'
				return data
			}
		},
		onPullDownRefresh: function() {
			this.getLocation();
		},
		methods: {
			// 获取附近商家
			getNearbyBusiness() {
				this.marker = [];
				const that = this;
				app.post('ApiBusiness/blist', {
					pagenum: 1,
					field: "juli",
					order: "asc",
					longitude: this.longitude,
					latitude: this.latitude,
					keyword: ''
				}, function(res) {
					if (res.data?.length) {
						that.merchantList = res.data;
						res.data.forEach(item => {
							that.marker.push({
								id: item.id,
								latitude: item.latitude, // 纬度
								longitude: item.longitude, //经度
								iconPath: '/static/img/address.png', // 显示的图标   
								rotate: 0, // 旋转度数
								width: 20,
								height: 30,
								alpha: 0.5, // 图片透明度
								callout: { // 自定义标记点上方的气泡窗口 点击有效
									content: item.name, // 文本
									color: '#ffffff', // 文字颜色
									fontSize: 14, // 文本大小
									borderRadius: 15, // 边框圆角
									borderWidth: '10',
									bgColor: '#e51860', // 背景颜色
									display: 'ALWAYS', // 常显
								},
							})
						})
					}
				});
			},
			setDistance(distance) {
				if (distance.includes("km")) {
					return distance.slice(0, -2)
				}
				return distance
			},
			getLocation() {
				const that = this;
				uni.getLocation({
					type: 'gcj02',
					complete: function(res) {
						if (res.longitude && res.latitude) {
							that.longitude = res.longitude
							that.latitude = res.latitude
						}
						// 获取附近商家
						that.getNearbyBusiness()
					}
				});
			},
			//地图标注点icon点击事件
			callouttap(e) {
				this.currentMerchant = this.merchantList.find(merchant => merchant.id === e.detail.markerId)
				this.$refs.popup.open('top')
			},
			// 点击租机按钮跳转
			  navigateToMerchantPage() {
			        console.log('Navigating to merchant page', this.currentMerchant);
			        if (this.currentMerchant && this.currentMerchant.id) {
			            uni.navigateTo({
			                url: `/pagesExt/zuji/prolistzuji?bid=${this.currentMerchant.id}`
			            });
			        } else {
			            console.log('Current merchant is null or has no id');
			        }
			    },
			onControltap() {
				this.getLocation()
			},
		}
	}
</script>

<style lang="less">
	.map-marker-popup-container {
		padding: 80rpx;
		background: #fff;
		border-radius: 40rpx 40rpx 0 0;

		.oilStation-top {
			width: 594rpx;
			height: 135rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
		}

		.oilStation-top-icon {
			width: 110rpx;
			/* height: 100%; */
			justify-content: center;
			align-items: center;
			flex-direction: row;
		}

		.oilStation-top-iconBg {
			width: 90rpx;
			height: 90rpx;
			border-radius: 50%;
			background-color: #ffb92e;
		}

		.oilStation-top-iconBg-img {
			width: 90rpx;
			height: 90rpx !important;
			border-radius: 50%;
		}

		.oilStation-top-name {
			flex: 1;
			padding: 0 15rpx;
		}

		.oilStation-top-nameBox {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			justify-content: space-evenly;
		}

		.oilStation-top-nameBox-name {
			font-size: 36rpx;
			color: #333333;
		}

		.oilStation-top-nameBox-address {
			font-size: 22rpx;
			color: #999;
		}

		.oilStation-top-navigation {
			width: 120rpx;
			height: 100%;
			justify-content: center;
			align-items: center;
		}

		.oilStation-top-navigation-iconBg {
			width: 120rpx;
			height: 120rpx;
			background-color: #ffc02e;
			border-radius: 50%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}

		.iconfont {
			font-family: unibtn;
			font-weight: 400;
		}

		.icondingwei {
			font-size: 50rpx;
			color: #101010;
		}

		.oilStation-top-navigation-iconBg-span {
			font-size: 22rpx;
			color: #101010;
		}

		.oilStation-bottom-lineUpBox {
			display: flex;
			align-items: center;
		}

		.oilStation-bottom-lineUpBox-lineUp {
			width: 250rpx;
			height: 60rpx;
			background-color: #eef1f4;
			border-radius: 20rpx;
			display: flex;
			justify-content: space-around;
			align-items: center;
		}

		.oilStation-bottom-lineUpBox-headBox {
			justify-content: flex-start;
			align-items: center;
			flex-direction: row;
		}

		.oilStation-bottom-lineUpBox-headBox-medal {
			width: 38rpx !important;
			height: 38rpx !important;
			border-radius: 50%;
			margin-left: -10rpx;
		}

		.oilStation-bottom-lineUpBox-headBox-medal:first-child {
			margin-left: 0;
		}

		.oilStation-bottom-lineUpBox-lineUpNum {
			font-size: 20rpx;
			color: #555555;
			justify-content: space-between;
			align-items: center;
			flex-direction: row;
		}

		.oilStation-bottom-lineUpBox-lineUpNum-num {
			font-size: 22rpx;
			color: #ff6f21;
		}

		.oilStation-bottom-lineUpBox-lineUpNum-txt {
			font-size: 20rpx;
			color: #999;
		}

		.oilStation-bottom-distanceBox {
			justify-content: center;
			align-items: center;
		}

		.oilStation-bottom-distanceBox-distance {
			width: 155rpx;
			height: 58rpx;
			padding: 0 10rpx;
			border-width: 1rpx;
			border-style: solid;
			border-color: #dddddd;
			border-radius: 20rpx;
			font-size: 20rpx;
			color: #555555;
			display: flex;
			justify-content: space-around;
			align-items: center;
			flex-direction: row;
		}

		.oilStation-bottom-distanceBox-num {
			font-size: 22rpx;
			color: #3294ff;
		}

		.oilStation-bottom-distanceBox-txt {
			font-size: 20rpx;
			color: #999;
		}

		.oilStation-bottom {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 20rpx;
		}

		.oilStation-bottom-collectBox {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
		}

		.oilStation-bottom-collectBox-collect {
			width: 110rpx;
			height: 40rpx;
			line-height: 40rpx;
			text-align: center;
			font-size: 20rpx;
			color: #555555;
			background-color: #eef1f4;
			border-radius: 50rpx;
		}

		.oilStation-bottom-collectBox-score {
			font-size: 18rpx;
			color: #ffb92e;
			margin-top: 5rpx;
			justify-content: center;
			align-items: center;
			flex-direction: row;
		}

		.iconstar {
			font-size: 20rpx;
			color: #ffb92e;
			margin: 0 5rpx;
		}
		
		.todayOilPrice {
			width: 594rpx;
			height: 240rpx;
			border-bottom-width: 1px;
			border-bottom-style: solid;
			border-bottom-color: #eef1f4;
		}
		
		.oil-title {
			width: 594rpx;
			margin: 40rpx 0 20rpx 0;
		}
		
		.oil-title-txt {
			font-size: 30rpx;
			color: #333333;
		}
		
		.todayOil-content {
			width: 594rpx;
			height: 155rpx;
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			flex-direction: row;
			padding-left: 40rpx;
		}
		
		.todayOil-priceByOilNum {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			position: relative;
		}
		
		.todayOil-priceByOilNum-sheng {
			font-size: 22rpx;
			color: #ffc02e;
			font-weight: bold;
		}
		
		.todayOil-priceByOilNum-sheng:first-letter {
			font-size: 35rpx;
			/* vertical-align: auto; */
		}
		
		.shengActive {
			background-image: linear-gradient(to right, #ff644f, #ff7f3f);
			/* -webkit-background-clip: text;
			-webkit-text-fill-color: transparent; */
		}
		
		.todayOil-priceByOilNum-oilNum {
			font-size: 25rpx;
			color: #777777;
			margin: 5rpx;
		}
		
		.myCarLabel {
			width: 117rpx;
			height: 33rpx;
			border-radius: 50rpx;
			background-color: #ffc02e;
			position: absolute;
			left: -20rpx;
			bottom: -40rpx;
			font-size: 20rpx;
			color: #101010;
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 10;
		}
		
		.myCarLabel-txt {
			font-size: 20rpx;
			color: #101010;
		}


	}
</style>
<style lang='less' scoped>
	.map-container {
		position: relative;
		overflow: hidden;
	}
</style>