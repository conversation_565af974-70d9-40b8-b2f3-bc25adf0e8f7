<template>
<view>
	<block v-if="isload">
		<view class="container">
			<view class="header">
				<text class="title">奖励记录</text>
			</view>

			<!-- 筛选条件 -->
			<view class="filter-box">
				<view class="filter-item">
					<picker @change="onActivityChange" :value="selectedActivityIndex" :range="activityOptions" range-key="title">
						<view class="picker-text">
							{{selectedActivityIndex >= 0 ? activityOptions[selectedActivityIndex].title : '选择活动'}}
							<image src="/static/img/arrow-down.png" class="arrow-icon"></image>
						</view>
					</picker>
				</view>
				<view class="filter-item">
					<picker @change="onStatusChange" :value="selectedStatusIndex" :range="statusOptions" range-key="text">
						<view class="picker-text">
							{{selectedStatusIndex >= 0 ? statusOptions[selectedStatusIndex].text : '选择状态'}}
							<image src="/static/img/arrow-down.png" class="arrow-icon"></image>
						</view>
					</picker>
				</view>
			</view>

			<!-- 统计信息 -->
			<view class="stats-box" v-if="statsData">
				<view class="stats-title">统计信息</view>
				<view class="stats-content">
					<view class="stats-item">
						<view class="stats-label">总记录数</view>
						<view class="stats-value">{{statsData.total_records}}</view>
					</view>
					<view class="stats-item">
						<view class="stats-label">总奖励金额</view>
						<view class="stats-value reward-amount">¥{{statsData.total_reward}}</view>
					</view>
					<view class="stats-item">
						<view class="stats-label">已发放</view>
						<view class="stats-value success">¥{{statsData.issued_amount}}</view>
					</view>
					<view class="stats-item">
						<view class="stats-label">待发放</view>
						<view class="stats-value pending">¥{{statsData.pending_amount}}</view>
					</view>
				</view>
			</view>

			<!-- 记录列表 -->
			<view class="records-box">
				<view class="records-title">奖励记录</view>
				<view class="records-content">
					<block v-for="(item, index) in recordsList" :key="item.id">
						<view class="record-item">
							<view class="record-header">
								<view class="activity-title">{{item.activity_title}}</view>
								<view class="record-time">{{item.create_time}}</view>
							</view>
							<view class="record-info">
								<view class="performance-info">
									<view class="perf-text">目标业绩：{{item.achievement_target}}</view>
									<view class="actual-perf">实际业绩：{{item.team_performance}}</view>
								</view>
								<view class="reward-info">
									<view class="reward-amount">¥{{item.reward_amount}}</view>
									<view class="status-tag" :class="item.status == 1 ? 'status-active' : item.status == 2 ? 'status-claimed' : item.status == 3 ? 'status-paid' : 'status-inactive'">
										{{item.status_text}}
									</view>
								</view>
							</view>
							<view class="record-footer">
								<view class="type-info">
									<text class="type-tag">{{item.performance_type_text}}</text>
									<text class="type-tag">{{item.reward_type_text}}</text>
								</view>
								<view class="level-info">等级 {{item.achievement_level}}</view>
							</view>
						</view>
					</block>
				</view>
				
				<!-- 加载状态 -->
				<view class="load-status">
					<text v-if="nodata" class="no-data">暂无记录</text>
					<text v-else-if="nomore" class="no-more">没有更多记录了</text>
					<loading v-else-if="loading"></loading>
				</view>
			</view>
		</view>
	</block>

	<loading v-if="loading && recordsList.length === 0"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			recordsList: [],
			statsData: null,
			activityList: [],
			activityOptions: [],
			selectedActivityIndex: -1,
			selectedActivityId: 0,
			statusOptions: [
				{value: '', text: '全部状态'},
				{value: 0, text: '待发放'},
				{value: 1, text: '已发放'},
				{value: 2, text: '已拒绝'}
			],
			selectedStatusIndex: 0,
			selectedStatus: '',
			pagenum: 1,
			limit: 20,
			nodata: false,
			nomore: false
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.refreshData();
	},
	onReachBottom: function () {
		if (!this.nodata && !this.nomore && !this.loading) {
			this.pagenum = this.pagenum + 1;
			this.getRecordsList();
		}
	},
	methods: {
		getdata: function() {
			var that = this;
			that.loading = true;
			
			// 获取活动列表
			app.get('ApiTuandui/getActivityList', {}, function(res) {
				if (res.status == 1) {
					that.activityList = res.data || [];
					that.activityOptions = [{title: '全部活动', id: 0}].concat(that.activityList);
					that.selectedActivityIndex = 0;
				}
				// 获取统计数据和记录列表
				that.getStatsData();
				that.getRecordsList();
			});
		},
		
		refreshData: function() {
			this.pagenum = 1;
			this.recordsList = [];
			this.nodata = false;
			this.nomore = false;
			this.getStatsData();
			this.getRecordsList();
		},
		
		getStatsData: function() {
			var that = this;
			var params = {};
			if (that.selectedActivityId > 0) {
				params.activity_id = that.selectedActivityId;
			}
			
			app.get('ApiTuandui/getRewardStats', params, function(res) {
				if (res.status == 1) {
					that.statsData = res.data;
				}
			});
		},
		
		getRecordsList: function() {
			var that = this;
			var params = {
				page: that.pagenum,
				limit: that.limit
			};
			
			if (that.selectedActivityId > 0) {
				params.activity_id = that.selectedActivityId;
			}
			
			if (that.selectedStatus !== '') {
				params.status = that.selectedStatus;
			}
			
			that.loading = true;
			app.get('ApiTuandui/getRewardRecords', params, function(res) {
				that.loading = false;
				that.isload = true;
				
				if (res.status == 1) {
					var data = res.data || [];
					
					if (data.length == 0) {
						if (that.pagenum == 1) {
							that.nodata = true;
						} else {
							that.nomore = true;
						}
					} else {
						if (that.pagenum == 1) {
							that.recordsList = data;
						} else {
							that.recordsList = that.recordsList.concat(data);
						}
					}
					
					uni.setNavigationBarTitle({
						title: '奖励记录'
					});
				} else {
					app.alert(res.msg || '获取记录失败');
				}
				
				uni.stopPullDownRefresh();
			});
		},
		
		onActivityChange: function(e) {
			var that = this;
			var index = e.detail.value;
			that.selectedActivityIndex = index;
			that.selectedActivityId = that.activityOptions[index].id;
			that.refreshData();
		},
		
		onStatusChange: function(e) {
			var that = this;
			var index = e.detail.value;
			that.selectedStatusIndex = index;
			that.selectedStatus = that.statusOptions[index].value;
			that.refreshData();
		}
	}
};
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	background-color: #fff;
	padding: 20rpx;
	text-align: center;
	border-bottom: 1px solid #f0f0f0;
}

.header .title {
	font-size: 36rpx;
	color: #333;
	font-weight: bold;
}

/* 筛选条件样式 */
.filter-box {
	background-color: #fff;
	padding: 20rpx;
	display: flex;
	gap: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.filter-item {
	flex: 1;
}

.picker-text {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
}

.arrow-icon {
	width: 20rpx;
	height: 20rpx;
}

/* 统计信息样式 */
.stats-box {
	background-color: #fff;
	margin-top: 20rpx;
}

.stats-title {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
	padding: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.stats-content {
	padding: 20rpx;
	display: flex;
	justify-content: space-between;
}

.stats-item {
	text-align: center;
	flex: 1;
}

.stats-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.stats-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.stats-value.reward-amount {
	color: #FA5151;
}

.stats-value.success {
	color: #28a745;
}

.stats-value.pending {
	color: #ffc107;
}

/* 记录列表样式 */
.records-box {
	background-color: #fff;
	margin-top: 20rpx;
}

.records-title {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
	padding: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.record-item {
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.record-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.activity-title {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
}

.record-time {
	font-size: 24rpx;
	color: #999;
}

.record-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.performance-info {
	flex: 1;
}

.perf-text, .actual-perf {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.reward-info {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.reward-amount {
	font-size: 32rpx;
	color: #FA5151;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.status-tag {
	font-size: 24rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.status-pending {
	background-color: #fff3cd;
	color: #856404;
}

.status-success {
	background-color: #d4edda;
	color: #155724;
}

.status-rejected {
	background-color: #f8d7da;
	color: #721c24;
}

.status-unknown {
	background-color: #f0f0f0;
	color: #999;
}

.record-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.type-info {
	display: flex;
	gap: 10rpx;
}

.type-tag {
	font-size: 24rpx;
	color: #666;
	padding: 4rpx 8rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
}

.level-info {
	font-size: 24rpx;
	color: #666;
}

/* 加载状态样式 */
.load-status {
	padding: 40rpx;
	text-align: center;
}

.no-data, .no-more {
	font-size: 28rpx;
	color: #999;
}
</style> 