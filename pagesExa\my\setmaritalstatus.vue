<template>
<view class="container">
	<block v-if="isload">
		<view class="form">
			<picker @change="bindPickerChange" :value="statusIndex" :range="statusList">
				<view class="picker-item">
					<view class="label">情感状态</view>
					<view class="value">{{statusList[statusIndex]}}</view>
					<image class="arrow" src="/static/img/arrowright.png" />
				</view>
			</picker>
		</view>
		<button class="set-btn" @tap="saveStatus" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">保 存</button>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			marital_status: '',
			statusIndex: 0,
			statusList: ['请选择', '单身', '恋爱中', '已婚', '离异', '丧偶', '保密']
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
    getdata: function() {
      var that = this;
      that.loading = true;
      app.get('ApiMy/set', {}, function(data) {
        that.loading = false;
        that.marital_status = data.userinfo.marital_status || '';
        
        // 根据当前情感状态值设置picker的索引
        if (that.marital_status) {
          const index = that.statusList.findIndex(item => item === that.marital_status);
          that.statusIndex = index > 0 ? index : 0;
        }
        
        that.loaded();
      });
    },
    
    bindPickerChange: function(e) {
      this.statusIndex = e.detail.value;
    },
    
    saveStatus: function() {
      var that = this;
      var marital_status = that.statusList[that.statusIndex];
      
      if (that.statusIndex === 0) {
        app.alert('请选择情感状态');
        return;
      }
      
      app.showLoading('提交中');
      app.post("ApiMy/setfield", {marital_status: marital_status}, function(data) {
        app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function() {
            app.goback(true);
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    }
  }
};
</script>
<style>
.form { 
  width: 94%;
  margin: 20rpx 3%;
  border-radius: 5px;
  padding: 0 3%;
  background: #FFF;
}
.picker-item {
  display: flex;
  align-items: center;
  width: 100%;
  height: 98rpx;
  line-height: 98rpx;
}
.picker-item .label {
  color: #000;
  width: 200rpx;
}
.picker-item .value {
  flex: 1;
  color: #444444;
}
.picker-item .arrow {
  width: 26rpx;
  height: 26rpx;
  margin-left: 20rpx;
}
.set-btn {
  width: 90%;
  margin: 60rpx 5%;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 48rpx;
  color: #FFFFFF;
  font-weight: bold;
}
</style> 