<template>
	<view class="face-guide-container">
		<!-- 顶部标题区域 -->
		<view class="header-section">
			<image class="header-image" :src="pre_url+'/static/img/face-diagnosis-icon.png'" mode="aspectFit"></image>
			<text class="header-title">AI智能面诊</text>
			<text class="header-subtitle">通过面部特征分析，了解身体健康状况</text>
		</view>

		<!-- 功能介绍区域 -->
		<view class="intro-section">
			<view class="intro-card">
				<view class="card-header">
					<text class="card-icon">🔍</text>
					<text class="card-title">什么是面诊？</text>
				</view>
				<text class="card-content">
					面诊是中医诊断的重要方法之一，通过观察面部的色泽、光润度、表情等变化，来判断脏腑功能、气血状况和疾病的性质。
				</text>
			</view>

			<view class="intro-card">
				<view class="card-header">
					<text class="card-icon">🎯</text>
					<text class="card-title">AI面诊功能</text>
				</view>
				<text class="card-content">
					运用先进的人工智能技术，结合传统中医理论，对面部特征进行精准分析，为您提供个性化的健康建议和调理方案。
				</text>
			</view>
		</view>

		<!-- 使用指南区域 -->
		<view class="guide-section">
			<text class="section-title">拍摄指南</text>
			<view class="guide-steps">
				<view class="step-item" v-for="(step, index) in guideSteps" :key="index">
					<view class="step-number">{{ index + 1 }}</view>
					<view class="step-content">
						<text class="step-title">{{ step.title }}</text>
						<text class="step-desc">{{ step.desc }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 注意事项区域 -->
		<view class="notice-section">
			<text class="section-title">注意事项</text>
			<view class="notice-list">
				<view class="notice-item" v-for="(notice, index) in noticeList" :key="index">
					<text class="notice-icon">⚠️</text>
					<text class="notice-text">{{ notice }}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="action-section">
			<button class="start-btn" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @click="startDiagnosis">
				<text class="btn-text">开始面诊拍摄</text>
			</button>
			<text class="disclaimer">
				*本功能仅供健康参考，不能替代专业医生诊断
			</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'FaceGuide',
	data() {
		return {
			// 云资源前缀URL
			pre_url: '',

			// 拍摄指南步骤数据
			guideSteps: [
				{
					title: '保持良好光线',
					desc: '在充足的自然光或白光下进行拍摄，避免暗光环境'
				},
				{
					title: '正面拍摄',
					desc: '正面面对摄像头，保持表情自然，避免化妆或遮挡'
				},
				{
					title: '适当距离',
					desc: '保持30-50cm的拍摄距离，确保面部完整清晰'
				},
				{
					title: '稳定拍摄',
					desc: '保持手机稳定，将面部置于画面中央进行拍摄'
				}
			],
			// 注意事项列表
			noticeList: [
				'请在自然状态下进行拍摄，避免浓妆',
				'确保面部清洁，无遮挡物',
				'避免在强烈阳光直射下拍摄',
				'如有面部疾病请先咨询医生',
				'孕妇、儿童使用前请咨询专业医生'
			]
		}
	},
	onLoad() {
		console.log('2025-07-17 INFO-[face-guide] 面诊引导页加载完成');

		// 初始化云资源前缀URL
		const app = getApp();
		this.pre_url = app.globalData.pre_url || '';
	},
	methods: {
		// 开始诊断
		startDiagnosis() {
			console.log('2025-07-17 INFO-[face-guide] 开始面诊拍摄');
			uni.navigateTo({
				url: '/pagesB/diagnosis/face/camera-new',
				success: () => {
					console.log('2025-07-17 INFO-[face-guide] 跳转到面诊拍摄页面成功');
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-guide] 跳转到面诊拍摄页面失败:', error);
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style>
.face-guide-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 30rpx;
	color: #fff;
}

.header-section {
	text-align: center;
	margin-bottom: 60rpx;
	padding-top: 60rpx;
}

.header-image {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
}

.header-title {
	font-size: 48rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 20rpx;
}

.header-subtitle {
	font-size: 28rpx;
	opacity: 0.8;
	line-height: 1.5;
}

.intro-section {
	margin-bottom: 60rpx;
}

.intro-card {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	backdrop-filter: blur(10rpx);
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.card-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
}

.card-content {
	font-size: 28rpx;
	line-height: 1.6;
	opacity: 0.9;
}

.guide-section {
	margin-bottom: 60rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	margin-bottom: 40rpx;
	display: block;
}

.guide-steps {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 40rpx;
	backdrop-filter: blur(10rpx);
}

.step-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 40rpx;
}

.step-item:last-child {
	margin-bottom: 0;
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: 600;
	margin-right: 30rpx;
	flex-shrink: 0;
}

.step-content {
	flex: 1;
}

.step-title {
	font-size: 30rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}

.step-desc {
	font-size: 26rpx;
	opacity: 0.8;
	line-height: 1.5;
}

.notice-section {
	margin-bottom: 80rpx;
}

.notice-list {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 40rpx;
	backdrop-filter: blur(10rpx);
}

.notice-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.notice-item:last-child {
	margin-bottom: 0;
}

.notice-icon {
	font-size: 28rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.notice-text {
	font-size: 26rpx;
	opacity: 0.9;
	line-height: 1.5;
	flex: 1;
}

.action-section {
	text-align: center;
}

.start-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	border: none;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
}

.btn-text {
	color: #fff;
}

.disclaimer {
	font-size: 24rpx;
	opacity: 0.7;
	line-height: 1.5;
}
</style>
