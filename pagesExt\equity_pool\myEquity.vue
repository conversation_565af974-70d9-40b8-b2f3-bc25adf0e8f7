<template>
	<view class="container">
		<block v-if="isload">
			<view class="equity-value-card" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}">
				<view class="current-value">
					<view class="label">当前股权单价</view>
					<view class="value">{{equity_value}}</view>
				</view>
				<view class="my-equity">
					<view class="equity-item">
						<view class="equity-num">{{total_equity.equity_num}}</view>
						<view class="equity-label">持有股权数量</view>
					</view>
					<view class="equity-item">
						<view class="equity-num">{{total_equity.equity_value}}</view>
						<view class="equity-label">股权总价值</view>
					</view>
				</view>
			</view>
			
			<!-- 如果将来需要显示股权历史记录，可以在这里添加 -->
			<view class="no-record" v-if="!haveRecord">
				<image class="no-data-img" src="/static/img/nodata.png" mode="widthFix"></image>
				<view class="no-data-text">暂无股权记录</view>
				<view class="tips">持有股权可以分享平台收益，获得更多权益</view>
			</view>
			
			<view class="action-btns">
				<view class="action-btn" @tap="goto" data-url="/pagesExt/equity_pool/ranking">
					<text class="iconfont icon_paihang"></text>
					<text>股权排行榜</text>
				</view>
				<view class="action-btn" @tap="goto" data-url="/pagesExt/equity_pool/index">
					<text class="iconfont icon_guquan"></text>
					<text>股权池概览</text>
				</view>
			</view>
			
			<!-- 股权说明卡片 -->
			<view class="equity-desc">
				<view class="desc-title">股权说明</view>
				<view class="desc-content">
					<view class="desc-item">
						<view class="desc-dot"></view>
						<view class="desc-text">股权是平台发展过程中的权益凭证</view>
					</view>
					<view class="desc-item">
						<view class="desc-dot"></view>
						<view class="desc-text">股权价值与平台发展状况密切相关</view>
					</view>
					<view class="desc-item">
						<view class="desc-dot"></view>
						<view class="desc-text">持有股权可以分享平台收益红利</view>
					</view>
					<view class="desc-item">
						<view class="desc-dot"></view>
						<view class="desc-text">股权价值可能会根据平台发展情况波动</view>
					</view>
				</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			loading: false,
			isload: false,
			pre_url: app.globalData.pre_url,
			
			equity_value: "0.00", // 单位股权价值
			total_equity: {
				equity_num: 0,    // 用户持有的股权数量
				equity_value: 0   // 用户股权价值
			},
			haveRecord: false    // 是否有股权记录
		};
	},
	onLoad: function(opt) {
		this.getdata();
	},
	onPullDownRefresh: function() {
		this.getdata();
	},
	methods: {
		getdata: function() {
			var that = this;
			that.loading = true;
			
			app.get('ApiEquityPool/myEquity', {}, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 1) {
					that.equity_value = parseFloat(res.data.equity_value).toFixed(2);
					that.total_equity = res.data.total_equity;
					that.haveRecord = res.data.equity_list && res.data.equity_list.length > 0;
					that.isload = true;
				} else {
					that.$refs.popmsg.show({
						msg: res.msg || '获取数据失败，请重试',
						isok: false
					});
				}
			});
		},
		
		// 跳转到指定页面
		goto: function(e) {
			var url = e.currentTarget.dataset.url;
			uni.navigateTo({
				url: url
			});
		}
	}
};
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.equity-value-card {
	border-radius: 16rpx;
	padding: 30rpx;
	color: #fff;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
	position: relative;
	overflow: hidden;
}

.equity-value-card::after {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxjaXJjbGUgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIiBjeD0iODAiIGN5PSI4MCIgcj0iNDAiIC8+CiAgICA8Y2lyY2xlIGZpbGw9InJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSIgY3g9IjEwIiBjeT0iMTAiIHI9IjIwIiAvPgo8L3N2Zz4=') no-repeat;
	background-size: cover;
	opacity: 0.1;
}

.current-value {
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	padding-bottom: 20rpx;
	margin-bottom: 20rpx;
}

.label {
	font-size: 28rpx;
	opacity: 0.8;
	margin-bottom: 10rpx;
}

.value {
	font-size: 60rpx;
	font-weight: bold;
	transition: all 0.3s ease;
}

.equity-value-card:active .value {
	transform: scale(1.05);
}

.my-equity {
	display: flex;
	justify-content: space-between;
	padding: 10rpx 0;
}

.equity-item {
	text-align: center;
	flex: 1;
	transition: all 0.3s ease;
}

.equity-item:active {
	transform: translateY(-5rpx);
}

.equity-num {
	font-size: 40rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.equity-label {
	font-size: 24rpx;
	opacity: 0.8;
}

.no-record {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	text-align: center;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.no-data-img {
	width: 200rpx;
	margin-bottom: 20rpx;
	transition: all 0.5s ease;
}

.no-data-img:hover {
	transform: rotate(5deg);
}

.no-data-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.tips {
	font-size: 26rpx;
	color: #999;
	margin-top: 20rpx;
	line-height: 1.6;
}

.action-btns {
	display: flex;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

.action-btn {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx 0;
	text-align: center;
	width: 48%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #333;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
	transition: all 0.2s ease;
	position: relative;
	overflow: hidden;
}

.action-btn::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.05);
	opacity: 0;
	transition: opacity 0.2s ease;
}

.action-btn:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}

.action-btn:active::after {
	opacity: 1;
}

.action-btn .iconfont {
	font-size: 50rpx;
	margin-bottom: 10rpx;
	color: #f0505a;
}

.equity-desc {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.desc-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
	padding-bottom: 15rpx;
}

.desc-content {
	padding: 10rpx 0;
}

.desc-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 15rpx;
	animation: fadeIn 0.5s ease forwards;
	opacity: 0;
}

.desc-item:nth-child(1) { animation-delay: 0.1s; }
.desc-item:nth-child(2) { animation-delay: 0.2s; }
.desc-item:nth-child(3) { animation-delay: 0.3s; }
.desc-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeIn {
	from { opacity: 0; transform: translateY(10rpx); }
	to { opacity: 1; transform: translateY(0); }
}

.desc-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 6rpx;
	background-color: #f0505a;
	margin-top: 12rpx;
	margin-right: 15rpx;
	flex-shrink: 0;
}

.desc-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}
</style> 