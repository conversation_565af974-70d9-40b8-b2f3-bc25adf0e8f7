<template>
	<view class="container">
		<view class="package-list">
			<view class="package-item" v-for="(item, index) in packageList" :key="index" @tap="gotoDetail(item.id)">
				<image class="package-pic" :src="item.pic" mode="aspectFill"></image>
				<view class="package-info">
					<view class="package-name">{{ item.name }}</view>
					<view class="package-desc">
						<text>含 {{ item.service_count || 0 }} 项服务</text>
						<!-- 可以根据需要添加更多描述信息，如有效期等 -->
					</view>
					<view class="package-bottom">
						<view class="package-price" :style="{color:t('color1')}">￥<text class="price-value">{{ item.sell_price }}</text></view>
						<button class="buy-btn" :style="{background:t('color1')}">立即抢购</button>
					</view>
				</view>
			</view>
		</view>

		<view class="loading-tip" v-if="loading">加载中...</view>
		<view class="nodata-tip" v-if="nodata">没有更多套餐了~</view>
		<view class="empty-tip" v-if="!loading && packageList.length === 0">
			<image src="/static/img/nodata.png" class="empty-img" mode="widthFix"></image>
			<text>暂无服务套餐</text>
		</view>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			packageList: [], // 套餐列表
			page: 1,         // 当前页码
			limit: 10,       // 每页数量
			total: 0,        // 总条数
			loading: false,  // 加载状态
			nodata: false    // 是否没有更多数据
		}
	},
	onLoad() {
		// 获取 globalData
		// const app = getApp();
		// if(app.globalData.config && app.globalData.config.t) {
		//     // this.t = app.globalData.config.t; // 移除赋值
		// } else {
		//     console.warn('onLoad 时 globalData.config.t 未准备好');
		// }

		this.getList(); // 页面加载时获取第一页数据
		uni.setNavigationBarTitle({
			title: '服务套餐'
		});
	},
	onReachBottom() {
		// 页面滚动到底部时加载更多
		if (this.loading || this.nodata) {
			return; // 如果正在加载或没有更多数据，则不执行
		}
		this.page++;
		this.getList();
	},
	methods: {
		// 获取套餐列表
		getList() {
			var that = this;
			if (that.loading || that.nodata) {
				return;
			}
			that.loading = true;
			app.post('ApiYuyuePackage/getList', {
				page: that.page,
				limit: that.limit
				// bid: xxx // 如果需要按商家筛选，传入bid
			}, function(res) {
				that.loading = false;
				if (res.status == 1) {
					const data = (res.data || []).map(item => {
						// 清理错误的图片URL前缀
						if (item.pic && item.pic.startsWith('https://localhost')) {
							console.warn('发现错误的图片前缀，已清理:', item.pic);
							item.pic = item.pic.substring('https://localhost'.length);
						}
						return item;
					});
					if (that.page === 1) {
						that.packageList = data;
					} else {
						that.packageList = that.packageList.concat(data);
					}
					that.total = res.total || 0;
					// 判断是否没有更多数据
					if (data.length < that.limit || that.packageList.length >= that.total) {
						that.nodata = true;
					}
				} else {
					app.error(res.msg || '获取套餐列表失败');
					if (that.page > 1) {
						that.page--; // 加载失败，页码回退
					}
				}
			}, function() {
				that.loading = false;
				if (that.page > 1) {
					that.page--; // 请求失败，页码回退
				}
				app.error('请求失败');
			});
		},
		// 跳转到套餐详情页
		gotoDetail(id) {
			if (!id) return;
			app.goto('/yuyue/packagedetail?id=' + id);
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.package-list {
	/* 列表容器样式 */
}

.package-item {
	display: flex;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.package-pic {
	width: 180rpx;
	height: 180rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
	flex-shrink: 0; /* 防止图片被压缩 */
}

.package-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	overflow: hidden; /* 防止内容溢出 */
}

.package-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 10rpx;
}

.package-desc {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.package-bottom {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
	margin-top: 10rpx;
}

.package-price {
	font-size: 24rpx;
	font-weight: bold;
}

.package-price .price-value {
	font-size: 36rpx;
	margin-left: 4rpx;
}

.buy-btn {
	padding: 0 24rpx;
	height: 56rpx;
	line-height: 56rpx;
	font-size: 24rpx;
	color: #fff;
	border-radius: 28rpx;
	text-align: center;
	/* 移除默认按钮边框 */
	border: none;
	outline: none;
	margin: 0; /* 移除按钮默认外边距 */
}
/* H5平台需要额外处理按钮边框 */
button::after {
	border: none;
}

/* 加载和无数据提示 */
.loading-tip, .nodata-tip, .empty-tip {
	text-align: center;
	color: #999;
	font-size: 24rpx;
	padding: 20rpx 0;
}

.empty-tip {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding-top: 100rpx;
}

.empty-img {
	width: 200rpx;
	margin-bottom: 20rpx;
}
</style> 