<template>
<view class="container">
	<block v-if="isload">

		<map v-if="psorder.status!=4" class="map" :longitude="binfo.longitude" :latitude="binfo.latitude" scale="14" :markers="[{
			id:0,
			latitude:binfo.latitude,
			longitude:binfo.longitude,
			iconPath: '/static/peisong/marker_business.png',
			width:'44',
			height:'54'
		},{
			id:1,
			latitude:orderinfo.latitude,
			longitude:orderinfo.longitude,
			iconPath: '/static/peisong/marker_kehu.png',
			width:'44',
			height:'54'
		},{
			id:2,
			latitude:worker.latitude,
			longitude:worker.longitude,
			iconPath: '/static/peisong/marker_qishou.png',
			width:'44',
			height:'54'
		}]"></map>
		<map v-else class="map" :longitude="binfo.longitude" :latitude="binfo.latitude" scale="14" :markers="[{
			id:0,
			latitude:binfo.latitude,
			longitude:binfo.longitude,
			iconPath: '/static/peisong/marker_business.png',
			width:'44',
			height:'54'
		},{
			id:0,
			latitude:orderinfo.latitude,
			longitude:orderinfo.longitude,
			iconPath: '/static/peisong/marker_kehu.png',
			width:'44',
			height:'54'
		}]"></map>

		<view class="order-box">
			<view class="head">
				<view v-if="psorder.fwtype==1">
					<view class="f1" v-if="psorder.status==3"><image src="/static/peisong/ps_time.png" class="img"/>已完成</view>
					<view class="f1" v-if="psorder.status==1"><image src="/static/peisong/ps_time.png" class="img"/> {{orderinfo.yydate}}<text class="t1">预计上门时间</text> </view>
					<view class="f1" v-if="psorder.status==2"><image src="/static/peisong/ps_time.png" class="img"/><text class="t1" style="margin-left:10rpx">服务中</text></view>
				</view>
				<view v-else-if="psorder.fwtype==2">
						<view class="f1" v-if="psorder.status==3"><image src="/static/peisong/ps_time.png" class="img"/>已完成</view>
						<view class="f1" v-else-if="psorder.status==1"><image src="/static/peisong/ps_time.png" class="img"/>期望上门时间<text class="t1">{{orderinfo.yydate}}</text> </view>
						<view class="f1" v-else-if="psorder.status==2"  ><image src="/static/peisong/ps_time.png" class="img"/>已到达，服务中</view>
				</view>
				<view class="flex1"></view>
				<view class="f2"><text class="t1">{{psorder.ticheng}}</text>元</view>
			</view>
			<view class="content" style="border-bottom:0">
				<view class="f1">
					<view class="t1"><text class="x1">{{psorder.juli}}</text><text class="x2">{{psorder.juli_unit}}</text></view>
					<view class="t2"><image src="/static/peisong/ps_juli.png" class="img"/></view>
					<view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view>
				</view>
				<view class="f2">
					<view class="t1">{{binfo.name}}</view>
					<view class="t2">{{binfo.address}}</view>
					<view class="t3">{{orderinfo.address}}</view>
						<view class="t2">{{orderinfo.area}}</view>
				</view>
				<view class="f3" @tap.stop="daohang"><image :src="pre_url+'/static/img/peisong/ps_daohang.png'" class="img"/></view>
			</view>
		</view>

		<view class="orderinfo">
			<view class="box-title">商品清单({{orderinfo.procount}})</view>
			<view v-for="(item, idx) in prolist" :key="idx" class="item">
				<text class="t1 flex1">{{item.name}} {{item.ggname}}</text>
				<text class="t2 flex0">￥{{item.sell_price}} ×{{item.num}} </text>
			</view>
		</view>
		
		<view class="orderinfo" v-if="psorder.status!=0">
			<view class="box-title">服务信息</view>
			<view class="item">
				<text class="t1">用户姓名</text>
				<text class="t2">{{orderinfo.linkman}}</text>
			</view>
			<view class="item">
				<text class="t1">用户电话</text>
				<text class="t2">{{orderinfo.tel}}</text>
			</view>
			<view class="item">
				<text class="t1">预约时间</text>
				<text class="t2">{{orderinfo.yydate}}</text>
			</view>
			<view class="item">
				<text class="t1">接单时间</text>
				<text class="t2">{{dateFormat(psorder.starttime)}}</text>
			</view>
			<view class="item" v-if="psorder.daodiantime">
				<text class="t1">{{yuyue_sign?'出发时间':'到店时间'}}</text>
				<text class="t2">{{dateFormat(psorder.daodiantime)}}</text>
			</view>
			<view class="item" v-if="psorder.arrival_distance && psorder.status>=2">
				<text class="t1">到达距离</text>
				<text class="t2">{{formatDistance(psorder.arrival_distance)}}</text>
			</view>
			<view class="item" v-if="psorder.sign_time">
				<text class="t1">开始时间</text>
				<text class="t2">{{dateFormat(psorder.sign_time)}}</text>
			</view>
			<view class="item" v-if="psorder.endtime">
				<text class="t1">完成时间</text>
				<text class="t2">{{dateFormat(psorder.endtime)}}</text>
			</view>
		</view>
		
		<!-- 自定义表单数据展示区域 -->
		<view class="orderinfo" v-if="formdata && formdata.length > 0">
			<view class="box-title">自定义表单信息</view>
			<view v-for="(item, idx) in formdata" :key="idx" class="item">
				<text class="t1">{{item[0]}}</text>
				<text class="t2" :class="{'form-input': item[2] === 'input', 'form-textarea': item[2] === 'textarea', 'form-select': item[2] === 'select'}">{{item[1] || '未填写'}}</text>
			</view>
		</view>
		
		<!-- 到达照片展示区域 -->
		<view class="orderinfo" v-if="psorder.arrival_photo && psorder.status>=2">
			<view class="box-title">到达现场照片</view>
			<view class="photo-area">
				<image v-for="(item, index) in psorder.arrival_photo_array" :key="index" 
						:src="item" mode="widthFix" class="service-photo" @tap="previewImage" :data-urls="psorder.arrival_photo_array" :data-current="item"/>
			</view>
		</view>
		
		<!-- 完成照片展示区域 -->
		<view class="orderinfo" v-if="psorder.complete_photo && psorder.status>=3">
			<view class="box-title">服务完成照片</view>
			<view class="photo-area">
				<image v-for="(item, index) in psorder.complete_photo_array" :key="index" 
						:src="item" mode="widthFix" class="service-photo" @tap="previewImage" :data-urls="psorder.complete_photo_array" :data-current="item"/>
			</view>
		</view>

		<!-- 服务团队信息展示区域（多技师功能） -->
		<view class="orderinfo" v-if="team_workers && team_workers.is_multi_worker && team_workers.other_workers.length > 0">
			<view class="box-title">服务团队（共{{team_workers.total_count}}人）</view>
			
			<!-- 当前技师信息 -->
			<view class="team-member current" v-if="team_workers.current_worker">
				<view class="member-header">
					<image class="member-avatar" :src="team_workers.current_worker.headimg || '/static/img/default_avatar.png'"></image>
					<view class="member-info">
						<view class="member-name">{{team_workers.current_worker.realname}}（我）</view>
						<view class="member-role" :class="{'leader': team_workers.current_worker.worker_role == 1}">
							{{team_workers.current_worker.role_text}}
						</view>
					</view>
					<view class="member-status current-status">当前技师</view>
				</view>
			</view>
			
			<!-- 其他技师信息 -->
			<view class="team-member" v-for="(member, index) in team_workers.other_workers" :key="index">
				<view class="member-header">
					<image class="member-avatar" :src="member.headimg || '/static/img/default_avatar.png'"></image>
					<view class="member-info">
						<view class="member-name">{{member.realname}}</view>
						<view class="member-role" :class="{'leader': member.worker_role == 1}">
							{{member.role_text}}
						</view>
					</view>
					<view class="member-contact" @tap="call" :data-tel="member.tel">
						<text class="contact-icon">📞</text>
						<text class="contact-text">联系</text>
					</view>
				</view>
			</view>
			
			<view class="team-tip">
				<text class="tip-text">💡 您可以联系其他技师协调服务进度</text>
			</view>
		</view>

		<view class="orderinfo">
			<view class="box-title">订单信息</view>
			<view class="item">
				<text class="t1">订单编号</text>
				<text class="t2" user-select="true" selectable="true">{{orderinfo.ordernum}}</text>
			</view>
			<view class="item">
				<text class="t1">下单时间</text>
				<text class="t2">{{dateFormat(orderinfo.createtime)}}</text>
			</view>
			<view class="item">
				<text class="t1">支付时间</text>
				<text class="t2">{{dateFormat(orderinfo.paytime)}}</text>
			</view>
			<view class="item">
				<text class="t1">支付方式</text>
				<text class="t2">{{orderinfo.paytype}}</text>
			</view>
			<view class="item">
				<text class="t1">商品金额</text>
				<text class="t2 red">¥{{orderinfo.product_price}}</text>
			</view>
			<view class="item">
				<text class="t1">实付款</text>
				<text class="t2 red">¥{{orderinfo.totalprice}}</text>
			</view>
			<!-- <view class="item">
				<text class="t1">备注</text>
				<text class="t2 red">{{orderinfo.message ? orderinfo.message : '无'}}</text>
			</view> -->
			<view class="item" v-if="order.remark">
				<text class="t1">后台备注</text>
				<text class="t2" style="color: #FF6F30;">{{order.remark}}</text>
			</view>
		</view>
		<view style="width:100%;height:120rpx"></view>
		<view class="bottom" v-if="psorder.status!=4">
			<view class="f1" v-if="psorder.status!=0" @tap="call" :data-tel="orderinfo.tel"><image src="/static/peisong/tel1.png" class="img"/>联系顾客</view>
			<view class="f2" v-if="psorder.status!=0" @tap="call" :data-tel="binfo.tel"><image src="/static/peisong/tel2.png" class="img"/>联系商家</view>
			
			<view class="btn1" @tap="qiangdan" :data-id="psorder.id" v-if="psorder.status==0 && psorder.isqd==1">立即抢单</view>
			<view class="btn1" 	style="background: #BCBFC7;" v-if="psorder.status==0 && !psorder.isqd">{{psorder.djs}}后可抢单</view>
			
			
			<block v-if="psorder.fwtype==1">
				<view class="btn1" @tap="openPunchModal" :data-id="psorder.id" data-st="2" v-if="psorder.status==1">顾客已到店</view>
				<view class="btn1" @tap="openPunchModal" :data-id="psorder.id" data-st="3" v-if="psorder.status==2">我已完成</view>
			</block>
			<block v-if="psorder.fwtype==2">
				<view class="btn1" @tap="openPunchModal" :data-id="psorder.id" data-st="2" v-if="psorder.status==1">我已到达</view>
				<view class="btn1" @tap="openPunchModal" :data-id="psorder.id" data-st="3" v-if="psorder.status==2">服务已完成</view>
				<view class="btn1" @tap="goToOrderList" v-if="psorder.status==3">返回列表</view>
			</block>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
	
	<!-- 打卡弹窗 -->
	<view class="punch-modal" v-if="showPunchModal">
		<view class="punch-content">
			<view class="punch-header">
				<text class="punch-title">{{punchTitle}}</text>
				<view class="close-btn" @tap="closePunchModal">×</view>
			</view>
			
			<!-- 位置信息区域 -->
			<view class="location-section">
				<view class="section-title"><text class="icon">📍</text> 位置信息</view>
				<view class="location-content" v-if="punchLocationInfo">
					<view class="location-status success">
						<text class="status-text">已获取位置信息</text>
						<text class="location-detail">经度: {{punchLocationInfo.longitude}}</text>
						<text class="location-detail">纬度: {{punchLocationInfo.latitude}}</text>
					</view>
				</view>
				<view class="location-content" v-else>
					<view class="location-status" :class="{'loading': isLocating}">
						<text class="status-text">{{isLocating ? '获取位置中...' : '点击获取位置'}}</text>
					</view>
					<view class="get-location-btn" @tap="getPunchLocation" v-if="!isLocating && !punchLocationInfo">
						获取位置信息
					</view>
				</view>
			</view>
			
			<!-- 照片上传区域 -->
			<view class="photo-section">
				<view class="section-title"><text class="icon">📷</text> {{punchPhotoType}} ({{punchPhotos.length}}/9)</view>
				<view class="photo-content">
					<!-- 已选择的照片列表 -->
					<view class="photo-list" v-if="punchPhotos.length > 0">
						<view class="photo-item" v-for="(photo, index) in punchPhotos" :key="index">
							<image :src="photo" class="preview-image" mode="aspectFill" @tap="previewPunchPhoto" :data-url="photo"></image>
							<view class="remove-icon" @tap="removePunchPhoto" :data-index="index">×</view>
						</view>
					</view>
					<!-- 上传按钮（当照片数量小于9时显示） -->
					<view class="photo-placeholder" @tap="selectPunchPhoto" v-if="punchPhotos.length < 9">
						<text class="placeholder-icon">+</text>
						<text class="placeholder-text">点击上传照片</text>
					</view>
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="punch-actions">
				<view class="cancel-btn" @tap="closePunchModal">取消</view>
				<view class="submit-btn" @tap="submitPunchData" :class="{'disabled': !canSubmitPunch}">
					确认提交
				</view>
			</view>
		</view>
	</view>
</view>
</template>

<script>
var app = getApp();
var interval2 = null;
export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			
      orderinfo: {},
      prolist: [],
			worker:{},
      binfo: {},
      psorder: {},
			order: {},
			yuyue_sign:false,
			nowtime2:'',
			formdata: [], // 自定义表单数据
			
			// 打卡弹窗相关
			showPunchModal: false,
			punchId: null,
			punchSt: null,
			punchTitle: '',
			punchPhotoType: '',
			punchLocationInfo: null,
			punchPhotos: [], 
			isLocating: false,
			team_workers: null
    };
  },
  computed: {
    // 是否可以提交打卡
    canSubmitPunch: function() {
      return this.punchLocationInfo && this.punchPhotos.length > 0;
    }
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onUnload:function(){
		clearInterval(this.interval2);
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			app.get('ApiYuyueWorker/orderdetail', {id: that.opt.id}, function (res) {
				if(res.status==0){
					app.alert(res.msg)
					setTimeout(function () {
					  app.goto('dating');
					}, 1000);
					
				}
				that.orderinfo = res.orderinfo;
				that.prolist = res.prolist;
				that.binfo = res.binfo;
				that.psorder = res.psorder;
				that.worker = res.worker;
				that.order = res.order || {}; // 获取完整的订单信息
				that.yuyue_sign = res.yuyue_sign
				that.nowtime2 = res.nowtime
				that.formdata = res.formdata || []; // 获取自定义表单数据
				
				console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][getdata_001]获取到自定义表单数据:", JSON.stringify(that.formdata));
				
				// 处理照片数组
				if (that.psorder.arrival_photo && !that.psorder.arrival_photo_array) {
					that.psorder.arrival_photo_array = [that.psorder.arrival_photo];
				}
				if (that.psorder.complete_photo && !that.psorder.complete_photo_array) {
					that.psorder.complete_photo_array = [that.psorder.complete_photo];
				}
				
				// 确保距离值是数字类型
				if (that.psorder.arrival_distance) {
					that.psorder.arrival_distance = parseFloat(that.psorder.arrival_distance);
				}
				
				if(res.isdelayed){
					clearInterval(interval2);
					interval2 = setInterval(function () {
						that.nowtime2 = that.nowtime2 + 1;
						that.getdjs();
					}, 1000);
				}
				that.team_workers = res.team_workers;
				that.loaded();
			});
		},
		getdjs: function () {
				var that = this;
				var nowtime = that.nowtime2;
				var psorder = that.psorder
				var totalsec = psorder.createtime * 1 + psorder.delayedtime * 60 - nowtime * 1;
				if (totalsec <= 0) {
					that.psorder.isqd = true;
				} else {
					var houer = Math.floor(totalsec / 3600);
					var min = Math.floor((totalsec - houer * 3600) / 60);
					var sec = totalsec - houer * 3600 - min * 60;
					var djs =  (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';
				}
				that.psorder.djs = djs;
			},
    qiangdan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      app.confirm('确定要接单吗?', function () {
				app.showLoading('提交中');
        app.post('ApiYuyueWorker/qiangdan', {id: id}, function (data) {
					app.showLoading(false);
					if(data.status==1){
						app.success(data.msg);
						setTimeout(function () {
              // 如果返回了预约时间信息，可以在这里更新显示
              if (data.appointment_time) {
                // 使用更明确的弹窗显示预约时间
                uni.showModal({
                  title: '接单成功',
                  content: '客户预约上门时间：' + data.appointment_time + '\n请按时到达客户位置',
                  showCancel: false,
                  confirmText: '我知道了'
                });
              }
						  that.getdata();
						}, 1000);
					}else{
					  app.error(data.msg);
						setTimeout(function () {
							app.goto('dating');
						}, 1000);
						
					}

        });
      });
    },
    setst: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var st = e.currentTarget.dataset.st;
      
      // 通过打开弹窗代替直接处理
      that.openPunchModal(e);
    },
    
    // 打开打卡弹窗
    openPunchModal: function(e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var st = e.currentTarget.dataset.st;
      
      // 设置打卡类型和标题
      var title = '';
      var photoType = '';
      
      if(st == 2){
        title = '到达打卡';
        photoType = '到达现场照片';
      } else if(st == 3) {
        title = '完成打卡';
        photoType = '服务完成照片';
      } else if(st == 4) {
        title = '结束订单';
        photoType = '订单结束确认照片';
      }
      
      // 重置打卡状态
      that.punchId = id;
      that.punchSt = st;
      that.punchTitle = title;
      that.punchPhotoType = photoType;
      that.punchLocationInfo = null;
      that.punchPhotos = []; 
      that.showPunchModal = true;
      
      // 自动开始获取位置
      that.getPunchLocation();
    },
    
    // 关闭打卡弹窗
    closePunchModal: function() {
      this.showPunchModal = false;
    },
    
    // 获取打卡位置
    getPunchLocation: function() {
      var that = this;
      
      that.isLocating = true;
      
      app.getLocation(function(locRes) {
        console.log("2023-09-26 16:39:25-INFO-[jdorderdetail][getPunchLocation_001]位置获取成功:", JSON.stringify(locRes));
        
        that.isLocating = false;
        that.punchLocationInfo = {
          longitude: locRes.longitude,
          latitude: locRes.latitude
        };
        
        // 显示位置获取成功提示
        uni.showToast({
          title: '位置获取成功',
          icon: 'success',
          duration: 1500
        });
      }, function(err) {
        console.log("2023-09-26 16:39:25-ERROR-[jdorderdetail][getPunchLocation_002]位置获取失败:", JSON.stringify(err));
        
        that.isLocating = false;
        
        // 提示重试
        uni.showModal({
          title: '位置获取失败',
          content: '请检查是否授予定位权限，并重试',
          confirmText: '重试',
          cancelText: '取消',
          success: function(res) {
            if(res.confirm) {
              that.getPunchLocation();
            }
          }
        });
      });
    },
    
    // 选择打卡照片
    selectPunchPhoto: function() {
      var that = this;
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][selectPunchPhoto_001]开始选择照片");
      
      // 使用系统标准的chooseImage方法
      app.chooseImage(function(imageUrls) {
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][selectPunchPhoto_002]照片上传成功:", imageUrls);
        
        // 将上传成功的图片URL添加到照片列表
        that.punchPhotos = that.punchPhotos.concat(imageUrls);
        
        // 提示上传成功
        uni.showToast({
          title: '照片上传成功',
          icon: 'success',
          duration: 1500
        });
      }, 9 - that.punchPhotos.length);
    },
    
    // 移除指定照片
    removePunchPhoto: function(e) {
        var index = e.currentTarget.dataset.index;
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][removePunchPhoto_001]移除照片，索引:", index);
        
        if (index >= 0 && index < this.punchPhotos.length) {
          this.punchPhotos.splice(index, 1);
          console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][removePunchPhoto_002]照片移除成功，剩余数量:", this.punchPhotos.length);
        }
    },
    
    // 预览选择的照片
    previewPunchPhoto: function(e) {
      var currentUrl = e.currentTarget.dataset.url;
      if(this.punchPhotos && this.punchPhotos.length > 0) {
        uni.previewImage({
          urls: this.punchPhotos,
          current: currentUrl
        });
      }
    },
    
    // 提交打卡数据
    submitPunchData: function() {
      var that = this;
      
      if(!that.canSubmitPunch) {
        uni.showToast({
          title: '请先获取位置并上传照片',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][submitPunchData_001]准备提交打卡数据");
      
      // 准备参数
      var params = {
        id: that.punchId,
        st: that.punchSt,
        longitude: that.punchLocationInfo.longitude,
        latitude: that.punchLocationInfo.latitude
      };
      
      // 设置照片参数
      if(that.punchSt == 2) {
        params.arrival_photo = that.punchPhotos.join(',');
      } else {
        params.complete_photo = that.punchPhotos.join(',');
      }
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][submitPunchData_002]提交参数:", JSON.stringify(params));
      
      // 显示提交中
      uni.showLoading({
        title: '提交中...',
        mask: true
      });
      
      // 提交数据
      app.post('ApiYuyueWorker/setst', params, function(data) {
        uni.hideLoading();
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][submitPunchData_003]提交响应:", JSON.stringify(data));
        
        if(data.status === 1) {
          // 成功，关闭弹窗并刷新数据
          that.closePunchModal();
          
          // 提示成功
          app.success(data.msg);
          
          // 显示详细信息
          var title = '';
          var content = '';
          
          if(that.punchSt == 2) {
            title = '到达打卡成功';
            content = data.msg || '您已成功打卡，请开始服务';
          } else if(that.punchSt == 3) {
            title = '完成打卡成功';
            content = data.msg || '服务已完成，感谢您的工作';
          } else if(that.punchSt == 4) {
            title = '订单已结束';
            content = data.msg || '订单已完成结算，感谢您的工作';
          }
          
          // 如果有距离信息，显示距离
          if(data.distance && that.punchSt == 2) {
            content = '您距离客户位置: ' + that.formatDistance(data.distance) + '\n' + content;
          }
          
          // 显示成功弹窗
          uni.showModal({
            title: title,
            content: content,
            showCancel: false,
            success: function() {
              // 刷新数据
              setTimeout(function() {
                that.getdata();
              }, 500);
            }
          });
        } else {
          // 失败提示
          app.alert(data.msg || '提交失败');
        }
      });
    },
		daohang:function(e){
			var that = this;
			var datainfo = that.psorder;
			var binfo = that.binfo
			var orderinfo = that.orderinfo
			uni.showActionSheet({
        itemList: ['导航到商家', '导航到用户'],
        success: function (res) {
					if(res.tapIndex >= 0){
						if (res.tapIndex == 0) {
							var longitude = datainfo.longitude
							var latitude = datainfo.latitude
							var name = binfo.name
							var address = binfo.address
						}else{
							var longitude = datainfo.longitude2
							var latitude = datainfo.latitude2
							var name = orderinfo.address
							var address = orderinfo.address
						}
						console.log(longitude);
						console.log(latitude);
						console.log(address);
						uni.openLocation({
						 latitude:parseFloat(latitude),
						 longitude:parseFloat(longitude),
						 name:name,
						 address:address,
						 scale: 13
						})
					}
				}
			});
		},
		call:function(e){
			var tel = e.currentTarget.dataset.tel;
			uni.makePhoneCall({
				phoneNumber: tel
			});
		},
		previewImage: function (e) {
			var that = this;
			var urls = e.currentTarget.dataset.urls;
			var current = e.currentTarget.dataset.current;
			uni.previewImage({
				urls: urls,
				current: current
			});
		},
		formatDistance: function (distance) {
			if (!distance && distance !== 0) {
				return '未知';
			}
			
			distance = parseFloat(distance);
			if (isNaN(distance)) {
				return '未知';
			}
			
			if (distance >= 1000) {
				return (distance / 1000).toFixed(2) + '公里';
			} else {
				return parseInt(distance) + '米';
			}
		},
		directEndOrder: function (e) {
			var that = this;
			var id = e.currentTarget.dataset.id;
			app.confirm('确定要结束订单吗?', function () {
				app.showLoading('提交中');
				
				// 获取当前位置作为可选参数，如果获取失败也继续提交
				app.getLocation(function(locRes) {
					// 成功获取位置后提交
					that.submitEndOrder(id, locRes.longitude, locRes.latitude);
				}, function(err) {
					// 位置获取失败，仍然提交但不带位置信息
					console.log("2023-09-26 16:39:25-INFO-[jdorderdetail][directEndOrder_001]位置获取失败，继续提交", JSON.stringify(err));
					that.submitEndOrder(id);
				});
			});
		},
		// 提交结束订单
		submitEndOrder: function(id, longitude, latitude) {
			var that = this;
			// 准备参数
			var params = {
				id: id,
				st: 4
			};
			
			// 如果有位置信息，添加到参数中
			if (longitude && latitude) {
				params.longitude = longitude;
				params.latitude = latitude;
			}
			
			console.log("2023-09-26 16:39:25-INFO-[jdorderdetail][submitEndOrder_001]提交参数:", JSON.stringify(params));
			
			// 提交数据
			app.post('ApiYuyueWorker/setst', params, function(data) {
				app.showLoading(false);
				console.log("2023-09-26 16:39:25-INFO-[jdorderdetail][submitEndOrder_002]提交响应:", JSON.stringify(data));
				
				if(data.status===1){
					// 提示成功
					app.success(data.msg || '订单已完成结算');
					
					// 显示详细信息
					uni.showModal({
						title: '订单已结束',
						content: data.msg || '订单已完成结算，感谢您的工作',
						showCancel: false,
						success: function() {
							// 刷新数据
							setTimeout(function() {
								that.getdata();
							}, 500);
						}
					});
				}else{
					app.error(data.msg || '提交失败');
				}
			});
		},
		// 返回订单列表
		goToOrderList: function() {
			console.log("2025-01-03 22:55:53,565-INFO-[jdorderdetail][goToOrderList_001]用户点击返回列表");
			app.goto('/yuyue/jdorderlist');
		}
  }
}
</script>
<style>

.map{width:100%;height:500rpx;overflow:hidden}
.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}
.ordertop .f1{color:#fff}
.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}
.ordertop .f1 .t2{font-size:24rpx}

.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}
.order-box .head .f1{display:flex;align-items:center;color:#222222}
.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}
.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}
.order-box .head .f2{color:#FF6F30}
.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}

.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}
.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}
.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}

.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}
.order-box .content .f2{}
.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t2{font-size:24rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f3 .img{width:72rpx;height:168rpx}

.orderinfo{width: 94%;margin:20rpx 3%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;border-radius:8px}
.orderinfo .box-title{color:#161616;font-size:30rpx;height:80rpx;line-height:80rpx;font-weight:bold}
.orderinfo .item{display:flex;width:100%;padding:10rpx 0;}
.orderinfo .item .t1{width:200rpx;color:#161616}
.orderinfo .item .t2{flex:1;text-align:right;color:#222222}
.orderinfo .item .red{color:red}

/* 自定义表单数据样式 */
.orderinfo .item .form-input {
  color: #333;
  font-weight: normal;
}

.orderinfo .item .form-textarea {
  color: #333;
  font-weight: normal;
  word-break: break-all;
  white-space: pre-wrap;
}

.orderinfo .item .form-select {
  color: #06A051;
  font-weight: bold;
}

.bottom{ width: 100%;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}
.bottom .f1{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55;border-right:1px solid #EAEEED}
.bottom .f1 .img{width:44rpx;height:44rpx}
.bottom .f2{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55}
.bottom .f2 .img{width:44rpx;height:44rpx}
.bottom .btn1{flex:1;background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:100rpx;line-height:100rpx;color:#fff;text-align:center;font-size:32rpx}

/* 照片展示区域样式 */
.photo-area {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 0;
}
.service-photo {
  width: 180rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}

/* 打卡弹窗样式 */
.punch-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.punch-content {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 20rpx;
  width: 85%;
  max-width: 650rpx;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);
}

.punch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.punch-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 48rpx;
  height: 48rpx;
  line-height: 40rpx;
  width: 48rpx;
  text-align: center;
  color: #999;
  cursor: pointer;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  color: #333;
}

.section-title .icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.location-section, .photo-section {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
}

.location-content {
  display: flex;
  flex-direction: column;
}

.location-status {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.location-status.success {
  color: #06A051;
  border-left: 8rpx solid #06A051;
}

.location-status.loading {
  color: #FF6F30;
  border-left: 8rpx solid #FF6F30;
}

.status-text {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.location-detail {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.get-location-btn {
  background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);
  color: #fff;
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 8rpx rgba(3, 178, 105, 0.2);
}

.photo-content {
  display: flex;
  flex-wrap: wrap;
}

.photo-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 20rpx;
}

.photo-item {
    position: relative;
    width: 31%;
    padding-bottom: 31%;
    margin-right: 2%;
    margin-bottom: 10rpx;
}

.photo-item:nth-child(3n) {
    margin-right: 0;
}

.preview-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.remove-icon {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    width: 36rpx;
    height: 36rpx;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 32rpx;
    font-size: 28rpx;
    font-weight: bold;
    z-index: 10;
}

.photo-actions {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 10rpx;
}

.remove-btn {
  background-color: #FF6F30;
  color: #fff;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 111, 48, 0.2);
}

.photo-placeholder {
  width: 31%;
  padding-bottom: 31%;
  position: relative;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
}

.photo-placeholder > * {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.placeholder-icon {
  font-size: 60rpx;
  color: #ccc;
  line-height: 1;
  margin-bottom: 10rpx;
  transform: translate(-50%, -70%);
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  transform: translate(-50%, 30%);
}

.punch-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.cancel-btn, .submit-btn {
  flex: 1;
  padding: 20rpx 0;
  border-radius: 50rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: bold;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
  margin-right: 20rpx;
}

.submit-btn {
  background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);
  color: #fff;
}

.submit-btn.disabled {
  background: linear-gradient(-90deg, #ccc 0%, #999 100%);
  color: #fff;
  opacity: 0.8;
}

/* 动画 */
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading .status-text:before {
  content: "";
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #FF6F30;
  border-top-color: transparent;
  border-radius: 50%;
  margin-right: 10rpx;
  animation: rotating 1s linear infinite;
}

/* 服务团队样式 */
.team-member {
	margin-bottom: 15rpx;
	border-radius: 8rpx;
	overflow: hidden;
}

.team-member.current {
	background: #f0f8ff;
	border: 1px solid #e1f3ff;
}

.member-header {
	display: flex;
	align-items: center;
	padding: 15rpx;
}

.member-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 15rpx;
}

.member-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.member-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.member-role {
	font-size: 24rpx;
	color: #999;
	padding: 4rpx 10rpx;
	background: #f5f5f5;
	border-radius: 10rpx;
	display: inline-block;
	width: fit-content;
}

.member-role.leader {
	background: #e8f4ff;
	color: #007aff;
}

.current-status {
	font-size: 24rpx;
	color: #007aff;
	padding: 8rpx 15rpx;
	background: #e8f4ff;
	border-radius: 15rpx;
}

.member-contact {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 10rpx 15rpx;
	background: #4CAF50;
	border-radius: 20rpx;
	color: white;
	font-size: 24rpx;
}

.contact-icon {
	font-size: 28rpx;
	margin-bottom: 2rpx;
}

.contact-text {
	font-size: 22rpx;
}

.team-tip {
	margin-top: 15rpx;
	padding: 15rpx;
	background: #fff8e1;
	border-radius: 8rpx;
	border-left: 4rpx solid #ffc107;
}

.tip-text {
	font-size: 24rpx;
	color: #ff8f00;
	line-height: 1.5;
}
</style>