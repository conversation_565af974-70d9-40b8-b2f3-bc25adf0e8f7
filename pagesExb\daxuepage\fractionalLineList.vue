<template>
	<view class="container">
		<block>
			<view class="search-container">
				<view class="topsearch flex-y-center">
					<view class="f1 flex-y-center">
						<image class="img" src="/static/img/search_ico.png"></image>
						<input :value="keyword" placeholder="搜索你感兴趣的大学"
							placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search"
							@confirm="search"></input>
					</view>
				</view>
				<view class="search-navbar">
					<!-- <HM-filterDropdown :menuTop="88" :filterData="filterData" :defaultSelected="defaultSelected"
						:updateMenuName="true" @confirm="confirm" dataFormat="Object">
						<template slot="body" style="width: 100%">
							<view class="search-navbar-item flex-x-center flex-y-center"
								@click.stop="showDrawer('showRight')">
								筛选
								<text :class="'iconfont iconshaixuan ' + (showfilter?'active':'')"></text>
							</view>
						</template>

					</HM-filterDropdown> -->

				</view>
			</view>
<!-- 添加描述文字 -->
			
			<view class="ind_business">
				<view class="ind_buslist" id="datalist">
					<block v-for="(item, index) in datalist" :key="index">
						<view @tap="goto" :data-url="'/pagesExa/daxuepage/index?id=' + item.id">
							<view class="ind_busbox flex1 flex-row">
								<view class="ind_buspic flex0">
									<image :src="item.logo"></image>
								</view>
								<view class="flex1">
									<view class="bus_title">{{item.name}}</view>
									<!-- 标签显示 -->
									<view class="tags-container">
										<!-- 大学类型标签 -->
										<block v-for="(type, index) in item.type_names" :key="'type_' + index">
											<view class="tag-item">{{type}}</view>
										</block>
										<!-- 学校性质标签 -->
										<block v-for="(nature, index) in item.school_nature" :key="'nature_' + index">
											<view class="tag-item">{{nature}}</view>
										</block>
										<!-- 招生类型标签 -->
										<block v-for="(enrollType, index) in item.enrollment_type"
											:key="'enroll_' + index">
											<view class="tag-item">{{enrollType}}</view>
										</block>
										<!-- 大学标签 -->
										<block v-for="(tag, index) in item.biaoqian_names" :key="'tag_' + index">
											<view class="tag-item">{{tag}}</view>
										</block>
									</view>
									<view class="bus_sales">收藏：{{item.sales}}</view>
									<view class="bus_address" v-if="item.address" @tap.stop="openLocation"
										:data-latitude="item.latitude" :data-longitude="item.longitude"
										:data-company="item.name" :data-address="item.address">
										<image src="/static/img/b_addr.png"
											style="width:26rpx;height:26rpx;margin-right:10rpx" />
										<text class="x1">{{item.address}}</text><text class="x2">{{item.juli}}</text>
									</view>
								</view>
							</view>
						</view>
					</block>
					<view class="result-description">
						<text>结果仅供参考</text>
					</view>
					<nomore v-if="nomore"></nomore>
					<nodata v-if="nodata"></nodata>
				</view>
			</view>
			<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange"
				:menuindex="menuindex"></buydialog>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	import HMFilterDropdown from './components/HM-filterDropdown/HM-filterDropdown.vue'
	import data from './common/data.js'; //筛选菜单数据
	var app = getApp();

	export default {
		components: {
			HMFilterDropdown
		},
		data() {
			return {
				opt: {},
				loading: false,
				menuindex: -1,

				pre_url: app.globalData.pre_url,
				field: 'juli',
				order: 'asc',
				oldcid: "",
				catchecid: "",
				longitude: '',
				latitude: '',
				clist: [],
				datalist: [],
				originalDatalist: [], // 原始数据
				pagenum: 1,
				keyword: '',
				type: '',
				score:'',
				subject_choice:'',
				school_type:'',
				nomore: false,
				nodata: false,
				types: "",
				showfilter: "",
				showtype: 0,
				buydialogShow: false,
				proid: 0,
				defaultSelected: [],
				type_id: '', // 学校类型id
				school_nature: '', // 学校性质
				enrollment_type: '', // 招生类型
				city: '' // 选择的城市
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			console.log('this.opt',this.opt)
			// this.oldcid = this.opt.cid;
			// this.catchecid = this.opt.cid;
			this.type = this.opt.type;
			this.score = this.opt.score;
			this.subject_choice = this.opt.subject_choice;
			this.school_type = this.opt.school_type;
			if (this.opt.keyword) {
				this.keyword = this.opt.keyword;
			}
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onReachBottom: function() {
			if (!this.nodata && !this.nomore) {
				this.pagenum = this.pagenum + 1;
				this.getDataList(true);
			}
		},
		methods: {
			getdata: function() {
				var that = this;
				// that.loading = true;
				// app.get('ApiDaxue/blist', {}, function(res) {
				// 	that.loading = false;
				// 	that.clist = res.clist;
				// 	that.filterData[1].submenu[3].submenu = res.leixinglist.map(item => {
				// 		return {
				// 			name: item.name,
				// 			value: item.id
				// 		}
				// 	})
				// 	that.showtype = res.showtype || 0;
				// 	that.loaded();
				// });
				app.getLocation(function(res) {
					console.log('res',res)
						var latitude = res.latitude;
						var longitude = res.longitude;
						that.longitude = longitude;
						that.latitude = latitude;
						that.getDataList();
					},
					function() {
						that.getDataList();
					});
			},
			getDataList: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var latitude = that.latitude;
				var longitude = that.longitude;
				var keyword = that.keyword;
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
				app.post('ApiDaxue/fenshuxianchaxun', {
					pagenum: pagenum,
					type: that.type,
					score:that.score,
					subject_choice:that.subject_choice,
					school_type:that.school_type,
					keyword: keyword
				}, function(res) {
					console.log('res',res)
					
					that.loading = false;
					uni.stopPullDownRefresh();
					var data = res.data;
					if (pagenum == 1) {
						that.datalist = data;
						that.originalDatalist = JSON.parse(JSON.stringify(data));
						if (data.length == 0) {
							that.nodata = true;
						}
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}
					// 在这里根据 sort 字段进行排序，降序排列
										that.datalist.sort((a, b) => b.sort - a.sort);

				});
			},
			// 打开窗口
			showDrawer(e) {
				console.log(e)
				this.$refs[e].open()
			},
			// 关闭窗口
			closeDrawer(e) {
				this.$refs[e].close()
			},
			// 抽屉状态发生变化触发
			change(e, type) {
				console.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));
				this[type] = e
			},
			cateClick: function(e) {
				var that = this;
				var cid = e.currentTarget.dataset.cid;
				that.catchecid = cid
			},
			filterConfirm() {
				this.cid = this.catchecid;
				this.gid = this.catchegid;
				this.getDataList();
				this.$refs['showRight'].close()
			},
			filterReset() {
				this.catchecid = this.oldcid;
				this.catchegid = '';
			},
			filterClick: function() {
				this.showfilter = !this.showfilter
			},
			changetab: function(e) {
				var that = this;
				var cid = e.currentTarget.dataset.cid;
				that.cid = cid
				that.pagenum = 1;
				that.datalist = [];
				that.getDataList();
			},
			search: function(e) {
				var that = this;
				var keyword = e.detail.value;
				that.keyword = keyword;
				that.pagenum = 1;
				that.datalist = [];
				that.getDataList();
			},
			sortClick: function(e) {
				var that = this;
				var t = e.currentTarget.dataset;
				that.field = t.field;
				that.order = t.order;
				that.getDataList();
			},
			filterClick: function(e) {
				var that = this;
				var types = e.currentTarget.dataset.types;
				that.types = types;
			},
			openLocation: function(e) {
				//console.log(e)
				var latitude = parseFloat(e.currentTarget.dataset.latitude)
				var longitude = parseFloat(e.currentTarget.dataset.longitude)
				var address = e.currentTarget.dataset.address
				uni.openLocation({
					latitude: latitude,
					longitude: longitude,
					name: address,
					scale: 13
				})
			},
			phone: function(e) {
				var phone = e.currentTarget.dataset.phone;
				uni.makePhoneCall({
					phoneNumber: phone,
					fail: function() {}
				});
			},
			buydialogChange: function(e) {
				if (!this.buydialogShow) {
					this.proid = e.currentTarget.dataset.proid
				}
				this.buydialogShow = !this.buydialogShow;
				console.log(this.buydialogShow);
			},
			confirm: function(e) {
				console.log('e', e)
				if (e.value[2][0].length !== 0) { // 综合排序
					if (e.value[2][0][0] == '离我最近') {
						this.field = 'comment_score';
						this.order = 'desc';
						this.getDataList();
					}
				}

				if (e.value[1][0].length !== 0) { // 获取学校性质
					this.school_nature = e.value[1][0][0]
					this.getDataList();
				}

				if (e.value[1][2].length !== 0) { // 获取招生类型
					this.enrollment_type = e.value[1][2][0]
					this.getDataList();
				}

				if (e.value[1][3].length !== 0) { // 获取学校类型
					this.type_id = e.value[1][3][0]
					this.getDataList();
				}

				if (e.value[0].length !== 0) { // 城市筛选
					this.city = e.value[0][1]
					this.getDataList();
				}

				// 判断筛选是否都是空数据,如果为空则清空数据重新请求
				if (this.isAllEmpty(e.value)) {
					this.field = '';
					this.order = '';
					this.school_nature = ''
					this.type_id = ''
					this.enrollment_type = ''
					this.getDataList();
				}
			},
			// 递归查询数组是否都是空的
			isAllEmpty: function(array) {
				// 遍历数组的每个元素
				for (let i = 0; i < array.length; i++) {
					const element = array[i];
					// 如果元素是数组，递归检查
					if (Array.isArray(element)) {
						if (!this.isAllEmpty(element)) {
							return false; // 如果找到非空的数组，返回false
						}
					} else if (element !== null && element !== undefined && element !== '全部城市') {
						// 如果元素不是null或undefined，说明有非空元素，返回false
						return false;
					}
				}
				return true; // 所有元素都为空，返回true
			}
		}
	};
</script>
<style>
	.search-container {
		position: fixed;
		width: 100%;
		background: #fff;
		z-index: 9;
		top: var(--window-top)
	}

	.topsearch {
		width: 100%;
		padding: 16rpx 20rpx;
	}

	.topsearch .f1 {
		height: 60rpx;
		border-radius: 30rpx;
		border: 0;
		background-color: #f7f7f7;
		flex: 1
	}

	.topsearch .f1 .img {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10px
	}

	.topsearch .f1 input {
		height: 100%;
		flex: 1;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
	}

	.topsearch .search-btn {
		display: flex;
		align-items: center;
		color: #5a5a5a;
		font-size: 30rpx;
		width: 60rpx;
		text-align: center;
		margin-left: 20rpx
	}

	.search-navbar {
		display: flex;
		text-align: center;
		align-items: center;
		padding: 5rpx 0
	}

	.search-navbar-item {
		width: 100%;
		font-size: 13px;
		color: #757575;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		transition: color .2s linear;


		/* flex: 1;
		height: 70rpx;
		line-height: 70rpx;
		position: relative;
		font-size: 28rpx;
		font-weight: bold;
		color: #323232 */
	}

	.search-navbar-item .iconshangla {
		position: absolute;
		top: -4rpx;
		padding: 0 6rpx;
		font-size: 20rpx;
		color: #7D7D7D
	}

	.search-navbar-item .icondaoxu {
		position: absolute;
		top: 8rpx;
		padding: 0 6rpx;
		font-size: 20rpx;
		color: #7D7D7D
	}

	.search-navbar-item .iconshaixuan {
		margin-left: 10rpx;
		font-size: 22rpx;
		color: #7d7d7d
	}

	.filter-scroll-view {
		margin-top: var(--window-top)
	}

	.search-filter {
		display: flex;
		flex-direction: column;
		text-align: left;
		width: 100%;
		flex-wrap: wrap;
		padding: 0;
	}

	.filter-content-title {
		color: #999;
		font-size: 28rpx;
		height: 30rpx;
		line-height: 30rpx;
		padding: 0 30rpx;
		margin-top: 30rpx;
		margin-bottom: 10rpx
	}

	.filter-title {
		color: #BBBBBB;
		font-size: 32rpx;
		background: #F8F8F8;
		padding: 60rpx 0 30rpx 20rpx;
	}

	.search-filter-content {
		display: flex;
		flex-wrap: wrap;
		padding: 10rpx 20rpx;
	}

	.search-filter-content .filter-item {
		background: #F4F4F4;
		border-radius: 28rpx;
		color: #2B2B2B;
		font-weight: bold;
		margin: 10rpx 10rpx;
		min-width: 140rpx;
		height: 56rpx;
		line-height: 56rpx;
		text-align: center;
		font-size: 24rpx;
		padding: 0 30rpx
	}

	.search-filter-content .close {
		text-align: right;
		font-size: 24rpx;
		color: #ff4544;
		width: 100%;
		padding-right: 20rpx
	}

	.search-filter button .icon {
		margin-top: 6rpx;
		height: 54rpx;
	}

	.search-filter-btn {
		display: flex;
		padding: 30rpx 30rpx;
		justify-content: space-between
	}

	.search-filter-btn .btn {
		width: 240rpx;
		height: 66rpx;
		line-height: 66rpx;
		background: #fff;
		border: 1px solid #e5e5e5;
		border-radius: 33rpx;
		color: #2B2B2B;
		font-weight: bold;
		font-size: 24rpx;
		text-align: center
	}

	.search-filter-btn .btn2 {
		width: 240rpx;
		height: 66rpx;
		line-height: 66rpx;
		border-radius: 33rpx;
		color: #fff;
		font-weight: bold;
		font-size: 24rpx;
		text-align: center
	}

	.ind_business {
		width: 100%;
		margin-top: 140rpx;
		font-size: 26rpx;
		padding: 0 24rpx
	}

	.ind_business .ind_busbox {
		width: 100%;
		background: #fff;
		padding: 20rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
		border-radius: 8rpx;
		position: relative
	}

	.ind_business .ind_buspic {
		width: 120rpx;
		height: 120rpx;
		margin-right: 28rpx;
	}

	.ind_business .ind_buspic image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
		object-fit: cover;
	}

	.ind_business .bus_title {
		font-size: 30rpx;
		color: #222;
		font-weight: bold;
		line-height: 46rpx
	}

	.ind_business .bus_score {
		font-size: 24rpx;
		color: #FC5648;
		display: flex;
		align-items: center
	}

	.ind_business .bus_score .img {
		width: 24rpx;
		height: 24rpx;
		margin-right: 10rpx
	}

	.ind_business .bus_score .txt {
		margin-left: 20rpx
	}

	.ind_business .indsale_box {
		display: flex
	}

	.ind_business .bus_sales {
		font-size: 24rpx;
		color: #999;
		position: absolute;
		top: 20rpx;
		right: 28rpx
	}

	.ind_business .bus_address {
		color: #999;
		font-size: 22rpx;
		height: 36rpx;
		line-height: 36rpx;
		margin-top: 6rpx;
		display: flex;
		align-items: center;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
	}

	.ind_business .bus_address .x2 {
		padding-left: 20rpx
	}

	.ind_business .prolist {
		white-space: nowrap;
		margin-top: 16rpx;
		margin-bottom: 10rpx;
	}

	.ind_business .prolist .product {
		width: 108rpx;
		height: 160rpx;
		overflow: hidden;
		display: inline-flex;
		flex-direction: column;
		align-items: center;
		margin-right: 24rpx
	}

	.ind_business .prolist .product .f1 {
		width: 108rpx;
		height: 108rpx;
		border-radius: 8rpx;
		background: #f6f6f6
	}

	.ind_business .prolist .product .f2 {
		font-size: 22rpx;
		color: #FC5648;
		font-weight: bold;
		margin-top: 4rpx
	}

	.ind_business .prolist2 {
		margin-top: 16rpx;
		margin-bottom: 10rpx;
	}

	.ind_business .prolist2 .product {
		width: 118rpx;
		overflow: hidden;
		display: inline-flex;
		flex-direction: column;
		margin-right: 10rpx;
		position: relative;
		min-height: 200rpx;
		padding-bottom: 20rpx
	}

	.ind_business .prolist2 .product .f1 {
		width: 118rpx;
		height: 118rpx;
		border-radius: 8rpx;
		background: #f6f6f6
	}

	.ind_business .prolist2 .product .f2 {
		font-size: 26rpx;
		color: #FC5648;
		font-weight: bold;
		margin-top: 4rpx;
	}

	.ind_business .prolist2 .product .f3 {
		font-size: 22rpx;
		font-weight: normal;
		color: #aaa;
		text-decoration: line-through;
	}

	.ind_business .prolist2 .product .f4 {
		font-size: 20rpx;
		font-weight: normal;
		color: #888;
	}

	.ind_business .prolist2 .product .f5 {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		position: absolute;
		display: relative;
		top: 140rpx;
		right: 0rpx;
		text-align: center;
	}

	.ind_business .prolist2 .product .f5 .icon_gouwuche {
		font-size: 28rpx;
		height: 48rpx;
		line-height: 48rpx
	}

	.ind_business .prolist2 .product .f5 .img {
		width: 100%;
		height: 100%
	}

	.tags-container {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10rpx;
		/* 标签容器与标题的间距 */
	}

	.tag-item {
		background: rgba(173, 216, 230, 0.3);
		/* 淡蓝色透明背景 */
		color: #007BFF;
		/* 标签文字颜色 */
		padding: 6rpx 12rpx;
		/* 标签内边距 */
		border-radius: 12rpx;
		/* 标签圆角 */
		margin-right: 8rpx;
		/* 标签之间的水平间距 */
		margin-bottom: 8rpx;
		/* 标签之间的垂直间距 */
		font-size: 24rpx;
		/* 标签文字大小 */
		white-space: nowrap;
		/* 防止标签文字换行 */
	}
	/* 添加一个新的样式用于描述文字 */
		.result-description {
			margin-top: 100rpx; /* 调整到搜索框下方 */
			
			font-size: 16rpx;
			color: #333;
			text-align: center;
			background-color: #f7f7f7;
			border-radius: 10rpx;
		}
</style>
