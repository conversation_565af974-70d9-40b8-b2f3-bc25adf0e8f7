<template>
	<view style="padding-bottom: 250px;">

		<view style="position: relative;height: 350px;">

			<image style="width: 100%;height: 300px;position: absolute;" :src="pics[0]"></image>

			<image v-if="is_h5" style="width: 20px;height: 20px;position: absolute;top: 20px;left: 20px;"
				:src="pre_url+'/static/img/arrow-left.png'" @tap="goBack"></image>

			<view style="position: absolute;left: 0;bottom: 0px;width: 100%;">

				<view style="margin: 0 10px; background:#fffffff2;border-radius: 5px;padding: 10px;">

					<view style="display: flex;align-items: center;justify-content: space-between;">

						<view style="display: flex;align-items: center;">

							<image style="width: 40px;height: 40px;border-radius: 50px;" :src="tuanzhang.logo"></image>

							<text style="font-size: 16px;font-weight: bold;margin-left: 10px;">{{tuanzhang.name}}</text>

						</view>

						<view>

							<image style="width: 30px;height: 30px;" :src="pre_url+'/static/icon/share.png'"
								@click="shareClick"></image>

							<image style="width: 30px;height: 30px;margin-left: 10px;" :src="pre_url+'/static/icon/pic.png'"
								@click="shareClick">
							</image>

						</view>

					</view>

					<view style="padding: 5px 10px;color: #aaa;letter-spacing: 1.1px;margin-top: 5px;">
						{{tuanzhang.desc}}
					</view>

				</view>

			</view>

		</view>

		<view style="background: #fff;margin:10px;padding: 15px;border-radius: 5px;">

			<view
				style="font-size: 16px;overflow-wrap: break-word;font-weight: bold;padding: 10px 0;letter-spacing: 1.1px">
				{{detail.name}}
			</view>

			<view style="background: #f1f1f1;display: flex;padding: 5px;border-radius: 8px;margin: 10px 0;">

				<!-- 			<image style="width: 50px;height: 50px;" src="../../static/img/tghd.png"></image>
			 -->
				<view style="padding: 5px 5px 0 10px;text-align: center;color: #3AA578;font-family: DingTalk JinBuTi, DingTalk JinBuTi;
font-weight: 400;width: 15%;font-style: normal;">本团商品</view>

				<view style="width: 1px;background: #ccc;margin: 5px 10px;"></view>

				<scroll-view scroll-x class="scroll-view" style="width: 75%;">

					<view class="scroll-item" v-for="(ite, ind) in products" :key="ind" @tap="gotoItem(ite.id)">
						<image style="width: 50px;height: 50px;border-radius: 5px;margin-right: 5px;" :src="ite.pic">
						</image>
					</view>

				</scroll-view>

			</view>

			<view style="color: #999999;font-size: 10px;">{{detail.createtime}}发布</view>

			<view style="height: 1px;background: #eee;margin: 15px 0 ;" v-if="content"></view>

			<view style="padding:8rpx 0">

				<dp :pagecontent="pagecontentm"></dp>
				<!-- 输出富文本 -->
				<view v-html="rich_content" style="padding:10px 0;"></view>
				<view style="color:#3AA578;text-align: center;padding-top: 10px;" @click="openAll()" v-if="is_show">
					查看全部</view>
			</view>

		</view>

		<view style="background: #fff;margin:10px;padding: 10px;border-radius: 5px;">

			<view :id="'good'+ind" style="display: flex;border-bottom: 1px solid #eee;padding: 10px 0;"
				v-for="(ite, ind) in products" :key="ind">

				<image style="width: 120px;height: 120px;border-radius: 8px;margin-right: 10px;" :src="ite.pic"
					@tap="gotoItem(ite.id)"></image>

				<view style="position: relative;width: 65%;margin: 5px 0;">

					<view style="font-size: 16px;font-weight: bold;" class="ellipsis-2-lines" @tap="gotoItem(ite.id)">
						{{ite.name}}
					</view>

					<view style="color: #aaa;" class="ellipsis-2-lines" @tap="gotoItem(ite.id)">{{ite.sellpoint}}</view>

					<view
						style="display: flex;justify-content: space-between;position: absolute;bottom: 0;width: 100%;">

						<view style="color: red;" @tap="gotoItem(ite.id)">

							<text style="font-weight: bold;">￥</text>

							<text style="font-size: 18px;font-weight: bold;">{{ite.sell_price}}</text>

							<view style="display: block;font-size: 12px;color: #999;margin-top: 4px;" v-if="ite.commission > 0">
								预估佣金 ￥{{ite.commission}}{{ite.commission_desc}}
							</view>

						</view>

						<view style="background: #E54D42;color: #fff;border-radius: 25px;padding: 15px 12px;font-size: 11px;line-height: 1.2;white-space: nowrap;height: 20px;display: flex;align-items: center;" 
							@tap="addBuy(ite.id)">加入购物车</view>
					</view>

				</view>

			</view>

		</view>
 
		<view style="background: #fff;margin:10px;padding: 10px;border-radius: 5px;" v-if="pinglunlist.length>0">

			<view
				style="display: flex;justify-content: space-between;padding-bottom: 10px;border-bottom: 1px solid #eee;">

				<view style="font-weight: bold;">评论</view>

				<view style="font-size: 12px;color: #aaa;">查看全部</view>

			</view>

			<view style="display: flex;margin: 10px 0;">

				<text
					style="background: #EEF6EF;color: #58A27E;border-radius: 5px;padding: 3px 8px;font-size: 13px;">性价比高(2)</text>

			</view>

			<view v-for="(ite, ind) in pinglunlist" :key="ind"
				style="border-bottom: 1px solid #eee;padding-bottom: 10px;">

				<view style="display: flex;margin-top: 10px;">

					<image style="width: 50px;height: 50px;border-radius: 5px;" :src="ite.headimg"></image>

					<view style="margin: 5px;">

						<view>{{ite.nickname}}</view>

						<view style="color: #aaa;">{{ite.createtime}}</view>

					</view>

				</view>

				<view style="overflow-wrap: break-word;margin: 10px 0;font-size: 16px;">{{ite.content}}</view>

				<view>

					<image style="width: 80px;height: 80px;border-radius: 5px;margin-right: 10px;" :src="it"
						v-for="(it, i) in ite.pics" :key="i" @tap="previewImage(it)"></image>

				</view>

			</view>

		</view>

		<!-- 		<view style="background: #fff;margin:10px;padding: 10px;border-radius: 8px;">
		
		   <view style="font-size: 14px;font-weight: bold;">精选评论</view>	
			
			<view style="margin-top: 5px;display: flex;align-items: center;">
				
				<image style="width: 40px;height: 40px;" src="../../static/img/tghd.png"></image>
				
				<view style="width: 85%;white-space: nowrap; overflow: hidden;text-overflow: ellipsis;">111111111111111111111111111111111111111</view>
				
			</view>
			
		</view> -->

		<view style="margin:10px;padding: 5px;" v-if="bijis.length>0">

			<view style="display: flex;align-items: center;">

				<view style="width: 3px;background: #58A27E;height: 15px;margin-right: 10px;"></view>

				<view style="font-size: 16px;font-weight: bold;">经销商推荐</view>

			</view>

			<view class="list">

				<view class="pbl" v-for="(item, j) in bijis" :key="j" @tap="goto" :data-url="item.yindao_link">

					<view class="image">
						<image fade-show lazy-load :lazy-load-margin="0" mode="widthFix" :src="item.coverimg"></image>
					</view>
					<view class="title" v-html="item.content"> </view>

					<view
						style="display: flex;align-items: center;justify-content: space-between;padding: 10px;color: #aaa;">

						<view style="display: flex;align-items: center;width: 60%;">

							<img style="width: 20px;height: 20px;border-radius: 50px;" :src="item.headimg"></img>

							<view
								style="font-size: 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;margin-left: 5px;">
								{{item.nickname}}
							</view>

						</view>

						<view style="display: flex;align-items: center;">

							<image style="width: 12px;height: 12px;" :src="pre_url+'/static/restaurant/like1.png'"></image>

							<view style="font-size: 10px;margin-left: 5px;">{{item.zan}}</view>

						</view>

					</view>

				</view>

			</view>
		</view>

		<view
			style="position: fixed; bottom: 0;display: flex;justify-content: space-between;align-items: center;width: 100%;background: #fff;padding: 10px 15px;">

			<view style="width: 45%;display: flex;">

				<view style="text-align: center;" @tap="goto" data-url="./tuanlist">

					<image style="width: 20px;height: 20px;" :src="pre_url+'/static/icon/home.png'" />

					<view>首页</view>

				</view>

				<!-- 			<view style="text-align: center;" @tap="gotokfurl"  v-if="is_h5">
				<image style="width: 20px;height: 20px;" src="/static/img/kefu.png" />
				<view class="t1">客服</view>
			</view> -->
				<button style="text-align: center;line-height:1.3" @tap="goto" :data-url="kfurl"
					v-if="kfurl!='contact::'">
					<image style="width: 20px;height: 20px;" :src="pre_url+'/static/img/kefu.png'" />
					<view class="t1">客服</view>
				</button>


				<view style="text-align: center;position: relative;" @tap="goto" data-url="/shopPackage/shop/cart">

					<view>

						<image style="width: 20px;height: 20px;" :src="pre_url+'/static/icon/buy.png'" />

						<view>购物车</view>

					</view>


					<view
						style="position: absolute;top:-5px;right:0;background: red;color: #fff;border-radius: 50px;padding: 0 5px;"
						v-if="cart_num>0">{{cart_num}}</view>

				</view>

			</view>

			<view
				:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)',width: '50%',textAlign: 'center',color: '#fff',padding: '10px',borderRadius: '5px',fontSize: '16px'}"
				@tap="openShop" >
				跟团购买
				<text style="font-size: 10px; margin-left: 4px;">(56人已跟团)</text>
			</view>


		</view>


		<view
			style="width: 70px;height: 90px;background: #fff;border-radius: 5px;position: fixed;right: 10px;top: 70%;padding: 3px;"
			v-if="products.length > 0">

			<!-- 商品轮播 -->
			<view style="position: relative;">
				<swiper autoplay interval="3000" @change="onSwiperChange" style="height: 65px;">
					<swiper-item v-for="(ite, ind) in products" :key="ind" @click="scrollToElement('good'+ind)">
						<image style="width: 100%;height: 100%;border-radius: 5px;" :src="ite.pic"></image>
						<view
							style="position: absolute;right: 0;top: 0;background: #00000077;padding: 2px 5px;color: #fff;font-size: 8px;border-radius:0 5px;">
							{{ ind + 1 }}
						</view>
					</swiper-item>
				</swiper>
			</view>

			<!-- 购买同款 -->
			<view style="text-align: center;font-size: 10px;margin-top: 3px;" @tap="buySameProduct">
				购买同款
			</view>
		</view>



		<view v-if="sharetypevisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal" style="height:320rpx;min-height:320rpx">
				<!-- <view class="popup__title">
					<text class="popup__title-text">请选择分享方式</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hidePstimeDialog"/>
				</view> -->
				<view class="popup__content">
					<view class="sharetypecontent">
						<view class="f1" @tap="shareapp" v-if="getplatform() == 'app'">
							<image class="img" :src="pre_url+'/static/img/weixin.png'" />
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'mp'">
							<image class="img" :src="pre_url+'/static/img/weixin.png'" />
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'h5'">
							<image class="img" :src="pre_url+'/static/img/weixin.png'" />
							<text class="t1">分享给好友</text>
						</view>
						<button class="f1" open-type="share" v-else>
							<image class="img" :src="pre_url+'/static/img/weixin.png'" />
							<text class="t1">分享给好友</text>
						</button>
						<view class="f2" @tap="showPoster">
							<image class="img" :src="pre_url+'/static/img/sharepic.png'" />
							<text class="t1">生成分享图片</text>
						</view>
					</view>
				</view>
			</view>
		</view>


		<view class="posterDialog" v-if="showposter">
			<view class="main">
				<view class="close" @tap="posterDialogClose">
					<image class="img" :src="pre_url+'/static/daihuo/shanchu.png'" />
				</view>
				<view class="content">
					<image class="img" :src="posterpic" mode="scaleToFill" >
					</image>
				</view>
			</view>

			<view style="display: flex;justify-content: space-between;margin: 0 10%;">

				<button class="mbtn" :style="'color:'+t('color1')+';border: 1px solid '+t('color1') "
					@click="baocun">保存图片</button>

				<button class="mbtn" :style="'color: #fff;background:'+t('color1')" open-type="share">分享给好友</button>

			</view>
		</view>

		<buydialog v-if="buydialogShow" :proid="product_id" :tid="tid" :btntype="btntype"
			@buydialogChange="buydialogChange" @showLinkChange="showLinkChange" :menuindex="menuindex"
			@addcart="addcart"></buydialog>


      <!-- 商品购物 -->
      <uni-popup ref="shopShow" type="bottom" style="z-index: 999;">
      	<view class="viewShop">
      		<!-- 修改头部，添加佣金合计显示 -->
      		<view style="padding: 20rpx;position: sticky;top: 0;background: #fff;z-index: 1;">
      			<view style="text-align: center;font-weight: bold;margin-bottom: 10rpx;">
      				文中提到的商品（{{products.length}}）
      			</view>
      			<view style="text-align: center;font-size: 24rpx;color: #999;">
      				预估佣金合计: <text style="color: #E54D42;font-weight: bold;">￥{{totalCommission}}</text>
      			</view>
      		</view>
      
      		<view class="cart-container">
      			<scroll-view :scroll-top="0" scroll-y="true" style="height: 50vh;">
      				<view class="cart-item" v-for="(item, index) in products" :key="index"
      					>
      					<view class="item-selector" @click="toggleSelection(item)">
      						<checkbox :checked="item.selected" style="transform:scale(0.8)"></checkbox>
      					</view>
      					<view class="image-box" @click="goto" :data-url="'/shopPackage/shop/product?id='+item.id">
      						<image class="item-image" :src="item.pic" mode="heightFix"></image>
      					</view>
      
      					<view class="item-info">
      						<text class="item-name"@click="goto" :data-url="'/shopPackage/shop/product?id='+item.id">{{ item.name }}</text>
      						<view style="display: flex;justify-content: space-between;width: 100%;">
      							<view>
      								<text class="item-price">
      									<span>￥</span>
      									<span style="font-size: 34rpx;font-weight: bold;">{{ item.sell_price }}</span>
      								</text>
      								<text style="font-size: 20rpx;color: #999;margin-left: 8px;" v-if="item.commission > 0">
      									预估佣金 ￥{{item.commission}}{{item.commission_desc}}
      								</text>
      							</view>
      							<view style="background: #E54D42;color: #fff;border-radius: 25px;padding: 3px 12px;font-size: 11px;line-height: 1.2;white-space: nowrap;height: 20px;display: flex;align-items: center;" 
      								@click.stop="addCartOne(item)">加入购物车</view>
      						</view>
      
      					</view>
      
      				</view>
      			</scroll-view>
      
      			<view style="position: sticky;bottom: 0;
      			height: 100rpx;display: flex;justify-content: space-between;
      			align-items: center;padding: 0 20rpx;">
      				<view @tap="shopAllSelectedClick">
      					<checkbox :checked="shopAllSelected" style="transform:scale(0.8)"></checkbox>
      					全选
      				</view>
      
						<view style="text-align: center;margin-left: 100px;position: relative;" @tap="goto" data-url="/shopPackage/shop/cart">
      
						
							<view>
						
								<image style="width: 25px;height: 25px;" :src="pre_url+'/static/icon/buy.png'" />
<!-- 						
								<view>购物车</view> -->
						
							</view>
						
						
							<view
								style="position: absolute;top:-5px;right:0;background: red;color: #fff;border-radius: 50px;padding: 0 5px;"
								v-if="cart_num>0">{{cart_num}}</view>
						
						</view>
      
						
						<view :class="isAnyItemSelected?'shopButtonActive':'shopButton'" @click="shop">
							加入购物车
						</view>
      			</view>
      		</view>
      	</view>
      </uni-popup>

	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {

				tid: 0,
				detail: '',
				opt: '',
				pics: [],
				products: [],
				ind: 0,
				pinglunlist: [],
				tuanzhang: '',
				rich_content: '', // 新增字段
				content: '',
				pagecontent: "",
				pagecontentm: "",

				sharetypevisible: false,
				buydialogShow: false,
				btntype: 1,
				showLinkStatus: false,
				menuindex: -1,
				cartnum: "",
				product_id: '',
				kfurl: '',
				timer: '',
				rid: 1,
				bijis: [],
				cart_num: 0,
				is_h5: false,
				is_show: true,
				currentProductIndex: 0, // 当前显示的商品索引

				showposter: false,
				posterpic: "",
				
				matchedData: [],
				shopAllSelected: false,
				cart_num : 0,
				totalCommission: 0,
			};
		},
		computed: {
			isAnyItemSelected() {
				return this.products.some(item => item.selected);
			}
		},
		onLoad(opt) {
			this.opt = opt;
			this.tid = this.opt.id;
			this.getdata();

			// #ifdef H5
			this.is_h5 = true;
			// #endif

		},
		onPullDownRefresh: function() {
			this.getdata();
		},

		onShow() {
			this.getCart();
		},

		methods: {
			// 打开购物车
			openShop() {

				//this.getShopList(this.detailInfo.productids)
				this.$refs.shopShow.open('top')
				
				this.getCart();
			},
			
			toggleSelection(item) {
				this.$set(item, 'selected', !item.selected); // 使用 $set 确保响应性
				this.shopAllSelected = this.products.every(item => item.selected)
			},
			
			// 全选
			shopAllSelectedClick() {
				this.shopAllSelected = !this.shopAllSelected
				this.products.forEach(item => {
					item.selected = this.shopAllSelected
				})
			},
			
			shop() {
				if (!this.isAnyItemSelected) {
					app.success('请选择商品');
					return;
				}
			
				this.products.forEach(async item => {
					await this.addCart(item.id)
				})
				app.success('添加成功');
				this.$refs.shopShow.close()
			},
			
			async addCartOne(item) {
				this.addCart(item.id)
				app.success('添加成功');
			},
			addCart(proid) {
				let that = this
				return new Promise((resolve, reject) => {
					app.post('ApiShop/addcart', {
						proid,
						num: 1
					}, function(res) {
						that.cart_num++;
						resolve('添加成功')
					});
				});
			},
			
			previewImage(src) {
				let arr = []
				arr.push(src)
				uni.previewImage({
					current: 1, // 当前显示图片索引
					urls: arr // 需要预览的图片http链接列表
				});
			},
			onSwiperChange(e) {
				this.currentProductIndex = e.detail.current; // 获取当前显示的商品索引
			},

			// 点击购买同款，跳转到当前显示的商品详情页面
			buySameProduct() {
				const currentProduct = this.products[this.currentProductIndex]; // 当前显示的商品
				if (currentProduct && currentProduct.id) {
					uni.navigateTo({
						url: '/shopPackage/shop/product?id=' + currentProduct.id // 跳转到商品详情页面
					});
				} else {
					// 处理没有找到商品的情况
					uni.showToast({
						title: '未找到商品',
						icon: 'none'
					});
				}
			},
			goBack() {
				uni.navigateBack();
			},
			gotoItem(id) {
				uni.navigateTo({
					url: '/shopPackage/shop/product?id=' + id
				})
			},


			scrollToElement(id) {
				uni.createSelectorQuery().select('#' + id).boundingClientRect(res => {
					if (res) {
						uni.pageScrollTo({
							scrollTop: res.top + (uni.getSystemInfoSync().screenHeight / 2), // 适当调整滚动位置
							duration: 300
						});
					}
				}).exec();
			},

			addBuy(e) {
				this.product_id = e;
				this.buydialogShow = !this.buydialogShow;
			},

			addcart: function(e) {
				console.log(e)
				this.cartnum = this.cartnum + e.num;
			},
			showLinkChange: function() {
				this.showLinkStatus = !this.showLinkStatus;
			},

			buydialogChange: function(e) {
				let that = this
				if (!that.buydialogShow) {
					that.btntype = e.currentTarget.dataset.btntype
				}
				that.buydialogShow = !that.buydialogShow;

				setTimeout(function() {
					that.getCart();
				}, 1000)

			},

			shareClick: function() {
				this.sharetypevisible = true;
			},
			handleClickMask: function() {
				this.sharetypevisible = false;
			},

			gotokfurl() {

				uni.navigateTo({
					url: '../../pagesExt/kefu/index?bid=' + this.opt.id
				})
			},

			openAll() {
				this.pagecontentm = this.pagecontent;
				this.is_show = false;
			},

			getdata: function() {
				var that = this;

				that.pagenum = 1;
				// that.datalist = [];
				that.loading = true;
				app.get('Apidaihuoyiuan/detail', {
					pid: 0,
					id: this.opt.id
				}, function(res) {

		            console.log(res)

					that.detail = res.detail
					that.rich_content = res.detail.rich_content; // 赋值富文本字段
					that.pics = res.detail.pic2.split(',')

					that.products = res.products

					that.product_id = that.products[0].id
					that.kfurl = '/pagesExt/kefu/index?bid=' + res.detail.bid
			

					that.tuanzhang = res.tuanzhang

					that.content = res.pagecontent[0].content
					that.pagecontent = res.pagecontent;

					let pages = []
					res.pagecontent.filter((item, ind) => {
						if (ind < 2) {
							pages.push(item)
						}
					});
					that.pagecontentm = pages

					res.pinglunlist.filter(item => {
						item.pics = item.content_pic.split(',')
					})

					that.pinglunlist = res.pinglunlist

					that.bijis = res.bijis

					that.totalCommission = res.totalCommission;
				});
			},

			getCart() {
				let that = this
				app.get('ApiShop/cart', {}, function(res) {
					console.log(res.cartlist[0].prolist.length)
					if (res.cartlist.length > 0) {
						that.cart_num = res.cartlist[0].prolist.length;
					}
				});
			},
			showPoster: function() {
				var that = this;
				that.showposter = true;
				that.sharetypevisible = false;


				app.showLoading('努力生成中');
				app.post('ApiTuanzhang/createposter', {
					pid: that.tid
				}, function(data) {
					app.showLoading(false);
					if (data.status == 0) {
						app.alert(data.msg);
					} else {
						that.posterpic = data.poster;
					}
				});
			},


			baocun() {
				let that = this
				uni.showLoading()
				uni.saveImageToPhotosAlbum({
					filePath: that.posterpic,
					success() {
						uni.hideLoading()
						uni.showToast({
							title: '保存成功',
							icon: 'success',
							duration: 2000
						})
					},
					fail: function(err) {
						uni.hideLoading()
						if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
							uni.openSetting({
								success(settingdata) {
									if (settingdata.authSetting['scope.writePhotosAlbum']) {
										uni.showToast({
											title: '您已授权成功，请重新保存海报',
											icon: 'success',
											duration: 2000
										})
									} else {
										uni.showToast({
											title: '尚未授权，无法保存海报',
											icon: 'none',
											duration: 2000
										})
									}
								}
							})
						}
					}

				})

			},

			posterDialogClose: function() {
				this.showposter = false;;
			},
			sharemp: function() {
				app.error('点击右上角发送给好友或分享到朋友圈');
				this.sharetypevisible = false
			},
			shareapp: function() {
				var that = this;
				uni.showActionSheet({
					itemList: ['发送给微信好友', '分享到微信朋友圈'],
					success: function(res) {
						if (res.tapIndex >= 0) {
							var scene = 'WXSceneSession';
							if (res.tapIndex == 1) {
								scene = 'WXSenceTimeline';
							}
							var sharedata = {};
							sharedata.provider = 'weixin';
							sharedata.type = 0;
							sharedata.scene = scene;
							sharedata.title = that.product.name;
							//sharedata.summary = app.globalData.initdata.desc;
							sharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid +
								'.html#/activity/luckycollage/product?scene=id_' + that.product.id +
								'-pid_' + app.globalData.mid;
							sharedata.imageUrl = that.product.pic;
							var sharelist = app.globalData.initdata.sharelist;
							if (sharelist) {
								for (var i = 0; i < sharelist.length; i++) {
									if (sharelist[i]['indexurl'] == '/activity/luckycollage/product') {
										sharedata.title = sharelist[i].title;
										sharedata.summary = sharelist[i].desc;
										sharedata.imageUrl = sharelist[i].pic;
										if (sharelist[i].url) {
											var sharelink = sharelist[i].url;
											if (sharelink.indexOf('/') === 0) {
												sharelink = app.globalData.pre_url + '/h5/' + app
													.globalData.aid + '.html#' + sharelink;
											}
											if (app.globalData.mid > 0) {
												sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') +
													'pid=' + app.globalData.mid;
											}
											sharedata.href = sharelink;
										}
									}
								}
							}
							uni.share(sharedata);
						}
					}
				});
			}
		}

	}
</script>

<style lang="less">
	.scroll-view {
		white-space: nowrap;
		/* 确保内容不换行 */
		width: 100%;
		/* 设置滚动视图的宽度 */
	}

	.scroll-item {
		display: inline-block;
		/* 内容项按行内块显示 */
		margin-right: 10px;
		/* 设置内边距 */
		text-align: center;

	}


	.waterfall-container {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin: 20px 0;
	}

	.waterfall-item {
		width: 49%;
		/* 保证两列间隔相等 */
		margin-bottom: 10px;
		background: #fff;
		border-radius: 8px;
	}



	.ellipsis-2-lines {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}


	.list {
		column-count: 2;
		box-sizing: content-box;
		margin-top: 10px;
	}

	.pbl {
		width: 100%;
		break-inside: avoid;
		overflow: hidden;
		border-radius: 5px;
		overflow: hidden;
		margin-bottom: 20rpx;
		background-color: #fff;
		box-sizing: border-box;

		&:last-child {
			margin-bottom: 10rpx;
		}

		.image {
			width: 100%;
			border-radius: 5px;
			overflow: hidden;

			&>image {
				width: 100%;
				height: 100%;
			}
		}

		.title {
			font-size: 32rpx;
			margin-bottom: 6rpx;
			display: -webkit-box;
			text-overflow: ellipsis;
			overflow: hidden;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2; //当属性值为3表示超出3行隐
			padding: 5px 10px;
			font-weight: bold;
		}

		.more {
			display: flex;
			justify-content: space-between;
			color: #9499aa;
			margin-bottom: 6rpx;
			font-size: 26rpx;
		}
	}


	/* 多行省略 */
	.multi-line-omit {
		word-break: break-all; // 允许单词内自动换行，如果一个单很长的话
		text-overflow: ellipsis; // 溢出用省略号显示
		overflow: hidden; // 超出的文本隐藏
		display: -webkit-box; // 作为弹性伸缩盒子模型显示
		-webkit-line-clamp: 2; // 显示的行
		-webkit-box-orient: vertical; // 设置伸缩盒子的子元素排列方式--从上到下垂直排列
	}

	/* 单行省略 */
	.one-line-omit {
		width: 100%; // 宽度100%：1vw等于视口宽度的1%；1vh等于视口高度的1%
		white-space: nowrap; // 溢出不换行
		overflow: hidden; // 超出的文本隐藏
		text-overflow: ellipsis; // 溢出用省略号显示
	}

	.sharetypecontent {
		height: 250rpx;
		width: 710rpx;
		margin: 20rpx;
		display: flex;
		padding: 50rpx;
		align-items: flex-end
	}

	.sharetypecontent .f1 {
		color: #51c332;
		width: 50%;
		height: 150rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #fff;
		font-size: 28rpx;
		border: 0
	}

	.sharetypecontent button::after {
		border: 0
	}

	.sharetypecontent .f1 .img {
		width: 90rpx;
		height: 90rpx
	}

	.sharetypecontent .f2 {
		color: #51c332;
		width: 50%;
		display: flex;
		flex-direction: column;
		align-items: center
	}

	.sharetypecontent .f2 .img {
		width: 90rpx;
		height: 90rpx
	}

	.sharetypecontent .t1 {
		height: 60rpx;
		line-height: 60rpx;
		color: #666
	}



	// 隐藏滚动条 vue3写法
	::v-deep ::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}

	.posterDialog {
		position: fixed;
		z-index: 9;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.8);
		top: var(--window-top);
		left: 0
	}

	.posterDialog .main {
		width: 80%;
		margin: 20% 10% 30rpx 10%;
		background: #fff;
		position: relative;
		border-radius: 10rpx
	}

	.posterDialog .close {
		position: absolute;
		top: -40px;
		right: 0
	}

	.posterDialog .close .img {
		width: 60rpx;
		height: 60rpx;
	}

	.posterDialog .content {
		width: 100%;
		padding:  20rpx;
		color: #333;
		font-size: 30rpx;
		text-align: center
	}

	.posterDialog .content .img {
		width: 540rpx;
		height: 900rpx
	}


	.posterDialog .mbtn {
		width: 47%;
		text-align: center;
		border-radius: 5px;
		font-size: 18px;
	}
</style>

<style>
	.viewShop {
		height: 60vh;
		width: 100%;
		border-radius: 20rpx 20rpx 0 0;
		background-color: #fff;
		padding-top: 20rpx;
	}
	.cart-container {
		display: flex;
		flex-direction: column;
		height: calc(100% - 120rpx); /* 减去头部高度 */
		overflow-y: auto;
	}

	.cart-item {
		display: flex;
		align-items: center;
		padding: 10px;
	}

	.item-selector {
		margin-right: 10px;
	}

	.image-box {
		background-color: #f4f4f4;
		border-radius: 10rpx;
		width: 130rpx;
		height: 130rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.item-image {
		height: 70%;
	}

	.item-info {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 130rpx;
		width: 67%;
	}

	.item-name {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}

	.item-price {
		font-size: 14px;
		color: #e65602;
	}

	.item-quantity {
		display: flex;
		align-items: center;
		margin-left: auto;
	}

	.item-quantity button {
		width: 30px;
		height: 30px;
		border: 1px solid #ddd;
		background-color: #fff;
		cursor: pointer;
	}

	.item-quantity text {
		margin: 0 5px;
	}

	.shopButton {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #a0a0a0;
		background-color: #ddd;
		font-size: 20rpx;
	}

	.shopButtonActive {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #fff;
		background-color: #eb8200;
		font-size: 20rpx;
	}
</style>