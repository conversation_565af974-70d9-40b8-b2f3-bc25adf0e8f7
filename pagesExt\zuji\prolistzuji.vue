<template>
	<view class="page-container">
		<view class="header">
			<image :src="business.logo" class="store-image"></image>
			<view class="store-info">
				<text class="store-name">{{business.name}}</text>
				<text class="store-address">{{business.address}}</text>
			</view>
		</view>

		<view class="model-selection">
			<text class="section-title">请选择机型</text>
			<view class="tab">
				<view v-for="item in catagoryList" :key="item.id"
					:class="['tab-item', activeTab === item.id ? 'active' : '']" @click="selectTab(item.id)">
					{{item.name}}
					<view class="active-after" v-if="activeTab === item.id"></view>
				</view>
			</view>
		</view>

		<view class="product-info">
			<view class="info-item">
				<text class="label">品牌</text>
				<picker mode="selector" :range="brands" range-key="name" @change="onBrandChange">
					<view class="picker">{{ selectedBrand }}<text class="arrow">></text></view>
				</picker>
			</view>
			<view class="info-item" v-for="(item, index) in guigeList" :key="item.k">
				<text class="label">{{item.title}}</text>
				<picker mode="selector" :range="item['items']" range-key="title"
					@change="handleCShangeGuigeData(index,  $event)">
					<view class="picker">
						{{ item['items'][guigeChoosenData[index]] && item['items'][guigeChoosenData[index]].title }}<text
							class="arrow">></text></view>
							
								
				</picker>
			</view>

		</view>


		<view class="credit-selection">
			<text class="section-title">芝麻信用分范围</text>
			<text class="note">*请选择客户支付宝芝麻信用分</text>
			<radio-group class="credit-options" @change="onCreditChange">
				<label class="radio-label">
					<radio value="400以下">400以下</radio>
				</label>
				<label class="radio-label">
					<radio value="400-500">400-500</radio>
				</label>
				<label class="radio-label">
					<radio value="500以上">500以上</radio>
				</label>
				<label class="radio-label">
					<radio value="老客户专属">老客户专属</radio>
					<text class="exclusive">包过！<text class="info-icon" @click="showPopup">i</text></text>
				</label>
			</radio-group>
		</view>

		<view v-if="selectedCreditRange" class="initial-payment-selection">
			<text class="section-title">首期金额</text>
			<view class="payment-options">
				<view v-if="selectedCreditRange == '500以上' || selectedCreditRange == '老客户专属'"
					:class="['payment-option', selectedPayment === '30%' ? 'selected' : '']"
					@click="selectPayment('30%')">30%</view>
				<view v-if="selectedCreditRange !== '400以下'"
					:class="['payment-option', selectedPayment === '40%' ? 'selected' : '']"
					@click="selectPayment('40%')">40%</view>
				<view :class="['payment-option', selectedPayment === '50%' ? 'selected' : '']"
					@click="selectPayment('50%')">50%</view>
			</view>
		</view>

		<view v-if="selectedCreditRange" class="lease-term-selection">
			<text class="section-title">租赁期数</text>
			<view class="lease-options">
				<view :class="['lease-option', selectedLease === '6期' ? 'selected' : '']" @click="selectLease('6期')">6期
				</view>

				<view :class="['lease-option', selectedLease === '8期' ? 'selected' : '']" @click="selectLease('8期')">8期
				</view>
			</view>
		</view>

		<view v-if="selectedCreditRange" class="contract-price">
			<text class="contract-label">设备总签约价</text>
			<text class="contract-price">¥ {{totalPrice}}</text>
		</view>

		<view v-if="selectedCreditRange" class="billing-details">
			<text class="section-title">账单明细</text>
			<view class="billing-item">
				<text class="billing-label">首期</text>
				<text class="billing-value">¥ 2310</text>
			</view>
			<view class="billing-item">
				<text class="billing-label">第2期租金</text>
				<text class="billing-value">¥ 1495</text>
			</view>
		</view>

		<view v-if="selectedCreditRange" class="next-step">
			<button type="primary" @click="nextStep">下一步</button>
		</view>

		<view v-if="showPopup" class="popup-overlay" @click="closePopup">
			<view class="popup-content">
				<text class="popup-text">该选项仅供参考，具体情况请咨询店铺工作人员。</text>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				activeTab: '新机',
				brands: ['苹果', '三星'],
				models: {
					'苹果': ['苹果15 Pro', '苹果14 Pro'],
					'三星': ['三星S21', '三星S20']
				},
				memories: {
					'苹果15 Pro': ['128g', '256g'],
					'苹果14 Pro': ['64g', '128g'],
					'三星S21': ['128g', '256g'],
					'三星S20': ['64g', '128g']
				},
				colors: {
					'苹果15 Pro': ['原色钛金属', '黑色'],
					'苹果14 Pro': ['黑色', '白色'],
					'三星S21': ['黑色', '白色'],
					'三星S20': ['蓝色', '黑色']
				},
				selectedBrand: '苹果',
				selectedModel: '苹果15 Pro',
				selectedMemory: '128g',
				selectedColor: '原色钛金属',
				selectedPayment: '30%',
				selectedLease: '6期',
				selectedCreditRange: '',
				showPopup: false,
				modelOptions: [], // 型号选项
				memoriesOptions: [], // 内存选项
				colorOptions: [],
				periodOptions: [],
				rateValue: 1,
				totalPrice: 0,
				sellPrice: 0,
				choosenGood: {},
				catagoryList: [], // 机型列表
				choosenCategory: {},
				activetab: null,
				responseData: {},
				guigeChoosenData: [],
				bid: 0,
				business:'' ,
				guigeList: []
			};
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();
			this.bid = this.opt.bid ? this.opt.bid : 0;
		},
		watch: {
			selectedPayment(newVal, oldVal) {
				if (this.selectedPayment === '30%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				} else if (this.selectedPayment === '40%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				} else if (this.selectedPayment === '50%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				}

			},
			selectedLease(newVal, oldVal) {
				if (this.selectedPayment === '30%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				} else if (this.selectedPayment === '40%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				} else if (this.selectedPayment === '50%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				}
			},
			rateValue(newVal, oldVal) {
				if (newVal) {
					this.totalPrice =parseFloat(this.choosenGood.sell_price * this.rateValue).toFixed(3)
				}
			},
			selectedBrand(newVal, oldVal) {
				if (newVal) {
					this.totalPrice = parseFloat(this.choosenGood.sell_price * this.rateValue).toFixed(3)
				}
			},
			guigeChoosenData(newVal, oldVal) {
				if(newVal.length == this.guigeList.length) {
					let guigeIndexString = newVal.join(',')
					let item = this.responseData.guigelist.find(_item => _item.ks === guigeIndexString && _item.proid === this.choosenGood.id)
					let priceItems = item && item.items
					
					let finalObj = priceItems.find(_t => _t.ks === guigeIndexString)
					this.totalPrice = finalObj && finalObj.sell_price
					
				}
				
			}


		},
		methods: {
			selectTab(tab) {

				this.activeTab = tab;
				this.brands = this.responseData.datalist.filter(item => item.cid == this.activeTab)
				
				this.selectedBrand = this.brands[0] && this.brands[0].name || ''

				this.choosenGood = this.brands[0] || null
				let guigeObj = JSON.parse(this.choosenGood.guigedata)
				this.guigeList = guigeObj
				
				for(var i in guigeObj) {
					this.guigeChoosenData[i] = 0
				}


			},
			// 选择品牌
			onBrandChange(e) {
				console.log('this.brands=======》》》》》》》》》=', this.brands, e)
				let guigeList = JSON.parse(this.brands[e.detail.value].guigedata)
				
				this.guigeList = guigeList
				console.log('this.guigeList=====', this.guigeList)
				
				for(var i in guigeList) {
					this.guigeChoosenData[i] = 0
				}
				this.choosenGood = this.brands[e.detail.value]
				this.selectedBrand = this.brands[e.detail.value]['name'];

			
			},

			// 选择型号
			onModelChange(e) {
				// this.selectedModel = this.modelOptions && this.modelOptions['items'] && this.modelOptions['items'][e.detail.value]['title']
				this.selectedModel = e.detail.value


				// this.selectedModel = this.memoriesOptions[this.selectedBrand][e.detail.value];
				// this.selectedMemory = this.memories[this.selectedModel][0];
				// this.selectedColor = this.colors[this.selectedModel][0];
			},
			onMemoryChange(e) {

				this.selectedMemory = this.memoriesOptions && this.memoriesOptions['items'] && this.memoriesOptions[
					'items'][e.detail.value]['title']
				// this.selectedMemory = this.memories[this.selectedModel][e.detail.value];
			},
			onColorChange(e) {
				// this.selectedColor = this.colors[this.selectedModel][e.detail.value];
				this.selectedColor = this.colorOptions && this.colorOptions['items'] && this.colorOptions['items'][e.detail
					.value
				]['title']
			},
			onCreditChange(e) {
				this.selectedCreditRange = e.detail.value;
			},
			selectPayment(payment) {
				this.selectedPayment = payment;

			},
			selectLease(lease) {
				this.selectedLease = lease;
			},
			showPopup() {
				this.showPopup = true;
			},
			closePopup() {
				this.showPopup = false;
			},
			nextStep() {
				// Add functionality for the next step button
				let guigeString = this.guigeChoosenData.join(',')
				
				let prodid = this.choosenGood.id
				uni.navigateTo({
					url: '/pagesExt/zuji/submitBuy?guigeString=' + guigeString + '&prodid='+ prodid
				})
			},
			//获取数据
			getdata: function() {
				var that = this;
				// var aid = that.opt.aid;
				var aid = 1;
				var bid = that.opt.bid ? that.opt.bid : '';
				app.get('Apizuji/prolist', {
					bid: bid,
					aid: aid
				}, function(res) {

					that.responseData = res
					that.business = res.business
					that.catagoryList = res.clist
					that.choosenCategory = res.clist[0]
					that.activeTab = res.clist[0].id
					that.brands = res.datalist.filter(item => item.cid == that.activeTab)
					that.selectedBrand = res.datalist[0].name

					that.choosenGood = res.datalist[0]
					let guigeObj = JSON.parse(res.datalist[0]['guigedata'])

					console.log("------------------", guigeObj)
					that.guigeList = guigeObj

					for (var i in guigeObj) {
						that.guigeChoosenData[i] = 0
					}

				})
			},
			setRateValue() {
				if (this.selectedPayment === '30%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				} else if (this.selectedPayment === '40%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				} else if (this.selectedPayment === '50%') {
					if (this.selectedLease === '6期') {
						this.rateValue = 1.37
					} else if (this.selectedLease === '8期') {
						this.rateValue = 1.51
					}
				}
			},
			calculatePrice() {
				console.log(' this.selectedBrand.sell_price----', this.selectedBrand.sell_price, this.rateValue)
				this.totalPrice = this.choosenGood.sell_price * this.rateValue

			},
			// 修改规格数据

			handleCShangeGuigeData(index, e) {
				this.$set(this.guigeChoosenData, index, e.target.value)
			}
		},


	}
</script>

<style>
	.page-container {
		background-color: #f0f0f0;
	}

	.header {
		display: flex;
		padding: 20px;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
	}

	.store-image {
		width: 80px;
		height: 80px;
		border-radius: 10px;
		object-fit: cover;
	}

	.store-info {
		margin-left: 20px;
		flex: 1;
	}

	.store-name {
		font-size: 18px;
		font-weight: bold;
	}

	.store-address {
		font-size: 14px;
		color: #888;
		margin-top: 5px;
	}

	.model-selection {
		padding: 10px 20px 0px;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
		margin: 10px;
		border-radius: 8px;
	}

	.section-title {
		font-size: 16px;
		font-weight: bold;
		margin-bottom: 20px;
	}

	.tab {
		display: flex;
		justify-content: space-around;
		margin: 12rpx;
		margin: 10px;
	}

	.tab-item {
		padding: 10px 20px;
		border-radius: 20px;
		text-align: center;
		flex: 1;
		margin: 0 5px;
		cursor: pointer;
		color: #ccc;
		font-size: 14px;
	}

	.tab-item.active {
		color: #000;
		font-weight: bold;
	}


	.product-info {
		padding: 20px;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
		margin: 10px;
		border-radius: 8px;
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		padding: 16px 0;
		border-bottom: 1px solid #f0f0f0;
	}

	.label {
		font-size: 14px;
		color: #555;
	}

	.picker {
		font-size: 14px;
		color: #000;
		display: flex;
		align-items: center;
	}

	.arrow {
		margin-left: 5px;
		color: #888;
	}

	.credit-selection {
		padding: 20px;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
		margin: 10px;
		border-radius: 8px;
	}

	.note {
		font-size: 12px;
		color: red;
	}

	.credit-options {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10px;
	}

	.radio-label {
		display: flex;
		align-items: center;
		font-size: 12px;
		margin-right: 10px;
		margin-bottom: 5px;
	}

	.exclusive {
		margin-left: 5px;
		background-color: #ff5a5f;
		color: #fff;
		padding: 2px 5px;
		border-radius: 5px;
	}

	.info-icon {
		margin-left: 5px;
		background-color: #007aff;
		color: #fff;
		padding: 2px 5px;
		border-radius: 50%;
		cursor: pointer;
	}

	.initial-payment-selection {
		padding: 20px;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
	}

	.payment-options {
		display: flex;
		justify-content: flex-start;
		margin-top: 10px;
		margin-right: 10px;
	}

	.payment-option {
		padding: 6px 10px;
		width: 120px;
		border: 1px solid #ccc;
		border-radius: 8px;
		text-align: center;
		margin: 0 5px;
		cursor: pointer;
		max-width: 200px;
		font-size: 16px
	}

	.payment-option.selected {
		background-color: #007aff;
		color: #fff;
		border-color: #007aff;
	}

	.lease-term-selection {
		padding: 20px;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
	}

	.lease-options {
		display: flex;
		justify-content: flex-start;
		margin-top: 10px;
	}

	.lease-option {
		padding: 6px 10px;
		width: 120px;
		border: 1px solid #ccc;
		border-radius: 8px;
		text-align: center;
		margin-right: 10px;

		margin: 0 5px;
		font-size: 16px;
		cursor: pointer;
	}

	.lease-option.selected {
		background-color: #007aff;
		color: #fff;
		border-color: #007aff;
	}

	.contract-price {
		padding: 20px;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
		display: flex;
		justify-content: space-between;
	}

	.contract-label {
		font-size: 16px;
		color: #555;
	}

	.contract-price {
		font-size: 16px;
		color: red;
	}

	.billing-details {
		padding: 20px;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
	}

	.billing-item {
		display: flex;
		justify-content: space-between;
		padding: 10px 0;
	}

	.billing-label {
		font-size: 14px;
		color: #555;
	}

	.billing-value {
		font-size: 14px;
		color: #000;
	}

	.next-step {
		text-align: center;
		padding: 20px;
		background-color: #fff;
	}

	button {
		width: 100%;
		background-color: #007aff;
		color: #fff;
		border: none;
		padding: 8px;
		border-radius: 8px;
		font-size: 14px;
		cursor: pointer;
	}

	button:active {
		background-color: #005bb5;
	}

	.popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.popup-content {
		background-color: #fff;
		padding: 20px;
		border-radius: 10px;
		text-align: center;
	}

	.popup-text {
		font-size: 14px;
		color: #333;
	}
</style>

<style lang="scss">
	.tab {
		.tab-item {
			position: relative;

			.active-after {
				width: 10px;
				height: 3px;
				background-color: #005bb5;
				position: absolute;
				right: 46%;
				border-radius: 8px;
				bottom: 0px;
			}
		}
	}
</style>
