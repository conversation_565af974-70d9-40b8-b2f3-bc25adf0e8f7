# PDF.js 使用指南

## 简介

PDF.js 是一个使用 HTML5 构建的可移植文档格式 (PDF) 查看器。它允许在不使用商业 PDF 插件的情况下，直接在网页中显示 PDF 文档。在本项目中，我们使用 PDF.js 来实现在各种平台（H5、小程序、APP）上预览 PDF 文件。

## 配置说明

### 1. H5环境

在 H5 环境中，我们使用 CDN 方式引入 PDF.js 库：

```javascript
// H5环境下使用CDN方式引入PDF.js
if (window) {
  const pdfjsScript = document.createElement('script');
  pdfjsScript.src = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.4.120/build/pdf.min.js';
  pdfjsScript.async = false;
  document.head.appendChild(pdfjsScript);
  
  // 等待PDF.js库加载完成
  pdfjsScript.onload = function() {
    if (window.pdfjsLib) {
      pdfjsLib = window.pdfjsLib;
      // 设置PDF.js工作线程路径
      pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.4.120/build/pdf.worker.min.js';
    }
  };
}
```

### 2. 非H5环境（小程序、APP等）

在非H5环境中，需要使用本地预构建的 PDF.js 文件：

```javascript
// 非H5环境尝试使用本地PDF.js库
try {
  pdfjsLib = require('./pdfjs/build/pdf.min.js');
} catch (error) {
  console.error('PDF.js导入失败:', error);
  pdfjsLib = null;
}
```

## 本地PDF.js文件获取方法

要使非H5环境正常工作，您需要下载预构建的PDF.js文件：

1. 访问 [PDF.js 发布页](https://github.com/mozilla/pdf.js/releases)
2. 下载最新的稳定版本 (例如: `pdfjs-3.4.120-dist.zip`)
3. 解压后，将以下文件复制到您的项目中：
   - `build/pdf.min.js` → 复制到 `pagesExa/filem/pdfjs/build/pdf.min.js`
   - `build/pdf.worker.min.js` → 复制到 `pagesExa/filem/pdfjs/build/pdf.worker.min.js`

## 使用方法

1. 初始化 PDF.js 查看器：

```javascript
initPdfViewer() {
  // 设置PDF.js工作线程路径
  if (pdfjsLib.GlobalWorkerOptions && !pdfjsLib.GlobalWorkerOptions.workerSrc) {
    pdfjsLib.GlobalWorkerOptions.workerSrc = './pdfjs/build/pdf.worker.min.js';
  }
  
  // 获取Canvas上下文
  const query = uni.createSelectorQuery().in(this);
  query.select('.pdf-canvas')
    .fields({ node: true, size: true })
    .exec((res) => {
      // 初始化Canvas
      this.canvasContext = res[0].node.getContext('2d');
      
      // 加载PDF文档
      this.loadPdf();
    });
}
```

2. 加载PDF文档：

```javascript
loadPdf() {
  const loadingOptions = {
    url: this.fileUrl,
    withCredentials: true
  };
  
  const loadingTask = pdfjsLib.getDocument(loadingOptions);
  
  loadingTask.promise
    .then(pdfDoc => {
      this.pdfDoc = pdfDoc;
      this.totalPages = pdfDoc.numPages;
      this.loading = false;
      
      // 渲染第一页
      this.renderPage(1);
    });
}
```

3. 渲染PDF页面：

```javascript
renderPage(pageNumber) {
  this.pdfDoc.getPage(pageNumber)
    .then(page => {
      // 调整Canvas大小
      const viewport = page.getViewport({ scale: this.scale });
      
      // 设置Canvas大小
      const canvas = this.canvasContext.canvas;
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      
      // 渲染PDF页面
      const renderContext = {
        canvasContext: this.canvasContext,
        viewport: viewport
      };
      
      page.render(renderContext).promise
        .then(() => {
          this.loading = false;
          this.currentPage = pageNumber;
        });
    });
}
```

## 常见问题

1. **PDF.js 库加载失败**
   - 检查网络连接是否正常
   - 确认CDN地址是否可用
   - 非H5环境检查本地PDF.js文件是否存在

2. **无法渲染PDF页面**
   - 检查PDF文件URL是否正确
   - 确认跨域问题是否已解决
   - 查看浏览器控制台是否有相关错误

3. **Worker文件加载失败**
   - 确保Worker文件路径正确设置
   - 检查Worker文件是否存在

## 参考资源

- [PDF.js 官方文档](https://mozilla.github.io/pdf.js/)
- [PDF.js GitHub 仓库](https://github.com/mozilla/pdf.js)
- [PDF.js CDN](https://cdn.jsdelivr.net/npm/pdfjs-dist/) 