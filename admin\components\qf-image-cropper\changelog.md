## 2.2.5（2024-07-30）
* 修复 当 checkRange=true 时，拖动四个伸缩角放大图片时还可能会超出或未到边界的问题
* 修复 当 checkRange=false 时，图片旋转时会放大图片适应裁剪尺寸的问题
* 修复 当 checkRange=true 时，图片旋转 90° 或 270° 进行缩放可能会无法拖动图片的问题
## 2.2.4（2024-06-21）
* 新增 reverseRotatable 属性，是否支持逆向翻转
* 修复 `2.1.7` 版本导致旋转后图片没有自动适配裁剪框的问题

## 2.2.3（2024-06-21）
* 新增 gpu 属性，是否开启硬件加速，图片缩放过程中如果出现元素的“留影”或“重影”效果，可通过该方式解决或减轻这一问题
* 修复 组件使用 `v-if` 并设置 `src` 属性时可能会出现图片渲染位置存在偏差的问题

## 2.2.2（2024-06-21）
* 优化 组件实例 chooseImage 方法支持传参
* 修复 组件使用 `v-if` 时组件无非正常渲染的问题

## 2.2.1（2024-06-15）
* 修复 H5平台不支持手势拖动图片的问题

## 2.2.0（2024-05-31）
* 修复 APP平台 `vue2` 项目因 `2.1.9` 版本修复 `vue3` 项目bug而引发的问题

## 2.1.9（2024-05-29）
* 修复 APP平台 `vue3` 项目因 uniapp `renderjs` 中未支持条件编译，导致运行了H5平台代码报错的问题

## 2.1.8（2024-05-29）
* 新增 zIndex 属性，调整组件层级
* 新增 组件内容插槽
* 优化 微信小程序平台动态修改元素style时的多余内容

## 2.1.7（2024-05-28）
* 新增 checkRange 属性，当 checkRange=false 时允许图片位置超出裁剪边界
* 新增 minScale 属性，图片最小缩放倍数，当 minScale<0 时可使图片宽高不再受裁剪区域宽高限制
* 新增 backgroundColor 属性，生成图片背景色，如果裁剪区域没有完全包含在图片中时，不设置该属性生成图片存在一定的透明块
* 优化 动态修改图片宽高但没有传入src时，尺寸适应问题
* 修复 APP平台通过 `this.$ownerInstance` 获取组件实例时机过早，其值为 `undefined` 导致报错界面没有正常渲染的问题

## 2.1.6（2023-04-16）
* 修复 组件使用 v-show 指令会导致选择图片后初始位置严重偏位的问题

## 2.1.5（2023-04-15）
* 新增 兼容APP平台

## 2.1.4（2023-03-13）
* 新增 fileType 属性，用于指定生成文件的类型，只支持 'jpg' 或 'png'，默认为 'png'
* 新增 delay 属性，微信小程序平台使用 `Canvas 2D` 绘制时控制图片从绘制到生成所需时间
* 优化 当生成图片的尺寸宽/高超过 Canvas 2D 最大限制（1365*1365）则将画布尺寸缩放在限制范围内绘制完成后输出目标尺寸
* 优化 旋转图标指示方向与实际旋转方向不符

## 2.1.3（2023-02-06）
* 优化 vue3支持

## 2.1.2（2023-02-03）
* 新增 navigation 属性，H5平台当 showAngle 为 true 时，使用插件的页面在 `page.json` 中配置了 "navigationStyle": "custom" 时，必须将此值设为 false ，否则四个可拉伸角的触发位置会有偏差
* 修复 H5平台部分设备（已知iPhone11以下机型）拍照的图片缩放时会闪动的问题

## 2.1.1（2022-12-06）
* 修复 横屏适配问题

## 2.1.0（2022-12-06）
* 新增 兼容H5平台，使用 renderjs 响应手势事件

## 2.0.0（2022-12-05）
* 重构 插件，使用 WXS 响应手势事件
* 新增 图片翻转
* 新增 拉伸裁剪框放大图片
* 新增 监听PC鼠标滚轮触发缩放
* 新增 圆形、圆角矩形的图片裁剪
* 优化 图片缩放，移动端以双指触摸中心点为缩放中心点，PC端以鼠标所在点为缩放中心点
* 优化 裁剪框样式
* 优化 图片位置拖动 支持边界回弹效果（滑动时可滑出边界，释放时回弹到边界）
* 优化 生成图片使用新版 Canvas 2D 接口
