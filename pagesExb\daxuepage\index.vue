<template>
	<view>
		<block v-if="isdiy">
			<view :style="'display:flex;min-height: 100vh;flex-direction: column;background-color:' + pageinfo.bgcolor">
				<view class="container">
					<!-- <dp :pagecontent="pagecontent" :menuindex="menuindex"></dp> -->
				</view>
			</view>
			<dp-guanggao :guanggaopic="guanggaopic" :guanggaourl="guanggaourl"></dp-guanggao>
		</block>
		<block v-else>
			<view v-if="switchIndex == 0">
				<!-- 头部标题 -->
				<view class="flex" style="padding: 30rpx;padding-right: 0;padding-bottom: 0;">
					<view style="margin-right: 10rpx;" v-if="business">
						<image :src="business.logo" style="width: 130rpx;height: 130rpx;" alt="" />
					</view>
					<view style="flex: 1;" v-if="business">
						<view class="text-h2 text-bold" style="margin-bottom: 10rpx;">
							{{business.name}}
						</view>
						<view class="flex">
							<view v-for="item in business.biaoqian_names" class="tag-item">
								{{item}}
							</view>
						</view>
					</view>
					<view>
						<view class="bg-blue flex align-center" style="margin-bottom: 10rpx;" @click="addfavorite">
							<view style="margin-right: 10rpx;">
								<image v-if="isfavorite" :src="pre_url+'/static/icon/collect.png'" style="width: 40rpx;height: 40rpx;" mode="">
									<image v-else :src="pre_url+'/static/icon/notCollect.png'" style="width: 40rpx;height: 40rpx;" mode=""></image>
								</image>
							</view>
							<view>
								收藏
							</view>
						</view>
						<button class="bg-blue flex align-center" open-type="share" style="height: 28.86px;">
							<view style="margin-right: 10rpx;line-height: 0px;">
								<image :src="pre_url+'/static/icon/share2.png'" style="width: 40rpx;height: 40rpx;" mode=""></image>
							</view>
							<view>
								分享
							</view>
						</button>
					</view>
				</view>

				<view class="box">
					<!-- 文章 -->
					<view class="mb30">
						<view class="article-item" v-for="item in articles" @tap="goto"
							:data-url="'/pagesExa/daxuepage/articledetail?id=' + item.id">
							<view class="flex align-center">
								<view style="margin-right: 10rpx;" class="text-bold">
									{{item.name}}
								</view>
								<view>
									<image :src="pre_url+'/static/icon/fire.png'" style="width: 40rpx;height: 40rpx;" mode=""></image>
								</view>
							</view>
							<view>
								<image :src="pre_url+'/static/icon/right.png'" style="width: 20rpx;height: 20rpx;" mode=""></image>
							</view>
						</view>
					</view>

					<!-- 学校简介 -->
					<view class="mb30">
						<view class="text-h2 text-bold mb30">
							学校简介
						</view>

						<view class="bg-grey flex align-center" @tap="goto" :data-url="'/pagesExa/daxuepage/schoolBlurb?id=' + opt.id">
							<view class="text-overflow">
								{{business.desc}}
							</view>
							<view>
								<image :src="pre_url+'/static/icon/right.png'" style="width: 20rpx;height: 20rpx;" mode=""></image>
							</view>
						</view>
					</view>

					<!-- 基本信息 -->
					<view class="mb30">
						<view class="text-h2 text-bold mb30">
							基本信息
						</view>

						<view class="flex align-center" style="margin-bottom: 40rpx;">
							<view class="flex align-center" style="flex: 1;">
								<view class="icon">
									<image :src="pre_url+'/static/icon/time.png'" style="width: 50rpx;height: 50rpx;" mode=""></image>
								</view>
								<view>
									<view class="text-h3 text-bold">
										{{business.established_year}} 年
									</view>
									<view style="font-size: 20rpx;color: #dddddd;">
										建校时间
									</view>
								</view>
							</view>

							<view class="flex align-center" style="flex: 1;">
								<view class="icon">
									<image :src="pre_url+'/static/icon/area.png'" style="width: 50rpx;height: 50rpx;" mode=""></image>
								</view>
								<view>
									<view class="text-h3 text-bold">
										{{business.campus_area}} 亩
									</view>
									<view style="font-size: 20rpx;color: #dddddd;">
										占地面积
									</view>
								</view>
							</view>
						</view>

						<view class="flex align-center">
							<view class="flex align-center" style="flex: 1;">
								<view class="icon">
									<image :src="pre_url+'/static/icon/building.png'" style="width: 50rpx;height: 50rpx;" mode=""></image>
								</view>
								<view>
									<view class="text-h3 text-bold">
										{{business.department_count}} 个
									</view>
									<view style="font-size: 20rpx;color: #dddddd;">
										院系数量
									</view>
								</view>
							</view>

							<view class="flex align-center" style="flex: 1;">
								<view class="icon">
									<image :src="pre_url+'/static/icon/speciality.png'" style="width: 50rpx;height: 50rpx;" mode=""></image>
								</view>
								<view>
									<view class="text-h3 text-bold">
										{{business.major_count}} 个
									</view>
									<view style="font-size: 20rpx;color: #dddddd;">
										专业数量
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 院校环境 -->
					<view class="mb30" v-if="pics.length">
						<view class="text-h2 text-bold mb30">
							院校环境
						</view>

						<scroll-view scroll-x="true" style="white-space: nowrap;width: 100%;">
							<view
								style="border-radius: 30rpx;overflow: hidden;margin-right: 20rpx;width: 340rpx;height: 200rpx;display: inline-block;"
								v-for="(item,index) in pics" @click="fnPreviewImage(index)">
								<image :src="item" style="width: 100%;height: 100%;" alt="" />
							</view>
						</scroll-view>
					</view>

					<!-- 校区地址 -->
					<view class="mb30">
						<view class="text-h2 text-bold mb30">
							校区地址
						</view>
						<view style="position: relative;">
							<map style="width: 100%; height: 300rpx;" :latitude="business.latitude"
								:longitude="business.longitude" :scale="12">
							</map>
							<view class="map-address">
								校区地址：{{business.city}}{{business.district}} {{business.address}}
							</view>
						</view>
					</view>

				</view>
			</view>

			<!-- 开设专业 -->
			<view v-if="switchIndex == 1">
				<speciality :daxueid="opt.id" />
			</view>

			<!-- 招生资料 -->
			<view v-if="switchIndex == 2">
				<view class="mb30">
					<view class="article-item" v-for="item in articles" @tap="goto"
						:data-url="'/pagesExa/daxuepage/articledetail?id=' + item.id">
						<view class="flex align-center">
							<view style="margin-right: 10rpx;" class="text-bold">
								{{item.name}}
							</view>
							<view>
								<image :src="pre_url+'/static/icon/fire.png'" style="width: 40rpx;height: 40rpx;" mode=""></image>
							</view>
						</view>
						<view>
							<image :src="pre_url+'/static/icon/right.png'" style="width: 20rpx;height: 20rpx;" mode=""></image>
						</view>
					</view>
					<nodata v-if="articles.length == 0"></nodata>
				</view>
			</view>

			<!-- 分数线 -->
			<view v-if="switchIndex == 3">
				<fractional-line :daxueid="opt.id" />
			</view>

			<!-- 底部菜单 -->
			<view class="tabbar">
				<view class="tabbar-item" v-for="(item, index) in tabbarList" :key="index" @click="switchTab(index)">
					<view>
						<image :src="item.icon" style="width: 40rpx;height: 40rpx;" mode=""></image>
					</view>
					<view>
						<text>{{ item.text }}</text>
					</view>
				</view>
			</view>

		</block>
		<loading v-if="loading"></loading>

		<!-- #ifdef MP-TOUTIAO -->
		<view class="dp-cover" v-if="video_status">
			<button open-type="share" data-channel="video" class="dp-cover-cover" :style="{
			zIndex:10,
			top:'60vh',
			left:'80vw',
			width:'110rpx',
			height:'110rpx'
		}">
				<image :src="pre_url+'/static/img/uploadvideo2.png'" :style="{width:'110rpx',height:'110rpx'}" />
			</button>
		</view>
		<!-- #endif -->

		<!-- <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar> -->
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	import speciality from './components/speciality.vue'
	import fractionalLine from './components/line.vue'
	var app = getApp();
	export default {
		components: {
			speciality,
			fractionalLine
		},
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				pre_url: app.globalData.pre_url,

				isdiy: 0,

				st: 0,
				business: [],
				countcomment: 0,
				couponcount: 0,
				pics: [],
				pagenum: 1,
				datalist: [],
				topbackhide: false,
				nomore: false,
				nodata: false,

				title: "",
				sysset: "",
				guanggaopic: "",
				guanggaourl: "",
				pageinfo: "",
				pagecontent: "",
				showfw: false,
				yuyue_clist: [],
				yuyue_cid: 0,
				video_status: 0,
				video_title: '',
				video_tag: [],
				bset: '',
				isfavorite: false,
				articles: [], // 文章
				tabbarList: [{
						icon: require('@/static/tabbar/menu.png'),
						text: '学校主页'
					},
					{
						icon: require('@/static/tabbar/menu.png'),
						text: '开设专业'
					},
					{
						icon: require('@/static/tabbar/menu.png'),
						text: '招生资料'
					},
					{
						icon: require('@/static/tabbar/menu.png'),
						text: '分数线'
					}
				], // 底部菜单
				switchIndex: 0
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.opt.bid = this.opt.id;
			this.st = this.opt.st || 0;
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onReachBottom: function() {
			if (this.isdiy == 0) {
				if (!this.nodata && !this.nomore) {
					this.pagenum = this.pagenum + 1;
					this.getDataList(true);
				}
			}
		},
		onPageScroll: function(e) {
			uni.$emit('onPageScroll', e);
		},
		onShareAppMessage: function() {
			//#ifdef MP-TOUTIAO
			console.log(shareOption);
			return {

				title: this.video_title,
				channel: "video",
				extra: {
					hashtag_list: this.video_tag,
				},
				success: () => {
					console.log("分享成功");
				},
				fail: (res) => {
					console.log(res);
					// 可根据 res.errCode 处理失败case
				},
			};
			//#endif
			return this._sharewx({
				title: this.business.name
			});
		},
		onPageScroll: function(e) {
			if (this.isdiy == 0) {
				var that = this;
				var scrollY = e.scrollTop;
				if (scrollY > 200 && !that.topbackhide) {
					that.topbackhide = true;
				}
				if (scrollY < 150 && that.topbackhide) {
					that.topbackhide = false;
				}
			}
		},
		methods: {
			getdata: function() {
				var that = this;
				var id = that.opt.id || 0;
				that.loading = true;
				app.get('ApiDaxue/index', {
					id: id
				}, function(res) {
					that.loading = false;
					that.isdiy = res.isdiy;
					that.business = res.daxue;
					that.countcomment = res.countcomment;
					that.couponcount = res.couponcount;
					that.pics = res.pics;
					var bset = res.bset;
					that.bset = bset;
					that.articles = res.articles
					if (bset) {
						if (bset.show_product) {
							that.st = 0;
						} else if (bset.show_comment) {
							that.st = 1;
						} else if (bset.show_detail) {
							that.st = 2;
						}
					}

					that.guanggaopic = res.guanggaopic;
					that.guanggaourl = res.guanggaourl;
					that.pageinfo = res.pageinfo;
					that.pagecontent = res.pagecontent;
					that.sysset = res.sysset;
					that.showfw = res.showfw || false;
					that.isfavorite = that.business.isfavorite
					if (that.showfw) {
						that.st = -1;
						that.yuyue_clist = res.yuyue_clist;
					}
					if (res.yuyueset) {
						that.video_status = res.yuyueset.video_status;
						that.video_title = res.yuyueset.video_title;
						that.video_tag = res.yuyueset.video_tag;
					}

					that.loaded({
						title: that.business.name,
						pic: that.business.logo
					});

					if (res.isdiy == 0) {
						that.isload = 1;
						uni.setNavigationBarTitle({
							title: that.business.name
						});
						that.getDataList();
					} else {
						if (res.status == 2) {
							//付费查看
							app.goto('/pages/pay/pay?fromPage=index&id=' + res.payorderid + '&pageid=' + that
								.res.id, 'redirect');
							return;
						}
						if (res.status == 1) {
							var pagecontent = res.pagecontent;
							that.isdiy = 1;

							that.title = res.pageinfo.title;
							that.sysset = res.sysset;
							that.guanggaopic = res.guanggaopic;

							that.guanggaourl = res.guanggaourl;
							that.pageinfo = res.pageinfo;
							that.pagecontent = pagecontent;
							uni.setNavigationBarTitle({
								title: res.pageinfo.title
							});
						} else {
							app.alert(res.msg);
						}
					}
				});
			},
			changetab: function(e) {
				var st = e.currentTarget.dataset.st;
				this.pagenum = 1;
				this.st = st;
				this.datalist = [];
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				this.getDataList();
			},
			getDataList: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var st = that.st;
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
				app.post('ApiDaxue/getdatalist', {
					id: that.business.id,
					st: st,
					pagenum: pagenum,
					yuyue_cid: that.yuyue_cid
				}, function(res) {
					that.loading = false;
					uni.stopPullDownRefresh();
					var data = res.data;
					if (pagenum == 1) {
						that.datalist = data;
						if (data.length == 0) {
							that.nodata = true;
						}
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}
				});
			},
			openLocation: function(e) {
				//console.log(e)
				var latitude = parseFloat(e.currentTarget.dataset.latitude)
				var longitude = parseFloat(e.currentTarget.dataset.longitude)
				var address = e.currentTarget.dataset.address
				uni.openLocation({
					latitude: latitude,
					longitude: longitude,
					name: address,
					scale: 13
				})
			},
			phone: function(e) {
				var phone = e.currentTarget.dataset.phone;
				uni.makePhoneCall({
					phoneNumber: phone,
					fail: function() {}
				});
			},
			//改变子分类
			changeyuyueCTab: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				this.nodata = false;
				this.yuyue_cid = id;
				this.pagenum = 1;
				this.datalist = [];
				this.nomore = false;
				this.getDataList();
			},
			// 图片预览
			fnPreviewImage: function(index) {
				uni.previewImage({
					current: index,
					urls: this.pics,
					loop: true,
					longPressActions: true
				})
			},
			//收藏操作
			addfavorite: function() {
				var that = this;
				app.post('ApiDaxue/addfavorite', {
					proid: that.business.id,
					type: 'daxue'
				}, function(data) {
					if (data.status == 1) {
						that.isfavorite = !that.isfavorite;
					}
					app.success(data.msg);
				});
			},
			// 跳转
			switchTab: function(index) {
				this.switchIndex = index
			}
		}
	}
</script>
<style>
	.container2 {
		padding: 16px;
		width: 95%;
		background-color: #f5f5f5;

	}

	.container2 {
		padding: 10px;
		width: 100%;
		background-color: #f5f5f5;
		margin-top: -10px;
	}

	.header {
		font-size: 18px;
		font-weight: bold;
		padding-bottom: 16px;
	}

	.card {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #ffffff;
		border-radius: 8px;
		padding: 16px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		width: 100%;
		box-sizing: border-box;
	}

	.content {
		display: flex;
		flex-direction: column;
	}

	.discount-text {
		font-size: 16px;
		font-weight: bold;
	}

	.highlight {
		color: #ff0000;
	}

	.sub-text {
		font-size: 14px;
		color: #666666;
		margin-top: 8px;
	}

	.pay-button {
		background-color: #ff4d4f;
		color: #ffffff;
		border: none;
		border-radius: 10px;
		padding: 4px 16px;
		font-size: 14px;
		cursor: pointer;
		margin-left: auto;
		margin-right: -10px;
	}

	.container {
		position: relative
	}

	.nodiydata {
		display: flex;
		flex-direction: column
	}

	.nodiydata .swiper {
		width: 100%;
		height: 400rpx;
		position: relative;
		z-index: 1
	}

	.nodiydata .swiper .image {
		width: 100%;
		height: 400rpx;
		overflow: hidden;
	}

	.nodiydata .topcontent {
		width: 94%;
		margin-left: 3%;
		padding: 24rpx;
		border-bottom: 1px solid #eee;
		margin-bottom: 20rpx;
		background: #fff;
		margin-top: -120rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 16rpx;
		position: relative;
		z-index: 2;
	}

	.nodiydata .topcontent .logo {
		width: 160rpx;
		height: 160rpx;
		margin-top: -104rpx;
		border: 2px solid rgba(255, 255, 255, 0.5);
		border-radius: 50%;
	}

	.nodiydata .topcontent .logo .img {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.nodiydata .topcontent .title {
		color: #222222;
		font-size: 36rpx;
		font-weight: bold;
		margin-top: 12rpx
	}

	.nodiydata .topcontent .desc {
		display: flex;
		align-items: center
	}

	.nodiydata .topcontent .desc .f1 {
		margin: 20rpx 0;
		font-size: 24rpx;
		color: #FC5648;
		display: flex;
		align-items: center
	}

	.nodiydata .topcontent .desc .f1 .img {
		width: 24rpx;
		height: 24rpx;
		margin-right: 10rpx;
	}

	.nodiydata .topcontent .desc .f2 {
		margin: 10rpx 0;
		padding-left: 60rpx;
		font-size: 24rpx;
		color: #999;
	}

	.nodiydata .topcontent .tel {
		font-size: 28rpx;
		color: #fff;
		padding: 16rpx 40rpx;
		border-radius: 60rpx;
		font-weight: normal
	}

	.nodiydata .topcontent .tel .img {
		width: 28rpx;
		height: 28rpx;
		vertical-align: middle;
		margin-right: 10rpx
	}

	.nodiydata .topcontent .address {
		width: 100%;
		display: flex;
		align-items: center;
		padding-top: 20rpx
	}

	.nodiydata .topcontent .address .f1 {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx
	}

	.nodiydata .topcontent .address .f2 {
		flex: 1;
		color: #999999;
		font-size: 26rpx
	}

	.nodiydata .topcontent .address .f3 {
		display: inline-block;
		width: 26rpx;
		height: 26rpx
	}

	.nodiydata .contentbox {
		width: 94%;
		margin-left: 3%;
		background: #fff;
		border-radius: 16rpx;
		margin-bottom: 32rpx;
		overflow: hidden
	}

	.nodiydata .shop_tab {
		display: flex;
		width: 100%;
		height: 90rpx;
		border-bottom: 1px solid #eee;
	}

	.nodiydata .shop_tab .cptab_text {
		flex: 1;
		text-align: center;
		color: #646566;
		height: 90rpx;
		line-height: 90rpx;
		position: relative
	}

	.nodiydata .shop_tab .cptab_current {
		color: #323233;
	}

	.nodiydata .shop_tab .after {
		display: none;
		position: absolute;
		left: 50%;
		margin-left: -16rpx;
		bottom: 10rpx;
		height: 3px;
		border-radius: 1.5px;
		width: 32rpx
	}

	.nodiydata .shop_tab .cptab_current .after {
		display: block;
	}


	.nodiydata .cp_detail {
		min-height: 500rpx
	}

	.nodiydata .comment .item {
		background-color: #fff;
		padding: 10rpx 20rpx;
		display: flex;
		flex-direction: column;
	}

	.nodiydata .comment .item .f1 {
		display: flex;
		width: 100%;
		align-items: center;
		padding: 10rpx 0;
	}

	.nodiydata .comment .item .f1 .t1 {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
	}

	.nodiydata .comment .item .f1 .t2 {
		padding-left: 10rpx;
		color: #333;
		font-weight: bold;
		font-size: 30rpx;
	}

	.nodiydata .comment .item .f1 .t3 {
		text-align: right;
	}

	.nodiydata .comment .item .f1 .t3 .img {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10rpx
	}

	.nodiydata .comment .item .score {
		font-size: 24rpx;
		color: #f99716;
	}

	.nodiydata .comment .item .score image {
		width: 140rpx;
		height: 50rpx;
		vertical-align: middle;
		margin-bottom: 6rpx;
		margin-right: 6rpx;
	}

	.nodiydata .comment .item .f2 {
		display: flex;
		flex-direction: column;
		width: 100%;
		padding: 10rpx 0;
	}

	.nodiydata .comment .item .f2 .t1 {
		color: #333;
		font-size: 28rpx;
	}

	.nodiydata .comment .item .f2 .t2 {
		display: flex;
		width: 100%
	}

	.nodiydata .comment .item .f2 .t2 image {
		width: 100rpx;
		height: 100rpx;
		margin: 10rpx;
	}

	.nodiydata .comment .item .f2 .t3 {
		color: #aaa;
		font-size: 24rpx;
	}

	.nodiydata .comment .item .f2 .t3 {
		color: #aaa;
		font-size: 24rpx;
	}

	.nodiydata .comment .item .f3 {
		width: 100%;
		padding: 10rpx 0;
		position: relative
	}

	.nodiydata .comment .item .f3 .arrow {
		width: 16rpx;
		height: 16rpx;
		background: #eee;
		transform: rotate(45deg);
		position: absolute;
		top: 0rpx;
		left: 36rpx
	}

	.nodiydata .comment .item .f3 .t1 {
		width: 100%;
		border-radius: 10rpx;
		padding: 10rpx;
		font-size: 22rpx;
		color: #888;
		background: #eee
	}

	.nodiydata .nomore-footer-tips {
		background: #fff !important
	}

	.nodiydata .covermy {
		position: fixed;
		z-index: 99999;
		cursor: pointer;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		overflow: hidden;
		z-index: 9999;
		top: 81vh;
		left: 82vw;
		color: #fff;
		background-color: rgba(92, 107, 129, 0.6);
		width: 110rpx;
		height: 110rpx;
		font-size: 26rpx;
		border-radius: 50%;
	}


	.classify-ul {
		width: 100%;
		height: 70rpx;
		padding: 0 10rpx;
	}

	.classify-li {
		flex-shrink: 0;
		display: flex;
		background: #F5F6F8;
		border-radius: 22rpx;
		color: #6C737F;
		font-size: 20rpx;
		text-align: center;
		height: 44rpx;
		line-height: 44rpx;
		padding: 0 28rpx;
		margin: 12rpx 10rpx 12rpx 0
	}

	.dp-cover {
		height: auto;
		position: relative;
	}

	.dp-cover-cover {
		position: fixed;
		z-index: 99999;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
		background-color: inherit;
	}
</style>

<style>
	page {
		background-color: #fff;
	}

	.box {
		padding: 30rpx;
	}

	.flex {
		display: flex;
	}

	.text-bold {
		font-weight: bold
	}

	.justify-center {
		justify-content: center;
	}

	.justify-between {
		justify-content: space-between;
	}

	.align-center {
		align-items: center;
	}

	.text-h2 {
		color: #000;
		font-size: 34rpx;
	}

	.mb30 {
		margin-bottom: 30rpx;
	}

	.text-h3 {
		color: #000;
		font-size: 30rpx;
	}

	.tag-item {
		background-color: #ededed;
		color: #b7b7b7;
		font-size: 24rpx;
		border-radius: 30rpx;
		padding: 6rpx 14rpx;
		margin-right: 10rpx;
	}

	.bg-blue {
		border-radius: 30rpx 0 0 30rpx;
		background-color: #19a7ff;
		color: #fff;
		padding: 6rpx 14rpx;
		font-size: 24rpx;
	}

	.icon {
		margin-right: 20rpx;
		width: 100rpx;
		height: 100rpx;
		text-align: center;
		line-height: 130rpx;
		background-color: #ededed;
		border-radius: 10rpx;
	}

	.text-overflow {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: normal;
	}

	.bg-grey {
		border-radius: 20rpx;
		background-color: #ededed;
		color: #515151;
		font-size: 24rpx;
		padding: 20rpx;
	}

	.article-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
		font-size: 24rpx;
	}

	.map-address {
		position: absolute;
		background-color: rgba(169, 169, 169, 0.8);
		padding: 10rpx 6rpx;
		color: #fff;
		width: 60%;
		height: 100rpx;
		left: 20%;
		top: 35%;
		border-radius: 10rpx;
	}

	.tabbar {
		position: fixed;
		display: flex;
		align-items: center;
		flex-direction: row;
		width: 100%;
		height: 110rpx;
		bottom: 10rpx;
		border-radius: 30rpx;
		background: #fafafa;
		font-size: 24rpx;
		color: #999;
		border-top: 1px solid #efefef;
		z-index: 999999;
		box-sizing: content-box;
	}

	.tabbar-item {
		flex: 1;
		text-align: center;
		color: #666;
	}

	.tabbar-item text:first-child {
		font-size: 48rpx;
		/* 根据设计调整 */
	}

	.tabbar-item text:last-child {
		font-size: 24rpx;
		/* 根据设计调整 */
	}
</style>