<template>
	<view class="live-gift">
		<!-- 礼物动画区域 -->
		<view class="gift-animation-area">
			<view 
				class="gift-item" 
				v-for="(item, index) in activeGifts" 
				:key="item.id"
				:style="{ bottom: index * 100 + 'rpx' }"
				:class="{ 'gift-show': item.show }"
			>
				<view class="gift-content">
					<image class="sender-avatar" :src="item.avatar" mode="aspectFill"></image>
					<view class="gift-info">
						<text class="sender-name">{{item.nickname}}</text>
						<text class="gift-name">送出 {{item.giftName}}</text>
					</view>
					<image class="gift-image" :src="item.giftImage" mode="aspectFit"></image>
					<view class="gift-count" v-if="item.count > 1">
						<text>x{{item.count}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 用户入场动画 -->
		<view class="welcome-animation" v-if="welcomeInfo.show">
			<view class="welcome-content">
				<text class="welcome-text">欢迎</text>
				<text class="user-name">{{welcomeInfo.nickname}}</text>
				<text class="welcome-text">进入直播间</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			activeGifts: [],
			giftQueue: [],
			isPlaying: false,
			welcomeInfo: {
				show: false,
				nickname: ''
			}
		}
	},
	
	methods: {
		// 发送礼物
		sendGift(gift) {
			this.giftQueue.push({
				id: Date.now(),
				nickname: gift.nickname || '用户昵称',
				avatar: gift.avatar || '/static/images/default-avatar.png',
				giftName: gift.name || '礼物',
				giftImage: gift.image || '/static/images/default-gift.png',
				count: gift.count || 1,
				show: false
			})
			
			if (!this.isPlaying) {
				this.playNextGift()
			}
		},
		
		// 播放下一个礼物动画
		playNextGift() {
			if (this.giftQueue.length === 0) {
				this.isPlaying = false
				return
			}
			
			this.isPlaying = true
			const gift = this.giftQueue.shift()
			gift.show = true
			
			// 最多同时显示3个礼物
			if (this.activeGifts.length >= 3) {
				this.activeGifts.shift()
			}
			
			this.activeGifts.push(gift)
			
			// 3秒后移除礼物动画
			setTimeout(() => {
				const index = this.activeGifts.findIndex(item => item.id === gift.id)
				if (index > -1) {
					this.activeGifts.splice(index, 1)
				}
				this.playNextGift()
			}, 3000)
		},
		
		// 显示用户入场动画
		showWelcome(nickname) {
			this.welcomeInfo = {
				show: true,
				nickname
			}
			
			setTimeout(() => {
				this.welcomeInfo.show = false
			}, 2000)
		}
	}
}
</script>

<style lang="scss">
.live-gift {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 200rpx;
	pointer-events: none;
	
	.gift-animation-area {
		position: relative;
		height: 300rpx;
		
		.gift-item {
			position: absolute;
			left: 20rpx;
			transform: translateX(-100%);
			transition: all 0.3s ease;
			opacity: 0;
			
			&.gift-show {
				transform: translateX(0);
				opacity: 1;
			}
			
			.gift-content {
				display: flex;
				align-items: center;
				background: rgba(0, 0, 0, 0.6);
				border-radius: 100rpx;
				padding: 10rpx;
				
				.sender-avatar {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
					margin-right: 10rpx;
				}
				
				.gift-info {
					margin-right: 20rpx;
					
					.sender-name {
						color: #FFD700;
						font-size: 24rpx;
						display: block;
					}
					
					.gift-name {
						color: #fff;
						font-size: 24rpx;
					}
				}
				
				.gift-image {
					width: 60rpx;
					height: 60rpx;
					margin-right: 10rpx;
				}
				
				.gift-count {
					background: linear-gradient(to right, #FF4656, #FF8C9A);
					border-radius: 30rpx;
					padding: 4rpx 20rpx;
					
					text {
						color: #fff;
						font-size: 24rpx;
						font-weight: bold;
					}
				}
			}
		}
	}
	
	.welcome-animation {
		position: absolute;
		left: 20rpx;
		bottom: 0;
		animation: welcomeSlideIn 0.3s ease;
		
		.welcome-content {
			background: rgba(0, 0, 0, 0.6);
			border-radius: 100rpx;
			padding: 10rpx 30rpx;
			display: flex;
			align-items: center;
			
			.welcome-text {
				color: rgba(255, 255, 255, 0.8);
				font-size: 24rpx;
			}
			
			.user-name {
				color: #FFD700;
				font-size: 24rpx;
				margin: 0 10rpx;
			}
		}
	}
}

@keyframes welcomeSlideIn {
	from {
		transform: translateX(-100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}
</style> 