
<template>
	<view>
		<view class="head" :style="'background:'+secondary_color">
			
			<view class="openingMessage_t">{{book.eName}}</view>
			
			<view class="openingMessage">

				<view class="openingMessage_d" v-for="(item, index) in select" :key="index">{{item.cen}}楼  {{item.y}}排{{item.x}}号  ￥{{item.price}}</view>

			</view>
			<view class="openingPrompt" :style="'background:'+primary_color">
				<view class="spaceBetween">
	<!-- 				<image src="@/static/img/mistake.png" class="openingPrompt_img"></image>
					<view class="openingPrompt_txt1">不支持退款</view> -->
					<image :src="pre_url+'/static/img/mistake.png'" class="openingPrompt_img"></image>
					<view class="openingPrompt_txt2">不支持改签</view>
				</view>
				<view class="openingPrompt_txt3">订场必读></view>
			</view>
		</view>
		<view class="content">
			
<!-- 			<view style="padding-top: 30rpx;">
				<view class="specialOffer"> 
		   	<view class="sl_title">预约信息</view>
				<view class="spaceBetween">
					<view class="sl_txt1_l">姓名</view>
					<view class="sl_txt1_r">
			            <input type="text" placeholder="请输入" v-model="name" class="custom-input" />
					</view>
				</view>
				<view class="spaceBetween" style="padding-top: 30rpx;">    
					<view class="sl_txt1_l">手机号码</view>
					<view class="sl_txt1_r">
						<input type="number" placeholder="请输入" maxlength="11" v-model="phone" class="custom-input" />
					</view>      
				</view>     
			  </view>
			 </view> -->
					
			
			<view style="padding-top: 30rpx;">
				<view class="specialOffer"> 
					<view class="sl_title">优惠活动</view>
					<view class="spaceBetween">
						<view class="sl_txt1_l">优惠券</view>
						<view class="sl_txt1_r">
							<view class="sl_txt1_r_a">无可用</view>
							<view class="sl_txt1_r_b">></view>
						</view>
					</view>
					<view class="spaceBetween" style="margin-top: 20rpx; border-top: 1rpx solid #dcdcdc;padding-top: 20rpx;" @click="showList">
						<view class="sl_txt1_l">套票活动</view>
						<view class="sl_txt1_r">
							<view class="sl_txt1_r_a" style="color: #EB7471;">-￥{{yh}}</view>
							<view class="sl_txt1_r_b">></view>
						</view>
					</view>
				</view>
			</view>
		</view>
<!-- 		<view class="discountCoupon">
			<view class="discountCoupon_title">
				<view class="discountCoupon_title_l alignItems">
					<image src="@/static/img/gift.png" class="discountCoupon_title_img"></image>
					<view class="discountCoupon_title_txt">百万补贴</view>
				</view>
				<view class="discountCoupon_title_r">球拍48小时内发货</view>
			</view>
			<view class="discountCoupon_content">
				<view class="discountCoupon_txt">
					100元定场券（本单可减20元）+一直趣运动联合研发全碳素球拍
				</view>
				<view></view>
				<view class="discountCoupon_txt1">
					支付成功后填写收货地址
					<text style="color: #999999; font-size: 20px;">❓</text>
				</view>
			</view>
		</view> -->
		<view class="bottom spaceBetween" style="padding: 20rpx;">
			<view>
				<view>
					<text class="bottom_txt1">合计</text>
					<text class="bottom_txt2" :style="'color:'+primary_color">{{money}}</text>
			<!-- 		<text class="bottom_txt3">明细∧</text> -->
				</view>
				<view class="bottom_txt4">已优惠{{yh}}</view>
			</view>
			<view class="bt" @tap="submit" :style="'background:'+primary_color">立即支付</view>
		</view>
		
		
		<uni-popup id="dialogShowCategory" ref="dialogShowCategory"  type="bottom" :mask-click="true">
		
		    <view class="view-main">
				  
		          <view class="view-title">套票活动<image class="icon" :src="pre_url+'/static/img/close.png'" @click="hideDialog()"></view>
			
				  <scroll-view scroll-y style="height: 600rpx;">
					  
					  <view  v-for="item,index in packs" :key="index">

						   <view v-if="item.num>0" style="display: flex;justify-content: space-between;padding-top: 10px;">  
						      <text>{{item.group_size}}人套餐 X {{item.num}}</text> 
						      <text style="color: #EB7471;">-￥{{item.total}}</text>
						   </view> 
						    
					  </view>
					  
				  </scroll-view>

			  </view>
			  
		  
		</uni-popup> 
		
	</view>
</template>

<script>
	
	var app = getApp();
	
	export default {
		data() {
			return {
               book : '',
			   select: [],
			   phone : '',
			   name : '',
			   money : 0,
			   primary_color : '',
			   secondary_color : '',
			   packs : [],
			   yh : 0,
			   pre_url: ''  // 添加云资源域名前缀
			}
		},
		onLoad(opt) {
			
			this.book = JSON.parse(opt.book);
			
			this.select = JSON.parse(opt.select);  
			
			this.packs = JSON.parse(opt.packs);  
			
			this.pre_url = app.globalData.pre_url || '';  // 初始化云资源域名前缀
			
			let num = 0;
			this.packs.filter(item=>{
				if(item.num>0){
					num+= item.total;
				}
			})
			

			this.yh = Math.round(num*100)/100; 
			
			this.money = opt.money;

			uni.setNavigationBarTitle({
				title: opt.title
			})
	
			this.primary_color = app.getCache('primary_color')
			
			this.secondary_color = app.getCache('secondary_color')
			
			
		},
		methods: {
            submit(){
				
				// if(this.name == ''){
					
				// 	uni.showToast({
				// 		icon:'none',
				// 		title:'请输入姓名'
				// 	})
					
				// 	return
				// }
				
				
				// if(this.phone == ''){
					
				// 	uni.showToast({
				// 		icon:'none',
				// 		title:'请输入手机号码'
				// 	})
					
				// 	return
				// }
				
				
				
				// if(this.phone.length < 11){
					
				// 	uni.showToast({
				// 		icon:'none',
				// 		title:'请输入正确手机号码'
				// 	})
					
				// 	return
				// }
				
				let data = this.book;
					
				app.showLoading('提交中');
					         
				app.post('ApiTheater/postOrder', data, function(res) {
					
					console.log(res);
					
					app.showLoading(false);
					if (res.status == 0) {
						app.alert(res.msg);
						return;
					}
					
					uni.navigateTo({
						url:'/pages/pay/pay?id='+res.data.id
					})

				});
				
			},
			
			showList(){

				this.$refs.dialogShowCategory.open('top');
			},
			hideDialog(){
				
				this.$refs.dialogShowCategory.close();
			}
			
		}
	}
</script>

<style lang="scss">
	.custom-input {
		width: 100%;
		height: 80rpx;
		padding: 20rpx 24rpx;
		border: 2rpx solid #e5e5e5;
		border-radius: 12rpx;
		font-size: 32rpx;
		color: #333;
		background-color: #fff;
		box-sizing: border-box;
		line-height: 40rpx;
	}
	
	.custom-input:focus {
		border-color: #007aff;
		outline: none;
		box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
	}
	
	.custom-input::placeholder {
		color: #999;
		font-size: 30rpx;
	}

	.jc_center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.alignItems {
		display: flex;
		align-items: center;
	}

	.spaceBetween {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.head {
		background-color: #7771ec;
	}

	.openingMessage {
		padding-left: 30rpx; 
		color: #ffffff;
		margin-bottom: 20rpx;
		height: 150px;
		overflow: auto;


		.openingMessage_d {}
	}
	
	.openingMessage_t {
		padding-left: 30rpx;
		margin-bottom: 10px;
		padding-top: 10px;
		color: #fff;
		font-size: 30rpx;
	}
	

	.openingPrompt {
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #ffffff;
		padding: 30rpx 30rpx 60rpx;
		background-color: #5b50da;
		border-radius: 30rpx 30rpx 0 0;

		.openingPrompt_txt1 {
			margin-right: 30rpx;
		}

		.openingPrompt_img {
			width: 30rpx;
			height: 30rpx;
			margin-right: 10rpx;
		}
	}

	.content {
		background-color: #f5f5f5;
		z-index: 100;
		border-radius: 30rpx 30rpx 0 0;
		margin-top: -40rpx;
	}

	.specialOffer {
		background-color: #ffffff;
		margin-left: 30rpx;
		margin-right: 30rpx;
		padding: 10rpx 30rpx 30rpx;
		border-radius: 10rpx;

		.sl_title {
			font-weight: bold;
			font-size: 32rpx;
			margin-bottom: 40rpx;
			margin-top: 30rpx;
		}

		.sl_txt1_l {}

		.sl_txt1_r {
			color: #999999;
			display: flex;
			align-items: center;

			.sl_txt1_r_a {}

			.sl_txt1_r_b {
				margin-left: 10rpx;
			}
		}

		.sl_txt2_l {}

		.sl_txt2_r {
			color: #999999;
			display: flex;
			align-items: center;

			.sl_txt2_r_a {}

			.sl_txt2_r_b {
				margin-left: 20rpx;
			}
		}
	}

	.tag_item {
		display: flex;
		align-items: center;
		border: 1px solid;
		color: #d62828;
		margin: 6rpx;
		border-radius: 8rpx;
		font-size: 24rpx;

		&.fitness {
			background: #d62828;
			color: white;
			border: none;
		}
	}

	.tag_icon {
		background: #efd599;
		height: 100%;
		width: 46rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 9rpx 0 18rpx 9rpx;
	}

	.discountCoupon {
		background-color: #ffffff;
		margin: 30rpx 30rpx 0;
		position: relative;
		.discountCoupon_title {
			display: flex;
			align-content: center;
			.discountCoupon_title_l {
				background-color: #d9643e;
				padding: 15rpx 20rpx  20rpx 10rpx;
				border-radius: 20rpx 30rpx 0 0;
				.discountCoupon_title_img {
					width: 30rpx;
					height: 30rpx;
					margin-left: 20rpx;
				}

				.discountCoupon_title_txt {
					color: #ffffff;
					margin-left: 20rpx;
					padding-right: 30rpx;
				}
			}

			.discountCoupon_title_r {
				padding: 15rpx 30rpx 20rpx;
				background-color: #f9edea;
				color: #d83a4d;
				border-radius: 0rpx 0rpx 20rpx 20rpx;
				margin-left: -10rpx;
			}
		}

		.discountCoupon_content {
			padding-top: 20rpx;
			background: #ffffff;
			position: absolute;
			top: 85%;
			border-radius: 20rpx ;
			.discountCoupon_txt {
				margin: 20rpx;
				font-weight: bold;
			}

			.discountCoupon_txt1 {
				margin: 20rpx;
				display: flex;
				align-items: center;
				color: #999999;
			}
		}
	}
	.bottom{
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		.bottom_txt1{
			font-size: 24rpx;
		}
		.bottom_txt2{
			font-size: 36rpx;
			color: #d74140;
			margin-left: 10rpx;
			font-weight: bold;
		}
		.bottom_txt3{
			color: #999999;
			margin-left: 10rpx;
		}
		.bottom_txt4{
			color: #999999;
			margin-top: 10rpx;
		}
		.bt{
			padding: 20rpx 10%;
			background-color: #6b63ec;
			border-radius: 40rpx;
			color: #ffffff;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
	
	.view-main{
		padding: 28rpx;
		font-size: 28rpx;
		color: #333333;
		border-radius: 20rpx 20rpx 0 0; 
	
		background: #fff;
	}
	
	.view-title {
	    justify-content: space-between;
		align-items: center;
	    display: flex;
		font-size: 30rpx;
	     border-bottom: 1px solid #ccc;
	     padding-bottom: 20px;
	}
	
	.icon{
		width: 25rpx;
		height: 25rpx;
		margin: auto 0;
	}
	
	.icon2{
		width: 45rpx;
		height: 45rpx;
		margin: auto 0;
	}
	
	.tit-t{
		padding: 50rpx 0 20rpx 0;
		font-size: 22rpx;
	}
	
	.tit-date{
		padding: 10px;  
		background: red;
		color: #fff;
		border-radius: 10rpx;
	}
	
	.tit-time{
		margin: 10rpx 0;
		border: 1px solid #f1f1f1;
		text-align: center;
		padding: 10rpx;
		border-radius: 10rpx;
	}
	
	.view-btn{
		background: red;
		color: #fff;
		text-align: center;
		padding: 10px;
		border-radius: 50rpx;
	}
	

</style>