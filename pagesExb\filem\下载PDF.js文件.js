/**
 * 下载PDF.js文件到项目目录的脚本
 * 
 * 使用方法：
 * 1. 安装Node.js
 * 2. 在命令行中执行: node 下载PDF.js文件.js
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// 要下载的文件列表
const files = [
  {
    url: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.4.120/build/pdf.min.js',
    dest: './pdfjs/build/pdf.min.js'
  },
  {
    url: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.4.120/build/pdf.worker.min.js',
    dest: './pdfjs/build/pdf.worker.min.js'
  }
];

// 确保目录存在
function ensureDirectoryExists(filePath) {
  const dirname = path.dirname(filePath);
  if (fs.existsSync(dirname)) {
    return true;
  }
  
  ensureDirectoryExists(dirname);
  fs.mkdirSync(dirname);
}

// 下载文件
function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    console.log(`开始下载 ${url} 到 ${dest}`);
    
    ensureDirectoryExists(dest);
    
    const file = fs.createWriteStream(dest);
    
    https.get(url, response => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败，状态码: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`下载完成: ${dest}`);
        resolve();
      });
    }).on('error', err => {
      fs.unlink(dest, () => {}); // 删除不完整的文件
      reject(err);
    });
    
    file.on('error', err => {
      fs.unlink(dest, () => {}); // 删除不完整的文件
      reject(err);
    });
  });
}

// 主函数
async function main() {
  console.log('开始下载PDF.js文件...');
  
  try {
    for (const file of files) {
      await downloadFile(file.url, file.dest);
    }
    
    console.log('\n所有文件下载完成！');
    console.log('\n注意: cmaps文件未包含在内，请从PDF.js官方发布版中获取这些文件。');
    console.log('您可以参考 PDF.js安装指南.md 获取更多信息。');
  } catch (error) {
    console.error('下载过程中出错:', error.message);
  }
}

// 执行主函数
main(); 