<template>
	<view class="container" :style="{ backgroundColor: pageinfo.bgcolor }">
		
		<!-- #ifdef MP-WEIXIN  -->
		<!-- <official-account></official-account> -->
		<!-- #endif -->
		<block v-if="xixie && xdata && xdata.xixie_mendian">
			<dp-xixie-mendian :mendian_data="xdata.mendian_data" @changePopupAddress="changePopupAddress"></dp-xixie-mendian>
		</block>
		<block v-if="platform == 'wx' && homeNavigationCustom == 2">
			<view class="navigation" :style="{ height: 44 + statusBarHeight + 'px', background: navigationBarBackgroundColor }">
				<view :style="{ height: statusBarHeight + 'px' }"></view>
				<view class="navcontent">
					<view class="topinfo">
						<image class="topinfoicon" :src="sysset.logo" />
						<view class="topinfotxt" :style="{ color: navigationBarTextStyle }">{{ sysset.name }}</view>
					</view>
					<view class="topsearch" :style="{ width: screenWidth - 210 + 'px' }" @tap="goto" data-url="/shopPackage/shop/search">
						<image src="/static/img/search.png" />
						<text style="font-size: 24rpx; color: #999">搜索感兴趣的商品</text>
					</view>
				</view>
			</view>
			<view style="width: 100%" :style="{ height: 44 + statusBarHeight + 'px' }"></view>
		</block>

		<block v-if="sysset.agent_card == 1 && sysset.agent_card_info">
			<view style="height: 10rpx"></view>
			<view class="agent-card">
				<view class="flex-y-center row1">
					<image class="logo" :src="sysset.agent_card_info.logo" />
					<view class="text">
						<view class="title limitText flex">{{ sysset.agent_card_info.shopname }}</view>
						<view class="limitText grey-text">{{ sysset.agent_card_info.address }}</view>
						<view class="grey-text flex-y-center">
							<image class="img" :src="pre_url + '/static/img/my.png'"></image>
							<view>{{ sysset.agent_card_info.name }}</view>
							<image class="img" :src="pre_url + '/static/img/tel.png'" style="margin-left: 30rpx"></image>
							<view @tap="goto" :data-url="'tel::' + sysset.agent_card_info.tel" style="position: relative">
								{{ sysset.agent_card_info.tel }}
								<view class="btn" @tap="goto" :data-url="'tel::' + sysset.agent_card_info.tel">拨打</view>
							</view>
						</view>
					</view>
					<view class="right">
						<image :src="pre_url + '/static/img/shop_vip.png'" mode="aspectFit" style="width: 180rpx; height: 48.5rpx"></image>
					</view>
				</view>
				<view class="flex-y-center flex-x-center agent-card-b" :style="{ background: t('color2') }">
					<view @tap="goto" :data-url="'/pagesExt/agent/card'">
						<image class="img" :src="pre_url + '/static/img/shop.png'"></image>
						店铺信息
					</view>
					<view @tap="goto" :data-url="'/pages/commission/poster'">
						<image class="img img2" :src="pre_url + '/static/img/card.png'"></image>
						店铺海报
					</view>
				</view>
			</view>
		</block>

		<block v-if="sysset.mode == 1">
			<view class="navigation" :style="{ height: 44 + 'px', background: navigationBarBackgroundColor }">
				<view class="navcontent">
					<view class="topinfo">
						<image class="topinfoicon" :src="sysset.logo" />
						<view class="topinfotxt" :style="{ color: navigationBarTextStyle, width: 'auto' }">{{ sysset.name }}</view>
					</view>
					<view class="topR">
						<text class="btn-text" @tap.stop="goto" data-url="/pagesExt/business/clist2">[切换]</text>
						<!-- 距离589m， -->
						<block v-if="sysset.address">{{ sysset.address }}</block>
					</view>
				</view>
			</view>
			<view style="width: 100%" :style="{ height: 44 + 'px' }"></view>
		</block>

		<block v-if="sysset.showgzts">
			<view style="width: 100%; height: 88rpx"></view>
			<view class="follow_topbar">
				<view class="headimg">
					<image :src="sysset.logo" />
				</view>
				<view class="info">
					<view class="i">
						欢迎进入
						<text :style="{ color: t('color1') }">{{ sysset.name }}</text>
					</view>
					<view class="i">关注公众号享更多专属服务</view>
				</view>
				<view class="sub" @tap="showsubqrcode" :style="{ 'background-color': t('color1') }">立即关注</view>
			</view>
			<uni-popup id="qrcodeDialog" ref="qrcodeDialog" type="dialog">
				<view class="qrcodebox">
					<image :src="sysset.qrcode" @tap="previewImage" :data-url="sysset.qrcode" class="img" />
					<view class="txt">长按识别二维码关注</view>
					<view class="close" @tap="closesubqrcode">
						<image src="/static/img/close2.png" style="width: 100%; height: 100%" />
					</view>
				</view>
			</uni-popup>
		</block>

		<dp :pagecontent="pagecontent" :menuindex="menuindex" @getdata="getdata"></dp>

		<view :class="sysset.ddbb_position == 'bottom' ? 'bobaobox_bottom' : 'bobaobox'" v-if="oglist && oglist.length > 0">
			<swiper style="position: relative; height: 54rpx; width: 450rpx" autoplay="true" :interval="5000" vertical="true">
				<swiper-item v-for="(item, index) in oglist" :key="index" @tap="goto" :data-url="item.tourl" class="flex-y-center">
					<image :src="item.headimg" style="width: 40rpx; height: 40rpx; border: 1px solid rgba(255, 255, 255, 0.7); border-radius: 50%; margin-right: 4px"></image>
					<view style="width: 400rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 22rpx">
						<text style="padding-right: 2px">{{ item.nickname }}</text>
						<text style="padding-right: 4px">{{ item.showtime }}</text>
						<text style="padding-right: 2px" v-if="item.type == 'collage' && item.buytype == '2'">发起拼团</text>
						<text v-else>购买了</text>
						<text>{{ item.name }}</text>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<block v-if="xixie && xdata && xdata.popup_address">
			<dp-xixie-popup-address
				:xixie_login="xdata.xixie_login"
				:xixie_location="xdata.xixie_location"
				:address_latitude="latitude"
				:address_longitude="longitude"
				:code="code"
				@changePopupAddress="changePopupAddress"
				@getdata="getdata"
				@setMendianData="setMendianData"
			></dp-xixie-popup-address>
		</block>
		<block v-if="xixie && display_buy">
			<dp-xixie-buycart :cartnum="cartnum" :cartprice="cartprice" :color="t('color1')" :colorrgb="t('color1rgb')"></dp-xixie-buycart>
		</block>

		<view v-if="copyright != ''" class="copyright">{{ copyright }}</view>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<dp-guanggao :guanggaopic="guanggaopic" :guanggaourl="guanggaourl"></dp-guanggao>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>
<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			menuindex: -1,
			pre_url: app.globalData.pre_url,
			platform: app.globalData.platform,
			homeNavigationCustom: app.globalData.homeNavigationCustom,
			navigationBarBackgroundColor: app.globalData.navigationBarBackgroundColor,
			navigationBarTextStyle: app.globalData.navigationBarTextStyle,

			id: 0,
			pageinfo: [],
			pagecontent: [],
			sysset: {},
			title: '',
			oglist: [],
			guanggaopic: '',
			guanggaourl: '',
			copyright: '',
			latitude: '',
			longitude: '',
			area: '',
			statusBarHeight: 20,
			screenWidth: 375,
			business: [],

			xixie: false,
			xdata: '',
			display_buy: '',
			cartnum: 0,
			cartprice: 0,
			code: '',
			area_id: '',
			area_name: ''
		};
	},
	onLoad: function (opt) {
		let that = this;
		that.opt = app.getopts(opt);
		var sysinfo = uni.getSystemInfoSync();

		that.statusBarHeight = sysinfo.statusBarHeight;
		that.screenWidth = sysinfo.screenWidth;
		var cachearea = app.getCache('user_current_area');
		var cachelongitude = app.getCache('user_current_longitude');
		var cachelatitude = app.getCache('user_current_latitude');
		if (cachearea || cachearea != 1) {
			that.area = cachearea;
			that.latitude = cachelatitude;
			that.longitude = cachelongitude;
		}

		var sto_id = uni.getStorageSync('area_id');
		if (sto_id || sto_id != 1) {
			that.area_id = sto_id;
		}

		that.getdata();

		uni.$on('city', function (data) {
			that.area_id = data.id;

			uni.setStorageSync('area_id', data.id);
			uni.setStorageSync('area_name', data.name);

			that.getdata();
		});

		// uni.getLocation({
		//       type: 'wgs84',
		//       success: (res) => {
		//           this.latitude = res.latitude; // 纬度
		//           this.longitude = res.longitude; // 经度
		//       },
		//       fail: (err) => {
		//           console.log('获取位置失败：', err);
		//       } 
		//   });  
		  
		// uni.navigateTo({ 
		// 	// url: '../../pagesExa/tuanzhangadmin/index/tuanedit'
		//     url:'../../daihuobiji/kuaituan/addtuan'  
		// }) 
		

		
	},

	onPullDownRefresh: function (e) {
		this.getdata();
	},
	onPageScroll: function (e) {
		uni.$emit('onPageScroll', e);
	},
	methods: {
		getdata(type) {
			var that = this;
			var opt = this.opt;
			var id = 0;
			if (opt.select_bid) {
				var select_bid = opt.select_bid;
				app.setCache('select_bid', select_bid);
			} else {
				var select_bid = app.getCache('select_bid');
			}

			if (opt && opt.id) {
				id = opt.id;
			}

			let obj = {
				id: id,
				select_bid: select_bid,
				pid: app.globalData.pid,
				area: that.area
			};

			if (type) {
				obj.latitude = that.latitude;
				obj.longitude = that.longitude;
			} else {
				obj.area_id = that.area_id;
			}

			console.log(obj);

			app.get('ApiIndex/index', obj, function (data) {
				console.log(data);
				that.loading = false;
				if (data.status == 2) {
					//付费查看
					app.goto('/pages/pay/pay?fromPage=index&id=' + data.payorderid + '&pageid=' + that.id, 'redirect');
					return;
				}
				if (data.status == 1) {
					var pagecontent = data.pagecontent;
					that.title = data.pageinfo.title;
					if (data.oglist) that.oglist = data.oglist;
					that.guanggaopic = data.guanggaopic;
					that.guanggaourl = data.guanggaourl;
					that.pageinfo = data.pageinfo;

					that.copyright = data.copyright;
					that.sysset = data.sysset;
					if (data.sysset.mode == 1 && data.business) {
						that.business = data.business;
						if (select_bid == '') app.setCache('select_bid', data.business.id);
					}

					uni.setNavigationBarTitle({
						title: data.pageinfo.title
					});

					that.loaded();

					let json = JSON.parse(data.sysset.area_set);

					if (that.latitude == '' && that.longitude == '' && json.gettype < 2) {
						app.getLocation(
							function (res) {
								console.log(res);
								that.latitude = res.latitude;
								that.longitude = res.longitude;

								that.getdata(1);
							},
							function (err) {
								console.log(err);
							}
						);
					} else {
						that.pagecontent = data.pagecontent;
					}

					if (data.xixie) {
						wx.login({
							success: function (res) {
								that.code = res.code;
							}
						});
						that.xixie = data.xixie;
						var xdata = data.xdata;
						that.xdata = xdata;

						that.display_buy = xdata.display_buy ? xdata.display_buy : false;
						that.cartnum = xdata.cartnum ? xdata.cartnum : 0;
						that.cartprice = xdata.cartprice ? xdata.cartprice : 0;
						if (xdata.cart_data) {
							setTimeout(function () {
								that.xcart_data = xdata.cart_data;
							}, 200);
						}
						if (xdata.popup_address) {
							setTimeout(function () {
								that.popup_address = data.popup_address;
							}, 200);
						}
					}
				} else {
					if (data.msg) {
						app.alert(data.msg, function () {
							if (data.url) app.goto(data.url);
						});
					} else if (data.url) {
						app.goto(data.url);
					} else {
						app.alert('您无查看权限');
					}
				}
			});
		},
		showsubqrcode: function () {
			this.$refs.qrcodeDialog.open();
		},
		closesubqrcode: function () {
			this.$refs.qrcodeDialog.close();
		},
		changePopupAddress: function (status) {
			this.xdata.popup_address = status;
		},
		setMendianData: function (data) {
			this.mendian_data = data;
		}
	}
};
</script>
<style>
.topR {
	flex: 1;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	color: #666;
}

.topR .btn-text {
	margin: 0 10rpx;
	color: #333;
}

.follow_topbar {
	height: 88rpx;
	width: 100%;
	max-width: 640px;
	background: rgba(0, 0, 0, 0.8);
	position: fixed;
	top: 0;
	z-index: 13;
}

.follow_topbar .headimg {
	height: 64rpx;
	width: 64rpx;
	margin: 6px;
	float: left;
}

.follow_topbar .headimg image {
	height: 64rpx;
	width: 64rpx;
}

.follow_topbar .info {
	height: 56rpx;
	padding: 16rpx 0;
}

.follow_topbar .info .i {
	height: 28rpx;
	line-height: 28rpx;
	color: #ccc;
	font-size: 24rpx;
}

.follow_topbar .info {
	height: 80rpx;
	float: left;
}

.follow_topbar .sub {
	height: 48rpx;
	width: auto;
	background: #fc4343;
	padding: 0 20rpx;
	margin: 20rpx 16rpx 20rpx 0;
	float: right;
	font-size: 24rpx;
	color: #fff;
	line-height: 52rpx;
	border-radius: 6rpx;
}

.qrcodebox {
	background: #fff;
	padding: 50rpx;
	position: relative;
	border-radius: 20rpx;
}

.qrcodebox .img {
	width: 400rpx;
	height: 400rpx;
}

.qrcodebox .txt {
	color: #666;
	margin-top: 20rpx;
	font-size: 26rpx;
	text-align: center;
}

.qrcodebox .close {
	width: 50rpx;
	height: 50rpx;
	position: absolute;
	bottom: -100rpx;
	left: 50%;
	margin-left: -25rpx;
	border: 1px solid rgba(255, 255, 255, 0.6);
	border-radius: 50%;
	padding: 8rpx;
}

.bobaobox {
	position: fixed;
	top: calc(var(--window-top) + 180rpx);
	left: 20rpx;
	z-index: 10;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 30rpx;
	color: #fff;
	padding: 0 10rpx;
}

.bobaobox_bottom {
	position: fixed;
	bottom: calc(env(safe-area-inset-bottom) + 150rpx);
	left: 0;
	right: 0;
	width: 470rpx;
	margin: 0 auto;
	z-index: 10;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 30rpx;
	color: #fff;
	padding: 0 10rpx;
}

@supports (bottom: env(safe-area-inset-bottom)) {
	.bobaobox_bottom {
		position: fixed;
		bottom: calc(env(safe-area-inset-bottom) + 150rpx);
		left: 0;
		right: 0;
		width: 470rpx;
		margin: 0 auto;
		z-index: 10;
		background: rgba(0, 0, 0, 0.6);
		border-radius: 30rpx;
		color: #fff;
		padding: 0 10rpx;
	}
}

.navigation {
	width: 100%;
	height: 64px;
	background: #fff;
	position: fixed;
	z-index: 99;
}

.navcontent {
	display: flex;
	align-items: center;
	height: 44px;
	padding-left: 10px;
}

.navcontent .topinfo {
	display: flex;
	align-items: center;
}

.navcontent .topinfoicon {
	width: 17px;
	height: 17px;
	border-radius: 4px;
}

.navcontent .topinfotxt {
	margin-left: 6px;
	font-size: 14px;
	font-weight: 600;
	width: 70px;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.navcontent .topsearch {
	width: 150px;
	height: 32px;
	background: #f2f2f2;
	border-radius: 16px;
	color: #232323;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
}

.navcontent .topsearch image {
	width: 14px;
	height: 15px;
	margin-right: 6px;
}

.limitText {
	flex: 1;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	color: #666;
}

.agent-card {
	height: auto;
	position: relative;
	background-color: #fff;
	margin: 0 20rpx 10rpx;
	font-size: 24rpx;
	border-radius: 0 10rpx 10rpx 10rpx;
	overflow: hidden;
	box-shadow: 0 0 8rpx 0px rgb(0 0 0 / 30%);
}

.agent-card .row1 {
	padding: 20rpx 10rpx 20rpx 20rpx;
}

.agent-card .logo {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
}

.agent-card .text {
	flex: 1;
	margin-left: 20rpx;
	color: #666;
	line-height: 180%;
}

.agent-card .title {
	color: #333;
	font-weight: bold;
	font-size: 32rpx;
}

.agent-card .right {
	height: 120rpx;
}

.agent-card .btn {
	position: absolute;
	right: -100rpx;
	padding: 0 14rpx;
	top: 0;
	border: 1px solid #b6c26e;
	border-radius: 10rpx;
	color: #b6c26e;
}

.agent-card .img {
	margin-right: 6rpx;
	width: 30rpx;
	height: 30rpx;
}

.agent-card .img2 {
	width: 32rpx;
	height: 32rpx;
}

.grey-text {
	color: #999;
	font-weight: normal;
}

.agent-card-b view {
	line-height: 72rpx;
	font-size: 28rpx;
	color: #444;
	width: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.agent-card-b view:first-child::after {
	content: '';
	width: 1px;
	height: 28rpx;
	border-right: 1px solid #444;
	position: absolute;
	right: 0;
}
</style>
