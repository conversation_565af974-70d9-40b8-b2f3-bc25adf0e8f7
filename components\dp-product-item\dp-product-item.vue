<template>
	<view style="width:100%">
		<!-- 只在有背景图或标题时显示头部样式 -->
		<view class="line-container" v-if="params && (params.bgimg || params.main_title)">
			<view class="line-bg" v-if="params.bgimg" :style="{
				backgroundImage: `url(${params.bgimg})`
			}"></view>
			
			<view class="header" v-if="params.main_title">
				<view class="title-box">
					<text class="main-title" :style="{
						color: params.main_title_color || '#fff'
					}">{{ params.main_title }}</text>
					<text class="sub-title" v-if="params.sub_title" :style="{
						color: params.sub_title_color || 'rgba(255, 255, 255, 0.9)'
					}">{{ params.sub_title }}</text>
				</view>
				<view class="more-link" v-if="params.hrefurl" @click="goto" :data-url="params.hrefurl" :style="{
					color: params.more_text_color || '#fff'
				}">
					{{ params.more_text || '查看更多' }} <text class="arrow">></text>
				</view>
			</view>
		</view>

		<!-- 原有的商品列表内容 -->
		<view class="dp-product-normal-item">
			<view class="item" v-for="(item,index) in data" :style="'background:'+probgcolor+';'+(showstyle==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (showstyle==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%'))" :key="item.id" @click="goto" :data-url="'/shopPackage/shop/product?id='+item[idfield]" >
				<view class="product-pic" >
					<image class="image" :src="item.pic" mode="widthFix"/>
					<image class="saleimg" :src="saleimg" v-if="saleimg!=''" mode="widthFix"/>
				</view>
				<view class="product-info">
					<view class="p1" v-if="showname == 1">{{item.name}}</view>
					<view class="p2">
						<view class="p2-1" v-if="showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)">
							<text v-if="item.is_member_yh == 0 && item.is_newcustom == 1" class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">￥</text>{{item.yh_price}}</text>
							<text v-else class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">￥</text>{{item.sell_price}}</text>
							<text class="t2" v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
							<text class="t3" v-if="item.juli" style="color:#888;">{{item.juli}}</text> 
						</view>
						<view class="p2-1" v-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0" style="height: 50rpx;line-height: 44rpx;">
							<text v-if="showstyle!=1" class="t1" :style="{color:t('color1'),fontSize:'30rpx'}">询价</text>
							<text v-if="showstyle==1" class="t1" :style="{color:t('color1')}">询价</text>
							<block v-if="item.xunjia_text && item.price_type == 1">
								<view class="lianxi" :style="{background:t('color1')}" @tap.stop="showLinkChange" :data-lx_name="item.lx_name" :data-lx_bid="item.lx_bid" :data-lx_bname="item.lx_bname" :data-lx_tel="item.lx_tel" data-btntype="2">{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>
							</block>
						</view>
					</view>
					<view class="p1" v-if="item.merchant_name" style="color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal;"><text>{{item.merchant_name}}</text></view>
					<view class="p1" v-if="item.main_business" style="color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;"><text>{{item.main_business}}</text></view>
					
					<view class="p3" v-if="showsales=='1' && item.sales>0">已售{{item.sales}}件</view>
					<view v-if="(showsales !='1' ||  item.sales<=0) && item.main_business" style="height: 44rpx;"></view>
					<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="showcart==1 && !item.price_type" @click.stop="buydialogChange" :data-proid="item[idfield]"><text class="iconfont icon_gouwuche"></text></view>
					<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="showcart==2 && !item.price_type" @click.stop="buydialogChange" :data-proid="item[idfield]"><image :src="cartimg" class="img"/></view>
				</view>
				<view class="bg-desc" v-if="item.hongbaoEdu > 0" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}">可获额度 +{{item.hongbaoEdu}}</view>
				<view class="bg-desc" v-if="item.huang_dx.types > 0"
					:style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}">
					可抵扣红包:{{item.huang_dx.nums}}</view>
			</view>
		</view>
		<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange" :menuindex="menuindex"></buydialog>
		<view class="posterDialog linkDialog" v-if="showLinkStatus">
			<view class="main">
				<view class="close" @tap="showLinkChange"><image class="img" src="/static/img/close.png"/></view>
				<view class="content">
					<view class="title">{{lx_name}}</view>
					<view class="row" v-if="lx_bid > 0">
						<view class="f1">店铺名称</view>
						<view class="f2" @tap="goto" :data-url="'/pagesExt/business/index?id='+lx_bid">{{lx_bname}}<image src="/static/img/arrowright.png" class="image"/></view>
					</view>
					<view class="row" v-if="lx_tel">
						<view class="f1">联系电话</view>
						<view class="f2" @tap="goto" :data-url="'tel::'+lx_tel" :style="{color:t('color1')}">{{lx_tel}}<image src="/static/img/copy.png" class="copyicon" @tap.stop="copy" :data-text="lx_tel"></image></view>
					</view>
				</view>
			</view>
		</view>
	</view>
	</template>
	<script>
		export default {
			data(){
				return {
					buydialogShow:false,
					proid:0,
					
					showLinkStatus:false,
					lx_name:'',
					lx_bid:'',
					lx_tel:''
				}
			},
			props: {
				showstyle:{default:2},
				menuindex:{default:-1},
				saleimg:{default:''},
				showname:{default:1},
				namecolor:{default:'#333'},
				showprice:{default:'1'},
				showsales:{default:'1'},
				showcart:{default:'1'},
				cartimg:{default:'/static/imgsrc/cart.svg'},
				data:{},
				idfield:{default:'id'},
				probgcolor:{default:'#fff'},
				params: {
					type: Object,
					default: () => ({
						bgimg: '',          // 背景图
						main_title: '',     // 大标题
						main_title_color: '', // 主标题颜色
						sub_title: '',      // 副标题
						sub_title_color: '', // 副标题颜色
						more_text: '',      // 更多文字
						more_text_color: '', // 更多文字颜色
						hrefurl: ''         // 更多链接
					})
				}
			},
			methods: {
				buydialogChange: function (e) {
					if(!this.buydialogShow){
						this.proid = e.currentTarget.dataset.proid
					}
					this.buydialogShow = !this.buydialogShow;
					console.log(this.buydialogShow);
				},
				addcart:function(){
					this.$emit('addcart');
				},
				showLinkChange: function (e) {
					var that = this;
					that.showLinkStatus = !that.showLinkStatus;
					that.lx_name = e.currentTarget.dataset.lx_name;
					that.lx_bid = e.currentTarget.dataset.lx_bid;
					that.lx_bname = e.currentTarget.dataset.lx_bname;
					that.lx_tel = e.currentTarget.dataset.lx_tel;
				},
			},
			mounted() {
				console.log('dp-product-item params:', this.params)
			}
		}
	</script>
	<style>
	.dp-product-normal-item{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}
	.dp-product-normal-item .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden;}
	.dp-product-normal-item .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}
	.dp-product-normal-item .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
	.dp-product-normal-item .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}
	.dp-product-normal-item .product-info {padding:20rpx 20rpx;position: relative;}
	.dp-product-normal-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
	.dp-product-normal-item .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}
	.dp-product-normal-item .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}
	.dp-product-normal-item .product-info .p2-1 .t1{font-size:36rpx;font-weight:bold;}
	.dp-product-normal-item .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
	.dp-product-normal-item .product-info .p2-1 .t3 {margin-left:10rpx;font-size:22rpx;color: #999;}
	.dp-product-normal-item .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}
	.dp-product-normal-item .product-info .p3{color:#999999;font-size:20rpx;margin-top:10rpx}
	.dp-product-normal-item .product-info .p4{width:52rpx;height:52rpx;border-radius:50%;position:absolute;display:relative;bottom:16rpx;right:20rpx;text-align:center;}
	.dp-product-normal-item .product-info .p4 .icon_gouwuche{font-size:30rpx;height:52rpx;line-height:52rpx}
	.dp-product-normal-item .product-info .p4 .img{width:100%;height:100%}
	.bg-desc {color: #fff; padding: 10rpx 20rpx;}
	
	.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}
	.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}
	.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}
	.posterDialog .close .img{ width:32rpx;height:32rpx;}
	.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}
	.posterDialog .content .img{width:540rpx;height:960rpx}
	.linkDialog {background:rgba(0,0,0,0.4);z-index:11;}
	.linkDialog .main{ width: 90%; position: fixed; top: 50%; left: 50%; margin: 0;-webkit-transform: translate(-50%,-50%);transform: translate(-50%,-50%);}
	.linkDialog .title {font-weight: bold;margin-bottom: 30rpx;}
	.linkDialog .row {display: flex; height:80rpx;line-height: 80rpx; padding: 0 16rpx;}
	.linkDialog .row .f1 {width: 40%; text-align: left;}
	.linkDialog .row .f2 {width: 60%; height:80rpx;line-height: 80rpx;text-align: right;align-items:center;}
	.linkDialog .image{width: 28rpx; height: 28rpx; margin-left: 8rpx;margin-top: 2rpx;}
	.linkDialog .copyicon {width: 28rpx; height: 28rpx; margin-left: 8rpx; position: relative; top: 4rpx;}
	
	.lianxi{color: #fff;border-radius: 50rpx 50rpx;line-height: 50rpx;text-align: center;font-size: 22rpx;padding: 0 14rpx;display: inline-block;float: right;}
	
	/* 头部样式 */
	.line-container {
		position: relative;
		overflow: hidden;
		padding: 20rpx 0;
		background: #f5f5f5;
		min-height: 260rpx;
		margin-bottom: 20rpx;
	}

	.line-bg {
		position: absolute;
		top: -20%;
		left: -20%;
		width: 140%;
		height: 140%;
		background-position: center;
		background-size: cover;
		z-index: 1;
		transform: scale(1.1);
	}

	.line-bg::after {
		content: '';
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.2) 100%);
	}

	.header {
		position: relative;
		z-index: 3;
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		padding: 30rpx;
		margin-top: 20rpx;
	}

	.title-box {
		display: flex;
		flex-direction: column;
	}

	.main-title {
		font-size: 40rpx;
		font-weight: bold;
		line-height: 1.2;
		margin-bottom: 8rpx;
	}

	.sub-title {
		font-size: 26rpx;
		line-height: 1.4;
	}

	.more-link {
		font-size: 28rpx;
		display: flex;
		align-items: center;
		padding: 10rpx 20rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
	}

	.more-link .arrow {
		margin-left: 6rpx;
	}
	</style>