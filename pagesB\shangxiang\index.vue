<template>
	<view class="container">
		<block v-if="isload">
			<!-- 背景图片 -->
			<image class="background-image" :src="settings.background_image || defaultBg"></image>
			<view class="background-overlay"></view>
			
			<!-- 顶部标题 -->
			<view class="header">
				<text class="title">上香供奉许愿</text>
				<text class="subtitle">虔诚祈福，心愿成真</text>
			</view>
			
			<!-- 浮动按钮区域 -->
			<view class="floating-buttons">
				<!-- <view class="floating-btn" @tap="goToRecentOrders">
					<image class="btn-icon" :src="pre_url+'/static/img/order-icon.png'"/>
					<text class="btn-text">最近订单</text>
				</view> -->
				<view class="floating-btn" @tap="goToMyWishes">
					<image class="btn-icon" :src="pre_url+'/static/img/my-wishes.png'"/>
					<text class="btn-text">许愿记录</text>
				</view>
			</view>
		
			<!-- 功能描述 -->
			<view class="description" v-if="settings.description">
				<text>{{settings.description}}</text>
			</view>
		
			<!-- 底部功能选项 -->
			<view class="bottom-function-container">
				<view class="function-grid">
					<view class="function-item" @tap="selectFunction" data-type="gonghua">
						<view class="function-icon-wrapper">
							<image class="function-icon" :src="pre_url+'/static/img/gonghua-icon.png'" mode="aspectFit"/>
						</view>
						<text class="function-name">供花</text>
						<text class="function-price">￥{{settings.gonghua_price}}</text>
					</view>
					<view class="function-item" @tap="selectFunction" data-type="gongguo">
						<view class="function-icon-wrapper">
							<image class="function-icon" :src="pre_url+'/static/img/gongguo-icon.png'" mode="aspectFit"/>
						</view>
						<text class="function-name">供果</text>
						<text class="function-price">￥{{settings.gongguo_price}}</text>
					</view>
					<view class="function-item" @tap="selectFunction" data-type="shangxiang">
						<view class="function-icon-wrapper">
							<image class="function-icon" :src="pre_url+'/static/img/shangxiang-icon.png'" mode="aspectFit"/>
						</view>
						<text class="function-name">上香</text>
						<text class="function-price">￥{{settings.shangxiang_price}}</text>
					</view>
					<view class="function-item" @tap="selectFunction" data-type="xuyuan">
						<view class="function-icon-wrapper">
							<image class="function-icon" :src="pre_url+'/static/img/xuyuan-icon.png'" mode="aspectFit"/>
						</view>
						<text class="function-name">许愿</text>
						<text class="function-price">￥{{settings.xuyuan_price}}</text>
					</view>
				</view>
			</view>
		
			<!-- 选择金额弹窗 -->
			<view v-if="showModal" class="modal-overlay" @tap="closeModal">
				<view class="modal-content" @tap.stop>
					<view class="modal-header">
						<text class="modal-title">{{getTypeName(selectedType)}}</text>
						<view class="close-btn" @tap="closeModal">×</view>
					</view>
					
					<!-- 金额选择 -->
					<view class="amount-section">
						<text class="section-label">选择金额</text>
						<view class="amount-options">
							<view class="amount-item" 
								  :class="{active: selectedAmount == getDefaultPrice(selectedType)}" 
								  @tap="selectAmount" 
								  :data-amount="getDefaultPrice(selectedType)">
								<text>￥{{getDefaultPrice(selectedType)}}</text>
							</view>
							<view class="amount-item" 
								  :class="{active: selectedAmount == (getDefaultPrice(selectedType) * 2)}" 
								  @tap="selectAmount" 
								  :data-amount="getDefaultPrice(selectedType) * 2">
								<text>￥{{getDefaultPrice(selectedType) * 2}}</text>
							</view>
							<view class="amount-item" 
								  :class="{active: selectedAmount == (getDefaultPrice(selectedType) * 5)}" 
								  @tap="selectAmount" 
								  :data-amount="getDefaultPrice(selectedType) * 5">
								<text>￥{{getDefaultPrice(selectedType) * 5}}</text>
							</view>
						</view>
						
						<!-- 自定义金额 -->
						<view v-if="settings.allow_custom_amount" class="custom-amount">
							<text class="custom-label">自定义金额</text>
							<input class="custom-input" 
								   type="digit" 
								   placeholder="请输入金额" 
								   v-model="customAmount" 
								   @input="onCustomAmountInput"/>
						</view>
					</view>
					
					<!-- 许愿内容 -->
					<view v-if="selectedType == 'xuyuan'" class="wish-section">
						<text class="section-label">许愿内容</text>
						<textarea class="wish-input" 
								  placeholder="请输入您的心愿..." 
								  v-model="wishContent" 
								  maxlength="200"></textarea>
						<text class="char-count">{{wishContent.length}}/200</text>
					</view>
					
					<!-- 确认按钮 -->
					<view class="modal-footer">
						<button class="confirm-btn" @tap="confirmSubmit">确认{{getTypeName(selectedType)}}</button>
					</view>
				</view>
			</view>
		</block>
		
		<nodata v-if="nodata"></nodata>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			isload: false,
			nodata: false,
			settings: {},
			recentWishes: [],
			showModal: false,
			selectedType: '',
			selectedAmount: 0,
			customAmount: '',
			wishContent: '',
			loading: false,
			defaultBg: '/static/img/shangxiang-bg.jpg',
			defaultAvatar: '/static/img/default-avatar.png',
			pre_url: '',
			textset: {},
			bid: 0
		}
	},
	onLoad: function(opt) {
		this.opt = app.getopts(opt);
		this.bid = this.opt.bid || 0;
		this.pre_url = app.globalData.pre_url;
		this.getSettings();
		this.getRecentWishes();
		console.log('背景图片路径:', this.settings.background_image || this.defaultBg);
		
		// 动态设置页面背景
		this.setPageStyle();
	},
	
	onPullDownRefresh: function() {
		this.getSettings();
		this.getRecentWishes();
	},
	methods: {
		// 设置页面样式
		setPageStyle() {
			// #ifdef H5
			// 在H5环境下修改页面背景色
			const pageBody = document.querySelector('.uni-page-body');
			if (pageBody) {
				pageBody.style.backgroundColor = 'transparent';
			}
			// #endif
			
			// #ifdef MP-WEIXIN
			// 在小程序环境下可以使用小程序API设置背景色
			wx.setBackgroundColor({
				backgroundColor: 'transparent'
			});
			// #endif
		},
		
		// 获取设置
		getSettings() {
			var that = this;
			that.loading = true;
			app.post('ApiShangxiang/getSettings', {}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					that.settings = res.data;
					console.log('接收到的背景设置:', that.settings.background_image);
					that.loaded();
				} else {
					app.error(res.msg || '获取设置失败');
				}
			});
		},
		
		// 获取最近许愿
		getRecentWishes() {
			var that = this;
			app.post('ApiShangxiang/getWishList', {
				page: 1,
				limit: 5
			}, function(res) {
				if (res.code === 1) {
					that.recentWishes = res.data.list || [];
				}
			});
		},
		
		// 选择功能
		selectFunction(e) {
			if (!this.settings.is_open) {
				this.$toast('功能暂未开放');
				return;
			}
			
			const type = e.currentTarget.dataset.type;
			this.selectedType = type;
			this.selectedAmount = this.getDefaultPrice(type);
			this.customAmount = '';
			this.wishContent = '';
			this.showModal = true;
		},
		
		// 选择金额
		selectAmount(e) {
			const amount = parseFloat(e.currentTarget.dataset.amount);
			this.selectedAmount = amount;
			this.customAmount = '';
		},
		
		// 自定义金额输入
		onCustomAmountInput(e) {
			const value = parseFloat(e.detail.value);
			if (value > 0) {
				this.selectedAmount = value;
			}
		},
		
		// 关闭弹窗
		closeModal() {
			this.showModal = false;
		},
		
		// 提交许愿
		confirmSubmit() {
			var that = this;
			if (!that.selectedAmount || that.selectedAmount <= 0) {
				app.error('请选择金额');
				return;
			}
			
			if (that.selectedType === 'xuyuan' && !that.wishContent.trim()) {
				app.error('请输入许愿内容');
				return;
			}
			
			that.loading = true;
			app.post('ApiShangxiang/createOrder', {
				type: that.selectedType,
				amount: that.selectedAmount,
				wish_content: that.wishContent,
				custom_amount: that.customAmount || 0,
				bid: that.bid
			}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					// 跳转到支付页面，支付成功后返回上香页面
					app.goto('/pages/pay/pay?id=' + res.data.payorderid + '&tourl=/pagesB/shangxiang/index');
				} else {
					app.error(res.msg || '提交失败');
				}
			});
		},
		
		// 获取默认价格
		getDefaultPrice(type) {
			switch (type) {
				case 'gonghua':
					return this.settings.gonghua_price || 10;
				case 'gongguo':
					return this.settings.gongguo_price || 20;
				case 'shangxiang':
					return this.settings.shangxiang_price || 30;
				case 'xuyuan':
					return this.settings.xuyuan_price || 50;
				default:
					return 10;
			}
		},
		
		// 获取类型名称
		getTypeName(type) {
			switch (type) {
				case 'gonghua':
					return '供花';
				case 'gongguo':
					return '供果';
				case 'shangxiang':
					return '上香';
				case 'xuyuan':
					return '许愿';
				default:
					return '';
			}
		},
		
		// 跳转到我的许愿记录
		goToMyWishes() {
			app.goto('/pagesB/shangxiang/myWishes');
		},
		
		// 跳转到最近订单
		goToRecentOrders() {
			app.goto('/pagesB/shangxiang/recentOrders');
		},
		
		// 页面加载完成
		loaded() {
			var that = this;
			that.isload = true;
			that.textset = app.globalData.textset;
			uni.setNavigationBarTitle({
				title: '上香供奉许愿'
			});
			uni.stopPullDownRefresh();
		},
		
		// 获取类型颜色
		getTypeColor(type) {
			switch (type) {
				case 'gonghua':
					return '#ff6b6b';
				case 'gongguo':
					return '#4ecdc4';
				case 'shangxiang':
					return '#ffe66d';
				case 'xuyuan':
					return '#ff8a80';
				default:
					return '#999';
			}
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return '';
			const date = new Date(time * 1000); // 假设time是时间戳（秒）
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			
			const now = new Date();
			const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
			const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
			const dateOnly = new Date(year, date.getMonth(), date.getDate());
			
			if (dateOnly.getTime() === today.getTime()) {
				return `今天 ${hours}:${minutes}`;
			} else if (dateOnly.getTime() === yesterday.getTime()) {
				return `昨天 ${hours}:${minutes}`;
			} else if (year === now.getFullYear()) {
				return `${month}-${day} ${hours}:${minutes}`;
			} else {
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			}
		}
	}
}
</script>

<style>
/* 添加全局样式设置 */
page,
uni-page-body {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
	background-color: transparent !important;
}

/* 移动端适配 */
/* #ifdef H5 */
uni-page-body {
	overflow-x: hidden;
	min-height: 100vh;
	background-color: transparent !important;
}
/* #endif */

.container {
	position: relative;
	min-height: 100vh;
	padding-bottom: 320rpx;
	overflow-x: hidden;
}

.background-image {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	z-index: -2;
}

.background-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0.6) 100%);
	pointer-events: none;
	z-index: -1;
}

.header {
	text-align: center;
	padding: 120rpx 0 80rpx;
	color: #fff;
	z-index: 1;
	position: relative;
	animation: fadeInDown 0.8s ease-out;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.title {
	font-size: 56rpx;
	font-weight: 700;
	margin-bottom: 24rpx;
	text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5);
	letter-spacing: 2rpx;
}

.subtitle {
	font-size: 32rpx;
	opacity: 0.95;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
	letter-spacing: 1rpx;
}

.bottom-function-container {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background: rgba(0, 0, 0, 0.5);
	padding: 30rpx 0;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.3);
	border-top-left-radius: 32rpx;
	border-top-right-radius: 32rpx;
	z-index: 10;
	backdrop-filter: blur(10px);
}

.function-grid {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
	gap: 20rpx;
	padding: 0 20rpx;
	animation: fadeInUp 0.8s ease-out 0.2s both;
}

.function-item {
	border-radius: 20rpx;
	padding: 24rpx 12rpx;
	text-align: center;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.function-item:active {
	transform: scale(0.96);
}

.function-icon-wrapper {
	width: 100rpx;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 10rpx;
}

.function-icon {
	width: 80rpx;
	height: 80rpx;
	transition: all 0.3s ease;
}

.function-item:active .function-icon {
	transform: scale(0.9);
}

.function-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
	letter-spacing: 1rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.function-price {
	font-size: 28rpx;
	font-weight: 700;
	color: #ffcccc;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.description {
	background: rgba(255, 255, 255, 0.9);
	margin: 0 32rpx 48rpx;
	border-radius: 24rpx;
	padding: 36rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	animation: fadeInUp 0.8s ease-out 0.4s both;
	backdrop-filter: blur(4px);
}

.description text {
	font-size: 30rpx;
	color: #5a6c7d;
	line-height: 1.7;
	letter-spacing: 0.5rpx;
}

.recent-wishes {
	background: rgba(255, 255, 255, 0.9);
	margin: 0 32rpx 48rpx;
	border-radius: 24rpx;
	padding: 36rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	animation: fadeInUp 0.8s ease-out 0.6s both;
	backdrop-filter: blur(4px);
}

.section-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
	font-size: 34rpx;
	font-weight: 600;
	color: #2c3e50;
	letter-spacing: 1rpx;
}

.section-title text {
	position: relative;
}

.section-title text::after {
	content: '';
	position: absolute;
	bottom: -8rpx;
	left: 0;
	width: 60rpx;
	height: 4rpx;
	background: linear-gradient(90deg, #ff6b6b 0%, #ff8a80 100%);
	border-radius: 2rpx;
}

.wish-list {
	max-height: 640rpx;
	overflow-y: auto;
}

.wish-item {
	display: flex;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f5f7fa;
	transition: all 0.3s ease;
}

.wish-item:last-child {
	border-bottom: none;
}

.wish-item:hover {
	background: rgba(255, 107, 107, 0.05);
	border-radius: 12rpx;
	margin: 0 -12rpx;
	padding: 24rpx 12rpx;
}

.user-avatar {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	margin-right: 20rpx;
	border: 3rpx solid rgba(255, 107, 107, 0.2);
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.wish-content {
	flex: 1;
}

.user-info {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.nickname {
	font-size: 28rpx;
	color: #2c3e50;
	font-weight: 500;
	margin-right: 12rpx;
}

.type-tag {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	color: #fff;
	font-weight: 500;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	margin-right: 12rpx;
}

.pay-status {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: 500;
	background: #ff6b6b;
	color: #fff;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.pay-status.paid {
	background: #4ecdc4;
}

.wish-text {
	font-size: 28rpx;
	color: #34495e;
	line-height: 1.5;
	margin-bottom: 8rpx;
}

.wish-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.wish-amount {
	font-size: 26rpx;
	color: #ff6b6b;
	font-weight: 600;
}

.wish-time {
	font-size: 24rpx;
	color: #95a5a6;
}

.type-badge {
	display: inline-block;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	color: #fff;
	margin-bottom: 10rpx;
}

.type-gonghua {
	background: #ff6b6b;
}

.type-gongguo {
	background: #4ecdc4;
}

.type-shangxiang {
	background: #ffe66d;
	color: #333;
}

.type-xuyuan {
	background: #ff8a80;
}

.content {
	font-size: 26rpx;
	color: #333;
	line-height: 1.4;
	margin-bottom: 10rpx;
}

.time {
	font-size: 22rpx;
	color: #999;
}

.my-wishes-entry {
	margin: 0 32rpx 48rpx;
	padding: 32rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	transition: all 0.3s ease;
	animation: fadeInUp 0.8s ease-out 0.8s both;
	backdrop-filter: blur(4px);
}

.my-wishes-entry:active {
	transform: scale(0.98);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.entry-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 16rpx;
}

.my-wishes-entry text {
	flex: 1;
	font-size: 32rpx;
	color: #2c3e50;
	font-weight: 600;
	letter-spacing: 1rpx;
}

.arrow-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 0.6;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.7);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(8rpx);
	animation: fadeIn 0.3s ease-out;
}

.modal-content {
	width: 90%;
	max-width: 640rpx;
	background: rgba(30, 30, 30, 0.95);
	border-radius: 32rpx;
	overflow: hidden;
	box-shadow: 0 24rpx 48rpx rgba(0, 0, 0, 0.5);
	animation: slideInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	backdrop-filter: blur(4px);
	border: 1rpx solid rgba(218, 165, 32, 0.3);
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 36rpx;
	border-bottom: 1rpx solid rgba(218, 165, 32, 0.3);
	background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
}

.modal-title {
	font-size: 38rpx;
	font-weight: 600;
	color: #daa520;
	letter-spacing: 1rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.close-btn {
	font-size: 48rpx;
	color: #daa520;
	width: 64rpx;
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	transition: all 0.3s ease;
	background: rgba(218, 165, 32, 0.1);
}

.close-btn:active {
	background: rgba(218, 165, 32, 0.2);
	transform: scale(0.9);
}

.amount-section, .wish-section {
	padding: 36rpx;
}

.section-label {
	font-size: 32rpx;
	font-weight: 600;
	color: #daa520;
	margin-bottom: 28rpx;
	letter-spacing: 0.5rpx;
	position: relative;
	display: inline-block;
}

.section-label:after {
	content: '';
	position: absolute;
	bottom: -8rpx;
	left: 0;
	width: 40rpx;
	height: 4rpx;
	background: linear-gradient(90deg, #daa520 0%, #e6c570 100%);
	border-radius: 2rpx;
}

.amount-options {
	display: flex;
	gap: 24rpx;
	margin-bottom: 32rpx;
}

.amount-item {
	flex: 1;
	padding: 30rpx 20rpx;
	border: 2rpx solid #333;
	border-radius: 20rpx;
	text-align: center;
	font-size: 32rpx;
	color: #daa520;
	font-weight: 500;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	background: rgba(30, 30, 30, 0.8);
}

.amount-item.active {
	border-color: #daa520;
	color: #1e1e1e;
	background: linear-gradient(135deg, #daa520 0%, #e6c570 100%);
	box-shadow: 0 8rpx 16rpx rgba(218, 165, 32, 0.3);
	transform: translateY(-2rpx);
}

.custom-amount {
	margin-bottom: 32rpx;
}

.custom-label {
	font-size: 28rpx;
	color: #daa520;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.custom-input {
	width: 100%;
	padding: 28rpx;
	border: 2rpx solid #333;
	border-radius: 20rpx;
	font-size: 30rpx;
	box-sizing: border-box;
	background: rgba(30, 30, 30, 0.8);
	transition: all 0.3s ease;
	color: #daa520;
}

.custom-input:focus {
	border-color: #daa520;
	background: rgba(30, 30, 30, 0.9);
	box-shadow: 0 0 0 4rpx rgba(218, 165, 32, 0.1);
}

.wish-input {
	width: 100%;
	height: 240rpx;
	padding: 28rpx;
	border: 2rpx solid #333;
	border-radius: 20rpx;
	font-size: 30rpx;
	box-sizing: border-box;
	resize: none;
	background: rgba(30, 30, 30, 0.8);
	transition: all 0.3s ease;
	line-height: 1.6;
	color: #daa520;
}

.wish-input:focus {
	border-color: #daa520;
	background: rgba(30, 30, 30, 0.9);
	box-shadow: 0 0 0 4rpx rgba(218, 165, 32, 0.1);
}

.char-count {
	text-align: right;
	font-size: 26rpx;
	color: #daa520;
	margin-top: 12rpx;
	font-weight: 500;
	opacity: 0.8;
}

.modal-footer {
	padding: 36rpx;
	border-top: 1rpx solid rgba(218, 165, 32, 0.3);
	background: rgba(30, 30, 30, 0.9);
}

.confirm-btn {
	width: 100%;
	padding: 32rpx;
	border-radius: 100rpx;
	text-align: center;
	font-size: 32rpx;
	color: #1e1e1e;
	font-weight: 600;
	border: none;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.4);
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	letter-spacing: 2rpx;
	background: linear-gradient(90deg, #daa520 0%, #e6c570 100%);
}

.confirm-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.5);
	opacity: 0.9;
}

/* 动画效果 */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes fadeInDown {
	from {
		opacity: 0;
		transform: translateY(-60rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(60rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(100rpx) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

/* 滚动条样式优化 */
.wish-list::-webkit-scrollbar {
	width: 8rpx;
}

.wish-list::-webkit-scrollbar-track {
	background: rgba(0, 0, 0, 0.1);
	border-radius: 4rpx;
}

.wish-list::-webkit-scrollbar-thumb {
	background: rgba(255, 107, 107, 0.3);
	border-radius: 4rpx;
}

.wish-list::-webkit-scrollbar-thumb:hover {
	background: rgba(255, 107, 107, 0.5);
}

.floating-buttons {
	position: fixed;
	top: 120rpx;
	right: 30rpx;
	z-index: 100;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.floating-btn {
	width: 140rpx;
	height: 140rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
	transition: all 0.3s ease;
	backdrop-filter: blur(4px);
}

.floating-btn:active {
	transform: scale(0.95);
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
}

.btn-text {
	font-size: 24rpx;
	color: #2c3e50;
	font-weight: 500;
}
</style>