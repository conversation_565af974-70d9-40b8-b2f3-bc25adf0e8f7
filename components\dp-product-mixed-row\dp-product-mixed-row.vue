<template>
<view class="dp-product-mixed-row">
	<!-- 标题栏 -->
	<view class="mixed-header" v-if="params && (params.main_title || params.sub_title)">
		<view class="bgstyle" v-if="params.bgimg">
			<image :src="params.bgimg" mode="aspectFill" class="bgimg"></image>
		</view>
		<view class="mixed-header-content">
			<view class="left">
				<text class="main-title" v-if="params.main_title" :style="{color: params.main_title_color || '#333'}">{{params.main_title}}</text>
				<text class="sub-title" v-if="params.sub_title" :style="{color: params.sub_title_color || '#999'}">{{params.sub_title}}</text>
			</view>
			<view class="right" v-if="params.more_text" @tap="goto" :data-url="params.hrefurl || ''">
				<text class="more-text" :style="{color: params.more_text_color || '#999'}">{{params.more_text}}</text>
				<text class="iconfont icon_arrowright_fill" :style="{color: params.more_text_color || '#999'}"></text>
			</view>
		</view>
	</view>
	
	<!-- 内容区域 -->
	<view class="mixed-content">
		<view v-for="(item, index) in list" :key="getItemKey(item)" class="mixed-item" 
			:class="{
				'article-container': item.content_type === 'article',
				'video-container': item.content_type === 'video',
				'product-container': !item.content_type
			}">
			<!-- 商品 -->
			<view v-if="!item.content_type" class="product-item" @tap="goto" :data-url="'/shopPackage/shop/product?id=' + item[idfield]">
				<image class="product-image" :src="item.pic" mode="aspectFill"></image>
				<image class="saleimg" :src="saleimg" v-if="saleimg !== ''"></image>
				<view class="product-info">
					<text class="product-name" v-if="showname == 1">{{item.name}}</text>
					<view class="price-box" v-if="showprice != '0' && (item.price_type != 1 || item.sell_price > 0)">
						<text class="sell-price">¥{{item.sell_price}}</text>
						<text class="market-price" v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">¥{{item.market_price}}</text>
					</view>
					<view class="price-box" v-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0">
						<text class="inquiry-price">询价</text>
						<view class="contact-btn" @tap.stop="showLinkChange" 
							:data-lx_name="item.lx_name" 
							:data-lx_bid="item.lx_bid" 
							:data-lx_bname="item.lx_bname" 
							:data-lx_tel="item.lx_tel" 
							data-btntype="2">
							{{item.xunjia_text || '联系TA'}}
						</view>
					</view>
					<view class="sales-info" v-if="(showsales=='1' && item.sales>0) || showstock=='1'">
						<text v-if="showsales=='1' && item.sales>0">已售{{item.sales}}</text>
						<text v-if="(showsales=='1' && item.sales>0) && showstock=='1'" class="separator">|</text>
						<text v-if="showstock=='1'">仅剩{{item.stock}}</text>
					</view>
					<view class="cart-btn" v-if="showcart=='1' && !item.price_type" @tap.stop="buydialogChange" :data-proid="item[idfield]">
						<text class="iconfont icon_gouwuche"></text>
					</view>
					<view class="cart-btn" v-if="showcart=='2' && !item.price_type" @tap.stop="buydialogChange" :data-proid="item[idfield]">
						<image :src="cartimg" class="cart-icon"></image>
					</view>
				</view>
			</view>
			
			<!-- 文章 -->
			<view v-if="item.content_type === 'article'" class="article-item" @tap="goto" :data-url="'/pages/article/detail?id=' + item.article_id">
				<view class="article-image-container">
					<image class="article-image" :src="item.cover_img || item.coverimg" mode="aspectFill"></image>
					<view class="article-tag">文章</view>
				</view>
				<view class="article-info">
					<text class="article-title">{{item.title}}</text>
					<view class="article-meta">
						<text class="article-date">{{formatDate(item.publish_time || item.created_at)}}</text>
						<text class="article-views">{{item.view_num || 0}}阅读</text>
					</view>
				</view>
			</view>
			
			<!-- 视频 -->
			<view v-if="item.content_type === 'video'" class="video-item" @tap="goto" :data-url="'/activity/shortvideo/detail?id=' + item.videoId">
				<view class="video-image-container">
					<image class="video-image" :src="item.coverimg" mode="aspectFill"></image>
					<view class="video-play-icon">
						<text class="iconfont icon_bofang"></text>
					</view>
					<view class="video-tag">视频</view>
				</view>
				<view class="video-info">
					<text class="video-title">{{item.title || '精彩短视频'}}</text>
					<view class="video-meta">
						<text class="video-view-count">
							<text class="iconfont icon_bofang1"></text>
							{{item.view_num || 0}}
						</text>
						<text class="video-like-count">
							<text class="iconfont icon_dianzan"></text>
							{{item.zan_num || 0}}
						</text>
					</view>
				</view>
			</view>
		</view>
	</view>
	
	<!-- 弹窗组件 -->
	<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange" :menuindex="menuindex"></buydialog>
	<view class="link-dialog" v-if="showLinkStatus">
		<view class="link-dialog-main">
			<view class="link-dialog-close" @tap="showLinkChange">
				<image class="link-dialog-close-icon" src="/static/img/close.png"></image>
			</view>
			<view class="link-dialog-content">
				<view class="link-dialog-title">{{lx_name}}</view>
				<view class="link-dialog-row" v-if="lx_bid > 0">
					<view class="link-dialog-label">店铺名称</view>
					<view class="link-dialog-value" @tap="goto" :data-url="'/pagesExt/business/index?id='+lx_bid">
						{{lx_bname}}
						<image src="/static/img/arrowright.png" class="link-dialog-arrow"></image>
					</view>
				</view>
				<view class="link-dialog-row" v-if="lx_tel">
					<view class="link-dialog-label">联系电话</view>
					<view class="link-dialog-value" @tap="goto" :data-url="'tel::'+lx_tel">
						{{lx_tel}}
						<image src="/static/img/copy.png" class="link-dialog-copy" @tap.stop="copy" :data-text="lx_tel"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
</view>
</template>

<script>
export default {
	props: {
		list: {
			type: Array,
			required: true
		},
		saleimg: {
			default: ''
		},
		showname: {
			default: 1
		},
		showprice: {
			default: '1'
		},
		showsales: {
			default: '1'
		},
		showcart: {
			default: '1'
		},
		cartimg: {
			default: '/static/imgsrc/cart.svg'
		},
		showstock: {
			default: '0'
		},
		showbname: {
			default: '0'
		},
		showcoupon: {
			default: '0'
		},
		idfield: {
			type: String,
			default: "id"
		},
		menuindex: {
			default: -1
		},
		probgcolor: {
			default: '#fff'
		},
		params: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			buydialogShow: false,
			proid: 0,
			showLinkStatus: false,
			lx_name: '',
			lx_bid: '',
			lx_bname: '',
			lx_tel: ''
		}
	},
	mounted() {
		// 通知父组件图片已加载完成
		this.$nextTick(() => {
			setTimeout(() => {
				this.$emit('image-load');
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-row][mounted_001] 混合行布局组件加载完成');
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-mixed-row][mounted_002] 修复了微信小程序中的类绑定语法问题');
			}, 300);
		});
	},
	methods: {
		// 获取项目的唯一键
		getItemKey(item) {
			if (item.content_type === 'article') {
				return 'article_' + (item.article_id || item.id);
			} else if (item.content_type === 'video') {
				return 'video_' + (item.videoId || item.id);
			} else {
				return 'product_' + item[this.idfield];
			}
		},
		
		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '';
			const date = new Date(parseInt(timestamp) * 1000);
			return date.getFullYear() + '-' + 
				('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
				('0' + date.getDate()).slice(-2);
		},
		
		// 购买弹窗
		buydialogChange(e) {
			if (!this.buydialogShow) {
				this.proid = e.currentTarget.dataset.proid;
			}
			this.buydialogShow = !this.buydialogShow;
		},
		
		// 添加到购物车
		addcart() {
			this.$emit('addcart');
		},
		
		// 联系商家
		showLinkChange(e) {
			this.showLinkStatus = !this.showLinkStatus;
			if (e.currentTarget.dataset) {
				this.lx_name = e.currentTarget.dataset.lx_name;
				this.lx_bid = e.currentTarget.dataset.lx_bid;
				this.lx_bname = e.currentTarget.dataset.lx_bname;
				this.lx_tel = e.currentTarget.dataset.lx_tel;
			}
		},
		
		// 复制文本
		copy(e) {
			uni.setClipboardData({
				data: e.currentTarget.dataset.text,
				success: () => {
					uni.showToast({
						title: '复制成功'
					});
				}
			});
		},
		
		// 页面跳转
		goto(e) {
			let url = e.currentTarget.dataset.url;
			if (!url) return;
			
			if (url.startsWith('tel::')) {
				// 拨打电话
				uni.makePhoneCall({
					phoneNumber: url.replace('tel::', '')
				});
			} else {
				// 页面跳转
				uni.navigateTo({
					url: url
				});
			}
		}
	}
}
</script>

<style>
.dp-product-mixed-row {
	width: 100%;
	background-color: #fff;
	position: relative;
}

/* 头部样式 */
.mixed-header {
	position: relative;
	width: 100%;
	height: 88rpx;
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.mixed-header .bgstyle {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
	overflow: hidden;
}

.mixed-header .bgimg {
	width: 100%;
	height: 100%;
}

.mixed-header-content {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
	position: relative;
	z-index: 2;
}

.mixed-header .left {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.mixed-header .main-title {
	font-size: 32rpx;
	font-weight: bold;
	line-height: 1.2;
}

.mixed-header .sub-title {
	font-size: 24rpx;
	line-height: 1.2;
	margin-top: 6rpx;
}

.mixed-header .right {
	display: flex;
	align-items: center;
}

.mixed-header .more-text {
	font-size: 24rpx;
	margin-right: 6rpx;
}

/* 内容区域样式 */
.mixed-content {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	justify-content: space-between;
}

.mixed-item {
	width: 48.5%;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	overflow: hidden;
	background-color: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 商品样式 */
.product-item {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	position: relative;
}

.product-image {
	width: 100%;
	height: 320rpx;
	background-color: #f5f5f5;
}

.saleimg {
	position: absolute;
	top: 0;
	left: 0;
	width: 80rpx;
	height: 80rpx;
	z-index: 1;
}

.product-info {
	padding: 16rpx;
	position: relative;
}

.product-name {
	font-size: 28rpx;
	line-height: 1.3;
	margin-bottom: 12rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.price-box {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.sell-price {
	font-size: 30rpx;
	color: #f5222d;
	font-weight: bold;
}

.market-price {
	font-size: 24rpx;
	color: #999;
	text-decoration: line-through;
	margin-left: 10rpx;
}

.inquiry-price {
	font-size: 30rpx;
	color: #f5222d;
}

.contact-btn {
	font-size: 24rpx;
	background-color: #f5222d;
	color: #fff;
	border-radius: 24rpx;
	padding: 0 16rpx;
	margin-left: 10rpx;
	line-height: 40rpx;
	height: 40rpx;
}

.sales-info {
	font-size: 24rpx;
	color: #999;
}

.separator {
	margin: 0 8rpx;
}

.cart-btn {
	position: absolute;
	right: 16rpx;
	bottom: 16rpx;
	width: 60rpx;
	height: 60rpx;
	background-color: rgba(245, 34, 45, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #f5222d;
}

.cart-icon {
	width: 36rpx;
	height: 36rpx;
}

/* 文章样式 */
.article-item {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.article-image-container {
	width: 100%;
	height: 320rpx;
	position: relative;
	background-color: #f5f5f5;
}

.article-image {
	width: 100%;
	height: 100%;
}

.article-tag {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
}

.article-info {
	padding: 16rpx;
}

.article-title {
	font-size: 28rpx;
	line-height: 1.4;
	margin-bottom: 10rpx;
	font-weight: bold;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.article-meta {
	display: flex;
	justify-content: space-between;
	font-size: 24rpx;
	color: #999;
}

/* 视频样式 */
.video-item {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.video-image-container {
	width: 100%;
	height: 320rpx;
	position: relative;
	background-color: #f5f5f5;
}

.video-image {
	width: 100%;
	height: 100%;
}

.video-play-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 80rpx;
	height: 80rpx;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 36rpx;
}

.video-tag {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
}

.video-info {
	padding: 16rpx;
}

.video-title {
	font-size: 28rpx;
	line-height: 1.4;
	margin-bottom: 10rpx;
	font-weight: bold;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.video-meta {
	display: flex;
	font-size: 24rpx;
	color: #999;
}

.video-view-count {
	display: flex;
	align-items: center;
	margin-right: 20rpx;
}

.video-like-count {
	display: flex;
	align-items: center;
}

.video-view-count .iconfont,
.video-like-count .iconfont {
	margin-right: 4rpx;
}

/* 弹窗样式 */
.link-dialog {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.link-dialog-main {
	width: 600rpx;
	background-color: #fff;
	border-radius: 12rpx;
	position: relative;
}

.link-dialog-close {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.link-dialog-close-icon {
	width: 32rpx;
	height: 32rpx;
}

.link-dialog-content {
	padding: 40rpx;
}

.link-dialog-title {
	font-size: 32rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 30rpx;
}

.link-dialog-row {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
	padding-bottom: 20rpx;
}

.link-dialog-label {
	width: 160rpx;
	font-size: 28rpx;
	color: #666;
}

.link-dialog-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	display: flex;
	align-items: center;
}

.link-dialog-arrow {
	width: 24rpx;
	height: 24rpx;
	margin-left: 8rpx;
}

.link-dialog-copy {
	width: 32rpx;
	height: 32rpx;
	margin-left: 12rpx;
}
</style> 