<template>
<view class="container">
	<dd-tab :itemdata="['全部('+countall+')','待审核('+count0+')','已通过('+count1+')','已驳回('+count2+')','已完成('+count3+')']" :itemst="['all','0','1','2','3']" :st="st" :isfixed="true" @changetab="changetab"></dd-tab>
	<view style="width:100%;height:100rpx"></view>
	
	<view class="topsearch flex-y-center">
		<view class="f1 flex-y-center">
			<image class="img" src="/static/img/search_ico.png"></image>
			<input :value="search.orderNo" placeholder="输入订单号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
		</view>
	</view>

	<view class="order-content">
		<block v-for="(item, index) in datalist" :key="index">
			<view class="order-box">
				<view class="order-header">
					<text class="order-no">订单号: {{item.order_no}}</text>
					<text class="order-status" :style="{color: getStatusColor(item.status)}">{{getStatusText(item.status)}}</text>
				</view>
				<view class="purchase-info" v-if="item.purchase_order_no">
					<text class="purchase-label">关联进货单号:</text>
					<text class="purchase-value">{{item.purchase_order_no}}</text>
				</view>
				<view class="order-info">
					<view class="info-item">
						<text class="label">退款金额:</text>
						<text class="value">￥{{item.total_price}}</text>
					</view>
					<view class="info-item">
						<text class="label">退货数量:</text>
						<text class="value">{{item.product_count}}种</text>
					</view>
					<view class="info-item">
						<text class="label">申请时间:</text>
						<text class="value">{{item.create_time}}</text>
					</view>
					<view class="info-item" v-if="item.audit_time">
						<text class="label">审核时间:</text>
						<text class="value">{{item.audit_time}}</text>
					</view>
					<view class="info-item" v-if="item.audit_remark">
						<text class="label">审核备注:</text>
						<text class="value">{{item.audit_remark}}</text>
					</view>
				</view>
				<view class="order-footer">
					<view class="btn" @tap="viewDetail" :data-id="item.id">查看详情</view>
				</view>
			</view>
		</block>
	</view>

	<view class="bottom-btn-container">
		<view class="bottom-btn primary" @tap="createReturnOrder">申请退货</view>
	</view>

	<loading v-if="loading"></loading>
	<nomore v-if="nomore"></nomore>
	<nodata v-if="nodata"></nodata>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      st: 'all',
      datalist: [],
      pagenum: 1,
      nodata: false,
      nomore: false,
      loading: false,
      count0: 0,  // 待审核
      count1: 0,  // 已通过
      count2: 0,  // 已驳回
      count3: 0,  // 已完成
      countall: 0,
      search: {
        orderNo: ''
      },
      isRefreshing: false, // 是否正在下拉刷新
      hasMore: true       // 是否还有更多数据
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  onPullDownRefresh: function () {
    if (this.isRefreshing) {
      uni.stopPullDownRefresh();
      return;
    }
    
    this.isRefreshing = true;
    this.pagenum = 1;
    this.hasMore = true;
    this.getdata(false, true);
  },
  onReachBottom: function () {
    if (this.loading || !this.hasMore || this.nodata) {
      return;
    }
    
    this.pagenum = this.pagenum + 1;
    this.getdata(true);
  },
  methods: {
    changetab: function (st) {
      var that = this;
      that.st = st;
      that.pagenum = 1;
      that.hasMore = true;
      that.getdata();
    },
    getdata: function (loadmore, isRefresh) {
      if (this.loading && !isRefresh) {
        return;
      }
      
      if (!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
        this.hasMore = true;
      }
      var that = this;
      var pagenum = that.pagenum;
      var st = that.st;
      that.nodata = false;
      that.nomore = false;
      that.loading = true;
      
      app.post('ApiAdminPurchase/getReturnOrders', {
        pagenum: pagenum,
        status: st === 'all' ? '' : st,
        order_no: that.search.orderNo || ''
      }, function (res) {
        that.loading = false;
        
        if (isRefresh) {
          that.isRefreshing = false;
          uni.stopPullDownRefresh();
        }
        
        if (res.code === 0) {
          var data = res.data.list || [];
          
          if (pagenum == 1) {
            console.log('退货订单统计数据:', res.data);
            
            that.count0 = parseInt(res.data.count0 || 0);
            that.count1 = parseInt(res.data.count1 || 0);
            that.count2 = parseInt(res.data.count2 || 0);
            that.count3 = parseInt(res.data.count3 || 0);
            that.countall = parseInt(res.data.total || 0);
            
            if (that.countall === 0 && data.length > 0) {
              console.log('API未返回退货统计数据，手动计算');
              that.countall = data.length;
              
              var count0 = 0, count1 = 0, count2 = 0, count3 = 0;
              data.forEach(function(item) {
                if (item.status == 0) count0++;
                else if (item.status == 1) count1++;
                else if (item.status == 2) count2++;
                else if (item.status == 3) count3++;
              });
              
              that.count0 = count0;
              that.count1 = count1;
              that.count2 = count2;
              that.count3 = count3;
            }
            
            that.datalist = data;
            
            if (data.length == 0) {
              that.nodata = true;
              that.hasMore = false;
            }
          } else {
            if (data.length == 0) {
              that.nomore = true;
              that.hasMore = false;
            } else {
              var datalist = that.datalist;
              var newdata = datalist.concat(data);
              that.datalist = newdata;
              
              if (that.countall > 0 && that.datalist.length >= that.countall) {
                that.hasMore = false;
                that.nomore = true;
              }
            }
          }
        } else {
          app.error(res.msg);
          that.nodata = true;
          that.hasMore = false;
        }
      });
    },
    searchConfirm: function(e) {
      this.search.orderNo = e.detail.value;
      this.pagenum = 1;
      this.hasMore = true;
      this.getdata(false);
    },
    getStatusText: function(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回',
        3: '已完成'
      };
      return statusMap[status] || '未知状态';
    },
    getStatusColor: function(status) {
      const colorMap = {
        0: '#FF9800', // 待审核 - 橙色
        1: '#4CAF50', // 已通过 - 绿色
        2: '#F44336', // 已驳回 - 红色
        3: '#2196F3'  // 已完成 - 蓝色
      };
      return colorMap[status] || '#999999';
    },
    viewDetail: function(e) {
      var id = e.currentTarget.dataset.id;
      app.goto('detail?id=' + id);
    },
    createReturnOrder: function() {
      app.goto('selectorder');
    }
  }
};
</script>

<style>
.container{ width:100%;}
.topsearch{width:94%;margin:10rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.order-content{display:flex;flex-direction:column}
.order-box{width:94%;margin:10rpx 3%;padding:20rpx;background:#fff;border-radius:8px;box-shadow:0 2px 5px rgba(0,0,0,0.05)}

.order-header{display:flex;justify-content:space-between;border-bottom:1px solid #f0f0f0;padding-bottom:15rpx;margin-bottom:15rpx}
.order-no{font-size:28rpx;color:#333;font-weight:bold}
.order-status{font-size:28rpx}

.purchase-info{background:#f9f9f9;padding:15rpx;border-radius:8rpx;margin-bottom:15rpx;display:flex;align-items:center}
.purchase-label{font-size:26rpx;color:#666;margin-right:10rpx}
.purchase-value{font-size:26rpx;color:#1989fa;flex:1}

.order-info{padding:10rpx 0}
.info-item{display:flex;margin-bottom:10rpx}
.info-item .label{width:160rpx;font-size:26rpx;color:#666}
.info-item .value{flex:1;font-size:26rpx;color:#333}

.order-footer{display:flex;justify-content:flex-end;margin-top:20rpx;padding-top:15rpx;border-top:1px solid #f0f0f0}
.btn{padding:10rpx 25rpx;font-size:24rpx;background:#f5f5f5;color:#333;border-radius:30rpx;margin-left:20rpx}
.btn.primary{background:#1989fa;color:#fff}

.bottom-btn-container{position:fixed;bottom:0;left:0;width:100%;padding:20rpx;background:#fff;border-top:1px solid #f0f0f0;z-index:100}
.bottom-btn{width:100%;height:80rpx;line-height:80rpx;text-align:center;border-radius:40rpx;font-size:28rpx}
.bottom-btn.primary{background:#1989fa;color:#fff}
</style> 