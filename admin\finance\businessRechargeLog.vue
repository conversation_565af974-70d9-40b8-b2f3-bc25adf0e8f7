<template>
<view>
	<block v-if="isload">
		<view class="topsearch flex-y-center">
			<picker mode="date" :value="start_date" @change="startDateChange" class="date-picker">
				<view class="picker-text">{{start_date || '开始日期'}}</view>
			</picker>
			<text class="date-separator">至</text>
			<picker mode="date" :value="end_date" @change="endDateChange" class="date-picker">
				<view class="picker-text">{{end_date || '结束日期'}}</view>
			</picker>
			<view class="search-btn" @tap="getdata">查询</view>
		</view>
		
		<view class="tab-nav">
			<view 
				v-for="(item, index) in tabs" 
				:key="index" 
				class="tab-item" 
				:class="{'active': status === item.value}"
				@tap="changeTab(item.value)"
			>
				{{item.name}}
			</view>
		</view>
		
		<view class="statistics-bar">
			<view class="statistics-item">
				<text class="label">充值总金额：</text>
				<text class="value">￥{{statistics.total_amount || '0.00'}}</text>
			</view>
			<view class="statistics-item">
				<text class="label">已通过金额：</text>
				<text class="value">￥{{statistics.approved_amount || '0.00'}}</text>
			</view>
		</view>
		
		<view class="content" v-if="datalist && datalist.length > 0">
			<view class="label">
				<text class="t1">充值记录（共{{total}}条）</text>
			</view>
			<view v-for="(item, index) in datalist" :key="index" class="item" @tap="gotoDetail(item.id)">
				<view class="item-header">
					<text class="amount">￥{{item.amount}}</text>
					<text class="status" :class="'status-'+item.status">{{item.status_text}}</text>
				</view>
				<view class="item-body">
					<view class="item-row">
						<text class="label">支付方式：</text>
						<text class="value">{{item.pay_type_text}}</text>
					</view>
					<view class="item-row">
						<text class="label">申请时间：</text>
						<text class="value">{{item.create_time}}</text>
					</view>
					<view class="item-row" v-if="item.status != 0">
						<text class="label">审核时间：</text>
						<text class="value">{{item.audit_time || '-'}}</text>
					</view>
					<view class="item-row" v-if="item.remark">
						<text class="label">备注说明：</text>
						<text class="value">{{item.remark}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<nodata v-if="nodata"></nodata>
		<nomore v-if="nomore"></nomore>
		
		<view class="floating-btn" @tap="gotoRecharge">
			<image src="/static/img/add.png" class="add-icon"></image>
			<text>申请充值</text>
		</view>
	</block>
	
	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
			loading: false,
      isload: false,
			pre_url: app.globalData.pre_url,
			
			// 筛选参数
			status: '', // 状态筛选
			start_date: '', // 开始日期
			end_date: '', // 结束日期
			
			// 数据列表
			pagenum: 1,
			total: 0,
      datalist: [],
      nodata: false,
      nomore: false,
			
			// 统计信息
			statistics: {
				total_amount: 0,
				approved_amount: 0
			},
			
			// 状态选项卡
			tabs: [
				{name: '全部', value: ''},
				{name: '待审核', value: '0'},
				{name: '已通过', value: '1'},
				{name: '已拒绝', value: '2'}
			]
    };
  },
  
  onLoad: function () {
		this.getdata();
  },
	
	onPullDownRefresh: function () {
		this.getdata();
	},
	
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  
  methods: {
		// 开始日期变更
		startDateChange(e) {
			this.start_date = e.detail.value;
		},
		
		// 结束日期变更
		endDateChange(e) {
			this.end_date = e.detail.value;
		},
		
		// 切换状态选项卡
		changeTab(status) {
			this.status = status;
			this.getdata();
		},
		
		// 获取充值记录数据
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var status = that.status;
			var start_time = that.start_date;
			var end_time = that.end_date;
			
			that.nodata = false;
			that.nomore = false;
			that.loading = true;
      
			// 构建请求参数
			const params = {
				page: pagenum,
				limit: 20,
				status: status
			};
			
			if (start_time) {
				params.start_time = start_time;
			}
			
			if (end_time) {
				params.end_time = end_time;
			}
			
			app.post('ApiAdminFinance/businessRechargeLog', params, function (res) {
				that.loading = false;
				if (res.status !== 1) {
					app.alert(res.msg || '获取数据失败');
					return;
				}
				
				var data = res.data.list;
				that.statistics = res.data.statistics || {
					total_amount: 0,
					approved_amount: 0
				};
				
        if (pagenum == 1){
					that.total = res.data.total || 0;
					that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.isload = true;
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            that.datalist = that.datalist.concat(data);
          }
        }
      });
    },
		
		// 跳转到充值详情
		gotoDetail(id) {
			uni.navigateTo({
				url: 'businessRechargeDetail?id=' + id
			});
		},
		
		// 跳转到充值申请页面
		gotoRecharge() {
			uni.navigateTo({
				url: 'businessRecharge'
			});
		}
  }
};
</script>

<style>
@import "../common.css";

.topsearch {
	width: 94%;
	margin: 16rpx 3%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.date-picker {
	width: 35%;
	height: 60rpx;
	background-color: #fff;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #333;
}

.picker-text {
	width: 100%;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.date-separator {
	color: #666;
	margin: 0 10rpx;
}

.search-btn {
	width: 20%;
	height: 60rpx;
	background-color: #FC5648;
	color: #fff;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
}

.tab-nav {
	width: 94%;
	margin: 0 3%;
	display: flex;
	height: 80rpx;
	background-color: #fff;
	border-radius: 40rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.tab-item {
	flex: 1;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #666;
}

.tab-item.active {
	background-color: #FC5648;
	color: #fff;
}

.statistics-bar {
	width: 94%;
	margin: 0 3% 20rpx;
	display: flex;
	justify-content: space-between;
	background-color: #f8f8f8;
	padding: 20rpx;
	border-radius: 10rpx;
}

.statistics-item {
	font-size: 24rpx;
	color: #666;
}

.statistics-item .value {
	color: #FC5648;
	font-weight: bold;
}

.content {
	width: 94%;
	margin: 0 3%;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.content .label {
	display: flex;
	width: 100%;
	padding: 24rpx 16rpx;
	color: #333;
	border-bottom: 1px solid #f5f5f5;
}

.content .label .t1 {
	flex: 1;
	font-size: 28rpx;
}

.content .item {
	width: 100%;
	padding: 20rpx;
	border-bottom: 1px solid #f5f5f5;
}

.content .item:last-child {
	border-bottom: none;
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.item-header .amount {
	font-size: 34rpx;
	font-weight: bold;
	color: #FC5648;
}

.item-header .status {
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.status-0 {
	background-color: #FFF1E6;
	color: #FF9500;
}

.status-1 {
	background-color: #E6FFF1;
	color: #09BB07;
}

.status-2 {
	background-color: #F1F1F1;
	color: #999;
}

.item-body {
	font-size: 26rpx;
	color: #666;
}

.item-row {
	display: flex;
	margin-bottom: 6rpx;
}

.item-row .label {
	width: 160rpx;
	padding: 0;
	border: none;
}

.item-row .value {
	flex: 1;
}

.floating-btn {
	position: fixed;
	right: 40rpx;
	bottom: 80rpx;
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background-color: #FC5648;
	color: #fff;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}

.floating-btn .add-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 6rpx;
}
</style> 