<template>
	<view class="content">
		<view class="header">
			<view class="top">
				<view class="title-box">
					<view class="title">高压办电</view>
					<view>帮代办、少跑腿</view>
				</view>
				<image src="@/static/highVoltage/icon1.png" class="logo"></image>
			</view>
			<valtageTab :status="detailInfo.status"></valtageTab>
		</view>
		<view class="container">
			

			<view style="padding: 10px;">
				
				<view v-if="detailInfo.formdata && detailInfo.formdata[0]">
				  {{detailInfo.formdata[0][0]}}:{{detailInfo.formdata[0][1]}}
				</view>
				<view v-else>数据不完整</view>
				
				<view v-if="detailInfo.formdata && detailInfo.formdata[1]">
				  {{detailInfo.formdata[1][0]}}:{{detailInfo.formdata[1][1]}}
				</view>
				<view v-else>数据不完整</view>
				
				<view v-if="detailInfo.formdata && detailInfo.formdata[2]">
				  {{detailInfo.formdata[2][0]}}:{{detailInfo.formdata[2][1]}}
				</view>
				<view v-else>数据不完整</view>
				
				<view v-if="detailInfo.formdata && detailInfo.formdata[3]">
				  {{detailInfo.formdata[3][0]}}:{{detailInfo.formdata[3][1]}}
				</view>
				<view v-else>数据不完整</view>

			</view>
			
			<view class="title" style="padding-left: 10px;">办电流程</view>
			<processSchedule :status="detailInfo.status" :data="detailInfo" @apply="applyVoltage">
			</processSchedule>
		</view>
		<!-- 		<view class="form">
			<view class="form-item box" v-for="(value, key) in detailInfo.formdata" :key="key">
				<view class="title">{{ value[0] }}</view>
				<view class="value">{{ value[1] }}</view>
			</view>
			<template v-if="detailInfo.status === 5">
				<view class="form-item box">
					<view class="title">施工单位</view>
					<input class="input" placeholder="请输入" v-model="formData.shigongdanwei" />
				</view>
				<view class="form-item box">
					<view class="title">报验内容</view>
					<textarea class="textarea input" v-model="formData.baoyanneirong" placeholder="请输入" />
				</view>
			</template>
		</view> -->
		<!-- <view class="btn" @click="commitSend" v-if="detailInfo.status === 5">申请送电</view> -->
		<view class="btn back-i" @click="back">返回</view>
	</view>
</template>

<script>
	import siteinfo from '../../siteinfo.js';
	var app = getApp();
	export default {
		data() {
			return {
				// 订单详情数据
				detailInfo: {},
				// 申请送电表单
				formData: {},
				id: null,
				delta: 1
			};
		},
		onLoad({
			id,
			delta
		}) {
			this.delta = delta;
			this.id = id;
			this.getDetail();
			console.log(siteinfo);
		},
		onPullDownRefresh() {
			this.getDetail();
		},
		methods: {
			back() {
				/**
				 * @method 返回上一页
				 */
				uni.navigateBack({
					delta: Number(this.delta)
				});
			},
			getDetail() {
				/**
				 * @method  获取详情
				 */
				const _this = this;
				app.post(
					'ApiShenqingbandian/orderdetail', {
						id: this.id
					},
					({
						detail
					}) => {
						
						console.log(detail);
						
						_this.detailInfo = detail;
						uni.stopPullDownRefresh();
					}
				);
			},
			commitSend() {
				/**
				 * @method 申请送电
				 */
				const _this = this;
				app.post('ApiShenqingbandian/orderCollect', this.formData, ({
					msg
				}) => {
					uni.showToast({
						title: msg,
						icon: 'none'
					});
					_this.getDetail();
				});
			},
			applyVoltage() {
				uni.navigateTo({
					url: '/pagesExt/highVoltage/electricityApply?id=' + this.id
				});
			}
		}
	};
</script>

<style lang="scss">
	@import 'styled.scss';
</style>