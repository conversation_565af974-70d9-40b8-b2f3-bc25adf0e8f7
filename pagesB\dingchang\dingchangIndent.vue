<template>
	<view>
		<view class="option">
			<view class="option_bt" :class="[selectedOption=='0'? 'option_get':'']" @click="selectedOption='0'" >全部</view>
			<view class="option_bt" :class="[selectedOption=='1'? 'option_get':'']" @click="selectedOption='1'">待评价</view>
		</view>
		<view class="jc_center">
			<view class="content">
				<view class="message">
					<view class="spaceBetween">
						<view class="message_txt1_l">
							<text>金满贯乒羽嘉西店</text>
							<text>></text>
						</view>
						<view class="message_txt1_r">未支付</view>
					</view>
					<view class="spaceBetween" style="margin-top: 30rpx;">
						<view class="message_txt2_l">羽毛球</view>
						<view class="message_txt2_r">总价：65.00</view>
					</view>
					<view class="spaceBetween" style="margin-top: 15rpx;">
						<view class="message_txt2_l">1号场</view>
						<view class="message_txt2_r">1个场次</view>
					</view>
					<view class="spaceBetween" style="margin-top: 15rpx;">
						<view class="message_txt2_l">2024-01-17</view>
						<view class="message_txt2_r">开始时间：13:00</view>
					</view>
					<view class="message_bt">
						<view class="message_bt_l jc_center">撤销订单</view>
						<view class="message_bt_r jc_center">去支付</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedOption:'0'
			}
		},
		methods: {

		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f5f5;
	}

	.jc_center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.alignItems {
		display: flex;
		align-items: center;
	}

	.spaceBetween {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.option{
		padding-top: 50rpx;
		background-color: #ffffff;
		display: flex; 
		justify-content: space-evenly; 
		flex-wrap: wrap;
		.option_get{
			color: #0000ff;
			border-bottom: 5rpx solid #0000ff;
		}
		.option_bt{
			padding-bottom: 10rpx;
			
		}
	}
	.content {
		width: 90%;
		padding-top: 50rpx;
	}

	.message {
		background-color: #ffffff;
		padding: 20rpx 50rpx 50rpx;
		border-radius: 20rpx;

		.message_txt1_l {
			font-weight: bold;
			font-size: 32rpx;
		}

		.message_txt1_l :last-child {
			color: #999999;
			font-weight: normal;
		}

		.message_txt1_r {
			color: #4c3be5;
			font-size: 28rpx;
		}

		.message_txt2_l {
			color: #999999;
			width: 50%;
		}

		.message_txt2_r {
			color: #999999;
			width: 50%;
		}

		.message_bt {
			display: flex;
			flex-direction: row-reverse;
			margin-top: 50rpx;

			.message_bt_l {
				order: 2;
				width: 30%;
				padding: 10rpx 25rpx;
				border: 1rpx solid #999999;
				border-radius: 30rpx;
			}

			.message_bt_r {
				order: 1;
				width: 30%;
				padding: 10rpx 25rpx;
				border-radius: 30rpx;
				color: #ffffff;
				background-color: #6b63ec;
				margin-left: 15rpx;
			}
		}
	}
</style>