<template>
<view>
    <block v-if="isload">
        <view class="orderinfo">
            <view class="item">
                <text class="t1">ID</text>
                <text class="t2">{{member.id}}</text>
            </view>
           
            <view class="item">
                <text class="t1">昵称</text>
                <text class="t2">{{member.company_name}}</text>
            </view>
            <view class="item">
                <text class="t1">地区</text>
                <text class="t2">{{member.address}}</text>
            </view>
            <view class="item">
                <text class="t1">加入时间</text>
                <text class="t2">{{member.created_at}}</text>
            </view>
            <view class="item">
                <text class="t1">姓名</text>
                <text class="t2">{{member.contact_name}}</text>
            </view>
            <view class="item">
                <text class="t1">电话</text>
                <text class="t2">{{member.phone}}</text>
            </view>
            
            <view class="item" v-if="member.remark">
                <text class="t1">备注</text>
                <text class="t2">{{member.remark}}</text>
            </view>
            <view class="item" v-if="ordershow" style="justify-content: space-between;">
                <text class="t1" style="color: #007aff;">商城订单</text>
                <view class="flex" @tap="goto" :data-url="'/admin/order/shoporder?mid='+member.id">{{member.ordercount}} <text class="iconfont iconjiantou" style="color:#999;font-weight:normal; margin-top: 2rpx;"></text></view>
            </view>	
        </view>
        <view style="width:100%;height:120rpx"></view>
      <view class="bottom">
          <view class="btn" @tap="remark" :data-id="member.id">备注</view>
          <view class="btn" @tap="showFollowUpDialog">跟进</view>
          <view class="btn" @tap="goto" :data-url="'shagjigenjinjilu?id=' + member.id">跟进记录</view>
          <view class="btn" @tap="goto" :data-url="'shangjixiugai?id=' + member.id">商机修改</view>
      </view>


        <uni-popup id="remarkDialog" ref="remarkDialog" type="dialog">
            <uni-popup-dialog mode="input" title="设置备注" value="" placeholder="请输入备注" @confirm="remarkConfirm"></uni-popup-dialog>
        </uni-popup>

        <uni-popup id="followUpDialog" ref="followUpDialog" type="dialog">
            <uni-popup-dialog mode="input" title="添加跟进记录" value="" placeholder="请输入跟进内容" @confirm="followUpConfirm"></uni-popup-dialog>
        </uni-popup>
    </block>
    <popmsg ref="popmsg"></popmsg>
    <loading v-if="loading"></loading>
</view>
</template>

<script>
var app = getApp();

export default {
    data() {
        return {
            isload: false,
            member: "",
            loading: false,
            firstPic: '',
            ordershow: false,
        };
    },
    onLoad: function (opt) {
        this.opt = app.getopts(opt);
        this.getdata();
    },
    onPullDownRefresh: function () {
        this.getdata();
    },
    methods: {
        getdata: function () {
            var that = this;
            that.loading = true;
            app.post('ApiTuozhancrm/shangjidetail', { mid: that.opt.mid }, function (res) {
                that.loading = false;
                that.member = res.member;
                that.ordershow = res.ordershow;
                uni.setNavigationBarTitle({
                    title: that.t('会员') + '信息'
                });

                const picsArray = res.member.pics.split(',');
                that.firstPic = picsArray[0]; // 赋值给 firstPic
                that.loaded();
            });
        },
        remark: function (e) {
            this.$refs.remarkDialog.open();
        },
        showFollowUpDialog: function () {
            this.$refs.followUpDialog.open();
        },
        remarkConfirm: function (done, value) {
            this.$refs.remarkDialog.close();
            var that = this;
            app.post('ApiTuozhancrm/remark', { remarkmid: that.opt.mid, remark: value }, function (data) {
                app.success(data.msg);
                setTimeout(function () {
                    that.getdata();
                }, 1000);
            });
        },
        followUpConfirm: function (done, value) {
            this.$refs.followUpDialog.close();
            var that = this;
            app.post('ApiTuozhancrm/addFollowUp', { businessId: that.opt.mid, followUpContent: value }, function (data) {
                app.success(data.msg);
                setTimeout(function () {
                    that.getdata();
                }, 1000);
            });
        },
    }
};
</script>

<style>
.address { display: flex; align-items: center; width: 100%; padding: 20rpx 3%; background: #FFF; margin-bottom: 20rpx; }
.address .img { width: 60rpx; }
.address image { width: 50rpx; height: 50rpx; }
.address .info { flex: 1; display: flex; flex-direction: column; }
.address .info .t1 { font-weight: bold; }

.product { width: 100%; padding: 14rpx 3%; background: #FFF; }
.product .content { display: flex; position: relative; width: 100%; padding: 16rpx 0px; border-bottom: 1px #e5e5e5 dashed; position: relative; }
.product .content:last-child { border-bottom: 0; }
.product .content image { width: 140rpx; height: 140rpx; }
.product .content .detail { display: flex; flex-direction: column; margin-left: 14rpx; flex: 1; }
.product .content .detail .t1 { height: 60rpx; line-height: 30rpx; color: #000; }
.product .content .detail .t2 { height: 46rpx; line-height: 46rpx; color: #999; overflow: hidden; font-size: 26rpx; }
.product .content .detail .t3 { display: flex; height: 30rpx; line-height: 30rpx; color: #ff4246; }
.product .content .detail .x1 { flex: 1; }
.product .content .detail .x2 { width: 100rpx; font-size: 32rpx; text-align: right; margin-right: 8rpx; }
.product .content .comment { position: absolute; top: 64rpx; right: 10rpx; border: 1px #ffc702 solid; border-radius: 10rpx; background: #fff; color: #ffc702; padding: 0 10rpx; height: 46rpx; line-height: 46rpx; }

.orderinfo { width: 94%; margin: 20rpx 3%; border-radius: 16rpx; padding: 14rpx 3%; background: #FFF; }
.orderinfo .item { display: flex; width: 100%; padding: 20rpx 0; border-bottom: 1px dashed #ededed; }
.orderinfo .item:last-child { border-bottom: 0; }
.orderinfo .item .t1 { width: 200rpx; }
.orderinfo .item .t2 { flex: 1; text-align: right; }
.orderinfo .item .red { color: red; }

.bottom { width: 100%; padding: 16rpx 20rpx; background: #fff; position: fixed; bottom: 0px; left: 0px; display: flex; justify-content: flex-end; align-items: center; }
.bottom .btn { border-radius: 10rpx; padding: 10rpx 16rpx; margin-left: 10px; border: 1px #999 solid; }

.uni-popup-dialog { width: 300px; border-radius: 5px; background-color: #fff; }
.uni-dialog-title { display: flex; flex-direction: row; justify-content: center; padding-top: 15px; padding-bottom: 5px; }
.uni-dialog-title-text { font-size: 16px; font-weight: 500; }
.uni-dialog-content { display: flex; flex-direction: row; justify-content: center; align-items: center; padding: 5px 15px 15px 15px; width: 100%; }
.uni-dialog-content-text { font-size: 14px; color: #6e6e6e; }
.uni-dialog-button-group { display: flex; flex-direction: row; border-top-color: #f5f5f5; border-top-style: solid; border-top-width: 1px; }
.uni-dialog-button { display: flex; flex: 1; flex-direction: row; justify-content: center; align-items: center; height: 45px; }
.uni-border-left { border-left-color: #f0f0f0; border-left-style: solid; border-left-width: 1px; }
.uni-dialog-button-text { font-size: 14px; }
.uni-button-color { color: #007aff; }
</style>
