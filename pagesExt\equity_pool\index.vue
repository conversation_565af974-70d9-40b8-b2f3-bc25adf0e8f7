<template>
	<view class="container">
		<block v-if="isload">
			<view class="equity-pool-header">
				<view class="header-card" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}">
					<view class="card-title">股权池总览</view>
					<view class="data-row">
						<view class="data-item">
							<view class="data-value">{{formatNumber(pool.total_amount)}}</view>
							<view class="data-label">股权池总金额</view>
						</view>
						<view class="data-item">
							<view class="data-value">{{formatNumber(pool.equity_count)}}</view>
							<view class="data-label">股权总数量</view>
						</view>
						<view class="data-item">
							<view class="data-value">{{formatNumber(pool.equity_value)}}</view>
							<view class="data-label">单位股权价值</view>
						</view>
					</view>
				</view>
			</view>
			
			<view class="my-equity-card">
				<view class="card-title">我的股权</view>
				<view class="data-row">
					<view class="data-item">
						<view class="data-value">{{formatNumber(user_equity.equity_num)}}</view>
						<view class="data-label">持有股权数量</view>
					</view>
					<view class="data-item">
						<view class="data-value">{{formatNumber(user_equity.equity_value)}}</view>
						<view class="data-label">我的股权价值</view>
					</view>
				</view>
				<view class="btn-row">
					<view class="btn" @tap="goto" data-url="/pagesExt/equity_pool/myEquity">我的股权详情</view>
				</view>
			</view>
			
			<view class="equity-chart">
				<view class="card-title">股权价值走势</view>
				<view class="chart-container" id="equityChart"></view>
			</view>
			
			<view class="equity-ranking">
				<view class="rank-header">
					<view class="rank-title">股权持有排行榜</view>
					<view class="rank-more" @tap="goto" data-url="/pagesExt/equity_pool/ranking">查看更多</view>
				</view>
				<view class="rank-list">
					<view class="rank-item" v-for="(item, index) in equity_ranking.slice(0, 5)" :key="index">
						<view class="rank-num" :class="index < 3 ? 'top' + (index + 1) : ''">{{index + 1}}</view>
						<view class="user-info">
							<image class="avatar" :src="item.headimg"></image>
							<view class="nickname">{{item.nickname}}</view>
						</view>
						<view class="equity-count">{{formatNumber(item.total_num)}}</view>
						<view class="equity-value">{{formatNumber(item.equity_value)}}</view>
					</view>
				</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();
var echarts = require('./components/echarts/echarts.min.js');

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			pre_url: app.globalData.pre_url,
			myChart: null,
			
			pool: {
				total_amount: 0,
				equity_count: 0,
				equity_value: 0
			},
			user_equity: {
				equity_num: 0,
				equity_value: 0
			},
			equity_ranking: [],
			chart_data: []
		};
	},
	onLoad: function(opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onShow: function() {
		// 页面显示时重新初始化图表
		if (this.isload && this.chart_data.length > 0) {
			this.$nextTick(() => {
				if (this.myChart) {
					this.myChart.resize();
				} else {
					this.initChart();
				}
			});
		}
	},
	onPullDownRefresh: function() {
		this.getdata();
	},
	onUnload: function() {
		// 页面卸载时销毁图表实例
		if (this.myChart) {
			window.removeEventListener('resize', this.myChart.resize);
			this.myChart = null;
		}
	},
	methods: {
		getdata: function() {
			var that = this;
			that.loading = true;
			app.get('ApiEquityPool/index', {}, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				if (res.status == 1) {
					that.pool = res.data.pool;
					that.user_equity = res.data.user_equity;
					that.equity_ranking = res.data.equity_ranking;
					that.chart_data = res.data.chart_data;
					that.isload = true;
					
					// 初始化图表
					that.$nextTick(() => {
						that.initChart();
					});
				} else {
					that.$refs.popmsg.show({
						msg: res.msg || '获取数据失败，请重试',
						isok: false
					});
				}
			});
		},
		
		// 初始化股权价值走势图表
		initChart: function() {
			if (!this.chart_data || this.chart_data.length === 0) return;
			
			var that = this;
			var chartDom = document.getElementById('equityChart');
			if (!chartDom) return;
			
			var myChart = echarts.init(chartDom);
			var dates = [];
			var values = [];
			
			for (var i = 0; i < that.chart_data.length; i++) {
				dates.push(that.chart_data[i].date);
				values.push(parseFloat(that.chart_data[i].value));
			}
			
			var option = {
				backgroundColor: '#ffffff',
				title: {
					text: '股权价值走势',
					left: 'center',
					textStyle: {
						color: '#333',
						fontSize: 14,
						fontWeight: 'bold'
					}
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: dates,
					axisLabel: {
						interval: Math.floor(dates.length / 6),
						fontSize: 10
					}
				},
				yAxis: {
					type: 'value',
					name: '股权价值',
					axisLabel: {
						formatter: '{value}'
					}
				},
				series: [{
					name: '股权价值',
					type: 'line',
					data: values,
					smooth: true,
					showSymbol: true,
					symbolSize: 6,
					itemStyle: {
						color: that.t('color1')
					},
					lineStyle: {
						width: 3,
						color: that.t('color1')
					},
					areaStyle: {
						color: {
							type: 'linear',
							x: 0,
							y: 0,
							x2: 0,
							y2: 1,
							colorStops: [{
								offset: 0,
								color: 'rgba(' + that.t('color1rgb') + ', 0.5)'
							}, {
								offset: 1,
								color: 'rgba(' + that.t('color1rgb') + ', 0.1)'
							}]
						}
					},
					animation: true
				}]
			};
			
			myChart.setOption(option);
			
			// 监听窗口变化，重绘图表
			window.addEventListener('resize', function() {
				myChart.resize();
			});
			
			// 将图表实例保存起来，避免重复创建
			this.myChart = myChart;
		},
		
		// 格式化数字，保留两位小数
		formatNumber: function(num) {
			if (typeof num !== 'number') {
				num = parseFloat(num) || 0;
			}
			return num.toFixed(2);
		},
		
		// 跳转到指定页面
		goto: function(e) {
			var url = e.currentTarget.dataset.url;
			uni.navigateTo({
				url: url
			});
		}
	}
};
</script>

<style>
.container {
	padding-bottom: 30rpx;
}

.equity-pool-header {
	padding: 20rpx;
}

.header-card {
	border-radius: 10rpx;
	color: #fff;
	padding: 30rpx 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	padding-bottom: 20rpx;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	margin-bottom: 20rpx;
}

.data-row {
	display: flex;
	justify-content: space-between;
	padding: 10rpx 0;
}

.data-item {
	text-align: center;
	flex: 1;
}

.data-value {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.data-label {
	font-size: 24rpx;
	opacity: 0.8;
}

.my-equity-card {
	margin: 20rpx;
	background-color: #fff;
	border-radius: 10rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.my-equity-card .card-title {
	color: #333;
	border-bottom: 1px solid #eee;
}

.my-equity-card .data-item .data-value {
	color: #f0505a;
}

.my-equity-card .data-item .data-label {
	color: #666;
}

.btn-row {
	margin-top: 30rpx;
	display: flex;
	justify-content: center;
}

.btn {
	background: linear-gradient(90deg, #f0505a 0%, #f78d94 100%);
	color: #fff;
	padding: 15rpx 40rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
}

.equity-chart {
	margin: 20rpx;
	background-color: #fff;
	border-radius: 10rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.equity-chart .card-title {
	color: #333;
	border-bottom: 1px solid #eee;
}

.chart-container {
	width: 100%;
	height: 400rpx;
	margin-top: 20rpx;
}

.equity-ranking {
	margin: 20rpx;
	background-color: #fff;
	border-radius: 10rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.rank-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.rank-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.rank-more {
	font-size: 24rpx;
	color: #999;
}

.rank-list {
	padding: 10rpx 0;
}

.rank-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1px solid #f5f5f5;
}

.rank-num {
	width: 60rpx;
	height: 60rpx;
	line-height: 60rpx;
	text-align: center;
	background-color: #f5f5f5;
	color: #999;
	border-radius: 30rpx;
	margin-right: 20rpx;
	font-size: 28rpx;
}

.rank-num.top1 {
	background-color: #ff7043;
	color: #fff;
}

.rank-num.top2 {
	background-color: #ff9800;
	color: #fff;
}

.rank-num.top3 {
	background-color: #ffc107;
	color: #fff;
}

.user-info {
	display: flex;
	align-items: center;
	flex: 2;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
}

.nickname {
	font-size: 28rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 150rpx;
}

.equity-count {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #666;
}

.equity-value {
	flex: 1;
	text-align: right;
	font-size: 28rpx;
	color: #f0505a;
	font-weight: bold;
}
</style> 