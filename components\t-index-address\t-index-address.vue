<template>
	<view class="address-component">
		<view class="address-component__main">
			<!-- 左侧城市列表 -->
			<scroll-view 
				class="address-component__main-list" 
				:scroll-top="scrollTop" 
				:scroll-into-view="scrollIntoView" 
				scroll-with-animation
				scroll-y>
				<view 
					class="address-component__main-item" 
					v-for="(item, index) in keys" 
					:key="index" 
					:id="'index-' + item">
					<view class="address-component__main-title">{{item}}</view>
					<view 
						class="address-component__main-row" 
						v-for="(town, idx) in formatData[item]" 
						:key="idx" 
						@click="handleTap(town)">
						<view class="address-component__main-text">{{town.name}}</view>
					</view>
				</view>
			</scroll-view>
			
			<!-- 右侧索引列表 -->
			<view class="address-component__letter">
				<view 
					class="address-component__letter-item" 
					v-for="(item, index) in keys" 
					:key="index" 
					@click="handleSelect(item)">
					{{item}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 't-index-address',
		props: {
			data: {
				type: [Object, String],
				default: () => ({})
			}
		},
		data() {
			return {
				formatData: {},
				keys: [],
				scrollTop: 0,
				scrollIntoView: '',
				lastScrollKey: ''
			}
		},
		watch: {
			data: {
				handler(newVal) {
					this.handleData(newVal);
					// 监听来自city.vue的搜索结果同步
					uni.$on("syncCity", (data) => {
						this.handleData(data);
					});
				},
				immediate: true
			}
		},
		methods: {
			// 处理数据
			handleData(data) {
				if (typeof data === 'string') {
					try {
						data = JSON.parse(data);
					} catch (e) {
						console.error('数据格式错误');
						data = {};
					}
				}
				
				this.formatData = data;
				this.keys = Object.keys(data).sort();
			},
			
			// 点击右侧字母索引，滚动到对应位置
			handleSelect(key) {
				console.log('2023-06-15 10:55:23-INFO-[t-index-address][handleSelect_001] 选择索引:', key);
				
				// 使用scroll-into-view方式滚动
				this.scrollIntoView = 'index-' + key;
				
				// 记录最后一次的滚动位置，避免重复滚动
				this.lastScrollKey = key;
				
				// 调试信息
				console.log('2023-06-15 10:55:23-INFO-[t-index-address][handleSelect_002] 滚动到:', this.scrollIntoView);
			},
			
			// 点击城市名称
			handleTap(item) {
				this.$emit('select', item);
			}
		},
		beforeDestroy() {
			uni.$off('syncCity');
		}
	}
</script>

<style>
	.address-component {
		height: calc(100vh - 200rpx);
	}
	
	.address-component__main {
		display: flex;
		position: relative;
		height: 100%;
	}
	
	.address-component__main-list {
		flex: 1;
		height: 100%;
		padding: 0 30rpx;
	}
	
	.address-component__main-item {
		padding: 10rpx 0;
	}
	
	.address-component__main-title {
		height: 50rpx;
		line-height: 50rpx;
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}
	
	.address-component__main-row {
		display: flex;
		align-items: center;
		height: 80rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.address-component__main-text {
		font-size: 28rpx;
		color: #666;
	}
	
	.address-component__letter {
		position: fixed;
		right: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 50rpx;
		text-align: center;
		padding: 10rpx 0;
		background-color: rgba(0, 0, 0, 0.3);
		border-radius: 30rpx;
		z-index: 999;
	}
	
	.address-component__letter-item {
		height: 40rpx;
		line-height: 40rpx;
		font-size: 24rpx;
		color: #fff;
	}
</style> 