<template>
<view class="container">
	<block v-if="isload">
		<form @submit="submitOrder">
			<!-- 地址选择 -->
			<view class="address-add flex-y-center" @tap="goto" data-url="/pages/address/address?fromPage=buy&type=1"> <!-- type=1 for potentially needing tongcheng -->
				<view class="f1">
					<image class="img" src="/static/img/address.png" />
				</view>
				<view class="f2 flex1" v-if="address.id">
					<view style="font-weight:bold;color:#111111;font-size:30rpx">{{address.name}} {{address.tel}}</view>
					<view style="font-size:24rpx">{{address.area}} {{address.address}}</view>
				</view>
				<view v-else class="f2 flex1">请选择服务地址</view>
				<image src="/static/img/arrowright.png" class="f3" />
			</view>

			<!-- 订单信息 -->
			<view class="buydata">
				<view class="bcontent">
					<!-- 商品信息 -->
					<view class="product">
						<view class="item flex">
							<view class="img">
								<image class="img" :src="productInfo.pic"></image>
							</view>
							<view class="info flex1">
								<view class="f1">{{productInfo.name}}</view>
								<view class="f2">共{{productInfo.total_period}}期</view>
								<view class="f3">￥{{productInfo.sell_price}}<text style="font-size: 24rpx;color:#999">/期</text></view>
							</view>
						</view>
					</view>

					<!-- 价格明细 -->
					<view class="price">
						<text class="f1">商品总价</text>
						<text class="f2">￥{{priceInfo.product_total_price}}</text>
					</view>
					<!-- <view class="price" v-if="priceInfo.leveldk_money > 0"> -->
						<!-- <text class="f1">{{t('会员')}}折扣</text> -->
						<!-- <text class="f2" style="color:#ff5043">-￥{{priceInfo.leveldk_money}}</text> -->
					<!-- </view> -->
					<view class="price">
						<view class="f1">{{t('优惠券')}}</view>
						<view v-if="couponList.length > 0" class="f2" @tap="showCouponList">
							<text style="color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx" :style="{background:t('color1')}">{{couponSelected.id ? couponSelected.name : couponList.length+'张可用'}}</text>
							<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text>
						</view>
						<text class="f2" v-else style="color:#999">无可用{{t('优惠券')}}</text>
					</view>
					<view class="price" v-if="couponSelected.id">
						<text class="f1">优惠金额</text>
						<text class="f2" style="color:#ff5043">-￥{{couponDiscount}}</text>
					</view>
					
					<!-- 期望开始日期 -->
					<view class="price">
						<text class="f1">期望开始日期</text>
						<picker mode="date" :value="startDate" :start="minStartDate" @change="bindStartDateChange">
							<view class="f2">{{startDate || '请选择'}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal"></text></view>
						</picker>
					</view>
					
					<!-- 备注 -->
					<view class="remark">
						<text class="f1">备注</text>
						<input class="f2" name="remark" placeholder="选填, 请输入备注信息" placeholder-style="color:#ccc"></input>
					</view>
				</view>
			</view>
			
			<!-- 积分抵扣 (如果需要) -->
			<!-- 
			<view class="scoredk flex" v-if="memberInfo.score > 0 && sysset.scoredk > 0">
				<checkbox-group @change="useScoreChange" class="flex" style="width:100%">
					<view class="f1">
						<view>{{memberInfo.score}} {{t('积分')}}可抵扣 <text style="color:#e94745">{{scoreDeductMoney}}</text> 元</view>
						
					</view>
					<view class="f2">使用{{t('积分')}}抵扣
						<checkbox value="1" :checked="useScore" style="margin-left:6px;transform:scale(.8)"></checkbox>
					</view>
				</checkbox-group>
			</view>
			-->

			<!-- 在这里添加支付方式选择的 UI 区域 -->
			<!-- 例如： -->
			<!--
			<view class="payment-method">
				<view class="title">支付方式</view>
				<radio-group @change="paytypeChange">
					<label class="flex-y-center">
						<radio value="1" :checked="selectedPaytype == 1"></radio> 微信支付
					</label>
					<label class="flex-y-center">
						<radio value="3" :checked="selectedPaytype == 3"></radio> 余额支付
					</label>
					... 其他支付方式 ...
				</radio-group>
			</view>
			-->

			<view style="width: 100%; height: 120rpx;"></view>

			<!-- 底部栏 -->
			<view class="footer flex" :class="menuindex > -1 ? 'tabbarbot' : 'notabbarbot'">
				<view class="text1 flex1">合计：
					<text style="font-weight:bold;font-size:36rpx;color:#FF5043">￥{{totalPrice}}</text>
				</view>
				<button class="op" form-type="submit" :style="{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" :disabled="submitDisabled">提交订单</button>
			</view>
		</form>

		<!-- 优惠券弹出层 -->
		<view v-if="couponVisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="hideCouponList"></view>
			<view class="popup__modal">
				<view class="popup__title">
					<text class="popup__title-text">请选择{{t('优惠券')}}</text>
					<image src="/static/img/close.png" class="popup__close" @tap.stop="hideCouponList" />
				</view>
				<view class="popup__content">
					<couponlist :couponlist="couponList" :choosecoupon="true" :selectedrid="couponSelected.id" @chooseCoupon="chooseCoupon" :bid="productInfo.bid"></couponlist> 
				</view>
			</view>
		</view>
		
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
import couponlist from '@/components/couponlist/couponlist.vue'; // 引入优惠券组件

export default {
	components: { couponlist },
	data() {
		return {
			opt: {},
			loading: true,
			isload: false,
			menuindex: -1,
			submitDisabled: false,

			productInfo: {}, // 商品信息
			address: {},     // 地址信息
			priceInfo: { product_total_price: '0.00', total_price: '0.00', coupon_money: '0.00' }, // 价格信息
			memberInfo: {},  // 会员信息
			couponList: [],  // 优惠券列表
			couponSelected: {}, // 已选优惠券
			couponDiscount: '0.00', // 优惠券抵扣金额
			totalPrice: '0.00', // 最终总价

			startDate: '',     // 期望开始日期
			minStartDate: '',  // 最小可选开始日期 (明天)
			
			couponVisible: false, // 优惠券弹窗

			selectedPaytype: 1 // 默认支付方式为1 (例如微信支付)，需要根据实际情况调整
			
			// 积分相关 (如果需要)
			// useScore: false,
			// scoreDeductMoney: '0.00'
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onShow: function() {
		// 如果从地址页面返回，尝试获取选择的地址
		var chosenAddress = uni.getStorageSync('choosedAddress');
		if (chosenAddress) {
			this.address = chosenAddress;
			uni.removeStorageSync('choosedAddress');
			this.calculatePrice(); // 地址变化可能影响运费（如果未来支持）
		}
	},
	methods: {
		getdata: function() {
			var that = this;
			var productId = that.opt.product_id;
			if (!productId) {
				app.alert('缺少服务ID', function() { app.goback(); });
				return;
			}
			
			that.loading = true;
			app.get('ApiPeriodicService/buy', { product_id: productId }, function(res) {
				that.loading = false;
				if (res.status == 0) {
					app.alert(res.msg, function() { app.goback(); });
					return;
				}
				that.productInfo = res.data.product;
				that.address = res.data.address || {}; // 使用接口返回的默认地址
				that.priceInfo = res.data.price_info;
				that.memberInfo = res.data.member_info;
				that.couponList = res.data.coupon_list || [];
				
				// 设置最小可选开始日期为明天
				var today = new Date();
				var tomorrow = new Date(today);
				tomorrow.setDate(tomorrow.getDate() + 1);
				that.minStartDate = that.formatDate(tomorrow);
				that.startDate = that.minStartDate; // 默认选中明天
				
				that.calculatePrice();
				that.isload = true;
			});
		},

		// 计算总价
		calculatePrice: function() {
			var basePrice = parseFloat(this.priceInfo.product_total_price) || 0;
			var couponDiscount = 0;
			if (this.couponSelected.id) {
				// 根据优惠券类型计算抵扣金额 (这里简化处理，直接使用后台计算好的total_price)
				// 实际可能需要前端根据券类型和金额计算
				// couponDiscount = parseFloat(this.couponSelected.money) || 0;
			}
			
			// 暂时从 priceInfo 直接获取总价，如果选择优惠券再调整
			var finalPrice = parseFloat(this.priceInfo.total_price) || basePrice;
			
			// 如果选择了优惠券，重新计算总价
			if (this.couponSelected.id) {
				couponDiscount = parseFloat(this.couponSelected.money) || 0;
				finalPrice = basePrice - couponDiscount;
				this.couponDiscount = couponDiscount.toFixed(2);
			} else {
				this.couponDiscount = '0.00';
			}
			
			// 积分抵扣逻辑 (如果需要)
			// if (this.useScore) {
			// 	 finalPrice -= parseFloat(this.scoreDeductMoney);
			// }

			if (finalPrice < 0) finalPrice = 0;
			this.totalPrice = finalPrice.toFixed(2);
		},

		// 选择开始日期
		bindStartDateChange: function(e) {
			this.startDate = e.detail.value;
		},

		// 格式化日期 YYYY-MM-DD
		formatDate: function(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},

		// 显示优惠券列表
		showCouponList: function() {
			if (this.couponList.length > 0) {
				this.couponVisible = true;
			}
		},
		// 隐藏优惠券列表
		hideCouponList: function() {
			this.couponVisible = false;
		},
		// 选择优惠券
		chooseCoupon: function(e) {
			if (this.couponSelected.id === e.rid) { // 取消选择
				this.couponSelected = {};
			} else {
				this.couponSelected = this.couponList[e.key];
			}
			this.hideCouponList();
			this.calculatePrice();
		},
		
		// 积分抵扣开关 (如果需要)
		// useScoreChange: function(e) {
		// 	 this.useScore = e.detail.value.length > 0;
		// 	 this.calculatePrice();
		// },

		// 添加支付方式切换方法
		paytypeChange: function(e) {
			this.selectedPaytype = parseInt(e.detail.value);
		},

		// 提交订单
		submitOrder: function(e) {
			var that = this;
			var formData = e.detail.value;

			if (!that.address || !that.address.id) {
				app.error('请选择服务地址');
				return;
			}
			if (!that.startDate) {
				app.error('请选择期望开始日期');
				return;
			}
			
			var postData = {
				product_id: that.productInfo.id,
				address_id: that.address.id,
				linkman: that.address.name, // 从地址对象获取
				tel: that.address.tel,     // 从地址对象获取
				address: that.address.province + that.address.city + that.address.area + that.address.address, // 拼接完整地址
				longitude: that.address.longitude || '',
				latitude: that.address.latitude || '',
				start_date: that.startDate,
				remark: formData.remark || '',
				coupon_id: that.couponSelected.id || 0, // 传递优惠券ID
				// usescore: that.useScore ? 1 : 0, // 如果使用积分抵扣
				platform: app.globalData.platform, 
				paytype: that.selectedPaytype // 添加选择的支付方式
			};

			that.submitDisabled = true;
			app.showLoading('提交中...');

			app.post('ApiPeriodicService/createOrder', postData, function(res) {
				app.showLoading(false);
				that.submitDisabled = false;
				if (res.status == 0) {
					app.error(res.msg);
					return;
				}
				// 下单成功，跳转支付页面
				app.goto('/pages/pay/pay?id=' + res.payorderid);

				// If payment needs params (e.g., WeChat JSAPI), access res.payparams
				// Example: let payParams = res.payparams;
			});
		}
	}
};
</script>

<style>
/* Reuse styles from tiantianshande/pagesExt/cycle/buy.vue and adapt */
.container { background-color: #f8f8f8; padding-bottom: 120rpx; /* Footer height */ }

.address-add { width: 94%; margin: 20rpx 3%; background: #fff; border-radius: 10rpx; padding: 20rpx 3%; min-height: 100rpx; display: flex; align-items: center; }
.address-add .f1 { margin-right: 20rpx; }
.address-add .f1 .img { width: 40rpx; height: 40rpx; }
.address-add .f2 { color: #666; font-size: 28rpx; }
.address-add .f2[v-if="address.id"] view:first-child { font-weight: bold; color: #111; font-size: 30rpx; margin-bottom: 4rpx; }
.address-add .f2[v-if="address.id"] view:last-child { font-size: 24rpx; color: #666; }
.address-add .f3 { width: 26rpx; height: 26rpx; margin-left: 10rpx; }

.buydata { width: 94%; margin: 20rpx 3%; background: #fff; border-radius: 10rpx; overflow: hidden; }
.bcontent { width: 100%; padding: 0 20rpx; }

.product { width: 100%; border-bottom: 1px solid #f4f4f4; padding: 20rpx 0; }
.product .item { width: 100%; display: flex; background: #fff; }
.product .info { padding-left: 20rpx; }
.product .info .f1 { color: #222222; font-weight: bold; font-size: 28rpx; line-height: 1.4; margin-bottom: 10rpx; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; }
.product .info .f2 { color: #999999; font-size: 24rpx; margin-bottom: 10rpx; }
.product .info .f3 { color: #FF4C4C; font-size: 28rpx; font-weight: bold; }
.product .img { width: 140rpx; height: 140rpx; border-radius: 8rpx; flex-shrink: 0; }

.price { width: 100%; padding: 20rpx 0; background: #fff; display: flex; align-items: center; border-bottom: 1px solid #f4f4f4; }
.price:last-child { border-bottom: none; }
.price .f1 { color: #333; font-size: 28rpx; }
.price .f2 { color: #111; font-weight: bold; text-align: right; flex: 1; font-size: 28rpx; display: flex; align-items: center; justify-content: flex-end; }
.price .f2 .iconfont { margin-left: 10rpx; font-size: 24rpx; }
.price picker .f2 { color: #111; font-weight: bold; }

.remark { width: 100%; padding: 20rpx 0; background: #fff; display: flex; align-items: center; }
.remark .f1 { color: #333; width: 200rpx; font-size: 28rpx; }
.remark .f2 { border: none; height: 70rpx; padding-left: 10rpx; text-align: right; flex: 1; font-size: 28rpx; }

.scoredk { width: 94%; margin: 20rpx 3%; border-radius: 10rpx; padding: 24rpx 20rpx; background: #fff; display: flex; align-items: center; }
.scoredk .f1 { color: #333333; font-size: 28rpx; }
.scoredk .f2 { color: #999999; text-align: right; flex: 1; font-size: 28rpx; }

.footer { width: 100%; background: #fff; position: fixed; left: 0px; bottom: 0px; padding: 10rpx 2%; display: flex; align-items: center; z-index: 8; border-top: 1px solid #eee; }
.footer .text1 { height: 100rpx; line-height: 100rpx; color: #2a2a2a; font-size: 28rpx; }
.footer .op { width: 240rpx; height: 80rpx; line-height: 80rpx; color: #fff; text-align: center; font-size: 30rpx; border-radius: 40rpx; }
.footer .op[disabled] { background-color: #ccc !important; background-image: none !important; }

/* Popups */
.popup__container { position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 100; }
.popup__overlay { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); }
.popup__modal { position: absolute; left: 0; right: 0; bottom: 0; background-color: #fff; border-top-left-radius: 20rpx; border-top-right-radius: 20rpx; padding-bottom: env(safe-area-inset-bottom); max-height: 70%; overflow-y: auto; }
.popup__title { display: flex; align-items: center; justify-content: space-between; padding: 30rpx; border-bottom: 1px solid #eee; }
.popup__title-text { font-size: 32rpx; font-weight: bold; color: #333; }
.popup__close { width: 36rpx; height: 36rpx; padding: 10rpx; }
.popup__content { padding: 0; max-height: 60vh; /* Limit coupon list height */ }
</style> 