<template>
<view class="container">
	<block v-if="isload">
		<view class="content">
			<view v-for="(item, index) in datalist" :key="index" class="item">
				<view class="f1">
						<text class="t1">提现金额：¥{{item.withdraw_amount}}</text>
						<text class="t2">{{item.createtime_format || item.createtime}}</text>
						<text class="t2">提现方式：{{item.withdraw_type}}</text>
						<text class="t2" v-if="item.status==2">驳回原因：{{item.reason || '无'}}</text>
				</view>
				<view class="f3">
						<text class="t1" v-if="item.status==0">{{item.status_text}}</text>
						<text class="t1" v-if="item.status==1">{{item.status_text}}</text>
						<text class="t2" v-if="item.status==2">{{item.status_text}}</text>
						<text class="t1" v-if="item.status==3">{{item.status_text}}</text>
						<block v-if="item.status==4">
							<view class="btn1" :style="{background:t('color1')}" @click="confirm_shoukuan(item.id)" v-if="item.wx_state=='WAIT_USER_CONFIRM' || item.wx_state=='TRANSFERING'">确认收款</view>
							<view class="t1" v-else-if="item.wx_state=='FAIL'">转账失败</view>
							<view class="t1" v-else>处理中</view>
						</block>
				</view>
			</view>
		</view>
	</block>
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			
			nodata: false,
			nomore: false,
			st: 1, // 固定为提现记录
			type:0,
			datalist: [],
			textset:{},
			pagenum: 1,
			set:{},
		};
	},
	
	// t函数用于获取主题颜色和多语言
	t: function(key) {
		return getApp().t(key);
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.type = this.opt.type || 0;
		uni.setNavigationBarTitle({
			title: '提现记录'
		});
		var that = this;
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	onReachBottom: function () {
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getdata(true);
		}
	},
	methods: {
		getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
			var that = this;
			var st = that.st;
			var type = that.type;
			var pagenum = that.pagenum;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
			app.post('ApiCityAgent/withdrawlog', {st: st, pagenum: pagenum, type: type}, function (res) {
				that.loading = false;
				var data = res.list || [];
				if (pagenum == 1) {
					that.textset = app.globalData.textset || {};
					that.set = res.set || {};
					that.datalist = data;
					if (data.length == 0) {
						that.nodata = true;
					}
					that.loaded();
				}else{
					if (data.length == 0) {
						that.nomore = true;
					} else {
						var datalist = that.datalist;
						var newdata = datalist.concat(data);
						that.datalist = newdata;
					}
				}
			});
		},
		
		// 数据加载完成
		loaded: function() {
			this.isload = true;
		},
		
		// 确认收款
		async confirm_shoukuan(id){
			var that = this;
			try {
				var a = await app.shoukuan(id,'cityagent_withdrawlog','');
				that.getdata();
			} catch (error) {
				that.$refs.popmsg.open({ type: 'error', title: '错误', content: '操作失败，请稍后重试' });
			}
		}
	}
};
</script>

<style>

.content{ width:94%;margin:20rpx 3%;}
.content .item{width:100%;background:#fff;margin:20rpx 0;padding:40rpx 30rpx;border-radius:8px;display:flex;align-items:center}
.content .item:last-child{border:0}
.content .item .f1{width:500rpx;display:flex;flex-direction:column}
.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}
.content .item .f1 .t2{color:#666666;font-size:24rpx;margin-top:10rpx}
.content .item .f1 .t3{color:#666666;font-size:24rpx;margin-top:10rpx;display: flex;}
.content .item .f1 .t3 image{width:40rpx;height:40rpx;border-radius:50%;margin-right:4px;align-content: center;}

.content .item .f3{ flex:1;width:200rpx;font-size:32rpx;text-align:right}
.content .item .f3 .t1{color:#03bc01}
.content .item .f3 .t2{color:#000000}

.data-empty{background:#fff}
.btn1{height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;padding: 0 15rpx;float: right;font-size: 25rpx;margin-left: 10rpx}
</style> 