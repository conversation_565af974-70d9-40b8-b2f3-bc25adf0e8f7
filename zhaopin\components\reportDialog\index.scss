.qts-mask {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 99999;
}

.qts-mask.show {
    visibility: visible;
}

.qts-mask.hidden {
    visibility: hidden;
}

.qts-mask-bg {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 99999;
}

.qts-mask-main {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    background: #fff;
    padding: 28rpx 32rpx 76rpx;
    border-radius: 24rpx 24rpx 0 0;
    z-index: 999999;
    transition: all 0.2s ease-in-out;
    transform: translateY(100vh);
}

.pb-32 {
    padding-bottom: 32rpx;
}

.qts-mask-main.popup {
    transform: translateY(0);
}

.qts-close-wrap {
    position: absolute;
    right: 0;
    top: 0;
    width: 96rpx;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qts-mask-close {
    width: 16rpx;
    height: 16rpx;
}

.qts-mask-title {
    text-align: center;
    font-size: 40rpx;
    font-weight: 600;
}

.qts-mask-notice,
.qts-mask-title {
    color: #111e38;
    margin-bottom: 32rpx;
}

.qts-mask-notice {
    height: 72rpx;
    background: #f6f7fb url('https://qiniu-image.qtshe.com/20200613_locationIcon.png') no-repeat 36rpx;
    background-size: 32rpx 32rpx;
    padding-left: 72rpx;
    display: flex;
    align-items: center;
    font-size: 24rpx;
    font-weight: 400;
    border-radius: 16rpx;
}

.qts-mask-item {
    color: #111e38;
    font-weight: 600;
    height: 44rpx;
    font-size: 28rpx;
    line-height: 44rpx;
    margin-bottom: 8rpx;
}

.qts-mask-tips {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    width: 100%;
    padding-bottom: 16rpx;
}

.qts-mask-tips rich-text {
    width: 100%;
    font-size: 24rpx;
    color: #6c6c6c;
    line-height: 1.5;
    padding: 16rpx;
}

.qts-mask-tips rich-text p {
    margin: 8rpx 0;
}

.qts-mask-tips rich-text ul {
    padding-left: 20rpx;
    margin: 8rpx 0;
}

.qts-mask-tips rich-text li {
    margin: 4rpx 0;
}

.qts-tips-item {
    padding: 16rpx;
    background: #f3f4f5;
    border-radius: 8rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
    font-size: 24rpx;
    color: #6c6c6c;
    justify-content: center;
}

.qts-mask-address,
.qts-tips-item {
    display: flex;
    align-items: center;
}

.qts-mask-address {
    justify-content: space-between;
    color: #fa5555;
    font-weight: 400;
    font-size: 28rpx;
    margin-bottom: 32rpx;
}

.qts-mask-address-detail {
    color: #111e38;
    width: 500rpx;
}

.qts-mask-time {
    color: #111e38;
    font-weight: 400;
    font-size: 28rpx;
    margin-bottom: 32rpx;
    line-height: 44rpx;
}

.qts-mask-salary {
    color: #FF6B00;
    font-weight: 600;
    font-size: 32rpx;
    margin-bottom: 32rpx;
    line-height: 44rpx;
}

.qts-mask-text {
    font-size: 28rpx;
    color: #3c3c3c;
    margin-bottom: 28rpx;
}

.qts-famous-recommend {
    width: 100%;
    margin-bottom: 32rpx;
}

.qts-recommend-item {
    display: flex;
    justify-content: space-between;
}

.qts-recommend-icon {
    display: block;
    width: 40rpx;
    height: 40rpx;
    margin-right: 32rpx;
    margin-top: 38rpx;
}

.qts-recommend-main {
    width: 614rpx;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2rpx solid #f3f4f5;
    padding-bottom: 16rpx;
    padding-top: 16rpx;
}

.qts-recommend-content,
.qts-recommend-main {
    display: flex;
    height: 116rpx;
}

.qts-recommend-content {
    width: 470rpx;
    justify-content: center;
    flex-direction: column;
}

.qts-recommend-title {
    color: #3c3c3c;
    font-size: 28rpx;
    font-weight: 700;
}

.qts-recommend-distance,
.qts-recommend-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.qts-recommend-distance {
    font-size: 24rpx;
    color: #9c9c9c;
    margin-top: 8rpx;
}

.qts-recommend-salary {
    color: #ff8000;
    font-weight: 700;
    font-size: 28rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 144rpx;
    text-align: right;
}

.qts-apply-content {
    text-align: center;
    margin-bottom: 16rpx;
    color: #9c9c9c;
    font-size: 24rpx;
}

.qts-mask-agreement {
    display: flex;
    font-size: 24rpx;
    color: #808999;
    padding-bottom: 32rpx;
    box-sizing: content-box;
    line-height: 36rpx;
}

.qts-mask-agreement .qts-sigle-checkbox {
    width: 32rpx;
    height: 32rpx;
    margin-right: 4rpx;
    flex-shrink: 0;
}

.qts-mask-agreement .qts-sigle-checkbox.normal {
    width: 32rpx;
    height: 32rpx;
    border: 4rpx solid #dbdee5;
    border-radius: 50%;
}

.qts-mask-agreement .qts-sigle-checkbox.active {
    background: url('https://qiniu-image.qtshe.com/20190812_checkActive.png') no-repeat;
    background-size: 100% 100%;
}

.qts-mask-agreement text {
    color: #01ca88;
}

.qts-mask-button {
    width: 460rpx;
    height: 96rpx;
    border-radius: 44rpx;
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40rpx auto 0;
}

.qts-mask-button-disable {
    width: 460rpx;
    height: 96rpx;
    border-radius: 44rpx;
    background: #999;
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40rpx auto 0;
}

.qts-success-box {
    width: 622rpx;
    padding: 136rpx 0 32rpx;
    background: #fff;
    border-radius: 24rpx;
    position: fixed;
    left: 50%;
    margin-left: -311rpx;
    top: 50%;
    margin-top: -210rpx;
}

.qts-success-pic {
    position: absolute;
    width: 348rpx;
    height: 292rpx;
    left: 50%;
    margin-left: -174rpx;
    top: -196rpx;
}

.qts-success-title {
    text-align: center;
    color: #111e38;
    font-weight: 500;
    font-size: 40rpx;
}

.qts-success-text {
    color: #808999;
    font-size: 28rpx;
    text-align: center;
    margin-top: 8rpx;
}

.qts-success-button {
    width: 558rpx;
    height: 96rpx;
    background: #00e699;
    border-radius: 24rpx;
    margin: 48rpx auto 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #111e38;
    font-weight: 500;
    font-size: 32rpx;
}

.qts-success-close {
    position: absolute;
    top: -112rpx;
    right: 0;
    width: 48rpx;
    height: 48rpx;
}

.qts-no-border {
    border: 0;
}

.qts-zhima {
    position: absolute;
    left: 0;
    width: 100%;
    bottom: 0;
    background: #fff;
    border-radius: 32rpx 32rpx 0 0;
    padding-top: 46rpx;
    padding-bottom: 68rpx;
}

.qts-zhima-tips {
    width: 686rpx;
    margin: 24rpx auto 0;
    padding-left: 40rpx;
    font-size: 28rpx;
    color: #111e38;
    line-height: 44rpx;
    position: relative;
}

.qts-zhima-tips::after {
    content: '';
    display: block;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background: #dadee6;
    top: 12rpx;
    left: 0;
    position: absolute;
}

.qts-zhima-icon {
    color: #1277ff;
    margin-left: 4rpx;
    vertical-align: bottom;
}

.qts-zhima-button {
    width: 686rpx;
    margin: 24rpx auto 0;
    height: 96rpx;
    border-radius: 24rpx;
    background: #00e699;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #111e38;
    font-size: 32rpx;
    font-weight: 600;
}

.qts-zhima-text {
    text-align: center;
    margin-top: 24rpx;
    color: #8a909d;
    font-size: 28rpx;
}

/* 客服二维码样式 */
.qts-service-qrcode {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 32rpx 0;
    padding: 24rpx;
}

.qts-qrcode-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 16rpx;
    border: 2rpx solid #f0f0f0;
    margin-bottom: 16rpx;
}

.qts-qrcode-tips {
    font-size: 24rpx;
    color: #808999;
    text-align: center;
    line-height: 1.4;
    max-width: 500rpx;
}
