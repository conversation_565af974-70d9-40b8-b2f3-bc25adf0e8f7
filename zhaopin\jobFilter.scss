.job-filter {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  
  .progress-bar {
    height: 4rpx;
    background-color: #f0f0f0;
    position: relative;
    
    .progress {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      background: linear-gradient(90deg, #1890ff, #096dd9);
      transition: width 0.3s ease;
    }
  }
  
  .step-title {
    padding: 40rpx 30rpx;
    
    .main-title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 12rpx;
      display: block;
    }
    
    .sub-title {
      font-size: 28rpx;
      color: #999;
    }
  }
  
  .step-content {
    flex: 1;
    height: 0;
    
    .scroll-container {
      height: 100%;
      padding: 0 30rpx;
    }
  }
  
  .category-list {
    .category-item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      margin-bottom: 20rpx;
      background-color: #f8f8f8;
      border-radius: 16rpx;
      transition: all 0.3s ease;
      
      &.active {
        background-color: #e6f7ff;
        border: 2rpx solid #1890ff;
      }
      
      .category-icon {
        width: 80rpx;
        height: 80rpx;
        margin-right: 20rpx;
      }
      
      .category-info {
        flex: 1;
        
        .category-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
          display: block;
        }
        
        .category-desc {
          font-size: 26rpx;
          color: #999;
        }
      }
      
      .check-icon {
        font-size: 40rpx;
        color: #1890ff;
        margin-left: 20rpx;
      }
    }
  }
  
  .filter-section {
    margin-bottom: 40rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 24rpx;
    }
    
    .option-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;
      
      .option-item {
        padding: 20rpx 40rpx;
        background-color: #f8f8f8;
        border-radius: 40rpx;
        font-size: 28rpx;
        color: #666;
        transition: all 0.3s ease;
        
        &.active {
          background-color: #1890ff;
          color: #fff;
        }
      }
    }
  }
  
  .city-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    
    .city-tag {
      display: flex;
      align-items: center;
      padding: 20rpx 30rpx;
      background-color: #f8f8f8;
      border-radius: 40rpx;
      font-size: 28rpx;
      color: #333;
      
      &.add-city {
        color: #1890ff;
        
        .add-icon {
          margin-right: 8rpx;
          font-size: 32rpx;
        }
      }
      
      .delete-icon {
        margin-left: 12rpx;
        font-size: 32rpx;
        color: #999;
      }
    }
  }
  
  .salary-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    
    .salary-item {
      text-align: center;
      padding: 20rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #666;
      transition: all 0.3s ease;
      
      &.active {
        background-color: #1890ff;
        color: #fff;
      }
    }
  }
  
  .footer {
    padding: 30rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .btn-group {
      display: flex;
      gap: 20rpx;
      
      button {
        height: 88rpx;
        line-height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
      
      .prev-btn {
        flex: 1;
        background-color: #f8f8f8;
        color: #666;
      }
      
      .next-btn {
        flex: 2;
        background: linear-gradient(135deg, #1890ff, #096dd9);
        color: #fff;
        
        &.full-width {
          flex: 1;
        }
      }
    }
  }
} 