<template>
<view class="container">
	<block v-if="isload">
		<view class="search-container" :style="history_show?'height:100%;':''">
			<view class="topsearch flex-y-center">
				<view class="f1 flex-y-center">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input :value="keyword" placeholder="搜索感兴趣的课程" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm" @input="searchChange"></input>
				</view>
				<view class="search-btn" @tap="searchbtn">
					<image src="/static/img/show-cascades.png" style="height:36rpx;width:36rpx" v-if="!history_show && productlisttype=='itemlist'"/>
					<image src="/static/img/show-list.png" style="height:36rpx;width:36rpx" v-if="!history_show && productlisttype=='item2'"/>
					<text v-if="history_show">搜索</text>
				</view>
			</view>
			<view class="search-history" v-show="history_show">
				<view>
					<text class="search-history-title">最近搜索</text>
					<view class="delete-search-history" @tap="deleteSearchHistory">
						<image src="/static/img/del.png" style="width:36rpx;height:36rpx"/>
					</view>
				</view>
				<view class="search-history-list">
					<view v-for="(item, index) in history_list" :key="index" class="search-history-item" :data-value="item" @tap="historyClick">{{item}}
					</view>
					<view v-if="!history_list || history_list.length==0" class="flex-y-center"><image src="/static/img/tanhao.png" style="width:36rpx;height:36rpx;margin-right:10rpx"/>暂无记录		</view>
				</view>
			</view>
			<view class="search-navbar" v-show="!history_show">
				<view @tap.stop="sortClick" class="search-navbar-item" :style="!field?'color:'+t('color1'):''" data-field="sort" data-order="desc">综合</view>
				<view @tap.stop="sortClick" class="search-navbar-item" :style="field=='join_num'?'color:'+t('color1'):''" data-field="join_num" data-order="desc">人气</view>
				<view @tap.stop="sortClick" class="search-navbar-item" data-field="price" :data-order="order=='asc'?'desc':'asc'">
					<text :style="field=='price'?'color:'+t('color1'):''">价格</text>
					<text class="iconfont iconshangla" :style="field=='price'&&order=='asc'?'color:'+t('color1'):''"></text>
					<text class="iconfont icondaoxu" :style="field=='price'&&order=='desc'?'color:'+t('color1'):''"></text>
				</view>
				<view class="search-navbar-item flex-x-center flex-y-center" @click.stop="showDrawer('showRight')">筛选 <text :class="'iconfont iconshaixuan ' + (showfilter?'active':'')"></text></view>
			</view>
			<uni-drawer ref="showRight" mode="right" @change="change($event,'showRight')" :width="280">
				<view class="filter-scroll-view">
					<scroll-view class="filter-scroll-view-box" scroll-y="true">
						<view class="search-filter">
							<view class="filter-title">筛选</view>
							<view class="filter-content-title">课程分类</view>
							<view class="search-filter-content">
								<view class="filter-item" :style="catchecid==oldcid?'color:'+t('color1')+'':''" @tap.stop="cateClick" :data-cid="oldcid">全部</view>
								<block v-for="(item, index) in clist" :key="index">
									<view class="filter-item" :style="catchecid==item.id?'color:'+t('color1')+'':''" @tap.stop="cateClick" :data-cid="item.id">{{item.name}}</view>
								</block>
							</view>
							<view class="filter-content-title">课程类型</view>
							<view class="search-filter-content">
								<view class="filter-item" :style="catchekctypeid==''?'color:'+t('color1')+';':''" @tap.stop="kctypeClick" data-kctype="">全部</view>
								<view class="filter-item" :style="catchekctypeid=='1'?'color:'+t('color1')+';':''" @tap.stop="kctypeClick" data-kctype="1">图文</view>
								<view class="filter-item" :style="catchekctypeid=='2'?'color:'+t('color1')+';':''" @tap.stop="kctypeClick" data-kctype="2">音频</view>
								<view class="filter-item" :style="catchekctypeid=='3'?'color:'+t('color1')+';':''" @tap.stop="kctypeClick" data-kctype="3">视频</view>
							</view>
							<view class="search-filter-btn">
								<view class="btn" @tap="filterReset">重置</view>
								<view class="btn2" :style="{background:t('color1')}" @tap="filterConfirm">确定</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</uni-drawer>

			
		</view>
		<view class="product-container">
			<block v-if="datalist && datalist.length>0">
				<dp-kecheng-item v-if="productlisttype=='item2'" :data="datalist" :sysset="sysset"></dp-kecheng-item>
				<dp-kecheng-itemlist v-if="productlisttype=='itemlist'" :data="datalist" :sysset="sysset"></dp-kecheng-itemlist>
			</block>
			<nomore text="没有更多课程了" v-if="nomore"></nomore>
			<nodata text="没有查找到相关课程" v-if="nodata"></nodata>
			<loading v-if="loading"></loading>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: true,
			menuindex:-1,

			nomore:false,
			nodata:false,
      keyword: '',
      pagenum: 1,
      datalist: [],
      history_list: [],
      history_show: true,
      order: '',
			field:'',
      oldcid: "",
      catchecid: "",
      catchekctypeid: "",
      cid: "",
      kctype: '',
      clist: [],
      sysset: {},
      productlisttype: 'item2',
      showfilter: "",
			bid: 0,
    };
  },
  onLoad: function (opt) {
		// 2025-01-03 15:30:01,001-INFO-[kecheng_search][onLoad_001] 初始化课程搜索页面参数
		console.log('2025-01-03 15:30:01,001-INFO-[kecheng_search][onLoad_001] 课程搜索页面初始化:', opt);
		
		this.opt = app.getopts(opt);
		this.oldcid = this.opt.cid || '';
		this.catchecid = this.opt.cid;
		this.cid = this.opt.cid;
		this.bid = this.opt.bid || 0;
		this.keyword = this.opt.keyword || '';
		
		// 2025-01-03 15:30:01,002-INFO-[kecheng_search][onLoad_002] 设置页面标题
		uni.setNavigationBarTitle({
			title: '课程搜索'
		});
		
    var productlisttype = app.getCache('kecheng_search_listtype');
    if (productlisttype) this.productlisttype = productlisttype;
    
		// 2025-01-03 15:30:01,003-INFO-[kecheng_search][onLoad_003] 获取课程搜索历史记录
		this.history_list = app.getCache('kecheng_search_history_list');
		this.getdata();
  },
	onPullDownRefresh: function () {
		// 2025-01-03 15:30:01,004-INFO-[kecheng_search][onPullDownRefresh_001] 下拉刷新课程数据
		this.getdata();
	},
  onReachBottom: function () {
  	// 2025-01-03 15:30:01,005-INFO-[kecheng_search][onReachBottom_001] 上拉加载更多课程
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getprolist();
    }
  },
  methods: {
		getdata:function(){
			var that = this;
			that.pagenum = 1;
			that.datalist = [];
			var cid = that.opt.cid;
			var bid = that.opt.bid;
			that.loading = true;
			
			// 2025-01-03 15:30:01,006-INFO-[kecheng_search][getdata_001] 获取课程分类和系统设置
			console.log('2025-01-03 15:30:01,006-INFO-[kecheng_search][getdata_001] 请求参数:', {cid: cid, bid: bid});
			
			app.get('ApiKecheng/list', {cid: cid, bid: bid}, function (res) {
				that.loading = false;
				// 2025-01-03 15:30:01,007-INFO-[kecheng_search][getdata_002] 获取课程分类数据成功
				console.log('2025-01-03 15:30:01,007-INFO-[kecheng_search][getdata_002] 分类数据:', res);
				
			  that.clist = res.clist;
			  that.sysset = res.sysset || {};
				that.loaded();
				
				if(that.keyword){
					// 2025-01-03 15:30:01,008-INFO-[kecheng_search][getdata_003] 关键词存在，开始搜索课程
					that.getprolist();
				}
			});
		},
    getprolist: function () {
      var that = this;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
      var order = that.order;
      var field = that.field;
      var kctype = that.kctype;
      var cid = that.cid;
      var bid = that.bid;
      
      that.history_show = false;
			that.loading = true;
			that.nodata = false;
      that.nomore = false;
      
      // 2025-01-03 15:30:01,009-INFO-[kecheng_search][getprolist_001] 开始搜索课程列表
      console.log('2025-01-03 15:30:01,009-INFO-[kecheng_search][getprolist_001] 搜索参数:', {
      	pagenum: pagenum, keyword: keyword, field: field, order: order, kctype: kctype, cid: cid, bid: bid
      });
      
      app.post('ApiKecheng/getlist',{
      	pagenum: pagenum,
      	keyword: keyword,
      	field: field,
      	order: order,
      	kctype: kctype,
      	cid: cid,
      	bid: bid
      }, function (res) {
				that.loading = false;
				// 2025-01-03 15:30:01,010-INFO-[kecheng_search][getprolist_002] 获取课程列表成功
				console.log('2025-01-03 15:30:01,010-INFO-[kecheng_search][getprolist_002] 课程数据:', res);
				
        var data = res.data;
        if (pagenum == 1) {
        	// 2025-01-03 15:30:01,011-INFO-[kecheng_search][getprolist_003] 第一页数据处理
          that.datalist = data;
          if (data.length == 0) {
          	// 2025-01-03 15:30:01,012-INFO-[kecheng_search][getprolist_004] 无搜索结果
            that.nodata = true;
          }
        }else{
        	// 2025-01-03 15:30:01,013-INFO-[kecheng_search][getprolist_005] 翻页数据处理
          if (data.length == 0) {
          	// 2025-01-03 15:30:01,014-INFO-[kecheng_search][getprolist_006] 无更多数据
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
            // 2025-01-03 15:30:01,015-INFO-[kecheng_search][getprolist_007] 拼接新数据成功
          }
        }
      });
    },
		// 打开窗口
		showDrawer(e) {
			// 2025-01-03 15:30:01,016-INFO-[kecheng_search][showDrawer_001] 打开筛选抽屉
			console.log('2025-01-03 15:30:01,016-INFO-[kecheng_search][showDrawer_001] 抽屉:', e);
			this.$refs[e].open()
		},
		// 关闭窗口
		closeDrawer(e) {
			// 2025-01-03 15:30:01,017-INFO-[kecheng_search][closeDrawer_001] 关闭筛选抽屉
			this.$refs[e].close()
		},
		// 抽屉状态发生变化触发
		change(e, type) {
			// 2025-01-03 15:30:01,018-INFO-[kecheng_search][change_001] 抽屉状态变化
			console.log('2025-01-03 15:30:01,018-INFO-[kecheng_search][change_001] 抽屉状态:', (type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));
			this[type] = e
		},
    searchChange: function (e) {
    	// 2025-01-03 15:30:01,019-INFO-[kecheng_search][searchChange_001] 搜索框内容变化
      this.keyword = e.detail.value;
      if (e.detail.value == '') {
      	// 2025-01-03 15:30:01,020-INFO-[kecheng_search][searchChange_002] 清空搜索框，显示历史记录
        this.history_show = true;
        this.datalist = [];
      }
    },
    searchbtn: function () {
      var that = this;
      if (that.history_show) {
      	// 2025-01-03 15:30:01,021-INFO-[kecheng_search][searchbtn_001] 执行搜索
        var keyword = that.keyword;
        that.searchproduct();
      } else {
      	// 2025-01-03 15:30:01,022-INFO-[kecheng_search][searchbtn_002] 切换列表显示类型
        if (that.productlisttype == 'itemlist') {
          that.productlisttype = 'item2';
          app.setCache('kecheng_search_listtype', 'item2');
        } else {
          that.productlisttype = 'itemlist';
          app.setCache('kecheng_search_listtype', 'itemlist');
        }
      }
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword
      // 2025-01-03 15:30:01,023-INFO-[kecheng_search][searchConfirm_001] 确认搜索课程
      console.log('2025-01-03 15:30:01,023-INFO-[kecheng_search][searchConfirm_001] 搜索关键词:', keyword);
      that.searchproduct();
    },
    searchproduct: function () {
      var that = this;
      that.pagenum = 1;
      that.datalist = [];
      // 2025-01-03 15:30:01,024-INFO-[kecheng_search][searchproduct_001] 添加搜索历史并执行搜索
      that.addHistory();
      that.getprolist();
    },
    sortClick: function (e) {
      var that = this;
      var t = e.currentTarget.dataset;
      that.field = t.field;
      that.order = t.order;
      // 2025-01-03 15:30:01,025-INFO-[kecheng_search][sortClick_001] 排序方式改变
      console.log('2025-01-03 15:30:01,025-INFO-[kecheng_search][sortClick_001] 排序:', {field: that.field, order: that.order});
      that.searchproduct();
    },
    cateClick: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
			if(cid === true) cid = '';
			// 2025-01-03 15:30:01,026-INFO-[kecheng_search][cateClick_001] 选择课程分类
			console.log('2025-01-03 15:30:01,026-INFO-[kecheng_search][cateClick_001] 分类ID:', cid);
      that.catchecid = cid
    },
    kctypeClick: function (e) {
      var that = this;
      var kctype = e.currentTarget.dataset.kctype;
			if(kctype === true) kctype = '';
			// 2025-01-03 15:30:01,027-INFO-[kecheng_search][kctypeClick_001] 选择课程类型
			console.log('2025-01-03 15:30:01,027-INFO-[kecheng_search][kctypeClick_001] 课程类型:', kctype);
      that.catchekctypeid = kctype
    },
		filterConfirm(){
			// 2025-01-03 15:30:01,028-INFO-[kecheng_search][filterConfirm_001] 确认筛选条件
			console.log('2025-01-03 15:30:01,028-INFO-[kecheng_search][filterConfirm_001] 筛选条件:', {
				cid: this.catchecid, kctype: this.catchekctypeid
			});
			this.cid = this.catchecid;
			this.kctype = this.catchekctypeid;
			this.searchproduct();
			this.$refs['showRight'].close()
		},
		filterReset(){
			// 2025-01-03 15:30:01,029-INFO-[kecheng_search][filterReset_001] 重置筛选条件
			this.catchecid = this.oldcid;
			this.catchekctypeid = '';
		},
    filterClick: function () {
      this.showfilter = !this.showfilter
    },
    addHistory: function () {
      var that = this;
      var keyword = that.keyword;
      if (app.isNull(keyword)) return;
      
      // 2025-01-03 15:30:01,030-INFO-[kecheng_search][addHistory_001] 添加课程搜索历史记录
      console.log('2025-01-03 15:30:01,030-INFO-[kecheng_search][addHistory_001] 添加历史记录:', keyword);
      
      var historylist = app.getCache('kecheng_search_history_list');
      if (app.isNull(historylist)) historylist = [];
      historylist.unshift(keyword);
      var newhistorylist = [];
      for (var i in historylist) {
        if (historylist[i] != keyword || i == 0) {
          newhistorylist.push(historylist[i]);
        }
      }
      if (newhistorylist.length > 5) newhistorylist.splice(5, 1);
      app.setCache('kecheng_search_history_list', newhistorylist);
      that.history_list = newhistorylist
    },
    historyClick: function (e){
      var that = this;
      var keyword = e.currentTarget.dataset.value;
      if (keyword.length == 0) return;
      that.keyword = keyword;
      // 2025-01-03 15:30:01,031-INFO-[kecheng_search][historyClick_001] 点击历史记录搜索
      console.log('2025-01-03 15:30:01,031-INFO-[kecheng_search][historyClick_001] 历史搜索:', keyword);
      that.searchproduct();
    },
    deleteSearchHistory: function () {
      var that = this;
      // 2025-01-03 15:30:01,032-INFO-[kecheng_search][deleteSearchHistory_001] 清空课程搜索历史
      that.history_list = null;
      app.removeCache("kecheng_search_history_list");
    }
  }
};
</script>
<style>
.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}
.topsearch{width:100%;padding:16rpx 20rpx;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.topsearch .search-btn{display:flex;align-items:center;color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}
.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0}
.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:28rpx;font-weight:bold;color:#323232}

.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}
.search-history {padding: 24rpx 34rpx;}
.search-history .search-history-title {color: #666;}
.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}
.search-history-list {padding: 24rpx 0 0 0;}
.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}

.filter-scroll-view{margin-top:var(--window-top)}
.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}
.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}
.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:60rpx 0 30rpx 20rpx;}
.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}
.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}
.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}
.search-filter button .icon{margin-top:6rpx;height:54rpx;}
.search-filter-btn{display:flex;padding:30rpx 30rpx;justify-content: space-between}
.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}
.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}

.product-container {width: 100%;margin-top: 190rpx;font-size:26rpx;padding:0 24rpx}
</style>