<template>
<view class="container">
	<block v-if="isload">
	<form @submit="formSubmit">
		<view class="mymoney" :style="{background:t('color1')}">
			<view class="f1">可用余额</view>
			<view class="f2"><text style="font-size:26rpx"></text>{{userinfo.money}}</view>
			<view class="f3" @tap="goto" data-url="/pages/money/moneylog"><text>余额明细</text><text class="iconfont iconjiantou" style="font-size:20rpx"></text></view>
		</view>
		<!-- <button class="btn" :style="{background:t('color1')}" @tap="formSubmit">兌換子賬號</button> -->
			<button class="btn" :style="{background:t('color1')}" @tap="formSubmit1">兌換主賬號</button>
			<!-- <button class="btn" :style="{background:t('color1')}" @tap="formSubmit2">歸集收益</button> -->
			<view class="content">
				<view class="info-item">
					<view>兌換賬號信息</view>
				</view>
				<view class="info-item">
					<view class="t1">賬號id</view>
					<view class="t2">帳號</view>
					<view class="t2">佣金</view>
					<view class="t2">余额</view>
					<!-- <view class="t2">主/子賬號</view> -->
				</view>
				<view class="info-item" v-for="(item, index) in datalist" :key="index">
					<view class="t1">{{item.id}}</view>
					<view class="t2"v-if="item.zi ==1">{{item.tel}}</view>
					<view class="t2" v-if="item.zi ==2">{{item.tel}}</view>
					<view class="t2">{{item.yongjin}}</view>
					<view class="t2">{{item.jine}}</view>
					<!-- <view class="t2"v-if="item.zi ==1">子</view>
					<view class="t2" v-if="item.zi ==2">主</view> -->
				</view>
			</view>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			datalist:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
      userinfo: [],
      money: 0,
      sysset: false,
      paytype: '微信钱包',
			tmplids:[],
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		var that = this;
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/yueWithdraw4', {}, function (res) {
				that.loading = false;
				uni.setNavigationBarTitle({
					title: '主賬號'
				});
				var sysset = res.sysset;
				that.sysset = sysset;
				that.datalist = res.list;
				that.tmplids = res.tmplids;
				that.userinfo = res.userinfo;
				var paytype = '微信钱包';
				if (sysset.withdraw_weixin == 1) {
					paytype = '微信钱包';
				}
				if (!sysset.withdraw_weixin || sysset.withdraw_weixin == 0) {
					paytype = '支付宝';
				}
				if ((!sysset.withdraw_weixin || sysset.withdraw_weixin == 0) && (!sysset.withdraw_aliaccount || sysset.withdraw_aliaccount == 0)) {
					paytype = '银行卡';
				}
				that.paytype = paytype;
				that.loaded();
			});
		},
	formSubmit2:function()
		{
			app.showLoading('收集中請耐心等待');
			app.post('ApiMy/shoujishouyizhu', {}, function (data) {
							app.showLoading(false);
			  if (data.status == 0) {
			    app.error(data.msg);
			    return;
			  } else {
			    app.success(data.msg);
			    that.subscribeMessage(function () {
			      setTimeout(function () {
			        app.goto('/pages/money/moneylog');
			      }, 1000);
			    });
			  }
			});
			
		},
    formSubmit: function () {
      var that = this;
  //     var usermoney = parseFloat(this.userinfo.money);
	 //  if(usermoney <= 0)
	 //  {
		// app.error('可兑换金额不足!');
		// return;
	 //  }
	app.goto('/pages/index/regzizhanghao?pid='+that.userinfo.id);
	//  this.$router.push('/pages/index/regzizhanghao?pid='+that.userinfo.id); // 将'/targetPage'替换为你的目标路径);
	  },
		formSubmit1:function()
		{
			var that = this;
			  app.goto('/pages/index/regzhuzhanghao?pid='+that.userinfo.id); 
			//this.$router.push('/pages/index/regzhuzhanghao?pid='+that.userinfo.id); // 将'/targetPage'替换为你的目标路径);
	},
  }
};
</script>
<style>
.container{display:flex;flex-direction:column}
.mymoney{width:94%;margin:20rpx 3%;border-radius: 10rpx 56rpx 10rpx 10rpx;position:relative;display:flex;flex-direction:column;padding:70rpx 0}
.mymoney .f1{margin:0 0 0 60rpx;color:rgba(255,255,255,0.8);font-size:24rpx;}
.mymoney .f2{margin:20rpx 0 0 60rpx;color:#fff;font-size:64rpx;font-weight:bold}
.mymoney .f3{height:56rpx;padding:0 10rpx 0 20rpx;border-radius: 28rpx 0px 0px 28rpx;background:rgba(255,255,255,0.2);font-size:20rpx;font-weight:bold;color:#fff;display:flex;align-items:center;position:absolute;top:94rpx;right:0}

.content2{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff}
.content2 .item1{display:flex;width:100%;border-bottom:1px solid #F0F0F0;padding:0 30rpx}
.content2 .item1 .f1{flex:1;font-size:32rpx;color:#333333;font-weight:bold;height:120rpx;line-height:120rpx}
.content2 .item1 .f2{color:#FC4343;font-size:44rpx;font-weight:bold;height:120rpx;line-height:120rpx}

.content2 .item2{display:flex;width:100%;padding:0 30rpx;padding-top:10rpx}
.content2 .item2 .f1{height:80rpx;line-height:80rpx;color:#999999;font-size:28rpx}

.content2 .item3{display:flex;width:100%;padding:0 30rpx;padding-bottom:20rpx}
.content2 .item3 .f1{height:100rpx;line-height:100rpx;font-size:60rpx;color:#333333;font-weight:bold;margin-right:20rpx}
.content2 .item3 .f2{display:flex;align-items:center;font-size:60rpx;color:#333333;font-weight:bold}
.content2 .item3 .f2 .input{font-size:60rpx;height:100rpx;line-height:100rpx;}
.content2 .item4{display:flex;width:94%;margin:0 3%;border-top:1px solid #F0F0F0;height:100rpx;line-height:100rpx;color:#8C8C8C;font-size:28rpx}

.withdrawtype{width:94%;margin:20rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;margin-top:20rpx;background:#fff}
.withdrawtype .f1{height:100rpx;line-height:100rpx;padding:0 30rpx;color:#333333;font-weight:bold}


.withdrawtype .f2{padding:0 30rpx}
.withdrawtype .f2 .item{border-bottom:1px solid #f5f5f5;height:100rpx;display:flex;align-items:center}
.withdrawtype .f2 .item:last-child{border-bottom:0}
.withdrawtype .f2 .item .t1{flex:1;display:flex;align-items:center;color:#333}
.withdrawtype .f2 .item .t1 .img{width:44rpx;height:44rpx;margin-right:40rpx}

.withdrawtype .f2 .item .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}
.withdrawtype .f2 .item .radio .radio-img{width:100%;height:100%}

.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}
.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 100rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}


</style>