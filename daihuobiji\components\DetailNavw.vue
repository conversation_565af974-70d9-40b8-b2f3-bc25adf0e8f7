<template>
	<view class="nav-bar" :style="{height: navBarHeight + 'rpx', paddingTop:statusBarHeight+ 'rpx',paddingbottom:statusBarHeight+ 'rpx' }">
		<view class="left" >
			<image class="back-icon" src="../../static/img/daihuobiji_back.png" @click="onBack"></image>
			<image
			  class="user-avatar"
			  :src="userInfo.headimg"
			  @click="onBack"
			></image>

			<text class="nav-text">{{ userInfo.nickname }}</text>
		</view>

		<view class="right">
			<view class="follow-status">
				<text @tap="guangzhu()">{{ followStatus ? '已关注' : '关注' }}</text>
			</view>
		</view>
		
		<view style="width: 100px;" v-if="weixin"></view>
		
	</view>
</template>

<script>
	var app = getApp();
	export default {
		props: {
			userInfo: {
				type: Object,
				default: () => ({
					name: '',
					avatar: '',
					  mid: '', // 确保包含 mid
					followStatus: false
				})
			},
			noteId: {
				type: String,
				required: true
			},
			userInfo: {
			        type: Object,
			        required: true
			    }
		},
		data() {
			return {
				navBarHeight: 140,
				statusBarHeight:0,
				followStatus: false,
				weixin : false
			}
		},
		mounted() {
			
			this.isguangzhu();
			
			// #ifdef MP-WEIXIN  
                this.weixin = true
				this.statusBarHeight = 100
				this.navBarHeight = 200
			// #endif
		},
		onShow() {
		    // 页面每次显示时重新加载数据，避免缓存问题
		    this.getdata();
			
		},

		methods: {
			onBack() {
				uni.navigateBack();
			},
			goto(mid) {
			    if (!mid) {
			        console.error('mid is missing');
			        return;
			    }
			    
			    const url = `/daihuobiji/detail/userbijishe?mid=${mid}`;
			    uni.navigateTo({
			        url: url,
			        fail: (err) => {
			            console.error('Navigation failed:', err);
			        }
			    });
			},

			isguangzhu() {
				var that = this;
				app.post('Apidaihuobiji/getNoteDetail', {
					usid: that.userInfo.mid,
					node_id: that.noteId,  
				}, function(res) {
					if (res.status === 1) {
						that.followStatus = res.data.is_followed === 1;
					} else {
						console.error('Error:', res.message || '获取关注状态失败');
					}
				});
			},

			guangzhu() {
				var that = this;
				const action = that.followStatus ? 'unfollow' : 'follow'; // 判断当前状态

				app.post('Apidaihuobiji/followUser', {
					node_id: that.noteId,
					guanzhuid: that.userInfo.mid,
					usid: that.userInfo.mid,
					action: action // 添加操作类型
				}, function(res) {
					if (res.status === 1) {
						that.followStatus = !that.followStatus; // 切换关注状态
					} else {
						console.error('Error:', res.message || '操作失败');
					}
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.nav-bar {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		background-color: #fff;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		padding: 0 16px;
		color: #333333;

		.left {
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;

			.back-icon {
				width: 44rpx;
				height: 44rpx;
				margin-right: 12rpx;
			}

			.user-avatar {
				width: 68rpx;
				height: 68rpx;
				border-radius: 100rpx;
				border: 1rpx solid #F6F6F6;
				background-color: #ccc;
				margin-right: 12rpx;
			}

			.nav-text {
				height: 37rpx;
				font-size: 28rpx;
				color: #333333;
				line-height: 33rpx;
			}
		}

		.right {
			.follow-status {
				width: 128rpx;
				height: 56rpx;
				line-height: 54rpx;
				text-align: center;
				border-radius: 31rpx;
				border: 1rpx solid #E3582F;
				font-size: 24rpx;
				color: #E3582F;
			}
		}
	}
</style>
