<template>
  <view class="pdf-container">
    <view v-if="loading" class="loading">
      <text>PDF文件加载中...</text>
    </view>
    <view v-else-if="error" class="error">
      <text>{{ error }}</text>
    </view>
    <view v-else class="pdf-viewer">
      <view class="pdf-controls">
        <view class="page-info">{{ currentPage }} / {{ totalPages }}</view>
        <view class="pdf-buttons">
          <button class="btn" :disabled="currentPage <= 1" @click="prevPage">上一页</button>
          <button class="btn" :disabled="currentPage >= totalPages" @click="nextPage">下一页</button>
        </view>
      </view>
      <scroll-view class="pdf-content" scroll-y>
        <canvas class="pdf-canvas" canvas-id="pdf-canvas" :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"></canvas>
      </scroll-view>
    </view>
  </view>
</template>

<script>
// 导入PDF.js库，使用try-catch处理导入失败的情况
let pdfjsLib;
try {
  pdfjsLib = require('../pdfjs/pdf.min.js');
} catch (error) {
  console.error('PDF.js导入失败:', error);
  pdfjsLib = null;
}

export default {
  name: 'PdfViewer',
  props: {
    url: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      pdfDoc: null,
      currentPage: 1,
      totalPages: 0,
      canvasWidth: 0,
      canvasHeight: 0,
      scale: 1.0,
      canvasContext: null,
      useIframe: false  // 是否使用iframe备选方案
    };
  },
  mounted() {
    console.log('PDF预览组件加载，URL:', this.url);
    
    // #ifdef H5
    // 在H5环境下，优先使用iframe显示PDF
    this.useIframe = true;
    this.tryIframeRender();
    return;
    // #endif
    
    // 如果PDF.js库不可用，显示错误信息
    if (!pdfjsLib) {
      this.loading = false;
      this.error = '未能加载PDF.js库，无法渲染PDF';
      console.error('PDF.js库不可用');
      return;
    }
    
    try {
      // 设置PDF.js工作线程路径
      if (pdfjsLib.GlobalWorkerOptions) {
        pdfjsLib.GlobalWorkerOptions.workerSrc = '../pdfjs/pdf.worker.min.js';
      }
      
      // 初始化Canvas上下文
      const query = uni.createSelectorQuery().in(this);
      query.select('.pdf-canvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res || !res[0]) {
            this.error = 'Canvas元素未找到';
            this.loading = false;
            return;
          }
          
          // 仅在支持的平台上进行Canvas操作
          // #ifdef APP-PLUS || H5
          try {
            this.canvasContext = res[0].node.getContext('2d');
          } catch (error) {
            console.error('获取Canvas上下文失败:', error);
            this.error = '无法初始化PDF渲染环境';
            this.loading = false;
            return;
          }
          // #endif
          
          // #ifdef MP-WEIXIN
          try {
            const canvas = uni.createCanvasContext('pdf-canvas', this);
            this.canvasContext = canvas;
          } catch (error) {
            console.error('创建Canvas上下文失败:', error);
            this.error = '无法初始化PDF渲染环境';
            this.loading = false;
            return;
          }
          // #endif
          
          // 加载PDF文档
          this.loadPdf();
        });
    } catch (error) {
      console.error('PDF预览组件初始化失败:', error);
      this.loading = false;
      this.error = `PDF预览初始化失败: ${error.message}`;
    }
  },
  methods: {
    // 加载PDF文档
    loadPdf() {
      this.loading = true;
      this.error = null;
      
      console.log('开始加载PDF文件:', this.url);
      
      try {
        // 创建加载任务选项，增加对CORS的支持
        const loadingOptions = {
          url: this.url,
          withCredentials: true
        };
        
        const loadingTask = pdfjsLib.getDocument(loadingOptions);
        
        if (loadingTask && loadingTask.promise) {
          loadingTask.promise
            .then(pdfDoc => {
              console.log('PDF加载成功，总页数:', pdfDoc.numPages);
              this.pdfDoc = pdfDoc;
              this.totalPages = pdfDoc.numPages;
              this.loading = false;
              
              // 渲染第一页
              this.renderPage(this.currentPage);
            })
            .catch(error => {
              this.loading = false;
              this.error = `无法加载PDF文件: ${error.message}`;
              console.error('PDF加载错误:', error);
              
              // 尝试退回到iframe渲染方式（仅在H5环境）
              // #ifdef H5
              this.tryIframeRender();
              // #endif
            });
        } else {
          throw new Error('PDF加载任务创建失败');
        }
      } catch (error) {
        console.error('加载PDF文件失败:', error);
        this.loading = false;
        this.error = `PDF加载失败: ${error.message}`;
        
        // 尝试退回到iframe渲染方式（仅在H5环境）
        // #ifdef H5
        this.tryIframeRender();
        // #endif
      }
    },
    
    // H5环境下的备选方案：使用iframe直接显示PDF
    // #ifdef H5
    tryIframeRender() {
      console.log('使用iframe渲染PDF:', this.url);
      this.useIframe = true;
      this.loading = false;
      this.error = null;
      
      this.$nextTick(() => {
        const container = document.querySelector('.pdf-container');
        if (container) {
          // 清空原有内容
          while (container.firstChild) {
            container.removeChild(container.firstChild);
          }
          
          // 创建iframe元素
          const iframe = document.createElement('iframe');
          iframe.src = this.url;
          iframe.style.width = '100%';
          iframe.style.height = '800px';
          iframe.style.border = 'none';
          container.appendChild(iframe);
        } else {
          console.error('未找到PDF容器元素');
          this.error = '未找到PDF容器元素';
        }
      });
    },
    // #endif
    
    // 渲染指定页面
    renderPage(pageNumber) {
      if (this.useIframe) return;
      
      this.loading = true;
      
      try {
        this.pdfDoc.getPage(pageNumber)
          .then(page => {
            // 调整Canvas大小以适应PDF页面
            const viewport = page.getViewport({ scale: this.scale });
            this.canvasWidth = viewport.width;
            this.canvasHeight = viewport.height;
            
            // 设置Canvas大小
            // #ifdef APP-PLUS || H5
            if (this.canvasContext && this.canvasContext.canvas) {
              const canvas = this.canvasContext.canvas;
              canvas.width = viewport.width;
              canvas.height = viewport.height;
            }
            // #endif
            
            // 渲染PDF页面
            const renderContext = {
              canvasContext: this.canvasContext,
              viewport: viewport
            };
            
            page.render(renderContext).promise
              .then(() => {
                this.loading = false;
                this.currentPage = pageNumber;
                
                // 小程序平台需要特殊处理
                // #ifdef MP-WEIXIN
                if (this.canvasContext && this.canvasContext.draw) {
                  this.canvasContext.draw();
                }
                // #endif
              })
              .catch(error => {
                this.loading = false;
                this.error = `无法渲染PDF页面: ${error.message}`;
                console.error('PDF渲染错误:', error);
              });
          })
          .catch(error => {
            this.loading = false;
            this.error = `无法获取PDF页面: ${error.message}`;
            console.error('获取PDF页面错误:', error);
          });
      } catch (error) {
        this.loading = false;
        this.error = `渲染PDF页面失败: ${error.message}`;
        console.error('渲染PDF页面异常:', error);
      }
    },
    
    // 上一页
    prevPage() {
      if (this.useIframe) return;
      
      if (this.currentPage > 1) {
        this.renderPage(this.currentPage - 1);
      }
    },
    
    // 下一页
    nextPage() {
      if (this.useIframe) return;
      
      if (this.currentPage < this.totalPages) {
        this.renderPage(this.currentPage + 1);
      }
    }
  }
};
</script>

<style>
.pdf-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading, .error {
  width: 100%;
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.error {
  color: #e74c3c;
}

.pdf-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-controls {
  height: 80rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.page-info {
  font-size: 26rpx;
  color: #333;
}

.pdf-buttons {
  display: flex;
}

.btn {
  font-size: 24rpx;
  padding: 4rpx 20rpx;
  margin: 0 10rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 6rpx;
}

.btn[disabled] {
  background-color: #cccccc;
  color: #999999;
}

.pdf-content {
  flex: 1;
  width: 100%;
  background-color: #f9f9f9;
  overflow: scroll;
}

.pdf-canvas {
  margin: 20rpx auto;
  background-color: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
</style> 