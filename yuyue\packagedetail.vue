<template>
	<view class="container" v-if="!loading && packageDetail.id">
		<!-- 套餐主图 -->
		<image class="package-banner" :src="packageDetail.pic" mode="widthFix"></image>

		<!-- 基本信息 -->
		<view class="section info-section">
			<view class="package-name">{{ packageDetail.name }}</view>
			<view class="package-tags">
				<text class="tag" v-if="packageDetail.valid_days > 0">有效期 {{ packageDetail.valid_days }} 天</text>
				<text class="tag" v-else>永久有效</text>
				<!-- 可以添加其他标签 -->
			</view>
			<view class="package-price" :style="{color: t('color1')}">￥<text class="price-value">{{ packageDetail.sell_price }}</text></view>
			<!-- 可以在这里添加原价、已售数量等信息 -->
		</view>

		<!-- 包含项目 -->
		<view class="section items-section" v-if="packageDetail.items && packageDetail.items.length > 0">
			<view class="section-title">包含服务项目</view>
			<view class="service-list">
				<view class="service-item" v-for="(item, index) in packageDetail.items" :key="index">
					<image class="service-pic" :src="item.product_pic || '/static/img/goods-default.png'" mode="aspectFill"></image>
					<view class="service-info">
						<view class="service-name">{{ item.product_name }}</view>
						<view class="service-times">x {{ item.num }} 次</view>
					</view>
					<!-- 可以添加服务价格或跳转到服务详情 -->
				</view>
			</view>
		</view>

		<!-- 商家信息 -->
		<view class="section business-section" v-if="packageDetail.business">
			<view class="section-title">适用商家</view>
			<view class="business-info" @tap="gotoBusiness(packageDetail.bid)">
				<image class="business-logo" :src="packageDetail.business.logo" mode="aspectFill"></image>
				<view class="business-details">
					<view class="business-name">{{ packageDetail.business.name }}</view>
					<view class="business-address">{{ packageDetail.business.address }}</view>
				</view>
				<text class="arrow">&gt;</text>
			</view>
		</view>

		<!-- 套餐详情 -->
		<view class="section detail-section">
			<view class="section-title">套餐详情</view>
			<rich-text class="rich-text-content" :nodes="packageDetail.content || '暂无详情'"></rich-text>
		</view>

		<!-- 底部购买栏 -->
		<view class="bottom-bar">
			<view class="bottom-price">
				<text>合计：</text>
				<text class="price-symbol" :style="{color: t('color1')}">￥</text>
				<text class="price-value-bottom" :style="{color: t('color1')}">{{ packageDetail.sell_price }}</text>
			</view>
			<button class="buy-button" :style="{background: t('color1')}" @tap="gotoBuy">立即购买</button>
		</view>

	</view>
	<view class="loading-container" v-else-if="loading">
		<text>加载中...</text>
	</view>
	<view class="empty-container" v-else>
		<text>未找到套餐信息</text>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			packageId: null,
			packageDetail: {},
			loading: true
		}
	},
	onLoad(options) {
		if (options.id) {
			this.packageId = options.id;
			this.getDetail();
		} else {
			app.error('缺少套餐ID', function() {
				app.goback();
			});
			this.loading = false;
		}
		uni.setNavigationBarTitle({
			title: '套餐详情'
		});
	},
	methods: {
		getDetail() {
			var that = this;
			that.loading = true;
			app.post('ApiYuyuePackage/getDetail', { id: that.packageId }, function(res) {
				console.log('ApiYuyuePackage/getDetail 返回:', JSON.stringify(res)); // 完整日志
				that.loading = false;
				// 调整判断条件，直接检查 res.status 和 res.package
				if (res.status == 1 && res.package) {
					try {
						// 直接从 res 中提取 package 和 business
						let packageData = JSON.parse(JSON.stringify(res.package)); // 深拷贝
						let businessData = res.business ? JSON.parse(JSON.stringify(res.business)) : null;
						console.log('提取的 packageData:', JSON.stringify(packageData));
						console.log('提取的 businessData:', JSON.stringify(businessData));

						// 清理图片URL (保持不变)
						if (packageData.pic && packageData.pic.startsWith('https://localhost')) {
							console.log('清理套餐图片前缀');
							packageData.pic = packageData.pic.substring('https://localhost'.length);
						}
						if (businessData && businessData.logo && businessData.logo.startsWith('https://localhost')) {
							console.log('清理商家Logo前缀');
							businessData.logo = businessData.logo.substring('https://localhost'.length);
						}
						// 清理 items 中的图片URL
						if(packageData.items && Array.isArray(packageData.items)) {
							packageData.items.forEach(item => {
								if(item.product_pic && item.product_pic.startsWith('https://localhost')){
									console.log('清理服务项图片前缀:', item.product_pic);
									item.product_pic = item.product_pic.substring('https://localhost'.length);
								}
							});
						}

						// 处理富文本中的图片样式 (保持不变)
						if (packageData.content && packageData.content !== '[]') { // 检查是否是无效的 '[]'
							console.log('格式化富文本内容');
							try {
								packageData.content = app.formatRichText(packageData.content);
							} catch (formatError) {
								console.error('formatRichText 出错:', formatError);
								packageData.content = '内容加载失败'; // 出错时给默认提示
							}
						} else if (packageData.content === '[]'){
							console.log('富文本内容为 [], 置空处理');
							packageData.content = ''; // 置为空字符串
						}

						// 构建最终对象
						const finalDetail = {
							...packageData,
							business: businessData,
							title: res.title, // 从 res 直接获取
							isfavorite: res.isfavorite // 从 res 直接获取
						};
						console.log('最终赋值给 packageDetail 的对象:', JSON.stringify(finalDetail));
						that.packageDetail = finalDetail;

						// 再次检查 packageDetail.id 是否存在
						if (!that.packageDetail || !that.packageDetail.id) {
							console.error('赋值后 packageDetail 或其 id 无效:', that.packageDetail);
							app.error('获取套餐详情失败(数据处理异常)');
							that.packageDetail = {}; // 确保渲染失败状态
						}

					} catch (e) {
						console.error('处理套餐详情数据时出错:', e);
						app.error('加载套餐信息异常');
						that.packageDetail = {}; // 清空数据，确保显示失败状态
					}
				} else {
					console.error('获取套餐详情失败，status 或 package 结构不符:', res);
					app.error(res.msg || '获取套餐详情失败', function() {
						app.goback();
					});
				}
			}, function() {
				that.loading = false;
				app.error('请求失败', function() {
					app.goback();
				});
			});
		},
		gotoBuy() {
			app.goto('/yuyue/packagebuy?package_id=' + this.packageId);
		},
		gotoBusiness(bid){
			if(bid){
				app.goto('/pages/business/index?id='+bid);
			}
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 120rpx; /* 为底部购买栏留出空间 */
	background-color: #f5f5f5;
}

.package-banner {
	width: 100%;
	display: block; /* 消除图片底部空隙 */
	vertical-align: bottom;
}

.section {
	background-color: #fff;
	margin: 20rpx;
	padding: 25rpx;
	border-radius: 16rpx;
}

.info-section .package-name {
	font-size: 34rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.info-section .package-tags {
	margin-bottom: 20rpx;
}

.info-section .tag {
	background-color: #fff5f5;
	color: #E64340;
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 4rpx;
	margin-right: 10rpx;
}

.info-section .package-price {
	font-size: 28rpx;
	font-weight: bold;
}

.info-section .price-value {
	font-size: 44rpx;
	margin-left: 4rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 25rpx;
	padding-left: 16rpx;
	border-left: 6rpx solid; /* 使用主题色 */
}

/* 包含项目 */
.items-section {
	/* section通用样式已包含padding */
}

.service-list {
	/* 列表容器 */
}

.service-item {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}
.service-item:last-child {
	margin-bottom: 0;
}

.service-pic {
	width: 100rpx;
	height: 100rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.service-info {
	flex: 1;
	overflow: hidden;
}

.service-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.service-times {
	font-size: 24rpx;
	color: #999;
}

/* 商家信息 */
.business-info {
    display: flex;
    align-items: center;
    padding: 15rpx 0;
}

.business-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 20rpx;
	flex-shrink: 0;
}

.business-details {
    flex: 1;
    overflow: hidden;
}

.business-name {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 5rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.business-address {
    font-size: 24rpx;
    color: #999;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.arrow{
	color: #bbb;
	font-size: 28rpx;
	margin-left: 10rpx;
}

/* 套餐详情 */
.detail-section {
	/* section通用样式已包含padding */
}

.rich-text-content {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}
.rich-text-content image, .rich-text-content img {
	max-width: 100% !important;
	height: auto !important;
	display: block;
	margin: 10rpx 0;
}

/* 底部购买栏 */
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100rpx; /* 根据内容调整 */
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx 0 30rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	box-sizing: border-box; /* 包含padding */
	padding-bottom: env(safe-area-inset-bottom); /* iPhone X 适配 */
	height: calc(100rpx + env(safe-area-inset-bottom));
}

.bottom-price {
	font-size: 24rpx;
	color: #333;
}

.bottom-price .price-symbol {
	font-size: 28rpx;
	margin-left: 8rpx;
	font-weight: bold;
}

.bottom-price .price-value-bottom {
	font-size: 36rpx;
	font-weight: bold;
}

.buy-button {
	height: 72rpx;
	line-height: 72rpx;
	font-size: 28rpx;
	color: #fff;
	padding: 0 40rpx;
	border-radius: 36rpx;
	text-align: center;
	border: none;
	outline: none;
	margin: 0;
}
button::after {
	border: none;
}


/* 加载和空状态 */
.loading-container, .empty-container {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 60vh;
	color: #999;
	font-size: 28rpx;
}
</style> 