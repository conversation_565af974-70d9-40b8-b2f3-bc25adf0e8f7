<template>
  <view class="container">
    <block v-if="isload">
      <!-- 总计信息展示 -->
      <view class="total-info">
        <text>历史交易总数：{{ totalCount }} 笔</text>
        <text>历史交易总额：￥{{ totalAmount }} 元</text>
      </view>

      <!-- 订单列表展示 -->
      <view class="order-content">
        <block v-for="(item, index) in datalist" :key="index">
          <view class="order-box">
            <!-- 订单头部信息 -->
            <view class="order-head">
              <view class="order-info">
                <text class="order-id">订单号：{{ item.order_id }}</text>
                <text class="order-time">清算时间：{{ formatTime(item.clear_time) }}</text>
              </view>
              <text class="changci-name">{{ item.changci_name }}</text>
            </view>

            <!-- 订单详情 -->
            <view class="order-content-details">
              <image :src="item.goods_pic" class="product-image"></image>
              <view class="detail-info">
                <text class="detail-name">{{ item.goods_name }}</text>
                <view class="user-info">
                  <text>买家ID：{{ item.buyer_id }}</text>
                  <text>卖家ID：{{ item.seller_id }}</text>
                </view>
                <text class="detail-price">成交价：￥{{ item.price }}</text>
              </view>
            </view>

            <!-- 添加物流信息展示 -->
            <view class="express-info" v-if="item.express_no">
              <view class="express-title">物流信息</view>
              <view class="express-detail">
                <view class="express-item">
                  <text class="express-label">物流公司：</text>
                  <text class="express-value">{{ item.express_company || '暂无' }}</text>
                </view>
                <view class="express-item">
                  <text class="express-label">物流单号：</text>
                  <text class="express-value">{{ item.express_no }}</text>
                  <text class="copy-btn" @tap="copyExpress(item.express_no)">复制</text>
                </view>
                <view class="express-item">
                  <text class="express-label">发货时间：</text>
                  <text class="express-value">{{ formatTime(item.express_time) }}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>

      <nomore v-if="nomore"></nomore>
      <nodata v-if="nodata"></nodata>
    </block>

    <!-- 底部加载状态 -->
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      datalist: [],
      pagenum: 1,
      nomore: false,
      nodata: false,
      totalCount: 0,
      totalAmount: 0
    };
  },

  onLoad: function(opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },

  onPullDownRefresh: function() {
    this.getdata();
  },

  onReachBottom: function() {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },

  methods: {
    // 获取历史订单数据
    getdata: function(loadmore) {
      var that = this;
      if (!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
      }

      that.nodata = false;
      that.nomore = false;
      that.loading = true;

      // 调用历史订单API
      app.post('ApiMiaosha/historyList', { 
        pagenum: that.pagenum
      }, function(res) {
        that.loading = false;

        if (res && res.datalist) {
          var data = res.datalist;
          that.totalCount = res.total_count || 0;
          that.totalAmount = res.total_amount || 0;

          if (that.pagenum == 1) {
            that.datalist = data;
            if (data.length == 0) {
              that.nodata = true;
            }
            that.isload = true;
          } else {
            if (data.length == 0) {
              that.nomore = true;
            } else {
              that.datalist = that.datalist.concat(data);
            }
          }
        } else {
          that.nodata = true;
        }
      });
    },

    // 格式化时间
    formatTime(timestamp) {
      let date = new Date(timestamp * 1000);
      return date.getFullYear() + '-' + 
             (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
             date.getDate().toString().padStart(2, '0') + ' ' +
             date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0');
    },

    // 添加复制物流单号方法
    copyExpress(expressNo) {
      uni.setClipboardData({
        data: expressNo,
        success: () => {
          uni.showToast({
            title: '物流单号已复制',
            icon: 'none'
          });
        }
      });
    }
  }
};
</script>

<style>
.container {
  width: 100%;
  background-color: #f8f8f8;
}

.total-info {
  background-color: #ffffff;
  padding: 20rpx;
  margin: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.order-content {
  display: flex;
  flex-direction: column;
}

.order-box {
  width: 94%;
  margin: 10rpx 3%;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.order-head {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.order-id {
  font-size: 26rpx;
  color: #333;
}

.order-time {
  font-size: 24rpx;
  color: #666;
}

.changci-name {
  font-size: 26rpx;
  color: #ff8758;
  padding: 4rpx 12rpx;
  background-color: #fff5f0;
  border-radius: 4rpx;
}

.order-content-details {
  display: flex;
  padding: 16rpx 0;
  border-top: 1px solid #f4f4f4;
}

.product-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  margin-right: 14rpx;
}

.detail-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}

.detail-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  font-size: 24rpx;
  color: #666;
}

.detail-price {
  font-size: 28rpx;
  color: #ff4246;
  font-weight: bold;
}

/* 添加物流信息相关样式 */
.express-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.express-title {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
  padding-left: 10rpx;
  border-left: 4rpx solid #ff8758;
}

.express-detail {
  padding: 0 10rpx;
}

.express-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.express-label {
  width: 140rpx;
  font-size: 26rpx;
  color: #666;
}

.express-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.copy-btn {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  color: #ff8758;
  border: 1rpx solid #ff8758;
  border-radius: 20rpx;
  margin-left: 20rpx;
}

.copy-btn:active {
  opacity: 0.8;
}
</style>