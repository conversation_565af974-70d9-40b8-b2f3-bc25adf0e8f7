<template>
    <view class="container">
        <!-- 页面标题 -->
        <view class="page-header">
            <text class="page-title">{{pageTitle}}</text>
            <view class="header-actions">
                <view class="filter-btn" @tap="toggleFilter">
                    <text class="filter-text">{{ my === 1 ? '查看全部' : '只看我的' }}</text>
                    <text class="filter-icon">{{ my === 1 ? '🔍' : '👤' }}</text>
                </view>
                <view class="add-btn" @tap="goto" data-url="/pagesExa/shoumai/sale">
                    <text class="add-icon">+</text>
                    <text class="add-text">發布</text>
                </view>
            </view>
        </view>
        
        <!-- 分段控制器 -->
        <view class="segment-control">
            <view class="scroll-container">
                <view class="segment" :class="{active: activeTab === 0}" @click="activeTabFn(0)">售賣中</view>
                <view class="segment" :class="{active: activeTab === 1}" @click="activeTabFn(1)">交易中</view>
                <view class="segment" :class="{active: activeTab === 2}" @click="activeTabFn(2)">已完成</view>
            </view>
        </view>

        <!-- 列表视图 -->
        <view class="tab-content">
            <!-- 无数据提示 -->
            <view class="empty-state" v-if="saleList.length === 0 && !loading">
                <image class="empty-icon" src="/static/img/empty.png" mode="aspectFit"></image>
                <text class="empty-text">{{ emptyStateText }}</text>
                <view class="empty-action" v-if="activeTab === 0 && my === 0">
                    <text @tap="goto" data-url="/pagesExa/shoumai/sale">立即發布售賣</text>
                </view>
            </view>
            
            <!-- 列表项 -->
            <view class="list-item" v-for="(item, index) in saleList" :key="index">
                <view class="list-item-header">
                    <view class="seller-info">
                        <text class="seller-badge" v-if="item.is_author === 1">我的</text>
                        <text class="seller-name">{{item.is_author === 1 ? '我' : item.nickname}}</text>
                    </view>
                    <text class="item-status" :class="{
                        'status-selling': activeTab === 0,
                        'status-canceled': activeTab === 2 && item.status === 3,
                        'status-success': activeTab === 2 && item.status === 1,
                        'status-rejected': activeTab === 2 && item.status === 2,
                        'status-completed': activeTab === 2 && item.status !== 1 && item.status !== 2 && item.status !== 3,
                        'status-pending': activeTab === 1 && item.is_voucher === 0,
                        'status-reviewing': activeTab === 1 && item.is_voucher === 1
                    }">{{ getStatusText(item) }}</text>
                </view>

                <view class="list-item-content">
                    <view class="asset-info">
                        <view class="asset-type">
                            <text class="asset-label">{{ item.asset_name || getAssetTypeName(item.asset_type) }}</text>
                        </view>
                        <view class="asset-amount">
                            <text class="amount-value">{{ item.commission }}</text>
                        </view>
                    </view>
                    
                    <view class="transaction-details">
                        <view class="detail-row">
                            <text class="detail-label">折扣率:</text>
                            <text class="detail-value">{{ (item.ratio * 100).toFixed(0) }}%</text>
                        </view>
                        <view class="detail-row">
                            <text class="detail-label">約合人民幣:</text>
                            <text class="detail-value price">¥{{ (item.commission * item.ratio).toFixed(2) }}</text>
                        </view>
                        <view class="detail-row" v-if="activeTab === 1 && item.is_sale === 1">
                            <text class="detail-label">購買人:</text>
                            <text class="detail-value">{{ item.buy_nickname }}</text>
                        </view>
                        <view class="detail-row">
                            <text class="detail-label">發布時間:</text>
                            <text class="detail-value time">{{ item.createtime }}</text>
                        </view>
                    </view>
                </view>

                <view class="list-item-footer">
                    <!-- 售卖中状态的按钮 -->
                    <template v-if="activeTab === 0">
                        <button class="action-btn buy-btn" @click="openModal(item)" v-if="item.is_author === 0">
                            <text class="btn-icon">💰</text>
                            <text class="btn-text">{{$t('買入')}}</text>
                        </button>
                        <button class="action-btn cancel-btn" @click="cancelOrder(item, index)" v-if="item.is_author === 1">
                            <text class="btn-icon">✖</text>
                            <text class="btn-text">{{$t('取消')}}</text>
                        </button>
                    </template>
                    
                    <!-- 交易中状态的按钮 -->
                    <template v-if="activeTab === 1">
                        <button class="action-btn upload-btn" @click="upVoucher(item)" v-if="item.is_buy === 1 && item.is_voucher === 0">
                            <text class="btn-icon">📤</text>
                            <text class="btn-text">{{$t('上傳憑證')}}</text>
                        </button>
                        <button class="action-btn audit-btn" @click="openAudit(item)" v-if="item.is_sale === 1 && item.is_voucher === 1">
                            <text class="btn-icon">✓</text>
                            <text class="btn-text">{{$t('審核')}}</text>
                        </button>
                        <button class="action-btn cancel-btn" @click="cancelOrder(item, index)" v-if="item.is_buy === 1">
                            <text class="btn-icon">✖</text>
                            <text class="btn-text">{{$t('取消')}}</text>
                        </button>
                    </template>
                    
                    <!-- 已完成状态的按钮 -->
                    <template v-if="activeTab === 2">
                        <button class="action-btn detail-btn" @click="showDetail(item)">
                            <text class="btn-text">查看詳情</text>
                        </button>
                    </template>
                </view>
            </view>
            
            <!-- 加载中提示 -->
            <view class="loading-more" v-if="loading">
                <text class="loading-text">加載中...</text>
            </view>
        </view>

        <!-- 买入模态窗口 -->
        <div class="modal" v-if="showModal && selectedItem">
            <div class="modal-content">
                <view class="modal-header">
                    <text class="modal-title">{{$t('買入資產')}}</text>
                    <text class="modal-close" @click="showModal = false">✕</text>
                </view>
                
                <view class="modal-body">
                    <view class="modal-section">
                        <view class="section-title">賣家信息</view>
                        <view class="info-row">
                            <text class="info-label">賣家:</text>
                            <text class="info-value">{{ selectedItem.nickname }}</text>
                        </view>
                        <view class="info-row">
                            <text class="info-label">資產類型:</text>
                            <text class="info-value">{{ selectedItem.asset_name || getAssetTypeName(selectedItem.asset_type) }}</text>
                        </view>
                        <view class="info-row">
                            <text class="info-label">可買數量:</text>
                            <text class="info-value">{{ selectedItem.commission }}</text>
                        </view>
                        <view class="info-row">
                            <text class="info-label">折扣率:</text>
                            <text class="info-value">{{ (selectedItem.ratio * 100).toFixed(0) }}%</text>
                        </view>
                    </view>

                    <view class="modal-section">
                        <view class="section-title">購買信息</view>
                        <view class="buy-input-wrapper">
                            <text class="input-label">購買數量</text>
                            <input 
                                type="number" 
                                class="buy-input" 
                                v-model="buyData"
                                @input="inputData" 
                                placeholder="請輸入購買數量"
                            />
                        </view>
                        <view class="buy-result">
                            <text class="result-label">應付金額:</text>
                            <text class="result-value">¥{{ buyRMB.toFixed(2) }}</text>
                        </view>
                        <view class="buy-note">
                            <text>* 提交後請及時上傳支付憑證，確保交易順利完成</text>
                        </view>
                    </view>
                </view>

                <view class="modal-footer">
                    <button class="modal-btn cancel" @click="showModal = false">{{$t('取消')}}</button>
                    <button class="modal-btn confirm" @click="buyItem(selectedItem)">{{$t('確認買入')}}</button>
                </view>
            </div>
        </div>

        <!-- 审核模态窗口 -->
        <div class="modal" v-if="showAuditModal && selectedItem">
            <div class="modal-content">
                <view class="modal-header">
                    <text class="modal-title">審核支付憑證</text>
                    <text class="modal-close" @click="showAuditModal = false">✕</text>
                </view>

                <view class="modal-body">
                    <view class="modal-section">
                        <view class="section-title">訂單信息</view>
                        <view class="info-row">
                            <text class="info-label">購買人:</text>
                            <text class="info-value">{{ selectedItem.buy_nickname }}</text>
                        </view>
                        <view class="info-row">
                            <text class="info-label">資產類型:</text>
                            <text class="info-value">{{ selectedItem.asset_name || getAssetTypeName(selectedItem.asset_type) }}</text>
                        </view>
                        <view class="info-row">
                            <text class="info-label">交易數量:</text>
                            <text class="info-value">{{ selectedItem.commission }}</text>
                        </view>
                        <view class="info-row">
                            <text class="info-label">應付金額:</text>
                            <text class="info-value highlight">¥{{ (selectedItem.ratio * selectedItem.commission).toFixed(2) }}</text>
                        </view>
                        <view class="info-row">
                            <text class="info-label">發布時間:</text>
                            <text class="info-value">{{ selectedItem.createtime }}</text>
                        </view>
                    </view>

                    <view class="modal-section">
                        <view class="section-title">支付憑證</view>
                        <view class="voucher-images">
                            <view v-for="(item, index) in selectedItem.buy_voucher" :key="index" class="voucher-image-wrapper">
                                <image :src="item" @tap="previewImage" :data-url="item" mode="aspectFit" class="voucher-image"></image>
                            </view>
                        </view>
                        <view class="audit-note">
                            <text>請務必確認上傳支付憑證的有效性與真實性，支付金額必須與訂單應付金額一致。</text>
                        </view>
                    </view>
                </view>

                <view class="modal-footer">
                    <button class="modal-btn reject" @click="postAudit(selectedItem, 2)">不通過</button>
                    <button class="modal-btn approve" @click="postAudit(selectedItem, 1)">通過</button>
                </view>
            </div>
        </div>
        
        <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
    </view>
</template>
<script>
var app = getApp();
export default {
    data() {
        return {
            opt: {},
            pagenum: 1,
            my: 0,
            activeTab: 0,
            saleList: [],
            nodata: false,
            loading: false,
            
            // 购买相关数据
            buyData: 0,
            buyRMB: 0,
            
            // 弹窗相关
            showModal: false,
            showAuditModal: false,
            selectedItem: null,
            
            // 资源相关
            pre_url: app.globalData.pre_url,
            content_pic: [],
            
            // 资产类型映射
            assetTypes: {
                'money': '余額',
                'score': '積分',
                'commission': '佣金',
                'heiscore': '現金券',
                'contribution': '貢獻值',
                'score_huangjin': '黃積分'
            }
        }
    },
    
    computed: {
        // 页面标题
        pageTitle() {
            const titles = ['售賣列表', '交易進行中', '歷史交易'];
            return titles[this.activeTab] || '資產售賣';
        },
        
        // 空状态提示文字
        emptyStateText() {
            if (this.activeTab === 0) {
                return this.my === 1 ? '您還沒有發布任何售賣' : '暫無可購買的資產';
            } else if (this.activeTab === 1) {
                return '暫無進行中的交易';
            } else {
                return '暫無已完成的交易';
            }
        }
    },
    
    i18n: {
        messages: {
            'zh-CN': {
                '售賣中': '售賣中',
                '交易中': '交易中',
                '已完成': '已完成', 
                '買入': '買入',
                '買入資產': '買入資產',
                '取消': '取消',
                '上傳憑證': '上傳憑證',
                '審核': '審核',
                '確認買入': '確認買入'
            }
        }
    },
    
    onLoad: function(opt) {
        this.opt = app.getopts(opt);
        if (this.opt.my) {
            this.my = parseInt(opt.my);
        }
        
        // 设置页面标题
        uni.setNavigationBarTitle({
            title: '資產售賣'
        });
        
        this.getdata();
    },
    
    onPullDownRefresh: function() {
        this.refreshData();
        uni.stopPullDownRefresh();
    },
    
    onReachBottom: function() {
        if (!this.nodata && !this.loading) {
            this.pagenum = this.pagenum + 1;
            this.getdata(true);
        }
    },
    
    methods: {
        // 国际化辅助函数
        $t(key) {
            if(this.$i18n && this.$i18n.t) {
                return this.$i18n.t(key);
            }
            return key;
        },
        
        // 刷新数据
        refreshData() {
            this.pagenum = 1;
            this.saleList = [];
            this.nodata = false;
            this.getdata();
        },
        
        // 切换过滤状态（我的/全部）
        toggleFilter() {
            this.my = this.my === 1 ? 0 : 1;
            this.refreshData();
        },
        
        // 获取数据
        getdata: function(loadmore) {
            if (!loadmore) {
                this.pagenum = 1;
                this.saleList = [];
                this.nodata = false;
            }
            
            var that = this;
            var pagenum = that.pagenum;
            var st = that.activeTab;
            
            that.loading = true;
            app.get('ApiShoumai/getList', { st: st, pagenum: pagenum, my: that.my }, function(res) {
                that.loading = false;
                
                if (!res || !res.datalist) {
                    that.nodata = true;
                    return;
                }
                
                var data = res.datalist;
                if (data.length == 0) {
                    that.nodata = true;
                }
                
                // 处理数据
                data.forEach(item => {
                    // 处理支付凭证字符串转数组
                    if (item.buy_voucher && typeof item.buy_voucher === 'string') {
                        item.buy_voucher = item.buy_voucher.split(',').filter(url => url);
                    } else if (!item.buy_voucher) {
                        item.buy_voucher = [];
                    }
                    
                    // 如果资产类型为空，默认为佣金
                    if (!item.asset_type) {
                        item.asset_type = 'commission';
                    }
                });
                
                var datalist = that.saleList;
                var newdata = datalist.concat(data);
                that.saleList = newdata;
            });
        },
        
        // 切换分段控制器
        activeTabFn(index) {
            if (this.activeTab !== index) {
                this.activeTab = index;
                this.refreshData();
            }
        },
        
        // 金额输入处理
        inputData(e) {
            if (!this.selectedItem) return;
            
            let value = e.detail ? e.detail.value : e.target.value;
            let maxAmount = parseFloat(this.selectedItem.commission);
            
            if (value === '' || isNaN(value)) {
                this.buyData = '';
                this.buyRMB = 0;
                return;
            }
            
            // 转换为数字并验证
            let amount = parseFloat(value);
            
            // 验证金额
            if (amount <= 0) {
                app.error('購買數量必須大於0');
                this.buyData = '';
                this.buyRMB = 0;
                return;
            }
            
            if (amount > maxAmount) {
                app.error('超出可購買數量');
                this.buyData = maxAmount;
                amount = maxAmount;
            } else {
                this.buyData = amount;
            }
            
            // 计算应付金额
            this.buyRMB = parseFloat((amount * this.selectedItem.ratio).toFixed(2));
        },
        
        // 获取资产类型名称
        getAssetTypeName(type) {
            return this.assetTypes[type] || '佣金';
        },
        
        // 获取状态文本
        getStatusText(item) {
            if (this.activeTab === 0) {
                return '售賣中';
            } else if (this.activeTab === 2) {
                if (item.status === 3) {
                    return '已取消';
                } else if (item.status === 1) {
                    return '交易成功';
                } else if (item.status === 2) {
                    return '審核未通過';
                } else {
                    return '已完成';
                }
            } else {
                if (item.is_buy === 1 && item.is_voucher === 0) {
                    return '待上傳憑證';
                } else if (item.is_sale === 1 && item.is_voucher === 1) {
                    return '待審核';
                } else {
                    return '交易中';
                }
            }
        },
        
        // 获取状态样式类
        getStatusClass(item) {
            if (this.activeTab === 0) {
                return 'status-selling';
            } else if (this.activeTab === 2) {
                if (item.status === 3) {
                    return 'status-canceled';
                } else if (item.status === 1) {
                    return 'status-success';
                } else if (item.status === 2) {
                    return 'status-rejected';
                } else {
                    return 'status-completed';
                }
            } else {
                if (item.is_voucher === 0) {
                    return 'status-pending';
                } else {
                    return 'status-reviewing';
                }
            }
        },
        
        // 购买操作
        buyItem(item) {
            if (!this.buyData || parseFloat(this.buyData) <= 0) {
                app.error('請輸入購買數量');
                return;
            }
            
            var that = this;
            
            // 二次确认
            app.confirm(`確認購買${this.buyData}${item.asset_name || this.getAssetTypeName(item.asset_type)}嗎？應付金額${this.buyRMB.toFixed(2)}元`, function() {
                app.showLoading('提交中');
                app.post('ApiShoumai/postBuy', { 
                    id: item.id, 
                    money: that.buyData 
                }, function(data) {
                    app.showLoading(false);
                    
                    if (data.status == 0) {
                        app.error(data.msg);
                        return;
                    } else {
                        app.success(data.msg);
                        that.showModal = false;
                        
                        // 跳转到凭证上传页面
                        setTimeout(function() {
                            app.goto("/pagesExa/shoumai/voucher?id=" + data.id);
                        }, 1000);
                    }
                });
            });
        },
        
        // 打开购买模态框
        openModal(item) {
            this.selectedItem = item;
            this.buyData = "";
            this.buyRMB = 0;
            this.showModal = true;
        },
        
        // 上传凭证
        upVoucher(item) {
            app.goto("/pagesExa/shoumai/voucher?id=" + item.id);
        },
        
        // 打开审核模态框
        openAudit(item) {
            this.selectedItem = item;
            this.showAuditModal = true;
        },
        
        // 查看交易详情
        showDetail(item) {
            // 如果需要查看详情，可以跳转到详情页或者打开详情模态框
            app.goto("/pagesExa/shoumai/detail?id=" + item.id);
        },
        
        // 取消订单
        cancelOrder(item, index) {
            var that = this;
            
            app.confirm('確定要取消該訂單嗎?', function () {
                app.showLoading('处理中');
                app.post('ApiShoumai/cancelOrder', {id: item.id}, function (data) {
                    app.showLoading(false);
                    
                    if (data.status == 0) {
                        app.error(data.msg);
                    } else {
                        that.saleList.splice(index, 1);
                        app.success(data.msg);
                    }
                });
            });
        },
        
        // 提交审核结果
        postAudit(item, st) {
            var that = this;
            var msg = st == 1 ? '是否確認通過審核?' : '是否確認不通過審核?';
            
            app.confirm(msg, function () {
                app.showLoading('处理中');
                app.post('ApiShoumai/postAudit', {id: item.id, st: st}, function (data) {
                    app.showLoading(false);
                    
                    if (data.status == 0) {
                        app.error(data.msg);
                    } else {
                        app.success(data.msg);
                        that.showAuditModal = false;
                        that.refreshData();
                    }
                });
            });
        },
        
        // 预览图片
        previewImage(e) {
            var url = e.currentTarget.dataset.url;
            uni.previewImage({
                urls: [url],
                current: url
            });
        }
    },
};
</script>

<style>
/* 页面容器 */
.container {
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  padding: 30rpx 30rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.page-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.filter-btn {
  display: flex;
  align-items: center;
  background: #f0f2f5;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.filter-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.filter-icon {
  font-size: 26rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  background: #533CD7;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
}

.add-icon {
  color: #fff;
  font-size: 28rpx;
  margin-right: 6rpx;
}

.add-text {
  color: #fff;
  font-size: 24rpx;
}

/* 分段控制器 */
.segment-control {
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.scroll-container {
  display: flex;
  background: #f0f2f5;
  border-radius: 8rpx;
  overflow: hidden;
}

.segment {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.segment.active {
  background-color: #533CD7;
  color: #fff;
  font-weight: 500;
}

/* 内容区域 */
.tab-content {
  flex: 1;
  padding: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 20rpx;
}

.empty-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  margin-bottom: 30rpx;
}

.empty-action {
  background: #533CD7;
  color: #fff;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
}

/* 列表项 */
.list-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f2f5;
}

.seller-info {
  display: flex;
  align-items: center;
}

.seller-badge {
  background: #e8f0fe;
  color: #533CD7;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
}

.seller-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.item-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

/* 状态样式 */
.status-selling {
  background: #e5f7ff;
  color: #0587f0;
}

.status-pending {
  background: #fff5e5;
  color: #ff9500;
}

.status-reviewing {
  background: #e6f7f2;
  color: #10b981;
}

.status-success {
  background: #e6f7f2;
  color: #10b981;
}

.status-canceled {
  background: #f1f1f1;
  color: #999;
}

.status-rejected {
  background: #ffefef;
  color: #ff4d4f;
}

.status-completed {
  background: #e8f0fe;
  color: #533CD7;
}

/* 列表内容 */
.list-item-content {
  padding: 20rpx 0;
}

.asset-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.asset-type {
  margin-right: 20rpx;
}

.asset-label {
  font-size: 26rpx;
  color: #666;
  background: #f5f5f5;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}

.asset-amount {
  flex: 1;
}

.amount-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.transaction-details {
  padding: 0 10rpx;
}

.detail-row {
  display: flex;
  margin-bottom: 10rpx;
}

.detail-label {
  width: 160rpx;
  font-size: 26rpx;
  color: #888;
}

.detail-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.detail-value.price {
  color: #ff6b6b;
  font-weight: 500;
}

.detail-value.time {
  color: #999;
  font-size: 24rpx;
}

/* 列表底部 */
.list-item-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f2f5;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64rpx;
  padding: 0 24rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  margin-left: 16rpx;
  border: none;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.buy-btn {
  background: #533CD7;
  color: #fff;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.upload-btn {
  background: #0587f0;
  color: #fff;
}

.audit-btn {
  background: #10b981;
  color: #fff;
}

.detail-btn {
  background: #f5f5f5;
  color: #666;
}

/* 加载更多 */
.loading-more {
  text-align: center;
  padding: 20rpx 0;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 模态窗口 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.modal-header {
  position: relative;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f2f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 32rpx;
  color: #999;
}

.modal-body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 30rpx;
}

.modal-section {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f2f5;
}

.modal-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-label {
  width: 160rpx;
  font-size: 26rpx;
  color: #888;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.info-value.highlight {
  color: #ff6b6b;
  font-weight: 500;
}

/* 购买输入 */
.buy-input-wrapper {
  margin-bottom: 20rpx;
}

.input-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.buy-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.buy-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0;
}

.result-label {
  font-size: 28rpx;
  color: #666;
}

.result-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.buy-note {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-top: 10rpx;
}

/* 凭证图片 */
.voucher-images {
  display: flex;
  flex-wrap: wrap;
}

.voucher-image-wrapper {
  width: 180rpx;
  height: 180rpx;
  margin: 0 20rpx 20rpx 0;
  background: #f5f5f5;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
}

.voucher-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.audit-note {
  padding: 16rpx;
  background: #fff9e6;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #f59942;
  line-height: 1.5;
  margin-top: 10rpx;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f2f5;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.modal-btn.confirm {
  background: #533CD7;
  color: #fff;
}

.modal-btn.reject {
  background: #ff6b6b;
  color: #fff;
  margin-right: 20rpx;
}

.modal-btn.approve {
  background: #10b981;
  color: #fff;
}
</style>
