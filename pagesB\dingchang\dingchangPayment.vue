<template>
	<view>
		<view class="head" :style="'background:'+secondary_color">
			<view class="openingMessage">
				<view class="openingMessage_t">{{book[0].day}}</view>
				<view class="openingMessage_d" v-for="(item, index) in select" :key="index">{{item.sessionName}} {{item.date}}</view>
			</view>
			<view class="openingPrompt" :style="'background:'+primary_color">
				<view class="spaceBetween">
					<image :src="pre_url+'/static/img/mistake.png'" class="openingPrompt_img"></image>
					<view class="openingPrompt_txt1">不支持退款</view>
					<image :src="pre_url+'/static/img/mistake.png'" class="openingPrompt_img"></image>
					<view class="openingPrompt_txt2">不支持改签</view>
				</view>
				<view class="openingPrompt_txt3">订场必读></view>
			</view>
		</view>
		<view class="content">
			
			<view style="padding-top: 30rpx;">
				<view class="specialOffer"> 
		   	<view class="sl_title">预约信息</view>
				<view class="spaceBetween">
					<view class="sl_txt1_l">姓名</view>
					<view class="sl_txt1_r">
			            <input type="text" placeholder="请输入" v-model="name" class="custom-input" />
					</view>
				</view>
				<view class="spaceBetween" style="padding-top: 30rpx;">    
					<view class="sl_txt1_l">手机号码</view>
					<view class="sl_txt1_r">
						<input type="number" placeholder="请输入" maxlength="11" v-model="phone" class="custom-input" />
					</view>      
				</view>     
			  </view>
			 </view>
					
			
			<view style="padding-top: 30rpx;">
				<view class="specialOffer"> 
					<view class="sl_title">优惠活动</view>
					<view class="spaceBetween">
						<view class="sl_txt1_l">卡券</view>
						<view class="sl_txt1_r">
							<view class="sl_txt1_r_a">选择卡券</view>
							<view class="sl_txt1_r_b">></view>
						</view>
					</view>
					<view class="spaceBetween"
						style="margin-top: 20rpx; border-top: 1rpx solid #dcdcdc;padding-top: 20rpx;">
						<view class="sl_txt2_l">活动</view>
						<view class="sl_txt2_r">
							<view class="tag_item fitness">
								<view class="tag_icon">
									<text style="color: #d62828; font-size: 18px;">🎁</text>
								</view>
								<view class="tag_text">全民健身补贴</view>
							</view>
							<view class="sl_txt2_r_b">></view>
						</view>
					</view>
				</view>
			</view>
			
			<view style="padding-top: 30rpx;" v-if="yue==1">
				<view class="specialOffer">   
				       
					<view class="spaceBetween">
						<view class="sl_txt1_l">是否约场</view>
						<view class="switchBox">
							<switch @change="switchChange" :color="primary_color" :checked="is_yue" />
						</view> 
					</view>
					
					<view style="font-size: 22rpx;padding-top: 20rpx;">发起约球后，其他用户也可以者到约球信息，并报名参加。</view>
				
					<view class="spaceBetween" style="padding-top: 30rpx;" v-if="is_yue">
						<view class="sl_txt1_l">约球主题</view>
						<view class="sl_txt1_r" style="width:500rpx;">
							<input type="text" placeholder="请输入" v-model="title" class="custom-input" />
						</view>
					</view>
					
					<view class="spaceBetween" style="padding-top: 30rpx;"  v-if="is_yue">
						<view class="sl_txt1_l">约球人数</view>
						<view class="sl_txt1_r" style="width:500rpx;">
							<input type="number" placeholder="请输入" v-model="application" class="custom-input" />
						</view>      
					</view> 
					
					<view class="spaceBetween" style="padding-top: 30rpx;"  v-if="is_yue">    
						<view class="sl_txt1_l">约球介绍</view>         
						<view class="sl_txt1_r" style="width:500rpx;">  
							<textarea placeholder="请输入" v-model="introduce" class="custom-textarea"></textarea>
						</view>      
					</view> 
					
			    </view>
	
			</view>	
			
		</view>
<!-- 		<view class="discountCoupon">
			<view class="discountCoupon_title">
				<view class="discountCoupon_title_l alignItems">
					<image src="@/static/img/gift.png" class="discountCoupon_title_img"></image>
					<view class="discountCoupon_title_txt">百万补贴</view>
				</view>
				<view class="discountCoupon_title_r">球拍48小时内发货</view>
			</view>
			<view class="discountCoupon_content">
				<view class="discountCoupon_txt">
					100元定场券（本单可减20元）+一直趣运动联合研发全碳素球拍
				</view>
				<view></view>
				<view class="discountCoupon_txt1">
					支付成功后填写收货地址
					<text style="color: #999999; font-size: 20px;">❓</text>
				</view>
			</view>
		</view> -->
		<view class="bottom spaceBetween" style="padding: 20rpx; position: fixed;z-index: 100;">
			<view>
				<view>
					<text class="bottom_txt1">合计</text>
					<text class="bottom_txt2" :style="'color:'+primary_color">¥{{formattedMoney}}</text>
			<!-- 		<text class="bottom_txt3">明细∧</text> -->
				</view>

				<view class="bottom_txt4" v-if="fee>0">手续费¥{{formattedFee}}</view>
				
				<view class="bottom_txt4" v-else>已优惠¥0</view>
			</view>
			<view class="bt" @tap="submit" :style="'background:'+primary_color">立即支付</view>
		</view>
	</view>
</template>

<script>
	
	var app = getApp();
	
	export default {
		data() {
			return {
               book : '',
			   select: [],
			   phone : '',
			   name : '',
			   money : 0,
			   primary_color : '',
			   secondary_color : '',
			   is_yue : false,
			   title : '',
			   introduce : '',
			   application : '',
			   yue : 0,
			   shou : {},
			   fee : 0,
			   pre_url: ''  // 添加云资源域名前缀
			}
		},
		onLoad(opt) {
			try {
				// 安全解析参数
				this.book = opt.book ? JSON.parse(decodeURIComponent(opt.book)) : [];
				this.yue = opt.yue || 0;
				this.select = opt.select ? JSON.parse(decodeURIComponent(opt.select)) : [];
				this.shou = opt.shou ? JSON.parse(decodeURIComponent(opt.shou)) : {};
				this.pre_url = app.globalData.pre_url || '';
				
				// 计算总价
				this.calculateTotalPrice();
				
			} catch (error) {
				console.error('参数解析错误:', error);
				uni.showToast({
					icon: 'none',
					title: '页面参数错误'
				});
				// 设置默认值
				this.money = 0;
				this.fee = 0;
			}
			
			
			
			uni.setNavigationBarTitle({
				title: opt.title
			})
	
			this.primary_color = app.getCache('primary_color')
			
			this.secondary_color = app.getCache('secondary_color')
			
			
		},
		computed: {
			// 计算属性：格式化的总金额
			formattedMoney() {
				return this.formatMoney(this.money);
			},
			// 计算属性：格式化的手续费
			formattedFee() {
				return this.formatMoney(this.fee);
			}
		},
		methods: {
			// 计算总价方法
			calculateTotalPrice() {
				// 重置金额
				this.money = 0;
				this.fee = 0;
				
				// 计算基础价格
				if (this.select && Array.isArray(this.select)) {
					this.select.forEach(item => {
						const price = parseFloat(item.price) || 0;
						this.money += price;
					});
				}
				
				// 计算手续费
				if (this.shou && Object.keys(this.shou).length > 0) {
					if (this.shou.is_percentage == 1) {
						// 按百分比计算手续费
						const feeRate = parseFloat(this.shou.fees) || 0;
						this.fee = parseFloat((this.money * feeRate / 100).toFixed(2));
					} else {
						// 固定手续费
						if (this.shou.fees_type == 1) {
							const duration = parseFloat(this.shou.duration) || 0;
							const fees = parseFloat(this.shou.fees) || 0;
							this.fee = parseFloat((duration * fees).toFixed(2));
						} else {
							this.fee = parseFloat(this.shou.fees) || 0;
						}
					}
					
					// 手续费加入总价
					this.money = parseFloat((this.money + this.fee).toFixed(2));
				}
				
				// 确保金额是数字
				this.money = isNaN(this.money) ? 0 : this.money;
				this.fee = isNaN(this.fee) ? 0 : this.fee;
				
				console.log('计算后的金额:', {
					money: this.money,
					fee: this.fee,
					select: this.select,
					shou: this.shou
				});
			},
			
			// 格式化金额显示
			formatMoney(amount) {
				if (isNaN(amount) || amount === null || amount === undefined) {
					return '0.00';
				}
				return parseFloat(amount).toFixed(2);
			},
			
            submit(){
				
				if(this.name == ''){
					
					uni.showToast({
						icon:'none',
						title:'请输入姓名'
					})
					
					return
				}
				
				
				if(this.phone == ''){
					
					uni.showToast({
						icon:'none',
						title:'请输入手机号码'
					})
					
					return
				}
				
				
				
				if(this.phone.length < 11){
					
					uni.showToast({
						icon:'none',
						title:'请输入正确手机号码'
					})
					
					return
				}
				
				let data = {
					book : this.book,
					name : this.name,
					phone : this.phone,
					title: this.title,
					introduce : this.introduce,
					application : this.application,
					is_appointment : this.is_yue?1:0
				}
					
				app.showLoading('提交中');

					         
				app.post('ApiVenues/postOrder', data, function(res) {
					
					console.log(res);
					
					app.showLoading(false);
					if (res.status == 0) {
						app.alert(res.msg);
						return;
					}
					
					uni.navigateTo({
						url:'/pages/pay/pay?id='+res.data.id
					})

				});
				
			},
			
			switchChange(e){
				this.is_yue =e.detail.value;
			}
		}
	}
</script>

<style lang="scss">
	.custom-input {
		width: 100%;
		height: 80rpx;
		padding: 20rpx 24rpx;
		border: 2rpx solid #e5e5e5;
		border-radius: 12rpx;
		font-size: 32rpx;
		color: #333;
		background-color: #fff;
		box-sizing: border-box;
		line-height: 40rpx;
	}
	
	.custom-input:focus {
		border-color: #007aff;
		outline: none;
		box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
	}
	
	.custom-input::placeholder {
		color: #999;
		font-size: 30rpx;
	}
	
	.custom-textarea {
		width: 100%;
		min-height: 160rpx;
		padding: 20rpx 24rpx;
		border: 2rpx solid #e5e5e5;
		border-radius: 12rpx;
		font-size: 32rpx;
		color: #333;
		background-color: #fff;
		box-sizing: border-box;
		resize: vertical;
		line-height: 44rpx;
	}
	
	.custom-textarea:focus {
		border-color: #007aff;
		outline: none;
		box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
	}
	
	.custom-textarea::placeholder {
		color: #999;
		font-size: 30rpx;
	}

	.jc_center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.alignItems {
		display: flex;
		align-items: center;
	}

	.spaceBetween {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.head {
		background-color: #7771ec;
	}

	.openingMessage {
		padding-left: 30rpx;
		padding-top: 80rpx;
		color: #ffffff;
		margin-bottom: 50rpx;

		.openingMessage_t {
			margin-bottom: 10rpx;
		}

		.openingMessage_d {}
	}

	.openingPrompt {
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #ffffff;
		padding: 30rpx 30rpx 60rpx;
		background-color: #5b50da;
		border-radius: 30rpx 30rpx 0 0;

		.openingPrompt_txt1 {
			margin-right: 30rpx;
		}

		.openingPrompt_img {
			width: 30rpx;
			height: 30rpx;
			margin-right: 10rpx;
		}
	}

	.content {
		background-color: #f5f5f5;
		z-index: 100;
		border-radius: 30rpx 30rpx 0 0;
		margin-top: -40rpx;
		padding-bottom: 180rpx;
	}

	.specialOffer {
		background-color: #ffffff;
		margin-left: 30rpx;
		margin-right: 30rpx;
		padding: 15rpx 30rpx;
		border-radius: 10rpx;

		.sl_title {
			font-weight: bold;
			font-size: 32rpx;
			margin-bottom: 40rpx;
			margin-top: 20rpx;
		}

		.sl_txt1_l {}

		.sl_txt1_r {
			color: #999999;
			display: flex;
			align-items: center;

			.sl_txt1_r_a {}

			.sl_txt1_r_b {
				margin-left: 20rpx;
			}
		}

		.sl_txt2_l {}

		.sl_txt2_r {
			color: #999999;
			display: flex;
			align-items: center;

			.sl_txt2_r_a {}

			.sl_txt2_r_b {
				margin-left: 20rpx;
			}
		}
	}

	.tag_item {
		display: flex;
		align-items: center;
		border: 1px solid;
		color: #d62828;
		margin: 6rpx;
		border-radius: 8rpx;
		font-size: 24rpx;

		&.fitness {
			background: #d62828;
			color: white;
			border: none;
		}
	}

	.tag_icon {
		background: #efd599;
		height: 100%;
		width: 46rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 9rpx 0 18rpx 9rpx;
	}

	.discountCoupon {
		background-color: #ffffff;
		margin: 30rpx 30rpx 0;
		position: relative;
		.discountCoupon_title {
			display: flex;
			align-content: center;
			.discountCoupon_title_l {
				background-color: #d9643e;
				padding: 15rpx 20rpx  20rpx 10rpx;
				border-radius: 20rpx 30rpx 0 0;
				.discountCoupon_title_img {
					width: 30rpx;
					height: 30rpx;
					margin-left: 20rpx;
				}

				.discountCoupon_title_txt {
					color: #ffffff;
					margin-left: 20rpx;
					padding-right: 30rpx;
				}
			}

			.discountCoupon_title_r {
				padding: 15rpx 30rpx 20rpx;
				background-color: #f9edea;
				color: #d83a4d;
				border-radius: 0rpx 0rpx 20rpx 20rpx;
				margin-left: -10rpx;
			}
		}

		.discountCoupon_content {
			padding-top: 20rpx;
			background: #ffffff;
			position: absolute;
			top: 85%;
			border-radius: 20rpx ;
			.discountCoupon_txt {
				margin: 20rpx;
				font-weight: bold;
			}

			.discountCoupon_txt1 {
				margin: 20rpx;
				display: flex;
				align-items: center;
				color: #999999;
			}
		}
	}
	.bottom{
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		.bottom_txt1{
			font-size: 24rpx;
		}
		.bottom_txt2{
			font-size: 36rpx;
			color: #d74140;
			margin-left: 10rpx;
			font-weight: bold;
		}
		.bottom_txt3{
			color: #999999;
			margin-left: 10rpx;
		}
		.bottom_txt4{
			color: #999999;
			margin-top: 10rpx;
		}
		.bt{
			padding: 20rpx 10%;
			background-color: #6b63ec;
			border-radius: 40rpx;
			color: #ffffff;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
	
	.switchBox{
		// 调整大小
		transform: scale(0.9,0.9)
	}
</style>