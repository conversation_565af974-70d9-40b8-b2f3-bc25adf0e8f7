<template>
  <view class="container">
    <view class="tabs">
      <view class="tab active">进行中</view>
      <view class="tab">已完成</view>
    </view>
    <view class="order-info">
      <text class="order-number">订单号：D20240520102208004317973104</text>
      <view class="info-row">
        <view class="info-item">
          <text class="amount">¥ 1.01</text>
          <text class="label">消费金额</text>
        </view>
        <view class="info-item">
          <text class="amount">¥ 1.01</text>
          <text class="label">最高补贴金额</text>
        </view>
        <view class="info-item">
          <text class="amount">17</text>
          <text class="label">订单排号</text>
        </view>
      </view>
    </view>
    <view class="footer">
      <text class="date">2024-05-20 10:22</text>
      <text class="action" @click="showModal">结束排队，领取补贴 &gt;</text>
    </view>
    <view class="floating-button">
      <text>加速补贴</text>
    </view>
    <view v-if="modalVisible" class="modal-overlay">
      <view class="modal-content">
        <view v-if="!selectedOption">
          <text class="modal-title">选择结束排队的原因</text>
          <view class="option" v-for="option in options" :key="option.value" @click="selectOption(option)">
            <text>{{ option.text }}</text>
          </view>
        </view>
        <view v-if="selectedOption" class="modal-info">
          <text class="modal-amount">{{ selectedOption.amount }}</text>
          <view class="modal-text">
            <text>{{ selectedOption.description }}</text>
          </view>
          <button class="modal-button confirm" @click="confirmEndQueue">确认结束领取补贴</button>
          <button class="modal-button back" @click="backToOptions">返回选择原因</button>
        </view>
        <button class="modal-button continue" @click="hideModal">继续排队等待补贴 &gt;</button>
      </view>
    </view>
  </view>
</template>

<style>
  /* 其他样式省略 */
  
  .footer {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    background-color: white;
    margin-top: 10px;
    border-top: 1px solid #e5e5e5;
  }
  
  .date {
    font-size: 14px;
    color: #999;
  }
  
  .action {
    font-size: 14px;
    color: #d92c1e;
    cursor: pointer;
  }
  
  .floating-button {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background-color: #666;
    color: white;
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 14px;
  }
  
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .modal-content {
    width: 80%;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
  }
  
  .modal-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .modal-info {
    margin-top: 20px;
  }
  
  .modal-amount {
    font-size: 24px;
    font-weight: bold;
    color: #d92c1e;
    margin-bottom: 20px;
  }
  
  .modal-text {
    font-size: 14px;
    color: #666;
    text-align: left;
    margin-bottom: 20px;
  }
  
  .modal-button {
    display: block;
    width: 100%;
    padding: 10px;
    margin-top: 10px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
  }
  
  .confirm {
    background-color: #d92c1e;
    color: white;
  }
  
  .back {
    background-color: #f5f5f5;
    color: #333;
  }
  
  .option {
    padding: 10px;
    background-color: #f5f5f5;
    margin-bottom: 10px;
    cursor: pointer;
  }
  
  .option:hover {
    background-color: #e5e5e5;
  }
</style>

<script>
export default {
  data() {
    return {
      modalVisible: false,
      selectedOption: null,
      options: [
        { value: 'reason1', text: '原因一', amount: '¥ 0.05', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。' },
        { value: 'reason2', text: '原因二', amount: '¥ 0.10', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。' },
        { value: 'reason3', text: '原因三', amount: '¥ 0.15', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。' }
      ]
    };
  },
  methods: {
    showModal() {
      this.modalVisible = true;
    },
    hideModal() {
      this.modalVisible = false;
      this.selectedOption = null;
    },
    selectOption(option) {
      this.selectedOption = option;
    },
    backToOptions() {
      this.selectedOption = null;
    },
    confirmEndQueue() {
      this.hideModal();
      // 处理结束排队逻辑
    }
  }
};
</script>
