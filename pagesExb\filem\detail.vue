<template>
<view>
	<block v-if="isload">
		<view class="container">
			<view class="header">
				<view class="file-icon">
					<image class="image" :src="getFileIcon()" mode="aspectFit"></image>
				</view>
				<text class="title" v-if="detail.showname==1">{{detail.name}}</text>
				<view class="subname" v-if="detail.subname">{{detail.subname}}</view>
				<view class="fileinfo">
					<view class="infoitem" v-if="detail.showsendtime==1">
						<image class="icon" src="/static/img/time.png" mode="aspectFit"></image>
						<text class="text">发布时间：{{detail.createtime}}</text>
					</view>
					<view class="infoitem" v-if="detail.showuploader==1">
						<image class="icon" src="/static/img/user.png" mode="aspectFit"></image>
						<text class="text">上传者：{{detail.uploader}}</text>
					</view>
					<view class="infoitem" v-if="detail.showviewcount==1">
						<image class="icon" src="/static/img/view.png" mode="aspectFit"></image>
						<text class="text">查看次数：{{detail.viewcount}}</text>
					</view>
					<view class="infoitem" v-if="detail.showdownloads==1">
						<image class="icon" src="/static/img/download.png" mode="aspectFit"></image>
						<text class="text">下载次数：{{detail.downloads}}</text>
					</view>
					<view class="infoitem">
						<image class="icon" src="/static/img/size.png" mode="aspectFit"></image>
						<text class="text">文件大小：{{detail.filesize_text}}</text>
					</view>
				</view>
				
				<!-- 文件内容预览区域 -->
				<view class="file-preview" v-if="canPreview">
					<!-- 根据文件类型显示不同的预览 -->
					<block v-if="previewType == 'image'">
						<image class="preview-image" :src="detail.filepath" mode="widthFix"></image>
					</block>
					<block v-else-if="previewType == 'pdf'">
						<!-- 简化版PDF预览 -->
						<view class="simple-pdf-preview">
							<view class="pdf-preview-header">
								<text>PDF文件预览</text>
							</view>
							<image class="pdf-preview-image" src="/static/img/filetypes/pdf.png" mode="aspectFit"></image>
							<view class="pdf-preview-info">
								<text>文件名: {{detail.name}}</text>
								<text>类型: PDF文档</text>
								<text>点击下方"预览"按钮查看完整内容</text>
							</view>
						</view>
					</block>
					<block v-else-if="previewType == 'text'">
						<view class="preview-text">
							<text>{{previewContent}}</text>
						</view>
					</block>
					<block v-else>
						<view class="no-preview">
							<text>暂不支持此类型文件在线预览</text>
						</view>
					</block>
				</view>
				<view class="no-preview" v-else>
					<text>暂不支持此类型文件在线预览</text>
				</view>
				
				<!-- 操作按钮区域 -->
				<view class="action-buttons">
					<!-- PDF预览按钮 -->
					<view class="preview-btn" v-if="previewType == 'pdf'" @click="openPdfViewer">
						<text>预览</text>
					</view>
					
					<!-- 下载按钮 -->
					<view class="download-btn" @click="downloadFile">
						<text>下载文件</text>
					</view>
				</view>
				
				<!-- 分类信息 -->
				<view class="category-info" v-if="detail.category">
					<text>分类：{{detail.category.name}}</text>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
// 导入PDF预览组件
import PdfViewer from './components/pdf-viewer.vue';

export default {
  components: {
    PdfViewer
  },
  data() {
    return {
		opt: {},
		loading: false,
		isload: false,
		menuindex: -1,

		detail: {},
		id: 0,
		
		// 预览相关
		canPreview: false,
		previewType: '', // image, pdf, text, none
		previewContent: '',
		useSimplePdfPreview: false,
    };
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.id = this.opt.id || 0;
		this.getdata();
  },
  onPullDownRefresh: function () {
		this.getdata();
  },
  onShareAppMessage: function() {
		var that = this;
		return this._sharewx({
			title: this.detail.name, 
			desc: this.detail.subname, 
			pic: '/static/img/filetypes/' + (this.detail.filetype_icon || 'file') + '.png',
			callback: function() {
				that.sharecallback();
			}
		});
  },
  onShareTimeline: function() {
		var that = this;
		var sharewxdata = this._sharewx({
			title: this.detail.name, 
			desc: this.detail.subname, 
			pic: '/static/img/filetypes/' + (this.detail.filetype_icon || 'file') + '.png',
			callback: function() {
				that.sharecallback();
			}
		});
		var query = (sharewxdata.path).split('?')[1];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
  },
  methods: {
	sharecallback: function() {
		// 分享回调
	},
	getdata: function() {
		var that = this;
		var id = that.id;
		that.loading = true;
		
		app.get('ApiFilem/detail', {id: id}, function (res) {
			that.loading = false;
			if (res.status == 1) {
				that.detail = res.detail;
				
				// 调试日志
				console.log('文件详情:', JSON.stringify(res.detail));
				console.log('文件路径:', res.detail.filepath);
				console.log('文件类型:', res.detail.filetype);
				
				// 设置页面标题
				uni.setNavigationBarTitle({
					title: res.detail.name
				});
				
				// 检查是否可以预览
				that.checkPreview();
				
				that.isload = true;
				uni.stopPullDownRefresh();
			} else {
				app.alert(res.msg || '获取文件详情失败');
			}
		});
	},
	
	// 下载文件
	downloadFile: function() {
		var that = this;
		var id = that.id;
		that.loading = true;
		
		app.get('ApiFilem/download', {id: id}, function (res) {
			that.loading = false;
			if (res.status == 1) {
				// 执行下载或打开文件
				var url = res.url;
				var filename = res.filename;
				
				// 小程序环境下使用小程序API下载
				// #ifdef MP-WEIXIN
				wx.downloadFile({
					url: url,
					success: function(res) {
						var filePath = res.tempFilePath;
						// 打开文件
						wx.openDocument({
							filePath: filePath,
							success: function() {
								console.log('打开文档成功');
							},
							fail: function(error) {
								console.log('打开文档失败', error);
								app.alert('文件打开失败，可能是不支持的文件类型');
							}
						});
					},
					fail: function(error) {
						console.log('下载文件失败', error);
						app.alert('文件下载失败');
					}
				});
				// #endif
				
				// H5环境下使用浏览器下载
				// #ifdef H5
				var a = document.createElement('a');
				a.href = url;
				a.download = filename;
				a.target = '_blank';
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
				// #endif
				
				// APP环境下使用APP API下载
				// #ifdef APP-PLUS
				uni.downloadFile({
					url: url,
					success: function(res) {
						if (res.statusCode === 200) {
							var filePath = res.tempFilePath;
							// 打开文件
							uni.openDocument({
								filePath: filePath,
								success: function() {
									console.log('打开文档成功');
								},
								fail: function(error) {
									console.log('打开文档失败', error);
									app.alert('文件打开失败，可能是不支持的文件类型');
								}
							});
						}
					},
					fail: function(error) {
						console.log('下载文件失败', error);
						app.alert('文件下载失败');
					}
				});
				// #endif
			} else {
				app.alert(res.msg || '获取下载地址失败');
			}
		});
	},
	
	// 检查文件是否可以预览
	checkPreview: function() {
		// 获取文件类型，如果filetype为空，则从文件路径中提取扩展名
		let filetype = this.detail.filetype ? this.detail.filetype.toLowerCase() : '';
		
		// 如果filetype为空，尝试从文件路径中提取扩展名
		if (!filetype && this.detail.filepath) {
			const filepath = this.detail.filepath;
			const fileExtMatch = filepath.match(/\.([^.]+)$/);
			if (fileExtMatch && fileExtMatch[1]) {
				filetype = fileExtMatch[1].toLowerCase();
				console.log('从文件路径提取的扩展名:', filetype);
			}
		}
		
		// 图片类型可以预览
		if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(filetype) > -1) {
			this.canPreview = true;
			this.previewType = 'image';
			return;
		}
        
        // PDF类型可以预览
        if (filetype === 'pdf') {
            this.canPreview = true;
            this.previewType = 'pdf';
            
            // 在小程序环境中使用简化版预览
            // #ifdef MP-WEIXIN
            this.useSimplePdfPreview = true;
            // #endif
            
            return;
        }
		
		// 文本类型可以预览（根据需求可添加更多）
		if (['txt', 'log', 'md', 'json', 'xml', 'html', 'css', 'js'].indexOf(filetype) > -1) {
			this.canPreview = true;
			this.previewType = 'text';
			// 这里可以通过API获取文本内容，但需要后端支持
			// 目前暂时不实现文本预览功能
			return;
		}
		
		// 其他类型暂不支持预览
		this.canPreview = false;
		this.previewType = '';
	},
	getFileIcon: function() {
		// 获取文件类型
		let filetype = this.detail.filetype ? this.detail.filetype.toLowerCase() : '';
		
		// 如果filetype为空，尝试从文件路径中提取扩展名
		if (!filetype && this.detail.filepath) {
			const filepath = this.detail.filepath;
			const fileExtMatch = filepath.match(/\.([^.]+)$/);
			if (fileExtMatch && fileExtMatch[1]) {
				filetype = fileExtMatch[1].toLowerCase();
			}
		}
		
		// 根据文件类型返回对应的图标
		let icon = filetype;
		
		// 如果有明确的文件类型图标，使用该图标
		if (this.detail.filetype_icon && this.detail.filetype_icon !== 'icon-file') {
			icon = this.detail.filetype_icon;
		} else if (filetype === 'pdf') {
			icon = 'pdf';
		} else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(filetype) > -1) {
			icon = 'image';
		} else if (['doc', 'docx'].indexOf(filetype) > -1) {
			icon = 'word';
		} else if (['xls', 'xlsx'].indexOf(filetype) > -1) {
			icon = 'excel';
		} else if (['ppt', 'pptx'].indexOf(filetype) > -1) {
			icon = 'ppt';
		} else if (['txt', 'log'].indexOf(filetype) > -1) {
			icon = 'txt';
		} else if (['zip', 'rar', '7z', 'tar', 'gz'].indexOf(filetype) > -1) {
			icon = 'zip';
		} else {
			icon = 'file';
		}
		
		return '/static/img/filetypes/' + icon + '.png';
	},
	// 打开PDF预览页面
	openPdfViewer: function() {
		uni.navigateTo({
			url: '/pagesExa/filem/pdf-viewer-page?id=' + this.id + '&name=' + encodeURIComponent(this.detail.name)
		});
	}
  }
}
</script>

<style>
.container {padding:20rpx;}
.header {background:#fff;border-radius:12rpx;padding:30rpx;margin-bottom:20rpx;}
.file-icon {display:flex;justify-content:center;margin-bottom:20rpx;}
.file-icon .image {width:120rpx;height:120rpx;}
.title {font-size:34rpx;font-weight:bold;color:#333;line-height:48rpx;text-align:center;margin-bottom:20rpx;display:block;}
.subname {font-size:28rpx;color:#666;line-height:40rpx;margin-bottom:30rpx;text-align:center;}

.fileinfo {background:#f8f8f8;border-radius:8rpx;padding:20rpx;margin-bottom:30rpx;}
.fileinfo .infoitem {display:flex;align-items:center;margin-bottom:10rpx;}
.fileinfo .infoitem:last-child {margin-bottom:0;}
.fileinfo .infoitem .icon {width:32rpx;height:32rpx;margin-right:10rpx;}
.fileinfo .infoitem .text {font-size:26rpx;color:#666;line-height:40rpx;}

.file-preview {margin-bottom:30rpx;border:1px solid #eee;border-radius:8rpx;overflow:hidden;background:#f9f9f9;}
.preview-image {width:100%;height:auto;}
.pdf-preview {width:100%;height:800rpx;border:none;background:#fff;}
.preview-text {padding:20rpx;font-size:26rpx;color:#333;line-height:40rpx;max-height:600rpx;overflow-y:auto;background:#fff;}
.no-preview {padding:100rpx 0;text-align:center;color:#999;font-size:28rpx;}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.preview-btn, .download-btn {
  flex: 1;
  text-align: center;
  font-size: 30rpx;
  line-height: 80rpx;
  border-radius: 8rpx;
  margin: 0 10rpx;
}

.preview-btn {
  background: #4285f4;
  color: #fff;
}

.preview-btn:active {
  background: #3367d6;
}

.download-btn {
  background: #007AFF;
  color: #fff;
}

.download-btn:active {
  background: #0056b3;
}

.category-info {font-size:26rpx;color:#999;text-align:center;}

.simple-pdf-preview {
  padding: 30rpx;
  background: #fff;
  text-align: center;
}
.pdf-preview-header {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 30rpx;
  font-weight: bold;
}
.pdf-preview-image {
  width: 160rpx;
  height: 160rpx;
  margin: 20rpx auto;
}
.pdf-preview-info {
  margin-top: 30rpx;
  text-align: left;
}
.pdf-preview-info text {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 50rpx;
}
</style> 