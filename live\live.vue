<template>
	<view class="live">
		<!-- 加载状态组件 -->
		<loading v-if="loading"></loading>
		
		<!-- H5端视频播放器 -->
		<!-- #ifdef H5 -->
		<template v-if="!useNativeVideo">
			<div class="prism-player" id="player-con"></div>
		</template>
		<template v-else>
			<video 
				class="video" 
				object-fit="contain" 
				:loop="true"
				:muted="false"
				:autoplay="true"
				:controls="true"
				:src="videoSrc"
				:poster="data.cover_img || '/static/live/video/1-poster.png'"
				x5-video-player-type="h5"
				x5-playsinline="true"
				webkit-playsinline="true"
				playsinline="true"
				@error="handleVideoError"
				@waiting="handleVideoWaiting"
				@loadeddata="handleVideoLoaded"
				@timeupdate="handleTimeUpdate"
			>
				<source :src="videoSrc" type="application/x-mpegURL">
				<source :src="videoSrc" type="video/mp4">
			</video>
		</template>
		<!-- #endif -->
		
		<!-- 其他平台视频播放器保持不变 -->
		<!-- #ifndef H5 -->
		<video 
			class="video" 
			object-fit="contain" 
			:loop="true"
			:autoplay="true"
			:controls="false"
			:show-center-play-btn="false"
			:src="videoSrc"
			:poster="data.cover_img || '/static/live/video/1-poster.png'"
			@error="handleVideoError"
		></video>
		<!-- #endif -->
		
		<!-- #ifdef H5 -->
		<div class="content" :class="{hide: hideCoverStatus}">
			<live-header 
				:data="data" 
				class="live-header"
				@onShare="handleHeaderShare"
			></live-header>
			<live-msg ref="liveMsg" class="live-msg"></live-msg>
			<live-footer 
				:room_id="room_id" 
				class="live-footer"
				@clear="handleClear" 
				@sendLiveMsg="sendLiveMsg" 
				@sendLike="sendLike"
				@welcomeUser="welcomeUser" 
				@sendGift="sendGift"
			></live-footer>
			<view class="watch-time-display" 
				:style="{
					background: 'linear-gradient(90deg,' + t('color1') + ' 0%,rgba(' + t('color1rgb') + ',0.8) 100%)'
				}"
			>
				<text class="watch-time-label">观看时长</text>
				<text class="watch-time-value">{{ formatWatchTime }}</text>
			</view>
		</div>
		<!-- #endif -->
		
		<!-- #ifndef H5 -->
		<cover-view class="content" :class="{hide: hideCoverStatus}">
			<!-- 头部导航 -->
			<cover-view class="nav-wrapper">
				<live-header 
					:data="data"
					@onShare="handleHeaderShare"
				></live-header>
			</cover-view>
			
			<!-- 消息区域 -->
			<cover-view class="msg-wrapper">
				<live-msg ref="liveMsg"></live-msg>
			</cover-view>
			
			<!-- 底部菜单 -->
			<cover-view class="footer-wrapper">
				<cover-view class="footer-content">
					<live-footer 
						:room_id="room_id"  <!-- 添加room_id属性 -->
						@clear="handleClear" 
						@sendLiveMsg="sendLiveMsg" 
						@sendLike="sendLike"
						@welcomeUser="welcomeUser" 
						@sendGift="sendGift"
					></live-footer>
				</cover-view>
			</cover-view>
		</cover-view>
		<!-- #endif -->
		
		<!-- 商品 -->
		<uni-popup ref="shopShow" type="bottom" @change="onPopupChange">
			<view class="viewShop popup-content">
				<view class="shop-title">
					<text>文中提到的商品（{{matchedData.length}}）</text>
					<view class="close-btn" @click="closeShopPopup()">
						<uni-icons type="closeempty" size="20" color="#666"></uni-icons>
					</view>
				</view>

				<scroll-view 
					:scroll-top="0" 
					scroll-y="true" 
					class="scroll-view"
					show-scrollbar="false"
					@scrolltolower="loadMore"
				>
					<view class="cart-item" 
						v-for="(item, index) in matchedData" 
						:key="index"
						@click="gotoProduct(item.proid)"
					>
						<view class="image-box">
							<image class="item-image" :src="item.pic" mode="aspectFill"></image>
						</view>

						<view class="item-info">
							<text class="item-name">{{ item.name }}</text>
							<view class="price-action">
								<text class="item-price">
									<text class="symbol">￥</text>
									<text class="number">{{ getProductPrice(item) }}</text>
								</text>
								<view class="buy-btn" @click.stop="gotoProduct(item.proid)">
									<text>去购买</text> 
								</view>
							</view>
						</view>
					</view>
					
					<uni-load-more :status="loadStatus" v-if="!loading"></uni-load-more>
				</scroll-view>
			</view>
		</uni-popup>
		
		<!-- 消息提示组件 -->
		<popmsg ref="popmsg"></popmsg>
		
		<!-- H5分享弹窗 -->
		<!-- #ifdef H5 -->
		<uni-popup ref="sharePopup" type="bottom">
			<view class="share-popup">
				<view class="share-title">分享到</view>
				<view class="share-options">
					<view class="share-item" @click="shareToWechat">
						<image :src="pre_url + '/static/img/reg-weixinlogin.png'" mode="aspectFit"></image>
						<text>微信好友</text>
					</view>
					<view class="share-item" @click="shareToTimeline">
						<image :src="pre_url + '/static/img/reg-weixinlogin.png'" mode="aspectFit"></image>
						<text>朋友圈</text>
					</view>
					<view class="share-item" @click="copyLink">
						<image :src="pre_url + '/static/img/share2.png'" mode="aspectFit"></image>
						<text>复制链接</text>
					</view>
				</view>
				<view class="share-cancel" @click="closeShare">取消</view>
			</view>
		</uni-popup>
		<!-- #endif -->
		
		<view v-if="sharetypevisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal" style="height:320rpx;min-height:320rpx">
				<view class="popup__content">
					<view class="sharetypecontent">
						<view class="f1" @tap="shareapp" v-if="getplatform() == 'app'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'mp'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'h5'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<button class="f1" open-type="share" v-else>
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</button>
						<view class="f2" @tap="showPoster">
							<image class="img" src="/static/img/sharepic.png"/>
							<text class="t1">生成分享图片</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="posterDialog" v-if="showposter">
			<view class="main">
				<view class="close" @tap="posterDialogClose">
					<image class="img" :src="pre_url + '/static/img/close.png'" mode="widthFix" @tap="previewImage" :data-url="posterpic"></image>
				</view>
				<view class="content">
					<image class="img" :src="posterpic" mode="widthFix" @tap="previewImage" :data-url="posterpic"></image>
				</view>
				<view style="display: flex;justify-content: space-between;padding: 0 10px 10px 10px;">
					<button class="pp4" @tap="savePhoto" 
						:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">保存</button>
					<button class="pp4" v-if="getplatform() == 'app'" @tap="shareapp"
						:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">转发</button>
					<button class="pp4" v-else-if="getplatform() == 'mp'" @tap="sharemp"
						:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">转发</button>
					<button class="pp4" v-else-if="getplatform() == 'h5'" @tap="sharemp"
						:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">转发</button>
					<button class="pp4" open-type="share" v-else
						:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">转发</button>
				</view>
			</view>
		</view>
		
		<!-- 海报浮动按钮 -->
		<view class="poster-float-btn" @click="showPosterDialog">
			<image :src="pre_url + '/static/img/poster-icon.png'" mode="aspectFit"></image>
		</view>
		
		<!-- 海报弹窗 -->
		<view class="poster-modal" v-if="showPosterModal" @click.self="closePosterModal">
			<view class="poster-modal-content">
				<view class="poster-modal-close" @click="closePosterModal">
					<uni-icons type="closeempty" size="24" color="#666"></uni-icons>
				</view>
				<view class="poster-modal-image">
					<image :src="posterpic" mode="widthFix" @click="previewImage"></image>
				</view>
				<view class="poster-modal-actions">
					<button @click="savePhoto" class="poster-save-btn">保存海报</button>
				</view>
			</view>
		</view>
		
		<!-- 观看奖励提示 -->
		<view 
			v-if="watchRewardToast.show" 
			class="watch-reward-toast"
			:style="{
				background: 'linear-gradient(90deg,' + t('color1') + ' 0%,rgba(' + t('color1rgb') + ',0.8) 100%)'
			}"
		>
			恭喜获得 {{ watchRewardToast.score }} 积分奖励！
		</view>
		
	</view>
</template>
<script>
	var app = getApp();
	import liveHeader from './components/liveHeader.vue';
	import liveGift from './components/liveGift.vue';
	import liveMsg from './components/liveMsg.vue';
	import liveFooter from './components/liveFooter.vue';
	import loading from '@/components/loading/loading.vue';
	import popmsg from '@/components/popmsg/popmsg.vue';
	import uniIcons from './components/uni-icons/uni-icons.vue'

	
	export default {
		components: { liveHeader, liveGift, liveMsg, liveFooter, loading, popmsg, uniIcons},
		data() {
			return {
				title:'',
				hideCoverStatus: false,
				data : {},
				room_id : '',
				matchedData: [],
				shopAllSelected: false,
				cart_num : 0,
				videoSrc: '@/static/live/video/1.mp4',
				retryCount: 0,
				maxRetries: 3,
				isLoading: false,
				pullUrl: '',
				player: null,
				useNativeVideo: false,  // 是否使用原video标签
				isSilenced: false, // 禁言状态
				isLiveOnline: false, // 直播开播状态
				anchorInfo: null, // 直播信息
				onlineCount: 1, // 初始化为1（当前用户）
				isSocketInited: false, // 标记是否已初始化
				userInfo: null,  // 添加用户信息存储
				loading: false,
				loadStatus: 'more',  // 加载状态：more-加载前、loading-加载中、noMore-没有更多
				pageNum: 1,
				pageSize: 10,
				// 分享相关数据
				share: {
					title: '',
					path: '',
					imageUrl: '',
					desc: '',
					content: ''
				},
				baseUrl: window.location.origin, // 获取当前域名
				pre_url: app.globalData.pre_url,
				sharetypevisible: false,
				showposter: false,
				posterpic: '',
				showPosterModal: false,
				watchTimer: {
					startTime: null,
					totalSeconds: 0,
					timer: null,
					lastUpdateTime: null
				},
				rewardRules: [], // 奖励规则
				watchRewardToast: {
					show: false,
					score: 0
				}
			}
		},
		computed: {
			isAnyItemSelected() {
				return this.matchedData.some(item => item.selected);
			},
			formatWatchTime() {
				const totalSeconds = this.watchTimer.totalSeconds || 0;
				const hours = Math.floor(totalSeconds / 3600);
				const minutes = Math.floor((totalSeconds % 3600) / 60);
				const seconds = totalSeconds % 60;
				
				return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
			}
		},

		onLoad(opt) {
			// 处理scene参数
			if (opt.scene) {
				try {
					// 解析scene参数 格式如: id_3-pid_717
					const sceneParams = {};
					opt.scene.split('-').forEach(item => {
						const [key, value] = item.split('_');
						sceneParams[key] = value;
					});
					
					// 如果scene中包含id，则使用该id
					if (sceneParams.id) {
						opt.id = sceneParams.id;
					}
					
					// 如果scene中包含pid，保存推广者id
					if (sceneParams.pid) {
						app.globalData.pid = sceneParams.pid;
					}
				} catch (e) {
					console.error('解析scene参数失败:', e);
				}
			}

			// 优先使用传入的 id，如果没有则使用本地存储的
			if (opt.id) {
				this.room_id = opt.id;
				// 保存到本地存储
				try {
					uni.setStorageSync('last_live_room_id', opt.id);
				} catch (e) {
					console.error('保存直播间ID失败:', e);
				}
			} else {
				// 尝试从本地存储获取
				let savedId;
				try {
					savedId = uni.getStorageSync('last_live_room_id');
				} catch (e) {
					console.error('获取直播间ID失败:', e);
				}
				
				if (savedId) {
					this.room_id = savedId;
				} else {
					// 如果既没有传入 id 也没有本地存储，可以显示提示或跳转到列表页
						uni.showToast({
							title: '直播间不存在',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
						return;
				}
			}
			
			console.log('页面加载 - room_id:', this.room_id);
			
			// 检查是否是从登录页返回
			if (opt.fromLogin) {
				this.onLoginSuccess();
			}
			
			// 初始化WebSocket连接和监听
			this.initSocketAndSendInit();
			
			// 添加页面可见性变化监听
			// #ifdef H5
			document.addEventListener('visibilitychange', this.handleVisibilityChange);
			// #endif
			
			this.initdata();
			this.startStreamRefresh();
		},
		
		methods: {
			// 获取用户信息
			async getUserInfo() {
				return new Promise((resolve) => {
					if(app.globalData.userinfo) {
						this.userInfo = app.globalData.userinfo;
						resolve(app.globalData.userinfo);
					} else {
						// 等待一段时间后再次检查
						setTimeout(() => {
							if(app.globalData.userinfo) {
								this.userInfo = app.globalData.userinfo;
								resolve(app.globalData.userinfo);
							} else {
								resolve(null);
							}
						}, 1000); // 等待1秒
					}
				});
			},

			// 初始化WebSocket连接和监听
			async initSocketAndSendInit() {
				// 先获取用户信息
				await this.getUserInfo();
				
				// 确保WebSocket已连接
				app.openSocket();
				
				// 初始化在线人数为1（当前用户）
				this.onlineCount = 1;
				if(this.$refs.liveHeader) {
					this.$refs.liveHeader.onlineCount = 1;
				}
				
				// 发送初始化消息
				app.sendSocketMessage({
					type: 'live_web_init',
					data: {
						aid: app.globalData.aid,
									mid: app.globalData.mid,
									room: this.room_id
					}
				});

				// 添加消息监听
				uni.onSocketMessage((res) => {
					try {
						const data = JSON.parse(res.data);
						this.receiveMessage(data);
					} catch(e) {
						console.error('解析消息失败:', e);
					}
				});
			},

			// 处理接收到的消息
			receiveMessage(data) {
				console.log('接收消息:', data);
				
				if(data.type == 'live_web'){
					// 添加调试日志
					console.log('处理live_web消息:', {
						has_data: !!data.data,
						has_info: !!(data.data && data.data.info),
						online_num: data.data?.online_num,
						message_type: data.data?.live_type
					});
					
					// 检查data.data和info是否存在
					if(data.data && data.data.info) {
						// 判断消息类型
						if(data.data.live_type === 'join') {
							// 用户进入直播间，增加计数
							this.onlineCount++;
							this.$refs.liveHeader.handleUserEnter({
								id: data.data.info.mid || data.data.info.gid,
								nickname: data.data.info.nickname,
								avatar: data.data.info.headimg,
								onlineCount: this.onlineCount
							});
							// 发送系统消息
							let msg = '-' + data.data.content + '-' + data.data.live_type;
							this.$refs.liveMsg.sendLiveMsg(msg);
						} 
						else if(data.data.live_type === 'out') {
							// 用户离开直播间，减少计数
							if(this.onlineCount > 0) {
								this.onlineCount--;
							}
							this.$refs.liveHeader.handleUserLeave(data.data.info.mid || data.data.info.gid);
							// 更新header组件的在线人数
							if(this.$refs.liveHeader) {
								this.$refs.liveHeader.onlineCount = this.onlineCount;
							}
							// 发送系统消息
							let msg = '-' + data.data.content + '-' + data.data.live_type;
							this.$refs.liveMsg.sendLiveMsg(msg);
						}
						else if(data.data.live_type === 'like') {
							// 点赞消息
							let msg = '-' + data.data.content + '-' + data.data.live_type;
							this.$refs.liveMsg.sendLiveMsg(msg);
						}
						else {
							// 用户消息带昵称
							let msg = data.data.info.nickname + '-' + data.data.content + '-' + data.data.live_type;
							this.$refs.liveMsg.sendLiveMsg(msg);
						}
					} else {
						// 处理其他类型的live_web消息
						if(data.data && data.data.content) {
							if(data.data.live_type === 'join' || data.data.live_type === 'out' || data.data.live_type === 'like') {
								// 系统消息
								let msg = '-' + data.data.content + '-' + data.data.live_type;
								this.$refs.liveMsg.sendLiveMsg(msg);
							} else {
								// 用户消息
								const nickname = app.globalData.userinfo ? app.globalData.userinfo.nickname : '用户';
								let msg = nickname + '-' + data.data.content;
								if(data.data.live_type) {
									msg += '-' + data.data.live_type;
								}
								this.$refs.liveMsg.sendLiveMsg(msg);
							}
						}
					}
				} else if(data.type == 'response') {
					// 处理response类型的消息
					console.log('收到response消息:', data);
				}
			},

			// 监听页面刷新
			handleVisibilityChange() {
				if(document.visibilityState === 'visible') {
					console.log('页面被刷新或重新获得焦点');
					this.initSocketAndSendInit();
				}
			},

			// 加载阿里云播放器源
			loadAliPlayer() {
				return new Promise((resolve, reject) => {
					if (window.Aliplayer) {
						resolve(window.Aliplayer);
						return;
					}

					// 加载CSS
					const link = document.createElement('link');
					link.rel = 'stylesheet';
					link.href = 'https://g.alicdn.com/de/prismplayer/2.15.6/skins/default/aliplayer-min.css';
					document.head.appendChild(link);

					// 加载JS
					const script = document.createElement('script');
					script.type = 'text/javascript';
					script.src = 'https://g.alicdn.com/de/prismplayer/2.15.6/aliplayer-min.js';
					script.async = true;
					script.onload = () => {
						if (window.Aliplayer) {
							resolve(window.Aliplayer);
						} else {
							reject(new Error('Aliplayer load failed'));
						}
					};
					script.onerror = reject;
					document.head.appendChild(script);
				});
			},

			async initPlayer(url) {
				// #ifdef H5
				try {
					// 检测浏览器类型和平台
					const ua = navigator.userAgent;
					const isIOS = /iPad|iPhone|iPod/.test(ua);
					const isAndroid = /Android/.test(ua);
					const isMobile = isIOS || isAndroid;
					
					// 获取不格式的流地址
					const m3u8Url = url;
					const flvUrl = url.replace('.m3u8', '.flv');
					
					// 如果是移动设备，优先使用原生video标签
					if (isMobile) {
						this.useNativeVideo = true;
						this.videoSrc = m3u8Url; // 移动端优先用HLS格式
						return;
					}

					await this.loadAliPlayer();
					
					if(this.player) {
						this.player.dispose();
						this.player = null;
					}
					
					// PC端播放器配置
					const playerConfig = {
						id: 'player-con',
						source: url,
						width: '100%',
						height: '100%',
						autoplay: true,
						isLive: true,
						rePlay: false,
						playsinline: true,
						preload: true,
						controlBarVisibility: 'hover',
						useH5Prism: true,
						useFlashPrism: false,
						format: 'm3u8',  // 默认使用HLS格式
						hlsOption: {
							enableWorker: true,
							debug: false,
							// HLS配置
							maxBufferLength: 30,
							maxMaxBufferLength: 60,
							manifestLoadingTimeOut: 10000,
							manifestLoadingMaxRetry: 5,
							levelLoadingTimeOut: 10000,
							levelLoadingMaxRetry: 5,
							fragLoadingTimeOut: 10000,
							fragLoadingMaxRetry: 5
						},
						skinLayout: [
							{ name: "bigPlayButton", align: "cc" },
							{ name: "H5Loading", align: "cc" },
							{ name: "errorDisplay", align: "tlabs", x: 0, y: 0 },
							{ name: "controlBar", align: "blabs", x: 0, y: 0,
								children: [
									{ name: "progress", align: "blabs", x: 0, y: 44 },
									{ name: "playButton", align: "tl", x: 15, y: 12 },
									{ name: "timeDisplay", align: "tl", x: 10, y: 7 },
									{ name: "fullScreenButton", align: "tr", x: 10, y: 12 },
									{ name: "volume", align: "tr", x: 5, y: 10 }
								]
							}
						]
					};

					// 创建播放器实例
					this.player = new Aliplayer(playerConfig);

					// 添加错误处理
					this.player.on('error', (e) => {
						console.error('播放器错误:', e);
						
						// 尝试切换到FLV格式
						if (e.paramData && e.paramData.error_code === 4) {
							console.log('尝试切换到FLV格式');
							this.player.loadByUrl(flvUrl, 'flv');
							return;
						}
						
						// 如果还是失，降级到原video
						this.useNativeVideo = true;
						this.videoSrc = m3u8Url;
					});

					// 添加播放器事件监听
					this.player.on('ready', () => {
						console.log('播放器就绪');
					});

					this.player.on('waiting', () => {
						this.showVideoLoading();
					});

					this.player.on('playing', () => {
						this.hideVideoLoading();
					});

				} catch (error) {
					console.error('播放器初始化失败:', error);
					// 降级使用原生video标签
					this.useNativeVideo = true;
					this.videoSrc = url;
				}
				// #endif
			},

			async initdata() {
				let that = this;
				// 如果没有 room_id，尝试从本地存储获取
				if (!that.room_id) {
					let savedId;
					try {
						savedId = uni.getStorageSync('last_live_room_id');
					} catch (e) {
						console.error('获取直播间ID失败:', e);
					}
					
					if (savedId) {
						that.room_id = savedId;
					} else {
						uni.showToast({
							title: '直播间不存在',
							icon: 'none'
						});
						return;
					}
				}

				app.get('ApiLiveWeb/getLiveInfo', {id: that.room_id}, function (data) {
					if (data.status == 1) {
						that.data = data.data;
						
						// 添加调试日志
						console.log('初始化数据:', {
							online_num: data.data.online_num,
							online_count: data.data.online_count,
							raw_data: data.data
						});
						
						// 初始化在线人数
						if(data.data.online_num) {
							that.onlineCount = data.data.online_num;
							if(that.$refs.liveHeader) {
								console.log('更新header组件在线人数:', data.data.online_num);
								that.$refs.liveHeader.onlineCount = data.data.online_num;
							}
						}
						
						// 处理主播信息和开播状态
						that.anchorInfo = data.data.anchor || null;
						that.isLiveOnline = data.data.online === 1;
						
						console.log('直信息：', {
							stream_url: that.data.stream_url,
							aid: that.data.aid,
							status: that.data.status,
							anchor: that.anchorInfo,
							isLiveOnline: that.isLiveOnline
						});
						
						if(that.data.stream_url && that.isLiveOnline) {
							// 检查流地址格式
							const streamUrl = that.data.stream_url;
							// 确是HLS格式
							const finalUrl = streamUrl.includes('.m3u8') ? 
								streamUrl : 
								`${streamUrl}${streamUrl.includes('?') ? '&' : '?'}format=m3u8`;
							
							// #ifdef H5
							that.initPlayer(finalUrl);
							// #endif
							
							// #ifndef H5
							that.videoSrc = finalUrl;
							// #endif
						} else {
							that.videoSrc = '@/static/live/video/1.mp4';
							uni.showToast({
								title: that.isLiveOnline ? '未获取到直播流地址' : '主播暂未开',
								icon: 'none'
							});
						}
						
						that.matchedData = data.data.product;
						
						// 设置分享信息
						that.share.title = data.data.share_title || data.data.title || '直播间';
						that.share.imageUrl = data.data.share_img || data.data.cover_img;
						that.share.desc = data.data.share_content || data.data.desc || '快来看直播啦';
						that.share.content = data.data.share_content || '精彩直播进行中';
						that.share.path = `/live/live?id=${that.room_id}`;
					}
				});
			},
			
			sendGift(data) {
				this.$refs.liveGift.sendGift(data.index);
			},
			
			
			welcomeUser() {
				// 如果弹窗已经打开，则关闭
				if (this.$refs.shopShow.isOpen) {
					this.closeShopPopup();
				} else {
					// 否则打开弹窗
					this.openShopPopup();
				}
			},
			
			
			handleVideoError(e) {
				console.error('视频播放错误详情：', {
					error: e,
					currentSrc: this.videoSrc,
					retryCount: this.retryCount
				});
				
				this.hideVideoLoading();
				
				// 检查是否期链接
				if (this.videoSrc.includes('auth_key') && this.retryCount < this.maxRetries) {
					this.retryCount++;
					console.log(`尝试重新获取直播， ${this.retryCount} 重试`);
					
					// 重新获取直播信息以获取新的流地址
					setTimeout(() => {
						this.initdata();
					}, 2000);
				} else {
					// 切换默认视频
					this.videoSrc = '/static/live/video/1.mp4';
					uni.showToast({
						title: '直播暂时无法观看，已切换到默视频',
						icon: 'none',
						duration: 3000
					});
				}
			},
			
			showVideoLoading() {
				if (!this.isLoading) {
					this.isLoading = true;
					uni.showLoading({
						title: '加载中...',
						mask: false
					});
				}
			},
			
			hideVideoLoading() {
				if (this.isLoading) {
					this.isLoading = false;
					uni.hideLoading();
				}
			},
			
			handleVideoLoaded() {
				console.log('视频加载成功');
				this.hideVideoLoading();
				this.retryCount = 0;
			},
			
			handleVideoWaiting() {
				if (!this.videoSrc) return;
				this.showVideoLoading();
			},
			
			handleTimeUpdate() {
				this.hideVideoLoading();
			},
			
			onUnload() {
				this.hideVideoLoading();
				if (this.refreshTimer) {
					clearInterval(this.refreshTimer);
				}
				// #ifdef H5
				if(this.player) {
					this.player.dispose();
					this.player = null;
				}
				// #endif
				
				// 移除监听器
				// #ifdef H5
				document.removeEventListener('visibilitychange', this.handleVisibilityChange);
				// #endif
			},
			
			getProductPrice(item) {
				if (item.guige && item.guige.length > 0) {
					const defaultSpec = item.guige.find(spec => spec.name === '默认规格') || item.guige[0];
					if (defaultSpec) {
						return defaultSpec.sell_price || defaultSpec.market_price || '0.00';
					}
				}
				if (item.sell_price) {
					return item.sell_price;
				}
				return '0.00';
			},
			
			gotoProduct(id) {
				if(!id) return;
				uni.navigateTo({
					url: `/shopPackage/shop/product?id=${id}`
				});
			},
			
			
			// 新增：定期刷新流地址
			startStreamRefresh() {
				// 每5分钟刷新一次直播流地址
				this.refreshTimer = setInterval(() => {
					if (this.videoSrc && this.videoSrc.includes('auth_key')) {
						console.log('定时刷新直播流地址');
						this.initdata();
					}
				}, 5 * 60 * 1000); // 5分钟
			},
			
			sendLiveInit() {
				app.sendSocketMessage({
					type: 'live_web_init',
					data: {
						aid: app.globalData.aid,
						mid: app.globalData.mid,
						room: this.room_id
					}
				});
			},
			
			// 打开商品弹窗
			openShopPopup() {
				// #ifdef H5
				document.body.classList.add('popup-open');
				// #endif
				this.$refs.shopShow.open('bottom');
			},
			
			// 关闭商品弹窗
			closeShopPopup() {
				// #ifdef H5
				document.body.classList.remove('popup-open');
				// #endif
				this.$refs.shopShow.close();
			},
			
			onPopupChange(e) {
				// 新弹窗状态
				this.$refs.shopShow.isOpen = e.show;
				
				// 处理弹窗关闭时的清理工作
				if (!e.show) {
					// #ifdef H5
					document.body.classList.remove('popup-open');
					// #endif
				}
			},
			
			handleClear(isCleared) {
				if (isCleared) {
					// 清空消息
					this.$refs.liveMsg.clearMessages();
					this.hideCoverStatus = true;
				} else {
					// 恢复消息
					this.$refs.liveMsg.restoreMessages();
					this.hideCoverStatus = false;
				}
			},
			
			// 加载更多数据
			loadMore() {
				if (this.loadStatus === 'loading' || this.loadStatus === 'noMore') return;
				
				this.loadStatus = 'loading';
				this.pageNum++;
				
				app.get('ApiLiveWeb/getProducts', {
					page: this.pageNum,
					pageSize: this.pageSize,
					room_id: this.room_id
				}, (res) => {
					if (res.status === 1) {
						if (res.data.length > 0) {
							this.matchedData = [...this.matchedData, ...res.data];
							this.loadStatus = 'more';
						} else {
							this.loadStatus = 'noMore';
						}
					} else {
						this.loadStatus = 'more';
						this.$refs.popmsg.show(res.msg || '加载失败');
					}
				});
			},
			
			// 显示消息提示
			showMsg(msg) {
				this.$refs.popmsg.show(msg);
			},
			
		
			// 分享功能
			onShareAppMessage() {
				return {
					title: this.data.title || '直播间',
					path: `/live/live?id=${this.room_id}`,
					imageUrl: this.data.cover_img
				}
			},
			
			// 分享到朋友圈
			onShareTimeline() {
				return {
					title: this.data.title || '直播间',
					path: `/live/live?id=${this.room_id}`,
					imageUrl: this.data.cover_img
				}
			},
			
			// 分享朋友
			onShareAppMessage() {
				return {
					title: this.data.title || '直播间',
					path: `/live/live?id=${this.room_id}`,
					imageUrl: this.data.cover_img,
					success(res) {
						uni.showToast({
							title: '分享成功'
						});
					},
					fail(res) {
						uni.showToast({
							title: '分享失败',
							icon: 'none'
						});
					}
				}
			},
			
			// H5 分享
			// #ifdef H5
			h5Share() {
				this.$refs.sharePopup.open();
			},
			
			shareToWechat() {
				uni.showToast({
					title: '请在微信内打开分享',
					icon: 'none'
				});
				this.$refs.sharePopup.close();
			},
			
			shareToTimeline() {
				uni.showToast({
					title: '请在微信内打开分享',
					icon: 'none'
				});
				this.$refs.sharePopup.close();
			},
			
			copyLink() {
				const shareUrl = window.location.href;
				uni.setClipboardData({
					data: shareUrl,
					success: () => {
							uni.showToast({
								title: '链接已复制，请粘贴发送给好友',
								icon: 'none',
								duration: 2000
							});
							this.$refs.sharePopup.close();
						}
					});
			},
			
			closeShare() {
				this.$refs.sharePopup.close();
			},
			// #endif
			
			// 处理头部分享按钮点击
			handleHeaderShare() {
				var that = this;
				// #ifdef APP-PLUS
				uni.showActionSheet({
					itemList: ['发送给微信好友', '分享到微信朋友圈'],
					success: function(res) {
						if (res.tapIndex >= 0) {
							var scene = 'WXSceneSession';
							if (res.tapIndex == 1) {
								scene = 'WXSenceTimeline';
							}
							
							var sharedata = {
								provider: 'weixin',
								type: 0,
								scene: scene,
								title: that.share.title || '直播间',
								summary: that.share.desc || '精彩直播进行中',
								href: app.globalData.pre_url + '/h5/' + app.globalData.aid + 
									'.html#/live/live?scene=id_' + that.room_id + 
									'-pid_' + app.globalData.mid,
								imageUrl: that.share.imageUrl || that.data.cover_img
							};
							
							// 检查是否有全局分享配置
							var sharelist = app.globalData.initdata.sharelist;
							if (sharelist) {
								for (var i = 0; i < sharelist.length; i++) {
									if (sharelist[i]['indexurl'] == '/live/live') {
										sharedata.title = sharelist[i].title;
										sharedata.summary = sharelist[i].desc;
										sharedata.imageUrl = sharelist[i].pic;
										if (sharelist[i].url) {
											var sharelink = sharelist[i].url;
											if (sharelink.indexOf('/') === 0) {
												sharelink = app.globalData.pre_url + '/h5/' + 
													app.globalData.aid + '.html#' + sharelink;
											}
											if (app.globalData.mid > 0) {
												sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') +
													'pid=' + app.globalData.mid;
											}
											sharedata.href = sharelink;
										}
									}
								}
							}
							
							uni.share(sharedata);
						}
					}
				});
				// #endif
				
				// #ifdef H5
				this.h5Share();
				// #endif
				
				// #ifdef MP-WEIXIN
				uni.showShareMenu({
					withShareTicket: true,
					menus: ['shareAppMessage', 'shareTimeline']
				});
				// #endif
			},
			
			shareClick() {
				this.sharetypevisible = true;
			},

			handleClickMask() {
				this.sharetypevisible = false;
			},

			// 生成分享海报
			showPoster() {
				var that = this;
				that.showposter = true;
				that.sharetypevisible = false;
				app.showLoading('生成海报中');
				app.post('ApiLiveWeb/getposter', {id: that.room_id}, function (data) {
					app.showLoading(false);
					if (data.status == 0) {
						app.alert(data.msg);
					} else {
						that.posterpic = data.poster;
					}
				});
			},

			posterDialogClose() {
				this.showposter = false;
			},

			// 分享到微信
			sharemp() {
				app.error('点击右上角发送给好友或分享到朋友圈');
				this.sharetypevisible = false;
			},

			// APP分享
			shareapp() {
				var that = this;
				that.sharetypevisible = false;
				uni.showActionSheet({
					itemList: ['发送给微信好友', '分享到微信朋友圈'],
					success: function(res) {
						if(res.tapIndex >= 0) {
							var scene = 'WXSceneSession';
							if (res.tapIndex == 1) {
								scene = 'WXSenceTimeline';
							}
							
							var sharedata = {
								provider: 'weixin',
								type: 0,
								scene: scene,
								title: that.share.title || '直播间',
								summary: that.share.desc || '精彩直播进行中',
								href: app.globalData.pre_url + '/h5/' + app.globalData.aid + 
									'.html#/live/live?scene=id_' + that.room_id + 
									'-pid_' + app.globalData.mid,
								imageUrl: that.share.imageUrl || that.data.cover_img
							};
							
							// 检查是否有全局分享配置
							var sharelist = app.globalData.initdata.sharelist;
							if (sharelist) {
								for (var i = 0; i < sharelist.length; i++) {
									if (sharelist[i]['indexurl'] == '/live/live') {
										sharedata.title = sharelist[i].title;
										sharedata.summary = sharelist[i].desc;
										sharedata.imageUrl = sharelist[i].pic;
										if (sharelist[i].url) {
											var sharelink = sharelist[i].url;
											if (sharelink.indexOf('/') === 0) {
												sharelink = app.globalData.pre_url + '/h5/' + 
													app.globalData.aid + '.html#' + sharelink;
											}
											if (app.globalData.mid > 0) {
												sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') +
													'pid=' + app.globalData.mid;
											}
											sharedata.href = sharelink;
										}
									}
								}
							}
							
							uni.share(sharedata);
						}
					}
				});
			},

			showPosterDialog() {
				var that = this;
				app.showLoading('生成海报中');
				app.post('ApiLiveWeb/getposter', {id: that.room_id}, function (data) {
					app.showLoading(false);
					if (data.status == 1) {
						that.posterpic = data.poster;
						that.showPosterModal = true;
					} else {
						app.alert(data.msg || '生成海报失败');
					}
				});
			},

			closePosterModal() {
				this.showPosterModal = false;
			},

			savePhoto() {
				// 保存海报到相册
				// #ifdef H5
				const link = document.createElement('a');
				link.href = this.posterpic;
				link.download = `live_poster_${this.room_id}.jpg`;
				link.click();
				// #endif

				// #ifndef H5
				uni.saveImageToPhotosAlbum({
					filePath: this.posterpic,
					success: () => {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('保存失败', err);
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						});
					}
				});
				// #endif
			},

			// 初始化观看时长计时器
			initWatchTimer() {
				const that = this;
				
				// 获取奖励规则
				app.get('ApiLiveWeb/getRewardRules', { live_id: this.room_id }, function(res) {
					if (res.code === 1) {
						that.rewardRules = res.data;
					}
				});

				// 重置计时器
				this.watchTimer = {
					startTime: new Date(),
					totalSeconds: 0,
					timer: null,
					lastUpdateTime: new Date()
				};

				// 启动计时器
				this.watchTimer.timer = setInterval(() => {
					this.watchTimer.totalSeconds++;
					
					// 每30秒更新一次服务器时间
					if (this.watchTimer.totalSeconds % 30 === 0) {
						this.updateWatchTime();
					}
				}, 1000);
			},

			// 更新观看时长到服务器
			updateWatchTime() {
				const that = this;
				const now = new Date();
				const watchTime = Math.floor((now - this.watchTimer.lastUpdateTime) / 1000);
				this.watchTimer.lastUpdateTime = now;

				app.post('ApiLiveWeb/updateWatchTime', {
					live_id: this.room_id,
					watch_time: watchTime
				}, function(res) {
					if (res.code === 1 && res.score > 0) {
						// 显示奖励提示
						that.showWatchRewardToast(res.score);
					}
				});
			},

			// 显示观看奖励提示
			showWatchRewardToast(score) {
				this.watchRewardToast = {
					show: true,
					score: score
				};

				// 3秒后自动隐藏
				setTimeout(() => {
					this.watchRewardToast.show = false;
				}, 3000);
			},
		},
		mounted() {
			this.sendLiveInit();
			// 初始化弹窗状态
			this.$nextTick(() => {
				this.$refs.shopShow.isOpen = false;
			});
		},
		
		// 添加页面显示时重新发送初始化
		onShow() {
			this.sendLiveInit();
			this.initWatchTimer();
			
			// 检查登录状态和参数
			const pages = getCurrentPages();
			if (pages.length > 1) {
				const prevPage = pages[pages.length - 2];
				if (prevPage && prevPage.route.includes('login')) {
					this.onLoginSuccess();
				}
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.pageNum = 1;
			this.loadStatus = 'more';
			this.initdata(() => {
				uni.stopPullDownRefresh();
			});
		},
	}
</script>

<style scoped lang="scss">
	.live {
		position: relative;
		height: 100vh;
		overflow: hidden;
	}
	
	/* H5环境下的播放器容器样式 */
	/* #ifdef H5 */
	.prism-player {
		width: 100%;
		height: 100vh;
		background: #000;
	}
	/* #endif */
	
	.video {
		width: 100%;
		height: 100vh;
		object-fit: contain;
		background: #000;
	}
	
	.content {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
		display: flex;
		flex-direction: column;
		transition: left 0.3s ease;
		padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
		padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
		
		/* #ifdef H5 */
		background: linear-gradient(180deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0) 100%);
		pointer-events: none; /* 允许点击穿透到视频 */
		
		/* 所有直接子元素恢复点击事件 */
		> * {
			pointer-events: auto;
		}
		
		.live-msg {
			position: absolute;
			bottom: 140rpx;
			left: 0;
			right: 0;
			height: 40vh;
			z-index: 99;
			pointer-events: auto;
		}

		.live-header {
			position: relative;
			z-index: 1001;
		}

		.live-footer {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 1001;
		}
		/* #endif */
	}
	
	.hide {
		left: 100vw;
	}
	

	.viewShop {
		position: relative;  // 添加相对定位
		z-index: 999999 !important;  // 提高z-index
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
		
		.shop-title {
			position: relative;
			height: 90rpx;
			line-height: 90rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: 500;
			border-bottom: 1rpx solid #f5f5f5;
			flex-shrink: 0;
			background: #fff;
			z-index: 999999;
			
			.close-btn {
				position: absolute;
				right: 20rpx;
				top: 50%;
				transform: translateY(-50%);
				padding: 10rpx;
			}
		}
		
		.scroll-view {
			flex: 1;
			height: calc(80vh - 90rpx);
			position: relative;
			z-index: 999999;
		}
		
		.cart-item {
			display: flex;
			padding: 20rpx;
			border-bottom: 1rpx solid #f5f5f5;
			background: #fff;
			position: relative;
			z-index: 999999;
			
			.image-box {
				width: 160rpx;
				height: 160rpx;
				margin-right: 20rpx;
				border-radius: 12rpx;
				
				.item-image {
					width: 100%;
					height: 100%;
				}
			}
			
			.item-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				min-width: 0; // 防止文本溢出
				
				.item-name {
					font-size: 28rpx;
					color: #333;
					line-height: 1.4;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}
				
				.price-action {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 10rpx;
					
					.item-price {
						color: #ff4757;
						
						.symbol {
							font-size: 24rpx;
						}
						
						.number {
							font-size: 34rpx;
							font-weight: bold;
							margin-left: 4rpx;
						}
					}
					
					.buy-btn {
						background: linear-gradient(to right, #ff4757, #ff6b81);
						height: 60rpx;
						padding: 0 30rpx;
						border-radius: 30rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						
						text {
							color: #fff;
							font-size: 26rpx;
						}
					}
				}
			}
		}
	}
	
	.item-selector {
		margin-right: 10px;
	}
	
	.item-image {
		height: 70%;
	}
	
	.item-info {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 130rpx;
		width: 67%;
	}
	
	.item-name {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}
	
	.item-price {
		font-size: 14px;
		color: #e65602;
		
		span {
			&:first-child {
				font-size: 24rpx;
			}
			&:last-child {
				font-weight: bold;
			}
		}
	}
	
	.item-quantity {
		display: flex;
		align-items: center;
		margin-left: auto;
	}
	
	.item-quantity button {
		width: 30px;
		height: 30px;
		border: 1px solid #ddd;
		background-color: #fff;
		cursor: pointer;
	}
	
	.item-quantity text {
		margin: 0 5px;
	}
	
	.shopButton {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #a0a0a0;
		background-color: #ddd;
		font-size: 20rpx;
	}
	
	.shopButtonActive {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #fff;
		background-color: #eb8200;
		font-size: 20rpx;
	}
	
	.buy-btn {
		background-color: #14c8ff;
		border-radius: 30rpx;
		padding: 6rpx 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		text {
			color: #ffffff;
			font-size: 24rpx;
			font-weight: 500;
		}
		
		&:active {
			opacity: 0.8;
		}
	}
	
	.cart-item {
		&:active {
			background-color: #f8f8f8;
		}
	}
	
	// #ifdef MP-WEIXIN
	.content {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
		display: flex;
		flex-direction: column;
	}
	
	.nav-wrapper {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1001;
		background: linear-gradient(to bottom, rgba(0,0,0,0.5), rgba(0,0,0,0));
		padding-top: env(safe-area-inset-top); /* 适配刘海屏 */
		height: calc(180rpx + env(safe-area-inset-top)); /* 增加高度以匹配header */
		display: flex;
		align-items: flex-end; /* 将内容对到底部 */
	}
	
	.msg-wrapper {
		flex: 1;
		margin-top: calc(180rpx + env(safe-area-inset-top)); /* 与nav-wrapper高度保一致 */
		margin-bottom: calc(120rpx + env(safe-area-inset-bottom));
		padding: 20rpx;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
		pointer-events: auto;
	}
	
	.footer-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.0 */
		height: calc(120rpx + env(safe-area-inset-bottom)); /* iOS 11.2+ */
		background-color: rgba(0, 0, 0, 0.7);
		z-index: 1001;
		pointer-events: auto;
		
		/* 内部内容容 */
		.footer-content {
			height: 120rpx;
			width: 100%;
			position: absolute;
			top: 0;
		}
	}
	// #endif
	
	:deep(.uni-popup) {
		z-index: 999999 !important;
	}
	
	:deep(.uni-popup__wrapper) {
		z-index: 999999 !important;
	}
	
	:deep(.uni-popup__mask) {
		z-index: 999998 !important;
		background-color: rgba(0, 0, 0, 0.6) !important;  // 增加罩明
	}
	
	/* 确保消容在正的位置 */
	/* #ifdef H5 */
	:deep(.message) {
		position: relative !important;
		height: 100% !important;
		width: 100% !important;
		left: 0 !important;
		right: 0 !important;
		bottom: 0 !important;
		
		.msg-scroll {
			background: transparent;
			border-radius: 12rpx;
		}
		
		.msg-item {
			background: rgba(0,0,0,0.6) !important;
		}
	}
	/* #endif */
</style>

<style lang="scss">
/* #ifdef MP-WEIXIN */
.uni-popup {
  z-index: 999999 !important;
}

.uni-popup .uni-popup__wrapper {
  z-index: 999999 !important;
}

.uni-popup .uni-popup__mask {
  z-index: 999998 !important;
}

.popup-content {
  position: relative !important;
  z-index: 100002 !important;
  height: auto;
  max-height: 80vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  transform: translateZ(2px);
}

body.popup-open {
  overflow: hidden !important;
  position: fixed;
  width: 100%;
}
/* #endif */

.viewShop {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
		
  .shop-title {
    position: relative;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    border-bottom: 1rpx solid #f5f5f5;
    flex-shrink: 0;
    background: #fff;
    z-index: 999999;
    
    .close-btn {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      padding: 10rpx;
    }
  }
  
  .scroll-view {
    flex: 1;
    height: calc(80vh - 90rpx);
    position: relative;
    z-index: 999999;
  }
  
  .cart-item {
    display: flex;
    padding: 20rpx;
    border-bottom: 1rpx solid #f5f5f5;
    background: #fff;
    position: relative;
    z-index: 999999;
    
    .image-box {
      width: 160rpx;
      height: 160rpx;
      margin-right: 20rpx;
      border-radius: 12rpx;
      overflow: hidden;
      flex-shrink: 0;
      
      .item-image {
        width: 100%;
        height: 100%;
      }
    }
    
    .item-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-width: 0;
      
      .item-name {
        font-size: 28rpx;
        color: #333;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .price-action {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10rpx;
        
        .item-price {
          color: #ff4757;
          
          .symbol {
            font-size: 24rpx;
          }
          
          .number {
            font-size: 34rpx;
            font-weight: bold;
            margin-left: 4rpx;
          }
        }
        
        .buy-btn {
          background: linear-gradient(to right, #ff4757, #ff6b81);
          height: 60rpx;
          padding: 0 30rpx;
          border-radius: 30rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          
          text {
            color: #fff;
            font-size: 26rpx;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
/* 全局样式调整 */
/* #ifdef H5 */
.uni-popup {
  &.popup-bottom {
    .uni-popup__wrapper {
      position: fixed !important;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 100000 !important;
      
      .uni-popup__wrapper {
        position: fixed !important;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100001 !important;
        transform: translateZ(1000px);
      }
      
      .uni-popup__mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.6) !important;
        z-index: 100000 !important;
      }
    }
  }
}

.popup-content {
  position: relative !important;
  z-index: 100001 !important;
  transform: translateZ(1000px);
}

body.popup-open {
  overflow: hidden;
}
/* #endif */

// ... 其他样式保持不变 ...
</style>

<style scoped lang="scss">
/* #ifdef H5 */
.uni-popup {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000 !important;  // 确保 z-index 足够高
  
  .uni-popup__wrapper {
    position: fixed !important;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10001 !important;  // 确保 z-index 足够高
    transform: translateZ(1000px);
  }
  
  .uni-popup__mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6) !important;
    z-index: 10000 !important;  // 确保 z-index 足够高
  }
}

.viewShop {
  position: relative;
  z-index: 10002 !important;  // 确保 z-index 足够高
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  
  .shop-title {
    position: sticky;
    top: 0;
    z-index: 10003 !important;  // 确保 z-index 足够高
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
  }
  
  .scroll-view {
    flex: 1;
    height: calc(80vh - 90rpx);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    position: relative;
    z-index: 10002 !important;  // 确保 z-index 足够高
  }
  
  .cart-item {
    position: relative;
    z-index: 10002 !important;  // 确保 z-index 足够高
    background: #fff;
    
    .image-box {
      overflow: hidden;
      flex-shrink: 0;
    }
  }
}
/* #endif */
</style>

<style lang="scss">
/* 添加分享弹窗样式 */
/* #ifdef H5 */
.share-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  
  .share-title {
    text-align: center;
    font-size: 32rpx;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .share-options {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0;
    
    .share-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      image {
        width: 100rpx;
        height: 100rpx;
        margin-bottom: 16rpx;
      }
      
      text {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
  
  .share-cancel {
    margin-top: 30rpx;
    text-align: center;
    padding: 20rpx 0;
    font-size: 32rpx;
    color: #333;
    border-top: 1px solid #eee;
    
    &:active {
      background-color: #f5f5f5;
    }
  }
}
/* #endif */
</style>

<style>
.popup__container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: rgba(0, 0, 0, 0.6);
}

.popup__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.popup__modal {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 20rpx;
}

.sharetypecontent {
  height: 250rpx;
  width: 710rpx;
  margin: 20rpx;
  display: flex;
  padding: 50rpx;
  align-items: flex-end;
}

.sharetypecontent .f1 {
  color: #51c332;
  width: 50%;
  height: 150rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  font-size: 28rpx;
  border: 0;
}

.sharetypecontent button::after {
  border: 0;
}

.sharetypecontent .f2 {
  color: #51c332;
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sharetypecontent .img {
  width: 90rpx;
  height: 90rpx;
}

.sharetypecontent .t1 {
  height: 60rpx;
  line-height: 60rpx;
  color: #666;
}

.posterDialog {
  position: fixed;
  z-index: 9;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.8);
  top: var(--window-top);
  left: 0;
}

.posterDialog .main {
  width: 80%;
  margin: 60rpx 10% 30rpx 10%;
  background: #fff;
  position: relative;
  border-radius: 20rpx;
}

.posterDialog .close {
  position: absolute;
  padding: 20rpx;
  top: 0;
  right: 0;
}

.posterDialog .close .img {
  width: 32rpx;
  height: 32rpx;
}

.posterDialog .content {
  width: 100%;
  padding: 70rpx 20rpx 30rpx 20rpx;
  color: #333;
  font-size: 30rpx;
  text-align: center;
}

.posterDialog .content .img {
  width: 540rpx;
  height: 960rpx;
}

.pp4 {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  color: #fff;
  font-size: 28rpx;
}
</style>

<style lang="scss">
/* 海报浮动按钮样式 */
.poster-float-btn {
    position: fixed;
    bottom: 120rpx;
    right: 30rpx;
    width: 100rpx;
    height: 100rpx;
    background: linear-gradient(135deg, #ff6b81, #ff4757);
    border-radius: 50%;
    box-shadow: 0 4px 10px rgba(255, 71, 87, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: transform 0.2s ease;
    
    image {
        width: 60rpx;
        height: 60rpx;
    }
    
    &:active {
        transform: scale(0.95);
    }
}

/* 海报弹窗样式 */
.poster-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    
    .poster-modal-content {
        width: 80%;
        max-width: 600rpx;
        background: white;
        border-radius: 20rpx;
        padding: 30rpx;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        
        .poster-modal-close {
            position: absolute;
            top: 20rpx;
            right: 20rpx;
            z-index: 10001;
        }
        
        .poster-modal-image {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-bottom: 30rpx;
            
            image {
                max-width: 100%;
                border-radius: 10rpx;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            }
        }
        
        .poster-modal-actions {
            display: flex;
            justify-content: center;
            
            .poster-save-btn {
                background: linear-gradient(to right, #ff4757, #ff6b81);
                color: white;
                border: none;
                padding: 20rpx 60rpx;
                border-radius: 50rpx;
                font-size: 32rpx;
                transition: transform 0.2s ease;
                
                &:active {
                    transform: scale(0.95);
                }
            }
        }
    }
}
</style>

<style lang="scss">
/* 观看奖励提示样式 */
.watch-reward-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 30rpx 60rpx;
    border-radius: 16rpx;
    color: white;
    font-size: 32rpx;
    text-align: center;
    z-index: 10001;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: toast-animation 0.3s ease;
}

@keyframes toast-animation {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.7);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}
</style>

<style lang="scss">
// ... 其他样式
.watch-time-display {
    position: fixed;
    top: 180rpx;
    right: 30rpx;
    padding: 16rpx 24rpx;
    border-radius: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 1000;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    
    .watch-time-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 4rpx;
    }
    
    .watch-time-value {
        font-size: 28rpx;
        color: #ffffff;
        font-weight: 500;
        font-family: monospace;
    }
}
</style>

