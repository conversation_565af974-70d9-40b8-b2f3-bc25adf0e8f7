<template>
<view class="container">
	<block v-if="isload">
		<view class="topfix">
			<view class="toplabel">
				<text class="t1">{{t('分销订单')}}（{{count}}）</text>
				<text class="t2">预计：+{{commissionyj}}元</text>
			</view>
			<dd-tab :itemdata="['所有订单','待付款','已付款','已完成','退款/售后']" :itemst="['0','1','2','3','5']" :st="st" :isfixed="false" @changetab="changetab"></dd-tab>
		</view>
		<view style="margin-top:190rpx"></view>
		<block v-if="datalist && datalist.length>0">
		<view class="content">
			<view v-for="(item, index) in datalist" :key="index" class="item">
				<view class="f1 flex"><text class="flex1">{{item.ordernum}}</text><text v-if="item.dannum && item.dannum > 0" style="color:#a55">第{{item.dannum}}单</text></view>
				<view class="f2">
					<view class="t1">
						<text class="x1">{{item.name}} ×{{item.num}}</text>
						<text class="x2">{{item.createtime}}</text>
						<view class="x3"><image :src="item.headimg"></image>{{item.nickname}}</view>
						<view class="x3" v-if="item.order_info">
							<view class="btn2" @tap="goto" :data-url="'/pagesExt/order/detail?id='+item.order_info.id+'&fromfenxiao=1'">详情</view>
							<view class="btn2" @tap.stop="logistics(item.order_info)" >查看物流</view>
						</view>
					</view>
					<view class="t2">
						<text class="x1">+{{item.commission}}</text>
						<text class="dior-sp6 yfk" v-if="item.status==1 || item.status==2">已付款</text>
						<text class="dior-sp6 dfk" v-if="item.status==0">待付款</text>
						<text class="dior-sp6 ywc" v-if="item.status==3">已完成</text>
						<text class="dior-sp6 ygb" v-if="item.status==4">已关闭</text>
						<text class="dior-sp6 ygb" v-if="item.refund_money > 0">退款/售后</text>
						<view class="contact-btn" @tap="contactCustomer(item)" v-if="item.tel">
							<image src="/static/img/phone.png" class="contact-icon"></image>
							<text>联系客户</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<uni-popup id="dialogSelectExpress" ref="dialogSelectExpress" type="dialog">
			<view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx" v-if="express_content">
				<view class="sendexpress-item" v-for="(item, index) in express_content" :key="index" @tap="goto" :data-url="'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no" style="display: flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 0;">
					<view class="flex1">{{item.express_com}} - {{item.express_no}}</view>
					<image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx"/>
				</view>
			</view>
		</uni-popup>
		</block>
	</block>
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      st: 0,
			count:0,
      commissionyj: 0,
      pagenum: 1,
      datalist: [],
			express_content:'',
      nodata: false,
      nomore: false
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.pagenum = 1;
		this.datalist = [];
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata();
    }
  },
  methods: {
		getdata: function () {
			var that = this;
      var pagenum = that.pagenum;
      var st = that.st;
			that.loading = true;
			that.nodata = false;
      that.nomore = false;
			app.get('ApiAgent/agorder',{st:st,pagenum: pagenum},function(res){
				that.loading = false;
				var data = res.datalist;
        if (pagenum == 1) {
					that.commissionyj = res.commissionyj;
					that.count = res.count;
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					uni.setNavigationBarTitle({
						title: that.t('分销订单')
					});
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
			});
		},
    changetab: function (st) {
      this.pagenum = 1;
      this.st = st;
      this.datalist = [];
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    logistics:function(e){
	     console.log(e)
      var express_com = e.express_com
      var express_no = e.express_no
      var express_content = e.express_content
      var express_type = e.express_type
      console.log(express_content)
      if(!express_content){
        app.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);
      }else{
        this.express_content = JSON.parse(express_content);
        console.log(express_content);
        this.$refs.dialogSelectExpress.open();
      }
    },
    contactCustomer(item) {
        if(!item.tel) {
            this.$refs.popmsg.show({
                msg: '暂无联系方式'
            });
            return;
        }
        uni.makePhoneCall({
            phoneNumber: item.tel,
            fail(err) {
                console.log('拨打电话失败', err);
            }
        });
    }
  }
};
</script>
<style>
.topfix{width: 100%;position:relative;position:fixed;background: #f9f9f9;top:var(--window-top);z-index:11;}
.toplabel{width: 100%;background: #f9f9f9;padding: 20rpx 20rpx;border-bottom: 1px #e3e3e3 solid;display:flex;}
.toplabel .t1{color: #666;font-size:30rpx;flex:1}
.toplabel .t2{color: #666;font-size:30rpx;text-align:right}


.content{ width:100%;}
.content .item{width:94%;margin-left:3%;border-radius:10rpx;background: #fff;margin-bottom:16rpx;}
.content .item .f1{width:100%;padding: 16rpx 20rpx;color: #666;border-bottom: 1px #f5f5f5 solid;}
.content .item .f2{display:flex;padding:20rpx;align-items:center}
.content .item .f2 .t1{display:flex;flex-direction:column;flex:auto}
.content .item .f2 .t1 .x2{ color:#999}
.content .item .f2 .t1 .x3{display:flex;align-items:center}
.content .item .f2 .t1 .x3 image{width:40rpx;height:40rpx;border-radius:50%;margin-right:4px}
.content .item .f2 .t2{ width:360rpx;text-align:right;display:flex;flex-direction:column;}
.content .item .f2 .t2 .x1{color: #000;height:44rpx;line-height: 44rpx;overflow: hidden;font-size:36rpx;}
.content .item .f2 .t2 .x2{height:44rpx;line-height: 44rpx;overflow: hidden;}

.dfk{color: #ff9900;}
.yfk{color: red;}
.ywc{color: #ff6600;}
.ygb{color: #aaaaaa;}
.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}

.contact-btn {
    display: inline-flex;
    align-items: center;
    background: #fff;
    padding: 2rpx 12rpx;
    border-radius: 2rpx;
    border: 1px solid #ddd;
    height: 40rpx;
    min-width: 120rpx;
    justify-content: center;
}

.contact-icon {
    width: 20rpx;
    height: 20rpx;
    margin-right: 4rpx;
}

.contact-btn text {
    color: #666;
    font-size: 22rpx;
    line-height: 1;
}

</style>