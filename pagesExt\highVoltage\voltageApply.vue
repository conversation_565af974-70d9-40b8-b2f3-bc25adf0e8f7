<template>
	<view class="content">
		<view class="header">
			<view class="top">
				<view class="title-box">
					<view class="title">高压办电</view>
					<view>帮代办、少跑腿</view>
				</view>
				<image src="@/static/highVoltage/icon1.png" class="logo"></image>
			</view>
			<valtageTab></valtageTab>
		</view>
		<view class="form" style="margin-top: 128rpx">
			<view class="form-item" v-for="(item, index) in formList" :key="index">
				<view class="title">
					{{ item.val1 }}
					<text style="color: red; margin-left: 5rpx" v-if="item.val3">*</text>
				</view>
				<input class="input" placeholder="请输入" v-model="formdata['form' + index]" />
			</view>
		</view>
		<view class="btn" @click="commitApply">提交申请</view>
		<view class="btn back-i" @click="back">返回</view>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			// 订单申请表单内容
			formList: [],
			// 订单申请表单
			formdata: {},
			// 订单详情数据
			detailInfo: {}
		};
	},
	onLoad() {
		this.getFormList();
	},
	methods: {
		back() {
			/**
			 * @method 返回上一页
			 */
			uni.navigateBack();
		},
		getFormList() {
			/**
			 * @method 获取表单字段
			 */
			const _this = this;
			app.post('ApiShenqingbandian/buy', {}, ({ allbuydata }) => {
				_this.formList = allbuydata[0]['formdata'];
			});
		},
		verify() {
			/**
			 * @method 表单校验
			 */
			for (var i = 0; i < this.formList.length; i++) {
				if (Number(this.formList[i].val3) === 1 && !this.formdata['form' + i]) {
					uni.showToast({
						title: '请输入' + this.formList[i].val1,
						icon: 'none'
					});
					return false;
				}
			}
			return true;
		},
		commitApply() {
			/**
			 * @method 提交申请
			 */
			const _this = this;
			if (this.verify())
				app.post(
					'ApiShenqingbandian/createOrder',
					{
						buydata: [{ formdata: this.formdata }]
					},
					({ msg, orderid }) => {
						uni.showToast({
							title: msg,
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateTo({
								url: './voltageApplicationForm?delta=2&id=' + orderid
							});
						}, 500);
					}
				);
		}
	}
};
</script>

<style lang="scss">
@import 'styled.scss';
</style>
