<template>
    <view>
        <block v-if="isdiy">
            <view :style="'display:flex;min-height: 100vh;flex-direction: column;background-color:' + pageinfo.bgcolor">
                <view class="container">
                    <dp :pagecontent="pagecontent" :menuindex="menuindex"></dp>
                </view>
            </view>
            <dp-guanggao :guanggaopic="guanggaopic" :guanggaourl="guanggaourl"></dp-guanggao>
        </block>
        <block v-else>
            <view class="container nodiydata" v-if="isload">
                <swiper v-if="pics.length>0" class="swiper" :indicator-dots="pics[1]?true:false" :autoplay="true" :interval="5000" indicator-color="#dcdcdc" indicator-active-color="#fff">
                    <block v-for="(item, index) in pics" :key="index">
                        <swiper-item class="swiper-item">
                            <image :src="item" mode="widthFix" class="image"/>
                        </swiper-item>
                    </block>
                </swiper>
                <view class="topcontent">
                    <view class="logo"><image class="img" :src="business.logo"/></view>
                    <view class="title">{{business.name}}</view>
                    <view class="desc">
                        <view class="f1">
                            <image class="img" v-for="(item2,index2) in [0,1,2,3,4]" :key="index2"  :src="'/static/img/star' + (business.comment_score>item2?'2':'') + '.png'"/>
                            <text class="txt">{{business.comment_score}}</text>
                        </view>
                        <view class="f2">销量 {{business.sales}}</view>
                    </view>
                    <view v-if="bset && bset.show_link" class="tel" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}">
                        <view @tap="phone" :data-phone="business.tel" class="tel_online"><image class="img" src="/static/img/tel.png"/>
                            {{bset && bset.show_linktext?bset.show_linktext:'联系商家'}}
                        </view>
                        
                    </view>
                    
                    <view class="address" @tap="openLocation" :data-latitude="business.latitude" :data-longitude="business.longitude" :data-company="business.name" :data-address="business.address">
                        <image class="f1" src="/static/img/shop_addr.png"/>
                        <view class="f2">{{business.address}}</view>  
                        <image class="f3" src="/static/img/arrowright.png"/>
                    </view>
                </view>
       <view class="container2" v-if="pay_switch == 1">
                      
                         <view class="card">
                           <view class="content">
                             <text class="discount-text">付款买单</text>
                             <text class="sub-text">共享补贴</text>
                           </view>
                           <button class="pay-button"  :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="goto" :data-url="'/pagesExt/maidan/pay?bid=' + business.id">点击买单</button>
                         </view>
                       </view>
                <view class="contentbox">
                    <view class="shop_tab">
                        <view v-if="showfw" :class="'cptab_text ' + (st==-1?'cptab_current':'')" @tap="changetab" data-st="-1">本店服务<view class="after" :style="{background:t('color1')}"></view></view>
                        <view v-if="bset && bset.show_product" :class="'cptab_text ' + (st==0?'cptab_current':'')" @tap="changetab" data-st="0">{{bset && bset.show_producttext?bset.show_producttext:'本店商品'}}<view class="after" :style="{background:t('color1')}"></view></view>
                        <view v-if="bset && bset.show_comment" :class="'cptab_text ' + (st==1?'cptab_current':'')" @tap="changetab" data-st="1">{{bset && bset.show_commenttext?bset.show_commenttext:'店铺评价'}}({{countcomment}})<view class="after" :style="{background:t('color1')}"></view></view>
                        <view v-if="bset && bset.show_detail" :class="'cptab_text ' + (st==2?'cptab_current':'')" @tap="changetab" data-st="2">{{bset && bset.show_detailtext?bset.show_detailtext:'商家详情'}}<view class="after" :style="{background:t('color1')}"></view></view>
                    </view>
    
                    <view class="cp_detail" v-if="st==-1" style="padding-top:20rpx">
                        <view class="classify-ul" v-if="yuyue_clist.length>0">
                            <view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;">
                             <view class="classify-li" :style="yuyue_cid==0?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''" @tap="changeyuyueCTab" :data-id="0">全部</view>
                             <block v-for="(item, idx2) in yuyue_clist" :key="idx2">
                             <view class="classify-li" :style="yuyue_cid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''" @tap="changeyuyueCTab" :data-id="item.id">{{item.name}}</view>
                             </block>
                            </view>
                        </view>
    
                        <dp-yuyue-itemlist :data="datalist" :menuindex="menuindex"></dp-yuyue-itemlist>
                        
                        <nomore v-if="nomore"></nomore>
                        <nodata v-if="nodata"></nodata>
                    </view>
    
                    <view class="cp_detail" v-if="st==0" style="padding-top:20rpx">
                        <dp-product-itemlist :data="datalist" :menuindex="menuindex"></dp-product-itemlist>
                        
                        <nomore v-if="nomore"></nomore>
                        <nodata v-if="nodata"></nodata>
                    </view>
    
                    <view class="cp_detail" v-if="st==1">
                        <view class="comment">
                            <block v-if="datalist.length>0">
                                <view v-for="(item, index) in datalist" :key="index" class="item">
                                    <view class="f1">
                                        <image class="t1" :src="item.headimg"/>
                                        <view class="t2">{{item.nickname}}</view>
                                        <view class="flex1"></view>
                                        <view class="t3"><image class="img" v-for="(item2,index2) in [0,1,2,3,4]" :key="index2"  :src="'/static/img/star' + (item.score>item2?'2':'') + '.png'"/></view>
                                    </view>
                                    <view style="color:#777;font-size:22rpx;">{{item.createtime}}</view>
                                    <view class="f2">
                                        <text class="t1">{{item.content}}</text>
                                        <view class="t2">
                                            <block v-if="item.content_pic!=''">
                                                <block v-for="(itemp, index) in item.content_pic" :key="index">
                                                    <view @tap="previewImage" :data-url="itemp" :data-urls="item.content_pic">
                                                        <image :src="itemp" mode="widthFix"/>
                                                    </view>
                                                </block>
                                            </block>
                                        </view>
                                    </view>
                                    <view class="f3" v-if="item.reply_content">
                                        <view class="arrow"></view>
                                        <view class="t1">商家回复：{{item.reply_content}}</view>
                                    </view>
                                </view>
                            </block>
                            <block v-else>
                                <nodata v-show="nodata"></nodata>
                            </block>
                        </view>
                    </view>
                    <view class="cp_detail" v-if="st==2" style="padding:20rpx">
                        <parse :content="business.content"></parse>
                    </view>
                </view>
                <view v-if="couponcount>0" class="covermy" style="top:65vh" @tap="goto" :data-url="'/pages/coupon/couponlist?bid=' + business.id">
                    <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">商家</text>
                    <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">{{t('优惠券')}}</text>
                </view>
                <view class="covermy" style="top:65vh" @tap="goto" :data-url="'/pages/coupon/couponlist?bid=' + business.id">
                    <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">商家</text>
                    <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">{{t('优惠券')}}</text>
                </view>
                <view class="covermy" style="top:65vh" @tap="goto" :data-url="'/pages/coupon/couponlist?bid=' + business.id">
                    <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">商家</text>
                    <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">{{t('优惠券')}}</text>
                </view>
                <view class="covermy" style="top:75vh;background:rgba(0,0,0,0.7)" @tap="goBack">
                    <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">返回</text>
                    <text style="padding:0 4rpx;height:36rpx;line-height:36rpx">上一页</text>
                </view>
            </view>
        </block>
        <loading v-if="loading"></loading>
        
        <!-- #ifdef MP-TOUTIAO -->
        <view class="dp-cover" v-if="video_status">
            <button open-type="share" data-channel="video" class="dp-cover-cover" :style="{
                zIndex:10,
                top:'60vh',
                left:'80vw',
                width:'110rpx',
                height:'110rpx'
            }">
                <image :src="pre_url+'/static/img/uploadvideo2.png'" :style="{width:'110rpx',height:'110rpx'}"/>
            </button>
        </view>
        <!-- #endif -->
        
        <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
        <popmsg ref="popmsg"></popmsg>
    </view>
    </template>
    
    <script>
    var app = getApp();
    export default {
        data() {
            return {
                opt:{},
                loading:false,
                isload: false,
                menuindex:-1,
                pre_url:app.globalData.pre_url,
    
                isdiy: 0,
    
                st: 0,
                business:[],
                countcomment:0,
                couponcount:0,
                pics:[],
                pagenum: 1,
                datalist: [],
                topbackhide: false,
                nomore: false,
                nodata:false,
    
                title: "",
                sysset: "",
                guanggaopic: "",
                guanggaourl: "",
                pageinfo: "",
                pagecontent: "",
                showfw:false,
                yuyue_clist:[],
                yuyue_cid:0,
                video_status:0,
                video_title:'',
                video_tag:[],
                bset:'',
                pay_switch: 0,
            }
        },
        onLoad: function (opt) {
            this.opt = app.getopts(opt);
            this.opt.bid = this.opt.id;
            this.st = this.opt.st || 0;
            this.getdata();
      },
        onPullDownRefresh: function () {
            this.getdata();
        },
        onReachBottom: function () {
            if (this.isdiy == 0) {
                if (!this.nodata && !this.nomore) {
                    this.pagenum = this.pagenum + 1;
                    this.getDataList(true);
                }
            }
        },
        onPageScroll: function (e) {
            uni.$emit('onPageScroll',e);
        },
        onShareAppMessage:function(){
            //#ifdef MP-TOUTIAO
            console.log(shareOption);
                return {
                    
                    title: this.video_title,
                    channel: "video",
                    extra: {
                            hashtag_list: this.video_tag,
                          },
                    success: () => {
                        console.log("分享成功");
                    },
                     fail: (res) => {
                        console.log(res);
                        // 可根据 res.errCode 处理失败case
                      },
                };
            //#endif
            return this._sharewx({title:this.business.name});
        },
        onPageScroll: function (e) {
            if (this.isdiy == 0) {
                var that = this;
                var scrollY = e.scrollTop;
                if (scrollY > 200 && !that.topbackhide) {
                    that.topbackhide = true;
                }
                if (scrollY < 150 && that.topbackhide) {
                    that.topbackhide = false;
                }
            }
        },
        methods: {
            getdata: function () {
                var that = this;
                var id = that.opt.id || 0;
                that.loading = true;
                app.get('ApiBusiness/index', {id: id}, function (res) {
                    that.loading = false;
                    that.isdiy = res.isdiy;
                    that.business = res.business;
                    that.countcomment = res.countcomment;
                    that.couponcount = res.couponcount;
                    that.pics = res.pics;
                    var bset = res.bset;
                    that.bset = bset;
                    that.pay_switch = res.pay_switch || 0;
                    if(bset){
                        if(bset.show_product){
                            that.st = 0;
                        }else if(bset.show_comment){
                            that.st = 1;
                        }else if(bset.show_detail){
                            that.st = 2;
                        }
                    }
    
                    that.guanggaopic = res.guanggaopic;
                    that.guanggaourl = res.guanggaourl;
                    that.pageinfo = res.pageinfo;
                    that.pagecontent = res.pagecontent;
                    that.sysset = res.sysset;
                    that.showfw = res.showfw || false;
                    if(that.showfw){
                        that.st = -1;
                        that.yuyue_clist = res.yuyue_clist;
                    }
                    if(res.yuyueset){
                        that.video_status = res.yuyueset.video_status;
                        that.video_title = res.yuyueset.video_title;
                        that.video_tag = res.yuyueset.video_tag;
                    }
                    
                    that.loaded({title:that.business.name,pic:that.business.logo});
    
                    if (res.isdiy == 0) {
                        that.isload = 1;
                        uni.setNavigationBarTitle({
                            title: that.business.name
                        });
                        that.getDataList();
                    } else {
                        if (res.status == 2) {
                            //付费查看
                            app.goto('/pages/pay/pay?fromPage=index&id=' + res.payorderid + '&pageid=' + that.res.id, 'redirect');
                            return;
                        }
                        if (res.status == 1) {
                            var pagecontent = res.pagecontent;
                            that.isdiy = 1;
    
                            that.title = res.pageinfo.title;
                            that.sysset = res.sysset;
                            that.guanggaopic = res.guanggaopic;
    
                            that.guanggaourl = res.guanggaourl;
                            that.pageinfo = res.pageinfo;
                            that.pagecontent = pagecontent;
                            uni.setNavigationBarTitle({
                                title: res.pageinfo.title
                            });
                        } else {
                            app.alert(res.msg);
                        }
                    }
                });
            },
            changetab: function (e) {
                var st = e.currentTarget.dataset.st;
                this.pagenum = 1;
                this.st = st;
                this.datalist = [];
                uni.pageScrollTo({
                    scrollTop: 0,
                    duration: 0
                });
                this.getDataList();
            },
            getDataList: function (loadmore) {
                if(!loadmore){
                    this.pagenum = 1;
                    this.datalist = [];
                }
                var that = this;
                var pagenum = that.pagenum;
                var st = that.st;
                that.loading = true;
                that.nodata = false;
                that.nomore = false;
                app.post('ApiBusiness/getdatalist', {id: that.business.id,st: st,pagenum: pagenum,yuyue_cid:that.yuyue_cid}, function (res) {
                    that.loading = false;
                    uni.stopPullDownRefresh();
            var data = res.data;
            if (pagenum == 1) {
              that.datalist = data;
              if (data.length == 0) {
                that.nodata = true;
              }
            }else{
              if (data.length == 0) {
                that.nomore = true;
              } else {
                var datalist = that.datalist;
                var newdata = datalist.concat(data);
                that.datalist = newdata;
              }
            }
          });
            },
            openLocation:function(e){
                //console.log(e)
                var latitude = parseFloat(e.currentTarget.dataset.latitude)
                var longitude = parseFloat(e.currentTarget.dataset.longitude)
                var address = e.currentTarget.dataset.address
                uni.openLocation({
                 latitude:latitude,
                 longitude:longitude,
                 name:address,
                 scale: 13
             })		
            },
            phone:function(e) {
                var phone = e.currentTarget.dataset.phone;
                uni.makePhoneCall({
                    phoneNumber: phone,
                    fail: function () {
                    }
                });
            },
            //改变子分类
            changeyuyueCTab: function (e) {
                var that = this;
                var id = e.currentTarget.dataset.id;
                this.nodata = false;
                this.yuyue_cid = id;
                this.pagenum = 1;
                this.datalist = [];
                this.nomore = false;
                this.getDataList();
            },
            goBack() {
                uni.navigateBack({
                    delta: 1
                });
            }
        }
    }
    </script>
    <style>
    
    .container2 {
      padding: 16px;
      width: 95%;
      background-color: #f5f5f5;
      
    }
    
    .container2 {
      padding: 10px;
      width: 100%;
      background-color: #f5f5f5;
      margin-top: -10px;
    }
    
    .header {
      font-size: 18px;
      font-weight: bold;
      padding-bottom: 16px;
    }
    
    .card {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #ffffff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      width: 100%;
      box-sizing: border-box;
    }
    
    .content {
      display: flex;
      flex-direction: column;
    }
    
    .discount-text {
      font-size: 16px;
      font-weight: bold;
    }
    
    .highlight {
      color: #ff0000;
    }
    
    .sub-text {
      font-size: 14px;
      color: #666666;
      margin-top: 8px;
    }
    
    .pay-button {
      background-color: #ff4d4f;
      color: #ffffff;
      border: none;
      border-radius: 10px;
      padding: 4px 16px;
      font-size: 14px;
      cursor: pointer;
      margin-left: auto;
      margin-right: -10px;
    }
    .container{position:relative}
    .nodiydata{display:flex;flex-direction:column}
    .nodiydata .swiper {width: 100%;height: 400rpx;position:relative;z-index:1}
    .nodiydata .swiper .image {width: 100%;height: 400rpx;overflow: hidden;}
    
    .nodiydata .topcontent{width:94%;margin-left:3%;padding: 24rpx; border-bottom:1px solid #eee;margin-bottom:20rpx; background: #fff;margin-top:-120rpx;display:flex;flex-direction:column;align-items:center;border-radius:16rpx;position:relative;z-index:2;}
    .nodiydata .topcontent .logo{width:160rpx;height:160rpx;margin-top:-104rpx;border:2px solid rgba(255,255,255,0.5);border-radius:50%;}
    .nodiydata .topcontent .logo .img{width:100%;height:100%;border-radius:50%;}
    
    .nodiydata .topcontent .title {color:#222222;font-size:36rpx;font-weight:bold;margin-top:12rpx}
    .nodiydata .topcontent .desc {display:flex;align-items:center}
    .nodiydata .topcontent .desc .f1{ margin:20rpx 0; font-size: 24rpx;color:#FC5648;display:flex;align-items:center}
    .nodiydata .topcontent .desc .f1 .img{ width:24rpx;height:24rpx;margin-right:10rpx;}
    .nodiydata .topcontent .desc .f2{ margin:10rpx 0;padding-left:60rpx;font-size: 24rpx;color:#999;}
    .nodiydata .topcontent .tel{font-size:28rpx;color:#fff; padding:16rpx 40rpx; border-radius: 60rpx; font-weight: normal }
    .nodiydata .topcontent .tel .img{ width: 28rpx;height: 28rpx; vertical-align: middle;margin-right: 10rpx}
    .nodiydata .topcontent .address{width:100%;display:flex;align-items:center;padding-top:20rpx}
    .nodiydata .topcontent .address .f1{width:28rpx;height:28rpx;margin-right:8rpx}
    .nodiydata .topcontent .address .f2{flex:1;color:#999999;font-size:26rpx}
    .nodiydata .topcontent .address .f3{display: inline-block; width:26rpx; height: 26rpx}
    
    .nodiydata .contentbox{width:94%;margin-left:3%;background: #fff;border-radius:16rpx;margin-bottom:32rpx;overflow:hidden}
    
    .nodiydata .shop_tab{display:flex;width: 100%;height:90rpx;border-bottom:1px solid #eee;}
    .nodiydata .shop_tab .cptab_text{flex:1;text-align:center;color:#646566;height:90rpx;line-height:90rpx;position:relative}
    .nodiydata .shop_tab .cptab_current{color: #323233;}
    .nodiydata .shop_tab .after{display:none;position:absolute;left:50%;margin-left:-16rpx;bottom:10rpx;height:3px;border-radius:1.5px;width:32rpx}
    .nodiydata .shop_tab .cptab_current .after{display:block;}
    
    
    .nodiydata .cp_detail{min-height:500rpx}
    .nodiydata .comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}
    .nodiydata .comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}
    .nodiydata .comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}
    .nodiydata .comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}
    .nodiydata .comment .item .f1 .t3{text-align:right;}
    .nodiydata .comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}
    .nodiydata .comment .item .score{ font-size: 24rpx;color:#f99716;}
    .nodiydata .comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}
    .nodiydata .comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}
    .nodiydata .comment .item .f2 .t1{color:#333;font-size:28rpx;}
    .nodiydata .comment .item .f2 .t2{display:flex;width:100%}
    .nodiydata .comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}
    .nodiydata .comment .item .f2 .t3{color:#aaa;font-size:24rpx;}
    .nodiydata .comment .item .f2 .t3{color:#aaa;font-size:24rpx;}
    .nodiydata .comment .item .f3{width:100%;padding:10rpx 0;position:relative}
    .nodiydata .comment .item .f3 .arrow{width: 16rpx;height: 16rpx;background:#eee;transform: rotate(45deg);position:absolute;top:0rpx;left:36rpx}
    .nodiydata .comment .item .f3 .t1{width:100%;border-radius:10rpx;padding:10rpx;font-size:22rpx;color:#888;background:#eee}
    
    .nodiydata .nomore-footer-tips{background:#fff!important}
    
    .nodiydata .covermy{position:fixed;z-index:99999;cursor:pointer;display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden;z-index:9999;top:81vh;left:82vw;color:#fff;background-color:rgba(92,107,129,0.6);width:110rpx;height:110rpx;font-size:26rpx;border-radius:50%;}
    
    
    .classify-ul{width:100%;height:70rpx;padding:0 10rpx;}
    .classify-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:22rpx;color:#6C737F;font-size:20rpx;text-align: center;height:44rpx; line-height:44rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}
    
        .dp-cover{height: auto; position: relative;}
        .dp-cover-cover{position:fixed;z-index:99999;cursor:pointer;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color: inherit;}
        
    .back-btn {
        top: 97vh !important;
    }
    </style>