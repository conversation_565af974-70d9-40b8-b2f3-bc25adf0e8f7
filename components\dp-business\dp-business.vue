<template>
<view class="dp-business" :style="{
	color:params.color,
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	fontSize:(params.fontsize*2)+'rpx'
}">
	<view class="busbox" v-for="(item,index) in data" :key="item.id">
		<view class="businfo" @click="goto" :data-url="'/pagesExt/business/index?id='+item.bid">
			<view class="f1"><image class="image" :src="item.logo"/></view>
			<view class="f2">
				<view class="title">{{item.name}}</view>
				<view class="score" v-if="params.showpingfen!=='0'"><image class="image" :src="pre_url+'/static/img/star'+item.commentscore+'.png'"/>{{item.comment_score}}分</view>
				<view class="sales" v-if="params.showsales!=='0'"><text>销量：</text>{{item.sales}}</view>
				<view class="address" v-if="params.showjianjie=='1'"><text :decode="true">{{item.content}}</text></view>
				<view class="address flex"><view class="flex1"><text v-if="params.showaddress!=='0'">{{item.address}}</text></view><view v-if="params.showdistance" :style="{color:t('color1')}">{{item.juli}}</view></view>
			</view>
		</view>
		<view class="buspro" v-if="params.showproduct == 1">
			<view class="item" v-for="(item2,index2) in item.prolist" :style="'width:23%;margin-right:'+(index2%4!=3?'2%':0)" :key="item2.id" @click="goto" :data-url="item2.module == 'yuyue' ? '/yuyue/product?id='+item2.id : '/shopPackage/shop/product?id='+item2.id">
				<view class="product-pic">
					<image class="image" :src="item2.pic" mode="widthFix"/>
				</view>
				<view class="product-info">
					<view class="p1">{{item2.name}}</view>
					<view class="p2">
						<view class="p2-1">
							<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">￥</text>{{item2.sell_price}}<text v-if="item2.module == 'yuyue'" style="font-size:24rpx">/{{item2.danwei}}</text></text>
						</view>
					</view>
					<view v-if="item2.module == 'yuyue'" class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" @click.stop="goto" :data-url="'/yuyue/product?id='+item2.id"><text class="iconfont icon_gouwuche"></text></view>
					<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="item2.module != 'yuyue' && !item2.price_type" @click.stop="buydialogChange" :data-proid="item2.id"><text class="iconfont icon_gouwuche"></text></view>
				</view>
			</view>
		</view>
	</view>
	<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange" :menuindex="menuindex"></buydialog>
</view>
</template>
<script>
	export default {
		data(){
			return {
				pre_url:getApp().globalData.pre_url,
				buydialogShow:false,
			}
		},
		props: {
			menuindex:{default:-1},
			params:{},
			data:{}
		},
		methods: {
			buydialogChange: function (e) {
				if(!this.buydialogShow){
					this.proid = e.currentTarget.dataset.proid
				}
				this.buydialogShow = !this.buydialogShow;
				console.log(this.buydialogShow);
			},
			addcart:function(){
				this.$emit('addcart');
			}
		}
	}
</script>
<style>
.dp-business{height: auto; position: relative;}
.dp-business .busbox{background: #fff;padding:16rpx;overflow: hidden;margin-bottom:16rpx;width:100%}
.dp-business .businfo{display:flex;width:100%}
.dp-business .businfo .f1{width:200rpx;height:200rpx; margin-right:20rpx;flex-shrink:0}
.dp-business .businfo .f1 .image{ width: 100%;height:100%;border-radius:20rpx;object-fit: cover;}
.dp-business .businfo .f2{flex:1}
.dp-business .businfo .f2 .title{font-size:28rpx;font-weight:bold; color: #222;line-height:46rpx;margin-bottom:3px;}
.dp-business .businfo .f2 .score{font-size:24rpx;color:#f99716;}
.dp-business .businfo .f2 .score .image{width:140rpx; height:50rpx; vertical-align: middle;margin-bottom:3px; margin-right:3px;}
.dp-business .businfo .f2 .sales{font-size:24rpx; color:#31C88E;margin-bottom:3px;}
.dp-business .businfo .f2 .address{color:#999;font-size:24rpx;line-height:40rpx;margin-bottom:3px;}

.dp-business .buspro{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap;margin-top:32rpx}
.dp-business .buspro .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden;}
.dp-business .buspro .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}
.dp-business .buspro .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-business .buspro .product-info {padding:20rpx 0;position: relative;}
.dp-business .buspro .product-info .p1 {color:#323232;font-weight:bold;font-size:24rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;height:36rpx}
.dp-business .buspro .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}
.dp-business .buspro .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}
.dp-business .buspro .product-info .p2-1 .t1{font-size:28rpx;}
.dp-business .buspro .product-info .p2-1 .t2 {margin-left:10rpx;font-size:22rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-business .buspro .product-info .p2-1 .t3 {margin-left:10rpx;font-size:22rpx;color: #999;}
.dp-business .buspro .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}
.dp-business .buspro .product-info .p3{color:#999999;font-size:20rpx;margin-top:10rpx}
.dp-business .buspro .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:20rpx;right:0;text-align:center;}
.dp-business .buspro .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}
.dp-business .buspro .product-info .p4 .img{width:100%;height:100%}
</style>