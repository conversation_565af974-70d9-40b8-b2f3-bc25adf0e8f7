<template>
<view class="container">
	<block v-if="isload">
		<view class="form">
			<picker @change="bindPickerChange" :value="educationIndex" :range="educationList">
				<view class="picker-item">
					<view class="label">学历</view>
					<view class="value">{{educationList[educationIndex]}}</view>
					<image class="arrow" src="/static/img/arrowright.png" />
				</view>
			</picker>
		</view>
		<button class="set-btn" @tap="saveEducation" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">保 存</button>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			education: '',
			educationIndex: 0,
			educationList: ['请选择', '小学', '初中', '高中', '中专', '大专', '本科', '硕士', '博士', '其他']
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
    getdata: function() {
      var that = this;
      that.loading = true;
      app.get('ApiMy/set', {}, function(data) {
        that.loading = false;
        that.education = data.userinfo.education || '';
        
        // 根据当前学历值设置picker的索引
        if (that.education) {
          const index = that.educationList.findIndex(item => item === that.education);
          that.educationIndex = index > 0 ? index : 0;
        }
        
        that.loaded();
      });
    },
    
    bindPickerChange: function(e) {
      this.educationIndex = e.detail.value;
    },
    
    saveEducation: function() {
      var that = this;
      var education = that.educationList[that.educationIndex];
      
      if (that.educationIndex === 0) {
        app.alert('请选择学历');
        return;
      }
      
      app.showLoading('提交中');
      app.post("ApiMy/setfield", {education: education}, function(data) {
        app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function() {
            app.goback(true);
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    }
  }
};
</script>
<style>
.form { 
  width: 94%;
  margin: 20rpx 3%;
  border-radius: 5px;
  padding: 0 3%;
  background: #FFF;
}
.picker-item {
  display: flex;
  align-items: center;
  width: 100%;
  height: 98rpx;
  line-height: 98rpx;
}
.picker-item .label {
  color: #000;
  width: 200rpx;
}
.picker-item .value {
  flex: 1;
  color: #444444;
}
.picker-item .arrow {
  width: 26rpx;
  height: 26rpx;
  margin-left: 20rpx;
}
.set-btn {
  width: 90%;
  margin: 60rpx 5%;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 48rpx;
  color: #FFFFFF;
  font-weight: bold;
}
</style> 