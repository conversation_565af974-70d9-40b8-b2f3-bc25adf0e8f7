<template>
<view class="dp-search" :style="containerStyle">

   <view class="city-selector" v-if="params.position>0" @click="selectCity()">
	   <view class="city-text" :style="{color: params.area_color || '#333333'}">{{city}}</view>
	   <image class="city-arrow" src="../../static/img/arrowdown.png"></image>
   </view>

   <view class="search-container">
	<!-- 根据openmore参数决定显示标准搜索框还是带类别选择的搜索框 -->
	<block v-if="!params.openmore || params.openmore<=0">
		<view class="dp-search-search"
			:style="{
				borderColor:params.bordercolor,
				borderRadius:params.borderradius+'px',
				width: isSearchBtnOutside ? 'calc(100% - ' + searchOffset + 'rpx)' : '100%',
				backgroundColor: searchBoxBgColor
			}">
			<view class="dp-search-search-f1"></view>
			<view class="dp-search-search-f2">
				<input class="dp-search-search-input" @confirm="searchgoto" @input="inputKeyword" :data-url="params.hrefurl" name="keyword"
					:placeholder="params.placeholder|| '输入关键字在店铺内搜索'" placeholder-style="color:#aaa;font-size:28rpx" :style="params.color?'color:'+params.color:''" />
			</view>
			<!-- 搜索框内的按钮 -->
			<view v-if="params.search_btn=='1' && params.btn_position=='inside'" class="dp-search-btn-inside" 
				@tap="searchgoto" :data-url="params.hrefurl"
				:style="{
					backgroundColor: params.btn_color || '#f0f0f0',
					color: params.btn_text_color || '#333333',
					borderRadius: (params.btn_radius || 0) + 'px',
					border: '1px solid ' + (params.btn_border_color || '#e6e6e6')
				}">
				<text v-if="params.btn_type!='icon'">{{params.btn_text || '搜索'}}</text>
				<image v-else class="dp-search-btn-icon-inside" :src="btnIcon" mode="aspectFit"></image>
			</view>
			<view class="dp-search-search-f3" v-if="params.image_search==1" @tap="goto" data-url="/pagesExt/shop/imgsearch" :style="'background-image:url('+pre_url+'/static/img/camera.png)'"></view>
		</view>
		
		<!-- 搜索框外的按钮 -->
		<view v-if="params.search_btn=='1' && params.btn_position!='inside'" class="dp-search-btn-wrap">
			<view @tap="searchgoto" :data-url="params.hrefurl" class="dp-search-btn" 
				:style="{
					backgroundColor: params.btn_color || '#f0f0f0',
					color: params.btn_text_color || '#333333',
					borderRadius: (params.btn_radius || 0) + 'px',
					border: '1px solid ' + (params.btn_border_color || '#e6e6e6'),
					width: btnSizeWidth + 'rpx'
				}">
				<text v-if="params.btn_type!='icon'">{{params.btn_text || '搜索'}}</text>
				<image v-else class="dp-search-btn-icon" :src="btnIcon" mode="aspectFit"></image>
			</view>
		</view>
	</block>
    
    <!-- 当openmore>0时显示带类别选择的搜索框 -->
    <block v-else>
        <view style="display: flex; width: 100%;">
            <view style="width: 140rpx;overflow: hidden;">
                <!--搜索列表s-->
                <picker @change="dataChange" :value="data_index" :range="data" range-key='title1' :style="'line-height: 72rpx;background-color:' + searchBoxBgColor + ';padding-left: 10rpx;overflow: hidden;border: 0;'">
                    <view style="width:80rpx;white-space: nowrap;overflow: hidden;float: left;">{{data_name}}</view>
                    <image src="/static/img/hdsanjiao.png" style="width: 26rpx;height: 26rpx;float: right;margin-top: 26rpx;"></image>
                </picker>
                <!--搜索列表e-->
            </view>
            
            <view class="dp-search-search"
                :style="{
                    borderColor:params.bordercolor,
                    borderRadius:params.borderradius+'px',
                    width: isSearchBtnOutside ? 'calc(100% - 140rpx - ' + searchOffset + 'rpx)' : 'calc(100% - 140rpx)',
                    backgroundColor: searchBoxBgColor
                }">
                <view class="dp-search-search-f1"></view>
                <view class="dp-search-search-f2">
                    <input class="dp-search-search-input" @confirm="searchgoto" @input="inputKeyword" :data-url="data_hrefurl" name="keyword"
                        :placeholder="data_placeholder?data_placeholder:params.placeholder|| '输入关键字在店铺内搜索'" placeholder-style="color:#aaa;font-size:28rpx" :style="params.color?'color:'+params.color:''" />
                </view>
                <!-- 多搜索模式下的搜索框内按钮 -->
                <view v-if="params.search_btn=='1' && params.btn_position=='inside'" class="dp-search-btn-inside" 
                    @tap="searchgoto" :data-url="data_hrefurl"
                    :style="{
                        backgroundColor: params.btn_color || '#f0f0f0',
                        color: params.btn_text_color || '#333333',
                        borderRadius: (params.btn_radius || 0) + 'px',
                        border: '1px solid ' + (params.btn_border_color || '#e6e6e6')
                    }">
                    <text v-if="params.btn_type!='icon'">{{params.btn_text || '搜索'}}</text>
                    <image v-else class="dp-search-btn-icon-inside" :src="btnIcon" mode="aspectFit"></image>
                </view>
                <view class="dp-search-search-f3" v-if="params.image_search==1" @tap="goto" :data-url="data_hrefurl" :style="'background-image:url('+pre_url+'/static/img/camera.png)'"></view>
            </view>
            
            <!-- 多搜索模式下的搜索框外按钮 -->
            <view v-if="params.search_btn=='1' && params.btn_position!='inside'" class="dp-search-btn-wrap">
                <view @tap="searchgoto" :data-url="data_hrefurl" class="dp-search-btn" 
                    :style="{
                        backgroundColor: params.btn_color || '#f0f0f0',
                        color: params.btn_text_color || '#333333',
                        borderRadius: (params.btn_radius || 0) + 'px',
                        border: '1px solid ' + (params.btn_border_color || '#e6e6e6'),
                        width: btnSizeWidth + 'rpx'
                    }">
                    <text v-if="params.btn_type!='icon'">{{params.btn_text || '搜索'}}</text>
                    <image v-else class="dp-search-btn-icon" :src="btnIcon" mode="aspectFit"></image>
                </view>
            </view>
        </view>
    </block>
   </view>
</view>
</template>
<script>
	var app =getApp();
	export default {
		data(){
			return {
				pre_url:getApp().globalData.pre_url,
                
                data_index:0,
                data_name:'',//类型名称
                data_placeholder:'',//搜索提示
                data_hrefurl:'',
                keyword:'',
				city : '',
                // 按钮图标映射
                btnIcons: {
                    'search': '/static/img/search_ico.png',
                    'arrow': '/static/img/arrowdown.png',
                    'plus': '/static/img/plus_search.png',
                    'plane': '/static/img/plane.png',
                    'check': '/static/img/check.png'
                }
			}
		},
		props: {
			params: {},
			data: {}
		},
        computed: {
            // 容器样式计算属性 - 微信小程序兼容格式
            containerStyle() {
                // 基础样式文本
                let styleStr = `margin:${(this.params.margin_y*2.2)}rpx ${(this.params.margin_x*2.2)}rpx;padding:${(this.params.padding_y*2.2)}rpx ${(this.params.padding_x*2.2)}rpx;`;

                // 根据背景类型设置不同的背景
                if (this.params.bg_type === 'image' && this.params.bgimg) {
                    styleStr += `background-image:url(${this.params.bgimg});background-size:cover;background-position:center;`;
                } else {
                    // 检查是否开启背景透明
                    if (this.params.bg_transparent == '1') {
                        styleStr += `background-color:transparent;`;
                    } else {
                        styleStr += `background-color:${this.params.bgcolor || '#F6F6F6'};`;
                    }
                }

                return styleStr;
            },
            // 判断搜索按钮是否在搜索框外
            isSearchBtnOutside() {
                return this.params.search_btn == '1' && this.params.btn_position !== 'inside';
            },
            // 搜索偏移计算属性
            searchOffset() {
                const btnWidth = this.btnSizeWidth;
                // 按钮宽度加上边距再加上一点额外空间
                return btnWidth + 40; // 按钮宽度 + 左边距(20rpx) + 右边距(20rpx)
            },
            // 按钮大小计算属性
            btnSizeWidth() {
                let size = parseInt(this.params.btn_border_size);
                if (isNaN(size)) size = 5; // 默认中等大小
                // 确保大小在1-10范围内
                size = Math.max(1, Math.min(10, size));
                
                // 将1-10的范围映射到实际宽度
                // 使用更大的比例因子，让按钮宽度变化更明显
                const minWidth = 60; // 最小宽度
                const maxWidth = 180; // 最大宽度
                
                // 线性映射 1->minWidth, 10->maxWidth
                const width = minWidth + (maxWidth - minWidth) * (size - 1) / 9;
                
                return Math.round(width);
            },
            // 按钮图标计算属性
            btnIcon() {
                // 如果有自定义图标路径，优先使用
                if (this.params.custom_icon_path) {
                    return this.params.custom_icon_path;
                }
                // 否则使用预设图标
                const iconType = this.params.btn_icon || 'search';
                return this.btnIcons[iconType] || this.btnIcons.search;
            },
            // 搜索框背景颜色计算属性
            searchBoxBgColor() {
                // 搜索框应该始终有背景色，透明设置应该只影响搜索框容器
                // 这里返回默认的白色背景，让搜索框保持可见
                return '#ffffff';
            }
        },
        mounted:function(){
            var that = this;
          //  console.log(that.params)  
		
            if(that.data){
                that.data_name        = that.data[0]['title1'];
                that.data_placeholder = that.data[0]['title2'];
                that.data_hrefurl     = that.data[0]['hrefurl'];
                console.log(that.data[0]['hrefurl'])
            }
			
			if(that.params.area_config && that.params.area_config.gettype<2){
				
				 that.city = that.params.current_area_name
				 			 
			}else{
				
				if(uni.getStorageSync('area_name')){
					 that.city =uni.getStorageSync('area_name')
				}else{
					 that.city = that.params.area_config && that.params.area_config.defaultcity_name
				}
				
			}
			

			
			 
			
			uni.$on('city',function(data){
				that.city  = data.name 

			})
			 
        },
		methods:{
			selectCity(){ 
	
				if(this.params.area_config && this.params.area_config.switcharea == 0){
					 app.goto('/pages/index/city?data='+encodeURIComponent(JSON.stringify(this.params)));
				}  
				
			    
			},
			
			searchgoto:function(e){
                var that = this;
                var keyword = that.keyword;
				var url = e.currentTarget.dataset.url;
                if (url.indexOf('?') > 0) {
                    url += '&keyword='+keyword;
                }else{
                    url += '?keyword='+keyword;
                }
                var opentype = e.currentTarget.dataset.opentype
                app.goto(url,opentype);
			},
            dataChange:function(e){
                var that = this;
                var data = that.data;
                var data_index  = e.detail.value;
                that.data_index = data_index;
                that.data_name        = data[data_index]['title1'];
                that.data_placeholder = data[data_index]['title2'];
                that.data_hrefurl     = data[data_index]['hrefurl'];
            },
            inputKeyword:function(e){
                var that = this;
                that.keyword = e.detail.value;
            }
		}
	}
</script>
<style>
.dp-search {padding:20rpx;height: auto; position: relative;display: flex;align-items: center;z-index: 10;}
.search-container {display: flex;flex: 1;align-items: center;}
.dp-search-search {height:72rpx;border: 1px solid #c0c0c0;border-radius: 6rpx;overflow: hidden;display:flex;}
.dp-search-search-f1 {height:72rpx;width:72rpx;color: #666;border: 0px;padding: 0px;margin: 0px;background:url('/static/img/search_ico.png') center no-repeat; background-size:30rpx;}
.dp-search-search-f2{height: 72rpx;flex:1}
.dp-search-search-f3 {height:72rpx;width:72rpx;color: #666;border: 0px;padding: 0px;margin: 0px;background-position: center;background-repeat: no-repeat; background-size:40rpx;}
.dp-search-search-input {height:72rpx;width: 100%;border: 0px;padding: 0px;margin: 0px;outline: none;color: #666;}

.dp-search-btn-wrap {display: flex;align-items: center;margin-left: 20rpx;}
.dp-search-btn {height: 72rpx;line-height:72rpx;text-align: center;display: flex;align-items: center;justify-content: center;padding: 0 20rpx;}
.dp-search-btn-icon {width: 40rpx;height: 40rpx;}

/* 搜索框内按钮样式 */
.dp-search-btn-inside {
    height: 56rpx;
    line-height: 56rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16rpx;
    margin: 8rpx;
    font-size: 28rpx;
    white-space: nowrap;
    min-width: 80rpx;
    max-width: 120rpx;
}

.dp-search-btn-icon-inside {
    width: 32rpx;
    height: 32rpx;
}

.city-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 18rpx;
    height: 60rpx;
    border-radius: 30rpx;
    margin-right: 20rpx;
    background-color: #ffffff;
    border: 1px solid #eeeeee;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.city-text {
    font-size: 28rpx;
    font-weight: 500;
    max-width: 140rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.city-arrow {
    width: 24rpx;
    height: 24rpx;
    margin-left: 8rpx;
}
</style>
