<template>
	<view class="container">
		<block v-if="isload">
			<view class="view-show" style="padding: 40rpx;">
				<view style="margin-bottom: 40rpx;font-weight: bold;font-size: 46rpx;">{{data.zhuanye_name}}</view>
				<view style="margin-bottom: 40rpx;">
					<view style="display: flex;justify-content: space-between;margin-bottom: 30rpx;">
						<view style="flex: 1;">
							<span style="color: #bbbbbb;">学历层次：</span>{{data.education_level}}
						</view>
						<view style="flex: 1;">
							<span style="color: #bbbbbb;">学制：</span>{{data.study_duration}}
						</view>
					</view>
					<view>
						<span style="color: #bbbbbb;">专业大类：</span>{{data.title}}
					</view>
				</view>

				<!-- 专业介绍 -->
				<view style="font-weight: bold;font-size: 32rpx;margin-bottom: 40rpx;">专业介绍</view>
				<view>
					{{data.description}}
				</view>
			</view>
		</block>
		<loading v-if="loading" loadstyle="left:62.5%"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				data: {},
				id: '',
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.id = this.opt.id ? this.opt.id : '';
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiDaxue/getDetailById', {
					id: that.id
				}, function(res) {
					that.loading = false;
					that.data = res.data;
					that.loaded();
				});
			}
		}
	};
</script>
<style>

</style>