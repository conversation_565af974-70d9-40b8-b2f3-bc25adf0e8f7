# 梦想方舟计划 - UniApp版本

## 项目概述

梦想方舟计划是一个创新的时空对话与AI变脸预测系统，通过先进的AI技术让用户与20年后的自己进行对话，并预测未来的样子。本项目将原HTML版本完整转换为UniApp版本，支持跨平台运行。

## 功能特性

### 🚀 核心功能
- **时空对话系统**: 与2049年的AI进行深度对话
- **AI变脸预测**: 使用AI技术预测20年后的样子
- **语音通话功能**: 与未来自己进行语音对话
- **梦想启蒙流程**: 完整的个人成长引导体验
- **成就系统**: 记录用户的时空之旅历程

### ✨ 技术特色
- **粒子系统**: 炫酷的时空粒子背景效果
- **打字效果**: 逐字显示的科幻感文字动画
- **全息投影**: 未来感十足的UI设计
- **语音识别**: 支持语音输入和合成
- **数据持久化**: 完整的用户数据保存机制

## 项目结构

```
pagesB/dreamark/
├── index.vue              # 欢迎页面 - 梦想方舟启动界面
├── dialogue.vue           # 对话页面 - 时空对话系统
├── camera.vue             # 拍照页面 - AI变脸预测
├── voice-chat.vue         # 语音页面 - 语音通话系统
├── ending.vue             # 结束页面 - 时空之旅完成
├── utils/
│   └── dreamark-utils.js  # 工具类库
└── README.md              # 项目说明文档
```

## 页面功能详解

### 1. 欢迎页面 (index.vue)
- **功能**: 系统启动和欢迎引导
- **特色**:
  - 机器人欢迎动画
  - 逐字打字效果
  - 时空粒子通道
  - 音效系统
  - 交互动画
  - **自动穿越**: 文字播放完成后自动跳转到对话页面
  - **穿越动画**: 炫酷的时空穿越视觉效果
  - **倒计时提示**: 3秒倒计时，用户可选择取消

### 2. 对话页面 (dialogue.vue)
- **功能**: 与AI进行时空对话
- **特色**:
  - AI问答系统
  - 用户输入处理
  - 进度追踪
  - 对话记录保存
  - 智能引导

### 3. 拍照页面 (camera.vue)
- **功能**: AI变脸预测系统
- **特色**:
  - 摄像头调用
  - 拍照功能
  - AI处理模拟
  - 结果展示
  - 扣子接口集成

### 4. 语音页面 (voice-chat.vue)
- **功能**: 语音通话系统
- **特色**:
  - 全息头像显示
  - 语音识别
  - 语音合成
  - 波纹动画
  - 对话记录

### 5. 结束页面 (ending.vue)
- **功能**: 时空之旅完成
- **特色**:
  - 结束引导
  - 梦想墙互动
  - 个性化宣言
  - 成就系统
  - 分享功能

## 技术实现

### 核心技术栈
- **框架**: UniApp (Vue.js)
- **样式**: CSS3 + 动画效果
- **存储**: UniApp Storage API
- **媒体**: Camera API, Audio API
- **网络**: HTTP请求 (预留AI接口)

### 关键组件

#### 粒子系统 (ParticleSystem)
```javascript
import { ParticleSystem } from './utils/dreamark-utils.js'

// 初始化粒子系统
const particles = new ParticleSystem('particlesCanvas', width, height)
particles.start()
```

#### 打字效果 (TypewriterEffect)
```javascript
import { TypewriterEffect } from './utils/dreamark-utils.js'

// 创建打字效果
const typewriter = new TypewriterEffect(text, callback, speed)
typewriter.start()
```

#### 音效管理 (AudioManager)
```javascript
import { AudioManager } from './utils/dreamark-utils.js'

// 播放音效
const audioManager = new AudioManager()
audioManager.play('/static/audio/startup.mp3')
```

## 数据流转

### 用户数据结构
```javascript
// 对话数据
{
  name: "用户姓名",
  age: "用户年龄", 
  dream: "用户梦想"
}

// 预测结果
{
  originalImage: "原始照片路径",
  predictedImage: "预测结果路径",
  timestamp: "生成时间"
}

// 语音设置
{
  language: "语言设置",
  speechSpeed: "语速设置",
  soundEffectEnabled: "音效开关"
}
```

### 页面跳转流程
```
index.vue → dialogue.vue → camera.vue → voice-chat.vue → ending.vue
    ↓           ↓             ↓             ↓             ↓
  启动系统    收集信息      AI预测        语音对话      完成旅程
```

## 配置说明

### 系统配置 (DreamArkConfig)
```javascript
export const DreamArkConfig = {
  // 音效文件路径
  audio: {
    startup: '/static/audio/startup.mp3',
    typing: '/static/audio/typing.mp3',
    portal: '/static/audio/portal.mp3'
  },
  
  // 动画配置
  animation: {
    typingSpeed: 80,
    particleCount: 50
  },
  
  // 存储键名
  storage: {
    dialogueData: 'user_dialogue_data',
    predictedImage: 'predicted_image_url'
  }
}
```

## 部署说明

### 1. 环境要求
- HBuilderX 3.0+
- UniApp CLI
- 微信开发者工具 (微信小程序)
- Android Studio (Android App)
- Xcode (iOS App)

### 2. 安装步骤
1. 将dreamark文件夹复制到 `pagesB/` 目录下
2. 在 `pages.json` 中添加页面路由配置
3. 准备音效文件到 `static/audio/` 目录
4. 配置AI接口地址 (如需要)
5. 编译运行

### 3. 页面路由配置
```json
{
  "root": "pagesB",
  "pages": [
    {"path": "dreamark/index", "style": {"navigationBarTitleText": "梦想方舟"}},
    {"path": "dreamark/dialogue", "style": {"navigationBarTitleText": "时空对话"}},
    {"path": "dreamark/camera", "style": {"navigationBarTitleText": "AI变脸预测"}},
    {"path": "dreamark/voice-chat", "style": {"navigationBarTitleText": "语音对话"}},
    {"path": "dreamark/ending", "style": {"navigationBarTitleText": "时空之旅完成"}}
  ]
}
```

## 使用指南

### 用户操作流程
1. **启动系统**: 点击"启动梦想方舟"开始体验
2. **欢迎引导**: 观看机器人欢迎动画和文字介绍
3. **自动穿越**: 文字播放完成后自动穿越到2049年（可取消）
4. **时空对话**: 回答AI的问题，建立个人档案
5. **AI预测**: 拍照或上传照片，获得未来预测
6. **语音通话**: 与未来自己进行语音对话
7. **完成旅程**: 选择明日萌像，获得个性化宣言

### 管理员功能
- **数据清空**: 长按特定按钮可清空用户数据
- **音效控制**: 可开启/关闭系统音效
- **配置管理**: 可调整用户性别、职业等信息

## 扩展开发

### 添加新功能
1. 在对应页面的 `methods` 中添加新方法
2. 在 `utils/dreamark-utils.js` 中添加公共工具
3. 更新配置文件中的相关设置
4. 测试功能完整性

### AI接口集成
```javascript
// 示例：集成AI变脸接口
async function callAIAPI(imageData) {
  try {
    const response = await uni.request({
      url: DreamArkConfig.api.baseUrl + '/predict',
      method: 'POST',
      data: { image: imageData },
      timeout: DreamArkConfig.api.timeout
    })
    return response.data
  } catch (error) {
    console.error('AI接口调用失败:', error)
    throw error
  }
}
```

## 注意事项

### 权限要求
- **摄像头权限**: 拍照功能需要
- **录音权限**: 语音功能需要  
- **存储权限**: 保存结果需要
- **网络权限**: AI接口调用需要

### 兼容性
- **微信小程序**: 完全支持
- **支付宝小程序**: 需要适配部分API
- **H5**: 需要处理跨域问题
- **App**: 需要配置原生权限

### 性能优化
- 粒子系统在低端设备上可能影响性能
- 大图片处理需要压缩优化
- 音效文件建议使用压缩格式
- 及时清理定时器和监听器

## 更新日志

### v1.0.1 (2024-01-18)
- ✅ 优化首页自动跳转逻辑
- ✅ 添加穿越时空动画效果
- ✅ 实现文字播放完成后自动穿越
- ✅ 添加倒计时提示和取消功能
- ✅ 统一手动和自动跳转的视觉效果

### v1.0.0 (2024-01-18)
- ✅ 完成HTML到UniApp的完整转换
- ✅ 实现5个核心页面功能
- ✅ 集成粒子系统和动画效果
- ✅ 添加音效管理和数据存储
- ✅ 完善用户交互和页面跳转
- ✅ 添加权限管理和错误处理

## 技术支持

如有问题或建议，请联系开发团队。

---

**梦想方舟计划** - 连接现在与未来的时空之桥 🚀
