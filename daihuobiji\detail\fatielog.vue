<template>
	<view class="page flex-col">
		<!-- Header user information omitted -->

		<view class="flex-col">
			<view class="text-wrapper_5 flex-row justify-between">
				<!-- 我的笔记 Tab -->
				<text class="text_10" :class="{ 'active': activeTab === 'notes' }"
					:style="'color: ' + (activeTab === 'notes' ? t('color1') : '#999999')" @click="switchTab('notes')">
					我的笔记
				</text>

				<!-- 我的评论 Tab -->
				<text class="text_11" :class="{ 'active': activeTab === 'favorites' }"
					:style="'color: ' + (activeTab === 'favorites' ? t('color1') : '#999999')"
					@click="switchTab('favorites')">
					我的评论
				</text>
			</view>

			<view style="padding: 24rpx 24rpx 60rpx 24rpx; margin-top: 15rpx;">
				<view class="list">
					<!-- 使用计算属性 currentList 来代替复杂的三元表达式 -->
					<view class="pbl" v-for="(item, index) in currentList" :key="index">
						<view class="image" @click="goItem(item.id)">
							<image fade-show lazy-load :lazy-load-margin="0" mode="widthFix" :src="item.coverimg"></image>
						</view>
						<view class="title" v-html="item.title" @click="goItem(item.id)"></view>

						<view @click="goItem(item.id)"
							style="display: flex;align-items: center;justify-content: space-between;padding: 10px;color: #aaa;">

							<view style="display: flex;align-items: center;width: 60%;">
								<img style="width: 20px;height: 20px;border-radius: 50px;" :src="item.headimg"></img>
								<view
									style="font-size: 10px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;margin-left: 5px;">
									{{item.nickname}}
								</view>
							</view>

							<view style="display: flex;align-items: center;">
								<image style="width: 12px;height: 12px;" src="../../static/restaurant/like1.png"></image>
								<view style="font-size: 10px;margin-left: 5px;">{{item.zan}}</view>
							</view>
						</view>
						
						<view class="action-buttons" v-if="activeTab === 'notes'">
							<button class="edit-button" @click.stop="editItem(item.id)">编辑</button>
							<button class="delete-button" @click.stop="confirmDelete(item.id)">删除</button>
						</view>
					</view>
				</view>

				<uni-load-more :status="loadStatus" v-if="!loading"></uni-load-more>
			</view>
		</view>

		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	import waterfallsFlow from "../components/maramlee-waterfalls-flow/maramlee-waterfalls-flow.vue";
	var app = getApp();
	export default {
		components: {
			waterfallsFlow
		},
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				pagecontent: {},
				activeTab: 'notes', // 默认选中“我的笔记”标签
				noteList: [],
				collectList: [],
				queryParams: {
					pagenum_notes: 1,
					pernum_notes: 10,
					pagenum_favorites: 1,
					pernum_favorites: 10
				},
				loadStatus: 'more', // 加载更多
			}
		},
		computed: {
			// 新增计算属性，用于替代模板中的复杂表达式
			currentList() {
				return this.activeTab === 'notes' ? this.noteList : this.collectList;
			}
		},
		watch: {
			activeTab: {
				handler(newValue, oldValue) {
					// 切换标签时重置分页参数
					if (newValue === 'notes') {
						this.queryParams.pagenum_notes = 1;
					} else {
						this.queryParams.pagenum_favorites = 1;
					}
					this.getdata();
				},
				immediate: true, // 初始化时立即执行
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.activeTab = opt.type ? opt.type : 'notes'
			this.getdata();
		},
		methods: {
			getdata: function() {
				let that = this;
				that.loading = true;
				app.get(that.activeTab == 'notes' ? 'ApiMy/userbiji' : '/Apidaihuobiji/getMyCommentsNotes', that
					.queryParams,
					function(data) {
						that.loading = false;
						that.pagecontent = data.userinfo; // 赋值用户信息

						if (that.activeTab === 'notes') {
							// 检查 data.notes 是否存在
							let arrList = (data.notes && data.notes.data) ? data.notes.data : [];
							if (that.queryParams.pagenum_notes === 1) {
								that.noteList = arrList;
							} else {
								that.noteList = that.noteList.concat(arrList);
							}
							// 根据 arrList 的长度更新加载状态
							that.loadStatus = arrList.length < that.queryParams.pernum_notes ? 'noMore' : 'more';
						} else {
							// 检查 data.datalist 是否存在
							let arrList1 = data.datalist || [];
							console.log('arrList1', arrList1)
							if (that.queryParams.pagenum_favorites === 1) {
								that.collectList = arrList1;
							} else {
								that.collectList = that.collectList.concat(arrList1);
							}
							// 根据 arrList1 的长度更新加载状态
							that.loadStatus = arrList1.length < that.queryParams.pernum_favorites ? 'noMore' : 'more';
						}

						uni.stopPullDownRefresh();
					});
			},
			switchTab(tab) {
				this.activeTab = tab;
			},
			goItem(id) {
				uni.navigateTo({
					url: `/daihuobiji/detail/index?id=${id}`,
				});
			},
			LinkTo() {
				uni.navigateTo({
					url: '/pages/my/usercenter'
				});
			},
			// 下拉刷新
			onPullDownRefresh() {
				if (this.activeTab === 'notes') {
					this.noteList = [];
					this.queryParams.pagenum_notes = 1;
				} else {
					this.collectList = [];
					this.queryParams.pagenum_favorites = 1;
				}
				this.getdata(); // 重新获取数据
			},
			// 触底加载更多
			onReachBottom() {
				console.log("Load more action");
				if (this.activeTab === 'notes') {
					if (this.loadStatus === 'more') {
						this.queryParams.pagenum_notes += 1;
						this.getdata();
					}
				} else {
					if (this.loadStatus === 'more') {
						this.queryParams.pagenum_favorites += 1;
						this.getdata();
					}
				}
			},
			// 编辑笔记
			editItem(id) {
				console.log(id);
				uni.navigateTo({
					url: `/daihuobiji/detail/fatieedit?id=${id}`,
				});
			},
			// 确认删除笔记
			confirmDelete(id) {
				let that = this;
				app.confirm('确定要删除此笔记吗?', () => {
					that.removeItem(id);
				});
			},
			// 删除笔记
			removeItem(id) {
				let that = this;
				app.post('Apidaihuobiji/detalbiji', {
					id
				}, function(res) {
					if (res.status === 1) {
						app.success('删除成功');

						// 清空列表并重置分页参数，然后重新获取数据
						if (that.activeTab === 'notes') {
							that.noteList = [];
							that.queryParams.pagenum_notes = 1; // 重置到第一页
						} else {
							that.collectList = [];
							that.queryParams.pagenum_favorites = 1; // 重置到第一页
						}
						that.getdata(); // 重新加载数据
					} else {
						app.error('删除失败');
					}
				});
			}
		}
	}
</script>

<style lang="less">
	@primary-color: #e3582f;
	@secondary-color: #ffab00;
	@danger-color: #ff4d4f;
	@text-color: #333;
	@sub-text-color: #8391aa;
	@gray-color: #999;
	@background-color: #f0f0f0;
	@white-color: #fff;
	@shadow-color: rgba(0, 0, 0, 0.1);
	
	.page {
	    background-color: @background-color;
	    width: 750rpx;
	    min-height: 100vh;
	}
	
	/* Tab切换的样式 */
	.text-wrapper_5 {
	    display: flex;
	    justify-content: center;
	    padding: 15rpx 0;
	    background-color: @white-color;
	    border-bottom: 1rpx solid @gray-color;
	
	    text {
	        font-size: 30rpx;
	        color: @gray-color;
	        cursor: pointer;
	        padding: 10rpx 20rpx;
	        margin: 0 40rpx;
	        text-align: center;
	        transition: color 0.3s, border-bottom 0.3s;
	
	        &.active {
	            color: @primary-color;
	            font-weight: bold;
	            border-bottom: 4rpx solid @primary-color;
	        }
	
	        &:hover {
	            color: @primary-color;
	        }
	    }
	}
	
	/* 笔记内容框样式 */
	.list {
		column-count: 2;
		box-sizing: content-box;
		margin-top: 10px;
	}

	.pbl {
		width: 100%;
		break-inside: avoid;
		overflow: hidden;
		border-radius: 5px;
		margin-bottom: 20rpx;
		background-color: #fff;
		box-sizing: border-box;

		&:last-child {
			margin-bottom: 10rpx;
		}

		.image {
			width: 100%;
			border-radius: 5px;
			overflow: hidden;

			&>image {
				width: 100%;
				height: 100%;
			}
		}

		.title {
			font-size: 32rpx;
			margin-bottom: 6rpx;
			display: -webkit-box;
			text-overflow: ellipsis;
			overflow: hidden;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2; // 超出2行省略
			padding: 5px 10px;
			font-weight: bold;
			word-break: break-word;
		}

		.more {
			display: flex;
			justify-content: space-between;
			color: #9499aa;
			margin-bottom: 6rpx;
			font-size: 26rpx;
		}

		.action-buttons {
			display: flex;
			justify-content: space-between;
			padding: 5rpx 16rpx 16rpx; /* 按钮的上下内边距 */
			background-color: @white-color;
	
			button {
				flex: 1; /* 按钮等宽 */
				margin: 0 8rpx; /* 按钮间的间距 */
				padding: 10rpx 0; /* 按钮内部间距 */
				font-size: 22rpx;
				border: none;
				border-radius: 8rpx;
				text-align: center;
				cursor: pointer;
				color: @white-color;
				transition: opacity 0.2s;
			}
	
			/* 编辑按钮样式 */
			.edit-button {
				background-color: @secondary-color;
			}
	
			/* 删除按钮样式 */
			.delete-button {
				background-color: @danger-color;
			}
	
			/* 按钮点击效果 */
			button:active {
				opacity: 0.8;
			}
		}
	}

	/* 多行省略 */
	.multi-line-omit {
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	/* 单行省略 */
	.one-line-omit {
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>
