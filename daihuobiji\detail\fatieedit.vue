<template>
  <view>
    <block v-if="isload">
      <form @submit="formsubmit">
        <view class="st_box">
          <!-- 标题栏 -->
          <view class="st_title flex-y-center"
            style="position: sticky;top: 0rpx;background-color: #fff;z-index: 3;justify-content: space-between;">
            <view @tap="goback" style="width:100rpx">
              <image :src="pre_url+'/static/img/goback.jpg'"></image>
            </view>
            <view>
              <image :src="pre_url+'/static/icon/warning.png'" style="width: 40rpx;" mode="widthFix"></image>
            </view>
          </view>
          <!-- 表单内容 -->
          <view class="st_form">
            <!-- 图片列表 -->
            <view class="flex" style="flex-wrap:wrap;">
              <view v-for="(item, index) in pics" :key="index" class="layui-imgbox">
                <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pics">
                  <image :src="pre_url+'/static/img/ico-del.png'"></image>
                </view>
                <view class="layui-imgbox-img">
                  <image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image>
                </view>
              </view>
              <!-- 上传按钮 -->
              <view class="uploadbtn"
                :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 75rpx;background-size:50rpx 50rpx;background-color:#F3F3F3;'"
                @tap="uploadimg" v-if="pics.length<9"  >
              </view>
            </view>

            <!-- 标题输入框 -->
            <view style="margin: 20rpx 0;">
              <input placeholder="填写标题会有更多赞哦~" name="title" style="height: 100rpx;" v-model="title"></input>
            </view>

            <!-- 正文输入框 -->
            <view style="border-bottom: 1px solid #EEEEEE;padding-bottom: 20rpx;">
              <textarea placeholder="添加正文" class="feedback-textarea" name="content"
                maxlength="-1" v-model="content"></textarea>
              <view>
                <!-- 标签选择 -->
                <scroll-view scroll-x="true" style="white-space: nowrap; width: 100%;">
                  <view class="tag-box"
                    :style="{
                      'display': 'inline-block',
                      'color': cindex === index ? '#fff' : '#999999',
                      'background': cindex === index ? t('color1') : 'transparent'
                    }"
                    @click="cateChange(index)"
                    v-for="(item, index) in cateArr"
                    :key="index">
                    {{ item }}
                  </view>
                </scroll-view>
              </view>
            </view>

            <!-- 隐藏的字段 -->
            <input type="text" hidden="true" name="pics" :value="pics" maxlength="-1"></input>
            <input type="text" hidden="true" name="video" :value="video" maxlength="-1"></input>

            <!-- 选择商品 -->
            <view class="picker-container" >
                <view class="picker-label" @tap="openShop">
                    <image :src="pre_url+'/static/img/shortvideo_cart.png'" style="width:26rpx;margin-right: 10rpx;"
                        mode="widthFix"></image>
                    {{selectedObj.name ? selectedObj.name : '选择商品' }}
                </view>
                <view class="picker-text" @tap="selectProduct">
                    {{ selectedObj.selectedItems.length > 0 ? `${selectedObj.selectedItems.length}件商品` : '请选择商品' }}
                </view>
                <image :src="pre_url+'/static/icon/right.png'" style="width:26rpx" mode="widthFix" @tap="selectProduct"></image>
            </view>
          </view>
        </view>
        <!-- 底部按钮 -->
        <view class="st_title flex-y-center" :style="{ paddingBottom: (safeBottomHeight + 80) + 'rpx' }">
          <button form-type="submit" :style="'background:'+t('color1')">保存修改</button>
        </view>
        <view style="width:100%;height:50rpx"></view>
      </form>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>

    <!-- 视频预览 兼容H5-->
    <uni-popup ref="videoShow" :mask-click="false">
        <view class="viewVideo">
            <!-- 兼容H5 -->
            <view class="close" @click="fullscreenchange"></view>
            <video id="myVideo" :src="cVideo" object-fit='contain' autoplay="false" :controls="true"
                show-fullscreen-btn="false" play-btn-position="center" show-loading='true'
                @fullscreenchange="fullscreenchange"></video>
        </view>
    </uni-popup>
			
    <!-- 商品购物 -->
    <uni-popup ref="shopShow" type="bottom">
        <view class="viewShop">
            <view style="text-align: center;font-weight: bold;margin-bottom: 20rpx;position: sticky;top: 0;">
                文中提到的商品（{{selectedObj.matchedData.length}}）
            </view>
    
            <view class="cart-container">
                <scroll-view :scroll-top="0" scroll-y="true" style="height: 50vh;">
                    <view class="cart-item" v-for="(item, index) in selectedObj.matchedData" :key="index" @click="goto" :data-url="'/shopPackage/shop/product?id='+item.id">
                        <view class="image-box">
                            <image class="item-image" :src="item.pic" mode="heightFix"></image>
                        </view>
        
                        <view class="item-info">
                            <text class="item-name">{{ item.name }}</text>
                            <view style="display: flex;justify-content: space-between;width: 100%;">
                                <text class="item-price">
                                    <span>￥</span>
                                    <span style="font-size: 34rpx;font-weight: bold;">{{ item.sell_price }}</span>
                                </text>
                            </view>
                        </view>
                    </view>
                </scroll-view>
    
                <view style="position: sticky;bottom: 0;
                height: 100rpx;display: flex;justify-content: space-between;
                align-items: center;padding: 0 20rpx;">
                    <view @tap="shopAllSelectedClick" style="visibility: hidden;">
                        <checkbox :checked="shopAllSelected" style="transform:scale(0.8)"></checkbox>
                        全选
                    </view>
                    <view class="shopButtonActive" @click="closePop">
                        点击关闭
                    </view>
                </view>
            </view>
        </view>
    </uni-popup>
  </view>
</template>

<script>
  var app = getApp();

  export default {
    data() {
      return {
        opt: {},
        loading: false,
        isload: false,
        menuindex: -1,
        selectedObj: {
          name: '',
          selectedItems: [], // ids
          matchedData: [] // 商品列表
        },
        pre_url: app.globalData.pre_url,
        cateArr: [],
        cindex: -1,
        pics: [],
        video: '',
        safeBottomHeight: 100,
        content: '',
        title: '',
        clist: [],
        id: null,
        cVideo: '',
        shopAllSelected: false
      };
    },

    onLoad: function(opt) {
      const systemInfo = wx.getSystemInfoSync();
      this.safeBottomHeight = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0;
      this.opt = app.getopts(opt);
      this.id = this.opt.id || null; // 获取笔记ID
      if (!this.id) {
        app.alert('缺少笔记ID');
        return;
      }
      this.getdata();
    },
    methods: {
      getdata: function() {
        var that = this;
        that.loading = true;
        // 获取分类列表
        app.get('Apidaihuobiji/fatie', {}, function(res) {
          if (res.status == 0) {
            that.loading = false;
            app.alert(res.msg);
            return;
          }
          that.clist = res.clist;
          var clist = res.clist;
          if (clist.length > 0) {
            var cateArr = [];
            for (var i in clist) {
              cateArr.push(clist[i].name);
            }
            that.cateArr = cateArr;
          } else {
            cateArr = false;
          }
          // 获取笔记详情
          that.getNoteDetail();
        });
      },

      // 获取笔记详情的方法
      getNoteDetail: function() {
        var that = this;
        app.post('Apidaihuobiji/detail', { id: that.id }, function(res) {
          that.loading = false;
          if (res.status == 1) {
            var data = res.detail;
            that.title = data.title || '';
            that.content = data.content || '';
            that.pics = data.pics || [];
            that.video = data.video || '';
            // 设置分类索引
            var cid = data.cid;
            for (var i = 0; i < that.clist.length; i++) {
              if (that.clist[i].id == cid) {
                that.cindex = i;
                break;
              }
            }
            that.isload = true;
          } else {
            app.error(res.msg);
          }
        });
      },

      cateChange: function(index) {
        this.cindex = index;
      },
      formsubmit: async function(e) {
        var that = this;
        var clist = that.clist;
        if (clist.length > 0) {
          if (that.cindex == -1) {
            app.error('请选择分类');
            return false;
          }
          var cid = clist[that.cindex].id;
        } else {
          var cid = 0;
        }
        var content = this.content;
        var title = this.title;
        var pics = [];
        var video = this.video;
        if (content == '' && pics == '') {
          app.error('请输入内容');
          return false;
        }
        console.log('pics', this.pics);
        for (var i = 0; i < this.pics.length; i++) {
          if (this.pics[i].startsWith('http')) {
            // 已经上传的图片，不需要再次上传
            pics.push(this.pics[i]);
          } else {
            app.showLoading('上传中');
            const res = await uni.uploadFile({
              url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app
                .globalData.aid + '/platform/' + app.globalData.platform +
                '/session_id/' +
                app.globalData.session_id,
              filePath: this.pics[i],
              name: 'file'
            });
            app.showLoading(false);
            let data = null
            if (Array.isArray(res)) {
              data = res[1]
            } else {
              data = res
            }
            data = JSON.parse(data.data);

            if (data.status == 1) {
              pics.push(data.url);
            } else {
              app.alert(data.msg);
            }
          }
        }

        pics = pics.join(',');

        // 编辑笔记
        var postData = {
          id: that.id,
          cid: cid,
          pics: pics,
          content: content,
          video: video,
          title: title
        };

        const goodid = that.selectedObj.selectedItems.join(',');
        app.post('Apidaihuobiji/editNote', {
          id: that.id,
          cid: cid,
          pics: pics,
          content: content,
          video: video,
          title: title,
          goodid: goodid
        }, function(res) {
          app.showLoading(false);
          if (res.status == 1) {
            app.success(res.msg);
            setTimeout(function() {
              uni.navigateBack();
            }, 1000);
          } else {
            app.error(res.msg);
          }
        });
      },

      uploadimg: function() {
        var that = this;
        app.chooseImage(function(urls) {
          for (var i = 0; i < urls.length; i++) {
            that.pics.push(urls[i]);
          }
        }, 9 - that.pics.length);
      },

      uploadvideo: function() {
        var that = this;
        uni.chooseVideo({
          sourceType: ['album', 'camera'],
          maxDuration: 60,
          success: function(res) {
            var tempFilePath = res.tempFilePath;
            app.showLoading('上传中');
            uni.uploadFile({
              url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app
                .globalData.aid + '/platform/' + app.globalData.platform +
                '/session_id/' + app.globalData.session_id,
              filePath: tempFilePath,
              name: 'file',
              success: function(res) {
                app.showLoading(false);
                var data = JSON.parse(res.data);

                if (data.status == 1) {
                  that.video = data.url;
                } else {
                  app.alert(data.msg);
                }
              },
              fail: function(res) {
                app.showLoading(false);
                app.alert(res.errMsg);
              }
            });
          },
          fail: function(res) {
            console.log(res);
          }
        });
      },
      removeimg: function(e) {
        var that = this;
        var index = e.currentTarget.dataset.index
        that.pics.splice(index, 1)
      },
      goback: function() {
        uni.navigateBack();
      },

      // 新增商品相关方法
      async getDetail() {
        let that = this;
        // 判断商品ID是否存在并且不为空
        if (that.opt.goodid) {
          let arr = that.opt.goodid.split(',')
          let names = []
          that.selectedObj.selectedItems = that.opt.goodid.split(',')
          for (let item of arr) {
            const name = await that.getProduct(item)
            names.push(name)
          }
          that.selectedObj.name = names.join(',')
        }
      },

      getProduct(id) {
        return new Promise((resolve, reject) => {
          app.get('ApiShop/product', {
            id: id
          }, function(res) {
            if (res && res.product) {
              resolve(res.product.name)
            } else {
              resolve('')
            }
          });
        });
      },
      
      closePop(){
        this.$refs.shopShow.close()
      },

      selectProduct() {
        const that = this;
        uni.navigateTo({
          url: '/daihuobiji/detail/selectgoods',
          events: {
            selectedGoods: function(data) {
              that.selectedObj.name = data.matchedData.map(item => item.name).join(',');
              that.selectedObj.selectedItems = data.selectedItems;
              that.selectedObj.matchedData = data.matchedData
            }
          },
          success: function(res) {}
        });
      },

      openShop() {
        this.$refs.shopShow.open('top')
      },

      // 视频相关方法
      close() {
        this.$refs.videoShow.close();
      },
      
      previewVideo(url) {
        this.cVideo = url;
        this.$refs.videoShow.open('top');
        this.videoContext = uni.createVideoContext('myVideo', this);
        this.videoContext.play();
      },
      
      fullscreenchange() {
        this.cVideo = '';
        this.videoContext.pause();
        this.close();
      },

      // 修改表单提交方法,添加商品ID
      formsubmit: async function(e) {
        var that = this;
        var clist = that.clist;
        if (clist.length > 0) {
          if (that.cindex == -1) {
            app.error('请选择分类');
            return false;
          }
          var cid = clist[that.cindex].id;
        } else {
          var cid = 0;
        }
        var content = this.content;
        var title = this.title;
        var pics = [];
        var video = this.video;
        if (content == '' && pics == '') {
          app.error('请输入内容');
          return false;
        }
        console.log('pics', this.pics);
        for (var i = 0; i < this.pics.length; i++) {
          if (this.pics[i].startsWith('http')) {
            // 已经上传的图片，不需要再次上传
            pics.push(this.pics[i]);
          } else {
            app.showLoading('上传中');
            const res = await uni.uploadFile({
              url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app
                .globalData.aid + '/platform/' + app.globalData.platform +
                '/session_id/' +
                app.globalData.session_id,
              filePath: this.pics[i],
              name: 'file'
            });
            app.showLoading(false);
            let data = null
            if (Array.isArray(res)) {
              data = res[1]
            } else {
              data = res
            }
            data = JSON.parse(data.data);

            if (data.status == 1) {
              pics.push(data.url);
            } else {
              app.alert(data.msg);
            }
          }
        }

        pics = pics.join(',');

        // 编辑笔记
        var postData = {
          id: that.id,
          cid: cid,
          pics: pics,
          content: content,
          video: video,
          title: title
        };

        const goodid = that.selectedObj.selectedItems.join(',');
        app.post('Apidaihuobiji/editNote', {
          id: that.id,
          cid: cid,
          pics: pics,
          content: content,
          video: video,
          title: title,
          goodid: goodid
        }, function(res) {
          app.showLoading(false);
          if (res.status == 1) {
            app.success(res.msg);
            setTimeout(function() {
              uni.navigateBack();
            }, 1000);
          } else {
            app.error(res.msg);
          }
        });
      }
    }
  };
</script>

<style>
	page {
		background: #ffffff
	}

	.st_box {
		padding: 20rpx 0;
		padding-top: 0;
	}

	.st_title {
		display: flex;
		justify-content: space-between;
		padding: 24rpx;
	}

	.st_title1 {
		display: flex;
		justify-content: space-between;
		padding: 24rpx;
		border-bottom: 1px solid #D0D0D0
	}

	.st_title image {
		width: 18rpx;
		height: 32rpx
	}

	.st_title text {
		color: #242424;
		font-size: 36rpx
	}

	/* .st_title button{ background: #31C88E; border-radius:6rpx; line-height: 48rpx;border: none; padding:0 20rpx ;color:#fff;margin:0} */

	.picker-container {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #eeeeee;
		padding: 40rpx 0;
		/* margin-top: 20rpx; */
	}

	.picker-label {
		font-size: 26rpx;
		color: #000000;
		flex: 1;
		display: flex;
		align-items: center;
	}

	.picker {
		flex: 2;
		text-align: right;
	}

	.picker-text {
		font-size: 26rpx;
		color: #333;
	}

	.st_title button {
		background: rgb(255, 36, 66);
		border-radius: 70rpx;
		line-height: 40rpx;
		border: none;
		padding: 0 30rpx;
		color: #fff;
		font-size: 28rpx;
		text-align: center;
		/* margin: 0; */
		width: 90%;
		display: flex;
		height: 80rpx;
		justify-content: center;
		align-items: center;
	}



	.st_form {
		background: #ffffff;
		margin: 10px;
	}

	.st_form input {
		width: 100%;
		height: 120rpx;
		border: none;
		border-bottom: 1px solid #EEEEEE;
	}

	.st_form input::-webkit-input-placeholder {
		/* WebKit browsers */
		color: rgb(214, 214, 214);
		font-size: 24rpx;
		font-weight: 300;
	}

	.st_form textarea {
		width: 100%;
		min-height: 200rpx;
		padding: 20rpx 0;
		padding-top: 10rpx;
		border: none;
	}

	.layui-imgbox {
		margin-right: 16rpx;
		margin-bottom: 10rpx;
		font-size: 24rpx;
		position: relative;
	}

	.layui-imgbox-close {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		right: -16rpx;
		top: -16rpx;
		z-index: 90;
		color: #999;
		font-size: 32rpx;
		background: #fff
	}

	.layui-imgbox-close image {
		width: 100%;
		height: 100%
	}

	.layui-imgbox-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		padding: 2px;
		border: #d3d3d3 1px solid;
		background-color: #f6f6f6;
		overflow: hidden;
		border-radius: 20rpx;
	}

	.layui-imgbox-img>image {
		max-width: 100%;
	}

	.layui-imgbox-repeat {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		line-height: 28rpx;
		right: 2px;
		bottom: 2px;
		color: #999;
		font-size: 30rpx;
		background: #fff
	}

	.uploadbtn {
		position: relative;
		height: 200rpx;
		width: 200rpx;
		border-radius: 20rpx;
	}

	.uploadbtn_ziti1 {
		height: 30rpx;
		line-height: 30rpx;
		font-size: 30rpx;
		margin-top: 20rpx;
	}

	.uploadbtn_ziti2 {
		height: 30rpx;
		line-height: 30rpx;
		font-size: 30rpx;
		padding-top: 20rpx;
		margin-top: 20rpx;
		border-top: 1px solid #EEEEEE;
	}

	.uploadbtn {
		position: relative;
		height: 200rpx;
		width: 200rpx
    }

	.feedback-textarea {
		font-size: 28rpx;
		width: 100%;
		font-weight: 350;
		/* color: rgb(199, 199, 199); */
	}

	.tag-box {
		background-color: #ededed;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		/* font-size: 18rpx; */
		margin-right: 15rpx;
	}

	.icon-box {
		width: 50rpx;
		height: 50rpx;
		text-align: center;
		line-height: 50rpx;
		background-color: #ededed;
		border-radius: 50rpx;
	}

	.checkbox-box ::v-deep .uni-checkbox-input {
		border-radius: 30rpx;
		margin: 0;
	}

	/* 添加商品相关样式 */
	.viewShop {
		height: 60vh;
		width: 100%;
		border-radius: 20rpx 20rpx 0 0;
		background-color: #fff;
		padding-top: 20rpx;
	}

	.cart-container {
		display: flex;
		flex-direction: column;
	}

	.cart-item {
		display: flex;
		align-items: center;
		padding: 10px;
	}

	.image-box {
		background-color: #f4f4f4;
		border-radius: 10rpx;
		width: 130rpx;
		height: 130rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.item-image {
		height: 70%;
	}

	.item-info {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 130rpx;
		width: 67%;
	}

	.item-name {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}

	.item-price {
		font-size: 14px;
		color: #e65602;
	}

	.shopButtonActive {
		border-radius: 50rpx;
		padding: 16rpx 60rpx;
		color: #fff;
		background-color: #eb8200;
		font-size: 20rpx;
	}
</style>
