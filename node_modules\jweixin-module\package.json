{"_from": "jweixin-module", "_id": "jweixin-module@1.6.0", "_inBundle": false, "_integrity": "sha512-dGk9cf+ipipHmtzYmKZs5B2toX+p4hLyllGLF6xuC8t+B05oYxd8fYoaRz0T30U2n3RUv8a4iwvjhA+OcYz52w==", "_location": "/jweixin-module", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "jweixin-module", "name": "jweixin-module", "escapedName": "jweixin-module", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/jweixin-module/-/jweixin-module-1.6.0.tgz", "_shasum": "4a7ea614083e3c9c3f49e2fdc2bb882cfa58dfcd", "_spec": "jweixin-module", "_where": "E:\\_web\\www.diandashop.com\\ddshop2\\uniapp", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/zhetengbiji/jweixin-module/issues"}, "bundleDependencies": false, "deprecated": false, "description": "微信JS-SDK", "devDependencies": {}, "homepage": "https://github.com/zhetengbiji/jweixin-module#readme", "keywords": ["wxjssdk", "weixin", "j<PERSON><PERSON>", "wechat", "jssdk", "wx"], "license": "ISC", "main": "lib/index.js", "name": "jweixin-module", "repository": {"type": "git", "url": "git+https://github.com/zhetengbiji/jweixin-module.git"}, "scripts": {}, "version": "1.6.0"}