<template>
  <view class="fenhong-dian-page">
    <!-- 分红点功能未开启提示 -->
    <view v-if="!showSwitch" class="disabled-message">
      <view class="disabled-icon">🚫</view>
      <view class="disabled-text">分红点功能暂未开启</view>
      <view class="disabled-desc">请联系管理员开启分红点功能</view>
    </view>

    <!-- 分红点主界面 -->
    <view v-else class="main-content">
      <!-- 顶部卡片 -->
      <view class="header-card">
        <view class="user-info">
          <image :src="userInfo.headimg || '/static/default-avatar.png'" class="avatar"></image>
          <view class="user-name">{{ userInfo.nickname || '未知用户' }}</view>
        </view>
        <view class="points-info">
          <view class="points-number">{{ fenhongNum }}</view>
          <view class="points-label">我的分红点</view>
        </view>
      </view>

      <!-- 统计卡片 -->
      <view class="stats-cards">
        <view class="stat-card">
          <view class="stat-number">{{ stats.my_today_new || 0 }}</view>
          <view class="stat-label">今日新增</view>
        </view>
        <view class="stat-card">
          <view class="stat-number">{{ stats.my_month_new || 0 }}</view>
          <view class="stat-label">本月新增</view>
        </view>
        <view class="stat-card">
          <view class="stat-number">{{ myRank || '未上榜' }}</view>
          <view class="stat-label">我的排名</view>
        </view>
      </view>

      <!-- 功能导航 -->
      <view class="nav-tabs">
        <view 
          v-for="tab in tabs" 
          :key="tab.key"
          :class="['tab-item', { active: activeTab === tab.key }]"
          @tap="switchTab"
          :data-key="tab.key"
        >
          {{ tab.name }}
        </view>
      </view>

      <!-- 分红记录 -->
      <view v-if="activeTab === 'logs'" class="tab-content">
        <view class="filter-bar">
          <view class="filter-item">
            <picker @change="onFilterChange" :value="filterIndex" :range="filterOptions">
              <view class="picker">{{ filterOptions[filterIndex] }}</view>
            </picker>
          </view>
          <view class="refresh-btn" @tap="refreshLogs">
            <text class="refresh-icon">🔄</text>
          </view>
        </view>

        <view class="log-list">
          <view v-if="logs.length === 0 && !loading" class="empty-state">
            <view class="empty-icon">📝</view>
            <view class="empty-text">暂无记录</view>
          </view>
          
          <view v-for="log in logs" :key="log.id" class="log-item">
            <view class="log-content">
              <view class="log-header">
                <text class="log-type" :class="log.type">{{ log.type_text }}</text>
                <text class="log-time">{{ log.createtime_text }}</text>
              </view>
              <view class="log-detail">
                <view class="log-remark">{{ log.remark || '系统操作' }}</view>
                <view class="log-points" :class="log.type">{{ log.points_text }}</view>
              </view>
            </view>
          </view>
          
          <view v-if="loading" class="loading-more">
            <view class="loading-spinner"></view>
            <text>加载中...</text>
          </view>
          
          <view v-if="!hasMore && logs.length > 0" class="no-more">没有更多数据了</view>
        </view>
      </view>

      <!-- 分红点奖励记录 -->
      <view v-if="activeTab === 'pointRewards'" class="tab-content">
        <view class="point-reward-list">
          <view v-if="pointRewardLogs.length === 0 && !pointRewardLoading" class="empty-state">
            <view class="empty-icon">🎁</view>
            <view class="empty-text">暂无奖励记录</view>
            <view class="empty-desc">完成订单后会获得分红点奖励</view>
          </view>
          
          <view v-for="reward in pointRewardLogs" :key="reward.id" class="point-reward-card">
            <view class="point-reward-header">
              <view class="reward-badge" :class="{ 'self-order': reward.is_self_order, 'team-order': !reward.is_self_order }">
                <text class="badge-icon">{{ reward.is_self_order ? '🛒' : '👥' }}</text>
                <text class="badge-text">{{ reward.order_source }}</text>
              </view>
              <text class="reward-time">{{ reward.createtime_text }}</text>
            </view>
            
            <view class="point-reward-main">
              <view class="order-section">
                <view class="order-info">
                  <view class="order-number">订单号：{{ reward.order_num }}</view>
                  <view class="order-member">下单人：{{ reward.order_member_name }}</view>
                </view>
              </view>
              <view class="points-section">
                <view class="points-amount">{{ reward.reward_points_text }}</view>
                <view class="points-label">获得分红点</view>
              </view>
            </view>
            
            <view class="point-reward-details">
              <view class="detail-row">
                <view class="detail-label">订单金额</view>
                <view class="detail-value">{{ reward.order_amount_text }}</view>
              </view>
              <view class="detail-row">
                <view class="detail-label">计算方式</view>
                <view class="detail-value">{{ reward.calc_type_text }}</view>
              </view>
            </view>
          </view>
          
          <view v-if="pointRewardLoading" class="loading-more">
            <view class="loading-spinner"></view>
            <text>加载中...</text>
          </view>
          
          <view v-if="!pointRewardHasMore && pointRewardLogs.length > 0" class="no-more">没有更多数据了</view>
        </view>
      </view>

      <!-- 现金分红 -->
      <view v-if="activeTab === 'rewards'" class="tab-content">
        <view class="reward-list">
          <view v-if="rewardLogs.length === 0 && !rewardLoading" class="empty-state">
            <view class="empty-icon">💰</view>
            <view class="empty-text">暂无分红记录</view>
            <view class="empty-desc">当有分红发放时会在这里显示</view>
          </view>
          
          <view v-for="reward in rewardLogs" :key="reward.id" class="reward-card">
            <view class="reward-header">
              <view class="reward-badge">
                <text class="badge-icon">💰</text>
                <text class="badge-text">现金分红</text>
              </view>
              <text class="reward-time">{{ reward.createtime_text }}</text>
            </view>
            
            <view class="reward-main">
              <view class="amount-section">
                <view class="main-amount">{{ reward.amount_text }}</view>
                <view class="amount-label">本次分红</view>
              </view>
            </view>
            
            <view class="reward-details">
              <view class="detail-row">
                <view class="detail-label">累计分红</view>
                <view class="detail-value primary">{{ reward.total_received_text }}</view>
              </view>
              <view class="detail-row">
                <view class="detail-label">有效分红点</view>
                <view class="detail-value" :class="{ 'warning': reward.valid_points <= 0 }">
                  {{ reward.valid_points_text }}
                </view>
              </view>
              <view class="detail-row">
                <view class="detail-label">剩余可分红</view>
                <view class="detail-value" :class="{ 'success': reward.remaining_amount > 0, 'danger': reward.remaining_amount <= 0 }">
                  {{ reward.remaining_amount_text }}
                </view>
              </view>
            </view>
            
            <view v-if="reward.remark && reward.remark !== '分红发放'" class="reward-remark">
              <text class="remark-label">备注：</text>
              <text class="remark-text">{{ reward.remark }}</text>
            </view>
          </view>
          
          <view v-if="rewardLoading" class="loading-more">
            <view class="loading-spinner"></view>
            <text>加载中...</text>
          </view>
          
          <view v-if="!rewardHasMore && rewardLogs.length > 0" class="no-more">没有更多数据了</view>
        </view>
      </view>

      <!-- 排行榜 -->
      <view v-if="activeTab === 'rank'" class="tab-content">
        <view class="rank-list">
          <view v-if="rankList.length === 0 && !rankLoading" class="empty-state">
            <view class="empty-icon">🏅</view>
            <view class="empty-text">暂无排行数据</view>
          </view>
          
          <view v-for="(item, index) in rankList" :key="item.id" class="rank-item" :class="{ 'is-self': item.is_self }">
            <view class="rank-number" :class="{ 'gold': item.rank === 1, 'silver': item.rank === 2, 'bronze': item.rank === 3 }">
              {{ item.rank }}
            </view>
            <view class="rank-user">
              <image :src="item.headimg || '/static/default-avatar.png'" class="rank-avatar"></image>
              <view class="rank-name">{{ item.nickname }}</view>
            </view>
            <view class="rank-points">{{ item.fenhong_num }}</view>
          </view>
          
          <view v-if="rankLoading" class="loading-more">
            <view class="loading-spinner"></view>
            <text>加载中...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 下拉刷新 -->
    <view v-if="refreshing" class="refresh-indicator">
      <view class="refresh-spinner"></view>
      <text>刷新中...</text>
    </view>
    
    <!-- 加载组件 -->
    <loading v-if="loading"></loading>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      // 基础数据
      showSwitch: false,
      fenhongNum: 0,
      userInfo: {},
      stats: {},
      myRank: 0,
      
      // 标签页
      activeTab: 'logs',
      tabs: [
        { key: 'logs', name: '点数记录' },
        { key: 'pointRewards', name: '点数奖励' },
        { key: 'rewards', name: '现金分红' },
        { key: 'rank', name: '排行榜' }
      ],
      
      // 筛选
      filterIndex: 0,
      filterOptions: ['全部记录', '增加记录', '减少记录'],
      filterValues: ['', 'add', 'sub'],
      
      // 分红记录
      logs: [],
      page: 1,
      limit: 10,
      loading: false,
      hasMore: true,
      
      // 分红点奖励记录
      pointRewardLogs: [],
      pointRewardPage: 1,
      pointRewardLoading: false,
      pointRewardHasMore: true,
      
      // 现金分红记录
      rewardLogs: [],
      rewardPage: 1,
      rewardLoading: false,
      rewardHasMore: true,
      
      // 排行榜
      rankList: [],
      rankPage: 1,
      rankLoading: false,
      rankHasMore: true,
      
      // 刷新状态
      refreshing: false,
    };
  },
  
  onLoad: function() {
    this.initData();
  },

  onPullDownRefresh: function() {
    this.initData();
  },

  onReachBottom: function() {
    this.onScrollToLower();
  },

  methods: {
    // 初始化数据
    initData: function() {
      var that = this;
      that.refreshing = true;
      that.getData();
    },

    // 获取基础数据
    getData: function() {
      var that = this;
      that.loading = true;
      
      app.post('ApiFenhongdian/getData', {}, function(res) {
        that.loading = false;
        that.refreshing = false;
        
        if (res.code === 1) {
          that.showSwitch = res.data.show_switch;
          that.fenhongNum = res.data.fenhong_num;
          that.userInfo = {
            nickname: res.data.nickname,
            headimg: res.data.headimg
          };
          
          // 功能开启后再获取其他数据
          if (that.showSwitch) {
            that.getStats();
            that.getLogs();
            that.getRankData();
          }
        } else {
          app.alert(res.msg || '获取数据失败');
        }
      });
    },

    // 获取统计数据
    getStats: function() {
      var that = this;
      
      app.post('ApiFenhongdian/getStats', {}, function(res) {
        if (res.status === 1) {
          that.stats = res.data;
        }
      });
    },

    // 获取分红记录
    getLogs: function(isLoadMore) {
      var that = this;
      isLoadMore = isLoadMore || false;
      
      if (that.loading || (!isLoadMore && !that.hasMore && that.logs.length > 0)) return;
      
      that.loading = true;
      
      var params = { 
        page: isLoadMore ? that.page : 1, 
        limit: that.limit 
      };
      
      var filterValue = that.filterValues[that.filterIndex];
      if (filterValue) {
        params.type = filterValue;
      }
      
      app.post('ApiFenhongdian/getLogs', params, function(res) {
        that.loading = false;
        
        if (res.code === 1) {
          if (isLoadMore) {
            that.logs = that.logs.concat(res.data);
          } else {
            that.logs = res.data;
            that.page = 1;
          }
          
          that.hasMore = res.data.length === that.limit;
          if (isLoadMore) that.page++;
        } else {
          app.alert(res.msg || '获取记录失败');
        }
      });
    },

    // 获取奖励记录
    getRewardLogs: function(isLoadMore) {
      var that = this;
      isLoadMore = isLoadMore || false;
      
      if (that.rewardLoading || (!isLoadMore && !that.rewardHasMore && that.rewardLogs.length > 0)) return;
      
      that.rewardLoading = true;
      
      var params = { 
        page: isLoadMore ? that.rewardPage : 1, 
        limit: that.limit 
      };
      
      app.post('ApiFenhongdian/getRewardLogs', params, function(res) {
        that.rewardLoading = false;
        
        if (res.status === 1) {
          if (isLoadMore) {
            that.rewardLogs = that.rewardLogs.concat(res.data);
          } else {
            that.rewardLogs = res.data;
            that.rewardPage = 1;
          }
          
          that.rewardHasMore = res.data.length === that.limit;
          if (isLoadMore) that.rewardPage++;
        }
      });
    },

    // 获取分红点奖励记录
    getPointRewardLogs: function(isLoadMore) {
      var that = this;
      isLoadMore = isLoadMore || false;
      
      if (that.pointRewardLoading || (!isLoadMore && !that.pointRewardHasMore && that.pointRewardLogs.length > 0)) return;
      
      that.pointRewardLoading = true;
      
      var params = { 
        page: isLoadMore ? that.pointRewardPage : 1, 
        limit: that.limit 
      };
      
      app.post('ApiFenhongdian/getPointRewardLogs', params, function(res) {
        that.pointRewardLoading = false;
        
        if (res.status === 1) {
          if (isLoadMore) {
            that.pointRewardLogs = that.pointRewardLogs.concat(res.data);
          } else {
            that.pointRewardLogs = res.data;
            that.pointRewardPage = 1;
          }
          
          that.pointRewardHasMore = res.data.length === that.limit;
          if (isLoadMore) that.pointRewardPage++;
        } else {
          console.log('获取分红点奖励记录失败:', res.msg);
        }
      });
    },

    // 获取排行榜数据
    getRankData: function(isLoadMore) {
      var that = this;
      isLoadMore = isLoadMore || false;
      
      if (that.rankLoading || (!isLoadMore && !that.rankHasMore && that.rankList.length > 0)) return;
      
      that.rankLoading = true;
      
      var params = { 
        page: isLoadMore ? that.rankPage : 1, 
        limit: that.limit 
      };
      
      app.post('ApiFenhongdian/getFenhongDianRank', params, function(res) {
        that.rankLoading = false;
        
        if (res.status === 1) {
          if (isLoadMore) {
            that.rankList = that.rankList.concat(res.data);
          } else {
            that.rankList = res.data;
            that.rankPage = 1;
          }
          
          that.myRank = res.my_rank || 0;
          that.rankHasMore = res.data.length === that.limit;
          if (isLoadMore) that.rankPage++;
        }
      });
    },

    // 切换标签
    switchTab: function(e) {
      var tab = e.currentTarget.dataset.key;
      this.activeTab = tab;
      
      if (tab === 'pointRewards' && this.pointRewardLogs.length === 0) {
        this.getPointRewardLogs();
      } else if (tab === 'rewards' && this.rewardLogs.length === 0) {
        this.getRewardLogs();
      } else if (tab === 'rank' && this.rankList.length === 0) {
        this.getRankData();
      }
    },

    // 筛选器改变
    onFilterChange: function(e) {
      this.filterIndex = e.detail.value;
      this.filterLogs();
    },

    // 筛选日志
    filterLogs: function() {
      this.logs = [];
      this.page = 1;
      this.hasMore = true;
      this.getLogs();
    },

    // 刷新日志
    refreshLogs: function() {
      this.logs = [];
      this.page = 1;
      this.hasMore = true;
      this.getLogs();
    },

    // 滚动加载更多
    onScrollToLower: function() {
      if (this.activeTab === 'logs') {
        this.getLogs(true);
      } else if (this.activeTab === 'pointRewards') {
        this.getPointRewardLogs(true);
      } else if (this.activeTab === 'rewards') {
        this.getRewardLogs(true);
      } else if (this.activeTab === 'rank') {
        this.getRankData(true);
      }
    }
  }
};
</script>

<style scoped>
/* 页面整体背景和布局 */
.fenhong-dian-page {
  min-height: 100vh;
  background: #f4f6f8; /* 更柔和的背景色 */
  padding: 24rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 功能未开启提示 */
.disabled-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
  text-align: center;
  padding: 40rpx;
}

.disabled-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  color: #dcdcdc;
}

.disabled-text {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.disabled-desc {
  font-size: 28rpx;
  color: #999;
}

/* 主内容区 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx; /* 使用 gap 统一间距 */
}

/* 顶部卡片 */
.header-card {
  background: linear-gradient(135deg, #8e9eab 0%, #eef2f3 100%); /* 金属质感渐变 */
  border-radius: 32rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.points-info {
  text-align: right;
}

.points-number {
  font-size: 64rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.points-label {
  font-size: 26rpx;
  color: #555;
  margin-top: 8rpx;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.stat-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.stat-number {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #888;
  margin-top: 12rpx;
}

/* 导航标签 */
.nav-tabs {
  display: flex;
  background: #fff;
  border-radius: 40rpx; /* 圆润的胶囊形状 */
  padding: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx;
  border-radius: 32rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #666;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.tab-item.active {
  background: #333;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 标签内容 */
.tab-content {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  min-height: 700rpx;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker {
  padding: 12rpx 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  background: #f9f9f9;
}

.refresh-btn {
  padding: 12rpx;
  border-radius: 50%;
  background: #f0f0f0;
  color: #333;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-icon {
  font-size: 28rpx;
}

/* 列表样式 */
.log-list, .reward-list, .rank-list {
  padding: 0 32rpx;
  max-height: 900rpx;
  overflow-y: auto;
}

/* 记录项 */
.log-item, .reward-item, .rank-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.log-item:last-child, .reward-item:last-child, .rank-item:last-child {
  border-bottom: none;
}

.log-content, .reward-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-header, .reward-header {
  /* Combined into parent */
}

.log-type, .reward-title {
  font-size: 28rpx;
  font-weight: 500;
}

.log-type.add { color: #2ecc71; }
.log-type.sub { color: #e74c3c; }

.log-time, .reward-time {
  font-size: 24rpx;
  color: #999;
}

.log-detail, .reward-detail {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.log-remark, .reward-info {
  font-size: 28rpx;
  color: #333;
}

.fenhong-amount, .total-received, .valid-points, .remaining-amount {
  font-size: 22rpx;
  color: #888;
  margin-top: 4rpx;
}

.fenhong-amount {
  color: #2ecc71;
  font-weight: 500;
}

.remaining-amount.text-danger {
  color: #e74c3c;
}

.reward-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #2ecc71;
  text-align: right;
}

.reward-remark {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  padding-top: 8rpx;
  border-top: 1rpx solid #f0f0f0;
}

.order-info, .amount-info, .calc-info {
  font-size: 22rpx;
  color: #888;
  margin-top: 4rpx;
}

.log-points, .reward-points {
  font-size: 32rpx;
  font-weight: 600;
  text-align: right;
}

.log-points.add, .reward-points { color: #2ecc71; }
.log-points.sub { color: #e74c3c; }

/* 排行榜项 */
.rank-item {
  gap: 24rpx;
}

.rank-item.is-self {
  background: #f4f6f8;
  border-radius: 16rpx;
  margin: 16rpx -32rpx; /* Extend background */
  padding: 32rpx;
}

.rank-number {
  width: 64rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #888;
  text-align: center;
}

.rank-number.gold, .rank-number.silver, .rank-number.bronze {
  font-size: 48rpx;
}
.rank-number.gold::before { content: '🥇'; }
.rank-number.silver::before { content: '🥈'; }
.rank-number.bronze::before { content: '🥉'; }

.rank-user {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.rank-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.rank-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.rank-points {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.rank-item.is-self .rank-name, .rank-item.is-self .rank-points {
  font-weight: 700;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.empty-desc {
  font-size: 24rpx;
  color: #bbb;
  margin-top: 16rpx;
}

/* 加载状态 */
.loading-more, .no-more {
  text-align: center;
  padding: 32rpx;
  color: #aaa;
  font-size: 26rpx;
}

.loading-spinner, .refresh-spinner {
  display: inline-block;
  width: 28rpx;
  height: 28rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-left-color: #333;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-right: 12rpx;
  vertical-align: middle;
}

.refresh-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  z-index: 1000;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

/* 现金分红卡片样式 */
.reward-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.reward-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reward-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}

.badge-icon {
  font-size: 28rpx;
}

.reward-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.amount-section {
  text-align: right;
}

.main-amount {
  font-size: 48rpx;
  font-weight: 700;
  color: #2ecc71;
  line-height: 1;
}

.amount-label {
  font-size: 24rpx;
  color: #888;
  margin-top: 4rpx;
}

.reward-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #555;
  font-weight: 500;
}

.detail-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.detail-value.primary {
  color: #2ecc71;
}

.detail-value.success {
  color: #27ae60;
}

.detail-value.warning {
  color: #f39c12;
}

.detail-value.danger {
  color: #e74c3c;
}

.reward-remark {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-top: 8rpx;
  border-left: 4rpx solid #3498db;
}

.remark-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.remark-text {
  font-size: 24rpx;
  color: #333;
}

/* 优化列表样式 */
.reward-list {
  padding: 24rpx;
  max-height: 900rpx;
  overflow-y: auto;
}

.point-reward-list {
  padding: 24rpx;
  max-height: 900rpx;
  overflow-y: auto;
}

.reward-card:last-child {
  margin-bottom: 0;
}

/* 添加悬浮效果 */
.reward-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 分红点奖励卡片样式 */
.point-reward-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.point-reward-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reward-badge.self-order {
  background: #e0f7fa;
  border: 1rpx solid #4db6ac;
  color: #00796b;
}

.reward-badge.team-order {
  background: #e8f5e9;
  border: 1rpx solid #66bb6a;
  color: #2e7d32;
}

.point-reward-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-section {
  text-align: left;
}

.order-info {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.order-number {
  font-weight: 500;
}

.order-member {
  font-size: 24rpx;
  color: #666;
}

.points-section {
  text-align: right;
}

.points-amount {
  font-size: 48rpx;
  font-weight: 700;
  color: #2ecc71;
  line-height: 1;
}

.points-label {
  font-size: 24rpx;
  color: #888;
  margin-top: 4rpx;
}

.point-reward-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #555;
  font-weight: 500;
}

.detail-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.point-reward-card:last-child {
  margin-bottom: 0;
}

.point-reward-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
