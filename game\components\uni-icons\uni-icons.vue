<!-- uni-icons 组件 -->
<template>
	<text :style="{ color: color, 'font-size': iconSize }" class="uni-icons" :class="[customPrefix,customPrefix?type:'']" @click="_onClick">{{unicode}}</text>
</template>

<script>
import icons from './icons.js'
const getVal = (val) => {
	const reg = /^[0-9]*$/g
	return (typeof val === 'number' || reg.test(val)) ? val + 'px' : val;
}

// #ifdef APP-PLUS
// 获取原生对象
const dom = weex.requireModule('dom');
// #endif

export default {
	name: 'UniIcons',
	emits: ['click'],
	props: {
		type: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: '#333333'
		},
		size: {
			type: [Number, String],
			default: 16
		},
		customPrefix: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			icons: icons.glyphs
		}
	},
	computed: {
		unicode() {
			let code = this.icons.find(v => v.font_class === this.type)
			if (code) {
				return unescape(`%u${code.unicode}`)
			}
			return ''
		},
		iconSize() {
			return getVal(this.size)
		}
	},
	methods: {
		_onClick() {
			this.$emit('click')
		}
	}
}
</script>

<style>
/* 引入字体图标 */
@font-face {
	font-family: uniicons;
	src: url('./uniicons.ttf') format('truetype');
}

.uni-icons {
	font-family: uniicons;
	text-decoration: none;
	text-align: center;
}
</style> 