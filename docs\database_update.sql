-- 添加总部商品标识字段
ALTER TABLE `product` ADD COLUMN `is_headquarters` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为总部商品 0否 1是';

-- 添加商品来源字段
ALTER TABLE `product` ADD COLUMN `from_headquarters` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否来自总部商品 0否 1是';

-- 添加总部商品ID字段
ALTER TABLE `product` ADD COLUMN `headquarters_id` INT(11) NOT NULL DEFAULT 0 COMMENT '总部商品ID，仅当from_headquarters=1时有效';

-- 添加索引
ALTER TABLE `product` ADD INDEX `idx_headquarters` (`is_headquarters`);
ALTER TABLE `product` ADD INDEX `idx_from_headquarters` (`from_headquarters`);
ALTER TABLE `product` ADD INDEX `idx_headquarters_id` (`headquarters_id`); 