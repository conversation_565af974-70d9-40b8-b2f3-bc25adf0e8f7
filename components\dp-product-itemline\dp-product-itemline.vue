<template>
<view style="width:100%">
	<!-- 只在有背景图时显示背景 -->
	<view class="line-container" v-if="params.bgimg || params.main_title">
		<view class="line-bg" v-if="params.bgimg" :style="{
			backgroundImage: `url(${params.bgimg})`
		}"></view>
		
		<!-- 只在有标题时显示标题区域 -->
		<view class="header" v-if="params.main_title">
			<view class="title-box">
				<text class="main-title" :style="{
					color: params.main_title_color,
					textShadow: params.main_title_color ? 'none' : '0 2rpx 4rpx rgba(0, 0, 0, 0.2)'
				}">{{ params.main_title }}</text>
				<text class="sub-title" v-if="params.sub_title" :style="{
					color: params.sub_title_color,
					textShadow: params.sub_title_color ? 'none' : '0 2rpx 4rpx rgba(0, 0, 0, 0.2)'
				}">{{ params.sub_title }}</text>
			</view>
			<view class="more-link" v-if="params.hrefurl" @click="goto" :data-url="params.hrefurl" :style="{
				color: params.more_text_color || '#fff'
			}">
				{{ params.more_text || '查看更多' }} <text class="arrow">></text>
			</view>
		</view>
		
		<view class="dp-product-itemline">
			<view class="item" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/shopPackage/shop/product?id='+item[idfield]" :style="{backgroundColor:probgcolor}">
				<view class="product-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
					<image class="saleimg" :src="saleimg" v-if="saleimg!=''" mode="widthFix"/>
				</view>
				<view class="product-info">
					<view class="p1" v-if="showname == 1">{{item.name}}</view>
					<view class="p2">
						<view class="p2-1" v-if="showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)">
							<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">￥</text>{{item.sell_price}}</text>
							<text class="t2" v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
						</view>
						<view class="p2-1" v-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0" style="height: 50rpx;line-height: 44rpx;">
							<text class="t1" :style="{color:t('color1'),fontSize:'30rpx'}">询价</text>
							<block v-if="item.xunjia_text && item.price_type == 1">
								<view class="lianxi" :style="{background:t('color1')}" @tap.stop="showLinkChange" :data-lx_name="item.lx_name" :data-lx_bid="item.lx_bid" :data-lx_bname="item.lx_bname" :data-lx_tel="item.lx_tel" data-btntype="2">{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>
							</block>
						</view>
					</view>
					<view class="p1" v-if="item.merchant_name" style="color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal"><text>{{item.merchant_name}}</text></view>
					<view class="p1" v-if="item.main_business" style="color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;"><text>{{item.main_business}}</text></view>
					<view class="p3" v-if="showsales=='1' && item.sales>0">已售{{item.sales}}件</view>
					<view v-if="(showsales !='1' ||  item.sales<=0) && item.main_business" style="height: 44rpx;"></view>
					<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="showcart==1 && !item.price_type" @click.stop="buydialogChange" :data-proid="item[idfield]"><text class="iconfont icon_gouwuche"></text></view>
					<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="showcart==2 && !item.price_type" @click.stop="buydialogChange" :data-proid="item[idfield]"><image :src="cartimg" class="img"/></view>
				</view>
			</view>
		</view>
	</view>

	<!-- 没有背景和标题时显示原始样式 -->
	<view class="dp-product-itemline" v-else>
		<view class="item" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/shopPackage/shop/product?id='+item[idfield]" :style="{backgroundColor:probgcolor}">
			<view class="product-pic">
				<image class="image" :src="item.pic" mode="widthFix"/>
				<image class="saleimg" :src="saleimg" v-if="saleimg!=''" mode="widthFix"/>
			</view>
			<view class="product-info">
				<view class="p1" v-if="showname == 1">{{item.name}}</view>
				<view class="p2">
					<view class="p2-1" v-if="showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)">
						<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">￥</text>{{item.sell_price}}</text>
						<text class="t2" v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
					</view>
					<view class="p2-1" v-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0" style="height: 50rpx;line-height: 44rpx;">
						<text class="t1" :style="{color:t('color1'),fontSize:'30rpx'}">询价</text>
						<block v-if="item.xunjia_text && item.price_type == 1">
							<view class="lianxi" :style="{background:t('color1')}" @tap.stop="showLinkChange" :data-lx_name="item.lx_name" :data-lx_bid="item.lx_bid" :data-lx_bname="item.lx_bname" :data-lx_tel="item.lx_tel" data-btntype="2">{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>
						</block>
					</view>
				</view>
				<view class="p1" v-if="item.merchant_name" style="color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal"><text>{{item.merchant_name}}</text></view>
				<view class="p1" v-if="item.main_business" style="color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;"><text>{{item.main_business}}</text></view>
				<view class="p3" v-if="showsales=='1' && item.sales>0">已售{{item.sales}}件</view>
				<view v-if="(showsales !='1' ||  item.sales<=0) && item.main_business" style="height: 44rpx;"></view>
				<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="showcart==1 && !item.price_type" @click.stop="buydialogChange" :data-proid="item[idfield]"><text class="iconfont icon_gouwuche"></text></view>
				<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="showcart==2 && !item.price_type" @click.stop="buydialogChange" :data-proid="item[idfield]"><image :src="cartimg" class="img"/></view>
			</view>
		</view>
	</view>
	<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange" :menuindex="menuindex"></buydialog>
	<view class="posterDialog linkDialog" v-if="showLinkStatus">
		<view class="main">
			<view class="close" @tap="showLinkChange"><image class="img" src="/static/img/close.png"/></view>
			<view class="content">
				<view class="title">{{lx_name}}</view>
				<view class="row" v-if="lx_bid > 0">
					<view class="f1">店铺名称</view>
					<view class="f2" @tap="goto" :data-url="'/pagesExt/business/index?id='+lx_bid">{{lx_bname}}<image src="/static/img/arrowright.png" class="image"/></view>
				</view>
				<view class="row" v-if="lx_tel">
					<view class="f1">联系电话</view>
					<view class="f2" @tap="goto" :data-url="'tel::'+lx_tel" :style="{color:t('color1')}">{{lx_tel}}<image src="/static/img/copy.png" class="copyicon" @tap.stop="copy" :data-text="lx_tel"></image></view>
				</view>
			</view>
		</view>
	</view>
</view>
</template>
<script>
	export default {
		data(){
			return {
				buydialogShow:false,
				proid:0,
                
                showLinkStatus:false,
                lx_name:'',
                lx_bid:'',
                lx_tel:''
			}
		},
		props: {
			showstyle:{default:2},
			menuindex:{default:-1},
			saleimg:{default:''},
			showname:{default:1},
			namecolor:{default:'#333'},
			showprice:{default:'1'},
			showsales:{default:'1'},
			showcart:{default:'1'},
			cartimg:{default:'/static/imgsrc/cart.svg'},
			data:{},
			idfield:{default:'id'},
			probgcolor:{default:'#fff'},
			params: {
				type: Object,
				default: () => ({
					bgimg: '',
					main_title: '',
					main_title_color: '',  // 主标题颜色
					sub_title: '',
					sub_title_color: '',   // 副标题颜色
					more_text: '',
					more_text_color: '',   // 更多文字颜色
					hrefurl: ''
				})
			}
		},
		methods: {
			buydialogChange: function (e) {
				if(!this.buydialogShow){
					this.proid = e.currentTarget.dataset.proid
				}
				this.buydialogShow = !this.buydialogShow;
				console.log(this.buydialogShow);
			},
			addcart:function(){
				this.$emit('addcart');
			},
            showLinkChange: function (e) {
                var that = this;
            	that.showLinkStatus = !that.showLinkStatus;
                that.lx_name = e.currentTarget.dataset.lx_name;
                that.lx_bid = e.currentTarget.dataset.lx_bid;
                that.lx_bname = e.currentTarget.dataset.lx_bname;
                that.lx_tel = e.currentTarget.dataset.lx_tel;
            },
		},
		mounted() {
			console.log('params:', this.params)
			console.log('Color params:', {
				mainTitle: this.params.main_title_color,
				subTitle: this.params.sub_title_color,
				moreText: this.params.more_text_color
			})
		},
		computed: {
			mainTitleStyle() {
				return {
					color: this.params.main_title_color || '#fff',
					textShadow: this.params.main_title_color ? 'none' : '0 2rpx 4rpx rgba(0, 0, 0, 0.2)'
				}
			},
			subTitleStyle() {
				return {
					color: this.params.sub_title_color || 'rgba(255, 255, 255, 0.9)',
					textShadow: this.params.sub_title_color ? 'none' : '0 2rpx 4rpx rgba(0, 0, 0, 0.2)'
				}
			},
			moreLinkStyle() {
				return {
					color: this.params.more_text_color || '#fff'
				}
			}
		}
	}
</script>
<style>
.line-container {
	position: relative;
	overflow: hidden;
	padding: 20rpx 0;
	background: #f5f5f5;
	min-height: 360rpx;  /* 设置最小高度 */
}

.line-bg {
	position: absolute;
	top: -20%;
	left: -20%;
	width: 140%;
	height: 140%;
	background-position: center;
	background-size: cover;
	z-index: 1;
	transform: scale(1.1);
}

.line-bg::after {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.2) 100%);
}

.header {
	position: relative;
	z-index: 3;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 30rpx;
	margin-top: 20rpx;
}

.title-box {
	display: flex;
	flex-direction: column;
}

.main-title {
	font-size: 40rpx;
	font-weight: bold;
	line-height: 1.2;
	margin-bottom: 8rpx;
}

.sub-title {
	font-size: 26rpx;
	line-height: 1.4;
}

.more-link {
	font-size: 28rpx;
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 30rpx;
}

.more-link .arrow {
	margin-left: 6rpx;
}

.dp-product-itemline {
	position: relative;
	z-index: 2;
	width: 100%;
	display: flex;
	overflow-x: scroll;
	overflow-y: hidden;
	margin-top: 60rpx;
	padding: 20rpx;
}

.dp-product-itemline .item {
	width: 220rpx;
	display: inline-block;
	position: relative;
	margin-bottom: 12rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 10rpx;
	margin-right: 4px;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.dp-product-itemline .product-pic {width:220rpx;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}
.dp-product-itemline .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-product-itemline .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}
.dp-product-itemline .product-info {padding:10rpx 10rpx;position: relative;}
.dp-product-itemline .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
.dp-product-itemline .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}
.dp-product-itemline .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}
.dp-product-itemline .product-info .p2-1 .t1{font-size:36rpx;font-weight:bold;}
.dp-product-itemline .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-product-itemline .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}
.dp-product-itemline .product-info .p3{color:#999999;font-size:20rpx;margin-top:10rpx}
.dp-product-itemline .product-info .p4{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;bottom:8rpx;right:20rpx;text-align:center;}
.dp-product-itemline .product-info .p4 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}
.dp-product-itemline .product-info .p4 .img{width:100%;height:100%}

.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}
.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}
.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}
.posterDialog .close .img{ width:32rpx;height:32rpx;}
.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}
.posterDialog .content .img{width:540rpx;height:960rpx}
.linkDialog {background:rgba(0,0,0,0.4);z-index:11;}
.linkDialog .main{ width: 90%; position: fixed; top: 50%; left: 50%; margin: 0;-webkit-transform: translate(-50%,-50%);transform: translate(-50%,-50%);}
.linkDialog .title {font-weight: bold;margin-bottom: 30rpx;}
.linkDialog .row {display: flex; height:80rpx;line-height: 80rpx; padding: 0 16rpx;}
.linkDialog .row .f1 {width: 40%; text-align: left;}
.linkDialog .row .f2 {width: 60%; height:80rpx;line-height: 80rpx;text-align: right;align-items:center;}
.linkDialog .image{width: 28rpx; height: 28rpx; margin-left: 8rpx;margin-top: 2rpx;}
.linkDialog .copyicon {width: 28rpx; height: 28rpx; margin-left: 8rpx; position: relative; top: 4rpx;}

.lianxi{color: #fff;border-radius: 50rpx 50rpx;line-height: 50rpx;text-align: center;font-size: 22rpx;padding: 0 14rpx;display: inline-block;float: right;}
</style>