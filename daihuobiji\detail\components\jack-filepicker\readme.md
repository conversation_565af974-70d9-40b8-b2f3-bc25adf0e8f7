# jack-filepicker简介

jack-filepicker是一款适用于H5端和微信小程序的图片选择器，与官方uni-file-picker不同的是：本组件支持**选择图片后进行直接编辑**的操作。

# 开发文档

1.首先导入本组件到项目。

2.在main.js中引入并安装。

```json
import JackFileupload from "@/uni_modules/jack-filepicker/components/jack-fileupload/jack-fileupload.vue"
//全局注册组件
Vue.component(JackFileupload)
```

3.在页面中使用组件。

```js
<jack-fileupload  />
```

4.h5中，在manifest.json源码视图的 h5 中配置[高德应用申请的key-web服务API]([创建应用和 Key-Web服务 API | 高德地图API (amap.com)](https://lbs.amap.com/api/webservice/create-project-and-key))，否则无法使用定位功能，同时将manifest.json中的注释删除。

5.微信小程序中，在manifest.json 源码视图的 mp-weixin中添加如下配置：

```json
"mp-weixin": {
		"appid": "",
		"setting": {
			"urlCheck": false
		},
		"usingComponents": true,
		//新加配置如下
		"requiredPrivateInfos": ["getLocation"],
		"permission": {
			"scope.userLocation": {
				"desc": "你的位置信息将用于位置信息展示"
			}
		}
		//新加配置结束
	},
```

5.在项目根目录进行下node_modules依赖的安装

```js
npm i @amap/amap-jsapi-loader --save
```
**注意：**

h5调试添加位置功能时，请在dege中调试(推荐);或者使用https协议的域名代理到本地址进行访问;也可以发布到服务器使用https域名访问');

微信小程序调试裁剪功能时，请在真机调试下进行。

## 配置说明

| 参数名         | 说明                                                         | 类型    | 例子                           | 默认值              | 可选值              |
| -------------- | ------------------------------------------------------------ | ------- | ------------------------------ | ------------------- | ------------------- |
| limit          | 最多可选的文件数                                             | Number  | 2                              | 1                   | -                   |
| readonly       | 组件是否只读（只能查看已经绑定的图片）                       | Boolean | true                           | false               | -                   |
| disablePreview | 是否禁止预览图片                                             | Boolean | true                           | false               | -                   |
| delIcon        | 是否显示删除图片按钮                                         | Boolean | true                           | false               | -                   |
| title          | 组件上方的文字提示（左侧显示标题，右侧显示上传数和可上传数，showTitle=true生效） | string  | 请选择图片                     | 请选择图片          | -                   |
| sourceType     | 选择的方式（从相册选择还是使用相机，默认都有）               | Array   | ['album']                      | ['album', 'camera'] | ['album', 'camera'] |
| fileExtname    | 选择的图片后缀名,不可为空数组                          | Array   | ['.png']                       | ['.png','.jpg','.jpeg','.webp']                  | -                   |
| v-model        | 组件绑定的图片链接，用来回显,也可以上传完成后对绑定的属性赋值 | Array   | ['http://zhiyin-caixukun.png'] | []                  | -                   |
| showTitle      | 是否显示组件标题                                             | Boolean | false                          | true                | -                   |

## 事件

| 事件名      | 事件说明       | 返回参数                   |
| ----------- | -------------- | -------------------------- |
| @delete     | 删除图片       | 见下文                     |
| @finishEdit | 完成图片的编辑 | 编辑完成返回此图片blob链接 |

```js
//@delete 返回参数
{
    index,//删除的图片索引
    url//删除的图片链接
}
```

## 示例

```vue
<template>
	<view class="content">
		<jack-fileupload
			v-model="list" 
			:limit="3" 
			:readonly="false"
			:disablePreview="false"
			:delIcon="true"
			:showTitle="true"
			title="请选择图片"
			:sourceType="['album', 'camera']"
			:fileExtname="['.png']"
			@finishEdit="saveSucc" 
			@delete="deleteItem" 
			/>
		<button @click="upload">123</button>
		<uni-file-picker ref="uploadRef" return-type="array" v-model="list" limit="5" @select="select" mode="grid"
			@success="success" @delete="deleteI" file-mediatype="image" title="最多选择5个文件"></uni-file-picker>
	</view>
</template>

<script>
	import {
		handleOper
	} from "@/custom-tab-bar/utils/index.js"
	export default {
		data() {
			return {
				list: [],
				title: 'Hello',
				read: false,
			}
		},
		onLoad() {

		},
		methods: {
			upload() {},
			saveSucc(e) {
				uni.uploadFile({
					url: 'http://localhost:84/upload',
					filePath: e,
					name: 'file',
					formData: {
						fileName: 'file.png'
					},
					success: res => {
						this.list.push(JSON.parse(res.data).url)
					}
				})
			},
			deleteItem(e) {
				console.log(e);
			},
			success(e) {
				console.log(e);
				console.log(this.list);
			},
			select(e) {
				console.log(e);
				this.$refs.uploadRef.upload()
				console.log(this.list);
			},
			deleteI(index) {
				console.log(index);
			}
		},
		onShow() {
			handleOper.call(this)
		},
		watch:{
			list(){
				console.log(this.list.length);
			}
		}
	}
</script>

<style>

</style>
```



## 小结

微信小程序端由于原生canvas组件层级问题，图片过长在真机会出现覆盖工具栏的问题，暂时未解决，请各位大佬指点迷津。
