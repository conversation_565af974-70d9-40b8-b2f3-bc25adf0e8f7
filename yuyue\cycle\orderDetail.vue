<template>
<view class="container">
	<block v-if="isload">
		<!-- 订单状态 -->
		<view class="order-status-block" :class="'status-bg-' + info.status">
			<view class="status-text">{{info.status_text}}</view>
			<view class="status-desc" v-if="info.status == 1">下次服务日期：{{info.next_service_date || '暂无安排'}}</view>
			<view class="status-desc" v-else-if="info.status == 2">服务已暂停，可随时恢复</view>
			<view class="status-desc" v-else-if="info.status == 3">您的周期服务已全部完成</view>
			<view class="status-desc" v-else-if="info.status == 4">服务已取消</view>
		</view>

		<!-- 服务地址 -->
		<view class="detail-card">
			<view class="card-title">
				<text>服务地址</text>
			</view>
			<view class="address-content">
				<view class="user-info">{{info.linkman}} {{info.tel}}</view>
				<view class="address-text">{{info.address}}</view>
			</view>
		</view>

		<!-- 服务信息 -->
		<view class="detail-card">
			<view class="card-title">
				<text>服务信息</text>
				<view class="more-link" @tap="goto" :data-url="'/yuyue/cycle/productDetail?id='+info.product_id">查看详情 ></view>
			</view>
			<view class="service-header">
				<image class="service-image" :src="info.product_pic" mode="aspectFill"></image>
				<view class="service-title">{{info.product_name}}</view>
			</view>
			<view class="info-list">
				<view class="info-item">
					<text class="label">单期价格</text>
					<text class="value price">￥{{info.price}}</text>
				</view>
				<view class="info-item">
					<text class="label">订单总价</text>
					<text class="value price">￥{{info.total_price || (info.price * info.total_period)}}</text>
				</view>
				<view class="info-item">
					<text class="label">总期数</text>
					<text class="value">{{info.total_period}}期</text>
				</view>
				<view class="info-item">
					<text class="label">已完成</text>
					<text class="value">{{info.served_period}}期</text>
				</view>
				<view class="info-item">
					<text class="label">服务周期</text>
					<text class="value">{{info.period_type_text || '-'}}</text>
				</view>
				<view class="info-item">
					<text class="label">开始日期</text>
					<text class="value">{{info.start_date || '-'}}</text>
				</view>
				<view class="info-item" v-if="info.end_date">
					<text class="label">结束日期</text>
					<text class="value">{{info.end_date}}</text>
				</view>
			</view>
		</view>

		<!-- 分期计划 -->
		<view class="detail-card">
			<view class="card-title">
				<text>服务计划</text>
				<view class="more-link" v-if="stageList.length > 3" @tap="goto" :data-url="'/yuyue/cycle/planList?id=' + info.id">查看全部</view>
			</view>
			<view class="stage-list">
				<block v-for="(stage, index) in stageList.slice(0, 3)" :key="index">
					<view class="stage-item">
						<view class="stage-header">
							<view class="stage-number">第 {{stage.period_num || (index+1)}} 期</view>
							<view class="stage-status" :class="'status-' + stage.status">{{stage.status_text}}</view>
						</view>
						<view class="stage-content">
							<view class="stage-date" v-if="stage.scheduled_date">计划日期：{{stage.scheduled_date}}</view>
							<view class="stage-worker" v-if="stage.stage_worker_name">服务人员：{{stage.stage_worker_name}}</view>
						</view>
					</view>
				</block>
				<view class="no-data" v-if="stageList.length === 0">暂无服务计划</view>
			</view>
		</view>

		<!-- 订单信息 -->
		<view class="detail-card">
			<view class="card-title">
				<text>订单信息</text>
			</view>
			<view class="info-list">
				<view class="info-item">
					<text class="label">订单编号</text>
					<view class="value-group">
						<text class="value order-num">{{info.ordernum}}</text>
						<text class="copy-btn" @tap="copy" :data-text="info.ordernum">复制</text>
					</view>
				</view>
				<view class="info-item" v-if="info.createtime">
					<text class="label">下单时间</text>
					<text class="value">{{formatTime(info.createtime)}}</text>
				</view>
				<view class="info-item" v-if="info.pay_status == 1">
					<text class="label">支付方式</text>
					<text class="value">{{info.pay_type || '线上支付'}}</text>
				</view>
				<view class="info-item" v-if="info.pay_time">
					<text class="label">支付时间</text>
					<text class="value">{{formatTime(info.pay_time)}}</text>
				</view>
				<view class="info-item" v-if="info.remark">
					<text class="label">订单备注</text>
					<text class="value">{{info.remark}}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-bar" v-if="info.status == 1 || info.status == 2">
			<view class="btn" :class="info.status == 1 ? 'btn-pause' : 'btn-resume'" @tap.stop="togglePause" :data-id="info.id">
				{{info.status == 1 ? '暂停服务' : '恢复服务'}}
			</view>
			<view class="btn btn-cancel" @tap.stop="cancelOrder" :data-id="info.id">取消服务</view>
		</view>

	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: true,
			isload: false,
			menuindex: -1,
			info: {},       // 订单主信息
			stageList: [],  // 分期计划列表
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onShow: function() {
		// 每次打开页面都刷新数据
		this.getdata(); 
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	methods: {
		getdata: function () {
			var that = this;
			var id = that.opt.id;
			if (!id) {
				app.alert('缺少订单ID', function() { app.goback(); });
				return;
			}
			that.loading = true;
			app.get('ApiPeriodicService/orderDetail', { id: id }, function(res) {
				that.loading = false;
				if (res.status == 0) {
					app.alert(res.msg, function() { app.goback(); });
					return;
				}
				that.info = res.info;
				that.stageList = res.stageList || [];
				that.isload = true;
				
				// 处理订单时间格式
				if (that.info.createtime && typeof that.info.createtime === 'number') {
					that.info.createtime_text = that.formatTime(that.info.createtime);
				}
				if (that.info.pay_time && typeof that.info.pay_time === 'number') {
					that.info.pay_time_text = that.formatTime(that.info.pay_time);
				}
			});
		},
		formatTime: function(timestamp) {
			if (!timestamp) return '-';
			var date = new Date(timestamp * 1000);
			var year = date.getFullYear();
			var month = date.getMonth() + 1;
			var day = date.getDate();
			var hour = date.getHours();
			var minute = date.getMinutes();
			
			month = month < 10 ? '0' + month : month;
			day = day < 10 ? '0' + day : day;
			hour = hour < 10 ? '0' + hour : hour;
			minute = minute < 10 ? '0' + minute : minute;
			
			return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
		},
		togglePause: function (e) {
			var that = this;
			var orderid = e.currentTarget.dataset.id;
			var actionText = that.info.status == 1 ? '暂停' : '恢复';
			app.confirm('确定要'+actionText+'该服务吗?', function () {
				app.showLoading('提交中');
				app.post('ApiPeriodicService/togglePauseOrder', {id: orderid}, function (data) {
					app.showLoading(false);
					if(data.status == 1){
						app.success(actionText+'成功');
						setTimeout(function () {
							that.getdata(); // 刷新页面数据
						}, 1000);
					}else{
						app.error(data.msg);
					}
				});
			});
		},
		cancelOrder: function (e) {
			var that = this;
			var orderid = e.currentTarget.dataset.id;
			app.confirm('确定要取消该周期服务吗? 未完成的服务将一并取消。', function () {
				app.showLoading('提交中');
				app.post('ApiPeriodicService/cancelOrder', {id: orderid}, function (data) {
					app.showLoading(false);
					if(data.status == 1){
						app.success('取消成功');
						setTimeout(function () {
							that.getdata(); // 刷新页面数据
						}, 1000);
					}else{
						app.error(data.msg);
					}
				});
			});
		},
		copy: function(e) {
			app.copy(e.currentTarget.dataset.text);
		}
	}
};
</script>

<style>
.container { 
	background-color: #f8f8f8; 
	padding-bottom: 120rpx; 
	min-height: 100vh;
}

/* 订单状态区块 */
.order-status-block { 
	padding: 40rpx 30rpx; 
	margin-bottom: 20rpx; 
	text-align: center;
	position: relative;
	color: #fff;
}
.status-bg-0 { background: linear-gradient(to right, #1890ff, #40a9ff); } /* 待激活 */
.status-bg-1 { background: linear-gradient(to right, #FFB600, #FFC740); } /* 进行中 */
.status-bg-2 { background: linear-gradient(to right, #FF8D00, #FFA940); } /* 已暂停 */
.status-bg-3 { background: linear-gradient(to right, #06A051, #36C17C); } /* 已完成 */
.status-bg-4 { background: linear-gradient(to right, #909399, #C0C4CC); } /* 已取消 */

.order-status-block .status-text { 
	font-size: 36rpx; 
	font-weight: bold; 
	margin-bottom: 16rpx; 
}
.order-status-block .status-desc { 
	font-size: 28rpx; 
	opacity: 0.9;
}

/* 详情卡片通用样式 */
.detail-card {
	width: 94%; 
	margin: 0 3% 20rpx; 
	background: #fff; 
	border-radius: 16rpx; 
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.card-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 30rpx;
	border-bottom: 1px solid #f4f4f4;
}

.card-title text {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.more-link {
	font-size: 24rpx;
	color: #2196f3;
}

/* 地址区块 */
.address-content {
	padding: 24rpx 30rpx;
}
.user-info { 
	font-size: 30rpx; 
	color: #333; 
	font-weight: bold; 
	margin-bottom: 10rpx; 
}
.address-text { 
	font-size: 28rpx; 
	color: #666; 
	line-height: 1.5; 
}

/* 服务信息区块 */
.service-header {
	display: flex;
	align-items: center;
	padding: 24rpx 30rpx;
	border-bottom: 1px solid #f4f4f4;
}
.service-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
	background-color: #f7f7f7;
}
.service-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.info-list {
	padding: 0 30rpx;
}
.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	font-size: 28rpx;
	border-bottom: 1px dashed #f4f4f4;
}
.info-item:last-child {
	border-bottom: none;
}
.info-item .label {
	color: #999;
}
.info-item .value {
	color: #333;
	text-align: right;
}
.info-item .price {
	color: #ff6b00;
	font-weight: bold;
}
.value-group {
	display: flex;
	align-items: center;
}
.order-num {
	max-width: 360rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 20rpx;
}
.copy-btn {
	color: #2196f3;
	font-size: 24rpx;
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	border: 1px solid #2196f3;
}

/* 分期计划区块 */
.stage-list {
	padding: 10rpx 30rpx 30rpx;
}
.stage-item {
	padding: 20rpx 0;
	border-bottom: 1px dashed #f4f4f4;
}
.stage-item:last-child {
	border-bottom: none;
}
.stage-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}
.stage-number {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}
.stage-status {
	font-size: 24rpx;
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	font-weight: 500;
}
.status-0 { background-color: #e6f7ff; color: #1890ff; } /* 待服务 */
.status-1 { background-color: #fff7e6; color: #FFB600; } /* 服务中 */
.status-2 { background-color: #e6fffb; color: #06A051; } /* 已完成 */
.status-3 { background-color: #f5f5f5; color: #999999; } /* 已跳过 */
.status-4 { background-color: #fff1f0; color: #f44336; } /* 服务失败 */

.stage-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}
.no-data {
	text-align: center;
	padding: 30rpx 0;
	color: #999;
	font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding: 20rpx 30rpx;
	background: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
	z-index: 100;
}

.btn {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 40rpx;
	font-size: 28rpx;
	font-weight: 500;
	margin: 0 15rpx;
}

.btn-pause {
	background-color: #fff;
	color: #ff9800;
	border: 1px solid #ff9800;
}

.btn-resume {
	background-color: #fff;
	color: #2196f3;
	border: 1px solid #2196f3;
}

.btn-cancel {
	background-color: #fff;
	color: #f44336;
	border: 1px solid #f44336;
}
</style> 