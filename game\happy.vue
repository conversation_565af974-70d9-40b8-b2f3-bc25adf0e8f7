<template>
	<view class="container">
		<view id="playgame">
			<view class="title">
				<view class="title-text" @tap="onBack">返回</view>
				<view class="title-text" @tap="goToOrder">订单</view>
			</view>
			<image src="@/game/static/game/happy/bg51.png" class="daka" mode="widthFix" />
			<view class="box_view">
				<view class="odds">
					<view class="odds-text">本次中奖概率</view>
					<view class="odds-num">{{ probability }}%</view>
				</view>
				<view class="line">
					<view clas="line-divider-l"></view>
					<view class="line-text">中奖商品</view>
					<view clas="line-divider-r"></view>
				</view>
				<view class="detail">
					<view class="detail-img-wrap">
						<image class="detail-img" :src="productInfo.pic" />
					</view>
					<view class="img-refresh-wrap" @tap="onRefresh">
						<image class="img-refresh" src="@/game/static/game/happy/refresh.png" />
					</view>
					<view>
						<view class="detail-title">{{ productInfo.name }}</view>
						<view class="lucky-price">
							幸运价：￥{{ price }}
							<view class="img-cursor-wrap" @tap="onUpdatePrice">
								<image class="img-cursor" src="@/game/static/game/happy/cursor.png" />
							</view>
						</view>
					</view>
				</view>
				<view class="tips">
					点击
					<view>
						<image class="img-cursor2" src="@/game/static/game/happy/cursor2.png" mode="widthFix" />
					</view>
					即可修改幸运价格
				</view>
			</view>
			<view class="btn_bot">
				<view class="btn_bot_l">
					<view class="btn_bot_l_img_wrap" @tap="onReduce">
						<image class="img-reduce" src="@/game/static/game/happy/reduce.png" />
					</view>
					<view class="btn_bot_l_text">{{ time }}次</view>
					<view @tap="onAdd">
						<image class="img-add" src="@/game/static/game/happy/add.png" />
					</view>
				</view>
				<view class="btn_bot_r" @tap="onSubmit">
					<view class="btn_bot_r_text">马上嗨</view>
				</view>
			</view>
			<!---->
		</view>
		<uni-popup ref="popup">
			<view class="pri_box">
				<view class="pri_box_header">
					幸运价格
					<view class="close_wrap" @tap="onClosePriceModal">
						<uni-icons type="closeempty" size="24" color="#fff"></uni-icons>
					</view>
				</view>
				<view class="pri_box_content">
					<text class="lucky_text">幸运价</text>
					<input class="input-class" type="text" :value="modalPrice" @input="inputPrice" />
					<view class="lucky_tips">点击输入框手动填写价格</view>
					<view class="btn-confirm" @tap="onConfirmUpdate">确认修改</view>
				</view>
			</view>
		</uni-popup>
		<!-- 中奖提示 -->
		<uni-popup ref="popupResult">
			<view class="pri_box">
				<view class="pri_box_header">
					<text>恭喜中奖</text>
					<view class="close_wrap" @tap="onCloseResultModal">
						<uni-icons type="closeempty" size="24" color="#fff"></uni-icons>
					</view>
				</view>
				<view class="goods_box">
					<view class="goods_box_header">
						<image class="goods-img" :src="productInfo.pic" />
					</view>
					<view class="goods_text">{{ productInfo.name }}</view>
				</view>
				<button class="btn-view" @click="goToOrder">去查看</button>
			</view>
		</uni-popup>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			loading: false,
			id: null,
			// 商品信息
			productInfo: {
				name: '',
				pic: '',
				sell_price: 0
			},
			time: 1,
			modalPrice: 0,
			probability: 0,
			price: 0
		};
	},
	computed: {},
	onLoad: async function (opt) {
		this.opt = app.getopts(opt);
		this.price = this.opt.amount;
		await this.getdata();

		if (this.opt.oid) {
			this.loading = true;
			await this.getResult();
		}
	},
	methods: {
		onBack() {
			uni.redirectTo({ url: `/shopPackage/shop/product?id=${this.opt.id}` });
		},
		goToOrder() {
			app.goto('/game/order');
		},
		/**
		 * 商品信息
		 */
		getdata: async function () {
			let that = this;
			let id = this.opt.id || 0;
			that.loading = true;
			await app.get(
				'ApiShop/product',
				{
					id: id
				},
				function (res) {
					that.loading = false;
					if (res.status == 0) {
						app.alert(res.msg);
						return;
					}
					that.productInfo = res.product;
					that.price = that.opt.amount;
					that.getProbability();
				}
			);
		},
		/**
		 * 刷新
		 */
		onRefresh() {
			this.getLuckPrice()
		},
		/**
		 * 修改价格-弹窗
		 */
		onUpdatePrice() {
			this.modalPrice = this.price;
			this.$refs.popup.open('top');
		},
		/**
		 * 价格-键盘输入
		 * @param {Object} e
		 */
		inputPrice(e) {
			const { value } = e.detail;
			this.modalPrice = value;
		},
		onClosePriceModal() {
			this.$refs.popup.close();
		},
		/**
		 * 确认修改
		 */
		onConfirmUpdate() {
			let that = this;
			that.$refs.popup.close();
			that.$nextTick(() => {
				that.price = that.modalPrice;
				that.getProbability();
			});
		},
		/**
		 * 获取商品幸运价格
		 */
		getLuckPrice () {
			let that = this;
			that.loading = true;
			const params = {
				sid: that.opt.gameId,
				pid: that.opt.id
			}
			app.get('ApiSweepstakes/getLuckPrice', params, function(res) {
				that.loading = false;
				if (res.status == 1) {
					that.price = res.price
					that.getProbability()
				} else {
					app.alert(res.msg);
				}
			})
		},
		getProbability() {
			if (this.time && this.price && this.productInfo.sell_price) {
				// let num = (this.time * this.price) / this.productInfo.sell_price;
				let num = this.price / this.productInfo.sell_price;
				this.probability = (num * 100 || 0).toFixed(2);
			} else {
				this.probability = (0).toFixed(2);
			}
		},
		onReduce() {
			if (this.time > 1) {
				this.time--;
			}
			this.getProbability();
		},
		onAdd() {
			this.time++;
			// this.getProbability();
		},
		onSubmit() {
			let that = this;
			that.loading = true;
			const params = {
				sid: that.opt.gameId,
				pid: that.opt.id,
				amount: that.price,
				frequency: that.time,
				tourl: app._fullurl()
			};
			app.post('ApiSweepstakes/postRaffle', params, function (res) {
				switch (res.status) {
					case 1:
						if (res.is_lottery === 1) {
							app.alert('恭喜你，中奖了');
						} else {
							app.alert('未中奖，请再接再厉');
						}
						break;
					case 2:
						app.confirm(res.msg || '出错了', function () {
							app.goto(res.pathUrl);
						});
						break;
					case 3:
						app.goto(res.pathUrl);
						break;
					default:
						app.alert(res.msg || '出错了');
						break;
				}
			});
		},
		async getResult() {
			let that = this;
			const params = {
				oid: this.opt.oid
			};
			await app.post('ApiSweepstakes/getResult', params, function (res) {
				if (res.status === 1) {
					if (res.is_lottery === 1) {
						app.alert('恭喜你，中奖了', function () {
							app.goto(app._delOrderId());
						});
					} else {
						app.alert('未中奖，请再接再厉', function () {
							app.goto(app._delOrderId());
						});
					}
				} else {
					app.alert(res.msg, function () {
						app.goto(app._delOrderId());
					});
				}
			});
		}
	}
};
</script>

<style scoped>
.container {
	height: 100vh;
	overflow: hidden;
}

#playgame {
	padding: 10px 20px;
	background: url(../game/static/game/happy/bg.png) no-repeat;
	background-size: 100% 100%;
	box-sizing: border-box;
	height: 100%;
	overflow: scroll;
}

#playgame .title {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
}

#playgame .title .title-text {
	background: #fab132;
	color: #fff;
	font-weight: 700;
	font-size: 13px;
	padding: 3px 10px;
	border-radius: 20px;
}

#playgame .daka {
	width: 90%;
	margin-left: 5%;
	margin-top: 10px;
}

#playgame .box_view {
	background: url(../game/static/game/happy/bg2.png) no-repeat;
	background-size: 100% 100%;
	height: 330px;
	width: 93%;
	margin-left: 3.5%;
	padding-top: 25px;
	box-sizing: border-box;
}

#playgame .box_view .odds {
	text-align: center;
	font-size: 14px;
	font-family: Source Han Sans CN;
	font-weight: 700;
	color: #6b2602;
}

#playgame .box_view .odds .odds-num {
	font-size: 24px;
	margin-top: 10px;
}

#playgame .box_view .select {
	margin-top: 30px;
}

#playgame .box_view .select div {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	padding: 0 25px;
	text-align: center;
}

#playgame .box_view .select div p {
	text-align: center;
	width: 71px;
}

#playgame .box_view .select div p img {
}

.line {
	display: flex;
	justify-content: center;
	width: 80%;
	margin: 55px auto 20px auto;
}

.line-divider {
	display: table;
	white-space: nowrap;
	height: auto;
	overflow: hidden;
	line-height: 1;
	text-align: center;
	padding: 10px 0;
	color: #666;
}

.line-divider:before {
	background-position: right 1em top 50%;
}

.line-divider:after {
	background-position: left 1em top 50%;
}

.line-divider:before,
.line-divider:after {
	content: '';
	display: table-cell;
	position: relative;
	top: 50%;
	width: 50%;
	background-repeat: no-repeat;
	background-image: url('../game/static/game/happy/line.png');
}

.line .line-text {
	color: rgb(107, 38, 2);
}

#playgame .box_view .detail {
	width: 80%;
	margin: 0 auto;
	background: #fff;
	border-radius: 5px;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: left;
	-webkit-justify-content: left;
	justify-content: left;
	padding: 25px 10px;
	position: relative;
}

.img-cursor {
	width: 23px;
	height: 23px;
	position: relative;
	top: 2px;
}

.img-refresh-wrap {
	width: 20px;
	height: 20px;
	border-radius: 50%;
	position: absolute;
	top: 8px;
	right: 8px;
	box-shadow: 0 0 2px 0 #8b4513;
}

.img-refresh {
	width: 100%;
	height: 100%;
}

.detail-img-wrap {
	width: 50px;
	height: 50px;
	margin-right: 10px;
}

.detail-img {
	height: 100%;
	max-width: 50px;
	border: 1px solid #ddd;
}

.detail-title {
	font-size: 14px;
	color: #333;
	font-weight: 700;
}

.lucky-price {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	color: #f85e06;
	position: relative;
	font-size: 15px;
	top: 5px;
	font-weight: 600;
}

.lucky-price .img-cursor-wrap {
	color: #999;
	font-weight: 400;
	margin-left: 10px;
}

.tips {
	display: flex;
	justify-content: flex-end;
	items-align: center;
	width: 90%;
	font-size: 12px;
	text-align: right;
	color: #f85e06;
	line-height: 20px;
}

.img-cursor2 {
	width: 15px;
	position: relative;
	top: 2px;
}

#playgame .box_view .tips {
	width: 90%;
	font-size: 12px;
	text-align: right;
	color: #f85e06;
	line-height: 20px;
}

#playgame .btn_bot {
	width: 292px;
	height: 55px;
	margin: 25px auto 0;
	line-height: 55px;
	text-align: center;
	color: #fff;
	font-weight: 700;
	font-size: 18px;
	display: flex;
	justify-content: center;
	align-items: center;
}

#playgame .btn_bot .btn_bot_l,
#playgame .btn_bot .btn_bot_r {
	width: 50%;
}

.btn_bot_l {
	color: #f85e06;
	background: url('@/game/static/game/happy/round_l.png');
	background-size: 100% 100%;
	position: relative;
	display: flex;
	justify-content: space-around;
	align-items: center;
	box-sizing: border-box;
	padding: 0 10px;
	left: 2px;
}

.btn_bot_r {
	color: #f85e06;
	background: url('@/game/static/game/happy/round_r.png');
	background-size: 100% 100%;
	position: relative;
	left: 2px;
}

#playgame .btn_bot .btn_bot_l .btn_bot_l_text {
	padding: 0 5px;
	font-size: 18px;
	position: relative;
}

.img-reduce {
	display: inline-block;
	width: 20px;
	height: 20px;
	position: relative;
	top: 3px;
}

.img-add {
	display: inline-block;
	width: 20px;
	height: 20px;
	position: relative;
	top: 3px;
}

.btn_bot_r_text {
	text-align: center;
	color: #fff;
	font-weight: 700;
	font-size: 18px;
	display: flex;
	justify-content: center;
}

/* 弹窗 */
.pri_box {
	width: 240px;
	height: 250px;
	margin: 180px auto 0;
	background-color: #fff;
	text-align: center;
}

.pri_box_header {
	height: 60px;
	background: url(../game/static/game/happy/box_header_bg.png) no-repeat;
	background-size: 100% 100%;
	line-height: 60px;
	color: #fff;
	font-size: 20px;
	font-weight: 700;
	position: relative;
}

.btn-confirm {
	width: 150px;
	height: 32px;
	text-align: center;
	margin: 36px auto 0;
	background: url(../game/static/game/happy/btn-confirm.png) no-repeat;
	background-size: 100% 100%;
	color: #fff;
	line-height: 32px;
	font-size: 14px;
}

.pri_box_content {
	padding: 20px;
	display: flex;
	flex-direction: column;
}

.input-class {
	background: #efefef;
	height: 32px;
	border-radius: 3px;
	border: none;
	outline: none;
	padding-left: 5px;
	color: #f85e06;
	font-weight: 700;
	text-align: left;
}

.lucky_text {
	text-align: left;
	margin-bottom: 10px;
	font-size: 13px;
}

.lucky_tips {
	margin-top: 3px;
	text-align: center;
	font-size: 12px;
	color: #aaa5a5;
}

.close_wrap {
	position: absolute;
	top: 15px;
	right: 5px;
	line-height: 0;
}

/* 弹窗商品信息 */

.goods_box {
	width: 230px;
	height: 210px;
	background: #fff;
	border: 3px solid #683afa;
	border-radius: 3px;
	text-align: center;
	margin: 30px auto 0;
}

.goods_box .goods_box_header {
	width: 180px;
	height: 108px;
	background: url(../game/static/game/lucky/goods_bg.png) no-repeat;
	background-size: 100% 100%;
	margin: 22px auto 0;
}

.goods-img {
	width: 50px;
	height: 50px;
	margin: 21px auto 0;
}

.goods_box .goods_text {
	font-size: 14px;
	color: #212121;
	line-height: 25px;
}
</style>
