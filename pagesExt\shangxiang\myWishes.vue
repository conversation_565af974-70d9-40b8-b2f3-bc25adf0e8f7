<template>
	<view class="container">
		<block v-if="isload">
			<!-- 统计数据 -->
			<view class="stats-section">
				<view class="stats-item">
					<text class="stats-number" :style="{color:t('color1')}">{{statistics.total_count || 0}}</text>
					<text class="stats-label">总次数</text>
				</view>
				<view class="stats-item">
					<text class="stats-number" :style="{color:t('color1')}">{{statistics.total_amount || 0}}</text>
					<text class="stats-label">总金额</text>
				</view>
				<view class="stats-item">
					<text class="stats-number" :style="{color:t('color1')}">{{statistics.wish_count || 0}}</text>
					<text class="stats-label">许愿次数</text>
				</view>
			</view>
			
			<!-- 类型筛选 -->
			<view class="filter-tabs">
				<view v-for="(tab, index) in filterTabs" 
					  :key="index" 
					  class="filter-tab" 
					  :class="{active: currentTab === index}"
					  @tap="switchTab" 
					  :data-index="index">
					<text>{{tab.name}}</text>
				</view>
			</view>
			
			<!-- 我的记录列表 -->
			<view class="my-wishes-list">
				<view v-for="(item, index) in datalist" :key="index" class="wish-record">
					<view class="record-header">
						<text class="type-badge" :style="{backgroundColor: getTypeColor(item.type)}">{{getTypeName(item.type)}}</text>
						<text class="amount" :style="{color:t('color1')}">￥{{item.amount}}</text>
						<text class="time">{{formatTime(item.create_time)}}</text>
					</view>
					
					<view class="record-content" v-if="item.wish_content">
						<text>{{item.wish_content}}</text>
					</view>
					
					<view class="record-footer">
						<text class="status" :class="getStatusClass(item.status)">{{getStatusText(item.status)}}</text>
						<view class="actions">
							<button class="action-btn share-btn" @tap="shareWish" :data-id="item.id">分享</button>
							<button class="action-btn edit-btn" 
									v-if="item.type === 'xuyuan'"
									@tap="editWish" 
									:data-id="item.id" 
									:data-content="item.wish_content">编辑</button>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-if="!loading && datalist.length === 0" class="empty-state">
				<image class="empty-icon" :src="pre_url+'/static/img/empty-my-wishes.png'"/>
				<text class="empty-text">暂无记录</text>
			</view>
			
			<!-- 编辑许愿弹窗 -->
			<view v-if="showEditModal" class="modal-overlay" @tap="closeEditModal">
				<view class="modal-content" @tap.stop>
					<view class="modal-header">
						<text class="modal-title">编辑许愿内容</text>
						<text class="close-btn" @tap="closeEditModal">×</text>
					</view>
					
					<view class="modal-body">
						<textarea class="edit-textarea" 
								  placeholder="请输入许愿内容" 
								  v-model="editContent" 
								  maxlength="200"></textarea>
						<text class="char-count">{{editContent.length}}/200</text>
					</view>
					
					<view class="modal-footer">
						<button class="cancel-btn" @tap="closeEditModal">取消</button>
						<button class="confirm-btn" 
								:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
								@tap="saveEdit">保存</button>
					</view>
				</view>
			</view>
		</block>
		
		<nodata v-if="nodata"></nodata>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			isload: false,
			nodata: false,
			statistics: {},
			datalist: [],
			filterTabs: [
				{name: '全部', type: ''},
				{name: '供花', type: 'gonghua'},
				{name: '供果', type: 'gongguo'},
				{name: '上香', type: 'shangxiang'},
				{name: '许愿', type: 'xuyuan'}
			],
			currentTab: 0,
			page: 1,
			limit: 20,
			hasMore: true,
			loading: false,
			loadingMore: false,
			showEditModal: false,
			editContent: '',
			editId: 0,
			pre_url: '',
			textset: {}
		}
	},
	
	onLoad: function(opt) {
		this.opt = app.getopts(opt);
		this.pre_url = app.globalData.pre_url;
		this.getStatistics();
		this.getdata();
	},
	
	onPullDownRefresh: function() {
		this.page = 1;
		this.hasMore = true;
		this.getStatistics();
		this.getdata();
	},
	
	onReachBottom: function() {
		if (this.hasMore && !this.loading) {
			this.page++;
			this.getdata();
		}
	},
	
	methods: {
		// 获取统计数据
		getStatistics() {
			var that = this;
			app.post('ApiShangxiang/getMyStatistics', {}, function(res) {
				if (res.code === 1) {
					that.statistics = res.data;
				}
			});
		},
		
		// 获取数据
		getdata() {
			var that = this;
			if (that.loading) return;
			that.loading = true;
			
			const params = {
				page: that.page,
				limit: that.limit,
				type: that.filterTabs[that.currentTab].type
			};
			
			app.post('ApiShangxiang/getMyWishList', params, function(res) {
				that.loading = false;
				if (res.code === 1) {
					const newList = res.data.list || [];
					if (that.page === 1) {
						that.datalist = newList;
					} else {
						that.datalist = that.datalist.concat(newList);
					}
					that.hasMore = newList.length >= that.limit;
					that.loaded();
				} else {
					app.error(res.msg || '获取记录失败');
				}
				uni.stopPullDownRefresh();
			});
		},
		
		// 切换标签
		switchTab(e) {
			const index = e.currentTarget.dataset.index;
			this.currentTab = index;
			this.page = 1;
			this.hasMore = true;
			this.getdata();
		},
		
		// 分享许愿
		shareWish(e) {
			const id = e.currentTarget.dataset.id;
			app.success('分享功能开发中');
		},
		
		// 编辑许愿
		editWish(e) {
			const id = e.currentTarget.dataset.id;
			const content = e.currentTarget.dataset.content;
			this.editId = id;
			this.editContent = content || '';
			this.showEditModal = true;
		},
		
		// 关闭编辑弹窗
		closeEditModal() {
			this.showEditModal = false;
			this.editContent = '';
			this.editId = 0;
		},
		
		// 保存编辑
		saveEdit() {
			var that = this;
			if (!that.editContent.trim()) {
				app.error('请输入许愿内容');
				return;
			}
			
			that.loading = true;
			app.post('ApiShangxiang/updateWish', {
				id: that.editId,
				wish_content: that.editContent
			}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					app.success('修改成功');
					that.closeEditModal();
					// 更新列表中的数据
					const item = that.datalist.find(item => item.id === that.editId);
					if (item) {
						item.wish_content = that.editContent;
					}
				} else {
					app.error(res.msg || '修改失败');
				}
			});
		},
		
		// 获取类型名称
		getTypeName(type) {
			switch (type) {
				case 'gonghua':
					return '供花';
				case 'gongguo':
					return '供果';
				case 'shangxiang':
					return '上香';
				case 'xuyuan':
					return '许愿';
				default:
					return '';
			}
		},
		
		// 获取类型颜色
		getTypeColor(type) {
			switch (type) {
				case 'gonghua':
					return '#ff6b6b';
				case 'gongguo':
					return '#4ecdc4';
				case 'shangxiang':
					return '#45b7d1';
				case 'xuyuan':
					return '#f9ca24';
				default:
					return '#999';
			}
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			return status === 1 ? 'status-active' : 'status-inactive';
		},
		
		// 获取状态文本
		getStatusText(status) {
			return status === 1 ? '显示中' : '已隐藏';
		},
		
		// 格式化时间
		formatTime(time) {
			return app.formatTime(time);
		},
		
		// 页面加载完成
		loaded() {
			var that = this;
			that.isload = true;
			that.textset = app.globalData.textset;
			uni.setNavigationBarTitle({
				title: that.t('我的许愿记录')
			});
		}
	}
}
</script>

<style>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

.stats-section {
	display: flex;
	background: #fff;
	margin: 20rpx;
	border-radius: 15rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
	flex: 1;
	text-align: center;
}

.stats-number {
	font-size: 36rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.filter-tabs {
	display: flex;
	background: #fff;
	margin: 0 20rpx 20rpx;
	border-radius: 15rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-tab {
	flex: 1;
	text-align: center;
	padding: 15rpx 0;
	font-size: 26rpx;
	color: #666;
	border-radius: 8rpx;
	transition: all 0.3s ease;
}

.filter-tab.active {
	color: #007aff;
	background: rgba(0, 122, 255, 0.1);
	font-weight: bold;
}

.my-wishes-list {
	padding: 0 20rpx 40rpx;
}

.wish-record {
	background: #fff;
	border-radius: 15rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.type-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	color: #fff;
	font-size: 22rpx;
	margin-right: 20rpx;
}

.amount {
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 20rpx;
}

.time {
	font-size: 22rpx;
	color: #999;
}

.record-content {
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 10rpx;
	margin-bottom: 20rpx;
	font-size: 26rpx;
	line-height: 1.5;
	color: #333;
}

.record-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.status {
	font-size: 24rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.status-active {
	color: #52c41a;
	background: #f6ffed;
	border: 1rpx solid #b7eb8f;
}

.status-inactive {
	color: #999;
	background: #f5f5f5;
	border: 1rpx solid #d9d9d9;
}

.actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	border: none;
	background: none;
}

.share-btn {
	color: #1890ff;
	background: #e6f7ff;
	border: 1rpx solid #91d5ff;
}

.edit-btn {
	color: #fa8c16;
	background: #fff7e6;
	border: 1rpx solid #ffd591;
}

.empty-state {
	text-align: center;
	padding: 100rpx 0;
}

.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-content {
	background: #fff;
	border-radius: 20rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.close-btn {
	font-size: 40rpx;
	color: #999;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-body {
	padding: 30rpx;
}

.edit-textarea {
	width: 100%;
	height: 200rpx;
	border: 1rpx solid #d9d9d9;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 26rpx;
	line-height: 1.5;
	resize: none;
	box-sizing: border-box;
}

.char-count {
	font-size: 22rpx;
	color: #999;
	text-align: right;
	margin-top: 10rpx;
	display: block;
}

.modal-footer {
	display: flex;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 20rpx;
}

.cancel-btn {
	flex: 1;
	padding: 24rpx 0;
	border: 1rpx solid #d9d9d9;
	border-radius: 10rpx;
	background: #fff;
	color: #666;
	font-size: 28rpx;
	text-align: center;
}

.confirm-btn {
	flex: 1;
	padding: 24rpx 0;
	border: none;
	border-radius: 10rpx;
	color: #fff;
	font-size: 28rpx;
	text-align: center;
}
</style>