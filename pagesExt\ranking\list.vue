<template>
<view>
	<block v-if="isload">
		<view class="container">
			<!-- 排行榜类型选择 -->
			<view class="tab-box">
				<view class="tab-item" :class="{'active': rankType === 1}" @tap="changeRankType(1)">成员排行</view>
				<view class="tab-item" :class="{'active': rankType === 2}" @tap="changeRankType(2)">进货排行</view>
			</view>
			
			<!-- 筛选条件 -->
			<view class="filter-box">
				<!-- 成员排行筛选 -->
				<view v-if="rankType === 1" class="filter-row">
					<picker :value="productIndex" :range="productList" range-key="name" @change="onProductChange">
						<view class="picker-item">
							<text>{{getProductName}}</text>
							<text class="icon-arrow"></text>
						</view>
					</picker>
					<picker :value="monthIndex" :range="monthList" range-key="name" @change="onMonthChange">
						<view class="picker-item">
							<text>{{getMonthName}}</text>
							<text class="icon-arrow"></text>
						</view>
					</picker>
				</view>
				
				<!-- 进货排行筛选 -->
				<view v-if="rankType === 2" class="filter-row">
					<picker :value="monthIndex" :range="monthList" range-key="name" @change="onMonthChange">
						<view class="picker-item">
							<text>{{getMonthName}}</text>
							<text class="icon-arrow"></text>
						</view>
					</picker>
				</view>
			</view>
			
			<!-- 排行榜列表 -->
			<view class="rank-list">
				<block v-for="(item, index) in rankList" :key="item.id">
					<!-- 成员排行显示 -->
					<view class="rank-item" v-if="rankType === 1">
						<view class="avatar-box" :class="'rank-' + (index + 1)">
							<image class="avatar" :src="item.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
						</view>
						<view class="info">
							<view class="name">{{item.nickname || '匿名用户'}}</view>
							<view class="level">{{item.level_name || '-'}}</view>
						</view>
						<view class="data">
							<view class="num">{{item.total_num || 0}}</view>
							<!-- <view class="amount">￥{{item.total_amount || 0}}</view> -->
						</view>
					</view>
					
					<!-- 进货排行显示 -->
					<view class="rank-item" v-if="rankType === 2">
						<view class="rank-num" :class="'rank-' + (index + 1)">
							<text class="num">{{index + 1}}</text>
							<view class="crown" v-if="index < 3"></view>
						</view>
						<view class="info">
							<view class="name product-link" @tap="goToProduct(item.id)">{{item.name || '-'}}</view>
							<view class="shop-name">{{item.shop_name || '-'}}</view>
						</view>
						<view class="data">
							<view class="num">销量：{{item.total_num || 0}}</view>
							<!-- <view class="amount">￥{{item.total_amount || 0}}</view> -->
						</view>
					</view>
				</block>
			</view>
			
			<!-- 加载更多 -->
			<view class="loading-more" v-if="loading">加载中...</view>
			<view class="no-more" v-if="nomore">没有更多数据了</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			isload: false,
			loading: true,
			nomore: false,
			
			rankType: 1, // 1成员排行 2进货排行
			page: 1,
			limit: 10,
			
			rankList: [],
			productList: [],
			monthList: [],
			productIndex: 0,
			monthIndex: 0,
			currentMonth: '', // 当前选中的月份值
			
			settings: null
		}
	},
	computed: {
		getProductName() {
			if (this.productList.length > 0 && this.productList[this.productIndex]) {
				return this.productList[this.productIndex].name;
			}
			return '全部商品';
		},
		getMonthName() {
			if (this.monthList.length > 0 && this.monthList[this.monthIndex]) {
				return this.monthList[this.monthIndex].name;
			}
			return '选择月份';
		}
	},
	onLoad() {
		this.getMonthList(); // 先获取月份列表
		this.getRankingSetting();
		this.getProductList();
	},
	onPullDownRefresh() {
		this.page = 1;
		this.rankList = [];
		this.getRankingList();
	},
	onReachBottom() {
		if(!this.nomore && !this.loading) {
			this.page++;
			this.getRankingList();
		}
	},
	methods: {
		// 获取排行榜设置
		getRankingSetting() {
			var that = this;
			app.get('ApiJietikaohe/getRankingSetting', {}, function(res) {
				if(res.code == 1) {
					that.settings = res.data;
					that.isload = true;
				}
			});
		},
		
		// 获取商品列表
		getProductList() {
			var that = this;
			app.get('ApiJietikaohe/getProductList', {}, function(res) {
				if(res.code == 1) {
					// 添加全部商品选项
					that.productList = [{
						id: 0,
						name: '全部商品'
					}].concat(res.data || []);
				}
			});
		},
		
		// 获取月份列表
		getMonthList() {
			var that = this;
			app.get('ApiJietikaohe/getMonthList', {}, function(res) {
				if(res.code == 1 && res.data && res.data.length > 0) {
					that.monthList = res.data;
					that.monthIndex = 0; // 默认选中第一个月份
					that.currentMonth = res.data[0].value;
					that.getRankingList(); // 获取第一个月份的数据
				}
			});
		},
		
		// 获取排行榜数据
		getRankingList() {
			var that = this;
			if(!that.currentMonth) {
				return;
			}
			
			that.loading = true;
			var params = {
				type: that.rankType,
				page: that.page,
				limit: that.limit,
				month: that.currentMonth
			};
			
			// 成员排行 - 只有当选择了具体商品时才传product_id
			if(that.rankType == 1 && that.productList[that.productIndex] && that.productList[that.productIndex].id !== 0) {
				params.product_id = that.productList[that.productIndex].id;
			}
			
			app.get('ApiJietikaohe/getRankingList', params, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if(res.code == 1) {
					if(that.page == 1) {
						that.rankList = res.data.list || [];
					} else {
						that.rankList = that.rankList.concat(res.data.list || []);
					}
					that.nomore = that.rankList.length >= (res.data.total || 0);
				} else {
					that.rankList = [];
					that.nomore = true;
				}
			});
		},
		
		// 切换排行类型
		changeRankType(type) {
			if(this.rankType === type) return;
			this.rankType = type;
			this.page = 1;
			this.rankList = [];
			this.getRankingList();
		},
		
		// 选择商品
		onProductChange(e) {
			this.productIndex = e.detail.value;
			this.page = 1;
			this.rankList = [];
			this.getRankingList();
		},
		
		// 选择月份
		onMonthChange(e) {
			this.monthIndex = e.detail.value;
			this.currentMonth = this.monthList[this.monthIndex].value;
			this.page = 1;
			this.rankList = [];
			this.getRankingList();
		},
		
		// 跳转到产品详情页
		goToProduct(id) {
			if(!id) return;
			uni.navigateTo({
				url: '/shopPackage/shop/product?id=' + id
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 30rpx 20rpx;
	background: #F8F9FD;
	min-height: 100vh;
	
	.tab-box {
		display: flex;
		background: #fff;
		padding: 16rpx;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		position: relative;
		overflow: hidden;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 200%;
			background: linear-gradient(180deg, rgba(250, 81, 81, 0.08) 0%, rgba(250, 81, 81, 0) 100%);
			opacity: 0.5;
			pointer-events: none;
		}
		
		.tab-item {
			flex: 1;
			text-align: center;
			font-size: 28rpx;
			color: #666;
			position: relative;
			padding: 24rpx 0;
			transition: all 0.3s ease;
			
			&.active {
				color: #FA5151;
				font-weight: 600;
				transform: scale(1.02);
				
				&:after {
					content: '';
					position: absolute;
					left: 50%;
					bottom: 0;
					transform: translateX(-50%);
					width: 48rpx;
					height: 6rpx;
					background: linear-gradient(90deg, #FA5151 0%, #FF7676 100%);
					border-radius: 6rpx;
				}
			}
		}
	}
	
	.filter-box {
		background: #fff;
		padding: 24rpx;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		
		.filter-row {
			display: flex;
			align-items: center;
			gap: 24rpx;
			
			.picker-item {
				flex: 1;
				height: 76rpx;
				background: #F8F9FD;
				border-radius: 12rpx;
				padding: 0 24rpx;
				font-size: 28rpx;
				color: #333;
				display: flex;
				align-items: center;
				justify-content: space-between;
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.98);
					background: #F0F2F5;
				}
				
				.icon-arrow {
					display: inline-block;
					width: 0;
					height: 0;
					border-left: 10rpx solid transparent;
					border-right: 10rpx solid transparent;
					border-top: 10rpx solid #999;
					margin-left: 12rpx;
					transition: transform 0.3s ease;
				}
			}
		}
	}
	
	.rank-list {
		background: #fff;
		border-radius: 16rpx;
		padding: 8rpx 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		
		.rank-item {
			display: flex;
			align-items: center;
			padding: 24rpx 16rpx;
			border-bottom: 1px solid rgba(0, 0, 0, 0.04);
			transition: all 0.3s ease;
			
			&:last-child {
				border-bottom: none;
			}
			
			&:hover {
				background: rgba(250, 81, 81, 0.02);
				transform: translateX(4rpx);
			}
			
			.avatar-box {
				width: 80rpx;
				height: 80rpx;
				margin-right: 24rpx;
				border-radius: 50%;
				padding: 4rpx;
				position: relative;
				
				&.rank-1 {
					background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
					box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
				}
				
				&.rank-2 {
					background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
					box-shadow: 0 4rpx 12rpx rgba(169, 169, 169, 0.3);
				}
				
				&.rank-3 {
					background: linear-gradient(135deg, #CD7F32 0%, #B87333 100%);
					box-shadow: 0 4rpx 12rpx rgba(205, 127, 50, 0.3);
				}
				
				.avatar {
					width: 100%;
					height: 100%;
					border-radius: 50%;
					background-color: #f5f5f5;
				}
			}
			
			.info {
				flex: 1;
				padding: 0 16rpx;
				
				.name {
					font-size: 30rpx;
					color: #333;
					margin-bottom: 8rpx;
					font-weight: 500;
					
					&.product-link {
						position: relative;
						display: inline-block;
						padding-bottom: 4rpx;
						
						&:after {
							content: '';
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							height: 2rpx;
							background: currentColor;
							transform: scaleX(0);
							transition: transform 0.3s ease;
							transform-origin: right;
						}
						
						&:active {
							opacity: 0.8;
						}
						
						&:hover:after {
							transform: scaleX(1);
							transform-origin: left;
						}
					}
					
					&.rank-1 {
						color: #FFD700;
						font-weight: 600;
					}
					
					&.rank-2 {
						color: #C0C0C0;
						font-weight: 600;
					}
					
					&.rank-3 {
						color: #CD7F32;
						font-weight: 600;
					}
				}
				
				.level,
				.shop-name {
					font-size: 24rpx;
					color: #999;
					display: flex;
					align-items: center;
					
					&::before {
						content: '';
						display: inline-block;
						width: 8rpx;
						height: 8rpx;
						background: #FA5151;
						border-radius: 50%;
						margin-right: 8rpx;
						opacity: 0.5;
					}
				}
			}
			
			.data {
				text-align: right;
				padding-left: 24rpx;
				
				.num {
					font-size: 32rpx;
					color: #333;
					font-weight: 600;
					margin-bottom: 8rpx;
					background: linear-gradient(90deg, #FA5151 0%, #FF7676 100%);
					-webkit-background-clip: text;
					color: transparent;
				}
			}
		}
	}
	
	.loading-more,
	.no-more {
		text-align: center;
		font-size: 24rpx;
		color: #999;
		padding: 32rpx 0;
		letter-spacing: 2rpx;
		
		&::before,
		&::after {
			content: '';
			display: inline-block;
			width: 80rpx;
			height: 1px;
			background: linear-gradient(90deg, transparent, #ddd);
			margin: 0 20rpx;
			vertical-align: middle;
		}
		
		&::after {
			background: linear-gradient(90deg, #ddd, transparent);
		}
	}
	
	.rank-num {
		width: 64rpx;
		height: 64rpx;
		line-height: 64rpx;
		text-align: center;
		font-size: 32rpx;
		font-weight: 600;
		margin-right: 24rpx;
		border-radius: 50%;
		background: #F8F9FD;
		color: #999;
		position: relative;
		transition: all 0.3s ease;
		
		.num {
			position: relative;
			z-index: 2;
		}
		
		.crown {
			position: absolute;
			top: -16rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 32rpx;
			height: 24rpx;
			background: url('data:image/svg+xml;utf8,<svg t="1709799047439" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4286"><path d="M910.8 405.8L814 492.9 755.3 333c-2.4-6.5-7.6-11.6-14.1-13.7-6.5-2.2-13.7-1.1-19.3 2.9L548.1 450 430.1 161.5c-3.2-7.9-10.9-13.1-19.5-13.1s-16.2 5.2-19.5 13.1L273.3 450l-174-127.9c-5.7-4.1-12.8-5.1-19.3-2.9-6.5 2.2-11.7 7.2-14.1 13.7L7.2 492.9l-96.8-87.1c-5.4-4.9-13-6.3-19.8-3.8-6.7 2.6-11.6 8.5-12.8 15.6-19.2 113.6 6.1 230.4 69.9 321.2 63.8 90.8 159.4 153.8 268.9 177.2 20.8 4.4 42 7.5 63.1 9.2 17.7 1.4 35.5 2.1 53.2 2.1 17.8 0 35.6-0.7 53.3-2.1 21.2-1.7 42.3-4.8 63.1-9.2 109.5-23.4 205.1-86.4 268.9-177.2 63.8-90.8 89.1-207.6 69.9-321.2-1.2-7.1-6.1-13.1-12.8-15.6-6.8-2.5-14.4-1.1-19.8 3.8z" fill="%23FFD700" p-id="4287"></path></svg>') no-repeat center/contain;
			z-index: 1;
		}
		
		&.rank-1 {
			background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
			color: #fff;
			box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
			transform: scale(1.1);
			
			&::after {
				content: '';
				position: absolute;
				top: -4rpx;
				left: -4rpx;
				right: -4rpx;
				bottom: -4rpx;
				border-radius: 50%;
				background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.2) 100%);
				z-index: 0;
				animation: pulse 1.5s ease-in-out infinite;
			}
		}
		
		&.rank-2 {
			background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
			color: #fff;
			box-shadow: 0 4rpx 12rpx rgba(169, 169, 169, 0.3);
			transform: scale(1.05);
		}
		
		&.rank-3 {
			background: linear-gradient(135deg, #CD7F32 0%, #B87333 100%);
			color: #fff;
			box-shadow: 0 4rpx 12rpx rgba(205, 127, 50, 0.3);
			transform: scale(1.02);
		}
	}
	
	@keyframes pulse {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		100% {
			transform: scale(1.5);
			opacity: 0;
		}
	}
	
	.info {
		flex: 1;
		padding: 0 16rpx;
		
		.name {
			font-size: 30rpx;
			color: #333;
			margin-bottom: 8rpx;
			font-weight: 500;
			
			&.product-link {
				position: relative;
				display: inline-block;
				padding-bottom: 4rpx;
				
				&:after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 0;
					width: 100%;
					height: 2rpx;
					background: currentColor;
					transform: scaleX(0);
					transition: transform 0.3s ease;
					transform-origin: right;
				}
				
				&:active {
					opacity: 0.8;
				}
				
				&:hover:after {
					transform: scaleX(1);
					transform-origin: left;
				}
			}
			
			&.rank-1 {
				color: #FFD700;
				font-weight: 600;
			}
			
			&.rank-2 {
				color: #C0C0C0;
				font-weight: 600;
			}
			
			&.rank-3 {
				color: #CD7F32;
				font-weight: 600;
			}
		}
	}
}
</style> 