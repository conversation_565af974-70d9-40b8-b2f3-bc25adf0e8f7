<template>
	<view class="live-container">
		<!-- 视频播放区域 -->
		<video 
			class="live-video" 
			:src="videoSrc"
			:poster="coverImage"
			object-fit="contain"
			:autoplay="true"
			:loop="true"
			:controls="false"
			:show-center-play-btn="false"
			@error="handleVideoError"
			@waiting="handleVideoWaiting"
			@loadeddata="handleVideoLoaded"
		></video>
		
		<!-- 直播内容区域 -->
		<view class="content-area">
			<!-- 头部信息 -->
			<live-header 
				:data="anchorInfo"
				@onShare="handleShare"
			></live-header>
			
			<!-- 消息列表 -->
			<live-msg ref="liveMsg"></live-msg>
			
			<!-- 礼物动画 -->
			<live-gift ref="liveGift"></live-gift>
			
			<!-- 底部操作栏 -->
			<live-footer 
				:room_id="roomId"
				@sendGift="handleSendGift"
				@sendMsg="handleSendMsg"
				@sendLike="handleSendLike"
			></live-footer>
			
			<!-- 观看人数 -->
			<view class="online-count">
				<text>{{onlineCount}}人观看</text>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading" v-if="isLoading">
			<view class="loading-content">
				<text>加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
import liveHeader from './components/liveHeader.vue'
import liveMsg from './components/liveMsg.vue'
import liveGift from './components/liveGift.vue'
import liveFooter from './components/liveFooter.vue'

export default {
	components: {
		liveHeader,
		liveMsg,
		liveGift,
		liveFooter
	},
	
	data() {
		return {
			roomId: '',
			videoSrc: '',
			coverImage: '',
			isLoading: true,
			onlineCount: 0,
			anchorInfo: {
				anchor: {
					headimg: '',
					nickname: '主播昵称'
				},
				online: 0,
				title: '直播标题'
			},
			retryCount: 0,
			maxRetries: 3
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.roomId = options.id
			this.initLiveRoom()
		}
	},
	
	methods: {
		// 初始化直播间
		async initLiveRoom() {
			try {
				// 这里替换为您的实际接口
				const res = await this.getLiveRoomInfo(this.roomId)
				if (res.code === 0) {
					this.anchorInfo = res.data
					this.videoSrc = res.data.stream_url
					this.coverImage = res.data.cover_img
					this.onlineCount = res.data.online_count || 0
				}
			} catch (error) {
				console.error('获取直播间信息失败:', error)
			} finally {
				this.isLoading = false
			}
		},
		
		// 视频相关处理方法
		handleVideoError(e) {
			console.error('视频播放错误:', e)
			if (this.retryCount < this.maxRetries) {
				this.retryCount++
				setTimeout(() => {
					this.initLiveRoom()
				}, 2000)
			}
		},
		
		handleVideoWaiting() {
			this.isLoading = true
		},
		
		handleVideoLoaded() {
			this.isLoading = false
			this.retryCount = 0
		},
		
		// 消息处理方法
		handleSendMsg(msg) {
			this.$refs.liveMsg.addMessage({
				nickname: '用户昵称',
				message: msg
			})
		},
		
		// 礼物处理方法
		handleSendGift(gift) {
			this.$refs.liveGift.sendGift(gift)
		},
		
		// 点赞处理方法
		handleSendLike() {
			// 实现点赞动画
		},
		
		// 分享处理方法
		handleShare() {
			uni.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
		},
		
		// 模拟获取直播间信息的接口
		getLiveRoomInfo(roomId) {
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve({
						code: 0,
						data: {
							stream_url: 'https://example.com/live.m3u8',
							cover_img: 'https://example.com/cover.jpg',
							anchor: {
								headimg: 'https://example.com/avatar.jpg',
								nickname: '测试主播'
							},
							online: 1,
							online_count: 100,
							title: '测试直播'
						}
					})
				}, 1000)
			})
		}
	}
}
</script>

<style lang="scss">
.live-container {
	position: relative;
	width: 100%;
	height: 100vh;
	background-color: #000;
	
	.live-video {
		width: 100%;
		height: 100%;
	}
	
	.content-area {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 10;
	}
	
	.online-count {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 30rpx;
		padding: 4rpx 20rpx;
		
		text {
			color: #fff;
			font-size: 24rpx;
		}
	}
	
	.loading {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
		
		.loading-content {
			background: rgba(0, 0, 0, 0.8);
			border-radius: 10rpx;
			padding: 20rpx 40rpx;
			
			text {
				color: #fff;
				font-size: 28rpx;
			}
		}
	}
}
</style> 