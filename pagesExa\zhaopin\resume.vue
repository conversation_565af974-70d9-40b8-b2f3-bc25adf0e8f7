<template>
  <view class="resume-page">
    <view class="header" :style="'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">
      <text class="title">完善简历</text>
      <text class="subtitle">完善您的简历信息，获得更多工作机会</text>
      
      <view class="step-nav">
        <view 
          v-for="(step, index) in steps" 
          :key="index"
          class="step-item"
          :class="{ 'active': currentStep === index, 'completed': index < currentStep }"
          @tap="jumpToStep(index)"
        >
          <view class="step-number" :style="currentStep === index || index < currentStep ? 'background-color:'+t('color1')+';color:#fff' : ''">
            {{ index + 1 }}
          </view>
          <text class="step-text">{{ step.title }}</text>
        </view>
      </view>
    </view>
    
    <swiper 
      class="content" 
      :style="{ height: contentHeight + 'px' }"
      :current="currentStep"
      @change="onSwiperChange"
      :disable-touch="true"
    >
      <!-- 基本信息 -->
      <swiper-item>
        <scroll-view scroll-y class="step-content">
          <view class="section basic-info">
            <view class="section-header">
              <text class="section-title">
                <view class="title-bar" :style="'background:'+t('color1')"></view>
                基本信息
              </text>
            </view>
            
            <view class="avatar-wrapper">
              <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
                <view v-if="formData.avatar" class="layui-imgbox">
                  <view class="layui-imgbox-close" @tap="removeAvatar"><image src="/static/img/ico-del.png"></image></view>
                  <view class="layui-imgbox-img">
                    <image :src="formData.avatar" @tap="previewImage" :data-url="formData.avatar" mode="aspectFill"></image>
                  </view>
                </view>
                <view v-else class="uploadbtn" 
                  :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" 
                  @tap="chooseAvatar">
                </view>
              </view>
              <text class="upload-text">点击上传头像</text>
            </view>
            
            <view class="form-group">
              <view class="input-wrapper">
                <text class="input-label required">姓名</text>
                <input 
                  v-model="formData.name" 
                  placeholder="请输入您的真实姓名" 
                  class="input-item" 
                  maxlength="20"
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                />
              </view>
              
              <view class="input-wrapper">
                <text class="input-label required">手机号码</text>
                <input 
                  v-model="formData.phone" 
                  type="number" 
                  placeholder="请输入手机号码" 
                  class="input-item"
                  maxlength="11"
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                />
              </view>
              
              <view class="input-row">
                <view class="input-wrapper half">
                  <text class="input-label required">性别</text>
                  <picker 
                    mode="selector" 
                    :range="genderOptions" 
                    @change="onGenderChange" 
                    class="input-item"
                    :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                  >
                    <view class="picker-item">
                      {{formData.gender || '请选择性别'}}
                    </view>
                  </picker>
                </view>
                
                <view class="input-wrapper half">
                  <text class="input-label required">出生日期</text>
                  <picker 
                    mode="date" 
                    :start="startDate" 
                    :end="endDate" 
                    @change="onBirthChange" 
                    class="input-item"
                    :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                  >
                    <view class="picker-item">
                      {{formData.birthday || '请选择出生日期'}}
                    </view>
                  </picker>
                </view>
              </view>
              
              <view class="input-wrapper">
                <text class="input-label">邮箱</text>
                <input 
                  v-model="formData.email" 
                  type="text"
                  placeholder="请输入邮箱地址" 
                  class="input-item"
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                />
              </view>
              
              <view class="input-wrapper">
                <text class="input-label">居住地址</text>
                <input 
                  v-model="formData.address" 
                  type="text"
                  placeholder="请输入居住地址" 
                  class="input-item"
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                />
              </view>

              <view class="input-row">
                <view class="input-wrapper half">
                  <text class="input-label required">民族</text>
                  <picker 
                    mode="selector" 
                    :range="nationOptions" 
                    @change="onNationChange" 
                    class="input-item"
                    :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                  >
                    <view class="picker-item">
                      {{formData.nation || '请选择民族'}}
                    </view>
                  </picker>
                </view>
                
                <view class="input-wrapper half">
                  <text class="input-label required">年龄</text>
                  <input 
                    v-model="formData.age" 
                    type="number"
                    placeholder="请输入年龄" 
                    class="input-item"
                    maxlength="3"
                    :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                  />
                </view>
              </view>

              <view class="input-wrapper">
                <text class="input-label required">文化程度</text>
                <picker 
                  mode="selector" 
                  :range="cultureOptions" 
                  @change="onCultureChange" 
                  class="input-item"
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                >
                  <view class="picker-item">
                    {{formData.culture || '请选择文化程度'}}
                  </view>
                </picker>
              </view>
            </view>
          </view>
        </scroll-view>
      </swiper-item>

      <!-- 教育经历 -->
      <swiper-item>
        <scroll-view scroll-y class="step-content">
          <view class="section education">
            <view class="section-header">
              <text class="section-title">
                <view class="title-bar" :style="'background:linear-gradient(to bottom,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"></view>
                教育经历
              </text>
              <view class="add-btn" @tap="addEducation" :style="'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)'">添加教育经历</view>
            </view>
            <view v-if="formData.education.length === 0" class="empty-tip">
              <image src="/static/images/empty.png" mode="aspectFit" class="empty-icon"></image>
              <text>暂无教育经历，点击上方添加</text>
            </view>
            <view v-else v-for="(item, index) in formData.education" :key="index" class="edu-item">
              <view class="item-header">
                <text class="item-index" :style="'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);box-shadow:0 4rpx 12rpx rgba('+t('color1rgb')+',0.2);'+(isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : '')">
                  {{ index + 1 }}
                </text>
                <view class="delete-btn" @tap="deleteEducation(index)">
                  <text class="delete-icon">×</text>
                  <text>删除</text>
                </view>
              </view>
              <view class="form-group">
                <view class="input-wrapper">
                  <text class="input-label">学校名称</text>
                  <input v-model="item.school" placeholder="请输入学校名称" class="input-item" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''" />
                </view>
                <view class="input-wrapper">
                  <text class="input-label">专业</text>
                  <input v-model="item.major" placeholder="请输入专业名称" class="input-item" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''" />
                </view>
                <view class="input-wrapper">
                  <text class="input-label">学历</text>
                  <picker mode="selector" :range="educationLevels" @change="(e) => onEduLevelChange(e, index)" class="input-item" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''">
                    <view class="picker-item">{{item.level || '请选择学历'}}</view>
                  </picker>
                </view>
                <view class="date-range">
                  <view class="date-item">
                    <text class="input-label">入学时间</text>
                    <picker mode="date" :start="startDate" :end="endDate" @change="(e) => onEduStartChange(e, index)" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''">
                      <view class="picker-item">{{item.startDate || '请选择入学时间'}}</view>
                    </picker>
                  </view>
                  <view class="date-separator"></view>
                  <view class="date-item">
                    <text class="input-label">毕业时间</text>
                    <picker mode="date" :start="startDate" :end="endDate" @change="(e) => onEduEndChange(e, index)" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''">
                      <view class="picker-item">{{item.endDate || '请选择毕业时间'}}</view>
                    </picker>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </swiper-item>

      <!-- 工作经历 -->
      <swiper-item>
        <scroll-view scroll-y class="step-content">
          <view class="section work-exp">
            <view class="section-header">
              <text class="section-title">
                <view class="title-bar" :style="'background:linear-gradient(to bottom,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"></view>
                工作经历
              </text>
              <view class="add-btn" @tap="addWork" :style="'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1);'+(isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : '')">添加工作经历</view>
            </view>
            <view v-if="formData.workExperience.length === 0" class="empty-tip">
              <image src="/static/images/empty.png" mode="aspectFit" class="empty-icon"></image>
              <text>暂无工作经历，点击上方添加</text>
            </view>
            <view v-else v-for="(item, index) in formData.workExperience" :key="index" class="work-item">
              <view class="item-header">
                <text class="item-index" :style="'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);box-shadow:0 4rpx 12rpx rgba('+t('color1rgb')+',0.2);'+(isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : '')">
                  {{ index + 1 }}
                </text>
                <view class="delete-btn" @tap="deleteWork(index)">
                  <text class="delete-icon">×</text>
                  <text>删除</text>
                </view>
              </view>
              <view class="form-group">
                <view class="input-wrapper">
                  <text class="input-label">公司名称</text>
                  <input v-model="item.company" placeholder="请输入公司名称" class="input-item" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''" />
                </view>
                <view class="input-wrapper">
                  <text class="input-label">职位</text>
                  <input v-model="item.position" placeholder="请输入职位名称" class="input-item" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''" />
                </view>
                <view class="date-range">
                  <view class="date-item">
                    <text class="input-label">入职时间</text>
                    <picker mode="date" :start="startDate" :end="endDate" @change="(e) => onWorkStartChange(e, index)" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''">
                      <view class="picker-item">{{item.startDate || '请选择入职时间'}}</view>
                    </picker>
                  </view>
                  <view class="date-separator"></view>
                  <view class="date-item">
                    <text class="input-label">离职时间</text>
                    <picker mode="date" :start="startDate" :end="endDate" @change="(e) => onWorkEndChange(e, index)" :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''">
                      <view class="picker-item">{{item.endDate || '请选择离职时间'}}</view>
                    </picker>
                  </view>
                </view>
                <view class="input-wrapper">
                  <text class="input-label">工作描述</text>
                  <textarea 
                    v-model="item.description" 
                    placeholder="请描述您的工作职责和成果" 
                    class="textarea-item"
                    :maxlength="500"
                    show-count
                    :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                  ></textarea>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </swiper-item>

      <!-- 求职意向 -->
      <swiper-item>
        <scroll-view scroll-y class="step-content">
          <view class="section job-intention">
            <view class="section-header">
              <text class="section-title">
                <view class="title-bar" :style="'background:linear-gradient(to bottom,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"></view>
                求职意向
              </text>
            </view>
            <view class="form-group">
              <view class="input-wrapper">
                <text class="input-label">期望职位</text>
                <input 
                  v-model="formData.expectedPosition" 
                  placeholder="请输入期望职位" 
                  class="input-item" 
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                />
              </view>
              
              <view class="input-wrapper">
                <text class="input-label">期望薪资</text>
                <view class="salary-input">
                  <input 
                    v-model="formData.expectedSalary" 
                    type="digit"
                    placeholder="请输入期望薪资" 
                    class="input-item" 
                    :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                  />
                  <text class="salary-unit">元/月</text>
                </view>
              </view>
              
              <view class="input-wrapper">
                <text class="input-label">期望城市</text>
                <picker 
                  mode="selector" 
                  :range="cityList" 
                  @change="onCityChange" 
                  class="input-item"
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                >
                  <view class="picker-item">
                    {{formData.expectedCity || '请选择期望城市'}}
                  </view>
                </picker>
              </view>
              
              <view class="input-wrapper">
                <text class="input-label">到岗时间</text>
                <picker 
                  mode="selector" 
                  :range="arrivalTimeOptions" 
                  @change="onArrivalTimeChange" 
                  class="input-item"
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                >
                  <view class="picker-item">
                    {{formData.arrivalTime || '请选择到岗时间'}}
                  </view>
                </picker>
              </view>
              
              <view class="input-wrapper">
                <text class="input-label">工作类型</text>
                <view class="job-type-group">
                  <view 
                    v-for="(type, index) in jobTypes" 
                    :key="index"
                    class="job-type-item"
                    :class="{ 'active': formData.jobType === type }"
                    @tap="selectJobType(type)"
                    :style="formData.jobType === type ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1') : ''"
                  >
                    {{ type }}
                  </view>
                </view>
              </view>
              
              <view class="input-wrapper">
                <text class="input-label">补充说明</text>
                <textarea 
                  v-model="formData.additionalInfo" 
                  placeholder="请补充说明您的其他期望（如：行业偏好、公司规模等）" 
                  class="textarea-item"
                  :maxlength="200"
                  show-count
                  :style="isFocused ? 'border-color:'+t('color1')+';box-shadow:0 0 0 3rpx rgba('+t('color1rgb')+',0.1)' : ''"
                ></textarea>
              </view>
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>

    <view class="footer safe-area-bottom">
      <view class="btn-group">
        <button 
          class="nav-btn prev-btn" 
          v-if="currentStep > 0"
          @tap="prevStep"
        >上一步</button>
        
        <button 
          class="nav-btn next-btn" 
          v-if="currentStep < steps.length - 1"
          @tap="nextStep"
          :disabled="!canGoNext"
          :style="'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"
        >下一步</button>
        
        <button 
          class="submit-btn" 
          v-if="currentStep === steps.length - 1"
          @tap="submitResume" 
          :disabled="isSubmitting"
          :style="'background:linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"
        >
          <text class="btn-text">{{ isSubmitting ? '保存中...' : '保存简历' }}</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      currentStep: 0,
      steps: [
        { title: '基本信息', required: ['name', 'phone', 'gender', 'birthday'] },
        { title: '教育经历', required: [] },
        { title: '工作经历', required: [] },
        { title: '求职意向', required: ['expectedPosition', 'expectedSalary', 'expectedCity'] }
      ],
      startDate: '1960-01-01',
      endDate: new Date().toISOString().split('T')[0],
      genderOptions: ['男', '女'],
      educationLevels: ['高中', '专科', '本科', '硕士', '博士'],
      cityList: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '南京'],
      arrivalTimeOptions: ['随时到岗', '1周内', '2周内', '1个月内', '1-3个月', '待商议'],
      jobTypes: ['全职', '兼职', '实习'],
      nationOptions: [
        '汉族','壮族','满族','回族','苗族','维吾尔族','土家族','彝族','蒙古族','藏族','布依族','侗族','瑶族','朝鲜族','白族',
        '哈尼族','哈萨克族','黎族','傣族','畲族','傈僳族','仡佬族','东乡族','高山族','拉祜族','水族','佤族','纳西族','羌族',
        '土族','仫佬族','锡伯族','柯尔克孜族','达斡尔族','景颇族','毛南族','撒拉族','布朗族','塔吉克族','阿昌族','普米族',
        '鄂温克族','怒族','京族','基诺族','德昂族','保安族','俄罗斯族','裕固族','乌孜别克族','门巴族','鄂伦春族','独龙族',
        '塔塔尔族','赫哲族','珞巴族'
      ],
      cultureOptions: ['小学', '初中', '高中', '专科', '本科', '硕士研究生', '博士研究生'],
      formData: {
        name: '',
        phone: '',
        gender: '',
        birthday: '',
        education: [],
        workExperience: [],
        expectedPosition: '',
        expectedSalary: '',
        expectedCity: '',
        arrivalTime: '',
        jobType: '全职',
        additionalInfo: '',
        avatar: '',
        email: '',
        address: '',
        nation: '',
        age: '',
        culture: '',
      },
      contentHeight: 0,
      isSubmitting: false,
      pre_url: '',
      loading: false,
      isload: false,
      isFocused: false
    }
  },
  computed: {
    canGoNext() {
      const currentStepData = this.steps[this.currentStep]
      if (!currentStepData.required.length) return true
      
      return currentStepData.required.every(field => {
        const value = this.formData[field]
        return value && value.toString().trim() !== ''
      })
    }
  },
  onLoad() {
    const systemInfo = uni.getSystemInfoSync()
    this.contentHeight = systemInfo.windowHeight - uni.upx2px(380)
    this.pre_url = app.globalData.pre_url
    this.getResumeData()
  },
  methods: {
    onGenderChange(e) {
      this.formData.gender = this.genderOptions[e.detail.value]
    },
    onBirthChange(e) {
      this.formData.birthday = e.detail.value
    },
    addEducation() {
      this.formData.education.push({
        school: '',
        major: '',
        level: '',
        startDate: '',
        endDate: ''
      })
    },
    deleteEducation(index) {
      var that = this;
      var edu = this.formData.education[index];
      
      // 如果有id，则调用删除接口
      if(edu.id) {
        app.post("ApiZhaopin/deleteEducation", {
          id: edu.id
        }, function(res) {
          if(res.status == 1) {
            that.formData.education.splice(index, 1);
            app.error('删除成功');
          } else {
            app.error(res.msg);
          }
        });
      } else {
        // 如果没有id，直接从数组中删除
        that.formData.education.splice(index, 1);
      }
    },
    onEduLevelChange(e, index) {
      this.formData.education[index].level = this.educationLevels[e.detail.value]
    },
    onEduStartChange(e, index) {
      this.formData.education[index].startDate = e.detail.value
    },
    onEduEndChange(e, index) {
      this.formData.education[index].endDate = e.detail.value
    },
    addWork() {
      this.formData.workExperience.push({
        company: '',
        position: '',
        startDate: '',
        endDate: '',
        description: ''
      })
    },
    deleteWork(index) {
      var that = this;
      var work = this.formData.workExperience[index];
      
      // 如果有id，则调用删除接口
      if(work.id) {
        app.post("ApiZhaopin/deleteWork", {
          id: work.id
        }, function(res) {
          if(res.status == 1) {
            that.formData.workExperience.splice(index, 1);
            app.error('删除成功');
          } else {
            app.error(res.msg);
          }
        });
      } else {
        // 如果没有id，直接从数组中删除
        that.formData.workExperience.splice(index, 1);
      }
    },
    onWorkStartChange(e, index) {
      this.formData.workExperience[index].startDate = e.detail.value
    },
    onWorkEndChange(e, index) {
      this.formData.workExperience[index].endDate = e.detail.value
    },
    onCityChange(e) {
      this.formData.expectedCity = this.cityList[e.detail.value]
    },
    onArrivalTimeChange(e) {
      this.formData.arrivalTime = this.arrivalTimeOptions[e.detail.value]
    },
    selectJobType(type) {
      this.formData.jobType = type
    },
    jumpToStep(index) {
      if (index < this.currentStep || this.canGoNext) {
        this.currentStep = index
      } else {
        uni.showToast({
          title: '请完善当前步骤',
          icon: 'none'
        })
      }
    },
    onSwiperChange(e) {
      const { current } = e.detail
      this.currentStep = current
    },
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    nextStep() {
      if (this.currentStep < this.steps.length - 1 && this.canGoNext) {
        this.currentStep++
      }
    },
    submitResume() {
      var that = this;
      var formData = this.formData;
      
      // 表单验证
      if (!formData.name) {
        app.error('请填写姓名');
        return false;
      }
      if (!formData.phone) {
        app.error('请填写手机号码');
        return false;
      }
      if (!formData.gender) {
        app.error('请选择性别');
        return false;
      }
      if (!formData.birthday) {
        app.error('请选择出生日期');
        return false;
      }
      if (!formData.avatar) {
        app.error('请上传头像');
        return false;
      }
      if (!formData.nation) {
        app.error('请选择民族');
        return false;
      }
      if (!formData.age) {
        app.error('请输入年龄');
        return false;
      }
      if (!formData.culture) {
        app.error('请选择文化程度');
        return false;
      }
      if (!formData.address) {
        app.error('请输入居住地址');
        return false;
      }
      if (formData.email && !this.validateEmail(formData.email)) {
        app.error('请输入正确的邮箱格式');
        return false;
      }
      
      // 构造提交数据
      var submitData = {
        name: formData.name,
        avatar: formData.avatar,
        gender: formData.gender === '男' ? 1 : 2,
        birthday: formData.birthday,
        work_years: 0, // 默认应届生
        education: formData.culture, // 使用文化程度作为最高学历
        phone: formData.phone,
        email: formData.email || '',
        current_status: 0, // 默认随时到岗
        expect_position: formData.expectedPosition,
        expect_salary: formData.expectedSalary,
        expect_city: formData.expectedCity,
        self_evaluation: formData.additionalInfo || '',
        nation: formData.nation,
        age: formData.age,
        address: formData.address,
        culture: formData.culture,
        arrival_time: formData.arrivalTime || ''
      };
      
      app.showLoading('提交中');
      // 保存基本信息
      app.post("ApiZhaopin/saveResumeBasic", submitData, function (res) {
        if(res.status == 1){
          var resume_id = res.data.resume_id;
          
          // 保存教育经历
          var eduPromises = formData.education.map(edu => {
            return new Promise((resolve, reject) => {
              app.post("ApiZhaopin/saveEducation", {
                id: edu.id || 0,
                school_name: edu.school,
                major: edu.major,
                education: edu.level,
                start_time: edu.startDate,
                end_time: edu.endDate,
                description: edu.description || ''
              }, resolve);
            });
          });
          
          // 保存工作经历
          var workPromises = formData.workExperience.map(work => {
            return new Promise((resolve, reject) => {
              app.post("ApiZhaopin/saveWork", {
                id: work.id || 0,
                company_name: work.company,
                position: work.position,
                department: work.department || '',
                start_time: work.startDate,
                end_time: work.endDate,
                description: work.description || ''
              }, resolve);
            });
          });
          
          // 等待所有保存完成
          Promise.all([...eduPromises, ...workPromises]).then(() => {
            app.showLoading(false);
            app.error('保存成功');
            setTimeout(function () {
              app.goto(app.globalData.indexurl);
            }, 1000);
          });
        } else {
          app.showLoading(false);
          app.error(res.msg);
        }
      });
    },
    chooseAvatar() {
      var that = this;
      app.chooseImage(function(urls) {
        if(urls && urls.length > 0) {
          that.formData.avatar = urls[0];
        }
      }, 1)
    },
    removeAvatar() {
      this.formData.avatar = '';
    },
    previewImage(e) {
      var url = e.currentTarget.dataset.url;
      if(url) {
        uni.previewImage({
          urls: [url]
        });
      }
    },
    validateEmail(email) {
      const reg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return reg.test(email);
    },
    // 获取简历数据
    getResumeData() {
      var that = this;
      that.loading = true;
      app.get('ApiZhaopin/getResumeDetail', {}, function (res) {
        that.loading = false;
        if (res.status == 1) {
          // 设置表单数据
          if(res.data) {
            // 处理基本信息
            that.formData = {
              name: res.data.name || '',
              phone: res.data.phone || '',
              gender: res.data.gender == 1 ? '男' : '女',
              birthday: res.data.birthday || '',
              avatar: res.data.avatar || '',
              email: res.data.email || '',
              address: res.data.address || '',
              nation: res.data.nation || '',
              age: res.data.age || '',
              culture: res.data.culture || '',
              education: [], // 初始化为空数组
              workExperience: [], // 初始化为空数组
              expectedPosition: res.data.expect_position || '',
              expectedSalary: res.data.expect_salary || '',
              expectedCity: res.data.expect_city || '',
              additionalInfo: res.data.self_evaluation || '',
              arrivalTime: res.data.arrival_time || '',
              jobType: '全职'
            };

            // 处理教育经历 - 直接赋值而不是追加
            that.formData.education = (res.data.education_list || []).map(item => ({
              id: item.id || 0,
              school: item.school_name,
              major: item.major,
              level: item.education,
              startDate: item.start_time,
              endDate: item.end_time,
              description: item.description
            }));

            // 处理工作经历 - 直接赋值而不是追加
            that.formData.workExperience = (res.data.work_list || []).map(item => ({
              id: item.id || 0,
              company: item.company_name,
              position: item.position,
              department: item.department,
              startDate: item.start_time,
              endDate: item.end_time,
              description: item.description
            }));
          }
          that.loaded();
        }
      });
    },
    // 加载完成
    loaded() {
      this.isload = true;
    },
    onNationChange(e) {
      this.formData.nation = this.nationOptions[e.detail.value]
    },
    onCultureChange(e) {
      this.formData.culture = this.cultureOptions[e.detail.value]
    }
  },
  watch: {
    'formData.email'(newVal) {
      if (newVal && !this.validateEmail(newVal)) {
        uni.showToast({
          title: '请输入正确的邮箱格式',
          icon: 'none'
        });
      } 
    }
  }
}
</script>

<style lang="scss" scoped>
.resume-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  
  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    padding: 40rpx 30rpx 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
    
    .title {
      font-size: 40rpx;
      font-weight: 600;
      color: #ffffff;
      letter-spacing: 1px;
    }
    
    .subtitle {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.95);
      margin-top: 12rpx;
      margin-bottom: 36rpx;
      letter-spacing: 0.5px;
    }
    
    .step-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: rgba(255, 255, 255, 0.12);
      border-radius: 16rpx;
      padding: 24rpx 20rpx;
      backdrop-filter: blur(10px);
      
      .step-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        
        &:not(:last-child)::after {
          content: '';
          position: absolute;
          right: -50%;
          top: 20rpx;
          width: 100%;
          height: 2rpx;
          background-color: rgba(255, 255, 255, 0.25);
          z-index: 0;
        }
        
        .step-number {
          width: 44rpx;
          height: 44rpx;
          background-color: rgba(255, 255, 255, 0.25);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 26rpx;
          color: #ffffff;
          margin-bottom: 12rpx;
          position: relative;
          z-index: 1;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          font-weight: 500;
        }
        
        .step-text {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          letter-spacing: 0.5px;
        }
        
        &.active {
          .step-number {
            background-color: #ffffff;
            transform: scale(1.15);
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
          }
          
          .step-text {
            color: #ffffff;
            font-weight: 500;
            transform: scale(1.05);
          }
        }
        
        &.completed {
          .step-number {
            background-color: #ffffff;
            
            &::after {
              content: '✓';
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              font-size: 24rpx;
            }
          }
          
          &::after {
            background-color: #ffffff;
          }
          
          .step-text {
            color: rgba(255, 255, 255, 0.9);
          }
        }
      }
    }
  }
  
  .content {
    position: fixed;
    top: 240rpx;
    left: 0;
    right: 0;
    bottom: 120rpx;
    overflow: hidden;
    
    .step-content {
      height: 100%;
      padding: 24rpx;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
  
  .section {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
    transition: transform 0.3s ease;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &:active {
      transform: scale(0.995);
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      
      .section-title {
        font-size: 34rpx;
        font-weight: 600;
        color: #2c3e50;
        position: relative;
        padding-left: 24rpx;
        letter-spacing: 0.5px;
        
        .title-bar {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6rpx;
          height: 32rpx;
          border-radius: 3rpx;
        }
      }
    }
  }
  
  .form-group {
    .input-wrapper {
      margin-bottom: 24rpx;
      
      &:last-child {
        margin-bottom: 16rpx;
      }
      
      .input-label {
        display: block;
        font-size: 28rpx;
        color: #2c3e50;
        margin-bottom: 12rpx;
        font-weight: 500;
        
        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 6rpx;
          font-weight: normal;
        }
      }
    }
    
    .input-item, .picker-item, .textarea-item {
      width: 100%;
      background-color: #f7f8fa;
      border-radius: 12rpx;
      font-size: 28rpx;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2rpx solid transparent;
      color: #2c3e50;
      
      &:focus {
        background-color: #ffffff;
        border-color: #1890ff;
        box-shadow: 0 0 0 3rpx rgba(24, 144, 255, 0.1);
      }
      
      &::placeholder {
        color: #a0aec0;
      }
    }
    
    .input-item, .picker-item {
      height: 92rpx;
      line-height: 92rpx;
      padding: 0 24rpx;
    }
    
    .textarea-item {
      height: 200rpx;
      padding: 24rpx;
      line-height: 1.6;
    }
  }
  
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: #ffffff;
    padding: 16rpx 32rpx;
    padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
    
    .btn-group {
      display: flex;
      gap: 24rpx;
      
      .nav-btn {
        flex: 1;
        height: 88rpx;
        line-height: 88rpx;
        font-size: 32rpx;
        border-radius: 44rpx;
        border: none;
        position: relative;
        overflow: hidden;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        
        &.prev-btn {
          background-color: #f7f8fa;
          color: #64748b;
          
          &:active {
            background-color: #e2e8f0;
            transform: scale(0.98);
          }
        }
        
        &.next-btn {
          background: linear-gradient(135deg, #1890ff, #36b4ff);
          color: #ffffff;
          
          &:active {
            transform: scale(0.98);
          }
          
          &:disabled {
            opacity: 0.7;
            background: #94a3b8;
            transform: none;
          }
        }
      }
      
      .submit-btn {
        flex: 1;
        height: 88rpx;
        line-height: 88rpx;
        background: linear-gradient(135deg, #1890ff, #36b4ff);
        color: #ffffff;
        font-size: 32rpx;
        border-radius: 44rpx;
        border: none;
        position: relative;
        overflow: hidden;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        
        &:active {
          transform: scale(0.98);
        }
        
        &[disabled] {
          opacity: 0.7;
          background: #94a3b8;
          transform: none;
        }
        
        .btn-text {
          position: relative;
          z-index: 1;
        }
      }
    }
  }
}

.job-intention {
  .form-group {
    .salary-input {
      position: relative;
      
      .input-item {
        padding-right: 120rpx;
      }
      
      .salary-unit {
        position: absolute;
        right: 24rpx;
        top: 50%;
        transform: translateY(-50%);
        color: #64748b;
        font-size: 26rpx;
      }
    }
    
    .job-type-group {
      display: flex;
      gap: 24rpx;
      margin-bottom: 32rpx;
      
      .job-type-item {
        flex: 1;
        height: 84rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f7f8fa;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #64748b;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        z-index: 1;
        
        &.active {
          background: rgba(24, 144, 255, 0.1);
          color: #1890ff;
          font-weight: 500;
          
          &::after {
            content: '';
            position: absolute;
            left: 20%;
            right: 20%;
            bottom: 0;
            height: 4rpx;
            background: linear-gradient(to right, #1890ff, #36b4ff);
            border-radius: 2rpx;
          }
        }
        
        &:active {
          transform: scale(0.98);
        }
      }
    }
  }
}

.basic-info {
  .avatar-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 44rpx;
    
    .avatar {
      width: 180rpx;
      height: 180rpx;
      border-radius: 90rpx;
      background-color: #f7f8fa;
      margin-bottom: 16rpx;
      border: 4rpx solid #ffffff;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.95);
      }
    }
    
    .upload-text {
      font-size: 26rpx;
      color: #64748b;
      letter-spacing: 0.5px;
    }
  }
  
  .form-group {
    .input-row {
      display: flex;
      justify-content: space-between;
      gap: 24rpx;
      margin-bottom: 32rpx;
      
      .input-wrapper {
        flex: 1;
        margin-bottom: 0;
      }
    }
  }
}

.education, .work-exp {
  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;
    color: #64748b;
    font-size: 28rpx;
    
    .empty-icon {
      width: 220rpx;
      height: 220rpx;
      margin-bottom: 24rpx;
      opacity: 0.8;
    }
  }
  
  .add-btn {
    font-size: 28rpx;
    color: #1890ff;
    display: flex;
    align-items: center;
    background-color: rgba(24, 144, 255, 0.1);
    padding: 14rpx 28rpx;
    border-radius: 32rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &::before {
      content: '+';
      margin-right: 8rpx;
      font-size: 32rpx;
      font-weight: normal;
    }
    
    &:active {
      transform: scale(0.95);
      background-color: rgba(24, 144, 255, 0.15);
    }
  }
  
  .edu-item, .work-item {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
    border: 2rpx solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &:active {
      transform: scale(0.995);
    }
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      
      .item-index {
        width: 44rpx;
        height: 44rpx;
        background: linear-gradient(135deg, #1890ff, #36b4ff);
        border-radius: 22rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 24rpx;
        font-weight: 600;
        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
      }
      
      .delete-btn {
        display: flex;
        align-items: center;
        color: #ff4d4f;
        font-size: 26rpx;
        padding: 8rpx 16rpx;
        border-radius: 24rpx;
        background-color: rgba(255, 77, 79, 0.1);
        transition: all 0.3s ease;
        
        .delete-icon {
          font-size: 32rpx;
          margin-right: 4rpx;
        }
        
        &:active {
          transform: scale(0.95);
          background-color: rgba(255, 77, 79, 0.15);
        }
      }
    }
    
    .form-group {
      .date-range {
        display: flex;
        align-items: flex-start;
        gap: 24rpx;
        margin-bottom: 24rpx;
        
        .date-item {
          flex: 1;
        }
        
        .date-separator {
          width: 44rpx;
          height: 92rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 40rpx;
          
          &::after {
            content: '';
            width: 24rpx;
            height: 2rpx;
            background-color: #94a3b8;
          }
        }
      }
    }
  }
}

.layui-imgbox {
  margin-right: 16rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  position: relative;
  
  .layui-imgbox-close {
    position: absolute;
    display: block;
    width: 32rpx;
    height: 32rpx;
    right: -16rpx;
    top: -16rpx;
    z-index: 90;
    color: #999;
    font-size: 32rpx;
    background: #fff;
    border-radius: 50%;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .layui-imgbox-img {
    display: block;
    width: 180rpx;
    height: 180rpx;
    padding: 2px;
    border: #d3d3d3 1px solid;
    background-color: #f6f6f6;
    overflow: hidden;
    border-radius: 50%;
    
    image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }
}

.uploadbtn {
  position: relative;
  height: 180rpx;
  width: 180rpx;
  border-radius: 50%;
  border: 2rpx dashed #d9d9d9;
  
  &:active {
    opacity: 0.8;
  }
}
</style> 