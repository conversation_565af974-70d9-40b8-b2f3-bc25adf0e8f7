<template>
<view class="container">
	<block v-if="isload">
		<view class="search-container">
			<view class="topsearch flex-y-center">
				<!-- 城市选择按钮移到左边 -->
				<view class="city-select" @tap="gotoCity" :class="{'active': currentCity, 'loading': cityLoading}">
					<text class="city-text">{{currentCity || '选择城市'}}</text>
					<text class="city-icon" v-if="!cityLoading">▼</text>
					<text class="city-icon loading-icon" v-if="cityLoading">⟳</text>
				</view>
				<view class="f1 flex-y-center">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input :value="keyword" placeholder="搜索你感兴趣的商家" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" @confirm="search"></input>
				</view>
			</view>
			<view class="search-navbar">
				<view @tap.stop="sortClick" class="search-navbar-item" :style="field=='juli'?'color:'+t('color1'):''" data-field="juli" data-order="asc">距离最近</view>
				<view @tap.stop="sortClick" class="search-navbar-item" :style="field=='comment_score'?'color:'+t('color1'):''" data-field="comment_score" data-order="desc">评分排序</view>
				<view @tap.stop="sortClick" class="search-navbar-item" data-field="sales" :data-order="order=='asc'?'desc':'asc'">
					<text :style="field=='sales'?'color:'+t('color1'):''">销量排序</text>
					<text class="iconfont iconshangla" :style="field=='sales'&&order=='asc'?'color:'+t('color1'):''"></text>
					<text class="iconfont icondaoxu" :style="field=='sales'&&order=='desc'?'color:'+t('color1'):''"></text>
				</view>
				<view @tap.stop="sortClick" class="search-navbar-item" data-field="sales_price" :data-order="order=='asc'?'desc':'asc'">
					<text :style="field=='sales_price'?'color:'+t('color1'):''">销售额</text>
					<text class="iconfont iconshangla" :style="field=='sales_price'&&order=='asc'?'color:'+t('color1'):''"></text>
					<text class="iconfont icondaoxu" :style="field=='sales_price'&&order=='desc'?'color:'+t('color1'):''"></text>
				</view>
				<view class="search-navbar-item flex-x-center flex-y-center" @click.stop="showDrawer('showRight')">筛选 <text :class="'iconfont iconshaixuan ' + (showfilter?'active':'')"></text></view>
			</view>
		</view>
		<uni-drawer ref="showRight" mode="right" @change="change($event,'showRight')" :width="280">
			<view class="filter-scroll-view">
				<view class="filter-title">筛选</view>
				<scroll-view class="filter-scroll-view-box" scroll-y="true">
					<view class="search-filter">
						<view class="filter-content-title">商家分类</view>
						<view class="search-filter-content">
							<view class="filter-item" :style="catchecid==oldcid?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''" @tap.stop="cateClick" :data-cid="oldcid">全部</view>
							<block v-for="(item, index) in clist" :key="index">
								<view class="filter-item" :style="catchecid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''" @tap.stop="cateClick" :data-cid="item.id">{{item.name}}</view>
							</block>
						</view>
					</view>
				</scroll-view>
				<view class="search-filter-btn">
					<view class="btn" @tap="filterReset">重置</view>
					<view class="btn2" :style="{background:t('color1')}" @tap="filterConfirm">确定</view>
				</view>
			</view>
		</uni-drawer>
		
		<view class="ind_business">
			<view class="ind_buslist" id="datalist">
				<block v-for="(item, index) in datalist" :key="index">
				<view @tap="goto" :data-url="'/pagesExt/business/index?id=' + item.id">
					<view class="ind_busbox flex1 flex-row">
						<view class="ind_buspic flex0"><image :src="item.logo"></image></view>
						<view class="flex1">
							<view class="bus_title">{{item.name}}</view>
							<view class="bus_sales">{{field=='sales_price'?'销售额：￥'+item.sales_price:('销量：'+item.sales)}}</view>
						<!-- 	<view class="bus_sales2">余额：{{item.totalmoney!=null?item.totalmoney:0}}</view>
							 -->
							<view class="bus_score">
								<image class="img" v-for="(item2,index2) in [0,1,2,3,4]" :src="'/static/img/star' + (item.comment_score>item2?'2':'') + '.png'"/>
								<view class="txt">{{item.comment_score}}分</view>
								
							</view>
							<!-- <view>当前用户余额 :</view> -->
							<view class="bus_address" v-if="item.address" @tap.stop="openLocation" :data-latitude="item.latitude" :data-longitude="item.longitude" :data-company="item.name" :data-address="item.address"><image src="/static/img/b_addr.png" style="width:26rpx;height:26rpx;margin-right:10rpx"/><text class="x1">{{item.address}}</text><text class="x2">{{item.juli}}</text></view>
							<view class="bus_address" v-if="item.tel" @tap.stop="phone" :data-phone="item.tel"><image src="/static/img/b_tel.png" style="width:26rpx;height:26rpx;margin-right:10rpx"/><text class="x1">联系电话：{{item.tel}}</text></view>
							<block v-if="showtype == 0">
							<scroll-view scroll-x="true" style="width: 510rpx;">
							<view class="prolist" v-if="(item.prolist).length > 0 || (item.restaurantProlist && (item.restaurantProlist).length > 0)">
								<view v-for="(item2, index2) in item.prolist" class="product" @tap.stop="goto" :data-url="'/shopPackage/shop/product?id=' + item2.id">
									<image class="f1" :src="item2.pic"></image>
									<view class="f2">￥{{item2.sell_price}}</view>
								</view>
								<block v-if="item.restaurantProlist && (item.restaurantProlist).length > 0">
									<view v-for="(item3, index3) in item.restaurantProlist" :key="index3" class="product" @tap.stop="goto" :data-url="'/restaurant/takeaway/product?id=' + item3.id">
									 	<image class="f1" :src="item3.pic"></image>
									 	<view class="f2">￥{{item3.sell_price}}</view>
									 </view>
								</block>
								
							</view>
							</scroll-view>
							</block>
							<block v-if="showtype == 1">
							<view class="prolist2" v-if="(item.prolist).length > 0 || (item.restaurantProlist && (item.restaurantProlist).length > 0)">
								<view v-for="(item2, index2) in item.prolist" class="product" @tap.stop="goto" :data-url="'/shopPackage/shop/product?id=' + item2.id">
									<image class="f1" :src="item2.pic"></image>
									<view class="f2">￥{{item2.sell_price}}</view>
									<view class="f3" v-if="item2.market_price">￥{{item2.market_price}}</view>
									<view class="f4">已售{{item2.sales}}件</view>
									<view class="f5" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" @click.stop="buydialogChange" :data-proid="item2.id"><text class="iconfont icon_gouwuche"></text></view>
								</view>
							</view>
							</block>
						</view>
					</view>
				</view>
				</block>
				<nomore v-if="nomore"></nomore>
				<nodata v-if="nodata"></nodata>
			</view>
		</view>
		<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange" :menuindex="menuindex"></buydialog>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			pre_url:app.globalData.pre_url,
      field: 'juli',
			order:'asc',
      oldcid: "",
      catchecid: "",
      longitude: '',
      latitude: '',
			clist:[],
      datalist: [],
      pagenum: 1,
      keyword: '',
      cid: '',
      nomore: false,
      nodata: false,
      types: "",
      showfilter: "",
			showtype:0,
			buydialogShow:false,
			proid:0,
			// 添加城市相关变量
			currentCity: '',
			currentCityId: 0,
			provinceId: 0,
			cityId: 0,
			districtId: 0,
			cityLoading: false,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.oldcid = this.opt.cid;
		this.catchecid = this.opt.cid;
		this.cid = this.opt.cid;
        if(this.opt.keyword) {
        	this.keyword = this.opt.keyword;
        }

		// 重置地区筛选参数
		this.provinceId = 0;
		this.cityId = 0;
		this.districtId = 0;
		this.currentCity = '';
		this.currentCityId = 0;

		this.getdata();

		// 监听城市选择结果
		uni.$on('city', this.handleCitySelect);

		console.log('2023-06-15 11:05:23-INFO-[cityblist][onLoad_001] 页面初始化完成');
  },
  onUnload: function() {
	// 取消监听，防止内存泄漏
	uni.$off('city', this.handleCitySelect);
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getDataList(true);
    }
  },
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiBusiness/blist', {}, function (res) {
				that.loading = false;
				that.clist = res.clist;
				that.showtype = res.showtype || 0;
				that.loaded();
			});
			app.getLocation(function (res) {
				var latitude = res.latitude;
				var longitude = res.longitude;
				that.longitude = longitude;
				that.latitude = latitude;
				that.getDataList();
			},
			function () {
				that.getDataList();
			});
		},
    getDataList: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var latitude = that.latitude;
      var longitude = that.longitude;
      var keyword = that.keyword;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;

			console.log('2023-06-15 11:05:23-INFO-[cityblist][getDataList_001] 发送请求，地区ID：', {
				provinceId: that.provinceId,
				cityId: that.cityId,
				districtId: that.districtId
			});

			var params = {
				pagenum: pagenum,
				cid: that.cid,
				field: that.field,
				order: that.order,
				longitude: longitude,
				latitude: latitude,
				keyword: keyword,
				// 添加地区筛选参数，确保是数字类型
				province_id: parseInt(that.provinceId || 0),
				city_id: parseInt(that.cityId || 0),
				district_id: parseInt(that.districtId || 0)
			};

			app.post('ApiBusiness/blist', params, function (res) {
        that.loading = false;
				uni.stopPullDownRefresh();
        var data = res.data;
        if (pagenum == 1) {
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
		// 打开窗口
		showDrawer(e) {
			console.log(e)
			this.$refs[e].open()
		},
		// 关闭窗口
		closeDrawer(e) {
			this.$refs[e].close()
		},
		// 抽屉状态发生变化触发
		change(e, type) {
			console.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));
			this[type] = e
		},
    cateClick: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
      that.catchecid = cid
    },
		filterConfirm(){
			this.cid = this.catchecid;
			this.gid = this.catchegid;
			this.getDataList();
			this.$refs['showRight'].close()
		},
		filterReset(){
			this.catchecid = this.oldcid;
			this.catchegid = '';
		},
    filterClick: function () {
      this.showfilter = !this.showfilter
    },
    changetab: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
      that.cid = cid
      that.pagenum = 1;
      that.datalist = [];
      that.getDataList();
    },
    search: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
			that.pagenum = 1;
      that.datalist = [];
      that.getDataList();
    },
    sortClick: function (e) {
      var that = this;
      var t = e.currentTarget.dataset;
      that.field = t.field;
      that.order = t.order;
      that.getDataList();
    },
    filterClick: function (e) {
      var that = this;
      var types = e.currentTarget.dataset.types;
      that.types = types;
    },
		openLocation:function(e){
			//console.log(e)
			var latitude = parseFloat(e.currentTarget.dataset.latitude)
			var longitude = parseFloat(e.currentTarget.dataset.longitude)
			var address = e.currentTarget.dataset.address
			uni.openLocation({
			 latitude:latitude,
			 longitude:longitude,
			 name:address,
			 scale: 13
		 })		
		},
		phone:function(e) {
			var phone = e.currentTarget.dataset.phone;
			uni.makePhoneCall({
				phoneNumber: phone,
				fail: function () {
				}
			});
		},
		buydialogChange: function (e) {
			if(!this.buydialogShow){
				this.proid = e.currentTarget.dataset.proid
			}
			this.buydialogShow = !this.buydialogShow;
			console.log(this.buydialogShow);
		},

		// 处理城市选择结果
		handleCitySelect: function(city) {
			console.log('2023-06-15 11:05:23-INFO-[cityblist][handleCitySelect_001] 选择的城市：', city);
			if(city && city.id) {
				this.currentCityId = city.id;
				this.currentCity = city.name;

				// 更新逻辑，根据city对象的level属性更准确地设置地区ID
				if(city.level !== undefined) {
					if(city.level === 0) { // 省级
						this.provinceId = city.id;
						this.cityId = 0;
						this.districtId = 0;
					} else if(city.level === 1) { // 市级
						this.cityId = city.id;
						this.provinceId = city.parent_id || 0;
						this.districtId = 0;
					} else if(city.level === 2) { // 区级
						this.districtId = city.id;
						this.cityId = city.parent_id || 0;

						// 尝试获取省份ID，如果无法获取则设置为0
						if(city.province_id) {
							this.provinceId = city.province_id;
						} else {
							// 可能需要额外API调用获取省份ID，但此处简化处理
							this.provinceId = 0;
						}
					}
				} else if(city.parent_id) {
					// 如果有parent_id属性，可以判断其层级
					this.cityId = city.id;
					this.provinceId = city.parent_id;
					this.districtId = 0;
				} else if(city.first_letter) {
					// 如果有first_letter属性，说明是从字母索引列表选择的城市
					// 判断是否是省级城市（简单判断：城市名称包含"省"或"自治区"）
					let name = city.name || '';
					if(name.indexOf('省') > -1 || name.indexOf('自治区') > -1 || name.indexOf('市') === name.length-1 && name.length > 2) {
						this.provinceId = city.id;
						this.cityId = 0;
						this.districtId = 0;
					} else {
						// 否则可能是城市，这里设置为城市级别
						this.cityId = city.id;
						this.provinceId = 0; // 无法知道其所属省份ID
						this.districtId = 0;
					}
				}

				console.log('2023-06-15 11:05:23-INFO-[cityblist][handleCitySelect_002] 设置的地区ID：', {
					provinceId: this.provinceId,
					cityId: this.cityId,
					districtId: this.districtId
				});

				// 重新获取数据
				this.getDataList();
			}
		},

		// 跳转到城市选择页面
		gotoCity: function() {
			// 防止重复点击
			if(this.cityLoading) return;

			this.cityLoading = true;
			console.log('2023-06-15 11:05:23-INFO-[cityblist][gotoCity_001] 开始获取城市列表数据');

			// 获取城市列表数据，使用系统配置的分区模式
			app.get('ApiArea/getActiveAreas', {
				// 不传入mode参数，让后端从系统配置中获取areamode
				with_children: true // 获取包含子级的数据
			}, (res) => {
				this.cityLoading = false;
				console.log('2023-06-15 11:05:23-INFO-[cityblist][gotoCity_002] 获取城市列表响应：', res);

				if(res.status === 1) {
					// 将数据传递给城市选择页面
					let data = encodeURIComponent(JSON.stringify({
						areas: res.data,
						area_config: {
							hotareas: '',
							hotareas_str: '',
							hotarea: 1,
							switcharearange: 0,
							switcharearangeareas_str: ''
						}
					}));

					console.log('2023-06-15 11:05:23-INFO-[cityblist][gotoCity_003] 跳转到城市选择页面');
					uni.navigateTo({
						url: '/pages/index/city?data=' + data
					});
				} else {
					console.log('2023-06-15 11:05:23-ERROR-[cityblist][gotoCity_004] 获取城市列表失败：', res.msg);
					uni.showToast({
						title: res.msg || '获取城市列表失败',
						icon: 'none',
						duration: 2000
					});
				}
			}, (error) => {
				this.cityLoading = false;
				console.log('2023-06-15 11:05:23-ERROR-[cityblist][gotoCity_005] 网络请求失败：', error);
				uni.showToast({
					title: '网络请求失败，请重试',
					icon: 'none',
					duration: 2000
				});
			});
		},
  }
};
</script>
<style>
.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}
.topsearch{width:100%;padding:16rpx 20rpx;display:flex;align-items:center;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1;margin: 0 10rpx;}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.topsearch .search-btn{display:flex;align-items:center;color:#5a5a5a;font-size:30rpx;width:60rpx;text-align:center;margin-left:20rpx}

/* 城市选择器样式 - 现代化设计 */
.city-select {
	height: 60rpx;
	line-height: 60rpx;
	padding: 0 24rpx;
	margin-right: 15rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #495057;
	display: flex;
	align-items: center;
	justify-content: space-between;
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	max-width: 160rpx;
	min-width: 120rpx;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	position: relative;
	flex-shrink: 0;
	border: 2rpx solid transparent;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 添加微妙的内阴影效果 */
.city-select:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 30rpx;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
	pointer-events: none;
	transition: opacity 0.3s ease;
}

/* 点击效果 */
.city-select:active {
	transform: scale(0.96) translateY(1rpx);
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.city-select:active:before {
	opacity: 0.5;
}

/* 城市图标样式优化 */
.city-icon {
	font-size: 20rpx;
	margin-left: 8rpx;
	color: #6c757d;
	transition: all 0.3s ease;
	transform: rotate(0deg);
}

/* 当前已选择城市的状态 - 渐变主题色 */
.city-select.active {
	background: linear-gradient(135deg, rgba(122, 131, 236, 0.15) 0%, rgba(122, 131, 236, 0.08) 100%);
	border-color: rgba(122, 131, 236, 0.3);
	color: #7A83EC;
	font-weight: 600;
	box-shadow: 0 4rpx 12rpx rgba(122, 131, 236, 0.15);
}

.city-select.active .city-icon {
	color: #7A83EC;
	transform: rotate(180deg);
}

.city-select.active:before {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.4) 100%);
}

/* 悬停效果（适用于支持hover的设备） */
@media (hover: hover) {
	.city-select:hover {
		transform: translateY(-1rpx);
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
		border-color: rgba(122, 131, 236, 0.2);
	}

	.city-select.active:hover {
		box-shadow: 0 6rpx 20rpx rgba(122, 131, 236, 0.25);
		border-color: rgba(122, 131, 236, 0.4);
	}
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.city-select {
		max-width: 240rpx;
		min-width: 200rpx;
		padding: 0 20rpx;
		font-size: 24rpx;
	}
}

/* 城市文本样式 */
.city-text {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 加载状态动画 */
.city-select.loading {
	pointer-events: none;
	opacity: 0.8;
}

.city-select.loading .loading-icon {
	animation: rotate 1s linear infinite;
	color: #7A83EC;
}

/* 旋转动画 */
@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* 脉冲动画效果 */
@keyframes pulse {
	0% {
		box-shadow: 0 4rpx 12rpx rgba(122, 131, 236, 0.15);
	}
	50% {
		box-shadow: 0 6rpx 20rpx rgba(122, 131, 236, 0.25);
	}
	100% {
		box-shadow: 0 4rpx 12rpx rgba(122, 131, 236, 0.15);
	}
}

.city-select.active {
	animation: pulse 2s ease-in-out infinite;
}

/* 微交互效果 */
.city-select:active .city-text {
	transform: translateX(1rpx);
}

.city-select:active .city-icon {
	transform: scale(0.9) rotate(180deg);
}

.city-select.active:active .city-icon {
	transform: scale(0.9) rotate(360deg);
}
.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0}
.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:26rpx;font-weight:bold;color:#323232;padding:0 5rpx;}
.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .iconshaixuan{margin-left:5rpx;font-size:22rpx;color:#7d7d7d}

.filter-scroll-view{display: flex;flex-direction: column;height: 100vh;background: #fff;}
.filter-scroll-view-box{flex: 1;overflow-y: auto;-webkit-overflow-scrolling: touch;}
.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}
.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}
.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:20rpx 0 20rpx 20rpx;flex-shrink: 0;}
.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}
.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}
.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}
.search-filter button .icon{margin-top:6rpx;height:54rpx;}
.search-filter-btn{display:flex;padding:30rpx 30rpx;justify-content: space-between;flex-shrink: 0;border-top: 1rpx solid #f5f5f5;background: #fff;}
.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}
.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}

.ind_business {width: 100%;margin-top: 190rpx;font-size:26rpx;padding:0 24rpx}
.ind_business .ind_busbox{ width:100%;background: #fff;padding:20rpx;overflow: hidden; margin-bottom:20rpx;border-radius:8rpx;position:relative}
.ind_business .ind_buspic{ width:120rpx;height:120rpx; margin-right: 28rpx; }
.ind_business .ind_buspic image{ width: 100%;height:100%;border-radius: 8rpx;object-fit: cover;}
.ind_business .bus_title{ font-size: 30rpx; color: #222;font-weight:bold;line-height:46rpx}
.ind_business .bus_score{font-size: 24rpx;color:#FC5648;display:flex;align-items:center}
.ind_business .bus_score .img{width:24rpx;height:24rpx;margin-right:10rpx}
.ind_business .bus_score .txt{margin-left:20rpx}
.ind_business .indsale_box{ display: flex}
.ind_business .bus_sales{ font-size: 24rpx; color:#999;position:absolute;top:20rpx;right:28rpx}

.ind_business .bus_address{color:#999;font-size: 22rpx;height:36rpx;line-height: 36rpx;margin-top:6rpx;display:flex;align-items:center;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.ind_business .bus_address .x2{padding-left:20rpx}
.ind_business .prolist{white-space: nowrap;margin-top:16rpx; margin-bottom: 10rpx;}
.ind_business .prolist .product{width:108rpx;height:160rpx;overflow:hidden;display:inline-flex;flex-direction:column;align-items:center;margin-right:24rpx}
.ind_business .prolist .product .f1{width:108rpx;height:108rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist .product .f2{font-size:22rpx;color:#FC5648;font-weight:bold;margin-top:4rpx}
.ind_business .prolist2{margin-top:16rpx; margin-bottom: 10rpx;}
.ind_business .prolist2 .product{width:118rpx;overflow:hidden;display:inline-flex;flex-direction:column;margin-right:10rpx;position:relative;min-height:200rpx;padding-bottom:20rpx}
.ind_business .prolist2 .product .f1{width:118rpx;height:118rpx;border-radius:8rpx;background:#f6f6f6}
.ind_business .prolist2 .product .f2{font-size:26rpx;color:#FC5648;font-weight:bold;margin-top:4rpx;}
.ind_business .prolist2 .product .f3{font-size:22rpx;font-weight:normal;color: #aaa;text-decoration: line-through;}
.ind_business .prolist2 .product .f4{font-size:20rpx;font-weight:normal;color: #888;}

.ind_business .prolist2 .product .f5{width:48rpx;height:48rpx;border-radius:50%;position:absolute;display:relative;top:140rpx;right:0rpx;text-align:center;}
.ind_business .prolist2 .product .f5 .icon_gouwuche{font-size:28rpx;height:48rpx;line-height:48rpx}
.ind_business .prolist2 .product .f5 .img{width:100%;height:100%}
</style>