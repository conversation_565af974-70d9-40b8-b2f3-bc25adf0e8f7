<template>
	<view class="container">
		<!-- 粒子背景容器 -->
		<canvas canvas-id="particlesCanvas" class="particles-canvas" 
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"></canvas>
		
		<!-- 装饰性背景 -->
		<view class="bg-grid"></view>
		<view class="bg-circles"></view>
		
		<view class="ending-console">
			<!-- 标题区域 -->
			<view class="header-section">
				<view class="completion-badge">
					<view class="badge-glow"></view>
					<text class="badge-icon">🎉</text>
				</view>
				<view class="title-info">
					<text class="main-title" :style="{color:t('color1')}">时空之旅完成</text>
					<text class="subtitle">恭喜您完成了与未来自己的对话</text>
				</view>
				<view class="time-display">
					<text class="year">2049</text>
					<text class="status">任务完成</text>
				</view>
			</view>
			
			<!-- 结束引导区域 -->
			<view class="ending-guidance">
				<view class="guidance-content">
					<text class="guidance-text">{{displayText}}</text>
					<text v-if="showCursor" class="typing-cursor">|</text>
				</view>
			</view>
			
			<!-- 梦想墙互动区域 -->
			<view class="dream-wall-section">
				<view class="section-header">
					<text class="section-title" :style="{color:t('color1')}">选择您的明日萌像</text>
					<text class="section-subtitle">每一种选择都代表着不同的未来可能</text>
				</view>
				
				<view class="dream-wall">
					<view v-for="(dream, index) in dreamOptions" :key="index" 
						  :class="['dream-item', {selected: selectedDream === index}]"
						  @tap="selectDream(index)">
						<view class="dream-icon">
							<text>{{dream.icon}}</text>
						</view>
						<view class="dream-info">
							<text class="dream-name">{{dream.name}}</text>
							<text class="dream-desc">{{dream.description}}</text>
						</view>
						<view v-if="selectedDream === index" class="selected-indicator">
							<text class="indicator-icon">✓</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 个性化宣言区域 -->
			<view v-if="selectedDream !== -1" class="declaration-section">
				<view class="declaration-header">
					<text class="declaration-title">您的未来宣言</text>
				</view>
				<view class="declaration-content">
					<text class="declaration-text">{{personalDeclaration}}</text>
				</view>
			</view>
			
			<!-- 成就系统区域 -->
			<view class="achievement-section">
				<view class="achievement-header">
					<text class="achievement-title">🏆 解锁成就</text>
				</view>
				<view class="achievements">
					<view v-for="(achievement, index) in unlockedAchievements" :key="index" 
						  class="achievement-item">
						<view class="achievement-icon">
							<text>{{achievement.icon}}</text>
						</view>
						<view class="achievement-info">
							<text class="achievement-name">{{achievement.name}}</text>
							<text class="achievement-desc">{{achievement.description}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 统计信息区域 -->
			<view class="statistics-section">
				<view class="stats-header">
					<text class="stats-title">时空之旅统计</text>
				</view>
				<view class="stats-grid">
					<view class="stat-item">
						<text class="stat-value">{{journeyStats.totalTime}}</text>
						<text class="stat-label">总用时</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{journeyStats.dialogueCount}}</text>
						<text class="stat-label">对话轮数</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{journeyStats.voiceTime}}</text>
						<text class="stat-label">语音时长</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{journeyStats.completionRate}}</text>
						<text class="stat-label">完成度</text>
					</view>
				</view>
			</view>
			
			<!-- 分享功能区域 -->
			<view class="share-section">
				<view class="share-header">
					<text class="share-title">分享您的时空之旅</text>
				</view>
				<view class="share-actions">
					<view class="share-btn" @tap="generateCertificate">
						<text class="share-icon">📜</text>
						<text class="share-text">生成证书</text>
					</view>
					<view class="share-btn" @tap="shareToWeChat">
						<text class="share-icon">💬</text>
						<text class="share-text">微信分享</text>
					</view>
					<view class="share-btn" @tap="shareToWeibo">
						<text class="share-icon">📱</text>
						<text class="share-text">微博分享</text>
					</view>
					<view class="share-btn" @tap="downloadResults">
						<text class="share-icon">⬇</text>
						<text class="share-text">下载结果</text>
					</view>
				</view>
			</view>
			
			<!-- 控制按钮区域 -->
			<view class="control-section">
				<view class="control-buttons">
					<view class="control-btn secondary" @tap="restartJourney">
						<text class="btn-icon">🔄</text>
						<text class="btn-text">重新开始</text>
					</view>
					<view class="control-btn primary" @tap="exploreMore">
						<text class="btn-icon">🚀</text>
						<text class="btn-text">探索更多</text>
					</view>
					<view class="control-btn" @tap="returnHome">
						<text class="btn-icon">🏠</text>
						<text class="btn-text">返回首页</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 庆祝动画覆盖层 -->
		<view v-if="showCelebration" class="celebration-overlay">
			<view class="celebration-content">
				<view class="celebration-fireworks">
					<view class="firework firework-1">🎆</view>
					<view class="firework firework-2">✨</view>
					<view class="firework firework-3">🎇</view>
					<view class="firework firework-4">⭐</view>
				</view>
				<text class="celebration-text">恭喜完成时空之旅！</text>
			</view>
		</view>
		
		<!-- 页脚信息 -->
		<view class="footer">
			<text class="footer-text">梦想方舟计划 v2049.1 | 时空之旅已完成 | 感谢您的参与</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			canvasWidth: 375,
			canvasHeight: 667,
			
			// 打字效果
			displayText: '',
			showCursor: false,
			endingText: '感谢您完成了这次奇妙的时空之旅！通过与2049年的自己对话，您已经窥见了未来的无限可能。现在，请选择您心中的明日萌像，让我们一起为您的未来画下美好的蓝图。',
			typingIndex: 0,
			typingTimer: null,
			
			// 梦想选择
			selectedDream: -1,
			dreamOptions: [
				{
					icon: '🔬',
					name: '科技先锋',
					description: '用创新改变世界',
					declaration: '我将成为科技创新的引领者，用智慧和创造力推动人类文明的进步，让科技成为连接梦想与现实的桥梁。'
				},
				{
					icon: '🎨',
					name: '艺术创作者',
					description: '用美感触动心灵',
					declaration: '我将用艺术的力量传递美好，创作出触动人心的作品，让世界因为美而变得更加温暖和有意义。'
				},
				{
					icon: '📚',
					name: '智慧导师',
					description: '用知识启发他人',
					declaration: '我将成为知识的传播者和智慧的引路人，用教育点亮更多人的心灯，让学习成为改变命运的力量。'
				},
				{
					icon: '💪',
					name: '健康生活家',
					description: '用活力感染生活',
					declaration: '我将追求身心的完美平衡，用健康的生活方式感染身边的每一个人，让生命充满活力和正能量。'
				},
				{
					icon: '🌱',
					name: '环保卫士',
					description: '用行动守护地球',
					declaration: '我将成为地球的守护者，用实际行动保护我们共同的家园，为子孙后代留下一个美丽的世界。'
				},
				{
					icon: '❤️',
					name: '社会贡献者',
					description: '用爱心温暖社会',
					declaration: '我将用爱心和善行温暖社会，帮助需要帮助的人，让世界因为我的存在而变得更加美好。'
				},
				{
					icon: '🗺️',
					name: '冒险探索者',
					description: '用勇气开拓未知',
					declaration: '我将勇敢地探索未知的领域，用冒险精神开拓人生的边界，让每一天都充满新的发现和可能。'
				},
				{
					icon: '🛡️',
					name: '温暖守护者',
					description: '用关爱守护所爱',
					declaration: '我将成为家人和朋友的坚强后盾，用无私的关爱守护所有珍贵的人和事，让温暖成为我的标志。'
				}
			],
			personalDeclaration: '',
			
			// 成就系统
			unlockedAchievements: [
				{
					icon: '🎯',
					name: '时空对话者',
					description: '成功与未来自己建立对话连接'
				},
				{
					icon: '📸',
					name: '未来预见者',
					description: '获得AI预测的未来形象'
				},
				{
					icon: '🎙️',
					name: '跨时空通话',
					description: '完成与未来自己的语音对话'
				},
				{
					icon: '🌟',
					name: '梦想启蒙者',
					description: '完成完整的梦想启蒙之旅'
				}
			],
			
			// 统计信息
			journeyStats: {
				totalTime: '15分钟',
				dialogueCount: '3轮',
				voiceTime: '5分钟',
				completionRate: '100%'
			},
			
			// 庆祝动画
			showCelebration: false,
			celebrationTimer: null
		}
	},
	onLoad() {
		this.initCanvas();
		this.loadJourneyData();
		this.startEndingSequence();
	},
	onReady() {
		// 页面渲染完成
	},
	onUnload() {
		this.clearTimers();
	},
	methods: {
		// 初始化画布
		initCanvas() {
			const systemInfo = uni.getSystemInfoSync();
			this.canvasWidth = systemInfo.windowWidth;
			this.canvasHeight = systemInfo.windowHeight;
		},
		
		// 加载旅程数据
		loadJourneyData() {
			try {
				// 加载用户对话数据
				const dialogueData = uni.getStorageSync('user_dialogue_data');
				if (dialogueData) {
					// 根据用户数据调整统计信息
					this.journeyStats.dialogueCount = Object.keys(dialogueData).length + '轮';
				}
				
				// 加载其他统计数据
				const startTime = uni.getStorageSync('journey_start_time');
				if (startTime) {
					const endTime = Date.now();
					const duration = Math.floor((endTime - startTime) / 1000 / 60);
					this.journeyStats.totalTime = duration + '分钟';
				}
			} catch (e) {
				console.error('加载旅程数据失败:', e);
			}
		},
		
		// 开始结束序列
		startEndingSequence() {
			// 显示庆祝动画
			this.showCelebration = true;
			this.playCelebrationSound();
			
			// 3秒后隐藏庆祝动画，开始打字效果
			this.celebrationTimer = setTimeout(() => {
				this.showCelebration = false;
				this.startTypingEffect();
			}, 3000);
		},
		
		// 开始打字效果
		startTypingEffect() {
			this.showCursor = true;
			this.typingIndex = 0;
			this.displayText = '';
			
			this.typingTimer = setInterval(() => {
				if (this.typingIndex < this.endingText.length) {
					this.displayText += this.endingText[this.typingIndex];
					this.typingIndex++;
				} else {
					this.completeTyping();
				}
			}, 80);
		},
		
		// 完成打字
		completeTyping() {
			this.clearTypingTimer();
			this.showCursor = false;
		},
		
		// 清除打字定时器
		clearTypingTimer() {
			if (this.typingTimer) {
				clearInterval(this.typingTimer);
				this.typingTimer = null;
			}
		},
		
		// 选择梦想
		selectDream(index) {
			this.selectedDream = index;
			this.personalDeclaration = this.dreamOptions[index].declaration;
			
			// 保存选择
			try {
				uni.setStorageSync('selected_dream', {
					index: index,
					dream: this.dreamOptions[index]
				});
			} catch (e) {
				console.error('保存梦想选择失败:', e);
			}
			
			// 播放选择音效
			this.playSelectionSound();
		},
		
		// 生成证书
		generateCertificate() {
			if (this.selectedDream === -1) {
				uni.showToast({
					title: '请先选择您的明日萌像',
					icon: 'none'
				});
				return;
			}
			
			uni.showLoading({
				title: '正在生成证书...'
			});
			
			// 模拟证书生成
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '证书生成成功',
					icon: 'success'
				});
			}, 2000);
		},
		
		// 分享到微信
		shareToWeChat() {
			// #ifdef MP-WEIXIN
			uni.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			});
			// #endif
			
			// #ifndef MP-WEIXIN
			uni.showToast({
				title: '微信分享功能开发中',
				icon: 'none'
			});
			// #endif
		},
		
		// 分享到微博
		shareToWeibo() {
			uni.showToast({
				title: '微博分享功能开发中',
				icon: 'none'
			});
		},
		
		// 下载结果
		downloadResults() {
			uni.showLoading({
				title: '正在打包结果...'
			});
			
			// 模拟下载过程
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '结果已保存到相册',
					icon: 'success'
				});
			}, 2000);
		},
		
		// 重新开始旅程
		restartJourney() {
			uni.showModal({
				title: '重新开始',
				content: '确定要清空所有数据重新开始时空之旅吗？',
				success: (res) => {
					if (res.confirm) {
						try {
							uni.clearStorageSync();
							uni.showToast({
								title: '已重置，即将重新开始',
								icon: 'success'
							});
							setTimeout(() => {
								uni.reLaunch({
									url: '/pagesB/dreamark/index'
								});
							}, 1000);
						} catch (e) {
							uni.showToast({
								title: '重置失败',
								icon: 'error'
							});
						}
					}
				}
			});
		},
		
		// 探索更多
		exploreMore() {
			uni.showToast({
				title: '更多功能敬请期待',
				icon: 'none'
			});
		},
		
		// 返回首页
		returnHome() {
			uni.reLaunch({
				url: '/pages/index/index'
			});
		},
		
		// 播放庆祝音效
		playCelebrationSound() {
			// 使用uni.createInnerAudioContext播放庆祝音效
			const audio = uni.createInnerAudioContext();
			audio.src = '/static/audio/celebration.mp3';
			audio.play();
		},
		
		// 播放选择音效
		playSelectionSound() {
			const audio = uni.createInnerAudioContext();
			audio.src = '/static/audio/selection.mp3';
			audio.play();
		},
		
		// 清除定时器
		clearTimers() {
			this.clearTypingTimer();
			if (this.celebrationTimer) {
				clearTimeout(this.celebrationTimer);
				this.celebrationTimer = null;
			}
		}
	}
}
</script>

<style>
/* 基础样式 */
page {
	background: #0a0a2a;
	color: #ffffff;
	font-family: 'PingFang SC', sans-serif;
}

.container {
	position: relative;
	min-height: 100vh;
	background: linear-gradient(135deg, #0a0a2a 0%, #1a1a3a 30%, #2a2a4a 70%, #1a1a3a 100%);
	overflow: hidden;
	/* 添加微妙的纹理效果 */
	background-image:
		radial-gradient(circle at 35% 65%, rgba(0, 247, 255, 0.02) 0%, transparent 50%),
		radial-gradient(circle at 65% 35%, rgba(189, 0, 255, 0.02) 0%, transparent 50%);
}

/* 粒子画布 */
.particles-canvas {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1;
	pointer-events: none;
}

/* 装饰背景 */
.bg-grid {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: 
		linear-gradient(rgba(0, 247, 255, 0.1) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 247, 255, 0.1) 1px, transparent 1px);
	background-size: 50px 50px;
	z-index: 2;
	animation: gridMove 20s linear infinite;
}

.bg-circles {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 2;
}

@keyframes gridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(50px, 50px); }
}

/* 结束控制台 */
.ending-console {
	position: relative;
	z-index: 10;
	padding: 40rpx;
	min-height: 100vh;
}

/* 标题区域 */
.header-section {
	display: flex;
	align-items: center;
	gap: 30rpx;
	margin-bottom: 40rpx;
	padding: 30rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
}

.completion-badge {
	position: relative;
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.badge-glow {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	right: -10rpx;
	bottom: -10rpx;
	background: radial-gradient(circle, rgba(255, 215, 0, 0.3), transparent);
	border-radius: 50%;
	animation: badgeGlow 2s infinite;
}

@keyframes badgeGlow {
	0%, 100% { opacity: 0.5; }
	50% { opacity: 1; }
}

.badge-icon {
	font-size: 48rpx;
	z-index: 2;
}

.title-info {
	flex: 1;
}

.main-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 10rpx;
}

.subtitle {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
	line-height: 1.4;
}

.time-display {
	text-align: right;
}

.year {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 5rpx;
}

.status {
	display: block;
	font-size: 20rpx;
	color: #7df9ff;
}

/* 结束引导区域 */
.ending-guidance {
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.guidance-content {
	text-align: center;
}

.guidance-text {
	font-size: 28rpx;
	color: #7df9ff;
	line-height: 1.6;
}

.typing-cursor {
	color: #00f7ff;
	font-size: 28rpx;
	animation: cursorBlink 1s infinite;
}

@keyframes cursorBlink {
	0%, 50% { opacity: 1; }
	51%, 100% { opacity: 0; }
}

/* 梦想墙区域 */
.dream-wall-section {
	margin-bottom: 40rpx;
}

.section-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.section-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 15rpx;
}

.section-subtitle {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
}

.dream-wall {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.dream-item {
	position: relative;
	flex: 1;
	min-width: 300rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 2px solid rgba(0, 247, 255, 0.3);
	border-radius: 15rpx;
	padding: 30rpx;
	transition: all 0.3s ease;
}

.dream-item:active {
	transform: scale(0.98);
}

.dream-item.selected {
	border-color: #00f7ff;
	background: rgba(0, 247, 255, 0.1);
	box-shadow: 0 0 30rpx rgba(0, 247, 255, 0.3);
}

.dream-icon {
	text-align: center;
	margin-bottom: 20rpx;
	font-size: 48rpx;
}

.dream-info {
	text-align: center;
}

.dream-name {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 10rpx;
}

.dream-desc {
	display: block;
	font-size: 22rpx;
	color: #7df9ff;
	line-height: 1.4;
}

.selected-indicator {
	position: absolute;
	top: 15rpx;
	right: 15rpx;
	width: 40rpx;
	height: 40rpx;
	background: #00f7ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.indicator-icon {
	font-size: 24rpx;
	color: #000;
	font-weight: bold;
}

/* 个性化宣言区域 */
.declaration-section {
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 215, 0, 0.3);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.declaration-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.declaration-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #ffd700;
}

.declaration-content {
	background: rgba(255, 215, 0, 0.1);
	border: 1px solid rgba(255, 215, 0, 0.3);
	border-radius: 15rpx;
	padding: 30rpx;
}

.declaration-text {
	font-size: 26rpx;
	color: #ffd700;
	line-height: 1.6;
	text-align: center;
}

/* 成就系统区域 */
.achievement-section {
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.achievement-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.achievement-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
}

.achievements {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.achievement-item {
	flex: 1;
	min-width: 300rpx;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 15rpx;
	padding: 25rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.achievement-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(0, 247, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	flex-shrink: 0;
}

.achievement-info {
	flex: 1;
}

.achievement-name {
	display: block;
	font-size: 26rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 8rpx;
}

.achievement-desc {
	display: block;
	font-size: 22rpx;
	color: #7df9ff;
	line-height: 1.3;
}

/* 统计信息区域 */
.statistics-section {
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.stats-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.stats-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
}

.stats-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.stat-item {
	flex: 1;
	min-width: 150rpx;
	text-align: center;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 15rpx;
	padding: 30rpx 20rpx;
}

.stat-value {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 10rpx;
}

.stat-label {
	display: block;
	font-size: 22rpx;
	color: #7df9ff;
}

/* 分享功能区域 */
.share-section {
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.share-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.share-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
}

.share-actions {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.share-btn {
	flex: 1;
	min-width: 150rpx;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 50rpx;
	padding: 25rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	transition: all 0.3s ease;
}

.share-btn:active {
	transform: scale(0.95);
	background: rgba(0, 247, 255, 0.2);
}

.share-icon {
	font-size: 32rpx;
}

.share-text {
	font-size: 22rpx;
	color: #7df9ff;
}

/* 控制按钮区域 */
.control-section {
	margin-bottom: 40rpx;
}

.control-buttons {
	display: flex;
	gap: 20rpx;
}

.control-btn {
	flex: 1;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 50rpx;
	padding: 30rpx 25rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
	transition: all 0.3s ease;
}

.control-btn:active {
	transform: scale(0.95);
	background: rgba(0, 247, 255, 0.2);
}

.control-btn.primary {
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	border-color: transparent;
}

.control-btn.secondary {
	background: rgba(255, 165, 0, 0.1);
	border-color: #ffa500;
}

.btn-icon {
	font-size: 32rpx;
}

.btn-text {
	font-size: 24rpx;
	color: #7df9ff;
}

.control-btn.primary .btn-text {
	color: #fff;
}

.control-btn.secondary .btn-text {
	color: #ffa500;
}

/* 庆祝动画覆盖层 */
.celebration-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	backdrop-filter: blur(5px);
}

.celebration-content {
	text-align: center;
}

.celebration-fireworks {
	position: relative;
	width: 400rpx;
	height: 400rpx;
	margin: 0 auto 40rpx;
}

.firework {
	position: absolute;
	font-size: 48rpx;
	animation: fireworkExplode 2s infinite;
}

.firework-1 {
	top: 20%;
	left: 20%;
	animation-delay: 0s;
}

.firework-2 {
	top: 20%;
	right: 20%;
	animation-delay: 0.5s;
}

.firework-3 {
	bottom: 20%;
	left: 20%;
	animation-delay: 1s;
}

.firework-4 {
	bottom: 20%;
	right: 20%;
	animation-delay: 1.5s;
}

@keyframes fireworkExplode {
	0% { transform: scale(0) rotate(0deg); opacity: 0; }
	50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
	100% { transform: scale(0.8) rotate(360deg); opacity: 0; }
}

.celebration-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #ffd700;
	animation: celebrationGlow 1s infinite alternate;
}

@keyframes celebrationGlow {
	0% { text-shadow: 0 0 20rpx #ffd700; }
	100% { text-shadow: 0 0 40rpx #ffd700, 0 0 60rpx #ffd700; }
}

/* 页脚 */
.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx;
	background: rgba(0, 0, 0, 0.8);
	border-top: 1px solid rgba(0, 247, 255, 0.3);
	z-index: 100;
}

.footer-text {
	text-align: center;
	font-size: 20rpx;
	color: #7df9ff;
	line-height: 1.4;
}
</style>
