<template>
<view class="container">
	<block v-if="isload">
		<view class="search-container">
			<view class="topsearch flex-y-center">
				<view class="f1 flex-y-center">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input :value="keyword" placeholder="搜索等级奖励规则" placeholder-style="font-size:26rpx;color:#C2C2C2" confirm-type="search" @confirm="search"></input>
				</view>
			</view>
		</view>
		
		<view class="ind_business">
			<view class="ind_buslist" id="datalist">
				<block v-for="(item, index) in datalist" :key="index">
				<view @tap="goto" :data-url="'/pagesExt/levelreward/index?id=' + item.id">
					<view class="ind_busbox flex1 flex-row">
						<view class="ind_buspic flex0">
							<view class="level-icon" :style="{background:t('color1')}">{{item.name.substr(0,1)}}</view>
						</view>
						<view class="flex1">
							<view class="bus_title">{{item.name}}</view>
							<view class="status-tag" :class="item.status == 1 ? 'status-active' : 'status-inactive'">
                                {{item.status == 1 ? '启用中' : '已禁用'}}
                            </view>
							
							<view class="bus_level">
								<text class="level-label">推荐人等级:</text>
								<text class="level-names" v-if="item.from_level_names && item.from_level_names.length > 0" v-for="(level, lidx) in item.from_level_names" :key="'from-'+lidx">
									{{level}}
								</text>
								<text class="level-names" v-else>{{item.from_level_name}}</text>
							</view>
							
							<view class="bus_level">
								<text class="level-label">被推荐人等级:</text>
								<text class="level-names" v-if="item.to_level_names && item.to_level_names.length > 0" v-for="(level, lidx) in item.to_level_names" :key="'to-'+lidx">
									{{level}}
								</text>
								<text class="level-names" v-else>{{item.to_level_name}}</text>
							</view>
							
							<view class="divider"></view>
							
							<view class="reward-rules">
								<view class="reward-rule" v-for="(rule, idx) in item.reward_rules" :key="idx" v-if="idx < 2">
									<text>推荐 {{rule.recommend_count}} 人，奖励 {{rule.reward_amount}} 
                                        {{rule.reward_type === 'balance' ? t('余额') : 
                                          rule.reward_type === 'points' ? t('积分') : 
                                          rule.reward_type === 'commission' ? t('佣金') : 
                                          rule.reward_type === 'xianjinquan' ? t('现金券') : t('贡献值')}}
                                    </text>
								</view>
								<view v-if="item.reward_rules && item.reward_rules.length > 2" style="text-align: right; color: #007AFF; font-size: 24rpx; padding: 6rpx 0;">
                                    查看更多规则 >
                                </view>
							</view>
							
							<view class="bus_time">创建时间: {{formatTime(item.createtime)}}</view>
						</view>
					</view>
				</view>
				</block>
				<nomore v-if="nomore"></nomore>
				<nodata v-if="nodata"></nodata>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
		opt:{},
		loading:false,
		isload: false,
		
		pre_url:app.globalData.pre_url,
		datalist: [],
		pagenum: 1,
		keyword: '',
		nomore: false,
		nodata: false,
    };
  },

  onLoad: function (opt) {
	this.opt = app.getopts(opt);
	if(this.opt.keyword) {
		this.keyword = this.opt.keyword;
	}
	this.getdata();
  },
  onPullDownRefresh: function () {
	this.getdata();
  },
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getDataList(true);
    }
  },
  methods: {
	getdata: function () {
		var that = this;
		that.loading = true;
		app.get('ApiLevelreward/getlevels', {}, function (res) {
			that.loading = false;
			that.loaded();
			that.getDataList();
		});
	},
    getDataList: function (loadmore) {
		if(!loadmore){
			this.pagenum = 1;
			this.datalist = [];
		}
        var that = this;
        var pagenum = that.pagenum;
        var keyword = that.keyword;
		that.loading = true;
		that.nodata = false;
		that.nomore = false;
        app.post('ApiLevelreward/getlist', {pagenum: pagenum, limit: 20, keyword: keyword}, function (res) {
			that.loading = false;
			uni.stopPullDownRefresh();
            var data = res.data;
            if (pagenum == 1) {
				that.datalist = data;
				if (data.length == 0) {
					that.nodata = true;
				}
            } else {
				if (data.length == 0) {
					that.nomore = true;
				} else {
					var datalist = that.datalist;
					var newdata = datalist.concat(data);
					that.datalist = newdata;
				}
            }
        });
    },
    search: function (e) {
		var that = this;
		var keyword = e.detail.value;
		that.keyword = keyword;
		that.pagenum = 1;
		that.datalist = [];
		that.getDataList();
    },
	formatTime: function(timestamp) {
		if (!timestamp) return '';
		var date = new Date(timestamp * 1000);
		var year = date.getFullYear();
		var month = ('0' + (date.getMonth() + 1)).slice(-2);
		var day = ('0' + date.getDate()).slice(-2);
		var hours = ('0' + date.getHours()).slice(-2);
		var minutes = ('0' + date.getMinutes()).slice(-2);
		
		return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
	}
  }
};
</script>
<style>
.search-container {
    position: fixed;
    width: 100%;
    background: linear-gradient(to right, #f9faff, #f5f7ff);
    z-index: 9;
    top: var(--window-top);
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10rpx); }
    to { opacity: 1; transform: translateY(0); }
}

.topsearch {
    width: 100%;
    padding: 20rpx 24rpx;
}

.topsearch .f1 {
    height: 70rpx;
    border-radius: 35rpx;
    border: 0;
    background-color: #fff;
    flex: 1;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.04);
    transition: box-shadow 0.3s;
}

.topsearch .f1:active {
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}

.topsearch .f1 .img {
    width: 28rpx;
    height: 28rpx;
    margin-left: 24rpx;
}

.topsearch .f1 input {
    height: 100%;
    flex: 1;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333;
}

.topsearch .search-btn {
    display: flex;
    align-items: center;
    color: #5a5a5a;
    font-size: 30rpx;
    width: 60rpx;
    text-align: center;
    margin-left: 20rpx;
}

.ind_business {
    width: 100%;
    margin-top: 110rpx;
    font-size: 26rpx;
    padding: 24rpx;
    background: #f9f9f9;
    min-height: calc(100vh - 110rpx);
}

.ind_business .ind_busbox {
    width: 100%;
    background: linear-gradient(to right, #fff, #f8f9ff);
    padding: 30rpx 24rpx;
    overflow: hidden;
    margin-bottom: 24rpx;
    border-radius: 20rpx;
    position: relative;
    box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.03);
    transition: transform 0.3s ease;
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ind_business .ind_busbox:nth-child(3n+1) {
    animation-delay: 0.1s;
}

.ind_business .ind_busbox:nth-child(3n+2) {
    animation-delay: 0.2s;
}

.ind_business .ind_busbox:nth-child(3n+3) {
    animation-delay: 0.3s;
}

.ind_business .ind_busbox:active {
    transform: translateY(2rpx);
}

.ind_business .ind_buspic {
    width: 90rpx;
    height: 90rpx;
    margin-right: 28rpx;
}

.ind_business .ind_buspic .level-icon {
    width: 100%;
    height: 100%;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 40rpx;
    font-weight: bold;
    box-shadow: 0 6rpx 12rpx rgba(0,0,0,0.12);
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.ind_business .bus_title {
    font-size: 32rpx;
    color: #222;
    font-weight: 600;
    line-height: 46rpx;
    margin-bottom: 12rpx;
}

.ind_business .bus_level {
    font-size: 26rpx;
    color: #666;
    line-height: 36rpx;
    margin-bottom: 10rpx;
    display: flex;
    flex-wrap: wrap;
}

.ind_business .bus_level .level-label {
    font-weight: 500;
    margin-right: 10rpx;
    color: #999;
}

.ind_business .bus_level .level-names {
    color: #333;
    background: #f5f6f8;
    padding: 4rpx 12rpx;
    border-radius: 6rpx;
    margin-right: 8rpx;
    margin-bottom: 6rpx;
    display: inline-block;
}

.ind_business .reward-rules {
    margin-top: 16rpx;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 12rpx;
    background: rgba(249,249,249,0.6);
    border-radius: 10rpx;
}

.ind_business .reward-rules .reward-rule {
    font-size: 26rpx;
    color: #FF6B00;
    line-height: 38rpx;
    background: rgba(255,107,0,0.08);
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    margin-bottom: 8rpx;
    display: inline-block;
    box-shadow: 0 2rpx 8rpx rgba(255,107,0,0.05);
    transition: transform 0.2s;
}

.ind_business .reward-rules .reward-rule:active {
    transform: scale(0.98);
}

.ind_business .bus_time {
    font-size: 24rpx;
    color: #999;
    margin-top: 16rpx;
    background: rgba(245,245,245,0.6);
    padding: 6rpx 12rpx;
    border-radius: 6rpx;
    display: inline-block;
}

/* 状态标签 */
.status-tag {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    padding: 6rpx 16rpx;
    border-radius: 30rpx;
    font-size: 24rpx;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.status-active {
    background: linear-gradient(to right, #E6F7E6, #d7f5d7);
    color: #07C160;
}

.status-inactive {
    background: linear-gradient(to right, #F5F5F5, #ebebeb);
    color: #999;
}

/* 分割线 */
.divider {
    height: 1rpx;
    background: linear-gradient(to right, rgba(238,238,238,0.5), rgba(238,238,238,0.8), rgba(238,238,238,0.5));
    margin: 16rpx 0;
}
</style> 