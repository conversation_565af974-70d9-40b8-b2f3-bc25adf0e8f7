<template>
<view class="dp-referral" :style="{
    margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx', 
    backgroundColor: params.bgcolor || '#FFFFFF', 
    borderRadius: params.radius+'rpx',
    boxShadow: params.shadowShow == '1' ? '0 4rpx 16rpx rgba(0, 0, 0, 0.08)' : 'none'
  }">
  <!-- 标题 -->
  <view class="referral-title" :style="{
      color: params.titlecolor || '#333333',
      borderBottom: params.showTitleBorder == '1' ? '1px solid #F2F2F2' : 'none',
      paddingBottom: params.showTitleBorder == '1' ? '20rpx' : '0'
    }">
    <text class="title-text">{{params.title || '我的推荐人'}}</text>
    <text class="title-desc" v-if="params.subtitle" :style="{color: params.subtitlecolor || '#999999'}">{{params.subtitle}}</text>
  </view>
  
  <!-- 推荐人信息区域 -->
  <view class="referral-content" @tap="showDetailInfo">
    <!-- 推荐人头像 -->
    <view class="referral-avatar" :class="{'avatar-with-border': params.avatarBorder == '1'}">
      <image class="avatar-img" :src="data.userinfo.referral_avatar || '/static/img/default-avatar.png'" mode="aspectFill"></image>
      <view class="level-badge" v-if="data.userinfo.referral_level" :style="{backgroundColor: params.badgeBgColor || '#FF6A00', color: params.badgeTextColor || '#FFFFFF'}">
        {{data.userinfo.referral_level}}
      </view>
    </view>
    
    <!-- 推荐人详细信息 -->
    <view class="referral-info">
      <view class="referral-name" :style="{color: params.namecolor || '#333333'}">
        {{data.userinfo.referral_name || '暂无推荐人'}}
      </view>
      <view class="referral-tags" v-if="params.showTags == '1' && data.userinfo.referral_level">
        <text class="level-tag" :style="{
            backgroundColor: params.tagBgColor || '#FFF5E6', 
            color: params.tagTextColor || '#FF6A00',
            borderColor: params.tagBorderColor || '#FFDCB5'
          }">{{data.userinfo.referral_level}}</text>
      </view>
      
      <!-- 展开查看更多 -->
      <view class="more-info-hint" v-if="params.showMoreInfo == '1'">
        <text class="more-info-text">点击查看更多信息</text>
        <text class="more-info-icon">▼</text>
      </view>
    </view>
    
    <!-- 联系方式 -->
    <view class="referral-contact" v-if="data.userinfo.referral_tel || data.userinfo.referral_weixin" @tap.stop>
      <view class="contact-btn tel-btn" v-if="data.userinfo.referral_tel" @tap="callReferral" 
          :style="{backgroundColor: params.btnBgColor || '#f9f9f9'}">
        <image class="contact-icon" src="/static/img/tel2.png"></image>
        <text class="contact-text" :style="{color: params.btnTextColor || '#666666'}">{{params.telBtnText || '联系Ta'}}</text>
      </view>
      <view class="contact-btn wechat-btn" v-if="data.userinfo.referral_weixin" @tap="copyWechat"
          :style="{backgroundColor: params.btnBgColor || '#f9f9f9'}">
        <image class="contact-icon" src="/static/img/weixin.png" v-if="isWeixinImgExists"></image>
        <image class="contact-icon" src="/static/img/wechat.png" v-else></image>
        <text class="contact-text" :style="{color: params.btnTextColor || '#666666'}">{{params.wechatBtnText || '复制微信'}}</text>
      </view>
    </view>
  </view>
  
  <!-- 展开的详细信息 -->
  <view class="detail-info" v-if="isDetailShown" :style="{
      backgroundColor: params.detailBgColor || '#F9F9F9', 
      borderRadius: params.detailRadius || '8rpx'
    }">
    <view class="detail-item" v-if="data.userinfo.referral_tel">
      <text class="detail-label">联系电话：</text>
      <text class="detail-value" user-select="true">{{data.userinfo.referral_tel}}</text>
    </view>
    <view class="detail-item" v-if="data.userinfo.referral_weixin">
      <text class="detail-label">微信号：</text>
      <text class="detail-value" user-select="true">{{data.userinfo.referral_weixin}}</text>
      <text class="copy-btn" @tap="copyWechat">复制</text>
    </view>
    <view class="detail-item" v-if="params.showReferralId == '1' && data.userinfo.referral_id">
      <text class="detail-label">推荐人ID：</text>
      <text class="detail-value">{{data.userinfo.referral_id}}</text>
    </view>
    <view class="detail-close" @tap="hideDetailInfo">收起 ▲</view>
  </view>
  
  <!-- 推荐内容 -->
  <view class="referral-message" v-if="params.showMessage == '1' && params.messageContent">
    <text class="message-text" :style="{color: params.messageColor || '#999999'}">{{params.messageContent}}</text>
  </view>
  
  <!-- 功能按钮区域 -->
  <view class="referral-actions" v-if="params.showActions == '1'">
    <view class="action-btn" @tap="handleAction(params.actionUrl1)" v-if="params.actionBtn1">
      <text class="action-text" :style="{color: params.actionBtnColor || '#FF6A00'}">{{params.actionBtn1}}</text>
    </view>
    <view class="action-btn" @tap="handleAction(params.actionUrl2)" v-if="params.actionBtn2">
      <text class="action-text" :style="{color: params.actionBtnColor || '#FF6A00'}">{{params.actionBtn2}}</text>
    </view>
  </view>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      isCalling: false,
      copySuccess: false,
      isDetailShown: false,
      isWeixinImgExists: false
    }
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  created() {
    // 检查图片是否存在
    this.checkImageExists('/static/img/weixin.png', (exists) => {
      this.isWeixinImgExists = exists;
    });
  },
  methods: {
    // 检查图片是否存在
    checkImageExists(url, callback) {
      uni.getImageInfo({
        src: url,
        success: function() {
          callback(true);
        },
        fail: function() {
          callback(false);
        }
      });
    },
    
    // 显示详细信息
    showDetailInfo() {
      if (this.params.showMoreInfo == '1') {
        this.isDetailShown = !this.isDetailShown;
      }
    },
    
    // 隐藏详细信息
    hideDetailInfo() {
      this.isDetailShown = false;
    },
    
    // 拨打推荐人电话
    callReferral() {
      if (this.isCalling) return;
      this.isCalling = true;
      
      const tel = this.data.userinfo.referral_tel;
      if (tel) {
        // 电话按钮震动效果
        const telBtn = document.querySelector('.tel-btn');
        if (telBtn) {
          telBtn.classList.add('btn-vibrate');
          setTimeout(() => {
            telBtn.classList.remove('btn-vibrate');
            this.isCalling = false;
          }, 500);
        }
        
        uni.makePhoneCall({
          phoneNumber: tel,
          success: () => {
            console.log('电话拨打成功');
          },
          fail: () => {
            console.log('电话拨打失败');
            uni.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          },
          complete: () => {
            this.isCalling = false;
          }
        });
      } else {
        uni.showToast({
          title: '推荐人未设置电话',
          icon: 'none'
        });
        this.isCalling = false;
      }
    },
    
    // 复制微信号
    copyWechat() {
      if (this.copySuccess) return;
      
      const weixin = this.data.userinfo.referral_weixin;
      if (weixin) {
        // 复制按钮效果
        const wechatBtn = document.querySelector('.wechat-btn');
        if (wechatBtn) {
          wechatBtn.classList.add('btn-success');
        }
        
        uni.setClipboardData({
          data: weixin,
          success: () => {
            this.copySuccess = true;
            uni.showToast({
              title: '微信号已复制',
              icon: 'success'
            });
            
            setTimeout(() => {
              if (wechatBtn) {
                wechatBtn.classList.remove('btn-success');
              }
              this.copySuccess = false;
            }, 1500);
          },
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        });
      } else {
        uni.showToast({
          title: '推荐人未设置微信号',
          icon: 'none'
        });
      }
    },
    
    // 处理功能按钮点击
    handleAction(url) {
      if (url) {
        app.goto(url);
      }
    }
  }
}
</script>

<style>
.dp-referral {
  padding: 30rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.referral-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.referral-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #FF6A00;
  border-radius: 3rpx;
}

.title-text {
  font-weight: bold;
}

.title-desc {
  font-size: 24rpx;
  font-weight: normal;
}

.referral-content {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}

.referral-avatar {
  margin-right: 30rpx;
  position: relative;
}

.avatar-with-border .avatar-img {
  border: 3rpx solid #FFE0C9;
  box-shadow: 0 4rpx 12rpx rgba(255, 106, 0, 0.15);
}

.avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.avatar-img:active {
  transform: scale(0.96);
}

.level-badge {
  position: absolute;
  right: -5rpx;
  bottom: 5rpx;
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  background-color: #FF6A00;
  color: #FFFFFF;
  border-radius: 20rpx;
  transform: scale(0.9);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.referral-info {
  flex: 1;
}

.referral-name {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.referral-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8rpx;
}

.level-tag {
  font-size: 22rpx;
  background-color: #FFF5E6;
  color: #FF6A00;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  border: 1px solid #FFDCB5;
  display: inline-block;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.more-info-hint {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
}

.more-info-text {
  font-size: 22rpx;
  color: #999;
}

.more-info-icon {
  font-size: 20rpx;
  color: #999;
  margin-left: 4rpx;
}

.referral-contact {
  display: flex;
  align-items: center;
}

.contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  margin-left: 16rpx;
  transition: all 0.3s ease;
}

.contact-btn:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.contact-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 6rpx;
}

.contact-text {
  font-size: 24rpx;
  color: #666;
}

/* 展开的详细信息 */
.detail-info {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 10rpx;
  font-size: 26rpx;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.detail-item {
  display: flex;
  padding: 12rpx 0;
  border-bottom: 1px dashed rgba(0,0,0,0.05);
  align-items: center;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: #999;
  width: 160rpx;
}

.detail-value {
  color: #333;
  flex: 1;
}

.copy-btn {
  color: #ff6a00;
  font-size: 22rpx;
  background: rgba(255,106,0,0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}

.detail-close {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  margin-top: 10rpx;
  padding: 10rpx 0;
}

/* 消息区域 */
.referral-message {
  margin-top: 20rpx;
  padding: 16rpx;
  background-color: #F9F9F9;
  border-radius: 10rpx;
}

.message-text {
  font-size: 24rpx;
  line-height: 1.5;
  color: #999;
}

/* 功能按钮区域 */
.referral-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.action-btn {
  margin-left: 20rpx;
  padding: 8rpx 24rpx;
  border: 1px solid rgba(255,106,0,0.3);
  border-radius: 30rpx;
}

.action-text {
  font-size: 24rpx;
  color: #FF6A00;
}

/* 动画效果 */
@keyframes vibrate {
  0% { transform: translateX(0); }
  25% { transform: translateX(3rpx); }
  50% { transform: translateX(-3rpx); }
  75% { transform: translateX(3rpx); }
  100% { transform: translateX(0); }
}

.btn-vibrate {
  animation: vibrate 0.3s ease 2;
}

.btn-success {
  background-color: #E6F7E6 !important;
}

/* 响应式适配 */
@media screen and (max-width: 320px) {
  .referral-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .referral-avatar {
    margin-bottom: 20rpx;
  }
  
  .referral-contact {
    margin-top: 20rpx;
    width: 100%;
    justify-content: flex-end;
  }
}
</style> 