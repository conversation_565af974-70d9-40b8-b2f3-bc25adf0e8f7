<template>
	<view class="container">
		<block v-if="isload">
			<view class="header-info" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}">
				<view class="equity-value">
					<view class="label">当前股权单价</view>
					<view class="value">{{equity_value}}</view>
				</view>
				<view class="my-ranking" v-if="my_ranking > 0">
					<view class="label">我的排名</view>
					<view class="value">第 {{my_ranking}} 名</view>
				</view>
				<view class="my-ranking" v-else>
					<view class="label">我的排名</view>
					<view class="value">暂未上榜</view>
				</view>
			</view>
			
			<view class="ranking-list">
				<view class="list-header">
					<view class="header-item rank">排名</view>
					<view class="header-item user">用户</view>
					<view class="header-item count">持股数</view>
					<view class="header-item value">价值</view>
				</view>
				
				<view class="list-content">
					<view class="list-item" v-for="(item, index) in equity_ranking" :key="index" :class="{'highlight': item.mid == my_mid}">
						<view class="item-rank" :class="'top' + item.ranking" v-if="item.ranking <= 3">{{item.ranking}}</view>
						<view class="item-rank" v-else>{{item.ranking}}</view>
						
						<view class="item-user">
							<image class="user-avatar" :src="item.headimg"></image>
							<view class="user-name">{{item.nickname}}</view>
						</view>
						
						<view class="item-count">{{formatNumber(item.total_num)}}</view>
						<view class="item-value">{{formatNumber(item.equity_value)}}</view>
					</view>
				</view>
				
				<view class="no-more" v-if="nomore">没有更多数据了</view>
				<view class="loading-more" v-if="!nomore && !nodata && equity_ranking.length > 0">
					<view class="loading-text" @tap="getMoreData">点击加载更多</view>
				</view>
				<view class="no-data" v-if="nodata">
					<image class="no-data-img" src="/static/img/nodata.png" mode="widthFix"></image>
					<view class="no-data-text">暂无排行数据</view>
				</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			loading: false,
			isload: false,
			pre_url: app.globalData.pre_url,
			
			equity_value: "0.00",  // 单位股权价值
			my_ranking: 0,         // 我的排名，0表示未上榜
			equity_ranking: [],    // 排行榜数据
			
			pagenum: 1,            // 当前页码
			nomore: false,         // 是否没有更多数据
			nodata: false,         // 是否暂无数据
			my_mid: 0              // 我的mid
		};
	},
	onLoad: function(opt) {
		this.getdata();
	},
	onPullDownRefresh: function() {
		this.pagenum = 1;
		this.getdata();
	},
	methods: {
		getdata: function() {
			var that = this;
			that.loading = true;
			that.nomore = false;
			that.nodata = false;
			
			// 重置页码时清空列表
			if (that.pagenum === 1) {
				that.equity_ranking = [];
			}
			
			app.get('ApiEquityPool/ranking', {pagenum: that.pagenum}, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 1) {
					that.equity_value = parseFloat(res.data.equity_value).toFixed(2);
					that.my_ranking = res.data.my_ranking || 0;
					
					if (that.pagenum === 1) {
						that.equity_ranking = res.data.equity_ranking;
						if (res.data.equity_ranking.length === 0) {
							that.nodata = true;
						}

						// 获取当前用户的mid
						if (app.globalData.member && app.globalData.member.mid) {
							that.my_mid = app.globalData.member.mid;
						}
					} else {
						// 追加数据
						if (res.data.equity_ranking.length === 0) {
							that.nomore = true;
						} else {
							that.equity_ranking = that.equity_ranking.concat(res.data.equity_ranking);
						}
					}
					
					that.isload = true;
				} else {
					that.$refs.popmsg.show({
						msg: res.msg || '获取数据失败，请重试',
						isok: false
					});
				}
			});
		},
		
		// 加载更多数据
		getMoreData: function() {
			if (!this.nomore && !this.loading) {
				this.pagenum++;
				this.getdata();
			}
		},
		
		// 格式化数字，保留两位小数
		formatNumber: function(num) {
			if (typeof num !== 'number') {
				num = parseFloat(num) || 0;
			}
			return num.toFixed(2);
		}
	}
};
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header-info {
	padding: 40rpx 30rpx;
	color: #fff;
	display: flex;
	justify-content: space-between;
}

.equity-value, .my-ranking {
	text-align: center;
}

.label {
	font-size: 24rpx;
	opacity: 0.8;
	margin-bottom: 10rpx;
}

.value {
	font-size: 40rpx;
	font-weight: bold;
}

.ranking-list {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	margin-top: -20rpx;
	padding: 30rpx 20rpx;
	min-height: 80vh;
}

.list-header {
	display: flex;
	padding: 20rpx 0;
	border-bottom: 1px solid #eee;
	font-size: 28rpx;
	color: #999;
}

.header-item {
	text-align: center;
}

.header-item.rank {
	flex: 1;
}

.header-item.user {
	flex: 3;
	text-align: left;
}

.header-item.count {
	flex: 2;
}

.header-item.value {
	flex: 2;
}

.list-content {
	padding: 10rpx 0;
}

.list-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1px solid #f5f5f5;
	transition: all 0.3s ease;
}

.list-item:active {
	background-color: #f9f9f9;
}

.list-item.highlight {
	background-color: rgba(240, 80, 90, 0.05);
}

.item-rank {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #666;
	font-weight: bold;
}

.item-rank.top1 {
	color: #fff;
	background-color: #ff7043;
	box-shadow: 0 4rpx 8rpx rgba(255, 112, 67, 0.3);
}

.item-rank.top2 {
	color: #fff;
	background-color: #ff9800;
	box-shadow: 0 4rpx 8rpx rgba(255, 152, 0, 0.3);
}

.item-rank.top3 {
	color: #fff;
	background-color: #ffc107;
	box-shadow: 0 4rpx 8rpx rgba(255, 193, 7, 0.3);
}

.item-user {
	flex: 3;
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 70rpx;
	height: 70rpx;
	border-radius: 35rpx;
	margin-right: 15rpx;
	border: 2px solid #fff;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.user-name {
	font-size: 28rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 150rpx;
}

.item-count {
	flex: 2;
	text-align: center;
	font-size: 28rpx;
	color: #666;
}

.item-value {
	flex: 2;
	text-align: center;
	font-size: 28rpx;
	color: #f0505a;
	font-weight: bold;
}

.no-more, .loading-more {
	text-align: center;
	padding: 30rpx 0;
	font-size: 26rpx;
	color: #999;
}

.loading-text {
	display: inline-block;
	padding: 10rpx 30rpx;
	border: 1px solid #ddd;
	border-radius: 30rpx;
}

.no-data {
	padding: 80rpx 0;
	text-align: center;
}

.no-data-img {
	width: 200rpx;
	margin-bottom: 20rpx;
}

.no-data-text {
	font-size: 28rpx;
	color: #999;
}
</style> 