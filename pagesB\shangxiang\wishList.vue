<template>
	<view class="container">
		<block v-if="isload">
			<!-- 搜索栏 -->
			<view class="search-bar">
				<input class="search-input" 
					   type="text" 
					   placeholder="搜索许愿内容" 
					   v-model="searchKeyword" 
					   @confirm="searchWishes"/>
				<button class="search-btn" @tap="searchWishes">搜索</button>
			</view>
			
			<!-- 筛选栏 -->
			<view class="filter-bar">
				<view class="filter-item">
					<text class="filter-label">类型:</text>
					<picker @change="onTypeChange" :value="typeIndex" :range="typeOptions">
						<view class="picker-text">{{typeOptions[typeIndex]}}</view>
					</picker>
				</view>
				<view class="filter-item">
					<text class="filter-label">排序:</text>
					<picker @change="onSortChange" :value="sortIndex" :range="sortOptions">
						<view class="picker-text">{{sortOptions[sortIndex]}}</view>
					</picker>
				</view>
			</view>
			
			<!-- 许愿列表 -->
			<view class="wish-list">
				<view v-for="(item, index) in datalist" :key="index" class="wish-item">
					<view class="user-info">
						<image class="user-avatar" :src="item.headimg || defaultAvatar"/>
						<view class="user-details">
							<text class="nickname">{{item.nickname}}</text>
							<text class="time">{{formatTime(item.create_time)}}</text>
						</view>
						<text class="type-tag" :style="{backgroundColor: getTypeColor(item.type)}">{{getTypeName(item.type)}}</text>
					</view>
					
					<view class="wish-content" v-if="item.wish_content">
						<text>{{item.wish_content}}</text>
					</view>
					
					<view class="wish-footer">
						<text class="amount" style="color: #ff6b6b">￥{{item.amount}}</text>
						<view class="actions">
							<view class="like-btn" @tap="toggleLike" :data-id="item.id">
								<image class="like-icon" :src="item.is_liked ? likedIcon : likeIcon"/>
								<text class="like-count">{{item.like_count || 0}}</text>
							</view>
							<button class="bless-btn" 
									style="background: linear-gradient(90deg, #ff6b6b 0%, rgba(255, 107, 107, 0.8) 100%)"
							@tap="sendBless" 
							:data-id="item.id">祝福</button>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-if="!loading && datalist.length === 0" class="empty-state">
				<image class="empty-icon" :src="pre_url+'/static/img/empty-wishes.png'"/>
				<text class="empty-text">暂无许愿记录</text>
			</view>
		</block>
		
		<nodata v-if="nodata"></nodata>
		<loading v-if="loading"></loading>
		<popmsg ref="popmsg"></popmsg>
		
		<!-- 筛选弹窗 -->
		<view class="modal" v-if="showFilterModal" @tap="showFilterModal = false">
			<view class="filter-modal" @tap.stop>
				<view class="modal-header">
					<view class="modal-title">筛选条件</view>
					<view class="close-btn" @tap="showFilterModal = false">×</view>
				</view>
				
				<view class="filter-content">
					<!-- 类型筛选 -->
					<view class="filter-section">
						<view class="section-title">供奉类型</view>
						<view class="option-list">
							<view class="option-item" 
								  :class="{active: filterType === ''}" 
								  @tap="selectFilterType('')">
								全部
							</view>
							<view class="option-item" 
								  :class="{active: filterType === 'gonghua'}" 
								  @tap="selectFilterType('gonghua')">
								供花
							</view>
							<view class="option-item" 
								  :class="{active: filterType === 'gongguo'}" 
								  @tap="selectFilterType('gongguo')">
								供果
							</view>
							<view class="option-item" 
								  :class="{active: filterType === 'shangxiang'}" 
								  @tap="selectFilterType('shangxiang')">
								上香
							</view>
							<view class="option-item" 
								  :class="{active: filterType === 'xuyuan'}" 
								  @tap="selectFilterType('xuyuan')">
								许愿
							</view>
						</view>
					</view>
					
					<!-- 排序方式 -->
					<view class="filter-section">
						<view class="section-title">排序方式</view>
						<view class="option-list">
							<view class="option-item" 
								  :class="{active: sortType === 'time_desc'}" 
								  @tap="selectSortType('time_desc')">
								最新发布
							</view>
							<view class="option-item" 
								  :class="{active: sortType === 'amount_desc'}" 
								  @tap="selectSortType('amount_desc')">
								金额最高
							</view>
							<view class="option-item" 
								  :class="{active: sortType === 'like_desc'}" 
								  @tap="selectSortType('like_desc')">
								点赞最多
							</view>
						</view>
					</view>
				</view>
				
				<view class="filter-footer">
					<view class="reset-btn" @tap="resetFilter">重置</view>
					<view class="confirm-btn" 
						  style="background: linear-gradient(90deg, #ff6b6b 0%, rgba(255, 107, 107, 0.8) 100%)"
						  @tap="applyFilter">确定</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			isload: false,
			nodata: false,
			datalist: [],
			searchKeyword: '',
			typeIndex: 0,
			sortIndex: 0,
			typeOptions: ['全部', '供花', '供果', '上香', '许愿'],
			sortOptions: ['最新发布', '金额最高', '点赞最多'],
			page: 1,
			limit: 20,
			hasMore: true,
			loading: false,
			loadingMore: false,
			defaultAvatar: '/static/img/default-avatar.png',
			likeIcon: '/static/img/like.png',
			likedIcon: '/static/img/liked.png',
			pre_url: '',
			textset: {}
		}
	},
	onLoad: function(opt) {
		this.opt = app.getopts(opt);
		this.pre_url = app.globalData.pre_url;
		this.getdata();
	},
	
	onPullDownRefresh: function() {
		this.page = 1;
		this.hasMore = true;
		this.getdata();
	},
	
	onReachBottom: function() {
		if (this.hasMore && !this.loading) {
			this.page++;
			this.getdata();
		}
	},
	
	onPullDownRefresh() {
		this.loadWishList(true);
	},
	
	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMore();
		}
	},
	
	methods: {
		// 获取数据
		getdata() {
			var that = this;
			if (that.loading) return;
			that.loading = true;
			
			const params = {
				page: that.page,
				limit: that.limit,
				keyword: that.searchKeyword,
				type: that.getTypeValue(),
				sort: that.getSortValue()
			};
			
			app.post('ApiShangxiang/getWishList', params, function(res) {
				that.loading = false;
				if (res.code === 1) {
					const newList = res.data.list || [];
					if (that.page === 1) {
						that.datalist = newList;
					} else {
						that.datalist = that.datalist.concat(newList);
					}
					that.hasMore = newList.length >= that.limit;
					that.loaded();
				} else {
					app.error(res.msg || '加载失败');
				}
				uni.stopPullDownRefresh();
			});
		},
		
		// 搜索许愿
		searchWishes() {
			this.page = 1;
			this.hasMore = true;
			this.getdata();
		},
		
		// 类型改变
		onTypeChange(e) {
			this.typeIndex = e.detail.value;
			this.page = 1;
			this.hasMore = true;
			this.getdata();
		},
		
		// 排序改变
		onSortChange(e) {
			this.sortIndex = e.detail.value;
			this.page = 1;
			this.hasMore = true;
			this.getdata();
		},
		
		// 选择筛选类型
		selectFilterType(type) {
			this.filterType = type;
		},
		
		// 选择排序类型
		selectSortType(type) {
			this.sortType = type;
		},
		
		// 重置筛选
		resetFilter() {
			this.filterType = '';
			this.sortType = 'time_desc';
		},
		
		// 应用筛选
		applyFilter() {
			this.showFilterModal = false;
			this.updateActiveFilters();
			this.loadWishList(true);
		},
		
		// 更新活跃筛选标签
		updateActiveFilters() {
			this.activeFilters = [];
			
			if (this.searchKeyword) {
				this.activeFilters.push({
					type: 'keyword',
					label: `搜索: ${this.searchKeyword}`
				});
			}
			
			if (this.filterType) {
				this.activeFilters.push({
					type: 'type',
					label: `类型: ${this.getTypeName(this.filterType)}`
				});
			}
			
			if (this.sortType !== 'time_desc') {
				const sortLabels = {
					'amount_desc': '金额最高',
					'like_desc': '点赞最多'
				};
				this.activeFilters.push({
					type: 'sort',
					label: `排序: ${sortLabels[this.sortType]}`
				});
			}
		},
		
		// 移除筛选标签
		removeFilter(index) {
			const filter = this.activeFilters[index];
			
			switch (filter.type) {
				case 'keyword':
					this.searchKeyword = '';
					break;
				case 'type':
					this.filterType = '';
					break;
				case 'sort':
					this.sortType = 'time_desc';
					break;
			}
			
			this.updateActiveFilters();
			this.loadWishList(true);
		},
		
		// 清空所有筛选
		clearAllFilters() {
			this.searchKeyword = '';
			this.filterType = '';
			this.sortType = 'time_desc';
			this.updateActiveFilters();
			this.loadWishList(true);
		},
		
		// 点赞/取消点赞
		toggleLike(e) {
			var that = this;
			const id = e.currentTarget.dataset.id;
			const item = that.datalist.find(item => item.id === id);
			if (!item) return;
			
			app.post('ApiShangxiang/toggleLike', { wish_id: id }, function(res) {
				if (res.code === 1) {
					item.is_liked = !item.is_liked;
					item.like_count = item.is_liked ? (item.like_count || 0) + 1 : Math.max(0, (item.like_count || 0) - 1);
				} else {
					app.error(res.msg || '操作失败');
				}
			});
		},
		
		// 发送祝福
		sendBless(e) {
			const id = e.currentTarget.dataset.id;
			app.success('祝福已发送');
		},
		
		// 获取类型名称
		getTypeName(type) {
			switch (type) {
				case 'gonghua':
					return '供花';
				case 'gongguo':
					return '供果';
				case 'shangxiang':
					return '上香';
				case 'xuyuan':
					return '许愿';
				default:
					return '';
			}
		},
		
		// 获取类型值
		getTypeValue() {
			const typeMap = ['', 'gonghua', 'gongguo', 'shangxiang', 'xuyuan'];
			return typeMap[this.typeIndex] || '';
		},
		
		// 获取排序值
		getSortValue() {
			const sortMap = ['time_desc', 'amount_desc', 'like_desc'];
			return sortMap[this.sortIndex] || 'time_desc';
		},
		
		// 获取类型颜色
		getTypeColor(type) {
			switch (type) {
				case 'gonghua':
					return '#ff6b6b';
				case 'gongguo':
					return '#4ecdc4';
				case 'shangxiang':
					return '#ffe66d';
				case 'xuyuan':
					return '#ff8a80';
				default:
					return '#999';
			}
		},
		
		// 格式化时间
		formatTime(time) {
			return app.formatTime(time);
		},
		
		// 页面加载完成
		loaded() {
			var that = this;
			that.isload = true;
			that.textset = app.globalData.textset;
			uni.setNavigationBarTitle({
				title: that.t('许愿列表')
			});
		}
	}
}
</script>

<style>
.page {
	background: #f5f5f5;
	min-height: 100vh;
}

.search-bar {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
}

.search-input-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f8f8f8;
	border-radius: 25rpx;
	padding: 0 20rpx;
	height: 70rpx;
}

.search-icon {
	width: 30rpx;
	height: 30rpx;
	margin-right: 15rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	height: 100%;
}

.filter-btn {
	width: 60rpx;
	height: 60rpx;
	margin-left: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.filter-btn image {
	width: 40rpx;
	height: 40rpx;
}

.filter-tags {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	flex-wrap: wrap;
	gap: 15rpx;
}

.filter-tag {
	display: flex;
	align-items: center;
	padding: 8rpx 15rpx;
	background: #f0f0f0;
	border-radius: 15rpx;
	font-size: 24rpx;
	color: #666;
}

.remove-tag {
	margin-left: 10rpx;
	font-size: 28rpx;
	color: #999;
}

.clear-all {
	padding: 8rpx 15rpx;
	color: #007aff;
	font-size: 24rpx;
}

.wish-list {
	padding: 20rpx 30rpx;
}

.wish-item {
	background: #fff;
	border-radius: 15rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.user-section {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.user-info {
	flex: 1;
}

.nickname {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 5rpx;
}

.time {
	font-size: 24rpx;
	color: #999;
}

.type-badge {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	color: #fff;
}

.type-gonghua {
	background: #ff6b6b;
}

.type-gongguo {
	background: #4ecdc4;
}

.type-shangxiang {
	background: #ffe66d;
	color: #333;
}

.type-xuyuan {
	background: #ff8a80;
}

.wish-content {
	margin-bottom: 20rpx;
}

.content-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	padding: 20rpx;
	background: #f8f8f8;
	border-radius: 10rpx;
}

.wish-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.amount {
	font-size: 32rpx;
	font-weight: bold;
}

.actions {
	display: flex;
	gap: 30rpx;
}

.action-btn {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #666;
	gap: 8rpx;
}

.action-btn image {
	width: 30rpx;
	height: 30rpx;
}

.like-btn.active {
	color: #ff6b6b;
}

.load-more {
	text-align: center;
	padding: 40rpx;
	font-size: 28rpx;
	color: #666;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-state image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}

.empty-state text {
	font-size: 28rpx;
	color: #999;
}

.modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.filter-modal {
	width: 100%;
	max-height: 80%;
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.close-btn {
	font-size: 40rpx;
	color: #999;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.filter-content {
	padding: 30rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.filter-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.option-list {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.option-item {
	padding: 15rpx 25rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	font-size: 26rpx;
	color: #666;
	transition: all 0.3s ease;
}

.option-item.active {
	border-color: #007aff;
	color: #007aff;
	background: rgba(0, 122, 255, 0.1);
}

.filter-footer {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

.reset-btn {
	flex: 1;
	padding: 30rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 10rpx;
	text-align: center;
	font-size: 28rpx;
	color: #666;
}

.confirm-btn {
	flex: 2;
	padding: 30rpx;
	border-radius: 10rpx;
	text-align: center;
	font-size: 28rpx;
	color: #fff;
	font-weight: bold;
}
</style>