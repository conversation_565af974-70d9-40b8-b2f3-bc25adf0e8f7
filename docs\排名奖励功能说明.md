# 排名奖励功能说明文档

## 功能概述

排名奖励功能是根据用户的下线人数排名或特定会员等级下线人数排名，给予相应奖励的功能。奖励模式分为根据自身消费和根据排名两种方式。

## 目录结构

```
pagesExa/ranking-reward/
├── index.vue         # 排名奖励首页
├── preview.vue       # 排名奖励规则预览
├── records.vue       # 排名奖励记录
└── rules.vue         # 排名奖励规则列表
```

## 页面说明

### 1. 排名奖励首页 (index.vue)

首页展示用户的奖励统计数据、可用的奖励规则列表以及最近的奖励记录。

**主要功能：**
- 展示累计获得奖励、已发放奖励、待发放奖励总额
- 显示可用的奖励规则列表
- 展示最近的奖励记录
- 提供导航到各功能页面的入口

**API接口：**
- `apipaimingjiang/getRankingStats`：获取用户的排名奖励统计数据
- `apipaimingjiang/getRankingRules`：获取用户可用的排名奖励规则列表

### 2. 排名奖励规则预览 (preview.vue)

展示特定奖励规则的详细信息，包括规则说明、分红池金额计算、排名列表以及用户自身的排名信息。

**主要功能：**
- 展示规则基本信息（名称、奖励模式、排名类型、奖励比例）
- 展示用户自身的排名信息
- 显示分红池金额和本月消费总额
- 提供周期选择器，可查看不同周期的排名情况
- 展示排名列表，包含排名、会员信息、统计数量、奖励比例、奖励金额等信息
- 支持分页浏览排名列表

**API接口：**
- `apipaimingjiang/previewRanking`：获取规则详情、排名列表和分红池金额计算
- `apipaimingjiang/getMyRankInfo`：获取用户自身的排名信息

### 3. 排名奖励记录 (records.vue)

展示用户的排名奖励记录历史，支持按规则筛选。

**主要功能：**
- 展示用户的奖励记录列表
- 支持按规则筛选记录
- 显示记录详情（规则名称、统计周期、排名、统计数量、奖励比例、奖励金额、状态等）
- 支持加载更多记录

**API接口：**
- `apipaimingjiang/getRankingRecords`：获取用户的排名奖励记录
- `apipaimingjiang/getRankingRules`：获取用户可用的排名奖励规则列表（用于筛选）

### 4. 排名奖励规则列表 (rules.vue)

展示所有可用的排名奖励规则列表。

**主要功能：**
- 展示所有可用的奖励规则列表
- 显示规则基本信息（名称、奖励模式、排名类型、奖励比例、奖励排名）
- 提供导航到规则预览页面的入口

**API接口：**
- `apipaimingjiang/getRankingRules`：获取用户可用的排名奖励规则列表

## 通用组件和样式

1. **状态标签**：用于显示奖励记录的状态（待发放、已发放、已拒绝）
2. **无数据提示**：当列表为空时显示的提示信息
3. **加载更多**：用于分页加载更多数据
4. **卡片样式**：统一的卡片样式，用于展示规则、记录等信息

## 日志记录

所有关键操作都添加了日志记录，格式为：`YYYY-MM-DD HH:MM:SS,SSS-INFO-[文件名][函数名_序号]`，方便排查问题。

## 使用示例

1. **查看排名奖励统计**：
   - 打开排名奖励首页，查看累计获得奖励、已发放奖励、待发放奖励总额

2. **查看排名奖励规则**：
   - 在首页点击奖励规则卡片，进入规则详情页
   - 查看规则基本信息、分红池金额、排名列表等

3. **查看排名奖励记录**：
   - 在首页点击"查看更多"，进入奖励记录页面
   - 可以按规则筛选记录

## 注意事项

1. 所有金额数据都保留两位小数显示
2. 日期格式统一使用 YYYY-MM-DD HH:MM:SS
3. 统计周期格式统一使用 YYYYMM，例如：202406 表示 2024年6月
4. 接口返回的状态码：1表示成功，0表示失败
5. 请求失败时，通过 msg 字段获取错误提示信息 