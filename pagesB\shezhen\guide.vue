<template>
	<view class="guide-container">
		<!-- 顶部标题区域 -->
		<view class="header-section">
			<image class="header-image" src="/static/shezhen/tongue-icon.png" mode="aspectFit"></image>
			<text class="header-title">AI智能舌诊</text>
			<text class="header-subtitle">通过舌象分析，了解身体健康状况</text>
		</view>

		<!-- 功能介绍区域 -->
		<view class="intro-section">
			<view class="intro-card">
				<view class="card-header">
					<text class="card-icon">🔍</text>
					<text class="card-title">什么是舌诊？</text>
				</view>
				<text class="card-content">
					舌诊是中医诊断的重要方法之一，通过观察舌质、舌苔的形态、色泽、润燥等变化，来判断疾病的性质、病位的深浅、气血的盛衰、津液的存亡。
				</text>
			</view>

			<view class="intro-card">
				<view class="card-header">
					<text class="card-icon">🎯</text>
					<text class="card-title">AI舌诊功能</text>
				</view>
				<text class="card-content">
					运用先进的人工智能技术，结合传统中医理论，对舌象进行精准分析，为您提供个性化的健康建议和调理方案。
				</text>
			</view>
		</view>

		<!-- 使用指南区域 -->
		<view class="guide-section">
			<text class="section-title">拍摄指南</text>
			<view class="guide-steps">
				<view class="step-item" v-for="(step, index) in guideSteps" :key="index">
					<view class="step-number">{{ index + 1 }}</view>
					<view class="step-content">
						<text class="step-title">{{ step.title }}</text>
						<text class="step-desc">{{ step.desc }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 注意事项区域 -->
		<view class="notice-section">
			<text class="section-title">注意事项</text>
			<view class="notice-list">
				<view class="notice-item" v-for="(notice, index) in noticeList" :key="index">
					<text class="notice-icon">⚠️</text>
					<text class="notice-text">{{ notice }}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="action-section">
			<button class="start-btn" @click="startDiagnosis">
				<text class="btn-text">开始舌诊拍摄</text>
			</button>
			<text class="disclaimer">
				*本功能仅供健康参考，不能替代专业医生诊断
			</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ShezhenGuide',
	data() {
		return {
			// 拍摄指南步骤数据
			guideSteps: [
				{
					title: '保持良好光线',
					desc: '在充足的自然光或白光下进行拍摄，避免暗光环境'
				},
				{
					title: '清洁口腔',
					desc: '拍摄前请清洁口腔，避免食物残留影响分析'
				},
				{
					title: '正确姿势',
					desc: '张大嘴巴，充分露出舌头，保持舌面平整'
				},
				{
					title: '稳定拍摄',
					desc: '保持手机稳定，将舌头置于画面中央进行拍摄'
				}
			],
			// 注意事项列表
			noticeList: [
				'请在饭后30分钟后进行拍摄',
				'避免刚刷牙或使用漱口水后立即拍摄',
				'不要在饮用有色饮料后立即检测',
				'如有舌部疾病请先咨询医生',
				'孕妇、儿童使用前请咨询专业医生'
			],
			// 舌诊配置信息
			configInfo: {
				isConfigured: false,
				usePublicAPI: true,
				remainingCalls: 0,
				callCost: 0
			}
		}
	},
	onLoad() {
		console.log('[2024-12-28 02:13:45] 舌诊指南页面加载完成');
		this.getShezhenConfig();
	},
	methods: {
		/**
		 * 获取舌诊配置信息
		 */
		getShezhenConfig() {
			var that = this;
			console.log('[2024-12-28 02:13:47] 开始获取舌诊配置');
			
			app.get('ApiSheZhen/getConfig', {}, function(res) {
				console.log('[2024-12-28 02:13:48] 获取配置成功：', res);
				if (res.status == 1) {
					that.configInfo = {
						isConfigured: res.data.isConfigured || false,
						usePublicAPI: res.data.usePublicAPI || false,
						remainingCalls: res.data.remainingCalls || 0,
						callCost: res.data.callCost || 0
					};
				} else {
					uni.showToast({
						title: res.msg || '获取配置失败',
						icon: 'none'
					});
				}
			});
		},
		
		/**
		 * 开始舌诊拍摄
		 * 跳转到拍摄页面
		 */
		startDiagnosis() {
			console.log('[2024-12-28 02:13:50] 开始舌诊 - 检查配置');
			
			// 检查是否有足够的分析次数
			if (this.configInfo.isConfigured && this.configInfo.remainingCalls <= 0) {
				uni.showToast({
					title: '分析次数不足，请联系管理员',
					icon: 'none'
				});
				return;
			}
			
			uni.navigateTo({
				url: '/pagesB/shezhen/camera',
				success: function() {
					console.log('[2024-12-28 02:13:52] 成功跳转到拍照页面');
				},
				fail: function() {
					console.log('[2024-12-28 02:13:53] 跳转到拍照页面失败');
					uni.showToast({
						title: '页面跳转失败',
						icon: 'error'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.guide-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
	box-sizing: border-box;
}

/* 顶部标题区域样式 */
.header-section {
	text-align: center;
	padding: 60rpx 0 40rpx;
	color: white;
}

.header-image {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 20rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	padding: 20rpx;
}

.header-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.header-subtitle {
	display: block;
	font-size: 28rpx;
	opacity: 0.9;
}

/* 功能介绍区域样式 */
.intro-section {
	margin-bottom: 40rpx;
}

.intro-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.card-icon {
	font-size: 36rpx;
	margin-right: 15rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.card-content {
	font-size: 28rpx;
	line-height: 1.6;
	color: #666;
}

/* 使用指南区域样式 */
.guide-section {
	margin-bottom: 40rpx;
}

.section-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	margin-bottom: 20rpx;
	text-align: center;
}

.guide-steps {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.step-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 25rpx;
}

.step-item:last-child {
	margin-bottom: 0;
}

.step-number {
	width: 50rpx;
	height: 50rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: bold;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.step-content {
	flex: 1;
}

.step-title {
	display: block;
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.step-desc {
	display: block;
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

/* 注意事项区域样式 */
.notice-section {
	margin-bottom: 40rpx;
}

.notice-list {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.notice-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.notice-item:last-child {
	margin-bottom: 0;
}

.notice-icon {
	font-size: 28rpx;
	margin-right: 15rpx;
	flex-shrink: 0;
}

.notice-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	flex: 1;
}

/* 底部操作区域样式 */
.action-section {
	text-align: center;
	padding-bottom: 40rpx;
}

.start-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #ff6b6b, #ee5a52);
	border-radius: 50rpx;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.3);
}

.start-btn::after {
	border: none;
}

.btn-text {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
}

.disclaimer {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	line-height: 1.4;
}
</style>