<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

namespace app\controller;
use think\facade\Db;
class ApiTuanzhang extends ApiCommon{
    public function initialize(){
		parent::initialize();
		$bset = Db::name('tuanzhang_sysset')->where('aid',aid)->find();
		if($bset['status'] == 0){
			die(jsonEncode(['status'=>-4, 'msg' => '功能未开启', 'url'=>'/pages/index/index']));
		}
	}
	//商家详情页
	public function index($bid=0){
		$bid = $bid>0 ? $bid : input('param.id/d');
		$tuanzhang = Db::name('tuanzhang')->where('aid',aid)->where('id',$bid)->where('status',1)->find();
		if(!$tuanzhang) return $this->json(['status'=>0,'msg'=>'商家信息不存在']);

		if($tuanzhang['is_open']==0) return $this->json(['status'=>-4,'msg'=>'商家未营业']);
		$is_open = 0;
		if($is_open==0){
			if($tuanzhang['start_hours'] != $tuanzhang['end_hours']){
				$start_time = strtotime(date('Y-m-d '.$tuanzhang['start_hours']));
				$end_time = strtotime(date('Y-m-d '.$tuanzhang['end_hours']));
				if(($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))){
					//return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
				}else{
					$is_open = 1;
				}
			}else{
				$is_open = 1;
			}
		}
		if($is_open==0){
			$start_time = strtotime(date('Y-m-d '.$tuanzhang['start_hours2']));
			$end_time = strtotime(date('Y-m-d '.$tuanzhang['end_hours2']));
			if($start_time == $end_time || ($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))){
				//return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
			}else{
				$is_open = 1;
			}
		}
		if($is_open==0){
			$start_time = strtotime(date('Y-m-d '.$tuanzhang['start_hours3']));
			$end_time = strtotime(date('Y-m-d '.$tuanzhang['end_hours3']));
			if($start_time == $end_time || ($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))){
				//return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
			}else{
				$is_open = 1;
			}
		}
		if($is_open == 0){
			return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
		}
		$countcomment = Db::name('tuanzhang_comment')->where('aid',aid)->where('bid',$bid)->where('status',1)->count();
		$couponcount= Db::name('coupon')->where('aid',aid)->where('bid',$bid)->where('tolist',1)->order('sort desc,id desc')->count();
		$bset = Db::name('tuanzhang_sysset')->where('aid',aid)->find();
		
		$prosales = Db::name('shop_product')->where('bid',$bid)->sum('sales');
		if($tuanzhang['sales'] < $prosales) $tuanzhang['sales'] = $prosales;

		$pagedata = Db::name('designerpage')->where('aid',aid)->where('bid',$bid)->where('ishome',1)->find();
		if(!$pagedata){
			$rdata = [];
			$rdata['isdiy'] = 0;
			$rdata['bset'] = $bset;
			$rdata['business'] = $tuanzhang;
			$rdata['countcomment'] = $countcomment;
			$rdata['couponcount'] = $couponcount;
			$rdata['pics'] = explode(',',$tuanzhang['pics']);
			$rdata['showfw'] = getcustom('tuanzhangindex_showfw');
			return $this->json($rdata);
		}

		$pageinfo = json_decode($pagedata['pageinfo'],true);
		$pagecontent = json_decode(\app\common\System::initpagecontent($pagedata['content'],aid,mid,platform),true);
		$pageparams = $pageinfo[0]['params'];
		
		
		$guanggaopic = '';
		$guanggaourl = '';
		if($pageparams['showgg']==1){
			$showgg = 0;
			if($pageparams['ggrenqun']){
				if($pageparams['ggrenqun']['0']){
					$showgg = 1;
				}
				if($pageparams['ggrenqun']['-1'] && $this->member['subscribe']==1){
					$showgg = 1;
				}
				if($pageparams['ggrenqun']['-2'] && $this->member['subscribe']!=1){
					$showgg = 1;
				}
				if($showgg==0 && $pageparams['ggrenqun'][$this->member['levelid']]){
					$showgg = 1;
				}
			}
			if($showgg == 1 && $pageparams['cishu']==0 && $this->member){
				$hasshowlog = Db::name('guanggao_showlog')->where('mid',mid)->where('pic',$pageparams['guanggao'])->find();
				if($hasshowlog){
					$showgg = 0;
				}else{
					Db::name('guanggao_showlog')->insert(['aid'=>aid,'mid'=>mid,'pic'=>$pageparams['guanggao'],'createtime'=>time()]);
				}
			}
			if($showgg){
				$guanggaopic = $pageparams['guanggao'];
				$guanggaourl = $pageparams['hrefurl'];
			}
		}
        $admin_set = Db::name('admin_set')->field('name,logo,desc,tel,gzts,ddbb,mode')->where('aid',aid)->find();

        $sysset = ['name'=>$tuanzhang['name'],'logo'=>$tuanzhang['logo'],'desc'=>$tuanzhang['address'],'tel'=>$tuanzhang['tel'],'mode'=>$admin_set['mode'],'address' => $tuanzhang['address']];
		
		if(!$pageparams['bgcolor']){
			$pageparams['bgcolor'] = '#f7f7f8';
		}

        $rdata = [];
		$rdata['status'] = 1;
		$rdata['isdiy'] = 1;
		$rdata['bset'] = $bset;
		$rdata['guanggaopic'] = $guanggaopic;
		$rdata['guanggaourl'] = $guanggaourl;
		$rdata['pageinfo'] = $pageparams;
		$rdata['pagecontent'] = $pagecontent;
		$rdata['business'] = $tuanzhang;
		$rdata['sysset'] = $sysset;
        return $this->json($rdata);
	}

	public function main(){
		$pageid = input('param.id/d');
		$pagedata = Db::name('designerpage')->where('aid',aid)->where('id',$pageid)->find();
		if(!$pagedata){
			return $this->json(['status'=>0,'msg'=>'页面不存在']);
		}
		$bid = $pagedata['bid'];

		$tuanzhang = Db::name('tuanzhang')->where('aid',aid)->where('id',$bid)->where('status',1)->find();
		$pageinfo = json_decode($pagedata['pageinfo'],true);
		$pagecontent = json_decode(\app\common\System::initpagecontent($pagedata['content'],aid,mid,platform),true);
		$pageparams = $pageinfo[0]['params'];
		if($pageparams['quanxian']){
			if(!$pageparams['quanxian']['0'] && !$pageparams['quanxian'][$this->member['levelid']]){
				return $this->json(['status'=>0,'msg'=>'您无查看权限']);
			}
		}
		if($pageparams['fufei']==1 && floatval($pageparams['money'])>0){//付费查看
			$hasff = Db::name('designerpage_order')->where('aid',aid)->where('pageid',$pagedata['id'])->where('mid',mid)->where('status',1)->find();
			if(!$hasff){
				$adata = array();
				$adata['aid'] = aid;
				$adata['pageid'] = $pagedata['id'];
				$adata['mid'] = mid;
				$adata['title'] = $pageparams['title'];
				$adata['price'] = floatval($pageparams['money']);
				$adata['ordernum'] = date('ymdHis').aid.rand(1000,9999);
				$adata['createtime'] = time();
				$orderid = Db::name('designerpage_order')->insertGetId($adata);
				return $this->json(['status'=>2,'msg'=>'需要付费查看','orderid'=>$orderid]);
			}
		}
		
		$guanggaopic = '';
		$guanggaourl = '';
		if($pageparams['showgg']==1){
			$showgg = 0;
			if($pageparams['ggrenqun']){
				if($pageparams['ggrenqun']['0']){
					$showgg = 1;
				}
				if($pageparams['ggrenqun']['-1'] && $this->member['subscribe']==1){
					$showgg = 1;
				}
				if($pageparams['ggrenqun']['-2'] && $this->member['subscribe']!=1){
					$showgg = 1;
				}
				if($showgg==0 && $pageparams['ggrenqun'][$this->member['levelid']]){
					$showgg = 1;
				}
			}
			if($showgg == 1 && $pageparams['cishu']==0 && $this->member){
				$hasshowlog = Db::name('guanggao_showlog')->where('mid',mid)->where('pic',$pageparams['guanggao'])->find();
				if($hasshowlog){
					$showgg = 0;
				}else{
					Db::name('guanggao_showlog')->insert(['aid'=>aid,'mid'=>mid,'pic'=>$pageparams['guanggao'],'createtime'=>time()]);
				}
			}
			if($showgg){
				$guanggaopic = $pageparams['guanggao'];
				$guanggaourl = $pageparams['hrefurl'];
			}
		}
		$sysset = ['name'=>$tuanzhang['name'],'logo'=>$tuanzhang['pic'],'desc'=>$tuanzhang['address'],'tel'=>$tuanzhang['tel']];
		
		if(!$pageparams['bgcolor']){
			$pageparams['bgcolor'] = '#f7f7f8';
		}
		$rdata = [];
		$rdata['status'] = 1;
		$rdata['isdiy'] = 1;
		$rdata['guanggaopic'] = $guanggaopic;
		$rdata['guanggaourl'] = $guanggaourl;
		$rdata['pageinfo'] = $pageparams;
		$rdata['pagecontent'] = $pagecontent;
		//dump($pagecontent);die;
		$rdata['sysset'] = $sysset;
		return $this->json($rdata);
	}
	//获取商品列表 评价列表
public function getdatalist() {
    $id = input('param.id/d'); // 团长ID
    $st = input('param.st/d');
    $pagenum = input('param.pagenum');
    if (!$pagenum) $pagenum = 1;
    if ($st == 0) { // 商品列表
        $pernum = 20;
        $bid = $id; // 团长ID

        $where = [];
        $where[] = ['t.bid', '=', $bid];
        $where[] = ['p.aid', '=', aid]; // 注意这里的aid是否需要从输入参数获取或有其他来源
        // 过滤商品状态
        $nowtime = time();
        $nowhm = date('H:i');
        $statusCondition = "(p.status = 1)
            OR (p.status = 2 AND unix_timestamp(p.start_time) <= $nowtime AND unix_timestamp(p.end_time) >= $nowtime)
            OR (p.status = 3 AND (
                (p.start_hours < p.end_hours AND p.start_hours <= '$nowhm' AND p.end_hours >= '$nowhm')
                OR (p.start_hours >= p.end_hours AND (p.start_hours <= '$nowhm' OR p.end_hours >= '$nowhm'))
            ))";
        $where[] = Db::raw($statusCondition);

        // 使用正确的关联列名 t.proid = p.id
        $prolist = Db::name('tuanzhang_guige')
            ->alias('t')
            ->join('shop_product p', 't.proid = p.id')
            ->where($where)
            ->field([
                'p.id',
                'p.name',
                'p.pic',
                'p.market_price',
                'p.cost_price',
                'p.sell_price',
                'p.weight',
                'p.stock',
                'p.procode',
                'p.sales',
                'p.lvprice_data',
                'p.givescore',
                'p.givescorehuang',
                'p.givescoregongxianzhi',
                'p.limit_start',
                'p.barcode',
                'MIN(t.sell_price) as tuanzhang_sell_price'
            ])
            ->group('p.id')
            ->page($pagenum, $pernum)
            ->order('p.id desc')
            ->select()
            ->toArray();

        if ($prolist) {
            foreach ($prolist as &$item) {
                $item['bid'] = $bid; // 为每个商品列表项添加bid
            }
        }

        if (request()->isPost()) {
            return $this->json(['status' => 1, 'data' => $prolist]);
        }
    }
}
	//商家优惠券
	public function couponlist(){
		//商家优惠券
		$couponlist= Db::name('coupon')->where('aid',aid)->where('bid',$id)->where("unix_timestamp(starttime)<=".time()." and unix_timestamp(endtime)>=".time())->order('sort desc,id desc')->select()->toArray();
		if(!$couponlist) $couponlist = [];
		foreach($couponlist as $k=>$v){
			$haveget = Db::name('coupon_record')->where('aid',aid)->where('mid',mid)->where('couponid',$v['id'])->count();
			$couponlist[$k]['haveget'] = $haveget;
			$couponlist[$k]['starttime'] = date('m-d H:i',strtotime($v['starttime']));
			$couponlist[$k]['endtime'] = date('m-d H:i',strtotime($v['endtime']));
		}
		$rdata = [];
		$rdata['couponlist'] = $couponlist;
		return $this->json($rdata);
	}
	//分类商家
	public function clist(){
		$clist = Db::name('tuanzhang_category')->where('aid',aid)->where('status',1)->order('sort desc,id')->select()->toArray();
		if(request()->isPost()){
			$cid = input('param.cid/d');
			$where = [];
			$where[] = ['aid','=',aid];
			$where[] = ['status','=',1];
			//分类 
			if($cid){
				$where[] = Db::raw('find_in_set('.$cid.',cid)'); //['cid','=',$cid];
			}else{
				//$where[] = Db::raw('find_in_set('.$clist[0]['id'].',cid)'); // ['cid','=',$clist[0]['id']];
			}
			if(input('param.keyword')){
				$where[] = ['name','like','%'.input('param.keyword').'%'];
			}
			$nowhm = date('H:i');
			$where[] = Db::raw("(start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm')) or (start_hours2<end_hours2 and start_hours2<='$nowhm' and end_hours2>='$nowhm') or (start_hours2>end_hours2 and (start_hours2<='$nowhm' or end_hours2>='$nowhm')) or (start_hours3<end_hours3 and start_hours3<='$nowhm' and end_hours3>='$nowhm') or (start_hours3>end_hours3 and (start_hours3<='$nowhm' or end_hours3>='$nowhm'))");

			$pernum = 12;
			$pagenum = input('post.pagenum');
			if(!$pagenum) $pagenum = 1;

			$longitude = input('post.longitude');
			$latitude = input('post.latitude');
			if($longitude && $latitude){
				$orderBy = Db::raw("({$longitude}-longitude)*({$longitude}-longitude) + ({$latitude}-latitude)*({$latitude}-latitude) ");
			}else{
				$orderBy = 'sort desc,id';
			}
			$datalist = Db::name('tuanzhang')->field("id,name,logo,desc,tel,address,longitude,latitude")->where($where)->order($orderBy)->page($pagenum,$pernum)->select()->toArray();
			if(!$datalist) $datalist = array();
			
			if($this->member){
				$memberlevel = Db::name('member_level')->where('id',$this->member['levelid'])->find();
			}else{
				$memberlevel = [];
			}
			foreach($datalist as $k=>$v){
				if($longitude && $latitude){
					$datalist[$k]['juli'] = (getdistance($longitude,$latitude,$v['longitude'],$v['latitude'],2)).'km';
				}else{
					$datalist[$k]['juli'] = '';
				}
				if($memberlevel && $memberlevel['notshowbtel']==1){
					$datalist[$k]['tel'] = Db::name('admin_set')->where('aid',aid)->value('tel');
				}
			}
			return $this->json(['status'=>1,'data'=>$datalist]);
		}
		$rdata = [];
		$rdata['clist'] = $clist;
		return $this->json($rdata);
	}
	//商家列表
	public function blist(){
		if(request()->isPost()){
			$pernum = 10;
			$pagenum = input('post.pagenum/d');
			if(!$pagenum) $pagenum = 1;
			$cid = input('post.cid/d');
			$where = [];
			$where[] = ['aid','=',aid];
			$where[] = ['status','=',1];
			$where[] = ['is_open','=',1];
			if($cid) $where[] = Db::raw('find_in_set('.$cid.',cid)'); // ['cid','=',$cid];
			if(input('param.keyword')){
				$where[] = ['name','like','%'.input('param.keyword').'%'];
			}
			
		
			$nowhm = date('H:i');
			$where[] = Db::raw("(start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm')) or (start_hours2<end_hours2 and start_hours2<='$nowhm' and end_hours2>='$nowhm') or (start_hours2>end_hours2 and (start_hours2<='$nowhm' or end_hours2>='$nowhm')) or (start_hours3<end_hours3 and start_hours3<='$nowhm' and end_hours3>='$nowhm') or (start_hours3>end_hours3 and (start_hours3<='$nowhm' or end_hours3>='$nowhm'))");

			$latitude = input('param.latitude');
			$longitude = input('param.longitude');
			if($longitude && $latitude){
				$order = Db::raw("({$longitude}-longitude)*({$longitude}-longitude) + ({$latitude}-latitude)*({$latitude}-latitude) ");
			}else{
				$order = 'sort desc,id desc';
			}
			$field = input('param.field');
			if($field && $field!='juli'){
				$order = $field.' '.input('param.order').',id desc';
			}
			$datalist = Db::name('tuanzhang')->where($where)->field('id,logo,name,tel,sales,address,latitude,longitude,comment_score')->page($pagenum,$pernum)->order($order)->select()->toArray();
			
			
			 
			$nowtime = time();
			$nowhm = date('H:i');
			if(!$datalist) $datalist = array();
			
			
			foreach($datalist as $k=>$v){
				$statuswhere = "`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )";
				
				if(false){}else{
					$prolist = Db::name('shop_product')->where('bid',$v['id'])->where($statuswhere)->field('id,pic,name,sales,market_price,sell_price')->limit(4)->order('sort desc,id desc')->select()->toArray();
				}
				
				if(!$prolist) $prolist = array();
				$v['prolist'] = $prolist;
                if(getcustom('restaurant')) {
                    $restaurantProlist = Db::name('restaurant_product')->where('bid',$v['id'])->where($statuswhere)->field('id,pic,name,sales,market_price,sell_price')->limit(4)->order('sort desc,id desc')->select()->toArray();
                    if(!$restaurantProlist) $restaurantProlist = array();
                    $v['restaurantProlist'] = $restaurantProlist;
                }
				if($longitude && $latitude){
					$v['juli'] = ''.getdistance($longitude,$latitude,$v['longitude'],$v['latitude'],2).'km';
				}else{
					$v['juli'] = '';
				}
				$prosales = Db::name('shop_product')->where('bid',$v['id'])->sum('sales');
				if($v['sales'] < $prosales) $v['sales'] = $prosales;
			
			
				$v['totalmoney'] = $m_b_info['totalmoney'];
				$datalist[$k] = $v;
			}
			return $this->json(['status'=>1,'data'=>$datalist]);
		}
		//分类
		$clist = Db::name('tuanzhang_category')->where('aid',aid)->where('status',1)->field('id,name,pic')->order('sort desc,id')->select()->toArray();
		
		$rdata = [];
		$rdata['clist'] = $clist;
		$rdata['showtype'] = 0;
		return $this->json($rdata);
	}
	//我的商家列表
	public function mylist(){
	    $adminset = Db::name('admin_set')->where('aid',aid)->field('yihuo_status,yihuo_baifenbi')->find();
	    if($adminset['yihuo_status'] != 1)
	    {
	         return $this->json(['status'=>0,'msg'=>'易货未开启!']);
	    }
	    $tuozhanidArr = Db::name('member_tuozhan')->where('aid',aid)->where('mid',mid)->find();
	    if(empty($tuozhanidArr) ||$tuozhanidArr['yihuo_istuozhan'] != 1 )
	    {
	        return $this->json(['status'=>0,'msg'=>'该用户不是拓展员!']);
	    }
	    //分类
		$clist = Db::name('tuanzhang_category')->where('aid',aid)->where('status',1)->field('id,name,pic')->order('sort desc,id')->select()->toArray();
	    
// 		if(request()->isPost()){
			$pernum = 10;
			$pagenum = input('post.pagenum/d');
			if(!$pagenum) $pagenum = 1;
			$cid = input('post.cid/d');
			$where = [];
			$where[] = ['aid','=',aid];
			$where[] = ['status','=',1];
// 			$mingxiid = input('param.mingxiid');
//     		if($mingxiid)
//     		{
//     		    $where[] = ['tuozhanid','=',$mingxiid];
//     		}else{
    		   $where[] = ['tuozhanid','=',mid];
    // 		}
// 			if()
			
// 			$where[] = ['is_open','=',1];
			if($cid) $where[] = Db::raw('find_in_set('.$cid.',cid)'); // ['cid','=',$cid];
			if(input('param.keyword')){
				$where[] = ['name','like','%'.input('param.keyword').'%'];
			}
			$nowhm = date('H:i');
			$where[] = Db::raw("(start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm')) or (start_hours2<end_hours2 and start_hours2<='$nowhm' and end_hours2>='$nowhm') or (start_hours2>end_hours2 and (start_hours2<='$nowhm' or end_hours2>='$nowhm')) or (start_hours3<end_hours3 and start_hours3<='$nowhm' and end_hours3>='$nowhm') or (start_hours3>end_hours3 and (start_hours3<='$nowhm' or end_hours3>='$nowhm'))");

			$latitude = input('param.latitude');
			$longitude = input('param.longitude');
			if($longitude && $latitude){
				$order = Db::raw("({$longitude}-longitude)*({$longitude}-longitude) + ({$latitude}-latitude)*({$latitude}-latitude) ");
			}else{
				$order = 'sort desc,id desc';
			}
			$field = input('param.field');
			if($field && $field!='juli'){
				$order = $field.' '.input('param.order').',id desc';
			}
			$datalist = Db::name('tuanzhang')->where($where)->field('id,logo,name,tel,sales,address,latitude,longitude,comment_score,yihuo_baifenbi')->page($pagenum,$pernum)->order($order)->select()->toArray();
			$nowtime = time();
			$nowhm = date('H:i');
			if(!$datalist) $datalist = array();
			foreach($datalist as $k=>$v){
				if($longitude && $latitude){
					$v['juli'] = ''.getdistance($longitude,$latitude,$v['longitude'],$v['latitude'],2).'km';
				}else{
					$v['juli'] = '';
				}
				$prosales = Db::name('shop_product')->where('bid',$v['id'])->sum('sales');
				if($v['sales'] < $prosales) $v['sales'] = $prosales;
				$datalist[$k] = $v;
				//抽佣比例
				if($v['yihuo_baifenbi'] == 0)
				{
				    $datalist[$k]['yihuo_baifenbi'] = $adminset['yihuo_baifenbi'];
				}
				//商家总流水
				$zongliushui = Db::name('shop_order')->where('aid',aid)->where('bid',$v['id'])->where('status','in','1,2,3')->sum('totalprice');
				$zongliushui2 = Db::name('maidan_order')->where('aid',aid)->where('bid',$v['id'])->where('status',1)->sum('money');
				$datalist[$k]['zongliushui'] = $zongliushui+$zongliushui2;
				//今日流水
				$start = strtotime(date('Y-m-d 00:00:00'));
				$end = strtotime(date('Y-m-d 23:59:59'));
				$jinday = Db::name('shop_order')->where('aid',aid)->where('bid',$v['id'])->where('status','in','1,2,3')
				->where('paytime','>=',$start)->where('paytime','<=',$end)->sum('totalprice');
				$jinday2 = Db::name('maidan_order')->where('aid',aid)->where('bid',$v['id'])->where('status',1)
				->where('paytime','>=',$start)->where('paytime','<=',$end)->sum('money');
				$datalist[$k]['jinday'] = $jinday+$jinday2;
				//昨日流水
				$start = strtotime(date('Y-m-d 00:00:00',strtotime('-1 days')));
				$end = strtotime(date('Y-m-d 23:59:59',strtotime('-1 days')));
				$zuoday = Db::name('shop_order')->where('aid',aid)->where('bid',$v['id'])->where('status','in','1,2,3')
				->where('paytime','>=',$start)->where('paytime','<=',$end)->sum('totalprice');
				$zuoday2 = Db::name('maidan_order')->where('aid',aid)->where('bid',$v['id'])->where('status',1)
				->where('paytime','>=',$start)->where('paytime','<=',$end)->sum('money');
				$datalist[$k]['zuoday'] = $zuoday+$zuoday2;
				//本月流水
				$start = strtotime(date('Y-m-01 00:00:00'));
				$end = strtotime(date('Y-m-31 23:59:59'));
				$jinmonth = Db::name('shop_order')->where('aid',aid)->where('bid',$v['id'])->where('status','in','1,2,3')
				->where('paytime','>=',$start)->where('paytime','<=',$end)->sum('totalprice');
				$jinmonth2 = Db::name('maidan_order')->where('aid',aid)->where('bid',$v['id'])->where('status',1)
				->where('paytime','>=',$start)->where('paytime','<=',$end)->sum('money');
				$datalist[$k]['jinmonth'] = $jinmonth+$jinmonth2;
				//抽佣数
				$chouyong = Db::name('shop_order_goods')->where('aid',aid)->where('bid',$v['id'])->where('tuozhanfei_status',1)->sum('tuozhanfei');
				$chouyong2 = Db::name('maidan_order')->where('aid',aid)->where('bid',$v['id'])->where('tuozhanfei_status',1)->sum('tuozhanfei');
				$datalist[$k]['chouyong'] = $chouyong+$chouyong2;
			}
			return $this->json(['status'=>1,'data'=>$datalist,'clist'=>$clist,'showtype'=>0]);
// 		}
		
		$rdata = [];
		return $this->json($rdata);
	}
	
    //商家列表
    public function blist2(){
            $pernum = 15;
            $pagenum = input('post.pagenum/d');
            if(!$pagenum) $pagenum = 1;
            $cid = input('post.cid/d');
            $where = [];
            $where[] = ['aid','=',aid];
            $where[] = ['status','=',1];
            $where[] = ['is_open','=',1];
            if($cid) $where[] = Db::raw('find_in_set('.$cid.',cid)'); // ['cid','=',$cid];
            if(input('param.keyword')){
                $where[] = ['name','like','%'.input('param.keyword').'%'];
            }
            
            //区域限制
        			$area_set = Db::name('admin_set')->where('aid',aid)->field('area_on,areamode')->find();
        			$area_id = input('param.area_id/d');
        			if($area_set && $area_set['area_on'] && $area_id){
        			    $permission = Db::name('admin_set_area_permission')->where(['a_area'=>md5(aid.$area_id)])->value('permission');
        			    //var_dump($permission);
        			    if($permission){
            			    $permission = json_decode($permission,true);
            			    if($area_set['areamode']==0)$limit_field = 'province_id';
            			    if($area_set['areamode']==1)$limit_field = 'city_id';
            			    if($area_set['areamode']==2)$limit_field = 'district_id';
            			    if($permission['client_visible_area']==0){
            			        //仅可见该区域
            			        $where[] = [$limit_field,'=',$area_id];
            			    }else if($permission['client_visible_area']==1){
            			        //可见部分区域
            			        $where[] = [$limit_field,'in',explode(',',$permission['client_visible_areas'])];
            			    }else if($permission['client_visible_area']==2){
            			        //不可���部分区域
            			        $where[] = [$limit_field,'notin',explode(',',$permission['client_unvisible_areas'])];
            			    } 
        			    }
        			}
            
            
            
            
            
            $nowhm = date('H:i');
            $where[] = Db::raw("(start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm')) or (start_hours2<end_hours2 and start_hours2<='$nowhm' and end_hours2>='$nowhm') or (start_hours2>end_hours2 and (start_hours2<='$nowhm' or end_hours2>='$nowhm')) or (start_hours3<end_hours3 and start_hours3<='$nowhm' and end_hours3>='$nowhm') or (start_hours3>end_hours3 and (start_hours3<='$nowhm' or end_hours3>='$nowhm'))");

            $latitude = input('param.latitude');
            $longitude = input('param.longitude');
            if($longitude && $latitude){
                $order = Db::raw("({$longitude}-longitude)*({$longitude}-longitude) + ({$latitude}-latitude)*({$latitude}-latitude) ");
            }else{
                $order = 'sort desc,id desc';
            }
            $field = input('param.field');
            if($field && $field!='juli'){
                $order = $field.' '.input('param.order').',id desc';
            }
            $datalist = Db::name('tuanzhang')->where($where)->field('id,logo,name,sales,address,latitude,longitude,comment_score,tel')->page($pagenum,$pernum)->order($order)->select()->toArray();
            $nowtime = time();
            $nowhm = date('H:i');
            if(!$datalist) $datalist = array();
            foreach($datalist as $k=>$v){
                $statuswhere = "`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )";
//                $prolist = Db::name('shop_product')->where('bid',$v['id'])->where($statuswhere)->field('id,pic,name,sales,market_price,sell_price')->limit(4)->order('sort desc,id desc')->select()->toArray();
                if(!$prolist) $prolist = array();
                $v['prolist'] = $prolist;
                if(getcustom('restaurant')) {
//                    $restaurantProlist = Db::name('restaurant_product')->where('bid',$v['id'])->where($statuswhere)->field('id,pic,name,sales,market_price,sell_price')->limit(4)->order('sort desc,id desc')->select()->toArray();
                    if(!$restaurantProlist) $restaurantProlist = array();
                    $v['restaurantProlist'] = $restaurantProlist;
                }
                if($longitude && $latitude){
                    $v['juli'] = ''.getdistance($longitude,$latitude,$v['longitude'],$v['latitude'],2).'km';
                }else{
                    $v['juli'] = '';
                }
//                $prosales = Db::name('shop_product')->where('bid',$v['id'])->sum('sales');
//                if($v['sales'] < $prosales) $v['sales'] = $prosales;
                $datalist[$k] = $v;
            }
            return $this->json(['status'=>1,'data'=>$datalist]);
    }
	//入驻申请
	public function apply(){
		$this->checklogin();
		if(request()->isPost()){
			$formdata = input('post.info/a');
				  $bset1 = Db::name('tuanzhang_sysset')->where('aid', aid)->find();
			$hasun = Db::name('admin_user')->where('id','<>',$formdata['id'])->where('un',$formdata['un'])->find();
			if($hasun){
				return $this->json(['status'=>0,'msg'=>'该账号已存在']);
			}
			if($formdata['tuozhanid'] >0){
    			$tuozhanidArr = Db::name('member_tuozhan')->where('aid',aid)->where('mid',$formdata['tuozhanid'])->field('yihuo_istuozhan,cateid')->find();
    			if(empty($tuozhanidArr))
    			{
    			    return $this->json(['status'=>0,'msg'=>'该拓展员未开启拓展']);
    			}
    			$curmember_cate = Db::name('yihuo_category')->where('aid',aid)->where('id',$tuozhanidArr['cateid'])->find();
    			if(empty($curmember_cate))
        	    {
        	         return $this->json(['status'=>0,'msg'=>'未找到拓展员分类']);
        	    }
        	    $xiaohao = json_decode($curmember_cate['xiaohao'],1);
        	    if(!empty($xiaohao))
        	    {
        	         if(isset($xiaohao[0]) && $xiaohao[0] >0)
        	         {
        	             $curmember_y = Db::name('member')->where('aid',aid)->where('id',$formdata['tuozhanid'])->field('tuozhanedu')->find();
        	             if($curmember_y['tuozhanedu'] < $xiaohao[0])
        	             {
        	                 return $this->json(['status'=>0,'msg'=>'该拓展员开设额度不足']);
        	             }
        	         }
        	    }
        	    
    			
			}
			$info = [];
			$info['aid'] = aid;
			$info['mid'] = mid;
			$info['mid2'] = mid;
			$info['tuozhanid'] = $formdata['tuozhanid']?$formdata['tuozhanid']:0;
			$info['cid'] = $formdata['cid'];
			$info['name'] = $formdata['name'];
			$info['desc'] = $formdata['desc'];
			$info['linkman'] = $formdata['linkman'];
			$info['linktel'] = $formdata['linktel'];
			$info['tel'] = $formdata['tel'];
			$info['logo'] = $formdata['pic'];
			$info['pics'] = $formdata['pics'];
			$info['content'] = $formdata['content'];
			$info['address'] = $formdata['address'];
			$info['latitude'] = $formdata['latitude'];
			$info['longitude'] = $formdata['longitude'];
			$info['zhengming'] = $formdata['zhengming'];
			$info['status'] = 0;
			$info['createtime'] = time();
			$info['feepercent'] = Db::name('tuanzhang_sysset')->where('aid',aid)->value('default_rate');
			
			$uinfo = [];
			$uinfo['un'] = $formdata['un'];
			$uinfo['pwd'] = $formdata['pwd'];
			if($formdata['id']){
				Db::name('tuanzhang')->where('aid',aid)->where('mid',mid)->where('id',$formdata['id'])->update($info);
				if($uinfo['pwd']!=''){
					$uinfo['pwd'] = md5($uinfo['pwd']);
				}else{
					unset($uinfo['pwd']);
				}
				Db::name('admin_user')->where('aid',aid)->where('bid',$info['id'])->where('id',$uinfo['id'])->update($uinfo);
			}else{
				$bid = Db::name('tuanzhang')->insertGetId($info);
				if(isset($xiaohao[0]) && $xiaohao[0] >0)
    	         {
    	             $curmember_y = Db::name('member')->where('aid',aid)->where('id',$formdata['tuozhanid'])->field('tuozhanedu')->find();
    	             if($curmember_y['tuozhanedu'] < $xiaohao[0])
    	             {
    	                 return $this->json(['status'=>0,'msg'=>'该拓展员开设额度不足']);
    	             }else{
    	                 \app\common\Member::addtuozhanedu(aid,$formdata['tuozhanid'],-$xiaohao[0],'开设商家id:'.$bid.'扣除');
    	             }
    	         }
				$uinfo['aid'] = aid;
				$uinfo['bid'] = $bid;
                $uinfo['mid'] = mid;
				$uinfo['auth_type'] = 1;
				$uinfo['pwd'] = md5($uinfo['pwd']);
				$uinfo['createtime'] = time();
				$uinfo['isadmin'] = 1;
				$uinfo['random_str'] = random(16);
				$id = Db::name('admin_user')->insertGetId($uinfo);
			}
			  return $this->json([
    'status' => 1, // 改为1表示成功
    'msg' => '提交成功,请等待审核，平台将于7个工作日内联系您核实信息，请留意来电',
    'after_register_url' => $bset1['after_register_url'] ?? '' // 添加跳转链接
]);
		}
		$info = Db::name('tuanzhang')->where('aid',aid)->where('mid',mid)->find();
		if($info && $info['status']==1){
			return $this->json(['status'=>2,'msg'=>'您已经是团长，直接进入管理吧']); 
		}
		$clist = Db::name('tuanzhang_category')->where('aid',aid)->where('status',1)->order('sort desc,id')->select()->toArray();
		//var_dump($clist);die;
		$bset = Db::name('tuanzhang_sysset')->where('aid',aid)->field('xieyi_show,xieyi')->find();
	
		$adminset = Db::name('admin_set')->where('aid',aid)->field('yihuo_status')->find();
		$yihuo_status =$adminset['yihuo_status'];
		$info['zhanshi'] = 0;
		if($yihuo_status == 1)
		{
		    $info['zhanshi'] = 1;
		}
		$rdata = [];
        $rdata['title'] = '申请成为团长';
		$rdata['clist'] = $clist;
		$rdata['bset'] = $bset;
		$rdata['info'] = $info ? $info : [];
		return $this->json($rdata);
	}
	
	//商品搜索
	public function search(){
		$bid = input('param.bid/d');
		//分类
		if(input('param.cid')){
			$clist = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('pid',input('param.cid/d'))->where('status',1)->order('sort desc,id')->select()->toArray();
			if(!$clist) $clist = [];
		}else{
			$clist = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('pid',0)->where('status',1)->order('sort desc,id')->select()->toArray();
			if(!$clist) $clist = [];
		}
		//分组
		//$glist = Db::name('shop_group')->where('aid',aid)->where('status',1)->select()->toArray();
		//if(!$glist) $glist = [];

		$productlisttype = cookie('productlisttype');
		if(!$productlisttype) $productlisttype = 'item2';
		
		$rdata = [];
		$rdata['clist'] = $clist;
		$rdata['glist'] = [];
		$rdata['productlisttype'] = $productlisttype;
		return $this->json($rdata);
	}
	//商品列表
	public function prolist(){
		$bid = input('param.bid/d');
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['bid','=',$bid];
		$nowtime = time();
		$nowhm = date('H:i');
		$where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");

		if(input('param.field') && input('param.order')){
			$order = input('param.field').' '.input('param.order').',sort,id desc';
		}else{
			$order = 'sort desc,id desc';
		}
		//分类 
		if(input('param.cid')){
			$cid = input('param.cid/d');
			$where[] = ['bcid','=',$cid];
			//子分类
			$clist = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('pid',$cid)->select()->toArray();
			if($clist){
				$cateArr = [$cid];
				foreach($clist as $c){
					$cateArr[] = $c['id'];
				}
				$where[] = ['bcid','in',$cateArr];
			}
		}
		if(input('param.keyword')){
			$where[] = ['name','like','%'.input('param.keyword').'%'];
		}
		if(input('param.groupid')) $where[] = Db::raw("find_in_set(".intval(input('param.groupid')).",gid)");
		if(input('param.gid')) $where[] = Db::raw("find_in_set(".intval(input('param.gid')).",gid)");
		$pernum = 10;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		$datalist = Db::name('shop_product')->field("id,pic,name,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint,fuwupoint")->where($where)->page($pagenum,$pernum)->order($order)->select()->toArray();
		if(!$datalist) $datalist = array();
		$datalist = $this->formatprolist($datalist);
		if(request()->isPost()){
			return $this->json(['status'=>1,'data'=>$datalist]);
		}
		
		//分类
		if(input('param.cid')){
			$clist = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('pid',input('param.cid/d'))->where('status',1)->order('sort desc,id')->select()->toArray();
			if(!$clist) $clist = [];
		}else{
			$clist = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('pid',0)->where('status',1)->order('sort desc,id')->select()->toArray();
			if(!$clist) $clist = [];
		}

		$productlisttype = cookie('productlisttype');
		if(!$productlisttype) $productlisttype = 'item2';
		
		$rdata = [];
		$rdata['clist'] = $clist;
		$rdata['glist'] = [];
		$rdata['datalist'] = $datalist;
		$rdata['productlisttype'] = $productlisttype;
		
		return $this->json($rdata);
	}
	//分类商品
	public function classify(){
		$order = 'sort desc,id desc';
		if(input('param.field') && input('param.order')){
			$order = input('param.field').' '.input('param.order').',sort,id desc';
		}else{
			$order = 'sort desc,id desc';
		}
		$bid = input('param.bid/d');
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['bid','=',$bid];
		//$where[] = ['status','=',1];
		$nowtime = time();
		$nowhm = date('H:i');
		$where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");

		$cid = input('param.cid');
		
		$clist = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('pid',0)->where('status',1)->order('sort desc,id')->select()->toArray();
		foreach($clist as $k=>$v){
			$rs = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('pid',$v['id'])->where('status',1)->order('sort desc,id')->select()->toArray();
			if(!$rs) $rs = [];
			$clist[$k]['child'] = $rs;
		}
		//if(!$cid) $cid = $clist[0]['id'];
		//分类 
		if($cid){
			$where[] = ['','=',intval($cid)];
			$title = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('id',$where['bcid'])->order('sort desc,id')->value('name');
			//子分类
			$child = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('bid',$bid)->where('pid',$where['bcid'])->select()->toArray();
			if($child){
				$cateArr = [$where['bcid']];
				foreach($child as $c){
					$cateArr[] = $c['id'];
				}
				$where[] = ['bcid','in',$cateArr];
			}
		}
		if(input('param.keyword')){
			$where[] = ['name','like','%'.input('param.keyword').'%'];
		}
		if(input('param.groupid')) $where[] = Db::raw("find_in_set(".intval(input('param.groupid')).",gid)");
		$pernum = 12;
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		$datalist = Db::name('shop_product')->field("pic,id,name,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint,fuwupoint")->where($where)->order($order)->page($pagenum,$pernum)->select()->toArray();
		if(!$datalist) $datalist = array();
		$datalist = $this->formatprolist($datalist);
		if(request()->isPost()){
			return $this->json(['status'=>1,'data'=>$datalist]);
		}

		$rdata = [];
		$rdata['clist'] = $clist;
		$rdata['datalist'] = $datalist;
		return $this->json($rdata);
	}
	//获取子分类
	public function getdownclist(){
		$pid = input('param.id/d');
		$clist = Db::name('tuanzhang_shop_category')->where('aid',aid)->where('pid',$pid)->where('status',1)->order('sort desc,id')->select()->toArray();
		if(!$clist) $clist = [];
		return $this->json(['status'=>1,'data'=>$clist]);
	}
	
	//商品评价
	public function commentlist(){
		$bid = input('param.bid/d');
		$pagenum = input('post.pagenum');
		if(!$pagenum) $pagenum = 1;
		$pernum = 20;
		$where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['bid','=',$bid];
		$where[] = ['status','=',1];
		$datalist = Db::name('tuanzhang_comment')->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
		if(!$datalist) $datalist = [];
		foreach($datalist as $k=>$pl){
			$datalist[$k]['createtime'] = date('Y-m-d H:i',$pl['createtime']);
			if($datalist[$k]['content_pic']) $datalist[$k]['content_pic'] = explode(',',$datalist[$k]['content_pic']);
		}
		
		$tuanzhang = Db::name('tuanzhang')->field('id,name,logo,desc,comment_num,comment_score,comment_haopercent')->where('aid',aid)->where('id',$bid)->where('status',1)->find();
		if(!$tuanzhang) return $this->json(['status'=>0,'msg'=>'商家信息不存在']);

		$rdata = [];
		$rdata['data'] = $datalist;
		$rdata['tuanzhang'] = $tuanzhang;
		return $this->json($rdata);
	}
	
	
	//生成团长post
	public function createposter()
	{
		$this->checklogin();
		$post = input('post.');
		$tid = input('param.tid/d');
		$platform = platform;
		$page = '/pagesB/login/login';
		$scene = 'tuanzhangid_'.$tid;
		//if($platform == 'mp' || $platform == 'h5' || $platform == 'app'){
		//	$page = PRE_URL .'/h5/'.aid.'.html#'. $page;
		//}
		$posterset = Db::name('admin_set_poster')->where('aid',aid)->where('type','tuanzhangteam')->where('platform',$platform)->order('id')->find();

		$posterdata = Db::name('member_poster')->where('aid',aid)->where('mid',mid)->where('scene',$scene)->where('type','tuanzhangteam')->where('posterid',$posterset['id'])->find();
		if(!$posterdata){
			$sysset = Db::name('admin_set')->where('aid',aid)->find();
			$textReplaceArr = [
				'[头像]'=>$this->member['headimg'],
				'[昵称]'=>$this->member['nickname'],
				'[姓名]'=>$this->member['realname'],
				'[手机号]'=>$this->member['mobile'],
				'[商城名称]'=>$sysset['name'],
			];

			$poster = $this->_getposter(aid,$platform,$posterset['content'],$page,$scene,$textReplaceArr);
			$posterdata = [];
			$posterdata['aid'] = aid;
			$posterdata['posterid']=$posterset['id'];
			$posterdata['mid'] = $this->member['id'];
			$posterdata['scene'] = $scene;
			$posterdata['page'] = $page;
			$posterdata['type'] = 'tuanzhangteam';
			$posterdata['poster'] = $poster;
			$posterdata['createtime'] = time();
			Db::name('member_poster')->insert($posterdata);
		}
		return $this->json(['status'=>1,'poster'=>$posterdata['poster']]);
	}
	
	//我的会员列表
	public function mytuanyuan()
	{
	    $this->checklogin();
	    $tid = input('param.tid/d');
		$midArr = Db::name('shop_order_goods')->where('aid',aid)->field('mid,tid')->where('tid',$tid)->where('status','in','1,2,3')->group('mid')->select()->toArray();
		$midArr = array_column($midArr,'mid');
		$pernum = 20;
		$pagenum = input('param.pagenum');
		if(!$pagenum) $pagenum = 1;

		$where = [];
		$where[] = ['aid','=',aid];
	
		$datalist = Db::name('member')->field('id,nickname,headimg,tel')->where($where)->page($pagenum,$pernum)->where('id','in',$midArr)->order('id desc')->select()->toArray();
		if(!$datalist) $datalist = [];
		
		return $this->json(['status'=>1,'data'=>$datalist]);
	}
	
	//我的订单
	public function mytuanorder()
	{
	    $this->checklogin();
	    $st = input('param.st');
		if(!input('?param.st') || $st === ''){
			$st = 'all';
		}
	    $tid = input('param.tid/d');
	    $pernum = 20;
	    $pagenum = input('param.pagenum');
	    if(!$pagenum) $pagenum = 1;
	    $where = [];
		$where[] = ['aid','=',aid];
		
		if(input('param.keyword')) $where[] = ['ordernum|name', 'like', '%'.input('param.keyword').'%'];
		if($st == 'all'){
			
		}elseif($st == '0'){
			$where[] = ['status','=',0];
		}elseif($st == '1'){
			$where[] = ['status','=',1];
		}elseif($st == '2'){
			$where[] = ['status','=',2];
		}elseif($st == '3'){
			$where[] = ['status','=',3];
		}elseif($st == '10'){
			$where[] = ['status','in','4,5'];
		}
		$where[] = ['tid','=',$tid];
	    $datalist = Db::name('shop_order_goods')->field('id,orderid,ordernum,mid,tid,daiticheng,pic,status,dhtid,totalprice,t_status')->where($where)->page($pagenum,$pernum)->order('id desc')->select()->toArray();
	    foreach($datalist as $k=>$v)
	    {
	        $member = Db::name('member')->field('id,nickname,headimg,tel')->where('aid',aid)->where('id',$v['mid'])->find();
	        $datalist[$k]['nickname'] = $member['nickname'];
	    }
		if(!$datalist) $datalist = [];
		
		return $this->json(['status'=>1,'data'=>$datalist]);
	}
	
	//带货团分类
	public function daihuotuancate()
	{
	    $this->checklogin();
	    if(empty(input('post.tid/d')))
	    {
	       return $this->json(['status'=>0,'msg'=>'请传入团长id']);  
	    }
	    $where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['bid','=',bid];
		$list = Db::name('daihuoyiuan_category')->field('id,name')->where($where)->where('status',1)->order('id desc')->select()->toArray();
		return $this->json(['status'=>1,'data'=>$list]);
	}
	//获取带货团商品分类
	public function  daihuotuanpro()
	{
	    $this->checklogin();
	    if(empty(input('param.tid/d')))
	    {
	       return $this->json(['status'=>0,'msg'=>'请传入团长id']);  
	    }
	    $where = [];
		$where[] = ['aid','=',aid];
		$where[] = ['bid','=',bid];
		$list = Db::name('shop_product')->field('id,name')->where($where)->where('status',1)->order('id desc')->select()->toArray();
		return $this->json(['status'=>1,'data'=>$list]);
	    
	}
	//我的带货团
public function adddaihuo()
{
    // 检查用户是否登录
    $this->checklogin();

    // 获取传递过来的ID（团长id）
    $tid = input('post.id/d');

    // 获取传递过来的 bid，如果没有则默认为 22
    $bid = input('post.bid/d', 22);

   
    // 根据ID查询对应的 daihuotuan 笔记数据
    $daihuotuanData = Db::name('daihuoyiuan')->where('id', $tid)->find();
    
    // 如果查询不到数据，返回错误信息
    if (!$daihuotuanData) {
        return $this->json(['status' => 0, 'msg' => '未找到对应的笔记']);
    }

    // 构建插入数据
    $insertdata = [
        'aid' => aid,
        'cid' => input('post.cid/d', $daihuotuanData['cid']),
        'name' => input('post.name', $daihuotuanData['name']),
        'subname' => input('post.subname', $daihuotuanData['subname']),
        'pic' => input('post.pic', $daihuotuanData['pic']),
        'author' => input('post.author', $daihuotuanData['author']),
        'content' => input('post.content', $daihuotuanData['content']),
        'tuanzhang' => $bid, // 同步团长id和bid
        'bid' => $bid, // 同样使用 bid 的值
        'createtime' => time(),
        'productids' => input('post.proids', $daihuotuanData['productids']),
    ];

    // 插入数据到 tuanzhangdaihuotuan 表
    $bool = Db::name('tuanzhangdaihuotuan')->insert($insertdata);

    // 判断插入结果并返回相应的JSON响应
    if ($bool) {
        return $this->json(['status' => 1, 'msg' => '添加成功']);
    } else {
        return $this->json(['status' => 0, 'msg' => '添加失败']);
    }
}

// 获取商品详情
public function getproductdetail() {
    $proid = input('param.id/d');
    $bid = input('param.bid/d'); // 接收前端传过来的bid参数
    $where = [];
    $where[] = ['aid', '=', aid];
    $where[] = ['id', '=', $proid];
    
    $field = "bid,id,pic,name,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint,fuwupoint,guigedata,status,ischecked,freighttype,start_time,end_time,start_hours,end_hours,balance,limit_start,perlimitdan,commissionset,commissiondata1,commissiondata2,commissiondata3,commissionset4,price_type,show_price,hide_price_text,hide_price_link,hide_price_detail_text,yh_price,is_newcustom";
    
    if (getcustom('plug_tengrui')) {
        $field .= ',house_status,group_status,group_ids,is_rzh,relation_type';
    }
    if (getcustom('shop_other_infor')) {
        $field .= ',xunjia_text';
    }
    if (getcustom('product_glass')) {
        $field .= ',product_type';
    }
    
    $product = Db::name('shop_product')->field($field)->where($where)->find();
    $product['is_member_yh'] = $this->member['is_yh'];
    
    if (!$product) {
        return $this->json(['status' => 0, 'msg' => '商品不存在']);
    }
    
    $product = $this->formatproduct($product);
    
    if ($product['status'] == 0) {
        return $this->json(['status' => 0, 'msg' => '商品已下架']);
    }
    if ($product['ischecked'] != 1) {
        return $this->json(['status' => 0, 'msg' => '商品未审核']);
    }
    if ($product['status'] == 2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())) {
        return $this->json(['status' => 0, 'msg' => '商品未上架']);
    }
    if ($product['status'] == 3) {
        $start_time = strtotime(date('Y-m-d ' . $product['start_hours']));
        $end_time = strtotime(date('Y-m-d ' . $product['end_hours']));
        if (($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))) {
            return $this->json(['status' => 0, 'msg' => '商品未上架']);
        }
    }
    
    if ($product['status'] == 2 || $product['status'] == 3) {
        $product['status'] = 1;
    }
    
    // 获取规格列表，并从 tuanzhang_guige 表获取团长售价
    // 使用传入的bid参数进行筛选
    $gglist = Db::name('tuanzhang_guige')
        ->where('proid', $proid)
        ->where('bid', $bid) // 添加bid筛选条件
        ->field([
            'id',
            'proid',
            'name',
            'pic',
            'market_price',
            'cost_price',
            'sell_price',
            'ks',
            'lvprice_data',
            'givescore',
            'givescorehuang',
            'givescoregongxianzhi',
            'limit_start',
            'barcode'
        ])
        ->select()
        ->toArray();
    
    if ($product['lvprice'] == 1) {
        $gglist = $this->formatgglist($gglist, $bid);
    }
    
    // 系统设置
    $sysset = Db::name('admin_set')->where('aid', aid)->field('name,logo,desc,fxjiesuantype,tel,kfurl,gzts,ddbb')->find();
    $shopset = Db::name('shop_sysset')->where('aid', aid)->field('showjd,comment,showcommission,hide_sales,hide_stock')->find();
    
    // 2024-07-09 - 隐藏价格
    if ($product['show_price'] > 0) {
        $this->checklogin();
        // 限制等级
        $levelids = explode(',', $product['show_price']);
        if (!in_array($this->member['levelid'], $levelids)) {
            $hide_price = true;
            unset(
                $product['market_price'],
                $product['sell_price'],
                $product['paidui_jiang'],
                $product['paidui_give'],
                $product['sell_price_origin'],
                $product['min_price'],
                $product['max_price']
            );
            $product['hide_price'] = 1;
        }
    }
    
    // 处理规格列表
    $guigelist = array();
    foreach ($gglist as $k => $v) {
        if ($product['balance'] > 0) {
            $v['advance_price'] = round($v['sell_price'] * (1 - $product['balance'] * 0.01), 2);
            $v['balance_price'] = round($v['sell_price'] * $product['balance'] * 0.01, 2);
        } else {
            $v['balance_price'] = 0;
        }
        // 预计佣金
        $commission = 0;
        $v['commission_desc'] = '元';
        // 计算佣金
        if ($this->member && $shopset['showcommission'] == 1 && $product['commissionset'] != -1) {
            $userlevel = Db::name('member_level')->where('aid', aid)->where('id', $this->member['levelid'])->find();
            if ($userlevel['can_agent'] != 0) {
                if ($product['commissionset'] == 1) { // 按比例
                    $commissiondata = json_decode($product['commissiondata1'], true);
                    if ($commissiondata && isset($commissiondata[$userlevel['id']]['commission1'])) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'] * ($v['sell_price'] - ($sysset['fxjiesuantype'] == 2 ? $v['cost_price'] : 0)) * 0.01;
                    }
                } elseif ($product['commissionset'] == 2) { // 按固定金额
                    $commissiondata = json_decode($product['commissiondata2'], true);
                    if ($commissiondata && isset($commissiondata[$userlevel['id']]['commission1'])) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'];
                    }
                } elseif ($product['commissionset'] == 3) { // 提成是积分
                    $commissiondata = json_decode($product['commissiondata3'], true);
                    if ($commissiondata && isset($commissiondata[$userlevel['id']]['commission1'])) {
                        $commission = $commissiondata[$userlevel['id']]['commission1'];
                        $v['commission_desc'] = '积分';
                    }
                } elseif ($product['commissionset'] == 0) { // 按会员等级
                    // fxjiesuantype 0按商品价格,1按成交价格,2按销售利润
                    if ($userlevel['commissiontype'] == 1) { // 固定金额按单
                        $commission = $userlevel['commission1'];
                    } else {
                        $commission = $userlevel['commission1'] * ($v['sell_price'] - ($sysset['fxjiesuantype'] == 2 ? $v['cost_price'] : 0)) * 0.01;
                    }
                }

                if ($product['commissionset4'] == 1 && $product['lvprice'] == 1) { // 极差分销
                    $lvprice_data = json_decode($v['lvprice_data'], true);
                    if (isset($lvprice_data[0])) {
                        $commission += $lvprice_data[0] - $v['sell_price'];
                        if ($commission < 0) $commission = 0;
                    }
                }
            }
        }
        $v['commission'] = round($commission, 2);

        // 隐藏价格
        if (isset($hide_price) && $hide_price) {
            $v['cost_price'] = $product['hide_price_detail_text'];
            $v['market_price'] = $product['hide_price_detail_text'];
            $v['sell_price'] = $product['hide_price_detail_text'];
            $v['commission'] = 0;
        }

        $guigelist[$v['ks']] = $v;
    }

    $guigedata = json_decode($product['guigedata'], true);
    $ggselected = [];
    foreach ($guigedata as $v) {
        $ggselected[] = 0;
    }
    $ks = implode(',', $ggselected);

    return $this->json([
        'status' => 1,
        'product' => $product,
        'guigelist' => $guigelist,
        'guigedata' => $guigedata,
        'ggselected' => $ggselected,
        'ks' => $ks,
        'shopset' => $shopset
    ]);
}

//订单提交页
	public function buy(){
		$this->checklogin();
		$prodata = explode('-',input('param.prodata'));
		$multi_promotion = 0;
        if(getcustom('multi_promotion')){
            $multi_promotion = 1;
        }
		
		$userlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
		$adminset = Db::name('admin_set')->where('aid',aid)->find();
		$product_set = Db::name('shop_sysset')->where('aid',aid)->find();
	
		$userinfo = [];
		$userinfo['discount'] = $userlevel['discount'];
		
		$userinfo['score'] = $this->member['score'];
		$userinfo['score2money'] = $adminset['score2money'];
		$userinfo['scoredk_money'] = round($userinfo['score'] * $userinfo['score2money'],2);
		$userinfo['scoredkmaxpercent'] = $adminset['scoredkmaxpercent'];
		$userinfo['scoremaxtype'] = 0; //0最大百分比 1最大抵扣金额
		
				
		$userinfo['heiscore'] = $this->member['heiscore'];
		$userinfo['score2moneyhei'] = $adminset['score2moneyhei'];
		$userinfo['scorebdkyfhei'] = round($userinfo['heiscore'] * $userinfo['score2moneyhei'],2);
		$userinfo['scoredkmaxpercenthei'] = $adminset['scoredkmaxpercenthei'];
		$userinfo['scoremaxtypehei'] = 0; //0最大百分比 1最大抵扣金额
		
		$userinfo['yue'] = $this->member['money'];
		$userinfo['score2moneyyu'] = $adminset['score2moneyyu'];
		$userinfo['scorebdkyfyu'] = round($userinfo['yue'] * $userinfo['score2moneyyu'],2);
		$userinfo['scoredkmaxpercentyu'] = $adminset['scoredkmaxpercentyu'];
		$userinfo['scoremaxtypeyu'] = 0; //0最大百分比 1最大抵扣金额

        $userinfo['scorehuang'] = $this->member['scorehuang'];
        $userinfo['score2moneyhuang'] = $adminset['score2moneyhuang'];
        $userinfo['scorebdkyfhuang'] = round($userinfo['scorehuang'] * $userinfo['score2moneyhuang'],2);
        $userinfo['scoredkmaxpercenthuang'] = $adminset['scoredkmaxpercenthuang'];
        $userinfo['scoremaxtypehuang'] = 0; //0最大百分比 1最大抵扣金额

		
		$userinfo['realname'] = $this->member['realname'];
		$userinfo['tel'] = $this->member['tel'];
		
		$scoredkmaxmoney = 0;
// 		$scoredkmaxmoneyhei
		$scoredkmaxmoneyhei = 0;
		$scoredkmaxmoneyyu = 0;
		
		$allbuydata = [];
		$autofahuo = 0;
        //满减活动
        $mjset = Db::name('manjian_set')->where('aid',aid)->find();
        if($mjset && $mjset['status']==1){
            $mjdata = json_decode($mjset['mjdata'],true);
            //指定分类
            if($mjset['fwtype']==1){
                //指定分类或商品分类不存在
                if(empty($mjset['categoryids'])){
                    $mjdata = array();
                }
            //指定商品
            }else if($mjset['fwtype']==2){
                if(empty($mjset['productids'])){
                    $mjdata = array();
                }
            }
            if(getcustom('plug_tengrui')) {
                if($mjdata){
                    $tr_check = new \app\common\TengRuiCheck();
                    //判断是否是否符合会员认证、会员关系、一户
                    $check_manjian = $tr_check->check_manjian($this->member,$mjset);
                    if($check_manjian && $check_manjian['status'] == 0){
                        $mjdata = array();
                    }
                }
            }
        }else{
            $mjdata = array();
        }
        //判断起订数量
        if($product_set['is_qd'] == 1){
            $qd_num = 0;
            foreach($prodata as $n=>$vs){
                $num_list = explode(',',$vs);
                $qd_num += $num_list[2];
            }
            $catestart_times = strtotime(date('Y-m-d 00:00:00'));
            //这里判断一下今天是否购�����过优惠
            $is_order = Db::name('shop_order')->where('paytime','>=',$catestart_times)->where('aid',aid)->where('mid',mid)->find();
            if($qd_num < $product_set['qd_num'] && !$is_order){
                return $this->json(['status'=>0,'msg'=>'最少'.$product_set['qd_num']."件起售"]);
            }
        }
		//判断结束
		foreach($prodata as $key=>$gwc){
			list($proid,$ggid,$num) = explode(',',$gwc);
            if(getcustom('to86yk')){
                $field = "id,aid,bid,cid,pic,name,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint,fuwupoint,freighttype,freightdata,perlimit,gettj,gettjtip,gettjurl,scoredkmaxset,scoredkmaxval,scoredkmaxsethei,scoredkmaxvalhei,scoredkmaxsetyu,scoredkmaxvalyu,status,start_time,end_time,start_hours,end_hours,balance,no_discount,limit_start,perlimitdan,to86yk_tid,scoredkmaxsethuang,scoredkmaxunithuang,diy_amount,is_newcustom,yh_price,yh_num,yh_danshu";
            }else{
                $field = "id,aid,bid,cid,pic,name,sales,market_price,sell_price,lvprice,lvprice_data,sellpoint,fuwupoint,freighttype,freightdata,perlimit,gettj,gettjtip,gettjurl,scoredkmaxset,scoredkmaxval,scoredkmaxsethei,scoredkmaxvalhei,scoredkmaxsetyu,scoredkmaxvalyu,status,start_time,end_time,start_hours,end_hours,balance,no_discount,limit_start,perlimitdan,scoredkmaxsethuang,scoredkmaxunithuang,diy_amount,is_newcustom,yh_price,yh_num,yh_danshu";
            }
            if(getcustom('plug_tengrui')) {
                $field .= ',house_status,group_status,group_ids,is_rzh,relation_type';
            }
            if(getcustom('product_glass')) {
                list($proid,$ggid,$num,$glass_record_id) = explode(',',$gwc);
                $field .= ',product_type';
            }
			$product = Db::name('shop_product')->field($field)->where('aid',aid)->where('ischecked',1)->where('id',$proid)->find();

			if(!$product){
				Db::name('shop_cart')->where('aid',aid)->where('proid',$proid)->delete();
				return $this->json(['status'=>0,'msg'=>'产品不存在或已下架']);
			}
			if($product['status']==0){
				return $this->json(['status'=>0,'msg'=>'商品未上架']);
			}
			if($product['status']==2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())){
				return $this->json(['status'=>0,'msg'=>'商品未上架']);
			}
			if($product['status']==3){
				$start_time = strtotime(date('Y-m-d '.$product['start_hours']));
				$end_time = strtotime(date('Y-m-d '.$product['end_hours']));
				if(($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))){
					return $this->json(['status'=>0,'msg'=>'商品未上架']);
				}
			}
			if(getcustom('product_glass')){
                $glassrecord = [];
			    if($glass_record_id){
			        $glassrecord = Db::name('glass_record')->where('aid',aid)->where('mid',$this->mid)->where('id',$glass_record_id)->find();
                }
                $product['glassrecord'] = $glassrecord??'';
            }

			if($product['freighttype'] == 3 || $product['freighttype'] == 4) $autofahuo = $product['freighttype'];
			$guige = Db::name('tuanzhang_guige')->where('id',$ggid)->find();
			if(!$guige){
				Db::name('shop_cart')->where('aid',aid)->where('ggid',$ggid)->delete();
				return $this->json(['status'=>0,'msg'=>'产品该规格不存在或已下架']);
			}
			if($guige['stock'] < $num){
				return $this->json(['status'=>0,'msg'=>'库存不足']);
			}
			$gettj = explode(',',$product['gettj']);
			if(!in_array('-1',$gettj) && !in_array($this->member['levelid'],$gettj) && (!in_array('0',$gettj) || $this->member['subscribe']!=1)){ //不是所有人
				if(!$product['gettjtip']) $product['gettjtip'] = '没有权限购买该商品';
				return $this->json(['status'=>0,'msg'=>$product['gettjtip'],'url'=>$product['gettjurl']]);
			}
			if($product['perlimit'] > 0){
				$buynum = $num + Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('proid',$product['id'])->where('status','in','0,1,2,3')->sum('num');
				if($buynum > $product['perlimit']){
					return $this->json(['status'=>0,'msg'=>'每人限购'.$product['perlimit'].'件']);
				}
			}
			//开启优惠 判断优惠程度
// 			if($this->member['is_yh'] == 0 && $product['is_newcustom'] == 1){
// 			    $buynum = $num + Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('proid',$product['id'])->where('status','in','0,1,2,3')->sum('num');
// 			    if($buynum > 5){
// 					return $this->json(['status'=>0,'msg'=>'每人最多优惠5件商品']);
// 				}
// 			}
            if($guige['limit_start'] > 0 && $num < $guige['limit_start']){
                return $this->json(['status'=>0,'msg'=>'['.$product['name'].']['.$guige['name'].'] '.$guige['limit_start'].'件起售']);
            }
			if($product['limit_start'] > 0 && $num < $product['limit_start']){
				return $this->json(['status'=>0,'msg'=>'['.$product['name'].'] '.$product['limit_start'].'件起售']);
			}
			if($product['perlimitdan'] > 0 && $num > $product['perlimitdan']){
				return $this->json(['status'=>0,'msg'=>'['.$product['name'].'] 每单限购'.$product['perlimitdan'].'件']);
			}
            if(getcustom('plug_tengrui')) {
                //判断是否是否符合会员认证、会员关系、一户
                $tr_check = new \app\common\TengRuiCheck();
                $check_product = $tr_check->check_product($this->member,$product);
                if($check_product && $check_product['status'] == 0){
                    return $this->json(['status'=>$check_product['status'],'msg'=>$check_product['msg']]);
                }
                $tr_roomId = $check_product['tr_roomId'];
            }
			if($product['lvprice']==1) $guige = $this->formatguige($guige, $product['bid']);
			if($product['scoredkmaxset']==0){
				if($userinfo['scoredkmaxpercent'] == 0){
					$userinfo['scoremaxtype'] = 1;
					$scoredkmaxmoney += 0;
				}else{
					if($userinfo['scoredkmaxpercent'] > 0 && $userinfo['scoredkmaxpercent']<100){
						$scoredkmaxmoney += $userinfo['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $num;
					}else{
						$scoredkmaxmoney += $guige['sell_price'] * $num;
					}
				}
			}elseif($product['scoredkmaxset']==1){
				$userinfo['scoremaxtype'] = 1;
				$scoredkmaxmoney += $product['scoredkmaxval'] * 0.01 * $guige['sell_price'] * $num;
			}elseif($product['scoredkmaxset']==2){
				$userinfo['scoremaxtype'] = 1;
				$scoredkmaxmoney += $product['scoredkmaxval'] * $num;
			}else{
				$userinfo['scoremaxtype'] = 1;
				$scoredkmaxmoney += 0;
			}

			if($product['scoredkmaxsethei']==0){
				if($userinfo['scoredkmaxpercenthei'] == 0){
					$userinfo['scoremaxtypehei'] = 1;
					$scoredkmaxmoneyhei += 0;
				}else{
					if($userinfo['scoredkmaxpercenthei'] > 0 && $userinfo['scoredkmaxpercenthei']<100){
						$scoredkmaxmoneyhei += $userinfo['scoredkmaxpercenthei'] * 0.01 * $guige['sell_price'] * $num;
					}else{
						$scoredkmaxmoneyhei += $guige['sell_price'] * $num;
					}
				}
			}elseif($product['scoredkmaxsethei']==1){
				$userinfo['scoremaxtypehei'] = 1;
				$scoredkmaxmoneyhei += $product['scoredkmaxvalhei'] * 0.01 * $guige['sell_price'] * $num;
			}elseif($product['scoredkmaxsethei']==2){
				$userinfo['scoremaxtypehei'] = 1;
				$scoredkmaxmoneyhei += $product['scoredkmaxvalhei'] * $num;
			}else{
				$userinfo['scoremaxtypehei'] = 1;
				$scoredkmaxmoneyhei += 0;
			}
			if($product['scoredkmaxsetyu']==0){
				if($userinfo['scoredkmaxpercentyu'] == 0){
					$userinfo['scoremaxtypeyu'] = 1;
					$scoredkmaxmoneyyu += 0;
				}else{
					if($userinfo['scoredkmaxpercentyu'] > 0 && $userinfo['scoredkmaxpercentyu']<100){
						$scoredkmaxmoneyyu += $userinfo['scoredkmaxpercentyu'] * 0.01 * $guige['sell_price'] * $num;
					}else{
						$scoredkmaxmoneyyu += $guige['sell_price'] * $num;
					}
				}
			}elseif($product['scoredkmaxsetyu']==1){
				$userinfo['scoremaxtypeyu'] = 1;
				$scoredkmaxmoneyyu += $product['scoredkmaxvalyu'] * 0.01 * $guige['sell_price'] * $num;
			}elseif($product['scoredkmaxsetyu']==2){
				$userinfo['scoremaxtypeyu'] = 1;
				$scoredkmaxmoneyyu += $product['scoredkmaxvalyu'] * $num;
			}else{
				$userinfo['scoremaxtypeyu'] = 1;
				$scoredkmaxmoneyyu += 0;
			}
            
            if($product['scoredkmaxsethuang']==0){
                if($userinfo['scoredkmaxpercenthuang'] == 0){
                    $userinfo['scoremaxtypehuang'] = 1;
                    $scoredkmaxmoneyhuang += 0;
                }else{
                    if($userinfo['scoredkmaxpercenthuang'] > 0 && $userinfo['scoredkmaxpercenthuang']<100){
                        $scoredkmaxmoneyhuang += $userinfo['scoredkmaxpercenthuang'] * 0.01 * $guige['sell_price'] * $num;
                    }else{
                        $scoredkmaxmoneyhuang += $guige['sell_price'] * $num;
                    }
                }
            }elseif($product['scoredkmaxsethuang']==1){
                $userinfo['scoremaxtypehuang'] = 1;
                $scoredkmaxmoneyhuang += $product['scoredkmaxunithuang'] * 0.01 * $guige['sell_price'] * $num;
            }elseif($product['scoredkmaxsethuang']==2){
                $userinfo['scoremaxtypehuang'] = 1;
                $scoredkmaxmoneyhuang += $product['scoredkmaxunithuang'] * $num;
            }else{
                $userinfo['scoremaxtypehuang'] = 1;
                $scoredkmaxmoneyhuang += 0;
            }

            
			if(!$allbuydata[$product['bid']]) $allbuydata[$product['bid']] = [];
			if(!$allbuydata[$product['bid']]['prodata']) $allbuydata[$product['bid']]['prodata'] = [];
			$allbuydata[$product['bid']]['prodata'][] = ['product'=>$product,'guige'=>$guige,'num'=>$num,'tid'=>$tid,'dhtid'=>$dhtid];
			if($product['to86yk_tid']){
				$extendInput = [['key'=>'input','val1'=>'充值账号','val2'=>'请输入充值账号','val3'=>1],['key'=>'input','val1'=>'确认账号','val2'=>'请再次输入充值账号','val3'=>1]];
			}
		}
		

		if($autofahuo>0 && count($prodata) > 1){
			return $this->json(['status'=>0,'msg'=>'虚拟商品请单独购买']);
		}
		$userinfo['scoredkmaxmoney'] = round($scoredkmaxmoney,2);
		$userinfo['scoredkmaxmoneyhei'] = round($scoredkmaxmoneyhei,2);
		$userinfo['scoredkmaxmoneyyu'] = round($scoredkmaxmoneyyu,2);
		$userinfo['scoredkmaxmoneyhuang'] = round($scoredkmaxmoneyhuang,2);
		$havetongcheng = 0;
		foreach($allbuydata as $bid=>$buydata){
			
			if(getcustom('guige_split')){
				$rs = \app\model\ShopProduct::checkstock($buydata['prodata']);
				if($rs['status'] == 0) return $this->json($rs);
			}
			if($autofahuo>0){
				$freightList = [['id'=>0,'name'=>($autofahuo==3?'自动发���':'在线卡密'),'pstype'=>$autofahuo]];
			}else{
				$freightList = \app\model\Freight::getList([['status','=',1],['aid','=',aid],['bid','=',$bid]]);
				$fids = [];
				foreach($freightList as $v){
					$fids[] = $v['id'];
				}
				foreach($buydata['prodata'] as $prodata){
					if($prodata['product']['freighttype']==0){
						$fids = array_intersect($fids,explode(',',$prodata['product']['freightdata']));
					}else{
						$thisfreightList = \app\model\Freight::getList([['status','=',1],['aid','=',aid],['bid','=',$bid]]);
						$thisfids = [];
						foreach($thisfreightList as $v){
							$thisfids[] = $v['id'];
						}
						$fids = array_intersect($fids,$thisfids);
					}
				}
				if(!$fids){
					if(count($buydata['prodata'])>1){
						return $this->json(['status'=>0,'msg'=>'所选择商品配送方式不同，请分别下单']);
					}else{
						return $this->json(['status'=>0,'msg'=>'获取配送方式失败']);
					}
				}
				$freightList = \app\model\Freight::getList([['status','=',1],['aid','=',aid],['bid','=',$bid],['id','in',$fids]]);
				foreach($freightList as $k=>$v){
					if($v['pstype']==2){ //同城配送
						$havetongcheng = 1;
					}
					if(getcustom('hmy_yuyue') && $v['pstype']==12){
						//红蚂蚁定制
						$havetongcheng = 1;
					}
				}
			}
			$allbuydata[$bid]['freightList'] = $freightList;
		}
		if($havetongcheng){
			$address = Db::name('member_address')->where('aid',aid)->where('mid',mid)->where('latitude','>',0)->order('isdefault desc,id desc')->find();
		}else{
			$address = Db::name('member_address')->where('aid',aid)->where('mid',mid)->order('isdefault desc,id desc')->find();
		}
		if(!$address) $address = [];
		$needLocation = 0;
		$allproduct_price = 0;
		foreach($allbuydata as $bid=>$buydata){
			if($bid!=0){
				$business = Db::name('business')->where('id',$bid)->field('id,aid,cid,name,logo,tel,address,sales,longitude,latitude,start_hours,end_hours,start_hours2,end_hours2,start_hours3,end_hours3,invoice,invoice_type')->find();
				
				$is_open = 0;
				if($is_open==0){
					if($business['start_hours'] != $business['end_hours']){
						$start_time = strtotime(date('Y-m-d '.$business['start_hours']));
						$end_time = strtotime(date('Y-m-d '.$business['end_hours']));
						if(($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))){
							//return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
						}else{
							$is_open = 1;
						}
					}else{
						$is_open = 1;
					}
				}
				if($is_open==0){
					$start_time = strtotime(date('Y-m-d '.$business['start_hours2']));
					$end_time = strtotime(date('Y-m-d '.$business['end_hours2']));
					if($start_time == $end_time || ($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))){
						//return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
					}else{
						$is_open = 1;
					}
				}
				if($is_open==0){
					$start_time = strtotime(date('Y-m-d '.$business['start_hours3']));
					$end_time = strtotime(date('Y-m-d '.$business['end_hours3']));
					if($start_time == $end_time || ($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time > $end_time && ($start_time > time() && $end_time < time()))){
						//return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
					}else{
						$is_open = 1;
					}
				}
				if($is_open == 0){
					return $this->json(['status'=>-4,'msg'=>'商家不在营业时间']);
				}
			}else{
				$business = Db::name('admin_set')->where('aid',aid)->field('id,name,logo,desc,tel,invoice,invoice_type,invoice_rate')->find();
			}
            $business['invoice_type'] = $business['invoice'] ? explode(',', $business['invoice_type']) : [];
			$product_priceArr = [];
			$product_price = 0;
			$diy_amount = 0;
			$needzkproduct_price = 0;
			$totalweight = 0;
			$totalnum = 0;
            $totalnum2 = 0;
			$prodataArr = [];
			$proids = [];
			$cids = [];
            //符合满减的商品价格
            $mj_price = 0;
            
			foreach($buydata['prodata'] as $prodata){
				$product_priceArr[] = $prodata['guige']['sell_price'] * $prodata['num'];
				$product_price += $prodata['guige']['sell_price'] * $prodata['num'];
				if($prodata['product']['balance']){
					$product_price = $product_price * (1-$prodata['product']['balance']*0.01);
				}
			
				if($prodata['product']['lvprice']==0 && $prodata['product']['no_discount'] == 0){ //未开启会员价
					$needzkproduct_price += $prodata['guige']['sell_price'] * $prodata['num'];
				}
				$diy_amount += $prodata['product']['diy_amount'] * $prodata['num'];
				$totalweight += $prodata['guige']['weight'] * $prodata['num'];
				if($prodata['product']['diy_amount'])
				{
				$totalnum += $prodata['num'];
                    //var_dump($totalnum);;
                }
                if($prodata['product'])
                {
                    $totalnum2 += $prodata['num'];
                   
				}
				
				$_prostr = $prodata['product']['id'].','.$prodata['guige']['id'].','.$prodata['num'];
				if(isset($prodata['product']['glassrecord']) && $prodata['product']['glassrecord']){
                    $_prostr .=','.$prodata['product']['glassrecord']['id'];
                }
				$prodataArr[] = $_prostr;
				$proids[] = $prodata['product']['id'];
				$cids = array_merge(explode(',',$prodata['product']['cid']),$cids);
                //如果满减设置存在，且数据存在
                if($mjset && $mjdata){
                    //指定分类
                    if($mjset['fwtype']==1){
                        //指定分类数组
                        $cat_arr     = explode(",",$mjset['categoryids']);
                        //商品分类
                        $pro_cat_arr = explode(",",$prodata['product']['cid']);
                        //交集
                        $j_arr = array_intersect($cat_arr,$pro_cat_arr);
                        if($j_arr){
                            $mj_price += $prodata['guige']['sell_price'] * $prodata['num'];
                            if($prodata['product']['balance']){
                                $mj_price = $mj_price * (1-$prodata['product']['balance']*0.01);
                            }
                        }
                    //指定商品
                    }else if($mjset['fwtype']==2){
                        $pro_arr = explode(",",$mjset['productids']);
                        //商品在指定商品内
                        if (in_array($prodata['product']['id'], $pro_arr)){
                            $mj_price += $prodata['guige']['sell_price'] * $prodata['num'];
                            if($prodata['product']['balance']){
                                $mj_price = $mj_price * (1-$prodata['product']['balance']*0.01);
                            }
                        }
                    }else{
                        $mj_price += $prodata['guige']['sell_price'] * $prodata['num'];
                        if($prodata['product']['balance']){
                            $mj_price = $mj_price * (1-$prodata['product']['balance']*0.01);
                        }
                    }
                }
			}
			$prodatastr = implode('-',$prodataArr);
			
			$rs = \app\model\Freight::formatFreightList($buydata['freightList'],$address,$product_price,$totalnum,$totalweight);

			$freightList = $rs['freightList'];
			$freightArr = $rs['freightArr'];
			if($rs['needLocation']==1) $needLocation = 1;
			
			$leveldk_money = 0;
			if($userlevel && $userlevel['discount']>0 && $userlevel['discount']<10){
				$leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
			}
			$leveldk_money = round($leveldk_money,2);
			$price = $product_price - $leveldk_money;
            $mj_price = $mj_price-$leveldk_money;

			$manjian_money = 0;
			$moneyduan = 0;
			if($mjdata && $mj_price>0){
                //如果是总满减
                if($mjset['total_status']==1){
                    //指定分类
                    if($mjset['fwtype']==1){
                        //查询他分类消费累计
                        $sum_money  = Db::name('shop_order_goods')
                            ->alias('sog')
                            ->join('shop_order so','so.id   = sog.orderid')
                            ->join('shop_product sp','sp.id = sog.proid')
                            ->where('sog.mid',mid)
                            ->where('so.status',3)
                            ->where('sp.cid','in',$mjset['categoryids'])
                            ->sum('sog.totalprice');

                        //分类退款累计
                        $refund_money = Db::name('shop_refund_order_goods')
                            ->alias('srog')
                            ->join('shop_order so','so.id   = srog.orderid')
                            ->join('shop_product sp','sp.id = srog.proid')
                            ->where('srog.mid',mid)
                            ->where('so.status',3)
                            ->where('so.refund_status',2)
                            ->where('sp.cid','in',$mjset['categoryids'])
                            ->sum('srog.refund_money');

                    //指定商品
                    }else if($mjset['fwtype']==2){
                        //查询他商品消费累计
                        $sum_money  = Db::name('shop_order_goods')
                            ->alias('sog')
                            ->join('shop_order so','so.id   = sog.orderid')
                            ->join('shop_product sp','sp.id = sog.proid')
                            ->where('sog.mid',mid)
                            ->where('so.status',3)
                            ->where('sog.proid','in',$mjset['productids'])
                            ->sum('sog.totalprice');

                        //商品退款累计
                        $refund_money = Db::name('shop_refund_order_goods')
                            ->alias('srog')
                            ->join('shop_order so','so.id   = srog.orderid')
                            ->join('shop_product sp','sp.id = srog.proid')
                            ->where('srog.mid',mid)
                            ->where('so.status',3)
                            ->where('so.refund_status',2)
                            ->where('srog.proid','in',$mjset['productids'])
                            ->sum('srog.refund_money');
                    //所有
                    }else{
                        //查询他消费累计
                        $sum_money    = Db::name('shop_order')->where('mid',mid)->where('status',3)->sum('totalprice');
                        //退款累计
                        $refund_money = Db::name('shop_order')->where('mid',mid)->where('status',3)->where('refund_status',2)->sum('refund_money');
                    }

                    //实际累计
                    $sj_money = $sum_money-$refund_money;
                    $sj_money = round($sj_money,2);
                    $all_price = $sj_money+$mj_price;
                    foreach($mjdata as $give){
                        if(($all_price - $leveldk_money)*1 >= $give['money']*1 && $give['money']*1 > $moneyduan){
                            $moneyduan = $give['money']*1;
                            $manjian_money = $give['jian']*1;
                        }
                    }
                }else{
                    foreach($mjdata as $give){
                        if(($mj_price - $leveldk_money)*1 >= $give['money']*1 && $give['money']*1 > $moneyduan){
                            $moneyduan = $give['money']*1;
                            $manjian_money = $give['jian']*1;
                        }
                    }
                }

			}
			if($manjian_money > 0){
				$allbuydata[$bid]['manjian_money'] = round($manjian_money,2);
			}else{
				$allbuydata[$bid]['manjian_money'] = 0;
			}
			
			$newcouponlist = [];
			$couponList = Db::name('coupon_record')
				->where("bid=-1 or bid=".$bid)->where('aid',aid)->where('mid',mid)->where('type','in','1,4,10')->where('status',0)->where('minprice','<=',$price - $manjian_money)->where('starttime','<=',time())->where('endtime','>',time())
				->order('id desc')->select()->toArray();
			if(!$couponList) $couponList = [];
			foreach($couponList as $k=>$v){
				//$couponList[$k]['starttime'] = date('m-d H:i',$v['starttime']);
				//$couponList[$k]['endtime'] = date('m-d H:i',$v['endtime']);
				$couponinfo = Db::name('coupon')->where('aid',aid)->where('id',$v['couponid'])->find();
                //0全场通用,1指定类目,2指定商品
                if(!in_array($couponinfo['fwtype'],[0,1,2])){
                    continue;
                }
				
				$usetj = explode(',',$couponinfo['usetj']);
				if(!in_array('-1',$usetj) && !in_array($this->member['levelid'],$usetj) && (!in_array('0',$usetj) || $this->member['subscribe']!=1)){
					continue;
				}

				$v['thistotalprice'] = $price - $manjian_money;
				if($couponinfo['fwtype']==2){//指定商品可用
					$productids = explode(',',$couponinfo['productids']);
					if(!array_intersect($proids,$productids)){
						continue;
					}
					$thistotalprice = 0;
					foreach($buydata['prodata'] as $k2=>$v2){
						$product = $v2['product'];
						if(in_array($product['id'],$productids)){
							$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
						}
					}
					if($thistotalprice < $v['minprice']){
						continue;
					}
					$v['thistotalprice'] = $thistotalprice;
				}
				if($couponinfo['fwtype']==1){//指定类目可用
					$categoryids = explode(',',$couponinfo['categoryids']);
					$clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
					foreach($clist as $kc=>$vc){
						$categoryids[] = $vc['id'];
						$cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
						$categoryids[] = $cate2['id'];
					}
					if(!array_intersect($cids,$categoryids)){
						continue;
					}
					$thistotalprice = 0;
					foreach($buydata['prodata'] as $k2=>$v2){
						$product = $v2['product'];
						if(array_intersect(explode(',',$product['cid']),$categoryids)){
							$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
						}
					}
					if($thistotalprice < $v['minprice']){
						continue;
					}
					$v['thistotalprice'] = $thistotalprice;
				}
				if($v['bid'] > 0){
					$binfo = Db::name('business')->where('aid',aid)->where('id',$v['bid'])->find();
					$v['bname'] = $binfo['name'];
				}
				$newcouponlist[] = $v;
			}
			$couponList = $newcouponlist;

			//促销活动
			$cuxiaolist = Db::name('cuxiao')->where('aid',aid)->where('bid',$bid)->where("(type in (1,2,3,4) and minprice<=".($price - $manjian_money).") or ((type=5 or type=6) and minnum<=".$totalnum.") ")->where('starttime','<',time())->where('endtime','>',time())->order('sort desc')->select()->toArray();
			$newcxlist = [];
			foreach($cuxiaolist as $k=>$v){
				$gettj = explode(',',$v['gettj']);
				if(!in_array('-1',$gettj) && !in_array($this->member['levelid'],$gettj)){ //不是所有人
					continue;
				}
				if($v['fwtype']==2){//指定商品可用
					$productids = explode(',',$v['productids']);
					if(!array_intersect($proids,$productids)){
						continue;
					}
					if($v['type']==1 || $v['type']==2 || $v['type']==3 || $v['type']==4){//指定商品是否达到金额要求
						$thistotalprice = 0;
						foreach($buydata['prodata'] as $k2=>$v2){
							$product = $v2['product'];
							if(in_array($product['id'],$productids)){
								$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
							}
						}
						if($thistotalprice < $v['minprice']){
							continue;
						}
					}
					if($v['type']==6 || $v['type']==5){//指定商品是否达到件数要求
						$thistotalnum = 0;
						foreach($buydata['prodata'] as $k2=>$v2){
							$product = $v2['product'];
							if(in_array($product['id'],$productids)){
								$thistotalnum += $v2['num'];
							}
						}
						if($thistotalnum < $v['minnum']){
							continue;
						}
					}
				}
				if($v['fwtype']==1){//指定类目可用
					$categoryids = explode(',',$v['categoryids']);
					$clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
					foreach($clist as $kc=>$vc){
						$categoryids[] = $vc['id'];
						$cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
						$categoryids[] = $cate2['id'];
					}
					if(!array_intersect($cids,$categoryids)){
						continue;
					}
					if($v['type']==1 || $v['type']==2 || $v['type']==3 || $v['type']==4){//指定商品是否达到金额要求
						$thistotalprice = 0;
						foreach($buydata['prodata'] as $k2=>$v2){
							$product = $v2['product'];
							if(array_intersect(explode(',',$product['cid']),$categoryids)){
								$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
							}
						}
						if($thistotalprice < $v['minprice']){
							continue;
						}
					}
					if($v['type']==6 || $v['type']==5){//指定类目内商品是否达到件数要求
						$thistotalnum = 0;
						foreach($buydata['prodata'] as $k2=>$v2){
							$product = $v2['product'];
							if(array_intersect(explode(',',$product['cid']),$categoryids)){
								$thistotalnum += $v2['num'];
							}
						}
						if($thistotalnum < $v['minnum']){
							continue;
						}
					}
				}
				if($v['type']==4 || $v['type']==5){
					if($v['fwtype']==2) {
					    //商品
                        $cuxiaomoney = 0;
                        $prozkArr = array_combine(explode(',', $v['productids']), explode(',', $v['prozk']));
                        $pronumArr = array_combine(explode(',', $v['productids']), explode(',', $v['pronum']));
                        foreach ($buydata['prodata'] as $k2 => $v2) {
                            $product = $v2['product'];
                            if ($prozkArr[$product['id']]) {
                                $prozk = $prozkArr[$product['id']];
                            } elseif (isset($prozkArr[$product['id']])) {
                                $prozk = $v['zhekou'];
                            } else {
                                $prozk = 10;
                            }
                            if ($v['type'] == 5 && $pronumArr[$product['id']] && intval($pronumArr[$product['id']]) > $v2['num']) {
                                $prozk = 10;
                            }
                            $cuxiaomoney += $product_priceArr[$k2] * (1 - $prozk * 0.1);
                        }
                    }elseif($v['fwtype']==1) {
					    //分类
					    $categoryPrice = 0;

                        foreach ($buydata['prodata'] as $k2 => $v2) {
                            $product = $v2['product'];
                            $cids2 = explode(',', $product['cid']);
                            if(array_intersect($cids2, $categoryids)) {
                                $categoryPrice += $v2['guige']['sell_price'] * $v2['num'];
                            }
                        }
                        $cuxiaomoney = $categoryPrice * (1 - $v['zhekou'] * 0.1);
					}else{
					    //全部
						$cuxiaomoney = $price * (1 - $v['zhekou'] * 0.1);
					}
					$v['cuxiaomoney'] = round($cuxiaomoney,2);
				}
                if(getcustom('plug_tengrui')) {
                    $tr_check = new \app\common\TengRuiCheck();
                    //判断是否是否符合会员认证、会员关系、一户
                    $check_cuxiao = $tr_check->check_cuxiao($this->member,$v);
                    if($check_cuxiao && $check_cuxiao['status'] == 0){
                        continue;
                    }
                }
				$newcxlist[] = $v;
			}
			
			if($extendInput){
				foreach($freightList as $fk=>$fv){
					$freightList[$fk]['formdata'] = array_merge($extendInput,$fv['formdata']);
				}
			}
            $invoice_price = 0;
			if($business['invoice'] && $business['invoice_rate'] > 0 && getcustom('invoice_rate')){
                $invoice_price = ($product_price + $freightList[0]['freight_price']) * $business['invoice_rate'] / 100;
            }
			$allbuydata[$bid]['bid'] = $bid;
			$allbuydata[$bid]['business'] = $business;
			$allbuydata[$bid]['prodatastr'] = $prodatastr;
			$allbuydata[$bid]['couponList'] = $couponList;
			$allbuydata[$bid]['couponCount'] = count($couponList);
			$allbuydata[$bid]['freightList'] = $freightList;
			$allbuydata[$bid]['freightArr'] = $freightArr;
			$allbuydata[$bid]['product_price'] = round($product_price,2);
            $allbuydata[$bid]['invoice_price'] = round($invoice_price,2);
            $allbuydata[$bid]['diy_amount'] = round($diy_amount,2);
            $allbuydata[$bid]['totalnum'] = $totalnum;
			$allbuydata[$bid]['leveldk_money'] = $leveldk_money;
			$allbuydata[$bid]['coupon_money'] = 0;
			$allbuydata[$bid]['coupontype'] = 1;
			$allbuydata[$bid]['couponrid'] = 0;
			$allbuydata[$bid]['couponrids'] = [];
			$allbuydata[$bid]['coupons'] = [];
			$allbuydata[$bid]['freightkey'] = 0;
			$allbuydata[$bid]['pstimetext'] = '';
			$allbuydata[$bid]['freight_time'] = '';
			$allbuydata[$bid]['storeid'] = 0;
			$allbuydata[$bid]['storename'] = '';
			$allbuydata[$bid]['cuxiaolist'] = $newcxlist;
			$allbuydata[$bid]['cuxiaoCount'] = count($newcxlist);
			$allbuydata[$bid]['cuxiao_money'] = 0;
			$allbuydata[$bid]['cuxiaotype'] = 0;
			$allbuydata[$bid]['cuxiaoid'] = 0;
			$allbuydata[$bid]['editorFormdata'] = [];

			$allbuydata[$bid]['coupon_peruselimit'] = 1;
			if(getcustom('coupon_peruselimit')){
				$shopset = Db::name('shop_sysset')->where('aid',aid)->find();
				$allbuydata[$bid]['coupon_peruselimit'] = $shopset['coupon_peruselimit'];
			}

			$allproduct_price += $product_price;
		}

		if(getcustom('plug_xiongmao')) {
            $admin = Db::name('admin')->where('id',aid)->find();
            if(in_array('order_change_price', explode(',',$admin['remark']))) {
                $order_change_price = true;
            }
        }
        $z_price = 0;
        $zongshu = count($allbuydata[0]['prodata']);
        foreach ($allbuydata[0]['prodata'] as $key =>$vals){
            $allbuydata[0]['prodata'][$key]['youhui']['is_yh'] = 0;//将是否显示优惠设定为0
            //判断单数信息
            $order_numss = Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('proid',$vals['product']['id'])->where('status','in','0,1,2,3')->count();
            // var_dump($order_numss);
            // var_dump($vals['product']['yh_danshu']);
            // var_dump($vals);
            // exit();
            if($allbuydata[0]['prodata'][$key]['product']['is_newcustom'] == 1 && $this->member['is_yh'] == 0 && $order_numss < $vals['product']['yh_danshu']){
                // echo "111";
                $buynum = Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('proid',$vals['product']['id'])->where('status','in','0,1,2,3')->sum('num');
                if($buynum <= $vals['product']['yh_num']){
              if($vals['product']['yh_num'] >= ($buynum+$vals['num']) && $vals['product']['yh_num'] > 0){
                    $allbuydata[0]['prodata'][$key]['guige']['sell_price'] = $allbuydata[0]['prodata'][$key]['product']['yh_price'];
                    //
                    $allbuydata[0]['prodata'][$key]['num'] = 0;
                    $allbuydata[0]['prodata'][$key]['youhui']['is_yh'] = 1;
                    $allbuydata[0]['prodata'][$key]['youhui']['product_price'] = $allbuydata[0]['prodata'][$key]['product']['yh_price'];
                    $allbuydata[0]['prodata'][$key]['youhui']['new_num'] = $vals['num'];
                    //
                    // echo $allbuydata[0]['prodata'][$key]['product']['yh_price'];
                    // echo '---------------';
                    // echo $buynum;
                    $z_price += ($allbuydata[0]['prodata'][$key]['product']['yh_price'] * $vals['num']);
                }else{
                    //给予正常价格购买的数量
                    $zcnums = $vals['num'] - ($vals['product']['yh_num'] - $buynum);
                    $allbuydata[0]['prodata'][$key]['num'] = $zcnums;
                    $allbuydata[0]['prodata'][$key]['youhui']['is_yh'] = 1;
                    $allbuydata[0]['prodata'][$key]['youhui']['new_num'] = ($vals['product']['yh_num'] - $buynum);
                    $allbuydata[0]['prodata'][$key]['youhui']['product_price'] = $allbuydata[0]['prodata'][$key]['product']['yh_price'];
                    $z_price += $zcnums * $vals['product']['sell_price'] + ($vals['product']['yh_num'] - $buynum)*$allbuydata[0]['prodata'][$key]['product']['yh_price'];
                    }
                }
            }else{
                $z_price += $vals['guige']['sell_price'] * $vals['num'];
            }
            $allbuydata[0]['product_price'] = $z_price;
        }
        // var_dump($allbuydata);
        // exit();
        // $allbuydata[0]['prodata'][0]['youhui']['is_yh'] = 0;//将是否显示优惠设定为0
        // //判断是否是优惠
        // if($allbuydata[0]['prodata'][0]['product']['is_newcustom'] == 1 && $this->member['is_yh'] == 0){
        //     //查询优惠
        //     $buynum = Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('proid',$product['id'])->where('status','in','0,1,2,3')->sum('num');
        //     //如果用户购买的次数小于优惠次数则替换
        //     if($product['yh_num'] >= ($buynum+$num) && $product['yh_num'] > 0){
        //         $allbuydata[0]['prodata'][0]['guige']['sell_price'] = $allbuydata[0]['prodata'][0]['product']['yh_price'];
        //         $allbuydata[0]['product_price'] = $allbuydata[0]['prodata'][0]['product']['yh_price'];
        //     }else{
        //         //给予正常价格购买的数量
        //         $zcnums = $num - ($product['yh_num'] - $buynum);
        //         $allbuydata[0]['prodata'][0]['num'] = $zcnums;
        //         $allbuydata[0]['prodata'][0]['youhui']['is_yh'] = 1;
        //         $allbuydata[0]['prodata'][0]['youhui']['new_num'] = ($product['yh_num'] - $buynum);
        //         $allbuydata[0]['prodata'][0]['youhui']['product_price'] = $allbuydata[0]['prodata'][0]['product']['yh_price'];
        //         $allbuydata[0]['product_price'] = $zcnums * $product['sell_price'] + ($product['yh_num'] - $buynum)*$allbuydata[0]['prodata'][0]['product']['yh_price'];
        //     }
        // }
       
		$rdata = [];
		$rdata['status'] = 1;
		$rdata['havetongcheng'] = $havetongcheng;
		$rdata['address'] = $address;
		$rdata['linkman'] = $address ? $address['name'] : strval($userinfo['realname']);
		$rdata['tel'] = $address ? $address['tel'] : strval($userinfo['tel']);
		if(!$rdata['linkman']){
			$lastorder = Db::name('shop_order')->where('aid',aid)->where('mid',mid)->where('linkman','<>','')->find();
			if($lastorder){
				$rdata['linkman'] = $lastorder['linkman'];
				$rdata['tel'] = $lastorder['tel'];
			}
		}
		$rdata['userinfo'] = $userinfo;
		$rdata['allbuydata'] = $allbuydata;
		$rdata['needLocation'] = $needLocation;
		$rdata['scorebdkyf'] = Db::name('admin_set')->where('aid',aid)->value('scorebdkyf');
		$rdata['scorebdkyfheihei'] = Db::name('admin_set')->where('aid',aid)->value('scorebdkyfhei');
		$rdata['buy_selectmember'] = false;
		if(getcustom('buy_selectmember')){
			$canselect = Db::name('member_level')->where('aid',aid)->where('can_buyselect',1)->find();
			if($canselect) $rdata['buy_selectmember'] = true;
		}
        $rdata['multi_promotion'] = $multi_promotion;
        $rdata['order_change_price'] = $order_change_price;
        $rdata['pstype3needAddress'] = false;
        if(getcustom('plug_zhiming')) {
            $rdata['pstype3needAddress'] = true;
        }
		$rdata['single_freight'] = 0;
        $rdata['total_freight'] = 0;
 // // 运费价格 $userlevel
if ($userlevel['freight']) {
    $freight_levels = json_decode($userlevel['freight'], true);

    // 按数量从低到高排序
    usort($freight_levels, function ($a, $b) {
        return $a['num'] - $b['num'];
    });

    $freightPrice = 0;
    $applicableLevel = null;

    // 匹配符合当前框数的运费等级
    foreach ($freight_levels as $level) {
        if ($totalnum2 >= $level['num']) {
            $freightPrice = $level['price'];
            $applicableLevel = $level;
        }
    }

    // 计算总运费和提示信息
    $rdata['single_freight'] = intval($freightPrice);
    $rdata['total_freight'] = $freightPrice * $totalnum2;

    // 提示下一个运费等级
    $next_level = null;
    foreach ($freight_levels as $level) {
        if ($totalnum2 < $level['num']) {
            $next_level = $level;
            break;
        }
    }

    // 根据是否存在下一个运费等级构建提示信息
    if ($next_level) {
        $remaining_boxes = $next_level['num'] - $totalnum2;
        $rdata['tip_freight'] = "当前订单共{$totalnum2}框，每框运费为{$freightPrice}元。再购买{$remaining_boxes}框，运费降至{$next_level['price']}元每框。";
    } else {
        $rdata['tip_freight'] = "当前订单共{$totalnum2}框，每框运费为{$freightPrice}元。已达到最低运费标准。";
    }
}

		return $this->json($rdata);
	}
       
		
		foreach($buydata as $data){
		    
			$i++;
			$bid = $data['bid'];
			if($data['prodata']){
				$prodata = explode('-',$data['prodata']);
			}else{
				return $this->json(['status'=>0,'msg'=>'产品数据错误']);
			}
			$product_priceArr = [];
			$product_price = 0;
			$balance_price = 0;
			$needzkproduct_price = 0;
			$givescore = 0; //奖励积分 确认收货后赠送
			$givescore2 = 0; //奖励积分2 付款后赠送
			$givescorehuang = 0;//奖励红包 确认收货后赠送
			$givescoregongxianzhi = 0;//奖励gongxianzhi 确认收货后赠送
			$totalweight = 0;//重量
			$totalnum = 0;
			$prolist = [];
			$proids = [];
			$cids = [];
			$invoice = [];

            if(getcustom('pay_yuanbao')) {
                $total_yuanbao   = 0;//总元宝价格
                $have_no_yuanbao = 0;//是否有非元宝商品
            }

            if($bid)
                $store_info = Db::name('business')->where('aid',aid)->where('id',$bid)->find();
            else
                $store_info = Db::name('admin_set')->where('aid',aid)->find();
            $store_name = $store_info['name'];

            $freightList = \app\model\Freight::getList([['status','=',1],['aid','=',aid],['bid','=',$bid]]);

			$fids = [];
			foreach($freightList as $v){
				$fids[] = $v['id'];
			}
			//			dd($data['prodataList']);
			$extendInput = [];
            //符合满减的商品价格
            $mj_price = 0;
			foreach($prodata as $key=>$pro){
				$sdata = explode(',',$pro);
				$sdata[2] = intval($sdata[2]);
				if(isset($sdata[3]))
				{
				    $tid=$sdata[3];
				}else{
				   $tid = 0;
				}
				//
				if(isset($sdata[4]))
				{
				    $dhtid=$sdata[4];
				}else{
				    $dhtid = 0;
				}
				if($sdata[2] <= 0) return $this->json(['status'=>0,'msg'=>'购买数量有误']);
				$product = Db::name('shop_product')->where('aid',aid)->where('ischecked',1)->where('bid',$bid)->where('id',$sdata[0])->find();
				if(!$product) return $this->json(['status'=>0,'msg'=>'产品不存在或已下架']);
				
				if($product['status']==0){
					return $this->json(['status'=>0,'msg'=>'商品未上架']);
				}
				if($product['status']==2 && (strtotime($product['start_time']) > time() || strtotime($product['end_time']) < time())){
					return $this->json(['status'=>0,'msg'=>'商品未上架']);
				}
				if($product['status']==3){
					$start_time = strtotime(date('Y-m-d '.$product['start_hours']));
					$end_time = strtotime(date('Y-m-d '.$product['end_hours']));
					if(($start_time < $end_time && ($start_time > time() || $end_time < time())) || ($start_time >= $end_time && ($start_time > time() && $end_time < time()))){
						return $this->json(['status'=>0,'msg'=>'商品未上架']);
					}
				}
	
				if(getcustom('shopcate_time')){
					$procids = explode(',',$product['cid']);
					$cate = Db::name('shop_category')->where('aid',aid)->where('id','in',$product['cid'])->select()->toArray();
					foreach($cate as $c){
						if($c['start_hours'] && $c['end_hours']){
							$catestart_time =  strtotime(date('Y-m-d '.$c['start_hours']));
							$cateend_time =  strtotime(date('Y-m-d '.$c['end_hours']));
							if(($catestart_time < $cateend_time && ($catestart_time > time() || $cateend_time < time())) || ($catestart_time >= $cateend_time && ($catestart_time > time() && $cateend_time < time()))){
								return $this->json(['status'=>0,'msg'=>'商品购买时间'.$c['start_hours'].'-'.$c['end_hours'].'请稍后再来']);
							}
						}
						
					}
				}


				
				if($key==0) $title = $product['name'];

				$guige = Db::name('tuanzhang_guige')->where('aid',aid)->where('id',$sdata[1])->find();
				
				$tuanzhangid = $guige['bid'];
				
				
				
				if(!$guige) return $this->json(['status'=>0,'msg'=>'产品规格不存在或已下架']);
				if($guige['stock'] < $sdata[2]){
					return $this->json(['status'=>0,'msg'=>'库存不足']);
				}
				$gettj = explode(',',$product['gettj']);
				if(!in_array('-1',$gettj) && !in_array($this->member['levelid'],$gettj) && (!in_array('0',$gettj) || $this->member['subscribe']!=1)){ //不是所有人
					if(!$product['gettjtip']) $product['gettjtip'] = '没有权限购买该商品';
					return $this->json(['status'=>0,'msg'=>$product['gettjtip'],'url'=>$product['gettjurl']]);
				}
				if($product['perlimit'] > 0){
					$buynum = $sdata[2] + Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('proid',$product['id'])->where('status','in','0,1,2,3')->sum('num');
					if($buynum > $product['perlimit']){
						return $this->json(['status'=>0,'msg'=>'每人限购'.$product['perlimit'].'件']);
					}
				}
                if($guige['limit_start'] > 0 && $sdata[2] < $guige['limit_start']){
                    return $this->json(['status'=>0,'msg'=>'['.$product['name'].']['.$guige['name'].'] '.$guige['limit_start'].'件起售']);
                }
				if($product['limit_start'] > 0 && $sdata[2] < $product['limit_start']){
					return $this->json(['status'=>0,'msg'=>$product['limit_start'].'件起售']);
				}
				if($product['perlimitdan'] > 0 && $sdata[2] > $product['perlimitdan']){
					return $this->json(['status'=>0,'msg'=>'['.$product['name'].'] 每单限购'.$product['perlimitdan'].'件']);
				}
                if(getcustom('plug_tengrui')) {
                    //判断是否是否符合会员认证、会员关系、一户、用户组
                    $tr_check = new \app\common\TengRuiCheck();
                    $check_product = $tr_check->check_product($this->member,$product);
                    if($check_product && $check_product['status'] == 0){
                        return $this->json(['status'=>$check_product['status'],'msg'=>$check_product['msg']]);
                    }
                    $tr_roomId = $check_product['tr_roomId'];
                    $product['tr_roomId'] = $tr_roomId;
                }
				if($product['lvprice']==1) $guige = $this->formatguige($guige,$product['bid']);
				if(getcustom('plug_xiongmao') && $data['prodataList']) {
				    //自定义价格
                    if($data['prodataList'][$key]['guige']['sell_price'] < $guige['sell_price']) {
                        return $this->json(['status'=>0,'msg'=>'"'.$product['name'].'"不能小于原价']);
                    }
                    $guige['sell_price'] = $data['prodataList'][$key]['guige']['sell_price'];
                }
                // if($iszuhe == 1)
                // {
                //     if($product['kuncun_type'] == 1){
                //         $product_price += $guige['sell_price'] * $sdata[2];  
                //     }
                // }else{
                //      $product_price += ($guige['sell_price'] * $sdata[2])+($product['diy_amount']*$sdata[2]);
                // }

				  
                
                //新增判断是否是优惠
                if($product['is_newcustom'] == 1 && $this->member['is_yh'] == 0){
                    //查询优惠
                    $buynum = Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('proid',$product['id'])->where('status','in','0,1,2,3')->sum('num');
                    // $new_yh = ($product['yh_num'] - $buynum);
                    //如果用户购买的次数小于优惠次数则替换
                    if($product['yh_num'] >= ($buynum+$sdata[2]) && $product['yh_num'] > 0){
                       $product_price += ($guige['sell_price'] * $sdata[2])+($product['diy_amount']*$sdata[2]) - (($guige['sell_price'] - $product['yh_price']) * $sdata[2]);//这里是计算优惠
                    }else{
                        //给予正常价格购买的数量
                        $zcnums = $sdata[2] - ($product['yh_num'] - $buynum);//价格为正常价格的产品
                        $new_yh = ($product['yh_num'] - $buynum);
                        $product_price += ($guige['sell_price'] * $zcnums) + ($product['diy_amount']*$sdata[2]) + ($new_yh * $product['yh_price']);
                    }
                }else{
                    $product_price += ($guige['sell_price'] * $sdata[2])+($product['diy_amount']*$sdata[2]);//这里是计算优惠
                }
                //新增结束
				if($product['balance']){
					$balance_price += $product_price * $product['balance']*0.01;
					$product_price = $product_price * (1-$product['balance']*0.01);
				}
				$product_priceArr[] = $guige['sell_price'] * $sdata[2];
				if($product['lvprice']==0 && $product['no_discount'] == 0){ //未开启会员价
					$needzkproduct_price += $guige['sell_price'] * $sdata[2];
				}
				$totalweight += $guige['weight'] * $sdata[2];
				$totalnum += $sdata[2];
				
				if($bid!=0)
				{
				    	$s= $bid.'-'.$product_price;
				        array_push($single_s_info,$s); //
				}
				if($product['scoredkmaxset']==0){
					if($sysset['scoredkmaxpercent'] == 0){
						$scoredkmaxmoney += 0;
					}else{
						if($sysset['scoredkmaxpercent'] > 0 && $sysset['scoredkmaxpercent']<100){
							$scoredkmaxmoney += $sysset['scoredkmaxpercent'] * 0.01 * $guige['sell_price'] * $sdata[2];
						}else{
							$scoredkmaxmoney += $guige['sell_price'] * $sdata[2];
						}
					}
				}elseif($product['scoredkmaxset']==1){
					$scoremaxtype = 1;
					$scoredkmaxmoney += $product['scoredkmaxval'] * 0.01 * $guige['sell_price'] * $sdata[2];
				}elseif($product['scoredkmaxset']==2){
					$scoremaxtype = 1;
					$scoredkmaxmoney += $product['scoredkmaxval'] * $sdata[2];
				}else{
					$scoremaxtype = 1;
					$scoredkmaxmoney += 0;
				}
				
				
				if($product['scoredkmaxsethei']==0){
    				if($sysset['scoredkmaxpercenthei'] == 0){
    					$scoremaxtypehei = 1;
    					$scoredkmaxmoneyhei += 0;
    				}else{
    					if($sysset['scoredkmaxpercenthei'] > 0 && $sysset['scoredkmaxpercenthei']<100){
    						$scoredkmaxmoneyhei += $sysset['scoredkmaxpercenthei'] * 0.01 * $guige['sell_price']* $sdata[2];
    					}else{
    						$scoredkmaxmoneyhei += $guige['sell_price'] * $sdata[2];
    					}
    				}
    			}elseif($product['scoredkmaxsethei']==1){
    				$scoremaxtypehei = 1;
    				$scoredkmaxmoneyhei += $product['scoredkmaxvalhei'] * 0.01 * $guige['sell_price'] * $sdata[2];
    			}elseif($product['scoredkmaxsethei']==2){
    				$scoremaxtypehei = 1;
    				$scoredkmaxmoneyhei += $product['scoredkmaxvalhei'] * $sdata[2];
    			}else{
    				$scoremaxtypehei = 1;
    				$scoredkmaxmoneyhei += 0;
    			}
    			
    			
    			//红包
    			if($product['scoredkmaxsethuang']==0){
    				if($sysset['scoredkmaxpercenthuang'] == 0){
    					$scoremaxtypehuang = 1;
    					$scoredkmaxmoneyhuang += 0;
    				}else{
    					if($sysset['scoredkmaxpercenthuang'] > 0 && $sysset['scoredkmaxpercenthuang']<100){
    						$scoredkmaxmoneyhuang += $sysset['scoredkmaxpercenthuang'] * 0.01 * $guige['sell_price']* $sdata[2];
    					}else{
    						$scoredkmaxmoneyhuang += $guige['sell_price'] * $sdata[2];
    					}
    				}
    			}elseif($product['scoredkmaxsethuang']==1){
    				$scoremaxtypehuang = 1;
    				$scoredkmaxmoneyhuang += $product['scoredkmaxunithuang'] * 0.01 * $guige['sell_price'] * $sdata[2];
    			}elseif($product['scoredkmaxsethuang']==2){
    				$scoremaxtypehuang = 1;
    				$scoredkmaxmoneyhuang += $product['scoredkmaxunithuang'] * $sdata[2];
    			}else{
    				$scoremaxtypehuang = 1;
    				$scoredkmaxmoneyhuang += 0;
    			}


                //视力档案
                $glass_record_id = 0;
                if(getcustom('product_glass')){
                    $glass_record_id = $sdata[3]??0;
                }

				$prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>$sdata[2],'tid'=>$tid,'dhtid'=>$dhtid,'isSeckill'=>0,'glass_record_id'=>$glass_record_id];
				
				if($product['freighttype']==0){
					$fids = array_intersect($fids,explode(',',$product['freightdata']));
				}else{
					$thisfreightList = \app\model\Freight::getList([['status','=',1],['aid','=',aid],['bid','=',$bid]]);
					$thisfids = [];
					foreach($thisfreightList as $v){
						$thisfids[] = $v['id'];
					}
					$fids = array_intersect($fids,$thisfids);
				}
				$proids[] = $product['id'];
				$cids = array_merge($cids,explode(',',$product['cid']));
				if($product['givescore_time'] == 0){
					$givescore += $guige['givescore'] * $sdata[2];
					$givescorehuang+=$guige['givescorehuang'] * $sdata[2];
					$givescoregongxianzhi+=$guige['givescoregongxianzhi'] * $sdata[2];
				}else{
					$givescore2 += $guige['givescore'] * $sdata[2];
				}

				if($product['to86yk_tid']){
					$extendInput = [['key'=>'input','val1'=>'充值账号','val2'=>'请输入充值账号','val3'=>1],['key'=>'input','val1'=>'确认账号','val2'=>'请再次输入充值账号','val3'=>1]];
				}
                if(getcustom('pay_yuanbao') ){
                    if($product['yuanbao']>0){
                        $total_yuanbao += $product['yuanbao'];
                    }else{
                        $have_no_yuanbao = 1;
                    }
                }
                //如果存在，且数据存在
                if($mjset && $mjdata){
                    //指定分类
                    if($mjset['fwtype']==1){
                        //指定分类数组
                        $cat_arr     = explode(",",$mjset['categoryids']);
                        //商品分类
                        $pro_cat_arr = explode(",",$product['cid']);
                        //交集
                        $j_arr = array_intersect($cat_arr,$pro_cat_arr);
                        if ($j_arr){
                            $mj_price += $guige['sell_price'] * $sdata[2];
                            if($product['balance']){
                                $mj_price = $mj_price * (1-$product['balance']*0.01);
                            }
                        }
                    //指定商品
                    }else if($mjset['fwtype']==2){
                        $pro_arr = explode(",",$mjset['productids']);
                        //商品在指定商品内
                        if (in_array($product['id'], $pro_arr)){
                            $mj_price += $guige['sell_price'] * $sdata[2];
                            if($product['balance']){
                                $mj_price = $mj_price * (1-$product['balance']*0.01);
                            }
                        }
                    }else{
                        $mj_price += $guige['sell_price'] * $sdata[2];
                        if($product['balance']){
                            $mj_price = $mj_price * (1-$product['balance']*0.01);
                        }
                    }
                }
			}
			if(!$fids){
				if(count($buydata['prodata'])>1){
					return $this->json(['status'=>0,'msg'=>'所选择商品配送方式不同，请分别下单']);
				}else{
					return $this->json(['status'=>0,'msg'=>'获取配送方式失败']);
				}
			}
			if(getcustom('guige_split')){
				$rs = \app\model\ShopProduct::checkstock($prolist);
				if($rs['status'] == 0) return $this->json($rs);
			}
			//会员折扣
			$leveldk_money = 0;
			if($userlevel && $userlevel['discount']>0 && $userlevel['discount']<10){
				$leveldk_money = $needzkproduct_price * (1 - $userlevel['discount'] * 0.1);
			}
			$totalprice = $product_price - $leveldk_money;
			
            $totalFreight = 0;
            $mj_price   = $mj_price - $leveldk_money;

            if($userlevel['freight']){
                $level = json_decode($userlevel['freight'],true);
                usort($level,function ($a,$b){
                    return $a['num'] < $b['num'];
                });
                // $maxLevel = 0;
                //
                $freightPrice = $level[count($level)-1]['price'];
                foreach ($level as $one){
                    if($totalnum >= $one['num']){
                        $freightPrice = $one['price'];
                        break;
                    }
                }
                $totalFreight = $freightPrice *$totalnum;
                $totalprice +=$totalFreight;

            }


			$manjian_money = 0;
			$moneyduan = 0;
			if($mjdata && $mj_price>0){
                //如果是总满减
                if($mjset['total_status']==1){
                    //指定分类
                    if($mjset['fwtype']==1){
                        //查询他分类消费累计
                        $sum_money  = Db::name('shop_order_goods')
                            ->alias('sog')
                            ->join('shop_order so','so.id   = sog.orderid')
                            ->join('shop_product sp','sp.id = sog.proid')
                            ->where('sog.mid',mid)
                            ->where('so.status',3)
                            ->where('sp.cid','in',$mjset['categoryids'])
                            ->sum('sog.totalprice');

                        //分类退款累计
                        $refund_money = Db::name('shop_refund_order_goods')
                            ->alias('srog')
                            ->join('shop_order so','so.id   = srog.orderid')
                            ->join('shop_product sp','sp.id = srog.proid')
                            ->where('srog.mid',mid)
                            ->where('so.status',3)
                            ->where('so.refund_status',2)
                            ->where('sp.cid','in',$mjset['categoryids'])
                            ->sum('srog.refund_money');

                    //指定商品
                    }else if($mjset['fwtype']==2){
                        //查询他商品消费累计
                        $sum_money  = Db::name('shop_order_goods')
                            ->alias('sog')
                            ->join('shop_order so','so.id   = sog.orderid')
                            ->join('shop_product sp','sp.id = sog.proid')
                            ->where('sog.mid',mid)
                            ->where('so.status',3)
                            ->where('sog.proid','in',$mjset['productids'])
                            ->sum('sog.totalprice');

                        //商品退款累计
                        $refund_money = Db::name('shop_refund_order_goods')
                            ->alias('srog')
                            ->join('shop_order so','so.id   = srog.orderid')
                            ->join('shop_product sp','sp.id = srog.proid')
                            ->where('srog.mid',mid)
                            ->where('so.status',3)
                            ->where('so.refund_status',2)
                            ->where('srog.proid','in',$mjset['productids'])
                            ->sum('srog.refund_money');
                    //所有
                    }else{
                        //查询他累计消费多少
                        $sum_money    = Db::name('shop_order')->where('mid',mid)->where('status',3)->sum('totalprice');
                        $refund_money = Db::name('shop_order')->where('mid',mid)->where('status',3)->where('refund_status',2)->sum('refund_money');
                    }
                    $sj_money = $sum_money-$refund_money;
                    $sj_money = round($sj_money,2);
                    $all_price = $sj_money+$mj_price;
                    foreach($mjdata as $give){
                        if($all_price*1 >= $give['money']*1 && $give['money']*1 > $moneyduan){
                            $moneyduan = $give['money']*1;
                            $manjian_money = $give['jian']*1;
                        }
                    }
                }else{
    				foreach($mjdata as $give){
    					if($mj_price*1 >= $give['money']*1 && $give['money']*1 > $moneyduan){
    						$moneyduan = $give['money']*1;
    						$manjian_money = $give['jian']*1;
    					}
    				}
                }
			}
			if($manjian_money <= 0) $manjian_money = 0;
			$totalprice = $totalprice - $manjian_money;
			if($totalprice < 0) $totalprice = 0;

			//运费
			$freight_price = 0;
			if($data['freight_id']){
				$freight = Db::name('freight')->where('aid',aid)->where('id',$data['freight_id'])->find();
				if($freight['pstype']==11){
					$freight['type11key'] = $data['type11key'];
				}
				if($freight['minpriceset']==1 && $freight['minprice']>0 && $freight['minprice'] > $product_price){
					return $this->json(['status'=>0,'msg'=>$freight['name'] . '满'.$freight['minprice'].'元起送']);
				}
				if(($address['name']=='' || $address['tel'] =='') && ($freight['pstype']==1 || $freight['pstype']==3) && $freight['needlinkinfo']==1){
					return $this->json(['status'=>0,'msg'=>'请填写联系人和联系电话']);
				}
				
				$rs = \app\model\Freight::getFreightPrice($freight,$address,$product_price,$totalnum,$totalweight);
				if($rs['status']==0) return $this->json($rs);
				$freight_price = $rs['freight_price'];
				//判断配送时间选择是否符合要求
				if($freight['pstimeset']==1){
					$freight_times = explode('~',$data['freight_time']);
					if($freight_times[1]){
						$freighttime = strtotime(explode(' ',$freight_times[0])[0] . ' '.$freight_times[1]);
					}else{
						$freighttime = strtotime($freight_times[0]);
					}
					if(time() + $freight['psprehour']*3600 > $freighttime){
						return $this->json(['status'=>0,'msg'=>(($freight['pstype']==0 || $freight['pstype']==2 || $freight['pstype']==10)?'配送':'提货').'时间必须在'.$freight['psprehour'].'小时之后']);
					}
				}
			}elseif($product['freighttype']==3){
				$freight = ['id'=>0,'name'=>'自动发货','pstype'=>3];
				if(getcustom('cefang') && ($address['name']=='' || $address['tel'] =='')){
					return $this->json(['status'=>0,'msg'=>'请填写联系人和联系电话']);
				}
			}elseif($product['freighttype']==4){
				$freight = ['id'=>0,'name'=>'在线卡密','pstype'=>4];
				if(getcustom('cefang') && ($address['name']=='' || $address['tel'] =='')){
					return $this->json(['status'=>0,'msg'=>'请填写联系人和联系电话']);
				}
			}else{
				$freight = ['id'=>0,'name'=>'包邮','pstype'=>0];
			}
			//优惠券
			$new_freight_price = $freight_price;
			$coupon_money = 0;
			if($data['couponrid']){
				$couponrids = explode(',',$data['couponrid']);
				foreach($couponrids as $couponrid){
					$couponrecord = Db::name('coupon_record')->where("bid=-1 or bid=".$data['bid'])->where('aid',aid)->where('mid',mid)->where('id',$couponrid)->find();
					if(!$couponrecord){
						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不存在']);
					}elseif($couponrecord['status']!=0){
						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'已使用过了']);
					}elseif($couponrecord['starttime'] > time()){
						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'尚未开始使用']);	
					}elseif($couponrecord['endtime'] < time()){
						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'已过期']);	
					}elseif($couponrecord['minprice'] > $totalprice){
						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不符合条件']);
					}elseif($couponrecord['type']!=1 && $couponrecord['type']!=4 && $couponrecord['type']!=10){
						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不符合条件']);
					}

					$couponinfo = Db::name('coupon')->where('aid',aid)->where('id',$couponrecord['couponid'])->find();
                    if(empty($couponinfo)){
                        return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不存在或已作废']);
                    }
                    //0全场通用,1指定类目,2指定商品
                    if(!in_array($couponinfo['fwtype'],[0,1,2])){
                        return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'超出可用范围']);
                    }
					if($couponrecord['from_mid']==0 && $couponinfo && $couponinfo['isgive']==2){
						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'仅可转赠']);
					}
					$usetj = explode(',',$couponinfo['usetj']);
					if(!in_array('-1',$usetj) && !in_array($this->member['levelid'],$usetj) && (!in_array('0',$usetj) || $this->member['subscribe']!=1)){
						return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'不可用']);
					}
					if($couponinfo['fwtype']==2){//指定商品可用
						$productids = explode(',',$couponinfo['productids']);
						if(!array_intersect($proids,$productids)){
							return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'指定商品可用']);
						}
						$thistotalprice = 0;
						foreach($prolist as $k2=>$v2){
							$product = $v2['product'];
							if(in_array($product['id'],$productids)){
								$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
							}
						}
						if($thistotalprice < $couponinfo['minprice']){
							return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'指定商品��达到'.$couponinfo['minprice'].'元']);
						}
					}
					if($couponinfo['fwtype']==1){//指定类目可用
						$categoryids = explode(',',$couponinfo['categoryids']);
						$clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
						foreach($clist as $kc=>$vc){
							$categoryids[] = $vc['id'];
							$cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
							$categoryids[] = $cate2['id'];
						}
						if(!array_intersect($cids,$categoryids)){
							return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'指定分类可用']);
						}
						$thistotalprice = 0;
						foreach($prolist as $k2=>$v2){
							$product = $v2['product'];
							if(array_intersect(explode(',',$product['cid']),$categoryids)){
								$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
							}
						}
						if($thistotalprice < $couponinfo['minprice']){
							return $this->json(['status'=>0,'msg'=>'该'.t('优惠券').'指定分类未达到'.$couponinfo['minprice'].'元']);
						}
					}

					Db::name('coupon_record')->where('id',$couponrid)->update(['status'=>1,'usetime'=>time()]);
					if($couponrecord['type']==4){//运费抵扣券
						$new_freight_price = 0;
					}elseif($couponrecord['type']==10){//折扣券
						if($couponinfo['fwtype']==1 || $couponinfo['fwtype']==2){
							$coupon_money += $thistotalprice * (100 - $couponrecord['discount']) * 0.01;
						}else{
							$coupon_money += $totalprice * (100 - $couponrecord['discount']) * 0.01;
						}
						if($coupon_money > $totalprice) $coupon_money = $totalprice;
					}else{
						$coupon_money += $couponrecord['money'];
						if($coupon_money > $totalprice) $coupon_money = $totalprice;
					}
				}
			}
			//促销活动
            $cuxiaomoney = 0;
            if(in_array('multi_promotion',getcustom())){
                if($data['cuxiaoid']) {
                    foreach ($data['cuxiaoid'] as $cuxiaoid) {
                        if($cuxiaoid > 0){
                            $cuxiaoid = $cuxiaoid;
                            $cuxiaoinfo = Db::name('cuxiao')->where("bid=-1 or bid=".$data['bid'])->where('aid',aid)->where('id',$cuxiaoid)->find();
                            if(!$cuxiaoinfo){
                                return $this->json(['status'=>0,'msg'=>'该促销活动不存在']);
                            }elseif($cuxiaoinfo['starttime'] > time()){
                                return $this->json(['status'=>0,'msg'=>'该促销活动尚未开始']);
                            }elseif($cuxiaoinfo['endtime'] < time()){
                                return $this->json(['status'=>0,'msg'=>'该促销活动已结束']);
                            }elseif($cuxiaoinfo['type']!=5 && $cuxiaoinfo['type']!=6 && $cuxiaoinfo['minprice'] > $totalprice){
                                return $this->json(['status'=>0,'msg'=>'该促销活动不符合条件']);
                            }elseif(($cuxiaoinfo['type']==5 || $cuxiaoinfo['type']==6) && $cuxiaoinfo['minnum'] > $totalnum){
                                return $this->json(['status'=>0,'msg'=>'该促销活动不符合条件']);
                            }
                            if($cuxiaoinfo['fwtype']==2){//指定商品可用
                                $productids = explode(',',$cuxiaoinfo['productids']);
                                if(!array_intersect($proids,$productids)){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动指定商品可用']);
                                } 
                                if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==2 || $cuxiaoinfo['type']==3 || $cuxiaoinfo['type']==4){//指定商品是否达到金额要求
                                    $thistotalprice = 0;
                                    foreach($prolist as $k2=>$v2){
                                        $product = $v2['product'];
                                        if(in_array($product['id'],$productids)){
                                            $thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
                                        }
                                    }
                                    if($thistotalprice < $cuxiaoinfo['minprice']){
                                        return $this->json(['status'=>0,'msg'=>'该促销活动指定商品总价未达到'.$cuxiaoinfo['minprice'].'元']);
                                    }
                                }
                                if($cuxiaoinfo['type']==6 || $cuxiaoinfo['type']==5){//指定商品是否达到件数要求
                                    $thistotalnum = 0;
                                    foreach($prolist as $k2=>$v2){
                                        $product = $v2['product'];
                                        if(in_array($product['id'],$productids)){
                                            $thistotalnum += $v2['num'];
                                        }
                                    }
                                    if($thistotalnum < $cuxiaoinfo['minnum']){
                                        return $this->json(['status'=>0,'msg'=>'该促销活动指定商品总数未达到'.$cuxiaoinfo['minnum'].'件']);
                                    }
                                }
                            }
                            if($cuxiaoinfo['fwtype']==1){//指定类目可用
                                $categoryids = explode(',',$cuxiaoinfo['categoryids']);
                                $clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
                                foreach($clist as $kc=>$vc){
                                    $categoryids[] = $vc['id'];
                                    $cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
                                    $categoryids[] = $cate2['id'];
                                }
                                if(!array_intersect($cids,$categoryids)){
                                    return $this->json(['status'=>0,'msg'=>'该促销活动指定分类可用']);
                                }
								if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==2 || $cuxiaoinfo['type']==3 || $cuxiaoinfo['type']==4){//指定商品是否达到金额要求
                                    $thistotalprice = 0;
                                    foreach($prolist as $k2=>$v2){
                                        $product = $v2['product'];
                                        if(array_intersect(explode(',',$product['cid']),$categoryids)){
                                            $thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
                                        }
                                    }
                                    if($thistotalprice < $cuxiaoinfo['minprice']){
                                        return $this->json(['status'=>0,'msg'=>'该促销活动指定分类总价未达到'.$cuxiaoinfo['minprice'].'元']);
                                    }
                                }
                                if($cuxiaoinfo['type']==6 || $cuxiaoinfo['type']==5){//指定类目内商品是否达到件数要求
                                    $thistotalnum = 0;
                                    foreach($prolist as $k2=>$v2){
                                        $product = $v2['product'];
                                        if(array_intersect(explode(',',$product['cid']),$categoryids)){
                                            $thistotalnum += $v2['num'];
                                        }
                                    }
                                    if($thistotalnum < $cuxiaoinfo['minnum']){
                                        return $this->json(['status'=>0,'msg'=>'该促销活动指定分类总数未达到'.$cuxiaoinfo['minnum'].'件']);
                                    }
                                }
                            }
                            if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==6){//满额立减 满件立减
                                $manjian_money = $manjian_money + $cuxiaoinfo['money'];
                                $cuxiaomoney += $cuxiaoinfo['money'] * -1;
                            }elseif($cuxiaoinfo['type']==2){//满额赠送
                                $cuxiaomoney += 0;
                                $product = Db::name('shop_product')->where('aid',aid)->where('id',$cuxiaoinfo['proid'])->find();
                                $guige = Db::name('shop_guige')->where('aid',aid)->where('id',$cuxiaoinfo['ggid'])->find();
                                if(!$product) return $this->json(['status'=>0,'msg'=>'赠送产品不存在']);
                                if(!$guige) return $this->json(['status'=>0,'msg'=>'赠送产品规格不存在']);
                                if($guige['stock'] < 1){
                                    return $this->json(['status'=>0,'msg'=>'赠送产品库存不足']);
                                }
                                $prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>1,'isSeckill'=>0];
                            }elseif($cuxiaoinfo['type']==3){//加价换购
                                $cuxiaomoney += $cuxiaoinfo['money'];
                                $product = Db::name('shop_product')->where('aid',aid)->where('id',$cuxiaoinfo['proid'])->find();
                                $guige = Db::name('shop_guige')->where('aid',aid)->where('id',$cuxiaoinfo['ggid'])->find();
                                if(!$product) return $this->json(['status'=>0,'msg'=>'换购产品不存在']);
                                if(!$guige) return $this->json(['status'=>0,'msg'=>'换购产品规格不存在']);
                                if($guige['stock'] < 1){
                                    return $this->json(['status'=>0,'msg'=>'换购产品库存不足']);
                                }
                                $prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>1,'isSeckill'=>0];
                            }elseif($cuxiaoinfo['type']==4 || $cuxiaoinfo['type']==5){//满额打折 满件打折
                                $cuxiaomoney4 = 0;
                                if($cuxiaoinfo['fwtype']==2){
                                    $prozkArr = array_combine(explode(',',$cuxiaoinfo['productids']),explode(',',$cuxiaoinfo['prozk']));
                                    $pronumArr = array_combine(explode(',',$cuxiaoinfo['productids']),explode(',',$cuxiaoinfo['pronum']));
                                    foreach($prolist as $k=>$v){
                                        $product = $v['product'];
                                        if($prozkArr[$product['id']]){
                                            $prozk = $prozkArr[$product['id']];
                                        }elseif(isset($prozkArr[$product['id']])){
                                            $prozk = $cuxiaoinfo['zhekou'];
                                        }else{
                                            $prozk = 10;
                                        }
                                        if($cuxiaoinfo['type']==5 && $pronumArr[$product['id']] && intval($pronumArr[$product['id']]) > $v['num']){
                                            $prozk = 10;
                                        }
                                        $cuxiaomoney4 += $product_priceArr[$k] * (1 - $prozk * 0.1);
                                    }
                                }elseif($cuxiaoinfo['fwtype']==1) {
                                    //分类
                                    $categoryPrice = 0;

                                    foreach ($prolist as $k2 => $v2) {
                                        $product = $v2['product'];
                                        $cids2 = explode(',', $product['cid']);
                                        if(array_intersect($cids2, $categoryids)) {
                                            $categoryPrice += $v2['guige']['sell_price'] * $v2['num'];
                                        }
                                    }
                                    $cuxiaomoney4 = $categoryPrice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
                                }else{
                                    $cuxiaomoney4 = $totalprice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
                                }
                                $cuxiaomoney4 = round($cuxiaomoney4,2);
                                $manjian_money = $manjian_money + $cuxiaomoney4;
                                $cuxiaomoney += $cuxiaomoney4 * -1;
                            }else{
                                $cuxiaomoney += 0;
                            }
                        }else{
                            $cuxiaomoney += 0;
                        }
                    }
                }
            } else {
                if($data['cuxiaoid'] > 0){
                    $cuxiaoid = $data['cuxiaoid'];
                    $cuxiaoinfo = Db::name('cuxiao')->where("bid=-1 or bid=".$data['bid'])->where('aid',aid)->where('id',$cuxiaoid)->find();
                    if(!$cuxiaoinfo){
                        return $this->json(['status'=>0,'msg'=>'该促销活动不存在']);
                    }elseif($cuxiaoinfo['starttime'] > time()){
                        return $this->json(['status'=>0,'msg'=>'该促销活动尚未开始']);
                    }elseif($cuxiaoinfo['endtime'] < time()){
                        return $this->json(['status'=>0,'msg'=>'该促销活动已结束']);
                    }elseif($cuxiaoinfo['type']!=5 && $cuxiaoinfo['type']!=6 && $cuxiaoinfo['minprice'] > $totalprice){
                        return $this->json(['status'=>0,'msg'=>'该促销活动不符合条件']);
                    }elseif(($cuxiaoinfo['type']==5 || $cuxiaoinfo['type']==6) && $cuxiaoinfo['minnum'] > $totalnum){
                        return $this->json(['status'=>0,'msg'=>'该促销活动不符合条件']);
                    }
                    if($cuxiaoinfo['fwtype']==2){//指定商品可用
                        $productids = explode(',',$cuxiaoinfo['productids']);
                        if(!array_intersect($proids,$productids)){
                            return $this->json(['status'=>0,'msg'=>'该促销活动指定商品可用']);
                        }
						if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==2 || $cuxiaoinfo['type']==3 || $cuxiaoinfo['type']==4){//指定商品是否达到金额要求
							$thistotalprice = 0;
							foreach($prolist as $k2=>$v2){
								$product = $v2['product'];
								if(in_array($product['id'],$productids)){
									$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
								}
							}
							if($thistotalprice < $cuxiaoinfo['minprice']){
								return $this->json(['status'=>0,'msg'=>'该促销活动指定商品未达到'.$cuxiaoinfo['minprice'].'元']);
							}
						}
                        if($cuxiaoinfo['type']==6 || $cuxiaoinfo['type']==5){//指定商品是否达到件数要求
                            $thistotalnum = 0;
                            foreach($prolist as $k2=>$v2){
                                $product = $v2['product'];
                                if(in_array($product['id'],$productids)){
                                    $thistotalnum += $v2['num'];
                                }
                            }
                            if($thistotalnum < $cuxiaoinfo['minnum']){
                                return $this->json(['status'=>0,'msg'=>'该促销活动指定商品未达到'.$cuxiaoinfo['minnum'].'件']);
                            }
                        }
                    }
                    if($cuxiaoinfo['fwtype']==1){//指定类目可用
                        $categoryids = explode(',',$cuxiaoinfo['categoryids']);
                        $clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
                        foreach($clist as $kc=>$vc){
                            $categoryids[] = $vc['id'];
                            $cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
                            $categoryids[] = $cate2['id'];
                        }
                        if(!array_intersect($cids,$categoryids)){
                            return $this->json(['status'=>0,'msg'=>'该促销活动指定分类可用']);
                        }
						if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==2 || $cuxiaoinfo['type']==3 || $cuxiaoinfo['type']==4){//指定商品是否达到金额要求
							$thistotalprice = 0;
							foreach($prolist as $k2=>$v2){
								$product = $v2['product'];
								if(array_intersect(explode(',',$product['cid']),$categoryids)){
									$thistotalprice += $v2['guige']['sell_price'] * $v2['num'];
								}
							}
							if($thistotalprice < $cuxiaoinfo['minprice']){
								return $this->json(['status'=>0,'msg'=>'该促销活动指定分类未达到'.$cuxiaoinfo['minprice'].'元']);
							}
						}
                        if($cuxiaoinfo['type']==6 || $cuxiaoinfo['type']==5){//指定类目内商品是否达到件数要求
                            $thistotalnum = 0;
                            foreach($prolist as $k2=>$v2){
                                $product = $v2['product'];
                                if(array_intersect(explode(',',$product['cid']),$categoryids)){
                                    $thistotalnum += $v2['num'];
                                }
                            }
                            if($thistotalnum < $cuxiaoinfo['minnum']){
                                return $this->json(['status'=>0,'msg'=>'该促销活动指定分类未达到'.$cuxiaoinfo['minnum'].'件']);
                            }
                        }
                    }
                    if($cuxiaoinfo['type']==1 || $cuxiaoinfo['type']==6){//满额立减 满件立减
                        $manjian_money = $manjian_money + $cuxiaoinfo['money'];
                        $cuxiaomoney = $cuxiaoinfo['money'] * -1;
                    }elseif($cuxiaoinfo['type']==2){//满额赠送
                        $cuxiaomoney = 0;
                        $product = Db::name('shop_product')->where('aid',aid)->where('id',$cuxiaoinfo['proid'])->find();
                        $guige = Db::name('shop_guige')->where('aid',aid)->where('id',$cuxiaoinfo['ggid'])->find();
                        if(!$product) return $this->json(['status'=>0,'msg'=>'赠送产品不存在']);
                        if(!$guige) return $this->json(['status'=>0,'msg'=>'赠送产品规格不存在']);
                        if($guige['stock'] < 1){
                            return $this->json(['status'=>0,'msg'=>'赠送产品库存不足']);
                        }
                        $prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>1,'isSeckill'=>0];
                    }elseif($cuxiaoinfo['type']==3){//加价换购
                        $cuxiaomoney = $cuxiaoinfo['money'];
                        $product = Db::name('shop_product')->where('aid',aid)->where('id',$cuxiaoinfo['proid'])->find();
                        $guige = Db::name('shop_guige')->where('aid',aid)->where('id',$cuxiaoinfo['ggid'])->find();
                        if(!$product) return $this->json(['status'=>0,'msg'=>'换购产品不存在']);
                        if(!$guige) return $this->json(['status'=>0,'msg'=>'换购产品规格不存在']);
                        if($guige['stock'] < 1){
                            return $this->json(['status'=>0,'msg'=>'换购产品库存不足']);
                        }
                        $prolist[] = ['product'=>$product,'guige'=>$guige,'num'=>1,'isSeckill'=>0];
                    }elseif($cuxiaoinfo['type']==4 || $cuxiaoinfo['type']==5){//满额打折 满件打折
                        if($cuxiaoinfo['fwtype']==2){
                            $cuxiaomoney = 0;
                            $prozkArr = array_combine(explode(',',$cuxiaoinfo['productids']),explode(',',$cuxiaoinfo['prozk']));
                            $pronumArr = array_combine(explode(',',$cuxiaoinfo['productids']),explode(',',$cuxiaoinfo['pronum']));
                            foreach($prolist as $k=>$v){
                                $product = $v['product'];
                                if($prozkArr[$product['id']]){
                                    $prozk = $prozkArr[$product['id']];
                                }elseif(isset($prozkArr[$product['id']])){
                                    $prozk = $cuxiaoinfo['zhekou'];
                                }else{
                                    $prozk = 10;
                                }
                                if($cuxiaoinfo['type']==5 && $pronumArr[$product['id']] && intval($pronumArr[$product['id']]) > $v['num']){
                                    $prozk = 10;
                                }
                                $cuxiaomoney += $product_priceArr[$k] * (1 - $prozk * 0.1);
                            }
                        }elseif($v['fwtype']==1) {
							//分类
							$categoryPrice = 0;
							foreach ($prolist as $k2 => $v2) {
								$product = $v2['product'];
								$cids2 = explode(',', $product['cid']);
								if(array_intersect($cids2, $categoryids)) {
									$categoryPrice += $v2['guige']['sell_price'] * $v2['num'];
								}
							}
							$cuxiaomoney = $categoryPrice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
						}else{
                            $cuxiaomoney = $totalprice * (1 - $cuxiaoinfo['zhekou'] * 0.1);
                        }
                        $cuxiaomoney = round($cuxiaomoney,2);
                        $manjian_money = $manjian_money + $cuxiaomoney;
                        $cuxiaomoney = $cuxiaomoney * -1;
                    }else{
                        $cuxiaomoney = 0;
                    }
                    if(getcustom('plug_tengrui')) {
                        $tr_check = new \app\common\TengRuiCheck();
                        //判断是否是否符合会员认证、会员关系、一户
                        $check_cuxiao = $tr_check->check_cuxiao($this->member,$cuxiaoinfo);
                        if($check_cuxiao && $check_cuxiao['status'] == 0){
                            $cuxiaomoney = 0;
                        }
                        $cuxiao_tr_roomId = $check_cuxiao['tr_roomId'];
                    }
                }else{
                    $cuxiaomoney = 0;
                }
            }

			$totalprice = $totalprice - $coupon_money + $cuxiaomoney;
			$totalprice = $totalprice + $new_freight_price;

            //发票
            $invoice_money = 0;
            if($store_info['invoice'] && $store_info['invoice_rate'] > 0 && $data['invoice']){
                $invoice_money = round($totalprice * $store_info['invoice_rate'] / 100,2);
                $totalprice = $totalprice + $invoice_money;
            }
			//积分抵扣
			$scoredkscore = 0;
			$scoredk_money = 0;
			if($post['usescore']==1){
				$adminset = Db::name('admin_set')->where('aid',aid)->find();
				$score2money = $adminset['score2money'];
				$scoredkmaxpercent = $adminset['scoredkmaxpercent'];
				$scorebdkyf = $adminset['scorebdkyf'];
				$scoredk_money = $this->member['score'] * $score2money;
				if($scorebdkyf == 1){//积分不抵扣运费
					if($scoredk_money > $totalprice - $new_freight_price) $scoredk_money = $totalprice - $new_freight_price;
				}else{
					if($scoredk_money > $totalprice) $scoredk_money = $totalprice;
				}
				if($scoremaxtype == 0){
					if($scoredkmaxpercent >= 0 && $scoredkmaxpercent < 100 && $scoredk_money > 0 && $scoredk_money > $totalprice * $scoredkmaxpercent * 0.01){
						$scoredk_money = $totalprice * $scoredkmaxpercent * 0.01;
					}
				}else{
					if($scoredk_money > $scoredkmaxmoney) $scoredk_money = $scoredkmaxmoney;
				}
				$totalprice = $totalprice - $scoredk_money;
				$totalprice = round($totalprice*100)/100;
				if($scoredk_money > 0){
					$scoredkscore = intval($scoredk_money / $score2money);
				}
			}
			
			//现金券抵扣
			$scoredkscorehei = 0;
			$scoredk_moneyhei = 0;
			if($post['usescorehei']==1){
				$adminset = Db::name('admin_set')->where('aid',aid)->find();
				$score2moneyhei = $adminset['score2moneyhei'];
				$scoredkmaxpercenthei = $adminset['scoredkmaxpercenthei'];
				$scorebdkyfhei = $adminset['scorebdkyfhei'];
				$scoredk_moneyhei = $this->member['heiscore'] * $score2moneyhei;
				if($scorebdkyfhei == 1){//积分不抵扣运费
					if($scoredk_moneyhei > $totalprice - $new_freight_price) $scoredk_moneyhei = $totalprice - $new_freight_price;
				}else{
					if($scoredk_moneyhei > $totalprice) $scoredk_moneyhei = $totalprice;
				}

				if($scoremaxtypehei == 0){
					if($scoredkmaxpercenthei >= 0 && $scoredkmaxpercenthei < 100 && $scoredk_moneyhei > 0 && $scoredk_moneyhei > $totalprice * $scoredkmaxpercenthei * 0.01){
						$scoredk_moneyhei = $totalprice * $scoredkmaxpercenthei * 0.01;
					}
				}else{
					if($scoredk_moneyhei > $scoredkmaxmoneyhei) $scoredk_moneyhei = $scoredkmaxmoneyhei;
				}
				$totalprice = $totalprice - $scoredk_moneyhei;
				$totalprice = round($totalprice*100)/100;
				if($scoredk_moneyhei > 0){
					$scoredkscorehei = intval($scoredk_moneyhei / $score2moneyhei);
				}
			}
			

	        //$scoremaxtypehuang
    		//$scoredkmaxmoneyhuang
            //红包抵扣
            $scoredkscorehuang = 0;
            $scoredk_moneyhuang = 0;
            if($post['usescorehuang']==1){
            
                $adminset = Db::name('admin_set')->where('aid',aid)->find();
                $score2moneyhuang = $adminset['score2moneyhuang'];
                $scoredkmaxpercenthuang = $adminset['scoredkmaxpercenthuang'];
                $scorebdkyfhuang = $adminset['scorebdkyfhuang'];
                $scoredk_moneyhuang = $this->member['scorehuang'] * $score2moneyhuang;
                if($scorebdkyfhuang == 1){//积分不抵扣运费
                    if($scoredk_moneyhuang > $totalprice - $new_freight_price) $scoredk_moneyhuang = $totalprice - $new_freight_price;
                }else{
                    if($scoredk_moneyhuang > $totalprice) $scoredk_moneyhuang = $totalprice;
                }

                if($scoremaxtypehuang == 0){
                    if($scoredkmaxpercenthuang >= 0 && $scoredkmaxpercenthuang < 100 && $scoredk_moneyhuang > 0 && $scoredk_moneyhuang > $totalprice * $scoredkmaxpercenthuang * 0.01){
                        $scoredk_moneyhuang = $totalprice * $scoredkmaxpercenthuang * 0.01;
                    }
                }else{
                    if($scoredk_moneyhuang > $scoredkmaxmoneyhuang) $scoredk_moneyhuang = $scoredkmaxmoneyhuang;
                }

                $totalprice = $totalprice - $scoredk_moneyhuang;
                $totalprice = round($totalprice*100)/100;
                if($scoredk_moneyhuang > 0){
                    $scoredkscorehuang = intval($scoredk_moneyhuang / $score2moneyhuang);
                }
            }
            
  


			//余额抵扣
			$scoredkscoreyu = 0;
			$scoredk_moneyyu = 0;
			if($post['usescoreyu']==1){
				$adminset = Db::name('admin_set')->where('aid',aid)->find();
				$score2moneyyu = $adminset['score2moneyyu'];
				$scoredkmaxpercentyu = $adminset['scoredkmaxpercentyu'];
				$scorebdkyfyu = $adminset['scorebdkyfyu'];
				$scoredk_moneyyu = $this->member['money'] * $score2moneyyu;
				if($scorebdkyfyu == 1){//积分不抵扣运费
					if($scoredk_moneyyu > $totalprice - $new_freight_price) $scoredk_moneyyu = $totalprice - $new_freight_price;
				}else{
					if($scoredk_moneyyu > $totalprice) $scoredk_moneyyu = $totalprice;
				}

				if($scoremaxtypeyu == 0){
					if($scoredkmaxpercentyu >= 0 && $scoredkmaxpercentyu < 100 && $scoredk_moneyyu > 0 && $scoredk_moneyyu > $totalprice * $scoredkmaxpercentyu * 0.01){
						$scoredk_moneyyu = $totalprice * $scoredkmaxpercentyu * 0.01;
					}
				}else{
					if($scoredk_moneyyu > $scoredkmaxmoneyyu) $scoredk_moneyyu = $scoredkmaxmoneyyu;
				}
				$totalprice = $totalprice - $scoredk_moneyyu;
				$totalprice = round($totalprice*100)/100;
				if($scoredk_moneyyu > 0){
					$scoredkscoreyu = intval($scoredk_moneyyu / $score2moneyyu);
				}
			}
			if(empty($member))
			{
			    $ylevelid = 0;
			}else{
			  $ylevelid  = $member['levelid'];  
			}
			
			
			$orderdata = [];
			$orderdata['aid'] = aid;
			$orderdata['ylevelid']=$ylevelid;
			$orderdata['mid'] = mid;
			$orderdata['bid'] = $data['bid'];
			if(count($buydata) > 1){
				$orderdata['ordernum'] = $ordernum.'_'.$i;
			}else{
				$orderdata['ordernum'] = $ordernum;
			}
			$orderdata['title'] = $title.(count($prodata)>1?'等':'');
			
			$orderdata['linkman'] = $address['name'];
            $orderdata['company'] = $address['company'];
			$orderdata['tel'] = $address['tel'];
			$orderdata['area'] = $address['area'];
			$orderdata['address'] = $address['address'];
			$orderdata['longitude'] = $address['longitude'];
			$orderdata['latitude'] = $address['latitude'];
			$orderdata['area2'] = $address['province'].','.$address['city'].','.$address['district'];
            //这里添加是否存在优惠的信息
            $orderdata['is_yh'] = $post['is_yh']?$post['is_yh']:0;//判断是否存在优惠情况
            $orderdata['ntext'] = $post['newarr']?json_encode($post['newarr']):0;//优惠详情放入
			$orderdata['totalprice'] = $totalprice;
            $orderdata['total_freight'] = $totalFreight;
			$orderdata['product_price'] = $product_price;
			$orderdata['leveldk_money'] = $leveldk_money;	//会员折扣
			$orderdata['manjian_money'] = $manjian_money;	//满减活动
			$orderdata['scoredk_money'] = $scoredk_money;	//积分抵扣
			$orderdata['scoredk_moneyhei'] = $scoredk_moneyhei;	//现金券抵扣
            $orderdata['scoredk_moneyhuang'] = $scoredk_moneyhuang;	//红包抵扣
			$orderdata['scoredk_moneyyu'] = $scoredk_moneyyu;	//余额抵扣

			$orderdata['coupon_money'] = $coupon_money + $freight_price - $new_freight_price;	//优惠券抵扣
			$orderdata['scoredkscore'] = $scoredkscore;		//抵扣掉的积分
			$orderdata['scoredkscorehei'] = $scoredkscorehei;		//抵扣掉的现金券
            $orderdata['scoredkscorehuang'] = $scoredkscorehuang;		//抵扣掉的红包
			$orderdata['scoredkscoreyu'] = $scoredkscoreyu;		//抵扣掉的现金券
            $orderdata['balance_price'] = $balance_price;	//尾款金额 定制的
			$orderdata['coupon_rid'] = $data['couponrid'];
			$orderdata['freight_price'] = $freight_price; //运费
            $orderdata['invoice_money'] = $invoice_money; //发票
			$orderdata['givescore'] = $givescore;
			$orderdata['givescorehuang'] = $givescorehuang;
			$orderdata['givescoregongxianzhi'] = $givescoregongxianzhi;
			$orderdata['givescore2'] = $givescore2;
			if($freight && ($freight['pstype']==0 || $freight['pstype']==10)){ //快递
				$orderdata['freight_text'] = $freight['name'].'('.$freight_price.'元)';
				$orderdata['freight_type'] = $freight['pstype'];
			}elseif($freight && $freight['pstype']==1){ //到店自提
				$orderdata['mdid'] = $data['storeid'];
				$mendian = Db::name('mendian')->where('aid',aid)->where('id',$data['storeid'])->find();
				$orderdata['freight_text'] = $freight['name'].'['.$mendian['name'].']';
				$orderdata['area2'] = $mendian['area'];
				$orderdata['freight_type'] = 1;
            }elseif($freight && $freight['pstype']==5){ //门店配送
                $orderdata['mdid'] = $data['storeid'];
                $mendian = Db::name('mendian')->where('aid',aid)->where('id',$data['storeid'])->find();
                $orderdata['freight_text'] = $freight['name'].'['.$mendian['name'].']';
                $orderdata['area2'] = $mendian['area'];
                $orderdata['freight_type'] = 5;
			}elseif($freight && $freight['pstype']==2){ //同城配送
				$orderdata['freight_text'] = $freight['name'].'('.$freight_price.'元)';
				$orderdata['freight_type'] = 2;
			}elseif($freight && $freight['pstype']==12){ //app配送
				$orderdata['freight_text'] = $freight['name'].'('.$freight_price.'元)';
				$orderdata['freight_type'] = 2;
			}elseif($freight && ($freight['pstype']==3 || $freight['pstype']==4)){ //自动发货 在线卡密
				$orderdata['freight_text'] = $freight['name'];
				$orderdata['freight_type'] = $freight['pstype'];
			}elseif($freight && $freight['pstype']==11){ //选择物流配送
				$type11pricedata = json_decode($freight['type11pricedata'],true);
				$orderdata['freight_text'] = $type11pricedata[$freight['type11key']]['name'].'('.$freight_price.'元)';
				$orderdata['freight_type'] = $freight['pstype'];
				$orderdata['freight_content'] = jsonEncode($type11pricedata[$freight['type11key']]);
			}else{
				$orderdata['freight_text'] = '包邮';
			}

			if($sysset['areafenhong_checktype'] == 1 && $orderdata['tel']){
				$addressrs = getaddressfromtel($orderdata['tel']);
				if($addressrs && $addressrs['province']){
					$orderdata['area2'] = $addressrs['province'].','.$addressrs['city'];
				}
			}

			$orderdata['freight_id'] = $freight['id'];
			$orderdata['freight_time'] = $data['freight_time']; //配送时间
			$orderdata['createtime'] = time();
			$orderdata['platform'] = platform;
			$orderdata['hexiao_code'] = random(16);
			$orderdata['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=shop&co='.$orderdata['hexiao_code']));
            if(getcustom('hexiao_member')){
                $orderdata['hexiao_code_member'] = mt_rand(1000,9999);
            }

            if(getcustom('buy_selectmember')){
                if($post['checkmemid']) $orderdata['checkmemid'] = $post['checkmemid'];
            }
            if(getcustom('pay_yuanbao')){
                $orderdata['total_yuanbao']   = $total_yuanbao;
                $orderdata['have_no_yuanbao'] = $have_no_yuanbao;
            }
            if(getcustom('plug_tengrui')){
                $orderdata['manjian_tr_roomId']  = $manjian_tr_roomId?$manjian_tr_roomId:0;
                $orderdata['cuxiao_tr_roomId']   = $cuxiao_tr_roomId?$cuxiao_tr_roomId:0;
                $orderdata['cuxiao_money']       = abs($cuxiaomoney);
                $orderdata['cuxiao_id']          = $cuxiaoid?$cuxiaoid:0;
            }
            
            $orderdata['kfj_shengyu']=$orderdata['totalprice'];
            $orderdata['cyjj_shengyu']=$orderdata['totalprice'];
            
            //
            $set = Db::name('admin_set')->where('aid',aid)->find();
            if($set['xsyj_shi'] ==1 && $set['xsyj_yeji'] > 0 && $set['xsyj_fhd'] >0)
            {
                $xsyjfh = $set['xsyj_yeji']*$totalprice*0.01;
                $zhou = date('w');
                $dianshu = $totalprice/$set['xsyj_fhd'];
                $orderdata['xsyjfh'] = $xsyjfh;
                $orderdata['dianshu'] = floor($dianshu);
                $orderdata['xsyjfhstatus'] = 0;
            }else
            {
                $orderdata['xsyjfh'] = 0;
                $orderdata['dianshu'] = 0;
                $orderdata['xsyjfhstatus'] = 0;
            }
            $d_orderid = $post['d_orderid']; // Assuming it's sent as 'd_orderid' in the POST data
            $orderdata['tuanzhangid'] = $tuanzhangid;
           $orderdata['d_orderid'] = $d_orderid;
            //var_dump($orderdata);die;
			$orderid = Db::name('shop_order')->insertGetId($orderdata);

			\app\model\Freight::saveformdata($orderid,'shop_order',$freight['id'],$data['formdata'],$extendInput);
			
			
			//单商品支付订单;
// 			$shop_id = $post[''];
// 			$shop=   Db::name('business')->where('aid',aid)->where('id',$bid)->find();
// 			print_r($shop);
// 			exit();
		//	$single_s_info = $shop['id']."-".$orderdata['totalprice'];
		    $single_s_info = implode(",",$single_s_info);
		    
		  //  print_r($single_s_info);
		  //  exit();
		    
			$payorderid = \app\model\Payorder::createorder(aid,$orderdata['bid'],$orderdata['mid'],'shop',$orderid,$orderdata['ordernum'],$orderdata['title'],$orderdata['totalprice'],$orderdata['scoredkscore'],$single_s_info
			);
			

			if($balance_price > 0){
				$balancedata = [];
				$balancedata['aid'] = aid;
				$balancedata['bid'] = $orderdata['bid'];
				$balancedata['mid'] = $orderdata['mid'];
				$balancedata['orderid'] = $orderid;
				$balancedata['ordernum'] = $orderdata['ordernum'];
				$balancedata['title'] = $orderdata['title'];
				$balancedata['money'] = $orderdata['balance_price'];
				$balancedata['type'] = 'balance';
				$balancedata['score'] = 0;
				$balancedata['createtime'] = time();
				$balancedata['status'] = 0;
				$balance_pay_orderid = Db::name('payorder')->insertGetId($balancedata);
				Db::name('shop_order')->where('id',$orderid)->update(['balance_pay_orderid'=>$balance_pay_orderid]);
			}

            //发票
            if($store_info['invoice'] && $data['invoice']) {
                $invoice = [
                    'order_type' => 'shop',
                    'orderid' => $orderid,
                    'ordernum' => $orderdata['ordernum'],
                    'type' => $data['invoice']['invoice_type'] ? $data['invoice']['invoice_type'] : 1,
                    'invoice_name' => $data['invoice']['invoice_name'] ? $data['invoice']['invoice_name'] : '个人',
                    'name_type' => $data['invoice']['name_type'] ? $data['invoice']['name_type'] : 1,
                    'tax_no' => $data['invoice']['tax_no'] ? $data['invoice']['tax_no'] : '',
                    'address' => $data['invoice']['address'] ? $data['invoice']['address'] : '',
                    'tel' => $data['invoice']['tel'] ? $data['invoice']['tel'] : '',
                    'bank_name' => $data['invoice']['bank_name'] ? $data['invoice']['bank_name'] : '',
                    'bank_account' => $data['invoice']['bank_account'] ? $data['invoice']['bank_account'] : '',
                    'mobile' => $data['invoice']['mobile'] ? $data['invoice']['mobile'] : '',
                    'email' => $data['invoice']['email'] ? $data['invoice']['email'] : ''
                ];
                $invoice['aid'] = aid;
                $invoice['bid'] = $orderdata['bid'];
                $invoice['create_time'] = time();
                Db::name('invoice')->insertGetId($invoice);
            }

			$alltotalprice += $orderdata['totalprice'];
			$alltotalscore += $orderdata['scoredkscore'];

			
			//是否是复购
			$hasordergoods = Db::name('shop_order_goods')->where('aid',aid)->where('mid',mid)->where('status','in','1,2,3')->find();
			if($hasordergoods){
				$isfg = 1;
			}else{
				$isfg = 0;
			}

            if(getcustom('everyday_hongbao')) {
                $hd = Db::name('hongbao_everyday')->where('aid', aid)->find();
            }
			$istc1 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
			$istc2 = 0;
			$istc3 = 0;
			$istc11 = 0; //设置了按单固定提成时 只将佣金计算到第一个商品里
			$istc22 = 0;
			$istc33 = 0;
			foreach($prolist as $key=>$v){
			    
				$product = $v['product'];
				$guige = $v['guige'];
				$num = $v['num'];
				$tid = $v['tid'];
				$dhtid = $v['dhtid'];
				$daiticheng = 0;
				if($dhtid >0)
				{
				    $tuanzhang = Db::name('tuanzhang')->where('aid',aid)->where('id',$tid)->find();
				    if(!empty($tuanzhang) &&  $tuanzhang['cid']>0)
				    {
				        $tuanzhang_cid = $tuanzhang['cid'];
				        $tuanzhang_level = Db::name('tuanzhang_category')->where('aid',aid)->where('id',$tuanzhang_cid)->find();
				        if(!empty($tuanzhang_level) && $tuanzhang_level['daiticheng'] >0)
				        {
				            $daiticheng = $tuanzhang_level['daiticheng'];
				        }
				    }
				}
				if($iszuhe == 1)
                {
                    if($product['kuncun_type'] == 1){
                        $product_price1= $guige['sell_price'] *$num; 
                    }else{
                        $product_price1 = 0;
                    }
                }else{
                  $product_price1= $guige['sell_price'] *$num; 
                }
				$ogdata = [];
			
				if($daiticheng >0)
				{
				    $daiticheng = $product_price1*$daiticheng*0.01;
				}
				$ogdata['aid'] = aid;
			
				$ogdata['bid'] = $product['bid'];
				$ogdata['mid'] = mid;
				$ogdata['orderid'] = $orderid;
				$ogdata['ordernum'] = $orderdata['ordernum'];
				$ogdata['proid'] = $product['id'];
				$ogdata['name'] = $product['name'];
				$ogdata['pic'] = $product['pic'];
				$ogdata['procode'] = $product['procode'];
                $ogdata['barcode'] = $product['barcode'];
				$ogdata['ggid'] = $guige['id'];
				$ogdata['ggname'] = $guige['name'];
				$ogdata['cid'] = $product['cid'];
				$ogdata['num'] = $num;
				$ogdata['diy_amount'] = $product['diy_amount'];
				$ogdata['tid'] = $tid;
				$ogdata['dhtid'] = $dhtid;
				$ogdata['daiticheng'] = $daiticheng;
				$ogdata['cost_price'] = $guige['cost_price'];
				$ogdata['sell_price'] = $guige['sell_price'];
				$ogdata['totalprice'] = $num * $guige['sell_price'];
				//$ogdata['totalprice'] = $product_price1;
                $ogdata['total_weight'] = $num * $guige['weight'];
				$ogdata['status'] = 0;
				$ogdata['createtime'] = time();
				$ogdata['isfg'] = $isfg;
				
				$ogdata['tuozhanid'] = 0;
				$ogdata['tuozhanfei'] = 0;
				$ogdata['tuozhanfei_status'] = 0;
				if($ogdata['bid']  >0)
				{
				    $business =  Db::name('business')->where('id',$ogdata['bid'])->field('tuozhanid,yihuo_baifenbi')->find();
				    if($business['tuozhanid'] >0)
				    {
				        $tuozhan_cate = [];
				        $tuozhanArr = Db::name('member_tuozhan')->where('aid',aid)->where('mid',$business['tuozhanid'])->find();
				        if(!empty($tuozhanArr))
				        {
				            $tuozhan_cate  =Db::name('yihuo_category')->where('aid',aid)->where('id',$tuozhanArr['cateid'])->find();
				        }
				        if($business['yihuo_baifenbi'] == 0)
        				{
        				    //用等级的
        				    if(!empty($tuozhan_cate) && $tuozhan_cate['zhitui'] > 0)
        				    {
        				        $chou_baifenbi = $tuozhan_cate['zhitui'];
        				    }
        				}else{
        				    $chou_baifenbi = $business['yihuo_baifenbi'];
        				}
        				$ogdata['tuozhanid'] = $business['tuozhanid'];
        				$ogdata['tuozhanfei'] = $ogdata['totalprice']*$chou_baifenbi*0.01;
        				
        				$ogdata['tuozhanid_jian'] = 0;
                		$ogdata['tuozhanfei_jian'] =0;
                				
                		$ogdata['tuozhanid_jianjian'] = 0;
                		$ogdata['tuozhanfei_jianjian'] = 0;
                				
        				$tuozhan_member= Db::name('member')->where('id',$business['tuozhanid'])->field('pid,path')->find();
        				if($tuozhan_member['pid'])
        				{
        				    $tuozhan_member_pid = Db::name('member_tuozhan')->where('aid',aid)->where('mid',$tuozhan_member['pid'])->find();
        				    if(!empty($tuozhan_member_pid) && $tuozhan_member_pid['yihuo_istuozhan'] == 1 && $tuozhan_member_pid['cateid'] >0){
        				        $tuozhan_cate_pid  =Db::name('yihuo_category')->where('aid',aid)->where('id',$tuozhan_member_pid['cateid'])->find();
                				$ogdata['tuozhanid_jian'] = $tuozhan_member['pid'];
                				$ogdata['tuozhanfei_jian'] = $ogdata['totalprice'] *$tuozhan_cate_pid['jiantui']*0.01;
                				
                				$tuozhan_member2= Db::name('member')->where('id',$tuozhan_member['pid'])->field('pid,path')->find();
                				if($tuozhan_member2['pid'])
                				{
                				    $tuozhan_member_pid2 = Db::name('member_tuozhan')->where('aid',aid)->where('mid',$tuozhan_member2['pid'])->find();
                				    if(!empty($tuozhan_member_pid2) && $tuozhan_member_pid2['yihuo_istuozhan'] == 1 && $tuozhan_member_pid2['cateid'] >0){
                				        $tuozhan_cate_pid  =Db::name('yihuo_category')->where('aid',aid)->where('id',$tuozhan_member_pid2['cateid'])->find();
                        				$ogdata['tuozhanid_jianjian'] = $tuozhan_member2['pid'];
                        				$ogdata['tuozhanfei_jianjian'] =$ogdata['totalprice'] *$tuozhan_cate_pid['jianjiantui']*0.01;
                				     }
                				}
        				    }
        				}
        				
				    }
				}
				
				if($product['to86yk_tid']){
					$ogdata['to86yk_tid'] = $product['to86yk_tid'];
				}
				if($product['fenhongset'] == 0){ //不参与分红
					$ogdata['isfenhong'] = 2;
				}
				
				$og_totalprice = $ogdata['totalprice'];
				if($product['balance'] > 0){
					$og_totalprice = $og_totalprice * (1 - $product['balance']*0.01);
				}

                $allproduct_price = $product_price;
                $og_leveldk_money = 0;
                $og_coupon_money = 0;
                $og_scoredk_money = 0;
                $og_manjian_money = 0;
                $og_hei_money = 0;
                if($allproduct_price > 0 && $og_totalprice > 0){
                    if($leveldk_money){
                        $og_leveldk_money = $og_totalprice / $allproduct_price * $leveldk_money;
                    }
                    if($coupon_money){
                        $og_coupon_money = $og_totalprice / $allproduct_price * $coupon_money;
                    }
                    if($scoredk_money){
                        $og_scoredk_money = $og_totalprice / $allproduct_price * $scoredk_money;
                    }
                    if($manjian_money){
                        $og_manjian_money = $og_totalprice / $allproduct_price * $manjian_money;
                    }
                    
                    //黑积分抵现
                    if($scoredk_moneyhei){
                        $og_hei_money = $og_totalprice / $allproduct_price * $scoredk_moneyhei;
                    }
                }
            
                $ogdata['scoredk_money'] = $og_scoredk_money;
                $ogdata['leveldk_money'] = $og_leveldk_money;
                $ogdata['manjian_money'] = $og_manjian_money;
                $ogdata['coupon_money'] = $og_coupon_money;
                $ogdata['hei_money'] = $og_hei_money;
                if($product['bid'] > 0) {
                    $totalprice_business = $og_totalprice - $og_manjian_money - $og_coupon_money - $og_hei_money;
                    //商品独立费率
                    if($product['feepercent'] != '' && $product['feepercent'] != null && $product['feepercent'] >= 0) {
                        $ogdata['business_total_money'] = $totalprice_business * (100-$product['feepercent']) * 0.01;
                    } else {
                        //商户费率
                        $ogdata['business_total_money'] = $totalprice_business * (100-$store_info['feepercent']) * 0.01;
                    }
                }

				//计算商品实际金额  商品金额 - 会员折扣 - 积分抵扣 - 满减抵扣 - 优惠券抵扣
				if($sysset['fxjiesuantype'] == 1 || $sysset['fxjiesuantype'] == 2){
					$og_totalprice = $og_totalprice - $og_leveldk_money - $og_scoredk_money - $og_manjian_money - $og_hei_money;
					if($couponrecord['type']!=4) {//运费抵扣券
						$og_totalprice -= $og_coupon_money;
					}
					$og_totalprice = round($og_totalprice,2);
					if($og_totalprice < 0) $og_totalprice = 0;
				}
				
				
				$ogdata['real_totalprice'] = $og_totalprice; //实际商品销售金额
					$paiduires = Db::name('paidui_set')->where('aid',aid)->find();
				$paidui_jiang = 0;
				$paidui_give = 0;
				if($paiduires['status'] == 1 &&$product['paiduistatus'] == 1)//开启了进入排队
				{
				    //商品的
				    if($product['paidui_jiang'] != 0.00  && $product['paidui_give'] != 0.00)
				    {
				         $paidui_jiang= $ogdata['real_totalprice']*$product['paidui_jiang']*0.01;
				         $paidui_give = $ogdata['real_totalprice']*$product['paidui_give']*0.01;
				    }else{
    				    //判断我的等级有没有设置
    				    if($userlevel['paidui_jiang'] >0)
    				    {
    				       $paidui_jiang= $ogdata['real_totalprice']*$userlevel['paidui_jiang']*0.01;
    				    }
    				    if($userlevel['paidui_give'] >0)
    				    {
    				        $paidui_give = $ogdata['real_totalprice']*$userlevel['paidui_give']*0.01;
    				    }
				    }
				    //设置了开启计算,保存订单商品表
				}
				
				//var_dump($paidui_jiang);die;
				//计算佣金的商品金额
					$ogdata['paidui_jiang'] = $paidui_jiang;
				$ogdata['paidui_give'] = $paidui_give;
				$commission_totalprice = $ogdata['totalprice'];
				if($sysset['fxjiesuantype']==1){ //按成交价格
					$commission_totalprice = $ogdata['real_totalprice'];
					if($commission_totalprice < 0) $commission_totalprice = 0;
				}
				$commission_totalpriceCache = $commission_totalprice;
				if($sysset['fxjiesuantype']==2){ //按销售利润
					$commission_totalprice = $ogdata['real_totalprice'] - $guige['cost_price'] * $num;
					if($commission_totalprice < 0) $commission_totalprice = 0;
				}
                if(getcustom('pay_yuanbao')){
                    $ogdata['yuanbao']       = $product['yuanbao'];
                    $ogdata['total_yuanbao'] = $num*$product['yuanbao'];
                }
                if(getcustom('plug_tengrui')) {
                    $ogdata['tr_roomId'] = $product['tr_roomId'];
                }
                if(getcustom('product_glass')){
                    $glass_record_id = $v['glass_record_id'];
                    if($glass_record_id) {
                        $glassrecord = Db::name('glass_record')->where('aid', aid)->where('id', $glass_record_id)->find();
                        if ($glassrecord) {
                            $orderglassrecord = [
                                'name' => $glassrecord['name'],
                                'desc' => $glassrecord['desc'],
                                'degress_left' => $glassrecord['degress_left'],
                                'degress_right' => $glassrecord['degress_right'],
                                'ipd' => $glassrecord['ipd'],
                                'correction_left' => $glassrecord['correction_left'],
                                'correction_right' => $glassrecord['correction_right'],
                                'is_ats' => $glassrecord['is_ats'],
                                'ats_left' => $glassrecord['ats_left'],
                                'ats_right' => $glassrecord['ats_right'],
                                'ats_zright' => $glassrecord['ats_zright'],
                                'ats_zleft' => $glassrecord['ats_zleft'],
                                'type' => $glassrecord['type'],
                                'mid' => $glassrecord['mid'],
                                'createtime' => time(),
                                'aid' => aid,
                                'bid' => $orderdata['bid'] ?? 0,
                                'glass_record_id' => $glassrecord['id']
                            ];
                            $order_glass_record_id = Db::name('order_glass_record')->insertGetId($orderglassrecord);
                            $ogdata['glass_record_id'] = $order_glass_record_id;
                        }
                    }
                }
                //
                $ogdata['fenhongbili'] =$product['fenhongbili'];
                $ogdata['jiandianjiangli'] =$product['jiandian'];
                $ogdata['koulvjifen'] =0;
                $ogdata['xiaofeizhifan'] =0;
                 $ogdata['is_yeji'] = $product['is_yeji'];
                //扣除绿积分和返消费值
                if($product['xiaofeizhi'])
                {
                    $xiaofeizhi = json_decode($product['xiaofeizhi'],1);
                    $xiaofeifan = $xiaofeizhi[$this->member['levelid']];
                    $xiaofeifan = $xiaofeifan*$num;
                    $xiaofeizhicount = $xiaofeifan;
                    //转换绿积分
                    //绿积分的价值 $sysset['lvjifenjiazhi']
                     //消费值绿积分转换 xiaofeizhizhuan
                      $kouchulvjifen = $xiaofeizhicount*$sysset['xiaofeizhizhuan']*0.01/$sysset['lvjifenjiazhi'];
                      $kouchulvjifen = round($kouchulvjifen,2);
                      $ogdata['koulvjifen'] =$kouchulvjifen;
                      $ogdata['xiaofeizhifan'] =$xiaofeizhicount;
                }
                //计算静态佣金和静态积分
                $agleveldata = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
                
                $jingyongcommission_totalprice = $ogdata['totalprice']*$agleveldata['fenyong_zong']*0.01;
                $ogdata['jingyong'] =round($jingyongcommission_totalprice*$agleveldata['fenyong_yongjin']*0.01,2);
                $ogdata['jingji'] =round($jingyongcommission_totalprice*$agleveldata['fenyong_jifen']*0.01,2);
                // var_dump($ogdata);die;
				$ogid = Db::name('shop_order_goods')->insertGetId($ogdata);
				$parent1 = [];$parent2 = [];$parent3 = [];$parent4 = [];
				$ogupdate = [];
				$ogupdate['fhparent2commission'] = 0;
                $ogupdate['fhparent3commission'] = 0;
                $ogupdate['fhparent4commission'] = 0;
				$agleveldata = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
				if($agleveldata['can_agent'] > 0 && $agleveldata['commission1own']==1){
					$this->member['pid'] = mid;
				}
				if($product['commissionset']!=-1){
					if($this->member['pid']){
						$parent1 = Db::name('member')->where('aid',aid)->where('id',$this->member['pid'])->find();
						if($parent1){
							$agleveldata1 = Db::name('member_level')->where('aid',aid)->where('id',$parent1['levelid'])->find();
							if($agleveldata1['can_agent']!=0 && (!$agleveldata1['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata1['commission_appointlevelid'])))){
								$ogupdate['parent1'] = $parent1['id'];
							}
						}
					}
					if($parent1['pid']){
						$parent2 = Db::name('member')->where('aid',aid)->where('id',$parent1['pid'])->find();
						if($parent2){
							$agleveldata2 = Db::name('member_level')->where('aid',aid)->where('id',$parent2['levelid'])->find();
							if($agleveldata2['can_agent']>1 && (!$agleveldata2['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata2['commission_appointlevelid'])))){
								$ogupdate['parent2'] = $parent2['id'];
							}
						}
					}
					if($parent2['pid']){
						$parent3 = Db::name('member')->where('aid',aid)->where('id',$parent2['pid'])->find();
						if($parent3){
							$agleveldata3 = Db::name('member_level')->where('aid',aid)->where('id',$parent3['levelid'])->find();
							if($agleveldata3['can_agent']>2 && (!$agleveldata3['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata3['commission_appointlevelid'])))){
								$ogupdate['parent3'] = $parent3['id'];
							}
						}
					}
                    if($parent3['pid']){
                        $parent4 = Db::name('member')->where('aid',aid)->where('id',$parent3['pid'])->find();
                        if($parent4){
                            $agleveldata4 = Db::name('member_level')->where('aid',aid)->where('id',$parent4['levelid'])->find();
							if($product['commissionpingjiset'] != 0){
								if($product['commissionpingjiset'] == 1){
									$commissionpingjidata1 = json_decode($product['commissionpingjidata1'],true);
									$agleveldata4['commission_parent_pj'] = $commissionpingjidata1[$agleveldata4['id']];
								}elseif($product['commissionpingjiset'] == 2){
									$commissionpingjidata2 = json_decode($product['commissionpingjidata2'],true);
									$agleveldata4['commission_parent_pj'] = $commissionpingjidata2[$agleveldata4['id']];
								}else{
									$agleveldata4['commission_parent_pj'] = 0;
								}
							}
                            //持续推荐奖励
                            if($agleveldata4['can_agent'] > 0 && ($agleveldata4['commission_parent'] > 0 || ($parent4['levelid']==$parent3['levelid'] && $agleveldata4['commission_parent_pj'] > 0))){
                                $ogupdate['parent4'] = $parent4['id'];
                            }else{
                                 $ogupdate['parent4'] = $parent4['id'];
                            }
                        }
                    }
                    if($parent4['pid']){
						$parent5 = Db::name('member')->where('aid',aid)->where('id',$parent4['pid'])->find();
						if($parent5){
							$agleveldata5 = Db::name('member_level')->where('aid',aid)->where('id',$parent5['levelid'])->find();
							if($agleveldata5['can_agent']>2 && (!$agleveldata5['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata5['commission_appointlevelid'])))){
								$ogupdate['parent5'] = $parent5['id'];
							}
						}
					}
					
					if($parent5['pid']){
						$parent6 = Db::name('member')->where('aid',aid)->where('id',$parent5['pid'])->find();
						if($parent6){
							$agleveldata6 = Db::name('member_level')->where('aid',aid)->where('id',$parent6['levelid'])->find();
							if($agleveldata6['can_agent']>2 && (!$agleveldata6['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata6['commission_appointlevelid'])))){
								$ogupdate['parent6'] = $parent6['id'];
							}
						}
					}
					if($parent6['pid']){
						$parent7 = Db::name('member')->where('aid',aid)->where('id',$parent6['pid'])->find();
						if($parent7){
							$agleveldata7 = Db::name('member_level')->where('aid',aid)->where('id',$parent7['levelid'])->find();
							if($agleveldata7['can_agent']>2 && (!$agleveldata7['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata7['commission_appointlevelid'])))){
								$ogupdate['parent7'] = $parent7['id'];
							}
						}
					}
					if($parent7['pid']){
						$parent8 = Db::name('member')->where('aid',aid)->where('id',$parent7['pid'])->find();
						if($parent8){
							$agleveldata8 = Db::name('member_level')->where('aid',aid)->where('id',$parent8['levelid'])->find();
							if($agleveldata8['can_agent']>2 && (!$agleveldata8['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata8['commission_appointlevelid'])))){
								$ogupdate['parent8'] = $parent8['id'];
							}
						}
					}
					if($parent8['pid']){
						$parent9 = Db::name('member')->where('aid',aid)->where('id',$parent8['pid'])->find();
						if($parent9){
							$agleveldata9 = Db::name('member_level')->where('aid',aid)->where('id',$parent9['levelid'])->find();
							if($agleveldata9['can_agent']>2 && (!$agleveldata9['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata9['commission_appointlevelid'])))){
								$ogupdate['parent9'] = $parent9['id'];
							}
						}
					}
					if($parent9['pid']){
						$parent10 = Db::name('member')->where('aid',aid)->where('id',$parent9['pid'])->find();
						if($parent10){
							$agleveldata10 = Db::name('member_level')->where('aid',aid)->where('id',$parent10['levelid'])->find();
							if($agleveldata10['can_agent']>2 && (!$agleveldata10['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata10['commission_appointlevelid'])))){
								$ogupdate['parent10'] = $parent10['id'];
							}
						}
					}
					if($parent10['pid']){
						$parent11 = Db::name('member')->where('aid',aid)->where('id',$parent10['pid'])->find();
						if($parent11){
							$agleveldata11 = Db::name('member_level')->where('aid',aid)->where('id',$parent11['levelid'])->find();
							if($agleveldata11['can_agent']>2 && (!$agleveldata11['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata11['commission_appointlevelid'])))){
								$ogupdate['parent11'] = $parent11['id'];
							}
						}
					}
					if($parent11['pid']){
						$parent12 = Db::name('member')->where('aid',aid)->where('id',$parent11['pid'])->find();
						if($parent12){
							$agleveldata12 = Db::name('member_level')->where('aid',aid)->where('id',$parent12['levelid'])->find();
							if($agleveldata12['can_agent']>2 && (!$agleveldata12['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata12['commission_appointlevelid'])))){
								$ogupdate['parent12'] = $parent12['id'];
							}
						}
					}
					if($parent12['pid']){
						$parent13 = Db::name('member')->where('aid',aid)->where('id',$parent12['pid'])->find();
						if($parent13){
							$agleveldata13 = Db::name('member_level')->where('aid',aid)->where('id',$parent13['levelid'])->find();
							if($agleveldata13['can_agent']>2 && (!$agleveldata13['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata13['commission_appointlevelid'])))){
								$ogupdate['parent13'] = $parent13['id'];
							}
						}
					}
					if($parent13['pid']){
						$parent14 = Db::name('member')->where('aid',aid)->where('id',$parent13['pid'])->find();
						if($parent14){
							$agleveldata14 = Db::name('member_level')->where('aid',aid)->where('id',$parent14['levelid'])->find();
							if($agleveldata14['can_agent']>2 && (!$agleveldata14['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata14['commission_appointlevelid'])))){
								$ogupdate['parent14'] = $parent14['id'];
							}
						}
					}
					if($parent14['pid']){
						$parent15 = Db::name('member')->where('aid',aid)->where('id',$parent14['pid'])->find();
						if($parent15){
							$agleveldata15 = Db::name('member_level')->where('aid',aid)->where('id',$parent15['levelid'])->find();
							if($agleveldata15['can_agent']>2 && (!$agleveldata15['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata15['commission_appointlevelid'])))){
								$ogupdate['parent15'] = $parent15['id'];
							}
						}
					}
					if($parent15['pid']){
						$parent16 = Db::name('member')->where('aid',aid)->where('id',$parent15['pid'])->find();
						if($parent16){
							$agleveldata16 = Db::name('member_level')->where('aid',aid)->where('id',$parent16['levelid'])->find();
							if($agleveldata16['can_agent']>2 && (!$agleveldata16['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata16['commission_appointlevelid'])))){
								$ogupdate['parent16'] = $parent16['id'];
							}
						}
					}
					if($parent16['pid']){
						$parent17 = Db::name('member')->where('aid',aid)->where('id',$parent16['pid'])->find();
						if($parent17){
							$agleveldata17 = Db::name('member_level')->where('aid',aid)->where('id',$parent17['levelid'])->find();
							if($agleveldata17['can_agent']>2 && (!$agleveldata17['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata17['commission_appointlevelid'])))){
								$ogupdate['parent17'] = $parent17['id'];
							}
						}
					}
					if($parent17['pid']){
						$parent18 = Db::name('member')->where('aid',aid)->where('id',$parent17['pid'])->find();
						if($parent18){
							$agleveldata18 = Db::name('member_level')->where('aid',aid)->where('id',$parent18['levelid'])->find();
							if($agleveldata18['can_agent']>2 && (!$agleveldata18['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata18['commission_appointlevelid'])))){
								$ogupdate['parent18'] = $parent18['id'];
							}
						}
					}
					if($parent18['pid']){
						$parent19 = Db::name('member')->where('aid',aid)->where('id',$parent18['pid'])->find();
						if($parent19){
							$agleveldata19 = Db::name('member_level')->where('aid',aid)->where('id',$parent19['levelid'])->find();
							if($agleveldata19['can_agent']>2 && (!$agleveldata19['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata19['commission_appointlevelid'])))){
								$ogupdate['parent19'] = $parent19['id'];
							}
						}
					}
					if($parent19['pid']){
						$parent20 = Db::name('member')->where('aid',aid)->where('id',$parent19['pid'])->find();
						if($parent20){
							$agleveldata20 = Db::name('member_level')->where('aid',aid)->where('id',$parent20['levelid'])->find();
							if($agleveldata20['can_agent']>2 && (!$agleveldata20['commission_appointlevelid'] || in_array($this->member['levelid'],explode(',',$agleveldata20['commission_appointlevelid'])))){
								$ogupdate['parent20'] = $parent20['id'];
							}
						}
					}
				// 	var_dump($ogupdate);die;
					if($product['commissionset']==1){//按商品设置的分销比例
						$commissiondata = json_decode($product['commissiondata1'],true);
						if($commissiondata){
							if($agleveldata1) $ogupdate['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $commission_totalprice * 0.01;
							if($agleveldata2) $ogupdate['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $commission_totalprice * 0.01;
							if($agleveldata3) $ogupdate['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $commission_totalprice * 0.01;
							if($agleveldata4) $ogupdate['parent4commission'] = $commissiondata[$agleveldata4['id']]['commission4'] * $commission_totalprice * 0.01;
							if($agleveldata5) $ogupdate['parent5commission'] = $commissiondata[$agleveldata5['id']]['commission5'] * $commission_totalprice * 0.01;
							if($agleveldata6) $ogupdate['parent6commission'] = $commissiondata[$agleveldata6['id']]['commission6'] * $commission_totalprice * 0.01;
							if($agleveldata7) $ogupdate['parent7commission'] = $commissiondata[$agleveldata7['id']]['commission7'] * $commission_totalprice * 0.01;
							if($agleveldata8) $ogupdate['parent8commission'] = $commissiondata[$agleveldata8['id']]['commission8'] * $commission_totalprice * 0.01;
							if($agleveldata9) $ogupdate['parent9commission'] = $commissiondata[$agleveldata9['id']]['commission9'] * $commission_totalprice * 0.01;
							if($agleveldata10) $ogupdate['parent10commission'] = $commissiondata[$agleveldata10['id']]['commission10'] * $commission_totalprice * 0.01;
							if($agleveldata11) $ogupdate['parent11commission'] = $commissiondata[$agleveldata11['id']]['commission11'] * $commission_totalprice * 0.01;
							if($agleveldata12) $ogupdate['parent12commission'] = $commissiondata[$agleveldata12['id']]['commission12'] * $commission_totalprice * 0.01;
							if($agleveldata13) $ogupdate['parent13commission'] = $commissiondata[$agleveldata13['id']]['commission13'] * $commission_totalprice * 0.01;
							if($agleveldata14) $ogupdate['parent14commission'] = $commissiondata[$agleveldata14['id']]['commission14'] * $commission_totalprice * 0.01;
							if($agleveldata15) $ogupdate['parent15commission'] = $commissiondata[$agleveldata15['id']]['commission15'] * $commission_totalprice * 0.01;
							if($agleveldata16) $ogupdate['parent16commission'] = $commissiondata[$agleveldata16['id']]['commission16'] * $commission_totalprice * 0.01;
							if($agleveldata17) $ogupdate['parent17commission'] = $commissiondata[$agleveldata17['id']]['commission17'] * $commission_totalprice * 0.01;
							if($agleveldata18) $ogupdate['parent18commission'] = $commissiondata[$agleveldata18['id']]['commission18'] * $commission_totalprice * 0.01;
							if($agleveldata19) $ogupdate['parent19commission'] = $commissiondata[$agleveldata19['id']]['commission19'] * $commission_totalprice * 0.01;
							if($agleveldata20) $ogupdate['parent20commission'] = $commissiondata[$agleveldata20['id']]['commission20'] * $commission_totalprice * 0.01;
						}
					}elseif($product['commissionset']==2){//按固定金额
						$commissiondata = json_decode($product['commissiondata2'],true);
						if($commissiondata){
							if(getcustom('fengdanjiangli') && $product['fengdanjiangli']){
								
							}else{
								if($agleveldata1) $ogupdate['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
								if($agleveldata2) $ogupdate['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
								if($agleveldata3) $ogupdate['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
								if($agleveldata4) $ogupdate['parent4commission'] = $commissiondata[$agleveldata4['id']]['commission4'] * $num;
								if($agleveldata5) $ogupdate['parent5commission'] = $commissiondata[$agleveldata5['id']]['commission5'] * $num;
								if($agleveldata6) $ogupdate['parent6commission'] = $commissiondata[$agleveldata6['id']]['commission6'] * $num;
								if($agleveldata7) $ogupdate['parent7commission'] = $commissiondata[$agleveldata7['id']]['commission7'] * $num;
								if($agleveldata8) $ogupdate['parent8commission'] = $commissiondata[$agleveldata8['id']]['commission8'] * $num;
								if($agleveldata9) $ogupdate['parent9commission'] = $commissiondata[$agleveldata9['id']]['commission9'] * $num;
								if($agleveldata10) $ogupdate['parent10commission'] = $commissiondata[$agleveldata10['id']]['commission10'] * $num;
								if($agleveldata11) $ogupdate['parent11commission'] = $commissiondata[$agleveldata11['id']]['commission11'] * $num;
    							if($agleveldata12) $ogupdate['parent12commission'] = $commissiondata[$agleveldata12['id']]['commission12'] * $num;
    							if($agleveldata13) $ogupdate['parent13commission'] = $commissiondata[$agleveldata13['id']]['commission13'] * $num;
    							if($agleveldata14) $ogupdate['parent14commission'] = $commissiondata[$agleveldata14['id']]['commission14'] * $num;
    							if($agleveldata15) $ogupdate['parent15commission'] = $commissiondata[$agleveldata15['id']]['commission15'] * $num;
    							if($agleveldata16) $ogupdate['parent16commission'] = $commissiondata[$agleveldata16['id']]['commission16'] * $num;
    							if($agleveldata17) $ogupdate['parent17commission'] = $commissiondata[$agleveldata17['id']]['commission17'] * $num;
    							if($agleveldata18) $ogupdate['parent18commission'] = $commissiondata[$agleveldata18['id']]['commission18'] * $num;
    							if($agleveldata19) $ogupdate['parent19commission'] = $commissiondata[$agleveldata19['id']]['commission19'] * $num;
    							if($agleveldata20) $ogupdate['parent20commission'] = $commissiondata[$agleveldata20['id']]['commission20'] * $num;
							}
						}
					}elseif($product['commissionset']==3){//提成是积分
						$commissiondata = json_decode($product['commissiondata3'],true);
						if($commissiondata){
							if($agleveldata1) $ogupdate['parent1score'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
							if($agleveldata2) $ogupdate['parent2score'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
							if($agleveldata3) $ogupdate['parent3score'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
							if($agleveldata4) $ogupdate['parent4score'] = $commissiondata[$agleveldata4['id']]['commission4'] * $num;
							if($agleveldata5) $ogupdate['parent5score'] = $commissiondata[$agleveldata5['id']]['commission5'] * $num;
							if($agleveldata6) $ogupdate['parent6score'] = $commissiondata[$agleveldata6['id']]['commission6'] * $num;
							if($agleveldata7) $ogupdate['parent7score'] = $commissiondata[$agleveldata7['id']]['commission7'] * $num;
							if($agleveldata8) $ogupdate['parent8score'] = $commissiondata[$agleveldata8['id']]['commission8'] * $num;
							if($agleveldata9) $ogupdate['parent9score'] = $commissiondata[$agleveldata9['id']]['commission9'] * $num;
							if($agleveldata10) $ogupdate['parent10score'] = $commissiondata[$agleveldata10['id']]['commission10'] * $num;
							if($agleveldata11) $ogupdate['parent11score'] = $commissiondata[$agleveldata11['id']]['commission11'] * $num;
							if($agleveldata12) $ogupdate['parent12score'] = $commissiondata[$agleveldata12['id']]['commission12'] * $num;
							if($agleveldata13) $ogupdate['parent13score'] = $commissiondata[$agleveldata13['id']]['commission13'] * $num;
							if($agleveldata14) $ogupdate['parent14score'] = $commissiondata[$agleveldata14['id']]['commission14'] * $num;
							if($agleveldata15) $ogupdate['parent15score'] = $commissiondata[$agleveldata15['id']]['commission15'] * $num;
							if($agleveldata16) $ogupdate['parent16score'] = $commissiondata[$agleveldata16['id']]['commission16'] * $num;
							if($agleveldata17) $ogupdate['parent17score'] = $commissiondata[$agleveldata17['id']]['commission17'] * $num;
							if($agleveldata18) $ogupdate['parent18score'] = $commissiondata[$agleveldata18['id']]['commission18'] * $num;
							if($agleveldata19) $ogupdate['parent19score'] = $commissiondata[$agleveldata19['id']]['commission19'] * $num;
							if($agleveldata20) $ogupdate['parent20score'] = $commissiondata[$agleveldata20['id']]['commission20'] * $num;
						}
					}else{ //按会员等级设置的分销比例
						if($agleveldata1){
							if(getcustom('plug_ttdz') && $isfg == 1){
								$agleveldata1['commission1'] = $agleveldata1['commission4'];
							}
							if($agleveldata1['commissiontype']==1){ //固定金额按单
								if($istc1==0){
									$ogupdate['parent1commission'] = $agleveldata1['commission1'];
									$istc1 = 1;
								}
							}else{
								$ogupdate['parent1commission'] = $agleveldata1['commission1'] * $commission_totalprice * 0.01;
							}
							if($agleveldata1['is_zuigao_money'] == 1)
							{
							    $agleveldata1['zuigao_money'] =  $commission_totalprice*$agleveldata1['zuigao_money']*0.01;
							}
							if($ogupdate['parent1commission'] > $agleveldata1['zuigao_money'] && $agleveldata1['zuigao_money'] >0)
							{
							    $ogupdate['parent1commission'] = $agleveldata1['zuigao_money'];
							}
						}
					
						if($agleveldata2){
							if(getcustom('plug_ttdz') && $isfg == 1){
								$agleveldata2['commission2'] = $agleveldata2['commission5'];
							}
							if($agleveldata2['commissiontype']==1){
								if($istc2==0){
									$ogupdate['parent2commission'] = $agleveldata2['commission2'];
									$istc2 = 1;
                                    //持续推荐奖励
                                    if($agleveldata2['commission_parent'] > 0 && $ogupdate['parent1']) {
                                        $ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $agleveldata2['commission_parent'];
                                    }
								}
							}else{
								$ogupdate['parent2commission'] = $agleveldata2['commission2'] * $commission_totalprice * 0.01;
                                //持续推荐奖励
                                if($agleveldata2['commission_parent'] > 0 && $ogupdate['parent1commission'] > 0 && $ogupdate['parent1']) {
                                    $ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $ogupdate['parent1commission'] * $agleveldata2['commission_parent'] * 0.01;
                                }
							}
							if($agleveldata2['is_zuigao_money'] == 1)
							{
							    $agleveldata2['zuigao_money'] =  $commission_totalprice*$agleveldata2['zuigao_money']*0.01;
							}
							if($ogupdate['parent2commission'] > $agleveldata2['zuigao_money']  && $agleveldata2['zuigao_money'] >0)
							{
							    $ogupdate['parent2commission'] = $agleveldata2['zuigao_money'];
							}

						}
						if($agleveldata3){
							if(getcustom('plug_ttdz') && $isfg == 1){
								$agleveldata3['commission3'] = $agleveldata3['commission6'];
							}
							
							if($agleveldata3['commissiontype']==1){
								if($istc3==0){
									$ogupdate['parent3commission'] = $agleveldata3['commission3'];
									$istc3 = 1;
                                    //持续推荐奖励
                                    if($agleveldata3['commission_parent'] > 0 && $ogupdate['parent2']) {
                                        $ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $agleveldata3['commission_parent'];
                                    }
								}
							}else{
								$ogupdate['parent3commission'] = $agleveldata3['commission3'] * $commission_totalprice * 0.01;
                                //持续推荐奖励
                                if($agleveldata3['commission_parent'] > 0 && $ogupdate['parent2commission'] > 0 && $ogupdate['parent2']) {
                                    $ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $ogupdate['parent2commission'] * $agleveldata3['commission_parent'] * 0.01;
                                }
							}
							if($agleveldata3['is_zuigao_money'] == 1)
							{
							    $agleveldata3['zuigao_money'] =  $commission_totalprice*$agleveldata3['zuigao_money']*0.01;
							}
							if($ogupdate['parent3commission'] > $agleveldata3['zuigao_money'] && $agleveldata3['zuigao_money'] >0)
							{
							    $ogupdate['parent3commission'] = $agleveldata3['zuigao_money'];
							}
						}
						//持续推荐奖励
                        if($agleveldata4['commission_parent'] > 0 && $ogupdate['parent3']) {
                            if($agleveldata3['commissiontype']==1){
                                $ogupdate['parent4commission'] = $agleveldata4['commission_parent'];
                            } else {
                                $ogupdate['parent4commission'] = $ogupdate['parent3commission'] * $agleveldata4['commission_parent'] * 0.01;
                            }
                            if($agleveldata4['is_zuigao_money'] == 1)
							{
							    $agleveldata4['zuigao_money'] =  $commission_totalprice*$agleveldata4['zuigao_money']*0.01;
							}
                            if($ogupdate['parent4commission'] > $agleveldata4['zuigao_money'] &&$agleveldata4['zuigao_money']>0)
							{
							    $ogupdate['parent4commission'] = $agleveldata4['zuigao_money'];
							}
                        }
                        //提成积分
                        if($agleveldata1){
							if($agleveldata1['commissiontype']==1){ 
								if($istc11==0){
									$ogupdate['parent1commissionscore'] = $agleveldata1['commission1_score'];
									$istc11 = 1;
								}
							}else{
								$ogupdate['parent1commissionscore'] = $agleveldata1['commission1_score'] * $commission_totalprice * 0.01;
							}
							 if($agleveldata1['is_zuigao_score'] == 1)
							{
							    $agleveldata1['zuigao_score'] =  $commission_totalprice*$agleveldata1['zuigao_score']*0.01;
							}
							if($ogupdate['parent1commissionscore'] > $agleveldata1['zuigao_score'] && $agleveldata1['zuigao_score'] >0)
							{
							    $ogupdate['parent1commissionscore'] = $agleveldata1['zuigao_score'];
							}
						}
						
						if($agleveldata2){
							if($agleveldata2['commissiontype']==1){
								if($istc22==0){
									$ogupdate['parent2commissionscore'] = $agleveldata2['commission2_score'];
									$istc22 = 1;
                                    //持续推荐奖励
                                    if($agleveldata2['commission_parent_score'] > 0 && $ogupdate['parent1']) {
                                        $ogupdate['parent2commissionscore'] = $ogupdate['parent2commissionscore'] + $agleveldata2['commission_parent_score'];
                                    }
								}
							}else{
								$ogupdate['parent2commissionscore'] = $agleveldata2['commission2_score'] * $commission_totalprice * 0.01;
                                //持续推荐奖励
                                if($agleveldata2['commission_parent_score'] > 0 && $ogupdate['parent1commissionscore'] > 0 && $ogupdate['parent1']) {
                                    $ogupdate['parent2commissionscore'] = $ogupdate['parent2commissionscore'] + $ogupdate['parent1commissionscore'] * $agleveldata2['commission_parent_score'] * 0.01;
                                }
							}
							 if($agleveldata2['is_zuigao_score'] == 1)
							{
							    $agleveldata2['zuigao_score'] =  $commission_totalprice*$agleveldata2['zuigao_score']*0.01;
							}
							if($ogupdate['parent2commissionscore'] > $agleveldata2['zuigao_score'] && $agleveldata2['zuigao_score'] >0)
							{
							    $ogupdate['parent2commissionscore'] = $agleveldata2['zuigao_score'];
							}

						}
						
						if($agleveldata3){
							if($agleveldata3['commissiontype']==1){
								if($istc33==0){
									$ogupdate['parent3commissionscore'] = $agleveldata3['commission3_score'];
									$istc33 = 1;
                                    //持续推荐奖励
                                    if($agleveldata3['commission_parent_score'] > 0 && $ogupdate['parent2']) {
                                        $ogupdate['parent3commissionscore'] = $ogupdate['parent3commissionscore'] + $agleveldata3['commission_parent_score'];
                                    }
								}
							}else{
								$ogupdate['parent3commissionscore'] = $agleveldata3['commission3_score'] * $commission_totalprice * 0.01;
                                //持续推荐奖励
                                if($agleveldata3['commission_parent_score'] > 0 && $ogupdate['parent2commissionscore'] > 0 && $ogupdate['parent2']) {
                                    $ogupdate['parent3commissionscore'] = $ogupdate['parent3commissionscore'] + $ogupdate['parent2commissionscore'] * $agleveldata3['commission_parent_score'] * 0.01;
                                }
							}
							if($agleveldata3['is_zuigao_score'] == 1)
							{
							    $agleveldata3['zuigao_score'] =  $commission_totalprice*$agleveldata3['zuigao_score']*0.01;
							}
							if($ogupdate['parent3commissionscore'] > $agleveldata3['zuigao_score'] && $agleveldata3['zuigao_score'] >0)
							{
							    $ogupdate['parent3commissionscore'] = $agleveldata3['zuigao_score'];
							}
						}
                        	//持续推荐奖励
                        if($agleveldata4['commission_parent_score'] > 0 && $ogupdate['parent3']) {
                            if($agleveldata3['commissiontype']==1){
                                $ogupdate['parent4commissionscore'] = $agleveldata4['commission_parent_score'];
                            } else {
                                $ogupdate['parent4commissionscore'] = $ogupdate['parent3commissionscore'] * $agleveldata4['commission_parent_score'] * 0.01;
                            }
                            	if($agleveldata4['is_zuigao_score'] == 1)
							{
							    $agleveldata4['zuigao_score'] =  $commission_totalprice*$agleveldata4['zuigao_score']*0.01;
							}
                            if($ogupdate['parent4commissionscore'] > $agleveldata4['zuigao_score'] && $agleveldata4['zuigao_score'] >0)
							{
							    $ogupdate['parent4commissionscore'] = $agleveldata4['zuigao_score'];
							}
                        }
                        /** 这里是收益抽佣**/
    //                     if($agleveldata1){
				// 			if($agleveldata1['commissiontype']==1){ //固定金额按单
				// 				if($istc1==0){
				// 					$ogupdate['parent1commissionshouyi'] = $agleveldata1['commissionshouyi1'];
				// 					$istc1 = 1;
				// 				}
				// 			}else{
				// 				$ogupdate['parent1commissionshouyi'] = $agleveldata1['commissionshouyi1'] * $commission_totalprice * 0.01;
				// 			}
    //                     }
    //                     if($agleveldata2){
    //                         if($agleveldata2['commissiontype']==1){
				// 				if($istc2==0){
				// 					$ogupdate['parent2commissionshouyi'] = $agleveldata2['commissionshouyi2'];
				// 					$istc2 = 1;
    //                                 //持续推荐奖励
    //                                 if($agleveldata2['commissionshouyi_parent'] > 0 && $ogupdate['parent1']) {
    //                                     $ogupdate['parent2commissionshouyi'] = $ogupdate['parent2commissionshouyi'] + $agleveldata2['commissionshouyi_parent'];
    //                                 }
				// 				}
				// 			}else{
				// 				$ogupdate['parent2commissionshouyi'] = $agleveldata2['commissionshouyi2'] * $commission_totalprice * 0.01;
    //                             //持续推荐奖励
    //                             if($agleveldata2['commissionshouyi_parent'] > 0 && $ogupdate['parent1commissionshouyi'] > 0 && $ogupdate['parent1']) {
    //                                 $ogupdate['parent2commissionshouyi'] = $ogupdate['parent2commissionshouyi'] + $ogupdate['parent1commissionshouyi'] * $agleveldata2['commissionshouyi_parent'] * 0.01;
    //                             }
				// 			}
    //                     }
    //                     if($agleveldata3)
    //                     {
    //                         if($agleveldata3['commissiontype']==1){
				// 				if($istc3==0){
				// 					$ogupdate['parent3commissionshouyi'] = $agleveldata3['commissionshouyi3'];
				// 					$istc3 = 1;
    //                                 //持续推荐奖励
    //                                 if($agleveldata3['commissionshouyi_parent'] > 0 && $ogupdate['parent2']) {
    //                                     $ogupdate['parent3commissionshouyi'] = $ogupdate['parent3commissionshouyi'] + $agleveldata3['commissionshouyi_parent'];
    //                                 }
				// 				}
				// 			}else{
				// 				$ogupdate['parent3commissionshouyi'] = $agleveldata3['commissionshouyi3'] * $commission_totalprice * 0.01;
    //                             //持续推荐奖励
    //                             if($agleveldata3['commissionshouyi_parent'] > 0 && $ogupdate['parent2commissionshouyi'] > 0 && $ogupdate['parent2']) {
    //                                 $ogupdate['parent3commissionshouyi'] = $ogupdate['parent3commissionshouyi'] + $ogupdate['parent2commissionshouyi'] * $agleveldata3['commissionshouyi_parent'] * 0.01;
    //                             }
				// 			}
    //                     }
                        // if($agleveldata4['commissionshouyi_parent'] > 0 && $ogupdate['parent3']){
                        //      if($agleveldata3['commissiontype']==1){
                        //         $ogupdate['parent4commissionshouyi'] = $agleveldata4['commissionshouyi_parent'];
                        //     } else {
                        //         $ogupdate['parent4commissionshouyi'] = $ogupdate['parent3commissionshouyi'] * $agleveldata4['commissionshouyi_parent'] * 0.01;
                        //     }
                        // }
                        
                        
					}
					//分红佣金
					if($agleveldata2){
						if($ogupdate['parent1commission']>0&&$agleveldata2['fh_commission2']>0){
						    $ogupdate['fhparent2commission'] = $ogupdate['parent1commission']*$agleveldata2['fh_commission2']* 0.01;
						}
					}
					if($agleveldata3){
						if($ogupdate['parent1commission']>0&&$agleveldata3['fh_commission3']>0){
						    $ogupdate['fhparent3commission'] = $ogupdate['parent1commission']*$agleveldata3['fh_commission3']* 0.01;
						}	
					}
					if($agleveldata4){
						if($ogupdate['parent1commission']>0&&$agleveldata4['fh_commission4']>0){
						    $ogupdate['fhparent4commission'] = $ogupdate['parent1commission']*$agleveldata4['fh_commission4']* 0.01;
						}	
					}
					//消费值
					$ogupdate['xfzparent2commission'] = 0;
                    $ogupdate['xfzparent3commission'] = 0;
                    $ogupdate['xfzparent4commission'] = 0;
                    if($product['commissionset'] == 0){
    					if($agleveldata1){
    						if($agleveldata1['xfz_commission2']>0){
    						     $ogupdate['xfzparent2id']  = $parent1['id'];
    						    if($agleveldata1['commissiontype'] == 1)
    						    {
    						         $ogupdate['xfzparent2commission'] = $agleveldata1['xfz_commission2']*$num;
    						    }else{
    						        $ogupdate['xfzparent2commission'] = $commission_totalprice*$agleveldata1['xfz_commission2']* 0.01;
    						    }
    						}
    					}
    					if($agleveldata2){
    						if($agleveldata2['xfz_commission3']>0){
    						    $ogupdate['xfzparent3id']  = $parent2['id'];
    						    if($agleveldata2['commissiontype'] == 1)
    						    {
    						        $ogupdate['xfzparent3commission'] = $agleveldata2['xfz_commission3']*$num;
    						    }else{
    						        $ogupdate['xfzparent3commission'] = $commission_totalprice*$agleveldata2['xfz_commission3']* 0.01;
    						    }
    						}	
    					}
    					if($agleveldata3){
    						if($agleveldata3['xfz_commission4']>0){
    						    $ogupdate['xfzparent4id']  = $parent3['id'];
    						    if($agleveldata3['commissiontype'] == 1)
    						    {
    						        $ogupdate['xfzparent4commission'] = $agleveldata3['xfz_commission4']*$num;
    						    }else{
    						        $ogupdate['xfzparent4commission'] = $commission_totalprice*$agleveldata3['xfz_commission4']* 0.01;
    						    }
    						}	
    					}
                    }
					//平级奖
					if($agleveldata4 && $ogupdate['parent3'] && $ogupdate['parent3commission'] > 0 && $agleveldata3['id'] == $agleveldata4['id']){
						$agleveldata4['commissionpingjitype'] = 0;
						if($product['commissionpingjiset'] != 0){
							if($product['commissionpingjiset'] == 1){
								$commissionpingjidata1 = json_decode($product['commissionpingjidata1'],true);
								$agleveldata4['commission_parent_pj'] = $commissionpingjidata1[$agleveldata4['id']]['commission'];
							}elseif($product['commissionpingjiset'] == 2){
								$commissionpingjidata2 = json_decode($product['commissionpingjidata2'],true);
								$agleveldata4['commission_parent_pj'] = $commissionpingjidata2[$agleveldata4['id']]['commission'];
								$agleveldata4['commissionpingjitype'] = 1;
							}else{
								$agleveldata4['commission_parent_pj'] = 0;
							}
						}
						if($agleveldata4['commission_parent_pj'] > 0) {
							if($agleveldata4['commissionpingjitype']==0){
								$ogupdate['parent4commission'] = $ogupdate['parent3commission'] * $agleveldata4['commission_parent_pj'] * 0.01;
							} else {
								$ogupdate['parent4commission'] = $agleveldata4['commission_parent_pj'];
							}
							$ogupdate['parent4'] = $parent4['id'];
						}
					}
					if($agleveldata3 && $ogupdate['parent2'] && $ogupdate['parent2commission'] > 0 && $agleveldata2['id'] == $agleveldata3['id']){
						$agleveldata3['commissionpingjitype'] = 0;
						if($product['commissionpingjiset'] != 0){
							if($product['commissionpingjiset'] == 1){
								$commissionpingjidata1 = json_decode($product['commissionpingjidata1'],true);
								$agleveldata3['commission_parent_pj'] = $commissionpingjidata1[$agleveldata3['id']]['commission'];
							}elseif($product['commissionpingjiset'] == 2){
								$commissionpingjidata2 = json_decode($product['commissionpingjidata2'],true);
								$agleveldata3['commission_parent_pj'] = $commissionpingjidata2[$agleveldata3['id']]['commission'];
								$agleveldata3['commissionpingjitype'] = 1;
							}else{
								$agleveldata3['commission_parent_pj'] = 0;
							}
						}
						if($agleveldata3['commission_parent_pj'] > 0){
							if(!$ogupdate['parent3']){
								$ogupdate['parent3commission'] = 0;
								$ogupdate['parent3'] = $parent3['id'];
							}
							if($agleveldata3['commissionpingjitype'] == 0){
								$ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $ogupdate['parent2commission'] * $agleveldata3['commission_parent_pj'] * 0.01;
							}else{
								$ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $agleveldata3['commission_parent_pj'];
							}
						}
					}
					if($agleveldata2 && $ogupdate['parent1'] && $ogupdate['parent1commission'] > 0 && $agleveldata1['id'] == $agleveldata2['id']){
						$agleveldata2['commissionpingjitype'] = 0;
						if($product['commissionpingjiset'] != 0){
							if($product['commissionpingjiset'] == 1){
								$commissionpingjidata1 = json_decode($product['commissionpingjidata1'],true);
								$agleveldata2['commission_parent_pj'] = $commissionpingjidata1[$agleveldata2['id']]['commission'];
							}elseif($product['commissionpingjiset'] == 2){
								$commissionpingjidata2 = json_decode($product['commissionpingjidata2'],true);
								$agleveldata2['commission_parent_pj'] = $commissionpingjidata2[$agleveldata2['id']]['commission'];
								$agleveldata2['commissionpingjitype'] = 1;
							}else{
								$agleveldata2['commission_parent_pj'] = 0;
							}
						}
						if($agleveldata2['commission_parent_pj'] > 0){
							if(!$ogupdate['parent2']){
								$ogupdate['parent2commission'] = 0;
								$ogupdate['parent2'] = $parent2['id'];
							}
							if($agleveldata2['commissionpingjitype'] == 0){
								$ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $ogupdate['parent1commission'] * $agleveldata2['commission_parent_pj'] * 0.01;
							}else{
								$ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $agleveldata2['commission_parent_pj'];
							}
						}
					}
				}
				if($ogupdate){
					Db::name('shop_order_goods')->where('id',$ogid)->update($ogupdate);
				}
				
				$totalcommission = 0;
				if($product['commissionset']!=4){
				    if($parent2 && $ogupdate['fhparent2commission']>0){
							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$parent2['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['fhparent2commission'],'score'=>0,'remark'=>'下二级购买商品分红','createtime'=>time(),'jl_type'=>2]);
				// 			$totalcommission += $ogupdate['parent2commission'];
						}
					if($parent3 && $ogupdate['fhparent3commission']>0){
							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$parent3['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['fhparent3commission'],'score'=>0,'remark'=>'下三级购买商品分红','createtime'=>time(),'jl_type'=>2]);
				// 			$totalcommission += $ogupdate['parent2commission'];
						}
					if($parent4 && $ogupdate['fhparent4commission']>0){
							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$parent4['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['fhparent4commission'],'score'=>0,'remark'=>'下四级购买商品分红','createtime'=>time(),'jl_type'=>2]);
				// 			$totalcommission += $ogupdate['parent2commission'];
						}
				// 			$totalcommission += $ogupdate['parent2commission'];
				// 		}	
				//      $memberlevel = Db::name('member_level')->where('aid',aid)->where('id',$this->member['levelid'])->find();
				// 	 $newbus_total = $this->member['bus_total'];
				// 	 $comzong = $ogupdate['parent1commission']+$ogupdate['parent2commission']+$ogupdate['parent3commission']+$ogupdate['parent4commission']+$ogupdate['parent5commission']+$ogupdate['parent6commission']+$ogupdate['parent7commission']+$ogupdate['parent8commission']+$ogupdate['parent9commission']+$ogupdate['parent10commission']+$ogupdate['parent11commission']+$ogupdate['parent12commission']+$ogupdate['parent13commission']+$ogupdate['parent14commission']+$ogupdate['parent15commission']+$ogupdate['parent16commission']+$ogupdate['parent17commission']+$ogupdate['parent18commission']+$ogupdate['parent19commission']+$ogupdate['parent20commission'];
				// 	  if($comzong <= $newbus_total ){
        					if(getcustom('plug_ttdz') && $isfg == 1){
        						if($ogupdate['parent1'] && ($ogupdate['parent1commission'] || $ogupdate['parent1score'])){
        							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent1'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent1commission'],'score'=>$ogupdate['parent1score'],'remark'=>'下级复购奖励','createtime'=>time()]);
        							$totalcommission += $ogupdate['parent1commission'];
        						}
        						if($ogupdate['parent2'] && ($ogupdate['parent2commission'] || $ogupdate['parent2score'])){
        							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent2'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent2commission'],'score'=>$ogupdate['parent2score'],'remark'=>'下二级复购奖励','createtime'=>time()]);
        							$totalcommission += $ogupdate['parent2commission'];
        						}
        						if($ogupdate['parent3'] && ($ogupdate['parent3commission'] || $ogupdate['parent3score'])){
        							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent3'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent3commission'],'score'=>$ogupdate['parent3score'],'remark'=>'下三级复购奖励','createtime'=>time()]);
        							$totalcommission += $ogupdate['parent3commission'];
        						}
        					}else{
        					   // 下二十级购买商品奖励
                                $Parentcommission=0;   //收益抽佣奖励：享受下级
        						if($ogupdate['parent1'] && ($ogupdate['parent1commission'] || $ogupdate['parent1score'])){
        						    //查下1下面的创业值
        						  //  如果够就返,不够就返0
        						    if($parent1['bus_total'] >= $ogupdate['parent1commission']){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent1'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent1commission'],'score'=>$ogupdate['parent1score'],'remark'=>'下级用户:'.mid.',购买商品奖励,订单编号:'.$orderdata['ordernum'],'createtime'=>time()]);
            							 \app\common\Member::getzhituichouyong(aid,$ogupdate['parent1'],$ogupdate['parent1commission'],$orderid,$ogid,$orderdata['ordernum']);
            							 \app\common\Member::getjiantuichouyong(aid,$ogupdate['parent1'],$ogupdate['parent1commission'],$orderid,$ogid,$orderdata['ordernum']);
            							$totalcommission += $ogupdate['parent1commission'];
                                        $Parentcommission=$ogupdate['parent1commission'];
        						    }else{
                                        Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent1'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$parent1['bus_total'],'score'=>$ogupdate['parent1score'],'remark'=>'下级用户:'.mid.',购买商品奖励,订单编号:'.$orderdata['ordernum'],'createtime'=>time()]);
                                         \app\common\Member::getzhituichouyong(aid,$ogupdate['parent1'],$parent1['bus_total'],$orderid,$ogid,$orderdata['ordernum']);
                                         \app\common\Member::getjiantuichouyong(aid,$ogupdate['parent1'],$parent1['bus_total'],$orderid,$ogid,$orderdata['ordernum']);
                                        $totalcommission += $parent1['bus_total'];
                                        $Parentcommission=$parent1['bus_total'];
        						    }
                                    //添加记录
                                    \app\common\Member::addParentcommissionLogs(aid,$ogupdate['parent1'],$orderid,$ogid,$Parentcommission,'下级分佣奖励');

        						}
        						if($ogupdate['parent2'] && ($ogupdate['parent2commission'] || $ogupdate['parent2score'])){
        						    if($parent2['bus_total'] >= $ogupdate['parent2commission']){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent2'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent2commission'],'score'=>$ogupdate['parent2score'],'remark'=>'下二级购买商品奖励','createtime'=>time()]);
            							\app\common\Member::getjiantuichouyong(aid,$ogupdate['parent2'],$ogupdate['parent2commission'],$orderid,$ogid,$orderdata['ordernum']);
            							$totalcommission += $ogupdate['parent2commission'];
        						    }else{
        						        Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent2'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$parent2['bus_total'],'score'=>$ogupdate['parent2score'],'remark'=>'下二级购买商品奖励','createtime'=>time()]);
        						         \app\common\Member::getjiantuichouyong(aid,$ogupdate['parent2'],$parent2['bus_total'],$orderid,$ogid,$orderdata['ordernum']);
            							$totalcommission += $parent2['bus_total'];
        						    }
        						}
        						if($ogupdate['parent3'] && ($ogupdate['parent3commission'] || $ogupdate['parent3score'])){
        						    if($parent3['bus_total'] >= $ogupdate['parent3commission']){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent3'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent3commission'],'score'=>$ogupdate['parent3score'],'remark'=>'下三级购买商品奖励','createtime'=>time()]);
            							\app\common\Member::getjiantuichouyong(aid,$ogupdate['parent3'],$ogupdate['parent3commission'],$orderid,$ogid,$orderdata['ordernum']);
            							$totalcommission += $ogupdate['parent3commission'];
        						    }else{
        						        Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent3'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$parent3['bus_total'],'score'=>$ogupdate['parent3score'],'remark'=>'下三级购买商品奖励','createtime'=>time()]);
        						        \app\common\Member::getjiantuichouyong(aid,$ogupdate['parent3'],$parent3['bus_total'],$orderid,$ogid,$orderdata['ordernum']);
            							$totalcommission +=$parent3['bus_total'];
        						    }
        						}
        						if($product['commissionset']  ==1 || $product['commissionset']  ==2 || $product['commissionset']  ==3){
        						    if($ogupdate['parent4'] && ($ogupdate['parent4commission'] || $ogupdate['parent4score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent4'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent4commission'],'score'=>$ogupdate['parent4score'],'remark'=>'下四级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent4commission'];
            						}
            						if($ogupdate['parent5'] && ($ogupdate['parent5commission'] || $ogupdate['parent5score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent5'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent5commission'],'score'=>$ogupdate['parent5score'],'remark'=>'下五级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent5commission'];
            						}
            						if($ogupdate['parent6'] && ($ogupdate['parent6commission'] || $ogupdate['parent6score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent6'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent6commission'],'score'=>$ogupdate['parent6score'],'remark'=>'下六级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent6commission'];
            						}
            						if($ogupdate['parent7'] && ($ogupdate['parent7commission'] || $ogupdate['parent7score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent7'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent7commission'],'score'=>$ogupdate['parent7score'],'remark'=>'下七级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent7commission'];
            						}
            						if($ogupdate['parent8'] && ($ogupdate['parent8commission'] || $ogupdate['parent8score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent8'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent8commission'],'score'=>$ogupdate['parent8score'],'remark'=>'下八级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent8commission'];
            						}
            						if($ogupdate['parent9'] && ($ogupdate['parent9commission'] || $ogupdate['parent9score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent9'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent9commission'],'score'=>$ogupdate['parent9score'],'remark'=>'下九级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent9commission'];
            						}
            						if($ogupdate['parent10'] && ($ogupdate['parent10commission'] || $ogupdate['parent10score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent10'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent10commission'],'score'=>$ogupdate['parent10score'],'remark'=>'下十级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent10commission'];
            						}
            						if($ogupdate['parent11'] && ($ogupdate['parent11commission'] || $ogupdate['parent11score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent11'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent11commission'],'score'=>$ogupdate['parent11score'],'remark'=>'下十一级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent11commission'];
            						}
            						if($ogupdate['parent12'] && ($ogupdate['parent12commission'] || $ogupdate['parent12score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent12'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent12commission'],'score'=>$ogupdate['parent12score'],'remark'=>'下十二级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent12commission'];
            						}
            						if($ogupdate['parent13'] && ($ogupdate['parent13commission'] || $ogupdate['parent13score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent13'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent13commission'],'score'=>$ogupdate['parent13score'],'remark'=>'下十三级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent13commission'];
            						}
            						if($ogupdate['parent14'] && ($ogupdate['parent14commission'] || $ogupdate['parent14score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent14'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent14commission'],'score'=>$ogupdate['parent14score'],'remark'=>'下十四级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent14commission'];
            						}
            						if($ogupdate['parent15'] && ($ogupdate['parent15commission'] || $ogupdate['parent15score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent15'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent15commission'],'score'=>$ogupdate['parent15score'],'remark'=>'下十五级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent15commission'];
            						}
            						if($ogupdate['parent16'] && ($ogupdate['parent16commission'] || $ogupdate['parent16score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent16'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent16commission'],'score'=>$ogupdate['parent16score'],'remark'=>'下十六级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent16commission'];
            						}
            						if($ogupdate['parent17'] && ($ogupdate['parent17commission'] || $ogupdate['parent17score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent17'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent17commission'],'score'=>$ogupdate['parent17score'],'remark'=>'下十七级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent17commission'];
            						}
            						if($ogupdate['parent18'] && ($ogupdate['parent18commission'] || $ogupdate['parent18score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent18'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent18commission'],'score'=>$ogupdate['parent18score'],'remark'=>'下十八级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent18commission'];
            						}
            						if($ogupdate['parent19'] && ($ogupdate['parent19commission'] || $ogupdate['parent19score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent19'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent19commission'],'score'=>$ogupdate['parent19score'],'remark'=>'下十九级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent19commission'];
            						}
            						if($ogupdate['parent20'] && ($ogupdate['parent20commission'] || $ogupdate['parent20score'])){
            							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent20'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent20commission'],'score'=>$ogupdate['parent20score'],'remark'=>'下二十级购买商品奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent20commission'];
            						}
        						}else{
                                    if($ogupdate['parent4'] && ($ogupdate['parent4commission'])){
                                        Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent4'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent4commission'],'score'=>0,'remark'=>'持续推荐奖励','createtime'=>time()]);
            							$totalcommission += $ogupdate['parent4commission'];
            							\app\common\Member::getjiantuichouyong(aid,$ogupdate['parent4'],$ogupdate['parent4commission'],$orderid,$ogid,$orderdata['ordernum']);
                                    }
        						}
        					}
				// 	if($ogupdate['parent1'] && $ogupdate['parent1commissionshouyi'])
				// 	{
				// 	    Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent1'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent1commissionshouyi'],'score'=>0,'remark'=>'下一级购买商品收益抽佣','createtime'=>time()]);
				// 	}
				// 	if($ogupdate['parent2'] && $ogupdate['parent2commissionshouyi'])
				// 	{
				// 	    Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent2'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent2commissionshouyi'],'score'=>0,'remark'=>'下二级购买商品收益抽佣','createtime'=>time()]);
				// 	}
				// 	if($ogupdate['parent3'] && $ogupdate['parent3commissionshouyi'])
				// 	{
				// 	    Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent3'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent3commissionshouyi'],'score'=>0,'remark'=>'下三级购买商品收益抽佣','createtime'=>time()]);
				// 	}
				// 	if($ogupdate['parent4'] && $ogupdate['parent4commissionshouyi'])
				// 	{
				// 	    Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$ogupdate['parent4'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$ogupdate['parent4commissionshouyi'],'score'=>0,'remark'=>'下四级购买商品收益抽佣','createtime'=>time()]);
				// 	}
					if($post['checkmemid'] && $commission_totalprice > 0){
						$checkmember = Db::name('member')->where('aid',aid)->where('id',$post['checkmemid'])->find();
						if($checkmember){
							$buyselect_commission = Db::name('member_level')->where('id',$checkmember['levelid'])->value('buyselect_commission');
							$checkmemcommission = $buyselect_commission * $commission_totalprice * 0.01;
							Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$checkmember['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$checkmemcommission,'score'=>0,'remark'=>'购买商品时指定奖励','createtime'=>time()]);
						}
					}
					
					
				}

				
				if($product['commissionset4']==1 && $product['lvprice']==1){ //极差分销
					if(getcustom('jicha_removecommission')){ //算极差时先减去分销的钱
						$commission_totalpriceCache = $commission_totalpriceCache - $totalcommission;
					}
					if($this->member['path']){
						$parentList = Db::name('member')->where('id','in',$this->member['path'])->order(Db::raw('field(id,'.$this->member['path'].')'))->select()->toArray();
						if($parentList){
							$parentList = array_reverse($parentList);
							$lvprice_data = json_decode($guige['lvprice_data'],true);
							$nowprice = $commission_totalpriceCache;
							$giveidx = 0;
							foreach($parentList as $k=>$parent){
								if($parent['levelid'] && $lvprice_data[$parent['levelid']]){
									$thisprice = floatval($lvprice_data[$parent['levelid']]) * $num;
									if($nowprice > $thisprice){
										$commission = $nowprice - $thisprice;
										$nowprice = $thisprice;
										$giveidx++;
										//if($giveidx <=3){
										//	$ogupdate['parent'.$giveidx] = $parent['id'];
										//	$ogupdate['parent'.$giveidx.'commission'] = $commission;
										//}
										Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$parent['id'],'frommid'=>mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$commission,'score'=>0,'remark'=>'下级购买商品差价','createtime'=>time()]);
									}
								}
							}
						}
					}
				}

				if(getcustom('commission_givedown')){
					$commission_recordlist = Db::name('member_commission_record')->field("mid,sum(commission) totalcommission")->where('aid',aid)->where('orderid',$orderid)->where('ogid',$ogid)->where('type','shop')->where('commission','>',0)->group('mid')->select()->toArray();
					foreach($commission_recordlist as $record){
						$thismember = Db::name('member')->where('id',$record['mid'])->find();
						$memberlevel = Db::name('member_level')->where('id',$thismember['levelid'])->find();
						if($memberlevel && ($memberlevel['givedown_percent'] > 0 || $memberlevel['givedown_commission'] > 0)){
							$downmemberlist = Db::name('member')->where('aid',aid)->where('pid',$record['mid'])->select()->toArray();
							if(!$downmemberlist) continue;
							$downcommission = $memberlevel['givedown_commission'] / count($downmemberlist) + $record['totalcommission'] * $memberlevel['givedown_percent'] * 0.01 / count($downmemberlist);
							foreach($downmemberlist as $downmember){
								Db::name('member_commission_record')->insert(['aid'=>aid,'mid'=>$downmember['id'],'frommid'=>$thismember['id'],'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$downcommission,'score'=>0,'remark'=>$memberlevel['givedown_txt'],'createtime'=>time()]);
							}
						}
					}
				}

				if(getcustom('everyday_hongbao')) {
                    $hongbaoEdu = 0;
                    if($product['everyday_hongbao_bl'] === null) {
                        $hongbaoEdu = $og_totalprice * $hd['shop_product_hongbao_bl'] / 100;
                    } elseif($product['everyday_hongbao_bl'] > 0 ) {
                        $hongbaoEdu = $og_totalprice * $product['everyday_hongbao_bl'] / 100;
                    }
                    $hongbaoEdu = round($hongbaoEdu,2);
                    if($hongbaoEdu > 0)
                        Db::name('shop_order_goods')->where('id',$ogid)->update(['hongbaoEdu' => $hongbaoEdu]);
                }
				//删除购物车
				Db::name('shop_cart')->where('aid',aid)->where('mid',mid)->where('ggid',$guige['id'])->where('proid',$product['id'])->delete();
				if($member['pid'] == 0)
				{
				    //减库存
    				Db::name('tuanzhang_guige')->where('aid',aid)->where('id',$guige['id'])->update(['stock'=>Db::raw("stock-$num"),'sales'=>Db::raw("sales+$num")]);
    				Db::name('shop_product')->where('aid',aid)->where('id',$product['id'])->update(['stock'=>Db::raw("stock-$num"),'sales'=>Db::raw("sales+$num")]);
    				// \app\common\Member::kouzuhekuucn($product,$num,aid);
    				if(getcustom('guige_split')){
    					\app\model\ShopProduct::declinkstock($product['id'],$guige['id'],$num);
    				}
				}else{
				     $yunkucun_status = \app\common\Member::getyunkucun(aid);
				     $goodssttaus = \app\common\Member::isyunproduct(aid,$product['id']);
				     if($yunkucun_status == 1  && $goodssttaus){
    				      if($ykcrs['zouhuo'] == 0)
                		  {
                		      if($iszuhe){
                    		      if($product['kuncun_type']==1)
                    		      {
                    		         $kuncun_type = 0; 
                    		      }else{
                    		          $kuncun_type = 1;
                    		      }
                		      }else{
                		          $kuncun_type=0;
                		      }
                		     //扣除上级库存,给上级佣金 ($aid,$mid,$proid,$ggid,$num,$ordergoods,$remark='')
                		      \app\common\Member::yunkucunlog(aid,$member['pid'],$product['id'],$guige['id'],'-'.$num,$ogdata,'下级下单,扣除');
                		      //扣除上级库存2
                		      //\app\common\Member::kouzuhekuucn2($product,$num,aid,$member['pid']);
                		      Db::name('shop_order_goods')->where('aid',aid)->where('orderid',$orderid)->update(['iszuhe'=>$iszuhe,'iszuheshow'=>$kuncun_type,'zouhuoshouxu'=>$ykcrs['shouxu'],'ti'=>2,'nahuoid'=>$member['pid'],'nahuoArr'=>json_encode([$member['pid']=>$orderdata['totalprice']])]);//云库存订单
                		      Db::name('shop_order')->where('aid',aid)->where('id',$orderid)->update(['zouhuoshouxu'=>$ykcrs['shouxu'],'ti'=>2,'nahuoid'=>$member['pid'],'nahuoArr'=>json_encode([$member['pid']=>$orderdata['totalprice']])]);//云库存订单
                		  }else{
                		        //无限查上级
                		         if(!empty($member['path']))
                		         {
                		             $curmemberlevel = Db::name('member_level')->where('aid',aid)->where('id',$member['levelid'])->find();
                		             $koujianArr = \app\common\Member::isshnagjiyunkuncun2(aid,$member['path'],$buydata,$curmemberlevel,$member['id'],[],$iszuhe);
                		             $nahuoArr = $koujianArr['nahuoArr'];
                		             if($koujianArr['nahuoid'] >0 )
                		             {
                		                  $cccc =\app\common\Member::yunkucunlog(aid,$koujianArr['nahuoid'],$product['id'],$guige['id'],'-'.$num,$ogdata,'下级下单,扣除');
                		                  //\app\common\Member::kouzuhekuucn2($product,$num,aid,$koujianArr['nahuoid']);
                		             }else{
                		                 //减库存
                            				Db::name('tuanzhang_guige')->where('aid',aid)->where('id',$guige['id'])->update(['stock'=>Db::raw("stock-$num"),'sales'=>Db::raw("sales+$num")]);
                            				Db::name('shop_product')->where('aid',aid)->where('id',$product['id'])->update(['stock'=>Db::raw("stock-$num"),'sales'=>Db::raw("sales+$num")]);
                            				// \app\common\Member::kouzuhekuucn($product,$num,aid);
                            				if(getcustom('guige_split')){
                            					\app\model\ShopProduct::declinkstock($product['id'],$guige['id'],$num);
                            				}
                		             }
                		             if($iszuhe){
                        		      if($product['kuncun_type']==1)
                        		      {
                        		         $kuncun_type = 0; 
                        		      }else{
                        		          $kuncun_type = 1;
                        		      }
                		             }else{
                		                 $kuncun_type=0;
                		             }
                		              Db::name('shop_order_goods')->where('aid',aid)->where('orderid',$orderid)->update(
                		                  ['iszuhe'=>$iszuhe,'iszuheshow'=>$kuncun_type,'zouhuoshouxu'=>$ykcrs['shouxu'],'ti'=>3,'nahuoid'=>$koujianArr['nahuoid'],'nahuoArr'=>json_encode($koujianArr['nahuoArr'])]
                		                  );//云库存走货订单
                		              Db::name('shop_order')->where('aid',aid)->where('id',$orderid)->update(
                		                   ['zouhuoshouxu'=>$ykcrs['shouxu'],'ti'=>3,'nahuoid'=>$koujianArr['nahuoid'],'nahuoArr'=>json_encode($koujianArr['nahuoArr'])]
                		                  );//云库存走货订单
                		             ;
                		         }
                		 }
				     }else{
				          //减库存
        				Db::name('tuanzhang_guige')->where('aid',aid)->where('id',$guige['id'])->update(['stock'=>Db::raw("stock-$num"),'sales'=>Db::raw("sales+$num")]);
        				Db::name('shop_product')->where('aid',aid)->where('id',$product['id'])->update(['stock'=>Db::raw("stock-$num"),'sales'=>Db::raw("sales+$num")]);
        				// \app\common\Member::kouzuhekuucn($product,$num,aid);
        				if(getcustom('guige_split')){
        					\app\model\ShopProduct::declinkstock($product['id'],$guige['id'],$num);
        				}
				     }
				}
			
			}
				// 	die;


			//公众号通知 订单提交成功
			$tmplcontent = [];
			$tmplcontent['first'] = '有新订单提交成功';
			$tmplcontent['remark'] = '点击进入查看~';
			$tmplcontent['keyword1'] = $store_name; //店铺
			$tmplcontent['keyword2'] = date('Y-m-d H:i:s',$orderdata['createtime']);//下单时间
			$tmplcontent['keyword3'] = $orderdata['title'];//商品
			$tmplcontent['keyword4'] = $orderdata['totalprice'].'元';//金额
			\app\common\Wechat::sendhttmpl(aid,$orderdata['bid'],'tmpl_orderconfirm',$tmplcontent,m_url('admin/order/shoporder'),$orderdata['mdid']);
			
			$tmplcontent = [];
			$tmplcontent['thing11'] = $orderdata['title'];
			$tmplcontent['character_string2'] = $orderdata['ordernum'];
			$tmplcontent['phrase10'] = '待付款';
			$tmplcontent['amount13'] = $orderdata['totalprice'].'元';
			$tmplcontent['thing27'] = $this->member['nickname'];
			\app\common\Wechat::sendhtwxtmpl(aid,$orderdata['bid'],'tmpl_orderconfirm',$tmplcontent,'admin/order/shoporder',$orderdata['mdid']);
		}

// 		$single_s_info	= $this->get_shop_pay($buydata);
		
		$single_s_info = implode(",",$single_s_info);
		// 		var_dump($product);
// 		exit();
        //统一判断信息
        if($product['is_newcustom'] == 1 && $this->member['is_yh'] == 0){
            //查询用户的订单信息
            $checknum = Db::name('shop_order_goods')->where('proid',$product['id'])->where('mid',mid)->sum('num');
            $order_goodss = Db::name('shop_order_goods')->where('ordernum',$ordernum)->select();
            if(!empty($post['newarr'])){
                $newarr = json_decode($post['newarr'],true);
            }
            // var_dump($post['newarr']);
            // exit();
            foreach ($post['newarr'] as $nkey=>$nval){
                foreach ($order_goodss as $keys=>$vals){
                    if($vals['proid'] == $nval['product_id']){
                        $all_num = $vals['num'] + ($checknum - $vals['num']);
                        // //如果没有超出则进行正常记录信息
                        if($all_num <= $product['yh_num']){
                            Db::name('shop_order_goods')->where('id',$vals['id'])->update([
                                "is_yh"=>1,
                                "yh_nums"=>$nval['yh_num'],
                                "yh_prices"=>$nval['yh_price'],
                            ]);
                        }
                        //如果超出则增加一条记录并减少正常记录数据
                        if($all_num > $product['yh_num']){
                            $ydd = $all_num - $product['yh_num'];
                            Db::name('shop_order_goods')->where('id',$vals['id'])->update([
                                "is_yh"=>0,
                                "num"=>$ydd
                            ]);
                            unset($vals['id']);
                            $vals['num'] = $nval['yh_num'];
                            $vals['yh_prices'] = $nval['yh_price'];
                            $vals['yh_nums'] = $nval['yh_num'];
                            $vals['is_yh'] = 1;
                            //var_dump($nval);
                            $gdatas = $vals;
                            //var_dump($gdatas);
                            Db::name('shop_order_goods')->insertGetId($gdatas);
                        }

                    }
                }
            }
            $alltotalprice = 100;
        }
        // exit();
		if(count($buydata) > 1){ //创建合并支付单
			$payorderid = \app\model\Payorder::createorder(aid,0,mid,'shop_hb',$orderid,$ordernum,$orderdata['title'],$alltotalprice,$alltotalscore,$single_s_info);
		}
		    //这里统计一下订单数量
			$knum = 0;
			$shops = Db::name('shop_order_goods')->where('ordernum',$ordernum)->select();
			foreach($shops as $key=>$val){
				$knum += $val['num'];
			}
			//查询用户
			$user = Db::name('member')->where('id',mid)->find();
			Db::name('member')->where('id',mid)->update([
				"kuang_num"=>$user['knum'] + $knum,
			]);
			// Db::name('shop_order')->where('ordernum',$ordernum)->update([
			//     "knum"=>$knum,
			// ]);
		//
		$param = input();
        $d_orderid = $param['d_orderid'];
        
        if(!empty($d_orderid))
        {
            $this->cancel_d_order($d_orderid);
        }
        
		return $this->json(['status'=>1,'payorderid'=>$payorderid,'msg'=>'提交成功']);
	}
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
}