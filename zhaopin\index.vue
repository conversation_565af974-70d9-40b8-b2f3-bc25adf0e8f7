<template> 
	<view>
		<loading v-if="loading"></loading>
		<page-meta :pageStyle="'overflow: ' + (listFilterVisitor ? 'hidden' : 'auto')"></page-meta>
		<view class="container">
			<view class="select-job-area" v-if="firstLoaded">
				<view :class="'gradient-bg fixed white'"
					:style="'top: 0px; z-index: 999;'">
					<tab 
						ref="jobTab"
						@tabChange="tabChange" 
						:tabList="tabList"
						:current="jobItemType"
						:style="'top: 0px; z-index: 999;'">
					</tab>
					<view v-if="showFilter">
						<list-filter
							:areas="areas"
							@change="filterChange"
							@visitorChange="(e) => listFilterVisitor = e.detail"
							class="filterRef"
							className="index-filter"
							:clearingList="clearingForms"
							:controlPop="controlPop"
							:jobtypeList="classifications"
							:ptpId="tabList[jobItemType].key"
							:sortRules="sortRules"
							:typeIndex="jobItemType"
							:userSex="userSex"
						></list-filter>
					</view>
				</view>

				<swiper class="swiper-container" 
					:current="jobItemType" 
					@change="handleSwiperChange"
					:style="{ height: 'calc(100vh - ' + (showFilter ? '206rpx' : '106rpx') + ')' }">
					<swiper-item v-for="(tab, index) in tabList" :key="index">
						<scroll-view 
							scroll-y 
							@scrolltolower="onReachBottom"
							:class="'school-life-job-list ' + (isFixed && showFilter ? 'padding206' : isFixed && !showFilter ? 'padding106' : tabList[jobItemType].key !== '1' ? 'padding16' : '') + ' ' + (tabList[jobItemType].key === '4' ? 'padding0' : '')"
						>
							<position-guide 
								@handleTapGuide="locationHandle" 
								v-if="tabList[jobItemType].key === '3' && showGuide">
							</position-guide>
							<regular-item
								@btnFresh="btFresh"
								:data="item"
								:index="idx"
								:listIndex="Number(tabList[jobItemType].key)"
								:ptpId="'jfb2-dn2b-dn1b-718d-' + tabList[jobItemType].key"
								:type="jobItemType"
								v-for="(item, idx) in homeList"
								:key="idx"
							></regular-item>
							<view class="noneList" v-if="homeList.length === 0 && !loading">
								<image lazyLoad mode="widthFix" :src="pre_url+'/static/img/wuzhaopin.png'"></image>
								<text>此时内心是空荡荡的</text>
							</view>
						</scroll-view>
					</swiper-item>
				</swiper>
			</view>
		</view>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
import listFilter from './components/newListFilter/index';
import positionGuide from './components/positionGuide/index';
import regularItem from './components/regularItem/index';
import tab from './components/tab/index';

var app = getApp();
export default {
	components: {
		listFilter,
		positionGuide,
		regularItem,
		tab
	},
	onLoad(opt) {
		this.opt = app.getopts(opt);
		this.getCategoryList();
		this.getTypeList();
	},
	onPullDownRefresh() {
		this.pageNum = 1;
		this.getModuleList(this.currentJobType);
	},
	onReachBottom() {
		if (!this.loading && !this.noMore) {
			this.pageNum++;
			this.getModuleList(this.currentJobType);
		}
	},
	data() {
		return {
			opt: {},
			loading: false,
			listFilterVisitor: false,
			townName: '茂名',
			tabList: [], // 移除静态数据，改为动态获取
			currentJobType: '', // 改为字符串类型
			loinTotal: 0,
			isSign: false,
			pageNum: 1,
			pageSize: 20,
			isEnd: false,
			jobItemType: 0,
			prevItemType: 0,
			homeList: [],
			showFilter: true,
			jobHomeList: [],
			isSearchFixed: false,
			isFixed: false,
			isLoading: true,
			firstLoaded: true,
			fixedClass: 'padding206',
			listGroupId: 1012,
			pre_url: getApp().globalData.pre_url, // 添加pre_url变量
			classifications: [], // 职位类型列表，格式：[{id, pid, name, sort, children}]
			sortRules: [
				{
					key: '',
					value: '默认'
				},
				{
					key: '2',
					value: '离我近'
				},
				{
					key: '3',
					value: '最新'
				}
			],
			areas: [
				{
					areaId: 0,
					townId: 0,
					areaName: '不限'
				},
				{
					areaId: 1806,
					townId: 205,
					areaName: '茂南区'
				},
				{
					areaId: 1809,
					townId: 205,
					areaName: '高州市'
				},
				{
					areaId: 1810,
					townId: 205,
					areaName: '化州市'
				},
				{
					areaId: 1811,
					townId: 205,
					areaName: '信宜市'
				},
				{
					areaId: 2939,
					townId: 205,
					areaName: '电白区'
				}
			],
			clearingForms: [
				{
					key: '99',
					value: '不限'
				},
				{
					key: '1',
					value: '日结'
				},
				{
					key: '2',
					value: '周结'
				},
				{
					key: '3',
					value: '月结'
				},
				{
					key: '4',
					value: '完工结算'
				},
				{
					key: '0',
					value: '其他结算'
				}
			],
			userSex: '',
			classList: [],
			searchList: [
				{
					name: '服务员',
					hot: 0,
					sourceType: 1
				},
				{
					name: '超市零售',
					hot: 0,
					sourceType: 1
				},
				{
					name: '日结兼职',
					hot: 0,
					sourceType: 2
				},
				{
					name: '日结',
					hot: 0,
					sourceType: 2
				},
				{
					name: '寒假工',
					hot: 0,
					sourceType: 2
				},
				{
					name: '夜班',
					hot: 0,
					sourceType: 2
				},
				{
					name: '配音',
					hot: 0,
					sourceType: 2
				},
				{
					name: '奶茶',
					hot: 0,
					sourceType: 2
				},
				{
					name: '剪辑',
					hot: 0,
					sourceType: 2
				},
				{
					name: '发传单',
					hot: 0,
					sourceType: 2
				}
			],
			showGuide: false,
			commandFilterData: {
				type_id: '',
				clearingForms: '',
				areaIds: '',
				sexRequire: '',
				sortRules: ''
			},
			controlPop: '',
			hostExistsJobIds: '',
			clearWordChange: false,
			collectImage: '',
			collectContentId: '',
			collectVisible: false,
			newUserTemplate: '',
			isCollectDialog: false,
			universeParam: {},
			refreshLocation: 17,
			firstPageJobNum: 10,
			waitRenderList: [],
			noMore: false,
			contentHeight: 0, // 新增：内容区域高度
			jobList: {}, // 修改为对象形式，存储各个tab的数据
			loadMoreStatus: {} // 修改为对象形式，存储各个tab的加载状态
		};
	},
	methods: {
		getCategoryList() {
			const that = this;
			app.get('apiZhaopin/getCategoryList', {}, function(res) {
				if (res.status == 1) {
					// 添加"推荐"选项
					const recommendedOption = {
						key: '0',
						value: '推荐'
					};
					that.tabList = [recommendedOption, ...res.data.map(item => ({
						key: item.id.toString(),
						value: item.name
					}))];
					
					// 默认选中"推荐"选项
					that.currentJobType = recommendedOption.key;
					that.getModuleList(recommendedOption.key);
				} else {
					app.alert(res.msg || '获取分类列表失败');
				}
			});
		},
		tabChange(index) {
			// 添加日志
			console.log('tabChange triggered:', {
				index,
				currentIndex: typeof index === 'object' ? index.detail.index : index,
				oldJobItemType: this.jobItemType
			});
			
			// 如果传入的是事件对象，则从detail中获取index
			const currentIndex = typeof index === 'object' ? index.detail.index : index;
			
			if (this.jobItemType === currentIndex) return;
			
			// 更新数据
			this.jobItemType = currentIndex;
			this.currentJobType = this.tabList[currentIndex].key;
			this.pageNum = 1;
			this.homeList = [];
			this.getModuleList(this.currentJobType);
			
			// 强制更新组件
			this.$forceUpdate();
		},
		getModuleList: function(params) {
			var that = this;
			that.loading = true;
			
			// 如果传入的是字符串（categoryId），转换为对象格式
			if (typeof params === 'string') {
				params = {
					category_id: params
				};
			}
			
			const requestParams = {
				page: that.pageNum || 1,
				limit: 10,
				...params,
				keyword: that.searchKeyword || ''
			};
			
			console.log('请求参数:', requestParams);
			
			app.get('apiZhaopin/getPositionList', requestParams, function(res) {
				that.loading = false;
				if (res.status == 1 && res.data.list) {
					// 处理返回的数据
					const processedList = res.data.list.map(item => ({
						partJobId: item.id,
						title: item.title,
						titleSimple: item.title,
						salary: item.salary,
						companyName: item.company?.name || '',
						logo: item.company?.logo || 'https://qiniu-image.qtshe.com/company_logo_default.png',
						addressDetail: item.address || '',
						distance: item.distance ? item.distance + 'km' : '',
						jobLineType: 1,
						category: 1,
						entryCount: item.views || 0,
						companyType: {
							key: '1',
							value: '企业'
						},
						labelList: {
							serviceLabels: [
								{
									labelId: 61,
									labelName: '企业认证',
									labelStyle: '{"id":10,"icon":"","color":"#72AAFA","borderColor":"#72AAFA","background":"#FFFFFF"}'
								}
							],
							descLabels: Object.entries(item.formatted_options || {}).map(([type, tags]) => 
								tags.map(tag => ({
									labelId: Math.random(),
									labelName: tag,
									labelStyle: '{"id":6,"icon":"","color":"#FA5555","borderColor":"#FEEEEE","background":"#FEEEEE"}'
								}))
							).flat()
						},
						listStyle: 1
					}));
					
					// 更新列表数据
					if (that.pageNum === 1) {
						that.homeList = processedList;
					} else {
						that.homeList = [...that.homeList, ...processedList];
					}
					
					// 更新加载状态
					that.noMore = processedList.length < 10;
					that.firstLoaded = true;
					
					console.log('获取职位列表成功:', that.homeList);
				} else {
					app.alert(res.msg || '获取职位列表失败');
				}
				
				uni.stopPullDownRefresh();
			});
		},
		filterChange(data) {
			this.commandFilterData = data.detail;
			const params = {
				...this.commandFilterData,
				category_id: this.tabList[this.jobItemType].key
			};
			this.pageNum = 1;
			this.getModuleList(params);
		},
		btFresh() {
			this.pageNum = 1;
			this.getModuleList(this.tabList[this.jobItemType].key);
		},
		locationHandle() {
			this.showGuide = false;
		},
		getTypeList(pid = -1) {
			const that = this;
			app.get('apiZhaopin/getTypeList', { pid }, function(res) {
				if (res.status == 1) {
					// 转换数据结构，保持原有字段不变，添加组件所需字段
					const formattedData = res.data.map(item => ({
						...item, // 保留原有所有字段
						classificationId: item.id,
						secondClassifications: (item.children || []).map(child => ({
							...child, // 保留原有所有字段
							classificationId: child.id,
							parentId: child.pid
						}))
					}));
					
					// 添加"全部"选项
					const allOption = {
						id: 0,
						pid: 0,
						name: '全部',
						sort: 0,
						classificationId: 0,
						secondClassifications: []
					};
					
					that.classifications = [allOption, ...formattedData];
				} else {
					app.alert(res.msg || '获取职位类型失败');
				}
			});
		},
		// 修改 handleSwiperChange 方法
		handleSwiperChange(e) {
			const index = e.detail.current;
			console.log('handleSwiperChange:', {
				index,
				currentJobItemType: this.jobItemType
			});
			
			// 避免重复触发
			if (this.jobItemType === index) return;
			
			// 直接调用 tabChange 方法
			this.tabChange(index);
		},
		// 新增：计算内容区域高度
		calculateContentHeight() {
			const query = uni.createSelectorQuery().in(this)
			query.select('.container').boundingClientRect(data => {
				const windowHeight = uni.getSystemInfoSync().windowHeight
				this.contentHeight = windowHeight - data.top
			}).exec()
		}
	},
	mounted() {
		this.calculateContentHeight()
		this.tabChange(0)
	},
	watch: {
		jobItemType(newVal) {
			console.log('jobItemType changed:', newVal);
			// 强制更新组件
			this.$forceUpdate();
		}
	}
};
</script>

<style lang="scss">
.container {
	width: 100%;
	height: 100%;
	background-color: #f5f5f5;
	overflow: hidden;
	.select-job-area {
		width: 100%;
		height: 100%;
		overflow: hidden;
		.school-life-job-list {
			width: 100%;
			height: 100%;
			overflow-y: auto;
			overflow-x: hidden;
			padding-bottom: 100rpx;
			padding-top: 10rpx;
			box-sizing: border-box;
			.noneList {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				image {
					width: 400rpx;
					height: 400rpx;
				}
				text {
					font-size: 32rpx;
					color: #999;
					margin-top: 20rpx;
				}
			}
		}
	}
}

.tab-content {
	flex: 1;
	width: 100%;
}

.scroll-content {
	height: 100%;
}

.job-list {
	padding: 20rpx;
}

.swiper-container {
	width: 100%;
	background-color: #f5f5f5;
}
</style>
