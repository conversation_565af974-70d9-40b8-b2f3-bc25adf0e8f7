<template>
	
	<view>
	
		<view style="padding-bottom: 50px;">
			
			
			<view class="option">
				<view class="option_bt" :class="[selectedOption=='0'? 'option_get':'']" @click="selectedOption='0'" >全部</view>
				<view class="option_bt" :class="[selectedOption=='1'? 'option_get':'']" @click="selectedOption='1'">待评价</view>
			</view>
			
			<view class="ll" v-for="(item, index) in list" :key="index">
				
				<view @tap="goDetail(item.id)">
				
			     <view class="ll-tit">
					 
					  <text style="font-size: 34rpx;font-weight: bold;">{{item.venues_title}}</text> 
					 
					  <text v-if="item.status == 2"> 已完成</text>
					  <text v-else-if="item.status == 3">已关闭</text>
					  <text v-else-if="item.status == 1">已支付</text>
					  <text v-else> 未支付 </text> 
					 
				</view>         
				         
				<view class="ll-txt">
					
					<text >{{item.order_detail[0].field_name}}</text>
					
					 <text>总价:{{item.total_price}}</text>
					
				</view>
			
				<view  class="ll-txt">
					
					<text>{{item.order_detail[0].region_name}}</text>
					
					 <text> {{item.order_detail[0].charges_list.length}}个场次</text>
					
				</view>
				
				<view  class="ll-txt">
					
					 <text>{{item.days}}</text>
					
					 <text>开始时间:{{item.order_detail[0].charges_list[0].time}}</text>
					
				</view>
				
				</view>
				
				<view class="line"></view>
				
			    <view class="ll-btn" >
					
					<!-- <view class="btn1" >撤销订单</view> -->
					
					<view class="btn2" v-if="item.status == 0"  @click="goPay(item.id)">去支付</view>
					
				</view>  
			 
			</view>
			
		</view>
		
	</view>
	
</template>

<script>
	var app = getApp();
	export default {
	  data() {
	    return {
			opt:{},
			list : [],
			selectedOption:'0'
				
	    };
	  },
	  
	   onLoad: function (opt) {
			this.opt = app.getopts(opt);
	   },
		onShow:function(){

			this.getList();
		},
		onPullDownRefresh: function () {
			
		},
	    methods: {
		
			getList: function() {
				
				var that = this;
				
				var obj ={
				
				}  
				
				app.get('/ApiVenues/getOrderList', obj, function(res) {
					
					console.log(res)
					
					if(res.status == 1){
						
						that.list = res.data.list
							
					}   	   
			 
				});
				
			 },
			 
			goDetail(id){
				 uni.navigateTo({
					url:'/pagesB/dingchang/dingchangOrderDetail?id='+id
				 })
			},
			
			goPay(id){
				 uni.navigateTo({
					url:'/pages/pay/pay?id='+id
				 })
			}


	   }
	}
</script>

<style lang="scss">
	
	.option{
		padding-top: 50rpx;
		padding-bottom: 20rpx;
		background-color: #ffffff;
		display: flex; 
		justify-content: space-evenly; 
		flex-wrap: wrap;
		font-size: 32rpx;
		
		
		.option_get{
			color: #0000ff;
			border-bottom: 5rpx solid #0000ff;   
		}
		.option_bt{
			padding-bottom: 10rpx;
			
		}
	}
	
	.ll{
		padding: 15px;
		border-radius: 10px; 
		background:#fff;
		margin: 15px;
	}
	
	.ll-tit{
		display: flex;
		justify-content: space-between;
	}
	
	.ll-txt{
		display: flex;margin-top: 10px;
	}
	
	.ll-txt text{
		flex: 1;
	}
	
	.line{
		border-bottom: 1px solid #eee;
		margin-top: 15px;
	}
	
	.ll-btn{
		display: flex;
		justify-content: flex-end;
	}
	
	.ll-btn .btn1{
		padding: 5px 10px;
		margin: 10px 0 0 10px;
		border-radius: 30px;
		border: 1px solid #eee;
	}
	
	.ll-btn .btn2{
		padding: 5px 10px;
		margin: 10px 0 0 10px;
		border-radius: 30px;
		background: #5F72F2;
		color: #fff;
	}
</style>