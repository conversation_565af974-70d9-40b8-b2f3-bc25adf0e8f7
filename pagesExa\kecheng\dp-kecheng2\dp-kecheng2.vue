<template>
	<view class="dp-product">
		<!--123排-->
		<dpItem v-if="type === 'item'" :data="data" :menuindex="menuindex"></dpItem>
		<!--横排-->
		<dpItemList v-else :data="data" :menuindex="menuindex"></dpItemList>
	</view>
</template>
<script>
	import dpItem from './item/dp-item.vue'
	import dpItemList from './itemList/dp-itemList'
	export default {
		components: {
			dpItem,
			dpItemList,
		},
		props: {
			type: {
				type: String,
				default: 'item'
			},
			data: {},
			menuindex: {
				type: Number,
				default: -1
			},
			sysset: {},
		}
	}
</script>
<style>
	.dp-product {
		width: 100%;
		height: auto;
		position: relative;
		overflow: hidden;
		padding: 0px;
		background: #fff;
	}
</style>