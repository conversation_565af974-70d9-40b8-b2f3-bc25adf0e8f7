<template>
<view class="dp-form" :style="{
	color:params.color,
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	fontSize:(params.fontsize*2)+'rpx'
}">
	<form @submit="editorFormSubmit" :data-formcontent="data.content" :data-tourl="params.hrefurl" :data-formid="data.id">
		<view style="display:none">{{test}}</view>
		
		<block v-if="xystatus==1 && xytitlePos=='top'">
			<view class="xycss1">
			  <checkbox-group @change="isagreeChange" style="display: inline-block;">
				  <checkbox style="transform: scale(0.6)"  value="1" :checked="isagree" :color="t('color1')"/>
			  </checkbox-group>
				<text>我已阅读并同意</text>
			  <text @tap="showxieyiFun" :style="{color:t('color1')}">{{xytitle}}</text>
			</view>
		</block>
		<view :class="params.style==1?'dp-form-item':'dp-form-item2'" v-for="(item,idx) in data.content" :style="{borderColor:params.linecolor,background:item.bgcolor?item.bgcolor:'transparent'}" :key="item.id" v-if="!item.linkitem || item.linkshow">
			<block v-if="item.key=='separate'">
				<view class="dp-form-separate" :class="item.val8=='1'?'dp-form-blod':''">{{item.val1}}</view>
			</block>
			<view v-if="item.key!='separate'" class="label" :class="item.val8=='1'?'dp-form-blod':''">{{item.val1}}<text v-if="item.val3==1&&params.showmuststar" style="color:red"> *</text></view>
			<block v-if="item.key=='input'">
				<block v-if="params.style==1">
					<text v-if="item.val5" style="margin-right:10rpx">{{item.val5}}</text>
					<!-- #ifdef MP-WEIXIN -->
					<block v-if="item.val4==2 && item.val6==1">
						<input @focus="inputFocus" @blur="inputBlur" :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" disabled="true" :name="'form'+idx" class="input disabled" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':'#efefef'}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
						<button class="authtel" :style="{backgroundColor:params.btnbgcolor,color:params.btncolor}"  open-type="getPhoneNumber" type="primary" @getphonenumber="getPhoneNumber" :data-idx="idx">获取手机号码</button>
					</block>
					<block v-else>
						<input :adjust-position="false"	@focus="inputFocus" @blur="inputBlur"	:type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'"	readonly	:name="'form'+idx" 	class="input" :class="'form'+idx"	:placeholder="item.val2" 	placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
					</block>
					<!-- #endif -->
					<!-- #ifndef MP-WEIXIN -->
					<block>
						<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" readonly :name="'form'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
					</block>
					<!-- #endif -->
				</block>
				
				<view v-if="params.style==2" class="value">
					<text v-if="item.val5" style="margin-right:10rpx">{{item.val5}}</text>
					<!-- #ifdef MP-WEIXIN -->
					<block v-if="item.val4==2 && item.val6==1">
						<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" disabled="true" :name="'form'+idx" class="input disabled" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':'#efefef'}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
						<button class="authtel" :style="{backgroundColor:params.btnbgcolor,color:params.btncolor}"  open-type="getPhoneNumber" type="primary" @getphonenumber="getPhoneNumber" :data-idx="idx">获取手机号码</button>
					</block>
					<block v-else>
						<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" readonly :name="'form'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
					</block>
					<!-- #endif -->
					<!-- #ifndef MP-WEIXIN -->
					<block>
						<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" readonly :name="'form'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
					</block>
					<!-- #endif -->
				</view>
			</block>
			<block v-if="item.key=='textarea'">
				<textarea :adjust-position="false" @focus="inputFocus"	 @blur="inputBlur" :name="'form'+idx" class='textarea' :class="'form'+idx" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
			</block>
			<block v-if="item.key=='radio'">
				<radio-group :name="'form'+idx" :class="item.val10=='1'?'rowalone':'flex'" style="flex-wrap:wrap" @change="setfield" :data-formidx="'form'+idx">
					<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center" :class="[item.val11=='1'?'checkborder':'',item.val10=='1'?'':'rowmore']" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor,padding:'0 10rpx',marginTop:'10rpx',borderRadius: '10rpx'}">
							<radio class="radio" :value="item1" :checked="formdata['form'+idx] && formdata['form'+idx]==item1 ? true : false"/>{{item1}}
					</label>
				</radio-group>
			</block>
			<block v-if="item.key=='checkbox'">
				<checkbox-group :name="'form'+idx" :class="item.val4=='1'?'rowalone':'flex'" style="flex-wrap:wrap" @change="setfield" :data-formidx="'form'+idx">
					<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center" :class="[item.val9=='1'?'checkborder':'',item.val4=='1'?'':'rowmore']" :style="{borderColor:params.inputbordercolor,'background-color':params.inputbgcolor,padding:'0 10rpx',marginTop:'10rpx',borderRadius: '10rpx'}">
						<checkbox class="checkbox" :value="item1" :checked="formdata['form'+idx] && inArray(item1,formdata['form'+idx]) ? true : false"/>{{item1}}
					</label>
				</checkbox-group>
			</block>
			<block v-if="item.key=='selector'">
				<picker class="picker" mode="selector" :name="'form'+idx" :value="editorFormdata[idx]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view class="flex-y-center flex-bt" v-if="editorFormdata[idx] || editorFormdata[idx]===0">
						<text>{{item.val2[editorFormdata[idx]]}}</text>
						<view class="arrow-area">
							<view class="input-arrow"></view>
						</view>
					</view>
					<view class="dp-form-normal flex-y-center flex-bt" v-else>
						<text>请选择</text>
						<view class="arrow-area">
							<view class="input-arrow"></view>
						</view>
					</view>
				</picker>
			</block>
			<block v-if="item.key=='time'">
				<picker class="picker" mode="time" :name="'form'+idx" :value="formdata['form'+idx]" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view class="flex-y-center flex-bt" v-if="editorFormdata[idx]">
						<text>{{editorFormdata[idx]}}</text>
						<view class="arrow-area">
							<view class="input-arrow"></view>
						</view>
					</view>
					<view class="dp-form-normal flex-y-center flex-bt" v-else>
						<text>请选择</text>
						<view class="arrow-area">
							<view class="input-arrow"></view>
						</view>
					</view>
				</picker>
			</block>
			<block v-if="item.key=='date'">
				<picker class="picker" mode="date" :name="'form'+idx" :value="formdata['form'+idx]" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view class="flex-y-center flex-bt" v-if="editorFormdata[idx]">
						<text>{{editorFormdata[idx]}}</text>
						<view class="arrow-area">
							<view class="input-arrow"></view>
						</view>
					</view>
					<view class="dp-form-normal flex-y-center flex-bt" v-else>
						<text>请选择</text>
						<view class="arrow-area">
							<view class="input-arrow"></view>
						</view>
					</view>
				</picker>
			</block>
			<block v-if="item.key=='year'">
				<picker class="picker" :name="'form'+idx" :value="formdata['form'+idx]" @change="yearChange" :data-idx="idx" :range="yearList" :data-formidx="'form'+idx">
					<view class="flex-y-center flex-bt" v-if="editorFormdata[idx]">
						<text>{{editorFormdata[idx]}}</text>
						<view class="arrow-area">
							<view class="input-arrow"></view>
						</view>
					</view>
					<view class="dp-form-normal flex-y-center flex-bt" v-else>
						<text>请选择</text>
						<view class="arrow-area">
							<view class="input-arrow"></view>
						</view>
					</view>
				</picker>
			</block>

			<block v-if="item.key=='region'">
				<uni-data-picker style="flex: 1;width: 100%;" :localdata="items" popup-title="请选择省市区" :placeholder="formdata['form'+idx] || '请选择省市区'" @change="onchange" :data-formidx="'form'+idx"></uni-data-picker>
				<!-- <picker class="picker" mode="region" :name="'form'+idx" value="" @change="editorBindPickerChange" :data-idx="idx">
					<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view> 
					<view v-else>请选择省市区</view>
				</picker> -->
				<input type="text" style="display:none" :name="'form'+idx" :value="regiondata ? regiondata : formdata['form'+idx]"/>
			</block>
			<block v-if="item.key=='upload'">
				<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
				<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
					<view class="dp-form-imgbox" v-if="editorFormdata[idx]">
						<view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx"><image src="/static/img/ico-del.png" class="image"></image></view>
						<view class="dp-form-imgbox-img"><image class="image" :src="editorFormdata[idx]" @click="previewImage" :data-url="editorFormdata[idx]" mode="aspectFit" :data-idx="idx"/></view>
					</view>
					<view v-else class="dp-form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-idx="idx" :data-formidx="'form'+idx"></view>
				</view>
			</block>
      <!-- #ifdef H5 || MP-WEIXIN -->
      <block v-if="item.key=='upload_file'">
        <input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
        <view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx">
          <view class="dp-form-imgbox" v-if="editorFormdata[idx]">
            <view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx">
							<image src="/static/img/ico-del.png" class="image"></image>
						</view>
            <view  style="overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;width: 530rpx;" @tap="download" :data-file="editorFormdata[idx]" >
							文件已上传成功
						</view>
          </view>
          <view v-else class="dp-form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="chooseFile" :data-idx="idx" :data-formidx="'form'+idx" style="margin-right:20rpx;"></view>
					<view v-if="item.val2" style="color:#999">{{item.val2}}</view>
        </view>
      </block>
      <!-- #endif -->
      <block v-if="item.key=='upload_video'">
        <input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
        <view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx">
          <view class="dp-form-imgbox" v-if="editorFormdata[idx]">
            <view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx">
                <image src="/static/img/ico-del.png" class="image"></image>
            </view>
            <view  style="overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;width: 430rpx;">
                <video  :src="editorFormdata[idx]" style="width: 100%;"/></video>
            </view>
          </view>
          <view v-else class="dp-form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="upVideo" :data-idx="idx" :data-formidx="'form'+idx" style="margin-right:20rpx;"></view>
					<view v-if="item.val2" style="color:#999">{{item.val2}}</view>
        </view>
      </block>
      <block v-if="item.key=='map'">
        <input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
        <view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx">

            <text class="flex1" style="text-align:right" :style="area ? '' : 'color:#BBBBBB'" @click="selectzuobiao" :data-idx="idx" :data-formidx="'form'+idx" >{{area ? area : '请选择您的位置'}}</text>

        </view>
      </block>
      <block v-if="item.key=='upload_pics'">
      	<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata && editorFormdata[idx]?editorFormdata[idx].join(','):''" maxlength="-1"/>
      	<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
      		<view v-for="(item2, index2) in editorFormdata[idx]" :key="index2" class="dp-form-imgbox" >
      			<view class="dp-form-imgbox-close" @tap="removeimg" :data-index="index2" data-type="pics" :data-idx="idx" :data-formidx="'form'+idx"><image src="/static/img/ico-del.png" class="image"></image></view>
      			<view class="dp-form-imgbox-img" style="margin-bottom: 10rpx;"><image class="image" :src="item2" @click="previewImage" :data-url="item2" mode="aspectFit" :data-idx="idx"/></view>
      		</view>
      		<view class="dp-form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3',marginBottom: '10rpx'}" @click="editorChooseImage" :data-idx="idx" :data-formidx="'form'+idx" data-type="pics"></view>
      	</view>
      </block>
		</view>
		<block v-if="data.payset==1">
			<view class="dp-form-item" v-if="!data.is_other_fee || data.is_other_fee==0">
				<text class="label" style="font-weight: bold;">{{ data.price_text != '' ? data.price_text : '支付金额：'}}</text>
				<input type="text" class="input" name="price" :value='data.price' v-if="data.priceedit==1" @input="setfield" data-formidx="price"/>
				<text v-if="data.priceedit==0">{{data.price}}</text>
				<text style="padding-left:10rpx">元</text>
				<!-- <text class="label" style="margin-left: 12rpx;color: #828282;" v-if="data.pay_remark != ''">{{ data.pay_remark }}</text> -->
			</view>
			
			<view class="dp-form-item" v-if="!data.is_other_fee || data.is_other_fee == 0">
			    <text class="label" style="margin-left: 12rpx; color: #828282; white-space: pre-wrap;width: 100%;float: left;text-align: left;" v-if="data.pay_remark != ''">{{ data.pay_remark }}</text>
			</view>

			
			
			
			<block  v-if="data.is_other_fee==1">
			<view :class="params.style==1?'dp-form-item-fee':'dp-form-item-fee2'" style="border: none;">
				<view class="dp-form-label">费用明细</view>
				<view class="dp-form-feelist">
				<checkbox-group name="feelist">
					<view class="dp-fee-item" v-for="(item,index) in data.fee_items" :key="index">
						<view class="dp-fee-name">{{item.name}}</view>
						<view class="dp-fee-money">￥{{item.money}}</view>
						<view class="dp-fee-check"><checkbox @click="feeChange" :data-index="index" :value="''+index" :checked="item.checked?true:false" :color="t('color1')" style="transform: scale(0.7);"></checkbox></view>
					</view>
				</checkbox-group>
				<view class="dp-fee-item sum">
					<view class="dp-fee-name">合计</view>
					<view class="dp-fee-money">￥<text>{{feetotal}}</text></view>
					<view class="dp-fee-check"></view>
				</view>
				</view>
			</view>
			</block>
		</block>
		<block v-if="xystatus==1 && xytitlePos=='bottom'">
			<view class="xycss1 dp-form-item">
			  <checkbox-group @change="isagreeChange" style="display: inline-block;">
				  <checkbox style="transform: scale(0.6)"  value="1" :checked="isagree" :color="t('color1')"/>
			  </checkbox-group>
				<text>我已阅读并同意</text>
			  <text @tap="showxieyiFun" :style="{color:t('color1')}">{{xytitle}}</text>
			</view>
		</block>
				
		<view v-if="showxieyi" class="xieyibox">
			<view class="xieyibox-content">
				<view style="overflow:scroll;height:100%;">
					<parse :content="xycontent" @navigate="navigate"></parse>
				</view>
				<view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"  @tap="hidexieyi">{{agree_button}}</view>
			</view>
		</view>
		
		<button @tap="editorFormSubmit" v-if="data != ''" class="dp-form-btn flex-xy-center" :style="{backgroundColor:params.btnbgcolor,border:'1px solid '+params.btnbordercolor,fontSize:(params.btnfontsize*2)+'rpx',color:params.btncolor,width:(params.btnwidth*2.2)+'rpx',height:(params.btnheight*2.2)+'rpx',lineHeight:(params.btnheight*2.2)+'rpx',borderRadius:(params.btnradius*2.2)+'rpx'}" :data-formcontent="data.content" :data-tourl="params.hrefurl" :data-formid="data.id">{{params.btntext}}</button>
		<view :style="{height:`${keyboardHeight}`+'px'}"></view>
	</form>
</view>
</template>
<script>
	var app = getApp();
	export default {
		data(){
			return {
				pre_url:getApp().globalData.pre_url,
				editorFormdata:[],
				test:'test',
				regiondata:'',
				items: [],
				tmplids: [],
				submitDisabled:false,
				formdata:{},
				formvaldata:{},
				authphone:'',
				platform:'',
				feetotal:0,
				yearList:[],
        area: '',
        adr_lon:'',
        adr_lat:'',
				timer:'',
				keyboardHeight:'0',
				xystatus:0,
				showxieyi:false,
				xytitle:'',
				xycontent:'',
				xytitlePos:'bottom',
				isagree:false,
				agree_button:''
			}
		},
		props: {
			params:{},
			data:{},
			latitude:'',
			longitude:'',
		},
	
		mounted:function(){
			var that = this;
			let year = [];
			for(let i=0;i<that.data.content.length;i++){
				if(that.data.content[i].key=='year'){
					for(let j=that.data.content[i].val2[0];j<=that.data.content[i].val2[1];j++){
						year.push(j);
					}
				}
			}
			this.yearList = year.reverse();
			that.platform = app.getplatform();
			app.get('ApiIndex/getCustom',{}, function (customs) {
				var url = app.globalData.pre_url+'/static/area.json';
				if(customs.data.includes('plug_zhiming')) {
					url = app.globalData.pre_url+'/static/area_gaoxin.json';
				}
				uni.request({
					url: app.globalData.pre_url+'/static/area.json',
					data: {},
					method: 'GET',
					header: { 'content-type': 'application/json' },
					success: function(res2) {
						that.items = res2.data
					}
				});
			});
			that.checkPayMoney()

			var pages = getCurrentPages(); //获取加载的页面
			var currentPage = pages[pages.length - 1]; //获取当前页面的对象
			var thispath = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url 
			var opts = currentPage.$vm.opt;
			var fromrecord = 0;
			if(opts && opts.fromrecord){
				fromrecord = opts.fromrecord;
			}
			that.xystatus = that.data.show_agree;
			that.xycontent = that.data.agree_desc;
			that.xytitle = that.data.agree_title;
			that.agree_button = that.data.agree_button;
			that.xytitlePos = that.data.agree_title_pos;
			app.get('ApiForm/getlastformdata',{formid:that.data.id,fromrecord:fromrecord}, function (res) {
				if(res && res.status == 1 && res.data){
					var formcontent = that.data.content;
					var editorFormdata = [];
					var formvaldata = {};
					formvaldata.price = that.data.price
					for(var i in formcontent){
						var thisval = res.data['form'+i];
						if (formcontent[i].key == 'region') {
							that.regiondata = thisval;
						}
						if (formcontent[i].key == 'selector') {
							for(var j in formcontent[i].val2){
								if(formcontent[i].val2[j] == res.data['form'+i]){
									thisval = j;
								}
							}
						}
						if (formcontent[i].key == 'checkbox') {
							if(res.data['form'+i]){
								res.data['form'+i] = (res.data['form'+i]).split(',');
							}else{
								res.data['form'+i] = [];
							}
						}
						editorFormdata.push(thisval);
						formvaldata['form'+i] = thisval;

						if(formcontent[i].key == 'radio' || formcontent[i].key=='selector'){
							var linkitem = formcontent[i].val1 + '|' + formcontent[i].val2[thisval];
							console.log(linkitem)
							for(var j in that.data.content){
								var thislinkitem = that.data.content[j].linkitem;
								if(thislinkitem == linkitem){
									that.data.content[j].linkshow = true;
									that.test = Math.random();
								}else if(thislinkitem && thislinkitem.split('|')[0] == formcontent[i].val1){
									that.data.content[j].linkshow = false;
									that.test = Math.random();
								}
							}
						}

					}
					that.editorFormdata = editorFormdata;
					that.formvaldata = formvaldata;
					that.formdata = res.data;
				}else{
					var formvaldata = {};
					formvaldata.price = that.data.price;
					that.formvaldata = formvaldata;
				}
			})
		},
		methods:{
			// 失去焦点
			inputBlur(){
				this.$set(this,'keyboardHeight',0)
			},
			// 获取焦点
			inputFocus(event){
				if (this.timer) {
					clearTimeout(this.timer)
				}
				this.timer = setTimeout(() => {
					this.timer = null
					const height = event.detail.height; //键盘高度
					const formidx = event.target.dataset.formidx
					if(height === 0){
						this.scrollToInput(0);
						return;
					}
				try{
				const query = uni.createSelectorQuery().in(this);
					query.select(`.${formidx}`).boundingClientRect((res) => {
						const windowHeight = uni.getSystemInfoSync().windowHeight;
						// 除去键盘的剩余高度
						let restHeight = windowHeight - height;
						// 元素左下角坐标
						let bottom = res.bottom;
						// 只有当元素被软键盘覆盖的时候才上推页面
						if (bottom <= restHeight) return;
						// 现阶段需要滚动的大小
						let scrollTop = bottom - restHeight;
						this.scrollToInput(height, scrollTop);
					}).exec();
				} catch(err){console.log(err)}
				},300)
			},
			// 获取页面滚动条位置
			getScrollOffset() {
			  return new Promise((resolve) => {
			    try {
			     const query = uni.createSelectorQuery().in(this);
			        query.selectViewport().scrollOffset((res) => {
			          resolve(res.scrollTop);
			        }).exec();
			    } catch (error) {
			      resolve(0);
			    }
			  });
			},
			// 监听页面键盘弹起推动页面
			scrollToInput(height,scrollTop){
				this.$set(this,'keyboardHeight',height)
				 if (scrollTop) {
				    try {
				      this.getScrollOffset().then((lastScrollTop) => {
				        uni.pageScrollTo({
				          // 如果已经存在滚动，在此基础上继续滚
				          scrollTop: lastScrollTop ? lastScrollTop + scrollTop : scrollTop,
				          duration: 0,
				        });
				      });
				    } catch (error) {}
				  }
			},
			onchange(e) {
        const value = e.detail.value
				console.log(value[0].text + ',' + value[1].text + ',' + value[2].text)
				this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;
      },
			setfield:function(e){
				var field = e.currentTarget.dataset.formidx;
				var value = e.detail.value;
				this.formvaldata[field] = value;
				var idx = field.replace('form','');
				console.log(idx);
				var thiscontent = this.data.content[idx]
				//console.log(thiscontent);
				if(thiscontent.key == 'radio' || thiscontent.key=='selector'){
					var linkitem = thiscontent.val1 + '|' + value;
					console.log(linkitem)
					for(var i in this.data.content){
						var thislinkitem = this.data.content[i].linkitem;
						if(thislinkitem == linkitem){
							this.data.content[i].linkshow = true;
							this.test = Math.random();
						}else if(thislinkitem && thislinkitem.split('|')[0] == thiscontent.val1){
							this.data.content[i].linkshow = false;
							this.test = Math.random();
						}
					}
				}
			},
			editorFormSubmit:function(e){
				var that = this;
				if (that.xystatus == 1 && !that.isagree) {
				  app.error('请先阅读并同意'+that.xytitle);
				  return false;
				}
				if(this.submitDisabled && app.globalData.mid > 0) return ;
				//console.log('form发生了submit事件，携带数据为：', e.detail.value)
				var subdata = e.detail.value;
				var subdata = JSON.parse(JSON.stringify(this.formvaldata));
				console.log(subdata)
				var formcontent = e.currentTarget.dataset.formcontent;
				var formid = e.currentTarget.dataset.formid;
				var tourl = e.currentTarget.dataset.tourl;
				var formdata = new Array();
				for (var i = 0; i < formcontent.length;i++){
					//console.log(subdata['form' + i]);
					if (formcontent[i].key == 'region') {
							subdata['form' + i] = that.regiondata;
					}
					if (formcontent[i].key!='separate' && formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === null || subdata['form' + i] === undefined || subdata['form' + i].length==0)){
						if(formcontent[i].linkitem == '' || formcontent[i].linkshow){
							app.alert(formcontent[i].val1+' 必填');return;
						}
					}
					if (formcontent[i].key =='switch'){
							if (subdata['form' + i]==false){
									subdata['form' + i] = '否'
							}else{
									subdata['form' + i] = '是'
							}
					}
					if (formcontent[i].key == 'selector') {
							subdata['form' + i] = formcontent[i].val2[subdata['form' + i]]
					}
					if (formcontent[i].key == 'input' && formcontent[i].val4 && subdata['form' + i]!==''){
						if(formcontent[i].val4 == '2'){ //手机号
							if (!/^1[3456789]\d{9}$/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
						if(formcontent[i].val4 == '3'){ //身份证号
							if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
						if(formcontent[i].val4 == '4'){ //邮箱
							if (!/^(.+)@(.+)$/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
					}
					formdata.push(subdata['form' + i])
				}
				
				//范围
				if(that.data.fanwei == 1 && (that.latitude == '' || that.longitude == '')) {
					app.alert('请定位您的位置或者刷新重试');return;
				}
				var feedata = [];
				if(that.data.payset==1 && that.data.is_other_fee==1){
					var feeitems = that.data.fee_items
					var feenum = 0;
					var feetotal  = 0;
					for(let i in feeitems){
						if(feeitems[i].checked){
							feenum++;
							feetotal = feetotal + parseFloat(feeitems[i].money);
							feedata.push(feeitems[i])
						}
					}
					if(feenum<1){
						app.error('请选择费用明细');
						return;
					}else{
						feetotal = feetotal.toFixed(2);
						formdata.price = feetotal;
						subdata.price = feetotal;
					}
					
				}
				//console.log(formdata);
				if(app.globalData.mid > 0){
					that.submitDisabled = true;
				}
				app.showLoading('提交中');

				var pages = getCurrentPages(); //获取加载的页面
				var currentPage = pages[pages.length - 1]; //获取当前页面的对象
				var thispath = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url 
				var opts = currentPage.$vm.opt;

				var posturl = 'ApiForm/formsubmit';
				if(that.params.isquery == '1'){
					posturl = 'ApiForm/formquery';
				}
                
                var edit_id = 0;
                if(opts && opts.fromrecord && opts.type){
                	if(opts.type == 'edit'){
                        edit_id = opts.fromrecord;
                    }
                }
        subdata.adr_lon = that.adr_lon;
        subdata.adr_lat = that.adr_lat;
				app.post(posturl, {formid:formid,formdata:subdata,price:subdata.price,fromurl:thispath+'?id='+opts.id,latitude:that.latitude,longitude:that.longitude,edit_id:edit_id,feedata:feedata},function(data){
					that.tmplids = data.tmplids;
					app.showLoading(false);
					if (data.status == 0) {
						//that.showsuccess(res.data.msg);
						setTimeout(function () {
							app.error(data.msg);
						}, 100)
						that.submitDisabled = false;
						return;
					}else if(data.status == 1) { //无需付款
						if(that.params.isquery == '1'){
							that.submitDisabled = false;
							app.goto(data.tourl);return;
						}
						that.subscribeMessage(function () {
							setTimeout(function () {
								app.success(data.msg);
							}, 100)
							setTimeout(function () {
								app.goto(tourl);
							}, 1000)
						});
						return;
					}else if(data.status==2){
						that.subscribeMessage(function () {
							setTimeout(function () {
								app.goto('/pages/pay/pay?id='+data.payorderid+'&tourl='+tourl);
							}, 100);
						});
					}
					that.submitDisabled = false;
				});
			},
			editorChooseImage: function (e) {
				var that = this;
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
        
        var type = e.currentTarget.dataset.type;
				app.chooseImage(function(data){
          if(type == 'pics'){
            var pics = editorFormdata[idx];
            if(!pics){
              pics = [];
            }
            for(var i=0;i<data.length;i++){
            	pics.push(data[i]);
            }
            console.log(pics)
            editorFormdata[idx] = pics;
            that.editorFormdata = editorFormdata
            console.log(editorFormdata[idx])
            console.log(editorFormdata)
            that.test = Math.random();
            var field = e.currentTarget.dataset.formidx;
            that.formvaldata[field] = pics;
          }else{
            editorFormdata[idx] = data[0];
            console.log(editorFormdata)
            that.editorFormdata = editorFormdata
            that.test = Math.random();
            
            var field = e.currentTarget.dataset.formidx;
            that.formvaldata[field] = data[0];
          }
					

				})
			},
			removeimg:function(e){
				var that = this;
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var field = e.currentTarget.dataset.formidx;
				var editorFormdata = this.editorFormdata;
        if(!editorFormdata) editorFormdata = [];
        
        var type  = e.currentTarget.dataset.type;
        var index = e.currentTarget.dataset.index;
        if(type == 'pics'){
          var pics = editorFormdata[idx]
          pics.splice(index,1);
          editorFormdata[idx] = pics;
          that.editorFormdata = editorFormdata
          that.test = Math.random();
          that.formvaldata[field] = pics;
        }else{
          editorFormdata[idx] = '';
          that.editorFormdata = editorFormdata
          that.test = Math.random();
          that.formvaldata[field] = '';
        }
			},
			yearChange:function(e){
				var idx = e.currentTarget.dataset.idx;
				var val = this.yearList[e.detail.value];
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = val;
				this.editorFormdata = editorFormdata;
				this.test = Math.random();
				var field = e.currentTarget.dataset.formidx;
				this.formvaldata[field] = val;
			},
			editorBindPickerChange:function(e){
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var val = e.detail.value;
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = val;
				this.editorFormdata = editorFormdata
				this.test = Math.random();
				var field = e.currentTarget.dataset.formidx;
				this.formvaldata[field] = val;

				var idx = field.replace('form','');
				var thiscontent = this.data.content[idx]
				//console.log(thiscontent);
				if(thiscontent.key == 'radio' || thiscontent.key=='selector'){
					var linkitem = thiscontent.val1 + '|' + thiscontent.val2[val];
					console.log(linkitem)
					for(var i in this.data.content){
						var thislinkitem = this.data.content[i].linkitem;
						if(thislinkitem == linkitem){
							this.data.content[i].linkshow = true;
							this.test = Math.random();
						}else if(thislinkitem && thislinkitem.split('|')[0] == thiscontent.val1){
							this.data.content[i].linkshow = false;
							this.test = Math.random();
						}
					}
				}

			},
			getPhoneNumber: function (e) {
				var that = this
				var idx = e.currentTarget.dataset.idx;
				var field = 'form'+idx;
				if(that.authphone){
					that.test = Math.random()
					that.formdata['form'+idx] = that.authphone;
					that.formvaldata[field] = that.authphone;
					return true;
				}
				if(e.detail.errMsg == "getPhoneNumber:fail user deny"){
					app.error('请同意授权获取手机号');return;
				}
				if(!e.detail.iv || !e.detail.encryptedData){
					app.error('请同意授权获取手机号');return;
				}
				wx.login({success (res1){
					console.log('res1')
					console.log(res1);
					var code = res1.code;
					//用户允许授权
					app.post('ApiIndex/authphone',{ iv: e.detail.iv,encryptedData:e.detail.encryptedData,code:code,pid:app.globalData.pid},function(res2){
						if (res2.status == 1) {
							that.authphone = res2.tel;
							that.test = Math.random()
							that.formdata['form'+idx] = that.authphone;
							that.formvaldata[field] = that.authphone;
						} else {
							app.error(res2.msg);
						}
						return;
					})
				}});
			},
            download:function(e){
                var that = this;
                var file = e.currentTarget.dataset.file;
                // #ifdef H5
                    window.location.href= file;
                // #endif
                
                // #ifdef MP-WEIXIN
                uni.downloadFile({
                	url: file, 
                	success: (res) => {
                        var filePath = res.tempFilePath;
                		if (res.statusCode === 200) {
                			uni.openDocument({
                              filePath: filePath,
                              showMenu: true,
                              success: function (res) {
                                console.log('打开文档成功');
                              }
                            });
                		}
                	}
                });
                // #endif
            },
            chooseFile:function(e){
                var that = this;
                var idx = e.currentTarget.dataset.idx;
                var field = e.currentTarget.dataset.formidx;
                
                var editorFormdata = this.editorFormdata;
                if(!editorFormdata) editorFormdata = [];
                
                var up_url = app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id;
                
                // #ifdef H5
                uni.chooseFile({
                    count: 1, //默认100
                    success: function (res) {
                        const tempFilePaths = res.tempFiles;

												if(tempFilePaths[0].size > 0){
													var maxsize = that.data.content[idx].val11;
													if(maxsize){
														maxsize = parseFloat(maxsize);
														if(maxsize > 0 && maxsize * 1024 * 1024 < tempFilePaths[0].size){
															app.alert('文件过大');return;
														}
													}
												}
                        //for (var i = 0; i < tempFilePaths.length; i++) {
                        	app.showLoading('上传中');
                        	uni.uploadFile({
                        		url: up_url,
                        		filePath: tempFilePaths[0]['path'],
                        		name: 'file',
                        		success: function(res) {
                        			app.showLoading(false);
                        			var data = JSON.parse(res.data);
                        			if (data.status == 1) {
                                        that.formvaldata[field] = data.url;
                                        
                                        editorFormdata[idx] = data.url;
                                        that.editorFormdata = editorFormdata;
                                        that.$set(that.editorFormdata, idx,data.url)
                        			} else {
                        				app.alert(data.msg);
                        			}
                        		},
                        		fail: function(res) {
                        			app.showLoading(false);
                        			app.alert(res.errMsg);
                        		}
                        	});
                        //}
                    }
                });
                // #endif
                // #ifdef MP-WEIXIN
                    wx.chooseMessageFile({
                      count: 1,
                      //type: 'file',
                      success (res) {
                        // tempFilePath可以作为 img 标签的 src 属性显示图片
                        const tempFilePaths = res.tempFiles
                        console.log(tempFilePaths);
                        
												if(tempFilePaths[0].size > 0){
													var maxsize = that.data.content[idx].val11;
													if(maxsize){
														maxsize = parseFloat(maxsize);
														if(maxsize > 0 && maxsize * 1024 * 1024 < tempFilePaths[0].size){
															app.alert('文件过大');return;
														}
													}
												}
                       
                        //for (var i = 0; i < tempFilePaths.length; i++) {
                        	app.showLoading('上传中');
                            console.log(tempFilePaths[0]);
                        	uni.uploadFile({
                        		url: up_url,
                        		filePath: tempFilePaths[0]['path'],
                        		name: 'file',
                        		success: function(res) {
                        			app.showLoading(false);
                        			var data = JSON.parse(res.data);
                        			if (data.status == 1) {
                                        that.formvaldata[field] = data.url;
                                        
                                        editorFormdata[idx] = data.url;
                                        that.editorFormdata = editorFormdata;
                                        that.$set(that.editorFormdata, idx,data.url)
                        			} else {
                        				app.alert(data.msg);
                        			}
                        		},
                        		fail: function(res) {
                        			app.showLoading(false);
                        			app.alert(res.errMsg);
                        		}
                        	});
                        //}
                      },
                      complete(res){
                          console.log(res)
                      }
                    })
                // #endif
            },
						checkPayMoney:function(){
							var that = this
							var data = that.data
							var feetotal = 0;
							if(data && data.is_other_fee){
								var feeitmes = data.fee_items;
								for(let i in feeitmes){
									feetotal += parseFloat(feeitmes[i].money)
									feeitmes[i]['checked'] = false
								}
								that.data.fee_items = feeitmes
								that.feetotal = feetotal.toFixed(2)
							}
						},
						feeChange:function(e){
							var that = this;
							var index = e.currentTarget.dataset.index
							var feeitems = that.data.fee_items
							if(feeitems[index].checked){
								feeitems[index].checked = false
							}else{
								feeitems[index].checked = true
							}
							var feetotal = 0;
							for(let i in feeitems){
								if(feeitems[i].checked){
									feetotal = feetotal + parseFloat(feeitems[i].money)
								}
							}
							that.feetotal = feetotal.toFixed(2);
							that.data.fee_items = feeitems
						},
            upVideo:function(e){
                var that = this;
                var that = this;
                var idx = e.currentTarget.dataset.idx;
                var field = e.currentTarget.dataset.formidx;
                
                var editorFormdata = this.editorFormdata;
                if(!editorFormdata) editorFormdata = [];
                var up_url = app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id;
                uni.chooseVideo({
                  sourceType: ['camera', 'album'],
                  success: function (res) {
                    var path = res.tempFilePath;
										if(res.size > 0){
											var maxsize = that.data.content[idx].val11;
											if(maxsize){
												maxsize = parseFloat(maxsize);
												if(maxsize > 0 && maxsize * 1024 * 1024 < res.size){
													app.alert('视频文件过大');return;
												}
											}
										}

                    app.showLoading('上传中');
                    console.log(path );
                    uni.uploadFile({
                      url: up_url,
                      filePath: path,
                      name: 'file',
                      success: function(res) {
                        app.showLoading(false);
                        var data = JSON.parse(res.data);
                        if (data.status == 1) {
                          that.formvaldata[field] = data.url;

                          editorFormdata[idx] = data.url;
                          that.editorFormdata = editorFormdata;
                          that.$set(that.editorFormdata, idx,data.url)
                        } else {
                          app.alert(data.msg);
                        }
                      },
                      fail: function(res) {
                        app.showLoading(false);
                        app.alert(res.errMsg);
                      }
                    });
                  }
                });
            },
      selectzuobiao: function (e) {
        console.log('selectzuobiao')
        console.log(e);
        var that = this;
        var idx = e.currentTarget.dataset.idx;
        var field = 'form'+idx;
        uni.chooseLocation({
          success: function (res) {
            console.log(res);
            that.area = res.address;
            // that.address = res.name;
            that.adr_lat = res.latitude;
            that.adr_lon = res.longitude;
            that.formdata['form'+idx] = res.address;
            that.formvaldata[field] = res.address;
          },
          fail: function (res) {
            console.log(res)
            if (res.errMsg == 'chooseLocation:fail auth deny') {
              //$.error('获取位置失败，请在设置中开启位置信息');
              app.confirm('获取位置失败，请在设置中开启位置信息', function () {
                uni.openSetting({});
              });
            }
          }
        });
      },
	  isagreeChange: function (e) {
	    var val = e.detail.value;
	    if (val.length > 0) {
	      this.isagree = true;
	    } else {
	      this.isagree = false;
	    }
	    console.log(this.isagree);
	  },
	  showxieyiFun: function () {
	    this.showxieyi = true;
	  },
	  hidexieyi: function () {
	    this.showxieyi = false;
	  	this.isagree = true;
	  },
		}
	}
</script>
<style>
.dp-form{height: auto; position: relative;overflow: hidden; padding: 10rpx 0px; background: #fff;}
.dp-form .radio{transform:scale(.7);}
.dp-form .checkbox{transform:scale(.7);}
.dp-form-item{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 10rpx;display:flex;align-items: center;}
.dp-form-item:last-child{border:0}
.dp-form-item .label{line-height: 70rpx;width:140rpx;margin-right: 10px;flex-shrink:0;text-align: right;white-space: nowrap;}
.dp-form-item .input{height: 70rpx;line-height: 70rpx;overflow: hidden;flex:1;border: 1px solid #e5e5e5;border-radius: 5px;padding: 0 15rpx;background:#fff;flex: 1;}
.dp-form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:5px;padding:15rpx}
.dp-form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.dp-form-item .radio2{display:flex;align-items:center;}
.dp-form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.dp-form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item .layui-form-switch{}
.dp-form-item .picker{min-height: 70rpx;line-height:70rpx;flex:1;border: 1px solid #e5e5e5;border-radius: 5px;padding: 0 5px;}

.dp-form-item2{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 10rpx;display:flex;flex-direction:column;align-items: flex-start;}
.dp-form-item2:last-child{border:0}
.dp-form-item2 .label{line-height: 85rpx;width:100%;margin-right: 10px;}
.dp-form-item2 .value{display: flex;justify-content: flex-start;width: 100%;flex: 1;}
.dp-form-item2 .input{height: 70rpx;line-height: 70rpx;overflow: hidden;width:100%;border: 1px solid #e5e5e5;border-radius: 5px;padding: 0 15rpx;background:#fff;flex: 1;}
.dp-form-item2 .textarea{height:180rpx;line-height:40rpx;overflow: hidden;width:100%;border:1px solid #eee;border-radius:5px;padding:15rpx}
.dp-form-item2 .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}
.dp-form-item2 .radio2{display:flex;align-items:center;}
.dp-form-item2 .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item2 .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}
.dp-form-item2 .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item2 .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item2 .layui-form-switch{}
.dp-form-item2 .picker{min-height: 70rpx;line-height:70rpx;flex:1;width:100%;border: 1px solid #e5e5e5;border-radius: 5px;padding: 0 5px;}
.dp-form-btn{margin: 0 auto;background: #ff4f4f;color: #fff;margin-top: 15px;text-align:center}
.dp-form-blod{font-weight: bold;}
.dp-form-imgbox{margin-right:16rpx;font-size:24rpx;position: relative;}
.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff;z-index:9;border-radius:50%}
.dp-form-imgbox-close .image{width:100%;height:100%}
.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden;}
.dp-form-imgbox-img>.image{width:100%;height:100%}
.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}
.dp-form-separate{width: 100%;padding: 20rpx;text-align: center;padding: 20rpx;font-size: 36rpx;font-weight: 500;color: #454545;}
.authtel{border-radius: 10rpx; line-height: 68rpx;margin-left: 10rpx;padding: 0 10rpx;}
.input.disabled{background: #EFEFEF;}
.dp-form-item-fee .dp-form-label{line-height: 70rpx;width:140rpx;text-align: right;}
.dp-form-feelist{flex: 1;padding-left: 32rpx;}
.dp-fee-item{display: flex;justify-content: flex-start;align-items: center;color: #a3a3a3;font-size: 24rpx;}
.dp-fee-item.sum{color: #222222;font-weight: bold;font-size: 28rpx;padding-top: 10rpx;}
.dp-form-label{flex-shrink: 0;}
.dp-fee-name{flex: 1;width: 60%;}
.dp-fee-money{width: 30%;flex-shrink: 0;}
.dp-fee-check{width: 10%;flex-shrink: 0;}

.dp-form-item-fee2 .dp-form-label{padding-top: 20rpx;}
.dp-form-item-fee2 .dp-form-feelist{flex: 1;padding: 4rpx 0;}

.dp-form-normal{color: grey;}
.arrow-area {
	position: relative;
	width: 20px;
	/* #ifndef APP-NVUE */
	display: flex;
	margin-left: auto;
	/* #endif */
	justify-content: center;
	transform: rotate(-45deg);
	transform-origin: center;
}
.input-arrow {width: 7px;height: 7px;border-left: 1px solid #999;border-bottom: 1px solid #999;}
.checkborder{border: 1px solid #dcdfe6;border-radius: 5px;margin-top: 15rpx;min-width: 300rpx;padding: 0 10rpx;}
.rowalone{width: 100%;}
.rowmore{margin-right: 20rpx;}

.xycss1{line-height: 60rpx;font-size: 24rpx;overflow: hidden;margin-top: 20rpx;}
.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}
.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}

</style>