<template>
<view class="container">
	<block v-if="isload">
		<view class="view-show">
		      <!-- 修改搜索框 -->
		        <view class="search-container">
		          <view class="search-box">
		            <image class="img" src="/static/img/search_ico.png"></image>
		            <input 
		              class="search-text" 
		              type="text" 
		              v-model="searchKeyword" 
		              placeholder="搜索感兴趣的商品" 
		              @confirm="searchProducts"
		              @input="onSearchInput"
		            />
		          </view>
		        </view>
			<view class="content-container">
				<view class="nav_left">
					<view :class="'nav_left_items ' + (curIndex == -1 ? 'active' : '')" :style="{color:curIndex == -1?t('color1'):'#333'}" @tap="switchRightTab" data-index="-1" data-id="0"><view class="before" :style="{background:t('color1')}"></view>全部</view>
					<block v-for="(item, index) in clist" :key="index">
						<view :class="'nav_left_items ' + (curIndex == index ? 'active' : '')" :style="{color:curIndex == index?t('color1'):'#333'}" @tap="switchRightTab" :data-index="index" :data-id="item.id"><view class="before" :style="{background:t('color1')}"></view>{{item.name}}</view>
					</block>
				</view>
				<view class="nav_right">
					<view class="nav_right-content">
						<view class="nav-pai">
							<view class="nav-paili" :style="(!field||field=='sort')?'color:'+t('color1'):''" @tap="changeOrder" data-field="sort" data-order="desc">综合</view> 
							<view class="nav-paili" :style="field=='sales'?'color:'+t('color1'):''" @tap="changeOrder" data-field="sales" data-order="desc">销量</view> 
							<view class="nav-paili" @tap="changeOrder" data-field="sell_price" :data-order="order=='asc'?'desc':'asc'">
								<text :style="field=='sell_price'?'color:'+t('color1'):''">价格</text>
								<text class="iconfont iconshangla" :style="field=='sell_price'&&order=='asc'?'color:'+t('color1'):''"></text>
								<text class="iconfont icondaoxu" :style="field=='sell_price'&&order=='desc'?'color:'+t('color1'):''"></text>
							</view>  
						</view>
						<view class="classify-ul" v-if="curIndex>-1 && clist[curIndex].child.length>0">
							<view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;">
							 <view class="classify-li" :style="curIndex2==-1?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''" @tap="changeCTab" :data-id="clist[curIndex].id" data-index="-1">全部</view>
							 <block v-for="(item, idx2) in clist[curIndex].child" :key="idx2">
							 <view class="classify-li" :style="curIndex2==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''" @tap="changeCTab" :data-id="item.id" :data-index="idx2">{{item.name}}</view>
							 </block>
							</view>
						</view>
						<scroll-view class="classify-box" scroll-y="true" @scrolltolower="scrolltolower">
							<view class="product-itemlist">
								<view class="item" v-for="(item,index) in datalist" :key="item.id" :class="item.stock <= 0 ? 'soldout' : ''" @click="goto" :data-url="'/shopPackage/shop/product?id='+item.id">
									<view class="product-pic">
										<image class="image" :src="item.pic" mode="widthFix"/>
										<view class="overlay"><view class="text">售罄</view></view>
									</view>
									<view class="product-info">
										<view class="p1"><text>{{item.name}}</text></view>
										<view class="p2" v-if="item.price_type != 1 || item.sell_price > 0">
											<text class="t1" :style="{color:t('color1')}"><text style="font-size:20rpx;padding-right:1px">￥</text>{{item.sell_price}}</text>
											<text class="t2" v-if="item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
										</view>
										<view class="p2" v-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0" style="height: 50rpx;line-height: 44rpx;">
											<text class="t1" :style="{color:t('color1'),fontSize:'30rpx'}">询价</text>
												<block v-if="item.xunjia_text && item.price_type == 1">
													<view class="lianxi" :style="{background:t('color1')}" @tap.stop="showLinkChange" :data-lx_name="item.lx_name" :data-lx_bid="item.lx_bid" :data-lx_bname="item.lx_bname" :data-lx_tel="item.lx_tel" data-btntype="2">{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>
												</block>
										</view>
										<view class="p1" v-if="item.merchant_name" style="color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal"><text>{{item.merchant_name}}</text></view>
										<view class="p1" v-if="item.main_business" style="color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;"><text>{{item.main_business}}</text></view>
										<view class="p3">
											<view class="p3-1" v-if="item.sales>0"><text style="overflow:hidden">已售{{item.sales}}件</text></view>
										</view>
									<view v-if="item.sales<=0 && item.merchant_name" style="height: 44rpx;"></view>
										<view class="p4" v-if="!item.price_type" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" @click.stop="buydialogChange" :data-proid="item.id"><text class="iconfont icon_gouwuche"></text></view>
									</view>
								</view>
							</view>
							<nomore text="没有更多商品了" v-if="nomore"></nomore>
							<nodata text="暂无相关商品" v-if="nodata"></nodata>
							<view style="width:100%;height:100rpx"></view>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>
		<buydialog v-if="buydialogShow" :proid="proid" @buydialogChange="buydialogChange" @addcart="afteraddcart" :menuindex="menuindex" btntype="1" :needaddcart="false"></buydialog>
		
		
		
		<view style="height:auto;position:relative">
			<view style="width:100%;height:100rpx"></view>
			<view class="footer flex" :class="menuindex>-1?'tabbarbot':'notabbarbot'">
			  
				<view class="cart_ico" :style="{background:'linear-gradient(0deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap.stop="handleClickMask"><image class="img" src="/static/img/cart.png"/><view class="cartnum" :style="{background:t('color1')}" v-if="cartList.total>0">{{cartList.total}}</view></view>
				<view class="text1">合计</view>
				<view class="text2 flex1" :style="{color:t('color1')}"><text style="font-size:20rpx">￥</text>{{cartList.totalprice}}</view>
				<view class="op" :style="{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="gopay">去添加</view>
			</view>
		</view>

		<view v-if="cartListShow" class="popup__container" style="margin-bottom:100rpx" :class="menuindex>-1?'tabbarbot':'notabbarbot'">
			<view class="popup__overlay" @tap.stop="handleClickMask" style="margin-bottom:100rpx" :class="menuindex>-1?'tabbarbot':'notabbarbot'"></view>
			<view class="popup__modal" style="min-height:400rpx;padding:0">
				<view class="popup__title" style="border-bottom:1px solid #EFEFEF">
					<text class="popup__title-text" style="color:#323232;font-weight:bold;font-size:32rpx">购物车</text>
					<view class="popup__close flex-y-center" @tap.stop="clearShopCartFn" style="color:#999999;font-size:24rpx"><image src="/static/img/del.png" style="width:24rpx;height:24rpx;margin-right:6rpx"/>清空</view>
				</view>
				<view class="popup__content" style="padding:0">
					<scroll-view scroll-y class="prolist">
						<block v-for="(cart, index) in cartList.list" :key="index">
							<view class="proitem">
								<image :src="cart.guige.pic?cart.guige.pic:cart.product.pic" class="pic flex0"></image>
								<view class="con">
									<view class="f1">{{cart.product.name}}</view>
									<view class="f2" v-if="cart.guige.name!='默认规格'">{{cart.guige.name}}</view>
									<view class="f3" style="color:#ff5555;margin-top:10rpx;font-size:28rpx">￥{{cart.guige.sell_price}}</view>
								</view>
								<view class="addnum">
									<view class="minus"><image class="img" src="/static/img/cart-minus.png" @tap="addcart" data-num="-1" :data-proid="cart.proid" :data-ggid="cart.ggid" :data-stock="cart.guige.stock"/></view>
									<text class="i">{{cart.num}}</text>
									<view class="plus"><image class="img" src="/static/img/cart-plus.png" @tap="addcart" data-num="1" :data-proid="cart.proid" :data-ggid="cart.ggid" :data-stock="cart.guige.stock"/></view>
								</view>
							</view>
						</block>
						<block v-if="!cartList.list.length">
							<text class="nopro">暂时没有商品喔~</text>
						</block>
					</scroll-view>
				</view>
			</view>
		</view>
	</block>
	   <!-- 浮动的“添加收货地址”按钮 -->
	        <view class="floating-address-btn" @tap="addAddress">
	            <image class="img" src="/static/img/address.png" />
	            <text class="btn-text">地址</text>
	        </view>
	        
	<!-- <loading v-if="loading"></loading> -->
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
		opt:{},
					loading:false,
		isload: false,
					menuindex:-1,
					pagenum: 1,
					nomore: false,
					nodata: false,
					order: '',
					field: '',
					clist: [],
					curIndex: -1,
					curIndex2: -1,
					datalist: [],
					nodata: false,
					curCid: 0,
					proid:0,
					buydialogShow: false,
					bid:'',
		      searchKeyword: '', // 搜索关键词
		      showLinkStatus:false,
		      lx_name:'',
		      lx_bid:'',
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			pagenum: 1,
			cartListShow:false,
			buydialogShow:false,
			harr:[],
			datalist: [],
			cartList:{},
			proid:'',
			totalprice:'0.00',
			currentActiveIndex: 0,
			animation: true,
			scrollToViewId: "",
			bid:'',
			scrollState:true,
			
			
			clist: [],
			curIndex: -1,
			curIndex2: -1,
			datalist: [],
			nodata: false,
			curCid: 0,
			proid:0
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.bid = this.opt.bid ? this.opt.bid  : '';
		this.send_id = this.opt.mid;
		this.sign_id = this.opt.sign_id;		
		this.getdata();
  },
  onShareAppMessage() {
	  let mid = uni.getStorageSync("mid");
  	return {
		title:"代客下单请求",
		path:'/shopPackage/shop/daigoupick?mid='+mid
	}
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
	  changeOrder: function (e) {
	      
	  	var t = e.currentTarget.dataset;
	    
	  	this.field = t.field; 
	  	this.order = t.order;
	   
	  	this.pagenum = 1;
	  	this.datalist = []; 
	  	this.nomore = false;
	  	
	  	this.getclassify();
	    
	  },
	    // 新增：添加收货地址的方法
	      addAddress: function () {
	          // 检查用户是否拥有 mid
	          if (this.opt.mid) {
	              // 导航到添加地址的页面
	              uni.navigateTo({
	                  url: '/pagesExa/daike/address/address?mid=' + this.opt.mid
	              });
	          } else {
	              // 提示用户登录或其他处理
	              this.$refs.popmsg.show('请先登录以添加收货地址');
	          }
	      },
		   onSearchInput(e) {
		        this.searchKeyword = e.target.value;
		        if (!this.searchKeyword.trim()) {
		          this.resetSearch();
		        }
		      },
		      searchProducts() {
		        // 当搜索关键词为空时，显示全部商品
		        if (!this.searchKeyword.trim()) {
		          this.resetSearch();
		          return;
		        }
		  
		        this.pagenum = 1;
		        this.datalist = [];
		        this.nomore = false;
		        this.nodata = false;
		        this.loading = true;
		  
		        const params = {
		          pagenum: this.pagenum,
		          keyword: this.searchKeyword,
		          bid: this.bid,
		          field: this.field,
		          order: this.order,
		        };
		  
		        app.post('ApiShop/getprolist', params, (res) => {
		          this.loading = false;
		          if (res.status === 1) {
		            this.datalist = res.data;
		            this.nodata = res.data.length === 0;
		          } else {
		            this.datalist = [];
		            this.nodata = true;
		            this.$refs.popmsg.show(res.msg || '搜索失败，请稍后重试');
		          }
		        });
		      },
		      resetSearch() {
		        // 重置搜索，显示初始数据
		        this.pagenum = 1;
		        this.datalist = [];
		        this.nomore = false;
		        this.nodata = false;
		        this.getproducts();
		      },
		      loadMore() {
		        if (this.nomore || this.loading) return;
		        this.pagenum += 1;
		        if (this.searchKeyword.trim()) {
		          this.searchProducts(); // 加载更多搜索结果
		        } else {
		          this.getproducts(true); // 加载更多初始数据
		        }
		      },
	switchRightTab: function (e) {
	    var that = this;
	
	    // 获取当前点击元素的数据属性
	    var id = e.currentTarget.dataset.id;
	    var index = parseInt(e.currentTarget.dataset.index, 10);
	
	    // 更新当前选中的索引
	    this.curIndex = index;
	    this.curIndex2 = -1;  // 假设这是用于其他目的的索引
	
	    // 如果是点击了“全部”按钮
	    if (index === -1) {
	        // 重置相关状态变量
	        this.nodata = false;
	        this.curCid = null;  // 或者设置为你定义的表示“全部”的值
	        this.pagenum = 1;
	        this.datalist = [];
	        this.nomore = false;
	
	        // 调用获取所有分类数据的方法
	        this.getAllData();
	    } else {
	        // 如果点击的是某个具体的分类
	        this.nodata = false;
	        this.curCid = id;
	        this.pagenum = 1;
	        this.datalist = [];
	        this.nomore = false;
	
	        // 调用获取指定分类数据的方法
	        this.getclassify();
	    }
	},
	
	// 新增一个方法来处理获取所有数据的逻辑
	getAllData: function () {
	    // 在这里实现获取所有数据的逻辑
	    // 例如，发送请求到服务器并更新datalist等
	    console.log('获取所有数据');
	    // 这里可以添加你的实际数据获取代码
	},
	    getproducts:function(loadmore)
		{
			var that = this;
			     
			var pagenum = that.pagenum;
			   
			var cid = that.curCid;
			var bid = that.opt.bid ? that.opt.bid : '';
			var order = that.order;
			    
			var field = that.field; 
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			var wherefield = {};
			wherefield.pagenum = pagenum;
			wherefield.field = field;
			wherefield.order = order;
			wherefield.bid = bid;
			if(bid > 0){
				wherefield.cid2 = cid;
			}else{
				wherefield.cid = cid;
			}
			app.post('ApiShop/getprolist2',wherefield, function (res) { 
				that.loading = false;
			
				uni.stopPullDownRefresh();
			
				var data = res.data;
				if (data.length == 0) {
					if(pagenum == 1){
						that.nodata = true;
					}else{
						that.nomore = true;
					}
				}
				var datalist = that.datalist;
				var newdata = datalist.concat(data);
				that.datalist = newdata;
			});
			
		},
	    getclassify:function(){
	    	var that = this;
	    	var nowcid = that.opt.cid;
	    	if (!nowcid) nowcid = '';
	    	that.pagenum = 1;
	    	that.datalist = [];
	    	that.loading = true;
	    	app.get('ApiShop/classify', {cid:nowcid,bid:that.bid}, function (res) {
	    		that.loading = false;
	    	  var clist = res.data;
	    	  that.clist = clist;
	    	  if (nowcid) {
	    	    for (var i = 0; i < clist.length; i++) {
	    	      if (clist[i]['id'] == nowcid) {
	    	        that.curIndex = i;
	    	        that.curCid = nowcid;
	    	      }
	    	      var downcdata = clist[i]['child'];
	    	      var isget = 0;
	    	      for (var j = 0; j < downcdata.length; j++) {
	    	        if (downcdata[j]['id'] == nowcid) {
	    	          that.curIndex = i;
	    	          that.curIndex2 = j;
	    	          that.curCid = nowcid;
	    	          isget = 1;
	    	          break;
	    	        }
	    	      }
	    	      if (isget) break;
	    	    }
	    	  }
	    		that.loaded();
	    		that.getproducts();
	    	});
	    },
		
		getdata: function () {
		    var that = this;
		    var bid = that.opt.bid ? that.opt.bid : 0;
		    var nowcid = that.opt.cid || that.curCid; // 获取当前分类ID或默认为空
		
		    that.pagenum = 1;
		    that.datalist = [];
		    that.loading = true;
		
		    if (nowcid) {
		      // 如果有选择的分类，则从分类接口获取数据
		      app.get('ApiShop/classify', { cid: nowcid, bid: bid }, function (res) {
		        that.loading = false;
		        var clist = res.data;
		        that.clist = clist;
		
		        // 更新当前选中的分类索引
		        for (var i = 0; i < clist.length; i++) {
		          if (clist[i]['id'] == nowcid) {
		            that.curIndex = i;
		            that.curCid = nowcid;
		            break;
		          }
		          var downcdata = clist[i]['child'];
		          for (var j = 0; j < downcdata.length; j++) {
		            if (downcdata[j]['id'] == nowcid) {
		              that.curIndex = i;
		              that.curIndex2 = j;
		              that.curCid = nowcid;
		              break;
		            }
		          }
		          if (that.curCid === nowcid) break;
		        }
		
		        // 加载商品数据
		        that.getdatalist();
		      });
		    } else {
		      // 如果没有选择的分类，则从快速购买接口获取数据
		      app.get('ApiShop/fastbuy', { bid: bid }, function (res) {
		        that.loading = false;
		        that.datalist = res.data;
		        that.cartList = res.cartList;
		        that.loaded();
		
		        // 调用分类接口以获取分类信息
		        that.getclassify();
		      });
		    }
		  },
    clickRootItem: function (t) {
			this.scrollState=false;
      var e = t.currentTarget.dataset;
      this.scrollToViewId = 'detail-' + e.rootItemId;
      this.currentActiveIndex = e.rootItemIndex;
			setTimeout(()=>{
				this.scrollState=true;
			},500)
    },
	addcart:function(e){
		var that = this;
		var ks = that.ks;
		var num = e.currentTarget.dataset.num;
		var proid = e.currentTarget.dataset.proid;
		var ggid = e.currentTarget.dataset.ggid;
		that.loading = true;
		app.post('ApiShop/addcart', {proid: proid,ggid: ggid,num: num}, function (res) {
			that.loading = false;
			if (res.status == 1) {
				that.getdata();
			} else {
				app.error(res.msg);
			}
		});
	},
    //加入购物车弹窗后
    afteraddcart: function (e) {
			e.hasoption = false;
            this.addcart({currentTarget:{dataset:e}});
    },
    clearShopCartFn: function () {
      var that = this;
      app.post("ApiShop/cartclear", {bid:that.opt.bid}, function (res) {
        that.getdata();
      });
    },
    gopay: function () {
      var cartList = this.cartList.list;
      if (cartList.length == 0) {
        app.alert('请先添加商品到购物车');
        return;
      }
	  let that = this;
      var prodata = [];
      for (var i = 0; i < cartList.length; i++) {
        prodata.push(cartList[i].proid + ',' + cartList[i].ggid + ',' + cartList[i].num);
      }
	  
	   let sign_id= uni.getStorageSync("mid");
      // app.goto('adddaigou?frompage=fastbuy&prodata=' + prodata.join('-')+ '&sign_id='+this.sign_id+'&send_id='+this.send_id+'&totalprice='+ that.cartList.totalprice);
       let url = "ApiDaigou/adddaigou";
	   app.post(
	     url,
		 {
			 'prodata':prodata,
			 "sign_id":sign_id,
			 'send_id':this.send_id,
			 'totalprice':that.cartList.totalprice
		 },
		 function(res)
		 {
			 let data = res;
			 if(data['status']==1)
			 {
				 uni.showModal({
				 	title:"",
					content:"选单成功",
					showCancel:false,
					complete() {
						uni.navigateTo({
							url:"/pagesExa/daike/daigoulistadmin"
						})
					}
				 })
			 }
		 }
	   );
	},
    gotoCatproductPage: function (t) {
      var e = t.currentTarget.dataset;
			if(this.bid){
				app.goto('/shopPackage/shop/prolist?bid='+this.bid+'&cid2=' + e.id);
			}else{
				app.goto('/shopPackage/shop/prolist?cid=' + e.id);
			}
    },
    scroll: function (e) {
		if(this.scrollState){
			var scrollTop = e.detail.scrollTop;
			var harr = this.harr;
			var countH = 0;
			for (var i = 0; i < harr.length; i++) {
			  if (scrollTop >= countH && scrollTop < countH + harr[i]) {
			    this.currentActiveIndex = i;
			    break;
			  }
			  countH += harr[i];
			}
		}
    },
		buydialogChange: function (e) {
			if(!this.buydialogShow){
				this.proid = e.currentTarget.dataset.proid
			}
			this.buydialogShow = !this.buydialogShow;
		},
		handleClickMask:function(){
			this.cartListShow = !this.cartListShow;
		}
  }
};
</script>
<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #fff;color: #939393;display: flex;flex-direction:column}
.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}
.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}
.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}
.search-box .search-text {font-size:24rpx;color:#C2C2C2;width: 100%;}

.content-container{flex:1;height:100%;display:flex;overflow: hidden;}

.nav_left{width: 25%;height:100%;background: #ffffff;overflow-y:scroll;}
.nav_left .nav_left_items{line-height:50rpx;color:#333;font-weight:bold;border-bottom:0px solid #E6E6E6;font-size:28rpx;position: relative;border-right:0 solid #E6E6E6;padding:25rpx 30rpx;}
.nav_left .nav_left_items.active{background: #fff;color:#333;font-size:28rpx;font-weight:bold}
.nav_left .nav_left_items .before{display:none;position:absolute;top:50%;margin-top:-12rpx;left:10rpx;height:24rpx;border-radius:4rpx;width:8rpx}
.nav_left .nav_left_items.active .before{display:block}

.nav_right{width: 75%;height:100%;display:flex;flex-direction:column;background: #f6f6f6;box-sizing: border-box;padding:20rpx 20rpx 0 20rpx}
.nav_right-content{background: #ffffff;padding:0 20rpx;height:100%}
.nav-pai{ width: 100%;display:flex;align-items:center;justify-content:center;}
.nav-paili{flex:1; text-align:center;color:#323232; font-size:28rpx;font-weight:bold;position: relative;height:80rpx;line-height:80rpx;}
.nav-paili .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.nav-paili .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}

.classify-ul{width:100%;height:100rpx;padding:0 10rpx;}
.classify-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:22rpx;color:#6C737F;font-size:20rpx;text-align: center;height:44rpx; line-height:44rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}

.classify-box{padding: 0 0 20rpx 0;width: 100%;height:calc(100% - 60rpx);overflow-y: scroll; border-top:1px solid #F5F6F8;}
.classify-box .nav_right_items{ width:100%;border-bottom:1px #f4f4f4 solid;  padding:16rpx 0;  box-sizing:border-box;  position:relative; }

.product-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}
.product-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:14rpx 0;border-radius:10rpx;border-bottom:1px solid #F8F8F8}
.product-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}
.product-itemlist .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.product-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}
.product-itemlist .product-info {width: 70%;padding:0 10rpx 5rpx 20rpx;position: relative;}
.product-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
.product-itemlist .product-info .p2{margin-top:10rpx;height:36rpx;line-height:36rpx;overflow:hidden;}
.product-itemlist .product-info .p2 .t1{font-size:32rpx;}
.product-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.product-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx}
.product-itemlist .product-info .p3-1{font-size:20rpx;height:30rpx;line-height:30rpx;text-align:right;color:#999}
.product-itemlist .product-info .p4{width:56rpx;height:56rpx;border-radius:50%;position:absolute;display:relative;bottom:6rpx;right:4rpx;text-align:center;}
.product-itemlist .product-info .p4 .icon_gouwuche{font-size:32rpx;height:56rpx;line-height:56rpx}
.overlay {background-color: rgba(0,0,0,.5); position: absolute; width:60%; height: 60%; border-radius: 50%; display: none; top: 20%; left: 20%;}
.overlay .text{ color: #fff; text-align: center; transform: translateY(100%);}
.product-itemlist .soldout .product-pic .overlay{ display: block;}
::-webkit-scrollbar{width: 0;height: 0;color: transparent;}

.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}
.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}
.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}
.posterDialog .close .img{ width:32rpx;height:32rpx;}
.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}
.posterDialog .content .img{width:540rpx;height:960rpx}
.linkDialog {background:rgba(0,0,0,0.4);z-index:11;}
.linkDialog .main{ width: 90%; position: fixed; top: 50%; left: 50%; margin: 0;-webkit-transform: translate(-50%,-50%);transform: translate(-50%,-50%);}
.linkDialog .title {font-weight: bold;margin-bottom: 30rpx;}
.linkDialog .row {display: flex; height:80rpx;line-height: 80rpx; padding: 0 16rpx;}
.linkDialog .row .f1 {width: 40%; text-align: left;}
.linkDialog .row .f2 {width: 60%; height:80rpx;line-height: 80rpx;text-align: right;align-items:center;}
.linkDialog .image{width: 28rpx; height: 28rpx; margin-left: 8rpx;margin-top: 2rpx;}
.linkDialog .copyicon {width: 28rpx; height: 28rpx; margin-left: 8rpx; position: relative; top: 4rpx;}

.lianxi{color: #fff;border-radius: 50rpx 50rpx;line-height: 50rpx;text-align: center;font-size: 22rpx;padding: 0 14rpx;display: inline-block;float: right;}

.prolist {max-height: 620rpx;min-height: 320rpx;overflow: hidden;padding:0rpx 20rpx;font-size: 28rpx;border-bottom: 1px solid #e6e6e6;}
.prolist .nopro {text-align: center;font-size: 26rpx;display: block;margin: 80rpx auto;}
.prolist .proitem{position: relative;padding:10rpx 0;display:flex;border-bottom:1px solid #eee}
.prolist .proitem .pic{width: 120rpx;height: 120rpx;margin-right: 20rpx;}
.prolist .proitem .con{padding-right:180rpx;padding-top:10rpx}
.prolist .proitem .con .f1{color:#323232;font-size:26rpx;line-height:32rpx;margin-bottom: 10rpx;margin-top: -6rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
.prolist .proitem .con .f2{font-size: 24rpx;line-height:28rpx;color: #999;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;overflow: hidden;}
.prolist .proitem .addnum {position: absolute;right: 20rpx;bottom:50rpx;font-size: 30rpx;color: #666;width: auto;display:flex;align-items:center}
.prolist .proitem .addnum .plus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}
.prolist .proitem .addnum .minus {width:48rpx;height:36rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}
.prolist .proitem .addnum .img{width:24rpx;height:24rpx}
.prolist .proitem .addnum .i {padding: 0 20rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx}
.prolist .tips {font-size: 22rpx;color: #666;text-align: center;line-height: 56rpx;background: #f5f5f5;}
.overlay {background-color: rgba(0,0,0,.5); position: absolute; width:60%; height: 60%; border-radius: 50%; display: none; top: 20%; left: 20%;}


.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;z-index:8;display:flex;align-items:center;padding:0 20rpx;border-top:1px solid #EFEFEF}
.footer .cart_ico{width:64rpx;height:64rpx;border-radius: 10rpx;display:flex;align-items:center;justify-content:center;position:relative}
.footer .cart_ico .img{width:36rpx;height:36rpx;}
.footer .cart_ico .cartnum{position:absolute;top:-17rpx;right:-17rpx;width:34rpx;height:34rpx;border:1px solid #fff;border-radius:50%;display:flex;align-items:center;justify-content:center;overflow:hidden;font-size:20rpx;font-weight:bold;color:#fff}
.footer .text1 {height: 100rpx;line-height: 100rpx;color:#555555;font-weight:bold;font-size: 30rpx;margin-left:40rpx;margin-right:10rpx}
.footer .text2 {font-size: 32rpx;font-weight:bold}
.footer .op{width: 200rpx;height: 72rpx;line-height:72rpx;border-radius: 36rpx;font-weight:bold;color:#fff;font-size:28rpx;text-align:center}
::-webkit-scrollbar{width: 0;height: 0;color: transparent;}
/* 添加地址按钮样式 */

/* 浮动“添加收货地址”按钮样式 */
.floating-address-btn {
    position: fixed;
    bottom: 120rpx; /* 距离底部的距离，调整以避免与底部导航栏重叠 */
    left: 20rpx;   /* 距离右边的距离 */
    width: 100rpx;
    height: 100rpx;
    background: linear-gradient(45deg, #aa0000, #aa0000); /* 你可以根据需要调整颜色 */
    border-radius: 50%; /* 圆形按钮 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
    z-index: 1000; /* 确保按钮在最上层 */
    cursor: pointer;
    transition: transform 0.3s;
}

.floating-address-btn:hover {
    transform: scale(1.1);
}

.floating-address-btn .img {
    width: 36rpx;
    height: 36rpx;
    margin-bottom: 4rpx;
}

.floating-address-btn .btn-text {
    font-size: 24rpx;
    color: #fff;
}

</style>
