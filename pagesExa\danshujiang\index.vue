<template>
  <view class="danshujiang-container">
    <!-- 顶部奖励概览卡片 -->
    <view class="reward-overview">
      <view class="overview-card">
        <view class="card-header">
          <text class="title">当前奖励进度</text>
          <text class="mode-tag" :class="rewardMode">{{rewardModeName}}</text>
        </view>
        <view class="progress-section">
          <view class="current-order">
            <text class="number">{{currentOrderCount}}</text>
            <text class="label">当前单数</text>
          </view>
          <view class="progress-bar-wrapper">
            <view class="progress-bar">
              <view class="progress" :style="{width: progressWidth + '%'}">
                <view class="progress-dot"></view>
              </view>
              <view class="milestone" v-for="(item, index) in milestones" :key="index"
                :class="{active: currentOrderCount >= item.orderNum, current: currentOrderCount === item.orderNum}"
                :style="{left: item.position + '%'}">
                <view class="milestone-dot"></view>
                <view class="milestone-info">
                  <text class="milestone-num">{{item.orderNum}}单</text>
                  <!-- <text class="milestone-reward">¥{{item.reward || '0.00'}}</text> -->
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- <view class="reward-stats">
          <view class="stat-item">
            <text class="amount">¥{{totalReward}}</text>
            <text class="label">累计奖励</text>
          </view>
          <view class="stat-item highlight">
            <text class="amount">¥{{pendingReward}}</text>
            <text class="label">待发放奖励</text>
          </view>
        </view> -->

        
      </view>
    </view>

    <!-- 总统计数据卡片 -->
    <view class="stats-overview">
      <view class="stats-card">
        <view class="stats-title">总统计数据</view>
        <view class="stats-grid">
          <view class="stats-item">
            <text class="amount">¥{{totalReward}}</text>
            <text class="label">总奖励金额</text>
          </view>
          <view class="stats-item">
            <text class="amount">¥{{rewardedAmount}}</text>
            <text class="label">已发放奖励</text>
          </view>
          <view class="stats-item">
            <text class="amount">¥{{pendingReward}}</text>
            <text class="label">待发放奖励</text>
          </view>
          <view class="stats-item">
            <text class="amount">¥{{pendingClaim || '0.00'}}</text>
            <text class="label">待领取奖励</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 奖励规则说明 -->
    <view class="rules-section">
      <view class="section-title">
        <text class="title-text">奖励规则</text>
      </view>
      <view class="rules-content">
        <block v-if="rewardMode === 'normal'">
          <view class="rule-item" v-for="(rule, index) in allRules" :key="index">
            <view class="rule-icon">{{index + 1}}</view>
            <view class="rule-detail">
              <text class="order-num">第{{rule.order_num}}单</text>
              <text class="reward-rate">{{rule.reward_rate}}%奖励</text>
            </view>
          </view>
        </block>
        <block v-if="rewardMode === 'ladder'">
          <view class="ladder-rules">
            <view class="ladder-item" v-for="(rule, index) in allRules" :key="index"
              :class="{active: currentOrderCount >= rule.order_num}">
              <view class="ladder-step">
                <text class="step-range">{{rule.order_num}}单</text>
                <text class="step-rate">{{rule.reward_rate}}%</text>
              </view>
            </view>
          </view>
        </block>
        <block v-if="rewardMode === 'cycle'">
          <view class="cycle-rules">
            <view class="cycle-progress">
              <view class="cycle-indicator" :style="{transform: 'rotate(' + calculateCycleRotation() + 'deg)'}"></view>
              <view v-for="(rule, index) in allRules" :key="index"
                class="cycle-step"
                :class="{active: currentStage && currentStage.order_num && currentStage.order_num === rule.order_num}"
                :style="{
                  transform: `rotate(${index * (360/allRules.length)}deg) translateY(-120rpx) rotate(-${index * (360/allRules.length)}deg)`
                }">
                <text class="step-num">{{rule.order_num}}单</text>
                <text class="step-rate">{{rule.reward_rate}}%</text>
              </view>
            </view>
            <view class="cycle-info">
              <text class="cycle-text">{{cycle_num}}单为一个循环</text>
              <text class="current-cycle">第{{Math.ceil(currentOrderCount/cycle_num)}}轮 第{{(currentOrderCount-1)%cycle_num+1}}单</text>
            </view>
          </view>
        </block>
        <view class="current-stage" v-if="currentStage">
          <view class="stage-info">
            <text class="label">当前阶段</text>
            <text class="value">{{currentStage.order_num || 0}}单 - {{currentStage.reward_rate || 0}}%</text>
          </view>
          <view class="stage-info" v-if="nextStage">
            <text class="label">下一阶段</text>
            <text class="value">{{nextStage.order_num || 0}}单 - {{nextStage.reward_rate || 0}}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 奖励记录列表 -->
    <view class="records-section">
      <view class="section-title">
        <text class="title-text">奖励记录</text>
      </view>
      <view class="records-list">
        <view class="record-item" v-for="(record, index) in rewardRecords" :key="index">
          <view class="record-main">
            <view class="order-info">
              <text class="order-id">订单 #{{record.order_id}}</text>
              <text class="order-amount">¥{{record.order_amount}}</text>
            </view>
            <view class="reward-info">
              <text class="reward-amount">+¥{{record.reward_amount}}</text>
              <text class="reward-rate">{{record.reward_rate}}%</text>
            </view>
          </view>
          <view class="record-status" :class="record.status === 1 ? 'success' : 'pending'">
            {{record.status === 1 ? '已发放' : '待发放'}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 奖励详情和手动领取区域 -->
    <view class="reward-details-section">
      <view class="section-title">
        <text class="title-text">奖励详情</text>
      </view>
      <view class="reward-details-list">
        <view class="detail-item" v-for="(item, index) in rewardDetails" :key="index">
          <view class="detail-header">
            <text class="order-title">订单 #{{item.order_id}}</text>
            <text class="order-num">第{{item.order_num_actual}}单</text>
          </view>
          <view class="detail-content">
            <view class="calculation">
              <text class="calc-text">{{item.calculation || `订单金额 ${item.order_amount} × 奖励比例 ${item.reward_rate}% = ${item.reward_amount}`}}</text>
            </view>
            <view class="detail-amount">
              <text class="amount-value">¥{{item.reward_amount}}</text>
              <view class="claim-btn" 
                :class="{
                  'can-claim': !item.is_claimed && !item.is_paid,
                  'pending': item.is_claimed && !item.is_paid,
                  'claimed': item.is_paid,
                }"
                @click="claimReward(item)">
                <text v-if="!item.is_claimed && !item.is_paid">领取</text>
                <text v-else-if="item.is_claimed && !item.is_paid">待发放</text>
                <text v-else>已发放</text>
              </view>
            </view>
          </view>
        </view>
        <nodata v-if="rewardDetails.length === 0"></nodata>
      </view>
    </view>

    <!-- 加载状态组件 -->
    <nodata v-if="nodata"></nodata>
    <nomore v-if="nomore"></nomore>
    <loading v-if="loading"></loading>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      activityId: '',
      currentOrderCount: 0,
      rewardMode: 'normal',
      rewardModeName: '',
      progressWidth: 0,
      totalReward: '0.00',
      pendingReward: '0.00',
      rewardedAmount: '0.00',
      milestones: [],
      allRules: [],
      currentStage: null,
      nextStage: null,
      rewardRecords: [],
      page: 1,
      limit: 10,
      loading: false,
      nodata: false,
      nomore: false,
      cycleRotation: 120,
      cycle_num: 0,
      rewardDetails: [],
      pendingClaim: '0.00'
    }
  },
  onLoad(options) {
    this.activityId = options.id || ''
    this.getdata()
  },
  onPullDownRefresh() {
    this.getdata()
  },
  onReachBottom() {
    if (!this.nomore && !this.nodata) {
      this.page = this.page + 1
      this.getRewardRecords()
    }
  },
  methods: {
    getdata(loadmore) {
      if (!loadmore) {
        this.page = 1
        this.rewardRecords = []
      }
      this.loading = true
      this.getUserRewardInfo()
      this.getRewardRecords()
      this.getRewardStats()
    },
    async getUserRewardInfo() {
      try {
        const that = this
        app.showLoading('加载中')
        app.post('ApiDanshujiang/getUserRewardInfo', {
          aid: app.aid,
          activity_id: this.activityId
        }, function(res) {
          app.showLoading(false)
          if (res.status === 1) {
            const data = res.data
            that.currentOrderCount = data.current_order_count
            that.rewardMode = data.reward_mode === 1 ? 'normal' : data.reward_mode === 2 ? 'ladder' : 'cycle'
            that.rewardModeName = data.reward_mode_name
            that.allRules = data.all_rules || []
            that.currentStage = data.current_stage
            that.nextStage = data.next_stage
            that.cycle_num = data.cycle_num
            that.rewardDetails = data.reward_details || []
            
            // 更新里程碑数据
            that.updateMilestones()
            // 计算进度
            that.calculateProgress()
          } else {
            app.error(res.msg)
          }
        })
      } catch (error) {
        app.showLoading(false)
        app.error('获取奖励信息失败')
      }
    },
    async getRewardRecords() {
      try {
        const that = this
        that.loading = true
        app.showLoading('加载中')
        app.post('ApiDanshujiang/getRewardRecords', {
          aid: app.aid,
          activity_id: this.activityId,
          page: this.page,
          limit: this.limit
        }, function(res) {
          that.loading = false
          app.showLoading(false)
          if (res.status === 1) {
            const { list, total } = res.data
            if (that.page === 1) {
              that.rewardRecords = list
              that.nodata = list.length === 0
            } else {
              that.rewardRecords = [...that.rewardRecords, ...list]
            }
            that.nomore = that.rewardRecords.length >= total
            uni.stopPullDownRefresh()
          } else {
            app.error(res.msg)
          }
        })
      } catch (error) {
        this.loading = false
        app.showLoading(false)
        app.error('获取奖励记录失败')
        uni.stopPullDownRefresh()
      }
    },
    async getRewardStats() {
      try {
        const that = this
        app.showLoading('加载中')
        app.post('ApiDanshujiang/getRewardStats', {
          aid: app.aid,
          activity_id: this.activityId
        }, function(res) {
          app.showLoading(false)
          if (res.status === 1) {
            const data = res.data
            // 更新总体统计数据
            that.totalReward = data.total_reward.toFixed(2)
            that.pendingReward = data.pending_reward.toFixed(2)
            that.rewardedAmount = data.issued_reward.toFixed(2)
            that.pendingClaim = data.pending_claim ? data.pending_claim.toFixed(2) : '0.00'
          } else {
            app.error(res.msg)
          }
        })
      } catch (error) {
        app.showLoading(false)
        app.error('获取奖励统计失败')
      }
    },
    updateMilestones() {
      if (!this.allRules || !this.allRules.length) return
      
      if (this.rewardMode === 'cycle') {
        const currentCycle = Math.ceil(this.currentOrderCount / this.cycle_num)
        const maxCycle = currentCycle + 1
        this.milestones = this.allRules.map((rule, index) => ({
          orderNum: rule.order_num + ((currentCycle - 1) * this.cycle_num),
          rate: rule.reward_rate,
          position: (index / (this.allRules.length - 1)) * 100,
          reward: this.calculateRewardAtMilestone(rule.order_num, rule.reward_rate)
        }))
      } else {
        const maxOrderNum = Math.max(...this.allRules.map(rule => rule.order_num)) * 2
        this.milestones = this.allRules.map((rule, index) => ({
          orderNum: rule.order_num,
          rate: rule.reward_rate,
          position: (rule.order_num / maxOrderNum) * 100,
          reward: this.calculateRewardAtMilestone(rule.order_num, rule.reward_rate)
        }))
      }
    },
    calculateRewardAtMilestone(orderNum, rate) {
      return ((rate / 100) * orderNum * 100).toFixed(2)
    },
    calculateProgress() {
      if (!this.allRules || !this.allRules.length) return
      
      if (this.rewardMode === 'cycle') {
        const currentPosition = ((this.currentOrderCount - 1) % this.cycle_num) + 1
        this.progressWidth = Math.max((currentPosition / this.cycle_num) * 100, 5)
      } else {
        const maxOrderNum = Math.max(...this.allRules.map(rule => rule.order_num)) * 2
        this.progressWidth = Math.min((this.currentOrderCount / maxOrderNum) * 100, 100)
      }
    },
    calculateCycleRotation() {
      if (!this.allRules || !this.allRules.length || !this.currentStage) return 0
      
      const currentIndex = this.allRules.findIndex(rule => 
        rule.order_num === (this.currentStage.order_num || 0)
      )
      
      if (currentIndex === -1) return 0
      
      const anglePerStep = 360 / this.allRules.length
      
      return currentIndex * anglePerStep
    },
    async claimReward(item) {
      if (item.is_claimed || item.is_paid) return
      
      try {
        const that = this
        app.showLoading('领取中')
        app.post('ApiDanshujiang/claimReward', {
          aid: app.aid,
          activity_id: item.activity_id || this.activityId,
          order_id: item.order_id
        }, function(res) {
          app.showLoading(false)
          if (res.status === 1) {
            item.is_claimed = true
            if (res.data.reward_details) {
              that.rewardDetails = res.data.reward_details
            }
            that.getUserRewardInfo()
            that.getRewardStats()
            app.success(res.msg || '领取成功，等待发放')
          } else {
            app.error(res.msg)
          }
        })
      } catch (error) {
        app.showLoading(false)
        app.error('领取奖励失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.danshujiang-container {
  min-height: 100vh;
  background: #f8f9fd;
  padding: 20rpx;
  position: relative;

  /deep/ .nodata, /deep/ .nomore, /deep/ .loading {
    position: relative;
    margin: 30rpx auto;
    text-align: center;
  }
}

.reward-overview {
  margin-bottom: 30rpx;
  
  .overview-card {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 20rpx;
    padding: 30rpx;
    color: #fff;
    box-shadow: 0 4rpx 20rpx rgba(99, 102, 241, 0.2);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .title {
        font-size: 32rpx;
        font-weight: bold;
      }

      .mode-tag {
        font-size: 24rpx;
        padding: 6rpx 16rpx;
        border-radius: 30rpx;
        background: rgba(255, 255, 255, 0.2);
      }
    }

    .progress-section {
      margin: 40rpx 0;

      .current-order {
        display: flex;
        align-items: baseline;
        margin-bottom: 30rpx;
        
        .number {
          font-size: 60rpx;
          font-weight: bold;
          margin-right: 12rpx;
          color: #ffd700;
          text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
        }

        .label {
          font-size: 28rpx;
          opacity: 0.9;
        }
      }

      .progress-bar-wrapper {
        padding: 30rpx 20rpx;
        position: relative;
      }

      .progress-bar {
        position: relative;
        height: 8rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20rpx;

        .progress {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          background: linear-gradient(90deg, #ffd700, #ffed4a);
          border-radius: 20rpx;
          transition: width 0.5s ease;

          .progress-dot {
            position: absolute;
            right: -8rpx;
            top: 50%;
            transform: translateY(-50%);
            width: 24rpx;
            height: 24rpx;
            background: #fff;
            border: 4rpx solid #ffd700;
            border-radius: 50%;
            box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
          }
        }

        .milestone {
          position: absolute;
          transform: translateX(-50%);
          top: -10rpx;
          z-index: 1;
          
          .milestone-dot {
            width: 16rpx;
            height: 16rpx;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            margin: 0 auto 16rpx;
            border: 3rpx solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
          }

          .milestone-info {
            position: absolute;
            top: 30rpx;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            white-space: nowrap;

            .milestone-num {
              font-size: 24rpx;
              margin-bottom: 4rpx;
              opacity: 0.8;
            }

            .milestone-reward {
              font-size: 24rpx;
              color: #ffd700;
              font-weight: bold;
            }
          }

          &.active {
            .milestone-dot {
              background: #fff;
              border-color: #ffd700;
              transform: scale(1.2);
            }

            .milestone-info {
              .milestone-num {
                opacity: 1;
              }
            }
          }

          &.current {
            .milestone-dot {
              background: #ffd700;
              border-color: #fff;
              transform: scale(1.4);
              box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
            }

            .milestone-info {
              .milestone-num,
              .milestone-reward {
                color: #fff;
                font-weight: bold;
                transform: scale(1.1);
              }
            }
          }
        }
      }
    }

    .reward-stats {
      display: flex;
      justify-content: space-between;
      padding: 30rpx;
      margin-top: 20rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 16rpx;

      .stat-item {
        text-align: center;

        .amount {
          font-size: 48rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
          display: block;
        }

        .label {
          font-size: 28rpx;
          opacity: 0.9;
          display: block;
        }

        &.highlight .amount {
          color: #ffd700;
        }
      }
    }
  }
}

.stats-overview {
  margin-bottom: 30rpx;
  
  .stats-card {
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .stats-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 20rpx;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20rpx;

      .stats-item {
        text-align: center;
        padding: 20rpx;
        background: #f8f9fd;
        border-radius: 12rpx;

        .amount {
          font-size: 36rpx;
          font-weight: bold;
          color: #6366f1;
          margin-bottom: 8rpx;
          display: block;
        }

        .label {
          font-size: 24rpx;
          color: #6b7280;
        }
      }
    }
  }
}

.rules-section, .records-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
    color: #1f2937;
  }
}

.rules-content {
  .rule-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 20rpx;
    background: #f8f9fd;
    border-radius: 12rpx;

    .rule-icon {
      width: 60rpx;
      height: 60rpx;
      background: #6366f1;
      color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 20rpx;
    }

    .rule-detail {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .order-num {
        font-size: 28rpx;
        color: #1f2937;
      }

      .reward-rate {
        font-size: 28rpx;
        color: #6366f1;
        font-weight: bold;
      }
    }
  }

  .ladder-rules {
    .ladder-item {
      margin-bottom: 20rpx;
      
      .ladder-step {
        padding: 20rpx;
        background: #f8f9fd;
        border-radius: 12rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .step-range {
          font-size: 28rpx;
          color: #1f2937;
        }

        .step-rate {
          font-size: 28rpx;
          color: #6366f1;
          font-weight: bold;
        }
      }

      &.active .ladder-step {
        background: #6366f1;
        color: #fff;

        .step-range, .step-rate {
          color: #fff;
        }
      }
    }
  }

  .cycle-rules {
    .cycle-progress {
      position: relative;
      width: 300rpx;
      height: 300rpx;
      border-radius: 50%;
      background: #f8f9fd;
      margin: 40rpx auto;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 80%;
        height: 80%;
        transform: translate(-50%, -50%);
        border: 2rpx dashed rgba(99, 102, 241, 0.2);
        border-radius: 50%;
      }

      .cycle-indicator {
        position: absolute;
        top: 0;
        left: 50%;
        width: 4rpx;
        height: 50%;
        background: #6366f1;
        transform-origin: 50% 100%;
        transition: transform 0.3s ease;
        
        &::after {
          content: '';
          position: absolute;
          top: 0rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 12rpx;
          height: 12rpx;
          background: #6366f1;
          border-radius: 50%;
        }
      }

      .cycle-step {
        position: absolute;
        top: 50%;
        left: 38%;
        transform-origin: center;
        text-align: center;
        min-width: 80rpx;

        &.active {
          .step-num, .step-rate {
            color: #6366f1;
            font-weight: bold;
            transform: scale(1.1);
          }
        }

        .step-num {
          font-size: 28rpx;
          color: #6366f1;
          font-weight: bold;
          margin-bottom: 2rpx;
          display: block;
          transition: all 0.3s ease;
        }

        .step-rate {
          font-size: 26rpx;
          color: #6b7280;
          display: block;
          transition: all 0.3s ease;
        }
      }
    }

    .cycle-info {
      text-align: center;
      margin-top: 30rpx;

      .cycle-text {
        font-size: 28rpx;
        color: #6b7280;
        display: block;
        margin-bottom: 10rpx;
      }

      .current-cycle {
        font-size: 32rpx;
        color: #6366f1;
        font-weight: bold;
      }
    }
  }

  .current-stage {
    margin-top: 30rpx;
    text-align: center;

    .stage-info {
      display: inline-block;
      margin: 10rpx 20rpx;

      .label {
        font-size: 28rpx;
        color: #6b7280;
      }

      .value {
        font-size: 32rpx;
        font-weight: bold;
        color: #1f2937;
      }
    }
  }
}

.records-list {
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 2rpx solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    .record-main {
      flex: 1;

      .order-info {
        margin-bottom: 10rpx;

        .order-id {
          font-size: 28rpx;
          color: #1f2937;
          margin-right: 20rpx;
        }

        .order-amount {
          font-size: 28rpx;
          color: #6b7280;
        }
      }

      .reward-info {
        .reward-amount {
          font-size: 32rpx;
          color: #6366f1;
          font-weight: bold;
          margin-right: 20rpx;
        }

        .reward-rate {
          font-size: 24rpx;
          color: #6b7280;
        }
      }
    }

    .record-status {
      font-size: 24rpx;
      padding: 6rpx 16rpx;
      border-radius: 30rpx;

      &.success {
        background: #dcfce7;
        color: #16a34a;
      }

      &.pending {
        background: #fef3c7;
        color: #d97706;
      }
    }
  }
}

.reward-details-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
    color: #1f2937;
  }

  .reward-details-list {
    .detail-item {
      margin-bottom: 20rpx;

      .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .order-title {
          font-size: 28rpx;
          color: #1f2937;
        }

        .order-num {
          font-size: 28rpx;
          color: #6b7280;
        }
      }

      .detail-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .calculation {
          flex: 1;
          font-size: 28rpx;
          color: #6b7280;
        }

        .detail-amount {
          display: flex;
          align-items: center;

          .amount-value {
            font-size: 32rpx;
            color: #6366f1;
            font-weight: bold;
            margin-right: 20rpx;
          }

          .claim-btn {
            font-size: 24rpx;
            padding: 6rpx 16rpx;
            border-radius: 30rpx;
            background: #f8f9fd;
            color: #6b7280;
            cursor: pointer;

            &.can-claim {
              background: #10b981;
              color: #fff;
              font-weight: bold;
            }

            &.pending {
              background: #fef3c7;
              color: #d97706;
            }

            &.claimed {
              background: #e5e7eb;
              color: #6b7280;
            }
          }
        }
      }
    }
  }
}
</style> 