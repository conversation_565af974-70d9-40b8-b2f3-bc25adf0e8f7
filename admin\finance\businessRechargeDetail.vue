<template>
<view>
	<block v-if="isload">
		<view class="content">
			<view class="header">
				<view class="amount">￥{{info.amount}}</view>
				<view class="status" :class="'status-'+info.status">{{info.status_text}}</view>
			</view>
			
			<view class="info-group">
				<view class="info-item">
					<text class="label">支付方式</text>
					<text class="value">{{info.pay_type_text || '-'}}</text>
				</view>
				<view class="info-item">
					<text class="label">申请时间</text>
					<text class="value">{{info.create_time || '-'}}</text>
				</view>
				<view class="info-item" v-if="info.update_time">
					<text class="label">更新时间</text>
					<text class="value">{{info.update_time}}</text>
				</view>
				<view class="info-item" v-if="info.audit_time">
					<text class="label">审核时间</text>
					<text class="value">{{info.audit_time}}</text>
				</view>
				<view class="info-item" v-if="info.remark">
					<text class="label">备注说明</text>
					<text class="value">{{info.remark}}</text>
				</view>
				<view class="info-item" v-if="info.audit_remark">
					<text class="label">审核备注</text>
					<text class="value">{{info.audit_remark}}</text>
				</view>
			</view>
			
			<view class="evidence-group" v-if="info.pay_evidence">
				<view class="group-title">支付凭证</view>
				<view class="evidence-image" @tap="previewImage">
					<image :src="info.pay_evidence" mode="widthFix"></image>
				</view>
			</view>
			
			<view class="status-info">
				<view class="status-icon" :class="'icon-'+info.status"></view>
				<view class="status-text">
					<text v-if="info.status === 0">充值申请已提交，等待平台审核</text>
					<text v-if="info.status === 1">充值申请已审核通过</text>
					<text v-if="info.status === 2">充值申请被拒绝，请联系客服</text>
				</view>
			</view>
			
			<view class="btn-group" v-if="info.status === 0">
				<view class="btn btn-cancel" @tap="cancelRecharge">撤销申请</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
			opt: {},
			loading: false,
      isload: false,
			pre_url: app.globalData.pre_url,
			
			info: {} // 充值详情
    };
  },
  
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		if (!this.opt.id) {
			app.alert('参数错误', function() {
				uni.navigateBack();
			});
			return;
		}
		this.getdata();
  },
	
  methods: {
		// 获取充值详情数据
    getdata: function () {
      var that = this;
			that.loading = true;
			app.post('ApiAdminFinance/businessRechargeDetail', {id: that.opt.id}, function (res) {
				that.loading = false;
				if (res.status !== 1) {
					app.alert(res.msg || '获取数据失败', function() {
						uni.navigateBack();
					});
					return;
				}
				
				that.info = res.data;
				that.isload = true;
      });
    },
		
		// 预览支付凭证图片
		previewImage: function() {
			if (this.info.pay_evidence) {
				uni.previewImage({
					urls: [this.info.pay_evidence]
				});
			}
		},
		
		// 撤销充值申请
		cancelRecharge: function() {
			var that = this;
			uni.showModal({
				title: '提示',
				content: '确定要撤销该充值申请吗？',
				success: function(res) {
					if (res.confirm) {
						that.loading = true;
						app.post('ApiAdminFinance/cancelBusinessRecharge', {id: that.opt.id}, function (res) {
							that.loading = false;
							if (res.status === 1) {
								app.alert('撤销成功', function() {
									// 返回上一页并刷新
									uni.navigateBack();
								});
							} else {
								app.alert(res.msg || '操作失败');
							}
						});
					}
				}
			});
		}
  }
};
</script>

<style>
@import "../common.css";

.content {
	width: 94%;
	margin: 20rpx 3%;
	border-radius: 16rpx;
	background-color: #fff;
	padding: 30rpx;
	position: relative;
}

.header {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
	border-bottom: 1px solid #f5f5f5;
	margin-bottom: 30rpx;
}

.amount {
	font-size: 60rpx;
	font-weight: bold;
	color: #FC5648;
	margin-bottom: 20rpx;
}

.status {
	padding: 6rpx 30rpx;
	border-radius: 30rpx;
	font-size: 28rpx;
}

.status-0 {
	background-color: #FFF1E6;
	color: #FF9500;
}

.status-1 {
	background-color: #E6FFF1;
	color: #09BB07;
}

.status-2 {
	background-color: #F1F1F1;
	color: #999;
}

.info-group {
	padding: 0 10rpx;
}

.info-item {
	display: flex;
	padding: 20rpx 0;
	border-bottom: 1px solid #f8f8f8;
}

.info-item:last-child {
	border-bottom: none;
}

.info-item .label {
	width: 180rpx;
	color: #999;
	font-size: 28rpx;
}

.info-item .value {
	flex: 1;
	color: #333;
	font-size: 28rpx;
}

.evidence-group {
	margin-top: 30rpx;
	padding-top: 30rpx;
	border-top: 10rpx solid #f8f8f8;
}

.group-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	padding-bottom: 20rpx;
}

.evidence-image {
	width: 100%;
	text-align: center;
}

.evidence-image image {
	width: 80%;
	max-width: 500rpx;
	border-radius: 10rpx;
	border: 1px solid #eee;
}

.status-info {
	margin-top: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.status-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.icon-0::before {
	content: "";
	width: 60rpx;
	height: 60rpx;
	background-image: url('/static/img/order1.png');
	background-size: cover;
}

.icon-1::before {
	content: "";
	width: 60rpx;
	height: 60rpx;
	background-image: url('/static/img/withdraw-cash.png');
	background-size: cover;
}

.icon-2::before {
	content: "";
	width: 60rpx;
	height: 60rpx;
	background-image: url('/static/img/withdraw-weixin.png');
	background-size: cover;
}

.status-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

.btn-group {
	margin-top: 60rpx;
	display: flex;
	justify-content: center;
}

.btn {
	width: 300rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
}

.btn-cancel {
	background-color: #f8f8f8;
	color: #666;
}
</style> 