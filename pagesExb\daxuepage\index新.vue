<template>
	<view>

		<!-- 头部标题 -->
		<view class="flex" style="padding: 30rpx;padding-right: 0;padding-bottom: 0;">
			<view style="margin-right: 10rpx;">
				<image src="https://picsum.photos/50/50" style="width: 130rpx;height: 130rpx;" alt="" />
			</view>
			<view style="flex: 1;">
				<view class="text-h2 text-bold" style="margin-bottom: 10rpx;">
					职业学院
				</view>
				<view class="flex">
					<view v-for="item in 3" class="tag-item">
						综合类
					</view>
				</view>
			</view>
			<view>
				<view class="bg-blue flex align-center" style="margin-bottom: 10rpx;">
					<view style="margin-right: 10rpx;">
						<image src="@/static/icon/notCollect.png" style="width: 40rpx;height: 40rpx;" mode=""></image>
					</view>
					<view>
						收藏
					</view>

				</view>
				<view class="bg-blue flex align-center">
					<view style="margin-right: 10rpx;">
						<image src="@/static/icon/share2.png" style="width: 40rpx;height: 40rpx;" mode=""></image>
					</view>
					<view>
						分享
					</view>
				</view>
			</view>
		</view>

		<view class="box">
			<!-- 文章 -->
			<view class="mb30">
				<view class="article-item">
					<view class="flex align-center">
						<view style="margin-right: 10rpx;" class="text-bold">
							柳州城市职业学院2024年招生计划
						</view>
						<view>
							<image src="@/static/icon/fire.png" style="width: 40rpx;height: 40rpx;" mode=""></image>
						</view>
					</view>
					<view>
						<image src="@/static/icon/right.png" style="width: 20rpx;height: 20rpx;" mode=""></image>
					</view>
				</view>
				<view class="article-item">
					<view class="flex align-center">
						<view style="margin-right: 10rpx;" class="text-bold">
							柳州城市职业学院2024年招生简章
						</view>
						<view>
							<image src="@/static/icon/fire.png" style="width: 40rpx;height: 40rpx;" mode=""></image>
						</view>
					</view>
					<view>
						<image src="@/static/icon/right.png" style="width: 20rpx;height: 20rpx;" mode=""></image>
					</view>
				</view>
			</view>

			<!-- 学校简介 -->
			<view class="mb30">
				<view class="text-h2 text-bold mb30">
					学校简介
				</view>

				<view class="bg-grey flex align-center">
					<view class="text-overflow">
						按时间快点发货啦会计师大后方不能氨基酸看到恢复了健康啊我打开链接请问饥渴寒热考虑进去文化而刻录机巧克力居委会认可了几千万和认可了就请问科教融合情况居委会人家看篮球文化认可了就去危害健康
					</view>
					<view>
						<image src="@/static/icon/right.png" style="width: 20rpx;height: 20rpx;" mode=""></image>
					</view>
				</view>
			</view>

			<!-- 基本信息 -->
			<view class="mb30">
				<view class="text-h2 text-bold mb30">
					基本信息
				</view>

				<view class="flex align-center" style="margin-bottom: 40rpx;">
					<view class="flex align-center" style="flex: 1;">
						<view class="icon">
							<image src="@/static/icon/time.png" style="width: 50rpx;height: 50rpx;" mode=""></image>
						</view>
						<view>
							<view class="text-h3 text-bold">
								1940 年
							</view>
							<view style="font-size: 20rpx;color: #dddddd;">
								建校时间
							</view>
						</view>
					</view>

					<view class="flex align-center" style="flex: 1;">
						<view class="icon">
							<image src="@/static/icon/area.png" style="width: 50rpx;height: 50rpx;" mode=""></image>
						</view>
						<view>
							<view class="text-h3 text-bold">
								1003 亩
							</view>
							<view style="font-size: 20rpx;color: #dddddd;">
								占地面积
							</view>
						</view>
					</view>
				</view>

				<view class="flex align-center">
					<view class="flex align-center" style="flex: 1;">
						<view class="icon">
							<image src="@/static/icon/building.png" style="width: 50rpx;height: 50rpx;" mode=""></image>
						</view>
						<view>
							<view class="text-h3 text-bold">
								0 个
							</view>
							<view style="font-size: 20rpx;color: #dddddd;">
								院系数量
							</view>
						</view>
					</view>

					<view class="flex align-center" style="flex: 1;">
						<view class="icon">
							<image src="@/static/icon/speciality.png" style="width: 50rpx;height: 50rpx;" mode="">
							</image>
						</view>
						<view>
							<view class="text-h3 text-bold">
								48 个
							</view>
							<view style="font-size: 20rpx;color: #dddddd;">
								专业数量
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 院校环境 -->
			<view class="mb30">
				<view class="text-h2 text-bold mb30">
					院校环境
				</view>

				<scroll-view scroll-x="true" style="white-space: nowrap;width: 100%;">
					<view
						style="border-radius: 30rpx;overflow: hidden;margin-right: 20rpx;width: 300rpx;height: 200rpx;display: inline-block;"
						v-for="item in 10" @click="previewImage('https://picsum.photos/500/300')">
						<image src="https://picsum.photos/500/300" style="width: 100%;height: 100%;" alt="" />
					</view>
				</scroll-view>
			</view>

			<!-- 校区地址 -->
			<view class="mb30">
				<view class="text-h2 text-bold mb30">
					校区地址
				</view>

		

			</view>

		</view>



	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		onLoad: function(opt) {
			
		},
		onPullDownRefresh: function() {

		},
		onReachBottom: function() {

		},
		onPageScroll: function(e) {

		},
		onShareAppMessage: function() {

		},
		onPageScroll: function(e) {

		},
		methods: {

			previewImage(images) {
				uni.previewImage({
					current: 0,
					urls: [images],
					loop: true,
					longPressActions: true
				})
			}
		}
	}
</script>
<style>
	page {
		background-color: #fff;
	}

	.box {
		padding: 30rpx;
	}

	.flex {
		display: flex;
	}

	.text-bold {
		font-weight: bold
	}

	.justify-center {
		justify-content: center;
	}

	.justify-between {
		justify-content: space-between;
	}

	.align-center {
		align-items: center;
	}

	.text-h2 {
		color: #000;
		font-size: 34rpx;
	}

	.mb30 {
		margin-bottom: 30rpx;
	}

	.text-h3 {
		color: #000;
		font-size: 30rpx;
	}

	.tag-item {
		background-color: #ededed;
		color: #b7b7b7;
		font-size: 24rpx;
		border-radius: 30rpx;
		padding: 6rpx 14rpx;
		margin-right: 10rpx;
	}

	.bg-blue {
		border-radius: 30rpx 0 0 30rpx;
		background-color: #19a7ff;
		color: #fff;
		padding: 6rpx 14rpx;
		font-size: 24rpx;
	}

	.icon {
		margin-right: 20rpx;
		width: 100rpx;
		height: 100rpx;
		text-align: center;
		line-height: 130rpx;
		background-color: #ededed;
		border-radius: 10rpx;
	}

	.text-overflow {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: normal;
	}

	.bg-grey {
		border-radius: 30rpx;
		background-color: #ededed;
		color: #515151;
		font-size: 24rpx;
		padding: 20rpx;
	}

	.article-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
		font-size: 24rpx;
	}
</style>