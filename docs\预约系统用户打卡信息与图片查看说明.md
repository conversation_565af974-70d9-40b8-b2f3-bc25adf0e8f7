# 预约系统用户打卡信息与图片查看说明

## 功能概述

本功能增强了预约系统中的服务进度查看功能，使用户能够在订单物流跟踪页面查看服务人员的打卡信息、位置信息和上传的服务照片，提升用户体验和服务透明度。

## API接口

### 1. 查看服务进度与打卡信息

**接口地址**: `ApiYuyue/logistics`

**请求方法**: GET/POST

**参数说明**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|------|
| express_no | string/int | 是 | 服务工单ID |

**返回字段说明**:

```javascript
{
  "worker_order": {
    "id": 123,                            // 工单ID
    "ordernum": "2025010101234567",       // 订单编号
    "status": 2,                          // 订单状态：0未接单，1已接单，2服务中，3已完成，4已取消
    "starttime": 1704067200,              // 接单时间戳
    "daodiantime": 1704074400,            // 到店/出发时间戳
    "sign_time": 1704074500,              // 开始服务时间戳
    "endtime": 0,                         // 完成时间戳
    "arrival_distance": 120,              // 到达距离(米)
    "arrival_photo": "https://example.com/photos/arrival123.jpg", // 到达照片
    "complete_photo": "",                 // 完成照片
    "juli": 3.5,                          // 总距离值
    "juli_unit": "km",                    // 总距离单位
    "juli2": 120,                         // 剩余距离值
    "juli2_unit": "m",                    // 剩余距离单位
    "leftminute": 15                      // 预计到达时间(分钟)
  },
  "worker": {
    "id": 456,                            // 服务人员ID
    "realname": "张师傅",                  // 服务人员姓名
    "tel": "13812345678",                 // 服务人员电话
    "latitude": 30.123456,                // 服务人员当前纬度
    "longitude": 120.123456               // 服务人员当前经度
  },
  "service_progress": [                   // 服务进度数组
    {
      "title": "订单已创建",                // 进度标题
      "status": "completed",              // 状态：completed(已完成) 或 waiting(等待中)
      "time": "2025-01-01 10:00:00",      // 时间
      "desc": "您的订单已创建，技师已接单",   // 描述文字
      "icon": "order-created"             // 图标名称
    },
    {
      "title": "技师已出发",
      "status": "completed",
      "time": "2025-01-01 11:00:00",
      "desc": "技师已出发前往服务地点",
      "icon": "technician-departed"
    },
    {
      "title": "技师已到达",
      "status": "completed",
      "time": "2025-01-01 13:00:00",
      "desc": "技师已到达服务地点，距离您约120米",
      "icon": "technician-arrived",
      "arrival_distance": 120,
      "arrival_distance_text": "120 米",
      "arrival_photo": "https://example.com/photos/arrival123.jpg"
    },
    {
      "title": "服务未完成",
      "status": "waiting",
      "time": "",
      "desc": "技师正在服务中",
      "icon": "service-in-progress"
    }
  ],
  "arrival_info": {                       // 到达信息
    "title": "技师已到达",
    "status": "completed",
    "time": "2025-01-01 13:00:00",
    "desc": "技师已到达服务地点，距离您约120米",
    "icon": "technician-arrived",
    "arrival_distance": 120,
    "arrival_distance_text": "120 米",
    "arrival_photo": "https://example.com/photos/arrival123.jpg"
  },
  "completion_info": {                    // 完成信息
    "title": "服务已完成",
    "status": "completed",
    "time": "2025-01-01 15:00:00",
    "desc": "服务已完成",
    "icon": "service-completed",
    "complete_photo": "https://example.com/photos/complete123.jpg"
  },
  "need_location": 1,                    // 是否需要显示位置信息：1显示，0不显示
  "need_arrival_photo": 1,               // 是否需要显示到达照片：1显示，0不显示
  "need_complete_photo": 1,              // 是否需要显示完成照片：1显示，0不显示
  "yuyue_sign": false                    // 是否是预约订单
}
```

## 界面组件结构

### 1. 服务进度时间轴

```html
<view class="service-timeline" v-if="worker_order.status!=0 && service_progress && service_progress.length>0">
  <view class="timeline-title">服务进度</view>
  <view class="timeline-container">
    <view v-for="(item, index) in service_progress" :key="index" class="timeline-item" :class="item.status === 'completed' ? 'completed' : 'waiting'">
      <view class="timeline-icon">
        <image :src="'/static/peisong/' + item.icon + '.png'" class="icon-img"/>
        <view class="timeline-line" v-if="index < service_progress.length - 1"></view>
      </view>
      <view class="timeline-content">
        <view class="timeline-header">
          <text class="timeline-title-text">{{item.title}}</text>
          <text class="timeline-time" v-if="item.time">{{item.time}}</text>
        </view>
        <view class="timeline-desc">{{item.desc}}</view>
        
        <!-- 到达照片 -->
        <view class="timeline-photo" v-if="item.arrival_photo && need_arrival_photo == 1">
          <image :src="item.arrival_photo" mode="aspectFill" @tap="previewImage" :data-src="item.arrival_photo" class="photo-img"/>
          <text class="photo-desc">到达现场照片</text>
        </view>
        
        <!-- 完成照片 -->
        <view class="timeline-photo" v-if="item.complete_photo && need_complete_photo == 1">
          <image :src="item.complete_photo" mode="aspectFill" @tap="previewImage" :data-src="item.complete_photo" class="photo-img"/>
          <text class="photo-desc">服务完成照片</text>
        </view>
      </view>
    </view>
  </view>
</view>
```

### 2. 技师位置信息

```html
<view class="technician-location" v-if="need_location == 1 && arrival_info && arrival_info.arrival_distance">
  <view class="location-title">
    <text class="icon-location">📍</text>
    <text>技师到达位置</text>
  </view>
  
  <view class="location-content">
    <view class="distance-info">
      <text class="label">距离您:</text>
      <text class="value">{{arrival_info.arrival_distance_text}}</text>
    </view>
    
    <view class="arrival-time">
      <text class="label">到达时间:</text>
      <text class="value">{{arrival_info.time}}</text>
    </view>
  </view>
  
  <view class="location-photo" v-if="arrival_info.arrival_photo && need_arrival_photo == 1">
    <image :src="arrival_info.arrival_photo" mode="aspectFill" @tap="previewImage" :data-src="arrival_info.arrival_photo" class="photo-img"/>
    <text class="photo-desc">到达现场照片</text>
  </view>
</view>
```

## CSS样式

### 1. 服务进度时间轴样式

```css
/* 服务进度时间轴样式 */
.service-timeline {
  margin: 20rpx 3%;
  width: 94%;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.timeline-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
}

.timeline-container {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 40rpx;
  position: relative;
}

.timeline-icon {
  width: 80rpx;
  position: relative;
}

.timeline-icon .icon-img {
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border-radius: 50%;
  z-index: 2;
  position: relative;
}

.timeline-line {
  position: absolute;
  top: 40rpx;
  left: 20rpx;
  width: 2rpx;
  height: calc(100% + 40rpx);
  background: #e0e0e0;
  z-index: 1;
}

.timeline-content {
  flex: 1;
  padding-left: 20rpx;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.timeline-title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.timeline-photo {
  margin-top: 20rpx;
}

.timeline-photo .photo-img {
  width: 100%;
  height: 360rpx;
  border-radius: 8rpx;
}

.photo-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.completed .timeline-icon .icon-img {
  border: 2rpx solid #07c160;
}

.waiting .timeline-icon .icon-img {
  border: 2rpx solid #ccc;
}
```

### 2. 技师位置信息样式

```css
/* 技师位置信息样式 */
.technician-location {
  margin: 20rpx 3%;
  width: 94%;
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.location-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.icon-location {
  margin-right: 10rpx;
  color: #07c160;
}

.location-content {
  margin-top: 20rpx;
}

.distance-info, .arrival-time {
  display: flex;
  margin-bottom: 16rpx;
}

.label {
  width: 160rpx;
  color: #666;
  font-size: 28rpx;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.location-photo {
  margin-top: 20rpx;
}

.location-photo .photo-img {
  width: 100%;
  height: 360rpx;
  border-radius: 8rpx;
}
```

## JavaScript方法

### 1. 图片预览功能

```javascript
// 图片预览功能
previewImage: function(e) {
  var src = e.currentTarget.dataset.src;
  if (!src) return;
  
  var imgList = [];
  
  // 收集所有可预览的照片
  if (this.arrival_info && this.arrival_info.arrival_photo) {
    imgList.push(this.arrival_info.arrival_photo);
  }
  
  if (this.completion_info && this.completion_info.complete_photo) {
    imgList.push(this.completion_info.complete_photo);
  }
  
  // 从服务进度中收集照片
  if (this.service_progress && this.service_progress.length > 0) {
    this.service_progress.forEach(function(item) {
      if (item.arrival_photo) {
        imgList.push(item.arrival_photo);
      }
      if (item.complete_photo) {
        imgList.push(item.complete_photo);
      }
    });
  }
  
  // 如果没有照片，使用当前照片
  if (imgList.length === 0) {
    imgList.push(src);
  }
  
  uni.previewImage({
    current: src,
    urls: imgList
  });
}
```

### 2. 日期格式化函数

```javascript
// 日期格式化
dateFormat: function(timestamp) {
  if (!timestamp) return '';
  var date = new Date(timestamp * 1000);
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  var hour = date.getHours();
  var minute = date.getMinutes();
  
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  hour = hour < 10 ? '0' + hour : hour;
  minute = minute < 10 ? '0' + minute : minute;
  
  return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
}
```

## 注意事项

1. 图标文件需要放置在 `/static/peisong/` 目录下，并以 `icon名称.png` 的格式命名
2. 根据后台设置的 `need_location`、`need_arrival_photo` 和 `need_complete_photo` 值决定是否显示位置信息和照片
3. 技师上传的照片可能较大，建议在服务端进行适当的压缩和优化
4. 图片预览功能依赖uni-app的 `uni.previewImage` API
5. 时间轴样式可根据实际项目UI风格进行调整

## 后续优化方向

1. 增加地图组件，直观显示技师到达位置与客户位置的距离
2. 增加服务过程中的实时聊天功能
3. 支持技师上传多张照片记录服务过程
4. 增加服务评价与反馈功能
5. 增加服务完成后的电子签名功能 