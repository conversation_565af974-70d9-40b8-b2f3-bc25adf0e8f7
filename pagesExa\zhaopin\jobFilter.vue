<template>
  <view class="job-filter">
    <!-- 顶部进度条 -->
    <view class="progress-bar">
      <view 
        class="progress" 
        :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
      ></view>
    </view>

    <!-- 步骤标题 -->
    <view class="step-title">
      <text class="main-title">{{ steps[currentStep - 1].title }}</text>
      <text class="sub-title">{{ steps[currentStep - 1].subtitle }}</text>
    </view>

    <!-- 步骤内容 -->
    <swiper class="step-content" :current="currentStep - 1" @change="handleStepChange">
      <!-- 第一步：选择职位类别 -->
      <swiper-item>
        <scroll-view scroll-y class="scroll-container">
          <view class="category-list">
            <view 
              v-for="(category, index) in jobCategories" 
              :key="index"
              class="category-item"
            >
              <view 
                class="category-header"
                @tap="toggleExpand(category.id)"
              >
                <view class="category-title">
                  <text class="title-text">{{ category.name || '未命名' }}</text>
                  <view class="header-right">
                    <view 
                      class="select-all"
                      :class="{ active: selectedCategories.includes(category.id) }"
                      @tap.stop="toggleCategory(category.id)"
                    >
                      <text>选择大类</text>
                      <text class="check-icon" v-if="selectedCategories.includes(category.id)">✓</text>
                    </view>
                    <text class="expand-icon" :class="{ expanded: expandedCategories.includes(category.id) }">
                      {{ expandedCategories.includes(category.id) ? '∨' : '＞' }}
                    </text>
                  </view>
                </view>
              </view>
              
              <view 
                class="sub-categories"
                :class="{ expanded: expandedCategories.includes(category.id) }"
                v-if="category.children && category.children.length > 0"
              >
                <view class="sub-wrapper">
                  <view 
                    v-for="(subCategory, subIndex) in category.children" 
                    :key="subIndex"
                    class="sub-category-item"
                    :class="{ active: selectedCategories.includes(subCategory.id) }"
                    @tap.stop="toggleCategory(subCategory.id)"
                  >
                    <text>{{ subCategory.name || '未命名' }}</text>
                    <text class="check-icon" v-if="selectedCategories.includes(subCategory.id)">✓</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </swiper-item>

      <!-- 第二步：工作形式和经验要求 -->
      <swiper-item>
        <scroll-view scroll-y class="scroll-container">
          <view class="filter-section">
            <text class="section-title">工作形式</text>
            <view class="option-list">
              <view 
                v-for="(mode, index) in workModeOptions" 
                :key="index"
                class="option-item"
                :class="{ active: selectedWorkMode === mode.value }"
                @tap="selectWorkMode(mode.value)"
              >
                {{ mode.label }}
              </view>
            </view>
          </view>

          <view class="filter-section">
            <text class="section-title">结算方式</text>
            <view class="option-list">
              <view 
                v-for="(payment, index) in paymentOptions" 
                :key="index"
                class="option-item"
                :class="{ active: selectedPayment === payment.value }"
                @tap="selectPayment(payment.value)"
              >
                {{ payment.label }}
              </view>
            </view>
          </view>

          <view class="filter-section">
            <text class="section-title">工作经验</text>
            <view class="option-list">
              <view 
                v-for="(exp, index) in experienceOptions" 
                :key="index"
                class="option-item"
                :class="{ active: selectedExperience === exp.value }"
                @tap="selectExperience(exp.value)"
              >
                {{ exp.label }}
              </view>
            </view>
          </view>

          <view class="filter-section">
            <text class="section-title">学历要求</text>
            <view class="option-list">
              <view 
                v-for="(edu, index) in educationOptions" 
                :key="index"
                class="option-item"
                :class="{ active: selectedEducation === edu.value }"
                @tap="selectEducation(edu.value)"
              >
                {{ edu.label }}
              </view>
            </view>
          </view>
        </scroll-view>
      </swiper-item>

      <!-- 第三步：薪资、城市和福利 -->
      <swiper-item>
        <scroll-view scroll-y class="scroll-container">
          <view class="filter-section">
            <text class="section-title">期望城市</text>
            <view class="city-list">
              <view 
                v-for="(city, index) in selectedCities" 
                :key="index"
                class="city-tag"
              >
                {{ city }}
                <text class="delete-icon" @tap="removeCity(index)">×</text>
              </view>
              <view 
                class="city-tag add-city" 
                @tap="showCityPicker"
                v-if="selectedCities.length < 3"
              >
                <text class="add-icon">+</text>
                添加城市
              </view>
            </view>
          </view>

          <view class="filter-section">
            <text class="section-title">薪资范围</text>
            <view class="salary-list">
              <view 
                v-for="(salary, index) in salaryOptions" 
                :key="index"
                class="salary-item"
                :class="{ active: selectedSalary === salary.value }"
                @tap="selectSalary(salary.value)"
              >
                {{ salary.label }}
              </view>
            </view>
          </view>

          <view class="filter-section">
            <text class="section-title">福利待遇</text>
            <text class="section-subtitle">可多选，最多选择5项</text>
            <view class="benefits-list">
              <view 
                v-for="(benefit, index) in benefitsOptions" 
                :key="index"
                class="benefit-item"
                :class="{ active: selectedBenefits.includes(benefit.value) }"
                @tap="toggleBenefit(benefit.value)"
              >
                {{ benefit.label }}
                <text class="check-icon" v-if="selectedBenefits.includes(benefit.value)">✓</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>

    <!-- 底部按钮 -->
    <view class="footer safe-area-bottom">
      <view class="btn-group">
        <button 
          class="prev-btn" 
          v-if="currentStep > 1"
          @tap="prevStep"
        >上一步</button>
        <button 
          class="next-btn" 
          :class="{ 'full-width': currentStep === 1 }"
          @tap="nextStep"
        >
          {{ currentStep === totalSteps ? '开始匹配' : '下一步' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp()
export default {
  data() {
    return {
      currentStep: 1,
      totalSteps: 3,
      steps: [
        {
          title: '选择职位类别',
          subtitle: '可多选，最多选择3个'
        },
        {
          title: '工作要求',
          subtitle: '选择工作形式和经验要求'
        },
        {
          title: '薪资福利',
          subtitle: '选择期望的薪资范围和福利待遇'
        }
      ],
      jobCategories: [],
      selectedCategories: [],
      experienceOptions: [
        { value: 0, label: '应届生' },
        { value: 1, label: '1-3年' },
        { value: 2, label: '3-5年' },
        { value: 3, label: '5-10年' },
        { value: 4, label: '10年以上' }
      ],
      selectedExperience: null,
      educationOptions: [
        { value: 1, label: '大专' },
        { value: 2, label: '本科' },
        { value: 3, label: '硕士' },
        { value: 4, label: '博士' }
      ],
      selectedEducation: null,
      selectedCities: [],
      salaryOptions: [
        { value: 1, label: '3k-5k' },
        { value: 2, label: '5k-10k' },
        { value: 3, label: '10k-15k' },
        { value: 4, label: '15k-20k' },
        { value: 5, label: '20k-30k' },
        { value: 6, label: '30k以上' }
      ],
      selectedSalary: null,
      workModeOptions: [
        { value: 1, label: '全职' },
        { value: 2, label: '兼职' },
        { value: 3, label: '实习' },
        { value: 4, label: '自由职业' },
        { value: 5, label: '临时工' }
      ],
      selectedWorkMode: null,
      paymentOptions: [
        { value: 1, label: '月结' },
        { value: 2, label: '周结' },
        { value: 3, label: '日结' },
        { value: 4, label: '项目结' }
      ],
      selectedPayment: null,
      benefitsOptions: [
        { value: 1, label: '五险一金' },
        { value: 2, label: '年终奖' },
        { value: 3, label: '带薪年假' },
        { value: 4, label: '加班补贴' },
        { value: 5, label: '餐补' },
        { value: 6, label: '交通补贴' },
        { value: 7, label: '通讯补贴' },
        { value: 8, label: '节日福利' },
        { value: 9, label: '团队建设' },
        { value: 10, label: '定期体检' }
      ],
      selectedBenefits: [],
      expandedCategories: []
    }
  },

  onLoad() {
    this.getJobCategories()
  },

  methods: {
    handleStepChange(e) {
      this.currentStep = e.detail.current + 1
    },

    getJobCategories() {
      const that = this
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
      
      app.post('apiZhaopin/getTypeList', {
        pid: -1  // -1表示获取所有类型,0表示获取顶级类型
      }, function(res) {
        uni.hideLoading()
        if (res.status === 1) {
          that.jobCategories = res.data.map(item => ({
            id: item.id,
            name: item.name,
            description: item.children?.length ? `包含${item.children.length}个细分职位` : '暂无子分类',
            icon: `/static/icons/job-${item.id}.png`,
            children: item.children || [],
            sort: item.sort || 0
          })).sort((a, b) => b.sort - a.sort)
          
          console.log('职位类型数据：', that.jobCategories) // 添加调试日志
        } else {
          uni.showToast({
            title: res.msg || '获取职位类型失败',
            icon: 'none'
          })
        }
      })
    },

    toggleCategory(categoryId) {
      const index = this.selectedCategories.indexOf(categoryId)
      if (index === -1) {
        if (this.selectedCategories.length >= 3) {
          uni.showToast({
            title: '最多选择3个职位类别',
            icon: 'none'
          })
          return
        }
        this.selectedCategories.push(categoryId)
      } else {
        this.selectedCategories.splice(index, 1)
      }
    },

    selectExperience(value) {
      this.selectedExperience = value
    },

    selectEducation(value) {
      this.selectedEducation = value
    },

    showCityPicker() {
      if (this.selectedCities.length >= 3) {
        uni.showToast({
          title: '最多选择3个城市',
          icon: 'none'
        })
        return
      }

      uni.showActionSheet({
        itemList: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '南京'],
        success: (res) => {
          const cities = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '南京']
          const selectedCity = cities[res.tapIndex]
          if (!this.selectedCities.includes(selectedCity)) {
            this.selectedCities.push(selectedCity)
          }
        }
      })
    },

    removeCity(index) {
      this.selectedCities.splice(index, 1)
    },

    selectSalary(value) {
      this.selectedSalary = value
    },

    selectWorkMode(value) {
      this.selectedWorkMode = value
    },

    selectPayment(value) {
      this.selectedPayment = value
    },

    toggleBenefit(value) {
      const index = this.selectedBenefits.indexOf(value)
      if (index === -1) {
        if (this.selectedBenefits.length >= 5) {
          uni.showToast({
            title: '最多选择5项福利',
            icon: 'none'
          })
          return
        }
        this.selectedBenefits.push(value)
      } else {
        this.selectedBenefits.splice(index, 1)
      }
    },

    validateStep() {
      switch (this.currentStep) {
        case 1:
          if (this.selectedCategories.length === 0) {
            uni.showToast({
              title: '请至少选择一个职位类别',
              icon: 'none'
            })
            return false
          }
          break
        case 2:
          if (this.selectedWorkMode === null || this.selectedPayment === null || 
              this.selectedExperience === null || this.selectedEducation === null) {
            uni.showToast({
              title: '请完善工作要求信息',
              icon: 'none'
            })
            return false
          }
          break
        case 3:
          if (this.selectedCities.length === 0 || this.selectedSalary === null || this.selectedBenefits.length === 0) {
            uni.showToast({
              title: '请完善薪资福利信息',
              icon: 'none'
            })
            return false
          }
          break
      }
      return true
    },

    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    nextStep() {
      if (!this.validateStep()) return

      if (this.currentStep < this.totalSteps) {
        this.currentStep++
      } else {
        this.submitFilter()
      }
    },

    async submitFilter() {
      // 获取选中项的文字
      const workModeText = this.workModeOptions.find(item => item.value === this.selectedWorkMode)?.label || ''
      const paymentText = this.paymentOptions.find(item => item.value === this.selectedPayment)?.label || ''
      const experienceText = this.experienceOptions.find(item => item.value === this.selectedExperience)?.label || ''
      const educationText = this.educationOptions.find(item => item.value === this.selectedEducation)?.label || ''
      const salaryText = this.salaryOptions.find(item => item.value === this.selectedSalary)?.label || ''
      const benefitsText = this.selectedBenefits.map(value => 
        this.benefitsOptions.find(item => item.value === value)?.label || ''
      ).filter(text => text)

      const filterData = {
        categories: this.selectedCategories,
        workMode: workModeText,
        payment: paymentText,
        experience: experienceText,
        education: educationText,
        cities: this.selectedCities,
        salary: salaryText,
        benefits: benefitsText
      }

      uni.showLoading({
        title: '匹配中...',
        mask: true
      })

      const that = this
      app.post('apiZhaopin/jobMatch', filterData, function(res) {
        uni.hideLoading()
        
        if (res.status === 1) {
          uni.navigateTo({
            url: '/zhaopin/jobMatch',
            success: () => {
              const eventChannel = that.getOpenerEventChannel()
              eventChannel.emit('filterData', {
                ...filterData,
                matchResult: res.data
              })
            }
          })
        } else {
          uni.showToast({
            title: res.msg || '匹配失败，请重试',
            icon: 'none'
          })
        }
      })
    },

    toggleExpand(categoryId) {
      const index = this.expandedCategories.indexOf(categoryId)
      if (index === -1) {
        this.expandedCategories.push(categoryId)
      } else {
        this.expandedCategories.splice(index, 1)
      }
    }
  }
}
</script>

<style lang="scss">
.job-filter {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  
  .progress-bar {
    height: 4rpx;
    background-color: #f0f0f0;
    position: relative;
    
    .progress {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      background: linear-gradient(90deg, #1890ff, #096dd9);
      transition: width 0.3s ease;
    }
  }
  
  .step-title {
    padding: 40rpx 30rpx;
    
    .main-title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 12rpx;
      display: block;
    }
    
    .sub-title {
      font-size: 28rpx;
      color: #999;
    }
  }
  
  .step-content {
    flex: 1;
    height: 0;
    
    .scroll-container {
      height: 100%;
      padding: 0 30rpx;
    }
  }
  
  .category-list {
    padding: 20rpx 0;
    
    .category-item {
      margin-bottom: 24rpx;
      background-color: #ffffff;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
      overflow: hidden;
      
      .category-header {
        padding: 32rpx;
        position: relative;
        background: #fff;
        border-bottom: none;
        
        &:active {
          background-color: #f8f8f8;
        }
        
        .category-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          .title-text {
            font-size: 34rpx;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            position: relative;
            padding-left: 24rpx;
            
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 8rpx;
              height: 36rpx;
              background: linear-gradient(to bottom, #1890ff, #096dd9);
              border-radius: 4rpx;
            }
          }
          
          .header-right {
            display: flex;
            align-items: center;
            gap: 20rpx;
          }
          
          .select-all {
            display: flex;
            align-items: center;
            padding: 12rpx 24rpx;
            border-radius: 28rpx;
            font-size: 26rpx;
            color: #1890ff;
            background: rgba(24, 144, 255, 0.1);
            border: 2rpx solid rgba(24, 144, 255, 0.2);
            transition: all 0.3s ease;
            
            &:active {
              transform: scale(0.96);
            }
            
            &.active {
              background: linear-gradient(135deg, #1890ff, #096dd9);
              color: #fff;
              border-color: transparent;
              box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
            }
            
            .check-icon {
              margin-left: 8rpx;
              font-size: 28rpx;
            }
          }
          
          .expand-icon {
            width: 48rpx;
            height: 48rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24rpx;
            color: #666;
            transition: all 0.3s ease;
            border-radius: 50%;
            background-color: #f5f5f5;
            border: 2rpx solid #eee;
            
            &.expanded {
              transform: rotate(90deg);
              background-color: #1890ff;
              color: #fff;
              border-color: transparent;
            }
          }
        }
      }

      .sub-categories {
        height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
        background-color: #f8f8f8;
        opacity: 0;
        transform: translateY(-10rpx);
        border-top: 2rpx solid #f0f0f0;
        
        &.expanded {
          height: auto;
          padding: 24rpx;
          opacity: 1;
          transform: translateY(0);
        }
        
        .sub-wrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;
        }
        
        .sub-category-item {
          display: inline-flex;
          align-items: center;
          padding: 16rpx 28rpx;
          background-color: #fff;
          border-radius: 32rpx;
          font-size: 26rpx;
          color: #666;
          transition: all 0.3s ease;
          border: 2rpx solid #f0f0f0;
          
          &:active {
            transform: scale(0.96);
          }
          
          &.active {
            background: linear-gradient(135deg, #1890ff, #096dd9);
            color: #fff;
            border-color: transparent;
            box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
            
            .check-icon {
              color: #fff;
            }
          }
          
          .check-icon {
            font-size: 28rpx;
            margin-left: 8rpx;
          }
        }
      }
    }
  }
  
  .filter-section {
    margin-bottom: 40rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        width: 6rpx;
        height: 28rpx;
        background: linear-gradient(to bottom, #1890ff, #096dd9);
        border-radius: 3rpx;
        margin-right: 16rpx;
      }
    }
    
    .option-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;
      
      .option-item {
        padding: 20rpx 40rpx;
        background-color: #f8f8f8;
        border-radius: 32rpx;
        font-size: 28rpx;
        color: #666;
        transition: all 0.3s ease;
        border: 2rpx solid transparent;
        
        &:active {
          transform: scale(0.96);
        }
        
        &.active {
          background: linear-gradient(135deg, #1890ff, #096dd9);
          color: #fff;
          box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
        }
      }
    }
  }
  
  .salary-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    
    .salary-item {
      text-align: center;
      padding: 24rpx 20rpx;
      background-color: #f8f8f8;
      border-radius: 16rpx;
      font-size: 28rpx;
      color: #666;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;
      
      &:active {
        transform: scale(0.96);
      }
      
      &.active {
        background: linear-gradient(135deg, #1890ff, #096dd9);
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
      }
    }
  }
  
  .city-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    
    .city-tag {
      display: flex;
      align-items: center;
      padding: 16rpx 28rpx;
      background-color: #f8f8f8;
      border-radius: 32rpx;
      font-size: 28rpx;
      color: #333;
      transition: all 0.3s ease;
      border: 2rpx solid #f0f0f0;
      
      &.add-city {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.1);
        border-color: rgba(24, 144, 255, 0.2);
        
        &:active {
          transform: scale(0.96);
        }
        
        .add-icon {
          margin-right: 8rpx;
          font-size: 32rpx;
        }
      }
      
      .delete-icon {
        margin-left: 12rpx;
        font-size: 32rpx;
        color: #999;
        
        &:active {
          color: #ff4d4f;
        }
      }
    }
  }

  .benefits-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    
    .benefit-item {
      display: inline-flex;
      align-items: center;
      padding: 16rpx 28rpx;
      background-color: #f8f8f8;
      border-radius: 32rpx;
      font-size: 26rpx;
      color: #666;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;
      
      &:active {
        transform: scale(0.96);
      }
      
      &.active {
        background: linear-gradient(135deg, #1890ff, #096dd9);
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
        
        .check-icon {
          color: #fff;
        }
      }
      
      .check-icon {
        font-size: 28rpx;
        margin-left: 8rpx;
      }
    }
  }

  .section-subtitle {
    font-size: 24rpx;
    color: #999;
    margin-top: -16rpx;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .footer {
    padding: 30rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    &.safe-area-bottom {
      padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
      padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
    }
    
    .btn-group {
      display: flex;
      gap: 20rpx;
      
      button {
        height: 88rpx;
        line-height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
      
      .prev-btn {
        flex: 1;
        background-color: #f8f8f8;
        color: #666;
      }
      
      .next-btn {
        flex: 2;
        background: linear-gradient(135deg, #1890ff, #096dd9);
        color: #fff;
        
        &.full-width {
          flex: 1;
        }
      }
    }
  }
}
</style> 