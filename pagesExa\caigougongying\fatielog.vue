<template>
  <view class="page">
    <block v-if="isload">
      <!-- 类型切换 -->
      <view class="tab-bar">
        <view 
          class="tab-item" 
          :class="{'active': type === 1}"
          @tap="changeType(1)"
        >
          <text class="tab-text">我的采购</text>
        </view>
        <view 
          class="tab-item" 
          :class="{'active': type === 2}"
          @tap="changeType(2)"
        >
          <text class="tab-text">我的供应</text>
        </view>
      </view>

      <!-- 列表内容 -->
      <view class="list-container">
        <block v-for="(item, index) in datalist" :key="index">
          <view class="list-item" @tap="goto" :data-url="'detail?id=' + item.id + '&type=' + type">
            <view class="item-header">
              <text class="title">{{item.title}}</text>
              <text class="category">{{item.categoryname}}</text>
            </view>
            <view class="item-content">
              <text class="content">{{item.content}}</text>
            </view>
            <view class="item-footer">
              <view class="info">
                <text class="time">{{item.addtime}}</text>
                <text class="status" :class="item.status === 1 ? 'active' : ''">
                  {{item.status === 1 ? '已完成' : '进行中'}}
                </text>
              </view>
              <view class="price" v-if="type === 2">
                <text class="price-text">￥{{item.price || '面议'}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 无数据提示 -->
        <view class="empty" v-if="nodata">
          <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无{{type === 1 ? '采购' : '供应'}}记录</text>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view class="load-more" v-if="nomore && !nodata">
        <text class="load-more-text">没有更多了</text>
      </view>
    </block>

    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      type: 1, // 1采购 2供应
      datalist: [],
      pagenum: 1,
      nodata: false,
      nomore: false
    };
  },

  onLoad(options) {
    this.opt = app.getopts(options);
    // 设置初始类型
    if(options.type) {
      this.type = parseInt(options.type);
    }
    this.getdata();
  },

  onPullDownRefresh() {
    this.getdata();
  },

  onReachBottom() {
    if(!this.nodata && !this.nomore) {
      this.pagenum++;
      this.getdata(true);
    }
  },

  methods: {
    // 切换类型
    changeType(type) {
      if(this.type === type) return;
      this.type = type;
      this.pagenum = 1;
      this.datalist = [];
      this.getdata();
    },

    // 获取数据
    getdata(loadmore) {
      var that = this;
      if(!loadmore) {
        that.pagenum = 1;
        that.datalist = [];
      }

      that.nodata = false;
      that.nomore = false;
      that.loading = true;

      app.get('ApiCaigou/my_posts', {
        type: that.type,
        page: that.pagenum,
        pagesize: 10
      }, function(res) {
        that.loading = false;
        that.isload = true;

        if(res.status === 1) {
          const data = res.data.list || [];
          if(loadmore) {
            that.datalist = that.datalist.concat(data);
          } else {
            that.datalist = data;
          }

          if(data.length === 0) {
            if(that.pagenum === 1) {
              that.nodata = true;
            } else {
              that.nomore = true;
            }
          }
        } else {
          app.error(res.msg || '获取列表失败');
        }

        // 停止下拉刷新
        uni.stopPullDownRefresh();
      }, function(err) {
        that.loading = false;
        that.isload = true;
        app.error('获取列表失败');
        uni.stopPullDownRefresh();
      });
    },

    // 页面跳转
    goto(e) {
      const url = e.currentTarget.dataset.url;
      if(url) {
        uni.navigateTo({
          url: url
        });
      }
    }
  }
};
</script>

<style>
.page {
  min-height: 100vh;
  background: #f6f7f9;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 类型切换栏 */
.tab-bar {
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  background: #ffffff;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #2c3e50;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #ff4757;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #ff4757;
  border-radius: 2rpx;
}

/* 列表容器 */
.list-container {
  padding: 24rpx;
}

.list-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.title {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 600;
  flex: 1;
  margin-right: 16rpx;
}

.category {
  font-size: 24rpx;
  color: #ff4757;
  background: rgba(255,71,87,0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.item-content {
  margin-bottom: 16rpx;
}

.content {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time {
  font-size: 24rpx;
  color: #95a5a6;
}

.status {
  font-size: 24rpx;
  color: #ff4757;
  background: rgba(255,71,87,0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status.active {
  color: #27ae60;
  background: rgba(39,174,96,0.1);
}

.price {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: 600;
}

/* 空状态 */
.empty {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #95a5a6;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx;
}

.load-more-text {
  font-size: 24rpx;
  color: #95a5a6;
}
</style>