@echo off
chcp 65001 >nul
echo ====================================
echo    一键生成HTML文件脚本
echo    生成范围：62.html - 130.html
echo ====================================
echo.

REM 检查index.html是否存在
if not exist "index.html" (
    echo [错误] 未找到index.html模板文件！
    echo 请确保在正确的目录下运行此脚本。
    pause
    exit /b 1
)

echo [信息] 开始生成HTML文件...
echo.

REM 循环生成62到130的HTML文件
for /L %%i in (62,1,130) do (
    echo 正在生成 %%i.html...
    
    REM 先复制原文件
    copy "index.html" "%%i.html" >nul
    
    REM 创建临时PowerShell脚本来替换内容
    echo $content = Get-Content '%%i.html' -Raw > temp_replace.ps1
    echo $content = $content -replace '<title>首页</title>', '<title>首页-%%i</title>' >> temp_replace.ps1
    echo $content ^| Out-File '%%i.html' -Encoding UTF8 >> temp_replace.ps1
    
    REM 运行PowerShell脚本
    powershell -ExecutionPolicy Bypass -File temp_replace.ps1
    
    REM 删除临时脚本
    del temp_replace.ps1 >nul 2>&1
)

echo.
echo [成功] 已生成 69 个HTML文件 (62.html - 130.html)
echo.
echo 文件生成统计：
set count=0
for /L %%i in (62,1,130) do (
    if exist "%%i.html" set /a count+=1
)
echo 成功生成文件数量: %count% 个

echo.
echo 示例文件列表：
for /L %%i in (62,1,65) do (
    if exist "%%i.html" echo   ✓ %%i.html
)
echo   ...
for /L %%i in (128,1,130) do (
    if exist "%%i.html" echo   ✓ %%i.html
)

echo.
echo ====================================
echo           生成完成！
echo ====================================
echo 提示：如需删除生成的文件，请运行 cleanup.bat
pause 