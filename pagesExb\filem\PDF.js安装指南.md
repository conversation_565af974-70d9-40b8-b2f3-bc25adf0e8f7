# PDF.js 安装指南

本文档指导您如何获取PDF.js的预编译文件，以便在非H5环境下使用PDF预览功能。

## 方法一：从GitHub下载预编译版本

1. 访问 [PDF.js发布页](https://github.com/mozilla/pdf.js/releases)
2. 下载最新的稳定版本（例如：`pdfjs-3.4.120-dist.zip`）
3. 解压下载的压缩包
4. 将以下文件复制到项目目录：
   - 复制 `build/pdf.min.js` → 到 `pagesExa/filem/pdfjs/build/pdf.min.js`
   - 复制 `build/pdf.worker.min.js` → 到 `pagesExa/filem/pdfjs/build/pdf.worker.min.js`
   - 复制 `cmaps/` 目录下所有文件 → 到 `pagesExa/filem/pdfjs/cmaps/` 目录

## 方法二：使用npm安装并复制文件

如果您有Node.js环境，可以使用npm安装pdf.js并复制文件：

```bash
npm install pdfjs-dist

# 然后将node_modules/pdfjs-dist中的文件复制到项目目录：
# - node_modules/pdfjs-dist/build/pdf.min.js → 到 pagesExa/filem/pdfjs/build/pdf.min.js
# - node_modules/pdfjs-dist/build/pdf.worker.min.js → 到 pagesExa/filem/pdfjs/build/pdf.worker.min.js
# - node_modules/pdfjs-dist/cmaps/ → 到 pagesExa/filem/pdfjs/cmaps/
```

## 方法三：直接从CDN下载文件

您可以直接从CDN下载文件并保存到项目目录：

1. 下载 pdf.min.js:
   - URL: https://cdn.jsdelivr.net/npm/pdfjs-dist@3.4.120/build/pdf.min.js
   - 保存到: `pagesExa/filem/pdfjs/build/pdf.min.js`

2. 下载 pdf.worker.min.js:
   - URL: https://cdn.jsdelivr.net/npm/pdfjs-dist@3.4.120/build/pdf.worker.min.js
   - 保存到: `pagesExa/filem/pdfjs/build/pdf.worker.min.js`

3. 对于cmaps文件，您需要从GitHub发布版下载，或者从npm包中复制。

## 验证安装

安装完成后，可以通过以下步骤验证PDF.js是否正确安装：

1. 确认以下文件存在：
   - `pagesExa/filem/pdfjs/build/pdf.min.js`
   - `pagesExa/filem/pdfjs/build/pdf.worker.min.js`
   - `pagesExa/filem/pdfjs/cmaps/` 目录下应有多个.bcmap文件

2. 打开应用，尝试预览PDF文件，验证功能是否正常。

## 故障排除

如果遇到问题，可以尝试以下解决方案：

1. 确保文件路径正确，文件名区分大小写
2. 检查浏览器控制台是否有JavaScript错误
3. 确认pdf.min.js和pdf.worker.min.js的版本匹配
4. 如果仍有问题，可以尝试使用H5环境，这样会使用CDN加载PDF.js 