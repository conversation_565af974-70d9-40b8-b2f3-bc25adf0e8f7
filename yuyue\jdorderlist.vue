<template>
<view class="container" :style="{backgroundColor: '#f7f8fa'}">
	<block v-if="isload">
		<view>
			<!-- 添加状态切换栏 -->
			<view class="status-tabs" :style="{backgroundColor: '#fff', boxShadow: '0 2rpx 10rpx rgba(0,0,0,0.05)'}">
				<view 
					class="tab-item" 
					:class="{ active: statusType === 'waiting' }" 
					@tap="switchStatusType('waiting')"
					:style="statusType === 'waiting' ? {color: t('color1'), borderBottomColor: t('color1')} : null"
				>待服务</view>
				<view 
					class="tab-item" 
					:class="{ active: statusType === 'serving' }" 
					@tap="switchStatusType('serving')"
					:style="statusType === 'serving' ? {color: t('color1'), borderBottomColor: t('color1')} : null"
				>服务中</view>
				<view 
					class="tab-item" 
					:class="{ active: statusType === 'completed' }" 
					@tap="switchStatusType('completed')"
					:style="statusType === 'completed' ? {color: t('color1'), borderBottomColor: t('color1')} : null"
				>已完成</view>
				<view 
					class="tab-item" 
					:class="{ active: statusType === 'transferred' }" 
					@tap="switchStatusType('transferred')"
					:style="statusType === 'transferred' ? {color: t('color1'), borderBottomColor: t('color1')} : null"
				>被转派</view>
				<view 
					class="tab-item" 
					:class="{ active: statusType === 'all' }" 
					@tap="switchStatusType('all')"
					:style="statusType === 'all' ? {color: t('color1'), borderBottomColor: t('color1')} : null"
				>全部</view>
			</view>
			<view class="search-container" :style="{boxShadow: '0 2rpx 10rpx rgba(0,0,0,0.05)'}">
				<view class="search-box" :style="{backgroundColor: '#f0f2f5'}">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input class="search-text" placeholder="搜索商家" placeholder-style="color:#aaa;font-size:24rpx" @confirm="searchConfirm"/>
				</view>
			</view>
			<block v-for="(item, index) in datalist" :key="item.id">
			<view class="order-box" @tap="goto" :data-url="'jdorderdetail?id=' + item.id" :style="{boxShadow: '0 2rpx 12rpx rgba(0,0,0,0.03)'}">
				<view class="head">
					<view class="status-container">
						<view v-if="item.fwtype==1">
							<view class="f1" v-if="item.status==3"><image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: t('color1')}">已完成</text></view>
							<view class="f1" v-if="item.status==1"><image src="/static/peisong/ps_time.png" class="img"/><text class="t1" :style="{color: t('color1')}">等待客户上门</text> </view>
							<view class="f1" v-if="item.status==2"><image src="/static/peisong/ps_time.png" class="img"/><text class="t1" :style="{color: t('color1')}">服务中</text></view>
							<view class="f1" v-if="item.status==-1"><image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: '#ff4d4f'}">已取消</text></view>
							<view class="f1" v-if="item.status==-2"><image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: '#f57c00'}">已改派</text></view>
						</view>
						<view v-else-if="item.fwtype==2">
							<view class="f1" v-if="item.status==3"><image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: t('color1')}">已完成</text></view>
							<view class="f1" v-else-if="item.status==1"><image src="/static/peisong/ps_time.png" class="img"/>期望上门时间<text class="t1" :style="{color: t('color1')}">{{item.orderinfo.yydate}}</text> </view>
							<block v-else-if="item.status==2">
									<view class="f1" v-if="showaddmoney">
										<block v-if="!item.sign_status">
											<image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: t('color1')}">已到达，等待服务</text>
										</block>
										<block v-else="!item.sign_status">
											<image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: t('color1')}">已到达，正在服务</text>
										</block>
									</view>
									<view class="f1" v-else><image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: t('color1')}">已到达，服务中</text></view>
							</block>
							<view class="f1" v-if="item.status==-1"><image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: '#ff4d4f'}">已取消</text></view>
							<view class="f1" v-if="item.status==-2"><image src="/static/peisong/ps_time.png" class="img"/><text :style="{color: '#f57c00'}">已改派</text></view>
						</view>
						<view class="order-num">订单号: {{item.ordernum}}</view>
					</view>
					<view class="flex1"></view>
					<view class="f2" :style="{color: '#ff7a45'}"><text class="t1">{{item.ticheng}}</text>元</view>
				</view>
				<view class="content">
					<view class="f1">
						<view class="t1"><text class="x1" :style="{color: '#ff7a45'}">{{item.juli}}</text><text class="x2">{{item.juli_unit}}</text></view>
						<view class="t2"><image src="/static/peisong/ps_juli.png" class="img"/></view>
						<view class="t3"><text class="x1" :style="{color: '#ff7a45'}">{{item.juli2}}</text><text class="x2">{{item.juli2_unit}}</text></view>
					</view>
					<view class="f2">
						<view class="t1">{{item.binfo.name}}</view>
						<view class="t2">{{item.binfo.address}}</view>
						<view class="t3">{{item.orderinfo.address}}</view>
						<view class="t2">{{item.orderinfo.area}}</view>
					</view>
					<view class="f3" @tap.stop="daohang" :data-index="index"><image :src="pre_url+'/static/img/peisong/ps_daohang.png'" class="img"/></view>
				</view>
				<view v-if="item.fwtype==1" class="op">
					<view class="t1" v-if="item.status==1" :style="{color: t('color1')}">已接单，待顾客上门</view>
					<view class="t1" v-if="item.status==2" :style="{color: t('color1')}">顾客已到达</view>
					<view class="t1" v-if="item.status==3" :style="{color: t('color1')}">已完成</view>
					<view class="flex1"></view>
					<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="2" v-if="item.status==1" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">顾客已到达</view>
					<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="3" v-if="item.status==2" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">我已完成</view>
				</view>
				<view v-else-if="item.fwtype==2" class="op">
					<view class="t1" v-if="item.status==1" :style="{color: t('color1')}">已接单，等待上门</view>
					<view class="t1" v-if="item.status==2" :style="{color: t('color1')}">已到达，共用时{{item.useminute}}分钟</view>
					<view class="t1" v-if="item.status==3" :style="{color: t('color1')}">已完成</view>
					<view class="flex1"></view>
				
					<block v-if="showaddmoney">
							<view class="btn1 btn2" @tap.stop="addmoney" v-if="item.sign_status==1 && item.status==2 && item.addprice<=0" :data-id="item.id" :style="{backgroundColor: 'rgba('+t('color1rgb')+',0.1)', color: t('color1'), borderColor: t('color1')}">补差价</view>
							<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="2" v-if="item.status==1" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">出发</view>
							<view class="btn1 btn2" @tap.stop="showpaycode" v-if="item.addprice>0" :data-id="item.id" :data-key="index" :style="{backgroundColor: 'rgba('+t('color1rgb')+',0.1)', color: t('color1'), borderColor: t('color1')}">查看补余款</view>
							<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="5" v-if="!item.sign_status && item.status==2" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">开始服务</view>
							<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="3" v-if="item.sign_status==1 && item.status==2" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">服务完成</view>
					</block>
					<block v-else>
						<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="2" v-if="item.status==1" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">我已到达</view>
						<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="3" v-if="item.status==2" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">我已完成</view>
					</block>
				</view>
			</view>
			</block>
		
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
		</view>
		
		<view class="tabbar" v-if="showtabbar">
			<view class="tabbar-bot"></view>
			<view class="tabbar-bar" style="background-color:#ffffff">
				<view @tap="goto" data-url="dating" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/home.png'"></image>
					</view>
					<view class="tabbar-text">大厅</view>
				</view>
				<view @tap="goto" data-url="jdorderlist" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/order'+(st!=3?'2':'')+'.png'"></image>
					</view>
					<view class="tabbar-text" :class="st!=3?'active':''" :style="st!=3? {color: t('color1')} : null">订单</view>
				</view>
				<view @tap="goto" data-url="jdorderlist?st=3" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/orderwc'+(st==3?'2':'')+'.png'"></image>
					</view>
					<view class="tabbar-text" :class="st==3?'active':''" :style="st==3? {color: t('color1')} : null">已完成</view>
				</view>
				<view v-if="showform" @tap="goto" data-url="formlog" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/dangan.png'"></image>
					</view>
					<view class="tabbar-text">档案</view>
				</view>
				<view @tap="goto" data-url="my" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/my.png'"></image>
					</view>
					<view class="tabbar-text">我的</view>
				</view>
			</view>
		</view>
		
		<view class="modal" v-if="showmodal">
			<view class="addmoney" :style="{borderRadius: '16rpx', boxShadow: '0 8rpx 20rpx rgba(0,0,0,0.1)'}">
					<view class="title" :style="{borderBottom: '1rpx solid #f0f0f0'}">{{addprice>0?'修改':'创建'}}补余款</view>
					<view class="item">
						<label class="label">金额：</label><input type="text" @input="bindMoney" name="blance_price" :value="addprice" placeholder="请输入补余款金额"  placeholder-style="font-size:24rpx"/>元
					</view>
					<view class="btn"><button class="btn-cancel" @tap="cancel" :style="{borderRadius: '50rpx'}">取消</button><button class="confirm" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);border-radius:50rpx'"  @tap.stop="addconfirm">确定</button></view>
			</view>
		</view>	
		
		<view class="modal" v-if="showpaycodes">
			<view class="addmoney" :style="{borderRadius: '16rpx', boxShadow: '0 8rpx 20rpx rgba(0,0,0,0.1)'}">
					<view class="title" :style="{borderBottom: '1rpx solid #f0f0f0'}">查看补余款</view>
					<view class="item" >
						<label>金额：</label><text class="price">{{addprice}}</text>元
					</view>
					<view class="item" style="padding-top: 0;">
						<label>支付状态：</label> <text class="t2" v-if="addmoneystatus==1" :style="{color: t('color1')}"> 已支付</text> 
						<text class="t2" v-if="addmoneystatus==0" style="color:#ff4d4f;"> 待支付</text>
					</view>
					<view class="qrcode"><image :src="paycode"></image></view>
					<view class="btn"><button class="btn-cancel" @tap="cancel" :style="{borderRadius: '50rpx'}">关闭</button> 
					
					<button class="btn-update" v-if="addmoneystatus==0" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);border-radius:50rpx'"  @tap="update" :data-key="index" :data-id="id"  >修改差价</button></view>
			</view>
		</view>	
		
		<!-- 添加打卡弹窗 -->
		<view class="punch-modal" v-if="showPunchModal">
			<view class="punch-content" :style="{borderRadius: '16rpx', boxShadow: '0 10rpx 30rpx rgba(0,0,0,0.15)'}">
				<view class="punch-header">
					<text class="punch-title" :style="{color: t('color1')}">{{punchTitle}}</text>
					<view class="close-btn" @tap="closePunchModal">×</view>
				</view>
				
				<!-- 位置信息区域 -->
				<view class="location-section" :style="{backgroundColor: '#f8f8fa'}">
					<view class="section-title"><text class="icon">📍</text> 位置信息</view>
					<view class="location-content" v-if="punchLocationInfo">
						<view class="location-status success" :style="{borderLeftColor: t('color1')}">
							<text class="status-text" :style="{color: t('color1')}">已获取位置信息</text>
							<text class="location-detail">经度: {{punchLocationInfo.longitude}}</text>
							<text class="location-detail">纬度: {{punchLocationInfo.latitude}}</text>
						</view>
					</view>
					<view class="location-content" v-else>
						<view class="location-status" :class="{'loading': isLocating}">
							<text class="status-text">{{isLocating ? '获取位置中...' : '点击获取位置'}}</text>
						</view>
						<view class="get-location-btn" @tap="getPunchLocation" v-if="!isLocating && !punchLocationInfo" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">
							获取位置信息
						</view>
					</view>
				</view>
				
				<!-- 照片上传区域 -->
				<view class="photo-section" :style="{backgroundColor: '#f8f8fa'}">
					<view class="section-title"><text class="icon">📷</text> {{punchPhotoType}} ({{punchPhotos.length}}/9)</view>
					<view class="photo-content">
						<!-- 已选择的照片列表 -->
						<view class="photo-list" v-if="punchPhotos.length > 0">
							<view class="photo-item" v-for="(photo, index) in punchPhotos" :key="index">
								<image :src="photo" class="preview-image" mode="aspectFill" @tap="previewPunchPhoto" :data-url="photo"></image>
								<view class="remove-icon" @tap="removePunchPhoto" :data-index="index">×</view>
							</view>
						</view>
						<!-- 上传按钮（当照片数量小于9时显示） -->
						<view class="photo-placeholder" @tap="selectPunchPhoto" v-if="punchPhotos.length < 9">
							<text class="placeholder-icon">+</text>
							<text class="placeholder-text">点击上传照片</text>
						</view>
					</view>
				</view>
				
				<!-- 操作按钮 -->
				<view class="punch-actions">
					<view class="cancel-btn" @tap="closePunchModal" :style="{borderRadius: '50rpx'}">取消</view>
					<view class="submit-btn" @tap="submitPunchData" :class="{'disabled': !canSubmitPunch}" :style="canSubmitPunch ? 'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%);border-radius:50rpx' : 'border-radius:50rpx'">
						确认提交
					</view>
				</view>
			</view>
		</view>
	</block>
	
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
	<view style="display:none">{{timestamp}}</view>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,

      st: '11',
      datalist: [],
      pagenum: 1,
      nomore: false,
      nodata: false,
			keyword:'',
			interval1:null,
			timestamp:'',
			showform:0,
			showtabbar:false,
			showaddmoney:false,
			showmodal:false,
			addprice:0,
			showpaycodes:false,
			paycode:'',
			addmoneystatus:0,
			
			// 打卡弹窗相关
			showPunchModal: false,
			punchId: null,
			punchSt: null,
			punchTitle: '',
			punchPhotoType: '',
			punchLocationInfo: null,
			punchPhotos: [],
			isLocating: false,
			statusType: 'waiting'
    };
  },
  computed: {
    // 是否可以提交打卡
    canSubmitPunch: function() {
      return this.punchLocationInfo && this.punchPhotos.length > 0;
    }
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st || '11';
		
		// 初始化状态类型
		this.statusType = this.opt.statusType || 'waiting';
		
		if(this.opt.mid){
			this.showtabbar = false;
		}else{
			this.showtabbar = true;
		}
		this.getdata();
  },
	onUnload:function(){
		clearInterval(this.interval1);
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    getdata: function (loadmore) {
			console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_001]开始获取数据，loadmore:", loadmore, "当前状态类型:", this.statusType);
			
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var st = that.st;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
			that.nodata = false;
			that.nomore = false;
			
			var requestParams = {
				st: st,
				pagenum: pagenum,
				keyword: keyword,
				mid: this.opt.mid,
				statusType: this.statusType
			};
			
			console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_002]请求参数:", JSON.stringify(requestParams));
			
      app.post('ApiYuyueWorker/orderlist', requestParams, function (res) {
				console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_003]接口响应状态:", res.status, "数据长度:", res.datalist ? res.datalist.length : 0);
				
				if(res.status==0){
					console.log("2025-01-03 22:55:53,565-ERROR-[jdorderlist][getdata_004]接口返回错误:", res.msg);
					app.alert(res.msg);
					return;
				}
        var data = res.datalist;
        if (pagenum == 1) {
					console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_005]首页数据加载，数据条数:", data.length);
					that.datalist = data;
					that.nowtime = res.nowtime
					that.showform = res.showform;
					that.showaddmoney = res.addmoney
          if (data.length == 0) {
            that.nodata = true;
						console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_006]无数据显示");
          }
					that.loaded();
					that.updatemylocation();
					clearInterval(that.interval1);
					that.interval1 = setInterval(function(){
						that.updatemylocation(true);
						that.nowtime = that.nowtime + 10;
					},10000)
        }else{
					console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_007]分页数据加载，数据条数:", data.length);
          if (data.length == 0) {
            that.nomore = true;
						console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_008]没有更多数据");
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
						console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_009]数据合并完成，总条数:", newdata.length);
          }
        }
      });
    },
		updatemylocation:function(){
			var that = this;
			app.getLocation(function(res){
				var longitude = res.longitude;
				var latitude = res.latitude;
				var datalist = that.datalist;
				console.log(datalist);
				for(var i in datalist){
					var thisdata = datalist[i];
					var rs = that.getdistance(thisdata.longitude2,thisdata.latitude2,longitude,latitude,1);
					thisdata.juli2 = rs.juli;
					thisdata.juli2_unit = rs.unit;
					thisdata.leftminute = parseInt((thisdata.yujitime - that.nowtime) / 60);
					datalist[i] = thisdata;
				}
				that.datalist = datalist;
				that.timestamp = parseInt((new Date().getTime())/1000);
				app.get('ApiYuyueWorker/updatemylocation',{longitude:longitude,latitude:latitude,t:that.timestamp},function(){
					//if(needload) that.getdata();
				});
			});
		},
		getdistance: function (lng1, lat1, lng2, lat2) {
			if(!lat1 || !lng1 || !lat2 || !lng2) return '';
			var rad1 = lat1 * Math.PI / 180.0;
			var rad2 = lat2 * Math.PI / 180.0;
			var a = rad1 - rad2;
			var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
			var r = 6378137;
			var juli = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));
			var unit = 'm';
			if(juli> 1000){
				juli = juli/1000;
				unit = 'km';
			}
			juli = juli.toFixed(1);
			return {juli:juli,unit:unit}
		},
    setst: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var st = e.currentTarget.dataset.st;
      
      // 通过打开弹窗代替直接处理
      that.openPunchModal(e);
    },
    
    // 打开打卡弹窗
    openPunchModal: function(e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var st = e.currentTarget.dataset.st;
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][openPunchModal_001]打开打卡弹窗，id:", id, "状态:", st);
      
      // 设置打卡类型和标题
      var title = '';
      var photoType = '';
      
      if(st == 2){
        if(that.showaddmoney){
          title = '出发打卡';
          photoType = '出发前照片';
        } else {
          title = '到达打卡';
          photoType = '到达现场照片';
        }
      } else if(st == 3 || st == 5) {
        title = '完成打卡';
        photoType = '服务完成照片';
      }
      
      // 重置打卡状态
      that.punchId = id;
      that.punchSt = st;
      that.punchTitle = title;
      that.punchPhotoType = photoType;
      that.punchLocationInfo = null;
      that.punchPhotos = [];
      that.showPunchModal = true;
      
      // 自动开始获取位置
      that.getPunchLocation();
    },
    
    // 关闭打卡弹窗
    closePunchModal: function() {
      this.showPunchModal = false;
    },
    
    // 获取打卡位置
    getPunchLocation: function() {
      var that = this;
      
      that.isLocating = true;
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getPunchLocation_001]开始获取位置");
      
      app.getLocation(function(locRes) {
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getPunchLocation_002]位置获取成功:", JSON.stringify(locRes));
        
        that.isLocating = false;
        that.punchLocationInfo = {
          longitude: locRes.longitude,
          latitude: locRes.latitude
        };
        
        // 显示位置获取成功提示
        uni.showToast({
          title: '位置获取成功',
          icon: 'success',
          duration: 1500
        });
      }, function(err) {
        console.log("2025-01-03 22:55:53,565-ERROR-[jdorderlist][getPunchLocation_003]位置获取失败:", JSON.stringify(err));
        
        that.isLocating = false;
        
        // 提示重试
        uni.showModal({
          title: '位置获取失败',
          content: '请检查是否授予定位权限，并重试',
          confirmText: '重试',
          cancelText: '取消',
          success: function(res) {
            if(res.confirm) {
              that.getPunchLocation();
            }
          }
        });
      });
    },
    
    // 选择打卡照片
    selectPunchPhoto: function() {
      var that = this;
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][selectPunchPhoto_001]开始选择照片");
      
      // 使用系统标准的chooseImage方法
      app.chooseImage(function(imageUrls) {
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][selectPunchPhoto_002]照片上传成功:", imageUrls);
        
        // 将上传成功的图片URL添加到照片列表
        that.punchPhotos = that.punchPhotos.concat(imageUrls);
        
        // 提示上传成功
        uni.showToast({
          title: '照片上传成功',
          icon: 'success',
          duration: 1500
        });
      }, 9 - that.punchPhotos.length);
    },
    
    // 重新选择照片
    reselectPunchPhoto: function(e) {
      var index = e.currentTarget.dataset.index;
      this.punchPhotos.splice(index, 1);
    },
    
    // 移除指定照片
    removePunchPhoto: function(e) {
      var index = e.currentTarget.dataset.index;
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][removePunchPhoto_001]移除照片，索引:", index);
      
      if (index >= 0 && index < this.punchPhotos.length) {
        this.punchPhotos.splice(index, 1);
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][removePunchPhoto_002]照片移除成功，剩余数量:", this.punchPhotos.length);
      }
    },
    
    // 预览选择的照片
    previewPunchPhoto: function(e) {
      var currentUrl = e.currentTarget.dataset.url;
      if(this.punchPhotos && this.punchPhotos.length > 0) {
        uni.previewImage({
          urls: this.punchPhotos,
          current: currentUrl
        });
      }
    },
    
    // 提交打卡数据
    submitPunchData: function() {
      var that = this;
      
      if(!that.canSubmitPunch) {
        uni.showToast({
          title: '请先获取位置并上传照片',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_001]准备提交打卡数据");
      
      // 准备参数
      var params = {
        id: that.punchId,
        st: that.punchSt,
        longitude: that.punchLocationInfo.longitude,
        latitude: that.punchLocationInfo.latitude
      };
      
      // 设置照片参数
      if(that.punchSt == 2) {
        params.arrival_photo = that.punchPhotos.join(',');
      } else {
        params.complete_photo = that.punchPhotos.join(',');
      }
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_002]提交参数:", JSON.stringify(params));
      
      // 显示提交中
      uni.showLoading({
        title: '提交中...',
        mask: true
      });
      
      // 提交数据
      app.post('ApiYuyueWorker/setst', params, function(data) {
        uni.hideLoading();
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_003]提交响应:", JSON.stringify(data));
        
        if(data.status === 1) {
          // 成功，关闭弹窗并刷新数据
          that.closePunchModal();
          
          // 提示成功
          app.success(data.msg);
          
          // 显示详细信息
          var title = '';
          var content = '';
          
          if(that.punchSt == 2) {
            if(that.showaddmoney) {
              title = '出发打卡成功';
              content = '您已成功打卡出发，请尽快前往客户位置';
            } else {
              title = '到达打卡成功';
              content = '您已成功打卡到达，请开始服务';
            }
          } else {
            title = '完成打卡成功';
            content = '服务已完成，感谢您的工作';
          }
          
          // 如果有距离信息，显示距离
          if(data.distance && that.punchSt == 2) {
            content = '您距离客户位置: ' + that.formatDistance(data.distance) + '\n' + content;
          }
          
          // 显示成功弹窗
          uni.showModal({
            title: title,
            content: content,
            showCancel: false,
            success: function() {
              // 刷新数据
              setTimeout(function() {
                that.getdata();
              }, 500);
            }
          });
        } else {
          // 失败提示
          app.alert(data.msg || '提交失败');
        }
      });
    },
    
    // 格式化距离显示
    formatDistance: function (distance) {
      if (!distance && distance !== 0) {
        return '未知';
      }
      
      distance = parseFloat(distance);
      if (isNaN(distance)) {
        return '未知';
      }
      
      if (distance >= 1000) {
        return (distance / 1000).toFixed(2) + '公里';
      } else {
        return parseInt(distance) + '米';
      }
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword
      that.getdata();
    },
		addmoney:function(e){
			var that=this
			this.showmodal=true
			this.id= e.currentTarget.dataset.id
		},
		cancel:function(e){
			var that=this
			this.showmodal=false
			this.showpaycodes=false
		},
		bindMoney:function(e){
			var that=this
			that.addprice = e.detail.value
		},
		addconfirm:function(e){
			var that = this
			if(!that.addprice){
				app.error('请输入金额');
				return;
			} 
			app.post('ApiYuyueWorker/addmoney', {id:that.id,price:that.addprice,addmoneyPayorderid:that.addmoneyPayorderid}, function (data) {
				app.showLoading(false);
				app.success(data.msg);
				if(data.payorderid){
						that.showmodal=false
						that.getdata()
				}
			});
		},
		showpaycode:function(e){
			var that=this
			this.showpaycodes=true
			var index= e.currentTarget.dataset.key
			that.index = index
			this.addprice = 	that.datalist[index].addprice
			this.paycode = 	that.datalist[index].paycode
			this.addmoneystatus = 	that.datalist[index].addmoneystatus
			this.addmoneyPayorderid = 	that.datalist[index].addmoneyPayorderid
			this.id= e.currentTarget.dataset.id
		},
		update:function(e){
			var that=this
			this.showmodal=true
			this.showpaycodes=false
			var index= e.currentTarget.dataset.key
			that.addprice = that.datalist[index].addprice
			
		},
		daohang:function(e){
			var that = this;
			var index = e.currentTarget.dataset.index;
			var datainfo = that.datalist[index];
			uni.showActionSheet({
				itemList: ['导航到商家', '导航到用户'],
				success: function (res) {
					if(res.tapIndex >= 0){
						if (res.tapIndex == 0) {
							var longitude = datainfo.longitude
							var latitude = datainfo.latitude
							var name = datainfo.binfo.name
							var address = datainfo.binfo.address
						}else{
							var longitude = datainfo.longitude2
							var latitude = datainfo.latitude2
							var name = datainfo.orderinfo.address
							var address = datainfo.orderinfo.address
						}
						uni.openLocation({
							latitude:parseFloat(latitude),
							longitude:parseFloat(longitude),
							name:name,
							address:address,
							scale: 13,
							success: function () {
								console.log('success');
							},
							fail:function(res){
								console.log(res);
							}
						})
					}
				}
			});
		},
		switchStatusType: function(type) {
			console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_001]状态切换，原状态:", this.statusType, "新状态:", type);
			
			if (this.statusType === type) {
				console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_002]状态相同，跳过切换");
				return; // 避免重复切换
			}
			
			this.statusType = type;
			
			console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_003]开始切换到状态:", type);
			
			// 更新URL参数，不刷新页面
			var url = 'jdorderlist?st=' + this.st;
			if (type !== 'all') {
				url += '&statusType=' + type;
			}
			if (this.opt.mid) {
				url += '&mid=' + this.opt.mid;
			}
			
			console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_004]构建URL:", url);
			
			// 使用history记录，不刷新页面
			var pages = getCurrentPages();
			var page = pages[pages.length - 1];
			if (page && page.route && page.route.indexOf('jdorderlist') > -1) {
				uni.redirectTo({
					url: url
				});
			}
			
			// 重置页面
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0
			});
			
			// 获取数据
			console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_005]开始获取数据");
			this.getdata();
		}
  }
};
</script>
<style>
@import "./common.css";
.container{ width:100%;display:flex;flex-direction:column}
.search-container {width: 100%;height:100rpx;padding: 20rpx 23rpx 20rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f0f0f0}
.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}
.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}
.search-box .search-text {font-size:24rpx;color:#222;width: 100%;}

.order-box{ width: 94%;margin:20rpx 3%;padding:16rpx 3%; background: #fff;border-radius:16rpx;box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);transition: all 0.3s ease;}
.order-box:active {transform: scale(0.98);}
.order-box .head{ display:flex;justify-content:space-between;width:100%; border-bottom: 1px #f0f0f0 solid; padding: 10rpx 0; overflow: hidden; color: #999;}
.order-box .head .status-container{display:flex;flex-direction:column;align-items:flex-start;}
.order-box .head .f1{display:flex;align-items:center;color:#222222;height:50rpx;line-height:50rpx;}
.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:10rpx}
.order-box .head .f1 .t1{margin-right:10rpx}
.order-box .head .f2{color:#ff7a45;height:100%;display:flex;align-items:center;}
.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}

.order-box .head .order-num {
  font-size: 22rpx;
  color: #999;
  margin-top: 6rpx;
  line-height: 30rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 350rpx;
}

.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f0f0f0;position:relative}
.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1 .x1{font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}
.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}

.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t3 .x1{font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}
.order-box .content .f2{flex:1;padding:0 20rpx}
.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t2{font-size:24rpx;color:#666666;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f3 .img{width:72rpx;height:168rpx}

.order-box .op{display:flex;justify-content:flex-end;align-items:center;width:100%; padding:20rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
.order-box .op .t1{font-weight:bold}
.order-box .op .btn1{height:70rpx;line-height:70rpx;color:#fff;border-radius:50rpx;text-align:center;font-size:28rpx; padding: 0 30rpx; font-size: 26rpx;box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);}
.order-box .op .btn2{ margin-right: 20rpx; font-size: 26rpx; border-radius:50rpx; border: 1rpx solid;}

.modal{ position: fixed; width: 100%; height: 100%; bottom: 0; background: rgba(0,0,0,0.5); z-index: 100; display: flex; justify-content: center;}
.modal .addmoney{ width: 100%; background: #fff; width: 80%; position: absolute; top: 30%; border-radius: 10rpx; }
.modal .title{ height: 88rpx; line-height: 88rpx; text-align: center; font-weight: bold; border-bottom: 1rpx solid #f5f5f5; font-size: 32rpx; }
.modal .item{ display: flex; padding: 30rpx; align-items: center;}
.modal .item input{ width: 200rpx; height: 60rpx; background: #f8f8fa; border-radius: 8rpx; padding: 0 16rpx;}
.modal .item label{ width:200rpx; text-align: right; font-weight: bold;}
.modal .item .t2{ font-weight: bold;}
.modal .btn{ display: flex; margin: 30rpx 20rpx; justify-content: space-between;}
.modal .btn .btn-cancel{ background-color: #F2F2F2; width: 180rpx; border-radius: 10rpx;}
.modal .btn .confirm{ width: 180rpx; border-radius: 10rpx; color: #fff;}
.modal .btn .btn-update{ width: 180rpx; border-radius: 10rpx; color: #fff; }
.modal .addmoney .price{ color: #ff4d4f; font-size: 32rpx; font-weight: bold;}
.modal .qrcode{ display: flex; align-items: center;}
.modal .qrcode image{width: 300rpx; height: 300rpx; margin: auto; box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.05); border-radius: 8rpx;}

/* 打卡弹窗样式 */
.punch-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.punch-content {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 20rpx;
  width: 85%;
  max-width: 650rpx;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);
}

.punch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.punch-title {
  font-size: 36rpx;
  font-weight: bold;
}

.close-btn {
  font-size: 48rpx;
  height: 48rpx;
  line-height: 40rpx;
  width: 48rpx;
  text-align: center;
  color: #999;
  cursor: pointer;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  color: #333;
}

.section-title .icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.location-section, .photo-section {
  margin-bottom: 30rpx;
  padding: 20rpx;
  border-radius: 16rpx;
}

.location-content {
  display: flex;
  flex-direction: column;
}

.location-status {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.location-status.success {
  border-left: 8rpx solid;
}

.location-status.loading {
  color: #ff7a45;
  border-left: 8rpx solid #ff7a45;
}

.status-text {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.location-detail {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.get-location-btn {
  color: #fff;
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.photo-content {
  display: flex;
  flex-wrap: wrap;
}

.photo-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 20rpx;
}

.photo-item {
    position: relative;
    width: 31%;
    padding-bottom: 31%;
    margin-right: 2%;
    margin-bottom: 10rpx;
}

.photo-item:nth-child(3n) {
    margin-right: 0;
}

.preview-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.remove-icon {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    width: 36rpx;
    height: 36rpx;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 32rpx;
    font-size: 28rpx;
    font-weight: bold;
    z-index: 10;
}

.photo-actions {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 10rpx;
}

.reselect-btn {
  background-color: #FF6F30;
  color: #fff;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 111, 48, 0.2);
}

.photo-placeholder {
  width: 31%;
  padding-bottom: 31%;
  position: relative;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
}

.photo-placeholder > * {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.placeholder-icon {
  font-size: 60rpx;
  color: #ccc;
  line-height: 1;
  margin-bottom: 10rpx;
  transform: translate(-50%, -70%);
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  transform: translate(-50%, 30%);
}

.punch-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.cancel-btn, .submit-btn {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  font-size: 30rpx;
  font-weight: bold;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
  margin-right: 20rpx;
}

.submit-btn {
  color: #fff;
}

.submit-btn.disabled {
  background: linear-gradient(-90deg, #ccc 0%, #999 100%);
  color: #fff;
  opacity: 0.8;
}

/* 动画 */
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading .status-text:before {
  content: "";
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #ff7a45;
  border-top-color: transparent;
  border-radius: 50%;
  margin-right: 10rpx;
  animation: rotating 1s linear infinite;
}

/* 改进tabbar样式 */
.tabbar-text.active {
  font-weight: bold;
}

/* 状态切换栏样式 */
.status-tabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 80rpx;
  width: 100%;
  padding: 0 10rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;
  overflow-x: auto;
  white-space: nowrap;
}

.tab-item {
  position: relative;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 26rpx;
  padding: 0 12rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
  box-sizing: border-box;
  flex-shrink: 0;
  min-width: 70rpx;
  text-align: center;
}

.tab-item.active {
  font-weight: bold;
  font-size: 28rpx;
}
</style>