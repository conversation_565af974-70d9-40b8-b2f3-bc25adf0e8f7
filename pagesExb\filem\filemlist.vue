<template>
<view class="container">
	<block v-if="isload">
		<view class="topsearch flex-y-center">
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="搜索文件" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
			</view>
		</view>
		<dd-tab :itemdata="cnamelist" :itemst="cidlist" :st="cid" :isfixed="false" @changetab="changetab" v-if="clist.length>0"></dd-tab>
		
		<view class="filem_list">
			<!-- 文件列表项 -->
			<view class="filem-item" v-for="(item, index) in datalist" :key="item.id" @click="goto" :data-url="'/pagesExa/filem/detail?id='+item.id">
				<view class="filem-icon">
					<image class="image" :src="'/static/img/filetypes/' + (item.filetype_icon || 'file') + '.png'" mode="aspectFit"></image>
				</view>
				<view class="filem-info">
					<view class="p1">{{item.name}}</view>
					<view class="p2" v-if="item.subname">{{item.subname}}</view>
					<view class="p3">
						<text class="filesize">{{item.filesize_text}}</text>
						<text class="filedate">{{item.createtime}}</text>
						<text class="fileview" v-if="item.showviewcount">查看: {{item.viewcount}}</text>
						<text class="filedown" v-if="item.showdownloads">下载: {{item.downloads}}</text>
					</view>
				</view>
			</view>
		</view>
	</block>
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
		opt: {},
		loading: false,
		isload: false,
		menuindex: -1,

		nodata: false,
		nomore: false,
		keyword: '',
		datalist: [],
		pagenum: 1,
		clist: [],
		cnamelist: [],
		cidlist: [],
		cid: 0,
		bid: 0,
		set: '',
		filetype: '',
		showprivate: 0,
    };
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.cid = this.opt.cid || 0;	
		this.bid = this.opt.bid || 0;
		this.filetype = this.opt.filetype || '';
		this.showprivate = this.opt.showprivate || 0;
		
		if (this.opt.keyword) {
			this.keyword = this.opt.keyword;
		}
		
		this.getdata();
		this.getcategory();
  },
  onPullDownRefresh: function () {
		this.getdata();
  },
  onReachBottom: function () {
		if (!this.nomore && !this.nodata) {
			this.pagenum = this.pagenum + 1;
			this.getdata(true);
		}
  },
  methods: {
    getdata: function (loadmore) {
		if(!loadmore){
			this.pagenum = 1;
			this.datalist = [];
			this.nodata = false;
			this.nomore = false;
		}
		
		var that = this;
		var pagenum = that.pagenum;
		var cid = that.cid;
		var bid = that.bid;
		var keyword = that.keyword;
		var filetype = that.filetype;
		var showprivate = that.showprivate;
		
		that.loading = true;
		
		// 调用文件列表接口
		app.post('ApiFilem/getfilemlist', {
			pagenum: pagenum, 
			cid: cid, 
			bid: bid, 
			keyword: keyword,
			filetype: filetype,
			showprivate: showprivate
		}, function (res) {
			that.loading = false;
			
			if (res.status == 1) {
				// 获取设置信息
				if (res.set) {
					that.set = res.set;
				}
				
				var newdata = res.data || [];
				if (newdata.length > 0) {
					that.datalist = that.datalist.concat(newdata);
					if (newdata.length < 10) {
						that.nomore = true;
					}
				} else {
					if (that.pagenum == 1) {
						that.nodata = true;
					} else {
						that.nomore = true;
					}
				}
				
				that.isload = true;
				uni.stopPullDownRefresh();
			} else {
				app.alert(res.msg || '获取数据失败');
				that.nodata = true;
			}
		});
    },
	
	// 获取文件分类
	getcategory: function() {
		var that = this;
		var bid = that.bid;
		
		app.post('ApiFilem/getcategory', {bid: bid}, function (res) {
			if (res.status == 1) {
				var clist = res.data || [];
				that.clist = clist;
				
				// 处理分类列表
				var cnamelist = [];
				var cidlist = [];
				
				// 添加"全部"选项
				cnamelist.push('全部');
				cidlist.push(0);
				
				// 遍历分类
				for (var i=0; i<clist.length; i++) {
					cnamelist.push(clist[i].name);
					cidlist.push(clist[i].id);
					
					// 处理子分类
					if (clist[i].child && clist[i].child.length > 0) {
						for (var j=0; j<clist[i].child.length; j++) {
							cnamelist.push('- ' + clist[i].child[j].name);
							cidlist.push(clist[i].child[j].id);
						}
					}
				}
				
				that.cnamelist = cnamelist;
				that.cidlist = cidlist;
			}
		});
	},
	
	// 切换分类
	changetab: function(e) {
		this.cid = e.currentTarget.dataset.st;
		this.pagenum = 1;
		this.datalist = [];
		this.nodata = false;
		this.nomore = false;
		this.getdata();
	},
	
	// 搜索确认
	searchConfirm: function(e) {
		this.keyword = e.detail.value;
		this.pagenum = 1;
		this.datalist = [];
		this.nodata = false;
		this.nomore = false;
		this.getdata();
	},
	
	// 页面跳转
	goto: function(e) {
		var url = e.currentTarget.dataset.url;
		if (url) {
			uni.navigateTo({
				url: url
			});
		}
	}
  }
}
</script>

<style>
.topsearch{width:100%;height:80rpx;padding:0 24rpx;background:#fff;border-bottom:1px solid #eee;}
.topsearch .f1{width:100%;height:60rpx;background:#f5f5f5;padding:0 20rpx;border-radius:30rpx;}
.topsearch .img{width:28rpx;height:28rpx;margin-right:6rpx;}
.topsearch input{width:100%;height:60rpx;font-size:26rpx;}

.filem_list{padding:10rpx 16rpx;background:#f6f6f7;margin-top:6rpx;}
.filem_list .filem-item {width:100%;display:flex;position:relative;margin-bottom:16rpx;background:#fff;border-radius:12rpx;padding:20rpx;overflow:hidden;}
.filem_list .filem-item .filem-icon {width:80rpx;height:80rpx;flex-shrink:0;display:flex;align-items:center;justify-content:center;}
.filem_list .filem-item .filem-icon .image {width:64rpx;height:64rpx;}
.filem_list .filem-item .filem-info {flex:1;margin-left:20rpx;overflow:hidden;}
.filem_list .filem-item .filem-info .p1 {font-size:28rpx;font-weight:bold;color:#333;line-height:40rpx;margin-bottom:6rpx;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;}
.filem_list .filem-item .filem-info .p2 {font-size:24rpx;color:#666;line-height:32rpx;margin-bottom:6rpx;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;}
.filem_list .filem-item .filem-info .p3 {font-size:22rpx;color:#999;display:flex;flex-wrap:wrap;}
.filem_list .filem-item .filem-info .p3 .filesize {margin-right:20rpx;}
.filem_list .filem-item .filem-info .p3 .filedate {margin-right:20rpx;}
.filem_list .filem-item .filem-info .p3 .fileview {margin-right:20rpx;}
.filem_list .filem-item .filem-info .p3 .filedown {margin-right:20rpx;}
</style> 