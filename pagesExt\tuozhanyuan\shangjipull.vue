<template>
	<view>
		<block v-if="isload">
			<view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==2">
				审核不通过：{{info.reason}}，请修改后再提交</view>
			<view style="color:red;padding:10rpx 30rpx;margin-top:20rpx" v-if="info.id && info.status==0">您已提交申请，请等待审核
			</view>
			<form @submit="subform">
				<view class="apply_box">


					<view class="apply_item" v-if="subordinates.length > 0">
						<view>选择商机来源<text style="color:red"> *</text></view>
						<view>
							<picker @change="sourceChange" :value="sourceIndex" :range="sourceArr">
								<view class="picker">{{sourceArr[sourceIndex]}}</view>
							</picker>
						</view>
					</view>
					<view class="apply_item">
						<view>联系人姓名<text style="color:red"> </text></view>
						<view class="flex-y-center"><input type="text" name="linkman" v-model="linkman"
								placeholder="请填写姓名"></input></view>
					</view>
					<view class="apply_item">
						<view>联系人电话<text style="color:red"> </text></view>
						<view class="flex-y-center"><input type="text" name="linktel" v-model="linktel"
								placeholder="请填写手机号码"></input></view>
					</view>


				</view>

				<view class="apply_box">
					<view class="apply_item">
						<view>商家名称<text style="color:red"> *</text></view>
						<view class="flex-y-center"><input type="text" name="name" :value="info.name"
								placeholder="请输入商家名称"></input></view>
					</view>
					<view class="apply_item">
						<view>商家备注<text style="color:red"> </text></view>
						<view class="flex-y-center"><input type="text" name="desc" :value="info.desc"
								placeholder="请输入商家描述"></input></view>
					</view>
					<view class="apply_item">
						<view>主营类目<text style="color:red"> *</text></view>
						<view>
							<picker @change="cateChange" :value="cindex" :range="cateArr">
								<view class="picker">{{cateArr[cindex]}}</view>
							</picker>
						</view>
					</view>
					<!-- <view class="apply_item">
					<view>餐饮类目<text style="color:red"> *</text></view>
					<view class="flex-y-center"><input type="text" name="cuisine_type" :value="info.cuisine_type" placeholder="类目不确定可以填"></input></view>
				</view> -->
					<view class="apply_item">
						<view>商家单价<text style="color:red"> </text></view>
						<view class="flex-y-center"><input type="text" name="price" :value="info.price"
								placeholder="类目不确定可以填"></input></view>
					</view>
					<view class="apply_item">
						<view>意向度<text style="color:red"> *</text></view>
						<picker @change="yixiangduChange" :value="yixiangduIndex" :range="yixiangduArr">
							<view class="picker">{{ yixiangduArr[yixiangduIndex] }}</view>
						</picker>

					</view>
					<view class="apply_item">
						<view>店铺坐标<text style="color:red"> </text></view>
						<view class="flex-y-center"><input type="text" disabled placeholder="请选择坐标" name="zuobiao"
								:value="latitude ? latitude+','+longitude:''" @tap="locationSelect"></input></view>
					</view>
					<view class="apply_item">
						<view>店铺地址<text style="color:red"> *</text></view>
						<view class="flex-y-center">
							<input type="text" name="address" v-model="address" placeholder="请输入商家详细地址"></input>
						</view>
					</view>

					<input type="text" hidden="true" name="latitude" :value="latitude"></input>
					<input type="text" hidden="true" name="longitude" :value="longitude"></input>
					<!-- 	<view class="apply_item">
					<view>联系电话<text style="color:red"> *</text></view>
					<view class="flex-y-center"><input type="text" name="tel" :value="info.tel" placeholder="请填写客服电话"></input></view>
				</view> -->
					<view class="apply_item" style="line-height:50rpx"><textarea name="sample_feedback"
							placeholder="请输入商家样品反馈" :value="info.sample_feedback"></textarea></view>
				</view>
				<!-- 	<view class="apply_box">
				<view class="apply_item" style="border-bottom:0"><text>商家主图<text style="color:red"> *</text></text></view>
				<view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
					<view v-for="(item, index) in pic" :key="index" class="layui-imgbox">
						<view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pic"><image src="/static/img/ico-del.png"></image></view>
						<view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
					</view>
					<view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pic" v-if="pic.length==0"></view>
				</view>
				<input type="text" hidden="true" name="pic" :value="pic.join(',')" maxlength="-1"></input>
			</view> -->
				<view class="apply_box">
					<view class="apply_item" style="border-bottom:0"><text>商家照片(3-4张)<text style="color:red">
							</text></text></view>
					<view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
						<view v-for="(item, index) in pics" :key="index" class="layui-imgbox">
							<view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pics">
								<image src="/static/img/ico-del.png"></image>
							</view>
							<view class="layui-imgbox-img">
								<image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image>
							</view>
						</view>
						<view class="uploadbtn"
							:style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'"
							@tap="uploadimg" data-field="pics" v-if="pics.length<5"></view>
					</view>
					<input type="text" hidden="true" name="pics" :value="pics.join(',')" maxlength="-1"></input>
				</view>
				<!-- <view class="apply_box">
				<view class="apply_item" style="border-bottom:0"><text>证明材料<text style="color:red"> </text></text></view>
				<view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
					<view v-for="(item, index) in zhengming" :key="index" class="layui-imgbox">
						<view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="zhengming"><image src="/static/img/ico-del.png"></image></view>
						<view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
					</view>
					<view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="zhengming"></view>
				</view>
				<input type="text" hidden="true" name="zhengming" :value="zhengming.join(',')" maxlength="-1"></input>
			</view>
			 -->

				<view style="padding:30rpx 0"><button form-type="submit" class="set-btn"
						:style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">提交申请</button>
				</view>
			</form>


		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>
<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				pre_url: app.globalData.pre_url,
				datalist: [],
				pagenum: 1,
				cateArr: [],
				cindex: 0,
				rateArr: [], // 商户费率名称数组
				rateIndex: 0, // 当前选中的费率索引
				selectedRate: null, // 当前选中的费率对象
				yixiangduArr: [], // 商户费率名称数组
				yixiangduIndex: 0, // 当前选中的费率索引
				yixiangduArr: [], // 意向度数组
				selectedyixiangdu: null, // 当前选中的费率对象
				isagree: false,
				showxieyi: false,
				pic: [],
				pics: [],
				zhengming: [],
				sourceArr: [], // 存储商机来源名称数组
				sourceIndex: 0, // 当前选中的商机来源索引
				subordinates: [], // 存储完整的商机来源数据
				info: {
					linkman: '',
					linktel: '',
					name: '', 
					desc: '',
					cuisine_type: '',
					price: '',
					sample_feedback: '',
					id: null,
					// 如果有其他属性，也请在这里初始化
				},
				linkman : '',
				linktel : '',
				bset: {},
				latitude: '',
				longitude: '',
				address: '',
				withdrawMethods: ['支付宝', '银行卡', '三方支付'],
				withdrawMethodIndex: 0,
				selectedWithdrawMethod: '支付宝'
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiTuozhancrm/apply', {}, function(res) {
					that.loading = false;
					
					if (res.status == 2) {
						app.alert(res.msg, function() {
							app.goto('/pagesExt/tuozhanyuan/tuozhancrm', 'redirect');
						});
						return;
					}
					uni.setNavigationBarTitle({
						title: res.title
					});

					// 处理分类列表
					var clist = res.clist;
					var cateArr = clist.map(item => item.name);
					that.clist = clist;
					that.cateArr = cateArr;

					// 处理意向度数据
					var yixiangdu = res.yixiangdu || [];
					var yixiangduArr = yixiangdu.map(item => item.name);
					that.yixiangdu = yixiangdu;
					that.yixiangduArr = yixiangduArr;

					// 处理商机来源数据，将对象转换为数组
					var subordinates = Object.values(res.subordinates || {});
					var sourceArr = subordinates.map(item => item.realname || item.nickname || '无名');
					that.subordinates = subordinates;
					that.sourceArr = sourceArr;

					// 初始化其他数据
					that.info = res.info || {};

					that.linkman = that.info.realname || '';
					that.linktel = that.info.tel || '';
					that.address = that.info.address || '';
					that.latitude = that.info.latitude || '';
					that.longitude = that.info.longitude || '';
					that.pic = that.info.pic ? [that.info.pic] : [];
					that.pics = that.info.pics ? that.info.pics.split(',') : [];
					that.zhengming = that.info.zhengming ? that.info.zhengming.split(',') : [];
					that.bset = res.bset || {};

					that.loaded();
				});
			},

			cateChange: function(e) {
				this.cindex = e.detail.value;
			},
			yixiangduChange: function(e) {
				var that = this; // 引用组件的上下文
				that.yixiangduIndex = e.detail.value;
				that.selectedyixiangdu = that.yixiangdu[that.yixiangduIndex]; // 使用 that
			},
			sourceChange: function(e) {
				var that = this;
				var sourceIndex = e.detail.value;
				
				
				console.log(e)
				
				that.sourceIndex = sourceIndex;
				var selectedSubordinate = that.subordinates[sourceIndex];

				if (selectedSubordinate) {
					var memberId = selectedSubordinate.id;

					// 保存当前的 memberId 和 that
					(function(currentMemberId, currentSourceIndex) {
						// 调用后端接口获取会员详细信息
						app.post('ApiTuozhancrm/getMemberInfo', {
							member_id: currentMemberId
						}, function(res) {
							if (res.status == 1) {
								var data = res.data;
								// 确保在回调中更新的是当前选择的用户信息
								if (that.sourceIndex === currentSourceIndex) {
									that.linkman = data.realname || ''; // 更新联系人姓名
									that.linktel = data.tel || ''; // 更新联系人电话
									console.log('更新后的 info 对象：', that.info);
								}
							} else {
								app.error(res.msg);
								that.linkman = '';
								that.linktel = '';
							}
						});
					})(memberId, sourceIndex); // 立即执行函数，传入当前的 memberId 和 sourceIndex
				} else {
					// 如果没有选中有效的下级成员，清空联系人信息
					that.linkman = '';
					that.linktel = '';
				}
			},

			withdrawMethodChange: function(e) {
				this.withdrawMethodIndex = e.detail.value;
				this.selectedWithdrawMethod = this.withdrawMethods[this.withdrawMethodIndex];
			},
			locationSelect: function() {
				var that = this;
				uni.chooseLocation({
					success: function(res) {
						that.latitude = res.latitude;
						that.longitude = res.longitude;
						that.address = res.name; // 更新地址为选择的地理位置名称
					},
					fail: function() {
						app.error('无法获取当前位置，请手动填写店铺地址');
					}
				});
			},

			subform: function(e) {
				var that = this;
				var info = e.detail.value;

				// if (info.linkman == '') {
				//   app.error('请填写联系人姓名');
				//   return false;
				// }
				// if (info.linktel == '') {
				//   app.error('请填写联系人电话');
				//   return false;
				// }

				//   if (info.name == '') {
				//     app.error('请填写商家名称');
				//     return false;
				//   }

				//   if (info.address == '') {
				//     app.error('请填写店铺地址');
				//     return false;
				//   }
				// 打印调试信息，检查 sourceIndex 和 subordinates 是否有效


				// if (info.pics == '') {
				//   app.error('请上传商家照片');
				//   return false;
				// }
				// 检查 subordinates 和 sourceIndex 是否有效
				// if (!that.subordinates || !that.subordinates[that.sourceIndex]) {
				//   app.error('请选择有效的商机来源');
				//   return false;
				// }
				// 设置表单信息
				info.address = that.address; // 使用手动输入的地址
				info.latitude = that.latitude || ''; // 如果没有选择坐标，则留空
				info.longitude = that.longitude || ''; // 如果没有选择坐标，则留空
				// 设置商机来源（下级成员ID）
				// 设置商机来源（下级成员ID）
				if (that.subordinates && that.subordinates.length > 0) {
					info.source = that.subordinates[that.sourceIndex].id;
				} else {
					info.source = ''; // 或根据后端需求设置为 null 或者不设置该字段
				}

				info.cid = that.clist[that.cindex].id;
				if (that.info && that.info.id) {
					info.id = that.info.id;
				}
				info.yixiangduid = that.yixiangdu[that.yixiangduIndex].id;
				if (that.info && that.info.id) {
					info.yixiangduid = that.info.yixiangduid;
				}

				// if (!info.rate_id) {
				//  app.error('请选择商户费率');
				// return false;
				// }
				// 继续现有的表单验证和提交逻辑
				// 您的现有验证逻辑...
				app.showLoading('提交中');
				app.post("ApiTuozhancrm/apply", {
					info: info
				}, function(res) {
					app.showLoading(false);
					app.error(res.msg);
					if (res.status == 1) {
						setTimeout(function() {
							app.goto('/pagesExt/tuozhanyuan/tuozhancrm', 'redirect');
						}, 1000);
					}
				});
			},
			isagreeChange: function(e) {
				var val = e.detail.value;
				this.isagree = val.length > 0;
			},
			showxieyiFun: function() {
				this.showxieyi = true;
			},
			hidexieyi: function() {
				this.showxieyi = false;
				this.isagree = true;
			},
			uploadimg: function(e) {
				var that = this;
				var field = e.currentTarget.dataset.field;
				var pics = that[field];
				if (!pics) pics = [];
				app.chooseImage(function(urls) {
					for (var i = 0; i < urls.length; i++) {
						pics.push(urls[i]);
					}
					if (field == 'pic') that.pic = pics;
					if (field == 'pics') that.pics = pics;
					if (field == 'zhengming') that.zhengming = pics;
				}, 1);
			},
			removeimg: function(e) {
				var that = this;
				var index = e.currentTarget.dataset.index;
				var field = e.currentTarget.dataset.field;
				if (field == 'pic') {
					var pics = that.pic;
					pics.splice(index, 1);
					that.pic = pics;
				} else if (field == 'pics') {
					var pics = that.pics;
					pics.splice(index, 1);
					that.pics = pics;
				} else if (field == 'zhengming') {
					var pics = that.zhengming;
					pics.splice(index, 1);
					that.zhengming = pics;
				}
			},
		}
	};
</script>

<style>
	radio {
		transform: scale(0.6);
	}

	checkbox {
		transform: scale(0.6);
	}

	.apply_box {
		padding: 2rpx 24rpx 0 24rpx;
		background: #fff;
		margin: 24rpx;
		border-radius: 10rpx
	}

	.apply_title {
		background: #fff
	}

	.apply_title .qr_goback {
		width: 18rpx;
		height: 32rpx;
		margin-left: 24rpx;
		margin-top: 34rpx;
	}

	.apply_title .qr_title {
		font-size: 36rpx;
		color: #242424;
		font-weight: bold;
		margin: 0 auto;
		line-height: 100rpx;
	}

	.apply_item {
		line-height: 100rpx;
		display: flex;
		justify-content: space-between;
		border-bottom: 1px solid #eee
	}

	.apply_box .apply_item:last-child {
		border: none
	}

	.apply_item input {
		width: 100%;
		border: none;
		color: #111;
		font-size: 28rpx;
		text-align: right
	}

	.apply_item input::placeholder {
		color: #999999
	}

	.apply_item textarea {
		width: 100%;
		min-height: 200rpx;
		padding: 20rpx 0;
		border: none;
	}

	.apply_item .upload_pic {
		margin: 50rpx 0;
		background: #F3F3F3;
		width: 90rpx;
		height: 90rpx;
		text-align: center
	}

	.apply_item .upload_pic image {
		width: 32rpx;
		height: 32rpx;
	}

	.set-btn {
		width: 90%;
		margin: 0 5%;
		height: 96rpx;
		line-height: 96rpx;
		border-radius: 48rpx;
		color: #FFFFFF;
		font-weight: bold;
	}

	.layui-imgbox {
		margin-right: 16rpx;
		margin-bottom: 10rpx;
		font-size: 24rpx;
		position: relative;
	}

	.layui-imgbox-close {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		right: -16rpx;
		top: -16rpx;
		z-index: 90;
		color: #999;
		font-size: 32rpx;
		background: #fff
	}

	.layui-imgbox-close image {
		width: 100%;
		height: 100%
	}

	.layui-imgbox-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		padding: 2px;
		border: #d3d3d3 1px solid;
		background-color: #f6f6f6;
		overflow: hidden
	}

	.layui-imgbox-img>image {
		max-width: 100%;
	}

	.layui-imgbox-repeat {
		position: absolute;
		display: block;
		width: 32rpx;
		height: 32rpx;
		line-height: 28rpx;
		right: 2px;
		bottom: 2px;
		color: #999;
		font-size: 30rpx;
		background: #fff
	}

	.uploadbtn {
		position: relative;
		height: 200rpx;
		width: 200rpx
	}
</style>