<template>
	<view class="tab-content">
		<template v-if="isTab">
			<template v-for="(tab, index) in tabs">
				<view class="tab" :class="{ active: isActiveTab(tab.statusCondition) }" :key="index">
					<view class="iconBox">
						<image class="icon" :src="require(`@/static/highVoltage/${tab.icon}.png`)"></image>
					</view>
					<view>{{ tab.label }}</view>
				</view>
				<view v-if="index < tabs.length - 1" class="line" :key="index + 'line'"></view>
			</template>
		</template>
		<template v-else>
			<text class="tip-text">高压办电说明介绍、高压办电说明介绍、高压办电说明介绍、高压办电说明介绍、高压办电说明介绍、高压办电说明介绍、高压办电说明介绍、高压办电说明</text>
		</template>
	</view>
</template>

<script>
export default {
	name: 'VoltageTab',
	props: {
		status: {
			type: Number,
			default: null
		},
		isTab: {
			type: Boolean,
			default: true
		}
	},
	computed: {
		tabs() {
			return [
				{ label: '申请办电', icon: 'tab1', statusCondition: true },
				{ label: '供电方案', icon: 'tab2', statusCondition: this.status >= 2 && this.status !== 4 },
				{ label: '验收送电', icon: 'tab3', statusCondition: this.status >= 5 && this.status !== 4 }
			];
		}
	},
	methods: {
		isActiveTab(condition) {
			return condition;
		}
	}
};
</script>

<style lang="scss">
.tab-content {
	height: 212rpx;
	border-radius: 20rpx;
	background-color: #ffffff;
	box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(174, 174, 174, 0.35);
	margin: 14rpx 24rpx 0;
	display: flex;
	align-items: center;
	justify-content: center;
	.tip-text {
		line-height: 40rpx;
		color: #3d8b6c;
		font-size: 28rpx;
		text-align: left;
		font-family: PingFangSC-bold;
		margin: 25rpx;
	}
	.iconBox {
		margin: 8rpx auto;
		width: 80rpx;
		height: 80rpx;
		background-color: #aeaeb2;
		border-radius: 50%;
		padding: 20rpx;
		.icon {
			width: 100%;
			height: 100%;
		}
	}
	.line {
		width: 94rpx;
		height: 2rpx;
		border: 2rpx #cccccc dashed;
		margin: 0 20rpx;
		position: relative;
		top: -16rpx;
	}
	.tab {
		font-size: 28rpx;
		color: #aeaeb2;
		&.active {
			color: #3d8b6c;
			.iconBox {
				background-color: #3d8b6c;
			}
		}
	}
}
</style>
