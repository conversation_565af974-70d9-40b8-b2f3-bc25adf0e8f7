<template>
<view class="container">
	<block v-if="isload">
		<!-- 搜索栏 -->
		<view class="topsearch flex-y-center">
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="输入服务名称搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="product-list">
			<block v-for="(item, index) in datalist" :key="index">
				<view class="product-item flex" @tap="goto" :data-url="'/yuyue/cycle/productDetail?id=' + item.id">
					<image class="product-pic" :src="item.pic"></image>
					<view class="product-info flex1">
						<view class="p1">{{item.name}}</view>
						<view class="p2">
							<text class="ps_title flex-y-center" :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}">共{{item.total_period}}期</text>
							<text class="ps_title flex-y-center" :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}" v-if="item.min_period > 1"> | {{item.min_period}}期起购</text>
						</view>
						<view class="p3 flex">
							<view class="price flex1"><text style="font-size:24rpx">￥</text>{{item.sell_price}}<text style="font-size:24rpx">/期</text></view>
							<view class="sales">已售 {{item.sales}}</view>
						</view>
					</view>
				</view>
			</block>
		</view>
		
		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
				opt:{},
				loading:false,
				isload: false,
				menuindex:-1,
				
				datalist: [],
				pagenum: 1,
				nomore: false,
				nodata: false,
				keyword:'',
				cid:'' // 分类ID，如果需要按分类筛选
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.cid = this.opt.cid || ''; // 接收分类ID
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
	onNavigationBarSearchInputConfirmed:function(e){
		this.searchConfirm({detail:{value:e.text}});
	},
	methods: {
		getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
		  var that = this;
		  var pagenum = that.pagenum;
			that.nodata = false;
			that.nomore = false;
			that.loading = true;
			// 注意修改接口地址为 ApiPeriodicService/productList
		  app.post('ApiPeriodicService/productList', {cid: that.cid, pagenum: pagenum, keyword:that.keyword}, function (res) {
				that.loading = false;
				// 兼容可能的返回格式
				var data = res.data.list ? res.data.list : []; 
				var total = res.data.count ? res.data.count : 0;
				
				if (pagenum == 1) {
					that.datalist = data;
					if (data.length == 0) {
						that.nodata = true;
					}
					that.loaded();
				}else{
					if (data.length == 0) {
						that.nomore = true;
					} else {
						var datalist = that.datalist;
						var newdata = datalist.concat(data);
						that.datalist = newdata;
					}
				}
		  });
		},
		searchConfirm:function(e){
			this.keyword = e.detail.value;
			this.getdata(false);
		}
	}
};
</script>
<style>
.container { background-color: #f8f8f8; }
.topsearch{width:94%;margin:10rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.product-list { width: 94%; margin: 0 3%; padding-top: 10rpx; display: flex; flex-direction: column; }
.product-item { width: 100%; background-color: #fff; border-radius: 10rpx; margin-bottom: 20rpx; padding: 20rpx; }
.product-pic { width: 160rpx; height: 160rpx; border-radius: 8rpx; margin-right: 20rpx; }
.product-info { display: flex; flex-direction: column; }
.product-info .p1 { color: #333; font-size: 28rpx; font-weight: bold; line-height: 1.4; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; margin-bottom: 10rpx; }
.product-info .p2 { display: flex; margin-bottom: 10rpx; }
.product-info .ps_title{height: 35rpx;background: #FFDED9;border-radius: 4rpx;padding: 0 10rpx;font-size: 20rpx;font-family: PingFang SC;color: #FF3143;margin-right: 10rpx;line-height:35rpx}
.product-info .p3 { display: flex; align-items: center; }
.product-info .p3 .price { color: #FF5043; font-size: 32rpx; font-weight: bold; }
.product-info .p3 .sales { color: #999; font-size: 24rpx; }
</style> 