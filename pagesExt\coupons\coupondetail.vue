<template>
<view class="container">
	<block v-if="isload">
		<view class="orderinfos">
			
			<block v-if="coupon.id">
				<view class="item flex-col">
					<view class="t2">
						<view class="guize_txt" style="margin-bottom: 1rem;">
							<parse :content="content" />
						</view>
					</view>	
				</view>
			</block>
		</view>
		
		<block v-if="mid == record.mid">
			<!-- 自用+转赠 -->
			<block v-if="coupon.isgive == 1 ||  coupon.isgive == 0 || (coupon.isgive==2 && record.from_mid) ">
				<block v-if="record.id &&  record.status==2">
					<view v-if="coupon.payment==2" class="btn-add" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"  @tap.stop="gotouse">去使用</view>
					<view v-else class="btn-add" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"   @tap="receiveCoupon"  :data-id="record.id" >去使用</view>
				</block>
				<block v-else>
					<view  class="btn-add"  :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="coupon.id">已使用</view>
				</block>
			</block>
			<block v-if="record.id && record.status==2  && (coupon.isgive == 1 || coupon.isgive == 2)">
				<view class="btn-add" @tap="shareapp" v-if="getplatform() == 'app'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">转赠好友</view>
				<view class="btn-add" @tap="sharemp" v-else-if="getplatform() == 'mp'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">转赠好友</view>
				<view class="btn-add" @tap="sharemp" v-else-if="getplatform() == 'h5'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">转赠好友</view>
				<button class="btn-add" open-type="share" v-else :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">转赠好友</button>
			</block>
		</block>
		<block v-else>
			
			<view v-if="record.status==1" class="btn-add" style="background:#9d9d9d">已使用</view>
			<view v-else-if="record.status==2 && isrec==1" class="btn-add" style="background:#9d9d9d">已抢光</view>
			<view v-else class="btn-add" @tap="getcoupon" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id" :data-send="send" :data-isrec="isrec">立即领取</view>
		</block>
		
		<view class='text-center' @tap="goto" data-url='/pagesExt/coupons/couponlist' style="margin-top: 40rpx; line-height: 60rpx;"><text>返回</text></view>

	</block>
	
	 
	<view  v-if="shareshow" @click="remove(0)" >
		<view class="cpt-mask"><image src="/static/img/sharebg.png" style="width: 100%;"></image>
		        </view>  
	</view> 

	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>

</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
            isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,

			textset:{},
			record:{},
			coupon:{},
			shareTitle:'',
			sharePic:'',
			shareDesc:'',
			shareLink:'',
			mid:0,
			content: '',
			send: 0,
			isrec: 0,
			shareshow: 0,
			//
			dhcode:''
		}
  },
	
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		console.log(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});
		var query = (sharewxdata.path).split('?')[1]+'&seetype=circle';
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  methods: {
	    gotouse:function()
		{
			uni.navigateTo({
				url:'pagesExt/lipin2/prodh?dhcode='+this.dhcode
			})
		}
	    ,
	    remove (mask) {
	              this.shareshow =  mask;
	    },
		getdata: function () {
			var that = this;
			that.loading = true;
			
			app.get('ApiLipin2/coupondetail', {rid: that.opt.rid,id: that.opt.id,send: that.opt.send,isrec: that.opt.isrec}, function (res) {
				that.loading = false;
				that.textset = app.globalData.textset;
				uni.setNavigationBarTitle({
					title: that.t('礼品卡') + '详情'
				});
				if(!res.coupon.id) {
					app.alert(that.t('礼品卡')+'不存在');return;
				}
				
				that.mid = app.globalData.mid;
				that.send = res.send;
				that.isrec = res.isrec;
				that.record = res.record;
				that.coupon = res.coupon;
				that.dhcode = res.record.code;
				
				that.shareTitle = that.coupon.shareTitle;
				that.shareDesc = that.coupon.shareDesc;
				if(that.coupon.sharePic){
					that.sharePic = that.coupon.sharePic;
				}else{
					that.sharePic = app.globalData.initdata.logo;
				}
				that.content = that.coupon.content;
				
				that.shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pagesExt/coupons/coupondetail?scene=id_'+that.coupon.id+'-pid_' + app.globalData.mid+'-rid_' + that.record.id+'-send_1';
				
				
				that.loaded({title:that.shareTitle,pic:that.sharePic,desc:that.shareDesc,link:that.shareLink});
			});
		},

		getcoupon:function(e){
			var that = this;
			var couponinfo = that.coupon;
			if (app.globalData.platform == 'wx' && couponinfo.rewardedvideoad && wx.createRewardedVideoAd) {
				app.showLoading();
				if(!app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad]){
					app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = wx.createRewardedVideoAd({ adUnitId: couponinfo.rewardedvideoad});
				}
				var rewardedVideoAd = app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad];
				rewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});
				rewardedVideoAd.onError((err) => {
					app.showLoading(false);
					app.alert(err.errMsg);
					console.log('onError event emit', err)
					rewardedVideoAd.offLoad()
					rewardedVideoAd.offClose();
				});
				rewardedVideoAd.onClose(res => {
					app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = null;
					if (res && res.isEnded) {
						//app.alert('播放结束 发放奖励');
						that.getcouponconfirm(e);
					} else {
						console.log('播放中途退出，不下发奖励');
					}
					rewardedVideoAd.offLoad()
					rewardedVideoAd.offClose();
				});
			}else{
				that.getcouponconfirm(e);
			}
		},
        getcouponconfirm: function (e) {
			var that = this;
			var datalist = that.datalist;
			var id = e.currentTarget.dataset.id;
			var send = e.currentTarget.dataset.send;
			var key = e.currentTarget.dataset.key;
			
				app.showLoading('领取中');
				app.post('ApiLipin2/getcoupon', {id: id,send:send}, function (data) {
					app.showLoading(false);
					if (data.status == 0) {
						app.error(data.msg);
					} else {
						app.success(data.msg);
						setTimeout(function(){
							app.goto('mycoupon');
						},1000)
					}
				});
			
        },
	
		receiveCoupon:function(e){
			var that = this;
			var couponinfo = that.coupon;
			if (app.globalData.platform == 'wx' && couponinfo.rewardedvideoad && wx.createRewardedVideoAd) {
				app.showLoading();
				if(!app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad]){
					app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = wx.createRewardedVideoAd({ adUnitId: couponinfo.rewardedvideoad});
				}
				var rewardedVideoAd = app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad];
				rewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});
				rewardedVideoAd.onError((err) => {
					app.showLoading(false);
					app.alert(err.errMsg);
					console.log('onError event emit', err)
					rewardedVideoAd.offLoad()
					rewardedVideoAd.offClose();
				});
				rewardedVideoAd.onClose(res => {
					app.globalData.rewardedVideoAd[couponinfo.rewardedvideoad] = null;
					if (res && res.isEnded) {
						//app.alert('播放结束 发放奖励');
						that.receiveCouponConfirm(e);
					} else {
						console.log('播放中途退出，不下发奖励');
					}
					rewardedVideoAd.offLoad()
					rewardedVideoAd.offClose();
				});
			}else{
				that.receiveCouponConfirm(e);
			}
		}
,
		receiveCouponConfirm:function(e){
			var that = this;
			var datalist = that.datalist;
			var rid = that.record.id;
			var dhcode = that.record.code;
			var cardno = that.record.cardno;
			var id = that.coupon.id;
			var send = that.send;
			app.showLoading('兑换中');
			app.post('ApiLipin2/dh1', {id: rid}, function (res) {
				app.showLoading(false);
				that.loading = false;
				if (res.status == 0) {
				  app.error(res.msg);
				  return;
				}
				if (res.status == 1) {
				  app.alert(res.msg, function () {
					app.goto('/pages/my/usercenter');
				  });
				}
				if (res.status == 2) {
				  app.success(res.msg);
				  setTimeout(function () {
					// app.goto('prodh?dhcode='+dhcode+'&cardno='+cardno);
					console.log("32312312");
					let that2 = that;
					app.post('/ApiLipin2/index',{dhcode: that.dhcode},function(res){
						if(res.status==0)
						{
							app.alert(res.msg,function(){
								uni.navigateBack();
							});
						}
						if(res.status==2)
						{
						     uni.navigateTo({
						     	url:'/pagesExt/lipin2/prodh?dhcode='+that2.dhcode
						     })	
						}
						
					})
					
				  }, 1000);
				}
				if (res.status == 3) {
				  app.alert(res.msg, function () {
				    app.goto('/pagesExt/coupon/mycoupon');
				  });
				}
				that.loaded();
			});
		},
		
		sharemp:function(){
			//app.error('点击右上角发送给好友或分享到朋友圈');
			this.shareshow=1;
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			that.sharetypevisible = false;
			uni.showActionSheet({
		    itemList: ['发送给微信好友', '分享到微信朋友圈'],
		    success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.shareTitle;
						sharedata.summary = that.shareDesc;
						sharedata.href = that.shareLink;
						sharedata.imageUrl = that.sharePic;
						
						uni.share(sharedata);
					}
		    }
		  });
		},
  }
};
</script>
<style>
.container{display:flex;flex-direction:column; padding-bottom: 30rpx;}
.couponbg{width:100%;height:500rpx;}
.orderinfo{ width:94%;margin: -400rpx 3% 20rpx 3%;border-radius:8px;padding:14rpx 3%;background: #FFF;color:#333;}
.orderinfo .topitem{display:flex;padding:60rpx 40rpx;align-items:center;border-bottom:2px dashed #E5E5E5;position:relative}
.orderinfo .topitem .f1{font-size:50rpx;font-weight:bold;}
.orderinfo .topitem .f1 .t1{font-size:60rpx;}
.orderinfo .topitem .f1 .t2{font-size:40rpx;}
.orderinfo .topitem .f2{margin-left:40rpx}
.orderinfo .topitem .f2 .t1{font-size:36rpx;color:#2B2B2B;font-weight:bold;height:50rpx;line-height:50rpx}
.orderinfo .topitem .f2 .t2{font-size:24rpx;color:#999999;height:50rpx;line-height:50rpx}
.orderinfo .item{display:flex;flex-direction:column;width:100%;padding:0 40rpx;margin-top:40rpx}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;color:#2B2B2B;font-weight:bold;font-size:30rpx;height:60rpx;line-height:60rpx}
.orderinfo .item .t2{color:#2B2B2B;font-size:24rpx;height:auto;line-height:40rpx;white-space:pre-wrap;}
.orderinfo .item .red{color:red}

.text-center { text-align: center;}
.btn-add{width:90%;margin:30rpx 5%;height:96rpx; line-height:96rpx; text-align:center;color: #fff;font-size:30rpx;font-weight:bold;border-radius:48rpx;}

.cpt-mask {  
        position: fixed;  
        top: 0;  
        left: 0;  
        width: 100%;  
        height: 100%;  
        background-color: rgba(0,0,0,0.8);  
        opacity: 0.8;  
        z-index: 99999; color: #fff;
    }  
	
</style>