<template>
<view>
	<view v-if="params.style==1" :style="'background: linear-gradient(180deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0) 100%);padding-bottom:'+(params.cardUpgradeShow==1?'0rpx':'0')+';'">
		<view class="dp-userinfo" :style="{background:'url('+params.bgimg+') no-repeat',backgroundsize:'100% auto',margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'}">
			<view class="banner">
				<view class='info'>
					<view class="f1">
						<image :src="data.userinfo.headimg" background-size="cover" class="headimg" @tap="weixinlogin"/>
						<view class="flex-y-center">
							<view class="nickname">{{data.userinfo.nickname}}</view>
			
							<text v-if="params.midshow=='1' && (data.userinfo.show_user_id === undefined || data.userinfo.show_user_id == 1)" style="font-size:26rpx;padding-left:10rpx">(ID:{{data.userinfo.id}})</text>
							<view class="user-level" @tap="openLevelup" data-levelid="" v-if="params.levelshow==1">
								<image class="level-img" :src="data.userlevel.icon" v-if="data.userlevel.icon"/>
								<view class="level-name">{{data.userlevel.name}}</view>
							</view>
							<view class="user-level" v-for="(item, index) in data.userlevelList" :key="index" @tap="openLevelup" :data-levelid="item.id" v-if="params.levelshow==1">
								<image class="level-img" :src='item.icon' v-if="item.icon"/>
								<view class="level-name">{{item.name}}</view>
							</view>
							<view class="usermid" style="margin-left:10rpx;font-size:24rpx;color:#999" v-if="data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1">邀请码：<text user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view>
							
							<!-- 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_001] Style1简单上级信息显示 -->
							<view v-if="params.parent_show==1 && data.userinfo.referral_detail && data.userinfo.referral_detail.nickname" class="parent-simple" style="margin-left:10rpx;margin-top:8rpx;">
								<text class="parent-label" style="font-size:22rpx;color:#999;margin-right:8rpx;">上级：</text>
								<text class="parent-name" style="font-size:24rpx;color:#333;font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text>
							</view>
						</view>
						<view v-if="data.zhaopin && data.zhaopin.show_zhaopin" class="flex">
							<text v-if="data.zhaopin.is_qiuzhi_renzheng && !data.zhaopin.is_qiuzhi_renzheng" class="zhaopin-renzheng">认证保障中</text>
							<text v-if="data.zhaopin.is_qiuzhi_qianyue" class="zhaopin-renzheng">签约保障中</text>
							<text v-if="data.zhaopin.is_zhaopin_renzheng" class="zhaopin-renzheng">认证企业</text>
						</view>
					</view>
					<block v-if="platform=='wx'">
						<view class="usercard" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
						<view class="usercard" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="addmembercard" :data-card_id="data.card_id"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
					</block>
					<block v-if="platform=='mp'">
						<view class="usercard" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
						<view class="usercard" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="goto" :data-url="data.card_returl"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
					</block>
				</view>
				<view class="custom_field">
					<view class='item' v-if="params.creditshow==1" data-money-type='credit' data-default-url='/pagesExa/my/creditlog' @tap='gotoMoneyItem'>
						<text class='t2'>{{data.userinfo.credit_score || 0}}</text>
						<text class="t1">{{t('信用分')}}</text>
					</view>
					<view class='item' v-if="params.workshow==1" data-money-type='work' data-default-url='/zhaopin/myApply' @tap='gotoMoneyItem'>
						<text class='t2'>{{data.userinfo.job_count || 0}}</text>
						<text class="t1">{{t('工作经历')}}</text>
					</view>
					<view class='item' v-if="params.inviteshow==1" data-money-type='invite' data-default-url='/activity/commission/myteam' @tap='gotoMoneyItem'>
						<text class='t2'>{{data.userinfo.team_count || 0}}</text>
						<text class="t1">{{t('邀请记录')}}</text>
					</view>
				</view>
				<view class="custom_field" v-if="params.moneyshow==1 || params.scoreshow==1 || params.couponshow==1">
					<view class='item' v-if="params.moneyshow==1" data-money-type='money' data-default-url='/pagesExb/money/recharge' @tap='gotoMoneyItem'>
						<text class='t2'>{{smartFormatAmount(data.userinfo.money)}}</text>
						<text class="t1">{{t('余额')}}</text>
					</view>
					<view class='item' v-if="params.commissionshow==1 && data.userlevel && data.userlevel.can_agent>0" data-money-type='commission' data-default-url='/activity/commission/index' @tap='gotoMoneyItem'>
						<text class='t2'>{{smartFormatAmount(data.userinfo.commission)}}</text>
						<text class="t1">{{t('佣金')}}</text>
					</view>
				
					<view class='item' v-if="params.scoreshow==1" data-money-type='score' data-default-url='/pagesExa/my/scorelog' @tap='gotoMoneyItem'>
						<text class='t2'>{{smartFormatAmount(data.userinfo.score)}}</text>
						<text class="t1">{{t('积分')}}</text>
					</view>
					<!-- <view class='item' v-if="params.scoreshow==1" data-url='/pagesExa/my/scoreloghuang' @tap='goto'>
						<text class='t2'>{{data.userinfo.bustotal}}</text>
						<text class="t1">创业值</text>
					</view> -->
					<!-- <view class='item' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>
						<text class='t2' style="color: #dd0000;">{{data.userinfo.sunshijine?data.userinfo.sunshijine:0}}</text>
						<text class="t1" style="color: #dd0000;">{{t('损失金额')}}</text>
					</view> -->
					<view class='item' v-if="params.couponshow==1" data-money-type='coupon' data-default-url='/pagesExb/coupon/mycoupon' @tap='gotoMoneyItem'>
						<text class='t2'>{{data.userinfo.couponcount}}</text>
						<text class="t1">{{t('优惠券')}}</text>
					</view>
					<view class='item' v-if="params.tuikuangshow==1">
						<text class='t2'>{{data.userinfo.kuang_num}}</text>
						<text class="t1">{{t('可退框数')}}</text>
					</view>
				</view>
				
				<view class="custom_field" >
				<!-- 	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money3' @tap='goto'>
						<text class='t2'>{{data.userinfo.scorehuang?data.userinfo.scorehuang:0}}</text>
						<text class="t1">{{t('红包')}}</text>
					</view>
					<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money3' @tap='goto'>
						<text class='t2'>{{data.userinfo.contribution_num?data.userinfo.contribution_num:0}}</text>
						<text class="t1">{{t('贡献值')}}</text>
					</view>
					<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>
						<text class='t2'>{{data.userinfo.guquan?data.userinfo.guquan:0}}</text>
						<text class="t1">{{t('期权激励')}}</text>
					</view> -->
					<!-- <view class='item' data-url='/pagesExt/othermoney/withdraw?type=money4' @tap='goto'>
						<text class='t2'>{{data.userinfo.money4?data.userinfo.money4:0}}</text>
						<text class="t1">{{t('余额4')}}</text>
					</view>
					<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money5' @tap='goto'>
						<text class='t2'>{{data.userinfo.money5?data.userinfo.money5:0}}</text>
						<text class="t1">{{t('余额5')}}</text>
					</view>
					<view class='item' data-url='/pagesExt/othermoney/frozen_moneylog' @tap='goto'>
						<text class='t2'>{{data.userinfo.frozen_money?data.userinfo.frozen_money:0}}</text>
						<text class="t1">{{t('冻结金额')}}</text>
					</view> -->
					<view class="custom_field" v-if="params.xianjinquanshow==1">
					<view class='item' data-money-type='xianjinquan' data-default-url='/pagesExb/money/moneylog?st=8' @tap='gotoMoneyItem'>
						<text class='t2'>{{smartFormatAmount(data.userinfo.heiscore?data.userinfo.heiscore:0)}}</text>
						<text class="t1">{{t('现金券')}}</text>
					</view>
					</view>
					 <view class="custom_field" v-if="params.huangjifenshow==1">
					<view class='item' data-money-type='huangjifen' data-default-url='/pagesExb/money/moneylog?st=9' @tap='gotoMoneyItem'>
							<text class='t2'>{{smartFormatAmount(data.userinfo.scorehuang?data.userinfo.scorehuang:0)}}</text>
							<text class="t1">{{t('黄积分')}}</text>
						</view>
						</view>
					  <view class="custom_field" v-if="params.gongxianzhishow==1">
					<view class='item' data-money-type='gongxianzhi' data-default-url='/pagesExb/money/moneylog?st=10' @tap='gotoMoneyItem'>
						<text class='t2'>{{smartFormatAmount(data.userinfo.contribution_num?data.userinfo.contribution_num:0)}}</text>
						<text class="t1">{{t('贡献值')}}</text>
					</view>
					</view>
					<view class="custom_field" v-if="params.shouyichishow==1">
					<view class='item' data-money-type='shouyichi' data-default-url='/pagesExb/money/moneylog?st=15' @tap='gotoMoneyItem'>
						<text class='t2'>{{smartFormatAmount(data.userinfo.syc_num)}}</text>
						<text class="t1">{{t('收益池')}}</text>
					</view>
					</view>
					  <view class="custom_field" v-if="params.chuangyezhishow==1">
					<view class='item' v-if="params.scoreshow==1" data-url='/pagesExa/my/scoreloghuang' @tap='goto'>
						<text class='t2'>{{smartFormatAmount(data.userinfo.bustotal)}}</text>
						<text class="t1">{{t('创业值')}}</text>
						</view>
					</view>
					  <view class="custom_field" v-if="params.suishijineshow==1">
					<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>
						<text class='t2' style="color: #dd0000;">{{smartFormatAmount(data.userinfo.sunshijine?data.userinfo.sunshijine:0)}}</text>
						<text class="t1" style="color: #dd0000;">{{t('损失金额')}}</text>
					</view>
					</view>
					  
				</view>
                <view class="custom_field" v-if="data.userinfo.othermoney_status==1">
                	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>
                		<text class='t2'>{{smartFormatAmount(data.userinfo.money2?data.userinfo.money2:0)}}</text>
                		<text class="t1">{{t('余额2')}}</text>
                	</view>
                	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money3' @tap='goto'>
                		<text class='t2'>{{smartFormatAmount(data.userinfo.money3?data.userinfo.money3:0)}}</text>
                		<text class="t1">{{t('余额3')}}</text>
                	</view>
                	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money4' @tap='goto'>
                		<text class='t2'>{{smartFormatAmount(data.userinfo.money4?data.userinfo.money4:0)}}</text>
                		<text class="t1">{{t('余额4')}}</text>
                	</view>
                	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money5' @tap='goto'>
                		<text class='t2'>{{smartFormatAmount(data.userinfo.money5?data.userinfo.money5:0)}}</text>
                		<text class="t1">{{t('余额5')}}</text>
                	</view>
                	<view class='item' data-url='/pagesExt/othermoney/frozen_moneylog' @tap='goto'>
                		<text class='t2'>{{smartFormatAmount(data.userinfo.frozen_money?data.userinfo.frozen_money:0)}}</text>
                		<text class="t1">{{t('冻结金额')}}</text>
                	</view>
                </view>
				<!-- <block v-if="params.parent_show==1 && data.parent_show">
					<view class="parent" v-if="data.parent" :style="'background: rgba('+t('color1rgb')+',10%);'">
							<view class="f1">
								<image class="parentimg" :src="data.parent.headimg"></image>
								<view class="parentimg-tag" :style="'background: rgba('+t('color1rgb')+',100%);'">{{t('推荐人')}}</view>
							</view>
							<view class="f2 flex1">
								<view class="nick">{{data.parent.nickname}}</view>
								<view class="nick" v-if="data.parent && data.parent.weixin" @tap="copy" :data-text="data.parent.weixin">微信号：{{data.parent.weixin}}<image src="../../static/img/copy.png" class="copyicon"></image></view>
							</view>
							<view class="f3" v-if="data.parent && data.parent.tel" @tap="goto" :data-url="'tel::'+data.parent.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
					</view>
					<view class="parent" v-else :style="'background: rgba('+t('color1rgb')+',10%);'">
						<image class="f1 parentimg" :src="data.sysset.logo"/>
						<view class="f2 flex1">
							<view class="nick">{{data.sysset.name}}</view>
							<view class="nick">{{data.sysset.tel}}</view>
						</view>
						<view class="f3" @tap="goto" :data-url="'tel::'+data.sysset.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
					</view>
				</block> -->
			</view>
			<view class="userset" @tap="goto" data-url="/pagesExa/my/set" v-if="params.seticonshow!=='0'"><image src="/static/img/set.png" class="img"/></view>
		</view>
	</view>
	
	
	<view v-if="params.style==2" :style="'background: linear-gradient(45deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'">
		<view class="dp-userinfo2" v-if="params.style==2" :style="{
			background:'url('+params.bgimg+') no-repeat',
			backgroundSize:'100% auto',
			margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
			padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
			height: (400 + (data.userinfo.othermoney_status==1 ? 110 : 0) + (params.cardUpgradeShow==1 ? 150 : 0) + (params.ordershow==1 ? 100 : 0)) + 'rpx'
		}">
			<view class="info">
				<image class="headimg" :src="data.userinfo.headimg" @tap="weixinlogin"/>
				<view class="nickname">
					<view class="nick">{{data.userinfo.nickname}} </view>
			
					<view v-if="data.zhaopin && data.zhaopin.show_zhaopin" class="flex">
						<text v-if="data.zhaopin.is_qiuzhi_renzheng && !data.zhaopin.is_qiuzhi_qianyue" class="qiuzhi-renzheng">认证保障中</text>
						<text v-if="data.zhaopin.is_qiuzhi_qianyue" class="qiuzhi-qianyue">签约保障中</text>
						<text v-if="data.zhaopin.is_zhaopin_renzheng" class="zhaopin-renzheng">认证企业</text>
					</view>
					<!-- <view class="desc">ID：{{userinfo.id}}</view> -->
					<view style="display: flex;" v-if="params.levelshow==1">
						<view class="user-level" @tap="openLevelup" data-levelid="">
							<image class="level-img" :src='data.userlevel.icon' v-if="data.userlevel.icon"/>
							<view class="level-name">{{data.userlevel.name}}</view>
						</view>
						<view class="user-level" v-for="(item, index) in data.userlevelList" :key="index" @tap="openLevelup" :data-levelid="item.id">
							<image class="level-img" :src='item.icon' v-if="item.icon"/>
							<view class="level-name">{{item.name}}</view>
						</view>
				
					</view>
			
					<view class="usermid" v-if="params.midshow=='1' && (data.userinfo.show_user_id === undefined || data.userinfo.show_user_id == 1)">用户ID：<text user-select="true" selectable="true">{{data.userinfo.id}}</text></view>
					<view class="usermid" v-if="data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1">邀请码：<text user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view>
					
					<!-- 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_002] Style2上级信息显示区域 -->
					<view v-if="params.parent_show==1 && data.userinfo.referral_detail && data.userinfo.referral_detail.nickname" class="parent-simple-style2" style="margin-top:8rpx;">
						<text class="parent-label" style="font-size:22rpx;color:rgba(255,255,255,0.7);margin-right:8rpx;">上级：</text>
						<text class="parent-name" style="font-size:24rpx;color:rgba(255,255,255,0.9);font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text>
					</view>
				</view>
				
				
				<view class="qhsf" v-if="params.toggleidentity==1" @click="goto" :data-url="params.hrefurl">切换身份</view>
				
				<view class="ktnum" v-if="params.ktnumshow=='1'" style="display: flex; position: absolute; right: 30rpx; top:30%;color:#ffff; " >开团次数：<text style="font-size: 24rpx;line-height: 40rpx;" >{{data.userinfo.ktnum}}</text></view>
			</view>

			<!-- 会员升级卡片 -->
			<view v-if="params.cardUpgradeShow==1 && data.userlevel && data.userlevel.name" class="dp-userinfo-vipcard">
				<!-- 卡片主体 - 使用会员等级的自定义封面和颜色 -->
				<view class="vipcard-container" :style="{
					background: getVipCardBackground(),
					borderRadius: '16rpx',
					position: 'relative',
					minHeight: '180rpx',
					overflow: 'hidden'
				}">
					<!-- 主封面背景 -->
					<image v-if="data.userlevel.card_main_cover" 
						   :src="data.userlevel.card_main_cover" 
						   class="vipcard-main-cover"
						   mode="aspectFill">
					</image>
					
					<!-- 颜色渐变遮罩层 -->
					<!-- <view class="vipcard-color-overlay" :style="{background: getVipCardColorOverlay()}"></view> -->
					
					<!-- 副封面装饰图 -->
					<image v-if="data.userlevel.card_sub_cover"
						   :src="data.userlevel.card_sub_cover" 
						   class="vipcard-sub-cover" 
						   mode="aspectFit">
					</image>
					
					<!-- 卡片内容 -->
					<!--<view class="vipcard-content">-->
						<!-- 左侧信息 -->
						<!--<view class="vipcard-left">
							<view class="vip-level-name" :style="{color: getVipLevelNameColor()}">
								{{data.userlevel.name || '会员等级'}}
							</view>
							<view v-if="data.userlevel.card_description" 
								  class="vip-description" 
								  :style="{color: getVipDescriptionColor()}">
								{{data.userlevel.card_description}}
							</view>
							<view v-if="data.userlevel.next_level_id > 0" 
								  class="vip-next-level" 
								  :style="{color: getVipDescriptionColor()}">
								升级享受更多特权
							</view>
							<view v-else-if="data.userlevel.next_level_id == 0" 
								  class="vip-max-level" 
								  :style="{color: getVipDescriptionColor()}">
								已达到最高等级
							</view>
						</view>-->
						
						<!-- 右侧升级按钮 -->
						<!--<view class="vipcard-right">
							<view class="vip-upgrade-btn" @tap="handleCardActivation" :style="{
								backgroundColor: getButtonColor(),
								borderColor: getButtonBorderColor(),
								color: getButtonTextColor()
							}">
								{{ getActivateButtonText() }}
							</view>
						</view>
					</view>-->
				</view>
			</view>

			<view class="custom_field" v-if="params.moneyshow==1 || params.scoreshow==1 || params.couponshow==1">
				<view class='item' v-if="params.moneyshow==1" data-money-type='money' data-default-url='/pagesExb/money/recharge' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.money)}}</text>
					<text class="t1">{{t('余额')}}</text>
				</view>
				<view class='item' v-if="params.commissionshow==1 && data.userlevel && data.userlevel.can_agent>0" data-money-type='commission' data-default-url='/pages/commission/index' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.commission)}}</text>
					<text class="t1">{{t('佣金')}}</text>
				</view>
				<view class='item' v-if="params.creditshow==1" data-money-type='credit' data-default-url='/pagesExa/my/creditlog' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.credit || 0}}</text>
					<text class="t1">{{t('信用分')}}</text>
				</view>
				<view class='item' v-if="params.workshow==1" data-money-type='work' data-default-url='/pagesExt/work/mywork' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.work_count || 0}}</text>
					<text class="t1">{{t('工作经历')}}</text>
				</view>
				<view class='item' v-if="params.inviteshow==1" data-money-type='invite' data-default-url='/pagesExt/team/index' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.team_count || 0}}</text>
					<text class="t1">{{t('邀请记录')}}</text>
				</view>
				<view class='item' v-if="params.scoreshow==1" data-money-type='score' data-default-url='/pagesExa/my/scorelog' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.score)}}</text>
					<text class="t1">{{t('积分')}}</text>
				</view>
				<view class='item' v-if="params.couponshow==1" data-money-type='coupon' data-default-url='/pagesExb/coupon/mycoupon' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.couponcount}}</text>
					<text class="t1">{{t('优惠券')}}</text>
				</view>
				<view class='item' v-if="params.formshow==1" data-url='/pages/form/formlog?st=1' @tap='goto'>
					<text class='t2'>{{data.userinfo.formcount}}</text>
					<text class="t1">{{params.formtext}}</text>
				</view>
			</view>
            <view class="custom_field" v-if="data.userinfo.othermoney_status==1">
            	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>
            		<text class='t2'>{{smartFormatAmount(data.userinfo.money2?data.userinfo.money2:0)}}</text>
            		<text class="t1">{{t('余额2')}}</text>
            	</view>
            	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money3' @tap='goto'>
            		<text class='t2'>{{smartFormatAmount(data.userinfo.money3?data.userinfo.money3:0)}}</text>
            		<text class="t1">{{t('余额3')}}</text>
            	</view>
            	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money4' @tap='goto'>
            		<text class='t2'>{{smartFormatAmount(data.userinfo.money4?data.userinfo.money4:0)}}</text>
            		<text class="t1">{{t('余额4')}}</text>
            	</view>
            	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money5' @tap='goto'>
            		<text class='t2'>{{smartFormatAmount(data.userinfo.money5?data.userinfo.money5:0)}}</text>
            		<text class="t1">{{t('余额5')}}</text>
            	</view>
                <view class='item' data-url='/pagesExt/othermoney/frozen_moneylog' @tap='goto'>
                	<text class='t2'>{{smartFormatAmount(data.userinfo.frozen_money?data.userinfo.frozen_money:0)}}</text>
                	<text class="t1">{{t('冻结金额')}}</text>
                </view>
            </view>
			<view class="userset" @tap="goto" data-url="/pagesExa/my/set" v-if="params.seticonshow!=='0'"><image src="/static/img/set.png" class="img"/></view>

			<block v-if="platform=='wx'">
				<view class="usercard" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
				<view class="usercard" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="addmembercard" :data-card_id="data.card_id"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
			</block>
			<block v-if="platform=='mp'">
				<view class="usercard" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
				<view class="usercard" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="goto" :data-url="data.card_returl"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
			</block>
		</view>
	</view>
	<!-- <view class="dp-userinfo-order" v-if="params.style==2 && params.parent_show==1 && data.parent_show" :style="{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',marginTop:params.style==2?'-100rpx':'0',marginBottom:'120rpx'}">
		<view class="parent" v-if="data.parent">
				<view class="f1">
					<image class="parentimg" :src="data.parent.headimg"></image>
					<view class="parentimg-tag" :style="'background: rgba('+t('color1rgb')+',100%);'">{{t('推荐人')}}</view>
				</view>
				<view class="f2 flex1">
					<view class="nick">{{data.parent.nickname}}</view>
					<view class="nick" v-if="data.parent && data.parent.weixin" @tap="copy" :data-text="data.parent.weixin">微信号：{{data.parent.weixin}}<image src="../../static/img/copy.png" class="copyicon"></image></view>
				</view>
				<view class="f3" v-if="data.parent && data.parent.tel" @tap="goto" :data-url="'tel::'+data.parent.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
		</view>
		<view class="parent" v-else>
			<image class="f1 parentimg" :src="data.sysset.logo"/>
			<view class="f2 flex1">
				<view class="nick">{{data.sysset.name}}</view>
				<view class="nick">{{data.sysset.tel}}</view>
			</view>
			<view class="f3" @tap="goto" :data-url="'tel::'+data.sysset.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
		</view>
	</view> -->

	<!-- Style 3 -->
	<view v-if="params.style==3" :style="'background: linear-gradient(to bottom, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0.1) 100%);padding-bottom:'+(params.cardUpgradeShow==1?'0rpx':'1rpx')+';'">
		<view class="dp-userinfo3" :style="{
				background: params.bgimg ? 'url('+params.bgimg+') no-repeat' : '#fff',
				backgroundSize: params.bgimg ? '100% auto' : 'auto',
				margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
				padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
			}">

			<view class="userset" @tap="goto" data-url="/pagesExa/my/set" v-if="params.seticonshow!=='0'"><image src="/static/img/set.png" class="img"/></view>
			<block v-if="platform=='wx'">
				<view class="usercard style3card" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
				<view class="usercard style3card" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="addmembercard" :data-card_id="data.card_id"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
			</block>
			<block v-if="platform=='mp'">
				<view class="usercard style3card" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
				<view class="usercard style3card" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="goto" :data-url="data.card_returl"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
			</block>

			<view class="info-main">
				<image class="headimg" :src="data.userinfo.headimg" @tap="weixinlogin"/>
				<view class="nickname">{{data.userinfo.nickname}}</view>
				<text v-if="params.midshow=='1' && (data.userinfo.show_user_id === undefined || data.userinfo.show_user_id == 1)" class="userid">(ID:{{data.userinfo.id}})</text>
				<view class="level-container">
					<view class="user-level" @tap="openLevelup" data-levelid="" v-if="params.levelshow==1 && data.userlevel && data.userlevel.name">
						<image class="level-img" :src="data.userlevel.icon" v-if="data.userlevel.icon"/>
						<view class="level-name">{{data.userlevel.name}}</view>
					</view>
					<view class="user-level" v-for="(item, index) in data.userlevelList" :key="index" @tap="openLevelup" :data-levelid="item.id" v-if="params.levelshow==1">
						<image class="level-img" :src='item.icon' v-if="item.icon"/>
						<view class="level-name">{{item.name}}</view>
					</view>
				</view>
				<view class="invite-code" v-if="data.userlevel && data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1">邀请码：<text user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view>
				
				<!-- 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_003] Style3上级信息显示区域 -->
				<view v-if="params.parent_show==1 && data.userinfo.referral_detail && data.userinfo.referral_detail.nickname" class="parent-simple-style3" style="margin-top:8rpx;font-size:22rpx;color:#666;">
					<text class="parent-label" style="font-size:22rpx;color:#999;margin-right:8rpx;">上级：</text>
					<text class="parent-name" style="font-size:24rpx;color:#333;font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text>
				</view>
				
				<view v-if="data.zhaopin && data.zhaopin.show_zhaopin" class="zhaopin-tags">
					<text v-if="data.zhaopin.is_qiuzhi_renzheng && !data.zhaopin.is_qiuzhi_qianyue" class="zhaopin-tag">认证保障中</text>
					<text v-if="data.zhaopin.is_qiuzhi_qianyue" class="zhaopin-tag">签约保障中</text>
					<text v-if="data.zhaopin.is_zhaopin_renzheng" class="zhaopin-tag">认证企业</text>
				</view>
			</view>

			<view class="custom_field_grid">
				<view class='item' v-if="params.moneyshow==1" data-money-type='money' data-default-url='/pagesExb/money/recharge' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.money)}}</text>
					<text class="t1">{{t('余额')}}</text>
				</view>
				<view class='item' v-if="params.commissionshow==1 && data.userlevel && data.userlevel.can_agent>0" data-money-type='commission' data-default-url='/activity/commission/index' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.commission)}}</text>
					<text class="t1">{{t('佣金')}}</text>
				</view>
				<view class='item' v-if="params.scoreshow==1" data-money-type='score' data-default-url='/pagesExa/my/scorelog' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.score)}}</text>
					<text class="t1">{{t('积分')}}</text>
				</view>
				<view class='item' v-if="params.couponshow==1" data-money-type='coupon' data-default-url='/pagesExb/coupon/mycoupon' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.couponcount}}</text>
					<text class="t1">{{t('优惠券')}}</text>
				</view>
				
				<view class='item' v-if="params.creditshow==1" data-money-type='credit' data-default-url='/pagesExa/my/creditlog' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.credit_score || 0}}</text>
					<text class="t1">{{t('信用分')}}</text>
				</view>
				<view class='item' v-if="params.workshow==1" data-money-type='work' data-default-url='/zhaopin/myApply' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.job_count || 0}}</text>
					<text class="t1">{{t('工作经历')}}</text>
				</view>
				<view class='item' v-if="params.inviteshow==1" data-money-type='invite' data-default-url='/activity/commission/myteam' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.team_count || 0}}</text>
					<text class="t1">{{t('邀请记录')}}</text>
				</view>
				<view class='item' v-if="params.tuikuangshow==1">
					<text class='t2'>{{data.userinfo.kuang_num}}</text>
					<text class="t1">{{t('可退框数')}}</text>
				</view>
				
				<view class='item' v-if="params.xianjinquanshow==1" data-money-type='xianjinquan' data-default-url='/pagesExb/money/moneylog?st=8' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.heiscore?data.userinfo.heiscore:0)}}</text>
					<text class="t1">{{t('现金券')}}</text>
				</view>
				<view class='item' v-if="params.huangjifenshow==1" data-money-type='huangjifen' data-default-url='/pagesExb/money/moneylog?st=9' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.scorehuang?data.userinfo.scorehuang:0)}}</text>
					<text class="t1">{{t('黄积分')}}</text>
				</view>
				<view class='item' v-if="params.gongxianzhishow==1" data-money-type='gongxianzhi' data-default-url='/pagesExb/money/moneylog?st=10' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.contribution_num?data.userinfo.contribution_num:0)}}</text>
					<text class="t1">{{t('贡献值')}}</text>
				</view>
				<view class='item' v-if="params.shouyichishow==1" data-money-type='shouyichi' data-default-url='/pagesExb/money/moneylog?st=15' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.syc_num)}}</text>
					<text class="t1">{{t('收益池')}}</text>
				</view>
				<view class='item' v-if="params.chuangyezhishow==1 && params.scoreshow==1" data-url='/pagesExa/my/scoreloghuang' @tap='goto'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.bustotal)}}</text>
					<text class="t1">{{t('创业值')}}</text>
				</view>
				<view class='item' v-if="params.suishijineshow==1" data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>
					<text class='t2' style="color: #dd0000;">{{smartFormatAmount(data.userinfo.sunshijine?data.userinfo.sunshijine:0)}}</text>
					<text class="t1" style="color: #dd0000;">{{t('损失金额')}}</text>
				</view>
				<view class='item' v-if="params.formshow==1" data-url='/pages/form/formlog?st=1' @tap='goto'>
					<text class='t2'>{{data.userinfo.formcount}}</text>
					<text class="t1">{{params.formtext}}</text>
				</view>
			</view>

			<view class="custom_field_othermoney" v-if="data.userinfo.othermoney_status==1">
				<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.money2?data.userinfo.money2:0)}}</text>
					<text class="t1">{{t('余额2')}}</text>
				</view>
				<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money3' @tap='goto'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.money3?data.userinfo.money3:0)}}</text>
					<text class="t1">{{t('余额3')}}</text>
				</view>
				<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money4' @tap='goto'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.money4?data.userinfo.money4:0)}}</text>
					<text class="t1">{{t('余额4')}}</text>
				</view>
				<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money5' @tap='goto'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.money5?data.userinfo.money5:0)}}</text>
					<text class="t1">{{t('余额5')}}</text>
				</view>
				<view class='item' data-url='/pagesExt/othermoney/frozen_moneylog' @tap='goto'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.frozen_money?data.userinfo.frozen_money:0)}}</text>
					<text class="t1">{{t('冻结金额')}}</text>
				</view>
			</view>
			
			<!-- Parent Info for Style 3, displayed at the bottom of the card -->
			<!-- <view v-if="params.parent_show==1 && data.parent_show" class="parent-info-style3">
				<view class="parent" v-if="data.parent" :style="'background: rgba('+t('color1rgb')+',0.05);border-radius:8rpx;margin-top:20rpx;'">
						<view class="f1">
							<image class="parentimg" :src="data.parent.headimg"></image>
							<view class="parentimg-tag" :style="'background: rgba('+t('color1rgb')+',1);'">{{t('推荐人')}}</view>
						</view>
						<view class="f2 flex1">
							<view class="nick">{{data.parent.nickname}}</view>
							<view class="nick" v-if="data.parent && data.parent.weixin" @tap="copy" :data-text="data.parent.weixin">微信号：{{data.parent.weixin}}<image src="../../static/img/copy.png" class="copyicon"></image></view>
						</view>
						<view class="f3" v-if="data.parent && data.parent.tel" @tap="goto" :data-url="'tel::'+data.parent.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
				</view>
				<view class="parent" v-else :style="'background: rgba('+t('color1rgb')+',0.05);border-radius:8rpx;margin-top:20rpx;'">
					<image class="f1 parentimg" :src="data.sysset.logo"/>
					<view class="f2 flex1">
						<view class="nick">{{data.sysset.name}}</view>
						<view class="nick">{{data.sysset.tel}}</view>
					</view>
					<view class="f3" @tap="goto" :data-url="'tel::'+data.sysset.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
				</view>
			</view> -->
		</view>
	</view>

	<!-- Style 4 -->
	<view v-if="params.style==4" :style="'background: linear-gradient(180deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0) 100%);padding-bottom:'+(params.cardUpgradeShow==1?'10rpx':'1rpx')+';'">
		<view class="dp-userinfo4" :style="{
				background: params.bgimg ? 'url('+params.bgimg+') no-repeat' : '#fff',
				backgroundSize: params.bgimg ? '100% auto' : 'auto',
				margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
				padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
			}">

			<!-- Top Bar for settings, card, etc. -->
			<view class="top-bar-style4">
				<view class="userset" @tap="goto" data-url="/pagesExa/my/set" v-if="params.seticonshow!=='0'"><image src="/static/img/set.png" class="img"/></view>
				<block v-if="platform=='wx'">
					<view class="usercard style4card" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
					<view class="usercard style4card" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="addmembercard" :data-card_id="data.card_id"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
				</block>
				<block v-if="platform=='mp'">
					<view class="usercard style4card" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
					<view class="usercard style4card" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="goto" :data-url="data.card_returl"><image class="img" src="/static/img/ico-card2.png"/><text class="txt">会员卡</text></view>
				</block>
			</view>

			<!-- User Info Section -->
			<view class="user-details-style4">
				<image class="headimg-style4" :src="data.userinfo.headimg" @tap="weixinlogin"/>
				<view class="text-info-style4">
					<view class="nickname-style4">{{data.userinfo.nickname}}
						<text v-if="params.midshow=='1' && (data.userinfo.show_user_id === undefined || data.userinfo.show_user_id == 1)" class="id-style4"> (ID:{{data.userinfo.id}})</text>
					</view>
					<view class="level-section-style4">
						<view class="user-level-style4" @tap="openLevelup" data-levelid="" v-if="params.levelshow==1 && data.userlevel && data.userlevel.name">
							<image class="level-img" :src="data.userlevel.icon" v-if="data.userlevel.icon"/>
							<text class="level-name">{{data.userlevel.name}}</text>
						</view>
						<view class="user-level-style4" v-for="(item, index) in data.userlevelList" :key="index" @tap="openLevelup" :data-levelid="item.id" v-if="params.levelshow==1">
							<image class="level-img" :src='item.icon' v-if="item.icon"/>
							<text class="level-name">{{item.name}}</text>
						</view>
					</view>
					<view class="invite-code-style4" v-if="data.userlevel && data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1">邀请码：<text user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view>
					
					<!-- 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_004] Style4上级信息显示区域 -->
					<view v-if="params.parent_show==1 && data.userinfo.referral_detail && data.userinfo.referral_detail.nickname" class="parent-simple-style4" style="margin-top:8rpx;font-size:22rpx;color:#666;">
						<text class="parent-label" style="font-size:22rpx;color:#999;margin-right:8rpx;">上级：</text>
						<text class="parent-name" style="font-size:24rpx;color:#333;font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text>
					</view>
					
					<view v-if="data.zhaopin && data.zhaopin.show_zhaopin" class="zhaopin-tags-style4">
						<text v-if="data.zhaopin.is_qiuzhi_renzheng && !data.zhaopin.is_qiuzhi_qianyue" class="zhaopin-tag">认证保障中</text>
						<text v-if="data.zhaopin.is_qiuzhi_qianyue" class="zhaopin-tag">签约保障中</text>
						<text v-if="data.zhaopin.is_zhaopin_renzheng" class="zhaopin-tag">认证企业</text>
					</view>
				</view>
			</view>
	<!-- 会员升级卡片 -->
			<view v-if="params.cardUpgradeShow==1 && data.userlevel && data.userlevel.name" class="dp-userinfo-vipcard">
				<!-- 卡片主体 - 使用会员等级的自定义封面和颜色 -->
				<view class="vipcard-container" :style="{
					background: getVipCardBackground(),
					borderRadius: '16rpx',
					position: 'relative',
					minHeight: '180rpx',
					overflow: 'hidden'
				}">
					<!-- 主封面背景 -->
					<image v-if="data.userlevel.card_main_cover" 
						   :src="data.userlevel.card_main_cover" 
						   class="vipcard-main-cover"
						   mode="aspectFill">
					</image>
					
					<!-- 颜色渐变遮罩层 -->
					<!-- <view class="vipcard-color-overlay" :style="{background: getVipCardColorOverlay()}"></view> -->
					
					<!-- 副封面装饰图 -->
					<image v-if="data.userlevel.card_sub_cover"
						   :src="data.userlevel.card_sub_cover" 
						   class="vipcard-sub-cover" 
						   mode="aspectFit">
					</image>
					
					<!-- 卡片内容 -->
					<!--<view class="vipcard-content">-->
						<!-- 左侧信息 -->
						<!--<view class="vipcard-left">
							<view class="vip-level-name" :style="{color: getVipLevelNameColor()}">
								{{data.userlevel.name || '会员等级'}}
							</view>
							<view v-if="data.userlevel.card_description" 
								  class="vip-description" 
								  :style="{color: getVipDescriptionColor()}">
								{{data.userlevel.card_description}}
							</view>
							<view v-if="data.userlevel.next_level_id > 0" 
								  class="vip-next-level" 
								  :style="{color: getVipDescriptionColor()}">
								升级享受更多特权
							</view>
							<view v-else-if="data.userlevel.next_level_id == 0" 
								  class="vip-max-level" 
								  :style="{color: getVipDescriptionColor()}">
								已达到最高等级
							</view>
						</view>-->
						
						<!-- 右侧升级按钮 -->
						<!--<view class="vipcard-right">
							<view class="vip-upgrade-btn" @tap="handleCardActivation" :style="{
								backgroundColor: getButtonColor(),
								borderColor: getButtonBorderColor(),
								color: getButtonTextColor()
							}">
								{{ getActivateButtonText() }}
							</view>
						</view>
					</view>-->
				</view>
			</view>
			<!-- Stats Grid -->
			<view class="stats-grid-style4">
				<view class='item' v-if="params.moneyshow==1" data-money-type='money' data-default-url='/pagesExb/money/recharge' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.money)}}</text><text class="t1">{{t('余额')}}</text>
				</view>
        <view class='item' v-if="params.scoreshow==1" data-money-type='score' data-default-url='/pagesExa/my/scorelog' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.score)}}</text><text class="t1">{{t('积分')}}</text>
				</view>
				<view class='item' v-if="params.couponshow==1" data-money-type='coupon' data-default-url='/pagesExb/coupon/mycoupon' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.couponcount}}</text><text class="t1">{{t('优惠券')}}</text>
				</view>
				<view class='item' v-if="params.commissionshow==1 && data.userlevel && data.userlevel.can_agent>0" data-money-type='commission' data-default-url='/activity/commission/index' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.commission)}}</text><text class="t1">{{t('佣金')}}</text>
				</view>
				<view class='item' v-if="params.creditshow==1" data-money-type='credit' data-default-url='/pagesExa/my/creditlog' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.credit_score || 0}}</text><text class="t1">{{t('信用分')}}</text>
				</view>
				<view class='item' v-if="params.workshow==1" data-money-type='work' data-default-url='/zhaopin/myApply' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.job_count || 0}}</text><text class="t1">{{t('工作经历')}}</text>
				</view>
				<view class='item' v-if="params.inviteshow==1" data-money-type='invite' data-default-url='/activity/commission/myteam' @tap='gotoMoneyItem'>
					<text class='t2'>{{data.userinfo.team_count || 0}}</text><text class="t1">{{t('邀请记录')}}</text>
				</view>
				<view class='item' v-if="params.tuikuangshow==1">
					<text class='t2'>{{data.userinfo.kuang_num}}</text><text class="t1">{{t('可退框数')}}</text>
				</view>
				<view class='item' v-if="params.xianjinquanshow==1" data-money-type='xianjinquan' data-default-url='/pagesExb/money/moneylog?st=8' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.heiscore?data.userinfo.heiscore:0)}}</text><text class="t1">{{t('现金券')}}</text>
				</view>
				<view class='item' v-if="params.huangjifenshow==1" data-money-type='huangjifen' data-default-url='/pagesExb/money/moneylog?st=9' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.scorehuang?data.userinfo.scorehuang:0)}}</text><text class="t1">{{t('黄积分')}}</text>
				</view>
				<view class='item' v-if="params.gongxianzhishow==1" data-money-type='gongxianzhi' data-default-url='/pagesExb/money/moneylog?st=10' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.contribution_num?data.userinfo.contribution_num:0)}}</text><text class="t1">{{t('贡献值')}}</text>
				</view>
				<view class='item' v-if="params.shouyichishow==1" data-money-type='shouyichi' data-default-url='/pagesExb/money/moneylog?st=15' @tap='gotoMoneyItem'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.syc_num)}}</text><text class="t1">{{t('收益池')}}</text>
				</view>
				<view class='item' v-if="params.chuangyezhishow==1 && params.scoreshow==1" data-url='/pagesExa/my/scoreloghuang' @tap='goto'>
					<text class='t2'>{{smartFormatAmount(data.userinfo.bustotal)}}</text><text class="t1">{{t('创业值')}}</text>
				</view>
				<view class='item' v-if="params.suishijineshow==1" data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'>
					<text class='t2' style="color: #dd0000;">{{smartFormatAmount(data.userinfo.sunshijine?data.userinfo.sunshijine:0)}}</text><text class="t1" style="color: #dd0000;">{{t('损失金额')}}</text>
				</view>
				<view class='item' v-if="params.formshow==1" data-url='/pages/form/formlog?st=1' @tap='goto'>
					<text class='t2'>{{data.userinfo.formcount}}</text><text class="t1">{{params.formtext}}</text>
				</view>
			</view>

			<!-- Other Money Section (if enabled) -->
      <view class="othermoney-section-style4" v-if="data.userinfo.othermoney_status==1">
      	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money2' @tap='goto'><text class='t2'>{{smartFormatAmount(data.userinfo.money2?data.userinfo.money2:0)}}</text><text class="t1">{{t('余额2')}}</text></view>
      	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money3' @tap='goto'><text class='t2'>{{smartFormatAmount(data.userinfo.money3?data.userinfo.money3:0)}}</text><text class="t1">{{t('余额3')}}</text></view>
      	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money4' @tap='goto'><text class='t2'>{{smartFormatAmount(data.userinfo.money4?data.userinfo.money4:0)}}</text><text class="t1">{{t('余额4')}}</text></view>
      	<view class='item' data-url='/pagesExt/othermoney/withdraw?type=money5' @tap='goto'><text class='t2'>{{smartFormatAmount(data.userinfo.money5?data.userinfo.money5:0)}}</text><text class="t1">{{t('余额5')}}</text></view>
        <view class='item' data-url='/pagesExt/othermoney/frozen_moneylog' @tap='goto'><text class='t2'>{{smartFormatAmount(data.userinfo.frozen_money?data.userinfo.frozen_money:0)}}</text><text class="t1">{{t('冻结金额')}}</text></view>
      </view>
			
			<!-- Parent Info (if enabled) -->
			<!-- <view v-if="params.parent_show==1 && data.parent_show" class="parent-info-style4">
				<view class="parent" v-if="data.parent" :style="'background: rgba('+t('color1rgb')+',0.05); border-radius: 8rpx;'">
						<view class="f1"><image class="parentimg" :src="data.parent.headimg"></image><view class="parentimg-tag" :style="'background: rgba('+t('color1rgb')+',1);'">{{t('推荐人')}}</view></view>
						<view class="f2 flex1"><view class="nick">{{data.parent.nickname}}</view><view class="nick" v-if="data.parent && data.parent.weixin" @tap="copy" :data-text="data.parent.weixin">微信号：{{data.parent.weixin}}<image src="../../static/img/copy.png" class="copyicon"></image></view></view>
						<view class="f3" v-if="data.parent && data.parent.tel" @tap="goto" :data-url="'tel::'+data.parent.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
				</view>
				<view class="parent" v-else :style="'background: rgba('+t('color1rgb')+',0.05); border-radius: 8rpx;'">
					<image class="f1 parentimg" :src="data.sysset.logo"/>
					<view class="f2 flex1"><view class="nick">{{data.sysset.name}}</view><view class="nick">{{data.sysset.tel}}</view></view>
					<view class="f3" @tap="goto" :data-url="'tel::'+data.sysset.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
				</view>
			</view> -->
		</view>
	</view>
	<!-- <view class="dp-userinfo-order" v-if="params.style==2 && params.parent_show==1 && data.parent_show" :style="{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',marginTop:params.style==2?'-100rpx':'0',marginBottom:'120rpx'}">
		<view class="parent" v-if="data.parent">
				<view class="f1">
					<image class="parentimg" :src="data.parent.headimg"></image>
					<view class="parentimg-tag" :style="'background: rgba('+t('color1rgb')+',100%);'">{{t('推荐人')}}</view>
				</view>
				<view class="f2 flex1">
					<view class="nick">{{data.parent.nickname}}</view>
					<view class="nick" v-if="data.parent && data.parent.weixin" @tap="copy" :data-text="data.parent.weixin">微信号：{{data.parent.weixin}}<image src="../../static/img/copy.png" class="copyicon"></image></view>
				</view>
				<view class="f3" v-if="data.parent && data.parent.tel" @tap="goto" :data-url="'tel::'+data.parent.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
		</view>
		<view class="parent" v-else>
			<image class="f1 parentimg" :src="data.sysset.logo"/>
			<view class="f2 flex1">
				<view class="nick">{{data.sysset.name}}</view>
				<view class="nick">{{data.sysset.tel}}</view>
			</view>
			<view class="f3" @tap="goto" :data-url="'tel::'+data.sysset.tel"><image src="../../static/img/tel2.png" class="handle-img"></image></view>
		</view>
	</view> -->

	<!-- Style 5 - Simplified without assets display -->
	<view v-if="params.style==5" :style="'background: linear-gradient(to bottom, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0.05) 100%);padding-bottom:'+(params.cardUpgradeShow==1?'0rpx':'1rpx')+';'">
		<view class="dp-userinfo5" :style="{
				background: params.bgimg ? 'url('+params.bgimg+') no-repeat' : '#fff',
				backgroundSize: params.bgimg ? '100% auto' : 'cover',
				margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
				padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx ' + 30+'rpx ' + (params.padding_y*2.2)+'rpx ',
			}">
			
			<!-- Top Options -->
			<view class="top-actions-style5">
				<view class="userset" @tap="goto" data-url="/pagesExa/my/set" v-if="params.seticonshow!=='0'">
					<image src="/static/img/set.png" class="img"/>
				</view>
				<block v-if="platform=='wx'">
					<view class="usercard style5card" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code">
						<image class="img" src="/static/img/ico-card2.png"/>
						<text class="txt">会员卡</text>
					</view>
					<view class="usercard style5card" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="addmembercard" :data-card_id="data.card_id">
						<image class="img" src="/static/img/ico-card2.png"/>
						<text class="txt">会员卡</text>
					</view>
				</block>
				<block v-if="platform=='mp'">
					<view class="usercard style5card" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard" :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code">
						<image class="img" src="/static/img/ico-card2.png"/>
						<text class="txt">会员卡</text>
					</view>
					<view class="usercard style5card" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="goto" :data-url="data.card_returl">
						<image class="img" src="/static/img/ico-card2.png"/>
						<text class="txt">会员卡</text>
					</view>
				</block>
			</view>
			
			<!-- Main Profile -->
			<view class="profile-main-style5">
				<image class="avatar-style5" :src="data.userinfo.headimg" @tap="weixinlogin" />
				<view class="profile-content-style5">
					<view class="name-container-style5">
						<view class="name-id-wrapper">
							<text class="name-style5">{{data.userinfo.nickname}}</text>
							<text v-if="params.midshow=='1' && (data.userinfo.show_user_id === undefined || data.userinfo.show_user_id == 1)" class="id-style5">（ID: {{data.userinfo.id}}）</text>
						</view>
					</view>
					
					<view class="badges-container-style5">
<!-- 						<view class="user-level-style5" @tap="openLevelup" data-levelid="" v-if="params.levelshow==1 && data.userlevel && data.userlevel.name">
							<image class="level-img" :src="data.userlevel.icon" v-if="data.userlevel.icon"/>
							<text class="level-name">{{data.userlevel.name}}</text>
						</view> -->
						<view class="user-level-style5" v-for="(item, index) in data.userlevelList" :key="index" @tap="openLevelup" :data-levelid="item.id" v-if="params.levelshow==1">
							<image class="level-img" :src='item.icon' v-if="item.icon"/>
							<text class="level-name">{{item.name}}</text>
						</view>
						
						<view v-if="data.zhaopin && data.zhaopin.show_zhaopin" class="cert-badges-style5">
							<text v-if="data.zhaopin.is_qiuzhi_renzheng && !data.zhaopin.is_qiuzhi_qianyue" class="cert-badge">认证保障中</text>
							<text v-if="data.zhaopin.is_qiuzhi_qianyue" class="cert-badge">签约保障中</text>
							<text v-if="data.zhaopin.is_zhaopin_renzheng" class="cert-badge">认证企业</text>
						</view>
					</view>
					
					<view class="invite-code-style5" v-if="data.userlevel && data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1">
						<text class="invite-label">邀请码：</text>
						<text class="invite-value" user-select="true" selectable="true">{{data.userinfo.yqcode}}</text>
					</view>
					
					<!-- 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_005] Style5上级信息显示区域 -->
					<view v-if="params.parent_show==1 && data.userinfo.referral_detail && data.userinfo.referral_detail.nickname" class="parent-simple-style5" style="margin-top:8rpx;font-size:22rpx;color:#666;">
						<text class="parent-label" style="font-size:22rpx;color:#999;margin-right:8rpx;">上级：</text>
						<text class="parent-name" style="font-size:24rpx;color:#333;font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text>
					</view>
				</view>
			</view>
				<!-- 会员升级卡片 -->
			<view v-if="params.cardUpgradeShow==1 && data.userlevel && data.userlevel.name" class="dp-userinfo-vipcard">
				<!-- 卡片主体 - 使用会员等级的自定义封面和颜色 -->
				<view class="vipcard-container" :style="{
					background: getVipCardBackground(),
					borderRadius: '16rpx',
					position: 'relative',
					minHeight: '180rpx',
					overflow: 'hidden'
				}">
					<!-- 主封面背景 -->
					<image v-if="data.userlevel.card_main_cover" 
						   :src="data.userlevel.card_main_cover" 
						   class="vipcard-main-cover"
						   mode="aspectFill">
					</image>
					
					<!-- 颜色渐变遮罩层 -->
					<!-- <view class="vipcard-color-overlay" :style="{background: getVipCardColorOverlay()}"></view> -->
					
					<!-- 副封面装饰图 -->
					<image v-if="data.userlevel.card_sub_cover"
						   :src="data.userlevel.card_sub_cover" 
						   class="vipcard-sub-cover" 
						   mode="aspectFit">
					</image>
					
					<!-- 卡片内容 -->
					<!--<view class="vipcard-content">-->
						<!-- 左侧信息 -->
						<!--<view class="vipcard-left">
							<view class="vip-level-name" :style="{color: getVipLevelNameColor()}">
								{{data.userlevel.name || '会员等级'}}
							</view>
							<view v-if="data.userlevel.card_description" 
								  class="vip-description" 
								  :style="{color: getVipDescriptionColor()}">
								{{data.userlevel.card_description}}
							</view>
							<view v-if="data.userlevel.next_level_id > 0" 
								  class="vip-next-level" 
								  :style="{color: getVipDescriptionColor()}">
								升级享受更多特权
							</view>
							<view v-else-if="data.userlevel.next_level_id == 0" 
								  class="vip-max-level" 
								  :style="{color: getVipDescriptionColor()}">
								已达到最高等级
							</view>
						</view>-->
						
						<!-- 右侧升级按钮 -->
						<!--<view class="vipcard-right">
							<view class="vip-upgrade-btn" @tap="handleCardActivation" :style="{
								backgroundColor: getButtonColor(),
								borderColor: getButtonBorderColor(),
								color: getButtonTextColor()
							}">
								{{ getActivateButtonText() }}
							</view>
						</view>
					</view>-->
				</view>
			</view>
			<!-- Parent Info if Enabled -->
			<!-- <view v-if="params.parent_show==1 && data.parent_show" class="parent-style5">
				<view class="parent-card-style5" v-if="data.parent">
					<view class="parent-header-style5">
						<image class="parent-avatar-style5" :src="data.parent.headimg"></image>
						<view class="parent-tag-style5" :style="'background: rgba('+t('color1rgb')+',1);'">{{t('推荐人')}}</view>
					</view>
					<view class="parent-info-style5">
						<view class="parent-name-style5">{{data.parent.nickname}}</view>
						<view class="parent-contact-style5" v-if="data.parent && data.parent.weixin" @tap="copy" :data-text="data.parent.weixin">
							微信号：{{data.parent.weixin}}
							<image src="../../static/img/copy.png" class="copy-icon-style5"></image>
						</view>
					</view>
					<view class="parent-action-style5" v-if="data.parent && data.parent.tel" @tap="goto" :data-url="'tel::'+data.parent.tel">
						<image src="../../static/img/tel2.png" class="call-icon-style5"></image>
					</view>
				</view>
				
				<view class="parent-card-style5" v-else>
					<image class="parent-avatar-style5" :src="data.sysset.logo"/>
					<view class="parent-info-style5">
						<view class="parent-name-style5">{{data.sysset.name}}</view>
						<view class="parent-contact-style5">{{data.sysset.tel}}</view>
					</view>
					<view class="parent-action-style5" @tap="goto" :data-url="'tel::'+data.sysset.tel">
						<image src="../../static/img/tel2.png" class="call-icon-style5"></image>
					</view>
				</view>
			</view> -->
		</view>
	</view>
	<!-- 新增推荐人信息模块 -->
	<view class="dp-userinfo-referral" v-if="shouldShowReferral()" :style="{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',marginTop:params.style==2?'-100rpx':'0',background:'#fff',borderRadius:'16rpx', marginBottom:'20rpx'}">
		<view class="referral-info">
			<view class="referral-avatar">
				<image class="avatar-img" :src="data.userinfo.referral_avatar || data.sysset.logo"></image>
				<view class="referral-tag" :style="'background: rgba('+t('color1rgb')+',100%);'">{{t('推荐人')}}</view>
			</view>
			<view class="referral-content flex1">
				<view class="referral-name">{{data.userinfo.referral_name || t('示例推荐人')}}</view>
			</view>
			<view class="referral-action" @tap="viewReferralInfo">
				<text class="action-text">{{t('查看信息')}}</text>
				<image src="/static/img/arrowright.png" class="action-icon"/>
			</view>
		</view>
		
		<view class="referral-internal-actions" v-if="params.referral_team_show==1 || params.referral_support_show==1" :style="{backgroundColor:params.referral_actions_bgcolor || '#f9f9f9',borderBottomLeftRadius:params.referral_actions_radius+'px',borderBottomRightRadius:params.referral_actions_radius+'px'}">
			<view class="action-item" v-if="params.referral_team_show==1" @tap="showTeamInfoPopup">
				<image class="action-item-icon" :src="params.referral_team_icon || '/static/img/dsn_team_icon.png'"></image>
				<text class="action-item-text">{{params.referral_team_text || t('团队信息')}}</text>
			</view>
			<view class="action-item" v-if="params.referral_support_show==1" @tap="handleLink(params.referral_support_hrefurl, params.referral_support_link_name)">
				<image class="action-item-icon" :src="params.referral_support_icon || '/static/img/dsn_support_icon.png'"></image>
				<text class="action-item-text">{{params.referral_support_text || t('联系官方')}}</text>
			</view>
		</view>
	</view>

	

	<view class="dp-userinfo-order" :style="{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',marginTop:params.style==2?'-100rpx':'0'}" v-if="params.ordershow==1">
		<view class="head">
			<text class="f1">我的订单</text>
			<view class="f2" @tap="goto" data-url="/pagesExt/order/orderlist"><text>查看全部订单</text><image src="/static/img/arrowright.png" class="image"/></view>
		</view>
		<view class="content">
			 <view class="item" @tap="goto" data-url="/pagesExt/order/orderlist?st=0">
					<text class="iconfont icondaifukuan" :style="{color:t('color1')}"></text>
					<view class="t2" v-if="data.orderinfo.count0>0">{{data.orderinfo.count0}}</view>
					<text class="t3">待付款</text>
			 </view>
			 <view class="item" @tap="goto" data-url="/pagesExt/order/orderlist?st=1">
					<!-- <image src="/static/img/order2.png" class="image"/> -->
					<text class="iconfont icondaifahuo" :style="{color:t('color1')}"></text>
					<view class="t2" v-if="data.orderinfo.count1>0">{{data.orderinfo.count1}}</view>
					<text class="t3">待发货</text>
			 </view>
			 <view class="item" @tap="goto" data-url="/pagesExt/order/orderlist?st=2">
					<!-- <image src="/static/img/order3.png" class="image"/> -->
					<text class="iconfont icondaishouhuo" :style="{color:t('color1')}"></text>
					<view class="t2" v-if="data.orderinfo.count2>0">{{data.orderinfo.count2}}</view>
					<text class="t3">待收货</text>
			 </view>
			 <view class="item" @tap="goto" data-url="/pagesExt/order/orderlist?st=3">
					<!-- <image src="/static/img/order4.png" class="image"/> -->
					<text class="iconfont iconyiwancheng" :style="{color:t('color1')}"></text>
					<view class="t2" v-if="data.orderinfo.count3>0">{{data.orderinfo.count3}}</view>
					<text class="t3">已完成</text>
			 </view>
			 <view class="item" @tap="goto" data-url="/pagesExt/order/refundlist">
					<!-- <image src="/static/img/order4.png" class="image"/> -->
					<text class="iconfont icontuikuandingdan" :style="{color:t('color1')}"></text>
					<view class="t2" v-if="data.orderinfo.count4>0">{{data.orderinfo.count4}}</view>
					<text class="t3">退款/售后</text>
			 </view>
		</view>
	</view>


</view>
</template>
<script>
	var app = getApp();
	export default {
		data(){
			return {
				textset:app.globalData.textset,
				platform:app.globalData.platform
			}
		},
		props: {
			params:{},
			data:{}
		},
		// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][mounted_lifecycle_001] 组件加载时自动检查用户ID显示权限
		mounted() {
			// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][mounted_lifecycle_002] 延迟执行确保数据完全加载
			this.$nextTick(() => {
				if (this.data && this.data.userinfo) {
					this.checkUserIdPermission();
				}
			});
		},
		methods:{
			// 智能格式化金额：去掉末尾无意义的0，但至少保留2位小数
			smartFormatAmount(amount) {
				if (!amount && amount !== 0) return '0.00';
				
				const num = parseFloat(amount);
				if (isNaN(num)) return '0.00';
				
				// 首先格式化到5位小数
				let formatted = num.toFixed(5);
				
				// 分离整数和小数部分
				const parts = formatted.split('.');
				if (parts.length === 1) {
					// 没有小数部分，添加.00
					return parts[0] + '.00';
				}
				
				let decimalPart = parts[1];
				// 从右边去掉0，但至少保留2位
				while (decimalPart.length > 2 && decimalPart.charAt(decimalPart.length - 1) === '0') {
					decimalPart = decimalPart.slice(0, -1);
				}
				
				return parts[0] + '.' + decimalPart;
			},
			
			// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][check_user_id_permission_001] 检查用户ID显示权限的方法
			checkUserIdPermission: function() {
				const timestamp = new Date().toLocaleString('zh-CN');
				console.log(`${timestamp}-INFO-[dp-userinfo][check_user_id_permission_002] 前端midshow参数:`, this.params.midshow);
				console.log(`${timestamp}-INFO-[dp-userinfo][check_user_id_permission_003] 后端show_user_id字段:`, this.data.userinfo.show_user_id);
				
				// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][check_user_id_permission_004] 双重验证逻辑：前端配置+后端权限
				const frontendAllowed = this.params.midshow == '1';
				const backendAllowed = this.data.userinfo.show_user_id === undefined || this.data.userinfo.show_user_id == 1;
				const finalAllowed = frontendAllowed && backendAllowed;
				
				console.log(`${timestamp}-INFO-[dp-userinfo][check_user_id_permission_005] 前端允许显示:`, frontendAllowed);
				console.log(`${timestamp}-INFO-[dp-userinfo][check_user_id_permission_006] 后端允许显示:`, backendAllowed);
				console.log(`${timestamp}-INFO-[dp-userinfo][check_user_id_permission_007] 最终是否显示ID:`, finalAllowed);
				
				return finalAllowed;
			},
			
			// 2025-01-03 新增：处理金币项目跳转的通用方法
			gotoMoneyItem: function(e) {
				// 获取点击的元素的自定义属性
				const moneyType = e.currentTarget.dataset.moneyType;
				const defaultUrl = e.currentTarget.dataset.defaultUrl;
				
				// 根据金币类型获取自定义链接
				let customUrl = '';
				switch(moneyType) {
					case 'money':
						customUrl = this.params.money_hrefurl;
						break;
					case 'commission':
						customUrl = this.params.commission_hrefurl;
						break;
					case 'score':
						customUrl = this.params.score_hrefurl;
						break;
					case 'coupon':
						customUrl = this.params.coupon_hrefurl;
						break;
					case 'credit':
						customUrl = this.params.credit_hrefurl;
						break;
					case 'work':
						customUrl = this.params.work_hrefurl;
						break;
					case 'invite':
						customUrl = this.params.invite_hrefurl;
						break;
					case 'xianjinquan':
						customUrl = this.params.xianjinquan_hrefurl;
						break;
					case 'huangjifen':
						customUrl = this.params.huangjifen_hrefurl;
						break;
					case 'gongxianzhi':
						customUrl = this.params.gongxianzhi_hrefurl;
						break;
					case 'shouyichi':
						customUrl = this.params.shouyichi_hrefurl;
						break;
					case 'peizi':
						customUrl = this.params.peizi_hrefurl;
						break;
					default:
						customUrl = '';
				}
				
				// 优先使用自定义链接，如果为空则使用默认链接
				const targetUrl = customUrl && customUrl.trim() !== '' ? customUrl : defaultUrl;
				
				console.log('金币跳转:', {
					moneyType: moneyType,
					customUrl: customUrl,
					defaultUrl: defaultUrl,
					targetUrl: targetUrl
				});
				
				// 执行跳转
				if (targetUrl) {
					app.goto(targetUrl);
				}
			},
			
			// 2025-01-03 23:12:33,001-INFO-[dp-userinfo][check_referral_exists_001] 检查是否存在真实推荐人信息的方法
			checkReferralExists: function() {
				const timestamp = new Date().toLocaleString('zh-CN');
				console.log(`${timestamp}-INFO-[dp-userinfo][check_referral_exists_002] 检查推荐人信息是否存在`);
				
				// 检查推荐人姓名是否存在且不为空
				const hasReferralName = this.data.userinfo && this.data.userinfo.referral_name && this.data.userinfo.referral_name.trim() !== '';
				
				// 检查推荐人详细信息是否存在
				const hasReferralDetail = this.data.userinfo && this.data.userinfo.referral_detail && 
					(this.data.userinfo.referral_detail.nickname || this.data.userinfo.referral_detail.weixin || this.data.userinfo.referral_detail.tel);
				
				// 只要有推荐人姓名或详细信息中的任一项，就认为存在推荐人
				const hasReferral = hasReferralName || hasReferralDetail;
				
				console.log(`${timestamp}-INFO-[dp-userinfo][check_referral_exists_003] 推荐人姓名存在:`, hasReferralName);
				console.log(`${timestamp}-INFO-[dp-userinfo][check_referral_exists_004] 推荐人详细信息存在:`, hasReferralDetail);
				console.log(`${timestamp}-INFO-[dp-userinfo][check_referral_exists_005] 最终判断是否有推荐人:`, hasReferral);
				
				return hasReferral;
			},
			
			// 2025-01-03 23:12:33,002-INFO-[dp-userinfo][should_show_referral_001] 判断是否应该显示推荐人信息模块
			shouldShowReferral: function() {
				// 同时检查前端配置和是否存在真实推荐人
				const configAllowed = this.params.referralshow == 1;
				const hasReferral = this.checkReferralExists();
				const shouldShow = configAllowed && hasReferral;
				
				const timestamp = new Date().toLocaleString('zh-CN');
				console.log(`${timestamp}-INFO-[dp-userinfo][should_show_referral_002] 前端配置允许显示:`, configAllowed);
				console.log(`${timestamp}-INFO-[dp-userinfo][should_show_referral_003] 存在推荐人信息:`, hasReferral);
				console.log(`${timestamp}-INFO-[dp-userinfo][should_show_referral_004] 最终是否显示推荐人模块:`, shouldShow);
				
				return shouldShow;
			},
			
			openLevelup:function(e){
				var levelid = e.currentTarget.dataset.levelid
				if(parseInt(this.params.levelclick) !== 0){
					app.goto('/pagesExa/my/levelinfo?id='+levelid)
				}
			},
			opencard:function(e){
				var cardId = e.currentTarget.dataset.card_id
				var code = e.currentTarget.dataset.card_code
				if(app.globalData.platform == 'mp') {
					var jweixin = require('jweixin-module');
					jweixin.openCard({
						cardList: [{
							cardId: cardId,
							code: code
						}],
						success:function(res) { }
					})
				} else {
					wx.openCard({
						cardList: [{
							cardId: cardId,
							code: code
						}],
						success:function(res) { }
					})
				}
				
			},
			//领取微信会员卡
			addmembercard:function(e){
				var cardId = e.currentTarget.dataset.card_id
				app.post('ApiCoupon/getmembercardparam',{card_id:cardId},function(res){
					if(res.status==0){
						app.alert(res.msg);
						return;
					}
					wx.navigateToMiniProgram({
						appId: 'wxeb490c6f9b154ef9', // 固定为此appid，不可改动
						extraData: res.extraData, // 包括encrypt_card_id outer_str biz三个字段，须从step3中获得的链接中获取参数
						success: function() {},
						fail: function() {},
						complete: function() {}
					})
				})
			},
			weixinlogin:function(){
				var that = this;
				var app = getApp();
				
				// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][weixinlogin_001] 检查用户是否已绑定微信
				if(app.globalData.platform == 'wx' || app.globalData.platform == 'mp'){
					// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][weixinlogin_002] 检查当前用户是否已绑定微信
					if(this.data.userinfo && this.data.userinfo.wxopenid) {
						// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][weixinlogin_003] 用户已绑定微信，跳转到个人中心
						app.success('您已绑定微信');
						app.goto('/pages/my/usercenter');
						return;
					}
					
					// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][weixinlogin_004] 用户未绑定微信，询问是否绑定
					uni.showModal({
						title: '绑定微信',
						content: '检测到您还未绑定微信，是否将微信信息绑定到当前账号？',
						confirmText: '绑定',
						cancelText: '取消',
						success: function(res) {
							if (res.confirm) {
								// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][weixinlogin_005] 用户确认绑定，执行绑定逻辑
								that.bindWeixinToCurrentUser();
							} else {
								// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][weixinlogin_006] 用户取消绑定
								console.log('用户取消绑定微信');
							}
						}
					});
				}
			},
			
			// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_001] 新增绑定微信到当前用户的方法
			bindWeixinToCurrentUser: function() {
				var that = this;
				var app = getApp();
				
				if(app.globalData.platform == 'wx') {
					// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_002] 微信小程序绑定逻辑
					wx.getUserProfile({
						lang: 'zh_CN',
						desc: '用于绑定微信到当前账号',
						success: function(res2) {
							console.log('2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_003] 获取微信用户信息成功:', res2);
							var userinfo = res2.userInfo;
							wx.login({
								success(res1) {
									var code = res1.code;
									console.log('2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_004] 微信登录成功，code:', code);
									
									// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_005] 调用绑定接口
									app.post('ApiIndex/wxRegister', {
										code: code,
										userinfo: userinfo,
										tel: that.data.userinfo.tel,  // 传递当前用户的手机号，用于绑定
										bind_existing: 1  // 标识这是绑定操作
									}, function(res) {
										console.log('2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_006] 绑定结果:', res);
										if (res.status == 1) {
											app.success('微信绑定成功');
											// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_007] 绑定成功，刷新页面数据
											var pages = getCurrentPages();
											var currentPage = pages[pages.length - 1];
											currentPage.$vm.getdata();
										} else {
											app.error(res.msg);
										}
									});
								},
								fail: function(res1) {
									console.log('2025-01-03 22:55:53,565-ERROR-[dp-userinfo][bind_weixin_to_current_user_008] 微信登录失败:', res1);
									app.error('微信登录失败');
								}
							});
						},
						fail: function(res2) {
							console.log('2025-01-03 22:55:53,565-ERROR-[dp-userinfo][bind_weixin_to_current_user_009] 获取微信用户信息失败:', res2);
							if (res2.errMsg == 'getUserProfile:fail auth deny') {
								app.error('取消授权');
							} else {
								app.error('获取微信信息失败');
							}
						}
					});
				} else if(app.globalData.platform == 'mp') {
					// 2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_010] 微信公众号绑定逻辑
					var frompage = encodeURIComponent(app._fullurl());
					var bindUrl = app.globalData.pre_url + '/index.php?s=ApiIndex/shouquan2&aid=' + app.globalData.aid + 
						'&session_id=' + app.globalData.session_id + '&pid=' + app.globalData.pid + 
						'&tel=' + that.data.userinfo.tel + '&bind_existing=1' + '&frompage=' + frompage;
					
					console.log('2025-01-03 22:55:53,565-INFO-[dp-userinfo][bind_weixin_to_current_user_011] 跳转到绑定页面:', bindUrl);
					location.href = bindUrl;
				}
			},
			// 新增方法：查看推荐人详细信息
			viewReferralInfo: function() {
				// 弹窗显示推荐人详细联系方式
				let referralInfo = this.data.userinfo.referral_detail || {};
				let content = '推荐人：' + (this.data.userinfo.referral_name || '示例推荐人');
				
				if (referralInfo.weixin) {
					content += '\n微信号：' + referralInfo.weixin;
				}
				
				if (referralInfo.tel) {
					content += '\n手机号：' + referralInfo.tel;
				}
				
				wx.showModal({
					title: '推荐人信息',
					content: content,
					showCancel: false,
					confirmText: '确定'
				});
			},
			
			// 显示团队信息弹窗
			showTeamInfoPopup: function() {
				// 调用查看推荐人信息的方法
				this.viewReferralInfo(); 
			},
			
			// 处理跳转链接
			handleLink: function(url, name) {
				if (url) {
					app.goto(url);
				} else {
					// 默认跳转到客服页面
					app.goto('/pagesExa/my/contact');
				}
			},
			
			copy: function(e) {
				var text = e.currentTarget.dataset.text;
				uni.setClipboardData({
					data: text,
					success: function() {
						uni.showToast({
							title: '已复制到剪贴板',
							icon: 'none'
						});
					}
				});
			},
			
			goto: function(e) {
				var url = e.currentTarget.dataset.url;
				app.goto(url);
			},
			// 新增：处理会员卡升级按钮点击事件
			handleCardActivation: function() {
				if (this.data.userlevel && this.data.userlevel.card_activate_url) {
					app.goto(this.data.userlevel.card_activate_url);
				} else {
					app.goto('/pagesExa/my/levelup');
				}
			},
			getVipCardBackground: function() {
				// 如果没有主封面图片，使用纯色渐变作为背景
				if (!this.data.userlevel || !this.data.userlevel.card_main_cover) {
					// 只有在设置了主色调时才使用自定义颜色
					if (this.data.userlevel && this.data.userlevel.card_primary_color && this.data.userlevel.card_primary_color.trim() !== '') {
						return `linear-gradient(135deg, ${this.data.userlevel.card_primary_color}, ${this.data.userlevel.card_secondary_color || '#f0f0f0'})`;
					}
					// 没有设置主色调时使用默认渐变
					return 'linear-gradient(135deg,rgb(255, 255, 255), #F4E5A7)';
				}
				// 有主封面图片时，背景设为透明，让图片显示
				return 'transparent';
			},
			getVipCardColorOverlay: function() {
				// 只有在有主封面图片且设置了自定义主色调时才显示遮罩层
				if (this.data.userlevel && 
					this.data.userlevel.card_main_cover && 
					this.data.userlevel.card_primary_color && 
					this.data.userlevel.card_primary_color.trim() !== '') {
					const secondaryColor = (this.data.userlevel.card_secondary_color && this.data.userlevel.card_secondary_color.trim() !== '') 
						? this.data.userlevel.card_secondary_color 
						: '#f0f0f0';
					return `linear-gradient(135deg, ${this.data.userlevel.card_primary_color}CC, ${secondaryColor}CC)`;
				}
				// 没有主封面图片或没有设置自定义颜色时，不显示遮罩层
				return 'transparent';
			},
			getVipLevelNameColor: function() {
				// 优先使用自定义的等级名称颜色
				if (this.data.userlevel && this.data.userlevel.card_level_name_color && this.data.userlevel.card_level_name_color.trim() !== '') {
					return this.data.userlevel.card_level_name_color;
				}
				// 没有设置自定义颜色时，使用默认白色文字
				return '#ffffff';
			},
			getVipDescriptionColor: function() {
				// 优先使用自定义的描述文字颜色
				if (this.data.userlevel && this.data.userlevel.card_description_color && this.data.userlevel.card_description_color.trim() !== '') {
					return this.data.userlevel.card_description_color;
				}
				// 没有设置自定义颜色时，使用默认白色文字，透明度稍低
				return 'rgba(255, 255, 255, 0.9)';
			},
			getButtonColor: function() {
				if (this.data.userlevel && this.data.userlevel.card_upgrade_btn_color) {
					return this.data.userlevel.card_upgrade_btn_color;
				}
				return '#000000';
			},
			getButtonBorderColor: function() {
				if (this.data.userlevel && this.data.userlevel.card_upgrade_btn_border_color) {
					return this.data.userlevel.card_upgrade_btn_border_color;
				}
				return '#333333';
			},
			getButtonTextColor: function() {
				const btnColor = this.getButtonColor();
				if (btnColor === '#000000' || btnColor.toLowerCase().includes('dark') || 
					(btnColor.includes('#') && parseInt(btnColor.replace('#', ''), 16) < 8388608)) {
					return '#ffffff'; // 深色背景用白色文字
				}
				return '#333333'; // 浅色背景用深色文字
			},
			getActivateButtonText: function() {
				if (this.data.userlevel && this.data.userlevel.card_activate_text) {
					return this.data.userlevel.card_activate_text;
				}
				return '立即升级';
			}
		}
	}
</script>
<style>
	
 .qhsf{
	     position: absolute;
	     right: 20px;
	     top: 70px;
	     color: #fff;
	     border: 0.5px solid;
	     padding: 3px 10px;
	     border-radius: 20px;
 }	
	
.dp-userinfo{position:relative}
.dp-userinfo .banner{width:100%;margin-top:120rpx;border-radius:16rpx;background:#fff;padding:0 20rpx 10rpx;color:#333;position:relative;}
.dp-userinfo .banner .info{display:flex;align-items:flex-end}
.dp-userinfo .banner .info .f1{display:flex;flex-direction:column;}
.dp-userinfo .banner .headimg{ margin-top:-60rpx;width:148rpx;height:148rpx;border-radius:50%;margin-right:20rpx;border:3px solid #eee;}
.dp-userinfo .banner .info{margin-left:20rpx;display:flex;flex:auto;}
.dp-userinfo .banner .info .nickname{min-width:140rpx;max-width:460rpx;text-align:center;height:80rpx;line-height:80rpx;font-size:34rpx;font-weight:bold;max-width: 300rpx;overflow: hidden;white-space: nowrap;}
.dp-userinfo .banner .getbtn{ width:120rpx;height:44rpx;padding:0 3px;line-height:44rpx;font-size: 24rpx;background: #09BB07;color:#fff;position: absolute;top:76rpx;left:10rpx;}
.dp-userinfo .banner .user-level{margin-left:5px;color:#b48b36;background-color:#ffefd4;margin-top:2px;width:auto;height:36rpx;border-radius:18rpx;padding:0 20rpx;display:flex;align-items:center}
.dp-userinfo .banner .user-level .level-img{width:32rpx;height:32rpx;margin-right:3px;margin-left:-14rpx;border-radius:50%;}
.dp-userinfo .banner .user-level .level-name{font-size:24rpx;}
.dp-userinfo .banner .user-level image{border-radius:50%;}
.dp-userinfo .banner .usercard{position:absolute;right:32rpx;top:28rpx;width:160rpx;height:60rpx;text-align:center;border:1px solid #FFB2B2;border-radius:8rpx;color:#FC4343;font-size:24rpx;font-weight:bold;display:flex;align-items:center;justify-content:center}
.dp-userinfo .banner .usercard .img{width:30rpx;height:30rpx;margin-right:8rpx;padding-bottom:4rpx}

.dp-userinfo .custom_field{display:flex;width:100%;align-items:center;padding:16rpx 8rpx;background:#fff}
.dp-userinfo .custom_field .item{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center}
.dp-userinfo .custom_field .item .t1{color:#666;font-size:26rpx}
.dp-userinfo .custom_field .item .t2{color:#111;font-weight:bold;font-size:36rpx;}

.dp-userinfo .userset{width:54rpx;height:54rpx;padding:10rpx;position:absolute;top:40rpx;right:30rpx}
.dp-userinfo .userset .img{width:100%;height:100%}

.dp-userinfo2{width:100%;height:490rpx;display:flex;flex-direction:column;position:relative}
.dp-userinfo2 .info{display:flex;margin-top:60rpx;margin-left:40rpx}
.dp-userinfo2 .info .headimg{width:108rpx;height:108rpx;background:#fff;border:3rpx solid rgba(255,255,255,0.7);border-radius:50%}
.dp-userinfo2 .info .nickname{margin-left:20rpx;display:flex;flex-direction:column;justify-content:center}
.dp-userinfo2 .info .nickname .nick{font-size:36rpx;font-weight:bold;color:#fff;height:60rpx;line-height:60rpx;max-width:400rpx;overflow:hidden;margin-right:10rpx}
.dp-userinfo2 .info .nickname .desc{font-size:24rpx;color:rgba(255,255,255,0.6);height:40rpx;line-height:40rpx}
.dp-userinfo2 .info .nickname .user-level{color:rgba(255,255,255,0.6);margin-top:2px;width:auto;height:36rpx;border-radius:18rpx;padding:0 20rpx;display:flex;align-items:center}
.dp-userinfo2 .info .nickname .user-level .level-img{width:32rpx;height:32rpx;margin-right:3px;margin-left:-14rpx;border-radius:50%;}
.dp-userinfo2 .info .nickname .user-level .level-name{font-size:24rpx;}
.dp-userinfo2 .info .nickname .usermid{color:rgba(255,255,255,0.8);font-size:24rpx;}

.dp-userinfo2 .custom_field{display:flex;width:100%;align-items:center;padding:16rpx 8rpx;margin-top:20rpx}
.dp-userinfo2 .custom_field .item{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center}
.dp-userinfo2 .custom_field .item .t1{color:rgba(255,255,255,0.6);font-size:24rpx;margin-top:10rpx}
.dp-userinfo2 .custom_field .item .t2{color:#FFFFFF;font-weight:bold;font-size:32rpx;}

.dp-userinfo2 .usercard{width:154rpx;height:54rpx;background:#fff;border-radius: 27rpx 0 0 27rpx;display:flex;align-items:center;padding-left:20rpx;position:absolute;top:140rpx;right:0}
.dp-userinfo2 .usercard .img{width:32rpx;height:32rpx;margin-right:6rpx}
.dp-userinfo2 .usercard .txt{color:#F4504C;font-size:24rpx;font-weight:bold}
.dp-userinfo2 .userset{width:54rpx;height:54rpx;padding:10rpx;position:absolute;top:40rpx;right:30rpx}
.dp-userinfo2 .userset .img{width:100%;height:100%}

.dp-userinfo-order{background:#fff;padding:0 20rpx;border-radius:16rpx;position:relative}
.dp-userinfo-order .head{ display:flex;align-items:center;width:100%;padding:16rpx 0;}
.dp-userinfo-order .head .f1{flex:auto;font-size:30rpx;padding-left:16rpx;font-weight:bold;color:#333}
.dp-userinfo-order .head .f2{ display:flex;align-items:center;color:#999;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}
.dp-userinfo-order .head .f2 .image{ width:30rpx;height:30rpx;}
.dp-userinfo-order .head .t3{ width:40rpx; height:40rpx;}
.dp-userinfo-order .content{ display:flex;width:100%;padding:0 0 10rpx 0;align-items:center;font-size:24rpx}
.dp-userinfo-order .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}
.dp-userinfo-order .content .item .image{ width:50rpx;height:50rpx}
.dp-userinfo-order .content .item .iconfont{font-size:60rpx}
.dp-userinfo-order .content .item .t3{ padding-top:3px}
.dp-userinfo-order .content .item .t2{display:flex;align-items:center;justify-content:center;background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:20rpx;width:35rpx;height:35rpx;text-align:center;}

.parent {padding:20rpx;border-radius:16rpx;justify-content: center;display:flex;align-items:center;font-size:24rpx; margin-bottom: 10rpx;}
.parent .parentimg{ width: 100rpx; height:100rpx; border-radius: 50%; z-index: 10;}
.parent .parentimg-tag { color: #fff; text-align: center; margin-top: -20rpx; z-index: 11; border-radius: 12rpx; padding: 2rpx 4rpx; position: relative; bottom: 2rpx;}
.parent .copyicon {width: 26rpx; height: 26rpx; margin-left: 8rpx; position: relative; top: 4rpx;}
.parent .f1 { position: relative;}
.parent .f2 { padding: 0 30rpx;}
.parent .handle-img {width: 60rpx; height: 60rpx;}
.parent .btn-box { padding: 20rpx 0;}
.parent button { padding: 0 40rpx; color: #fff; border-radius:20rpx; line-height: 60rpx;}

.qiuzhi-renzheng{color:#eeda65;background:#3a3a3a;border-radius: 8rpx;padding: 4rpx 8rpx;margin: 0 4rpx;font-size: 22rpx;}
.qiuzhi-qianyue{color:#eeda65;background:#3a3a3a;border-radius: 8rpx;padding: 4rpx 8rpx;margin: 0 4rpx;font-size: 22rpx;}
.zhaopin-renzheng{color:#eeda65;background:#3a3a3a;border-radius: 8rpx;padding: 4rpx 8rpx;margin: 0 4rpx;font-size: 22rpx;}

.stat-field {
	background: rgba(255,255,255,0.9);
	border-radius: 12rpx;
	margin: 20rpx 0;
	padding: 20rpx 0;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.stat-item {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}
.stat-item .t2 {
	font-size: 40rpx;
	font-weight: bold;
	line-height: 1.2;
	margin-bottom: 8rpx;
}
.stat-item .t1 {
	font-size: 26rpx;
	color: #666;
}
.stat-line {
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 2rpx;
	height: 40rpx;
	opacity: 0.3;
}

/* 新增推荐人模块样式 */
.dp-userinfo-referral {
	background: #fff;
	padding: 0;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
	overflow: hidden;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	transition: all 0.3s ease;
}

.referral-info {
	padding: 28rpx 34rpx;
	display: flex;
	align-items: center;
	position: relative;
}

.referral-avatar {
	position: relative;
	margin-right: 24rpx;
}

.avatar-img {
	width: 110rpx;
	height: 110rpx;
	border-radius: 50%;
	border: 4rpx solid rgba(255,255,255,0.8);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
}

.avatar-img:active {
	transform: scale(0.96);
}

.referral-tag {
	color: #fff;
	text-align: center;
	font-size: 20rpx;
	margin-top: -20rpx;
	z-index: 11;
	border-radius: 12rpx;
	padding: 4rpx 12rpx;
	position: relative;
	bottom: 6rpx;
	left: 50%;
	transform: translateX(-50%);
	white-space: nowrap;
	box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.15);
}

.referral-content {
	flex: 1;
	padding: 6rpx 0;
}

.referral-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.referral-action {
	display: flex;
	align-items: center;
	background: rgba(0,0,0,0.03);
	padding: 10rpx 20rpx;
	border-radius: 30rpx;
	transition: all 0.3s ease;
}

.referral-action:active {
	background: rgba(0,0,0,0.08);
}

.action-text {
	font-size: 26rpx;
	color: #555;
	margin-right: 2rpx;
}

.action-icon {
	width: 24rpx;
	height: 24rpx;
	margin-left: 6rpx;
	opacity: 0.7;
}

.referral-internal-actions {
	display: flex;
	padding: 24rpx 20rpx;
	border-top: 1px solid rgba(0,0,0,0.05);
	justify-content: space-around;
}

.action-item {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	flex: 1;
	height: 60rpx;
	position: relative;
}

.action-item:not(:last-child):after {
	content: '';
	position: absolute;
	right: 0;
	top: 20%;
	height: 60%;
	width: 1px;
	background: rgba(0,0,0,0.05);
}

.action-item:active {
	background: rgba(0,0,0,0.02);
}

.action-item-icon {
	width: 36rpx;
	height: 36rpx;
	margin-right: 10rpx;
}

.action-item-text {
	font-size: 28rpx;
	color: #444;
}


.vipcard-shine {
	position: absolute;
	top: 0;
	left: -150%;
	width: 80%;
	height: 100%;
	background: linear-gradient(90deg, 
		rgba(255, 255, 255, 0) 0%, 
		rgba(255, 255, 255, 0.05) 50%, 
		rgba(255, 255, 255, 0) 100%);
	transform: skewX(-25deg);
	animation: shine 5s infinite;
	pointer-events: none;
	z-index: 2;
}

@keyframes shine {
	0% {
		left: -150%;
	}
	50% {
		left: 150%;
	}
	100% {
		left: 150%;
	}
}

.vipcard-sparkles-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 3; /* 在 vipcard-shine 之上 */
	overflow: hidden; /* 确保闪光点不会超出卡片范围 */
}

.vipcard-sparkle {
	position: absolute;
	width: 6rpx;
	height: 6rpx;
	background-color: #F4E5A7; /* 淡金色闪光点 */
	border-radius: 50%;
	box-shadow: 0 0 8rpx #F4E5A7, 0 0 12rpx #D4AF37;
	opacity: 0;
	animation: sparkle-animation 2s infinite;
}

/* 定义闪光动画 */
@keyframes sparkle-animation {
	0%, 100% { opacity: 0; transform: scale(0.5); }
	50% { opacity: 1; transform: scale(1.2); }
	70% { opacity: 0.8; transform: scale(1); }
}

/* 个别闪光点的位置和动画延迟 */
.sparkle1 { top: 20%; left: 15%; animation-delay: 0s; }
.sparkle2 { top: 40%; left: 80%; animation-delay: 0.7s; width: 8rpx; height: 8rpx; }
.sparkle3 { top: 75%; left: 25%; animation-delay: 1.2s; width: 5rpx; height: 5rpx; }
.sparkle4 { top: 10%; left: 60%; animation-delay: 0.3s; }
.sparkle5 { top: 85%; left: 50%; animation-delay: 1.8s; width: 7rpx; height: 7rpx; }

.vipcard-pattern {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0.03;
	background-image: linear-gradient(45deg, #D4AF37 25%, transparent 25%), 
		linear-gradient(-45deg, #D4AF37 25%, transparent 25%), 
		linear-gradient(45deg, transparent 75%, #D4AF37 75%), 
		linear-gradient(-45deg, transparent 75%, #D4AF37 75%);
	background-size: 20rpx 20rpx;
	background-position: 0 0, 0 10rpx, 10rpx -10rpx, -10rpx 0px;
	pointer-events: none;
	z-index: 0;
}

.vipcard-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	z-index: 1;
	gap: 20rpx;
	min-height: 80rpx;
}

.vipcard-left {
	flex: 1;
}

.vip-title-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.vip-title {
	font-size: 28rpx;
	font-weight: bold;
	background: linear-gradient(90deg, #D4AF37 0%, #F4E5A7 50%, #D4AF37 100%);
	-webkit-background-clip: text;
	color: transparent;
	margin-right: 15rpx;
	position: relative;
}

.vip-title::after {
	content: "";
	position: absolute;
	bottom: -4rpx;
	left: 0;
	width: 60%;
	height: 1px;
	background: linear-gradient(90deg, #D4AF37, transparent);
}

.vip-desc {
	display: none;
}

.vip-level {
	display: flex;
	align-items: center;
}

.level-text {
	font-size: 24rpx;
	font-weight: bold;
	color: #D4AF37;
}

.vip-progress-row {
	display: flex;
	align-items: center;
	font-size: 22rpx;
}

.vip-progress-label {
	color: rgba(255, 255, 255, 0.6);
	margin-right: 10rpx;
	font-size: 22rpx;
}

.level-progress {
	width: 120rpx;
	height: 10rpx;
	background-color: rgba(255, 255, 255, 0.05);
	border-radius: 5rpx;
	overflow: hidden;
	margin: 0 10rpx;
	box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}

.level-progress-inner {
	height: 100%;
	background: linear-gradient(90deg, #D4AF37 0%, #F4E5A7 50%, #D4AF37 100%);
	border-radius: 5rpx;
	box-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
}

.vipcard-right {
	display: flex;
	align-items: center;
}

.vip-upgrade-btn {
	padding: 12rpx 32rpx;
	background: #333;
	border-radius: 30rpx;
	font-size: 26rpx;
	font-weight: bold;
	color: #fff;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.2);
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
	text-align: center;
	white-space: nowrap;
	border: 1px solid transparent;
}

.vip-upgrade-btn::after {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 50%;
	background: linear-gradient(180deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
	border-radius: 20rpx 20rpx 0 0;
}

.vip-badge {
	display: none;
}

.level-hint {
	font-size: 22rpx;
	color: rgba(255, 255, 255, 0.5);
}

.vipcard-decoration, .vipcard-decoration-2 {
	display: none;
}

/* 添加装饰元素 */
.dp-userinfo-vipcard::before {
	content: "";
	position: absolute;
	top: -30rpx;
	right: -30rpx;
	width: 100rpx;
	height: 100rpx;
	background: radial-gradient(circle, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0) 70%);
	border-radius: 50%;
	z-index: 0;
}

.dp-userinfo-vipcard::after {
	content: "";
	position: absolute;
	bottom: -40rpx;
	left: 40rpx;
	width: 120rpx;
	height: 120rpx;
	background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, rgba(212, 175, 55, 0) 70%);
	border-radius: 50%;
	z-index: 0;
}

/* Style 3 */
.dp-userinfo3 {
	background-size: 100% auto;
	background-repeat: no-repeat;
	border-radius: 16rpx;
	padding: 30rpx;
	position: relative;
	color: #333; /* Default text color, assuming white bg or light bgimg */
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
	/* background-color: #fff; Ensure this is set if no bgimg, handled by inline style */
}
.dp-userinfo3 .userset{
	width:54rpx;height:54rpx;padding:10rpx;position:absolute;top:20rpx;right:20rpx;z-index:5;
}
.dp-userinfo3 .userset .img{width:100%;height:100%}

.dp-userinfo3 .usercard.style3card {
	width:160rpx;height:60rpx;
	background:rgba(255,255,255,0.9);
	border:1px solid rgba(0,0,0,0.05);
	border-radius:30rpx;
	display:flex;align-items:center;justify-content:center;
	position:absolute;
	top: 20rpx; 
	right: 90rpx; /* Positioned to the left of settings icon */
	box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);
	z-index: 5;
}
.dp-userinfo3 .usercard.style3card .img{width:32rpx;height:32rpx;margin-right:8rpx;}
.dp-userinfo3 .usercard.style3card .txt{color:#F4504C;font-size:24rpx;font-weight:bold;}


.dp-userinfo3 .info-main {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	margin-top: 60rpx; /* Space for userset and usercard */
	margin-bottom: 30rpx;
	position: relative; 
}
.dp-userinfo3 .headimg {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	border: 6rpx solid #fff;
	box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
	margin-bottom: 20rpx;
}
.dp-userinfo3 .nickname {
	font-size: 38rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}
.dp-userinfo3 .userid {
	font-size: 24rpx;
	color: #888;
	margin-bottom: 10rpx;
}
.dp-userinfo3 .level-container {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-wrap: wrap;
	margin-bottom: 10rpx;
}
.dp-userinfo3 .user-level{
	color:#b48b36;background-color:#ffefd4; margin: 5rpx;
	height:36rpx;border-radius:18rpx;padding:0 15rpx;display:flex;align-items:center; font-size:22rpx;
}
.dp-userinfo3 .user-level .level-img{width:30rpx;height:30rpx;margin-right:5rpx;margin-left:-10rpx;border-radius:50%;}
.dp-userinfo3 .user-level .level-name{font-size:22rpx;}

.dp-userinfo3 .invite-code {
	font-size: 24rpx;
	color: #666;
	background-color: #f0f0f0;
	padding: 5rpx 15rpx;
	border-radius: 20rpx;
	margin-bottom: 15rpx;
	display: inline-block; /* To fit content */
}
.dp-userinfo3 .zhaopin-tags {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	margin-top: 10rpx;
}
.dp-userinfo3 .zhaopin-tag {
	color:#eeda65;background:#3a3a3a;border-radius: 8rpx;padding: 4rpx 8rpx;margin: 0 4rpx 8rpx;font-size: 20rpx;
}

.dp-userinfo3 .custom_field_grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(150rpx, 1fr)); /* Responsive grid */
	gap: 15rpx 10rpx; /* row-gap column-gap */
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
}
.dp-userinfo3 .custom_field_grid .item {
	display:flex;flex-direction:column;justify-content:center;align-items:center; text-align:center; padding: 10rpx 0;
}
.dp-userinfo3 .custom_field_grid .item .t1{color:#666;font-size:24rpx; margin-top: 5rpx;}
.dp-userinfo3 .custom_field_grid .item .t2{color:#333;font-weight:bold;font-size:32rpx;}

.dp-userinfo3 .custom_field_othermoney {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(150rpx, 1fr));
	gap: 15rpx 10rpx;
	padding:16rpx;
	background:#f9f9f9; 
	border-radius: 12rpx;
	margin-bottom: 20rpx;
}
.dp-userinfo3 .custom_field_othermoney .item{
	display:flex;flex-direction:column;justify-content:center;align-items:center; text-align:center; padding: 10rpx 0;
}
.dp-userinfo3 .custom_field_othermoney .item .t1{color:#666;font-size:24rpx; margin-top: 5rpx;}
.dp-userinfo3 .custom_field_othermoney .item .t2{color:#333;font-weight:bold;font-size:30rpx;}

.dp-userinfo3 .parent-info-style3 .parent{ /* Reuses .parent but allows specific overrides if needed */
	padding:20rpx; /* Standard parent padding */
}

/* Style 4 */
.dp-userinfo4 {
	border-radius: 16rpx;
	position: relative;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
	color: #333;
}
.dp-userinfo4 .top-bar-style4 {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding: 10rpx 0; /* Reduced padding as items are small */
	height: 60rpx; /* Explicit height */
}
.dp-userinfo4 .userset{
	width:48rpx;height:48rpx;padding:0; /* Remove default padding */
	margin-left: 20rpx; /* Space from card icon */
	display: flex; align-items: center; justify-content: center;
}
.dp-userinfo4 .userset .img{width:100%;height:100%}

.dp-userinfo4 .usercard.style4card {
	width:auto; /* Fit content */
	height:48rpx;
	padding: 0 20rpx; /* Horizontal padding */
	background:rgba(255,255,255,0.85);
	border:1px solid rgba(0,0,0,0.05);
	border-radius:24rpx;
	display:flex;align-items:center;justify-content:center;
	box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.08);
}
.dp-userinfo4 .usercard.style4card .img{width:28rpx;height:28rpx;margin-right:8rpx;}
.dp-userinfo4 .usercard.style4card .txt{color:#F4504C;font-size:22rpx;font-weight:bold;}

.dp-userinfo4 .user-details-style4 {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	margin-top: 10rpx; /* Add some space below top-bar */
}
.dp-userinfo4 .headimg-style4 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	border: 4rpx solid #fff;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
	margin-right: 25rpx;
}
.dp-userinfo4 .text-info-style4 {
	display: flex;
	flex-direction: column;
	justify-content: center;
	flex: 1;
}
.dp-userinfo4 .nickname-style4 {
	font-size: 34rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	display: flex;
	align-items: center;
}
.dp-userinfo4 .id-style4 {
	font-size: 22rpx;
	color: #888;
	margin-left: 10rpx;
}
.dp-userinfo4 .level-section-style4 {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	margin-bottom: 8rpx;
}
.dp-userinfo4 .user-level-style4 {
	background-color: #fffaeb; /* Lighter gold */
	color: #d4af37; /* Gold text */
	border: 1px solid #f0e0b0; /* Soft gold border */
	height: 32rpx;
	border-radius: 16rpx;
	padding: 0 12rpx;
	display: flex;
	align-items: center;
	font-size: 20rpx;
	margin-right: 10rpx;
	margin-bottom: 5rpx; /* For wrapping */
}
.dp-userinfo4 .user-level-style4 .level-img {
	width: 24rpx;
	height: 24rpx;
	margin-right: 6rpx;
	border-radius: 50%;
}
.dp-userinfo4 .user-level-style4 .level-name {
	font-size: 20rpx;
}
.dp-userinfo4 .invite-code-style4 {
	font-size: 22rpx;
	color: #777;
	background-color: #f5f5f5;
	padding: 4rpx 12rpx;
	border-radius: 16rpx;
	margin-bottom: 8rpx;
	align-self: flex-start; /* Align to start if wrapped */
}
.dp-userinfo4 .zhaopin-tags-style4 {
	display: flex;
	flex-wrap: wrap;
	margin-top: 5rpx;
}
.dp-userinfo4 .zhaopin-tags-style4 .zhaopin-tag { /* Reusing zhaopin-tag from global is fine if visual is same */
	color:#c8aa6d;background-color:rgba(58,58,58,0.1); border-radius: 8rpx;padding: 3rpx 7rpx;margin: 0 4rpx 5rpx;font-size: 18rpx; border: 1px solid rgba(58,58,58,0.2);
}

.dp-userinfo4 .stats-grid-style4,
.dp-userinfo4 .othermoney-section-style4 {
	display: grid;
	grid-template-columns: repeat(3, 1fr); /* 3 items per row */
	gap: 10rpx;
	padding: 20rpx;
	background-color: rgba(249,249,249,0.7);
	border-radius: 12rpx;
	margin-top: 25rpx;
}
.dp-userinfo4 .stats-grid-style4 .item,
.dp-userinfo4 .othermoney-section-style4 .item {
	display:flex;flex-direction:column;justify-content:center;align-items:center; text-align:center; padding: 15rpx 5rpx;
}
.dp-userinfo4 .stats-grid-style4 .item .t1,
.dp-userinfo4 .othermoney-section-style4 .item .t1 {
	color:#555;font-size:22rpx; margin-top: 6rpx;
}
.dp-userinfo4 .stats-grid-style4 .item .t2,
.dp-userinfo4 .othermoney-section-style4 .item .t2 {
	color:#333;font-weight:bold;font-size:30rpx;
}

.dp-userinfo4 .parent-info-style4 {
	margin-top: 25rpx;
}
.dp-userinfo4 .parent-info-style4 .parent { /* Reuses .parent, specific styles if needed */
	padding: 15rpx; /* Slightly less padding */
	margin-bottom: 0; /* No margin if it's the last element in card */
}

/* Style 5 - Simplified style without assets display */
.dp-userinfo5 {
	border-radius: 16rpx;
	position: relative;
	box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.08);
	color: #333;
	overflow: hidden;
}

.dp-userinfo5 .top-actions-style5 {
	display: flex;
	justify-content: flex-end;
	padding: 15rpx;
	align-items: center;
	position: absolute;
	top: 0;
	right: 0;
	z-index: 10;
}

.dp-userinfo5 .userset {
	width: 48rpx;
	height: 48rpx;
	margin-left: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255,255,255,0.8);
	border-radius: 50%;
	box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);
}

.dp-userinfo5 .userset .img {
	width: 30rpx;
	height: 30rpx;
}

.dp-userinfo5 .usercard.style5card {
	background: rgba(255,255,255,0.8);
	padding: 6rpx 15rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);
}

.dp-userinfo5 .style5card .img {
	width: 28rpx;
	height: 28rpx;
	margin-right: 6rpx;
}

.dp-userinfo5 .style5card .txt {
	color: #F4504C;
	font-size: 22rpx;
	font-weight: bold;
}

.dp-userinfo5 .profile-main-style5 {
	padding: 30rpx;
	display: flex;
	align-items: center;
	position: relative;
}

.dp-userinfo5 .avatar-style5 {
	width: 140rpx;
	height: 140rpx;
	border-radius: 50%;
	border: 5rpx solid #fff;
	box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
	margin-right: 25rpx;
}

.dp-userinfo5 .profile-content-style5 {
	flex: 1;
}

.dp-userinfo5 .name-container-style5 {
	margin-bottom: 15rpx;
}

.dp-userinfo5 .name-id-wrapper {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.dp-userinfo5 .name-style5 {
	font-size: 36rpx;
	font-weight: bold;
	margin-right: 10rpx;
}

.dp-userinfo5 .id-style5 {
	font-size: 24rpx;
	color: #666;
	background-color: rgba(0,0,0,0.05);
	padding: 2rpx 10rpx;
	border-radius: 10rpx;
	display: inline-block;
}

.dp-userinfo5 .badges-container-style5 {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 15rpx;
}

.dp-userinfo5 .user-level-style5 {
	display: flex;
	align-items: center;
	background: linear-gradient(45deg, #f8e9c6, #ffffff);
	border: 1px solid #f0e0b0;
	padding: 4rpx 15rpx;
	border-radius: 16rpx;
	margin-right: 10rpx;
	margin-bottom: 10rpx;
}

.dp-userinfo5 .user-level-style5 .level-img {
	width: 28rpx;
	height: 28rpx;
	margin-right: 6rpx;
	border-radius: 50%;
}

.dp-userinfo5 .user-level-style5 .level-name {
	font-size: 22rpx;
	color: #b48b36;
}

.dp-userinfo5 .cert-badges-style5 {
	display: flex;
	flex-wrap: wrap;
}

.dp-userinfo5 .cert-badge {
	font-size: 20rpx;
	color: #d4af37;
	background: rgba(212,175,55,0.1);
	border: 1px solid rgba(212,175,55,0.3);
	padding: 2rpx 12rpx;
	border-radius: 10rpx;
	margin-right: 10rpx;
	margin-bottom: 10rpx;
}

.dp-userinfo5 .invite-code-style5 {
/* 	margin-top: 10rpx; */
	background: rgba(249,249,249,0.6);
	padding: 6rpx 15rpx;
	border-radius: 20rpx;
	display: inline-flex;
	align-items: center;
}

.dp-userinfo5 .invite-label {
	font-size: 22rpx;
	color: #666;
}

.dp-userinfo5 .invite-value {
	font-size: 22rpx;
	color: #333;
}

.dp-userinfo5 .parent-style5 {
	margin-top: 20rpx;
	padding: 20rpx;
	background: rgba(249,249,249,0.6);
	border-radius: 12rpx;
}

.dp-userinfo5 .parent-card-style5 {
	display: flex;
	align-items: center;
}

.dp-userinfo5 .parent-header-style5 {
	position: relative;
	margin-right: 20rpx;
}

.dp-userinfo5 .parent-avatar-style5 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 2rpx solid #fff;
}

.dp-userinfo5 .parent-tag-style5 {
	position: absolute;
	bottom: -5rpx;
	left: 50%;
	transform: translateX(-50%);
	font-size: 18rpx;
	color: #fff;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
	white-space: nowrap;
}

.dp-userinfo5 .parent-info-style5 {
	flex: 1;
}

.dp-userinfo5 .parent-name-style5 {
	font-size: 26rpx;
	font-weight: bold;
	margin-bottom: 4rpx;
}

.dp-userinfo5 .parent-contact-style5 {
	font-size: 22rpx;
	color: #666;
	display: flex;
	align-items: center;
}

.dp-userinfo5 .copy-icon-style5 {
	width: 22rpx;
	height: 22rpx;
	margin-left: 6rpx;
}

.dp-userinfo5 .parent-action-style5 {
	padding: 10rpx;
}

.dp-userinfo5 .call-icon-style5 {
	width: 48rpx;
	height: 48rpx;
}

/* 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_001] 简单上级信息显示区域 */
.parent-simple {
	padding: 10rpx 15rpx;
	border-radius: 8rpx;
	background: rgba(t('color1rgb'), 0.05);
	margin-top: 10rpx;
}

.parent-simple-content {
	display: flex;
	align-items: center;
}

.parent-label {
	font-size: 22rpx;
	color: #999;
	margin-right: 10rpx;
}

.parent-name {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

/* 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_002] Style2上级信息显示区域 */
.parent-simple-style2 {
	padding: 10rpx 15rpx;
	border-radius: 8rpx;
	background: rgba(t('color1rgb'), 0.05);
	margin-top: 10rpx;
}

.parent-simple-style2-content {
	display: flex;
	align-items: center;
}

.parent-label-style2 {
	font-size: 22rpx;
	color: rgba(255,255,255,0.7);
	margin-right: 10rpx;
}

.parent-name-style2 {
	font-size: 24rpx;
	color: rgba(255,255,255,0.9);
	font-weight: 500;
}

/* 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_003] Style3上级信息显示区域 */
.parent-simple-style3 {
	padding: 10rpx 15rpx;
	border-radius: 8rpx;
	background: rgba(t('color1rgb'), 0.05);
	margin-top: 10rpx;
}

.parent-simple-style3-content {
	display: flex;
	align-items: center;
}

.parent-label-style3 {
	font-size: 22rpx;
	color: #666;
	margin-right: 10rpx;
}

.parent-name-style3 {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

/* 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_004] Style4上级信息显示区域 */
.parent-simple-style4 {
	padding: 10rpx 15rpx;
	border-radius: 8rpx;
	background: rgba(t('color1rgb'), 0.05);
	margin-top: 10rpx;
}

.parent-simple-style4-content {
	display: flex;
	align-items: center;
}

.parent-label-style4 {
	font-size: 22rpx;
	color: #666;
	margin-right: 10rpx;
}

.parent-name-style4 {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

/* 2025-01-03 22:55:53,565-INFO-[dp-userinfo][render_parent_info_005] Style5上级信息显示区域 */
.parent-simple-style5 {
	padding: 10rpx 15rpx;
	border-radius: 8rpx;
	background: rgba(t('color1rgb'), 0.05);
	margin-top: 10rpx;
}

.parent-simple-style5-content {
	display: flex;
	align-items: center;
}

.parent-label-style5 {
	font-size: 22rpx;
	color: #666;
	margin-right: 10rpx;
}

.parent-name-style5 {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

/* VIP卡片新样式 */
.vipcard-main-cover {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border-radius: 16rpx;
	opacity: 1;
	z-index: 1;
}

.vipcard-color-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border-radius: 16rpx;
	opacity: 0.7;
	z-index: 2;
	mix-blend-mode: multiply;
}

.vipcard-sub-cover {
	position: absolute;
	width: 100rpx;
	max-height: 100rpx;
	right: 20rpx;
	top: 15rpx;
	z-index: 3;
	opacity: 0.9;
}

.vipcard-content {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 3;
	padding: 20rpx;
	gap: 20rpx;
	min-height: 140rpx;
	width: 100%;
	box-sizing: border-box;
}

.vipcard-left {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.vip-level-name {
	font-size: 32rpx;
	font-weight: bold;
	line-height: 1.2;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.vip-description {
	font-size: 24rpx;
	line-height: 1.3;
	opacity: 0.9;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.vip-next-level,
.vip-max-level {
	font-size: 22rpx;
	line-height: 1.2;
	opacity: 0.8;
}

.vipcard-right {
	flex-shrink: 0;
	margin-left: auto;
}

.vip-level-desc {
	font-size: 24rpx;
	color: #666;
}
</style>