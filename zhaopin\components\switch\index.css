.i-switch {
    width: 84rpx;
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 40rpx;
    border: 2rpx solid #ccc;
    background-color: #ccc;
    position: relative;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    transition: all 0.2s ease-in-out;
}

.i-switch-hide-input {
    display: none;
    opacity: 0;
}

.i-switch-inner {
    color: #fff;
    font-size: 24rpx;
    position: absolute;
    left: 50rpx;
    vertical-align: middle;
}

.i-switch:after {
    content: '';
    width: 32rpx;
    height: 32rpx;
    border-radius: 32rpx;
    background-color: #fff;
    position: absolute;
    cursor: pointer;
    transition: left 0.2s ease-in-out, width 0.2s ease-in-out;
    z-index: 888;
}

.i-switch-large {
    width: 120rpx;
}

.i-switch-large.i-switch-checked:after {
    left: 74rpx;
}

.i-switch-checked:after {
    left: 40rpx;
}

.i-switch-default:after {
    left: 2rpx;
    top: 2rpx;
}

.i-switch-checked {
    border-color: #0c8;
    background-color: #0c8;
}

.i-switch-checked .i-switch-inner {
    left: 10rpx;
}

.i-switch-checked:after {
    left: 46rpx;
    top: 2rpx;
}

.i-switch-disabled {
    background: #f3f3f3;
    border-color: #f3f3f3;
}

.i-switch-disabled:after {
    background: #ccc;
    cursor: not-allowed;
}

.i-switch-disabled .i-switch-inner {
    color: #ccc;
}
