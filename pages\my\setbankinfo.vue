<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit" @reset="formReset">
		<view class="form">
			<view class="form-item">
					<text class="label">开户行</text>
					<picker class="picker" mode="selector" name="bankname" value="0" :range="banklist" @change="bindBanknameChange">
						<view v-if="bankname">{{bankname}}</view>
						<view v-else>请选择开户行</view>
					</picker>
			</view>
			<view class="form-item">
					<text class="label">所属分支行</text>
					<input type="text" class="input" placeholder="请输入分支行" name="bankaddress" :value="userinfo.bankaddress" placeholder-style="color:#BBBBBB;font-size:28rpx"></input>
			</view>
			<view class="form-item">
					<text class="label">持卡人姓名</text>
					<input type="text" class="input" placeholder="请输入持卡人姓名" name="bankcarduser" :value="userinfo.bankcarduser" placeholder-style="color:#BBBBBB;font-size:28rpx"></input>
			</view>
			<view class="form-item">
					<text class="label">银行卡号</text>
					<input type="text" class="input" placeholder="请输入银行卡号" name="bankcardnum" :value="userinfo.bankcardnum" placeholder-style="color:#BBBBBB;font-size:28rpx"></input>
			</view>
		</view>
		<button class="set-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">保 存</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
		
      banklist: ['工商银行', '农业银行', '中国银行', '建设银行', '招商银行', '邮储银行', '交通银行', '浦发银行', '民生银行', '兴业银行', '平安银行', '中信银行', '华夏银行', '广发银行', '光大银行', '北京银行', '宁波银行'],
      bankname: '',
			userinfo:{},
			textset:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.isload = true
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/set', {}, function (data) {
				that.loading = false;
				that.userinfo = data.userinfo;
				that.bankname = data.userinfo.bankname;
				that.loaded();
			});
		},
    formSubmit: function (e) {
      var formdata = e.detail.value;
			var bankname = this.bankname
			var bankcarduser = formdata.bankcarduser
			var bankcardnum = formdata.bankcardnum
			var bankaddress = formdata.bankaddress
      if (bankname == '') {
        app.alert('请选择开户行');return;
      }
			app.showLoading('提交中');
      app.post("ApiMy/setfield", {bankname:bankname,bankaddress:bankaddress,bankcarduser:bankcarduser,bankcardnum:bankcardnum}, function (data) {
				app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function () {
            app.goback(true);
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    },
    bindBanknameChange: function (e) {
      this.bankname = this.banklist[e.detail.value];
    },
  }
};
</script>
<style>
.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;}
.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;line-height:98rpx;}
.form-item:last-child{border:0}
.form-item .label{color: #000;width:200rpx;}
.form-item .input{flex:1;color: #000;}
.form-item .picker{height: 60rpx;line-height:60rpx;margin-left: 0;flex:1;color: #000;}
.set-btn{width: 90%;margin:60rpx 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
</style>