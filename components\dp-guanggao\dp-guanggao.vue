<template>
<view class="advert flex-xy-center" v-if="guanggaopic && guanggaostatus=='1'">
	<view class="advert_module">
		<image :src="guanggaopic" @tap="goto" :data-url="guanggaourl" mode="widthFix" class="advert_poster" alt=""/>
		<view class="advert_close flex-xy-center">
			<image @tap="guanggaoClick" src="../../static/img/close2.png" alt=""/>
		</view>
	</view>
</view>
</template>
<script>
	var app = getApp();
	export default {
		data(){
			return {
				guanggaostatus:'1'
			}
		},
		props: {
			guanggaourl:"",
			guanggaopic:""
		},
		mounted:function(){
			
		},
		methods:{
			guanggaoClick(){
				this.guanggaostatus='0'
			}
		}
	}
</script>
<style>
	.advert{
		position: fixed;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
		z-index: 20;
		background: rgba(0, 0, 0, 0.7);
	}
	.advert_module{
		position: relative;
		width: 80%;
	}
	.advert_poster{
		position: relative;
		width: 85%;
		display: block;
		margin: 0 auto;
	}
	.advert_close{
		position: relative;
		height: 50rpx;
		width: 50rpx;
		margin: 0 auto;
		border-radius: 100rpx;
		border: 1px solid #fff;
		margin-top: 10rpx;
	}
	.advert_close image{
		height: 30rpx;
		width: 30rpx;
	}
</style>