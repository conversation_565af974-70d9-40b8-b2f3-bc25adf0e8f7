<template>
<view class="container">
	<view class="order-info">
		<view class="product-info">
			<image :src="orderInfo.propic" mode="aspectFill"></image>
			<view class="info">
				<text class="name">{{orderInfo.proname}}</text>
				<text class="price">￥{{orderInfo.totalprice}}</text>
			</view>
		</view>
	</view>

	<view class="comment-form">
		<view class="rating">
			<text class="label">服务评分：</text>
			<view class="stars">
				<view 
					v-for="n in 5" 
					:key="n" 
					class="star" 
					:class="{'active': n <= rating}"
					@tap="setRating(n)"
				>
					<text class="iconfont" :class="n <= rating ? 'iconxingshifill' : 'iconxingshi'"></text>
				</view>
			</view>
		</view>
		
		<view class="textarea-box">
			<textarea 
				v-model="content" 
				placeholder="请输入您的评价内容" 
				maxlength="200"
				class="textarea"
			></textarea>
			<text class="word-count">{{content.length}}/200</text>
		</view>
		
		<view class="upload-box">
			<view class="label">上传图片(最多3张)：</view>
			<view class="image-list">
				<view 
					v-for="(item, index) in imageList" 
					:key="index"
					class="image-item"
				>
					<image :src="item" mode="aspectFill"></image>
					<view class="delete-btn" @tap="deleteImage(index)">
						<text class="iconfont iconclose"></text>
					</view>
				</view>
				<view 
					class="upload-btn" 
					@tap="chooseImage" 
					v-if="imageList.length < 3"
				>
					<text class="iconfont iconjiahao"></text>
				</view>
			</view>
		</view>
	</view>

	<view class="footer">
		<button 
			class="submit-btn" 
			:disabled="!canSubmit"
			@tap="submitComment"
			:style="{backgroundColor: canSubmit ? t('color1') : '#cccccc'}"
		>提交评价</button>
	</view>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			orderId: '',
			orderInfo: {},
			rating: 5,
			content: '',
			imageList: []
		}
	},
	
	computed: {
		canSubmit() {
			return this.rating > 0 && this.content.trim().length > 0;
		}
	},
	
	onLoad(options) {
		this.orderId = options.id;
		this.getOrderInfo();
	},
	
	methods: {
		// 获取订单信息
		getOrderInfo() {
			var that = this;
			app.get('ApiYuyue/orderDetail', {
				id: that.orderId
			}, function(res) {
				if(res.status == 1) {
					that.orderInfo = res.data;
				} else {
					app.error(res.msg);
				}
			});
		},
		
		// 设置评分
		setRating(n) {
			this.rating = n;
		},
		
		// 选择图片
		chooseImage() {
			var that = this;
			uni.chooseImage({
				count: 3 - that.imageList.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: function(res) {
					var tempFilePaths = res.tempFilePaths;
					// 上传图片
					for(var i = 0; i < tempFilePaths.length; i++) {
						that.uploadImage(tempFilePaths[i]);
					}
				}
			});
		},
		
		// 上传图片
		uploadImage(filePath) {
			var that = this;
			app.showLoading('上传中');
			uni.uploadFile({
				url: app.globalData.siteroot + 'ApiYuyue/upload',
				filePath: filePath,
				name: 'file',
				formData: {
					'aid': app.globalData.aid
				},
				success: function(res) {
					app.showLoading(false);
					var data = JSON.parse(res.data);
					if(data.status == 1) {
						that.imageList.push(data.url);
					} else {
						app.error(data.msg);
					}
				},
				fail: function() {
					app.showLoading(false);
					app.error('上传失败');
				}
			});
		},
		
		// 删除图片
		deleteImage(index) {
			this.imageList.splice(index, 1);
		},
		
		// 提交评价
		submitComment() {
			var that = this;
			app.post('ApiYuyue/comment', {
				order_id: that.orderId,
				score: that.rating,
				content: that.content,
				pics: that.imageList.join(',')
			}, function(res) {
				if(res.status == 1) {
					app.alert('评价成功', function() {
						app.goto('/yuyue/orderlist');
					});
				} else {
					app.error(res.msg);
				}
			});
		}
	}
};
</script>

<style>
.container {
	padding: 20rpx;
	min-height: 100vh;
	background: #f5f5f5;
}

.order-info {
	background: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.product-info {
	display: flex;
}

.product-info image {
	width: 140rpx;
	height: 140rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
}

.info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.price {
	font-size: 32rpx;
	color: #ff4d4f;
	font-weight: bold;
}

.comment-form {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx 20rpx;
}

.rating {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.label {
	font-size: 28rpx;
	color: #333;
	margin-right: 20rpx;
}

.stars {
	display: flex;
}

.star {
	font-size: 40rpx;
	color: #ccc;
	margin-right: 10rpx;
}

.star.active {
	color: #ffc107;
}

.textarea-box {
	position: relative;
	margin-bottom: 30rpx;
}

.textarea {
	width: 100%;
	height: 200rpx;
	border: 1px solid #e8e8e8;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.word-count {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	font-size: 24rpx;
	color: #999;
}

.upload-box {
	margin-bottom: 30rpx;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	margin-top: 20rpx;
}

.image-item {
	width: 160rpx;
	height: 160rpx;
	position: relative;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
}

.image-item image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
}

.delete-btn {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-btn {
	width: 160rpx;
	height: 160rpx;
	border: 1px dashed #ccc;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 60rpx;
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx;
	background: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
}

.submit-btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	color: #fff;
	font-size: 30rpx;
	border-radius: 40rpx;
}
</style>