<template>
<view>
	<block v-if="isload">
		<view class="container">
			<view class="swiper-container">
				<swiper class="swiper" :indicator-dots="false" :autoplay="true" :interval="5000" @change="swiperChange">
					<block v-for="(item, index) in product.pics" :key="index">
						<swiper-item class="swiper-item">
							<view class="swiper-item-view"><image class="img" :src="item" mode="widthFix"/></view>
						</swiper-item>
					</block>
				</swiper>
				<view class="imageCount">{{current+1}}/{{product.pics.length}}</view>
			</view>
			<view class="kanjia_title">
				<view class="f1">
					<view class="t1">砍价最低 </view>
					<view class="t2"><text style="font-size:40rpx;font-weight:bold">{{product.min_price}}</text> 元可拿</view>
					<view class="t3">{{product.sales}}人已砍价成功</view>
				</view>
				<view class="f3">
					<view class="t1">{{ product.starttime > nowtime ? '距活动开始还有' : '距活动结束还剩'}}</view>
					<view class="t2" id="djstime"><text class="djsspan">{{djsday}}</text> 天 <text class="djsspan">{{djshour}}</text> : <text class="djsspan">{{djsmin}}</text> : <text class="djsspan">{{djssec}}</text></view>
				</view>
			</view>
			<view class="header"> 
				<view class="title">
					<view class="lef">
						<text>{{product.name}}</text>
					</view>
					<view class="share" @tap="shareClick">
						<image src="/static/img/share.png"></image>
						<text>分享</text>
					</view>
				</view>
				<view class="sales_stock">
					<view class="f1">原价：￥{{product.sell_price}} </view>
					<view class="f2">已砍走{{product.sales}}件 剩余{{product.stock}}件</view>
				</view>
			</view>
			<view class="joinlist" v-if="product.saleing>0">
				<view v-for="(join, index) in joinlist" :key="index" class="t1">
					<image :src="join.headimg"></image>
				</view>
				<view class="t1" v-if="product.saleing>7">
					<image src="/static/img/moreuser.png"></image>
				</view>
				<view class="t2">{{product.saleing}}人正在参加</view>
			</view>

			<view class="shop" v-if="shopset.showjd==1">
				<image :src="business.logo" class="p1"/>
				<view class="p2 flex1">
					<view class="t1">{{business.name}}</view>
					<view class="t2">{{business.desc}}</view>
				</view> 
				<button class="p4" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="goto" :data-url="product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid" data-opentype="reLaunch">进入店铺</button>
			</view>
			<view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view>
			<view class="detail">
				<dp :pagecontent="pagecontent"></dp>
			</view>
			
			<view style="width:100%;height:70px;"></view>

			<view class="bottombar flex-row" :class="menuindex>-1?'tabbarbot':'notabbarbot'" v-if="product.status==1">
				<view class="f1">
					<view class="item" @tap="goto" :data-url="kfurl" v-if="kfurl!='contact::'">
						<image class="img" src="/static/img/kefu.png"/>
						<view class="t1">客服</view>
					</view>
					<button class="item" v-else open-type="contact">
						<image class="img" src="/static/img/kefu.png"/>
						<view class="t1">客服</view>
					</button>
					<view class="item flex1" @tap="shareClick">
						<image class="img" src="/static/img/share2.png"/>
						<view class="t1">分享</view> 
					</view>
					<view class="item" @tap="addfavorite">
						<image class="img" src="/static/img/shoucang.png"/>
						<view class="t1">{{isfavorite?'已收藏':'收藏'}}</view>
					</view>
				</view>
				<view class="op">
					<block v-if="imJoin==0">
						<view class="tobuy" :style="{background:t('color1')}" v-if="product.starttime > nowtime" style="background:#aaa">活动未开始</view>
						<view class="tobuy" :style="{background:t('color1')}" v-else-if="product.endtime < nowtime" style="background:#aaa">活动已结束</view>
						<view class="tobuy" :style="{background:t('color1')}" @tap="joinin" v-else>立即参与砍价</view>
					</block>
					<block v-else>
						<view class="tobuy" :style="{background:t('color1')}" @tap="joinin">查看我的砍价</view>
					</block>
				</view>
			</view>
		</view>
		
		<view v-if="sharetypevisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal" style="height:320rpx;min-height:320rpx">
				<!-- <view class="popup__title">
					<text class="popup__title-text">请选择分享方式</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hidePstimeDialog"/>
				</view> -->
				<view class="popup__content">
					<view class="sharetypecontent">
						<view class="f1" @tap="shareapp" v-if="getplatform() == 'app'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'mp'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'h5'">
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</view>
						<button class="f1" open-type="share" v-else>
							<image class="img" src="/static/img/weixin.png"/>
							<text class="t1">分享给好友</text>
						</button>
						<view class="f2" @tap="showPoster">
							<image class="img" src="/static/img/sharepic.png"/>
							<text class="t1">生成分享图片</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="posterDialog" v-if="showposter">
			<view class="main">
				<view class="close" @tap="posterDialogClose"><image class="img" src="/static/img/close.png"/></view>
				<view class="content">
					<image class="img" :src="posterpic" mode="widthFix" @tap="previewImage" :data-url="posterpic"></image>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
var interval = null;

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      current: 0,
			indexurl:app.globalData.indexurl,
			platform:app.globalData.platform,
			tabnum: 1,
      num: 1,
      isfavorite: false,
      btntype: 1,
      ggselected: [],
      ks: '',
      gwcnum: 1,
      nodata: 0,
      userinfo: [],
      djsday: '00',
      djshour: '00',
      djsmin: '00',
      djssec: '00',
      product: "",
      business: {},
      shopset: "",
      pagecontent: "",
      joinlist: "",
      nowtime: "",
      imJoin: "",
      title: "",
      sharepic: "",
      sharetypevisible: false,
      showposter: false,
      posterpic: "",
			kfurl:'',
    };
  },

 
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.product.name,pic:this.product.pic,callback:function(){that.sharecallback();}});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic,callback:function(){that.sharecallback();}});
		var query = (sharewxdata.path).split('?')[1];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  onUnload: function () {
    clearInterval(interval);
  },
  methods: {
		getdata: function () {
			var that = this;
			var id = that.opt.id;
			that.loading = true;
			app.get('ApiKanjia/product', {id: id}, function (res) {
				that.loading = false;
				if (res.status == 0) {
					app.alert(res.msg);
					return;
				}
				var product = res.product;
				var business = res.business;
				var pagecontent = JSON.parse(product.detail);

				that.product = product;
				that.business = business;
				that.shopset = res.shopset;
				that.pagecontent = pagecontent;
				that.joinlist = res.joinlist;
				that.nowtime = res.nowtime;
				that.imJoin = res.imJoin;
				that.title = product.name;
				that.isfavorite = res.isfavorite;
				that.sharepic = product.pics[0];
				clearInterval(interval);
				interval = setInterval(function () {
					that.nowtime = that.nowtime + 1;
					that.getdjs();
				}, 1000);
				uni.setNavigationBarTitle({
					title: product.name
				});
				that.kfurl = '/pagesExt/kefu/index?bid='+res.product.bid;
				if(app.globalData.initdata.kfurl != ''){
					that.kfurl = app.globalData.initdata.kfurl;
				}
				if(that.business && that.business.kfurl){
					that.kfurl = that.business.kfurl;
				}
				that.loaded({title:res.product.name,pic:res.product.pic,callback:function(){that.sharecallback();}});
			});
		},
		sharecallback:function(){
			var that = this;
			app.post("ApiKanjia/share", {proid: that.product.id}, function (res) {
				if (res.status == 1) {
					//setTimeout(function () {
					//	that.getdata();
					//}, 1000);
				} else if (res.status == 0) {
					//dialog(res.msg);
				}
			});
		},
    swiperChange: function (e) {
      var that = this;
      that.current = e.detail.current;
    },
    getdjs: function () {
      var that = this;
      if (that.product.starttime * 1 > that.nowtime * 1) {
        var totalsec = that.product.starttime * 1 - that.nowtime * 1;
      } else {
        var totalsec = that.product.endtime * 1 - that.nowtime * 1;
      }
      if (totalsec <= 0) {
        that.djsday = '00';
        that.djshour = '00';
        that.djsmin = '00';
        that.djssec = '00';
      } else {
        var date = Math.floor(totalsec / 86400);
        var houer = Math.floor((totalsec - date * 86400) / 3600);
        var min = Math.floor((totalsec - date * 86400 - houer * 3600) / 60);
        var sec = totalsec - date * 86400 - houer * 3600 - min * 60;
        var djsday = (date < 10 ? '0' : '') + date;
        var djshour = (houer < 10 ? '0' : '') + houer;
        var djsmin = (min < 10 ? '0' : '') + min;
        var djssec = (sec < 10 ? '0' : '') + sec;
        that.djsday = djsday;
        that.djshour = djshour;
        that.djsmin = djsmin;
        that.djssec = djssec;
      }
    },
    joinin: function (e) {
      var type = e.currentTarget.dataset.type;
      var that = this;
      var proid = that.product.id;
      app.goto('join?proid=' + proid);
    },
    //收藏操作
    addfavorite: function () {
      var that = this;
      var proid = that.product.id;
			app.showLoading('加载中');
      app.post('ApiKanjia/addfavorite', {proid: proid,type: 'kanjia'}, function (data) {
				app.showLoading(false);
        if (data.status == 1) {
          that.isfavorite = !that.isfavorite;
        }
        app.success(data.msg);
      });
    },
    tabClick: function (e) {
      this.tabnum = e.currentTarget.dataset.num;
    },
    shareClick: function () {
      this.sharetypevisible = true;
    },
    handleClickMask: function () {
      this.sharetypevisible = false;
    },
    showPoster: function () {
      var that = this;
      that.showposter = true;
      that.sharetypevisible = false;
			app.showLoading('努力生成中');
      app.post('ApiKanjia/getposter', {proid: that.product.id}, function (data) {
				app.showLoading(false);
        if (data.status == 0) {
          app.alert(data.msg);
        } else {
          that.posterpic = data.poster;
        }
      });
    },
    posterDialogClose: function () {
      this.showposter = false;
    },
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.product.name;
						//sharedata.summary = app.globalData.initdata.desc;
						sharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/kanjia/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;
						sharedata.imageUrl = that.product.pic;
						var sharelist = app.globalData.initdata.sharelist;
						if(sharelist){
							for(var i=0;i<sharelist.length;i++){
								if(sharelist[i]['indexurl'] == '/activity/kanjia/product'){
									sharedata.title = sharelist[i].title;
									sharedata.summary = sharelist[i].desc;
									sharedata.imageUrl = sharelist[i].pic;
									if(sharelist[i].url){
										var sharelink = sharelist[i].url;
										if(sharelink.indexOf('/') === 0){
											sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;
										}
										if(app.globalData.mid>0){
											 sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;
										}
										sharedata.href = sharelink;
									}
								}
							}
						}
						uni.share(sharedata);
					}
        }
      });
		}
	}
};
</script>
<style>
.swiper-container{position:relative}
.swiper {width: 100%;height: 750rpx;overflow: hidden;}
.swiper-item-view{width: 100%;height: 750rpx;}
.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}

.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}

.kanjia_title{ width:100%;height:110rpx;display:flex;align-items:center;}
.kanjia_title .f1{height:110rpx;background: linear-gradient(90deg,#FF3143,#FE6748);display:flex;flex-direction:column;flex:1;justify-content:center;position:relative;padding-left:20rpx}
.kanjia_title .f1 .t1{font-size:24rpx;color:#fff}
.kanjia_title .f1 .t2{font-size:24rpx;color:#fff;}
.kanjia_title .f1 .t3{background:rgba(255, 255, 255,0.9);height:46prx;line-height:46rpx;border-radius:23rpx;padding:0 20rpx;color:#FF3143;font-size:24rpx;position:absolute;right:10rpx;top:30rpx;}
.kanjia_title .f3{width:280rpx;height:110rpx;background:#FFDBDF;color:#333;display:flex;flex-direction:column;align-items:center;justify-content:center}
.kanjia_title .f3 .t2{color:#FF3143}
.kanjia_title .djsspan{font-size:22rpx;border-radius:8rpx;background:#FF3143;color:#fff;text-align:center;padding:4rpx 8rpx;margin:0 4rpx}

.header {width: 100%;padding: 0 3%;background: #fff;display:flex;flex-direction:column}
.header .title {padding: 10px 0px;line-height:44rpx;font-size:32rpx;display:flex;}
.header .title .lef{display:flex;flex-direction:column;justify-content: center;flex:1;color:#222222;font-weight:bold}
.header .title .lef .t2{ font-size:26rpx;color:#999;padding-top:10rpx;font-weight:normal}
.header .title .share{width:88rpx;height:88rpx;padding-left:20rpx;border-left:0 solid #f5f5f5;text-align:center;font-size:24rpx;color:#222;display:flex;flex-direction:column;align-items:center}
.header .title .share image{width:32rpx;height:32rpx;margin-bottom:4rpx}
.header .sales_stock{display:flex;justify-content:space-between;height:40rpx;line-height:40rpx;margin-bottom:20rpx;font-size:24rpx;color:#777777}

.choose{ display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; height: 80rpx; line-height: 80rpx; padding: 0 3%; color: #505050; }
.choose .f2{ width: 40rpx; height: 40rpx;}

.joinlist{width:100%;display:flex;align-items:center;margin-top:20rpx;background: #fff;padding:30rpx 3%;}
.joinlist .t1{margin-left: -10rpx;height:40rpx;}
.joinlist .t1 image{width:50rpx;height:50rpx;border-radius: 50%;border:2px solid #fff;}
.joinlist .t2{font-size:24rpx;color:#787878}


.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}
.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}
.shop .p2{padding-left:10rpx}
.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}
.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}
.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}

.detail{min-height:200rpx;}

.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}
.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}
.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}
.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}

.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}
.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}
.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:80rpx;position:relative}
.bottombar .f1 .item .img{ width:44rpx;height:44rpx}
.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}
.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}
.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;display:flex;align-items:center;justify-content:center}

.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx;display:flex;padding:50rpx;align-items:flex-end}
.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}
.sharetypecontent button::after{border:0}
.sharetypecontent .f1 .img{width:90rpx;height:90rpx}
.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}
.sharetypecontent .f2 .img{width:90rpx;height:90rpx}
.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}

.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}
.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}
.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}
.posterDialog .close .img{ width:40rpx;height:40rpx;}
.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}
.posterDialog .content .img{width:540rpx;height:960rpx}
</style>