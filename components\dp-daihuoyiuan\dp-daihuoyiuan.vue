<template>
 <view>
	<view class="item-list" v-for="(item, index) in data" :key="index" @click="goto" :data-url="'/daihuobiji/kuaituan/detail?id='+item.proid">
		<view class="item-header">
			<image class="item-image" :src="item.pic"></image>
			<view class="item-author">{{item.author}}</view>
		</view> 
		<view class="item-name">{{item.name}}</view>
		<view class="item-price">
			<text class="currency-symbol">￥</text>
			<text class="price-value">{{item.priceRange}}</text>
		</view>
		<view class="item-pics">
			<image class="item-pic" v-for="(ite, ind) in item.pic2" :key="ind" :src="ite" v-if="ind<3"></image>
		</view>
		<view class="item-footer">
			<view class="viewers">
				<image class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
				<image class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
				<image class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
				<text class="view-count">{{item.readcount}}人看过</text>
			</view>
			<view class="share">
				<image class="share-icon" src="../../static/img/share.png"></image>
				<text class="share-text">点击分享</text>
			</view>
		</view>
	</view>
</view>
</template>

<script>
	export default {
		props: {
			params: {},
			data: {}
		}
	}
</script>

<style>
	.item-list {
		background: #fff;
		margin: 10px;
		padding: 15px;
		border-radius: 8px;
	}

	.item-header {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #eee;
		padding-bottom: 10px;
		margin-bottom: 10px;
	}

	.item-image {
		width: 50px;
		height: 50px;
		border-radius: 5px;
	}

	.item-author {
		margin: 5px;
		font-size: 14px;
	}

	.item-name {
		overflow-wrap: break-word;
		font-size: 16px;
	}

	.item-price {
		margin: 10px 0;
		color: red;
	}

	.currency-symbol {
		font-size: 12px;
		font-weight: bold;
	}

	.price-value {
		font-size: 16px;
		font-weight: bold;
	}

	.item-pics {
		display: flex;
	}

	.item-pic {
		width: 100px;
		height: 100px;
		border-radius: 8px;
		margin-right: 5px;
	}

	.item-footer {
		display: flex;
		background: #F6F6F6;
		padding: 5px;
		border-radius: 5px;
		align-items: center;
		justify-content: space-between;
		margin-top: 10px;
	}

	.viewers {
		display: flex;
		align-items: center;
	}

	.viewer-icon {
		width: 20px;
		height: 20px;
		border-radius: 50px;
		margin-left: -10px;
	}

	.view-count {
		font-size: 12px;
		margin-left: 10px;
		color: #507C85;
	}

	.share {
		display: flex;
		align-items: center;
		padding: 0 10px;
		border-left: 1px solid #ccc;
	}

	.share-icon {
		width: 15px;
		height: 15px;
	}

	.share-text {
		font-size: 12px;
		margin-left: 10px;
		color: #507C85;
	}
</style>