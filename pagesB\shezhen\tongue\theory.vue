<template>
	<view class="theory-container">
		<!-- 2025-01-03 22:55:53,565-INF0-[theory][init_001] 舌象理论页面初始化 -->
		
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<view class="nav-title">健康报告</view>
			<view class="nav-right">
				<text class="menu-icon">•••</text>
				<text class="record-icon">●</text>
			</view>
		</view>
		
		<!-- 舌象体征论述 -->
		<view class="theory-section">
			<text class="section-title">舌象体征论述</text>
			
			<!-- 水湿论述 -->
			<view class="theory-item">
				<view class="theory-header">
					<text class="theory-name water-wet">水湿：</text>
					<text class="theory-summary">中央生湿，湿生土，土生甘，甘生脾</text>
				</view>
				<view class="theory-content">
					<text class="theory-text">夫水湿者，乃脾胃之气所化也。脾胃壮则水湿化，脾胃弱则水湿停。故水湿之为病，皆因脾胃不足所致。其症多见体重肢肿，肌肤不泽，小便短少，大便溏泄，舌淡苔滑，脉沉缓等。治宜健脾利湿，以复其常。水湿之症，头身困重，四肢酸楚，食欲不振，腹胀便溏，舌淡苔滑，脉沉缓。或见面色萎黄，肌肤不泽，小便短少，体重肢肿。此皆因脾胃不足，运化失职，湿邪内生所致。</text>
				</view>
			</view>
			
			<!-- 脾胃虚论述 -->
			<view class="theory-item">
				<view class="theory-header">
					<text class="theory-name spleen-weak">脾胃虚：</text>
					<text class="theory-summary">脾胃为后天之本，气血生化之源。</text>
				</view>
				<view class="theory-subtitle">
					<text>脾胃者，仓廪之官，五味出焉</text>
				</view>
				<view class="theory-content">
					<text class="theory-text">百病皆由脾胃衰而生也。夫脾胃虚，则湿土之气潴于脘下，肾与膀胱受邪。膀胱壮寒，肾为阴火，二者偶弱，润泽之气虚，而煎熬之气胜，故小便黄赤而数，大便乃秘涩而不行。脾胃受伤，则水反为湿，谷反为满，精华之气不能输化于上，故合污下降而污利作矣。</text>
					<image class="doctor-avatar" src="/static/images/doctor-avatar.png"></image>
				</view>
			</view>
		</view>
		
		<!-- 患病风险 -->
		<view class="risk-section">
			<text class="section-title">患病风险</text>
			
			<view class="risk-grid">
				<!-- 第一行 -->
				<view class="risk-item">
					<view class="risk-circle" :style="{background: `conic-gradient(#1890ff 0% ${riskData[0].percentage}%, #f0f0f0 ${riskData[0].percentage}% 100%)`}">
						<text class="risk-percentage">{{riskData[0].percentage}}%</text>
					</view>
					<text class="risk-name">{{riskData[0].name}}</text>
				</view>
				
				<view class="risk-item">
					<view class="risk-circle" :style="{background: `conic-gradient(#1890ff 0% ${riskData[1].percentage}%, #f0f0f0 ${riskData[1].percentage}% 100%)`}">
						<text class="risk-percentage">{{riskData[1].percentage}}%</text>
					</view>
					<text class="risk-name">{{riskData[1].name}}</text>
				</view>
				
				<view class="risk-item">
					<view class="risk-circle" :style="{background: `conic-gradient(#1890ff 0% ${riskData[2].percentage}%, #f0f0f0 ${riskData[2].percentage}% 100%)`}">
						<text class="risk-percentage">{{riskData[2].percentage}}%</text>
					</view>
					<text class="risk-name">{{riskData[2].name}}</text>
				</view>
				
				<!-- 第二行 -->
				<view class="risk-item">
					<view class="risk-circle" :style="{background: `conic-gradient(#1890ff 0% ${riskData[3].percentage}%, #f0f0f0 ${riskData[3].percentage}% 100%)`}">
						<text class="risk-percentage">{{riskData[3].percentage}}%</text>
					</view>
					<text class="risk-name">{{riskData[3].name}}</text>
				</view>
				
				<view class="risk-item">
					<view class="risk-circle" :style="{background: `conic-gradient(#1890ff 0% ${riskData[4].percentage}%, #f0f0f0 ${riskData[4].percentage}% 100%)`}">
						<text class="risk-percentage">{{riskData[4].percentage}}%</text>
					</view>
					<text class="risk-name">{{riskData[4].name}}</text>
				</view>
				
				<view class="risk-item">
					<view class="risk-circle" :style="{background: `conic-gradient(#1890ff 0% ${riskData[5].percentage}%, #f0f0f0 ${riskData[5].percentage}% 100%)`}">
						<text class="risk-percentage">{{riskData[5].percentage}}%</text>
					</view>
					<text class="risk-name">{{riskData[5].name}}</text>
				</view>
			</view>
		</view>
		
		<!-- 需要警惕 -->
		<view class="warning-section">
			<text class="section-title">需要警惕</text>
			
			<view class="warning-list">
				<view class="warning-item" v-for="(item, index) in warningList" :key="index">
					<view class="warning-header">
						<text class="warning-bullet">•</text>
						<text class="warning-name">{{item.name}}：</text>
					</view>
					<text class="warning-desc">{{item.description}}</text>
					<image v-if="item.showDoctor" class="doctor-avatar-small" src="/static/images/doctor-avatar.png"></image>
				</view>
			</view>
		</view>
		
		<!-- 结果解读 -->
		<view class="result-section">
			<text class="section-title">结果解读</text>
			
			<view class="balance-chart">
				<view class="chart-container">
					<!-- 健康天平图 -->
					<view class="balance-circle">
						<view class="balance-inner">
							<text class="balance-center">平和</text>
							<view class="balance-point"></view>
						</view>
						<!-- 四个方向标签 -->
						<text class="direction-label top">实</text>
						<text class="direction-label right">热</text>
						<text class="direction-label bottom">虚</text>
						<text class="direction-label left">寒</text>
					</view>
					
					<view class="balance-status">
						<view class="status-dot"></view>
						<text class="status-text">伴随痰湿</text>
					</view>
				</view>
			</view>
			
			<view class="result-description">
				<text class="result-text">通过健康天平分析得出，您的身体偏寒且正气偏虚，处于痰湿状态。</text>
				<text class="result-text">健康天平圆点越靠近中心身体越健康，越偏离中心并健康状态越严重。</text>
			</view>
			
			<view class="result-note">
				<text class="note-text">注：「健康天平」由省中医院副院长杨志敏教授带领的健康辨识团队运用中医健康和态辨识体系，结合经络脏腑阴阳五行、气血津液等理论得出。</text>
				<image class="doctor-avatar-note" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view>
		
		<!-- VIP会员专享 -->
		<view class="vip-section">
			<view class="vip-header">
				<text class="vip-icon">👑</text>
				<text class="vip-text">VIP会员专享</text>
			</view>
			
			<view class="vip-card">
				<text class="vip-title">广东省中医院为您专属定制</text>
				<text class="vip-subtitle">健康调理方案</text>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="btn-secondary">返回</view>
			<view class="btn-secondary">重新拍照</view>
			<view class="btn-primary">分享</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 2025-01-03 22:55:53,565-INF0-[theory][data_001] 初始化理论页面数据
				riskData: [
					{ name: '肥胖', percentage: 45 },
					{ name: '痰疾', percentage: 38 },
					{ name: '关节炎', percentage: 21 },
					{ name: '脂溢性脱发', percentage: 33 },
					{ name: '高血糖、高血脂、高尿酸', percentage: 15 },
					{ name: '代谢性酸中毒', percentage: 7 }
				],
				warningList: [
					{
						name: '肥胖',
						description: '水湿人群体内的水液异常停滞，导致体内水液堆积而容易出现全身水肿，全身浮肿会使整个体形看起来很虚胖。',
						showDoctor: false
					},
					{
						name: '痰疾',
						description: '水湿人群体内的水液代谢异常，水液过多容易出现面部出油多，油脂堵塞毛孔，容易导致废物排泄障碍，皮肤而出现痤疮，即平时常说的痘痘。',
						showDoctor: true
					},
					{
						name: '关节炎',
						description: '水湿人群由于水液运化异常，水液',
						showDoctor: false
					}
				]
			}
		},
		methods: {
			// 2025-01-03 22:55:53,565-INF0-[theory][goBack_001] 返回上一页功能
			goBack() {
				console.log('2025-01-03 22:55:53,565-INF0-[theory][goBack_001] 用户点击返回');
				uni.navigateBack();
			}
		},
		
		onLoad() {
			console.log('2025-01-03 22:55:53,565-INF0-[theory][onLoad_001] 舌象理论页面加载完成');
		}
	}
</script>

<style scoped>
/* 2025-01-03 22:55:53,565-INF0-[theory][style_001] 舌象理论页面样式定义 */

.theory-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 顶部导航样式 */
.top-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: white;
	position: relative;
}

.nav-left {
	display: flex;
	align-items: center;
}

.back-icon {
	font-size: 40rpx;
	color: #333;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.nav-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.menu-icon {
	font-size: 30rpx;
	color: #666;
}

.record-icon {
	font-size: 30rpx;
	color: #333;
}

/* 舌象体征论述 */
.theory-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.theory-item {
	margin-bottom: 40rpx;
}

.theory-item:last-child {
	margin-bottom: 0;
}

.theory-header {
	display: flex;
	align-items: baseline;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.theory-name {
	font-size: 32rpx;
	font-weight: 500;
}

.theory-name.water-wet {
	color: #1890ff;
}

.theory-name.spleen-weak {
	color: #52c41a;
}

.theory-summary {
	font-size: 28rpx;
	color: #666;
}

.theory-subtitle {
	margin-bottom: 20rpx;
}

.theory-subtitle text {
	font-size: 28rpx;
	color: #333;
}

.theory-content {
	position: relative;
}

.theory-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	padding-right: 100rpx;
}

.doctor-avatar {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 患病风险 */
.risk-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.risk-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 40rpx;
	margin-top: 30rpx;
}

.risk-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
}

.risk-circle {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.risk-percentage {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.risk-name {
	font-size: 24rpx;
	color: #666;
	text-align: center;
}

/* 需要警惕 */
.warning-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.warning-list {
	margin-top: 30rpx;
}

.warning-item {
	margin-bottom: 30rpx;
	position: relative;
}

.warning-item:last-child {
	margin-bottom: 0;
}

.warning-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 10rpx;
}

.warning-bullet {
	color: #ff4d4f;
	font-size: 24rpx;
	font-weight: bold;
}

.warning-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.warning-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	padding-right: 100rpx;
}

.doctor-avatar-small {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 结果解读 */
.result-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.balance-chart {
	margin: 40rpx 0;
}

.chart-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30rpx;
}

.balance-circle {
	width: 400rpx;
	height: 400rpx;
	border: 4rpx solid #1890ff;
	border-radius: 50%;
	position: relative;
	background: linear-gradient(45deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.05) 100%);
}

.balance-inner {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
}

.balance-center {
	font-size: 48rpx;
	font-weight: 500;
	color: #1890ff;
}

.balance-point {
	width: 20rpx;
	height: 20rpx;
	background-color: #1890ff;
	border-radius: 50%;
	margin-left: -60rpx;
}

.direction-label {
	position: absolute;
	font-size: 32rpx;
	color: #666;
}

.direction-label.top {
	top: -10rpx;
	left: 50%;
	transform: translateX(-50%);
}

.direction-label.right {
	right: -10rpx;
	top: 50%;
	transform: translateY(-50%);
}

.direction-label.bottom {
	bottom: -10rpx;
	left: 50%;
	transform: translateX(-50%);
}

.direction-label.left {
	left: -10rpx;
	top: 50%;
	transform: translateY(-50%);
}

.balance-status {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-dot {
	width: 16rpx;
	height: 16rpx;
	background-color: #1890ff;
	border-radius: 50%;
}

.status-text {
	font-size: 28rpx;
	color: #333;
}

.result-description {
	margin: 30rpx 0;
}

.result-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 15rpx;
}

.result-note {
	background-color: #f8f9fa;
	padding: 30rpx;
	border-radius: 15rpx;
	position: relative;
}

.note-text {
	font-size: 24rpx;
	color: #999;
	line-height: 1.6;
	padding-right: 100rpx;
}

.doctor-avatar-note {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* VIP会员专享 */
.vip-section {
	margin: 30rpx;
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	border-radius: 15rpx;
	padding: 30rpx;
}

.vip-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.vip-icon {
	font-size: 32rpx;
}

.vip-text {
	font-size: 28rpx;
	color: #8b4513;
	font-weight: 500;
}

.vip-card {
	text-align: center;
}

.vip-title {
	font-size: 32rpx;
	color: #8b4513;
	margin-bottom: 10rpx;
	display: block;
}

.vip-subtitle {
	font-size: 48rpx;
	color: #8b4513;
	font-weight: bold;
}

/* 底部按钮 */
.bottom-buttons {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	background-color: white;
	margin-top: 30rpx;
}

.btn-secondary {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	border: 2rpx solid #ddd;
	border-radius: 25rpx;
	font-size: 28rpx;
	color: #666;
}

.btn-primary {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	background-color: #1890ff;
	color: white;
	border-radius: 25rpx;
	font-size: 28rpx;
}
</style> 