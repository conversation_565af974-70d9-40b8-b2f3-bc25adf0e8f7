<template>
  <view class="container">
    <block v-if="isload">
      <!-- 筛选栏 -->
      <view class="filter-bar">
        <view 
          class="filter-item" 
          :class="{active: filter === 'today'}" 
          @tap="changeFilter('today')">
          今日
        </view>
        <view 
          class="filter-item" 
          :class="{active: filter === 'yesterday'}" 
          @tap="changeFilter('yesterday')">
          昨日
        </view>
        <view 
          class="filter-item" 
          :class="{active: filter === 'all'}" 
          @tap="changeFilter('all')">
          全部
        </view>
      </view>

      <view>
        <view class="order-box">
          <view class="head">
            <view class="f1">今日需配单数量:</view>
            <view class="flex1"></view>
            <view class="f2"><text class="t1">{{datalist.ds}}</text>单</view>
          </view>
          <view class="head">
            <view class="f1">今日需配件数量:</view>
            <view class="flex1"></view>
            <view class="f2"><text class="t1">{{datalist.js}}</text>件</view>
          </view>
          <view 
            style="margin-top: 20rpx;"  
            class="f1"  
            v-for="(item, index) in datalist.data" 
            :key="item.id">
            {{item.name}}
            <span style="color: red;margin-left: 50rpx;">X{{item.xnum}}</span>
          </view>
          <!-- 其他内容 -->
        </view>
      </view>

      <nomore v-if="nomore"></nomore>
      <nodata v-if="nodata"></nodata>

      <!-- 底部导航栏 -->
      <view class="tabbar">
        <view class="tabbar-bot"></view>
        <view class="tabbar-bar" style="background-color:#ffffff">
          <view @tap="goto" data-url="dating" data-opentype="reLaunch" class="tabbar-item">
            <view class="tabbar-image-box">
              <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/home2.png'"></image>
            </view>
            <view class="tabbar-text active">大厅</view>
          </view>
          <view @tap="goto" data-url="orderlist" data-opentype="reLaunch" class="tabbar-item">
            <view class="tabbar-image-box">
              <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/order.png'"></image>
            </view>
            <view class="tabbar-text">订单</view>
          </view>
          <view @tap="goto" data-url="orderlist?st=4" data-opentype="reLaunch" class="tabbar-item">
            <view class="tabbar-image-box">
              <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/orderwc.png'"></image>
            </view>
            <view class="tabbar-text">已完成</view>
          </view>
          <view @tap="goto" data-url="my" data-opentype="reLaunch" class="tabbar-item">
            <view class="tabbar-image-box">
              <image class="tabbar-icon" :src="pre_url+'/static/img/peisong/my.png'"></image>
            </view>
            <view class="tabbar-text">我的</view>
          </view>
        </view>
      </view>

    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
    <view style="display:none">{{timestamp}}</view>
  </view>
</template>
<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      pre_url: app.globalData.pre_url,

      // 新增的筛选状态
      filter: 'today', // 默认筛选为“今日”

      st: 'all',
      datalist: {
        ds: 0, // 今日需配单数量
        js: 0, // 今日需配件数量
        data: []
      },
      pagenum: 1,
      nomore: false,
      nodata: false,
      interval1: null,
      timestamp: '',
      nowtime: '',
      keyword: '', // 添加关键字搜索
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  onUnload: function(){
    clearInterval(this.interval1);
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    // 新增的筛选项切换方法
    changeFilter: function(newFilter) {
      if (this.filter !== newFilter) {
        this.filter = newFilter;
        this.pagenum = 1;
        this.datalist = {
          ds: 0,
          js: 0,
          data: []
        };
        this.nodata = false;
        this.nomore = false;
        this.getdata();
      }
    },
    // 修改后的 getdata 方法
    getdata: function (loadmore) {
      if(!loadmore){
        this.pagenum = 1;
        this.datalist = {
          ds: 0,
          js: 0,
          data: []
        };
      }
      var that = this;
      var filter = that.filter;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
      that.nodata = false;
      that.nomore = false;
      that.loading = true;

      // 根据筛选状态设置 'st' 参数
      // 假设后端API根据 'st' 参数过滤数据
      // 'today' => 'today', 'yesterday' => 'yesterday', 'all' => 'all'
      var st;
      if(filter === 'today') {
        st = 'today';
      } else if(filter === 'yesterday') {
        st = 'yesterday';
      } else {
        st = 'all';
      }

      app.post('ApiPeisong/datings', {st: st, pagenum: pagenum, keyword: keyword}, function (res) {
        that.loading = false;
        if(res.status==0){
          app.alert(res.msg);
          return;
        }
        var data = res.datalist;
        if (pagenum == 1) {
          that.datalist = data;
          that.nowtime = res.nowtime
          if (data.data.length == 0) {
            that.nodata = true;
          }
          that.loaded();
          that.updatemylocation(false);
          clearInterval(that.interval1);
          that.interval1 = setInterval(function(){
            that.updatemylocation(true);
            that.nowtime = that.nowtime + 10;
          },10000)
        } else {
          if (data.data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.data.concat(data.data);
            that.datalist.data = newdata;
            // 更新其他统计数据
            that.datalist.ds += data.ds;
            that.datalist.js += data.js;
          }
        }
      });
    },
    updatemylocation: function(needload){
      console.log('updatemylocation');
      var that = this;
      app.getLocation(function(res){
        var longitude = res.longitude;
        var latitude = res.latitude;
        var datalist = that.datalist.data;
        for(var i in datalist){
          var thisdata = datalist[i];
          var rs = that.getdistance(thisdata.longitude2, thisdata.latitude2, longitude, latitude, 1);
          thisdata.juli2 = rs.juli;
          thisdata.juli2_unit = rs.unit;
          thisdata.leftminute = parseInt((thisdata.yujitime - that.nowtime) / 60);
          datalist[i] = thisdata;
        }
        that.datalist.data = datalist;
        that.timestamp = parseInt((new Date().getTime())/1000);
        app.get('ApiPeisong/updatemylocation',{longitude:longitude, latitude:latitude, t: that.timestamp}, function(){
          // if(needload) that.getdata();
        });
      });
    },
    getdistance: function (lng1, lat1, lng2, lat2) {
      if(!lat1 || !lng1 || !lat2 || !lng2) return '';
      var rad1 = lat1 * Math.PI / 180.0;
      var rad2 = lat2 * Math.PI / 180.0;
      var a = rad1 - rad2;
      var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
      var r = 6378137;
      var juli = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));
      var unit = 'm';
      if(juli > 1000){
        juli = juli / 1000;
        unit = 'km';
      }
      juli = juli.toFixed(1);
      return {juli:juli, unit:unit}
    },
    qiangdan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var st = e.currentTarget.dataset.st;
      app.confirm('确定要接单吗?', function () {
        app.showLoading('提交中');
        app.post('ApiPeisong/qiangdan', {id: id}, function (data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    daohang: function(e){
      var that = this;
      var index = e.currentTarget.dataset.index;
      var datainfo = that.datalist.data[index];
      uni.showActionSheet({
        itemList: ['导航到商家', '导航到用户'],
        success: function (res) {
          if(res.tapIndex >= 0){
            if (res.tapIndex == 0) {
              var longitude = datainfo.longitude
              var latitude = datainfo.latitude
              var name = datainfo.binfo.name
              var address = datainfo.binfo.address
            } else {
              var longitude = datainfo.longitude2
              var latitude = datainfo.latitude2
              var name = datainfo.orderinfo.address
              var address = datainfo.orderinfo.address
            }
            uni.openLocation({
              latitude: parseFloat(latitude),
              longitude: parseFloat(longitude),
              name: name,
              address: address,
              scale: 13,
              success: function () {
                console.log('success');
              },
              fail: function(res){
                console.log(res);
              }
            })
          }
        }
      });
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword
      that.getdata();
    },
    receiveMessage: function (data) {
      var that = this;
      if(data.type == 'peisong' || data.type == 'peisong_jiedan') {
        that.getdata();
      }
      return false;
    }
  }
};
</script>
<style>
@import "./common.css";

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.filter-bar {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.filter-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #999999;
  padding: 10rpx 0;
  cursor: pointer;
}

.filter-item.active {
  color: #06A051;
  font-weight: bold;
  border-bottom: 2rpx solid #06A051;
}

.order-box {
  width: 94%;
  margin: 20rpx 3%;
  padding: 6rpx 3%;
  background: #fff;
  border-radius: 8px;
}

.order-box .head {
  display: flex;
  width: 100%;
  border-bottom: 1px #f5f5f5 solid;
  height: 88rpx;
  line-height: 88rpx;
  overflow: hidden;
  color: #999;
}

.order-box .head .f1 {
  display: flex;
  align-items: center;
  color: #222222;
}

.order-box .head .f1 .img {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4px;
}

.order-box .head .f1 .t1 {
  color: #06A051;
  margin-right: 10rpx;
}

.order-box .head .f2 {
  color: #FF6F30;
}

.order-box .head .f2 .t1 {
  font-size: 36rpx;
  margin-right: 4rpx;
}

.order-box .content {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 16rpx 0px;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.order-box .content .f1 {
  width: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-box .content .f1 .t1 {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-box .content .f1 .t1 .x1 {
  color: #FF6F30;
  font-size: 28rpx;
  font-weight: bold;
}

.order-box .content .f1 .t1 .x2 {
  color: #999999;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.order-box .content .f1 .t2 .img {
  width: 12rpx;
  height: 36rpx;
  margin: 10rpx 0;
}

.order-box .content .f1 .t3 {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-box .content .f1 .t3 .x1 {
  color: #FF6F30;
  font-size: 28rpx;
  font-weight: bold;
}

.order-box .content .f1 .t3 .x2 {
  color: #999999;
  font-size: 24rpx;
}

.order-box .content .f2 {
  flex: 1;
  padding: 0 20rpx;
}

.order-box .content .f2 .t1 {
  font-size: 36rpx;
  color: #222222;
  font-weight: bold;
  line-height: 50rpx;
  margin-bottom: 6rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.order-box .content .f2 .t2 {
  font-size: 28rpx;
  color: #222222;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.order-box .content .f2 .t3 {
  font-size: 36rpx;
  color: #222222;
  font-weight: bold;
  line-height: 50rpx;
  margin-top: 30rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.order-box .content .f3 .img {
  width: 72rpx;
  height: 168rpx;
}

.order-box .op {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  padding: 20rpx 0px;
  border-top: 1px #f4f4f4 solid;
  color: #555;
}

.order-box .op .btn1 {
  width: 100%;
  background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);
  height: 88rpx;
  line-height: 88rpx;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 32rpx;
}

.filter-bar {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.filter-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #999999;
  padding: 10rpx 0;
  cursor: pointer;
}

.filter-item.active {
  color: #06A051;
  font-weight: bold;
  border-bottom: 2rpx solid #06A051;
}

.tabbar {
  position: fixed;
  bottom: 0;
  width: 100%;
}

.tabbar-bot {
  height: 20rpx;
  background-color: #ffffff;
}

.tabbar-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100rpx;
}

.tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.tabbar-image-box {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.tabbar-icon {
  width: 100%;
  height: 100%;
}

.tabbar-text {
  font-size: 24rpx;
  color: #999999;
}

.tabbar-text.active {
  color: #06A051;
}

</style>
