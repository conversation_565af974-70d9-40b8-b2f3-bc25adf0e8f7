<template>
<view>
	<block v-if="isload">
		<view class="topsearch flex-y-center">
				<view class="f1 flex-y-center">
					<image class="img" :src="pre_url+'/static/img/search_ico.png'"></image>
					<input :value="keyword" placeholder="搜索感兴趣的帖子" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
				</view>
			</view>
		<view class="container2">
			<image :src="banner" style="width:100%;height:auto" mode="widthFix" v-if="banner"></image>
			<view class="datalist">
			<block v-for="(item, index) in datalist" :key="index">
				<view class="item">
					<view class="top">
						<image :src="item.headimg" class="f1"></image>
						<view class="f2">
							<view class="t1">{{item.nickname}}</view>
							<view class="t2">{{item.showtime}}</view>
						</view>
					</view>
					
					<!-- 帖子主题 -->
					<view class="title-section" @tap="goto" :data-url="'detail?id=' + item.id">
						<text class="post-title">{{item.title || '无主题'}}</text>
					</view>
					
					<!-- 帖子内容 -->
					<view class="con" @tap="goto" :data-url="'detail?id=' + item.id">
						<view class="f1">
							<text class="content-text" :class="item.expanded ? 'expanded' : 'collapsed'">{{item.expanded ? item.content : (item.summary || item.content)}}</text>
							<text class="expand-btn" v-if="!item.expanded && (item.content.length > 100)" @tap.stop="toggleExpand(index)">展开</text>
							<text class="expand-btn" v-if="item.expanded" @tap.stop="toggleExpand(index)">收起</text>
						</view>
						<view class="f2" v-if="item.pics && !item.expanded">
							<image v-for="(pic, idx) in item.pics.slice(0,3)" :key="idx" :src="pic" mode="widthFix" style="height:auto"></image>
							<text class="more-pics" v-if="item.pics.length > 3">+{{item.pics.length - 3}}</text>
						</view>
						<view class="f2" v-if="item.pics && item.expanded">
							<image v-for="(pic, idx) in item.pics" :key="idx" :src="pic" mode="widthFix" style="height:auto"></image>
						</view>
						<video class="video" id="video" :src="item.video" v-if="item.video && item.expanded" @tap.stop="playvideo"></video>
						<view class="video-placeholder" v-if="item.video && !item.expanded" @tap.stop="toggleExpand(index)">
							<image :src="pre_url+'/static/img/video_play.png'" class="play-icon"></image>
							<text>点击展开查看视频</text>
						</view>
					</view>
					
					<!-- 评论预览 -->
					<view class="comment-preview" v-if="item.preview_comments && item.preview_comments.length > 0">
						<view class="comment-item" v-for="(comment, cidx) in item.preview_comments" :key="cidx">
							<image :src="comment.headimg" class="comment-avatar"></image>
							<text class="comment-nickname">{{comment.nickname}}:</text>
							<text class="comment-content">{{comment.content}}</text>
						</view>
						<text class="more-comments" v-if="item.plcount > 3" @tap="goto" :data-url="'detail?id=' + item.id">查看全部{{item.plcount}}条评论</text>
					</view>
					
					<view class="bot">
						<view class="f1"><image :src="pre_url+'/static/img/lt_eye.png'" style="margin-top:0"></image>{{item.readcount}}</view>
						<view class="f1" style="margin-left:60rpx;" @tap="quickComment" :data-id="item.id" :data-index="index"><image :src="pre_url+'/static/img/lt_pinglun.png'"></image>{{item.plcount}}</view>
						<view class="f2"></view>
						<view class="f4" @tap.stop="savecontent" :data-id="item.id" :data-index="index" v-if="sysset.cansave"><image :src="pre_url+'/static/img/lt_save.png'"></image>保存</view>
						<view class="f3" @tap.stop="zan" :data-id="item.id" :data-index="index"><image :src="pre_url+'/static/img/lt_like' + (item['iszan']==0?'':'2') + '.png'"></image>{{item.zan}}</view>
					</view>
				</view>
			</block>
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
			</view>
		</view>
		<view class="covermy" :class="menuindex>-1?'tabbarbot':'notabbarbot'" @tap="goto" :data-url="'fatie?cid=' + cid"><image :src="pre_url+'/static/img/lt_fatie.png'"></image></view>
		
		<!-- 快速评论弹窗 -->
		<view class="comment-modal" v-if="showCommentModal" @tap="hideCommentModal">
			<view class="comment-modal-content" @tap.stop="">
				<view class="comment-modal-header">
					<text>快速评论</text>
					<text class="close-btn" @tap="hideCommentModal">×</text>
				</view>
				<view class="comment-modal-body">
					<textarea class="comment-input" v-model="commentContent" placeholder="说点什么..." maxlength="200" auto-focus></textarea>
					<view class="comment-actions">
						<text class="char-count">{{commentContent.length}}/200</text>
						<button class="send-btn" @tap="sendComment" :disabled="!commentContent.trim()">发送</button>
					</view>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			
			sysset:{},
			cid:'',
      datalist: [],
      pagenum: 1,
      keyword: '',
      nomore: false,
      nodata: false,
			
			// 快速评论相关
			showCommentModal: false,
			commentContent: '',
			currentCommentIndex: -1,
			currentCommentId: 0
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.cid = this.opt.cid || '';
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
			that.nodata = false;
			that.nomore = false;
      that.loading = true;
      app.post('ApiLuntan/ltlist', {cid: that.cid,pagenum: pagenum,keyword:keyword}, function (res) {
				that.loading = false;
        that.loaddingmore = false;
        var data = res.datalist;
        if (pagenum == 1) {
					uni.setNavigationBarTitle({
						title: res.title
					});
					that.pernum = res.pernum;
					
					// 为每个帖子添加展开状态
					data.forEach(function(item) {
						item.expanded = false;
					});
					
					that.datalist = data;
					that.sysset = res.sysset;
					that.banner = res.banner;
					that.title = res.title;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
						// 为新数据添加展开状态
						data.forEach(function(item) {
							item.expanded = false;
						});
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
		
		// 切换内容展开/收起
		toggleExpand: function(index) {
			this.datalist[index].expanded = !this.datalist[index].expanded;
			this.$forceUpdate();
		},
		
		// 快速评论
		quickComment: function(e) {
			var that = this;
			var id = e.currentTarget.dataset.id;
			var index = e.currentTarget.dataset.index;
			
			that.currentCommentId = id;
			that.currentCommentIndex = index;
			that.commentContent = '';
			that.showCommentModal = true;
		},
		
		// 隐藏评论弹窗
		hideCommentModal: function() {
			this.showCommentModal = false;
			this.commentContent = '';
			this.currentCommentId = 0;
			this.currentCommentIndex = -1;
		},
		
		// 发送评论
		sendComment: function() {
			var that = this;
			if (!that.commentContent.trim()) {
				app.error('请输入评论内容');
				return;
			}
			
			app.post('ApiLuntan/quickComment', {
				id: that.currentCommentId,
				content: that.commentContent
			}, function(res) {
				if (res.status == 1) {
					app.success(res.msg);
					
					// 更新评论数
					if (that.currentCommentIndex >= 0) {
						that.datalist[that.currentCommentIndex].plcount++;
						
						// 如果有新评论且需要审核，不添加到预览中
						if (res.comment) {
							// 将新评论添加到预览列表前面
							if (!that.datalist[that.currentCommentIndex].preview_comments) {
								that.datalist[that.currentCommentIndex].preview_comments = [];
							}
							that.datalist[that.currentCommentIndex].preview_comments.unshift(res.comment);
							// 只保留前3条
							if (that.datalist[that.currentCommentIndex].preview_comments.length > 3) {
								that.datalist[that.currentCommentIndex].preview_comments.pop();
							}
						}
						
						that.$forceUpdate();
					}
					
					that.hideCommentModal();
				} else {
					app.error(res.msg);
				}
			});
		},
		
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword
      that.getdata();
    },
    zan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var index = e.currentTarget.dataset.index;
      var datalist = that.datalist;
      app.post("ApiLuntan/zan", {id: id}, function (res) {
        if (res.type == 0) {
          //取消点赞
          datalist[index].iszan = 0;
          datalist[index].zan = datalist[index].zan - 1;
        } else {
          datalist[index].iszan = 1;
          datalist[index].zan = datalist[index].zan + 1;
        }
        that.datalist = datalist;
      });
    },
		savecontent:function(e){
			var that = this;
			var index = e.currentTarget.dataset.index;
			var info = that.datalist[index];
			that.fuzhi(info.content,function(){
				that.savpic(info.pics,function(){
					that.savevideo(info.video);
				});
			})
		},
		fuzhi:function(content,callback){
			if(!content){
				typeof callback == 'function' && callback();
				return;
			}
			var that = this;
			uni.setClipboardData({
				data: content,
				success: function () {
					app.success('已复制到剪贴板');
					setTimeout(function(){
					typeof callback == 'function' && callback();
					},500)
				},
				fail:function(){
					app.error('请长按文本内容复制');
					setTimeout(function(){
						typeof callback == 'function' && callback();
					},500)
				}
			});
		},
		savpic:function(pics,callback){
			if(!pics){
				typeof callback == 'function' && callback();
				return;
			}
			if(app.globalData.platform == 'mp' || app.globalData.platform == 'h5'){
				app.error('请长按图片保存');return;
			}
			this.picindex = 0;
			this.savpic2(pics);
			typeof callback == 'function' && callback();
		},
		savpic2:function(pics){
			var that = this;
			var picindex = this.picindex;
			if(picindex >= pics.length){
				app.showLoading(false);
				app.success('已保存到相册');
				return;
			}
			var pic = pics[picindex];
			app.showLoading('图片保存中');
			uni.downloadFile({
				url: pic,
				success (res) {
					if (res.statusCode === 200) {
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success:function () {
								that.picindex++;
								that.savpic2(pics);
							},
							fail:function(){
								app.showLoading(false);
								app.error('保存失败');
							}
						})
					}
				},
				fail:function(){
					app.showLoading(false);
					app.error('下载失败');
				}
			});
		},
		savevideo:function(video){
			if(!video) return;
			app.showLoading('视频下载中');
			uni.downloadFile({
				url: video,
				success (res) {
					if (res.statusCode === 200) {
						uni.saveVideoToPhotosAlbum({
							filePath: res.tempFilePath,
							success:function () {
								app.showLoading(false);
								app.success('视频保存成功');
							},
							fail:function(){
								app.showLoading(false);
								app.error('视频保存失败');
							}
						})
					}
				},
				fail:function(){
					app.showLoading(false);
					app.error('视频下载失败!');
				}
			});
		},
    playvideo: function () {}
  }
};
</script>
<style>
.container2{width:100%;padding:20rpx;background:#fff;}

.topsearch{width:100%;padding:20rpx 20rpx;margin-bottom:10rpx;margin-bottom:10rpx;background:#fff}
.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1}
.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}


.navbox{background: #fff;height: auto;overflow: hidden;}
.nav_li{width:25%;text-align: center;box-sizing: border-box;padding:30rpx 0 10rpx;float: left;color:#222;font-size:24rpx}
.nav_li image{width:80rpx;height: 80rpx;margin-bottom:10rpx;}

.listtitle{width:100%;padding:0 24rpx;color:#222;font-weight:bold;font-size:32rpx;height:60rpx;line-height:60rpx}
.datalist{width:100%;padding:0 24rpx;}
.datalist .item{width:100%;display:flex;flex-direction:column;padding:24rpx 0;border-bottom:1px solid #f1f1f1}
.datalist .item .top{width:100%;display:flex;align-items:center}
.datalist .item .top .f1{width:80rpx;height:80rpx;border-radius:50%;margin-right:16rpx}
.datalist .item .top .f2 .t1{color:#222;font-weight:bold;font-size:28rpx}
.datalist .item .top .f2 .t2{color:#bbb;font-size:24rpx}

/* 主题样式 */
.title-section{width:100%;margin:16rpx 0;}
.post-title{color:#222;font-weight:bold;font-size:32rpx;line-height:1.4;}

.datalist .item .con{width:100%;padding:16rpx 0;display:flex;flex-direction:column;color:#000}
.datalist .item .con .f1{position:relative;}
.content-text{font-size:28rpx;line-height:1.6;color:#333;}
.content-text.collapsed{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:3;overflow:hidden;}
.content-text.expanded{white-space:pre-wrap;}
.expand-btn{color:#1989fa;font-size:26rpx;margin-left:10rpx;cursor:pointer;}

.datalist .item .con .f2{margin-top:16rpx;display:flex;flex-wrap:wrap;position:relative;}
.datalist .item .con .f2 image{width:31%;margin-right:2%;margin-bottom:10rpx;border-radius:8rpx}
.more-pics{position:absolute;right:2%;bottom:10rpx;background:rgba(0,0,0,0.6);color:#fff;padding:4rpx 8rpx;border-radius:4rpx;font-size:24rpx;}

.video-placeholder{width:100%;height:200rpx;background:#f5f5f5;border-radius:8rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin-top:16rpx;}
.play-icon{width:60rpx;height:60rpx;margin-bottom:10rpx;}
.video-placeholder text{color:#666;font-size:26rpx;}

.datalist .item .con .video{width:80%;height:300rpx;margin-top:20rpx}

/* 评论预览样式 */
.comment-preview{width:100%;background:#f8f9fa;border-radius:8rpx;padding:16rpx;margin:16rpx 0;}
.comment-item{display:flex;align-items:flex-start;margin-bottom:12rpx;}
.comment-item:last-child{margin-bottom:0;}
.comment-avatar{width:40rpx;height:40rpx;border-radius:50%;margin-right:12rpx;flex-shrink:0;}
.comment-nickname{color:#1989fa;font-size:24rpx;margin-right:8rpx;flex-shrink:0;}
.comment-content{color:#666;font-size:24rpx;line-height:1.4;flex:1;}
.more-comments{color:#1989fa;font-size:24rpx;margin-top:8rpx;display:block;}

.datalist .item .bot{width:100%;display:flex;align-items:center;color:#222222;font-size:28rpx}
.datalist .item .bot .f1{display:flex;align-items:center;font-weight:bold;cursor:pointer;}
.datalist .item .bot .f1 image{width:36rpx;height:36rpx;margin-right:16rpx;margin-top:2px}
.datalist .item .bot .f2{flex:1;}
.datalist .item .bot .f3{display:flex;align-items:center;font-weight:bold}
.datalist .item .bot .f3 image{width:40rpx;height:40rpx;margin-right:16rpx}
.datalist .item .bot .f4{display:flex;align-items:center;margin-right:30rpx}
.datalist .item .bot .f4 image{width:40rpx;height:40rpx;margin-right:10rpx}
.datalist .item .bot .btn2{color:#fff;background:#FE1A29;border:1px solid #FE1A29;padding:6rpx 40rpx;font-size:24rpx;border-radius:40rpx;margin-left:16rpx}

/* 快速评论弹窗样式 */
.comment-modal{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);z-index:9999;display:flex;align-items:flex-end;}
.comment-modal-content{width:100%;background:#fff;border-radius:16rpx 16rpx 0 0;padding:32rpx;max-height:80vh;}
.comment-modal-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:24rpx;}
.comment-modal-header text{font-size:32rpx;font-weight:bold;}
.close-btn{font-size:48rpx;color:#999;line-height:1;}
.comment-modal-body{}
.comment-input{width:100%;min-height:200rpx;padding:16rpx;border:1px solid #e5e5e5;border-radius:8rpx;font-size:28rpx;resize:none;}
.comment-actions{display:flex;justify-content:space-between;align-items:center;margin-top:16rpx;}
.char-count{color:#999;font-size:24rpx;}
.send-btn{background:#1989fa;color:#fff;border:none;padding:12rpx 32rpx;border-radius:6rpx;font-size:28rpx;}
.send-btn[disabled]{background:#ccc;color:#999;}

.covermy{position:fixed;z-index:99999;bottom:0;right:0;width:130rpx;height:130rpx;box-sizing:content-box}
.covermy image{width:100%;height:100%}

.nomore-footer-tips{background:#fff!important}
</style>