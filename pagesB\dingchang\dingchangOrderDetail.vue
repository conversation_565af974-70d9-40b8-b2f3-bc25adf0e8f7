<template>
	
	<view class="jc_center">

		<view class="content">
			
			<view class="state" v-if="data.status == 3">已关闭</view> 
			<view class="state" v-else-if="data.status == 2">已完成</view> 
			<view class="state" v-else-if="data.status == 1">已支付</view> 
			<view class="state" v-else>待支付</view> 
			
			<view class="message">
				<view class="message_txt1">{{data.venues_title}}</view>
				<view class="message_txt2">预定日期: {{data.days}}</view>
				<view class="message_txt2">时间场地：
				   <view v-for="(item, index) in data.order_detail" :key="index">
				   
				      <text style="margin-right: 10rpx;">{{item.region_name}}</text>
				   
				      <text v-for="(it, index) in item.charges_list" :key="ind">{{it.time_period}}  {{it.price}}元</text>
				   </view>
				</view>
				<view class="message_txt2">订单编号：{{data.ordernum}}</view>
				<view class="message_txt2">实付金额：{{data.totalprice}}元</view>
			</view>
			
			<view class="message1">
				<view class="message_txt1">手机号码：{{tel}}</view>
				
				<view class="message_txt2">凭改手机号后四位验证</view>
			</view>
			
			
		
			<view class="hxqrbox">
				<image :src="data.hexiao_qr" @tap="previewImage" :data-url="data.hexiao_qr" class="img"/>
				<view class="txt">请出示核销码给核销员进行核销</view>
			</view>
		
				
		</view>
		          
	</view>
	
	
</template>

<script>
	var app = getApp();
	export default {
	  data() {
	    return {
			opt:{},
			data : '',
			tel : ''
			
	    };
	  },
	  
	   onLoad: function (opt) {
			this.opt = app.getopts(opt);
			this.getData();  
			this.getTel();
	   },
	   
		methods: {
			getData(){
				var that = this;
				
				var obj ={
					id : this.opt.id
				}  
				
				app.get('/ApiVenues/getOrderDetail', obj, function(res) {
					
					console.log(res)
					
					if(res.status == 1){
						
					     that.data = res.data
							
					}   	   
							 
				});
			},
			
			getTel: function () {
				var that = this;
				app.get('ApiMy/set', {}, function (data) {
					console.log(data)
					that.tel = data.userinfo.tel.substring(0,3)+'****'+data.userinfo.tel.substring(7,11);
				});
			},
		},
	
	}
	
	
</script>

<style lang="scss">     
   
	  
.jc_center {  
	display: flex;
	justify-content: center;
	align-items: center;
}	  
	 
.content{
	width: 90%;
	padding-top: 50rpx;
}

.content .state{
   text-align: center;
	padding-bottom: 10px;
	font-size: 20px
}

.message{
	background-color: #ffffff;
	padding: 30rpx 40rpx 40rpx;
	border-radius: 20rpx;
	
	.message_txt1{
		font-size: 36rpx;
		font-weight: bold;
	}
	.message_txt2{
		font-size: 26rpx;
		color: #797979;
		margin-top: 15rpx;
	}
	.message_txt3{
		text-align: right;
		margin-top: 50rpx;
	}
	.message_txt3 :last-child {
		color: #6c64ec;
		font-size: 30rpx;
		font-weight: bold;
	}
}

.message1{
	background-color: #ffffff;
	padding:  30rpx 40rpx;
	margin-top: 30rpx;
	border-radius: 20rpx;
	display: flex;
	justify-content: space-between;
	
	.message_txt1{
		font-size: 28rpx;
		font-weight: bold;
	}
	.message_txt2{
		font-size: 24rpx;
		color: #797979;
	}
	
}


.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx;text-align: center;margin-top: 30rpx}
.hxqrbox .img{width:400rpx;height:400rpx}
.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
	
</style>