<template>
<view class="container">
	<block v-if="isload">
		<view class="header-container">
			<view class="config-selector">
				<picker @change="onConfigChange" :value="configIndex" :range="configOptions" range-key="name">
					<view class="picker-display">
						<text>{{selectedConfig.name || '全部活动'}}</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="filter-tabs">
				<view class="tab-item" 
					  v-for="(tab, index) in statusTabs" 
					  :key="index"
					  :class="{active: currentStatus == tab.value}"
					  @click="changeStatus(tab.value)">
					<text>{{tab.label}}</text>
				</view>
			</view>
		</view>
		
		<view class="content-container">
			<scroll-view class="scroll-view" scroll-y="true" @scrolltolower="loadmore" refresher-enabled="true" @refresherrefresh="onRefresh" :refresher-triggered="refreshing">
				<view class="record-list">
					<view class="record-item" v-for="(item, index) in recordList" :key="index">
						<view class="record-header">
							<view class="activity-info">
								<text class="activity-name">{{item.config_name}}</text>
								<text class="record-time">{{item.createtime_text}}</text>
							</view>
							<view class="status-badge" :class="item.status == 0 ? 'pending' : (item.status == 1 ? 'claimed' : 'expired')">
								<text>{{getStatusText(item.status)}}</text>
							</view>
						</view>
						
						<view class="record-content">
							<view class="reward-info">
								<view class="reward-item">
									<text class="label">奖励类型：</text>
									<text class="value">{{item.reward_type_text}}</text>
								</view>
								<view class="reward-item">
									<text class="label">奖励金额：</text>
									<text class="value amount" style="color: #FF5722">{{item.reward_amount}}</text>
								</view>
								<view class="reward-item" v-if="item.deduct_contribution > 0">
									<text class="label">扣除贡献值：</text>
									<text class="value deduct">-{{item.deduct_contribution}}</text>
								</view>
								<view class="reward-item" v-if="item.position_info">
									<text class="label">点位信息：</text>
									<text class="value">{{item.position_info}}</text>
								</view>
							</view>
							
							<view class="action-buttons" v-if="item.status == 0">
								<view class="btn-claim" style="background: linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%)"
									  @click="claimReward(item)">
									<text>立即领取</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<view class="empty-state" v-if="recordList.length == 0 && !loading">
					<image src="/static/img/empty-reward.png" class="empty-icon"/>
					<view class="empty-text">暂无奖励记录</view>
				</view>
				
				<view class="load-more" v-if="recordList.length > 0">
					<text v-if="loading">加载中...</text>
					<text v-else-if="!hasMore">没有更多了</text>
					<text v-else>上拉加载更多</text>
				</view>
			</scroll-view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			refreshing: false,
			configOptions: [{id: 0, name: '全部活动'}],
			configIndex: 0,
			selectedConfig: {id: 0, name: '全部活动'},
			statusTab: [
				{label: '全部', value: -1},
				{label: '待领取', value: 0},
				{label: '已领取', value: 1},
				{label: '已过期', value: 2}
			],
			currentStatus: -1,
			recordList: [],
			page: 1,
			hasMore: true
		};
	},
	
	computed: {
		statusTab() {
			return [
				{label: '全部', value: -1},
				{label: '待领取', value: 0},
				{label: '已领取', value: 1},
				{label: '已过期', value: 2}
			];
		}
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		var config_id = opt.config_id || 0;
		var status = opt.status || -1;
		this.currentStatus = parseInt(status);
		this.getdata(config_id);
	},
	onPullDownRefresh: function () {
		this.onRefresh();
	},
	methods: {
		getdata:function(preselectedConfigId){
			var that = this;
			that.loaded();
			that.getConfigs(preselectedConfigId);
		},
		
		getConfigs: function(preselectedConfigId){
			var that = this;
			app.post('ApiPaidan/getConfigs', {}, function (res) {
				if(res.code == 1){
					that.configOptions = [{id: 0, name: '全部活动'}].concat(res.data);
					
					// 如果有预选的配置ID，设置为选中状态
					if(preselectedConfigId > 0){
						for(var i = 0; i < that.configOptions.length; i++){
							if(that.configOptions[i].id == preselectedConfigId){
								that.configIndex = i;
								that.selectedConfig = that.configOptions[i];
								break;
							}
						}
					}
					
					that.getRecordList();
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		onConfigChange: function(e){
			var index = e.detail.value;
			this.configIndex = index;
			this.selectedConfig = this.configOptions[index];
			this.resetList();
			this.getRecordList();
		},
		
		changeStatus: function(status){
			this.currentStatus = status;
			this.resetList();
			this.getRecordList();
		},
		
		resetList: function(){
			this.recordList = [];
			this.page = 1;
			this.hasMore = true;
		},
		
		getRecordList: function(){
			var that = this;
			if(that.loading || !that.hasMore) return;
			
			that.loading = true;
			
			var params = {
				page: that.page,
				limit: 10
			};
			
			if(that.selectedConfig.id > 0){
				params.config_id = that.selectedConfig.id;
			}
			
			if(that.currentStatus >= 0){
				params.status = that.currentStatus;
			}
			
			app.post('ApiPaidan/getRewardRecords', params, function (res) {
				that.loading = false;
				that.refreshing = false;
				uni.stopPullDownRefresh();
				
				if(res.code == 1){
					var newList = res.data.list || [];
					
					if(that.page == 1){
						that.recordList = newList;
					}else{
						that.recordList = that.recordList.concat(newList);
					}
					
					that.hasMore = newList.length >= 10;
					that.page++;
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},
		
		loadmore: function(){
			this.getRecordList();
		},
		
		onRefresh: function(){
			this.refreshing = true;
			this.resetList();
			this.getRecordList();
		},
		
		claimReward: function(item){
			var that = this;
			
			uni.showModal({
				title: '确认领取',
				content: '确定要领取这笔奖励吗？',
				success: function(res) {
					if (res.confirm) {
						that.loading = true;
						
						app.post('ApiPaidan/claimReward', {
							reward_id: item.id
						}, function (res) {
							that.loading = false;
							
							if(res.code == 1){
								that.$refs.popmsg.show({type: 'success', msg: '领取成功'});
								// 更新当前项状态
								item.status = 1;
								item.claim_time = res.data.claim_time;
								item.claim_time_text = res.data.claim_time_text;
							}else{
								that.$refs.popmsg.show({type: 'error', msg: res.msg});
							}
						});
					}
				}
			});
		},
		
		getStatusClass: function(status){
			switch(status){
				case 0: return 'pending';
				case 1: return 'claimed';
				case 2: return 'expired';
				default: return 'pending';
			}
		},
		
		getStatusText: function(status){
			switch(status){
				case 0: return '待领取';
				case 1: return '已领取';
				case 2: return '已过期';
				default: return '未知';
			}
		},
		
		loaded: function () {
			this.isload = true;
		},
		
		getmenuindex: function (e) {
			this.menuindex = e;
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}
.header-container{width:100%;padding:30rpx;background-color:#fff;border-bottom:1px solid #f5f5f5;}
.config-selector{margin-bottom:20rpx;}
.picker-display{display:flex;justify-content:space-between;align-items:center;padding:20rpx;background:#f8f8f8;border-radius:12rpx;font-size:28rpx;color:#333;}
.picker-arrow{color:#999;}
.filter-tabs{display:flex;}
.tab-item{flex:1;text-align:center;padding:20rpx 0;font-size:28rpx;color:#666;border-bottom:4rpx solid transparent;}
.tab-item.active{color:#007aff;border-bottom-color:#007aff;font-weight:bold;}
.content-container{flex:1;height:100%;overflow:hidden;}
.scroll-view{width:100%;height:100%;}
.record-list{padding:20rpx;}
.record-item{background:#fff;margin-bottom:20rpx;border-radius:16rpx;padding:30rpx;box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);}
.record-header{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20rpx;}
.activity-info{flex:1;}
.activity-name{font-size:32rpx;color:#333;font-weight:bold;margin-bottom:8rpx;display:block;}
.record-time{font-size:24rpx;color:#999;}
.status-badge{padding:8rpx 16rpx;border-radius:20rpx;font-size:22rpx;}
.status-badge.pending{background:#fff2e8;color:#ff8f00;}
.status-badge.claimed{background:#e8f5e8;color:#4caf50;}
.status-badge.expired{background:#ffebee;color:#f44336;}
.record-content{}
.reward-info{margin-bottom:20rpx;}
.reward-item{display:flex;justify-content:space-between;margin-bottom:12rpx;font-size:28rpx;}
.label{color:#666;}
.value{color:#333;font-weight:bold;}
.value.amount{font-size:32rpx;}
.value.deduct{color:#f44336;}
.action-buttons{display:flex;justify-content:center;}
.btn-claim{padding:16rpx 40rpx;border-radius:40rpx;color:#fff;font-size:28rpx;text-align:center;}
.empty-state{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:100rpx 0;}
.empty-icon{width:200rpx;height:200rpx;margin-bottom:30rpx;}
.empty-text{font-size:28rpx;color:#666;}
.load-more{text-align:center;padding:30rpx;font-size:24rpx;color:#999;}
</style>