# 工作日历功能开发说明

## 功能概述

为服务人员提供工作日历功能，方便查看每日的工作安排和订单情况。通过日历视图，服务人员可以直观地看到哪些日期有订单，以及每个日期的订单数量和状态分布情况。

## 实现功能

1. **日历视图**：
   - 显示月份日历，支持月份切换
   - 标记当天日期特殊显示
   - 对有订单的日期进行标记，显示订单数量
   - 使用不同颜色标识不同状态的订单

2. **日期订单列表**：
   - 点击日期查看当天所有订单
   - 支持按订单状态筛选
   - 显示订单详细信息，包括客户信息、商品信息、预约时间等
   - 提供订单操作功能，如开始服务、完成服务等

3. **评价查看**：
   - 对于已评价的订单，提供评价详情查看功能
   - 显示服务评价和商品评价
   - 支持评价图片预览

## 技术实现

### 页面结构

- `/yuyue/calendar.vue`：主日历页面
- `/yuyue/my.vue`：添加工作日历入口

### API接口

1. **获取日历数据**
   - 接口：`/Apiyuyue/worker_calendar`
   - 请求参数：
     - worker_id：服务人员ID
     - year：年份
     - month：月份
     - status：可选，筛选状态
   - 返回数据：每天的订单统计和状态分布

2. **获取日期订单**
   - 接口：`/Apiyuyue/worker_date_orders`
   - 请求参数：
     - worker_id：服务人员ID
     - date：日期，格式YYYY-MM-DD
     - status：可选，筛选状态
   - 返回数据：订单详细信息列表

3. **获取订单评价**
   - 接口：`/Apiyuyue/order_comment`
   - 请求参数：
     - order_id：订单ID
   - 返回数据：订单的评价详情

4. **订单状态更新**
   - 接口：`ApiYuyueWorker/updateOrderStatus`
   - 请求参数：
     - order_id：订单ID
     - status：状态值（2:开始服务，3:完成服务）

### 状态说明

订单状态定义：
- 1：已支付
- 2：已派单
- 3：已完成
- 4：待评价
- 5：已评价

### UI设计

1. **日历视图**：
   - 使用网格布局展示日历
   - 不同状态订单使用不同颜色点标识
   - 今天日期使用特殊背景色突出显示

2. **订单列表**：
   - 参考 jdorderlist 的样式
   - 使用卡片式布局展示订单信息
   - 根据订单状态显示不同的操作按钮

3. **评价详情**：
   - 弹窗展示评价信息
   - 展示星级评分
   - 支持评价图片预览

## 使用方法

1. 在"我的"页面点击"工作日历"进入日历页面
2. 在日历上可以直观看到每天的订单情况
3. 点击有订单的日期，查看当天的订单详情
4. 可以按状态筛选当天订单
5. 对订单执行相应的操作
6. 对已评价的订单可查看评价详情

## 后续优化方向

1. 添加周视图和日视图，方便更细粒度查看
2. 提供订单搜索功能
3. 支持日历事件导出到系统日历
4. 增加订单提醒功能
5. 优化日历的响应速度和交互体验 