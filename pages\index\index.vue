<template>
	<view class="container" :style="{ backgroundColor: pageinfo.bgcolor }">
		
		<!-- 开屏遮罩层 -->
		<view v-if="showSplash" class="splash-overlay">
			<view class="splash-container">
				<!-- 图片开屏 -->
				<image 
					v-if="splashConfig.display_type === 'image'" 
					:src="splashConfig.image_url" 
					class="splash-image"
					mode="aspectFill"
					@click="handleSplashClick"
				/>
				
				<!-- 视频开屏 -->
				<video 
					v-if="splashConfig.display_type === 'video'"
					:src="splashConfig.video_url"
					class="splash-video"
					:autoplay="true"
					:muted="true"
					:controls="false"
					:loop="false"
					@click="handleSplashClick"
				/>
				
				<!-- 跳过按钮 -->
				<view 
					v-if="splashConfig.skip_enabled === 1" 
					class="splash-skip"
					@click="skipSplash"
				>
					<text v-if="skipCountdown > 0">{{skipCountdown}}s</text>
					<text v-else>跳过</text>
				</view>
				
				<!-- 进度条 -->
				<view class="splash-progress">
					<view 
						class="splash-progress-bar" 
						:style="{ width: progressPercent + '%' }"
					></view>
				</view>
				
				<!-- 链接按钮 -->
				<view 
					v-if="splashConfig.link_url && splashConfig.link_name" 
					class="splash-link"
					@click="handleSplashClick"
				>
					{{splashConfig.link_name}}
				</view>
			</view>
		</view>
		
		<!-- #ifdef MP-WEIXIN  -->
		<!-- <official-account></official-account> -->
		<!-- 微信小程序环境下的自定义导航栏 -->
		<view class="custom-menu" 
			:class="[
				!isValidPageInfo() ? 'menu-default' : '',
				pageinfo.menuStyle === 'fixed' ? 'menu-fixed' : '',
				pageinfo.menuStyle === 'float' && pageinfo.scrollOpacity === '1' ? 'menu-float' : '',
				pageinfo.isOpacity === '1' ? 'menu-opacity' : '',
				pageinfo.bgType === 'gradient' ? 'menu-gradient' : '',
				pageinfo.menuStyle === 'default' ? 'menu-default' : ''
			]"
			:style="{
				height: (menuTop+50) + 'px',
				'--nav-bg-color': isValidPageInfo() && pageinfo.bgType === 'solid' ? pageinfo.navBgColor : '',
				backgroundColor: !isValidPageInfo() || pageinfo.menuStyle === 'default' ? '#ffffff' :
					(pageinfo.bgType === 'gradient' && pageinfo.topGradient === '1' ? 'transparent' :
					(pageinfo.bgType === 'solid' ? pageinfo.navBgColor : 'transparent')),
				backgroundImage: isValidPageInfo() && pageinfo.bgType === 'gradient' && pageinfo.topGradient !== '1' ?
					`linear-gradient(${pageinfo.gradientDirection || 'to bottom'}, ${pageinfo.gradientStart || 'rgba(0,0,0,0.6)'}, ${pageinfo.gradientEnd || 'rgba(0,0,0,0)'} 50%, transparent 100%)` :
					(isValidPageInfo() && pageinfo.bgImage ? `url(${pageinfo.bgImage})` : 'none'),
				display: isValidPageInfo() && pageinfo.menuStyle === 'hidden' ? 'none' : 'block',
				paddingTop: menuTop+'px'
			}"
		>
			<view class="menu-content" 
				:class="{ 'menu-content-default': !isValidPageInfo() || pageinfo.menuStyle === 'default' }"
				:style="{
					textAlign: !isValidPageInfo() ? 'center' : pageinfo.menuAlign,
					color: !isValidPageInfo() || pageinfo.menuStyle === 'default' ? '#000000' : pageinfo.menuTextColor,
					height: !isValidPageInfo() || pageinfo.menuStyle === 'default' ? '100%' : (pageinfo.menuHeight - pageinfo.searchTopMargin + 'px'),
					lineHeight: !isValidPageInfo() || pageinfo.menuStyle === 'default' ? 'normal' : (pageinfo.menuHeight - pageinfo.searchTopMargin + 'px')
				}"
			>
				<!-- 默认模式下的标题 -->
				<template v-if="!isValidPageInfo() || pageinfo.menuStyle === 'default'">
					<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
					<view class="nav-content" :style="{ height: '44px' }">
						<text class="default-title">{{pageinfo.title || title || sysset.name || ''}}</text>
					</view>
				</template>
				
				<!-- 其他模式的内容 -->
				<template v-else>
					<view class="menu-left">
						<!-- Logo模式 -->
						<template v-if="pageinfo.leftMode !== 'location'">
							<image class="menu-logo" :src="sysset.logo" mode="aspectFit"/>
		<!-- 					<text class="menu-title" :style="{ color: getTextColor() }">
								{{pageinfo.title}}
							</text> -->
						</template>
						
						<!-- 位置模式 -->
						<template v-else>
							<view class="location-wrapper" @tap="handleLocationTap">
								<view class="location-content">
									<text class="location-text" :style="{ color: getTextColor() }">
										<block v-if="pageinfo.locationType === 'nearby'">
											<text v-if="isLocating">定位中...</text>
											<text v-else-if="locationFailed">定位失败</text>
											<text v-else>{{ area || '获取位置' }}</text>
										</block>
										<block v-else>
											<text>{{ currentAreaName || '选择区域' }}</text>
										</block>
									</text>
									<image 
										class="arrow-icon" 
										:class="{ 'is-locating': isLocating }"
										src="/static/img/arrowdown.png" 
										mode="aspectFit"
									/>
								</view>
							</view>
						</template>
					</view>
					<!-- 搜索框根据配置显示 -->
					<view v-if="pageinfo.showSearch === '1'" 
						class="menu-right search-container"
						:class="[
							pageinfo.searchPosition === 'left' ? 'search-left' : 
							pageinfo.searchPosition === 'center' ? 'search-center' : 
							'search-right'
						]"
					>
						<view class="search-box" 
							@tap="handleSearchTap" 
							:style="{
								width: pageinfo.searchWidth + 'px',
								height: pageinfo.searchHeight + 'px',
								lineHeight: pageinfo.searchHeight + 'px',
								borderRadius: pageinfo.searchRadius + 'px',
								backgroundColor: getSearchBoxBackground()
							}"
						>
							<image 
								src="/static/img/search.png" 
								mode="aspectFit"
								:style="{
									width: (pageinfo.searchHeight * 0.5) + 'px',
									height: (pageinfo.searchHeight * 0.5) + 'px'
								}"
							/>
							<text :style="{ 
								fontSize: (pageinfo.searchHeight * 0.4) + 'px',
								color: getSearchTextColor()
							}">
					
								{{'搜索'}}
							</text>
						</view>
					</view>
				</template>
			</view>
		</view>
		<!-- 导航栏占位元素 -->
		<view v-if="!isValidPageInfo() || pageinfo.menuStyle === 'fixed' || pageinfo.menuStyle === 'default'" 
			:style="{ height: (menuTop+40) + 'px' }"
		></view>
		
		<!-- 添加顶部渐变延伸效果，从导航栏开始无缝延伸 -->
		<view v-if="isValidPageInfo() && pageinfo.bgType === 'gradient' && pageinfo.topGradient === '1'"
			class="top-gradient-extension"
			:style="{
				backgroundImage: `linear-gradient(${pageinfo.gradientDirection || 'to bottom'}, ${pageinfo.gradientStart || 'rgba(0,0,0,0.6)'}, ${pageinfo.gradientEnd || 'rgba(0,0,0,0)'} 30%, transparent 100%)`,
				top: '0px'
			}"
		></view>
		<!-- #endif -->
		
		<block v-if="xixie && xdata && xdata.xixie_mendian">
			<dp-xixie-mendian :mendian_data="xdata.mendian_data" @changePopupAddress="changePopupAddress"></dp-xixie-mendian>
		</block>

		<block v-if="platform == 'wx' && xdata.xixie_mendian">
			<view class="navigation" :style="{ height: 44 + statusBarHeight + 'px', background: 'transparent' }">
				<view :style="{ height: statusBarHeight + 'px' }"></view>
				<view class="navcontent">
					<view class="topinfo">
						<image class="topinfoicon" :src="sysset.logo" />
						<view class="topinfotxt" :style="{ color: '#fff' }">{{ sysset.name }}</view>
					</view>
					<view class="topsearch" :style="{ width: screenWidth - 210 + 'px', background: 'rgba(255,255,255,0.2)' }" @tap="goto" data-url="/shopPackage/shop/search">
						<image src="/static/img/search.png" />
						<text style="font-size: 24rpx; color: #fff">搜索感兴趣的商品</text>
					</view>
				</view>
			</view>
			<view style="width: 100%" :style="{ height: 44 + statusBarHeight + 'px' }"></view>
		</block>
		
		<block v-if="sysset.mode == 1">
			<view class="navigation" :style="{ 
				height: 44 + 'px', 
				background: 'rgba(255,255,255,0.95)',
				backdropFilter: 'blur(10px)',
				WebkitBackdropFilter: 'blur(10px)'
			}">
				<view class="navcontent">
					<view class="topinfo">
						<image class="topinfoicon" :src="sysset.logo" mode="aspectFill"/>
						<view class="topinfotxt" :style="{ 
							color: '#333',
							fontWeight: '600'
						}">{{ sysset.name }}</view>
					</view>
					<view class="topR">
						<text class="btn-text" 
							:style="{
								background: 'linear-gradient(90deg,' + t('color1') + ' 0%,rgba(' + t('color1rgb') + ',0.8) 100%)',
								boxShadow: '0 2px 8px ' + t('color1') + '40'
							}" 
							@tap.stop="goto" 
							data-url="/pagesExt/business/clist2">
							<image 
								class="switch-icon" 
								:src="pre_url+'/static/img/switch.png'" 
								mode="aspectFit"
								:style="{
									width: '16px',
									height: '16px',
									transform: 'scale(1.2)',
									imageRendering: 'crisp-edges'
								}"
							/>
							切换
						</text>
						<block v-if="sysset.address">
							<text class="address-text">{{ sysset.address }}</text>
						</block>
					</view>
				</view>
			</view>
			<view style="width: 100%" :style="{ height: 44 + 'px' }"></view>
		</block>

		<!-- 外网模式 -->
		<block v-if="sysset.mode == 2">
			<view class="outernet-mode">
				<view class="loading-container">
					<image v-if="sysset.logo" class="outernet-logo" :src="sysset.logo" mode="aspectFit"></image>
					<view class="outernet-title" v-if="sysset.name">{{sysset.name}}</view>
					<view class="spinner"></view>
					<view class="loading-text">正在加载...</view>
					<view class="manual-jump" v-if="sysset.outernet_url" @tap="manualJump">
						<text>点击进入</text>
					</view>
				</view>
			</view>
		</block>

		<block v-if="sysset.agent_card == 1 && sysset.agent_card_info">
			<view style="height: 10rpx"></view>
			<view class="agent-card">
				<view class="flex-y-center row1">
					<image class="logo" :src="sysset.agent_card_info.logo" />
					<view class="text">
						<view class="title limitText flex">{{ sysset.agent_card_info.shopname }}</view>
						<view class="limitText grey-text">{{ sysset.agent_card_info.address }}</view>
						<view class="grey-text flex-y-center">
							<image class="img" :src="pre_url + '/static/img/my.png'"></image>
							<view>{{ sysset.agent_card_info.name }}</view>
							<image class="img" :src="pre_url + '/static/img/tel.png'" style="margin-left: 30rpx"></image>
							<view @tap="goto" :data-url="'tel::' + sysset.agent_card_info.tel" style="position: relative">
								{{ sysset.agent_card_info.tel }}
								<view class="btn" @tap="goto" :data-url="'tel::' + sysset.agent_card_info.tel">拨打</view>
							</view>
						</view>
					</view>
					<view class="right">
						<image :src="pre_url + '/static/img/shop_vip.png'" mode="aspectFit" style="width: 180rpx; height: 48.5rpx"></image>
					</view>
				</view>
				<view class="flex-y-center flex-x-center agent-card-b" :style="{ background: t('color2') }">
					<view @tap="goto" :data-url="'/pagesExt/agent/card'">
						<image class="img" :src="pre_url + '/static/img/shop.png'"></image>
						店铺信息
					</view>
					<view @tap="goto" :data-url="'/pages/commission/poster'">
						<image class="img img2" :src="pre_url + '/static/img/card.png'"></image>
						店铺海报
					</view>
				</view>
			</view>
		</block>

		<block v-if="sysset.showgzts">
			<view style="width: 100%; height: 88rpx"></view>
			<view class="follow_topbar">
				<view class="headimg">
					<image :src="sysset.logo" />
				</view>
				<view class="info">
					<view class="i">
						欢迎进入
						<text :style="{ color: t('color1') }">{{ sysset.name }}</text>
					</view>
					<view class="i">关注公众号享更多专属服务</view>
				</view>
				<view class="sub" @tap="showsubqrcode" :style="{ 'background-color': t('color1') }">立即关注</view>
			</view>
			<uni-popup id="qrcodeDialog" ref="qrcodeDialog" type="dialog">
				<view class="qrcodebox">
					<image :src="sysset.qrcode" @tap="previewImage" :data-url="sysset.qrcode" class="img" />
					<view class="txt">长按识别二维码关注</view>
					<view class="close" @tap="closesubqrcode">
						<image src="/static/img/close2.png" style="width: 100%; height: 100%" />
					</view>
				</view>
			</uni-popup>
		</block>

		<dp :pagecontent="pagecontent" :menuindex="menuindex" @getdata="getdata"></dp>

		<view :class="sysset.ddbb_position == 'bottom' ? 'bobaobox_bottom' : 'bobaobox'" v-if="oglist && oglist.length > 0">
			<swiper style="position: relative; height: 54rpx; width: 450rpx" autoplay="true" :interval="5000" vertical="true">
				<swiper-item v-for="(item, index) in oglist" :key="index" @tap="goto" :data-url="item.tourl" class="flex-y-center">
					<image :src="item.headimg" style="width: 40rpx; height: 40rpx; border: 1px solid rgba(255, 255, 255, 0.7); border-radius: 50%; margin-right: 4px"></image>
					<view style="width: 400rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 22rpx">
						<text style="padding-right: 2px">{{ item.nickname }}</text>
						<text style="padding-right: 4px">{{ item.showtime }}</text>
						<text style="padding-right: 2px" v-if="item.type == 'collage' && item.buytype == '2'">发起拼团</text>
						<text v-else>购买了</text>
						<text>{{ item.name }}</text>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<block v-if="xixie && xdata && xdata.popup_address">
			<dp-xixie-popup-address
				:xixie_login="xdata.xixie_login"
				:xixie_location="xdata.xixie_location"
				:address_latitude="latitude"
				:address_longitude="longitude"
				:code="code"
				@changePopupAddress="changePopupAddress"
				@getdata="getdata"
				@setMendianData="setMendianData"
			></dp-xixie-popup-address>
		</block>
		<block v-if="xixie && display_buy">
			<dp-xixie-buycart :cartnum="cartnum" :cartprice="cartprice" :color="t('color1')" :colorrgb="t('color1rgb')"></dp-xixie-buycart>
		</block>

		<view v-if="copyright != ''" class="copyright">{{ copyright }}</view>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<dp-guanggao :guanggaopic="guanggaopic" :guanggaourl="guanggaourl"></dp-guanggao>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>
<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			menuindex: -1,
			pre_url: app.globalData.pre_url,
			platform: app.globalData.platform,
			homeNavigationCustom: app.globalData.homeNavigationCustom,
			navigationBarBackgroundColor: app.globalData.navigationBarBackgroundColor,
			navigationBarTextStyle: app.globalData.navigationBarTextStyle,

			id: 0,
			pageinfo: {
				// 设置默认菜单样式为居中标题
				menuStyle: 'default',
				title: '',
				menuAlign: 'center',
				bgType: 'solid',
				navBgColor: '#ffffff',
				topGradient: '0' // 添加顶部渐变延伸控制
			},
			pagecontent: [],
			sysset: {},
			title: '',
			oglist: [],
			guanggaopic: '',
			guanggaourl: '',
			copyright: '',
			latitude: '',
			longitude: '',
			area: '',
			locationInfo: null,
			statusBarHeight: 20,
			screenWidth: 375,
			business: [],

			xixie: false,
			xdata: '',
			display_buy: '',
			cartnum: 0,
			cartprice: 0,
			code: '',
			area_id: '',
			area_name: '',
			bannerList: [
				{ image: '/static/images/banner1.png' },
				{ image: '/static/images/banner2.png' },
				{ image: '/static/images/banner3.png' }
			],
			scrollTop: 0,

			// 区域相关数据
			areaEnabled: false, // 区域功能是否启用
			currentAreaId: '', // 当前选中的区域ID
			currentAreaName: '', // 当前区域名称
			areaList: {}, // 区域列表
			isLocating: false, // 是否正在定位中
			locationFailed: false, // 定位是否失败
			menuTop : 0,

			// 开屏相关数据
			showSplash: false,
			splashConfig: {},
			splashStartTime: 0,
			splashDuration: 0,
			skipCountdown: 0,
			progressPercent: 0,
			splashTimer: null,
			splashVideo: null,
			splashImage: null,
		};
	},
	onLoad: function (opt) {
		let that = this;
		that.opt = app.getopts(opt);
		var sysinfo = uni.getSystemInfoSync();

		that.statusBarHeight = sysinfo.statusBarHeight;
		that.screenWidth = sysinfo.screenWidth;

		// 设置默认导航栏标题
		uni.setNavigationBarTitle({
			title: that.pageinfo.title || that.title || ''
		});
		
		// #ifdef MP-WEIXIN 
		let menu = uni.getMenuButtonBoundingClientRect();
		that.menuTop = menu.top; 
        console.log(menu);
		// #endif

		// 初始化区域数据
		that.initAreaData();

		// 监听区域选择事件
		uni.$on('city', function(data) {
			that.currentAreaId = data.id;
			that.currentAreaName = data.name;

			uni.setStorageSync('area_id', data.id);
			uni.setStorageSync('area_name', data.name);

			that.getdata();
		});

		// 监听位置选择事件
		uni.$on('location_selected', function(data) {
			that.latitude = data.latitude;
			that.longitude = data.longitude;
			that.area = data.display_name || (data.district + data.street); // 优先使用显示名称
			that.locationInfo = data;
			
			// 保存位置信息
			uni.setStorageSync('location_info', data);
			
			// 重新获取数据
			that.getdata(1);
		});

		// 监听商家切换事件
		uni.$on('updateAfterSwitch', (data) => {
			console.log('Received update event with data:', data);
			if (data && data.select_bid) {
				// 先保存新的select_bid到缓存
				app.setCache('select_bid', data.select_bid);
				
				// 检测是否为iOS设备
				const isIOS = uni.getSystemInfoSync().platform === 'ios';
				console.log('当前设备平台:', isIOS ? 'iOS' : '非iOS');
				
				if (isIOS) {
					// iOS设备使用更安全的方式：先更新数据，再使用navigateTo
					console.log('iOS设备：使用优化的页面切换方式');
					
					// 直接更新当前页面数据
					this.opt.select_bid = data.select_bid;
					
					// 显示加载中
					uni.showLoading({
						title: '加载中...',
						mask: true
					});
					
					// 先获取数据
					this.getdata();
					
					// 延迟关闭加载提示
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				} else {
					// 非iOS设备使用原来的reLaunch方式
					const newUrl = `/pages/index/index?select_bid=${data.select_bid}`;
					console.log('非iOS设备：使用reLaunch刷新页面，URL:', newUrl);
					
					// 使用reLaunch强制刷新页面
					uni.reLaunch({
						url: newUrl,
						success: () => {
							console.log('Page refreshed with new URL:', newUrl);
						},
						fail: (err) => {
							console.error('Failed to refresh page:', err);
							// 如果失败，至少更新数据
							this.opt.select_bid = data.select_bid;
							this.getdata();
						}
					});
				}
			}
		});

		// 检查是否需要自动定位
		// 从缓存中获取上次的位置信息
		const lastLocation = uni.getStorageSync('location_info');
		if (lastLocation) {
			this.latitude = lastLocation.latitude;
			this.longitude = lastLocation.longitude;
			this.area = lastLocation.district + lastLocation.street;
			this.locationInfo = lastLocation;
		}

		// 获取数据，不自动请求定位
		that.getdata();
	},

	onPullDownRefresh: function (e) {
		this.getdata();
	},
	onPageScroll: function (e) {
		uni.$emit('onPageScroll', e);
	},
	onUnload() {
		// 移除事件监听
		uni.$off('city');
		uni.$off('location_selected');
		// 清理事件监听
		uni.$off('updateAfterSwitch');
		
		// 清理开屏定时器
		if (this.splashTimer) {
			clearInterval(this.splashTimer);
			this.splashTimer = null;
		}
	},
	methods: {
		// 显示开屏
		showSplashScreen() {
			console.log('显示开屏');
			this.showSplash = true;
			
			// 通过事件通知tabbar隐藏
			uni.$emit('hideTabbar', true);
			
			this.startSplashCountdown();
			this.recordSplashShow();
		},

		// 隐藏开屏
		hideSplashScreen() {
			console.log('隐藏开屏');
			this.showSplash = false;
			
			// 通过事件通知tabbar显示
			uni.$emit('hideTabbar', false);
			
			// 清理倒计时
			if (this.splashTimer) {
				clearInterval(this.splashTimer);
				this.splashTimer = null;
			}
		},

		// 开始开屏倒计时
		startSplashCountdown() {
			if (!this.splashConfig) return;
			
			this.splashStartTime = Date.now();
			this.splashDuration = parseInt(this.splashConfig.display_time) * 1000 || 3000; // 默认3秒
			this.skipCountdown = parseInt(this.splashConfig.skip_time) || 2;
			this.progressPercent = 0;
			
			// 清理之前的定时器
			if (this.splashTimer) {
				clearInterval(this.splashTimer);
			}
			
			this.splashTimer = setInterval(() => {
				const currentTime = Date.now();
				const elapsedTime = currentTime - this.splashStartTime;
				
				// 更新进度条
				this.progressPercent = Math.min((elapsedTime / this.splashDuration) * 100, 100);
				
				// 更新跳过倒计时
				const skipTimeMs = parseInt(this.splashConfig.skip_time) * 1000;
				const remainingSkipTime = Math.max(0, Math.ceil((skipTimeMs - elapsedTime) / 1000));
				this.skipCountdown = remainingSkipTime;
				
				// 检查是否应该结束开屏
				if (elapsedTime >= this.splashDuration) {
					this.hideSplashScreen();
				}
			}, 100);
		},

		// 跳过开屏
		skipSplash() {
			// 检查跳过功能是否启用
			if (this.splashConfig.skip_enabled !== 1) {
				console.log('跳过功能未启用');
				return;
			}
			
			// 检查是否还在等待期内
			if (this.skipCountdown > 0) {
				console.log('还在跳过等待期内，剩余', this.skipCountdown, '秒');
				return;
			}
			
			console.log('用户跳过开屏');
			this.hideSplashScreen();
		},

		// 处理开屏点击
		handleSplashClick() {
			if (this.splashConfig && this.splashConfig.link_url) {
				console.log('点击开屏跳转:', this.splashConfig.link_url);
				// 先隐藏开屏
				this.hideSplashScreen();
				// 然后跳转
				app.gotopage(this.splashConfig.link_url);
			} else {
				// 如果没有链接，直接隐藏开屏
				this.hideSplashScreen();
			}
		},

		// 记录开屏显示统计
		recordSplashShow() {
			if (!this.splashConfig || !this.splashConfig.id) {
				return;
			}

			const params = {
				splash_id: this.splashConfig.id,
				platform: app.globalData.platform || 'unknown',
				action: 'show',
				timestamp: Date.now()
			};

			app.get('ApiIndex/recordsplashshow', params, function(data) {
				if (data.status == 1) {
					console.log('开屏统计记录成功');
				} else {
					console.log('开屏统计记录失败:', data.msg);
				}
			});
		},

		// 检查并初始化开屏
		checkAndInitSplash(splashConfig) {
			if (!splashConfig || splashConfig.is_enabled != 1) {
				console.log('开屏未启用');
				return;
			}

			// 验证开屏内容
			if (splashConfig.display_type === 'image' && !splashConfig.image_url) {
				console.log('开屏图片URL为空');
				return;
			}

			if (splashConfig.display_type === 'video' && !splashConfig.video_url) {
				console.log('开屏视频URL为空');
				return;
			}

			console.log('开屏配置有效，准备显示:', splashConfig);
			
			this.splashConfig = splashConfig;
			
			// 延迟一下显示开屏，确保页面已经渲染
			setTimeout(() => {
				this.showSplashScreen();
			}, 100);
		},
		
		getdata(event) {
			var that = this;
			var opt = this.opt;
			var id = 0;
			
			// 检查是否是特定组件的刷新请求
			let isSpecificComponent = false;
			let componentId = '';
			let componentStyle = '';
			
			// 判断是否为对象参数（来自组件的刷新请求）
			if (event && typeof event === 'object') {
				// 兼容不同环境的参数结构
				componentId = event.componentId || event.id || '';
				componentStyle = event.style || '';
				const forceRefresh = event.forceRefresh || false;
				
				if (componentId) {
					isSpecificComponent = true;
					console.log('首页收到特定组件刷新请求:', {
						组件ID: componentId,
						样式: componentStyle,
						强制刷新: forceRefresh,
						时间戳: event._timestamp || new Date().getTime()
					});
					
					// 处理强制刷新标记
					if (forceRefresh) {
						console.log('检测到强制刷新标记，将模拟前端刷新');
						
						// 尝试直接在前端进行数据刷新
						try {
							const targetComponent = this.findComponentById(componentId);
							if (targetComponent) {
								console.log('找到目标组件:', targetComponent.temp, targetComponent.id);
								
								// 确保组件有数据
								if (targetComponent.data && targetComponent.data.length > 0) {
									// 随机排序数据
									const shuffled = [...targetComponent.data].sort(() => 0.5 - Math.random());
									// 直接更新组件数据
									targetComponent.data = shuffled;
									console.log('前端数据刷新成功!');
									
									// 告知用户
									uni.showToast({
										title: '换一换成功',
										icon: 'none',
										duration: 1500
									});
									
									// 无需继续向后端发送请求
									return;
								}
							}
						} catch (error) {
							console.error('前端刷新数据失败:', error);
						}
					}
				}
			}
			
			// 从URL参数中获取select_bid
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			const query = currentPage.options || {};
			
			// 优先使用URL参数中的select_bid
			var select_bid = query.select_bid || opt.select_bid || app.getCache('select_bid');
			
			console.log('Loading data with select_bid:', select_bid);
			console.log('Current URL:', currentPage.route, query);

			let obj = {
				id: id,
				select_bid: select_bid,
				pid: app.globalData.pid,
				area: that.area
			};
			
			// 如果是特定组件的刷新请求，添加相关参数
			if (isSpecificComponent) {
				obj.componentId = componentId;
				obj.componentStyle = componentStyle;
			}

			// 打印请求参数以便调试
			console.log('Request params:', obj);

			if (typeof event === 'boolean' && event) {
				obj.latitude = that.latitude;
				obj.longitude = that.longitude;
			} else {
				obj.area_id = that.area_id;
			}

			// 显示加载状态
			that.loading = true;

			app.get('ApiIndex/index', obj, function (data) {
				console.log('API response:', data);
				that.loading = false;
				if (data.status == 2) {
					//付费查看
					app.goto('/pages/pay/pay?fromPage=index&id=' + data.payorderid + '&pageid=' + that.id, 'redirect');
					return;
				}
				if (data.status == 1) {
					// 处理特定组件的刷新
					if (isSpecificComponent && data.componentData) {
						console.log('接收到特定组件数据:', data.componentData);
						// 查找并更新特定组件
						for (let i = 0; i < that.pagecontent.length; i++) {
							if (that.pagecontent[i].id === componentId) {
								// 只更新数据部分，保留其他属性
								that.pagecontent[i].data = data.componentData;
								console.log('已更新组件数据:', componentId, that.pagecontent[i].temp);
								break;
							}
						}
						return; // 只更新组件数据，不做全局刷新
					}
					
					var pagecontent = data.pagecontent;
					that.title = data.pageinfo.title || '';
					if (data.oglist) that.oglist = data.oglist;
					that.guanggaopic = data.guanggaopic;
					that.guanggaourl = data.guanggaourl;
					
					// 检查开屏配置
					if (data.splash_config) {
						console.log('接收到开屏配置:', data.splash_config);
						that.checkAndInitSplash(data.splash_config);
					}
					
					// 检查pageinfo是否为空对象或未定义
					if (!data.pageinfo || Object.keys(data.pageinfo).length === 0) {
						console.log('未接收到后端菜单配置，使用默认居中标题样式');
						// 保持默认值不变
					} else {
						// 合并默认值和后端配置
						that.pageinfo = Object.assign({}, that.pageinfo, data.pageinfo);
						// 确保title被正确设置
						that.pageinfo.title = data.pageinfo.title || that.title || that.sysset.name || '';
					}

					that.copyright = data.copyright;
					that.sysset = data.sysset;
					
					// 门店模式下的处理
					if (data.sysset.mode == 1) {
						console.log('门店模式：主动请求定位');
						// 如果没有位置信息，主动请求定位
						if (!that.latitude || !that.longitude) {
							that.getCurrentLocation();
						}
						if (data.business) {
							that.business = data.business;
							if (select_bid == '') app.setCache('select_bid', data.business.id);
						}
					}
					
					// 外网模式处理 - 自动跳转到外部URL
					if (data.sysset.mode == 2 && data.sysset.outernet_url) {
						console.log('外网模式：准备跳转到外部URL', data.sysset.outernet_url);
						
						// 添加超时处理，如果3秒内没有成功跳转，仅显示手动跳转按钮
						let jumpTimer = setTimeout(() => {
							console.log('跳转超时，显示手动跳转按钮');
							that.loading = false;
						}, 3000);
						
						try {
							// 使用plus.runtime.openURL在app环境下打开外部链接
							// #ifdef APP-PLUS
							console.log('APP环境：使用plus.runtime.openURL跳转');
							plus.runtime.openURL(data.sysset.outernet_url);
							clearTimeout(jumpTimer);
							// #endif
							
							// 在小程序环境下，使用web-view页面跳转
							// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
							console.log('小程序环境：准备跳转到WebView页面');
							// 修正WebView页面路径
							const webviewUrl = '/pages/index/webView3?url=' + encodeURIComponent(data.sysset.outernet_url);
							console.log('跳转到的WebView路径：', webviewUrl);
							
							// 延迟500ms再跳转，让界面先渲染出来
							setTimeout(() => {
								// 使用reLaunch替代navigateTo，关闭所有页面并跳转到新页面，这样就不会有返回按钮
								uni.reLaunch({
									url: webviewUrl,
									success: function() {
										console.log('WebView跳转成功');
										clearTimeout(jumpTimer);
									},
									fail: function(err) {
										console.error('WebView跳转失败', err);
										// 跳转失败时不显示弹窗，只在日志中输出错误
										clearTimeout(jumpTimer);
									}
								});
							}, 500);
							// #endif
							
							// 在H5环境下直接跳转
							// #ifdef H5
							console.log('H5环境：使用location.href直接跳转');
							window.location.href = data.sysset.outernet_url;
							clearTimeout(jumpTimer);
							// #endif
						} catch (error) {
							console.error('外网跳转发生错误：', error);
							clearTimeout(jumpTimer);
							uni.showModal({
								title: '跳转错误',
								content: '跳转外部链接时发生错误：' + error.message,
								showCancel: false
							});
						}
						
						return; // 阻止继续执行后续代码
					}

					// 设置导航栏标题
					const displayTitle = that.pageinfo.title || that.title || that.sysset.name || '';
					uni.setNavigationBarTitle({
						title: displayTitle
					});

					that.loaded();

					// 初始化区域数据
					that.initAreaData();

					// 检查是否需要自动定位（非门店模式下的定位逻辑）
					if (data.sysset.mode != 1) {
						let json = JSON.parse(data.sysset.area_set || '{}');
						
						// 从两个地方获取自动定位设置：1. area_set中的auto_location 2. pageinfo中的auto_location
						let autoLocation = json.auto_location === '1' || that.pageinfo.auto_location === '1';
						
						// 检查是否为附近模式
						let isNearbyMode = that.pageinfo.locationType === 'nearby';
						
						console.log('定位设置检查：', {
							'area_set.auto_location': json.auto_location,
							'pageinfo.auto_location': that.pageinfo.auto_location,
							'pageinfo.locationType': that.pageinfo.locationType,
							'最终自动定位设置': autoLocation,
							'是否为附近模式': isNearbyMode,
							'当前纬度': that.latitude,
							'当前经度': that.longitude
						});

						// 当满足以下条件时自动请求定位：
						// 1. 自动定位设置为开启
						// 2. 当前没有位置信息或位置信息为空
						// 3. 位置类型为附近模式
						if ((that.latitude === '' || that.longitude === '' || !that.latitude || !that.longitude) && 
							(autoLocation && isNearbyMode)) {
							console.log('开始自动请求定位...');
							that.getCurrentLocation();
						} else {
							that.pagecontent = data.pagecontent;
						}
					} else {
						that.pagecontent = data.pagecontent;
					}

					if (data.xixie) {
						wx.login({
							success: function (res) {
								that.code = res.code;
							}
						});
						that.xixie = data.xixie;
						var xdata = data.xdata;
						that.xdata = xdata;

						that.display_buy = xdata.display_buy ? xdata.display_buy : false;
						that.cartnum = xdata.cartnum ? xdata.cartnum : 0;
						that.cartprice = xdata.cartprice ? xdata.cartprice : 0;
						if (xdata.cart_data) {
							setTimeout(function () {
								that.xcart_data = xdata.cart_data;
							}, 200);
						}
						if (xdata.popup_address) {
							setTimeout(function () {
								that.popup_address = data.popup_address;
							}, 200);
						}
					}
				} else {
					if (data.msg) {
						app.alert(data.msg, function () {
							if (data.url) app.goto(data.url);
						});
					} else if (data.url) {
						app.goto(data.url);
					} else {
						app.alert('您无查看权限');
					}
				}
			});
		},
		showsubqrcode: function () {
			this.$refs.qrcodeDialog.open();
		},
		closesubqrcode: function () {
			this.$refs.qrcodeDialog.close();
		},
		changePopupAddress: function (status) {
			this.xdata.popup_address = status;
		},
		setMendianData: function (data) {
			this.mendian_data = data;
		},
		handleBannerClick(item) {
			if (item.url) {
				uni.navigateTo({
					url: item.url
				})
			}
		},
		// 获取文字颜色
		getTextColor() {
			if (!this.isValidPageInfo()) {
				return '#000000'; // 默认黑色文字
			}
			
			if (this.pageinfo.menuStyle === 'float' || this.pageinfo.menuStyle === 'fixed') {
				if (this.pageinfo.isOpacity === '1' && this.pageinfo.opacity < 0.5) {
					return '#fff';
				}
			}
			return this.pageinfo.menuTextColor || '#000000';
		},
		
		// 获取搜索框背景色
		getSearchBoxBackground() {
			// 无论什么情况都返回白色背景
			return '#ffffff';
		},
		
		// 获取搜索框文字颜色
		getSearchTextColor() {
			if (!this.isValidPageInfo()) {
				return '#666'; // 默认搜索框文字颜色
			}
			
			if (this.pageinfo.searchTextColor) {
				return this.pageinfo.searchTextColor;
			}
			
			if (this.pageinfo.menuStyle === 'float' || this.pageinfo.menuStyle === 'fixed') {
				if (this.pageinfo.isOpacity === '1' && this.pageinfo.opacity < 0.5) {
					return '#fff';
				}
			}
			return '#666';
		},
		handleSearchTap() {
			const searchUrl = this.pageinfo.hrefurl || '/shopPackage/shop/search';
			app.goto(searchUrl);
		},
		// 初始化区域数据
		initAreaData() {
			// 先尝试从缓存获取区域信息
			const cachedAreaId = uni.getStorageSync('area_id');
			const cachedAreaName = uni.getStorageSync('area_name');
			
			if (this.sysset && this.sysset.area_on) {
				this.areaEnabled = true;
				
				// 优先使用缓存的区域信息
				if (cachedAreaId && cachedAreaName) {
					this.currentAreaId = cachedAreaId;
					this.currentAreaName = cachedAreaName;
				} else if (this.sysset.current_area_id && this.sysset.current_area_name) {
					// 如果没有缓存，使用系统设置的默认区域
					this.currentAreaId = this.sysset.current_area_id;
					this.currentAreaName = this.sysset.current_area_name;
					
					// 保存到缓存
					uni.setStorageSync('area_id', this.currentAreaId);
					uni.setStorageSync('area_name', this.currentAreaName);
				} else {
					// 如果都没有，尝试使用区域列表的第一个区域
					const areaList = this.sysset.area_list || {};
					if (Object.keys(areaList).length > 0) {
						const firstArea = Object.values(areaList)[0];
						this.currentAreaId = firstArea.id;
						this.currentAreaName = firstArea.name;
						
						// 保存到缓存
						uni.setStorageSync('area_id', this.currentAreaId);
						uni.setStorageSync('area_name', this.currentAreaName);
					}
				}
				
				// 区域列表
				this.areaList = this.sysset.area_list || {};
				
				console.log('当前区域信息：', {
					id: this.currentAreaId,
					name: this.currentAreaName
				});
			}
		},
		
		// 获取当前位置 - 修改为支持自动和手动触发
		getCurrentLocation() {
			if (this.isLocating) return;
			this.isLocating = true;
			this.locationFailed = false;
			
			console.log('正在获取位置...');
			
			app.getLocation((res) => {
				console.log('位置获取成功：', res);
				this.latitude = res.latitude;
				this.longitude = res.longitude;
				
				// 获取详细地址信息
				app.get('apiIndex/getLocation', {
					latitude: res.latitude,
					longitude: res.longitude,
					is_store_mode: this.sysset.mode == 1 ? 1 : 0 // 添加门店模式标识
				}, (result) => {
					console.log('地址信息获取结果：', result);
					if(result.status === 1) {
						// 更新地址信息
						this.area = result.data.district + result.data.street; // 只显示区域和街道
						this.locationInfo = result.data; // 保存完整地址信息
						// 保存位置信息
						uni.setStorageSync('location_info', result.data);
						
						// 根据模式决定是否重新获取数据
						if (this.sysset.mode == 1) {
							// 门店模式：使用type=1调用getdata，传递经纬度
							console.log('门店模式：使用位置信息重新获取数据');
							this.getdata(1);
						} else {
							// 非门店模式：只在需要时重新获取数据
							if (this.pageinfo.locationType === 'nearby') {
								this.getdata(1);
							} else {
								this.pagecontent = this.pagecontent;
							}
						}
					} else {
						this.locationFailed = true;
						console.error('获取地址信息失败：', result.msg || '未知错误');
						if (this.sysset.mode == 1) {
							// 门店模式下定位失败的处理
							app.alert('获取位置信息失败，这可能会影响到您查看附近门店的功能');
						}
					}
					this.isLocating = false;
				});
			}, (err) => {
				console.error('获取位置失败：', err);
				this.locationFailed = true;
				this.isLocating = false;
				
				// 获取失败时尝试使用上次保存的位置
				const lastLocation = uni.getStorageSync('location_info');
				if (lastLocation) {
					this.area = lastLocation.district + lastLocation.street;
					this.locationInfo = lastLocation;
					
					if (this.sysset.mode == 1) {
						// 门店模式下使用上次位置
						this.latitude = lastLocation.latitude;
						this.longitude = lastLocation.longitude;
						console.log('门店模式：使用上次保存的位置信息');
						this.getdata(1);
					}
				} else if (this.sysset.mode == 1) {
					// 门店模式下没有历史位置信息的处理
					app.alert('获取位置信息失败，这可能会影响到您查看附近门店的功能');
				}
			});
		},
		
		// 处理位置点击
		handleLocationTap() {
			if (this.pageinfo.locationType === 'nearby') {
				// 如果是附近模式，可以先尝试获取位置
				if (!this.latitude || !this.longitude) {
					// 如果没有位置信息，先获取位置
					this.getCurrentLocation();
					return;
				}
				
				// 跳转到位置搜索页面
				const params = {
					latitude: this.latitude,
					longitude: this.longitude,
					current_address: this.area,
					location_info: this.locationInfo
				};
				app.goto('/pages/index/location?data=' + encodeURIComponent(JSON.stringify(params)));
			} else {
				// 检查是否允许切换区域
				const areaConfig = JSON.parse(this.sysset.area_set || '{}');
				if (areaConfig.switcharea == 0) { // 当 switcharea 为 0 时允许切换区域
					const params = {
						area_list: this.sysset.area_list,
						current_area_id: this.currentAreaId,
						current_area_name: this.currentAreaName,
						area_config: areaConfig
					};
					app.goto('/pages/index/city?data=' + encodeURIComponent(JSON.stringify(params)));
				}
			}
		},
		
		// 检查pageinfo是否有效
		isValidPageInfo() {
			// 检查pageinfo是否为空对象或未定义或menuStyle未定义
			return this.pageinfo && 
				   Object.keys(this.pageinfo).length > 0 && 
				   this.pageinfo.menuStyle !== undefined;
		},
		// 手动跳转到外部URL
		manualJump: function() {
			const that = this;
			if (!that.sysset || !that.sysset.outernet_url) {
				uni.showModal({
					title: '跳转提示',
					content: '无效的外部链接',
					showCancel: false
				});
				return;
			}
			
			console.log('手动跳转到外部URL：', that.sysset.outernet_url);
			
			try {
				// APP环境
				// #ifdef APP-PLUS
				plus.runtime.openURL(that.sysset.outernet_url);
				// #endif
				
				// 小程序环境
				// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
				const webviewUrl = '/pages/index/webView3?url=' + encodeURIComponent(that.sysset.outernet_url);
				// 使用reLaunch关闭所有页面并跳转到新页面，去除返回按钮
				uni.reLaunch({
					url: webviewUrl,
					fail: function(err) {
						console.error('手动跳转失败', err);
						uni.showModal({
							title: '跳转提示',
							content: '跳转失败，请稍后再试或联系管理员',
							showCancel: false
						});
					}
				});
				// #endif
				
				// H5环境
				// #ifdef H5
				window.location.href = that.sysset.outernet_url;
				// #endif
			} catch (error) {
				console.error('手动跳转发生错误：', error);
				uni.showModal({
					title: '跳转错误',
					content: '跳转外部链接时发生错误：' + error.message,
					showCancel: false
				});
			}
		},
		// 新增方法：根据ID查找组件
		findComponentById(id) {
			if (!this.pagecontent || !this.pagecontent.length) return null;
			
			for (let i = 0; i < this.pagecontent.length; i++) {
				if (this.pagecontent[i].id === id) {
					return this.pagecontent[i];
				}
			}
			return null;
		},
	}
};
</script>
<style>
.topR {
	flex: 1;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	color: #666;
}

.topR .btn-text {
	padding: 4px 12px;
	background: v-bind('t("color1")');
	color: #fff;
	border-radius: 16px;
	font-size: 13px;
	font-weight: 500;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px v-bind('`${t("color1")}40`');
	display: flex;
	align-items: center;
	gap: 4px;
}



.btn-text:active {
	transform: scale(0.95);
	opacity: 0.9;
	box-shadow: 0 1px 4px v-bind('`${t("color1")}20`');
}

.address-text {
	font-size: 13px;
	color: #333;
	max-width: 180px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	opacity: 0.85;
	padding-left: 8px;
	position: relative;
}

.address-text::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 1px;
	height: 12px;
	background: rgba(0,0,0,0.08);
}

.follow_topbar {
	height: 88rpx;
	width: 100%;
	max-width: 640px;
	background: rgba(0, 0, 0, 0.8);
	position: fixed;
	top: 0;
	z-index: 13;
}

.follow_topbar .headimg {
	height: 64rpx;
	width: 64rpx;
	margin: 6px;
	float: left;
}

.follow_topbar .headimg image {
	height: 64rpx;
	width: 64rpx;
}

.follow_topbar .info {
	height: 56rpx;
	padding: 16rpx 0;
}

.follow_topbar .info .i {
	height: 28rpx;
	line-height: 28rpx;
	color: #ccc;
	font-size: 24rpx;
}

.follow_topbar .info {
	height: 80rpx;
	float: left;
}

.follow_topbar .sub {
	height: 48rpx;
	width: auto;
	background: #fc4343;
	padding: 0 20rpx;
	margin: 20rpx 16rpx 20rpx 0;
	float: right;
	font-size: 24rpx;
	color: #fff;
	line-height: 52rpx;
	border-radius: 6rpx;
}

.qrcodebox {
	background: #fff;
	padding: 50rpx;
	position: relative;
	border-radius: 20rpx;
}

.qrcodebox .img {
	width: 400rpx;
	height: 400rpx;
}

.qrcodebox .txt {
	color: #666;
	margin-top: 20rpx;
	font-size: 26rpx;
	text-align: center;
}

.qrcodebox .close {
	width: 50rpx;
	height: 50rpx;
	position: absolute;
	bottom: -100rpx;
	left: 50%;
	margin-left: -25rpx;
	border: 1px solid rgba(255, 255, 255, 0.6);
	border-radius: 50%;
	padding: 8rpx;
}

.bobaobox {
	position: fixed;
	top: calc(var(--window-top) + 180rpx);
	left: 20rpx;
	z-index: 10;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 30rpx;
	color: #fff;
	padding: 0 10rpx;
}

.bobaobox_bottom {
	position: fixed;
	bottom: calc(env(safe-area-inset-bottom) + 150rpx);
	left: 0;
	right: 0;
	width: 470rpx;
	margin: 0 auto;
	z-index: 10;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 30rpx;
	color: #fff;
	padding: 0 10rpx;
}

@supports (bottom: env(safe-area-inset-bottom)) {
	.bobaobox_bottom {
		position: fixed;
		bottom: calc(env(safe-area-inset-bottom) + 150rpx);
		left: 0;
		right: 0;
		width: 470rpx;
		margin: 0 auto;
		z-index: 10;
		background: rgba(0, 0, 0, 0.6);
		border-radius: 30rpx;
		color: #fff;
		padding: 0 10rpx;
	}
}

.navigation {
	width: 100%;
	position: fixed;
	z-index: 99;
	box-shadow: 0 2px 8px rgba(0,0,0,0.06);
	transition: all 0.3s ease;
}

.navcontent {
	display: flex;
	align-items: center;
	height: 44px;
	padding: 0 16px;
	justify-content: space-between;
}

.topinfo {
	display: flex;
	align-items: center;
	gap: 8px;
}

.topinfoicon {
	width: 28px;
	height: 28px;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
}

.topinfoicon:active {
	transform: scale(0.95);
}

.topinfotxt {
	font-size: 16px;
	transition: color 0.3s ease;
}

.topR {
	display: flex;
	align-items: center;
	gap: 8px;
}

.btn-text {
	padding: 4px 12px;
	color: #fff;
	border-radius: 16px;
	font-size: 13px;
	font-weight: 500;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	gap: 4px;
}

.switch-icon {
	width: 16px !important;
	height: 12px !important;
	opacity: 1;
	filter: brightness(1.1) contrast(1.1);
	transform: scale(1.2);
	image-rendering: -webkit-optimize-contrast;
	-webkit-font-smoothing: antialiased;
}

.btn-text:active {
	transform: scale(0.95);
	opacity: 0.9;
}

.address-text {
	font-size: 13px;
	color: #333;
	max-width: 180px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	opacity: 0.85;
	padding-left: 8px;
	position: relative;
}

.address-text::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 1px;
	height: 12px;
	background: rgba(0,0,0,0.08);
}

@supports (backdrop-filter: blur(10px)) {
	.navigation {
		background: rgba(255,255,255,0.8);
		backdrop-filter: blur(10px);
	}
}

.agent-card {
	height: auto;
	position: relative;
	background-color: #fff;
	margin: 0 20rpx 10rpx;
	font-size: 24rpx;
	border-radius: 0 10rpx 10rpx 10rpx;
	overflow: hidden;
	box-shadow: 0 0 8rpx 0px rgb(0 0 0 / 30%);
}

.agent-card .row1 {
	padding: 20rpx 10rpx 20rpx 20rpx;
}

.agent-card .logo {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
}

.agent-card .text {
	flex: 1;
	margin-left: 20rpx;
	color: #666;
	line-height: 180%;
}

.agent-card .title {
	color: #333;
	font-weight: bold;
	font-size: 32rpx;
}

.agent-card .right {
	height: 120rpx;
}

.agent-card .btn {
	position: absolute;
	right: -100rpx;
	padding: 0 14rpx;
	top: 0;
	border: 1px solid #b6c26e;
	border-radius: 10rpx;
	color: #b6c26e;
}

.agent-card .img {
	margin-right: 6rpx;
	width: 30rpx;
	height: 30rpx;
}

.agent-card .img2 {
	width: 32rpx;
	height: 32rpx;
}

.grey-text {
	color: #999;
	font-weight: normal;
}

.agent-card-b view {
	line-height: 72rpx;
	font-size: 28rpx;
	color: #444;
	width: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.agent-card-b view:first-child::after {
	content: '';
	width: 1px;
	height: 28rpx;
	border-right: 1px solid #444;
	position: absolute;
	right: 0;
}

/* 修改指示点样式 */
.wx-swiper-dots {
	position: relative;
	left: unset !important;
	right: 0;
}

.wx-swiper-dot {
	width: 16rpx !important;
	height: 16rpx !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.wx-swiper-dot-active {
	background: #ffffff !important;
}

.custom-banner {
	position: relative;
	width: 100%;
	height: 360rpx;
}

.banner-background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.swiper {
	position: relative;
	width: 92%;
	height: 300rpx;
	margin: 30rpx auto;
}

.swiper-item {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
}

.slide-image {
	width: 100%;
	height: 100%;
	border-radius: 16rpx;
}

/* 导航栏基础样式 */
.custom-menu {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 1000;
	transition: all 0.3s ease;
}

/* 固定和悬浮导航样式 */
.menu-fixed,
.menu-float {
	position: fixed;
}

/* 透明效果 */
.menu-opacity {
	background: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0)) !important;
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
}

/* 渐变背景效果 */
.menu-gradient {
	backdrop-filter: none !important;
	-webkit-backdrop-filter: none !important;
}

.menu-gradient.menu-opacity {
	background: none !important;
}

/* 导航内容布局 */
.menu-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20rpx;
}

/* 左侧布局 */
.menu-left {
	display: flex;
	align-items: center;
}

.menu-logo {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	margin-right: 10rpx;
}

.menu-title {
	font-size: 28rpx;
	font-weight: bold;
}

/* 搜索框容器 */
.search-container {
	position: relative;
	height: 100%;
	display: flex;
	align-items: center;
}

/* 搜索框基础样式 */
.search-box {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 20rpx;
	transition: all 0.3s ease;
	box-sizing: border-box;
	cursor: pointer;
}

.search-box:active {
	transform: scale(0.98);
	opacity: 0.9;
}

.search-box image {
	margin-right: 10rpx;
	opacity: 0.8;
}

.search-box text {
	transition: color 0.3s ease;
}

/* 搜索框位置样式 */
.search-left {
	margin-right: auto;
	margin-left: 20rpx;
}

.search-center {
	position: absolute;
	left: 28%;
	transform: translateX(-28%);
}

.search-right {
	margin-left: auto;
}

/* 文字阴影和搜索框阴影效果 */
.menu-fixed .menu-title,
.menu-float .menu-title,
.menu-opacity .menu-title,
.menu-gradient .menu-title {
	text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.menu-fixed .search-box,
.menu-float .search-box,
.menu-opacity .search-box,
.menu-gradient .search-box {
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 确保纯色背景不被其他样式覆盖 */
.custom-menu[style*="background-color"] {
	background-color: var(--nav-bg-color) !important;
}

/* 搜索框悬浮效果 */
.menu-float .search-box,
.menu-fixed .search-box {
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
}

/* 位置模式样式 */
.location-wrapper {
	display: flex;
	align-items: center;
	padding: 0 10rpx;
	cursor: pointer;
	min-width: 160rpx;
}

.location-content {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	
	border-radius: 8rpx;
}

.location-text {
	font-size: 28rpx;
	font-weight: bold;
	max-width: 200rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
	margin-left: 6rpx;
	transition: all 0.3s ease;
}

.arrow-icon.is-locating {
	animation: rotating 1s linear infinite;
}

@keyframes rotating {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.location-wrapper:active:not(.is-locating) .location-content {
	opacity: 0.8;
}

.location-wrapper:active:not(.is-locating) .arrow-icon {
	transform: rotate(180deg);
}

/* 定位状态样式 */
.location-text .locating {
	color: rgba(255, 255, 255, 0.7);
	font-weight: normal;
}

.location-text .location-failed {
	color: #ff4d4f;
}

/* 区域选择样式 */
.area-disabled {
	color: rgba(255, 255, 255, 0.5);
	font-style: italic;
}

/* 默认导航栏样式 */
.menu-default {
	position: fixed !important;
	top: 0;
	left: 0;
	width: 100%;
	background-color: #ffffff !important;
	border-bottom: 1rpx solid #f1f1f1;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	z-index: 999;
	padding: 0 !important;
	margin: 0 !important;
	height: auto !important;
}

.menu-content-default {
	display: flex;
	flex-direction: column;
	padding: 0 !important;
	margin: 0 !important;
	background-color: #ffffff !important;
	position: relative;
	height: 100% !important;
}

.status-bar {
	width: 100%;
	background-color: #ffffff;
	flex-shrink: 0;
}

.nav-content {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	position: relative;
	background-color: #ffffff;
	flex-shrink: 0;
}

.default-title {
	font-size: 34rpx;
	font-weight: 500;
	color: #000000;
	text-align: center;
	max-width: 400rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	line-height: 44px;
}

.menu-default .menu-left,
.menu-default .menu-right,
.menu-default .search-container {
	display: none;
}

.outernet-mode {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: #ffffff;
	z-index: 9999;
	display: flex;
	justify-content: center;
	align-items: center;
}

.outernet-logo {
	width: 80px;
	height: 80px;
	margin-bottom: 15px;
	border-radius: 10px;
	box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.outernet-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 30px;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-bottom: 80px;
}

.spinner {
	margin: 0 auto;
	border: 4px solid rgba(0, 0, 0, 0.1);
	border-left-color: #3a8eee;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.loading-text {
	margin-top: 15px;
	font-size: 16px;
	font-weight: bold;
	text-align: center;
	color: #333;
}

.manual-jump {
	margin-top: 20px;
	padding: 8px 20px;
	background: linear-gradient(90deg, #3a8eee 0%, #44a4ff 100%);
	color: #fff;
	border-radius: 20px;
	font-size: 14px;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
	text-align: center;
}

.manual-jump:active {
	transform: scale(0.95);
	opacity: 0.9;
}

/* 添加顶部渐变延伸层样式 */
.top-gradient-extension {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 600rpx; /* 增加高度确保覆盖足够区域 */
	z-index: -1; /* 作为背景层，在所有内容下方 */
	pointer-events: none; /* 确保不影响点击事件 */
}

.container {
	position: relative; /* 确保相对定位，使渐变层能够正确定位 */
	z-index: 1; /* 创建层叠上下文，让内容在渐变层上方 */
}

/* 开屏样式 */
.splash-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999999;
	background: #000;
	display: flex;
	justify-content: center;
	align-items: center;
}

.splash-container {
	width: 100%;
	height: 100%;
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.splash-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.splash-video {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.splash-skip {
	position: absolute;
	top: 60rpx;
	right: 40rpx;
	background: rgba(0, 0, 0, 0.6);
	color: #fff;
	padding: 16rpx 24rpx;
	border-radius: 40rpx;
	font-size: 24rpx;
	font-weight: 500;
	backdrop-filter: blur(10rpx);
	-webkit-backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
	cursor: pointer;
	z-index: 100001;
}

.splash-skip:active {
	transform: scale(0.95);
	opacity: 0.8;
}

.splash-progress {
	position: absolute;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 400rpx;
	height: 8rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 4rpx;
	overflow: hidden;
	backdrop-filter: blur(5rpx);
	-webkit-backdrop-filter: blur(5rpx);
	z-index: 100001;
}

.splash-progress-bar {
	height: 100%;
	background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));
	border-radius: 4rpx;
	transition: width 0.1s ease;
	box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
}

.splash-link {
	position: absolute;
	bottom: 200rpx;
	left: 50%;
	transform: translateX(-50%);
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
	color: #333;
	padding: 20rpx 40rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
	font-weight: 600;
	backdrop-filter: blur(15rpx);
	-webkit-backdrop-filter: blur(15rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	cursor: pointer;
	z-index: 100001;
	text-align: center;
	min-width: 200rpx;
}

.splash-link:active {
	transform: translateX(-50%) scale(0.95);
	opacity: 0.9;
}

/* 适配安全区域 */
@supports (bottom: env(safe-area-inset-bottom)) {
	.splash-progress {
		bottom: calc(100rpx + env(safe-area-inset-bottom));
	}
	
	.splash-link {
		bottom: calc(200rpx + env(safe-area-inset-bottom));
	}
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
	.splash-skip {
		top: 50rpx;
		right: 30rpx;
		padding: 12rpx 20rpx;
		font-size: 22rpx;
	}
	
	.splash-progress {
		width: 300rpx;
		bottom: 80rpx;
	}
	
	.splash-link {
		bottom: 160rpx;
		padding: 16rpx 32rpx;
		font-size: 26rpx;
	}
}

@media screen and (min-width: 768px) {
	.splash-skip {
		top: 80rpx;
		right: 60rpx;
		padding: 20rpx 32rpx;
		font-size: 28rpx;
	}
	
	.splash-progress {
		width: 500rpx;
		height: 10rpx;
		bottom: 120rpx;
	}
	
	.splash-link {
		bottom: 240rpx;
		padding: 24rpx 48rpx;
		font-size: 32rpx;
	}
}
</style>
