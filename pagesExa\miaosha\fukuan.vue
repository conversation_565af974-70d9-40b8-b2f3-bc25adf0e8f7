<template>
<view class="container">
	<block v-if="isload">
		<dd-tab :itemdata="['本场售出','本场购入']" :itemst="['0','1']" :st="st" :isfixed="true" @changetab="changetab"></dd-tab>
		<view style="width:100%;height:100rpx"></view>
		<!-- #ifndef H5 || APP-PLUS -->
		<!--  #endif -->
		<view class="order-content" v-if="st==0">
		<view class="order-box2">
		  <text class="order-title2">当前场次售卖金额</text>
		  <text class="order-amount2">{{changshouchu}}</text> 元
		</view>


			<block v-for="(item, index) in datalist" :key="index">
				<view class="order-box" @tap="goto" :data-url="'detail?id=' + item.neworderid">
					<view class="head">
						<view class="f1" v-if="item.bid!=0" @tap.stop="goto" :data-url="'/pagesExt/business/index?id=' + item.bid">
							<image src="/static/img/ico-shop.png"></image> {{item.binfo.name}}</view>
						<view class="f1" v-else><image :src="item.binfo.logo" class="logo-row"></image> 
						{{item.binfo.name}}
						</view>
						<view class="flex1"></view>
						<text style="color:red" v-if="item.prostatus2 == 0">未上架</text>
						<text style="color:red" v-if="item.prostatus2 == 2">委卖中</text>
						<text style="color:red" v-if="item.prostatus2 == 3">待付款</text>
						<text style="color:red" v-if="item.prostatus2 == 4">待确认</text>
						<text style="color:red" v-if="item.prostatus2 == 5">已提货</text>
						<text style="color:red" v-if="item.prostatus2 == 6">已确认</text>
					</view>
		
					<block v-for="(item2, idx) in item.prolist" :key="idx">
						<view class="content"   :style="idx+1==item.procount?'border-bottom:none':''">
							<view @tap.stop="goto" :data-url="'/pagesExa/miaosha/product?id=' + item.id">
								<image :src="item2.pic"></image>
							</view>
							<view class="detail">
								<text class="t1">{{item2.name}}</text>
								<text class="t2">{{item2.ggname}}</text>
								<view class="t3">
									<text class="x1 flex1">￥{{item2.sell_price}}</text>
								</view>
							</view>
						</view>
					</block>
					<view class="bottom">
						商品状态:
						<text style="color:red" v-if="item.prostatus2 == 0">未上架</text>
						<text style="color:red" v-if="item.prostatus2 == 2">委卖中</text>
						<text style="color:red" v-if="item.prostatus2 == 3">待付款</text>
						<text style="color:red" v-if="item.prostatus2 == 4">待确认</text>
						<text style="color:red" v-if="item.prostatus2 == 5">已提货</text>
						<text style="color:red" v-if="item.prostatus2 == 6">已确认</text>
					</view>
					<view class="bottom">
						购买人:
						<text style="color:red">{{item.gmr}}</text>
					</view>
				</view>
			</block>
		</view>
		<view class="order-content" v-if="st == 1">
			<view class="order-box2">
			  <text class="order-title2">当前场次购入金额</text>
			  <text class="order-amount2">{{goumai}}</text> 元
			</view>

			<block v-for="(item, index) in datalist" :key="index">
				<view class="order-box" @tap="goto" :data-url="'detail?id=' + item.id">
					<view class="head">
						<view class="f1" v-if="item.bid!=0" @tap.stop="goto" :data-url="'/pagesExt/business/index?id=' + item.bid"><image src="/static/img/ico-shop.png"></image> {{item.binfo.name}}</view>
						<view class="f1" v-else><image :src="item.binfo.logo" class="logo-row"></image> 
						{{item.binfo.name}}
						<!-- <view v-if="item.miaoshaid >0" style="margin-left: 10px;color: #ff4246;"> 秒杀订单</view> -->
						</view>
						<view class="flex1"></view>
						<text v-if="item.status==0" class="st0">待付款</text>
						<text v-if="item.status==1 && item.freight_type!=1" class="st1">待发货</text>
						<text v-if="item.status==1 && item.freight_type==1" class="st1">待提货</text>
						<text v-if="item.status==2" class="st2">待收货</text>
						<text v-if="item.status==3" class="st3">已完成</text>
						<text v-if="item.status==4" class="st4">已关闭</text>
					</view>

					<block v-for="(item2, idx) in item.prolist" :key="idx">
						<view class="content"   :style="idx+1==item.procount?'border-bottom:none':''">
							<view @tap.stop="goto"  v-if="item.miaoshaid ==0" :data-url="'/shopPackage/shop/product?id=' + item2.proid">
								<image :src="item2.pic"></image>
							</view>
							<view @tap.stop="goto"  v-if="item.miaoshaid >0" :data-url="'/pagesExa/miaosha/product?id=' + item.miaoshaid">
								<image :src="item2.pic"></image>
							</view>
							<view class="detail">
								<text class="t1">{{item2.name}}</text>
								<text class="t2">{{item2.ggname}}</text>
								<view class="t3">
									<text class="x1 flex1">￥{{item.totalprice}} </text> 
									<text class="x2">×{{item2.num}}</text>
								</view>
							</view>
						</view>
					</block>
					<view class="bottom">
						<text>共计{{item.procount}}件商品 实付:￥{{item.totalprice}}  <span v-if="item.balance_price > 0 && item.balance_pay_status == 0"  style="display: block; float: right;">尾款：￥{{item.balance_price}}</span></text>
					</view>
					<view class="bottom">售卖人昵称:
						<text style="color:red">{{item.outname}}</text>
					</view>
					<view class="op">
						<block v-if="([1,2,3]).includes(item.status) && item.invoice">
							<view class="btn2" @tap.stop="goto" :data-url="'invoice?type=shop&orderid=' + item.id">发票</view>
						</block>
						<view @tap.stop="goto" :data-url="'detail?id=' + item.id" class="btn2">详情</view>
			
			            <!-- <view  class="btn2" @tap="quxiaodingdan" :data-orderid="item.id">取消订单</view> -->
						<block v-if="item.paytypeid==5">
						    	<view @tap.stop="goto" :data-url="'detail2?id=' + item.id" class="btn2">查看凭证</view>
						</block>
						<!-- item. -->
						<block v-if="item.weituo == 1 &&item.status==3">
							<view @tap.stop="goto" :data-url="'buyweituo?id=' + item.id" class="btn2">委托上架</view>
						</block>
						<block v-if="item.status==0">
							<!-- <view class="btn2" @tap.stop="quxiaodingdan" :data-orderid="item.id">取消订单</view> -->
							<!-- <view class="btn2" @tap.stop="toclose" :data-id="item.id">关闭订单</view> -->
						<!-- 	<view  v-if="item.miaoshaid ==0">
							<view class="btn1" v-if="item.paytypeid!=5" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pages/pay/pay?id=' + item.payorderid">去付款</view>
							</view>
							<view  v-if="item.miaoshaid">
							<view class="btn1" v-if="item.paytypeid!=5" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pagesExa/miaosha/pay?id=' + item.payorderid">去付款</view>
							<view class="btn1" v-if="item.paytypeid!=5" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pagesExa/miaosha/payshangchuan?id=' + item.payorderid">去上传凭证</view>
							</view>
							
                            <block v-if="item.paytypeid==5">
                                <view class="btn1" v-if="item.transfer_check == 1" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pages/pay/transfer?id=' + item.payorderid">付款凭证</view>
                                <view class="btn1" v-else :style="{background:t('color1')}">
                                    <text v-if="item.transfer_check == 0">转账待审核</text>
                                    <text v-if="item.transfer_check == -1">转账已驳回</text>
                                </view>
                            </block> -->
							
						</block>
						<block v-if="item.status==1">
							<block v-if="item.paytypeid!='4'">
								<view class="btn2" @tap.stop="goto" :data-url="'refundSelect?orderid=' + item.id + '&price=' + item.totalprice" v-if="canrefund==1 && item.refundnum < item.procount">退款</view>
							</block>
							<block v-else>
								<!-- <view class="btn2">{{codtxt}}</view> -->
							</block>
						</block>
						<block v-if="item.status==2">
							<block v-if="item.paytypeid!='4'">
								<view class="btn2" @tap.stop="goto" :data-url="'refundSelect?orderid=' + item.id + '&price=' + item.totalprice" v-if="canrefund==1 && item.refundnum < item.procount">退款</view>
							</block>
							<block v-else>
								<!-- <view class="btn2">{{codtxt}}</view> -->
							</block>
							<block v-if="item.freight_type!=3 && item.freight_type!=4">
									<view class="btn2" v-if="item.express_type =='express_wx'" @tap.stop="logistics" :data-index="index">订单跟踪</view>
									<view class="btn2" v-else @tap.stop="logistics" :data-index="index">查看物流</view>
							</block>
							
							<view v-if="item.balance_pay_status == 0 && item.balance_price > 0" class="btn1" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pages/pay/pay?id=' + item.balance_pay_orderid">支付尾款</view>
							<view v-if="item.paytypeid!='4' && (item.balance_pay_status==1 || item.balance_price==0)" class="btn1" :style="{background:t('color1')}" @tap.stop="orderCollect" :data-id="item.id">确认收货</view>
						</block>
						<block v-if="(item.status==1 || item.status==2) && item.freight_type==1">
							<view class="btn2" @tap.stop="showhxqr" :data-hexiao_qr="item.hexiao_qr">核销码</view>
						</block>
						<view v-if="item.refundCount" class="btn2" @tap.stop="goto" :data-url="'refundlist?orderid='+ item.id">查看退款</view>
						<block v-if="item.status==3 || item.status==4">
							<!-- <view class="btn2" @tap.stop="todel" :data-id="item.id">删除订单</view> -->
						</block>
						<block v-if="item.bid>0 && item.status==3">
							<view v-if="item.iscommentdp==0" class="btn1" :style="{background:t('color1')}" @tap.stop="goto" :data-url="'/pagesExt/order/commentdp?orderid=' + item.id">评价店铺</view>
						</block>
					</view>
				</view>
			</block>
		</view>
		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>
	<view class="summary-section">
	  <view class="summary-text">
	    当前场次售卖金额 <text class="text-red">{{changshouchu}}</text> 元, 当前场次购入金额 <text class="text-red">{{goumai}}</text> 元
	  </view>
	  
	  <view class="action-buttons">
	    <view v-if="changshouchu == goumai" class="btn red-btn">
	      当前场次无需支付, 无需收款
	    </view>
	    <view v-if="changshouchu > goumai" class="btn red-btn" @tap="goto" :data-url="'pay3?id=' + changciid">
	      需收款 {{shoukuan}} 元 ，去收款
	    </view>
	    <view v-if="changshouchu < goumai" class="btn red-btn" @tap="goto" :data-url="'pay2?id=' + changciid">
	      去付款/查看付款
	    </view>
	  </view>
	</view>

	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      st: '0',
      datalist: [],
      pagenum: 1,
      nomore: false,
			nodata:false,
      codtxt: "",
			canrefund:1,
			express_content:'',
			selectExpressShow:false,
			hexiao_qr:'',
			keyword:'',
			changciid:0,
			changshouchu:0,
			goumai:0,
			shoukuan:0,
			fukuan:0,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		if(this.opt && this.opt.st){
			this.st = this.opt.st;
		}
		this.changciid = this.opt.id;
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
	onNavigationBarSearchInputConfirmed:function(e){
		this.searchConfirm({detail:{value:e.text}});
	},
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var st = that.st;
	  var changciid = that.changciid;
			that.nodata = false;
			that.nomore = false;
			that.loading = true;
      app.post('ApiMiaosha/jiesuan', {changciid:changciid,st: st,pagenum: pagenum,keyword:that.keyword}, function (res) {
			that.loading = false;
			if(res.status == 0)
			{
				app.error(res.msg)
				setTimeout(function () {
				  app.goback(-1);
				}, 1000);
			}else{
				var data = res.datalist;
				if (pagenum == 1) {
					that.changshouchu = res.changshouchu;
					that.goumai = res.goumai;
					that.shoukuan = that.changshouchu-that.goumai
					that.shoukuan = that.shoukuan.toFixed(0);
					that.fukuan=(that.goumai-that.changshouchu)
					that.fukuan = that.fukuan.toFixed(0);
					// that.changshouchu = 200;
					// that.goumai = 100;
					// that.canrefund = res.canrefund;
					that.datalist = data;
					if (data.length == 0) {
						that.nodata = true;
					}
					that.loaded();
				}else{
				  if (data.length == 0) {
					that.nomore = true;
				  } else {
					var datalist = that.datalist;
					var newdata = datalist.concat(data);
					that.datalist = newdata;
				  }
				}
			}
      });
    },

    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
		searchConfirm:function(e){
			this.keyword = e.detail.value;
      this.getdata(false);
		}
  }
};
</script>
<style>
	.bottom {
  display: flex;
  align-items: center;
  padding: 10rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 10rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
  font-weight: 500;
}

.status {
  font-size: 30rpx;
  color: #ff4246;
  font-weight: bold;
}

.buyer {
  font-size: 30rpx;
  color: #ff4246;
  font-weight: bold;
}

.container{ width:100%;}
.topsearch{width:94%;margin:10rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.order-content{display:flex;flex-direction:column;margin-bottom: 100px;}
.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}
.order-box .head .f1{display:flex;align-items:center;color:#333}
.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}
.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }
.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }
.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }
.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }
.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }

.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}
.order-box .content:last-child{ border-bottom: 0; }
.order-box .content image{ width: 140rpx; height: 140rpx;}
.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}
.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}
.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}
.order-box .content .detail .x1{ flex:1}
.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}

.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}

.btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}
.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}
.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:10rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}
.daifu-btn{background: #fc5729;}
.op{width:94%;margin:20rpx 3%;display:flex;align-items:center;margin-top:40rpx}
.op .btn{flex:1;height:100rpx;line-height:100rpx;background:#07C160;width:90%;margin:0 10rpx;border-radius:10rpx;color: #fff;font-size:28rpx;font-weight:bold;display:flex;align-items:center;justify-content:center}
.op .btn .img{width:48rpx;height:48rpx;margin-right:20rpx}


.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.hxqrbox .img{width:400rpx;height:400rpx}
.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}
.summary-section {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 20rpx 0;
  z-index: 999;
}

.summary-text {
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  text-align: center;
}

.text-red {
  color: #ff4246;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10rpx 20rpx;
}

.btn {
  display: flex;
  justify-content: center;  /* 水平居中 */
  align-items: center;       /* 垂直居中 */
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;  /* 设置按钮高度，保证高度一致 */
  border-radius: 6rpx;
  font-size: 30rpx;
  text-align: center;
}

.red-btn {
  background-color: #ff4246;
  color: #fff;
  font-weight: bold;
}

.order-box2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  margin: 40rpx 80rpx 40rpx 80rpx;  /* 增加 margin 来和上下元素分开 */
  background: #fff;  /* 高端白色背景 */
  border-radius: 20rpx;  /* 更加圆润的边角设计 */
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.08);  /* 强调的阴影，增加立体感 */
  border: 1rpx solid #e6e6e6;  /* 添加轻微的边框，突出卡片 */
  font-family: 'Helvetica Neue', sans-serif;
}

.order-title2 {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.order-amount2 {
  font-size: 36rpx;
  color: #ff4246;
  font-weight: bold;
}

/* 使元素与下面的内容有明显的分割 */
.order-box + * {
  margin-top: 60rpx;  /* 增加与下方元素的间距 */
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;  /* 添加轻微分割线，明确区分 */
}

</style>