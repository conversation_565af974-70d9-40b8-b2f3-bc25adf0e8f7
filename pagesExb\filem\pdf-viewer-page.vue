<template>
<view class="pdf-viewer-container">
  <view class="header">
    <view class="back-btn" @click="goBack">
      <text class="iconfont icon-back"></text>
    </view>
    <view class="title">
      <text>{{fileName}}</text>
    </view>
    <view class="right-placeholder"></view>
  </view>
  
  <!-- PDF查看器 -->
  <web-view v-if="pdfUrl" :src="viewerUrl" class="pdf-viewer"></web-view>
  
  <!-- 加载中 -->
  <view v-if="loading" class="loading">
    <text>PDF加载中...</text>
  </view>
  
  <!-- 错误提示 -->
  <view v-if="error" class="error">
    <text>{{errorMsg}}</text>
    <view class="retry-btn" @click="loadPdf">
      <text>重试</text>
    </view>
  </view>
</view>
</template>

<script>
export default {
  data() {
    return {
      id: '', // 文件ID
      fileName: '', // 文件名
      pdfUrl: '', // PDF文件URL
      viewerUrl: '', // PDF.js查看器URL
      loading: true, // 加载状态
      error: false, // 错误状态
      errorMsg: '' // 错误信息
    }
  },
  onLoad(options) {
    console.log('2025-03-17 10:00:00-INFO-[pdf-viewer-page][onLoad_001] 页面加载参数:', options);
    
    if (options.id) {
      this.id = options.id;
    }
    
    if (options.name) {
      this.fileName = decodeURIComponent(options.name);
    }
    
    // 加载PDF文件
    this.loadPdf();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 加载PDF文件
    loadPdf() {
      this.loading = true;
      this.error = false;
      
      console.log('2025-03-17 10:00:01-INFO-[pdf-viewer-page][loadPdf_001] 开始加载PDF文件, ID:', this.id);
      
      // 获取文件详情
      uni.request({
        url: this.$baseUrl + '/api/file/detail',
        method: 'GET',
        data: {
          id: this.id
        },
        success: (res) => {
          console.log('2025-03-17 10:00:02-INFO-[pdf-viewer-page][loadPdf_002] 文件详情获取成功:', res.data);
          
          if (res.data.code === 0 && res.data.data) {
            const detail = res.data.data;
            this.pdfUrl = detail.filepath;
            
            // 构建PDF.js查看器URL
            // 注意：需要对PDF URL进行编码
            const encodedPdfUrl = encodeURIComponent(this.pdfUrl);
            this.viewerUrl = `/static/pdfjs/web/viewer.html?file=${encodedPdfUrl}`;
            
            console.log('2025-03-17 10:00:03-INFO-[pdf-viewer-page][loadPdf_003] PDF查看器URL:', this.viewerUrl);
            
            this.loading = false;
          } else {
            this.handleError('获取文件详情失败: ' + (res.data.msg || '未知错误'));
          }
        },
        fail: (err) => {
          console.error('2025-03-17 10:00:04-ERROR-[pdf-viewer-page][loadPdf_004] 获取文件详情失败:', err);
          this.handleError('网络请求失败，请检查网络连接');
        }
      });
    },
    
    // 处理错误
    handleError(msg) {
      this.loading = false;
      this.error = true;
      this.errorMsg = msg || '加载PDF失败，请稍后重试';
      console.error('2025-03-17 10:00:05-ERROR-[pdf-viewer-page][handleError_001]', this.errorMsg);
    }
  }
}
</script>

<style>
.pdf-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  background-color: #007AFF;
  padding: 0 20rpx;
}

.back-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn text {
  color: #fff;
  font-size: 40rpx;
}

.title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 32rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 20rpx;
}

.right-placeholder {
  width: 80rpx;
}

.pdf-viewer {
  flex: 1;
  width: 100%;
}

.loading, .error {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading text {
  font-size: 30rpx;
  color: #666;
}

.error text {
  font-size: 30rpx;
  color: #ff3b30;
  margin-bottom: 30rpx;
}

.retry-btn {
  background: #007AFF;
  color: #fff;
  padding: 20rpx 60rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.retry-btn:active {
  background: #0056b3;
}
</style> 