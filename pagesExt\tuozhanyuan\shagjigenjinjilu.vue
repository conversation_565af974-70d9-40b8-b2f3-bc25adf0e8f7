<template>
<view class="container">
    <block v-if="isload">
        <view v-if="datalist.length === 0" class="no-data">暂无跟进记录</view>
       <block v-if="isload">
             <view v-if="!datalist.length" class="no-data">暂无跟进记录</view>
             <view v-for="(item, index) in datalist" :key="item.id" class="item">
               <view class="follow-up-content">
                 <text class="content">{{ item.follow_up_content }}</text>
                 <text class="date">跟进时间：{{ item.follow_up_date }}</text>
               </view>
             </view>
           </block>
    </block>
    
    <nomore v-if="nomore"></nomore>
    <nodata v-if="nodata"></nodata>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      datalist: [],
      pagenum: 1,
      nomore: false,
      nodata: false,
    };
  },

  onLoad(opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  
  onPullDownRefresh() {
    this.getdata();
  },
  
  onReachBottom() {
    if (!this.nodata && !this.nomore) {
      this.pagenum++;
      this.getdata(true);
    }
  },
  
  methods: {
    getdata(loadmore) {
      if (!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
      }

      const id = this.opt.id;
      if (!id) {
        app.error('参数错误');
        setTimeout(() => app.goback(), 1000);
        return;
      }

      this.nodata = false;
      this.nomore = false;
      this.loading = true;

      app.post('ApiTuozhancrm/getFollowUps', { id, pagenum: this.pagenum }, (res) => {
        this.loading = false;
        const data = res.data || [];
        
        if (this.pagenum === 1) {
          this.datalist = data;
          this.nodata = data.length === 0;
        } else {
          this.nomore = data.length === 0;
          if (!this.nomore) {
            this.datalist = this.datalist.concat(data);
          }
        }

        this.isload = true; // 数据加载完毕
      });
    }
  }
};
</script>

<style>
.container { padding: 20rpx; }
.item { width: 94%; margin: 0 3%; padding: 20rpx; background: #fff; margin-top: 20rpx; border-radius: 20rpx; }
.product-item2 { display: flex; padding: 20rpx 0; border-bottom: 1px solid #E6E6E6; }
.product-pic { width: 180rpx; height: 180rpx; background: #ffffff; overflow: hidden; }
.product-pic image { width: 100%; height: 100%; }
.product-info { flex: 1; padding: 5rpx 10rpx; }
.p1 { word-break: break-all; text-overflow: ellipsis; overflow: hidden; height: 80rpx; line-height: 40rpx; font-size: 30rpx; color: #111; }
.p2 { font-size: 32rpx; height: 40rpx; line-height: 40rpx; }
.p2 .t2 { margin-left: 10rpx; font-size: 26rpx; color: #888; text-decoration: line-through; }
.p3 { font-size: 24rpx; height: 50rpx; line-height: 50rpx; overflow: hidden; }
.p3 .t1 { color: #aaa; font-size: 24rpx; }
.p3 .t2 { color: #888; font-size: 24rpx; }
.foot { display: flex; align-items: center; width: 100%; height: 100rpx; line-height: 100rpx; color: #999; font-size: 24rpx; }
.no-data { text-align: center; color: #999; font-size: 24rpx; margin-top: 50rpx; }
</style>
