<template>
	<view>
		<block v-if="isload">
			<view class="container">
				<!-- 排行榜类型选择 -->
				<view class="tab-box">
					<view class="tab-item" :class="{'active': rankType === 1}" @tap="changeRankType(1)">个人风云榜</view>
					<view class="tab-item" :class="{'active': rankType === 2}" @tap="changeRankType(2)">商品风云榜</view>
				</view>
				
				<!-- 筛选条件 -->
				<view class="filter-box">
					<view class="filter-row">
						<picker :value="monthIndex" :range="monthList" range-key="name" @change="onMonthChange">
							<view class="picker-item">
								<text>{{getMonthName}}</text>
								<text class="icon-arrow"></text>
							</view>
						</picker>
					</view>
				</view>
				
				<!-- 排行榜列表 -->
				<view class="rank-list">
					<block v-for="(item, index) in rankList" :key="item.id">
						<!-- 个人风云榜显示 -->
						<view class="rank-item" v-if="rankType === 1">
							<view class="rank-num" :class="'rank-' + (index + 1)">
								<image v-if="index < 3" :src="getMedalImage(index)" mode="aspectFit" class="medal-icon"></image>
								<text v-else>{{index + 1}}</text>
							</view>
							<view class="info">
								<view class="name">{{item.nickname || '匿名用户'}}</view>
							</view>
						</view>
						
						<!-- 商品风云榜显示 -->
						<view class="rank-item" v-if="rankType === 2" @tap="goToProduct(item.id)">
							<view class="rank-num" :class="'rank-' + (index + 1)">
								<image v-if="index < 3" :src="getMedalImage(index)" mode="aspectFit" class="medal-icon"></image>
								<text v-else>{{index + 1}}</text>
							</view>
							<view class="info">
								<view class="name product-link">{{item.name || '-'}}</view>
								<view class="sales-info">
									<view class="sales-item" v-for="(tier, idx) in item.tier_sales" :key="idx">
										<text class="tier-name">{{tier.name}}</text>
										<text class="tier-num">{{tier.num}}盒</text>
									</view>
								</view>
							</view>
							<view class="total-sales">
								<text>总销量</text>
								<text class="num">{{item.total_num || 0}}盒</text>
							</view>
						</view>
					</block>
				</view>
				
				<!-- 加载更多 -->
				<view class="loading-more" v-if="loading">加载中...</view>
				<view class="no-more" v-if="nomore">没有更多数据了</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
	</view>
	</template>
	
	<script>
	var app = getApp();
	export default {
		data() {
			return {
				isload: false,
				loading: true,
				nomore: false,
				
				rankType: 1, // 1个人风云榜 2商品风云榜
				page: 1,
				limit: 10,
				
				rankList: [],
				monthList: [],
				monthIndex: 0,
				currentMonth: '', // 当前选中的月份值
				
				settings: null,
				pre_url: '',  // 域名前缀
				
				medals: {
					0: '/static/img/gold-medal.png',
					1: '/static/img/silver-medal.png',
					2: '/static/img/bronze-medal.png'
				}
			}
		},
		computed: {
			getMonthName() {
				if (this.monthList.length > 0 && this.monthList[this.monthIndex]) {
					return this.monthList[this.monthIndex].name;
				}
				return '选择月份';
			}
		},
		onLoad() {
			this.pre_url = app.globalData.pre_url;  // 获取域名前缀
			this.getMonthList(); // 先获取月份列表
			this.getRankingSetting();
		},
		onPullDownRefresh() {
			this.page = 1;
			this.rankList = [];
			this.getRankingList();
		},
		onReachBottom() {
			if(!this.nomore && !this.loading) {
				this.page++;
				this.getRankingList();
			}
		},
		methods: {
			// 获取排行榜设置
			getRankingSetting() {
				var that = this;
				app.get('ApiJietikaohe/getRankingSetting', {}, function(res) {
					if(res.code == 1) {
						that.settings = res.data;
						that.isload = true;
					}
				});
			},
			
			// 获取月份列表
			getMonthList() {
				var that = this;
				app.get('ApiJietikaohe/getMonthList', {}, function(res) {
					if(res.code == 1 && res.data && res.data.length > 0) {
						that.monthList = res.data;
						that.monthIndex = 0; // 默认选中第一个月份
						that.currentMonth = res.data[0].value;
						that.getRankingList(); // 获取第一个月份的数据
					}
				});
			},
			
			// 获取奖牌图片
			getMedalImage(index) {
				return this.pre_url + this.medals[index] || '';
			},
			
			// 获取排行榜数据
			getRankingList() {
				var that = this;
				if(!that.currentMonth) {
					return;
				}
				
				that.loading = true;
				var params = {
					type: that.rankType,
					page: that.page,
					limit: that.limit,
					month: that.currentMonth
				};
				
				app.get('ApiJietikaohe/getAllRankingList', params, function(res) {
					that.loading = false;
					uni.stopPullDownRefresh();
					
					if(res.code == 1) {
						if(that.page == 1) {
							that.rankList = res.data.list || [];
						} else {
							that.rankList = that.rankList.concat(res.data.list || []);
						}
						that.nomore = that.rankList.length >= (res.data.total || 0);
					} else {
						that.rankList = [];
						that.nomore = true;
					}
				});
			},
			
			// 切换排行类型
			changeRankType(type) {
				if(this.rankType === type) return;
				this.rankType = type;
				this.page = 1;
				this.rankList = [];
				this.getRankingList();
			},
			
			// 选择月份
			onMonthChange(e) {
				this.monthIndex = e.detail.value;
				this.currentMonth = this.monthList[this.monthIndex].value;
				this.page = 1;
				this.rankList = [];
				this.getRankingList();
			},
			
			// 跳转到商品详情
			goToProduct(id) {
				if(!id) return;
				uni.navigateTo({
					url: '/pages/product/detail?id=' + id
				});
			}
		}
	}
	</script>
	
	<style lang="scss">
	.container {
		padding: 30rpx 20rpx;
		background: #F8F9FD;
		min-height: 100vh;
		
		.tab-box {
			display: flex;
			background: #fff;
			padding: 20rpx;
			border-radius: 20rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);
			position: relative;
			overflow: hidden;
			
			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 200%;
				background: linear-gradient(180deg, rgba(250, 81, 81, 0.05) 0%, rgba(250, 81, 81, 0) 100%);
				opacity: 0.5;
				pointer-events: none;
			}
			
			.tab-item {
				flex: 1;
				text-align: center;
				font-size: 30rpx;
				color: #666;
				position: relative;
				padding: 24rpx 0;
				transition: all 0.3s ease;
				
				&.active {
					color: #FA5151;
					font-weight: 600;
					transform: scale(1.02);
					
					&:after {
						content: '';
						position: absolute;
						left: 50%;
						bottom: 0;
						transform: translateX(-50%);
						width: 60rpx;
						height: 6rpx;
						background: linear-gradient(90deg, #FA5151 0%, #FF7676 100%);
						border-radius: 6rpx;
					}
				}
			}
		}
		
		.filter-box {
			background: #fff;
			padding: 24rpx;
			border-radius: 20rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);
			
			.filter-row {
				display: flex;
				align-items: center;
				gap: 24rpx;
				
				.picker-item {
					flex: 1;
					height: 80rpx;
					background: #F8F9FD;
					border-radius: 16rpx;
					padding: 0 30rpx;
					font-size: 28rpx;
					color: #333;
					display: flex;
					align-items: center;
					justify-content: space-between;
					transition: all 0.3s ease;
					
					&:active {
						transform: scale(0.98);
						background: #F0F2F5;
					}
					
					.icon-arrow {
						display: inline-block;
						width: 0;
						height: 0;
						border-left: 12rpx solid transparent;
						border-right: 12rpx solid transparent;
						border-top: 12rpx solid #999;
						margin-left: 16rpx;
						transition: transform 0.3s ease;
					}
				}
			}
		}
		
		.rank-list {
			background: #fff;
			border-radius: 20rpx;
			padding: 16rpx 24rpx;
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);
			
			.rank-item {
				display: flex;
				align-items: center;
				padding: 30rpx 20rpx;
				border-bottom: 1px solid rgba(0, 0, 0, 0.04);
				transition: all 0.3s ease;
				
				&:last-child {
					border-bottom: none;
				}
				
				&:hover {
					background: rgba(250, 81, 81, 0.02);
					transform: translateX(4rpx);
				}
				
				.rank-num {
					width: 72rpx;
					height: 72rpx;
					line-height: 72rpx;
					text-align: center;
					font-size: 34rpx;
					font-weight: 600;
					margin-right: 30rpx;
					border-radius: 50%;
					color: #999;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
					
					&.rank-1, &.rank-2, &.rank-3 {
						background: transparent;
						box-shadow: none;
						
						.medal-icon {
							width: 72rpx;
							height: 72rpx;
							transition: all 0.3s ease;
						}
					}
					
					&:not(.rank-1):not(.rank-2):not(.rank-3) {
						background: #F8F9FD;
						border: 2rpx solid rgba(0, 0, 0, 0.05);
					}
				}
				
				.info {
					flex: 1;
					padding: 0 20rpx;
					
					.name {
						font-size: 32rpx;
						color: #333;
						margin-bottom: 12rpx;
						font-weight: 500;
						
						&.product-link {
							position: relative;
							cursor: pointer;
							
							&:hover {
								color: #FA5151;
							}
							
							// 添加文字溢出省略
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							max-width: 400rpx;
						}
					}
					
					.sales-info {
						display: flex;
						flex-wrap: wrap;
						gap: 16rpx;
						
						.sales-item {
							background: #F8F9FD;
							padding: 8rpx 16rpx;
							border-radius: 8rpx;
							font-size: 24rpx;
							
							.tier-name {
								color: #666;
								margin-right: 8rpx;
							}
							
							.tier-num {
								color: #FA5151;
								font-weight: 500;
							}
						}
					}
				}
				
				.total-sales {
					display: flex;
					flex-direction: column;
					align-items: flex-end;
					padding: 0 10rpx;
					
					text {
						font-size: 24rpx;
						color: #999;
						
						&.num {
							font-size: 34rpx;
							font-weight: 600;
							color: #FA5151;
							margin-top: 4rpx;
						}
					}
				}
			}
		}
		
		.loading-more,
		.no-more {
			text-align: center;
			font-size: 26rpx;
			color: #999;
			padding: 40rpx 0;
			letter-spacing: 2rpx;
			
			&::before,
			&::after {
				content: '';
				display: inline-block;
				width: 100rpx;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1));
				margin: 0 24rpx;
				vertical-align: middle;
			}
			
			&::after {
				background: linear-gradient(90deg, rgba(0, 0, 0, 0.1), transparent);
			}
		}
	}
	
	.rank-list {
		.rank-item {
			.rank-num {
				.medal-icon {
					width: 72rpx;
					height: 72rpx;
				}
			}
		}
	}
	</style> 