<template>
	<view class="container">
		<block v-if="isload">
			<view class="mymoney" :style="{background:t('color1')}">
				<view class="f1">积分转佣金</view>
				<view class="f2"><text style="font-size:26rpx">积分：</text>{{userinfo.score || 0}}</view>
				<view class="f3" @tap="goto" data-url="/pages/money/moneylog?st=6"><text>转换记录</text>
				<text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view>
				<view class="f1" style="font-size:20rpx;margin-top: 20px;">转换比例</view>
				<view class="f2"><text style="font-size:26rpx"></text>{{config.rate || 100}} ：1</view>
			</view>
			
			<view class="content2">
				<view class="balance-info">
					<view class="balance-item">
						<text class="balance-label">当前积分</text>
						<text class="balance-value">{{userinfo.score || 0}}</text>
					</view>
					<view class="balance-item">
						<text class="balance-label">当前佣金</text>
						<text class="balance-value">{{userinfo.commission || 0}}</text>
					</view>
				</view>

				<view class="rules-section">
					<view class="rules-title">转换规则</view>
					<view class="rules-content">
						<view class="rule-item">• 兑换比例：{{config.rate || 100}}积分 = 1佣金</view>
						<view class="rule-item" v-if="config.beishu > 0">• 转换倍数：必须是{{config.beishu}}的倍数</view>
						<view class="rule-item">• 转换范围：{{config.min || 0}} - {{config.max > 0 ? config.max : '不限制'}}积分</view>
						<view class="rule-item" v-if="config.fee_rate > 0">• 手续费：{{(config.fee_rate * 100).toFixed(2)}}%</view>
					</view>
				</view>

				<block v-if="caninput==1">
					<view class="item3">
						<view class="f2"><input type="digit" name="score" :value="score" placeholder="请输入转换积分数量"
								placeholder-style="color:#999;font-size:40rpx" @input="scoreinput"
								style="font-size:60rpx" /></view>
					</view>
				</block>
				
				<view class="preview-section" v-if="score > 0">
					<view class="preview-item">
						<text class="preview-label">预计获得佣金：</text>
						<text class="preview-value">{{calculatedCommission.toFixed(6)}}</text>
					</view>
					<view class="preview-item" v-if="calculatedFee > 0">
						<text class="preview-label">手续费：</text>
						<text class="preview-value fee">{{calculatedFee.toFixed(6)}}</text>
					</view>
				</view>

				<block v-if="caninput==1 && config.need_paypwd">
					<view class="item3">
						<view class="f2"><input type="number" name="paypwd" :value="paypwd" placeholder="请输入支付密码"
								placeholder-style="color:#999;font-size:40rpx" @input="paypwdinput"
								style="font-size:60rpx" maxlength="6" /></view>
					</view>
				</block>
				
				<view style="margin-top:40rpx;padding:0 30rpx;line-height:42rpx;" v-if="shuoming">
					<parse :content="shuoming" @navigate="navigate"></parse>
				</view>
			</view>
			
			<view class="op">
				<view class="btn" @tap="convert" :style="{background:t('color1')}">确认转换</view>
			</view>

			<view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center" @tap="goto" data-url="/pages/my/paypwd" v-if="config.need_paypwd && !userinfo.has_paypwd">设置支付密码<image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx"/></view>
				
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				textset: {},
				canrecharge: 0,
				userinfo: {},
				config: {
					status: 0,
					rate: 100,
					beishu: 0,
					min: 0,
					max: 0,
					need_paypwd: false,
					fee_rate: 0
				},
				shuoming: '',
				score: '',
				paypwd: '',
				calculatedCommission: 0,
				calculatedFee: 0,
				caninput: 1
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();
		},
		
		onPullDownRefresh: function() {
			this.getdata();
		},
		
		methods: {
			getdata: function() {
				var that = this;
				app.loading = true;
				app.get('ApiMoney/scoreToCommission', {}, function(res) {
					app.loading = false;
					if (res.status == 0) {
						app.error(res.msg || '功能未开启');
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
						return;
					}
					that.isload = true;
					that.textset = app.globalData.textset;
					uni.setNavigationBarTitle({
						title: '积分转佣金'
					});
					
					that.config = {
						status: res.status,
						rate: res.rate || 100,
						beishu: res.beishu || 0,
						min: res.min || 0,
						max: res.max || 0,
						need_paypwd: res.need_paypwd || false,
						fee_rate: res.fee_rate || 0
					};
					that.userinfo = res.userinfo || {};
					that.shuoming = res.shuoming || '';
					that.caninput = res.caninput || 1;
					
					if (that.config.need_paypwd && !res.has_paypwd) {
						app.error('请先设置支付密码');
					}
					
					that.loaded();
				});
			},
			
			scoreinput: function(e) {
				var score = e.detail.value;
				if (parseFloat(score) < 0) {
					app.error('必须大于0');
					return;
				}
				
				this.score = score;
				this.calculateCommission();
			},
			
			paypwdinput: function(e) {
				var paypwd = e.detail.value;
				this.paypwd = paypwd.trim();
			},
			
			calculateCommission: function() {
				const score = parseInt(this.score) || 0;
				if (score <= 0) {
					this.calculatedCommission = 0;
					this.calculatedFee = 0;
					return;
				}
				
				const commission = score / this.config.rate;
				const fee = commission * this.config.fee_rate;
				
				this.calculatedCommission = commission - fee;
				this.calculatedFee = fee;
			},
			
			convert: function(e) {
				var that = this;
				var score = that.score;
				var paypwd = that.paypwd;
				
				if (!score) {
					return app.error('请输入转换积分数量');
				}
				
				const scoreNum = parseInt(score);
				
				if (scoreNum < that.config.min) {
					return app.error('转换积分不能少于' + that.config.min);
				}
				
				if (that.config.max > 0 && scoreNum > that.config.max) {
					return app.error('转换积分不能超过' + that.config.max);
				}
				
				if (that.config.beishu > 0 && scoreNum % that.config.beishu !== 0) {
					return app.error('转换积分必须是' + that.config.beishu + '的倍数');
				}
				
				if (scoreNum > parseFloat(that.userinfo.score || 0)) {
					return app.error('积分余额不足');
				}
				
				if (that.config.need_paypwd && !paypwd) {
					return app.error('请输入支付密码');
				}
				
				// 确认转换
				const confirmMsg = `确认转换${scoreNum}积分为${that.calculatedCommission.toFixed(6)}佣金吗？${that.calculatedFee > 0 ? '\n手续费：' + that.calculatedFee.toFixed(6) + '佣金' : ''}`;
				
				uni.showModal({
					title: '确认转换',
					content: confirmMsg,
					success: function(modalRes) {
						if (modalRes.confirm) {
							that.doConvert(scoreNum, paypwd);
						}
					}
				});
			},
			
			doConvert: function(score, paypwd) {
				var that = this;
				that.loading = true;
				
				var postData = { score: score };
				if (that.config.need_paypwd) {
					postData.paypwd = paypwd;
				}
				
				app.post('ApiMoney/scoreToCommission', postData, function(res) {
					that.loading = false;
					if (res.status == 0) {
						app.error(res.msg);
						return;
					} else {
						app.error('转换成功');
						// 重置表单
						that.score = '';
						that.paypwd = '';
						that.calculatedCommission = 0;
						that.calculatedFee = 0;
						// 刷新数据
						that.getdata();
					}
				});
			}
		}
	};
</script>

<style>
	.container {
		display: flex;
		flex-direction: column
	}

	.mymoney {
		width: 94%;
		margin: 20rpx 3%;
		border-radius: 10rpx 56rpx 10rpx 10rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		padding: 70rpx 0
	}

	.mymoney .f1 {
		margin: 0 0 0 60rpx;
		color: rgba(255, 255, 255, 0.8);
		font-size: 24rpx;
	}

	.mymoney .f2 {
		margin: 20rpx 0 0 60rpx;
		color: #fff;
		font-size: 64rpx;
		font-weight: bold
	}

	.mymoney .f3 {
		height: 56rpx;
		padding: 0 10rpx 0 20rpx;
		border-radius: 28rpx 0px 0px 28rpx;
		background: rgba(255, 255, 255, 0.2);
		font-size: 20rpx;
		font-weight: bold;
		color: #fff;
		display: flex;
		align-items: center;
		position: absolute;
		top: 94rpx;
		right: 0
	}

	.content2 {
		width: 94%;
		margin: 10rpx 3%;
		border-radius: 10rpx;
		display: flex;
		flex-direction: column;
		background: #fff
	}

	.balance-info {
		display: flex;
		padding: 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.balance-item {
		flex: 1;
		text-align: center;
	}

	.balance-label {
		display: block;
		font-size: 24rpx;
		color: #999;
		margin-bottom: 10rpx;
	}

	.balance-value {
		display: block;
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}

	.rules-section {
		padding: 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.rules-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}

	.rules-content {
		background: #f8f9fa;
		padding: 20rpx;
		border-radius: 10rpx;
	}

	.rule-item {
		font-size: 24rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 8rpx;
	}

	.rule-item:last-child {
		margin-bottom: 0;
	}

	.preview-section {
		padding: 20rpx 30rpx;
		background: #f0f9ff;
		border-bottom: 1px solid #F0F0F0;
	}

	.preview-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.preview-item:last-child {
		margin-bottom: 0;
	}

	.preview-label {
		font-size: 26rpx;
		color: #666;
	}

	.preview-value {
		font-size: 28rpx;
		color: #007AFF;
		font-weight: bold;
	}

	.preview-value.fee {
		color: #ff6b6b;
	}

	.content2 .item3 {
		display: flex;
		width: 100%;
		padding: 0 30rpx;
		border-bottom: 1px solid #F0F0F0;
	}

	.content2 .item3 .f2 {
		display: flex;
		align-items: center;
		font-size: 60rpx;
		color: #333333;
		font-weight: bold;
		width: 100%;
	}

	.content2 .item3 .f2 input {
		height: 120rpx;
		line-height: 120rpx;
		width: 100%;
	}

	.op {
		width: 96%;
		margin: 20rpx 2%;
		display: flex;
		align-items: center;
		margin-top: 40rpx
	}

	.op .btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		background: #07C160;
		width: 90%;
		margin: 0 10rpx;
		border-radius: 10rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center
	}
</style> 