<template>
<view class="container">
	<block v-if="isload">
		<view class="content">
			<view class="info-item" style="height:136rpx;line-height:136rpx">
				<view class="t1" style="flex:1;">头像</view>
				<image :src="userinfo.headimg" style="width:88rpx;height:88rpx;" @tap="uploadHeadimg"/>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setnickname">
				<view class="t1">昵称</view>
				<view class="t2">{{userinfo.nickname}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<view class="content">
			<view class="info-item" @tap="goto" data-url="setidcard">
				<view class="t1">身份证认证</view>
				<view class="t2">{{userinfo.realname}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setrealname">
				<view class="t1">姓名</view>
				<view class="t2">{{userinfo.realname}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="settel">
				<view class="t1">手机号</view>
				<view class="t2">{{userinfo.tel}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setsex">
				<text class="t1">性别</text>
				<text class="t2" v-if="userinfo.sex==1">男</text>
				<text class="t2" v-else-if="userinfo.sex==2">女</text>
				<text class="t2" v-else>未知</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setbirthday">
				<text class="t1">生日</text>
				<text class="t2">{{userinfo.birthday}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		
		<!-- <view class="content">
			<view class="info-item" @tap="goto" data-url="setweixin">
				<view class="t1">微信号</view>
				<view class="t2">{{userinfo.weixin}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setaliaccount">
				<view class="t1">支付宝账号</view>
				<view class="t2">{{userinfo.aliaccount}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setbankinfo">
				<text class="t1">银行卡</text>
				<text class="t2">{{userinfo.bankname ? '已设置' : ''}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view> -->
		
		<view class="content">
			<view class="info-item" @tap="goto" data-url="/pages/address/address">
				<view class="t1">收货地址</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<view class="content" v-if="userinfo.haspwd==1">
			<view class="info-item" @tap="goto" data-url="/pagesExa/my/setpwd">
				<view class="t1">修改密码</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<!-- #ifdef MP -->
		<view class="content">
			<view class="info-item" @tap="goto" data-url="/pagesB/login/login">
				<view class="t1">切换账号</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<!-- #endif -->
		<view class="content">
			<view class="info-item" @tap="logout">
				<view class="t1">退出登录</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<!-- #ifdef APP-PLUS -->
		<view class="content">
			<view class="info-item" @tap="delaccount">
				<view class="t1">注销账号</view>
				<view class="t2"></view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view>
		<!-- #endif -->
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			userinfo:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/set', {}, function (data) {
				that.loading = false;
				that.userinfo = data.userinfo;
				that.loaded();
			});
		},
		uploadHeadimg:function(){
			var that = this;
			app.chooseImage(function(urls){
				var headimg = urls[0];
				that.userinfo.headimg = headimg;
				app.post('ApiMy/setfield',{headimg:headimg});
			},1)
		},
		delaccount:function(){
			app.confirm('注销账号后该账号下的所有数据都将删除并且无法恢复，确定要注销吗？',function(){
				app.showLoading('注销中');
				app.get('ApiMy/delaccount', {}, function (data) {
					app.showLoading(false);
					if(data.status == 1){
						app.alert(data.msg,function(){
							app.goto('/pages/index/index');
						});
					}else{
						app.alert(data.msg);
					}
				});
			})
		},
		logout:function(){
			var that = this;
			that.loading = true;
			app.get('ApiIndex/logout', {}, function (data) {
				app.showLoading(false);
				if(data.status == 0){
					app.alert(data.msg);
				}
			});
		}
  }
};
</script>
<style>
.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:0 20rpx;}
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}


</style>