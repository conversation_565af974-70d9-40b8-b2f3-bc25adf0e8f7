<template>
<view class="container">
	<block v-if="isload">
		<dd-tab :itemdata="['全部','待付款','已支付','已完成','已退款']" :itemst="['-1','0','1','2','3']" :st="st" :isfixed="true" @changetab="changetab"></dd-tab>
		<view style="width:100%;height:100rpx"></view>
		<!-- #ifndef H5 || APP-PLUS -->
		<view class="topsearch flex-y-center">
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
			</view>
		</view>
		<!--  #endif -->
		<view class="order-content">

			<block>
				
				<view class="ll" v-for="(item, index) in list" :key="index">
					
					<view @tap="goDetail(item.id)">
					
				     <view class="ll-tit">
						 
						  <text style="font-size: 34rpx;font-weight: bold;">{{item.episode?item.episode.title:''}}</text>  
						
						  <text v-if="item.status == 2"> 已完成</text>
						  <text v-else-if="item.status == 3">已退款</text>
						  <text v-else-if="item.status == 1">已支付</text>
						  <text v-else> 未支付 </text> 
						 
					</view>         
					         
					<view class="ll-txt">
						
	
						 <text>总价:{{item.totalprice}}元</text>
						
					</view>
				
					<view  class="ll-txt">
		
						 <text>{{item.order_detail.list?item.order_detail.list.length:'1'}}个座位</text>
						
					</view>
					
					<view  class="ll-txt">
						
						 <text>开始时间:{{item.order_detail.title}}</text>
						
					</view>
					
					</view>
					
					<view class="line"></view>
					
				    <view class="ll-btn" >
						
						<view class="btn-1" @click="goDetail(item.id)">详情</view>
						
						<view class="btn-2" :style="'background:'+primary_color" v-if="item.status == 0"  @click="goPay(item.payorder_id)">去支付</view>
						
						<view class="btn-2" :style="'background:'+primary_color" v-if="item.status == 1"  @tap.stop="showhxqr" :data-hexiao_qr="item.hexiao_qr">核销码</view>
						
					</view>   
				 
				</view>
				
			</block>
			
		</view>
		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>

		
		<uni-popup id="dialogHxqr" ref="dialogHxqr" type="dialog">
			<view class="hxqrbox">
				<image :src="hexiao_qr" @tap="previewImage" :data-url="hexiao_qr" class="img"/>
				<view class="txt">请出示核销码给核销员进行核销</view>
				<view class="close" @tap="closeHxqr">
					<image src="/static/img/close2.png" style="width:100%;height:100%"/>
				</view>
			</view>
		</uni-popup>

		<uni-popup id="dialogSelectExpress" ref="dialogSelectExpress" type="dialog">
			<view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx" v-if="express_content">
				<view class="sendexpress" v-for="(item, index) in express_content" :key="index" style="border-bottom: 1px solid #f5f5f5;padding:20rpx 0;">
					<view class="sendexpress-item" @tap="goto" :data-url="'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no" style="display: flex;">
						<view class="flex1" style="color:#121212">{{item.express_com}} - {{item.express_no}}</view>
						<image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx"/>
					</view>
					<view v-if="item.express_oglist" style="margin-top:20rpx">
						<view class="oginfo-item" v-for="(item2, index2) in item.express_oglist" :key="index2" style="display: flex;align-items:center;margin-bottom:10rpx">
							<image :src="item2.pic" style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0"/>
							<view class="flex1" style="color:#555">{{item2.name}}({{item2.ggname}})</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: true,
			menuindex:-1,

      st: '',
      datalist: [],
      pagenum: 1,
      nomore: false,
			nodata:false,
      codtxt: "",
			canrefund:1,
			express_content:'',
			selectExpressShow:false,
			hexiao_qr:'',
			keyword:'',
		list : [],
			primary_color : '',
			secondary_color : ''
			
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		if(this.opt && this.opt.st){
			this.st = this.opt.st;
		}
		//this.getdata();
		this.getList();
		
		this.primary_color = app.getCache('primary_color')
		this.secondary_color = app.getCache('secondary_color')
		
  },
	onPullDownRefresh: function () {
		//this.getdata();
		this.getList();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
     // this.getdata(true);
	 	this.getList();
    }
  },
	onNavigationBarSearchInputConfirmed:function(e){
		this.searchConfirm({detail:{value:e.text}});
	},
  methods: {
		goDetail(id){
			 uni.navigateTo({
				url:'/pagesB/theater/dingchangOrderDetail?id='+id
			 })
		}, 
		
	goPay(id){
		 uni.navigateTo({
			url:'/pages/pay/pay?id='+id
		 })
	},	
	  
	 getList: function() {
	 	
	 	var that = this;

	 	var obj ={}  
		
		if(that.st >= 0){         
			
			obj.status = that.st
		}
		
		console.log(that.st)
		
	 	app.get('/ApiTheater/getOrderList', obj, function(res) {

            console.log(res)

	 		if(res.status == 1){
	 			that.list = res.data.list	
				
	 		}   

	 	});
	  }, 
	  

    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
	   this.getList();
      //this.getdata();
    },
    toclose: function (e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm('确定要关闭该订单吗?', function () {
				app.showLoading('提交中');
        app.post('ApiOrder/closeOrder', {orderid: orderid}, function (data) {
					app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    todel: function (e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm('确定要删除该订单吗?', function () {
				app.showLoading('删除中');
        app.post('ApiOrder/delOrder', {orderid: orderid}, function (data) {
					app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    orderCollect: function (e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm('确定要收货吗?', function () {
				app.showLoading('提交中');
        app.post('ApiOrder/orderCollect', {orderid: orderid}, function (data) {
					app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
		logistics:function(e){
			var index = e.currentTarget.dataset.index;
			var orderinfo = this.datalist[index];
			var express_com = orderinfo.express_com
			var express_no = orderinfo.express_no
			var express_content = orderinfo.express_content
			var express_type = orderinfo.express_type
			var prolist = orderinfo.prolist
			console.log(express_content)
			if(!express_content){
				app.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no+'&type='+express_type);
			}else{
				express_content = JSON.parse(express_content);
				for(var i in express_content){
					if(express_content[i].express_ogids){
						var express_ogids = (express_content[i].express_ogids).split(',');
						console.log(express_ogids);
						var express_oglist = [];
						for(var j in prolist){
							if(app.inArray(prolist[j].id+'',express_ogids)){
								express_oglist.push(prolist[j]);
							}
						}
						express_content[i].express_oglist = express_oglist;
					}
				}
				this.express_content = express_content;
				console.log(express_content);
				this.$refs.dialogSelectExpress.open();
			}
		},
		hideSelectExpressDialog:function(){
			this.$refs.dialogSelectExpress.close();
		},
		showhxqr:function(e){
			this.hexiao_qr = e.currentTarget.dataset.hexiao_qr
			this.$refs.dialogHxqr.open();
		},
		closeHxqr:function(){
			this.$refs.dialogHxqr.close();
		},
		searchConfirm:function(e){
			this.keyword = e.detail.value;
      this.getdata(false);
		}
  }
};
</script>
<style>
.container{ width:100%;}
.topsearch{width:94%;margin:10rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.order-content{display:flex;flex-direction:column}
.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}
.order-box .head .f1{display:flex;align-items:center;color:#333}
.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}
.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }
.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }
.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }
.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }
.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }

.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}
.order-box .content:last-child{ border-bottom: 0; }
.order-box .content image{ width: 140rpx; height: 140rpx;}
.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}
.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}
.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}
.order-box .content .detail .x1{ flex:1}
.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}

.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}

.btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}
.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}

.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.hxqrbox .img{width:400rpx;height:400rpx}
.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}

	.ll{
		padding: 10px 15px;
		border-radius: 10px; 
		background:#fff;
		margin: 5px 12px;
	}
	
	.ll-tit{
		display: flex;
		justify-content: space-between;
	}
	
	.ll-txt{
		display: flex;margin-top: 10px;
	}
	
	.ll-txt text{
		flex: 1;
	}
	
	.line{
		border-bottom: 1px solid #eee;
		margin-top: 15px;
	}
	
	.ll-btn{
		display: flex;
		justify-content: flex-end;
	}
	
	.ll-btn .btn-1{
		padding: 5px 15px;
		margin: 8px 0 0 8px;
		border-radius: 30px;
		border: 1px solid #eee;
	}
	
	.ll-btn .btn-2{
		padding: 5px 15px;
		margin: 8px 0 0 8px;
		border-radius: 30px;
		background: #5F72F2;
		color: #fff;
	}

</style>