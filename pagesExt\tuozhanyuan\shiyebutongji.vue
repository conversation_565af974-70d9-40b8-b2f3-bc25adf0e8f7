<template>
<view class="container">
	<block v-if="isload">
		<view class="stats-container">
			<!-- 用户推广统计卡片 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">用户推广统计</text>
				</view>
				
				<view class="stats-grid">
					<!-- 新增数据统计 -->
					<view class="stats-row">
						<view class="stats-item">
							<text class="stats-value">{{member.day}}</text>
							<text class="stats-label">今日新增</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{member.zuo}}</text>
							<text class="stats-label">昨日新增</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{member.month}}</text>
							<text class="stats-label">本月新增</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{member.zongji}}</text>
							<text class="stats-label">总计</text>
						</view>
					</view>

					<!-- 销量数据统计 -->
					<view class="stats-row">
						<view class="stats-item">
							<text class="stats-value highlight">￥{{memberxiaoliang.day.toFixed(2)}}</text>
							<text class="stats-label">今日销量</text>
						</view>
						<view class="stats-item">
							<text class="stats-value highlight">￥{{memberxiaoliang.zuo.toFixed(2)}}</text>
							<text class="stats-label">昨日销量</text>
						</view>
						<view class="stats-item">
							<text class="stats-value highlight">￥{{memberxiaoliang.month.toFixed(2)}}</text>
							<text class="stats-label">本月销量</text>
						</view>
						<view class="stats-item">
							<text class="stats-value highlight">￥{{memberxiaoliang.zongji.toFixed(2)}}</text>
							<text class="stats-label">总销量</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 商户推广统计卡片 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">商户推广统计</text>
				</view>
				
				<view class="stats-grid">
					<!-- 新增数据统计 -->
					<view class="stats-row">
						<view class="stats-item">
							<text class="stats-value">{{business.day}}</text>
							<text class="stats-label">今日新增</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{business.zuo}}</text>
							<text class="stats-label">昨日新增</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{business.month}}</text>
							<text class="stats-label">本月新增</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{business.zongji}}</text>
							<text class="stats-label">总计</text>
						</view>
					</view>

					<!-- 销量数据统计 -->
					<view class="stats-row">
						<view class="stats-item">
							<text class="stats-value highlight">￥{{businessxiaoliang.day.toFixed(2)}}</text>
							<text class="stats-label">今日销量</text>
						</view>
						<view class="stats-item">
							<text class="stats-value highlight">￥{{businessxiaoliang.zuo.toFixed(2)}}</text>
							<text class="stats-label">昨日销量</text>
						</view>
						<view class="stats-item">
							<text class="stats-value highlight">￥{{businessxiaoliang.month.toFixed(2)}}</text>
							<text class="stats-label">本月销量</text>
						</view>
						<view class="stats-item">
							<text class="stats-value highlight">￥{{businessxiaoliang.zongji.toFixed(2)}}</text>
							<text class="stats-label">总销量</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</block>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			pre_url:app.globalData.pre_url,
      field: 'juli',
			order:'asc',
      oldcid: "",
      catchecid: "",
      longitude: '',
      latitude: '',
			clist:[],
      datalist: [],
      pagenum: 1,
      keyword: '',
      cid: '',
      nomore: false,
      nodata: false,
      types: "",
      showfilter: "",
			showtype:0,
			buydialogShow:false,
			proid:0,
			member:{},
			memberxiaoliang:{},
			business:{},
			businessxiaoliang:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.oldcid = this.opt.cid;
		this.catchecid = this.opt.cid;
		this.cid = this.opt.cid;
		this.mingxiid = this.opt.mingxiid;
        if(this.opt.keyword) {
        	this.keyword = this.opt.keyword;
        }
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getDataList(true);
    }
  },
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiYihuo/mydata', {mingxiid:that.mingxiid}, function (res) {
				that.loading = false;
				that.clist = res.clist;
				that.member= res.member;
				that.memberxiaoliang = res.memberxiaoliang;
				that.business = res.business;
				that.businessxiaoliang = res.businessxiaoliang;
				that.showtype = res.showtype || 0;
				that.loaded();
			});
			app.getLocation(function (res) {
				var latitude = res.latitude;
				var longitude = res.longitude;
				that.longitude = longitude;
				that.latitude = latitude;
				that.getDataList();
			},
			function () {
				that.getDataList();
			});
		},
    getDataList: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var latitude = that.latitude;
      var longitude = that.longitude;
      var keyword = that.keyword;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
      app.post('ApiBusiness/mylist', {pagenum: pagenum,cid: that.cid,field: that.field,order: that.order,longitude: longitude,latitude: latitude,keyword: keyword}, function (res) {
        that.loading = false;
				uni.stopPullDownRefresh();
	    if(res.status == 0)
		{
			app.alert(res.msg, function () {
				app.goto('/pages/my/usercenter', 'redirect');
			})
			return;
		}
        var data = res.data;
        if (pagenum == 1) {
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
		// 打开窗口
		showDrawer(e) {
			console.log(e)
			this.$refs[e].open()
		},
		// 关闭窗口
		closeDrawer(e) {
			this.$refs[e].close()
		},
		// 抽屉状态发生变化触发
		change(e, type) {
			console.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));
			this[type] = e
		},
    cateClick: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
      that.catchecid = cid
    },
		filterConfirm(){
			this.cid = this.catchecid;
			this.gid = this.catchegid;
			this.getDataList();
			this.$refs['showRight'].close()
		},
		filterReset(){
			this.catchecid = this.oldcid;
			this.catchegid = '';
		},
    filterClick: function () {
      this.showfilter = !this.showfilter
    },
    changetab: function (e) {
      var that = this;
      var cid = e.currentTarget.dataset.cid;
      that.cid = cid
      that.pagenum = 1;
      that.datalist = [];
      that.getDataList();
    },
    search: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
			that.pagenum = 1;
      that.datalist = [];
      that.getDataList();
    },
    sortClick: function (e) {
      var that = this;
      var t = e.currentTarget.dataset;
      that.field = t.field;
      that.order = t.order;
      that.getDataList();
    },
    filterClick: function (e) {
      var that = this;
      var types = e.currentTarget.dataset.types;
      that.types = types;
    },
		openLocation:function(e){
			//console.log(e)
			var latitude = parseFloat(e.currentTarget.dataset.latitude)
			var longitude = parseFloat(e.currentTarget.dataset.longitude)
			var address = e.currentTarget.dataset.address
			uni.openLocation({
			 latitude:latitude,
			 longitude:longitude,
			 name:address,
			 scale: 13
			})		
		},
		phone:function(e) {
			var phone = e.currentTarget.dataset.phone;
			uni.makePhoneCall({
				phoneNumber: phone,
				fail: function () {
				}
			});
		},
		buydialogChange: function (e) {
			if(!this.buydialogShow){
				this.proid = e.currentTarget.dataset.proid
			}
			this.buydialogShow = !this.buydialogShow;
			console.log(this.buydialogShow);
		},
  }
};
</script>
<style>
/* 容器样式 */
.stats-container {
  padding: 24rpx;
  background: #f5f6fa;
}

/* 统计卡片 */
.stats-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.card-header {
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 统计网格 */
.stats-grid {
  width: 100%;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.stats-row:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

/* 统计项 */
.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 8rpx;
}

.stats-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-value.highlight {
  color: #fe2b2e;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

</style>