<template>
<view class="container">
  <block v-if="isload">

    <map v-if="psorder.status!=4" class="map" :longitude="binfo.longitude" :latitude="binfo.latitude" scale="14" :markers="[
      {
        id: 0,
        latitude: binfo.latitude,
        longitude: binfo.longitude,
        iconPath: '/static/peisong/marker_business.png',
        width: '44',
        height: '54'
      },
      {
        id: 1,
        latitude: orderinfo.latitude,
        longitude: orderinfo.longitude,
        iconPath: '/static/peisong/marker_kehu.png',
        width: '44',
        height: '54'
      },
      {
        id: 2,
        latitude: psuser.latitude,
        longitude: psuser.longitude,
        iconPath: '/static/peisong/marker_qishou.png',
        width: '44',
        height: '54'
      }
    ]"></map>
    <map v-else class="map" :longitude="binfo.longitude" :latitude="binfo.latitude" scale="14" :markers="[
      {
        id: 0,
        latitude: binfo.latitude,
        longitude: binfo.longitude,
        iconPath: '/static/peisong/marker_business.png',
        width: '44',
        height: '54'
      },
      {
        id: 1,
        latitude: orderinfo.latitude,
        longitude: orderinfo.longitude,
        iconPath: '/static/peisong/marker_kehu.png',
        width: '44',
        height: '54'
      }
    ]"></map>

    <view class="order-box">
      <view class="head">
        <view class="f1" v-if="psorder.status==4"><image src="/static/peisong/ps_time.png" class="img"/>已送达</view>
        <view class="f1" v-else-if="psorder.leftminute>0"><image src="/static/peisong/ps_time.png" class="img"/><text class="t1">{{psorder.leftminute}}分钟内</text> 送达</view>
        <view class="f1" v-else><image src="/static/peisong/ps_time.png" class="img"/>已超时<text class="t1" style="margin-left:10rpx">{{-psorder.leftminute}}分钟</text></view>
        <view class="flex1"></view>
        <view class="f2"><text class="t1">{{psorder.ticheng}}</text>元</view>
      </view>
      <view class="content" style="border-bottom:0">
        <view class="f1">
          <view class="t1"><text class="x1">{{psorder.juli}}</text><text class="x2">{{psorder.juli_unit}}</text></view>
          <view class="t2"><image src="/static/peisong/ps_juli.png" class="img"/></view>
          <view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view>
        </view>
        <view class="f2">
          <view class="t1">{{binfo.name}}</view>
          <view class="t2">{{binfo.address}}</view>
          <view class="t3">{{orderinfo.address}}</view>
            <view class="t2">{{orderinfo.area}}</view>
        </view>
        <view class="f3" @tap.stop="daohang"><image :src="pre_url+'/static/img/peisong/ps_daohang.png'" class="img"/></view>
      </view>
    </view>

    <view class="orderinfo">
      <view class="box-title">商品清单({{orderinfo.procount}})</view>
      <view v-for="(item, idx) in prolist" :key="idx" class="item">
        <text class="t1 flex1">{{item.name}} {{item.ggname}}</text>
        <text class="t2 flex0">￥{{item.sell_price}} ×{{item.num}} </text>
      </view>
    </view>
    
    <view class="orderinfo" v-if="psorder.status!=0">
      <view class="box-title">配送信息</view>
      <view class="item">
        <text class="t1">接单时间</text>
        <text class="t2">{{dateFormat(psorder.starttime)}}</text>
      </view>
      <view class="item" v-if="psorder.daodiantime">
        <text class="t1">到店时间</text>
        <text class="t2">{{dateFormat(psorder.daodiantime)}}</text>
      </view>
      <view class="item" v-if="psorder.quhuotime">
        <text class="t1">取货时间</text>
        <text class="t2">{{dateFormat(psorder.quhuotime)}}</text>
      </view>
      <view class="item" v-if="psorder.endtime">
        <text class="t1">送达时间</text>
        <text class="t2">{{dateFormat(psorder.endtime)}}</text>
      </view>
    </view>

    <view class="orderinfo">
      <view class="box-title">订单信息</view>
      <view class="item">
        <text class="t1">订单编号</text>
        <text class="t2" user-select="true" selectable="true">{{orderinfo.ordernum}}</text>
      </view>
      <view class="item">
        <text class="t1">下单时间</text>
        <text class="t2">{{dateFormat(orderinfo.createtime)}}</text>
      </view>
      <view class="item">
        <text class="t1">支付时间</text>
        <text class="t2">{{dateFormat(orderinfo.paytime)}}</text>
      </view>
      <view class="item">
        <text class="t1">支付方式</text>
        <text class="t2">{{orderinfo.paytype}}</text>
      </view>
      <view class="item">
        <text class="t1">商品金额</text>
        <text class="t2 red">¥{{orderinfo.product_price}}</text>
      </view>
      <view class="item">
        <text class="t1">实付款</text>
        <text class="t2 red">¥{{orderinfo.totalprice}}</text>
      </view>
      <view class="item">
        <text class="t1">备注</text>
        <text class="t2 red">{{orderinfo.message ? orderinfo.message : '无'}}</text>
      </view>
	   <view class="item qr-section" v-if="orderinfo.qr">
	      <text class="t1">付款二维码</text>
	      <image :src="orderinfo.qr" class="qr-image" @tap="openQR"></image>
	    </view>
		  <!-- 新增客户线下支付确认按钮 -->
		        <view class="confirm-pay-section" v-if="orderinfo.qr">
		          <button class="confirm-pay-btn" @tap="confirmOfflinePayment">确认线下支付</button>
		        </view>
				<view class="confirm-pay-section" v-if="!orderinfo.qr">
				  <button class="confirm-pay-btn" >本单已支付请和客户核对确认</button>
				</view>
	<view class="apply_box">
		<view class="apply_item" style="border-bottom:0"><text>上传凭证(1-5张)<text style="color:red"> *</text></text></view>
		<view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
			<view v-for="(item, index) in pics" :key="index" class="layui-imgbox">
				<view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pics"><image src="/static/img/ico-del.png"></image></view>
				<view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
			</view>
			<view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pics" v-if="pics.length<5"></view>
		</view>
		<input type="text" hidden="true" name="pics" :value="pics.join(',')" maxlength="-1"></input>
	</view>
    </view>
    
    <view style="width:100%;height:180rpx"></view>
<!-- 移除 v-if="psorder.status!=4" -->
<view class="bottom notabbarbot">
  <!-- 联系顾客按钮 -->
  <view class="f1" v-if="psorder.status!=0" @tap="call" :data-tel="orderinfo.tel">
    <image src="/static/peisong/tel1.png" class="img"/>联系顾客
  </view>
  <!-- 操作退框按钮 -->
  <view class="f1" v-if="psorder.status!=0" @tap="tuikuang()">
    <image src="/static/img/exp_shou.png" class="img"/>操作退框
  </view>
  <!-- 根据不同状态显示不同的操作按钮 -->
  <view class="btn1" @tap="qiangdan" :data-id="psorder.id" v-if="psorder.status==0">立即抢单</view>
 <view class="btn1" @tap="setst" :data-id="psorder.id" data-st="4" v-if="psorder.status==1">我已送达</view>
  <view class="btn1" @tap="setst" :data-id="psorder.id" data-st="4" v-if="psorder.status==2">我已送达</view>
 
  <view class="btn1" @tap="setst" :data-id="psorder.id" data-st="4" v-if="psorder.status==3">我已送达</view>
  <view class="btn1" @tap="setpic" :data-id="psorder.id" data-st="4"  v-if="psorder.status==4">一键上传</view>
  
 
</view>

  </block>
  <loading v-if="loading"></loading>
  <dp-tabbar :opt="opt"></dp-tabbar>
  <popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      pre_url: app.globalData.pre_url,
      orderinfo: {},
      prolist: [],
      psuser: {},
      binfo: {},
      pics: [], // 修改此处，确保pic数组存在
      psorder: {},
      is_tk: 0,
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  
   
  methods: {
    getdata: function () {
      var that = this;
      that.loading = true;
      app.get('ApiPeisong/orderdetail', { id: that.opt.id }, function (res) {
        that.loading = false;
        if (res.status == 0) {
          app.alert(res.msg); return;
        }
        that.orderinfo = res.orderinfo;
        that.prolist = res.prolist;
        that.binfo = res.binfo;
        that.psorder = res.psorder;
        that.psuser = res.psuser;
        that.is_tk = res.orderinfo.is_tk;
        that.isload = true; // 修改此处，确保页面加载完成后显示内容
		 // 确保 pics 是一个数组
		    if (typeof that.psorder.pics === 'string') {
		      that.pics = that.psorder.pics ? that.psorder.pics.split(',') : [];
		    } else if (Array.isArray(that.psorder.pics)) {
		      that.pics = that.psorder.pics;
		    } else {
		      that.pics = [];
		    }
		
      });
    },
    // 弹出退框数量
    tuikuang() {
      let that = this;
      if (that.is_tk == 1) {
        uni.showToast({
          icon: 'error',
          title: '此单已经退框完成无需再次退框'
        })
        return false;
      }
      uni.showModal({
        title: '请输入退框数量',
        content: '',
        editable: true, // 是否显示输入框
        placeholderText: '退框数量', // 输入框提示内容
        confirmText: '确认',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            if (isNaN(res.content) || res.content == '') {
              uni.showToast({
                icon: 'error',
                title: '请输入数字整数'
              })
              return false;
            } else {
              // 请求数据信息
              app.get('ApiPeisong/tuikuangs', { id: that.opt.psid, order_num: that.orderinfo.ordernum, tuikuang_num: res.content }, function (res) {
                let icon = 'success';
                if (res.status == 0) {
                  icon = 'error'
                }
                uni.showToast({
                  icon: icon,
                  title: res.message
                })
                that.getdata(); // 重新加载数据防止二次退框
              });
            }
          }
        }
      });
    },
	 previewImage: function (index) {
	      var that = this;
	      uni.previewImage({
	        current: that.psorder.pics[index],
	        urls: that.psorder.pics
	      });
	    },
    uploadimg:function(e){
    	var that = this;
    	var field= e.currentTarget.dataset.field
    	var pics = that[field]
    	if(!pics) pics = [];
    	app.chooseImage(function(urls){
    		for(var i=0;i<urls.length;i++){
    			pics.push(urls[i]);
    		}
    		if(field == 'pic') that.pic = pics;
    		if(field == 'pics') that.pics = pics;
    		if(field == 'zhengming') that.zhengming = pics;
    	},1)
    },
    removeimg:function(e){
    	var that = this;
    	var index= e.currentTarget.dataset.index
    	var field= e.currentTarget.dataset.field
    	if(field == 'pic'){
    		var pics = that.pic
    		pics.splice(index,1);
    		that.pic = pics;
    	}else if(field == 'pics'){
    		var pics = that.pics
    		pics.splice(index,1);
    		that.pics = pics;
    	}else if(field == 'zhengming'){
    		var pics = that.zhengming
    		pics.splice(index,1);
    		that.zhengming = pics;
    	}
    },
    daohang: function (e) {
      var that = this;
      var datainfo = that.psorder;
      var binfo = that.binfo;
      var orderinfo = that.orderinfo;
      uni.showActionSheet({
        itemList: ['导航到商家', '导航到用户'],
        success: function (res) {
          if (res.tapIndex >= 0) {
            if (res.tapIndex == 0) {
              var longitude = datainfo.longitude;
              var latitude = datainfo.latitude;
              var name = binfo.name;
              var address = binfo.address;
            } else {
              var longitude = datainfo.longitude2;
              var latitude = datainfo.latitude2;
              var name = orderinfo.address;
              var address = orderinfo.address;
            }
            uni.openLocation({
              latitude: parseFloat(latitude),
              longitude: parseFloat(longitude),
              name: name,
              address: address,
              scale: 13
            })
          }
        }
      });
    },
    call: function (e) {
      var tel = e.currentTarget.dataset.tel;
      uni.makePhoneCall({
        phoneNumber: tel
      });
    },
	  openQR() {
	    if (this.orderinfo.qr) {
	      uni.previewImage({
	        current: this.orderinfo.qr, // 当前显示图片的http链接
	        urls: [this.orderinfo.qr]    // 需要预览的图片http链接列表
	      });
	    } else {
	      uni.showToast({
	        icon: 'none',
	        title: '二维码图片不存在'
	      });
	    }
	  },
	  
	      // 新增确认线下支付方法
	      confirmOfflinePayment() {
	        var that = this;
	        uni.showModal({
	          title: '确认支付',
	          content: '确认您已完成线下支付？',
	          success: function (res) {
	            if (res.confirm) {
	              // 调用后端 API 更新支付类型
	              app.showLoading('提交中');
	              app.post('ApiPeisong/confirmOfflinePay', { id: that.psorder.id }, function (res) {
	                app.showLoading(false);
	                if (res.status == 2) { // 假设 status=2 表示成功
	                  app.success(res.msg);
	                  that.getdata(); // 重新加载订单详情
	                } else {
	                  app.error(res.msg);
	                }
	              });
	            }
	          }
	        });
	      },

    qiangdan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      app.confirm('确定要接单吗?', function () {
        app.showLoading('提交中');
        app.post('ApiPeisong/qiangdan', { id: id }, function (data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    setst: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var st = e.currentTarget.dataset.st;
      var tips = '';
      if (st == 2) {
        tips = '确定改为已到店吗?';
      } else if (st == 3) {
        tips = '确定改为已取货吗?';
      } else if (st == 4) {
        tips = '确定改为已送达吗?';
      }
      app.confirm(tips, function () {
        // 检查是否有上传的图片
        // if (that.pics.length === 0) {
        //   app.alert('请先上传凭证图片');
        //   return;
        // }
        app.showLoading('提交中');
        app.post('ApiPeisong/setstjidan', { id: id, st: st, pics: that.pics }, function (data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
	 setpic: function (e) {
	     var that = this;
	     var id = e.currentTarget.dataset.id; // 获取订单ID
	     var st = e.currentTarget.dataset.st; // 获取状态（如果需要）
	 
	     // 可选提示信息，根据需求填写
	     var tips = '确认上传凭证图片吗？';
	 
	     // 弹出确认框
	     uni.showModal({
	         title: '确认操作',
	         content: tips,
	         success: function (res) {
	             if (res.confirm) {
	                 // 检查是否有上传的图片
	                 if (that.pics.length === 0) {
	                     uni.showToast({
	                         icon: 'none',
	                         title: '请先上传凭证图片'
	                     });
	                     return;
	                 }
	 
	                 uni.showLoading({ title: '上传中...' });
	 
	                 // 调用后端API，上传图片
	                 app.post('ApiPeisong/setpic', { 
	                     id: id,
	                     pics: that.pics // 传递图片数组
	                 }, function (data) {
	                     uni.hideLoading();
	 
	                     if (data.status === 1) {
	                         uni.showToast({ icon: 'success', title: '上传成功' });
	                         that.getdata(); // 重新加载订单详情
	                     } else {
	                         uni.showToast({ icon: 'none', title: data.msg });
	                     }
	                 });
	             }
	         }
	     });
	 },

    dateFormat: function (timestamp) {
      var date = new Date(timestamp * 1000);
      var Y = date.getFullYear() + '-';
      var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
      var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
      return Y + M + D + ' ' + h + ':' + m + ':' + s;
    }
  }
}
</script>

<style>
/* 添加二维码图片的样式 */
.qr-section {
  display: flex;
  align-items: center;
  padding: 10rpx 0;
  border-top: 1px solid #eee;
}

.qr-section .t1 {
  width: 200rpx;
  color: #161616;
}

.qr-image {
  width: 200rpx;
  height: 200rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  margin-left: 20rpx;
  cursor: pointer;
}
/* 确认线下支付按钮样式 */
.confirm-pay-section {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}

.confirm-pay-btn {
  width: 90%;
  height: 96rpx;
  line-height: 96rpx;
  background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);
  border-radius: 48rpx;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
}
.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }
.apply_box .apply_item:last-child{ border:none}
.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}
.apply_item input::placeholder{ color:#999999}
.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}
.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }
.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }
.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}

.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}
.layui-imgbox-close image{width:100%;height:100%}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}

.map { width: 100%; height: 500rpx; overflow: hidden; }
.ordertop { width: 100%; height: 220rpx; padding: 50rpx 0 0 70rpx; }
.ordertop .f1 { color: #fff; }
.ordertop .f1 .t1 { font-size: 32rpx; height: 60rpx; line-height: 60rpx; }
.ordertop .f1 .t2 { font-size: 24rpx; }

.order-box { width: 94%; margin: 20rpx 3%; padding: 6rpx 3%; background: #fff; border-radius: 8px; }
.order-box .head { display: flex; width: 100%; border-bottom: 1px #f5f5f5 solid; height: 88rpx; line-height: 88rpx; overflow: hidden; color: #999; }
.order-box .head .f1 { display: flex; align-items: center; color: #222222; }
.order-box .head .f1 .img { width: 24rpx; height: 24rpx; margin-right: 4px; }
.order-box .head .f1 .t1 { color: #06A051; margin-right: 10rpx; }
.order-box .head .f2 { color: #FF6F30; }
.order-box .head .f2 .t1 { font-size: 36rpx; margin-right: 4rpx; }

.order-box .content { display: flex; justify-content: space-between; width: 100%; padding: 16rpx 0px; border-bottom: 1px solid #f5f5f5; position: relative; }
.order-box .content .f1 { width: 100rpx; display: flex; flex-direction: column; align-items: center; }
.order-box .content .f1 .t1 { display: flex; flex-direction: column; align-items: center; }
.order-box .content .f1 .t1 .x1 { color: #FF6F30; font-size: 28rpx; font-weight: bold; }
.order-box .content .f1 .t1 .x2 { color: #999999; font-size: 24rpx; margin-bottom: 8rpx; }
.order-box .content .f1 .t2 .img { width: 12rpx; height: 36rpx; margin: 10rpx 0; }

.order-box .content .f1 .t3 { display: flex; flex-direction: column; align-items: center; }
.order-box .content .f1 .t3 .x1 { color: #FF6F30; font-size: 28rpx; font-weight: bold; }
.order-box .content .f1 .t2 .img { width: 12rpx; height: 36rpx; margin: 10rpx 0; }
.order-box .content .f2 { padding: 0 10rpx; }
.order-box .content .f2 .t1 { font-size: 36rpx; color: #222222; font-weight: bold; line-height: 50rpx; margin-bottom: 6rpx; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; }
.order-box .content .f2 .t2 { font-size: 28rpx; color: #222222; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 3; overflow: hidden; }
.order-box .content .f2 .t3 { font-size: 36rpx; color: #222222; font-weight: bold; line-height: 50rpx; margin-top: 30rpx; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; }
.order-box .content .f3 .img { width: 72rpx; height: 168rpx; }

.orderinfo { width: 94%; margin: 20rpx 3%; margin-top: 10rpx; padding: 14rpx 3%; background: #FFF; border-radius: 8px; }
.orderinfo .box-title { color: #161616; font-size: 30rpx; height: 80rpx; line-height: 80rpx; font-weight: bold; }
.orderinfo .item { display: flex; width: 100%; padding: 10rpx 0; }
.orderinfo .item .t1 { width: 200rpx; color: #161616; }
.orderinfo .item .t2 { flex: 1; text-align: right; color: #222222; }
.orderinfo .item .red { color: red; }

.bottom { width: 100%; background: #fff; position: fixed; bottom: 0px; left: 0px; display: flex; justify-content: flex-end; align-items: center; }
.bottom .f1 { width: 188rpx; display: flex; align-items: center; flex-direction: column; font-size: 20rpx; color: #373C55; border-right: 1px solid #EAEEED; }
.bottom .f1 .img { width: 44rpx; height: 44rpx; }
.bottom .f2 { width: 188rpx; display: flex; align-items: center; flex-direction: column; font-size: 20rpx; color: #373C55; border-right: 1px solid #EAEEED; }
.bottom .f2 .img { width: 44rpx; height: 44rpx; }
.bottom .btn1 { flex: 1; background: linear-gradient(-90deg, #06A051 0%, #03B269 100%); height: 100rpx; line-height: 100rpx; color: #fff; text-align: center; font-size: 32rpx; }
</style>
