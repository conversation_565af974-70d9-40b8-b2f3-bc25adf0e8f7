<template>
	<view class="waterfalls-box" :style="{ height: height + 'px' }">
		<view v-for="(item, index) of list" class="waterfalls-list" :key="item[idfield]"
			:id="'waterfalls-list-id-' + item[idfield]" :ref="'waterfalls-list-id-' + item[idfield]" :style="{
        '--offset': offset + 'px',
        '--cols': cols,
				'background':probgcolor,
        top: allPositionArr[index] ? allPositionArr[index].top : 0,
        left: allPositionArr[index] ? allPositionArr[index].left : 0,
      }" @click="goto" :data-url="'/shopPackage/shop/product?id='+item[idfield]">
			<!-- 加载中遮罩层 -->
			<view class="loading-mask" v-if="!loadedItems[index]">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			
			<!-- 闪光效果 -->
			<view class="shine-effect" v-if="shiningItems[index]"></view>
			
			<image class="waterfalls-list-image" mode="widthFix" :style="imageStyle" :src="item[imageSrcKey] || ' '"
				@load="imageLoadHandle(index)" @error="imageLoadHandle(index)" />
			<image class="saleimg" :src="saleimg" v-if="saleimg!=''" mode="widthFix" />
			<view>
				<view class="product-info">
					<view class="p1" v-if="showname == 1">{{item.name}}</view>
					<view class="binfo flex-y-center" v-if="showbname&&item.binfo">
						<image :src="item.binfo.logo" class="t1"></image><text class="t2">{{item.binfo.name}}</text>
					</view>
					<view class="p2">
						<view class="p2-1" v-if="showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)">
							<text class="t1" :style="{color:t('color1')}"><text
									style="font-size:24rpx">￥</text>{{item.sell_price}}</text>
							<text class="t2"
								v-if="showprice == '1' && item.market_price*1 > item.sell_price*1">￥{{item.market_price}}</text>
							<text class="t3" v-if="item.juli" style="color:#888;">{{item.juli}}</text>
						</view>
						<view class="p2-1" v-if="item.xunjia_text && item.price_type == 1 && item.sell_price <= 0"
							style="height: 50rpx;line-height: 44rpx;">
							<text v-if="showstyle!=1" class="t1" :style="{color:t('color1'),fontSize:'30rpx'}">询价</text>
							<text v-if="showstyle==1" class="t1" :style="{color:t('color1')}">询价</text>
							<block v-if="item.xunjia_text && item.price_type == 1">
								<view class="lianxi" :style="{background:t('color1')}" @tap.stop="showLinkChange"
									:data-lx_name="item.lx_name" :data-lx_bid="item.lx_bid"
									:data-lx_bname="item.lx_bname" :data-lx_tel="item.lx_tel" data-btntype="2">
									{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>
							</block>
						</view>
					</view>
					<view class="p1" v-if="item.merchant_name"
						style="color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal;">
						<text>{{item.merchant_name}}</text></view>
					<view class="p1" v-if="item.main_business"
						style="color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;">
						<text>{{item.main_business}}</text></view>
					<view class="p3" v-if="(showsales=='1' && item.sales>0) || showstock=='1'">
						<text v-if="showsales=='1' && item.sales>0">已售{{item.sales}}</text>
						<text v-if="(showsales=='1' && item.sales>0) && showstock=='1'"
							style="padding:0 4px;font-size:22rpx">|</text>
						<text v-if="showstock=='1'">仅剩{{item.stock}}</text>
					</view>
					<view v-if="(showsales !='1' ||  item.sales<=0) && item.main_business" style="height: 44rpx;">
					</view>

					<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}"
						v-if="showcart==1 && !item.price_type" @click.stop="buydialogChange"
						:data-proid="item[idfield]"><text class="iconfont icon_gouwuche"></text></view>
					<view class="p4" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}"
						v-if="showcart==2 && !item.price_type" @click.stop="buydialogChange"
						:data-proid="item[idfield]">
						<image :src="cartimg" class="img" />
					</view>
				</view>
				<view v-if="showcoupon==1 && (item.couponlist).length>0" class="couponitem">
					<view class="f1">
						<view v-for="(coupon, index2) in item.couponlist" :key="index2" class="t"
							:style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}">
							<text v-if="coupon.minprice > 0">满{{coupon.minprice}}减{{coupon.money}}</text>
							<text v-if="coupon.minprice == 0">{{coupon.money}}元无门槛</text>
						</view>
					</view>
				</view>
				<view class="bg-desc" v-if="item.hongbaoEdu > 0"
					:style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}">
					可获额度 +{{item.hongbaoEdu}}</view>
					
				<view class="bg-desc" v-if="item.huang_dx.types > 0"
					:style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}">
					可抵扣红包:{{item.huang_dx.nums}}</view>
			</view>
		</view>
		<buydialog v-if="buydialogShow" :proid="proid" @addcart="addcart" @buydialogChange="buydialogChange"
			:menuindex="menuindex"></buydialog>
		<view class="posterDialog linkDialog" v-if="showLinkStatus">
			<view class="main">
				<view class="close" @tap="showLinkChange">
					<image class="img" src="/static/img/close.png" />
				</view>
				<view class="content">
					<view class="title">{{lx_name}}</view>
					<view class="row" v-if="lx_bid > 0">
						<view class="f1">店铺名称</view>
						<view class="f2" @tap="goto" :data-url="'/pagesExt/business/index?id='+lx_bid">{{lx_bname}}
							<image src="/static/img/arrowright.png" class="image" />
						</view>
					</view>
					<view class="row" v-if="lx_tel">
						<view class="f1">联系电话</view>
						<view class="f2" @tap="goto" :data-url="'tel::'+lx_tel" :style="{color:t('color1')}">{{lx_tel}}
							<image src="/static/img/copy.png" class="copyicon" @tap.stop="copy" :data-text="lx_tel">
							</image>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		props: {
			list: {
				type: Array,
				required: true
			},
			// offset 间距，单位为 px
			offset: {
				type: Number,
				default: 8
			},
			// 列表渲染的 key 的键名，值必须唯一，默认为 id
			idfield: {
				type: String,
				default: "id"
			},
			// 图片 src 的键名
			imageSrcKey: {
				type: String,
				default: "pic"
			},
			// 列数
			cols: {
				type: Number,
				default: 2,
				validator: (num) => num >= 2
			},
			imageStyle: {
				type: Object
			},

			showstyle: {
				default: 2
			},
			menuindex: {
				default: -1
			},
			saleimg: {
				default: ''
			},
			showname: {
				default: 1
			},
			namecolor: {
				default: '#333'
			},
			showprice: {
				default: '1'
			},
			showsales: {
				default: '1'
			},
			showcart: {
				default: '1'
			},
			cartimg: {
				default: '/static/imgsrc/cart.svg'
			},
			showstock: {
				default: '0'
			},
			showbname: {
				default: '0'
			},
			showcoupon: {
				default: '0'
			},
			probgcolor:{default:'#fff'}
		},
		data() {
			return {
				topArr: [], // left, right 多个时依次表示第几列的数据
				allPositionArr: [], // 保存所有的位置信息
				allHeightArr: [], // 保存所有的 height 信息
				height: 0, // 外层包裹高度
				oldNum: 0,
				num: 0,
				buydialogShow: false,
				proid: 0,
				showLinkStatus: false,
				lx_name: '',
				lx_bid: '',
				lx_tel: '',
				forceRefresh: false, // 用于强制刷新布局的标志
				loadedItems: {}, // 用于跟踪每个商品是否已加载完成
				shiningItems: {}, // 用于跟踪每个商品是否正在显示闪光效果
			};
		},
		created() {
			this.refresh();
		},
		watch: {
			// 监听list变化，当数据更新时重新刷新布局
			list: {
				handler(newVal, oldVal) {
					console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][list_watch_001] 瀑布流数据变化，重新计算布局');
					
					// 如果是首次加载或完全刷新数据，则执行完整刷新
					if (!oldVal || oldVal.length === 0 || newVal.length <= oldVal.length) {
						this.refresh();
						// 重置所有加载状态
						this.resetLoadingStates(newVal.length);
					} else {
						// 如果是追加数据，只处理新增部分，保持已有元素位置不变
						this.appendRefresh(oldVal.length);
						// 只为新增加的项目初始化加载状态
						this.initLoadingStates(oldVal.length, newVal.length);
					}
					
					// 允许DOM更新后再执行计算
					this.$nextTick(() => {
						if (newVal.length > 0) {
							// 只有在完全刷新时重置所有计算数据
							if (!oldVal || oldVal.length === 0) {
								this.allHeightArr = [];
								this.allPositionArr = [];
								this.num = 0;
							}
						}
					});
				},
				deep: true
			}
		},
		methods: {
			buydialogChange: function(e) {
				if (!this.buydialogShow) {
					this.proid = e.currentTarget.dataset.proid
				}
				this.buydialogShow = !this.buydialogShow;
			},
			addcart: function() {
				this.$emit('addcart');
			},
			showLinkChange: function(e) {
				var that = this;
				that.showLinkStatus = !that.showLinkStatus;
				that.lx_name = e.currentTarget.dataset.lx_name;
				that.lx_bid = e.currentTarget.dataset.lx_bid;
				that.lx_bname = e.currentTarget.dataset.lx_bname;
				that.lx_tel = e.currentTarget.dataset.lx_tel;
			},

			// 重置所有加载状态
			resetLoadingStates(length) {
				// 初始化所有项目为未加载状态
				const loadedItems = {};
				const shiningItems = {};
				for (let i = 0; i < length; i++) {
					loadedItems[i] = false;
					shiningItems[i] = false;
				}
				this.loadedItems = loadedItems;
				this.shiningItems = shiningItems;
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][resetLoadingStates_001] 重置所有商品加载状态');
			},
			
			// 初始化新增项目的加载状态
			initLoadingStates(startIndex, endIndex) {
				for (let i = startIndex; i < endIndex; i++) {
					this.$set(this.loadedItems, i, false);
					this.$set(this.shiningItems, i, false);
				}
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][initLoadingStates_001] 初始化新增商品加载状态');
			},
			
			// 显示闪光效果
			showShineEffect(index) {
				// 设置闪光效果为可见
				this.$set(this.shiningItems, index, true);
				
				// 2秒后隐藏闪光效果
				setTimeout(() => {
					this.$set(this.shiningItems, index, false);
				}, 2000);
				
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][showShineEffect_001] 显示商品闪光效果:', index);
			},

			imageLoadHandle(index) {
				// 如果已经计算过这个索引并且不是强制刷新，不再重复计算
				if (this.allHeightArr[index] && this.allPositionArr[index] && !this.forceRefresh) {
					return;
				}
				
				const id = "waterfalls-list-id-" + this.list[index][this.idfield],
					query = uni.createSelectorQuery().in(this);
				query
					.select("#" + id)
					.fields({
						size: true
					}, (data) => {
						if (!data) {
							console.log('2025-06-10 20:55:53,565-WARN-[dp-product-waterfall][imageLoadHandle_001] 瀑布流未找到元素:', id);
							return;
						}
						
						this.num++;
						console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][imageLoadHandle_002] 瀑布流图片加载完成:', index, '总数:', this.num, '列表长度:', this.list.length);
						
						this.$set(this.allHeightArr, index, data.height);
						const getTopArrMsg = () => {
							let arrtmp = [...this.topArr].sort((a, b) => a - b);
							return {
								shorterIndex: this.topArr.indexOf(arrtmp[0]),
								shorterValue: arrtmp[0],
								longerIndex: this.topArr.indexOf(arrtmp[this.cols - 1]),
								longerValue: arrtmp[this.cols - 1],
							};
						};
						const {
							shorterIndex,
							shorterValue
						} = getTopArrMsg();
						const position = {
							top: shorterValue + "px",
							left: (data.width + this.offset) * shorterIndex + "px",
						};
						this.$set(this.allPositionArr, index, position);
						this.topArr[shorterIndex] =
							shorterValue + this.allHeightArr[index] + this.offset;
						this.height = getTopArrMsg().longerValue - this.offset;
						
						// 图片加载完成，标记为已加载
						this.$set(this.loadedItems, index, true);
						
						// 显示闪光效果
						this.showShineEffect(index);
						
						// 所有图片都加载完成后通知父组件
						if (this.num === this.list.length) {
							this.oldNum = this.num;
							this.$emit("image-load");
							console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][imageLoadHandle_003] 瀑布流所有图片加载完成');
						}
					})
					.exec();
			},
			appendRefresh(startIndex) {
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][appendRefresh_001] 瀑布流追加刷新，起始索引:', startIndex);
				
				// 使用现有的topArr继续计算，不重置已有元素的位置
				// 检查topArr是否已经初始化
				if (this.topArr.length === 0) {
					let arr = [];
					for (let i = 0; i < this.cols; i++) {
						arr.push(0);
					}
					this.topArr = arr;
				}
				
				// 仅处理新增的部分
				this.forceRefresh = true;
				
				// 设置一个标志，300ms后取消强制刷新状态
				setTimeout(() => {
					this.forceRefresh = false;
					console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][appendRefresh_002] 瀑布流追加刷新完成');
				}, 300);
			},
			refresh() {
				console.log('2025-06-10 20:55:53,565-INFO-[dp-product-waterfall][refresh_001] 刷新瀑布流布局');
				let arr = [];
				for (let i = 0; i < this.cols; i++) {
					arr.push(0);
				}
				this.topArr = arr;
				this.num = 0;
				this.oldNum = 0;
				this.height = 0;
				this.forceRefresh = true;
				
				// 重置所有加载状态
				this.resetLoadingStates(this.list.length);
				
				// 设置一个标志，300ms后取消强制刷新状态
				setTimeout(() => {
					this.forceRefresh = false;
				}, 300);
			},
		},
	};
</script>
<style scoped>
	.waterfalls-box {
		position: relative;
		width: 100%;
		overflow: hidden;
	}

	.waterfalls-box .waterfalls-list {
		width: calc((100% - var(--offset) * (var(--cols) - 1)) / var(--cols));
		position: absolute;
		background-color: #fff;
		border-radius: 8rpx;
		left: calc(-50% - var(--offset));
		overflow: hidden;
	}

	.waterfalls-box .waterfalls-list .waterfalls-list-image {
		width: 100%;
		will-change: transform;
		border-radius: 8rpx 8rpx 0 0;
		display: block;
	}
	
	/* 加载中遮罩层样式 */
	.loading-mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.8);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		z-index: 10;
	}
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f0f0f0;
		border-top: 4rpx solid #f5222d;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
	
	.loading-text {
		margin-top: 20rpx;
		font-size: 24rpx;
		color: #666;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	/* 闪光效果样式 */
	.shine-effect {
		position: absolute;
		top: 0;
		left: -150%;
		width: 100%;
		height: 100%;
		background: linear-gradient(
			to right, 
			rgba(255, 255, 255, 0) 0%,
			rgba(255, 255, 255, 0.3) 50%,
			rgba(255, 255, 255, 0) 100%
		);
		transform: skewX(-25deg);
		z-index: 5;
		animation: shine 2s ease-in-out;
	}
	
	@keyframes shine {
		0% { left: -150%; }
		100% { left: 150%; }
	}

	.waterfalls-box .saleimg {
		position: absolute;
		width: 60px;
		height: auto;
		top: 0;
		left: -3px;
		z-index: 6;
	}

	.waterfalls-box .product-info {
		padding: 20rpx 20rpx;
		position: relative;
	}

	.waterfalls-box .product-info .p1 {
		color: #323232;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 36rpx;
		margin-bottom: 10rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.waterfalls-box .product-info .p2 {
		display: flex;
		align-items: center;
		overflow: hidden;
		padding: 2px 0
	}

	.waterfalls-box .product-info .p2-1 {
		flex-grow: 1;
		flex-shrink: 1;
		height: 40rpx;
		line-height: 40rpx;
		overflow: hidden;
		white-space: nowrap
	}

	.waterfalls-box .product-info .p2-1 .t1 {
		font-size: 36rpx;
		font-weight: bold;
	}

	.waterfalls-box .product-info .p2-1 .t2 {
		margin-left: 10rpx;
		font-size: 24rpx;
		color: #aaa;
		text-decoration: line-through;
		/*letter-spacing:-1px*/
	}

	.waterfalls-box .product-info .p2-1 .t3 {
		margin-left: 10rpx;
		font-size: 22rpx;
		color: #999;
	}

	.waterfalls-box .product-info .p2-2 {
		font-size: 20rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: right;
		padding-left: 20rpx;
		color: #999
	}

	.waterfalls-box .product-info .p3 {
		color: #999999;
		font-size: 20rpx;
		margin-top: 10rpx
	}

	.waterfalls-box .product-info .p4 {
		width: 52rpx;
		height: 52rpx;
		border-radius: 50%;
		position: absolute;
		display: relative;
		bottom: 16rpx;
		right: 20rpx;
		text-align: center;
	}

	.waterfalls-box .product-info .p4 .icon_gouwuche {
		font-size: 30rpx;
		height: 52rpx;
		line-height: 52rpx
	}

	.waterfalls-box .product-info .p4 .img {
		width: 100%;
		height: 100%
	}

	.waterfalls-box .product-info .binfo {
		padding: 6rpx 0;
		display: flex;
		align-items: center;
		min-width: 0;
	}

	.waterfalls-box .product-info .binfo .t1 {
		width: 30rpx;
		height: 30rpx;
		border-radius: 50%;
		margin-right: 10rpx;
		flex-shrink: 0;
	}

	.waterfalls-box .product-info .binfo .t2 {
		color: #666;
		font-size: 24rpx;
		font-weight: normal;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.waterfalls-box .couponitem {
		width: 100%;
		padding: 0 20rpx 20rpx 20rpx;
		font-size: 24rpx;
		color: #333;
		display: flex;
		align-items: center;
	}

	.waterfalls-box .couponitem .f1 {
		flex: 1;
		display: flex;
		flex-wrap: nowrap;
		overflow: hidden
	}

	.waterfalls-box .couponitem .f1 .t {
		margin-right: 10rpx;
		border-radius: 3px;
		font-size: 22rpx;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 10rpx;
		flex-shrink: 0;
		overflow: hidden
	}

	.bg-desc {
		color: #fff;
		padding: 10rpx 20rpx;
	}

	.posterDialog {
		position: fixed;
		z-index: 9;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.8);
		top: var(--window-top);
		left: 0
	}

	.posterDialog .main {
		width: 80%;
		margin: 60rpx 10% 30rpx 10%;
		background: #fff;
		position: relative;
		border-radius: 20rpx
	}

	.posterDialog .close {
		position: absolute;
		padding: 20rpx;
		top: 0;
		right: 0
	}

	.posterDialog .close .img {
		width: 32rpx;
		height: 32rpx;
	}

	.posterDialog .content {
		width: 100%;
		padding: 70rpx 20rpx 30rpx 20rpx;
		color: #333;
		font-size: 30rpx;
		text-align: center
	}

	.posterDialog .content .img {
		width: 540rpx;
		height: 960rpx
	}

	.linkDialog {
		background: rgba(0, 0, 0, 0.4);
		z-index: 11;
	}

	.linkDialog .main {
		width: 90%;
		position: fixed;
		top: 50%;
		left: 50%;
		margin: 0;
		-webkit-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);
	}

	.linkDialog .title {
		font-weight: bold;
		margin-bottom: 30rpx;
	}

	.linkDialog .row {
		display: flex;
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 16rpx;
	}

	.linkDialog .row .f1 {
		width: 40%;
		text-align: left;
	}

	.linkDialog .row .f2 {
		width: 60%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: right;
		align-items: center;
	}

	.linkDialog .image {
		width: 28rpx;
		height: 28rpx;
		margin-left: 8rpx;
		margin-top: 2rpx;
	}

	.linkDialog .copyicon {
		width: 28rpx;
		height: 28rpx;
		margin-left: 8rpx;
		position: relative;
		top: 4rpx;
	}

	.lianxi {
		color: #fff;
		border-radius: 50rpx 50rpx;
		line-height: 50rpx;
		text-align: center;
		font-size: 22rpx;
		padding: 0 14rpx;
		display: inline-block;
		float: right;
	}
</style>
