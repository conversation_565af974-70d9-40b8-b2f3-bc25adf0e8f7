<template>
  <view class="diagnosis-guide">
    <!-- 顶部导航 -->
    <view class="nav-header">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="nav-title">面诊指南</view>
      <view class="nav-right"></view>
    </view>

    <!-- 页面内容 -->
    <scroll-view scroll-y class="guide-content">
      <!-- 功能介绍 -->
      <view class="intro-section">
        <view class="intro-card">
          <view class="intro-icon">
            <image src="/static/images/diagnosis/face-intro.png" mode="aspectFit"></image>
          </view>
          <view class="intro-text">
            <text class="intro-title">AI智能面诊</text>
            <text class="intro-desc">通过面部图片分析，了解您的健康状况</text>
          </view>
        </view>
        
        <view class="features-list">
          <view class="feature-item">
            <text class="feature-icon">🎯</text>
            <text class="feature-text">精准识别面部特征</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">📊</text>
            <text class="feature-text">专业健康分析报告</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">💡</text>
            <text class="feature-text">个性化调理建议</text>
          </view>
        </view>
      </view>

      <!-- 拍摄指南 -->
      <view class="guide-section">
        <view class="section-header">
          <text class="section-icon">📷</text>
          <text class="section-title">面诊拍摄指南</text>
        </view>
        
        <view class="guide-steps">
          <view class="step-item">
            <view class="step-number">1</view>
            <view class="step-content">
              <text class="step-title">正面拍摄</text>
              <text class="step-desc">请正面面对摄像头，确保面部完整清晰</text>
            </view>
            <view class="step-image">
              <image src="/static/images/diagnosis/face-step1.png" mode="aspectFit"></image>
            </view>
          </view>

          <view class="step-item">
            <view class="step-number">2</view>
            <view class="step-content">
              <text class="step-title">充足光线</text>
              <text class="step-desc">选择自然光充足的环境，避免强光直射</text>
            </view>
            <view class="step-image">
              <image src="/static/images/diagnosis/face-step2.png" mode="aspectFit"></image>
            </view>
          </view>

          <view class="step-item">
            <view class="step-number">3</view>
            <view class="step-content">
              <text class="step-title">表情自然</text>
              <text class="step-desc">保持自然表情，眼神平视镜头</text>
            </view>
            <view class="step-image">
              <image src="/static/images/diagnosis/face-step3.png" mode="aspectFit"></image>
            </view>
          </view>

          <view class="step-item">
            <view class="step-number">4</view>
            <view class="step-content">
              <text class="step-title">无遮挡物</text>
              <text class="step-desc">请摘除帽子、眼镜等遮挡物</text>
            </view>
            <view class="step-image">
              <image src="/static/images/diagnosis/face-step4.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 注意事项 -->
      <view class="notice-section">
        <view class="section-header">
          <text class="section-icon">⚠️</text>
          <text class="section-title">注意事项</text>
        </view>
        
        <view class="notice-list">
          <view class="notice-item">
            <text class="notice-icon">•</text>
            <text class="notice-text">请在拍摄前洗净面部，确保面部清洁</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">•</text>
            <text class="notice-text">避免在化妆后拍摄，以免影响分析结果</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">•</text>
            <text class="notice-text">选择安静的环境，避免他人干扰</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">•</text>
            <text class="notice-text">如有面部疾病，请先咨询医生</text>
          </view>
        </view>
      </view>

      <!-- 收费说明 -->
      <view class="price-section" v-if="config.price && config.price > 0">
        <view class="section-header">
          <text class="section-icon">💰</text>
          <text class="section-title">收费说明</text>
        </view>
        
        <view class="price-card">
          <view class="price-info">
            <text class="price-label">单次面诊价格</text>
            <text class="price-value">¥{{ config.price }}</text>
          </view>
          
          <view class="free-info" v-if="config.can_use_free">
            <text class="free-badge">VIP免费</text>
            <text class="free-desc">今日还可免费使用 {{ config.free_times - config.today_used }} 次</text>
          </view>
          
          <view class="remaining-info" v-if="config.use_public_api">
            <text class="remaining-label">平台剩余次数：</text>
            <text class="remaining-count">{{ config.platform_calls_remaining }}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作 -->
    <view class="bottom-actions">
      <view class="action-buttons">
        <button class="btn-secondary" @click="viewHistory">
          查看历史
        </button>
        <button class="btn-primary" @click="startDiagnosis" :disabled="!canStart">
          开始面诊
        </button>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading-overlay" v-if="loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载配置...</text>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();

export default {
  data() {
    return {
      loading: true,
      config: {
        price: 0,
        free_times: 0,
        today_used: 0,
        can_use_free: false,
        is_vip: false,
        description: '',
        guide_content: '',
        use_public_api: false,
        platform_calls_remaining: 0
      },
      canStart: false
    }
  },

  onLoad(options) {
    this.getConfig();
  },

  methods: {
    // 获取面诊配置
    async getConfig() {
      try {
        this.loading = true;
        
        const res = await app.get('ApiDiagnosis/getConfig', {
          diagnosis_type: 'face'
        });
        
        if (res.code === 1) {
          this.config = res.data;
          this.canStart = true;
          
          // 检查是否可以开始面诊
          if (this.config.use_public_api && this.config.platform_calls_remaining <= 0) {
            this.canStart = false;
            uni.showToast({
              title: '平台次数不足',
              icon: 'none'
            });
          }
        } else {
          uni.showToast({
            title: res.msg || '获取配置失败',
            icon: 'none'
          });
          this.canStart = false;
        }
      } catch (error) {
        console.error('获取配置失败:', error);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.canStart = false;
      } finally {
        this.loading = false;
      }
    },

    // 开始面诊
    startDiagnosis() {
      if (!this.canStart) {
        return;
      }

      // 跳转到拍摄页面
      uni.navigateTo({
        url: '/pagesB/diagnosis/face/camera'
      });
    },

    // 查看历史记录
    viewHistory() {
      uni.navigateTo({
        url: '/pagesB/diagnosis/face/history'
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss" scoped>
.diagnosis-guide {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44rpx 32rpx 20rpx;
  position: relative;
  z-index: 10;
}

.nav-back {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  
  .icon-back {
    font-size: 32rpx;
    color: #fff;
  }
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.nav-right {
  width: 64rpx;
}

/* 内容区域 */
.guide-content {
  flex: 1;
  padding: 0 32rpx 200rpx;
  max-height: calc(100vh - 200rpx);
}

/* 介绍部分 */
.intro-section {
  margin-bottom: 48rpx;
}

.intro-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 32rpx;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.intro-icon {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  
  image {
    width: 100%;
    height: 100%;
  }
}

.intro-text {
  flex: 1;
}

.intro-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.intro-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.features-list {
  display: flex;
  justify-content: space-between;
}

.feature-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  margin: 0 8rpx;
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.feature-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.3;
}

/* 指南部分 */
.guide-section,
.notice-section,
.price-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 步骤列表 */
.guide-steps {
  .step-item {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 24rpx;
}

.step-content {
  flex: 1;
  margin-right: 16rpx;
}

.step-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.step-image {
  width: 80rpx;
  height: 80rpx;
  
  image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
  }
}

/* 注意事项 */
.notice-list {
  .notice-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.notice-icon {
  font-size: 24rpx;
  color: #667eea;
  margin-right: 12rpx;
  margin-top: 4rpx;
}

.notice-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 价格信息 */
.price-card {
  .price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
  }
}

.price-label {
  font-size: 28rpx;
  color: #333;
}

.price-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #667eea;
}

.free-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.free-badge {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  margin-right: 16rpx;
}

.free-desc {
  font-size: 24rpx;
  color: #666;
}

.remaining-info {
  display: flex;
  align-items: center;
}

.remaining-label {
  font-size: 24rpx;
  color: #666;
}

.remaining-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #667eea;
  margin-left: 8rpx;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 32rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.btn-secondary,
.btn-primary {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
}

.btn-secondary {
  background: #f5f5f5;
  color: #333;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  
  &:disabled {
    background: #ccc;
    color: #999;
  }
}

/* 加载中 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #fff;
  padding: 48rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>