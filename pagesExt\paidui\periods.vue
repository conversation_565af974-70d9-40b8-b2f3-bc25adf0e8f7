<template>
<view class="container">
	<block v-if="isload">
		<!-- 用户信息横幅 -->
		<view class="banner" :style="{background:t('color1')}">
			<image :src="userinfo.headimg || '/static/img/default-avatar.png'"/>
			<view class="info">
				<text class="nickname">{{userinfo.nickname || '用户'}}</text>
				<text class="subtitle">我的分期排队</text>
			</view>
		</view>
		
		<!-- 统计信息卡片 -->
		<view class="stats-container">
			<view class="stats-card">
				<view class="stats-row">
					<view class="stat-item">
						<text class="stat-value">{{user_summary.total_periods || 0}}</text>
						<text class="stat-label">总分期数</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{user_summary.completed_periods || 0}}</text>
						<text class="stat-label">已完成</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{user_summary.waiting_periods || 0}}</text>
						<text class="stat-label">待分红</text>
					</view>
				</view>
				<view class="stats-row">
					<view class="stat-item">
						<text class="stat-value">{{user_summary.completion_rate || 0}}%</text>
						<text class="stat-label">完成率</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">￥{{user_summary.total_distributed || 0}}</text>
						<text class="stat-label">已分红金额</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">￥{{user_summary.total_waiting || 0}}</text>
						<text class="stat-label">待分红金额</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 状态切换标签 -->
		<dd-tab :itemdata="['全部','待分红','已分红']" :itemst="['all','waiting','completed']" :st="currentStatus" :isfixed="false" @changetab="changeStatus"></dd-tab>
		
		<!-- 分期列表 -->
		<view class="periods-content">
			<block v-if="periodsList && periodsList.length > 0">
				<view v-for="(item, index) in periodsList" :key="index" class="period-card">
					<view class="period-header">
						<view class="period-title">
							<text class="period-id">分期 #{{item.id}}</text>
							<text class="period-num">第{{item.period_num}}期/共{{item.total_periods}}期</text>
						</view>
						<view class="period-status">
							<block v-if="item.status == 0">
								<text class="status-waiting">待分红</text>
							</block>
							<block v-else>
								<text class="status-completed">已分红</text>
							</block>
						</view>
					</view>
					
					<view class="period-info">
						<view class="info-row">
							<view class="info-item">
								<text class="info-label">排队ID:</text>
								<text class="info-value">{{item.paidui_id}}</text>
							</view>
							<view class="info-item">
								<text class="info-label">分红金额:</text>
								<text class="info-value amount">￥{{item.period_amount}}</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item">
								<text class="info-label">创建时间:</text>
								<text class="info-value">{{item.createtime_text}}</text>
							</view>
							<view class="info-item" v-if="item.status == 1">
								<text class="info-label">分红时间:</text>
								<text class="info-value">{{item.distributed_time_text}}</text>
							</view>
						</view>
					</view>
					
					<view class="period-progress" v-if="item.paidui_summary">
						<view class="progress-info">
							<text class="progress-text">排队进度: {{item.paidui_summary.distributed_periods}}/{{item.paidui_summary.total_periods}} ({{item.paidui_summary.completion_rate}}%)</text>
						</view>
						<view class="progress-bar">
							<view class="progress-fill" :style="{width: item.paidui_summary.completion_rate + '%'}"></view>
						</view>
					</view>
					
					<view class="period-actions">
						<view class="action-btn" @tap="showPeriodDetail(item)">
							<text>查看详情</text>
						</view>
						<view class="action-btn" @tap="viewPaiduiDetail(item.paidui_id)">
							<text>查看排队</text>
						</view>
					</view>
				</view>
			</block>
		</view>
		
		<!-- 分期详情弹窗 -->
		<view v-if="showDetailModal" class="modal-overlay" @tap="closeDetailModal">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">分期详情</text>
					<text class="modal-close" @tap="closeDetailModal">×</text>
				</view>
				<view class="modal-body">
					<view class="detail-item">
						<text class="detail-label">分期ID:</text>
						<text class="detail-value">{{currentPeriod.id}}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">排队ID:</text>
						<text class="detail-value">{{currentPeriod.paidui_id}}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">期数:</text>
						<text class="detail-value">第{{currentPeriod.period_num}}期 / 共{{currentPeriod.total_periods}}期</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">分红金额:</text>
						<text class="detail-value amount">￥{{currentPeriod.period_amount}}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">总金额:</text>
						<text class="detail-value">￥{{currentPeriod.total_amount}}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">状态:</text>
						<text class="detail-value">{{currentPeriod.status_text}}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">创建时间:</text>
						<text class="detail-value">{{currentPeriod.createtime_text}}</text>
					</view>
					<view class="detail-item" v-if="currentPeriod.status == 1">
						<text class="detail-label">分红时间:</text>
						<text class="detail-value">{{currentPeriod.distributed_time_text}}</text>
					</view>
					<view class="detail-item" v-if="currentPeriod.ordernum">
						<text class="detail-label">订单号:</text>
						<text class="detail-value">{{currentPeriod.ordernum}}</text>
					</view>
					<view class="detail-item" v-if="currentPeriod.nickname">
						<text class="detail-label">会员昵称:</text>
						<text class="detail-value">{{currentPeriod.nickname}}</text>
					</view>
				</view>
			</view>
		</view>
	</block>
	
	<!-- 数据状态组件 -->
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      nodata: false,
      nomore: false,
      periodsList: [],
      userinfo: {
        nickname: '用户',
        headimg: '/static/img/default-avatar.png'
      },
      user_summary: {
        total_periods: 0,
        completed_periods: 0,
        waiting_periods: 0,
        completion_rate: 0,
        total_distributed: 0,
        total_waiting: 0
      },
      pagination: {},
      pagenum: 1,
      currentStatus: 'all',
      showDetailModal: false,
      currentPeriod: {}
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getPeriodsData();
  },

  onPullDownRefresh: function () {
    this.getPeriodsData();
  },

  onReachBottom: function () {
    if (!this.nodata && !this.nomore && this.pagination.page < this.pagination.pages) {
      this.pagenum = this.pagenum + 1;
      this.getPeriodsData(true);
    }
  },

  methods: {
    // 标记页面加载完成
    loaded: function() {
      this.isload = true;
      uni.stopPullDownRefresh();
    },

    // 获取主题颜色
    t: function(colorName) {
      if (colorName === 'color1') {
        return '#1890ff';
      }
      return '#333';
    },

    // 获取分期数据
    getPeriodsData: function (loadmore) {
      if (!loadmore) {
        this.pagenum = 1;
        this.periodsList = [];
      }
      
      var that = this;
      that.loading = true;
      that.nodata = false;
      that.nomore = false;
      
      var params = {
        page: that.pagenum,
        limit: 20
      };
      
      // 根据状态筛选
      if (that.currentStatus === 'waiting') {
        params.status = 0;  // 待分红
      } else if (that.currentStatus === 'completed') {
        params.status = 1;  // 已分红
      }
      
      console.log('筛选参数:', params);
      
      app.post('ApiPaidui/myPeriods', params, function (res) {
        that.loading = false;
        
        if (res.status == 1) {
          var data = res.data || [];
          
          if (that.pagenum == 1) {
            // 设置用户信息和统计信息
            that.userinfo = res.userinfo || {
              nickname: '用户',
              headimg: '/static/img/default-avatar.png'
            };
            that.user_summary = res.user_summary || {
              total_periods: 0,
              completed_periods: 0,
              waiting_periods: 0,
              completion_rate: 0,
              total_distributed: 0,
              total_waiting: 0
            };
            that.pagination = res.pagination || {};
            
            uni.setNavigationBarTitle({
              title: '我的分期排队'
            });
            
            that.periodsList = data;
            
            if (data.length == 0) {
              that.nodata = true;
            }
            
            that.loaded();
          } else {
            if (data.length == 0) {
              that.nomore = true;
            } else {
              var newData = that.periodsList.concat(data);
              that.periodsList = newData;
            }
          }
        } else {
          app.error(res.msg || '获取数据失败');
        }
      }, function() {
        that.loading = false;
        app.error('网络请求失败');
      });
    },

    // 切换状态
    changeStatus: function (status) {
      console.log('切换状态到:', status);
      this.currentStatus = status;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getPeriodsData();
    },

    // 显示分期详情
    showPeriodDetail: function (period) {
      var that = this;
      that.loading = true;
      
      app.post('ApiPaidui/periodDetail', {period_id: period.id}, function (res) {
        that.loading = false;
        
        if (res.status == 1) {
          that.currentPeriod = res.data || period;
          that.showDetailModal = true;
        } else {
          app.error(res.msg || '获取详情失败');
        }
      }, function() {
        that.loading = false;
        app.error('网络请求失败');
      });
    },

    // 关闭详情弹窗
    closeDetailModal: function () {
      this.showDetailModal = false;
      this.currentPeriod = {};
    },

    // 查看排队详情
    viewPaiduiDetail: function (paidui_id) {
      app.goto('/pagesExt/paidui/paidui?paidui_id=' + paidui_id);
    },

    // 查看分期详情
    viewPaidui: function (paidui_id) {
      app.goto('/pagesExt/paidui/paidui?paidui_id=' + paidui_id);
    },

    // 格式化时间
    formatTime: function (timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp * 1000);
      return date.getFullYear() + '-' + 
             String(date.getMonth() + 1).padStart(2, '0') + '-' + 
             String(date.getDate()).padStart(2, '0') + ' ' + 
             String(date.getHours()).padStart(2, '0') + ':' + 
             String(date.getMinutes()).padStart(2, '0');
    }
  }
};
</script>

<style>
.container {
  width: 100%;
  background-color: #f5f5f5;
}

/* 横幅样式 */
.banner {
  display: flex;
  width: 100%;
  height: 400rpx;
  padding: 40rpx 32rpx;
  color: #fff;
  position: relative;
}

.banner image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.banner .info {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding-top: 10rpx;
}

.banner .nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.banner .subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 统计信息样式 */
.stats-container {
  width: 100%;
  padding: 0 30rpx;
  margin-top: -180rpx;
  position: relative;
  margin-bottom: 20rpx;
}

.stats-card {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 分期列表样式 */
.periods-content {
  width: 100%;
  padding: 0 30rpx;
}

.period-card {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.period-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.period-title {
  display: flex;
  flex-direction: column;
}

.period-id {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.period-num {
  font-size: 24rpx;
  color: #666;
}

.period-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-waiting {
  background: #fff7e6;
  color: #fa8c16;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.period-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.info-value.amount {
  color: #ff4d4f;
  font-weight: bold;
}

.period-progress {
  margin-bottom: 20rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  transition: width 0.3s ease;
}

.period-actions {
  display: flex;
  justify-content: space-between;
}

.action-btn {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #f0f0f0;
  border-radius: 8rpx;
  margin: 0 10rpx;
  font-size: 26rpx;
  color: #666;
}

.action-btn:first-child {
  margin-left: 0;
}

.action-btn:last-child {
  margin-right: 0;
}

.action-btn:active {
  background: #e0e0e0;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  max-height: 80%;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
}

.modal-body {
  padding: 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.detail-value.amount {
  color: #ff4d4f;
  font-weight: bold;
}
</style> 