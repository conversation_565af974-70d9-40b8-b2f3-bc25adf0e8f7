<template>
	<div class="marquee">
		<div class="text">
			<slot></slot>
		</div>
	</div>
</template>

<script>
	export default {
		name: 'MarqueeComponent',
		props: {

		},
		mounted() {

		},

	};
</script>

<style scoped>
	@keyframes scroll {
		0% {
			transform: translateX(100%);
		}

		100% {
			transform: translateX(-100%);
		}
	}

	.marquee {
		overflow: hidden;
		white-space: nowrap;
	}

	.text {
		display: inline-block;
		animation-iteration-count: infinite;
		animation : scroll 15s linear infinite;
		color: #E5733D;
	}
</style>