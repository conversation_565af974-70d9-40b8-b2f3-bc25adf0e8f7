<template>
    <view class="wxface">
        <view class="face-grid">
            <view 
                class="face-item" 
                v-for="(face, index) in faces" 
                :key="index" 
                @tap="selectFace(face)"
            >
                {{ face }}
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            faces: [
                '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
                '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
                '😘', '😗', '😙', '😚', '😋', '😜', '😝', '😛',
                '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐',
                // 您可以根据需要添加更多表情
            ],
        };
    },
    methods: {
        selectFace(face) {
            this.$emit('selectface', face);
        },
    },
};
</script>

<style scoped>
.wxface {
    width: 100%;
    background-color: #f5f5f5;
    padding: 10rpx;
    box-sizing: border-box;
}

.face-grid {
    display: flex;
    flex-wrap: wrap;
}

.face-item {
    width: 20%;
    text-align: center;
    font-size: 40rpx;
    padding: 10rpx 0;
    cursor: pointer;
}
</style>
