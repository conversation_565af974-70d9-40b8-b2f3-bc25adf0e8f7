<template>
<view class="container">
	<block v-if="isload">
		<view class="search-container">
			<view class="title-box">
				<view>排单活动列表</view>
			</view>
		</view>
		<view class="content-container">
			<scroll-view class="activity-box" scroll-y="true" @scrolltolower="scrolltolower">
				<view class="activity-itemlist">
					<view class="item" v-for="(item,index) in datalist" :key="item.id" @click="toActivityDetail" :data-id="item.id">
						<view class="activity-header">
							<view class="activity-name">{{item.name}}</view>
							<view class="activity-status" style="color: #FF5722">进行中</view>
						</view>
						<view class="activity-info">
							<view class="info-row">
								<view class="label">复制模式：</view>
								<view class="value">{{item.copy_mode_text}}</view>
							</view>
							<view class="info-row">
								<view class="label">财富点位：</view>
								<view class="value">{{item.wealth_position_text}}</view>
							</view>
							<view class="info-row">
								<view class="label">奖励金额：</view>
								<view class="value" style="color: #FF5722">{{item.wealth_reward_amount}}元</view>
							</view>
						</view>
						<view class="activity-stats">
							<view class="stat-item">
								<view class="stat-value">{{item.participant_count}}</view>
								<view class="stat-label">参与人数</view>
							</view>
							<view class="stat-item">
								<view class="stat-value" style="color: #FF5722">{{item.my_position_count}}</view>
								<view class="stat-label">我的点位</view>
							</view>
							<view class="action-btn" style="background: linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%)">查看详情</view>
						</view>
						<view class="activity-time">创建时间：{{item.createtime_text}}</view>
					</view>
				</view>
				<nomore text="没有更多活动了" v-if="nomore"></nomore>
				<nodata text="暂无排单活动" v-if="nodata"></nodata>
				<view style="width:100%;height:100rpx"></view>
			</scroll-view>
		</view>
	</block>
	<loading v-if="loading" loadstyle="left:62.5%"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			pagenum: 1,
			nomore: false,
			nodata: false,
			datalist: []
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	methods: {
		
		getdata:function(){
			var that = this;
			that.pagenum = 1;
			that.datalist = [];
			that.loaded();
			that.getdatalist();
		},
		getdatalist: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}

			var that = this;
			var pagenum = that.pagenum;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
			app.post('ApiPaidan/getActivityList', {page: pagenum, limit: 10}, function (res) { 
				that.loading = false;
				uni.stopPullDownRefresh();

				if(res.code == 1){
					var data = res.data.list;
					if (data.length == 0) {
						if(pagenum == 1){
							that.nodata = true;
						}else{
							that.nomore = true;
						}
					}
					var datalist = that.datalist;
					var newdata = datalist.concat(data);
					that.datalist = newdata;
				}else{
					that.$refs.popmsg.show({type: 'error', msg: res.msg});
				}
			});
		},

		scrolltolower: function () {
			if (!this.nomore) {
				this.pagenum = this.pagenum + 1;    
				this.getdatalist(true);
			}
		},
		
		toActivityDetail(e){
			var id = e.currentTarget.dataset.id;
			app.goto('/pagesB/paidan/activity-products?config_id='+id);
		},
		
		loaded: function () {
			this.isload = true;
		},
		
		getmenuindex: function (e) {
			this.menuindex = e;
		}
	}
};
</script>

<style>
page {height:100%;}
.container{width: 100%;height:100%;max-width:640px;background-color: #f6f6f6;color: #939393;display: flex;flex-direction:column}
.search-container {width: 100%;height: 94rpx;padding: 16rpx 23rpx 14rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}
.title-box{color:#000000;font-size:26rpx;justify-content:center;width: 100%;align-items:center;text-align: center;height:60rpx;line-height: 60rpx;}
.content-container{flex:1;height:100%;display:flex;overflow: hidden;}
.activity-box{width: 100%;height:100%;padding:20rpx;}
.activity-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px;}
.activity-itemlist .item{width:100%;display: block;position: relative;margin-bottom: 20rpx;background: #fff;padding:30rpx;border-radius:16rpx;box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);}
.activity-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:20rpx;}
.activity-name{font-size:32rpx;font-weight:bold;color:#333;flex:1;}
.activity-status{font-size:24rpx;padding:8rpx 16rpx;background:rgba(0,0,0,0.05);border-radius:20rpx;}
.activity-info{margin-bottom:20rpx;}
.info-row{display:flex;margin-bottom:12rpx;}
.info-row .label{font-size:26rpx;color:#666;width:160rpx;}
.info-row .value{font-size:26rpx;color:#333;flex:1;}
.activity-stats{display:flex;align-items:center;margin-bottom:20rpx;}
.stat-item{flex:1;text-align:center;}
.stat-value{font-size:32rpx;font-weight:bold;color:#333;}
.stat-label{font-size:22rpx;color:#999;margin-top:8rpx;}
.action-btn{padding:16rpx 32rpx;color:#fff;font-size:26rpx;border-radius:30rpx;text-align:center;margin-left:40rpx;}
.activity-time{font-size:22rpx;color:#999;text-align:right;}
</style>