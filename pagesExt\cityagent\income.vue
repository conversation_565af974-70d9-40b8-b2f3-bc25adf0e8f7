<template>
<view class="container">
	<block v-if="isload">
		<!-- 顶部统计卡片 -->
		<view class="stats-card">
			<view class="stats-item">
				<text class="stats-number">¥{{statistics.total_income}}</text>
				<text class="stats-label">总收益</text>
			</view>
			<view class="stats-item">
				<text class="stats-number">¥{{statistics.today_income}}</text>
				<text class="stats-label">今日收益</text>
			</view>
			<view class="stats-item">
				<text class="stats-number">¥{{statistics.month_income}}</text>
				<text class="stats-label">本月收益</text>
			</view>
		</view>

		<!-- 标签页 -->
		<dd-tab :itemdata="['收益明细','转换记录']" :itemst="['0','1']" :st="st" :isfixed="false" @changetab="changetab"></dd-tab>
		
		<!-- 筛选条件 -->
		<view class="filter-container" v-if="st == 0">
			<view class="filter-row">
				<picker @change="onIncomeTypeChange" :value="incomeTypeIndex" :range="incomeTypeList" range-key="name">
					<view class="filter-item">
						<text>{{incomeTypeList[incomeTypeIndex].name}}</text>
						<text class="iconfont iconjiantou"></text>
					</view>
				</picker>
			</view>
			<view class="filter-row">
				<picker mode="date" @change="onStartDateChange" :value="startDate">
					<view class="filter-item">
						<text>开始日期：{{startDate || '请选择'}}</text>
					</view>
				</picker>
				<picker mode="date" @change="onEndDateChange" :value="endDate">
					<view class="filter-item">
						<text>结束日期：{{endDate || '请选择'}}</text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 收益明细 -->
			<block v-if="st == 0">
				<view v-for="(item, index) in datalist" :key="index" class="income-item">
					<view class="item-header">
						<text class="item-title">{{item.description || '订单佣金'}}</text>
						<text class="item-amount" :class="item.amount > 0 ? 'positive' : 'negative'">
							{{item.amount > 0 ? '+' : ''}}¥{{item.amount}}
						</text>
					</view>
					<view class="item-content">
						<view class="item-info">
							<text class="info-label">收益类型：</text>
							<text class="info-value">{{getIncomeTypeName(item.income_type)}}</text>
						</view>
						<view class="item-info" v-if="item.order_sn">
							<text class="info-label">订单号：</text>
							<text class="info-value">{{item.order_sn}}</text>
						</view>
						<view class="item-info" v-if="item.order_amount > 0">
							<text class="info-label">订单金额：</text>
							<text class="info-value">¥{{item.order_amount}}</text>
						</view>
						<view class="item-info">
							<text class="info-label">佣金比例：</text>
							<text class="info-value">{{item.commission_rate}}%</text>
						</view>
						<view class="item-info">
							<text class="info-label">获得时间：</text>
							<text class="info-value">{{item.createtime_format}}</text>
						</view>
					</view>
				</view>
			</block>

			<!-- 转换记录 -->
			<block v-if="st == 1">
				<view v-for="(item, index) in datalist" :key="index" class="withdraw-item">
					<view class="item-header">
						<text class="item-title">转换申请</text>
						<text class="item-amount">¥{{item.amount}}</text>
					</view>
					<view class="item-content">
						<view class="item-info">
							<text class="info-label">转换方式：</text>
							<text class="info-value">{{getWithdrawTypeName(item.withdraw_type)}}</text>
						</view>
						<view class="item-info">
							<text class="info-label">手续费：</text>
							<text class="info-value">¥{{item.fee}}</text>
						</view>
						<view class="item-info">
							<text class="info-label">实际到账：</text>
							<text class="info-value">¥{{item.actual_amount}}</text>
						</view>
						<view class="item-info">
							<text class="info-label">申请时间：</text>
							<text class="info-value">{{item.createtime_format}}</text>
						</view>
						<view class="item-info" v-if="item.remark">
							<text class="info-label">备注：</text>
							<text class="info-value">{{item.remark}}</text>
						</view>
					</view>
					<view class="item-status">
						<text class="status-text" :style="{color: item.status_color}">{{item.status_text}}</text>
					</view>
				</view>
			</block>
		</view>
	</block>
	
	<!-- 空状态 -->
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	
	<!-- 底部导航 -->
	<dp-tabbar :opt="opt"></dp-tabbar>
	
	<!-- 消息提示 -->
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			nodata: false,
			nomore: false,
			st: '0', // 当前标签页：0-收益明细，1-转换记录
			page: 1,
			limit: 20,
			datalist: [],
			statistics: {
				total_income: '0.00',
				today_income: '0.00',
				month_income: '0.00'
			},
			// 收益类型筛选
			incomeTypeIndex: 0,
			incomeTypeList: [
				{key: 'all', name: '全部类型'},
				{key: '1', name: '订单佣金'},
				{key: '2', name: '推荐奖励'},
				{key: '3', name: '平台奖励'},
				{key: '4', name: '其他收益'}
			],
			// 时间筛选
			startDate: '',
			endDate: ''
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = opt.st || '0';
		this.getdata();
	},

	onPullDownRefresh: function () {
		this.page = 1;
		this.getdata();
	},

	onReachBottom: function () {
		if (!this.nomore) {
			this.page++;
			this.getdata(true);
		}
	},

	methods: {
		// 获取数据
		getdata: function (isLoadMore = false) {
			var that = this;
			
			if (!isLoadMore) {
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
			}
			
			var params = {
				page: that.page,
				limit: that.limit
			};
			
			// 收益明细参数
			if (that.st == '0') {
				if (that.incomeTypeIndex > 0) {
					params.income_type = that.incomeTypeList[that.incomeTypeIndex].key;
				}
				if (that.startDate) {
					params.start_time = that.startDate;
				}
				if (that.endDate) {
					params.end_time = that.endDate;
				}
			}
			
			var apiUrl = that.st == '0' ? 'ApiCityAgent/getIncomeList' : 'ApiCityAgent/getWithdrawList';
			
			app.get(apiUrl, params, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 0) {
					app.error(res.msg);
					return;
				}
				
				// 设置导航标题
				uni.setNavigationBarTitle({
					title: that.st == '0' ? '收益明细' : '转换记录'
				});
				
				// 更新统计数据
				if (res.statistics) {
					that.statistics = res.statistics;
				}
				
				// 更新列表数据
				if (isLoadMore) {
					that.datalist = that.datalist.concat(res.list);
				} else {
					that.datalist = res.list;
				}
				
				// 检查是否还有更多数据
				if (res.list.length < that.limit) {
					that.nomore = true;
				}
				
				// 检查是否为空
				if (that.datalist.length === 0) {
					that.nodata = true;
				}
				
				that.loaded();
			});
		},
		
		// 切换标签页
		changetab: function (index) {
			this.st = index;
			this.page = 1;
			this.datalist = [];
			this.getdata();
		},
		
		// 收益类型选择
		onIncomeTypeChange: function (e) {
			this.incomeTypeIndex = e.detail.value;
			this.page = 1;
			this.getdata();
		},
		
		// 开始日期选择
		onStartDateChange: function (e) {
			this.startDate = e.detail.value;
			this.page = 1;
			this.getdata();
		},
		
		// 结束日期选择
		onEndDateChange: function (e) {
			this.endDate = e.detail.value;
			this.page = 1;
			this.getdata();
		},
		
		// 获取收益类型名称
		getIncomeTypeName: function (type) {
			// 如果是字符串类型且不是纯数字，直接返回
			if (typeof type === 'string' && isNaN(type)) {
				return type;
			}
			
			// 数字类型映射
			const types = {
				1: '订单佣金',
				2: '推荐奖励', 
				3: '平台奖励',
				4: '其他收益',
				'1': '订单佣金',
				'2': '推荐奖励',
				'3': '平台奖励',
				'4': '其他收益'
			};
			return types[type] || type || '未知';
		},
		
		// 获取转换方式名称
		getWithdrawTypeName: function (type) {
			const types = {
				'weixin': '微信钱包',
				'alipay': '支付宝',
				'bank': '银行卡',
				'commission': '余额转换'
			};
			return types[type] || '未知';
		},
		
		// 数据加载完成
		loaded: function () {
			this.isload = true;
		}
	}
};
</script>

<style>
.container {
	background: #f8f8f8;
	min-height: 100vh;
}

/* 统计卡片样式 */
.stats-card {
	display: flex;
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
	flex: 1;
	text-align: center;
}

.stats-number {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #4CAF50;
	margin-bottom: 10rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #666;
}

/* 筛选条件样式 */
.filter-container {
	background: white;
	margin: 0 20rpx 20rpx;
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.filter-row {
	display: flex;
	margin-bottom: 20rpx;
}

.filter-row:last-child {
	margin-bottom: 0;
}

.filter-item {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	padding: 0 20rpx;
	background: #f8f8f8;
	border-radius: 10rpx;
	margin-right: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
	color: #333;
}

.filter-item:last-child {
	margin-right: 0;
}

/* 内容区域样式 */
.content {
	padding: 0 20rpx;
}

/* 收益明细样式 */
.income-item {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.item-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.item-amount {
	font-size: 36rpx;
	font-weight: bold;
}

.item-amount.positive {
	color: #4CAF50;
}

.item-amount.negative {
	color: #F44336;
}

.item-content {
	
}

.item-info {
	display: flex;
	margin-bottom: 15rpx;
}

.item-info:last-child {
	margin-bottom: 0;
}

.info-label {
	width: 160rpx;
	font-size: 26rpx;
	color: #666;
}

.info-value {
	flex: 1;
	font-size: 26rpx;
	color: #333;
}

/* 转换记录样式 */
.withdraw-item {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.item-status {
	text-align: right;
	margin-top: 20rpx;
	padding-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
}

.status-text {
	font-size: 28rpx;
	font-weight: bold;
}
</style> 