# 进货退货系统前端API使用指南

## 概述

本文档提供了进货退货系统前端开发人员使用API接口的指南，包括请求方式、参数说明和示例代码。

## 基础信息

- **基础URL**: `/ApiAdminPurchase`
- **请求方式**: POST
- **数据格式**: JSON
- **公共参数**:
  - `aid`: 账号ID
  - `bid`: 商户ID
  - `session_id`: 会话ID

## 请求示例

### 使用jQuery发起请求

```javascript
$.ajax({
    url: '/api/ApiAdminPurchase/getPurchaseOrders',
    type: 'POST',
    data: {
        aid: 1,
        bid: 2,
        session_id: 'your_session_id',
        page: 1,
        limit: 10
    },
    dataType: 'json',
    success: function(res) {
        if(res.code === 0) {
            // 处理成功响应
            console.log(res.data.list);
        } else {
            // 处理错误
            alert(res.msg);
        }
    },
    error: function(xhr, status, error) {
        console.error('请求失败:', error);
    }
});
```

### 使用Axios发起请求

```javascript
axios.post('/api/ApiAdminPurchase/getPurchaseOrders', {
    aid: 1,
    bid: 2,
    session_id: 'your_session_id',
    page: 1,
    limit: 10
})
.then(function(response) {
    const res = response.data;
    if(res.code === 0) {
        // 处理成功响应
        console.log(res.data.list);
    } else {
        // 处理错误
        alert(res.msg);
    }
})
.catch(function(error) {
    console.error('请求失败:', error);
});
```

## 进货管理功能实现

### 1. 获取商品列表并创建进货订单

```javascript
// 1. 获取可进货商品列表
function getProductList(page = 1, limit = 10, name = '', cid = '') {
    return axios.post('/api/ApiAdminPurchase/getProductList', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        page: page,
        limit: limit,
        name: name,
        cid: cid
    });
}

// 2. 创建进货订单
function createPurchaseOrder(productIds, quantities, remark = '') {
    return axios.post('/api/ApiAdminPurchase/createPurchaseOrder', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        productIds: productIds,
        quantities: quantities,
        remark: remark
    });
}

// 使用示例
async function handleCreateOrder() {
    try {
        // 假设已经选择了商品和数量
        const productIds = [101, 102];
        const quantities = [2, 1];
        const remark = '测试进货单';
        
        const response = await createPurchaseOrder(productIds, quantities, remark);
        const res = response.data;
        
        if(res.code === 0) {
            alert('订单创建成功，订单ID：' + res.data.order_id);
            // 跳转到订单详情页
            window.location.href = '/product_purchase/detail?id=' + res.data.order_id;
        } else {
            alert('创建失败：' + res.msg);
        }
    } catch(error) {
        console.error('请求失败:', error);
        alert('网络错误，请稍后重试');
    }
}
```

### 2. 获取进货订单列表和详情

```javascript
// 1. 获取进货订单列表
function getPurchaseOrders(page = 1, limit = 10, status = '', orderNo = '') {
    return axios.post('/api/ApiAdminPurchase/getPurchaseOrders', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        page: page,
        limit: limit,
        status: status,
        order_no: orderNo
    });
}

// 2. 获取进货订单详情
function getPurchaseOrderDetail(id) {
    return axios.post('/api/ApiAdminPurchase/getPurchaseOrderDetail', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        id: id
    });
}

// 使用示例
async function loadOrderList() {
    try {
        const response = await getPurchaseOrders(1, 10);
        const res = response.data;
        
        if(res.code === 0) {
            // 渲染订单列表
            const orderList = res.data.list;
            let html = '';
            
            orderList.forEach(order => {
                html += `
                <tr>
                    <td>${order.id}</td>
                    <td>${order.order_no}</td>
                    <td>${order.total_price}</td>
                    <td>${getStatusText(order.status)}</td>
                    <td>${order.create_time}</td>
                    <td>
                        <a href="javascript:;" onclick="viewOrderDetail(${order.id})">查看</a>
                    </td>
                </tr>`;
            });
            
            $('#orderTableBody').html(html);
        } else {
            alert('获取订单列表失败：' + res.msg);
        }
    } catch(error) {
        console.error('请求失败:', error);
        alert('网络错误，请稍后重试');
    }
}

function getStatusText(status) {
    const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回',
        3: '已完成'
    };
    return statusMap[status] || '未知状态';
}

async function viewOrderDetail(id) {
    try {
        const response = await getPurchaseOrderDetail(id);
        const res = response.data;
        
        if(res.code === 0) {
            // 跳转到详情页或显示详情弹窗
            window.location.href = '/product_purchase/detail?id=' + id;
        } else {
            alert('获取订单详情失败：' + res.msg);
        }
    } catch(error) {
        console.error('请求失败:', error);
        alert('网络错误，请稍后重试');
    }
}
```

## 退货管理功能实现

### 1. 获取可退货的进货订单并创建退货单

```javascript
// 1. 获取可退货的进货订单列表
function getPurchaseOrdersForReturn(page = 1, limit = 10, orderNo = '') {
    return axios.post('/api/ApiAdminPurchase/getPurchaseOrdersForReturn', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        page: page,
        limit: limit,
        order_no: orderNo
    });
}

// 2. 获取进货订单中可退货的商品
function getPurchaseOrderItems(purchaseOrderId) {
    return axios.post('/api/ApiAdminPurchase/getPurchaseOrderItems', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        purchase_order_id: purchaseOrderId
    });
}

// 3. 创建退货订单
function createReturnOrder(purchaseOrderId, itemIds, quantities, remark) {
    return axios.post('/api/ApiAdminPurchase/createReturnOrder', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        purchase_order_id: purchaseOrderId,
        item_ids: itemIds,
        quantities: quantities,
        remark: remark
    });
}

// 使用示例
async function handleCreateReturnOrder() {
    try {
        // 假设已经选择了进货订单和要退货的商品
        const purchaseOrderId = 1;
        const itemIds = [1, 2];
        const quantities = [1, 1];
        const remark = '商品质量问题';
        
        const response = await createReturnOrder(purchaseOrderId, itemIds, quantities, remark);
        const res = response.data;
        
        if(res.code === 0) {
            alert('退货单创建成功，退货单ID：' + res.data.order_id);
            // 跳转到退货单详情页
            window.location.href = '/product_return/detail?id=' + res.data.order_id;
        } else {
            alert('创建失败：' + res.msg);
        }
    } catch(error) {
        console.error('请求失败:', error);
        alert('网络错误，请稍后重试');
    }
}
```

### 2. 获取退货订单列表和详情

```javascript
// 1. 获取退货订单列表
function getReturnOrders(page = 1, limit = 10, status = '', orderNo = '') {
    return axios.post('/api/ApiAdminPurchase/getReturnOrders', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        page: page,
        limit: limit,
        status: status,
        order_no: orderNo
    });
}

// 2. 获取退货订单详情
function getReturnOrderDetail(id) {
    return axios.post('/api/ApiAdminPurchase/getReturnOrderDetail', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        id: id
    });
}

// 使用示例与进货订单类似，可参考上面的示例代码
```

## 总平台审核功能实现

### 1. 审核进货订单

```javascript
function auditPurchaseOrder(id, status, auditRemark) {
    return axios.post('/api/ApiAdminPurchase/auditPurchaseOrder', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        id: id,
        status: status, // 1=通过，2=驳回
        audit_remark: auditRemark
    });
}

// 使用示例
async function handleAuditOrder(id) {
    try {
        const status = $('#auditStatus').val(); // 1=通过，2=驳回
        const auditRemark = $('#auditRemark').val();
        
        if(!auditRemark) {
            alert('请填写审核备注');
            return;
        }
        
        const response = await auditPurchaseOrder(id, status, auditRemark);
        const res = response.data;
        
        if(res.code === 0) {
            alert('审核成功');
            // 刷新页面或返回列表
            window.location.reload();
        } else {
            alert('审核失败：' + res.msg);
        }
    } catch(error) {
        console.error('请求失败:', error);
        alert('网络错误，请稍后重试');
    }
}
```

### 2. 审核退货订单

```javascript
function auditReturnOrder(id, status, auditRemark) {
    return axios.post('/api/ApiAdminPurchase/auditReturnOrder', {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        id: id,
        status: status, // 1=通过，2=驳回
        audit_remark: auditRemark
    });
}

// 使用示例与审核进货订单类似
```

## 错误处理

在使用API时，应当注意处理以下几种常见错误：

1. **登录失效**：当返回code为-4或-10时，表示用户未登录或登录已过期，应当跳转到登录页面。
2. **权限不足**：当返回code为1且msg包含"权限"相关信息时，表示用户没有操作权限。
3. **参数错误**：当返回code为1且msg包含"参数错误"时，检查传递的参数是否正确。
4. **余额不足**：创建进货订单时，如果商户余额不足，会返回相应的错误信息。

## 最佳实践

1. **统一处理请求和响应**：创建一个统一的请求函数，处理公共参数和错误。
2. **使用Promise或async/await**：使用现代JavaScript特性简化异步请求处理。
3. **添加加载状态**：在请求过程中显示加载状态，提升用户体验。
4. **缓存常用数据**：对不经常变化的数据进行缓存，减少请求次数。

```javascript
// 统一请求函数示例
function request(url, data = {}) {
    // 显示加载状态
    showLoading();
    
    // 添加公共参数
    const params = {
        aid: app.globalData.aid,
        bid: app.globalData.bid,
        session_id: app.globalData.sessionId,
        ...data
    };
    
    return axios.post(url, params)
        .then(response => {
            const res = response.data;
            
            // 隐藏加载状态
            hideLoading();
            
            // 处理登录失效
            if(res.code === -4 || res.code === -10) {
                // 跳转到登录页
                window.location.href = '/pagesB/login/login?frompage=' + encodeURIComponent(window.location.href);
                return Promise.reject(new Error('登录已失效，请重新登录'));
            }
            
            return res;
        })
        .catch(error => {
            // 隐藏加载状态
            hideLoading();
            console.error('请求失败:', error);
            return Promise.reject(error);
        });
}

// 使用示例
async function getOrderList() {
    try {
        const res = await request('/api/ApiAdminPurchase/getPurchaseOrders', {
            page: 1,
            limit: 10
        });
        
        if(res.code === 0) {
            // 处理成功响应
            renderOrderList(res.data.list);
        } else {
            // 处理业务错误
            showError(res.msg);
        }
    } catch(error) {
        // 已在request函数中处理了基本错误，这里处理其他异常
        showError('操作失败，请稍后重试');
    }
} 