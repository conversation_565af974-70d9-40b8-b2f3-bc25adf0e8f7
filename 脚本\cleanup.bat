@echo off
chcp 65001 >nul
echo ====================================
echo      HTML文件清理脚本
echo    删除范围：62.html - 130.html
echo ====================================
echo.

echo [警告] 此操作将删除所有生成的HTML文件！
echo.
set /p confirm=确认删除？(输入 Y 继续，其他键取消): 

if /i "%confirm%" neq "Y" (
    echo.
    echo [取消] 操作已取消。
    pause
    exit /b 0
)

echo.
echo [信息] 开始清理文件...
echo.

REM 统计要删除的文件数量
set count=0
for /L %%i in (62,1,130) do (
    if exist "%%i.html" set /a count+=1
)

if %count% equ 0 (
    echo [信息] 没有找到需要删除的文件。
    echo.
    pause
    exit /b 0
)

echo 找到 %count% 个文件需要删除...
echo.

REM 删除文件
set deleted=0
for /L %%i in (62,1,130) do (
    if exist "%%i.html" (
        echo 删除 %%i.html...
        del "%%i.html" >nul 2>&1
        if not exist "%%i.html" set /a deleted+=1
    )
)

echo.
echo ====================================
echo [完成] 已删除 %deleted% 个HTML文件
echo ====================================
echo.
pause 