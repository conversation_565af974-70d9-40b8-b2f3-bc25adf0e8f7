<template>
<view class="container">
	<view class="header">
		<text class="title">{{cityInfo.name || '城市详情'}}</text>
		<text class="subtitle">查看城市详细数据</text>
	</view>
	
	<view class="content">
		<!-- 城市基本信息 -->
		<view class="info-card">
			<view class="card-header">
				<image src="/static/img/icon-city.png" class="card-icon"></image>
				<text class="card-title">基本信息</text>
			</view>
			<view class="info-list">
				<view class="info-item">
					<text class="info-label">城市名称：</text>
					<text class="info-value">{{cityInfo.name}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">行政代码：</text>
					<text class="info-value">{{cityInfo.code}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">下辖区县：</text>
					<text class="info-value">{{cityInfo.district_count}}个</text>
				</view>
				<view class="info-item">
					<text class="info-label">代理开始：</text>
					<text class="info-value">{{formatTime(cityInfo.start_time)}}</text>
				</view>
			</view>
		</view>
		
		<!-- 业绩统计 -->
		<view class="stats-card">
			<view class="card-header">
				<image src="/static/img/icon-stats.png" class="card-icon"></image>
				<text class="card-title">业绩统计</text>
			</view>
			<view class="stats-grid">
				<view class="stats-item">
					<text class="stats-number">{{statistics.total_orders}}</text>
					<text class="stats-label">总订单数</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">￥{{statistics.total_amount}}</text>
					<text class="stats-label">总业绩</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">￥{{statistics.total_commission}}</text>
					<text class="stats-label">总佣金</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">{{statistics.month_orders}}</text>
					<text class="stats-label">本月订单</text>
				</view>
			</view>
		</view>
		
		<!-- 区县列表 -->
		<view class="district-card" v-if="districts.length > 0">
			<view class="card-header">
				<image src="/static/img/icon-district.png" class="card-icon"></image>
				<text class="card-title">下辖区县</text>
			</view>
			<view class="district-list">
				<view class="district-item" 
					v-for="district in districts" 
					:key="district.id"
					@tap="viewDistrictDetail(district)">
					<view class="district-info">
						<text class="district-name">{{district.name}}</text>
						<text class="district-stats">{{district.orders}}笔订单 | ￥{{district.revenue}}收益</text>
					</view>
					<image src="/static/img/arrow-right.png" class="arrow-icon"></image>
				</view>
			</view>
		</view>
		
		<!-- 月度趋势 -->
		<view class="trend-card">
			<view class="card-header">
				<image src="/static/img/icon-trend.png" class="card-icon"></image>
				<text class="card-title">月度趋势</text>
			</view>
			<view class="trend-chart">
				<view class="chart-item" v-for="item in monthlyTrend" :key="item.month">
					<view class="chart-bar">
						<view class="bar-fill" :style="{height: item.percentage + '%', background: getColor('color1') || '#4CAF50'}"></view>
					</view>
					<text class="chart-label">{{item.month}}</text>
				</view>
			</view>
			<view class="trend-legend">
				<view class="legend-item">
					<view class="legend-color" :style="{background: getColor('color1') || '#4CAF50'}"></view>
					<text class="legend-text">订单量</text>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-container">
			<button class="action-btn" @tap="viewOrders">查看订单</button>
			<button class="action-btn" @tap="viewIncome">查看收益</button>
		</view>
	</view>
	
	<!-- 加载状态 -->
	<loading v-if="loading"></loading>
	
	<!-- 消息提示 -->
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			city_id: 0,
			loading: false,
			cityInfo: {},
			statistics: {},
			districts: [],
			monthlyTrend: []
		};
	},
	
	onLoad(options) {
		this.city_id = options.city_id || 0;
		if (this.city_id) {
			this.loadCityDetail();
		}
	},
	
	methods: {
		// 安全获取颜色值
		getColor: function(colorKey) {
			try {
				if (typeof this.t === 'function') {
					return this.t(colorKey);
				}
				return null;
			} catch (e) {
				console.log('获取颜色失败:', e);
				return null;
			}
		},
		
		// 加载城市详情
		loadCityDetail() {
			var that = this;
			that.loading = true;
			
			app.get('ApiCityAgent/getCityDetail', {city_id: this.city_id}, function(res) {
				that.loading = false;
				
				if (res.status === 1) {
					that.cityInfo = res.cityInfo;
					that.statistics = res.statistics;
					that.districts = res.districts;
					that.monthlyTrend = res.monthlyTrend;
					
					// 设置导航标题
					uni.setNavigationBarTitle({
						title: res.cityInfo.name + ' - 详情'
					});
				} else {
					app.error(res.msg);
				}
			});
		},
		
		// 查看区县详情
		viewDistrictDetail(district) {
			app.goto('/pagesExt/cityagent/district-detail?district_id=' + district.id);
		},
		
		// 查看订单
		viewOrders() {
			app.goto('/pagesExt/cityagent/orders?city_id=' + this.city_id);
		},
		
		// 查看收益
		viewIncome() {
			app.goto('/pagesExt/cityagent/income?city_id=' + this.city_id);
		},
		
		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return '暂无';
			const date = new Date(timestamp * 1000);
			return date.toLocaleDateString();
		}
	}
};
</script>

<style>
.container {
	background: #f8f8f8;
	min-height: 100vh;
}

.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 30rpx;
	color: white;
	text-align: center;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 28rpx;
	opacity: 0.9;
}

.content {
	padding: 30rpx;
}

/* 卡片通用样式 */
.info-card, .stats-card, .district-card, .trend-card {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.card-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 15rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 信息列表样式 */
.info-list {
	padding: 30rpx;
}

.info-item {
	display: flex;
	margin-bottom: 20rpx;
}

.info-item:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

/* 统计样式 */
.stats-grid {
	display: flex;
	padding: 30rpx;
	flex-wrap: wrap;
}

.stats-item {
	width: 50%;
	text-align: center;
	margin-bottom: 30rpx;
}

.stats-number {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #4CAF50;
	margin-bottom: 10rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #666;
}

/* 区县列表样式 */
.district-list {
	padding: 0 30rpx;
}

.district-item {
	display: flex;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.district-item:last-child {
	border-bottom: none;
}

.district-info {
	flex: 1;
}

.district-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.district-stats {
	font-size: 24rpx;
	color: #666;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
}

/* 趋势图样式 */
.trend-chart {
	display: flex;
	align-items: flex-end;
	padding: 30rpx;
	height: 200rpx;
	justify-content: space-around;
}

.chart-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.chart-bar {
	width: 20rpx;
	height: 120rpx;
	background: #f0f0f0;
	border-radius: 10rpx;
	position: relative;
	margin-bottom: 10rpx;
	display: flex;
	align-items: flex-end;
}

.bar-fill {
	width: 100%;
	border-radius: 10rpx;
	transition: height 0.3s ease;
}

.chart-label {
	font-size: 20rpx;
	color: #666;
}

.trend-legend {
	padding: 0 30rpx 30rpx;
	display: flex;
	justify-content: center;
}

.legend-item {
	display: flex;
	align-items: center;
}

.legend-color {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	margin-right: 10rpx;
}

.legend-text {
	font-size: 24rpx;
	color: #666;
}

/* 操作按钮样式 */
.action-container {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	font-size: 28rpx;
	border-radius: 40rpx;
	border: none;
}
</style> 