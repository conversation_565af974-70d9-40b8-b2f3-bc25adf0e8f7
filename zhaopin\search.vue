<template>
  <view class="search-page">
    <!-- 搜索头部 -->
    <view class="search-header">
      <view class="search-bar">
        <input 
          type="text" 
          v-model="searchParams.keyword" 
          placeholder="搜索职位名称、公司名称" 
          class="search-input"
          @confirm="handleSearch"
        />
        <view class="search-btn" @tap="handleSearch" :style="'background:'+t('color1')">
          <text class="search-text">搜索</text>
        </view>
      </view>
      
      <!-- 筛选条件 -->
      <scroll-view scroll-x class="filter-scroll" show-scrollbar="false">
        <view class="filter-tags">
          <view 
            class="filter-tag" 
            :class="{ active: showFilterPanel }"
            @tap="toggleFilterPanel"
          >
            筛选
            <text class="iconfont icon-filter"></text>
          </view>
          <view 
            class="filter-tag" 
            :class="{ active: currentSort === 'time' }"
            @tap="handleSort('time')"
          >
            最新
          </view>
          <view 
            class="filter-tag" 
            :class="{ active: currentSort === 'salary' }"
            @tap="handleSort('salary')"
          >
            薪资
            <text class="sort-icon" :class="sortType === 'desc' ? 'desc' : ''"></text>
          </view>
          <view 
            class="filter-tag" 
            :class="{ active: currentSort === 'views' }"
            @tap="handleSort('views')"
          >
            热门
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选面板 -->
    <view class="filter-panel" v-if="showFilterPanel">
      <!-- 职位分类 -->
      <view class="filter-section">
        <view class="section-title">职位分类</view>
        <view class="tag-list">
          <view 
            v-for="(item, index) in categoryList" 
            :key="index"
            class="tag-item"
            :class="{ active: searchParams.category_id === item.id }"
            @tap="selectCategory(item)"
            :style="searchParams.category_id === item.id ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''"
          >
            {{ item.name }}
          </view>
        </view>
      </view>

      <!-- 职位类型 -->
      <view class="filter-section">
        <view class="section-title">职位类型</view>
        <view class="tag-list">
          <view 
            v-for="(item, index) in jobTypes" 
            :key="index"
            class="tag-item"
            :class="{ active: searchParams.type_id === item.id }"
            @tap="selectJobType(item)"
            :style="searchParams.type_id === item.id ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''"
          >
            {{ item.name }}
          </view>
        </view>
      </view>

      <!-- 学历要求 -->
      <view class="filter-section">
        <view class="section-title">学历要求</view>
        <view class="tag-list">
          <view 
            v-for="(item, index) in educationList" 
            :key="index"
            class="tag-item"
            :class="{ active: searchParams.education === item }"
            @tap="selectEducation(item)"
            :style="searchParams.education === item ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''"
          >
            {{ item }}
          </view>
        </view>
      </view>

      <!-- 工作经验 -->
      <view class="filter-section">
        <view class="section-title">工作经验</view>
        <view class="tag-list">
          <view 
            v-for="(item, index) in experienceList" 
            :key="index"
            class="tag-item"
            :class="{ active: searchParams.experience === item }"
            @tap="selectExperience(item)"
            :style="searchParams.experience === item ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''"
          >
            {{ item }}
          </view>
        </view>
      </view>

      <!-- 薪资范围 -->
      <view class="filter-section">
        <view class="section-title">薪资范围</view>
        <view class="tag-list">
          <view 
            v-for="(item, index) in salaryRanges" 
            :key="index"
            class="tag-item"
            :class="{ active: searchParams.salary_min === item.min && searchParams.salary_max === item.max }"
            @tap="selectSalary(item)"
            :style="searchParams.salary_min === item.min && searchParams.salary_max === item.max ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''"
          >
            {{ item.text }}
          </view>
        </view>
      </view>

      <!-- 地区选择 -->
      <view class="filter-section">
        <view class="section-title">地区选择</view>
        <view class="area-picker">
          <picker 
            mode="region" 
            @change="handleRegionChange" 
            :value="[searchParams.province, searchParams.city, searchParams.district]"
          >
            <view class="picker-item">
              {{ searchParams.province || '选择地区' }}
              {{ searchParams.city ? ' - ' + searchParams.city : '' }}
              {{ searchParams.district ? ' - ' + searchParams.district : '' }}
            </view>
          </picker>
        </view>
      </view>

      <!-- 福利标签 -->
      <view class="filter-section">
        <view class="section-title">福利标签</view>
        <view class="tag-list">
          <view 
            v-for="(item, index) in benefitsList" 
            :key="index"
            class="tag-item"
            :class="{ active: searchParams.benefits.includes(item) }"
            @tap="toggleBenefit(item)"
            :style="searchParams.benefits.includes(item) ? 'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')+';border-color:'+t('color1') : ''"
          >
            {{ item }}
          </view>
        </view>
      </view>

      <!-- 筛选按钮 -->
      <view class="filter-buttons">
        <button class="reset-btn" @tap="resetFilters">重置</button>
        <button 
          class="confirm-btn" 
          @tap="confirmFilters"
          :style="'background:'+t('color1')"
        >确定</button>
      </view>
    </view>

    <!-- 搜索结果统计 -->
    <view class="search-stats" v-if="statistics">
      <text class="stats-text">共找到 {{ statistics.total }} 个职位</text>
    </view>

    <!-- 职位列表 -->
    <scroll-view 
      scroll-y 
      class="job-list"
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view 
        v-for="(item, index) in jobList" 
        :key="index"
        class="job-item"
        @tap="goToDetail(item.id)"
      >
        <view class="job-header">
          <view class="job-title">{{ item.title }}</view>
          <view class="job-salary" :style="'color:'+t('color1')">{{ item.salary }}元/月</view>
        </view>
        
        <view class="job-info">
          <text class="info-item">
            <text class="iconfont icon-location"></text>
            {{ item.work_address }}
          </text>
          <text class="info-item">
            <text class="iconfont icon-education"></text>
            {{ item.education }}
          </text>
          <text class="info-item">
            <text class="iconfont icon-experience"></text>
            {{ item.experience }}
          </text>
        </view>
        
        <view class="company-info">
          <image class="company-logo" :src="item.company_logo" mode="aspectFill"></image>
          <view class="company-detail">
            <view class="company-name">{{ item.company_name }}</view>
            <view class="company-tags">
              <text class="tag" v-if="item.scale">{{ ['','20人以下','20-99人','100-499人','500-999人','1000-9999人','10000人以上'][item.scale] }}</text>
              <text class="tag" v-if="item.nature">{{ ['','民营企业','外资企业','合资企业','国企'][item.nature] }}</text>
            </view>
          </view>
        </view>
        
        <view class="work-info">
          <text class="work-tag">{{ item.work_mode }}</text>
          <text class="work-tag">{{ item.work_time_type }}</text>
          <text class="work-tag">{{ item.work_time_start.substring(0,5) }}-{{ item.work_time_end.substring(0,5) }}</text>
          <text class="work-tag" v-if="item.work_intensity">{{ item.work_intensity }}</text>
        </view>

        <view class="update-time">{{ item.update_time.substring(0,10) }} 更新</view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-status">
        <view v-if="isLoading" class="loading">加载中...</view>
        <view v-else-if="noMore" class="no-more">没有更多了</view>
      </view>
    </scroll-view>
    <nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      searchParams: {
        keyword: '',
        category_id: '',
        type_id: '',
        education: '',
        experience: '',
        salary_min: '',
        salary_max: '',
        province: '',
        city: '',
        district: '',
        benefits: [],
        sort_field: 'time',
        sort_type: 'desc',
        page: 1,
        limit: 10
      },
      showFilterPanel: false,
      currentSort: 'time',
      sortType: 'desc',
      jobList: [],
      statistics: null,
      isLoading: false,
      noMore: false,
      isRefreshing: false,
      
      // 筛选选项
      categoryList: [], // 职位分类列表
      jobTypes: [], // 职位类型列表
      educationList: ['不限', '高中', '专科', '本科', '硕士', '博士'],
      experienceList: ['不限', '应届生', '1-3年', '3-5年', '5-10年', '10年以上'],
      salaryRanges: [
        { min: '', max: '', text: '不限' },
        { min: 0, max: 5000, text: '5K以下' },
        { min: 5000, max: 10000, text: '5-10K' },
        { min: 10000, max: 15000, text: '10-15K' },
        { min: 15000, max: 20000, text: '15-20K' },
        { min: 20000, max: 30000, text: '20-30K' },
        { min: 30000, max: 50000, text: '30-50K' },
        { min: 50000, max: '', text: '50K以上' }
      ],
      benefitsList: ['五险一金', '年终奖', '加班补助', '餐补', '交通补助', '住房补贴', '节日福利', '团建活动']
    }
  },
  computed: {
    topSalaryRange() {
      if (!this.statistics?.salary_distribution) return '';
      const distributions = Object.entries(this.statistics.salary_distribution);
      return distributions.sort((a, b) => b[1] - a[1])[0][0];
    },
    topSalaryPercentage() {
      if (!this.statistics?.salary_distribution) return 0;
      const distributions = Object.values(this.statistics.salary_distribution);
      const total = distributions.reduce((sum, val) => sum + val, 0);
      const max = Math.max(...distributions);
      return Math.round((max / total) * 100);
    },
    topExperience() {
      if (!this.statistics?.experience_distribution) return '';
      const distributions = Object.entries(this.statistics.experience_distribution);
      return distributions.sort((a, b) => b[1] - a[1])[0][0];
    },
    topExperiencePercentage() {
      if (!this.statistics?.experience_distribution) return 0;
      const distributions = Object.values(this.statistics.experience_distribution);
      const total = distributions.reduce((sum, val) => sum + val, 0);
      const max = Math.max(...distributions);
      return Math.round((max / total) * 100);
    }
  },
  onLoad(options) {
    // 从URL参数中获取关键词
    if (options.keyword) {
      this.searchParams.keyword = decodeURIComponent(options.keyword);
    }
    this.getInitialData();
    this.searchJobs();
  },
  methods: {
    // 获取初始数据
    getInitialData() {
      var that = this;
      app.get('ApiZhaopin/getSearchInitData', {}, function(res) {
        if (res.status === 1) {
          that.categoryList = res.data.categories;
          that.jobTypes = res.data.job_types;
        }
      });
    },
    
    // 搜索职位
    searchJobs(isLoadMore = false) {
      if (this.isLoading) return;
      
      var that = this;
      this.isLoading = true;
      
      if (!isLoadMore) {
        this.searchParams.page = 1;
        this.jobList = [];
        this.noMore = false;
      }
      
      app.post('ApiZhaopin/searchPositions', this.searchParams, function(res) {
        that.isLoading = false;
        that.isRefreshing = false;
        
        if (res.status === 1) {
          if (isLoadMore) {
            that.jobList = [...that.jobList, ...res.data.list];
          } else {
            that.jobList = res.data.list;
          }
          
          that.statistics = res.data.statistics;
          that.noMore = res.data.list.length < that.searchParams.limit;
        } else {
          app.error(res.msg);
        }
      });
    },
    
    // 处理搜索
    handleSearch() {
      this.showFilterPanel = false;
      this.searchJobs();
    },
    
    // 处理排序
    handleSort(type) {
      if (this.currentSort === type) {
        this.sortType = this.sortType === 'desc' ? 'asc' : 'desc';
      } else {
        this.currentSort = type;
        this.sortType = 'desc';
      }
      
      this.searchParams.sort_field = type;
      this.searchParams.sort_type = this.sortType;
      this.searchJobs();
    },
    
    // 切换筛选面板
    toggleFilterPanel() {
      this.showFilterPanel = !this.showFilterPanel;
    },
    
    // 选择分类
    selectCategory(category) {
      this.searchParams.category_id = this.searchParams.category_id === category.id ? '' : category.id;
    },
    
    // 选择职位类型
    selectJobType(type) {
      this.searchParams.type_id = this.searchParams.type_id === type.id ? '' : type.id;
    },
    
    // 选择学历
    selectEducation(education) {
      this.searchParams.education = this.searchParams.education === education ? '' : education;
    },
    
    // 选择经验
    selectExperience(experience) {
      this.searchParams.experience = this.searchParams.experience === experience ? '' : experience;
    },
    
    // 选择薪资范围
    selectSalary(range) {
      if (this.searchParams.salary_min === range.min && this.searchParams.salary_max === range.max) {
        this.searchParams.salary_min = '';
        this.searchParams.salary_max = '';
      } else {
        this.searchParams.salary_min = range.min;
        this.searchParams.salary_max = range.max;
      }
    },
    
    // 处理地区选择
    handleRegionChange(e) {
      const [province, city, district] = e.detail.value;
      this.searchParams.province = province;
      this.searchParams.city = city;
      this.searchParams.district = district;
    },
    
    // 切换福利标签
    toggleBenefit(benefit) {
      const index = this.searchParams.benefits.indexOf(benefit);
      if (index > -1) {
        this.searchParams.benefits.splice(index, 1);
      } else {
        this.searchParams.benefits.push(benefit);
      }
    },
    
    // 重置筛选
    resetFilters() {
      this.searchParams = {
        ...this.searchParams,
        category_id: '',
        type_id: '',
        education: '',
        experience: '',
        salary_min: '',
        salary_max: '',
        province: '',
        city: '',
        district: '',
        benefits: []
      };
    },
    
    // 确认筛选
    confirmFilters() {
      this.showFilterPanel = false;
      this.searchJobs();
    },
    
    // 加载更多
    loadMore() {
      if (this.isLoading || this.noMore) return;
      this.searchParams.page++;
      this.searchJobs(true);
    },
    
    // 下拉刷新
    onRefresh() {
      this.isRefreshing = true;
      this.searchJobs();
    },
    
    // 跳转到详情页
    goToDetail(id) {
      app.goto('/zhaopin/partdetails?id=' + id);
    }
  }
}
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-top: 240rpx;
  
  .search-header {
    position: fixed;
    top: var(--window-top, 0);
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(to bottom, #ffffff, rgba(255,255,255,0.98));
    padding: 24rpx 30rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    
    .search-bar {
      display: flex;
      align-items: center;
      background-color: #ffffff;
      border-radius: 36rpx;
      padding: 0 20rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
      border: 2rpx solid #f0f0f0;
      
      .search-input {
        flex: 1;
        height: 72rpx;
        font-size: 30rpx;
        color: #333;
        letter-spacing: 0.5px;
        
        &::placeholder {
          color: #999;
          font-weight: 300;
        }
      }
      
      .search-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      }
      
      .search-btn:active {
        opacity: 0.8;
        transform: scale(0.95);
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
      }
      
      .search-text {
        color: #FFFFFF;
        font-size: 28rpx;
        font-weight: 500;
        line-height: 1;
      }
    }
    
    .filter-scroll {
      white-space: nowrap;
      
      .filter-tags {
        display: inline-flex;
        padding: 12rpx 0;
        
        .filter-tag {
          display: inline-flex;
          align-items: center;
          height: 72rpx;
          padding: 0 28rpx;
          margin-right: 20rpx;
          font-size: 28rpx;
          color: #666;
          background-color: #ffffff;
          border-radius: 36rpx;
          transition: all 0.3s ease;
          font-weight: 500;
          letter-spacing: 0.5px;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
          
          &:active {
            transform: scale(0.95);
            opacity: 0.8;
          }
          
          &.active {
            color: #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
            font-weight: 600;
            transform: translateY(-2rpx);
            box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.15);
          }
          
          .sort-icon {
            width: 0;
            height: 0;
            margin-left: 10rpx;
            border-left: 8rpx solid transparent;
            border-right: 8rpx solid transparent;
            border-bottom: 10rpx solid #999;
            transition: transform 0.3s ease;
            
            &.desc {
              transform: rotate(180deg);
            }
          }
          
          .icon-filter {
            font-size: 28rpx;
            margin-left: 6rpx;
          }
        }
      }
    }
  }
  
  .filter-panel {
    position: fixed;
    top: 240rpx;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: #ffffff;
    padding: 40rpx 30rpx calc(env(safe-area-inset-bottom) + 120rpx);
    overflow-y: auto;
    animation: slideIn 0.3s ease;
    
    .filter-section {
      margin-bottom: 40rpx;
      
      &:first-child {
        padding-top: 20rpx;
      }
      
      .section-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 28rpx;
        display: flex;
        align-items: center;
        
        &::before {
          content: '';
          width: 6rpx;
          height: 32rpx;
          background: linear-gradient(to bottom, #1890ff, #36b4ff);
          border-radius: 3rpx;
          margin-right: 12rpx;
        }
      }
      
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -8rpx;
        
        .tag-item {
          height: 72rpx;
          padding: 0 28rpx;
          margin: 8rpx;
          font-size: 28rpx;
          color: #666;
          background-color: #f7f8fa;
          border: 2rpx solid transparent;
          border-radius: 36rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.25s ease;
          letter-spacing: 0.5px;
          
          &:active {
            transform: scale(0.95);
            opacity: 0.8;
          }
          
          &.active {
            color: #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
            border-color: rgba(24, 144, 255, 0.3);
            font-weight: 500;
            transform: translateY(-2rpx);
            box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.15);
          }
        }
      }
      
      .area-picker {
        .picker-item {
          height: 88rpx;
          line-height: 88rpx;
          padding: 0 30rpx;
          background-color: #f7f8fa;
          border-radius: 16rpx;
          font-size: 28rpx;
          color: #333;
          position: relative;
          
          &::after {
            content: '';
            position: absolute;
            right: 30rpx;
            top: 50%;
            width: 12rpx;
            height: 12rpx;
            border-right: 2rpx solid #999;
            border-bottom: 2rpx solid #999;
            transform: translateY(-50%) rotate(45deg);
          }
        }
      }
    }
    
    .filter-buttons {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
      background-color: #ffffff;
      box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
      display: flex;
      gap: 20rpx;
      z-index: 100;
      
      button {
        flex: 1;
        height: 92rpx;
        line-height: 92rpx;
        font-size: 32rpx;
        border-radius: 46rpx;
        transition: all 0.3s ease;
        letter-spacing: 1px;
        
        &:active {
          transform: scale(0.98);
          opacity: 0.9;
        }
        
        &.reset-btn {
          background-color: #f7f8fa;
          color: #666;
          border: 2rpx solid #e5e5e5;
          
          &:active {
            background-color: #f0f0f0;
          }
        }
        
        &.confirm-btn {
          background: linear-gradient(135deg, #1890ff, #36b4ff);
          color: #ffffff;
          font-weight: 500;
          box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.25);
          
          &:active {
            box-shadow: 0 2rpx 6rpx rgba(24, 144, 255, 0.2);
          }
        }
      }
    }
  }
  
  .search-stats {
    background-color: #ffffff;
    padding: 16rpx 30rpx;
    margin: 0 20rpx 20rpx;
    border-radius: 12rpx;
    
    .stats-text {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .job-list {
    height: calc(100vh - 240rpx);
    padding: 20rpx;
    
    .job-item {
      background: linear-gradient(to bottom, #ffffff, #fafafa);
      border-radius: 20rpx;
      padding: 28rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
      position: relative;
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      }
      
      .job-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;
        
        .job-title {
          font-size: 34rpx;
          font-weight: 600;
          color: #333;
          background: linear-gradient(90deg, #333 60%, #666);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        
        .job-salary {
          font-size: 32rpx;
          font-weight: 600;
          text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.1);
        }
      }
      
      .job-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
        margin-bottom: 20rpx;
        
        .info-item {
          font-size: 26rpx;
          color: #666;
          display: flex;
          align-items: center;
          padding: 4rpx 16rpx;
          background: #f8f9fa;
          border-radius: 6rpx;
          
          .iconfont {
            font-size: 26rpx;
            margin-right: 6rpx;
            color: #999;
          }
        }
      }
      
      .company-info {
        display: flex;
        align-items: center;
        margin: 24rpx 0;
        padding-bottom: 24rpx;
        border-bottom: 2rpx solid #f5f5f5;
        
        .company-logo {
          width: 88rpx;
          height: 88rpx;
          border-radius: 12rpx;
          margin-right: 16rpx;
          border: 2rpx solid #f0f0f0;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        }
        
        .company-detail {
          flex: 1;
          
          .company-name {
            font-size: 30rpx;
            color: #333;
            margin-bottom: 8rpx;
            font-weight: 500;
          }
          
          .company-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8rpx;
            
            .tag {
              font-size: 24rpx;
              color: #666;
              background: #f8f9fa;
              padding: 4rpx 16rpx;
              border-radius: 6rpx;
            }
          }
        }
      }
      
      .work-info {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        margin-bottom: 24rpx;
        
        .work-tag {
          font-size: 24rpx;
          color: #1890ff;
          background: rgba(24, 144, 255, 0.06);
          padding: 8rpx 20rpx;
          border-radius: 8rpx;
        }
      }
      
      .update-time {
        position: absolute;
        right: 28rpx;
        bottom: 28rpx;
        font-size: 22rpx;
        color: #999;
      }
    }
    
    .loading-status {
      padding: 40rpx 30rpx;
      text-align: center;
      color: #999;
      font-size: 26rpx;
      letter-spacing: 1px;
      
      .loading {
        &::before {
          content: '';
          display: inline-block;
          width: 28rpx;
          height: 28rpx;
          border: 3rpx solid #f0f0f0;
          border-top-color: #666;
          border-radius: 50%;
          margin-right: 8rpx;
          vertical-align: middle;
          animation: loading 0.6s linear infinite;
        }
      }
      
      .no-more {
        display: flex;
        align-items: center;
        justify-content: center;
        
        &::before,
        &::after {
          content: '';
          flex: 1;
          height: 1rpx;
          background: linear-gradient(to right, transparent, #eee, transparent);
          margin: 0 20rpx;
        }
      }
    }
  }
}

@keyframes loading {
  from { transform: rotate(0); }
  to { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
</rewritten_file>