<template>
	<view class="cropper-wrapper" :style="{ height: windowHeight + 'px' }">
		<!-- 工具栏 -->
		<view class="cropper-buttons">
			<view class="btn cancel" @tap="cancel">取消</view>
			<view class="btn confirm" @tap="confirm">确定</view>
		</view>
		<!-- 裁剪框 -->
		<view class="cropper-main">
			<canvas class="cropper-canvas" :style="{ width: width + 'px', height: height + 'px', backgroundColor: 'rgba(0, 0, 0, 0.8)' }" canvas-id="cropper" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd"></canvas>
		</view>
	</view>
</template>

<script>
// #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-QQ
import cropper from './qf-image-cropper.render.js';
// #endif

// #ifdef MP-ALIPAY || MP-BAIDU || MP-TOUTIAO
import cropper from './qf-image-cropper.render.js';
// #endif

export default {
	name:"qf-image-cropper",
	props: {
		width: {
			type: Number,
			default: 300
		},
		height: {
			type: Number,
			default: 300
		},
		radius: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			windowHeight: 0,
			imageInfo: {
				path: '',
				width: 0,
				height: 0
			},
			cropperData: {
				left: 0,
				top: 0,
				scale: 1,
				angle: 0
			},
			startTouch: {
				x: 0,
				y: 0,
				scale: 1,
				angle: 0
			}
		}
	},
	mounted() {
		this.windowHeight = uni.getSystemInfoSync().windowHeight;
		this.chooseImage();
	},
	methods: {
		chooseImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					uni.getImageInfo({
						src: res.tempFilePaths[0],
						success: (image) => {
							this.imageInfo = image;
							this.cropperData.left = (this.width - image.width) / 2;
							this.cropperData.top = (this.height - image.height) / 2;
							this.draw();
						}
					});
				}
			});
		},
		touchStart(e) {
			const touch = e.touches[0];
			this.startTouch = {
				x: touch.x,
				y: touch.y,
				scale: this.cropperData.scale,
				angle: this.cropperData.angle
			};
		},
		touchMove(e) {
			const touch = e.touches[0];
			const deltaX = touch.x - this.startTouch.x;
			const deltaY = touch.y - this.startTouch.y;
			
			this.cropperData.left += deltaX;
			this.cropperData.top += deltaY;
			
			this.startTouch.x = touch.x;
			this.startTouch.y = touch.y;
			
			this.draw();
		},
		touchEnd() {
			this.draw();
		},
		draw() {
			const ctx = uni.createCanvasContext('cropper', this);
			
			// 清空画布
			ctx.clearRect(0, 0, this.width, this.height);
			
			// 绘制图片
			ctx.save();
			ctx.translate(this.cropperData.left, this.cropperData.top);
			ctx.scale(this.cropperData.scale, this.cropperData.scale);
			ctx.rotate(this.cropperData.angle * Math.PI / 180);
			ctx.drawImage(this.imageInfo.path, 0, 0, this.imageInfo.width, this.imageInfo.height);
			ctx.restore();
			
			ctx.draw();
		},
		cancel() {
			this.$emit('cancel');
		},
		confirm() {
			uni.canvasToTempFilePath({
				canvasId: 'cropper',
				x: 0,
				y: 0,
				width: this.width,
				height: this.height,
				success: (res) => {
					this.$emit('crop', {
						tempFilePath: res.tempFilePath,
						cropperData: this.cropperData
					});
				}
			}, this);
		}
	}
}
</script>

<style>
.cropper-wrapper {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background-color: #000;
	z-index: 1001;
}

.cropper-buttons {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	height: 50px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20px;
	background-color: #000;
}

.cropper-buttons .btn {
	width: 80px;
	height: 35px;
	line-height: 35px;
	text-align: center;
	color: #fff;
	border-radius: 2px;
}

.cropper-buttons .cancel {
	background-color: #666;
}

.cropper-buttons .confirm {
	background-color: #07c160;
}

.cropper-main {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 50px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.cropper-canvas {
	position: relative;
}
</style> 