<template>
	<view class="container">
		<block v-if="isload">
			<!-- 余额统计头部 -->
			<view class="header-stats">
				<view class="stats-container">
					<view class="stats-item">
						<text class="stats-amount">{{activeTab === 'income' ? formatHighPrecisionAmount(currentBalance) : smartFormatAmount(currentBalance)}}</text>
						<text class="stats-label">可用{{t('消费券')}}</text>
					</view>
					<view class="stats-item">
						<text class="stats-amount">{{activeTab === 'income' ? formatHighPrecisionAmount(totalCount) : smartFormatAmount(totalCount)}}</text>
						<text class="stats-label" v-if="activeTab === 'income'">累计补贴</text>
						<text class="stats-label" v-else>累计收入</text>
					</view>
				</view>
			</view>

			<!-- Tab切换区域 -->
			<view class="tab-section">
				<view class="tab-item" :class="activeTab === 'income' ? 'active' : ''" @tap="switchTab('income')">
					<text class="tab-text">收入明细</text>
					<view class="tab-underline" v-if="activeTab === 'income'"></view>
				</view>
				<view class="tab-item" :class="activeTab === 'change' ? 'active' : ''" @tap="switchTab('change')">
					<text class="tab-text">变动明细</text>
					<view class="tab-underline" v-if="activeTab === 'change'"></view>
				</view>
			</view>
			
			<!-- 日期选择器 -->
			<view class="date-selector" @tap="showDatePicker">
				<text class="date-text">{{currentDate}}</text>
				<image class="calendar-icon" src="/static/img/calendar.png" mode="aspectFit"></image>
			</view>
			
			<!-- 表格头部 -->
			<view class="table-header" v-if="activeTab === 'income'">
				<text class="header-date">结算日期</text>
				<text class="header-count">参与订单数</text>
				<text class="header-amount">收入{{t('消费券')}}</text>
			</view>

			<!-- 收入明细列表 -->
			<view class="table-content" v-if="activeTab === 'income'">
				<view v-for="(item, index) in incomeList" :key="index" class="table-row">
					<text class="row-date">{{item.settle_date}}</text>
					<text class="row-count">{{item.order_count}}</text>
					<text class="row-amount">{{formatHighPrecisionAmount(item.total_amount)}}</text>
				</view>

				<!-- 空状态 -->
				<view v-if="incomeList.length === 0" class="empty-state">
					<text class="empty-text">没有更多了</text>
				</view>
			</view>
			
			<!-- 变动明细列表 -->
			<view class="change-list" v-if="activeTab === 'change'">
				<view v-for="(item, index) in datalist" :key="index" class="change-item">
					<view class="change-info">
						<text class="change-title">{{item.remark}}</text>
						<text class="change-time">{{item.createtime}}</text>
						<text class="change-balance">变更后{{t('消费券')}}: {{smartFormatAmount(item.after)}}</text>
					</view>
					<view class="change-amount" :class="item.money > 0 ? 'amount-positive' : 'amount-negative'">
						<text class="amount-symbol">{{item.money > 0 ? '+' : ''}}</text>
						<text class="amount-value">{{smartFormatAmount(item.money)}}</text>
					</view>
				</view>
				
				<!-- 空状态 -->
				<view v-if="datalist.length === 0" class="empty-state">
					<text class="empty-text">没有更多了</text>
				</view>
			</view>
			
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
		</block>
		
		<!-- 日期选择器弹窗 -->
		<view class="date-picker-mask" v-if="showDatePickerModal" @tap="hideDatePicker">
			<view class="date-picker-content" @tap.stop>
				<view class="picker-header">
					<text class="picker-cancel" @tap="hideDatePicker">取消</text>
					<text class="picker-title">选择月份</text>
					<text class="picker-confirm" @tap="confirmDatePicker">确定</text>
				</view>
				<picker-view class="picker-view" :value="pickerValue" @change="onPickerChange">
					<picker-view-column>
						<view v-for="(year, index) in years" :key="index" class="picker-item">{{year}}</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(month, index) in months" :key="index" class="picker-item">{{month}}</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
		
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		inject: ['reload'], // 注入 reload 方法
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				
				// Tab状态
				activeTab: 'income', // 'income' 或 'change'

				// 余额数据
				datalist: [],
				incomeList: [],
				pagenum: 1,
				nodata: false,
				nomore: false,

				// 统计数据
				currentBalance: '0.00', // 当前余额
				totalCount: '0.00', // 累计收入
				
				// 日期选择器
				currentDate: '2025-07',
				showDatePickerModal: false,
				pickerValue: [0, 6], // 年份索引，月份索引
				years: [],
				months: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.initCurrentDate();
			this.initDatePicker();
			this.getdata();
		},
		
		onPullDownRefresh: function() {
			this.getdata(true);
		},
		
		onReachBottom: function() {
			if (!this.nodata && !this.nomore) {
				this.pagenum = this.pagenum + 1;
				this.getdata(true);
			}
		},
		
		methods: {
			// 初始化当前日期
			initCurrentDate() {
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0'); // getMonth()返回0-11，所以要+1
				this.currentDate = `${year}-${month}`;
			},
			
			// 初始化日期选择器数据
			initDatePicker() {
				const currentYear = new Date().getFullYear();
				const currentMonth = new Date().getMonth(); // 0-11
				
				this.years = [];
				// 生成最近5年的年份选项
				for (let i = currentYear - 2; i <= currentYear + 2; i++) {
					this.years.push(i.toString());
				}
				
				// 设置默认选中值，与当前日期同步
				const yearIndex = this.years.indexOf(currentYear.toString());
				this.pickerValue = [yearIndex, currentMonth]; // currentMonth已经是0-11，直接使用
			},
			
			// 切换Tab
			switchTab(tab) {
				if (this.activeTab !== tab) {
					this.activeTab = tab;
					this.pagenum = 1;
					this.getdata();
				}
			},
			
			// 显示日期选择器
			showDatePicker() {
				this.showDatePickerModal = true;
			},
			
			// 隐藏日期选择器
			hideDatePicker() {
				this.showDatePickerModal = false;
			},
			
			// 日期选择器值改变
			onPickerChange(e) {
				this.pickerValue = e.detail.value;
			},
			
			// 确认选择日期
			confirmDatePicker() {
				const yearIndex = this.pickerValue[0];
				const monthIndex = this.pickerValue[1];
				const selectedYear = this.years[yearIndex];
				const selectedMonth = this.months[monthIndex]; // months数组是['01','02'...]，索引已正确
				
				this.currentDate = `${selectedYear}-${selectedMonth}`;
				this.hideDatePicker();
				
				// 重新获取数据
				this.pagenum = 1;
				this.getdata();
			},
			
			// 获取数据
			getdata: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
					this.incomeList = [];
				}

				var that = this;
				var pagenum = that.pagenum;

				that.nodata = false;
				that.nomore = false;
				that.loading = true;

				// 根据Tab类型调用不同接口
				if (that.activeTab === 'income') {
					// 收入明细：调用新的分红收入接口
					app.post('ApiMoney/getIncomeDetail', {
						pagenum: pagenum,
						date: that.currentDate // 传递选择的日期
					}, function(res) {
						that.loading = false;
						
						if (res.code == 1) {
							var data = res.data;
							
							if (pagenum == 1) {
								// 设置页面标题
								uni.setNavigationBarTitle({
									title: that.t('消费券')
								});

								that.incomeList = data;

								if (data.length == 0) {
									that.nodata = true;
								}

								// 使用返回的统计数据
								if (res.stats) {
									that.currentBalance = res.stats.current_balance;
									that.totalCount = res.stats.total_income;
								}
								
								that.loaded();
							} else {
								if (data.length == 0) {
									that.nomore = true;
								} else {
									var incomeList = that.incomeList;
									var newlist = incomeList.concat(data);
									that.incomeList = newlist;
								}
							}
						} else {
							that.loaded();
							if (pagenum == 1) {
								that.nodata = true;
							}
						}
					});
				} else {
					// 变动明细：调用原有的moneylog接口
					var st = 0; // 余额明细的st值为0
					
					app.post('ApiMy/moneylog', {
						st: st,
						pagenum: pagenum,
						date: that.currentDate // 传递选择的日期
					}, function(res) {
						that.loading = false;
						var data = res.data;

						if (pagenum == 1) {
							// 设置页面标题
							uni.setNavigationBarTitle({
								title: that.t('消费券')
							});

							that.datalist = data;

							if (data.length == 0) {
								that.nodata = true;
							}

							// 计算统计数据
							that.calculateStats(data);
							that.loaded();
						} else {
							if (data.length == 0) {
								that.nomore = true;
							} else {
								var datalist = that.datalist;
								var newdata = datalist.concat(data);
								that.datalist = newdata;
							}
						}
					});
				}
			},
			

			
			// 计算统计数据
			calculateStats(data) {
				let totalIncomeCount = 0; // 只计算正数收入的累计

				// 累计收入：只计算正数金额
				data.forEach(item => {
					if (item.money > 0) {
						totalIncomeCount += parseFloat(item.money);
					}
				});

				// 当前余额：使用最新记录的after字段（最准确的当前余额）
				if (data.length > 0) {
					this.currentBalance = parseFloat(data[0].after || 0).toFixed(2);
				} else {
					this.currentBalance = '0.00';
				}

				this.totalCount = totalIncomeCount.toFixed(2); // 累计收入只显示正数总和
			},
			
			// 智能格式化金额显示：如果后两位是00则不显示
			formatAmount(amount) {
				const num = parseFloat(amount);
				// 检查是否为整数或只有1-2位有效小数
				if (num % 1 === 0) {
					// 整数，显示2位小数
					return num.toFixed(2);
				} else if ((num * 100) % 1 === 0) {
					// 2位小数，直接显示
					return num.toFixed(2);
				} else {
					// 超过2位小数，显示所有小数位但去掉末尾的0
					return parseFloat(num.toFixed(5)).toString();
				}
			},
			
			// 智能格式化金额：去掉末尾无意义的0，但至少保留2位小数
			smartFormatAmount(amount) {
				if (!amount && amount !== 0) return '0.00';
				
				const num = parseFloat(amount);
				if (isNaN(num)) return '0.00';
				
				// 首先格式化到5位小数
				let formatted = num.toFixed(5);
				
				// 去掉末尾的0，但至少保留2位小数
				// 例如：0.45000 -> 0.45, 0.45600 -> 0.456, 1.00000 -> 1.00
				
				// 分离整数和小数部分
				const parts = formatted.split('.');
				if (parts.length === 1) {
					// 没有小数部分，添加.00
					return parts[0] + '.00';
				}
				
				let decimalPart = parts[1];
				// 从右边去掉0，但至少保留2位
				while (decimalPart.length > 2 && decimalPart.charAt(decimalPart.length - 1) === '0') {
					decimalPart = decimalPart.slice(0, -1);
				}
				
				return parts[0] + '.' + decimalPart;
			},
			
			// 高精度格式化金额：专用于收入明细，智能显示小数位
			formatHighPrecisionAmount(amount) {
				if (!amount && amount !== 0) return '0.00';
				
				const num = parseFloat(amount);
				if (isNaN(num)) return '0.00';
				
				// 先格式化到5位小数
				let formatted = num.toFixed(5);
				
				// 智能去掉末尾的0，但至少保留2位小数
				// 例如：6.23000 -> 6.23, 6.23400 -> 6.234, 6.00000 -> 6.00
				const parts = formatted.split('.');
				if (parts.length === 1) {
					// 没有小数部分，添加.00
					return parts[0] + '.00';
				}
				
				let decimalPart = parts[1];
				// 从右边去掉0，但至少保留2位
				while (decimalPart.length > 2 && decimalPart.charAt(decimalPart.length - 1) === '0') {
					decimalPart = decimalPart.slice(0, -1);
				}
				
				return parts[0] + '.' + decimalPart;
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
		background: linear-gradient(180deg, #6C7CE7 0%, #F5F6FA 40%);
		min-height: 100vh;
		padding-bottom: 20rpx;
	}

	/* 头部余额统计区域 */
	.header-stats {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60rpx 40rpx 40rpx;
		background: transparent;
	}

	.stats-container {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 20rpx;
		padding: 30rpx 40rpx;
		margin: 0 20rpx;
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: 100%;
		box-shadow: 0 4px 12px #f8f8f8;
	}

	.stats-item {
		flex: 1;
		text-align: center;
	}

	.stats-amount {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: #6C7CE7;
		line-height: 1.2;
	}

	.stats-label {
		display: block;
		font-size: 24rpx;
		color: #666;
		margin-top: 10rpx;
	}

	/* Tab切换区域 */
	.tab-section {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 40rpx 20rpx;
	}

	.tab-item {
		position: relative;
	}

	.tab-text {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.8);
		font-weight: 500;
	}

	.tab-item.active .tab-text {
		color: #fff;
		font-weight: bold;
	}

	.tab-underline {
		position: absolute;
		bottom: -10rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background: #fff;
		border-radius: 2rpx;
	}

	/* 日期选择器 */
	.date-selector {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0 40rpx 30rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		padding: 16rpx 32rpx;
		cursor: pointer;
	}

	.date-text {
		font-size: 28rpx;
		color: #fff;
		margin-right: 16rpx;
	}

	.calendar-icon {
		width: 32rpx;
		height: 32rpx;
	}

	/* 表格头部 */
	.table-header {
		display: flex;
		align-items: center;
		background: rgba(255, 255, 255, 0.1);
		padding: 20rpx 40rpx;
		margin: 0 40rpx;
		border-radius: 8rpx 8rpx 0 0;
	}

	.header-date,
	.header-count,
	.header-amount {
		flex: 1;
		text-align: center;
		font-size: 26rpx;
		color: #fff;
		font-weight: 500;
	}

	/* 表格内容 */
	.table-content {
		background: #fff;
		margin: 0 40rpx;
		border-radius: 0 0 8rpx 8rpx;
		overflow: hidden;
	}

	.table-row {
		display: flex;
		align-items: center;
		padding: 24rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.table-row:last-child {
		border-bottom: none;
	}

	.row-date,
	.row-count,
	.row-amount {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		color: #333;
	}

	.row-amount {
		color: #6C7CE7;
		font-weight: 500;
	}

	/* 余额变动明细列表 */
	.change-list {
		padding: 0 40rpx;
	}

	.change-item {
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 16rpx;
		padding: 24rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	}

	.change-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.change-title {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 8rpx;
	}

	.change-time {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 6rpx;
	}

	.change-balance {
		font-size: 22rpx;
		color: #666;
	}

	.change-amount {
		display: flex;
		align-items: baseline;
		margin-left: 20rpx;
	}

	.amount-symbol {
		font-size: 24rpx;
		font-weight: bold;
	}

	.amount-value {
		font-size: 32rpx;
		font-weight: bold;
	}

	.amount-positive {
		color: #52C41A;
	}

	.amount-negative {
		color: #FF4D4F;
	}

	/* 日期选择器弹窗 */
	.date-picker-mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		z-index: 1000;
		display: flex;
		align-items: flex-end;
	}

	.date-picker-content {
		width: 100%;
		background: #fff;
		border-radius: 16rpx 16rpx 0 0;
		padding-bottom: 40rpx;
	}

	.picker-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 40rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.picker-cancel,
	.picker-confirm {
		font-size: 28rpx;
		color: #6C7CE7;
	}

	.picker-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
	}

	.picker-view {
		height: 400rpx;
		width: 100%;
	}

	.picker-item {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
		font-size: 28rpx;
		color: #333;
	}

	/* 空状态 */
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 80rpx 40rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}

	/* 响应式调整 */
	@media screen and (max-width: 375px) {
		.stats-container {
			padding: 24rpx 30rpx;
			margin: 0 10rpx;
			width: 100%;
		}
		
		.stats-item {
			flex: 1;
		}
		
		.stats-amount {
			font-size: 40rpx;
		}
		
		
	}
</style> 