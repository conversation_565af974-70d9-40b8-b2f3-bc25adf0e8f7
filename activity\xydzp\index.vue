<template>
<view class="container">
	<block v-if="isload">
		<view class="pageback" :style="{ background:backcolor==''?'#f58d40':backcolor}"></view>
		<view class="wrap" :style="'background-image:url(' + info.bgpic + ');background-size:100% 100%;'">
			<view class="header clearfix">
				<view class="rule" @tap="changemaskrule">活动规则</view>
				<!-- <view class="lyj_tongji" style="">
					第二轮贡献值总数:{{z_tj.z_count}}				</view> -->
				<view @tap="goto" :data-url="'myprize?hid=' + info.id" class="my">我的奖品</view>
			</view>
			<view class="title" :style="'background-image:url(' + info.banner + ');background-size:100% 100%;'"></view>

			<view class="canvas" :hidden="showmaskrule || jxshow">
				<canvas canvas-id="roulette" style=" width: 650rpx; height: 650rpx;">
				</canvas>
				<cover-image :src="pre_url + '/static/img/xydzp_start.png'" class="start" @tap="rollStart"></cover-image>
			</view>

			<view class="border" v-if="info.use_type != 2">您今日还有 <text id="change">{{remaindaytimes}}</text> 次抽奖机会</view>
			
				<!-- <view class="border" v-if="info.use_type != 2">第二轮贡献值总数 <text id="change">{{z_tj.z_count}}	</text> </view>
				 -->
				
				
			<view class="border2" v-if="info.use_type == 1 && info.usescore>0"><text v-if="!info.is_tr">每次</text><text v-else>本次</text>抽奖将消耗 <text>{{info.usescore}}</text> {{t('积分')}}，您共有 <text id="myscore">{{member.score}}</text> {{t('积分')}}</view>
			<view class="border2" v-if="info.use_type == 3 && info.usecontribution>0"><text v-if="!info.is_tr">每次</text><text v-else>本次</text>抽奖将消耗 <text>{{info.usecontribution}}</text> {{t('贡献值')}}，您共有 <text id="myscore">{{member.contribution_num}}</text> {{t('贡献值')}}</view>
			<view class="border2" v-if="info.use_type == 2 && info.usemoney>0">每次抽奖将消耗 <text>{{t('余额')}}</text>{{info.usemoney}}元 ，您共有 <text id="mymoney">{{member.money}}</text> 元</view>
            
			<!--滚动信息-->
			<view class="scroll">
				<view class="p" :style="'background-image: url('+pre_url+'/static/img/dzp/list.png);background-size:100% 100%;'"></view>
				<view class="sideBox">
					<swiper class="bd" autoplay="true" :indicator-dots="false" current="0" :vertical="true" circular="true">
						<swiper-item v-for="(item, index) in zjlist" :key="index" class="sitem" v-if="index%2==0">
							<view>恭喜{{item.nickname}} 获得<text class="info">{{item.jxmc}}</text></view>
							<view v-if="zjlist[index+1]">恭喜{{zjlist[index+1].nickname}} 获得<text class="info">{{zjlist[index+1].jxmc}}</text></view>
						</swiper-item>
					</swiper>
				</view>
			</view>

			<view id="mask-rule" v-if="showmaskrule">
				<view class="box-rule">
					<view class="h2">活动规则说明</view>
					<view id="close-rule" :style="'background-image:url('+pre_url+'/static/img/dzp/close.png);background-size:100%'" @tap="changemaskrule"></view>
					<view class="con">
						<view class="text">
							<text decode="true" space="true">{{info.guize}}</text>
						</view>
					</view>
				</view>
			</view>
			<!--中奖提示-->
			<view id="mask" v-if="jxshow && jx>0">
				<view class="blin"></view>
				<view class="caidai" :style="'background-image: url('+pre_url+'/static/img/dzp/dianzhui.png);'"></view>
				<view class="winning reback" :style="'background:url(' + pre_url + '/static/img/dzp/bg2.png) no-repeat;background-size:100% 100%;'">
					<view class="p">
						<view>恭喜您抽中了</view>
						<view class="b" id="text1">{{jxmc}}</view>
					</view>
					<view @tap="changemask" class="btn">确定</view>
				</view>
			</view>
			<!--未中奖提示-->
			<view id="mask2" v-if="jxshow && jx==0">
				<view class="blin"></view>
				<view class="caidai" :style="'background-image: url('+pre_url+'/static/img/dzp/dianzhui.png);'"></view>
				<view class="winning reback" :style="'background:url(' + pre_url + '/static/img/dzp/bg3.png) no-repeat;background-size:100% 100%;'">
					<view class="p">
						<view class="b" id="text2">{{jxmc}}</view>
					</view>
					<view @tap="changemask" class="btn">确定</view>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var dot_inter, bool;
var app = getApp();
var windowWidth = uni.getSystemInfoSync().windowWidth;

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			pre_url:app.globalData.pre_url,
      speed: 10,

      /**转盘速度 */
      speedDot: 1000,

      /**点切换速度 */
      dotColor: ['#ffffff', '#FCDF00'],
      dotColor_1: ['#ffffff', '#FCDF00'],
      dotColor_2: ['#FCDF00', '#ffffff'],
      jxshow: false,
      showmaskrule: false,
			info:{},
			member:{},
			remaindaytimes:0,
			remaintimes:0,
			zjlist:[],
      jxarr: "",
      latitude: "",
      longitude: "",
      angelTo: "",
      jxmc: "",
      jx: "",
	  backcolor: "",
	  z_tj:{},
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onShareAppMessage: function () {
		var that = this;
		var title = that.info.name;
		if (that.info.sharetitle) title = that.info.sharetitle;
		var sharepic = that.info.sharepic ? that.info.sharepic : '';
		var sharelink = that.info.sharelink ? that.info.sharelink : '';
		var sharedesc = that.info.sharedesc ? that.info.sharedesc : '';
		return this._sharewx({title:title,desc:sharedesc,link:sharelink,pic:sharepic,callback:function(){that.sharecallback();}});
  },
	onShareTimeline:function(){
		var that = this;
		var title = that.info.name;
		if (that.info.sharetitle) title = that.info.sharetitle;
		var sharepic = that.info.sharepic ? that.info.sharepic : '';
		var sharelink = that.info.sharelink ? that.info.sharelink : '';
		var sharedesc = that.info.sharedesc ? that.info.sharedesc : '';
		var sharewxdata = this._sharewx({title:title,desc:sharedesc,link:sharelink,pic:sharepic,callback:function(){that.sharecallback();}});
		var query = (sharewxdata.path).split('?')[1];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  methods: {
		getdata: function () {
			var that = this;
			var id = that.opt.id;
			that.loading = true;
			app.get('ApiChoujiang/index', {id: id}, function (res) {
				that.loading = false;
				if(res.status == 0){
					app.alert(res.msg);
					return;
				}
				that.info = res.info;
				that.jxarr = res.jxarr;
				that.member = res.member;
				that.remaindaytimes = res.remaindaytimes;
				that.remaintimes = res.remaintimes;
				that.zjlist = res.zjlist;
				that.backcolor = res.info.bgcolor;
				uni.setNavigationBarTitle({
					title: res.info.name
				});
				that.downloadFile(res.jxarr, 0);
				if (that.info.fanwei == 1) {
					app.getLocation(function (res) {
						var latitude = res.latitude;
						var longitude = res.longitude;
						that.latitude = latitude;
						that.longitude = longitude;
						console.log(longitude);
					});
				}
				that.z_tj=res.z;
				var title = that.info.name;
				if (that.info.sharetitle) title = that.info.sharetitle;
				var sharepic = that.info.sharepic ? that.info.sharepic : '';
				var sharelink = that.info.sharelink ? that.info.sharelink : '';
				var sharedesc = that.info.sharedesc ? that.info.sharedesc : '';
				that.loaded({title:title,desc:sharedesc,link:sharelink,pic:sharepic,callback:function(){that.sharecallback();}});
			});
		},
		sharecallback:function(){
			var that = this;
			app.post("ApiChoujiang/share", {hid: that.info.id}, function (res) {
				if (res.status == 1) {
					setTimeout(function () {
						that.getdata();
					}, 1000);
				} else if (res.status == 0) {//dialog(res.msg);
				}
			});
		},
    downloadFile: function (jxarr, i) {
      var that = this;
      if (jxarr[i].pic) {
        uni.downloadFile({
          url: jxarr[i].pic,
          success: function (res) {
            if (res.tempFilePath) {
              jxarr[i].pic = res.tempFilePath;
              that.jxarr = jxarr;
            }
          },
          complete: function () {
            if (jxarr.length > i + 1) {
              that.downloadFile(jxarr, i + 1);
            } else {
              that.dotStart();
            }
          }
        });
      } else {
        if (jxarr.length > i + 1) {
          that.downloadFile(jxarr, i + 1);
        } else {
          that.dotStart();
        }
      }
    },
    changemaskrule: function () {
      this.showmaskrule = !this.showmaskrule
    },
    changemask: function () {
      this.jxshow = !this.jxshow;
      this.getdata();
    },
    rollStart: function () {
      var that = this;
      if (bool) return; // 如果在执行就退出
      bool = true; // 标志为 在执行
      app.post('ApiChoujiang/index', {id: that.info.id,op: 'getjx',longitude: that.longitude,latitude: that.latitude}, function (res) {
        if (res.status != 1) {
          app.alert(res.msg);
          bool = false;
          return;
        } else {
          //奖品数量等于10,指针落在对应奖品区域的中心角度[252, 216, 180, 144, 108, 72, 36, 360, 324, 288]
          var angel = 360 - 360 / res.jxcount * res.jxindex;
        }
        angel += 360 * 6;
        var baseStep = 30; // 起始滚动速度

        var baseSpeed = 0.3;
        var count = 1;
        var timer = setInterval(function () {
          that.angelTo = count
          clearInterval(dot_inter);
          that.drawCanvas();

          if (count == angel) {
            console.log('完毕');
            bool = false;
            clearInterval(timer);
            that.jxshow = true;
            that.jxmc = res.jxmc;
            that.jx = res.jx;
            that.dotStart();

            if (res.jxtp == 2 && res.spdata) {
              uni.sendBizRedPacket({
                timeStamp: res.spdata.timeStamp,
                // 支付签名时间戳，
                nonceStr: res.spdata.nonceStr,
                // 支付签名随机串，不长于 32 位
                package: res.spdata.package,
                //扩展字段，由商户传入
                signType: res.spdata.signType,
                // 签名方式，
                paySign: res.spdata.paySign,
                // 支付签名
                success: function (res) {
                  console.log(res);
                },
                fail: function (res) {
                  console.log(res);
                },
                complete: function (res) {
                  console.log(res);
                }
              });
            }
          }

          count = count + baseStep * ((angel - count) / angel > baseSpeed ? baseSpeed : (angel - count) / angel);

          if (angel - count < 0.5) {
            count = angel;
          }
        }, that.speed);
      });
    },
    drawCanvas: function () {
      var that = this;
      var ctx = uni.createCanvasContext('roulette', this);
      var angelTo = this.angelTo || 0;
      var width = windowWidth / 750 * 650;
      var height = width;
      var x = width / 2;
      var y = width / 2;
      var num = that.jxarr.length;
      ctx.translate(x, y);
      ctx.clearRect(-width, -height, width, height);
      ctx.rotate(angelTo * Math.PI / 180); // 画外圆

      ctx.beginPath();
      ctx.lineWidth = width / 2;
      ctx.strokeStyle = '#FFF7C5';
      ctx.arc(0, 0, width / 4, 0, 2 * Math.PI);
      ctx.stroke();
      ctx.beginPath();
      ctx.lineWidth = 1;
      ctx.strokeStyle = '#D9644F';
      ctx.arc(0, 0, width / 2 - 1, 0, 2 * Math.PI);
      ctx.stroke();
      ctx.beginPath();
      ctx.lineWidth = 2;
      ctx.strokeStyle = '#FDF28C';
      ctx.arc(0, 0, width / 2 - 3, 0, 2 * Math.PI);
      ctx.stroke();
      ctx.beginPath();
      ctx.lineWidth = 15;
      ctx.strokeStyle = '#F8645E';
      ctx.arc(0, 0, width / 2 - 14, 0, 2 * Math.PI);
      ctx.stroke(); // 装饰点

      var dotColor = that.dotColor;
      var startAngel = 0;

      for (var i = 0; i < 26; i++) {
        ctx.beginPath();
        var radius = width / 2 - 14;
        var xr = radius * Math.cos(startAngel);
        var yr = radius * Math.sin(startAngel);
        ctx.fillStyle = dotColor[i % dotColor.length];
        ctx.arc(xr, yr, 4, 0, 2 * Math.PI);
        ctx.fill();
        startAngel += 360 / 26 * (Math.PI / 180);
      }

      var jxarr = that.jxarr;
      ctx.rotate(-(360 / num) * Math.PI / 180);

      for (var i = 0; i < num; i++) {
        ctx.rotate(360 / num * Math.PI / 180); //ctx.setFontSize(14)

        ctx.font = 'normal bold 14px Arial';
        ctx.fillStyle = '#e75228', ctx.textAlign = "center";
        ctx.fillText(jxarr[i].mc, 0, -(width / 2 - 50));
        ctx.drawImage(jxarr[i].pic, -20, -(width / 2 - 70), 40, 40); //ctx.restore();
      }

      if (num % 2 == 0) {
        ctx.rotate(180 / num * Math.PI / 180);
      }

      for (var i = 0; i < num; i++) {
        //ctx.save();
        ctx.rotate(360 / num * Math.PI / 180);
        ctx.beginPath();
        ctx.lineWidth = 2;
        ctx.moveTo(0, 0);
        ctx.lineTo(0, width / 2 - 20);
        ctx.setStrokeStyle('#f6625c');
        ctx.stroke();
      }

      ctx.draw();
    },
    dotStart: function () {
      var that = this;
      var times = 0;
      that.drawCanvas();
      dot_inter = setInterval(function () {
        if (times % 2) {
          var dotColor = that.dotColor_1;
        } else {
          var dotColor = that.dotColor_2;
        }

        times++;
        that.dotColor = dotColor;
        that.drawCanvas();
      }, that.speedDot);
    }
  }
};
</script>
<style>
.canvas{position: relative;}
.canvas{width: 650rpx;height: 650rpx;margin:0 auto;margin-bottom:20rpx}
.canvas>canvas{z-index: 0;}
.start{height: 200rpx;width: 160rpx;position: absolute;top:50%;left: 50%;margin-left: -80rpx;margin-top: -110rpx;z-index: 2;}

.wrap {width:100%;height:100%;}
.header{width:100%;padding:22rpx 37rpx 0 37rpx;display:flex;justify-content:space-between}
.rule,.my{width:140rpx;height:60rpx;border: 1px solid #f58d40;font-size:30rpx;line-height:60rpx;text-align: center;color: #f58d40;border-radius:5rpx;}
.title {width:640rpx;height:316rpx;margin: auto;margin-top:-60rpx;}

/*次数*/
.border {width: 380rpx;height:64rpx;margin: 0 auto 25rpx;background:#fb3a13;font-size:24rpx;line-height:64rpx;text-align: center;color: #fff;border-radius:45rpx}
.border2 {width:600rpx;height:50rpx;margin: 0 auto;background:#dbaa83;font-size:24rpx;line-height:50rpx;text-align: center;color: #fff;border-radius:10rpx}
.scroll {width:550rpx;height:185rpx;margin:75rpx auto 0 auto;}
.scroll .p {width: 372rpx;height:24rpx;margin: auto;}
.sideBox{  width: 100%;height:100rpx;margin-top:20rpx;padding: 10rpx 0 10rpx 0;background-color: rgba(255, 255, 255, 0.2);border-radius:10rpx;overflow:hidden;}
.sideBox .bd {width: 100%;height:80rpx;overflow:hidden;}
.sideBox .sitem{overflow:hidden;text-align: center;font-size:20rpx;line-height:40rpx;color: #fff;}

/*规则弹窗*/
#mask-rule,#mask {position: fixed;left: 0;top: 0;z-index: 999;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.85);}
#mask-rule .box-rule {position: relative;margin: 30% auto;padding-top: 40rpx;width: 90%;height: 675rpx;border-radius: 20rpx;background-color: #f58d40;}
#mask-rule .box-rule .star {position: absolute;left: 50%;top: -100rpx;margin-left: -130rpx;width: 259rpx;height:87rpx;}
#mask-rule .box-rule .h2 {width: 100%;text-align: center;line-height: 34rpx;font-size: 34rpx;font-weight: normal;color: #fff;}
#mask-rule #close-rule {position: absolute;right: 34rpx;top: 38rpx;width: 40rpx;height: 40rpx;}
/*内容盒子*/
#mask-rule .con {overflow: auto;position: relative;margin: 40rpx auto;padding-right: 15rpx;width: 580rpx;height: 82%;line-height: 48rpx;font-size: 26rpx;color: #fff;}
#mask-rule .con .text {position: absolute;top: 0;left: 0;width: inherit;height: auto;}
/*中奖提示*/
#mask,#mask2{position: fixed;left: 0;top: 0;z-index: 999;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.85);}
#mask .blin,#mask2 .blin {width: 100%;height: 100%;-o-animation: circle 10s linear infinite;-ms-animation: circle 10s linear infinite;-moz-animation: circle 10s linear infinite;-webkit-animation: circle 10s linear infinite;animation: circle 10s linear infinite;}
@keyframes circle {0% {-o-transform: rotate(0deg);-ms-transform: rotate(0deg);-moz-transform: rotate(0deg);-webkit-transform: rotate(0deg);transform: rotate(0deg);}100% {-o-transform: rotate(360deg);-ms-transform: rotate(360deg);-moz-transform: rotate(360deg);-webkit-transform: rotate(360deg);transform: rotate(360deg);}}
#mask .caidai,#mask2 .caidai{position: absolute;left: 0;top: 0;z-index: 1;width: 100%;height: 100%;-o-transform: scale(1.2);-ms-transform: scale(1.2);-moz-transform: scale(1.2);-webkit-transform: scale(1.2);transform: scale(1.2);}
#mask .winning,#mask2 .winning {position: absolute;left: 50%;top: 50%;z-index: 1;width: 675rpx;height: 600rpx;margin: -300rpx 0 0 -338rpx;-o-transform: scale(0.1);-ms-transform: scale(0.1);-moz-transform: scale(0.1);-webkit-transform: scale(0.1);transform: scale(0.1);}
#mask .reback,#mask2 .reback{-o-animation: reback .5s linear forwards;-ms-animation: reback .5s linear forwards;-moz-animation: reback .5s linear forwards;-webkit-animation: reback .5s linear forwards;animation: reback .5s linear forwards;}
@keyframes reback {100% {-o-transform: scale(1);-ms-transform: scale(1);-moz-transform: scale(1);-webkit-transform: scale(1);transform: scale(1);}}
.winning .p{ position: absolute;left: 50%;top: 30%;width:80%;margin-left:-40%;color:#FFF;font-size:52rpx;text-align:center;}
.winning .b{ font-size:44rpx;}
.winning .btn {position: absolute;left: 50%;bottom: 15%;z-index: 2;width: 364rpx;height: 71rpx;line-height: 71rpx;margin-left: -182rpx;background-color:#ffee8d;border-radius:45rpx;-webkit-border-radius:45rpx;color:#f62a39;text-align:center;font-size:45rpx;
}
@keyframes shake {50% {-o-transform: rotate(-5deg);-ms-transform: rotate(-5deg);-moz-transform: rotate(-5deg);-webkit-transform: rotate(-5deg);transform: rotate(-5deg);}100% {-o-transform: rotate(5deg);-ms-transform: rotate(5deg);-moz-transform: rotate(5deg);-webkit-transform: rotate(5deg);transform: rotate(5deg);}}
@keyframes fadein {100% {opacity: 1;-o-transform: rotate(360deg);-ms-transform: rotate(360deg);-moz-transform: rotate(360deg);-webkit-transform: rotate(360deg);transform: rotate(360deg);}}
.pageback{position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: -1;}

.lyj_tongji{
	    text-align: center;
	    line-height: 30px;
	    width: 178px;
	    height: 30px;
	    font-size: 15px;
	    line-height: 30px;
	    text-align: center;
	    color: #ff0000;
	    border-radius: 2px;
}
</style>