<template>
<view class="container">
	<block v-if="isload">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="header-title">文件上传</view>
		</view>

		<!-- 上传区域 -->
		<view class="upload-area">
			<view class="upload-card" @tap="chooseFile">
				<view class="upload-icon">
					<text class="iconfont iconshangchuan" :style="{color: t('color1')}"></text>
				</view>
				<view class="upload-text">点击选择文件</view>
				<view class="upload-desc">支持图片、文档等多种格式</view>
			</view>
		</view>

		<!-- 文件列表 -->
		<view class="file-list" v-if="fileList.length > 0">
			<view class="list-title">已选择文件</view>
			<block v-for="(file, index) in fileList" :key="index">
				<view class="file-item">
					<view class="file-icon">
						<text class="iconfont" :class="getFileIcon(file.name)"></text>
					</view>
					<view class="file-info">
						<view class="file-name">{{file.name}}</view>
						<view class="file-size">{{formatFileSize(file.size)}}</view>
					</view>
					<view class="file-status" v-if="file.status">
						<text v-if="file.status === 'uploading'" class="status-text uploading">上传中...</text>
						<text v-else-if="file.status === 'success'" class="iconfont iconzhengchang success"></text>
						<text v-else-if="file.status === 'error'" class="iconfont iconcuowu error"></text>
					</view>
					<view class="file-actions">
						<view class="action-btn delete" @tap="removeFile" :data-index="index">
							<text class="iconfont iconshanchu"></text>
						</view>
					</view>
				</view>
			</block>
		</view>

		<!-- 上传按钮 -->
		<view class="upload-actions" v-if="fileList.length > 0">
			<view class="btn-upload" :style="{background: t('color1')}" @tap="uploadFiles" :class="uploading ? 'disabled' : ''">
				<text v-if="!uploading">开始上传</text>
				<text v-else>上传中...</text>
			</view>
		</view>

		<!-- 上传历史 -->
		<view class="upload-history" v-if="historyList.length > 0">
			<view class="history-title">上传历史</view>
			<block v-for="(item, index) in historyList" :key="index">
				<view class="history-item">
					<view class="history-icon">
						<text class="iconfont" :class="getFileIcon(item.file_name)"></text>
					</view>
					<view class="history-info">
						<view class="history-name">{{item.file_name}}</view>
						<view class="history-time">{{item.upload_time_text}}</view>
					</view>
					<view class="history-status">
						<text v-if="item.status === 1" class="iconfont iconzhengchang success"></text>
						<text v-else class="iconfont iconcuowu error"></text>
					</view>
				</view>
			</block>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			isload: false,
			loading: false,
			fileList: [],
			historyList: [],
			uploading: false
		};
	},

	onLoad: function(opt) {
		this.getUploadHistory();
		this.loaded();
	},

	methods: {
		// 选择文件
		chooseFile: function() {
			var that = this;
			
			uni.chooseFile({
				count: 5,
				extension: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt', '.xlsx', '.xls'],
				success: function(res) {
					var files = res.tempFiles;
					files.forEach(function(file) {
						that.fileList.push({
							name: file.name,
							path: file.path,
							size: file.size,
							status: 'ready'
						});
					});
				},
				fail: function(err) {
					that.$refs.popmsg.show('选择文件失败');
				}
			});
		},

		// 移除文件
		removeFile: function(e) {
			var index = e.currentTarget.dataset.index;
			this.fileList.splice(index, 1);
		},

		// 上传文件
		uploadFiles: function() {
			if (this.uploading || this.fileList.length === 0) return;

			var that = this;
			that.uploading = true;

			// 逐个上传文件
			var uploadPromises = that.fileList.map(function(file, index) {
				return that.uploadSingleFile(file, index);
			});

			Promise.all(uploadPromises).then(function(results) {
				that.uploading = false;
				var successCount = results.filter(r => r.success).length;
				that.$refs.popmsg.show(`上传完成，成功${successCount}个，失败${results.length - successCount}个`);
				
				// 清空已上传的文件
				that.fileList = that.fileList.filter(file => file.status !== 'success');
				
				// 刷新历史记录
				that.getUploadHistory();
			});
		},

		// 上传单个文件
		uploadSingleFile: function(file, index) {
			var that = this;
			
			return new Promise(function(resolve) {
				// 更新状态为上传中
				that.$set(that.fileList[index], 'status', 'uploading');

				uni.uploadFile({
					url: app.globalData.pre_url + '/api/ApiCoze/uploadfile',
					filePath: file.path,
					name: 'file',
					header: {
						'Authorization': 'Bearer ' + app.getToken()
					},
					success: function(res) {
						try {
							var data = JSON.parse(res.data);
							if (data.code === 1) {
								that.$set(that.fileList[index], 'status', 'success');
								that.$set(that.fileList[index], 'file_id', data.data.file_id);
								resolve({ success: true, data: data });
							} else {
								that.$set(that.fileList[index], 'status', 'error');
								resolve({ success: false, error: data.msg });
							}
						} catch (e) {
							that.$set(that.fileList[index], 'status', 'error');
							resolve({ success: false, error: '上传失败' });
						}
					},
					fail: function(err) {
						that.$set(that.fileList[index], 'status', 'error');
						resolve({ success: false, error: '网络错误' });
					}
				});
			});
		},

		// 获取上传历史
		getUploadHistory: function() {
			var that = this;
			
			// 这里可以调用API获取上传历史，暂时使用模拟数据
			that.historyList = [];
		},

		// 获取文件图标
		getFileIcon: function(fileName) {
			var ext = fileName.split('.').pop().toLowerCase();
			switch (ext) {
				case 'jpg':
				case 'jpeg':
				case 'png':
				case 'gif':
					return 'icontupian';
				case 'pdf':
					return 'iconpdf';
				case 'doc':
				case 'docx':
					return 'iconword';
				case 'xls':
				case 'xlsx':
					return 'iconexcel';
				case 'txt':
					return 'iconwenben';
				default:
					return 'iconwenjian';
			}
		},

		// 格式化文件大小
		formatFileSize: function(bytes) {
			if (bytes === 0) return '0 B';
			var k = 1024;
			var sizes = ['B', 'KB', 'MB', 'GB'];
			var i = Math.floor(Math.log(bytes) / Math.log(k));
			return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
		}
	}
};
</script>

<style>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	background: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #eee;
	position: fixed;
	top: var(--window-top);
	left: 0;
	right: 0;
	z-index: 100;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
}

.upload-area {
	padding: 30rpx;
	margin-top: 120rpx;
}

.upload-card {
	background: #fff;
	border-radius: 20rpx;
	padding: 80rpx 30rpx;
	text-align: center;
	border: 2rpx dashed #ddd;
}

.upload-icon {
	margin-bottom: 30rpx;
}

.upload-icon .iconfont {
	font-size: 80rpx;
}

.upload-text {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 15rpx;
}

.upload-desc {
	font-size: 24rpx;
	color: #999;
}

.file-list {
	margin: 30rpx;
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
}

.list-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.file-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.file-item:last-child {
	border-bottom: none;
}

.file-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 12rpx;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.file-icon .iconfont {
	font-size: 32rpx;
	color: #666;
}

.file-info {
	flex: 1;
}

.file-name {
	font-size: 26rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.file-size {
	font-size: 22rpx;
	color: #999;
}

.file-status {
	margin-right: 20rpx;
}

.status-text {
	font-size: 22rpx;
}

.status-text.uploading {
	color: #1890ff;
}

.success {
	color: #52c41a;
	font-size: 28rpx;
}

.error {
	color: #ff4d4f;
	font-size: 28rpx;
}

.file-actions {
	display: flex;
	align-items: center;
}

.action-btn {
	width: 50rpx;
	height: 50rpx;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.delete {
	background: #ff4d4f;
	color: #fff;
}

.action-btn .iconfont {
	font-size: 24rpx;
}

.upload-actions {
	padding: 30rpx;
}

.btn-upload {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #fff;
}

.btn-upload.disabled {
	opacity: 0.6;
}

.upload-history {
	margin: 30rpx;
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
}

.history-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.history-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.history-item:last-child {
	border-bottom: none;
}

.history-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 12rpx;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.history-icon .iconfont {
	font-size: 32rpx;
	color: #666;
}

.history-info {
	flex: 1;
}

.history-name {
	font-size: 26rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.history-time {
	font-size: 22rpx;
	color: #999;
}

.history-status {
	font-size: 28rpx;
}
</style>
