<template>
    <view @tap="handleTapGuide" class="position-guide-box ptp_exposure_location" data-ptpid="5bc9-1fe8-8bdb-2b49">
        <view class="position-guide">
            <view>
                <view class="title">Hi～你正在浏览杭州的兼职</view>
                <view class="sub-title">开启定位，为你推荐附近高薪兼职</view>
            </view>
            <view class="open">开启定位</view>
        </view>
    </view>
</template>

<script>

export default {
    data() {
        return {};
    },
    props: {},
    methods: {
        handleTapGuide: function () {}
    },
    mounted: function () {}
};
</script>
<style>
@import './index.css';
</style>
