<template>
<view class="container">
	<block v-if="isload">
		<view class="topsearch flex-y-center">
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="搜索感兴趣的训练营" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
			</view>
		</view>
		
		<view class="training_list">
			<!--横排-->
			<view v-if="listtype=='0'" class="training-itemlist" v-for="(item,index) in datalist" :key="item.id" @click="goto" :data-url="'/pagesExb/training/detail?id='+item.id">
				<view class="training-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
				</view>
				<view class="training-info">
					<view class="p1" :style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''">{{item.name}}</view>
					<view class="p3" :style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''" v-if="item.subname">
						{{item.subname}}
					</view>
					<view class="p2">
						<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
						<text style="overflow:hidden">阅读 {{item.readcount}}</text>
					</view>
					<view class="course-info" v-if="item.kcname">
						<text class="course-tag">课程</text>
						<text class="course-name">{{item.kcname}}</text>
					</view>
					<view class="permission-status" v-if="item.need_buy == 1">
						<text class="permission-tag" :class="item.has_permission ? 'has-permission' : 'no-permission'">
							{{item.has_permission ? '已解锁' : '需购买课程'}}
						</text>
					</view>
				</view>
			</view>
			<!--双排-->
			<view v-if="listtype=='1'" class="training-item2" v-for="(item,index) in datalist" :key="item.id" :style="{marginRight:index%2==0?'2%':'0'}" @click="goto" :data-url="'/pagesExb/training/detail?id='+item.id">
				<view class="training-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
				</view>
				<view class="training-info">
					<view class="p1" :style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''">{{item.name}}</view>
					<view class="p3" :style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''" v-if="item.subname">
						{{item.subname}}
					</view>
					<view class="p2">
						<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
						<text style="overflow:hidden">阅读 {{item.readcount}}</text>
					</view>
					<view class="course-info" v-if="item.kcname">
						<text class="course-tag">课程</text>
						<text class="course-name">{{item.kcname}}</text>
					</view>
					<view class="permission-status" v-if="item.need_buy == 1">
						<text class="permission-tag" :class="item.has_permission ? 'has-permission' : 'no-permission'">
							{{item.has_permission ? '已解锁' : '需购买课程'}}
						</text>
					</view>
				</view>
			</view>
			<!--单排-->
			<view v-if="listtype=='3'" class="training-item1" v-for="(item,index) in datalist" :key="item.id" @click="goto" :data-url="'/pagesExb/training/detail?id='+item.id">
				<view class="training-pic">
					<image class="image" :src="item.pic" mode="widthFix"/>
				</view>
				<view class="training-info">
					<view class="p1" :style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''">{{item.name}}</view>
					<view class="p3" :style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''" v-if="item.subname">
						{{item.subname}}
					</view>
					<view class="p2">
						<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
						<text style="overflow:hidden">阅读 {{item.readcount}}</text>
					</view>
					<view class="course-info" v-if="item.kcname">
						<text class="course-tag">课程</text>
						<text class="course-name">{{item.kcname}}</text>
					</view>
					<view class="permission-status" v-if="item.need_buy == 1">
						<text class="permission-tag" :class="item.has_permission ? 'has-permission' : 'no-permission'">
							{{item.has_permission ? '已解锁' : '需购买课程'}}
						</text>
					</view>
				</view>
			</view>
		</view>
	</block>
	<nodata v-if="nodata" text="暂无训练营内容"></nodata>
	<nomore v-if="nomore" text="没有更多训练营了"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

			nodata:false,
			nomore:false,
			keyword:'',
      datalist: [],
      pagenum: 1,
      datalist: [],
      kcid: 0,
			bid: 0,
			listtype:0,
            set:'',
    };
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.kcid = this.opt.kcid || 0;	
		this.bid = this.opt.bid || 0;
        if(this.opt.keyword) {
        	this.keyword = this.opt.keyword;
        }
        
        // 2025-01-03 22:55:53,565-INFO-[training_index][onLoad_001] 初始化训练营列表页面
        console.log('2025-01-03 22:55:53,565-INFO-[training_index][onLoad_001] 初始化训练营列表页面, kcid:', this.kcid);
        
    this.getdata();
  },
	onPullDownRefresh: function () {
		// 2025-01-03 22:55:53,565-INFO-[training_index][onPullDownRefresh_001] 下拉刷新训练营列表
		console.log('2025-01-03 22:55:53,565-INFO-[training_index][onPullDownRefresh_001] 下拉刷新训练营列表');
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nomore && !this.nodata) {
      // 2025-01-03 22:55:53,565-INFO-[training_index][onReachBottom_001] 上拉加载更多训练营
      console.log('2025-01-03 22:55:53,565-INFO-[training_index][onReachBottom_001] 上拉加载更多训练营, pagenum:', this.pagenum + 1);
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },

  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
      var kcid = that.kcid;
      
			// 2025-01-03 22:55:53,565-INFO-[training_index][getdata_001] 开始获取训练营数据
			console.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_001] 开始获取训练营数据, pagenum:', pagenum, 'kcid:', kcid, 'keyword:', keyword);
			
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			
      app.post('ApiKechengTraining/gettraininglist', {
      	bid: that.bid,
      	kcid: kcid,
      	page: pagenum,
      	pagesize: 10,
      	keyword: keyword
      }, function (res) {
				that.loading = false;
				
				// 2025-01-03 22:55:53,565-INFO-[training_index][getdata_002] 获取训练营数据响应
				console.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_002] 获取训练营数据响应, status:', res.status, 'data_length:', res.data ? res.data.length : 0);
				
				if (res.status == 0) {
					// 2025-01-03 22:55:53,565-ERROR-[training_index][getdata_003] 获取训练营数据失败
					console.error('2025-01-03 22:55:53,565-ERROR-[training_index][getdata_003] 获取训练营数据失败:', res.msg);
					app.error(res.msg);
					return;
				}
				
        var data = res.data;
        if (pagenum == 1) {
					that.listtype = res.listtype || 0;
                    that.set = res.set || {};

					uni.setNavigationBarTitle({
						title: res.title || '训练营'
					});
					
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
            // 2025-01-03 22:55:53,565-INFO-[training_index][getdata_004] 没有训练营数据
            console.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_004] 没有训练营数据');
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
            // 2025-01-03 22:55:53,565-INFO-[training_index][getdata_005] 没有更多训练营数据
            console.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_005] 没有更多训练营数据');
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
            
            // 2025-01-03 22:55:53,565-INFO-[training_index][getdata_006] 加载更多训练营数据成功
            console.log('2025-01-03 22:55:53,565-INFO-[training_index][getdata_006] 加载更多训练营数据成功, 新增:', data.length, '总数:', newdata.length);
          }
        }
      });
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
      
      // 2025-01-03 22:55:53,565-INFO-[training_index][searchConfirm_001] 搜索训练营
      console.log('2025-01-03 22:55:53,565-INFO-[training_index][searchConfirm_001] 搜索训练营, keyword:', keyword);
      
      that.getdata();
    },
  }
};
</script>
<style>
page{background:#f6f6f7}
.topsearch{width:100%;padding:20rpx 20rpx;background:#fff}
.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1;overflow:hidden}
.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;background-color:#f5f5f5;}

.training_list{padding:10rpx 16rpx;background:#f6f6f7;margin-top:6rpx;}
.training_list .training-item1 {width:100%;display: inline-block;position: relative;margin-bottom:16rpx;background: #fff;border-radius:12rpx;overflow:hidden}
.training_list .training-item1 .training-pic {width:100%;height:auto;overflow:hidden;background: #ffffff;}
.training_list .training-item1 .training-pic .image{width: 100%;height:auto}
.training_list .training-item1 .training-info {padding:10rpx 20rpx 20rpx 20rpx;}
.training_list .training-item1 .training-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.training_list .training-item1 .training-info .t1{word-break: break-all;text-overflow: ellipsis;overflow: hidden;display: block;font-size: 32rpx;}
.training_list .training-item1 .training-info .t2{word-break: break-all;text-overflow: ellipsis;padding-top:4rpx;overflow:hidden;}
.training_list .training-item1 .training-info .p2{flex-grow:0;flex-shrink:0;display:flex;padding:10rpx 0;font-size:24rpx;color:#a88;overflow:hidden}

.training_list .training-item2 {width: 49%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}
.training_list .training-item2 .training-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}
.training_list .training-item2 .training-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.training_list .training-item2 .training-info {padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}
.training_list .training-item2 .training-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.training_list .training-item2 .training-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}

.training_list .training-itemlist {width:100%;display: inline-block;position: relative;margin-bottom:12rpx;padding:12rpx;background: #fff;display:flex;border-radius:8rpx;}
.training_list .training-itemlist .training-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 25%;position: relative;}
.training_list .training-itemlist .training-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.training_list .training-itemlist .training-info {width: 65%;height:auto;overflow:hidden;padding:0 20rpx;display:flex;flex-direction:column;justify-content:space-between}
.training_list .training-itemlist .training-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:92rpx}
.training_list .training-itemlist .training-info .p2{display:flex;flex-grow:0;flex-shrink:0;font-size:24rpx;color:#a88;overflow:hidden;padding-bottom:6rpx}

.p3{color:#8c8c8c;font-size:26rpx;line-height:40rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin:6rpx 0;}

/* 课程信息样式 */
.course-info {
  display: flex;
  align-items: center;
  margin: 8rpx 0;
}
.course-tag {
  background: linear-gradient(45deg, #FF6B35, #F7931E);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
  font-weight: bold;
}
.course-name {
  color: #666;
  font-size: 24rpx;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 权限状态样式 */
.permission-status {
  margin: 8rpx 0;
}
.permission-tag {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
}
.permission-tag.has-permission {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: #fff;
}
.permission-tag.no-permission {
  background: linear-gradient(45deg, #ff9800, #f57c00);
  color: #fff;
}
</style> 