<template>
	<view class="container">
		<!-- 粒子背景容器 -->
		<canvas canvas-id="particlesCanvas" class="particles-canvas" 
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"></canvas>
		
		<!-- 装饰性背景 -->
		<view class="bg-grid"></view>
		<view class="bg-circles"></view>
		
		<view class="camera-console">
			<!-- 标题区域 -->
			<view class="header-section">
				<view class="ai-logo">
					<view class="logo-glow"></view>
					<text class="logo-icon">✨</text>
				</view>
				<view class="title-info">
					<text class="main-title" :style="{color:t('color1')}">AI变脸预测系统</text>
					<text class="subtitle">使用先进的AI技术预测您20年后的样子</text>
				</view>
				<view class="time-display">
					<text class="year">2049</text>
					<text class="status">{{currentStatus}}</text>
				</view>
			</view>
			
			<!-- 主要内容区域 -->
			<view class="main-content">
				<!-- 摄像头区域 -->
				<view v-if="currentStep === 'camera'" class="camera-section">
					<view class="camera-container">
						<camera
							v-if="showCamera && !showConfig"
							class="camera-preview"
							device-position="front"
							flash="off"
							@error="onCameraError"
							@initdone="onCameraReady">
						</camera>

						<!-- 摄像头被暂停时的占位符 -->
						<view v-if="showConfig" class="camera-placeholder">
							<text class="placeholder-text">摄像头已暂停</text>
							<text class="placeholder-desc">完成配置后将自动恢复</text>
						</view>
						
						<!-- 拍照框架 -->
						<view class="photo-frame">
							<view class="frame-corner top-left"></view>
							<view class="frame-corner top-right"></view>
							<view class="frame-corner bottom-left"></view>
							<view class="frame-corner bottom-right"></view>
							<view class="scan-line"></view>
						</view>
						
						<!-- 摄像头状态 -->
						<view class="camera-status">
							<view class="status-indicator">
								<view class="status-dot" :class="{active: cameraReady}"></view>
								<text class="status-text">{{cameraStatusText}}</text>
							</view>
						</view>

						<!-- 调试控制 -->
						<view class="debug-controls" v-if="showDebugInfo">
							<button class="debug-btn" @tap="forceShowConfig" type="default">强制显示配置</button>
							<button class="debug-btn" @tap="toggleCamera" type="default">切换摄像头</button>
							<button class="debug-btn" @tap="testAlert" type="default">测试弹窗</button>
							<view class="debug-info-text">
								<text>showCamera: {{showCamera}}</text>
								<text>showConfig: {{showConfig}}</text>
								<text>currentStep: {{currentStep}}</text>
							</view>
						</view>
					</view>
					
					<!-- 拍照控制 -->
					<view class="photo-controls">
						<view class="capture-btn" @tap="capturePhoto" :class="{disabled: !cameraReady}"
							  :style="{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
							<view class="btn-glow"></view>
							<text class="btn-icon">📷</text>
							<text class="btn-text">拍照</text>
						</view>
						<view class="upload-btn" @tap="chooseImage">
							<view class="btn-glow"></view>
							<text class="btn-icon">📁</text>
							<text class="btn-text">选择图片</text>
						</view>
					</view>
				</view>
				
				<!-- 预览和处理区域 -->
				<view v-if="currentStep === 'preview'" class="preview-section">
					<view class="preview-container">
						<view class="photo-preview">
							<image :src="capturedImageUrl" class="preview-image" mode="aspectFit"></image>
							<view class="preview-overlay">
								<view class="analysis-grid"></view>
								<view class="analysis-points"></view>
							</view>
						</view>
						
						<!-- 处理控制 -->
						<view class="process-controls">
							<view class="process-btn" @tap="startProcessing">
								<view class="btn-glow"></view>
								<text class="btn-icon">🔮</text>
								<text class="btn-text">开始AI预测</text>
							</view>
							<view class="process-info">
								<text class="info-text">AI将分析您的面部特征，预测20年后的样子</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 处理中状态 -->
				<view v-if="currentStep === 'processing'" class="processing-section">
					<view class="processing-container">
						<view class="ai-brain">
							<view class="brain-core"></view>
							<view class="neural-waves"></view>
							<view class="data-streams"></view>
						</view>
						<view class="processing-info">
							<text class="process-title">AI正在处理中...</text>
							<view class="progress-bar">
								<view class="progress-fill" :style="{width: progressPercent + '%'}"></view>
							</view>
							<view class="processing-steps">
								<view v-for="(step, index) in processingSteps" :key="index"
									  :class="['step', {active: currentProcessStep === index}]">
									<text class="step-icon">{{step.icon}}</text>
									<text class="step-text">{{step.text}}</text>
								</view>
							</view>
							<view class="reminder-btn" @tap="playReminderAudio">
								<text class="reminder-icon">🔊</text>
								<text class="reminder-text">播放处理提醒</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 结果展示区域 -->
				<view v-if="currentStep === 'result'" class="result-section">
					<view class="result-container">
						<view class="result-header">
							<text class="result-title">✨ AI预测结果</text>
							<text class="result-subtitle">这就是您20年后的样子！</text>
						</view>
						
						<view class="result-comparison">
							<view class="photo-compare">
								<view class="photo-item">
									<view class="photo-wrapper">
										<image :src="capturedImageUrl" class="compare-image" mode="aspectFit"></image>
										<text class="photo-label">现在的您</text>
									</view>
								</view>
								<view class="transform-arrow">
									<text class="arrow-icon">→</text>
									<text class="arrow-text">20年后</text>
								</view>
								<view class="photo-item">
									<view class="photo-wrapper">
										<image :src="predictedImageUrl" class="compare-image" mode="aspectFit"></image>
										<text class="photo-label">2044年的您</text>
									</view>
								</view>
							</view>
						</view>
						
						<view class="result-actions">
							<view class="action-btn" @tap="playHandoverAudio">
								<text class="action-icon">▶</text>
								<text class="action-text">播放交接语音</text>
							</view>
							<view class="action-btn primary" @tap="goToVoiceChat">
								<text class="action-icon">📞</text>
								<text class="action-text">与未来对话</text>
							</view>
							<view class="action-btn" @tap="downloadResult">
								<text class="action-icon">⬇</text>
								<text class="action-text">下载结果</text>
							</view>
							<view class="action-btn" @tap="shareResult">
								<text class="action-icon">📤</text>
								<text class="action-text">分享结果</text>
							</view>
							<view class="action-btn secondary" @tap="retakePhoto">
								<text class="action-icon">📷</text>
								<text class="action-text">重新拍照</text>
							</view>
							<view class="action-btn clear-all" @tap="clearAllAndRestart">
								<text class="action-icon">🔄</text>
								<text class="action-text">清空记录，重新开始</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部控制 -->
			<view class="bottom-controls">
				<view class="control-btn" @tap="playWelcomeAudio">
					<text class="control-icon">🔊</text>
					<text class="control-text">播放欢迎语音</text>
				</view>
				<view class="control-btn" @tap="showConfigModal">
					<text class="control-icon">⚙</text>
					<text class="control-text">用户配置</text>
				</view>
				<view class="control-btn" @tap="goBack">
					<text class="control-icon">←</text>
					<text class="control-text">返回对话</text>
				</view>
				<view class="control-btn" @tap="goHome">
					<text class="control-icon">🏠</text>
					<text class="control-text">返回方舟</text>
				</view>
			</view>
		</view>
		
		<!-- 用户信息配置模态框 - 重新设计 -->
		<view v-if="showConfig" class="config-modal">
			<view class="config-overlay" @tap="hideConfigModal"></view>
			<view class="config-container">
				<view class="config-header">
					<text class="config-title">👤 个人信息设置</text>
					<view class="config-close" @tap="hideConfigModal">
						<text class="close-text">×</text>
					</view>
				</view>

				<scroll-view class="config-content" scroll-y="true">
					<view v-if="showConfigTip" class="config-tip">
						<text class="tip-icon">ℹ</text>
						<text class="tip-text">首次使用需要设置性别和职业信息，以便AI更好地为您预测未来形象。</text>
					</view>

					<!-- 性别选择 - 使用按钮组 -->
					<view class="config-item">
						<text class="config-label">性别: <text class="required">*</text></text>
						<view class="gender-buttons">
							<view v-for="(option, index) in genderOptions.slice(1)" :key="index"
								  :class="['gender-btn', {active: genderIndex === index + 1}]"
								  @tap="selectGender(index + 1)"
								  @click="selectGender(index + 1)"
								  @touchstart="onGenderTouchStart"
								  @touchend="onGenderTouchEnd">
								<text class="btn-text">{{option}}</text>
								<text v-if="genderIndex === index + 1" class="btn-check">✓</text>
							</view>
						</view>
					</view>

					<!-- 职业输入 -->
					<view class="config-item">
						<text class="config-label">职业: <text class="required">*</text></text>
						<view class="input-wrapper">
							<!-- 方案1: input输入框 -->
							<input v-if="!useTextarea"
								   :value="userProfession"
								   class="config-input"
								   placeholder="请输入您的职业"
								   placeholder-style="color:#B2B5BE;font-size:28rpx"
								   maxlength="20"
								   type="text"
								   confirm-type="done"
								   cursor-spacing="50"
								   @input="onProfessionInput"
								   @focus="onInputFocus"
								   @blur="onInputBlur"
								   @confirm="onInputConfirm" />

							<!-- 方案2: textarea输入框 -->
							<textarea v-else
									  :value="userProfession"
									  class="config-textarea"
									  placeholder="请输入您的职业"
									  placeholder-style="color:#B2B5BE;font-size:28rpx"
									  maxlength="20"
									  auto-height
									  cursor-spacing="50"
									  @input="onProfessionInput"
									  @focus="onInputFocus"
									  @blur="onInputBlur"></textarea>
						</view>
						<view class="input-tip">
							<text class="tip-text">如：医生、教师、工程师、学生等</text>
						</view>
						<view class="debug-info" v-if="showDebugInfo">
							<text class="debug-text">当前值: {{userProfession}}</text>
							<view class="debug-buttons">
								<view class="debug-btn" @tap="testInput">测试输入</view>
								<view class="debug-btn" @tap="clearInput">清空</view>
								<view class="debug-btn" @tap="toggleInputType">{{useTextarea ? 'input' : 'textarea'}}</view>
								<view class="debug-btn" @tap="toggleDebug">{{showDebugInfo ? '隐藏' : '显示'}}调试</view>
							</view>
						</view>
					</view>
					<view class="config-description">
						<text class="desc-title">说明：</text>
						<text class="desc-item">• 性别信息用于AI算法选择合适的预测模型</text>
						<text class="desc-item">• 职业信息影响未来形象的气质和风格预测</text>
						<text class="desc-item">• 所有信息仅用于本次预测，不会上传服务器</text>
					</view>
				</scroll-view>

				<view class="config-footer">
					<view class="config-btn" @tap="saveConfig">
						<text class="btn-icon">💾</text>
						<text class="btn-text">保存配置</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误提示模态框 -->
		<view v-if="showError" class="modal" @tap="hideErrorModal">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">⚠ 提示</text>
					<view class="modal-close" @tap="hideErrorModal">
						<text class="close-icon">×</text>
					</view>
				</view>
				<view class="modal-body">
					<text class="error-message">{{errorMessage}}</text>
				</view>
				<view class="modal-footer">
					<view class="modal-btn" @tap="hideErrorModal">
						<text class="btn-icon">✓</text>
						<text class="btn-text">确定</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 页脚信息 -->
		<view class="footer">
			<text class="footer-text">AI变脸预测系统 v2049.1 | 基于深度学习技术 | 数据安全加密传输</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			canvasWidth: 375,
			canvasHeight: 667,
			currentStep: 'camera', // camera, preview, processing, result
			currentStatus: 'AI处理中...',
			showCamera: false,
			cameraReady: false,
			cameraStatusText: '正在启动摄像头...',
			capturedImageUrl: '',
			predictedImageUrl: '',
			progressPercent: 0,
			currentProcessStep: 0,
			processingSteps: [
				{ icon: '🔍', text: '面部识别' },
				{ icon: '🧠', text: 'AI分析' },
				{ icon: '✨', text: '预测生成' }
			],
			
			// 配置相关
			showConfig: false,
			showConfigTip: false,
			genderIndex: -1, // -1表示未选择，0开始表示具体选项
			genderOptions: ['请选择性别', '男', '女', '其他'],
			userProfession: '',
			showCustomGenderPicker: false, // 备用的自定义选择器
			showDebugInfo: true, // 调试信息开关
			useTextarea: false, // 是否使用textarea

			// 错误处理
			showError: false,
			errorMessage: '',
			
			// 定时器
			processingTimer: null,
			progressTimer: null
		}
	},
	onLoad() {
		console.log('=== 页面加载开始 ===');
		this.initCanvas();

		// 延迟执行，确保页面渲染完成
		setTimeout(() => {
			this.loadUserConfig();
			this.checkDialogueData();
			this.initCamera();

			// 强制显示调试信息
			this.showDebugInfo = true;

			// 如果5秒后还没有配置弹窗，强制显示
			setTimeout(() => {
				if (!this.showConfig && this.genderIndex <= 0) {
					console.log('强制显示配置弹窗');
					this.forceShowConfig();
				}
			}, 5000);
		}, 500);
	},
	onReady() {
		// 页面渲染完成
	},
	onUnload() {
		this.clearTimers();
	},
	methods: {
		// 初始化画布
		initCanvas() {
			const systemInfo = uni.getSystemInfoSync();
			this.canvasWidth = systemInfo.windowWidth;
			this.canvasHeight = systemInfo.windowHeight;
		},
		
		// 加载用户配置
		loadUserConfig() {
			console.log('=== 开始加载用户配置 ===');

			// 简化处理，直接显示配置弹窗
			this.showConfigTip = true;
			this.showConfig = true;
			this.genderIndex = -1;
			this.userProfession = '';

			console.log('强制显示配置弹窗 - showConfig:', this.showConfig, 'showConfigTip:', this.showConfigTip);
		},
		
		// 检查对话数据
		checkDialogueData() {
			const dialogueData = uni.getStorageSync('user_dialogue_data');
			if (!dialogueData || !dialogueData.name) {
				// 没有对话数据，返回对话页面
				uni.showModal({
					title: '提示',
					content: '请先完成时空对话',
					showCancel: false,
					success: () => {
						uni.navigateTo({
							url: '/pagesB/dreamark/dialogue'
						});
					}
				});
			}
		},
		
		// 初始化摄像头
		initCamera() {
			console.log('=== 开始初始化摄像头 ===');

			// 简化处理，直接启用摄像头
			this.showCamera = true;
			this.cameraReady = true;
			this.cameraStatusText = '摄像头已就绪';

			console.log('摄像头状态设置完成 - showCamera:', this.showCamera, 'cameraReady:', this.cameraReady);

			// 强制更新视图
			this.$forceUpdate();
		},
		
		// 摄像头就绪
		onCameraReady() {
			this.cameraReady = true;
			this.cameraStatusText = '摄像头已就绪';
			this.currentStatus = '准备拍照';
		},
		
		// 摄像头错误
		onCameraError(e) {
			console.error('摄像头错误:', e);
			this.showError = true;
			this.errorMessage = '摄像头启动失败，请检查设备权限设置。';
		},
		
		// 拍照
		capturePhoto() {
			if (!this.cameraReady) return;
			
			const ctx = uni.createCameraContext();
			ctx.takePhoto({
				quality: 'high',
				success: (res) => {
					this.capturedImageUrl = res.tempImagePath;
					this.currentStep = 'preview';
					this.currentStatus = '照片预览';
				},
				fail: (err) => {
					console.error('拍照失败:', err);
					this.showError = true;
					this.errorMessage = '拍照失败，请重试。';
				}
			});
		},
		
		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album'],
				success: (res) => {
					this.capturedImageUrl = res.tempFilePaths[0];
					this.currentStep = 'preview';
					this.currentStatus = '照片预览';
				},
				fail: (err) => {
					console.error('选择图片失败:', err);
				}
			});
		},
		
		// 开始处理
		startProcessing() {
			this.currentStep = 'processing';
			this.currentStatus = 'AI分析中';
			this.progressPercent = 0;
			this.currentProcessStep = 0;
			
			// 模拟处理过程
			this.simulateProcessing();
		},
		
		// 模拟处理过程
		simulateProcessing() {
			let progress = 0;
			let stepIndex = 0;
			
			this.progressTimer = setInterval(() => {
				progress += Math.random() * 10 + 5;
				if (progress > 100) progress = 100;
				
				this.progressPercent = progress;
				
				// 更新处理步骤
				if (progress > 30 && stepIndex === 0) {
					this.currentProcessStep = 1;
					stepIndex = 1;
				} else if (progress > 70 && stepIndex === 1) {
					this.currentProcessStep = 2;
					stepIndex = 2;
				}
				
				if (progress >= 100) {
					clearInterval(this.progressTimer);
					setTimeout(() => {
						this.completeProcessing();
					}, 1000);
				}
			}, 200);
		},
		
		// 完成处理
		completeProcessing() {
			// 这里应该调用真实的AI接口
			// 暂时使用模拟结果
			this.predictedImageUrl = this.capturedImageUrl; // 临时使用原图
			this.currentStep = 'result';
			this.currentStatus = '预测完成';
			
			// 保存预测结果
			try {
				uni.setStorageSync('predicted_image_url', this.predictedImageUrl);
			} catch (e) {
				console.error('保存预测结果失败:', e);
			}
		},
		
		// 重新拍照
		retakePhoto() {
			this.currentStep = 'camera';
			this.currentStatus = '准备拍照';
			this.capturedImageUrl = '';
			this.predictedImageUrl = '';
		},
		
		// 跳转到语音对话
		goToVoiceChat() {
			uni.navigateTo({
				url: '/pagesB/dreamark/voice-chat'
			});
		},
		
		// 下载结果
		downloadResult() {
			if (!this.predictedImageUrl) return;
			
			uni.saveImageToPhotosAlbum({
				filePath: this.predictedImageUrl,
				success: () => {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
				},
				fail: () => {
					uni.showToast({
						title: '保存失败',
						icon: 'error'
					});
				}
			});
		},
		
		// 分享结果
		shareResult() {
			// #ifdef MP-WEIXIN
			uni.showShareMenu({
				withShareTicket: true
			});
			// #endif
			
			// #ifndef MP-WEIXIN
			uni.showToast({
				title: '分享功能开发中',
				icon: 'none'
			});
			// #endif
		},
		
		// 清空所有数据重新开始
		clearAllAndRestart() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空所有记录重新开始吗？此操作不可恢复。',
				success: (res) => {
					if (res.confirm) {
						try {
							uni.clearStorageSync();
							uni.showToast({
								title: '已清空，重新开始',
								icon: 'success'
							});
							setTimeout(() => {
								uni.navigateTo({
									url: '/pagesB/dreamark/dialogue'
								});
							}, 1000);
						} catch (e) {
							uni.showToast({
								title: '清空失败',
								icon: 'error'
							});
						}
					}
				}
			});
		},
		
		// 显示配置模态框
		showConfigModal() {
			this.showConfig = true;
		},
		
		// 隐藏配置模态框
		hideConfigModal() {
			// 如果是首次使用且没有设置性别，不允许关闭
			if (this.showConfigTip && (this.genderIndex <= 0)) {
				uni.showToast({
					title: '请先选择性别信息',
					icon: 'none'
				});
				return;
			}
			this.showConfig = false;
			this.showConfigTip = false;

			// 重新启用摄像头（如果之前被暂停）
			this.$nextTick(() => {
				if (!this.showCamera && this.cameraReady) {
					this.showCamera = true;
				}
			});
		},
		
		// 性别选择改变
		onGenderChange(e) {
			this.genderIndex = e.detail.value;
			console.log('性别选择改变:', this.genderIndex, this.genderOptions[this.genderIndex]);

			// 如果选择了"请选择性别"，提示用户
			if (this.genderIndex === 0) {
				uni.showToast({
					title: '请选择具体的性别',
					icon: 'none',
					duration: 1500
				});
			}
		},

		// 自定义性别选择
		selectGender(index) {
			console.log('点击性别选择，索引:', index);
			this.genderIndex = index;
			console.log('性别选择完成:', this.genderIndex, this.genderOptions[this.genderIndex]);

			// 添加触觉反馈（如果支持）
			// #ifdef APP-PLUS
			if (uni.vibrateShort) {
				uni.vibrateShort();
			}
			// #endif

			// 显示选择反馈
			uni.showToast({
				title: `已选择：${this.genderOptions[this.genderIndex]}`,
				icon: 'none',
				duration: 1000
			});
		},

		// 职业输入处理
		onProfessionInput(e) {
			console.log('输入事件触发:', e.detail.value);
			this.userProfession = e.detail.value;
			this.$forceUpdate(); // 强制更新视图
		},

		// 输入确认
		onInputConfirm(e) {
			console.log('输入确认:', e.detail.value);
			this.userProfession = e.detail.value;
		},

		// 输入框获得焦点
		onInputFocus(e) {
			console.log('职业输入框获得焦点');
			// 确保输入框可见
			this.$nextTick(() => {
				// 可以在这里添加滚动到输入框的逻辑
			});
		},

		// 输入框失去焦点
		onInputBlur(e) {
			console.log('职业输入框失去焦点，当前值:', this.userProfession);
		},

		// 测试输入功能
		testInput() {
			this.userProfession = '测试职业';
			console.log('测试输入，设置值为:', this.userProfession);
			uni.showToast({
				title: '已设置测试值',
				icon: 'success'
			});
		},

		// 清空输入
		clearInput() {
			this.userProfession = '';
			console.log('清空输入');
			uni.showToast({
				title: '已清空',
				icon: 'success'
			});
		},

		// 切换调试模式
		toggleDebug() {
			this.showDebugInfo = !this.showDebugInfo;
		},

		// 切换输入框类型
		toggleInputType() {
			this.useTextarea = !this.useTextarea;
			uni.showToast({
				title: `已切换到${this.useTextarea ? 'textarea' : 'input'}`,
				icon: 'none'
			});
		},

		// 性别按钮触摸开始
		onGenderTouchStart(e) {
			console.log('性别按钮触摸开始');
			// 可以在这里添加触摸开始的视觉反馈
		},

		// 性别按钮触摸结束
		onGenderTouchEnd(e) {
			console.log('性别按钮触摸结束');
			// 触摸结束时的处理
		},
		
		// 保存配置
		saveConfig() {
			// 验证性别选择
			if (this.genderIndex <= 0) {
				uni.showToast({
					title: '请选择性别',
					icon: 'none'
				});
				return;
			}

			// 验证职业输入
			if (!this.userProfession.trim()) {
				uni.showToast({
					title: '请输入职业信息',
					icon: 'none'
				});
				return;
			}

			try {
				const gender = this.genderOptions[this.genderIndex];
				uni.setStorageSync('user_gender', gender);
				uni.setStorageSync('user_profession', this.userProfession.trim());

				uni.showToast({
					title: '配置已保存',
					icon: 'success'
				});

				this.hideConfigModal();
			} catch (e) {
				uni.showToast({
					title: '保存失败',
					icon: 'error'
				});
			}
		},
		
		// 隐藏错误模态框
		hideErrorModal() {
			this.showError = false;
			this.errorMessage = '';
		},
		
		// 播放欢迎语音
		playWelcomeAudio() {
			uni.showToast({
				title: '播放欢迎语音',
				icon: 'none'
			});
		},
		
		// 播放提醒音频
		playReminderAudio() {
			uni.showToast({
				title: '播放处理提醒',
				icon: 'none'
			});
		},
		
		// 播放交接语音
		playHandoverAudio() {
			uni.showToast({
				title: '播放交接语音',
				icon: 'none'
			});
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		},
		
		// 返回首页
		goHome() {
			uni.navigateTo({
				url: '/pagesB/dreamark/index'
			});
		},
		
		// 清除定时器
		clearTimers() {
			if (this.processingTimer) {
				clearInterval(this.processingTimer);
				this.processingTimer = null;
			}
			if (this.progressTimer) {
				clearInterval(this.progressTimer);
				this.progressTimer = null;
			}
		},

		// 强制显示配置弹窗（调试用）
		forceShowConfig() {
			console.log('=== 强制显示配置弹窗 ===');
			this.showConfig = true;
			this.showConfigTip = true;
			this.$forceUpdate();
		},

		// 切换摄像头状态（调试用）
		toggleCamera() {
			console.log('=== 切换摄像头状态 ===');
			this.showCamera = !this.showCamera;
			console.log('摄像头状态:', this.showCamera);
			this.$forceUpdate();
		},

		// 测试弹窗（调试用）
		testAlert() {
			console.log('=== 测试弹窗被点击 ===');
			uni.showModal({
				title: '测试',
				content: '这是一个测试弹窗，如果你能看到这个，说明点击事件正常工作。',
				success: (res) => {
					console.log('弹窗结果:', res);
					if (res.confirm) {
						uni.showToast({
							title: '测试成功',
							icon: 'success'
						});
					}
				}
			});
		}
	}
}
</script>

<style>
/* 基础样式 */
page {
	background: #0a0a2a;
	color: #ffffff;
	font-family: 'PingFang SC', sans-serif;
}

/* 移除移动端默认点击效果 */
* {
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	user-select: none;
}

/* 允许文本选择的元素 */
input, textarea {
	-webkit-user-select: auto;
	user-select: auto;
}

.container {
	position: relative;
	min-height: 100vh;
	background: linear-gradient(135deg, #0a0a2a 0%, #1a1a3a 50%, #2a2a4a 100%);
	overflow: hidden;
}

/* 粒子画布 */
.particles-canvas {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1;
	pointer-events: none;
}

/* 装饰背景 */
.bg-grid {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: 
		linear-gradient(rgba(0, 247, 255, 0.1) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 247, 255, 0.1) 1px, transparent 1px);
	background-size: 50px 50px;
	z-index: 2;
	animation: gridMove 20s linear infinite;
}

.bg-circles {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 2;
}

@keyframes gridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(50px, 50px); }
}

/* AI变脸预测控制台 */
.camera-console {
	position: relative;
	z-index: 10;
	padding: 40rpx;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: linear-gradient(135deg, rgba(10, 10, 42, 0.95), rgba(26, 26, 58, 0.9));
	backdrop-filter: blur(20rpx);
}

/* 标题区域 */
.header-section {
	display: flex;
	align-items: center;
	gap: 30rpx;
	margin-bottom: 40rpx;
	padding: 30rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
}

.ai-logo {
	position: relative;
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.logo-glow {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	right: -10rpx;
	bottom: -10rpx;
	background: radial-gradient(circle, rgba(0, 247, 255, 0.3), transparent);
	border-radius: 50%;
	animation: logoGlow 2s infinite;
}

@keyframes logoGlow {
	0%, 100% { opacity: 0.5; }
	50% { opacity: 1; }
}

.logo-icon {
	font-size: 48rpx;
	z-index: 2;
}

.title-info {
	flex: 1;
}

.main-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 10rpx;
}

.subtitle {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
	line-height: 1.4;
}

.time-display {
	text-align: right;
}

.year {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 5rpx;
}

.status {
	display: block;
	font-size: 20rpx;
	color: #7df9ff;
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

/* 摄像头区域 */
.camera-section {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.camera-container {
	position: relative;
	flex: 1;
	background: rgba(0, 0, 0, 0.5);
	border: 2px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	overflow: hidden;
	margin-bottom: 40rpx;
}

.camera-preview {
	width: 100%;
	height: 100%;
	min-height: 400rpx;
	/* 确保摄像头不会覆盖模态框 */
	position: relative;
	z-index: 1;
}

/* 摄像头占位符 */
.camera-placeholder {
	width: 100%;
	height: 100%;
	min-height: 400rpx;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 20rpx;
	border-radius: 20rpx;
}

.placeholder-text {
	font-size: 32rpx;
	color: #7df9ff;
	font-weight: bold;
}

.placeholder-desc {
	font-size: 24rpx;
	color: #7df9ff;
	opacity: 0.7;
}

/* 拍照框架 */
.photo-frame {
	position: absolute;
	top: 50rpx;
	left: 50rpx;
	right: 50rpx;
	bottom: 50rpx;
	pointer-events: none;
}

.frame-corner {
	position: absolute;
	width: 40rpx;
	height: 40rpx;
	border: 3px solid #00f7ff;
}

.top-left {
	top: 0;
	left: 0;
	border-right: none;
	border-bottom: none;
}

.top-right {
	top: 0;
	right: 0;
	border-left: none;
	border-bottom: none;
}

.bottom-left {
	bottom: 0;
	left: 0;
	border-right: none;
	border-top: none;
}

.bottom-right {
	bottom: 0;
	right: 0;
	border-left: none;
	border-top: none;
}

.scan-line {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: linear-gradient(90deg, transparent, #00f7ff, transparent);
	animation: scanMove 2s linear infinite;
}

@keyframes scanMove {
	0% { transform: translateY(0); }
	100% { transform: translateY(400rpx); }
}

/* 摄像头状态 */
.camera-status {
	position: absolute;
	bottom: 20rpx;
	left: 20rpx;
	right: 20rpx;
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 15rpx;
	background: rgba(0, 0, 0, 0.7);
	padding: 15rpx 25rpx;
	border-radius: 50rpx;
	backdrop-filter: blur(10px);
}

.status-dot {
	width: 16rpx;
	height: 16rpx;
	background: #ff4444;
	border-radius: 50%;
	transition: all 0.3s ease;
}

.status-dot.active {
	background: #00ff80;
	box-shadow: 0 0 10rpx #00ff80;
}

.status-text {
	font-size: 24rpx;
	color: #7df9ff;
}

/* 拍照控制 */
.photo-controls {
	display: flex;
	gap: 30rpx;
	justify-content: center;
	margin-bottom: 40rpx;
}

.capture-btn,
.upload-btn {
	position: relative;
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	color: #fff;
	padding: 35rpx 55rpx;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	gap: 25rpx;
	transition: all 0.3s ease;
	overflow: hidden;
	box-shadow:
		0 15rpx 35rpx rgba(0, 247, 255, 0.3),
		0 5rpx 15rpx rgba(189, 0, 255, 0.2),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
}

.capture-btn:active,
.upload-btn:active {
	transform: translateY(2rpx);
	box-shadow:
		0 10rpx 25rpx rgba(0, 247, 255, 0.4),
		0 3rpx 10rpx rgba(189, 0, 255, 0.3);
}

.capture-btn.disabled {
	opacity: 0.5;
	background: #666;
}

.btn-glow {
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
	transform: rotate(45deg);
	transition: all 0.5s;
	opacity: 0;
}

.capture-btn:active .btn-glow,
.upload-btn:active .btn-glow {
	animation: shimmer 0.8s ease-in-out;
	opacity: 1;
}

@keyframes shimmer {
	0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
	100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.btn-icon {
	font-size: 32rpx;
}

.btn-text {
	font-size: 28rpx;
	font-weight: bold;
}

/* 预览区域 */
.preview-section {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.preview-container {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.photo-preview {
	position: relative;
	flex: 1;
	background: rgba(0, 0, 0, 0.5);
	border: 2px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	overflow: hidden;
	margin-bottom: 40rpx;
}

.preview-image {
	width: 100%;
	height: 100%;
	min-height: 400rpx;
}

.preview-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}

.analysis-grid {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image: 
		linear-gradient(rgba(0, 247, 255, 0.2) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 247, 255, 0.2) 1px, transparent 1px);
	background-size: 30px 30px;
	animation: analysisMove 3s linear infinite;
}

@keyframes analysisMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(30px, 30px); }
}

.analysis-points {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 200rpx;
	height: 200rpx;
	border: 2px solid #00f7ff;
	border-radius: 50%;
	animation: pointsScan 2s linear infinite;
}

@keyframes pointsScan {
	0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
	100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
}

/* 处理控制 */
.process-controls {
	text-align: center;
}

.process-btn {
	position: relative;
	background: linear-gradient(45deg, #ff00c8, #bd00ff);
	color: #fff;
	padding: 30rpx 60rpx;
	border-radius: 50rpx;
	display: inline-flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.process-info {
	margin-bottom: 40rpx;
}

.info-text {
	font-size: 24rpx;
	color: #7df9ff;
	line-height: 1.4;
}

/* 处理中状态 */
.processing-section {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
}

.processing-container {
	text-align: center;
	padding: 60rpx;
}

.ai-brain {
	position: relative;
	width: 200rpx;
	height: 200rpx;
	margin: 0 auto 60rpx;
}

.brain-core {
	width: 100%;
	height: 100%;
	background: radial-gradient(circle, rgba(0, 247, 255, 0.3), rgba(189, 0, 255, 0.1));
	border: 3px solid #00f7ff;
	border-radius: 50%;
	animation: brainPulse 2s infinite;
}

@keyframes brainPulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}

.neural-waves {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 150rpx;
	height: 150rpx;
	border: 2px solid rgba(255, 0, 200, 0.5);
	border-radius: 50%;
	animation: waveExpand 1.5s infinite;
}

@keyframes waveExpand {
	0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
	100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

.data-streams {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.data-streams::before,
.data-streams::after {
	content: '';
	position: absolute;
	width: 4rpx;
	height: 40rpx;
	background: linear-gradient(to bottom, #00f7ff, transparent);
	animation: dataFlow 1s infinite;
}

.data-streams::before {
	top: 20rpx;
	left: 50%;
	transform: translateX(-50%);
}

.data-streams::after {
	bottom: 20rpx;
	right: 50%;
	transform: translateX(50%);
	animation-delay: 0.5s;
}

@keyframes dataFlow {
	0% { opacity: 0; transform: translateY(-20rpx); }
	50% { opacity: 1; }
	100% { opacity: 0; transform: translateY(20rpx); }
}

.process-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 40rpx;
}

/* 进度条 */
.progress-bar {
	width: 100%;
	height: 8rpx;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 10rpx;
	overflow: hidden;
	margin-bottom: 40rpx;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #00f7ff, #bd00ff);
	border-radius: 10rpx;
	transition: width 0.3s ease;
}

/* 处理步骤 */
.processing-steps {
	display: flex;
	justify-content: space-around;
	margin-bottom: 40rpx;
}

.step {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
	opacity: 0.5;
	transition: all 0.3s ease;
}

.step.active {
	opacity: 1;
	transform: scale(1.1);
}

.step-icon {
	font-size: 48rpx;
}

.step-text {
	font-size: 24rpx;
	color: #7df9ff;
}

/* 提醒按钮 */
.reminder-btn {
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid #00f7ff;
	border-radius: 50rpx;
	padding: 20rpx 40rpx;
	display: inline-flex;
	align-items: center;
	gap: 15rpx;
}

.reminder-icon {
	font-size: 24rpx;
}

.reminder-text {
	font-size: 24rpx;
	color: #7df9ff;
}

/* 结果展示区域 */
.result-section {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.result-container {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.result-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.result-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 15rpx;
}

.result-subtitle {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
}

/* 结果对比 */
.result-comparison {
	flex: 1;
	margin-bottom: 40rpx;
}

.photo-compare {
	display: flex;
	align-items: center;
	gap: 30rpx;
	padding: 40rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
}

.photo-item {
	flex: 1;
}

.photo-wrapper {
	position: relative;
	background: rgba(0, 0, 0, 0.5);
	border: 2px solid rgba(0, 247, 255, 0.3);
	border-radius: 15rpx;
	overflow: hidden;
}

.compare-image {
	width: 100%;
	height: 300rpx;
}

.photo-label {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.8);
	color: #7df9ff;
	text-align: center;
	padding: 15rpx;
	font-size: 24rpx;
}

.transform-arrow {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
}

.arrow-icon {
	font-size: 48rpx;
	color: #00f7ff;
}

.arrow-text {
	font-size: 20rpx;
	color: #7df9ff;
}

/* 结果操作 */
.result-actions {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	min-width: 200rpx;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid #00f7ff;
	border-radius: 50rpx;
	padding: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	transition: all 0.3s ease;
}

.action-btn:active {
	transform: scale(0.95);
	background: rgba(0, 247, 255, 0.2);
}

.action-btn.primary {
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	border-color: transparent;
}

.action-btn.secondary {
	background: rgba(255, 165, 0, 0.1);
	border-color: #ffa500;
}

.action-btn.clear-all {
	background: rgba(255, 68, 68, 0.1);
	border-color: #ff4444;
	flex-basis: 100%;
	margin-top: 20rpx;
}

.action-icon {
	font-size: 24rpx;
}

.action-text {
	font-size: 24rpx;
	color: #7df9ff;
}

.action-btn.primary .action-text {
	color: #fff;
}

.action-btn.secondary .action-text {
	color: #ffa500;
}

.action-btn.clear-all .action-text {
	color: #ff4444;
}

/* 底部控制 */
.bottom-controls {
	display: flex;
	gap: 15rpx;
	margin-top: 40rpx;
}

.control-btn {
	flex: 1;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 30rpx;
	padding: 20rpx 15rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	transition: all 0.3s ease;
}

.control-btn:active {
	transform: scale(0.95);
	background: rgba(0, 247, 255, 0.2);
}

.control-icon {
	font-size: 24rpx;
}

.control-text {
	font-size: 20rpx;
	color: #7df9ff;
}

/* 新的配置模态框 */
.config-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 999999; /* 提高z-index确保在摄像头之上 */
	display: flex;
	flex-direction: column;
}

.config-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(5px);
}

.config-container {
	position: relative;
	z-index: 100000;
	background: linear-gradient(135deg, #1a1a3a, #2a2a4a);
	margin: 80rpx 40rpx;
	border-radius: 20rpx;
	border: 2px solid rgba(0, 247, 255, 0.3);
	display: flex;
	flex-direction: column;
	max-height: calc(100vh - 160rpx);
	overflow: hidden;
}

.config-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1px solid rgba(0, 247, 255, 0.2);
	flex-shrink: 0;
}

.config-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
}

.config-close {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 68, 68, 0.1);
	border: 1px solid #ff4444;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.close-text {
	font-size: 32rpx;
	color: #ff4444;
}

.config-content {
	flex: 1;
	padding: 30rpx;
	overflow-y: auto;
}

/* 配置提示 */
.config-tip {
	display: flex;
	align-items: flex-start;
	gap: 15rpx;
	background: rgba(255, 0, 200, 0.1);
	border: 1px solid #ff00c8;
	border-radius: 10rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;
}

.tip-icon {
	font-size: 24rpx;
	color: #ff00c8;
	margin-top: 2rpx;
}

.tip-text {
	flex: 1;
	font-size: 24rpx;
	color: #ff00c8;
	line-height: 1.4;
}

/* 配置项 */
.config-item {
	margin-bottom: 40rpx;
}

.config-label {
	display: block;
	font-size: 28rpx;
	color: #7df9ff;
	margin-bottom: 20rpx;
}

/* 性别按钮组 */
.gender-buttons {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
}

.gender-btn {
	flex: 1;
	min-width: 150rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 2px solid rgba(0, 247, 255, 0.3);
	border-radius: 50rpx;
	padding: 25rpx 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	transition: all 0.3s ease;
	/* 移除默认的点击效果 */
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	user-select: none;
	outline: none;
	/* 确保可点击 */
	cursor: pointer;
	position: relative;
	overflow: hidden;
}

.gender-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 247, 255, 0.1);
	opacity: 0;
	transition: opacity 0.2s ease;
}

.gender-btn:active::before {
	opacity: 1;
}

.gender-btn:active {
	transform: scale(0.95);
	border-color: #00f7ff;
}

.gender-btn.active {
	background: rgba(0, 247, 255, 0.2);
	border-color: #00f7ff;
	box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.3);
}

.gender-btn .btn-text {
	font-size: 28rpx;
	color: #7df9ff;
}

.gender-btn.active .btn-text {
	color: #00f7ff;
	font-weight: bold;
}

.gender-btn .btn-check {
	font-size: 24rpx;
	color: #00f7ff;
	font-weight: bold;
}

/* 输入框包装器 */
.input-wrapper {
	position: relative;
}

.config-input {
	width: 100%;
	background: rgba(0, 0, 0, 0.5);
	border: 2px solid rgba(0, 247, 255, 0.5);
	border-radius: 15rpx;
	padding: 25rpx;
	font-size: 28rpx;
	color: #fff;
	box-sizing: border-box;
	transition: all 0.3s ease;
	min-height: 80rpx;
	line-height: 1.4;
	/* 确保输入框可以获得焦点 */
	position: relative;
	z-index: 1;
}

.config-input:focus {
	border-color: #00f7ff;
	background: rgba(0, 247, 255, 0.15);
	box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.3);
	outline: none;
}

/* textarea样式 */
.config-textarea {
	width: 100%;
	background: rgba(0, 0, 0, 0.5);
	border: 2px solid rgba(0, 247, 255, 0.5);
	border-radius: 15rpx;
	padding: 25rpx;
	font-size: 28rpx;
	color: #fff;
	box-sizing: border-box;
	transition: all 0.3s ease;
	min-height: 80rpx;
	line-height: 1.4;
	resize: none;
	position: relative;
	z-index: 1;
}

.config-textarea:focus {
	border-color: #00f7ff;
	background: rgba(0, 247, 255, 0.15);
	box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.3);
	outline: none;
}

.input-tip {
	margin-top: 10rpx;
}

.input-tip .tip-text {
	font-size: 22rpx;
	color: #7df9ff;
	opacity: 0.7;
}

/* 调试信息 */
.debug-info {
	margin-top: 10rpx;
	padding: 10rpx;
	background: rgba(255, 0, 0, 0.1);
	border: 1px solid rgba(255, 0, 0, 0.3);
	border-radius: 8rpx;
}

.debug-text {
	font-size: 20rpx;
	color: #ff6666;
	margin-bottom: 10rpx;
}

.debug-buttons {
	display: flex;
	gap: 10rpx;
	flex-wrap: wrap;
}

.debug-btn {
	background: rgba(255, 0, 0, 0.2);
	border: 1px solid #ff6666;
	border-radius: 20rpx;
	padding: 8rpx 15rpx;
	font-size: 18rpx;
	color: #ff6666;
	transition: all 0.3s ease;
}

.debug-btn:active {
	transform: scale(0.95);
	background: rgba(255, 0, 0, 0.3);
}

.required {
	color: #ff4444;
	font-weight: bold;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #fff;
	transition: all 0.3s ease;
	position: relative;
	z-index: 10001;
}

/* 旧样式已删除，使用新的config-modal样式 */

.gender-option {
	flex: 1;
	min-width: 150rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 2px solid rgba(0, 247, 255, 0.3);
	border-radius: 50rpx;
	padding: 20rpx 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	transition: all 0.3s ease;
	position: relative;
}

.gender-option:active {
	transform: scale(0.95);
}

.gender-option.selected {
	background: rgba(0, 247, 255, 0.2);
	border-color: #00f7ff;
	box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.3);
}

.option-text {
	font-size: 28rpx;
	color: #7df9ff;
}

.gender-option.selected .option-text {
	color: #00f7ff;
	font-weight: bold;
}

.option-check {
	font-size: 24rpx;
	color: #00f7ff;
	font-weight: bold;
}

/* 选择器切换按钮 */
.picker-switch {
	text-align: center;
	margin-top: 15rpx;
}

.switch-text {
	font-size: 22rpx;
	color: #7df9ff;
	opacity: 0.7;
	text-decoration: underline;
}

.form-input {
	width: 100%;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #fff;
	box-sizing: border-box;
	transition: all 0.3s ease;
}

.form-input:focus {
	border-color: #00f7ff;
	background: rgba(0, 247, 255, 0.1);
	box-shadow: 0 0 20rpx rgba(0, 247, 255, 0.2);
}

/* 表单描述 */
.form-description {
	font-size: 24rpx;
	color: #7df9ff;
	line-height: 1.4;
}

.desc-title {
	display: block;
	font-weight: bold;
	margin-bottom: 15rpx;
}

.desc-item {
	display: block;
	margin-bottom: 10rpx;
}

/* 配置描述 */
.config-description {
	margin-top: 30rpx;
	padding: 20rpx;
	background: rgba(0, 247, 255, 0.05);
	border: 1px solid rgba(0, 247, 255, 0.2);
	border-radius: 15rpx;
}

.desc-title {
	display: block;
	font-size: 26rpx;
	color: #00f7ff;
	font-weight: bold;
	margin-bottom: 15rpx;
}

.desc-item {
	display: block;
	font-size: 22rpx;
	color: #7df9ff;
	line-height: 1.5;
	margin-bottom: 8rpx;
}

/* 配置底部 */
.config-footer {
	padding: 30rpx;
	border-top: 1px solid rgba(0, 247, 255, 0.2);
	flex-shrink: 0;
}

.config-btn {
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	color: #fff;
	padding: 25rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 28rpx;
	font-weight: bold;
	transition: all 0.3s ease;
}

.config-btn:active {
	transform: scale(0.95);
}

/* 错误消息 */
.error-message {
	font-size: 28rpx;
	color: #7df9ff;
	line-height: 1.5;
}

/* 页脚 */
.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx;
	background: rgba(0, 0, 0, 0.8);
	border-top: 1px solid rgba(0, 247, 255, 0.3);
	z-index: 100;
}

.footer-text {
	text-align: center;
	font-size: 20rpx;
	color: #7df9ff;
	line-height: 1.4;
}

/* 调试控制 */
.debug-controls {
	position: fixed;
	top: 100rpx;
	right: 20rpx;
	background: rgba(255, 0, 0, 0.1);
	border: 1px solid rgba(255, 0, 0, 0.3);
	border-radius: 10rpx;
	padding: 20rpx;
	z-index: 10000;
	max-width: 200rpx;
}

.debug-btn {
	background: rgba(255, 0, 0, 0.2) !important;
	border: 1px solid #ff6666 !important;
	border-radius: 20rpx !important;
	padding: 10rpx 20rpx !important;
	margin-bottom: 10rpx;
	font-size: 20rpx !important;
	color: #ff6666 !important;
	text-align: center;
	width: 100%;
	line-height: 1.2;
}

.debug-btn::after {
	border: none !important;
}

.debug-info-text {
	font-size: 18rpx;
	color: #ff6666;
	margin-top: 10rpx;
}

.debug-info-text text {
	display: block;
	margin-bottom: 5rpx;
}
</style>
