<template>
	<view>
		<block v-if="isload">
			<view class="navigation">
				<img :src="pre_url+'/static/img/diylight_n1.png'" class="navigation_item" alt="" @tap="goback">
				<img :src="pre_url+'/static/img/diylight_n2.png'" class="navigation_item" alt="" @tap="goto"
					:data-url="'/shopPackage/shop/cart'">
				<img :src="pre_url+'/static/img/diylight_n3.png'" class="navigation_item" alt="" @tap="uploadImg()">
				<img :src="pre_url+'/static/img/diylight_n4.png'" class="navigation_item" alt="" @tap="draw">
				<img :src="pre_url+'/static/img/diylight_n5.png'" class="navigation_item" alt="">
			</view>
			<movable-area>
				<movable-view v-for="(item,index) in dataList" animation='false' damping='100000' :disabled='item.state'
					:style="'height:'+item.height+'rpx'+';'+'width:'+item.width+'rpx'" :key="index" :x="item.x"
					:y="item.y" direction="all" class="move_module" @change="onChange($event,index)">
					<view class="move_item" :style="'transform:rotate('+item.rotate+'deg'+');'"
						@click="moveClick(index)" :class="item.active?'move_active':''">
						<img :src="item.url[item.lightIndex]" class="move_image" alt="">
						<view v-if="item.active" @touchstart='spinStart($event,index)'
							@touchmove='spinMove($event,index)' @touchend='spinEnd($event,index)' class="move_spin">
							<img :src="pre_url+'/static/img/diylight_spin.png'" alt="">
						</view>
						<view v-if="item.active" class="move_close" @click="moveDelete(index)">
							<img :src="pre_url+'/static/img/diylight_close.png'" alt="">
						</view>
						<view v-if="item.active" @touchstart='sizeStart($event,index)'
							@touchmove='sizeMove($event,index)' @touchend='sizeEnd($event,index)' class="move_size">
							<img :src="pre_url+'/static/img/diylight_size.png'" alt="">
						</view>
					</view>
				</movable-view>
			</movable-area>
			<view class="table" :class="menuindex>-1?'tabbarbot':'notabbarbot'">
				<view @click="sceneClick" class="table_item">
					<img class="table_icon" :src="pre_url+'/static/img/diylight_image.png'" alt="">更换场景
				</view>
				<view @click="lightClick" class="table_item">
					<img class="table_icon" :src="pre_url+'/static/img/diylight_lamp.png'" alt="">选择灯具
				</view>
				<view v-if="switchState" @click="switchClick" class="table_item">
					<img class="table_icon" :src="pre_url+'/static/img/diylight_closeL.png'" alt="">关灯
				</view>
				<view v-if="!switchState" @click="switchClick" class="table_item">
					<img class="table_icon" :src="pre_url+'/static/img/diylight_openL.png'" alt="">开灯
				</view>
			</view>
			<view v-if="!switchState" class="page_cut"></view>
			<view v-if="sceneState" class="alert">
				<view @click="sceneClick" class="alert_hide"></view>
				<view class="alert_module" :class="menuindex>-1?'alert_opt':''">
					<view class="alert_head">
						<view class="alert_btn" @tap="uploadImg">
							+上传场景
						</view>
						<img @click="sceneClick" class="alert_close" :src="pre_url+'/static/img/diylight_closeA.png'"
							alt="">
					</view>
					<scroll-view scroll-y="true" class="alert_content">
						<view v-for="(item,index) in sceneList" v-if="sceneList.length" :key="index"
							@click="sceneChoose(item)" class="alert_item">
							<img :src="item" mode="widthFix" alt="">
						</view>
						<nodata text="没有查找到相关商品" type='small' v-if="!sceneList.length"></nodata>
					</scroll-view>
				</view>
			</view>
			<view v-if="lightState" class="alert">
				<view @click="lightClick" class="alert_hide"></view>
				<view class="alert_module" :class="menuindex>-1?'alert_opt':''">
					<view class="alert_title">
						<text>选择灯具</text>
						<img @click="lightClick" class="alert_close" :src="pre_url+'/static/img/diylight_closeA.png'"
							alt="">
					</view>
					<scroll-view scroll-x="true" class="alert_table">
						<view v-for="(item,index) in tableList" :key="index" @click="tableClick(index)"
							:class="tableIndex==index?'alert_active':''">
							{{item.name}}
						</view>
					</scroll-view>
					<scroll-view scroll-y="true" class="alert_content">
						<view v-for="(item,index) in lightList" :key="index" @click="lightChoose(item)"
							class="alert_item">
							<img :src="item.url[0]" mode="widthFix" alt="">
						</view>
						<nodata text="没有查找到相关商品" type='small' v-if="!lightList.length"></nodata>
					</scroll-view>
				</view>
			</view>
			<img class="scene" @click="areaClick" :src="scene" mode="widthFix" alt="">
			<view v-for="(item,index) in dataList" v-if="item.active" class="class"
				:class="menuindex>-1?'class_opt':''">
				<img v-for="(itemS,indexS) in item.url" :src="itemS" @click="lightType(index,indexS)"
					:class="item.lightIndex==indexS?'class_item class_active':'class_item'" alt="" />
			</view>
			<view class="cart" v-for="(item,index) in dataList" :key="index" v-if="item.real" @tap="goto"
				:data-url="'/shopPackage/shop/product?id='+item.id">
				<img :src="item.realUrl" class="cart_item" alt="">
				<view class="cart_title">加购物车</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
		<canvas canvas-id="myCanvas" id="myCanvas"></canvas>
	</view>
</template>
<script>
	var app = getApp();
	export default {
		data() {
			return {
				pre_url: app.globalData.pre_url,
				opt: {},
				loading: false,
				isload: true,
				menuindex: -1,

				scene: "https://s1.ax1x.com/2022/05/13/OsmCOf.png",
				tableList: [],
				dataList: [],
				tableIndex: 0,
				switchState: true,
				sceneState: false,
				lightState: false,
				sceneList: [],
				lightList: [],
				changeStart: '',
				sizeAry: [],
				pagenum: 1
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onReachBottom: function() {},
		methods: {
			draw() {
				uni.getSystemInfo({
					success: (res) => {
						let heightPage = res.screenHeight || res.windowHeight;
						let widthPage = res.screenWidth || res.windowWidth;
						if (this.menuindex > -1) {
							heightPage = heightPage - 61
						}
						let currentData = ''
						for(let i=0;i<this.dataList.length;i++){
							if(this.dataList[i].real){
								currentData = this.dataList[i]
							}
						}
						const ctx = uni.createCanvasContext('myCanvas');
						if(currentData==''){
							ctx.drawImage(this.scene, 0, 0, res.screenWidth || res.windowWidth, res.screenHeight || res.windowHeight);
						}else{
							ctx.drawImage(currentData.url[currentData.lightIndex], currentData.x, currentData.y, currentData.width, currentData.height);
							ctx.drawImage(this.scene, 0, 0, res.screenWidth || res.windowWidth, res.screenHeight || res.windowHeight);
						}
						ctx.draw(true,(ret)=>{
							uni.canvasToTempFilePath({
								x: 0,
								y: 0,
								width: widthPage,
								height: heightPage,
								destWidth: widthPage,
								destHeight: heightPage,
								canvasId: 'myCanvas',
								success: (res) => {
									uni.getImageInfo({
										src: res.tempFilePath,
										success: (image) => {
											/* 保存图片到手机相册 */
											uni.saveImageToPhotosAlbum({
												filePath: image.path,
												success: function() {
													uni.showModal({
														title: '保存成功',
														content: '图片已成功保存到相册',
														showCancel: false
													});
												},
												complete(res) {
													console.log(res);
												}
											});
										}
									});
								}
							}, this)
						})
					}
				});
			},
			getdata() {
				var that = this;
				// that.pagenum = 1;
				// that.datalist = [];
				that.loading = true;
				app.get('ApiShop/diylight', {}, function(res) {
					that.loading = false;
					if (res.status == 1) {
						// that.info = res.info;
						that.sceneList = res.data.bgimgs;
						that.loaded();
					} else {
						app.alert(res.msg);
					}

				});
				app.get('ApiShop/category1', {}, function(res2) {
					if (res2.status == 1) {
						// that.info = res.info;
						that.tableList = res2.data;
						that.loaded();
						that.getprolist();
					} else {
						app.alert(res2.msg);
					}
				});
			},
			getprolist: function() {
				var that = this;
				var pagenum = that.pagenum;
				var cid = that.tableList[that.tableIndex]['id'];
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
				app.post('ApiShop/getprolistForLight', {
					pagenum: pagenum,
					cid: cid
				}, function(res) {
					that.loading = false;
					var data = res.data;
					if (pagenum == 1) {
						that.lightList = data;
						if (data.length == 0) {
							that.nodata = true;
						}
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.lightList;
							var newdata = datalist.concat(data);
							that.lightList = newdata;
						}
					}
				});
			},
			uploadImg: function() {
				var that = this;
				app.chooseImage(function(urls) {
					var imgurl = urls[0];
					app.post('ApiShop/diylightUpload', {
						imgurl: imgurl
					}, function(res) {
						that.loading = false;
						if (res.status == 1) {
							var datalist = that.sceneList;
							var newdata = urls.concat(datalist);
							that.sceneList = newdata;
							that.scene = imgurl;
						} else {
							app.alert(res.msg);
						}
					});
				}, 1)
			},
			lightType(index, indexS) {
				this.dataList[index].lightIndex = indexS
			},
			areaClick() {
				for (let i = 0; i < this.dataList.length; i++) {
					this.dataList[i].active = false
				}
			},
			moveClick(index) {
				this.changeFocus(index)
				this.changeReal(index)
			},
			changeFocus(index) {
				for (let i = 0; i < this.dataList.length; i++) {
					if (this.dataList[i]) {
						this.dataList[i].active = false
					}
				}
				if (this.dataList[index]) {
					this.dataList[index].active = true
				}
			},
			changeReal(index) {
				for (let i = 0; i < this.dataList.length; i++) {
					if (this.dataList[i]) {
						this.dataList[i].real = false
					}
				}
				if (this.dataList[index]) {
					this.dataList[index].real = true
				}
			},
			lightChoose(e) {
				let item = {
					id: e.id,
					x: 150,
					y: 150,
					height: 200,
					width: 200,
					url: e.url,
					realUrl: e.realUrl,
					rotate: 0,
					state: false,
					active: true,
					real: true,
					lightIndex: 0
				}
				this.dataList.push(item)
				this.lightClick()
				this.changeReal(this.dataList.length - 1)
				this.changeFocus(this.dataList.length - 1)
			},
			tableClick(e) {
				var oldindex = this.tableIndex;
				this.tableIndex = e;
				if (e != oldindex)
					this.getprolist();
			},
			lightClick() {
				if (this.lightState) {
					this.lightState = false;
				} else {
					this.lightState = true;
				}
			},
			sceneChoose(e) {
				this.scene = e;
				this.sceneClick()
			},
			sceneClick() {
				if (this.sceneState) {
					this.sceneState = false;
				} else {
					this.sceneState = true;
				}
			},
			switchClick() {
				if (this.switchState) {
					this.switchState = false;
				} else {
					this.switchState = true;
				}
			},
			sizeStart(event, index) {
				this.changeFocus(index)
				this.changeStart = event.touches[0].pageX > event.touches[0].pageY ? event.touches[0].pageX :
					event.touches[0].pageY
				this.dataList[index].state = true
			},
			sizeMove(event, index) {
				this.changeFocus(index)
				var moveData = event.touches[0].pageX > event.touches[0].pageY ? event.touches[0].pageX :
					event.touches[0].pageY;
				var endData = moveData - this.changeStart
				this.sizeAry.push(endData)
				if (this.sizeAry.length > 1) {
					let sizeNum = this.sizeAry[this.sizeAry.length - 1] - this.sizeAry[this.sizeAry.length -
						2];
					if (sizeNum > 0) {
						this.dataList[index].height = this.dataList[index].height + 3
						this.dataList[index].width = this.dataList[index].width + 3
					} else {
						this.dataList[index].height = this.dataList[index].height - 3
						this.dataList[index].width = this.dataList[index].width - 3
					}
				}
			},
			sizeEnd(event, index) {
				this.dataList[index].state = false
			},
			spinStart(event, index) {
				this.changeStart = event.touches[0].pageY
				this.dataList[index].state = true
			},
			spinMove(event, index) {
				var endData = event.touches[0].pageY - this.changeStart
				this.sizeAry.push(endData)
				if (this.sizeAry.length > 1) {
					let sizeNum = this.sizeAry[this.sizeAry.length - 1] - this.sizeAry[this.sizeAry.length -
						2];
					if (sizeNum > 0) {
						this.dataList[index].rotate = this.dataList[index].rotate + 2
					} else {
						this.dataList[index].rotate = this.dataList[index].rotate - 2
					}
				}
			},
			spinEnd(event, index) {
				this.dataList[index].state = false
			},
			moveDelete(e) {
				this.dataList.splice(e, 1);
				setTimeout(() => {
					if (this.dataList.length > 0) {
						let have = ''
						for (let i = 0; i < this.dataList.length; i++) {
							if (this.dataList[i].real) {
								have = i
							}
						}
						if (have == '') {
							let index = 0;
							this.dataList[index].real = true
							this.dataList[index].active = true
						}
					}
				}, 10)
			},
			onChange: function(event, index) {
				this.changeFocus(index)
				this.dataList[index].x = event.detail.x
				this.dataList[index].y = event.detail.y
			}
		}
	}
</script>

<style>
	page {
		position: absolute;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}

	.scene {
		position: absolute;
		left: -100%;
		right: -100%;
		bottom: -100%;
		top: -100%;
		display: block;
		width: 100%;
		margin: auto auto;
	}

	movable-area {
		position: absolute;
		height: 100%;
		width: 100%;
		overflow: hidden;
	}

	.move_module {
		position: absolute;
		z-index: 5;
	}

	.move_item {
		position: absolute;
		height: 100%;
		width: 100%;
		border: 2px dashed rgba(0, 0, 0, 0);
	}

	.move_active {
		border: 2px dashed #fff;
	}

	.move_image {
		position: absolute;
		height: 100%;
		width: 100%;
	}

	.move_spin {
		position: absolute;
		height: 40rpx;
		width: 40rpx;
		top: -20rpx;
		right: -20rpx;
		border-radius: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #666;
	}

	.move_spin img {
		height: 30rpx;
		width: 30rpx;
		display: block;
	}

	.move_close {
		position: absolute;
		height: 40rpx;
		width: 40rpx;
		left: -20rpx;
		bottom: -20rpx;
		border-radius: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #666;
	}

	.move_close img {
		height: 30rpx;
		width: 30rpx;
		display: block;
	}

	.move_size {
		position: absolute;
		height: 40rpx;
		width: 40rpx;
		right: -20rpx;
		bottom: -20rpx;
		border-radius: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #666;
	}

	.move_size img {
		height: 30rpx;
		width: 30rpx;
		transform: rotate(90deg);
		display: block;
	}

	.class {
		position: absolute;
		width: 100%;
		padding: 0 50rpx;
		bottom: 100rpx;
		display: flex;
		align-items: center;
	}

	.class_item {
		height: 110rpx;
		width: 110rpx;
		border-radius: 10rpx;
		margin-right: 20rpx;
		border: 2px solid rgba(0, 0, 0, 0);
	}

	.class_active {
		border: 2px solid #e8506f;
	}

	.class_opt {
		bottom: 230rpx;
	}

	.cart {
		position: absolute;
		left: 50rpx;
		top: 100rpx;
	}

	.cart_item {
		height: 110rpx;
		width: 110rpx;
		border-radius: 10rpx;
		border: 2px solid #e8506f;
	}

	.cart_title {
		font-size: 28rpx;
		color: #333;
		text-align: center;
		margin: 5rpx 0 0 0;
	}

	.navigation {
		position: absolute;
		padding: 30rpx;
		width: 100%;
		box-sizing: border-box;
		top: 0;
		display: flex;
		z-index: 10;
	}

	.navigation_item {
		margin-right: 30rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.table {
		position: fixed;
		bottom: 0;
		height: 80rpx;
		width: 100%;
		font-size: 26rpx;
		color: #a9a19e;
		display: flex;
		align-items: center;
		z-index: 10;
		background: rgba(0, 0, 0, 0.7);
	}

	.table_item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.table_icon {
		height: 50rpx;
		width: 50rpx;
		margin-right: 15rpx;
	}

	.page_cut {
		position: fixed;
		height: 100%;
		width: 100%;
		z-index: 5;
		background: rgba(0, 0, 0, 0.3);
	}

	.alert {
		position: fixed;
		height: 100%;
		width: 100%;
		background: rgba(0, 0, 0, 0.7);
		z-index: 15;
	}

	.alert_hide {
		position: absolute;
		height: 100%;
		width: 100%;
	}

	.alert_module {
		position: absolute;
		padding: 30rpx;
		width: 100%;
		box-sizing: border-box;
		background: #fff;
		bottom: 0;
		border-radius: 18rpx 18rpx 0 0;
	}

	.alert_opt {
		bottom: 110rpx;
	}

	.alert_head {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.alert_btn {
		position: relative;
		height: 60rpx;
		width: 180rpx;
		border: 1px solid #65c498;
		color: #65c498;
		font-size: 26rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 100rpx;
		background: rgba(104, 196, 153, 0.15);
	}

	.alert_close {
		height: 40rpx;
		width: 40rpx;
	}

	.alert_content {
		position: relative;
		display: flex;
		flex-wrap: wrap;
		white-space: normal;
		max-height: 450rpx;
		padding-top: 30rpx;
	}

	.alert_item {
		position: relative;
		width: 30%;
		height: 200rpx;
		margin-right: 5%;
		margin-bottom: 30rpx;
		overflow: hidden;
		display: inline-block;
	}

	.alert_item:nth-child(3n) {
		margin-right: 0;
	}

	.alert_item img {
		position: absolute;
		left: -100%;
		right: -100%;
		bottom: -100%;
		top: -100%;
		display: block;
		width: 100%;
		margin: auto auto;
	}

	.alert_title {
		position: relative;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #333;
	}

	.alert_table {
		position: relative;
		padding: 30rpx 0 0 0;
		white-space: nowrap;
	}

	.alert_table view {
		position: relative;
		padding-right: 30rpx;
		font-size: 28rpx;
		display: inline-block;
	}

	.alert_active {
		color: #e0110a;
		font-weight: bold;
	}
</style>
