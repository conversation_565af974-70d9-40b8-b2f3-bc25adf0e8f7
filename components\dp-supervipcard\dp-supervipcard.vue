<template>
	<view class="dp-banner" :style="params.bgimgshow==0?'height:70px;':''+'margin:'+(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx;'">

        <image v-if="params.bgimgshow == 1" style="width: 100%;height:100px;border-radius: 10px;" mode="scaleToFill" :src="params.bgimg" @tap.stop="goto" :data-url="params.hrefurl"></image> 

		<view class="tab" :style="'background:'+params.bgcolor">   
			
			<view class="yu" v-if="item.show == 1" v-for="(item, index) in data" :key="index"  @tap.stop="goto" :data-url="item.hrefurl">
				
				<view :style="'color:'+params.color+';font-size:'+params.fontsize+'px'">{{item.text}}</view>
				
				<view class="bold" :style="'color:'+params.numcolor+';font-size:'+params.numfontsize+'px'">{{item.value}}</view>
				 
			</view>
			
			
			<view class="line" v-if="params.codeshow == 1"></view>
			
			
			<view class="yu" v-if="params.codeshow == 1" @tap.stop="goto" :data-url="params.hrefurl2">
				
				<image  :style="'width:'+params.usercodeimgsize+'px;height:'+params.usercodeimgsize+'px;'" :src="params.usercodeimg" ></image>
				
				<view :style="'color:'+params.color+';font-size:'+params.fontsize+'px'">会员码</view>
				
			</view>


		</view>

	</view>
	
</template>
<script>
	export default {
		data() {
			return {
				"bannerindex": 0
			}
		},
		props: {
			params: {},
			data: {}
		},
		methods: {

		}
	}
</script>
<style>
	.dp-banner {
		  position: relative;
		  background: #fff;
		  background-size: cover; /* 背景图片覆盖整个容器 */
		  background-position: center; /* 背景图片居中显示 */
		  background-repeat: no-repeat; /* 背景图片不重复 */
		  height: 150px; /* 容器高度，可以根据需要设置 */
		  border-radius: 10px;
	}   
	
	.tab{
		width: 100%;  
		position: absolute;
		bottom: 0;
		display: flex;
		left: 0;
		display: flex;
		align-items: center;
		background: #fff;
		padding: 10px 20px;
		border-radius: 10px ;
	}
	
	.yu{
		flex: 1;
		text-align: center;
	}
	
	.bold{
		font-weight: bold;
	    margin-top: 5px;
	}
	
	.line{
		width: 1px;
		background: #aaa;
		height: 40px;

	}
</style>