<template>
  <view class="container">
    <view class="order-info" v-if="orderInfo && orderInfo.product">
      <view class="product-info">
        <image :src="orderInfo.product.pic" mode="aspectFill"></image>
        <view class="info">
          <text class="name">{{orderInfo.product.name}}</text>
          <text class="price" :style="'color:'+t('color1')">￥{{orderInfo.product.sell_price}}</text>
        </view>
      </view>
      <view class="order-detail">
        <text>订单号：{{orderInfo.order_no || ''}}</text>
        <text>预约时间：{{orderInfo.yy_time || ''}}</text>
      </view>
    </view>

    <view class="worker-list-section">
      <view class="section-title">选择服务人员</view>
      
      <view class="worker-list" v-if="workerList.length > 0">
        <view class="worker-item" 
          v-for="(worker, index) in workerList" 
          :key="index"
          :class="{'active': selectedWorkerId === worker.id}"
          :style="selectedWorkerId === worker.id ? 'background: rgba('+t('color1rgb')+',0.1)' : ''"
          @tap="selectWorker(worker)">
          <image :src="worker.avatar || '/static/img/default-avatar.png'" mode="aspectFill" class="avatar"></image>
          <view class="worker-info">
            <text class="name">{{worker.name}}</text>
            <view class="stats">
              <text>评分：{{worker.rating || '5.0'}}分</text>
              <text>已完成：{{worker.order_count || '0'}}单</text>
            </view>
          </view>
          <view class="select-icon" 
               v-if="selectedWorkerId === worker.id"
               :style="'background:'+t('color1')">
            <text class="iconfont iconcheck"></text>
          </view>
        </view>
      </view>
      
      <view class="no-worker" v-else>
        <text>当前预约时间暂无可用服务人员</text>
        <text>请返回选择其他时间段</text>
      </view>
    </view>

    <view class="footer">
      <button class="submit-btn" 
              :disabled="!selectedWorkerId" 
              :class="{'shadow-box': selectedWorkerId}"
              :style="selectedWorkerId ? 'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)' : 'background:#ccc'" 
              @tap="submitAssign">确认选择</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderId: '',
      yydate: '',
      orderInfo: {
        product: {
          pic: '',
          name: '',
          sell_price: 0
        },
        order_no: '',
        yy_time: ''
      },
      workerList: [],
      selectedWorkerId: ''
    }
  },
  
  onLoad(options) {
    var app = getApp();
    console.log('选择服务人员页面加载，参数:', options);
    if(options && options.id) {
      this.orderId = options.id;
      this.yydate = options.yydate || '';
      this.getOrderInfo();
    } else {
      app.error('参数错误');
    }
  },
  
  methods: {
    // 获取订单信息
    getOrderInfo() {
      var app = getApp();
      var that = this;
      app.showLoading('加载订单信息');
      
      console.log('获取订单详情，ID:', that.orderId);
      
      app.get('ApiYuyue/orderDetail', {
        id: that.orderId
      }, function(res) {
        app.showLoading(false);
        console.log('获取订单信息原始返回:', res);
        
        // 兼容不同的返回结构
        if (res.status == 1 && res.data) {
          that.orderInfo = res.data;
        } else if (res.detail) {
          that.orderInfo = res.detail;
        } else {
          that.orderInfo = res;
        }
        
        // 确保product存在
        if(!that.orderInfo.product) {
          that.orderInfo.product = {
            pic: that.orderInfo.propic || '',
            name: that.orderInfo.proname || '商品信息',
            sell_price: that.orderInfo.product_price || that.orderInfo.totalprice || 0
          };
        }
        
        // 添加订单号
        if (!that.orderInfo.order_no) {
          that.orderInfo.order_no = that.orderInfo.ordernum || '';
        }
        
        // 获取预约时间
        if (!that.orderInfo.yy_time) {
          if (that.orderInfo.yy_time_format) {
            that.orderInfo.yy_time = that.orderInfo.yy_time_format;
          } else if (that.orderInfo.yydate && that.orderInfo.yytime) {
            that.orderInfo.yy_time = that.orderInfo.yydate + ' ' + that.orderInfo.yytime;
          }
        }
          
        // 获取服务人员列表
        that.getWorkerList();
      });
    },
    
    // 获取服务人员列表
    getWorkerList() {
      var app = getApp();
      var that = this;
      app.showLoading('加载服务人员');
      
      // 优先使用传入的yydate，如果没有则使用订单中的日期
      const date = that.yydate || that.orderInfo.yydate || '';
      
      console.log('获取服务人员参数:', {
        id: that.orderId,
        yydate: date
      });
      
      app.get('ApiYuyue/getAvailableWorkers', {
        id: that.orderId,
        yydate: date
      }, function(res) {
        app.showLoading(false);
        
        console.log('获取服务人员列表返回:', res);
        
        let workerData = [];
        
        // 兼容不同的返回结构
        if (res.status == 1 && res.data) {
          workerData = res.data;
        } else if (res.list) {
          workerData = res.list;
        } else if (Array.isArray(res)) {
          workerData = res;
        }
        
        that.workerList = workerData;
        
        // 如果只有一个服务人员，自动选择
        if(that.workerList.length === 1) {
          that.selectWorker(that.workerList[0]);
        }
      });
    },
    
    // 选择服务人员
    selectWorker(worker) {
      console.log('选择服务人员:', worker);
      this.selectedWorkerId = worker.id;
    },
    
    // 提交分配
    submitAssign() {
      var app = getApp();
      var that = this;
      if(!that.selectedWorkerId) {
        app.error('请选择服务人员');
        return;
      }
      
      const params = {
        id: that.orderId,
        worker_id: that.selectedWorkerId
      };
      
      console.log('提交分配参数:', params);
      
      app.showLoading('提交选择');
      app.post('ApiYuyue/assignWorker', params, function(res) {
        app.showLoading(false);
        console.log('分配服务人员返回:', res);
        
        // 处理不同的返回结构
        let success = false;
        let message = '';
        
        if (res.status == 1) {
          success = true;
          message = res.msg || '选择成功';
        } else if (res.code == 200 || res.code == 1) {
          success = true;
          message = res.message || res.msg || '选择成功';
        }
        
        if (success) {
          app.alert(message, function() {
            app.goto('/yuyue/orderlist');
          });
        } else {
          app.error(res.msg || res.message || '选择失败');
        }
      });
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.order-info {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.product-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-info image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.info {
  flex: 1;
}

.name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.price {
  font-size: 32rpx;
  font-weight: bold;
}

.order-detail {
  font-size: 24rpx;
  color: #999;
}

.order-detail text {
  display: block;
  margin-bottom: 10rpx;
}

.worker-list-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 120rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.worker-list {
  display: flex;
  flex-direction: column;
}

.worker-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.worker-info {
  flex: 1;
}

.worker-info .name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.worker-info .stats {
  display: flex;
  font-size: 24rpx;
  color: #999;
}

.worker-info .stats text {
  margin-right: 20rpx;
}

.select-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-worker {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.no-worker text {
  display: block;
  margin-bottom: 10rpx;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
}

/* 新增阴影效果的类 */
.shadow-box {
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.2);
}
</style> 