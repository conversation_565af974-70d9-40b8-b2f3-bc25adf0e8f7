.job-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #f5f5f5 100%);
  padding-bottom: env(safe-area-inset-bottom);
  color-scheme: light;

  // 玻璃拟态加载动画
  .loading-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;

    .loading-spinner {
      width: 80rpx;
      height: 80rpx;
      border: 6rpx solid rgba(255, 77, 79, 0.1);
      border-top: 6rpx solid #FF4D4F;
      border-radius: 50%;
      animation: spin 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
      box-shadow: 0 0 30rpx rgba(255, 77, 79, 0.2);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: -6rpx;
        left: -6rpx;
        right: -6rpx;
        bottom: -6rpx;
        border-radius: 50%;
        border: 6rpx solid transparent;
        border-top: 6rpx solid rgba(255, 77, 79, 0.3);
        animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite reverse;
      }
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .header {
    background: #ffffff;
    padding: 40rpx 30rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
    border-radius: 0 0 32rpx 32rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 240rpx;
      background: linear-gradient(135deg, rgba(255, 77, 79, 0.08) 0%, rgba(255, 107, 107, 0.03) 100%);
      z-index: 0;
      opacity: 0.8;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 2rpx;
    }

    .title-wrapper {
      position: relative;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 28rpx;

      .title {
        font-size: 40rpx;
        font-weight: 700;
        color: #2c3e50;
        flex: 1;
        margin-right: 24rpx;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        line-height: 1.4;
        letter-spacing: 0.5rpx;
      }

      .status {
        font-size: 24rpx;
        padding: 8rpx 24rpx;
        border-radius: 28rpx;
        background: #f5f5f5;
        color: #999;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        transform: translateY(4rpx);

        &.status-active {
          background: linear-gradient(135deg, #52c41a 0%, #3dac08 100%);
          color: #fff;
          box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.2);
        }
      }
    }

    .salary {
      position: relative;
      font-size: 48rpx;
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      -webkit-background-clip: text;
      color: transparent;
      font-weight: 800;
      margin-bottom: 32rpx;
      letter-spacing: 1rpx;
      text-shadow: 0 2px 4px rgba(255, 77, 79, 0.1);
      
      &::after {
        content: '/月';
        font-size: 24rpx;
        font-weight: 500;
        margin-left: 8rpx;
        opacity: 0.8;
      }
    }

    .basic-info {
      position: relative;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 32rpx;
      padding: 0 4rpx;

      text {
        font-size: 28rpx;
        color: #666;
        margin-right: 40rpx;
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
        padding: 6rpx 0;
        transition: all 0.3s ease;
        
        .iconfont {
          font-size: 36rpx;
          margin-right: 12rpx;
          color: #8c8c8c;
          transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        &:hover {
          color: #2c3e50;
          transform: translateX(4rpx);
          
          .iconfont {
            color: #FF4D4F;
            transform: scale(1.2) rotate(12deg);
          }
        }
      }
    }

    .tags-scroll {
      position: relative;
      white-space: nowrap;
      margin: 0 -30rpx;
      padding: 0 30rpx;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 10rpx;
        width: 60rpx;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #fff 100%);
        pointer-events: none;
      }

      &::-webkit-scrollbar {
        display: none;
      }

      .tags {
        display: inline-flex;
        padding-bottom: 10rpx;
        
        .tag {
          display: inline-flex;
          align-items: center;
          background: rgba(255, 77, 79, 0.06);
          color: #FF4D4F;
          font-size: 26rpx;
          padding: 10rpx 28rpx;
          border-radius: 28rpx;
          margin-right: 20rpx;
          transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
          border: 1rpx solid rgba(255, 77, 79, 0.1);
          font-weight: 600;
          box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.05);

          &:active {
            transform: scale(0.95) translateY(2rpx);
            background: rgba(255, 77, 79, 0.1);
            box-shadow: 0 1rpx 4rpx rgba(255, 77, 79, 0.05);
          }
        }
      }
    }
  }

  .company {
    background: #ffffff;
    padding: 40rpx 30rpx;
    margin: 0 20rpx 24rpx;
    display: flex;
    align-items: center;
    position: relative;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
    border-radius: 24rpx;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 120rpx;
      background: linear-gradient(135deg, rgba(44, 62, 80, 0.02) 0%, rgba(44, 62, 80, 0) 100%);
      z-index: 0;
    }

    &.company-hover {
      transform: scale(0.98) translateY(4rpx);
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
      background: #fafafa;
    }

    .logo {
      width: 128rpx;
      height: 128rpx;
      border-radius: 20rpx;
      margin-right: 28rpx;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
      border: 4rpx solid #fff;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      position: relative;
      z-index: 1;
      background: #fff;

      &:active {
        transform: scale(0.95) rotate(-4deg);
      }
    }

    .company-info {
      flex: 1;
      position: relative;
      z-index: 1;

      .name {
        font-size: 36rpx;
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 16rpx;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        letter-spacing: 0.5rpx;
      }

      .desc {
        font-size: 26rpx;
        color: #8c8c8c;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        text {
          position: relative;
          margin-right: 32rpx;
          padding: 4rpx 0;
          
          &:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -16rpx;
            top: 50%;
            transform: translateY(-50%);
            width: 1rpx;
            height: 24rpx;
            background: #eee;
          }
        }
      }
    }

    .icon-arrow-right {
      font-size: 40rpx;
      color: #bfbfbf;
      margin-left: 24rpx;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      position: relative;
      z-index: 1;
    }

    &:active .icon-arrow-right {
      transform: translateX(8rpx);
      color: #FF4D4F;
    }
  }

  .content-scroll {
    height: calc(100vh - 120rpx);
    padding: 0 20rpx;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .section {
    background: #ffffff;
    padding: 40rpx 30rpx;
    margin-bottom: 24rpx;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 120rpx;
      background: linear-gradient(135deg, rgba(44, 62, 80, 0.02) 0%, rgba(44, 62, 80, 0) 100%);
      z-index: 0;
    }

    &:hover {
      transform: translateY(-4rpx);
      box-shadow: 0 12rpx 36rpx rgba(0, 0, 0, 0.08);
    }

    .section-title {
      font-size: 34rpx;
      color: #2c3e50;
      font-weight: 700;
      margin-bottom: 32rpx;
      display: flex;
      align-items: center;
      position: relative;
      z-index: 1;

      .iconfont {
        font-size: 44rpx;
        background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
        -webkit-background-clip: text;
        color: transparent;
        margin-right: 16rpx;
        transition: all 0.3s ease;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -12rpx;
        left: 0;
        width: 48rpx;
        height: 4rpx;
        background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
        border-radius: 2rpx;
        transition: all 0.3s ease;
      }

      &:hover {
        .iconfont {
          transform: scale(1.1) rotate(12deg);
        }

        &::after {
          width: 60rpx;
        }
      }
    }

    .rich-content {
      font-size: 28rpx;
      color: #4a4a4a;
      line-height: 1.8;
      letter-spacing: 0.5rpx;
      position: relative;
      z-index: 1;

      view {
        margin-bottom: 20rpx;
        padding: 4rpx 0;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .info-list {
      position: relative;
      z-index: 1;

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
        font-size: 28rpx;
        line-height: 1.6;
        padding: 16rpx 24rpx;
        background: #f9f9f9;
        border-radius: 16rpx;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        border: 1rpx solid transparent;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          background: #f5f5f5;
          border-color: rgba(0, 0, 0, 0.05);
          transform: translateX(4rpx);
        }

        .label {
          color: #8c8c8c;
          width: 160rpx;
          flex-shrink: 0;
          font-weight: 600;
        }

        .value {
          color: #2c3e50;
          flex: 1;
          font-weight: 500;

          &.highlight {
            color: #FF4D4F;
            font-weight: 700;
            font-size: 32rpx;
            text-shadow: 0 2px 4px rgba(255, 77, 79, 0.1);
          }
        }
      }
    }
  }

  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 120rpx;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    padding-bottom: env(safe-area-inset-bottom);
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
    z-index: 99;

    .collect {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 40rpx;
      padding: 12rpx 28rpx;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      border-radius: 16rpx;
      position: relative;
      
      &:active {
        transform: scale(0.92);
        background: rgba(0, 0, 0, 0.03);
      }
      
      .iconfont {
        font-size: 48rpx;
        color: #bfbfbf;
        margin-bottom: 8rpx;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        transform-origin: center 60%;

        &.icon-heart-fill {
          color: #FF4D4F;
          animation: heartBeat 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
      }

      text {
        font-size: 24rpx;
        color: #8c8c8c;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      &:hover text {
        color: #666;
      }
    }

    .apply-btn {
      flex: 1;
      height: 88rpx;
      line-height: 88rpx;
      background: linear-gradient(135deg, #ff6b6b 0%, #FF4D4F 100%);
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 700;
      border-radius: 44rpx;
      text-align: center;
      margin: 0;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      border: none;
      position: relative;
      overflow: hidden;
      letter-spacing: 2rpx;
      box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.25);

      &::after {
        display: none;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:active {
        transform: scale(0.96) translateY(2rpx);
        box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.2);

        &::before {
          opacity: 1;
        }
      }

      &.apply-btn-disabled {
        background: #d9d9d9;
        opacity: 0.8;
        box-shadow: none;
        transform: none;
      }
    }
  }

  @keyframes heartBeat {
    0% {
      transform: scale(1) rotate(0);
    }
    15% {
      transform: scale(1.4) rotate(12deg);
    }
    30% {
      transform: scale(0.9) rotate(-8deg);
    }
    45% {
      transform: scale(1.2) rotate(4deg);
    }
    80% {
      transform: scale(0.95) rotate(-2deg);
    }
    100% {
      transform: scale(1) rotate(0);
    }
  }
} 