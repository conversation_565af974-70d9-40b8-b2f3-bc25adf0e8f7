<template>
  <view class="page flex-col">
    <view class="box_6 flex-col">
      <div class="nav-bar_1 flex-col">
        <image
          class="icon_6"
          referrerpolicy="no-referrer"
          src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG33649801bf54b184979dadbe3c3e5444.png"
        />
      </div>
      <view class="box_7 flex-row justify-between">
        <view class="single-avatar_1 flex-col">
          <image
            class="image_1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNGb0bf6e8b59c7139355e4a70e45957ae2.png"
          />
        </view>
        <view class="text-wrapper_3 flex-col justify-between">
          <text class="text_3">用户昵称</text>
          <text class="text_4">用户&nbsp;ID：15616161</text>
        </view>
      </view>
      <text class="text_5">用户个人简介</text>
      <view class="box_8 flex-row justify-between">
        <view class="text-group_4 flex-col justify-between">
          <text class="text_18">30</text>
          <text class="text_19">我的关注</text>
        </view>
        <view class="text-group_5 flex-col justify-between">
          <text class="text_20">30</text>
          <text class="text_21">关注我的</text>
        </view>
        <view class="text-wrapper_4 flex-col justify-between">
          <text class="text_22">30</text>
          <text class="text_23">积分/余额</text>
        </view>
      </view>
      <view class="box_9 flex-row">
        <button class="button_1 flex-col" @click="onClick_1">
          <text class="text_24">我的档案</text>
        </button>
        <view class="icon_7 flex-col">
          <image
            class="label_2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNGd750727f256e01d8c264eb8889d4eb68.png"
          />
        </view>
      </view>
 
      <view class="group_3 flex-row">
        <view class="text-group_6 flex-col justify-between">
          <text class="text_6">30</text>
          <text class="text_7">被引用次数</text>
        </view>
        <view class="box_3 flex-col"></view>
        <view class="text-group_7 flex-col justify-between">
          <text class="text_8">100</text>
          <text class="text_9">我的评论</text>
        </view>
      </view>
    </view>
    <view class="box_10 flex-col">
      <view class="block_4 flex-row">
        <div class="tabs_2 flex-col">
          <view class="text-wrapper_5 flex-row justify-between">
            <text class="text_10">笔记</text>
            <text class="text_11">收藏</text>
          </view>
        </div>
      </view>
      <view class="block_5 flex-row justify-between">
        <view class="block_2 flex-col">
          <image
            class="image_2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG0f9c8bee0e09b8d1fcaaa1d3ec59396d.png"
          />
          <text class="text_12">深海系类橘子海&nbsp;简约渐变高级感壁纸</text>
        </view>
        <view class="block_3 flex-col">
          <image
            class="image_3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG8ad3b1acad9a22e5bd0f3c48678a06af.png"
          />
          <text class="text_13">三度艺术经典丛书：艺术设计的三大构成</text>
        </view>
      </view>
    </view>
    <view class="tab-bar_1 flex-row">
      <view class="tab-bar-item_6 flex-col justify-between">
        <image
          class="icon_1"
          referrerpolicy="no-referrer"
          src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG79a688939c3e7b3898dc819c16343088.png"
        />
        <text class="text_14">首页</text>
      </view>
      <view class="tab-bar-item_7 flex-col justify-between">
        <image
          class="icon_2"
          referrerpolicy="no-referrer"
          src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNGf8e8690be38aef10ec63f71b6f2835a9.png"
        />
        <text class="text_15">购物</text>
      </view>
      <view class="tab-bar-item_8 flex-col">
        <image
          class="icon_3"
          referrerpolicy="no-referrer"
          src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG3adab9f5c6bae655cf24cf0710186b5d.png"
        />
      </view>
      <view class="tab-bar-item_9 flex-col justify-between">
        <image
          class="icon_4"
          referrerpolicy="no-referrer"
          src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG5db36ae286b5e3e695b9e28ac7c17c9b.png"
        />
        <text class="text_16">消息</text>
      </view>
      <view class="tab-bar-item_10 flex-col justify-between">
        <image
          class="icon_5"
          referrerpolicy="no-referrer"
          src="https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG267e9d74e1a805761175a9123b121775.png"
        />
        <text class="text_17">我的</text>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      constants: {},
    };
  },
  methods: {
    onClick_1() {
      alert(1);
    },
  },
};
</script>
<style>
.page {
  background-color: rgba(240, 240, 240, 1);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}

.box_6 {
  background-image: linear-gradient(
    180deg,
    rgba(227, 88, 49, 1) 0,
    rgba(227, 88, 49, 1) 77.110332%,
    rgba(227, 88, 49, 0) 99.153721%
  );
  position: relative;
  width: 750rpx;
  height: 673rpx;
}

.nav-bar_1 {
  height: 73rpx;
  margin-top: 80rpx;
  width: 750rpx;
}

.icon_6 {
  width: 44rpx;
  height: 44rpx;
  margin: 12rpx 0 0 674rpx;
}

.box_7 {
  width: 335rpx;
  height: 126rpx;
  margin: 29rpx 0 0 32rpx;
}

.single-avatar_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 50%;
  height: 126rpx;
  border: 1px solid rgba(255, 255, 255, 1);
  width: 126rpx;
}

.image_1 {
  width: 126rpx;
  height: 126rpx;
}

.text-wrapper_3 {
  width: 183rpx;
  height: 84rpx;
  margin-top: 21rpx;
}

.text_3 {
  width: 128rpx;
  height: 45rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}

.text_4 {
  width: 183rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.6);
  font-size: 22rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin-top: 8rpx;
}

.text_5 {
  width: 144rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24rpx;
  font-family: MiSans-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 32rpx 0 0 32rpx;
}

.box_8 {
  width: 321rpx;
  height: 67rpx;
  margin: 32rpx 0 202rpx 32rpx;
}

.text-group_4 {
  width: 88rpx;
  height: 67rpx;
}

.text_18 {
  width: 31rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24rpx;
  font-family: MiSans VF-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin-left: 30rpx;
}

.text_19 {
  width: 88rpx;
  height: 29rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin-top: 6rpx;
}

.text-group_5 {
  width: 88rpx;
  height: 67rpx;
  margin-left: 24rpx;
}

.text_20 {
  width: 31rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24rpx;
  font-family: MiSans VF-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin-left: 30rpx;
}

.text_21 {
  width: 88rpx;
  height: 29rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin-top: 6rpx;
}

.text-wrapper_4 {
  width: 97rpx;
  height: 67rpx;
  margin-left: 24rpx;
}

.text_22 {
  width: 31rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24rpx;
  font-family: MiSans VF-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin-left: 34rpx;
}

.text_23 {
  width: 97rpx;
  height: 29rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin-top: 6rpx;
}

.box_9 {
  position: absolute;
  left: 321rpx;
  top: -238rpx;
  width: 824rpx;
  height: 841rpx;
  background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/FigmaDDSSlicePNG385ce8759f0b88c7034c8e89504e756e.png) -218rpx
    0rpx no-repeat;
  background-size: 1014rpx 1025rpx;
}

.button_1 {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 54px;
  height: 56rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 163rpx;
  margin: 647rpx 0 0 138rpx;
}

.text_24 {
  width: 96rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 12rpx 0 0 34rpx;
}

.icon_7 {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  height: 56rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 56rpx;
  margin: 647rpx 443rpx 0 24rpx;
}

.label_2 {
  width: 38rpx;
  height: 38rpx;
  margin: 9rpx 0 0 9rpx;
}

.group_1 {
  height: 88rpx;
  width: 750rpx;
  position: absolute;
  left: 0;
  top: 0;
}

.block_1 {
  width: 750rpx;
  height: 88rpx;
}

.text-wrapper_1 {
  width: 108rpx;
  height: 39rpx;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -0.5600000023841858px;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 31rpx 0 0 42rpx;
}

.text_1 {
  width: 108rpx;
  height: 39rpx;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 28rpx;
  letter-spacing: -0.5600000023841858px;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}

.text_2 {
  width: 108rpx;
  height: 39rpx;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}

.box_1 {
  background-color: rgba(51, 51, 51, 1);
  width: 34rpx;
  height: 22rpx;
  margin: 36rpx 0 0 438rpx;
}

.box_2 {
  background-color: rgba(51, 51, 51, 1);
  width: 31rpx;
  height: 22rpx;
  margin: 35rpx 0 0 10rpx;
}

.label_1 {
  width: 49rpx;
  height: 23rpx;
  margin: 35rpx 29rpx 0 9rpx;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  position: absolute;
  left: 24rpx;
  top: 499rpx;
  width: 702rpx;
  height: 152rpx;
}

.text-group_6 {
  width: 110rpx;
  height: 86rpx;
  margin: 33rpx 0 0 122rpx;
}

.text_6 {
  width: 35rpx;
  height: 41rpx;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 32rpx;
  font-family: DINPro-Bold;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 39rpx;
}

.text_7 {
  width: 110rpx;
  height: 29rpx;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin-top: 16rpx;
}

.box_3 {
  background-color: rgba(234, 234, 234, 1);
  width: 1rpx;
  height: 64rpx;
  margin: 44rpx 0 0 119rpx;
}

.text-group_7 {
  width: 88rpx;
  height: 86rpx;
  margin: 33rpx 133rpx 0 129rpx;
}

.text_8 {
  width: 53rpx;
  height: 41rpx;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 32rpx;
  font-family: DINPro-Bold;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 18rpx;
}

.text_9 {
  width: 88rpx;
  height: 29rpx;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin-top: 16rpx;
}

.box_10 {
  width: 750rpx;
  height: 842rpx;
}

.block_4 {
  width: 160rpx;
  height: 37rpx;
  margin: 2rpx 0 0 32rpx;
}

.tabs_2 {
  height: 37rpx;
  width: 160rpx;
}

.text-wrapper_5 {
  width: 160rpx;
  height: 37rpx;
}

.text_10 {
  width: 56rpx;
  height: 37rpx;
  overflow-wrap: break-word;
  color: rgba(227, 88, 47, 1);
  font-size: 28rpx;
  font-family: MiSans VF-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}

.text_11 {
  width: 56rpx;
  height: 37rpx;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 28rpx;
  font-family: MiSans VF-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}

.block_5 {
  width: 702rpx;
  height: 620rpx;
  margin: 24rpx 0 159rpx 24rpx;
}

.block_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  width: 343rpx;
  height: 620rpx;
}

.image_2 {
  width: 343rpx;
  height: 490rpx;
}

.text_12 {
  width: 303rpx;
  height: 74rpx;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 28rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: left;
  margin: 24rpx 0 32rpx 20rpx;
}

.block_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  width: 343rpx;
  height: 470rpx;
}

.image_3 {
  width: 343rpx;
  height: 340rpx;
}

.text_13 {
  width: 303rpx;
  height: 74rpx;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 28rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: left;
  margin: 24rpx 0 32rpx 20rpx;
}

.tab-bar_1 {
  box-shadow: inset 0px 1px 0px 0px rgba(226, 228, 231, 1);
  background-color: rgba(255, 255, 255, 1);
  width: 750rpx;
  height: 110rpx;
  margin-top: -1rpx;
}

.tab-bar-item_6 {
  width: 48rpx;
  height: 80rpx;
  margin: 15rpx 0 0 64rpx;
}

.icon_1 {
  width: 48rpx;
  height: 48rpx;
}

.text_14 {
  width: 44rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 4rpx 0 0 2rpx;
}

.tab-bar-item_7 {
  width: 48rpx;
  height: 80rpx;
  margin: 15rpx 0 0 86rpx;
}

.icon_2 {
  width: 48rpx;
  height: 48rpx;
}

.text_15 {
  width: 44rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 4rpx 0 0 2rpx;
}

.tab-bar-item_8 {
  height: 68rpx;
  width: 87rpx;
  margin: 21rpx 0 0 85rpx;
}

.icon_3 {
  width: 87rpx;
  height: 68rpx;
}

.tab-bar-item_9 {
  width: 48rpx;
  height: 80rpx;
  margin: 15rpx 0 0 85rpx;
}

.icon_4 {
  width: 48rpx;
  height: 48rpx;
}

.text_16 {
  width: 44rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 4rpx 0 0 2rpx;
}

.tab-bar-item_10 {
  width: 48rpx;
  height: 80rpx;
  margin: 15rpx 65rpx 0 86rpx;
}

.icon_5 {
  width: 48rpx;
  height: 48rpx;
}

.text_17 {
  width: 44rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 22rpx;
  font-family: MiSans VF-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 4rpx 0 0 2rpx;
}

</style>
