<template>
	
<view :style="containerStyle">
	

<view class="dp-membercard" :style="{
    margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx', 
    borderRadius: params.radius+'rpx',
    boxShadow: params.shadowShow == '1' ? '0 4rpx 16rpx rgba(0, 0, 0, 0.08)' : 'none'
  }">
  
  <!-- 会员卡主体 - 使用自定义封面和颜色 -->
  <view class="flex-col section" 
        :style="{
          background: getCardBackground(),
          borderRadius: '20rpx',
          position: 'relative',
          minHeight: '300rpx'
        }">
	  
	  <!-- 主封面背景 -->
	  <image v-if="data.levelinfo && data.levelinfo.card_main_cover" 
	         :src="data.levelinfo.card_main_cover" 
	         class="main-cover-bg"
	         mode="aspectFill">
	  </image>
	  
	  <!-- 移除默认背景图片，只有设置了主封面时才显示 -->
	  
	  <!-- 颜色渐变遮罩层 - 覆盖在背景图片上 -->
	  <view class="color-overlay" :style="{background: getColorOverlay()}"></view>
	  
	  <!-- 闪光特效遮罩层 -->
	  <view class="shimmer-overlay"></view>
	  
	  <!-- 副封面装饰图 -->
	  <image v-if="data.levelinfo && data.levelinfo.card_sub_cover"
	         :src="data.levelinfo.card_sub_cover" 
	         class="sub-cover-icon" 
	         mode="widthFix">
	  </image>
	  
	  <!-- 会员等级名称 -->
	  <view class="vtxt" :style="{color: getLevelNameColor()}">{{getLevelName()}}</view>
	  
	  <!-- 会员卡描述 -->
	  <view v-if="data.levelinfo && data.levelinfo.card_description" 
	        class="card-desc" 
	        :style="{color: getDescriptionColor()}">
	    {{data.levelinfo.card_description}}
	  </view>
	  
  	  <!-- 激活按钮 - 使用自定义文字和链接 -->
  	  <view class="vbtn" 
  	        @tap="handleCardActivation" 
  	        v-if="params.show_upgrade == '1' || (data.levelinfo && data.levelinfo.card_activate_url)"
  	        :style="{
  	          backgroundColor: getButtonColor(),
  	          borderColor: getButtonBorderColor(),
  	          color: getButtonTextColor()
  	        }">
  	    {{getActivateButtonText()}}
  	  </view>
	  
  </view>
  
  <!-- 会员卡详细信息区域 -->
  <view class="card-info-section" v-if="params.showDetailInfo == '1'">
    <!-- 等级信息 -->
    <view class="level-info">
      <text class="level-name">{{getLevelName()}}</text>
      <text class="level-desc" v-if="!data.levelinfo || !data.levelinfo.is_max_level">
        下一等级：{{getNextLevelName()}}
      </text>
      <text class="level-desc" v-else>已达到最高等级</text>
    </view>
    
    <!-- 会员统计信息 -->
    <view class="member-stats" v-if="data.levelinfo">
      <view class="stat-item">
        <text class="stat-value">{{data.levelinfo.level_member_count || 0}}</text>
        <text class="stat-label">同级会员</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{data.levelinfo.level_rank || 1}}</text>
        <text class="stat-label">我的排名</text>
      </view>
    </view>
  </view>
  
  <!-- 特权列表 -->
  <view class="privilege-section" v-if="params.showPrivilegeList == '1'">
    <view class="section-title">
      <text class="section-title-text">{{params.privilegeTitle || '会员特权'}}</text>
    </view>
    <view class="privilege-grid">
      <view class="privilege-grid-item" v-for="(item, index) in getPrivilegeList()" :key="index">
        <image class="privilege-icon" :src="item.icon || '/static/img/star.png'"></image>
        <text class="privilege-name">{{item.name}}</text>
        <text class="privilege-desc">{{item.desc}}</text>
      </view>
    </view>
  </view>
</view>

</view>	
	

</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      defaultPrivileges: [
        { name: '专属折扣', desc: '会员专享商品折扣', icon: '/static/img/star.png' },
        { name: '生日礼包', desc: '生日当月送惊喜礼包', icon: '/static/img/star.png' },
        { name: '积分加速', desc: '购物积分加速获取', icon: '/static/img/star.png' },
        { name: '专属客服', desc: '会员专属客服通道', icon: '/static/img/star.png' }
      ]
    }
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    // 2025-01-03 23:05:12,001-INFO-[membercard_component][container_style_computed_001] - 获取容器样式计算属性，包含上浮距离设置
    containerStyle() {
      const floatTop = this.params.float_top ? parseFloat(this.params.float_top) : 0;
      return {
        width: '100%',
        marginTop: floatTop ? `-${floatTop}rpx` : '0', // 如果设置了上浮距离，应用负的margin-top
        position: floatTop ? 'relative' : 'static', // 设置了浮动时使用相对定位
        zIndex: floatTop ? '10' : 'auto' // 浮动时提高层级
      };
    }
  },
  methods: {
    // 2025-01-03 22:55:53,608-INFO-[membercard_component][get_card_background_001] - 获取会员卡背景样式
    getCardBackground() {
      // 如果没有主封面图片，使用纯色渐变作为背景
      if (!this.data.levelinfo || !this.data.levelinfo.card_main_cover) {
        // 只有在设置了主色调时才使用自定义颜色
        if (this.data.levelinfo && this.data.levelinfo.card_primary_color && this.data.levelinfo.card_primary_color.trim() !== '') {
          return `linear-gradient(135deg, ${this.data.levelinfo.card_primary_color}, ${this.data.levelinfo.card_secondary_color || '#f0f0f0'})`;
        }
        // 没有设置主色调时使用透明背景，不显示任何背景
        return 'transparent';
      }
      // 有主封面图片时，背景设为透明，让图片显示
      return 'transparent';
    },
    
    // 2025-01-03 22:55:53,617-INFO-[membercard_component][get_color_overlay_001] - 获取颜色渐变遮罩层
    getColorOverlay() {
      // 只有在有主封面图片且设置了自定义主色调时才显示遮罩层
      if (this.data.levelinfo && 
          this.data.levelinfo.card_main_cover && 
          this.data.levelinfo.card_primary_color && 
          this.data.levelinfo.card_primary_color.trim() !== '') {
        const secondaryColor = (this.data.levelinfo.card_secondary_color && this.data.levelinfo.card_secondary_color.trim() !== '') 
          ? this.data.levelinfo.card_secondary_color 
          : '#f0f0f0';
        return `linear-gradient(135deg, ${this.data.levelinfo.card_primary_color}CC, ${secondaryColor}CC)`;
      }
      // 没有主封面图片或没有设置自定义颜色时，不显示遮罩层
      return 'transparent';
    },
    
    // 2025-01-03 22:55:53,609-INFO-[membercard_component][get_text_color_001] - 获取文字颜色
    getTextColor() {
      // 只有在设置了主色调时才根据背景色判断文字颜色
      if (this.data.levelinfo && this.data.levelinfo.card_primary_color && this.data.levelinfo.card_primary_color.trim() !== '') {
        const bgColor = this.data.levelinfo.card_primary_color;
        // 简单的亮度判断，实际可以用更复杂的算法
        const isLight = bgColor.includes('#f') || bgColor.includes('#e') || bgColor.includes('#d');
        return isLight ? '#333333' : '#ffffff';
      }
      // 没有设置主色调时使用默认白色文字
      return '#ffffff';
    },
    
    // 2025-01-03 22:55:53,618-INFO-[membercard_component][get_level_name_color_001] - 获取等级名称颜色
    getLevelNameColor() {
      // 优先使用自定义的等级名称颜色
      if (this.data.levelinfo && this.data.levelinfo.card_level_name_color && this.data.levelinfo.card_level_name_color.trim() !== '') {
        return this.data.levelinfo.card_level_name_color;
      }
      // 没有设置自定义颜色时，使用通用文字颜色
      return this.getTextColor();
    },
    
    // 2025-01-03 22:55:53,619-INFO-[membercard_component][get_description_color_001] - 获取描述文字颜色
    getDescriptionColor() {
      // 优先使用自定义的描述文字颜色
      if (this.data.levelinfo && this.data.levelinfo.card_description_color && this.data.levelinfo.card_description_color.trim() !== '') {
        return this.data.levelinfo.card_description_color;
      }
      // 没有设置自定义颜色时，使用通用文字颜色
      return this.getTextColor();
    },
    
    // 2025-01-03 22:55:53,610-INFO-[membercard_component][get_button_color_001] - 获取升级按钮背景颜色
    getButtonColor() {
      // 优先使用专门的升级按钮颜色
      if (this.data.levelinfo && this.data.levelinfo.card_upgrade_btn_color) {
        return this.data.levelinfo.card_upgrade_btn_color;
      }
      // 如果没有设置专门的按钮颜色，使用默认黑色
      return '#000000';
    },
    
    // 2025-01-03 22:55:53,611-INFO-[membercard_component][get_button_border_color_001] - 获取升级按钮边框颜色
    getButtonBorderColor() {
      // 优先使用专门的升级按钮边框颜色
      if (this.data.levelinfo && this.data.levelinfo.card_upgrade_btn_border_color) {
        return this.data.levelinfo.card_upgrade_btn_border_color;
      }
      // 如果没有设置专门的按钮边框颜色，使用默认灰色
      return '#333333';
    },
    
    // 2025-01-03 22:55:53,616-INFO-[membercard_component][get_button_text_color_001] - 获取升级按钮文字颜色
    getButtonTextColor() {
      const btnColor = this.getButtonColor();
      // 根据按钮背景色判断文字颜色
      if (btnColor === '#000000' || btnColor.toLowerCase().includes('dark') || 
          (btnColor.includes('#') && parseInt(btnColor.replace('#', ''), 16) < 8388608)) {
        return '#ffffff'; // 深色背景用白色文字
      }
      return '#333333'; // 浅色背景用深色文字
    },
    
    // 2025-01-03 22:55:53,612-INFO-[membercard_component][get_activate_button_text_001] - 获取激活按钮文字
    getActivateButtonText() {
      if (this.data.levelinfo && this.data.levelinfo.card_activate_text) {
        return this.data.levelinfo.card_activate_text;
      }
      return '立即升级';
    },
    
    // 2025-01-03 22:55:53,613-INFO-[membercard_component][handle_card_activation_001] - 处理卡片激活
    handleCardActivation() {
      if (this.data.levelinfo && this.data.levelinfo.card_activate_url) {
        // 使用自定义的激活链接
        app.goto(this.data.levelinfo.card_activate_url);
      } else {
        // 默认跳转到升级页面
        app.goto('/pagesExa/my/levelup');
      }
    },
    
    // 格式化会员卡号
    formatCardNo(cardNo) {
      if (!cardNo) return '';
      
      // 每4位添加一个空格
      return cardNo.replace(/(.{4})/g, '$1 ').trim();
    },
    
    // 获取按钮文本
    getActionText() {
      if (!this.data.userinfo || !this.data.userinfo.id) {
        return this.params.registerBtnText || '立即注册';
      }
      
      if (!this.data.card) {
        return this.params.applyBtnText || '立即开通';
      }
      
      return this.params.viewBtnText || '查看详情';
    },
    
    // 处理会员卡操作
    handleCardAction() {
      // 根据不同状态执行不同操作
      if (!this.data.userinfo || !this.data.userinfo.id) {
        // 未登录，跳转注册/登录
        if (this.params.registerUrl) {
          app.goto(this.params.registerUrl);
        } else {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          });
        }
      } else if (!this.data.card) {
        // 已登录但无会员卡，跳转开通
        if (this.params.applyUrl) {
          app.goto(this.params.applyUrl);
        } else {
          uni.showToast({
            title: '会员卡开通功能暂未开放',
            icon: 'none'
          });
        }
      } else {
        // 已有会员卡，跳转详情
        if (this.params.detailUrl) {
          app.goto(this.params.detailUrl);
        } else {
          uni.showToast({
            title: '会员卡详情功能暂未开放',
            icon: 'none'
          });
        }
      }
    },
    
    // 获取特权列表
    getPrivilegeList() {
      // 优先使用后端数据
      if (this.data.card && this.data.card.privileges && this.data.card.privileges.length) {
        return this.data.card.privileges;
      }
      
      // 没有数据则使用默认数据
      return this.defaultPrivileges;
    },
    
    // 获取等级名称
    getLevelName() {
      if (this.data.levelinfo && this.data.levelinfo.name) {
        return this.data.levelinfo.name;
      }
      if (this.data.card && this.data.card.level_name) {
        return this.data.card.level_name;
      }
      return '大众会员';
    },
    
    // 获取下一等级名称
    getNextLevelName() {
      if (this.data.levelinfo && this.data.levelinfo.next_level_name) {
        return this.data.levelinfo.next_level_name;
      }
      return '暂无更高等级';
    },
    
    // 获取升级所需金额
    getUpgradeNeedMoney() {
      return this.params.upgradeNeedMoney || '500';
    },
    
    // 获取下一等级折扣
    getNextLevelDiscount() {
      return this.params.nextLevelDiscount || '9.5';
    },
    
    // 获取进度条百分比
    getProgressPercent() {
      return this.params.progressPercent || '80';
    },
    
    // 获取进度条宽度
    getProgressWidth() {
      const percent = this.getProgressPercent();
      return (310 * percent) / 100;
    },
    
    // 处理会员升级
    handleUpgrade() {
      // 跳转到升级页面
      app.goto('/pagesExa/my/levelup');
    },
    
    // 2025-01-03 23:05:12,001-INFO-[membercard_component][get_container_style_001] - 获取容器样式，包含上浮距离设置
    getContainerStyle() {
      const floatTop = this.params.float_top ? parseFloat(this.params.float_top) : 0;
      return {
        width: '100%',
        marginTop: floatTop ? `-${floatTop}rpx` : '0', // 如果设置了上浮距离，应用负的margin-top
        position: floatTop ? 'relative' : 'static', // 设置了浮动时使用相对定位
        zIndex: floatTop ? '10' : 'auto' // 浮动时提高层级
      };
    }
  }
}
</script>

<style>
/* 引入优设标题黑字体 */
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('https://fonts.gstatic.com/s/notosanstc/v35/-nFuOG829Oofr2wohFbTp9ifNAn722rq0MXz76Cy_CpOtma3uNQ.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* 备用字体设置 */
@font-face {
  font-family: 'YouSheBiaoTiHei-Fallback';
  src: local('PingFang SC Heavy'), local('Microsoft YaHei Bold'), local('SimHei');
  font-weight: 900;
  font-style: normal;
}

.dp-membercard {
  padding: 30rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 主封面背景 */
.main-cover-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  opacity: 0.8;
}

.default-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

/* 颜色渐变遮罩层 - 覆盖在背景图片上 */
.color-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  opacity: 0.7;
  z-index: 1;
  mix-blend-mode: multiply;
}

/* 闪光特效遮罩层 */
.shimmer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  z-index: 2;
  pointer-events: none;
  overflow: hidden;
}

.shimmer-overlay::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    135deg,
    transparent,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 45%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.3) 55%,
    transparent 70%,
    transparent
  );
  animation: shimmer-sweep 2s ease-out 1;
  animation-delay: 0.5s;
}

@keyframes shimmer-sweep {
  0% {
    transform: translateX(-100%) translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%);
    opacity: 0;
  }
}

/* 副封面装饰图 */
.sub-cover-icon {
  width: 100px;
  position: absolute;
  right: 0px;
  top: 5px;
  z-index: 2;
}

/* 会员卡描述 */
.card-desc {
  position: absolute;
  left: 20px;
  top: 60px;
  width: 60%;
  font-size: 24rpx;
  line-height: 1.4;
  z-index: 2;
  opacity: 0.9;
}

/* 会员卡信息区域 */
.card-info-section {
  margin-top: 30rpx;
  padding: 30rpx;
  background-color: #f9f9f9;
  border-radius: 20rpx;
}

.level-info {
  margin-bottom: 30rpx;
}

.level-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 10rpx;
}

.level-desc {
  font-size: 26rpx;
  color: #666666;
  display: block;
}

/* 会员统计信息 */
.member-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #999999;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #333;
  border-radius: 3rpx;
}

.title-text {
  font-weight: bold;
}

.title-desc {
  font-size: 24rpx;
  font-weight: normal;
}

/* 说明信息 */
.card-description {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
}

.desc-text {
  font-size: 24rpx;
  line-height: 1.5;
  color: #999;
}

/* 特权列表 */
.privilege-section {
  margin-top: 30rpx;
}

.section-title {
  margin-bottom: 20rpx;
  padding-left: 16rpx;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #333;
  border-radius: 3rpx;
}

.section-title-text {
  font-size: 30rpx;
  font-weight: bold;
}

.privilege-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.privilege-grid-item {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.privilege-grid-item .privilege-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.privilege-grid-item .privilege-name {
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.privilege-grid-item .privilege-desc {
  font-size: 22rpx;
  color: #999;
}

/* 新的会员卡样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-start {
  justify-content: flex-start;
}

.items-center {
  align-items: center;
}

.self-start {
  align-self: flex-start;
}

.self-stretch {
  align-self: stretch;
}

.flex-1 {
  flex: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.ml-13 {
  margin-left: 26rpx;
}

.mt-14 {
  margin-top: 14rpx;
}

.ml-4 {
  margin-left: 4rpx;
}

.section {
  padding-top: 12rpx;
  background-color: #d3d3d3;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 30rpx rgba(255, 255, 255, 0.1);
  animation: card-glow 4s ease-in-out infinite alternate;
}

.image {
  width: 126rpx;
  height: 54rpx;
}

.group {
  margin-top: 8rpx;
  padding-left: 46rpx;
}

.font {
  font-size: 34rpx;
  font-family: PingFang SC Heavy, PingFang SC;
  line-height: 31.72rpx;
  font-weight: 800;
  color: #333333;
}

.text {
  margin-left: 20rpx;
}

.text_2 {
  line-height: 31.62rpx;
}

.font_2 {
  font-size: 24rpx;
  font-family: 萍方-简, PingFang SC;
  line-height: 22.2rpx;
  color: #333333;
}

.text_3 {
  margin-left: 20rpx;
}

.section_2 {
  background-color: #bbbbbb;
  border-radius: 8rpx;
  height: 14rpx;
}

.section_3 {
  background-color: #9f9f9f;
  border-radius: 8rpx;
  width: 310rpx;
  height: 14rpx;
}

.font_3 {
  font-size: 24rpx;
  font-family: 萍方-简, PingFang SC;
  line-height: 28rpx;
  color: #ffffff;
}

.text_4 {
  font-size: 26rpx;
  line-height: 19.02rpx;
}

.image_2 {
  opacity: 0.49;
  width: 152rpx;
  height: 130rpx;
}

.section_4 {
  margin-top: 22rpx;
}

.view {
  padding: 20rpx 0 20rpx;
  background-color: #8b8b8b;
  border-radius: 0rpx 0rpx 10rpx 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upgrade-btn {
  background: #333333;
  border-radius: 30rpx;
  padding: 12rpx 70rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.upgrade-btn::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

.btn-text {
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 2rpx;
}

.group_2 {
  display: none;
}

.pos {
  position: absolute;
  right: 112rpx;
  top: 112rpx;
}

.pos_2 {
  position: absolute;
  right: 112rpx;
  bottom: 30rpx;
}

/* 响应式调整 */
@media screen and (max-width: 320px) {
  .privilege-grid {
    grid-template-columns: 1fr;
  }
}

.vicon{
	width: 100px;
	position: absolute;
	right: 0px;
	top: 5px;
	z-index: 2;
}

.vtxt{
    position: absolute;
    width: 120px;
    left: 20px;
    top: 20px;
    font-size: 22px;
    font-weight: 900;
    font-family: 'HarmonyOS Sans SC', 'Source Han Sans CN Heavy', 'Noto Sans SC Black', 'PingFang SC Heavy', 'Microsoft YaHei Bold', sans-serif;
    z-index: 2;
    letter-spacing: 1px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.15);
    color: #ffffff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.vbtn{
	position: absolute;
	right: 0;
	margin: auto;
	left: 0;
	bottom: 15px;
	background: #000;
	color: #fff;
	padding: 7px 20px;
	border-radius: 20px;
	width: 100px;
	text-align: center;
	z-index: 2;
	border: 2px solid transparent;
	transition: all 0.3s ease;
}

@keyframes card-glow {
  0% {
    box-shadow: 0 0 30rpx rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 50rpx rgba(255, 255, 255, 0.2), 0 0 80rpx rgba(255, 255, 255, 0.1);
  }
}

</style> 