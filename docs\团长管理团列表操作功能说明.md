# 团长管理团列表操作功能说明文档

## 功能概述

在团长管理页面的团列表中，增加了一系列操作按钮，包括分享、编辑、删除、上架、下架、隐藏和复制功能，方便团长对自己的团进行管理。

## 前端实现

在团长管理页面的团列表中，每个团项目底部增加了操作按钮区域，包含以下按钮：

1. **编辑**：跳转到团编辑页面
2. **复制**：复制一个相同的团
3. **上架/下架**：根据当前状态显示对应按钮，控制团的上架状态
4. **隐藏/显示**：根据当前状态显示对应按钮，控制团的显示状态
5. **删除**：删除该团
6. **分享**：分享该团到微信等平台

## 后端接口需求

### 1. 获取团列表接口

**接口名称**：`ApituanzhangAdminIndex/getmylisttuan`

**请求参数**：
- `bid`：团长ID
- `cid`：分类ID
- `pagenum`：页码
- `keyword`：搜索关键词

**返回数据**：
- `status`：状态码，1表示成功
- `data`：团列表数据
- `listtype`：列表类型
- `clist`：分类列表
- `set`：设置信息
- `title`：页面标题

**团数据字段**：
- `id`：团ID
- `name`：团名称
- `pic`：团主图
- `pic2`：团图片集（逗号分隔的图片URL）
- `author`：团长名称
- `priceRange`：价格范围
- `readcount`：阅读数
- `business_tel`：商家电话
- `status`：上架状态（0未上架，1已上架）
- `is_hidden`：隐藏状态（0显示，1隐藏）

### 2. 编辑团

前端直接跳转到编辑页面：`/pagesExa/tuanzhangadmin/tuan/edit?id=团ID`

### 3. 复制团接口

**接口名称**：`ApituanzhangAdminIndex/copytuan`

**请求参数**：
- `id`：要复制的团ID

**返回数据**：
- `status`：状态码，1表示成功
- `msg`：操作结果消息

### 4. 设置团状态接口（上架/下架）

**接口名称**：`ApituanzhangAdminIndex/setstatus`

**请求参数**：
- `id`：团ID
- `status`：状态值（0下架，1上架）

**返回数据**：
- `status`：状态码，1表示成功
- `msg`：操作结果消息

### 5. 设置团隐藏状态接口

**接口名称**：`ApituanzhangAdminIndex/sethidden`

**请求参数**：
- `id`：团ID
- `is_hidden`：隐藏状态（0显示，1隐藏）

**返回数据**：
- `status`：状态码，1表示成功
- `msg`：操作结果消息

### 6. 删除团接口

**接口名称**：`ApituanzhangAdminIndex/deltuan`

**请求参数**：
- `id`：要删除的团ID

**返回数据**：
- `status`：状态码，1表示成功
- `msg`：操作结果消息

## 后端实现说明

### 1. 复制团功能

复制团功能需要在后端实现以下逻辑：
1. 根据传入的团ID查询原团信息
2. 复制原团的所有信息，生成一个新的团记录
3. 将新团的状态设置为未上架（status=0）
4. 可以在团名称后添加"(复制)"字样，以区分原团
5. 返回操作结果

```php
public function copytuan()
{
    $id = input('post.id/d');
    if (!$id) {
        return $this->json(['status' => 0, 'msg' => '参数错误']);
    }
    
    // 查询原团信息
    $tuan = Db::name('daihuotuan')->where('id', $id)->find();
    if (!$tuan) {
        return $this->json(['status' => 0, 'msg' => '团不存在']);
    }
    
    // 复制团信息
    unset($tuan['id']);
    $tuan['name'] = $tuan['name'] . '(复制)';
    $tuan['status'] = 0; // 默认设为未上架
    $tuan['create_time'] = time();
    
    // 插入新团
    $newId = Db::name('daihuotuan')->insertGetId($tuan);
    
    if ($newId) {
        return $this->json(['status' => 1, 'msg' => '复制成功']);
    } else {
        return $this->json(['status' => 0, 'msg' => '复制失败']);
    }
}
```

### 2. 设置团状态功能

设置团状态功能需要在后端实现以下逻辑：
1. 根据传入的团ID和状态值更新团的状态
2. 返回操作结果

```php
public function setstatus()
{
    $id = input('post.id/d');
    $status = input('post.status/d');
    
    if (!$id) {
        return $this->json(['status' => 0, 'msg' => '参数错误']);
    }
    
    // 更新团状态
    $result = Db::name('daihuotuan')->where('id', $id)->update(['status' => $status]);
    
    if ($result !== false) {
        return $this->json(['status' => 1, 'msg' => $status == 1 ? '上架成功' : '下架成功']);
    } else {
        return $this->json(['status' => 0, 'msg' => '操作失败']);
    }
}
```

### 3. 设置团隐藏状态功能

设置团隐藏状态功能需要在后端实现以下逻辑：
1. 根据传入的团ID和隐藏状态值更新团的隐藏状态
2. 返回操作结果

```php
public function sethidden()
{
    $id = input('post.id/d');
    $is_hidden = input('post.is_hidden/d');
    
    if (!$id) {
        return $this->json(['status' => 0, 'msg' => '参数错误']);
    }
    
    // 更新团隐藏状态
    $result = Db::name('daihuotuan')->where('id', $id)->update(['is_hidden' => $is_hidden]);
    
    if ($result !== false) {
        return $this->json(['status' => 1, 'msg' => $is_hidden == 1 ? '隐藏成功' : '显示成功']);
    } else {
        return $this->json(['status' => 0, 'msg' => '操作失败']);
    }
}
```

### 4. 删除团功能

删除团功能需要在后端实现以下逻辑：
1. 根据传入的团ID删除对应的团记录
2. 返回操作结果

```php
public function deltuan()
{
    $id = input('post.id/d');
    
    if (!$id) {
        return $this->json(['status' => 0, 'msg' => '参数错误']);
    }
    
    // 删除团
    $result = Db::name('daihuotuan')->where('id', $id)->delete();
    
    if ($result) {
        return $this->json(['status' => 1, 'msg' => '删除成功']);
    } else {
        return $this->json(['status' => 0, 'msg' => '删除失败']);
    }
}
```

## 数据库字段说明

在`daihuotuan`表中，需要确保有以下字段：

- `id`：主键，自增
- `name`：团名称
- `pic`：团主图
- `pic2`：团图片集（逗号分隔的图片URL）
- `author`：团长名称
- `priceRange`：价格范围
- `readcount`：阅读数
- `business_tel`：商家电话
- `status`：上架状态（0未上架，1已上架）
- `is_hidden`：隐藏状态（0显示，1隐藏）
- `create_time`：创建时间

如果`is_hidden`字段不存在，需要添加该字段：

```sql
ALTER TABLE `daihuotuan` ADD COLUMN `is_hidden` tinyint(1) NOT NULL DEFAULT 0 COMMENT '隐藏状态：0显示，1隐藏';
```

## 注意事项

1. 所有接口需要进行权限验证，确保只有团长本人可以操作自己的团
2. 删除操作需要谨慎，建议在删除前检查是否有关联数据
3. 上架操作可能需要检查团信息是否完整
4. 复制功能应该复制团的所有相关信息，包括图片等资源
5. 分享功能需要生成正确的分享链接和图片 