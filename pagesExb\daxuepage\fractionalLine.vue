<!-- 分数线查询 -->
<template>
	<view class="box">
		<view style="font-size: 30rpx;font-weight: bold;margin-bottom: 30rpx;text-align: center;">选择学校分类</view>

		<radio-group @change="radioChange" class="item-box">
			<view class="item">
				<view class="justify-between">
					<view>高考</view>
					<view>
						<radio value="option1" :checked="option1Checked" activeBorderColor="#8799fa"
							activeBackgroundColor="#8799fa" />
					</view>
				</view>

				<view v-if="option1Checked">
					<!-- 物理，历史 -->
					<view>
						<view style="height: 36px;line-height: 36px;">
							请选择(二选一)
						</view>
						<view class="tag-selector">
							<view v-for="(tag, index) in tags" :key="index" class="tag-item"
								:class="{ 'selected': selectedTags.includes(tag.value) }"
								@click="toggleTag(tag.value,'1')">
								{{ tag.text }}
							</view>
						</view>
					</view>
					<!-- <view>
						<view style="height: 36px;line-height: 36px;">
							请选择(四选二)
						</view>
						<view class="tag-selector">
							<view v-for="(tag, index) in subjectTags" :key="index" class="tag-item2"
								:class="{ 'selected': subjectSelectedTags.includes(tag.value) }"
								@click="toggleTag(tag.value,'2')">
								{{ tag.text }}
							</view>
						</view>
					</view> -->
					<view style="padding: 14rpx;border: 1px solid #ccc;background-color: #fff;border-radius: 8rpx;">
						<input type="text" v-model="score1" placeholder="请输入分数" class="custom-input" />
					</view>
				</view>
			</view>
			<view class="item">
				<view class="justify-between">
					<view>单招</view>
					<view>
						<radio value="option2" :checked="option2Checked" activeBorderColor="#8799fa"
							activeBackgroundColor="#8799fa" />
					</view>
				</view>

				<view v-if="option2Checked">
					<picker @change="pickerChange($event,'1')" :value="index" :range="array">
						<view class="picker-text">{{school?school:'请选择'}}</view>
					</picker>

					<view style="padding: 14rpx;border: 1px solid #ccc;background-color: #fff;border-radius: 8rpx;">
						<input type="text" v-model="score2" placeholder="请输入分数" class="custom-input" />
					</view>
				</view>

			</view>
			<view class="item">
				<view class="justify-between">
					<view>专升本</view>
					<view>
						<radio value="option3" :checked="option3Checked" activeBorderColor="#8799fa"
							activeBackgroundColor="#8799fa" />
					</view>
				</view>

				<view v-if="option3Checked">
					<!-- 		<picker @change="pickerChange($event,'2')" :value="index" :range="disciplineArray">
						<view class="picker-text">{{discipline?discipline:'请选择'}}</view>
					</picker> -->

					<view style="padding: 14rpx;border: 1px solid #ccc;background-color: #fff;border-radius: 8rpx;">
						<input type="text" v-model="score3" placeholder="请输入分数" class="custom-input" />
					</view>
				</view>
			</view>
		</radio-group>

		<button class="gradient-button" @click="search">查询</button>

	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				option1Checked: false, // 高考分数线
				option2Checked: false, // 分类单招分数线
				option3Checked: false, // 专升本分数线
				tags: [{
						text: '物理',
						value: '物理'
					},
					{
						text: '历史',
						value: '历史'
					},
				],
				selectedTags: [],
				maxSelect: 1,

				subjectTags: [{
						text: '生物',
						value: '生物'
					},
					{
						text: '化学',
						value: '化学'
					},
					{
						text: '地理',
						value: '地理'
					},
					{
						text: '政治',
						value: '政治'
					}
				],
				subjectSelectedTags: [],
				subjectMaxSelect: 2,
				array: ['中职', '普高'],
				school: '',
				disciplineArray: ['理科', '文科'],
				discipline: '',
				score1: '', // 高考分数线
				score2: '', // 分类单招分数线
				score3: '' // 专升本分数线
			}
		},
		methods: {
			radioChange(e) {
				console.log('选中项的value值：', e.detail.value);
				this.selectedTags = []
				this.subjectSelectedTags = []
				this.school = ''
				this.score1 = '', // 高考分数线
					this.score2 = '', // 分类单招分数线
					this.score3 = '' // 专升本分数线
				// 根据选中的值更新数据
				switch (e.detail.value) {
					case 'option1':
						this.option1Checked = true;
						this.option2Checked = false;
						this.option3Checked = false;
						break;
					case 'option2':
						this.option1Checked = false;
						this.option2Checked = true;
						this.option3Checked = false;
						break;
					case 'option3':
						this.option1Checked = false;
						this.option2Checked = false;
						this.option3Checked = true;
						break;
					default:
						break;
				}
			},
			toggleTag(tagValue, type) {
				if (type == '1') {
					const index = this.selectedTags.indexOf(tagValue);
					if (index > -1) {
						// 如果已经选中，则取消选中
						this.selectedTags.splice(index, 1);
					} else {
						// 如果未选中，且当前选中数量未达到最大限制，则添加到选中列表
						if (this.selectedTags.length < this.maxSelect) {
							this.selectedTags.push(tagValue);
						} else {
							this.selectedTags = []
							this.selectedTags.push(tagValue);
						}
					}
				} else {
					const index = this.subjectSelectedTags.indexOf(tagValue);
					if (index > -1) {
						// 如果已经选中，则取消选中
						this.subjectSelectedTags.splice(index, 1);
					} else {
						// 如果未选中，且当前选中数量未达到最大限制，则添加到选中列表
						if (this.subjectSelectedTags.length < this.subjectMaxSelect) {
							this.subjectSelectedTags.push(tagValue);
						} else {
							this.subjectSelectedTags = []
							this.subjectSelectedTags.push(tagValue);
						}
					}
				}
			},
			pickerChange(e, type) {
				if (type == '1') {
					this.school = this.array[e.detail.value]
				} else {
					this.discipline = this.disciplineArray[e.detail.value]
				}
			},
			// 查询
			search() {
				let type = this.option1Checked ? '高考分数线' : (this.option2Checked ? '分类单招分数线' : '专升本分数线')
				let score = this.option1Checked ? this.score1 : (this.option2Checked ? this.score2 : this.score3)
				let subject_choice = this.option1Checked ? this.selectedTags[0] : ''
				let school_type = this.option2Checked ? this.school : ''
				uni.navigateTo({
					url: '/pagesExa/daxuepage/fractionalLineList?type=' + type + '&score=' + score +
						'&subject_choice=' + subject_choice
				});
			}

		}
	}
</script>

<style scoped>
	.box {
		padding: 30rpx 0;
		/* 渐变方向：从上到下 */
		background-image: linear-gradient(to bottom, #E6E6FA, #B0E0E6);
		height: 100vh;
	}

	.item-box {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.item {
		border-radius: 20rpx;
		min-height: 140rpx;
		width: 70%;
		background-color: #fff;
		box-shadow: 0px 8rpx 16rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 30rpx;
		line-height: 70px;
		padding: 0 30rpx;
		background-image: linear-gradient(to bottom, #fafafa, #f1f0fa);
		padding-bottom: 20rpx;
	}

	.justify-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%
	}

	/* 紫色渐变背景按钮样式 */
	.gradient-button {
		background-image: linear-gradient(to right, #25b9e2, #4b9cff);
		/* 从左到右的紫色渐变 */
		color: white;
		/* 文字颜色为白色 */
		border: none;
		/* 无边框 */
		font-size: 28rpx;
		/* 字体大小 */
		cursor: pointer;
		/* 鼠标悬停时显示指针 */
		outline: none;
		/* 点击时无轮廓 */
		text-align: center;
		transition: background 0.3s ease;
		/* 渐变效果 */
		border-radius: 100rpx;
		width: 70%;
		height: 80rpx;
		line-height: 80rpx;
		position: fixed;
		bottom: 30rpx;
		left: 15%;
	}

	.custom-input {
		width: 100%;
		font-size: 24rpx;
	}

	.custom-input::placeholder {
		color: #aaa;
		font-size: 24rpx;
	}

	.tag-selector {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}

	.tag-item {
		padding: 10rpx 20rpx;
		border: 1px solid #ccc;
		border-radius: 4px;
		cursor: pointer;
		height: 52rpx;
		line-height: 30rpx;
		width: 47%;
		text-align: center;
		font-size: 20rpx;
	}

	.tag-item2 {
		padding: 10rpx 20rpx;
		border: 1px solid #ccc;
		border-radius: 4px;
		cursor: pointer;
		height: 52rpx;
		line-height: 28rpx;
		width: 21%;
		font-size: 20rpx;
		text-align: center;
	}

	.tag-item2.selected {
		background-color: #3094ff;
		color: white;
		border-color: #3094ff;
	}

	.tag-item.selected {
		background-color: #3094ff;
		color: white;
		border-color: #3094ff;
	}

	.picker-text {
		color: #848484;
		padding: 14rpx;
		border: 1px solid #ccc;
		background-color: #fff;
		border-radius: 8rpx;
		height: 76rpx;
		line-height: 40rpx;
		margin-bottom: 30rpx;
		font-size: 26rpx;
	}
</style>
