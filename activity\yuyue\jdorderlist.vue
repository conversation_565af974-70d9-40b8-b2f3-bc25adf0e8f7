<template>
<view class="container">
	<block v-if="isload">
		<view>
			<view class="search-container">
				<view class="search-box">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input class="search-text" placeholder="搜索商家" placeholder-style="color:#aaa;font-size:24rpx" @confirm="searchConfirm"/>
				</view>
			</view>
			<block v-for="(item, index) in datalist" :key="item.id">
			<view class="order-box" @tap="goto" :data-url="'jdorderdetail?id=' + item.id">
				<view class="head">
					<view v-if="item.fwtype==1">
						<view class="f1" v-if="item.status==3"><image src="/static/peisong/ps_time.png" class="img"/>已完成</view>
						<view class="f1" v-if="item.status==1"><image src="/static/peisong/ps_time.png" class="img"/><text class="t1">等待客户上门</text> </view>
						<view class="f1" v-if="item.status==2"><image src="/static/peisong/ps_time.png" class="img"/><text class="t1" style="margin-left:10rpx">服务中</text></view>
						<view class="f1" v-if="item.status==2"><image src="/static/peisong/ps_time.png" class="img"/><text class="t1" style="margin-left:10rpx">已取消</text></view>
					</view>
					<view v-else-if="item.fwtype==2">
						<view class="f1" v-if="item.status==3"><image src="/static/peisong/ps_time.png" class="img"/>已完成</view>
						<view class="f1" v-else-if="item.status==1"><image src="/static/peisong/ps_time.png" class="img"/>期望上门时间<text class="t1">{{item.orderinfo.yydate}}</text> </view>
						<block v-else-if="item.status==2">
								<view class="f1" v-if="showaddmoney">
									<block v-if="!item.sign_status">
										<image src="/static/peisong/ps_time.png" class="img"/>已到达，等待服务
									</block>
									<block v-else="!item.sign_status">
										<image src="/static/peisong/ps_time.png" class="img"/>已到达，正在服务
									</block>
								</view>
								<view class="f1" v-else>	<image src="/static/peisong/ps_time.png" class="img"/>已到达，服务中</view>
						</block>
						<view class="f1" v-if="item.status==-1"><image src="/static/peisong/ps_time.png" class="img"/>已取消</view>
					</view>
					<view class="flex1"></view>
					<view class="f2"><text class="t1">{{item.ticheng}}</text>元</view>
				</view>
				<view class="content">
					<view class="f1">
						<view class="t1"><text class="x1">{{item.juli}}</text><text class="x2">{{item.juli_unit}}</text></view>
						<view class="t2"><image src="/static/peisong/ps_juli.png" class="img"/></view>
						<view class="t3"><text class="x1">{{item.juli2}}</text><text class="x2">{{item.juli2_unit}}</text></view>
					</view>
					<view class="f2">
						<view class="t1">{{item.binfo.name}}</view>
						<view class="t2">{{item.binfo.address}}</view>
						<view class="t3">{{item.orderinfo.address}}</view>
						<view class="t2">{{item.orderinfo.area}}</view>
					</view>
					<view class="f3" @tap.stop="daohang" :data-index="index"><image :src="pre_url+'/static/img/peisong/ps_daohang.png'" class="img"/></view>
				</view>
				<view v-if="item.fwtype==1" class="op">
					<view class="t1" v-if="item.status==1">已接单，待顾客上门</view>
					<view class="t1" v-if="item.status==2">顾客已到达</view>
					<view class="t1" v-if="item.status==3">已完成</view>
					<view class="flex1"></view>
					<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="2" v-if="item.status==1">顾客已到达</view>
					<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="3" v-if="item.status==2">我已完成</view>
				</view>
				<view v-else-if="item.fwtype==2" class="op">
					<view class="t1" v-if="item.status==1">已接单，等待上门</view>
					<view class="t1" v-if="item.status==2">已到达，共用时{{item.useminute}}分钟</view>
					<view class="t1" v-if="item.status==3">已完成</view>
					<view class="flex1"></view>
				
					<block v-if="showaddmoney">
							<view class="btn1 btn2" @tap.stop="addmoney" v-if="item.sign_status==1 && item.status==2 && item.addprice<=0" :data-id="item.id">补差价</view>							<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="2" v-if="item.status==1">出发</view>
							<view class="btn1 btn2" @tap.stop="showpaycode" v-if="item.addprice>0" :data-id="item.id" :data-key="index">查看补余款</view>
							<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="5" v-if="!item.sign_status && item.status==2">开始服务</view>
							<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="3" v-if="item.sign_status==1 && item.status==2">服务完成</view>
					</block>
					<block v-else>
						<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="2" v-if="item.status==1">我已到达</view>
						<view class="btn1" @tap.stop="setst" :data-id="item.id" data-st="3" v-if="item.status==2">我已完成</view>
					</block>
					
			
				</view>
			
			</view>
			</block>
		
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
		</view>
		<!-- <view style="width:100%;height:120rpx"></view> -->
		<!-- <view class="bottom">
			<view class="my">
				<image src="/static/img/my.png" class="img"/>
				<text>我的</text>
			</view>
			<view class="btn1" @tap="setpsst" data-st="1" v-if="psuser.status==0">暂停接单中</view>
			<view class="btn2" :style="{background:t('color1')}" @tap="setpsst" data-st="0" v-if="psuser.status==1">开启接单中</view>
		</view> -->
		
		<view class="tabbar" v-if="showtabbar">
			<view class="tabbar-bot"></view>
			<view class="tabbar-bar" style="background-color:#ffffff">
				<view @tap="goto" data-url="dating" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/home.png'"></image>
					</view>
					<view class="tabbar-text">大厅</view>
				</view>
				<view @tap="goto" data-url="jdorderlist" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/order'+(st!=3?'2':'')+'.png'"></image>
					</view>
					<view class="tabbar-text" :class="st!=3?'active':''">订单</view>
				</view>
				<view @tap="goto" data-url="jdorderlist?st=3" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/orderwc'+(st==3?'2':'')+'.png'"></image>
					</view>
					<view class="tabbar-text" :class="st==3?'active':''">已完成</view>
				</view>
				<view v-if="showform" @tap="goto" data-url="formlog" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/dangan.png'"></image>
					</view>
					<view class="tabbar-text">档案</view>
				</view>
				<view @tap="goto" data-url="my" data-opentype="reLaunch" class="tabbar-item">
					<view class="tabbar-image-box">
						<image class="tabbar-icon" :src="pre_url+'/static/img/peisong/my.png'"></image>
					</view>
					<view class="tabbar-text">我的</view>
				</view>
			</view>
		</view>
		
		<view class="modal" v-if="showmodal">
			<view class="addmoney">
					<view class="title">{{addprice>0?'修改':'创建'}}补余款</view>
					<view class="item">
						<label class="label">金额：</label><input type="text" @input="bindMoney" name="blance_price" :value="addprice" placeholder="请输入补余款金额"  placeholder-style="font-size:24rpx"/>元
					</view>
					<view class="btn"><button class="btn-cancel" @tap="cancel">取消</button><button class="confirm"  :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"  @tap.stop="addconfirm">确定</button></view>
			</view>
		</view>	
		
		<view class="modal" v-if="showpaycodes">
			<view class="addmoney">
					<view class="title">查看补余款</view>
					<view class="item" >
						<label>金额：</label><text class="price">{{addprice}}</text>元
					</view>
					<view class="item" style="padding-top: 0;">
						<label>支付状态：</label> <text class="t2" v-if="addmoneystatus==1"> 已支付</text> 
						<text class="t2" v-if="addmoneystatus==0" style="color:red;"> 待支付</text>
					</view>
					<view class="qrcode"><image :src="paycode"></image></view>
					<view class="btn"><button class="btn-cancel" @tap="cancel">关闭</button> 
					
					<button class="btn-update" v-if="addmoneystatus==0" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"  @tap="update" :data-key="index" :data-id="id"  >修改差价</button></view>
			</view>
		</view>	
		
		<!-- 添加打卡弹窗 -->
		<view class="punch-modal" v-if="showPunchModal">
			<view class="punch-content">
				<view class="punch-header">
					<text class="punch-title">{{punchTitle}}</text>
					<view class="close-btn" @tap="closePunchModal">×</view>
				</view>
				
				<!-- 位置信息区域 -->
				<view class="location-section">
					<view class="section-title"><text class="icon">📍</text> 位置信息</view>
					<view class="location-content" v-if="punchLocationInfo">
						<view class="location-status success">
							<text class="status-text">已获取位置信息</text>
							<text class="location-detail">经度: {{punchLocationInfo.longitude}}</text>
							<text class="location-detail">纬度: {{punchLocationInfo.latitude}}</text>
						</view>
					</view>
					<view class="location-content" v-else>
						<view class="location-status" :class="{'loading': isLocating}">
							<text class="status-text">{{isLocating ? '获取位置中...' : '点击获取位置'}}</text>
						</view>
						<view class="get-location-btn" @tap="getPunchLocation" v-if="!isLocating && !punchLocationInfo">
							获取位置信息
						</view>
					</view>
				</view>
				
				<!-- 照片上传区域 -->
				<view class="photo-section">
					<view class="section-title"><text class="icon">📷</text> {{punchPhotoType}}</view>
					<view class="photo-content" v-if="punchPhoto">
						<image :src="punchPhoto" class="preview-image" mode="aspectFit" @tap="previewPunchPhoto"></image>
						<view class="photo-actions">
							<view class="reselect-btn" @tap="reselectPunchPhoto">重新选择</view>
						</view>
					</view>
					<view class="photo-content" v-else>
						<view class="photo-placeholder" @tap="selectPunchPhoto">
							<text class="placeholder-icon">+</text>
							<text class="placeholder-text">点击上传照片</text>
						</view>
					</view>
				</view>
				
				<!-- 操作按钮 -->
				<view class="punch-actions">
					<view class="cancel-btn" @tap="closePunchModal">取消</view>
					<view class="submit-btn" @tap="submitPunchData" :class="{'disabled': !canSubmitPunch}">
						确认提交
					</view>
				</view>
			</view>
		</view>
	</block>
	
	
	
	
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
	<view style="display:none">{{timestamp}}</view>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,

      st: '11',
      datalist: [],
      pagenum: 1,
      nomore: false,
      nodata: false,
			keyword:'',
			interval1:null,
			timestamp:'',
			showform:0,
			showtabbar:false,
			showaddmoney:false,
			showmodal:false,
			addprice:0,
			showpaycodes:false,
			paycode:'',
			addmoneystatus:0,
			
			// 打卡弹窗相关
			showPunchModal: false,
			punchId: null,
			punchSt: null,
			punchTitle: '',
			punchPhotoType: '',
			punchLocationInfo: null,
			punchPhoto: null,
			isLocating: false
    };
  },
  computed: {
    // 是否可以提交打卡
    canSubmitPunch: function() {
      return this.punchLocationInfo && this.punchPhoto;
    }
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st || '11';
		if(this.opt.mid){
			this.showtabbar = false;
		}else{
			this.showtabbar = true;
		}
		this.getdata();
  },
	onUnload:function(){
		clearInterval(this.interval1);
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var st = that.st;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
			that.nodata = false;
			that.nomore = false;
      app.post('ApiYuyueWorker/orderlist', {st: st,pagenum: pagenum,keyword:keyword,mid:this.opt.mid}, function (res) {
				if(res.status==0){
					app.alert(res.msg);
					return;
				}
        var data = res.datalist;
        if (pagenum == 1) {
					that.datalist = data;
					that.nowtime = res.nowtime
					that.showform = res.showform;
					that.showaddmoney = res.addmoney
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
					that.updatemylocation();
					clearInterval(that.interval1);
					that.interval1 = setInterval(function(){
						that.updatemylocation(true);
						that.nowtime = that.nowtime + 10;
					},10000)
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
		updatemylocation:function(){
			var that = this;
			app.getLocation(function(res){
				var longitude = res.longitude;
				var latitude = res.latitude;
				var datalist = that.datalist;
				console.log(datalist);
				for(var i in datalist){
					var thisdata = datalist[i];
					var rs = that.getdistance(thisdata.longitude2,thisdata.latitude2,longitude,latitude,1);
					thisdata.juli2 = rs.juli;
					thisdata.juli2_unit = rs.unit;
					thisdata.leftminute = parseInt((thisdata.yujitime - that.nowtime) / 60);
					datalist[i] = thisdata;
				}
				that.datalist = datalist;
				that.timestamp = parseInt((new Date().getTime())/1000);
				app.get('ApiYuyueWorker/updatemylocation',{longitude:longitude,latitude:latitude,t:that.timestamp},function(){
					//if(needload) that.getdata();
				});
			});
		},
		getdistance: function (lng1, lat1, lng2, lat2) {
			if(!lat1 || !lng1 || !lat2 || !lng2) return '';
			var rad1 = lat1 * Math.PI / 180.0;
			var rad2 = lat2 * Math.PI / 180.0;
			var a = rad1 - rad2;
			var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
			var r = 6378137;
			var juli = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));
			var unit = 'm';
			if(juli> 1000){
				juli = juli/1000;
				unit = 'km';
			}
			juli = juli.toFixed(1);
			return {juli:juli,unit:unit}
		},
    setst: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var st = e.currentTarget.dataset.st;
      
      // 通过打开弹窗代替直接处理
      that.openPunchModal(e);
    },
    
    // 打开打卡弹窗
    openPunchModal: function(e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var st = e.currentTarget.dataset.st;
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][openPunchModal_001]打开打卡弹窗，id:", id, "状态:", st);
      
      // 设置打卡类型和标题
      var title = '';
      var photoType = '';
      
      if(st == 2){
        if(that.showaddmoney){
          title = '出发打卡';
          photoType = '出发前照片';
        } else {
          title = '到达打卡';
          photoType = '到达现场照片';
        }
      } else if(st == 3 || st == 5) {
        title = '完成打卡';
        photoType = '服务完成照片';
      }
      
      // 重置打卡状态
      that.punchId = id;
      that.punchSt = st;
      that.punchTitle = title;
      that.punchPhotoType = photoType;
      that.punchLocationInfo = null;
      that.punchPhoto = null;
      that.showPunchModal = true;
      
      // 自动开始获取位置
      that.getPunchLocation();
    },
    
    // 关闭打卡弹窗
    closePunchModal: function() {
      this.showPunchModal = false;
    },
    
    // 获取打卡位置
    getPunchLocation: function() {
      var that = this;
      
      that.isLocating = true;
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getPunchLocation_001]开始获取位置");
      
      app.getLocation(function(locRes) {
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getPunchLocation_002]位置获取成功:", JSON.stringify(locRes));
        
        that.isLocating = false;
        that.punchLocationInfo = {
          longitude: locRes.longitude,
          latitude: locRes.latitude
        };
        
        // 显示位置获取成功提示
        uni.showToast({
          title: '位置获取成功',
          icon: 'success',
          duration: 1500
        });
      }, function(err) {
        console.log("2025-01-03 22:55:53,565-ERROR-[jdorderlist][getPunchLocation_003]位置获取失败:", JSON.stringify(err));
        
        that.isLocating = false;
        
        // 提示重试
        uni.showModal({
          title: '位置获取失败',
          content: '请检查是否授予定位权限，并重试',
          confirmText: '重试',
          cancelText: '取消',
          success: function(res) {
            if(res.confirm) {
              that.getPunchLocation();
            }
          }
        });
      });
    },
    
    // 选择打卡照片
    selectPunchPhoto: function() {
      var that = this;
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][selectPunchPhoto_001]开始选择照片");
      
      uni.showActionSheet({
        itemList: ['拍照', '从相册选择'],
        success: function(res) {
          var sourceType = res.tapIndex === 0 ? ['camera'] : ['album'];
          
          uni.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: sourceType,
            success: function(res) {
              if(res.tempFilePaths && res.tempFilePaths.length > 0) {
                console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][selectPunchPhoto_002]照片选择成功:", res.tempFilePaths[0]);
                
                that.punchPhoto = res.tempFilePaths[0];
                
                // 显示选择成功提示
                uni.showToast({
                  title: '照片选择成功',
                  icon: 'success',
                  duration: 1500
                });
              }
            },
            fail: function(err) {
              console.log("2025-01-03 22:55:53,565-ERROR-[jdorderlist][selectPunchPhoto_003]照片选择失败:", JSON.stringify(err));
              
              uni.showToast({
                title: '照片选择失败',
                icon: 'none',
                duration: 1500
              });
            }
          });
        }
      });
    },
    
    // 重新选择照片
    reselectPunchPhoto: function() {
      this.punchPhoto = null;
      this.selectPunchPhoto();
    },
    
    // 预览选择的照片
    previewPunchPhoto: function() {
      if(this.punchPhoto) {
        uni.previewImage({
          urls: [this.punchPhoto],
          current: this.punchPhoto
        });
      }
    },
    
    // 提交打卡数据
    submitPunchData: function() {
      var that = this;
      
      if(!that.canSubmitPunch) {
        uni.showToast({
          title: '请先获取位置并上传照片',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_001]准备提交打卡数据");
      
      // 准备参数
      var params = {
        id: that.punchId,
        st: that.punchSt,
        longitude: that.punchLocationInfo.longitude,
        latitude: that.punchLocationInfo.latitude
      };
      
      // 设置照片参数
      if(that.punchSt == 2) {
        params.arrival_photo = that.punchPhoto;
      } else {
        params.complete_photo = that.punchPhoto;
      }
      
      console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_002]提交参数:", JSON.stringify(params));
      
      // 显示提交中
      uni.showLoading({
        title: '提交中...',
        mask: true
      });
      
      // 提交数据
      app.post('ApiYuyueWorker/setst', params, function(data) {
        uni.hideLoading();
        console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_003]提交响应:", JSON.stringify(data));
        
        if(data.status === 1) {
          // 成功，关闭弹窗并刷新数据
          that.closePunchModal();
          
          // 提示成功
          app.success(data.msg);
          
          // 显示详细信息
          var title = '';
          var content = '';
          
          if(that.punchSt == 2) {
            if(that.showaddmoney) {
              title = '出发打卡成功';
              content = '您已成功打卡出发，请尽快前往客户位置';
            } else {
              title = '到达打卡成功';
              content = '您已成功打卡到达，请开始服务';
            }
          } else {
            title = '完成打卡成功';
            content = '服务已完成，感谢您的工作';
          }
          
          // 如果有距离信息，显示距离
          if(data.distance && that.punchSt == 2) {
            content = '您距离客户位置: ' + that.formatDistance(data.distance) + '\n' + content;
          }
          
          // 显示成功弹窗
          uni.showModal({
            title: title,
            content: content,
            showCancel: false,
            success: function() {
              // 刷新数据
              setTimeout(function() {
                that.getdata();
              }, 500);
            }
          });
        } else {
          // 失败提示
          app.alert(data.msg || '提交失败');
        }
      });
    },
    
    // 格式化距离显示
    formatDistance: function (distance) {
      if (!distance && distance !== 0) {
        return '未知';
      }
      
      distance = parseFloat(distance);
      if (isNaN(distance)) {
        return '未知';
      }
      
      if (distance >= 1000) {
        return (distance / 1000).toFixed(2) + '公里';
      } else {
        return parseInt(distance) + '米';
      }
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword
      that.getdata();
    },
		addmoney:function(e){
			var that=this
			this.showmodal=true
			this.id= e.currentTarget.dataset.id
		},
		cancel:function(e){
			var that=this
			this.showmodal=false
			this.showpaycodes=false
		},
		bindMoney:function(e){
			var that=this
			that.addprice = e.detail.value
		},
		addconfirm:function(e){
			var that = this
			if(!that.addprice){
				app.error('请输入金额');
				return;
			} 
			app.post('ApiYuyueWorker/addmoney', {id:that.id,price:that.addprice,addmoneyPayorderid:that.addmoneyPayorderid}, function (data) {
				app.showLoading(false);
				app.success(data.msg);
				if(data.payorderid){
						that.showmodal=false
						that.getdata()
				}
			});
		},
		showpaycode:function(e){
			var that=this
			this.showpaycodes=true
			var index= e.currentTarget.dataset.key
			that.index = index
			this.addprice = 	that.datalist[index].addprice
			this.paycode = 	that.datalist[index].paycode
			this.addmoneystatus = 	that.datalist[index].addmoneystatus
			this.addmoneyPayorderid = 	that.datalist[index].addmoneyPayorderid
			this.id= e.currentTarget.dataset.id
		},
		update:function(e){
			var that=this
			this.showmodal=true
			this.showpaycodes=false
			var index= e.currentTarget.dataset.key
			that.addprice = that.datalist[index].addprice
			
		},
		daohang:function(e){
			var that = this;
			var index = e.currentTarget.dataset.index;
			var datainfo = that.datalist[index];
			uni.showActionSheet({
				itemList: ['导航到商家', '导航到用户'],
				success: function (res) {
					if(res.tapIndex >= 0){
						if (res.tapIndex == 0) {
							var longitude = datainfo.longitude
							var latitude = datainfo.latitude
							var name = datainfo.binfo.name
							var address = datainfo.binfo.address
						}else{
							var longitude = datainfo.longitude2
							var latitude = datainfo.latitude2
							var name = datainfo.orderinfo.address
							var address = datainfo.orderinfo.address
						}
						uni.openLocation({
							latitude:parseFloat(latitude),
							longitude:parseFloat(longitude),
							name:name,
							address:address,
							scale: 13,
							success: function () {
								console.log('success');
							},
							fail:function(res){
								console.log(res);
							}
						})
					}
				}
			});
		},
  }
};
</script>
<style>
@import "./common.css";
.container{ width:100%;display:flex;flex-direction:column}
.search-container {width: 100%;height:100rpx;padding: 20rpx 23rpx 20rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}
.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}
.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}
.search-box .search-text {font-size:24rpx;color:#222;width: 100%;}

.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}
.order-box .head .f1{display:flex;align-items:center;color:#222222}
.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}
.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}
.order-box .head .f2{color:#FF6F30}
.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}

.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}
.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}
.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}

.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}
.order-box .content .f2{flex:1;padding:0 20rpx}
.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t2{font-size:24rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f3 .img{width:72rpx;height:168rpx}

.order-box .op{display:flex;justify-content:flex-end;align-items:center;width:100%; padding:20rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
.order-box .op .t1{color:#06A051;font-weight:bold}
.order-box .op .btn1{background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:70rpx;line-height:70rpx;color:#fff;border-radius:10rpx;text-align:center;font-size:28rpx; padding: 0 20rpx; font-size: 24rpx;}
.order-box .op .btn2{ margin-right: 20rpx; font-size: 24rpx; background:rgb(5,162,83,0.1) ; color: #06A152; border: 1rpx solid #1AB271; }

.modal{ position: fixed; width: 100%; height: 100%; bottom: 0; background: rgb(0,0,0,0.4); z-index: 100; display: flex; justify-content: center;}
.modal .addmoney{ width: 100%; background: #fff; width: 80%; position: absolute; top: 30%; border-radius: 10rpx; }
.modal .title{ height: 80rpx; ;line-height: 80rpx; text-align: center; font-weight: bold; border-bottom: 1rpx solid #f5f5f5; font-size: 32rpx; }
.modal .item{ display: flex; padding: 30rpx;}
.modal .item input{ width: 200rpx;}
.modal .item label{ width:200rpx; text-align: right; font-weight: bold;}
.modal .item .t2{ color: #008000; font-weight: bold;}
.modal .btn{ display: flex; margin: 30rpx 20rpx; }
.modal .btn .btn-cancel{  background-color: #F2F2F2; width: 150rpx; border-radius: 10rpx;}
.modal .btn .confirm{ width: 150rpx; border-radius: 10rpx; color: #fff;}
.modal .btn .btn-update{ width: 150rpx; border-radius: 10rpx; color: #fff; }
.modal .addmoney .price{ color: red; font-size: 32rpx; font-weight: bold;}
.modal .qrcode{ display: flex; align-items: center;}
.modal .qrcode image{width: 300rpx; height: 300rpx; margin: auto;}

/* 打卡弹窗样式 */
.punch-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.punch-content {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 20rpx;
  width: 85%;
  max-width: 650rpx;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);
}

.punch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.punch-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 48rpx;
  height: 48rpx;
  line-height: 40rpx;
  width: 48rpx;
  text-align: center;
  color: #999;
  cursor: pointer;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  color: #333;
}

.section-title .icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.location-section, .photo-section {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
}

.location-content {
  display: flex;
  flex-direction: column;
}

.location-status {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.location-status.success {
  color: #06A051;
  border-left: 8rpx solid #06A051;
}

.location-status.loading {
  color: #FF6F30;
  border-left: 8rpx solid #FF6F30;
}

.status-text {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.location-detail {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.get-location-btn {
  background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);
  color: #fff;
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 8rpx rgba(3, 178, 105, 0.2);
}

.photo-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.preview-image {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.photo-actions {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 10rpx;
}

.reselect-btn {
  background-color: #FF6F30;
  color: #fff;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 111, 48, 0.2);
}

.photo-placeholder {
  width: 100%;
  height: 300rpx;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
}

.placeholder-icon {
  font-size: 80rpx;
  color: #ccc;
  line-height: 80rpx;
  margin-bottom: 20rpx;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

.punch-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.cancel-btn, .submit-btn {
  flex: 1;
  padding: 20rpx 0;
  border-radius: 50rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: bold;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
  margin-right: 20rpx;
}

.submit-btn {
  background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);
  color: #fff;
}

.submit-btn.disabled {
  background: linear-gradient(-90deg, #ccc 0%, #999 100%);
  color: #fff;
  opacity: 0.8;
}

/* 动画 */
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading .status-text:before {
  content: "";
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #FF6F30;
  border-top-color: transparent;
  border-radius: 50%;
  margin-right: 10rpx;
  animation: rotating 1s linear infinite;
}
</style>