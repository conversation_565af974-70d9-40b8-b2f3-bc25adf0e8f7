<template>
<view>
	<block v-if="isload">
		<view class="swiper-container"  >
			<swiper class="swiper" :indicator-dots="false" :autoplay="true" :interval="500000" @change="swiperChange">
				<block v-for="(item, index) in product.pics" :key="index">
					<swiper-item class="swiper-item">
						<view class="swiper-item-view"><image class="img" :src="item" mode="widthFix"/></view>
					</swiper-item>
				</block>
			</swiper>
			<view class="imageCount">{{current+1}}/{{(product.pics).length}}</view>
		</view>
	
		<view class="header"> 
			<view class="price_share">
				<view class="title">{{product.name}}</view>
                <view class="share" @tap="shareClick"><image class="img" :src="pre_url + '/static/img/share.png'"/><text class="txt">分享</text></view>
			</view>
			<view class="pricebox flex">
				<view class="price">
					<view class="f1" v-if="product.price>0" :style="{color:t('color1')}">
						￥<text style="font-size:36rpx">{{product.price}}</text>
					</view>
					<view class="f1" v-else :style="{color:t('color1')}">
						<text style="font-size:36rpx">免费</text>
					</view>
					<view class="f2"  v-if="product.market_price>0">￥{{product.market_price}}</view>
					
				</view>
				<view class="sales_stock">
					<view class="f1">{{product.count}}节课<block v-if="sysset && sysset.show_join_num == 1"><text style="margin: 0 6rpx;">|</text>已有{{product.join_num}}人学习</block> </view>
				</view>	
			</view>
			<view class="commission" :style="{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}" v-if="kechengset.showcommission==1 && product.commission > 0">分享好友购买预计可得{{t('佣金')}}：<text style="font-weight:bold;padding:0 2px">{{product.commission}}</text>{{product.commission_desc}}</view>
			<view class="upsavemoney shining-effect" :style="{background:'linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)',color:'#653a2b'}" v-if="kechengset.show_lvupsavemoney==1 &&product.upgrade_text && product.price > 0">
				<!-- <view class="flex1">升级到 {{product.nextlevelname}} 预计可节省<text style="font-weight:bold;padding:0 2px;color:#ca4312">{{product.upsavemoney}}</text>元</view> -->
				<view class="flex1">{{product.upgrade_text}} </view>
				<view style="margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312" @tap="goto" data-url="/pagesExa/my/levelup">立即升级<image :src="pre_url + '/static/img/arrowright2.png'" style="width:30rpx;height:30rpx"/></view>
				<!-- <view style="margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312" @tap="goto" data-url="/pagesExa/my/levelup"><image :src="pre_url + '/static/img/arrowright2.png'" style="width:30rpx;height:30rpx"/></view> -->
			</view> 
		</view>		
		
		<view class="detail">
			<view class="detail_title">
				<view class="order-tab2">
					<view :class="'item ' + (curTopIndex == 1 ? 'on' : '')" @tap="switchTopTab" :data-index="1" >课程介绍<view class="after" :style="{background:t('color1')}"></view></view>
					<view :class="'item ' + (curTopIndex == 2 ? 'on' : '')" @tap="switchTopTab" :data-index="2" >课程目录<view class="after" :style="{background:t('color1')}"></view></view>
					<view :class="'item ' + (curTopIndex == 3 ? 'on' : '')" @tap="switchTopTab" :data-index="3" v-if="bind_articles && bind_articles.length > 0">训练营<view class="after" :style="{background:t('color1')}"></view></view>
				</view>
			</view>
			<block v-if="curTopIndex==1"><dp :pagecontent="pagecontent"></dp></block>
			<block v-if="curTopIndex==2">
				<view class="mulubox flex" v-for="(item, index) in datalist" :key="index" @tap="todetail" :data-mianfei='item.ismianfei' :data-url="'mldetail?id='+item.id+'&kcid='+item.kcid">
					<view class="left_box">
						<image v-if="item.kctype==1" :src="pre_url + '/static/img/tw_icon.png'" /> 
						<image v-if="item.kctype==2" :src="pre_url + '/static/img/mp3_icon.png'" />
						<image v-if="item.kctype==3" :src="pre_url + '/static/img/video_icon.png'" /> 
					</view>
					<view class="right_box flex">
						<view class="title_box">
							<view class="t1"> {{item.name}}</view>
							<view> <text  v-if="item.kctype==1"  class="t2">图文课程 </text>
								<text v-if="item.kctype==2"  class="t2">音频课程 </text>
								<text v-if="item.kctype==3"  class="t2">视频课程 </text>
								<text  v-if="item.kctype!=1" class="t2"> 时长: {{item.duration?item.duration:'未知'}}</text>
							</view>
						</view>
						<view class="skbtn" v-if="item.ismianfei && product.price>0">试看</view>
						<view class="skbtn" v-if="product.price==0">免费</view>
					</view>		
				</view>
				<view class="load-more-btn" v-if="!nomore && !nodata && datalist.length > 9" @tap="loadMore" :style="{background:t('color1')}">
					<view class="load-icon" v-if="loading"></view> 
					{{loading ? '加载中' : '加载更多'}}
				</view>
				<nomore text="没有更多课程了" v-if="nomore"></nomore>
				<nodata text="没有查找到相关课程" v-if="nodata"></nodata>
			</block>
			<block v-if="curTopIndex==3">
				<view class="article-list">
					<view class="article-item" v-for="(item, index) in bind_articles" :key="index" @tap="toArticle" :data-id="item.id">
						<view class="article-content">
							<view class="article-title">{{item.name}}</view>
							<view class="article-desc">{{item.subname}}</view>
						</view>
						<view class="article-image">
							<image :src="item.pic" mode="aspectFill"></image>
						</view>
					</view>
				</view>
				<nodata text="暂无训练营相关文章" v-if="bind_articles.length === 0"></nodata>
			</block>
		</view>
		
		<view>
		<view class="xihuan" v-if="tjdatalist.length > 0">
				<view class="xihuan-line"></view>
				<view class="xihuan-text">
					<image :src="pre_url + '/static/img/xihuan.png'" class="img"/>
					<text class="txt">为您推荐</text>
				</view>
				<view class="xihuan-line"></view>
			</view>
			<view class="prolist">
				<dp-kecheng-item :data="tjdatalist" @addcart="addcart" :menuindex="menuindex"></dp-kecheng-item>
			</view>
		</view>
		
		<!-- 添加记笔记浮动按钮 -->
		<!-- <view class="note-button" v-if="kechengset && kechengset.enable_notes == 1" @tap="showNoteDialog" :style="{background:t('color1')}">
			<text>记笔记</text>
		</view> -->
		
		<!-- 笔记功能弹窗 -->
		<view v-if="noteDialogVisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="hideNoteDialog"></view>
			<view class="popup__modal note-modal">
				<view class="popup__title">
					<text class="popup__title-text">我的课程笔记</text>
					<image :src="pre_url + '/static/img/close.png'" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hideNoteDialog"/>
				</view>
				<view class="popup__content">
					<view class="note-tabs">
						<view :class="'tab-item ' + (noteTabIndex == 1 ? 'active' : '')" @tap="switchNoteTab" :data-index="1">
							<text>添加笔记</text>
						</view>
						<view :class="'tab-item ' + (noteTabIndex == 2 ? 'active' : '')" @tap="switchNoteTab" :data-index="2">
							<text>我的笔记</text>
						</view>
					</view>
					
					<!-- 添加笔记页面 -->
					<view v-if="noteTabIndex == 1" class="add-note-content">
						<view class="form-group">
							<view class="form-label">笔记内容</view>
							<textarea 
								class="note-textarea" 
								v-model="noteForm.content" 
								placeholder="请输入您的学习笔记..."
								maxlength="1000"
								:show-count="true"
							></textarea>
						</view>
						<view class="form-group">
							<view class="form-label">学习进度</view>
							<view class="progress-input">
								<slider 
									:value="noteForm.study_progress" 
									@change="onProgressChange"
									:min="0" 
									:max="100" 
									:step="1"
									:show-value="true"
									activeColor="#FF5347"
								/>
								<text class="progress-text">{{noteForm.study_progress}}%</text>
							</view>
						</view>
						<view class="form-actions">
							<button class="btn-cancel" @tap="resetNoteForm">重置</button>
							<button class="btn-submit" @tap="submitNote" :style="{background:t('color1')}">
								{{editingNoteId ? '更新笔记' : '保存笔记'}}
							</button>
						</view>
					</view>
					
					<!-- 我的笔记列表 -->
					<view v-if="noteTabIndex == 2" class="my-notes-content">
						<view class="notes-filter">
							<view class="filter-item">
								<text class="filter-label">筛选：</text>
								<picker @change="onFilterChange" :value="filterIndex" :range="filterOptions">
									<view class="picker-text">{{filterOptions[filterIndex]}}</view>
								</picker>
							</view>
						</view>
						
						<view class="notes-list" v-if="myNotesList.length > 0">
							<view class="note-item" v-for="(note, index) in myNotesList" :key="note.id">
								<view class="note-header">
									<view class="note-time">{{note.createtime_format}}</view>
									<view class="note-actions">
										<text class="action-btn edit-btn" @tap="editNote" :data-note="JSON.stringify(note)">编辑</text>
										<text class="action-btn delete-btn" @tap="deleteNote" :data-id="note.id">删除</text>
									</view>
								</view>
								<view class="note-content">{{note.content}}</view>
								<view class="note-info">
									<text class="note-progress">学习进度: {{note.study_progress}}%</text>
									<text class="note-chapter" v-if="note.chapter_name">章节: {{note.chapter_name}}</text>
								</view>
							</view>
						</view>
						
						<view class="no-notes" v-else>
							<image :src="pre_url + '/static/img/empty.png'" class="empty-icon"/>
							<text class="empty-text">暂无笔记记录</text>
							<text class="empty-tip">开始学习并记录您的想法吧！</text>
						</view>
						
						<view class="load-more-notes" v-if="!noMoreNotes && myNotesList.length > 0" @tap="loadMoreNotes">
							<text>{{loadingNotes ? '加载中...' : '加载更多'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view style="width:100%;height:140rpx;"></view>
		<view class="bottombar flex-row" :class="menuindex>-1?'tabbarbot':'notabbarbot'" v-if="product.status==1">
			<view class="f1">
				<view class="item" @tap="goto" :data-url="'/pages/index/index'">
					<image class="img" :src="pre_url + '/static/img/shou.png'"/>
					<view class="t1">首页</view>
				</view>
				<view class="item" @tap="goto" :data-url="kfurl" v-if="kfurl!='contact::'">
					<image class="img" :src="pre_url + '/static/img/kefu.png'"/>
					<view class="t1">客服</view>
				</view>
				<button class="item" v-else open-type="contact">
					<image class="img" :src="pre_url + '/static/img/kefu.png'"/>
					<view class="t1">客服</view>
				</button>

				<view class="item" @tap="addfavorite">
					<image class="img" :src="pre_url + '/static/img/shoucang.png'"/>
					<view class="t1">{{isfavorite?'已收藏':'收藏'}}</view>
				</view>
			</view>
			<view class="op">
				<view v-if="product.price==0 || product.ispay==1"  class="tobuy flex-x-center flex-y-center" @tap="goto" :data-url="'mldetail?kcid='+product.id" :style="{background:t('color1')}" >立即学习</view>
				<view v-else class="tobuy flex-x-center flex-y-center" @tap="tobuy" :style="{background:t('color1')}" >立即购买</view>
			</view>
		</view>

		<scrolltop :isshow="scrolltopshow"></scrolltop>

		
		<view v-if="sharetypevisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal" style="height:320rpx;min-height:320rpx">
				<!-- <view class="popup__title">
					<text class="popup__title-text">请选择分享方式</text>
					<image :src="pre_url + '/static/img/close.png'" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="hidePstimeDialog"/>
				</view> -->
				<view class="popup__content">
					<view class="sharetypecontent">
						<view class="f1" @tap="shareapp" v-if="getplatform() == 'app'">
							<image class="img" :src="pre_url + '/static/img/weixin.png'"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'mp'">
							<image class="img" :src="pre_url + '/static/img/weixin.png'"/>
							<text class="t1">分享给好友</text>
						</view>
						<view class="f1" @tap="sharemp" v-else-if="getplatform() == 'h5'">
							<image class="img" :src="pre_url + '/static/img/weixin.png'"/>
							<text class="t1">分享给好友</text>
						</view>
						<button class="f1" open-type="share" v-else>
							<image class="img" :src="pre_url + '/static/img/weixin.png'"/>
							<text class="t1">分享给好友</text>
						</button>
						<view class="f2" @tap="showPoster">
							<image class="img" :src="pre_url + '/static/img/sharepic.png'"/>
							<text class="t1">生成分享图片</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="posterDialog" v-if="showposter">
			<view class="main">
				<view class="close" @tap="posterDialogClose"><image class="img" :src="pre_url + '/static/img/close.png'"/></view>
				<view class="content">
					<image class="img" :src="posterpic" mode="widthFix" @tap="previewImage" :data-url="posterpic"></image>
				</view>
			</view>
		</view>
		
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
var interval = null;

export default {
	data() {
		return {
			pre_url: app.globalData.pre_url,
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			isload:false,
			isfavorite: false,
			current: 0,
			product: [],
			pagecontent: "",
			title: "",
			sharepic: "",
			sharetypevisible: false,
			showposter: false,
			posterpic: "",
			scrolltopshow: false,
			kfurl:'',
			timeDialogShow: false,
			curTopIndex: 1,
			datalist: [],
			tjdatalist:[],
			kechengset: {},
			business:{},
			sysset:{},
			bind_articles: [],
			pagenum: 1,
			nomore: false,
			nodata: false,
			noteDialogVisible: false,
			noteTabIndex: 1,
			noteForm: {
				content: '',
				study_progress: 0,
				note_time: '',
				chapter_id: 0
			},
			editingNoteId: null,
			filterIndex: 0,
			filterOptions: ['全部', '学习进度', '章节'],
			notesPageNum: 1,
			myNotesList: [],
			noMoreNotes: false,
			loadingNotes: false
		};
	},
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.product.name,pic:this.product.pic});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});
		var query = (sharewxdata.path).split('?')[1];
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
	onUnload: function () {
		clearInterval(interval);
	},
	onReachBottom: function () {
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getdatalist(true);
		}
	},

	methods: {
		getdata:function(){
			var that = this;
			var id = this.opt.id || 0;
			that.loading = true;
			app.get('ApiKecheng/detail', {id: id}, function (res) {
				that.loading = false;
				if (res.status == 0) {
					app.alert(res.msg);
					return;
				}
				that.textset = app.globalData.textset;
				var product = res.product;
				var pagecontent = JSON.parse(product.detail);
				that.product = product;
				that.pagecontent = pagecontent;
				that.title = product.name;
				that.isfavorite = res.isfavorite;
				that.sharepic = product.pics[0];
				that.tjdatalist = res.tjdatalist;
				that.kechengset = res.kechengset;
				that.business = res.business;
				that.sysset = res.sysset;
				that.bind_articles = res.bind_articles || [];
				
				// 如果当前选中的是训练营tab，但没有文章数据，则切换到第一个tab
				if (that.curTopIndex == 3 && (!that.bind_articles || that.bind_articles.length === 0)) {
					that.curTopIndex = 1;
				}
				
				uni.setNavigationBarTitle({
					title: product.name
				});
				that.kfurl = '/pagesExt/kefu/index?bid='+product.bid;
				if(app.globalData.initdata.kfurl != ''){
					that.kfurl = app.globalData.initdata.kfurl;
				}
				if(that.business && that.business.kfurl){
					that.kfurl = that.business.kfurl;
				}
				that.loaded({title:product.name,pic:product.pic});
			});
		},
		swiperChange: function (e) {
			var that = this;
			that.current = e.detail.current
		},
		buydialogChange: function (e) {
			if(!this.buydialogShow){
				this.btntype = e.currentTarget.dataset.btntype;
			}
			this.buydialogShow = !this.buydialogShow;
		},
		currgg: function (e) {
			console.log(e);
			var that = this
			this.ggname = e.ggname;
			that.ggid = e.ggid
			that.proid = e.proid
			that.num = e.num
		},
		switchTopTab: function (e) {
		  var that = this;
		  this.curTopIndex = e.currentTarget.dataset.index;
		  this.getdatalist();
		},
		getdatalist: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
			var that = this;
			var pagenum = that.pagenum;
			var id = that.opt.id ? that.opt.id : '';
			var order = that.order;
			var field = that.field; 
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			app.post('ApiKecheng/getmululist', {pagenum: pagenum,field: field,order: order,id:id}, function (res) { 
				that.loading = false;
				uni.stopPullDownRefresh();
				var data = res.data;
				if (pagenum == 1) {
				  that.datalist = data;
				  if (data.length == 0) {
				    that.nodata = true;
				  }
				}else{
				  if (data.length == 0) {
				    that.nomore = true;
				  } else {
				    var datalist = that.datalist;
				    var newdata = datalist.concat(data);
				    that.datalist = newdata;
				  }
				}
			});
		},
		todetail:function(e){
			var that = this;
		    var url = e.currentTarget.dataset.url;
			var ismf = e.currentTarget.dataset.mianfei;
			if(ismf==1 || that.product.ispay==1 || that.product.price==0){
				app.goto(url);
			}else{
				app.error('请先购买课程');
			}
		},
		//收藏操作
		addfavorite: function () {
			var that = this;
			var proid = that.product.id;
			app.post('ApiKecheng/addfavorite', {proid: proid,type: 'kecheng'}, function (data) {
				if (data.status == 1) {
					that.isfavorite = !that.isfavorite;
				}
				app.success(data.msg);
			});
		},
		shareClick: function () {
			this.sharetypevisible = true;
		},
		handleClickMask: function () {
			this.sharetypevisible = false
		},
		showPoster: function () {
			var that = this;
			that.showposter = true;
			that.sharetypevisible = false;
			app.showLoading('生成海报中');
			app.post('ApiKecheng/getposter', {proid: that.product.id}, function (data) {
				app.showLoading(false);
				if (data.status == 0) {
					app.alert(data.msg);
				} else {
					that.posterpic = data.poster;
				}
			});
		},
		posterDialogClose: function () {
			this.showposter = false;
		},
		showfuwudetail: function () {
			this.showfuwudialog = true;
		},
		hidefuwudetail: function () {
			this.showfuwudialog = false
		},
		showcuxiaodetail: function () {
			this.showcuxiaodialog = true;
		},
		hidecuxiaodetail: function () {
			this.showcuxiaodialog = false
		},
		getcoupon:function(){
			this.showcuxiaodialog = false;
			this.getdata();
		},
		onPageScroll: function (e) {
			var that = this;
			var scrollY = e.scrollTop;     
			if (scrollY > 200) {
				that.scrolltopshow = true;
			}
			if(scrollY < 150) {
				that.scrolltopshow = false
			}
		},	
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			that.sharetypevisible = false;
			uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.product.name;
						//sharedata.summary = app.globalData.initdata.desc;
						sharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/kecheng/detail?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;
						sharedata.imageUrl = that.product.pic;
						var sharelist = app.globalData.initdata.sharelist;
						if(sharelist){
							for(var i=0;i<sharelist.length;i++){
								if(sharelist[i]['indexurl'] == '/activity/kecheng/detail'){
									sharedata.title = sharelist[i].title;
									sharedata.summary = sharelist[i].desc;
									sharedata.imageUrl = sharelist[i].pic;
									if(sharelist[i].url){
										var sharelink = sharelist[i].url;
										if(sharelink.indexOf('/') === 0){
											sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;
										}
										if(app.globalData.mid>0){
											 sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;
										}
										sharedata.href = sharelink;
									}
								}
							}
						}
						uni.share(sharedata);
					}
        }
      });
		},
		showsubqrcode:function(){
			this.$refs.qrcodeDialog.open();
		},
		closesubqrcode:function(){
			this.$refs.qrcodeDialog.close();
		},
		tobuy: function (e) {
			var that=this
			//购买
			app.post('ApiKecheng/createOrder', {
				kcid:that.product.id,
			}, function(res) {
				app.showLoading(false);
				if (res.status == 0) {
					app.error(res.msg);
					return;
				}
				app.goto('/pages/pay/pay?id=' + res.payorderid);
			});
		},
		toArticle: function(e) {
			var trainingId = e.currentTarget.dataset.id;
			console.log('2025-01-03 22:55:53,565-INFO-[product][toArticle_001] 跳转到训练营详情页面, training_id:', trainingId);
			app.goto('/pagesExb/training/detail?id=' + trainingId);
		},
		// 添加跳转到记笔记页面的方法
		goToNote: function() {
			console.log('2025-01-03 22:55:53,565-INFO-[product][goToNote_001] 显示笔记功能弹窗');
			this.showNoteDialog();
		},
		
		// 显示笔记弹窗
		showNoteDialog: function() {
			console.log('2025-01-03 22:55:53,565-INFO-[product][showNoteDialog_001] 显示笔记弹窗');
			this.noteDialogVisible = true;
			this.noteTabIndex = 1;
			this.resetNoteForm();
			this.getMyNotes();
		},
		
		// 隐藏笔记弹窗
		hideNoteDialog: function() {
			console.log('2025-01-03 22:55:53,565-INFO-[product][hideNoteDialog_001] 隐藏笔记弹窗');
			this.noteDialogVisible = false;
			this.editingNoteId = null;
			this.resetNoteForm();
		},
		
		// 切换笔记标签页
		switchNoteTab: function(e) {
			var index = e.currentTarget.dataset.index;
			console.log('2025-01-03 22:55:53,565-INFO-[product][switchNoteTab_001] 切换笔记标签页到:', index);
			this.noteTabIndex = index;
			if (index == 2) {
				this.getMyNotes();
			}
		},
		
		// 重置笔记表单
		resetNoteForm: function() {
			console.log('2025-01-03 22:55:53,565-INFO-[product][resetNoteForm_001] 重置笔记表单');
			this.noteForm = {
				content: '',
				study_progress: 0,
				note_time: '',
				chapter_id: 0
			};
			this.editingNoteId = null;
		},
		
		// 学习进度变化
		onProgressChange: function(e) {
			console.log('2025-01-03 22:55:53,565-INFO-[product][onProgressChange_001] 学习进度变化:', e.detail.value);
			this.noteForm.study_progress = e.detail.value;
		},
		
		// 提交笔记
		submitNote: function() {
			var that = this;
			console.log('2025-01-03 22:55:53,565-INFO-[product][submitNote_001] 开始提交笔记');
			
			if (!that.noteForm.content.trim()) {
				app.error('请输入笔记内容');
				return;
			}
			
			var apiUrl = that.editingNoteId ? 'ApiKechengNotes/updateNote' : 'ApiKechengNotes/addNote';
			var params = {
				kcid: that.product.id,
				chapter_id: that.noteForm.chapter_id || 0,
				content: that.noteForm.content.trim(),
				study_progress: that.noteForm.study_progress,
				note_time: that.noteForm.note_time
			};
			
			if (that.editingNoteId) {
				params.id = that.editingNoteId;
			}
			
			console.log('2025-01-03 22:55:53,565-INFO-[product][submitNote_002] 提交参数:', params);
			
			app.showLoading('保存中...');
			app.post(apiUrl, params, function(res) {
				app.showLoading(false);
				console.log('2025-01-03 22:55:53,565-INFO-[product][submitNote_003] 笔记保存结果:', res);
				
				if (res.status == 1) {
					app.success(res.msg);
					that.resetNoteForm();
					that.noteTabIndex = 2;
					that.getMyNotes();
				} else {
					app.error(res.msg);
				}
			});
		},
		
		// 获取我的笔记列表
		getMyNotes: function(loadMore = false) {
			var that = this;
			console.log('2025-01-03 22:55:53,565-INFO-[product][getMyNotes_001] 获取我的笔记列表，loadMore:', loadMore);
			
			if (!loadMore) {
				that.notesPageNum = 1;
				that.myNotesList = [];
			}
			
			that.loadingNotes = true;
			that.noMoreNotes = false;
			
			var params = {
				kcid: that.product.id,
				pagenum: that.notesPageNum,
				pernum: 10
			};
			
			app.post('ApiKechengNotes/getMyNotes', params, function(res) {
				that.loadingNotes = false;
				console.log('2025-01-03 22:55:53,565-INFO-[product][getMyNotes_002] 笔记列表获取结果:', res);
				
				if (res.status == 1) {
					var data = res.data || [];
					if (that.notesPageNum == 1) {
						that.myNotesList = data;
					} else {
						that.myNotesList = that.myNotesList.concat(data);
					}
					
					if (data.length < 10) {
						that.noMoreNotes = true;
					}
				} else {
					app.error(res.msg);
				}
			});
		},
		
		// 编辑笔记
		editNote: function(e) {
			var noteData = JSON.parse(e.currentTarget.dataset.note);
			console.log('2025-01-03 22:55:53,565-INFO-[product][editNote_001] 编辑笔记:', noteData);
			
			this.editingNoteId = noteData.id;
			this.noteForm = {
				content: noteData.content,
				study_progress: noteData.study_progress || 0,
				note_time: noteData.note_time || '',
				chapter_id: noteData.chapter_id || 0
			};
			this.noteTabIndex = 1;
		},
		
		// 删除笔记
		deleteNote: function(e) {
			var that = this;
			var noteId = e.currentTarget.dataset.id;
			console.log('2025-01-03 22:55:53,565-INFO-[product][deleteNote_001] 删除笔记ID:', noteId);
			
			app.confirm('确定要删除这条笔记吗？', function() {
				app.showLoading('删除中...');
				app.post('ApiKechengNotes/deleteNote', {id: noteId}, function(res) {
					app.showLoading(false);
					console.log('2025-01-03 22:55:53,565-INFO-[product][deleteNote_002] 删除结果:', res);
					
					if (res.status == 1) {
						app.success(res.msg);
						that.getMyNotes();
					} else {
						app.error(res.msg);
					}
				});
			});
		},
		
		// 筛选变化
		onFilterChange: function(e) {
			var index = e.detail.value;
			console.log('2025-01-03 22:55:53,565-INFO-[product][onFilterChange_001] 筛选变化:', index);
			this.filterIndex = index;
			this.getMyNotes();
		},
		
		// 加载更多笔记
		loadMoreNotes: function() {
			console.log('2025-01-03 22:55:53,565-INFO-[product][loadMoreNotes_001] 加载更多笔记');
			if (!this.loadingNotes && !this.noMoreNotes) {
				this.notesPageNum++;
				this.getMyNotes(true);
			}
		},
		
		loadMore: function() {
			if (!this.nodata && !this.nomore && !this.loading) {
				this.pagenum = this.pagenum + 1;
				this.getdatalist(true);
			}
		}
	}

};
</script>
<style>
.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}
.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}
.follow_topbar .headimg image {height:64rpx; width:64rpx;}
.follow_topbar .info {height:56rpx; padding:16rpx 0;}
.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;}
.follow_topbar .info {height:80rpx; float:left;}
.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}
.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.qrcodebox .img{width:400rpx;height:400rpx}
.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}

.goback{ position: absolute; top:0 ;width:64rpx ; height: 64rpx;z-index: 10000; margin: 30rpx;}
.goback img{ width:64rpx ; height: 64rpx;}

.swiper-container{position:relative}
.swiper {width: 100%;height: 420rpx;overflow: hidden;}
.swiper-item-view{width: 100%;height: 750rpx;}
.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}

.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}

.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}
.provideo image{width:50rpx;height:50rpx;}
.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}

.videobox{width:100%;height:750rpx;text-align:center;background:#000}
.videobox .video{width:100%;height:650rpx;}
.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx;}

.header {padding: 20rpx 3%;background: #fff; width: 100%; border-radius:10rpx; margin: auto; margin-bottom: 20rpx; position: relative;}
.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between}
.header .price_share .price{display:flex;align-items:flex-end}
.header .price_share .price .f1{font-size:50rpx;color:#51B539;font-weight:bold}
.header .price_share .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}
.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center;flex-shrink: 0;}
.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}
.header .price_share .share .txt{color:#333333;font-size:20rpx}
.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}
.header .sellpoint{font-size:28rpx;color: #666;padding-top:20rpx;}
.header .sales_stock{height:60rpx;line-height:60rpx;font-size:24rpx;color:#BBB; }
.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}
.header .upsavemoney{display:flex;align-items:center;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:70rpx;padding:0 20rpx;position:relative;overflow:hidden;}

/* 闪光特效 */
@keyframes shining {
  0% {
    transform: translateX(-100%) rotate(30deg);
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(300%) rotate(30deg);
    opacity: 0;
  }
}

.shining-effect {
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(202, 67, 18, 0.2);
  transform: translateZ(0);
}

.shining-effect:before {
  content: "";
  position: absolute;
  top: -180%;
  left: -50%;
  width: 30%;
  height: 300%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shining 4s ease-in-out infinite;
  animation-delay: 1s;
  will-change: transform;
  pointer-events: none;
}

.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}
.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}
.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1000rpx;}
.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}
.popup__title-text{font-size:32rpx}
.popup__close{position:absolute;top:34rpx;right:34rpx}
.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}
.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}
.service-item .prefix{padding-top: 2px;}
.service-item .suffix{padding-left: 10rpx;}
.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}


.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}
.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}
.shop .p2{padding-left:10rpx}
.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}
.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx;}
.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}

.detail{min-height:200rpx; width: 100%; margin: auto; border-radius: 10rpx; background: #fff;padding-bottom: 10px;}

.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:40rpx}
.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}
.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}
.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}

.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}
.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}
.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:80rpx;position:relative}
.bottombar .f1 .item .img{ width:44rpx;height:44rpx}
.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}
.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}
.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}
.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}
.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}

.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx;display:flex;padding:50rpx;align-items:flex-end}
.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}
.sharetypecontent button::after{border:0}
.sharetypecontent .f1 .img{width:90rpx;height:90rpx}
.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}
.sharetypecontent .f2 .img{width:90rpx;height:90rpx}
.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}

.posterDialog{ position:fixed;z-index:9;width:100%;height:100%;background:rgba(0,0,0,0.8);top:var(--window-top);left:0}
.posterDialog .main{ width:80%;margin:60rpx 10% 30rpx 10%;background:#fff;position:relative;border-radius:20rpx}
.posterDialog .close{ position:absolute;padding:20rpx;top:0;right:0}
.posterDialog .close .img{ width:40rpx;height:40rpx;}
.posterDialog .content{ width:100%;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}
.posterDialog .content .img{width:540rpx;height:960rpx}

.pricebox{ width: 100%;border:1px solid #fff; justify-content: space-between;}
.pricebox .price{display:flex;align-items:flex-end}
.pricebox .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:4rpx}

.order-tab2{display:flex;width:auto;min-width:100%; padding-top: 10rpx; border-bottom: 1px solid #F7F7F7;}
.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:80rpx; line-height:80rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}
.order-tab2 .on{color:#222222;}
.order-tab2 .after{display:none;position:absolute;left:47%;margin-left:-20rpx;bottom:0rpx;height:6rpx;border-radius:1.5px;width:60rpx}
.order-tab2 .on .after{display:block}

.mulubox{ padding-top: 35rpx; padding-left: 30rpx;}
.left_box{ display: flex;}
.left_box image{ width: 44rpx; height:44rpx; margin-right: 40rpx; margin-top: 26rpx; }
.right_box{ border-bottom: 1px solid #F6F6F6; padding-bottom: 30rpx; width: 100%; justify-content: space-between;}
.title_box .t1{ color: #1E252F; font-size: 28rpx; font-weight: bold;}
.title_box .t2{ color: #B8B8B8;font-size: 24rpx;line-height: 60rpx; margin-right: 15rpx;}
.skbtn{  background-color: #FFEEEC; text-align: center; margin-right: 10px; height: 44rpx; width: 95rpx; color: #FC6D65; font-size: 24rpx; line-height: 40rpx; border-radius: 22rpx; margin-top: 20rpx;}
.xihuan{height: auto;overflow: hidden;display:flex;align-items:center;width:100%;padding:12rpx 160rpx}
.xihuan-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #eee}
.xihuan-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}
.xihuan-text .txt{color:#111;font-size:30rpx}
.xihuan-text .img{text-align:center;width:36rpx;height:36rpx;margin-right:12rpx}
.prolist{width: 100%;height:auto;padding: 8rpx 20rpx;}

.article-list {
  padding: 20rpx 30rpx;
}
.article-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.article-item:active {
  background-color: #f9f9f9;
}
.article-content {
  flex: 1;
  padding-right: 30rpx;
}
.article-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.article-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.article-info {
  display: none;
}
.article-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}
.article-image:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
}
.article-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.sharetypecontent{ height:250rpx;width:710rpx;margin:20rpx;display:flex;padding:50rpx;align-items:flex-end}
.sharetypecontent .f1{ color:#51c332;width:50%;height:150rpx;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:28rpx;border:0}
.sharetypecontent button::after{border:0}
.sharetypecontent .f1 .img{width:90rpx;height:90rpx}
.sharetypecontent .f2{ color:#51c332;width:50%;display:flex;flex-direction:column;align-items:center}
.sharetypecontent .f2 .img{width:90rpx;height:90rpx}
.sharetypecontent .t1{height:60rpx;line-height:60rpx;color:#666}

/* 记笔记按钮样式 */
.note-button {
	position: fixed;
	right: 30rpx;
	bottom: 280rpx;
	z-index: 99;
	border-radius: 40rpx;
	padding: 16rpx 36rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	color: #fff;
	transform: scale(1);
	transition: all 0.3s ease;
}
.note-button:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.note-button image {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
	filter: brightness(10);
}
.note-button text {
	font-size: 28rpx;
	color: #fff;
	font-weight: 500;
}

/* 加载更多按钮样式 */
.load-more-btn {
	width: 60%;
	height: 70rpx;
	line-height: 70rpx;
	margin: 30rpx auto;
	text-align: center;
	color: #fff;
	font-size: 26rpx;
	border-radius: 35rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	transition: all 0.2s;
	display: flex;
	align-items: center;
	justify-content: center;
}
.load-more-btn:active {
	opacity: 0.8;
}

/* 加载图标样式 */
@keyframes loading-rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.load-icon {
	width: 24rpx;
	height: 24rpx;
	margin-right: 10rpx;
	border: 2rpx solid #fff;
	border-top: 2rpx solid transparent;
	border-radius: 50%;
	animation: loading-rotate 0.8s linear infinite;
}

/* 笔记功能样式 */
.note-modal {
	min-height: 800rpx;
	max-height: 1200rpx;
}

.note-tabs {
	display: flex;
	border-bottom: 2rpx solid #f5f5f5;
	margin-bottom: 30rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 0;
	font-size: 28rpx;
	color: #666;
	position: relative;
}

.tab-item.active {
	color: #FF5347;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 4rpx;
	background: #FF5347;
	border-radius: 2rpx;
}

.add-note-content {
	padding: 0 20rpx;
}

.form-group {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
	font-weight: bold;
}

.note-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 12rpx;
	font-size: 26rpx;
	line-height: 1.6;
	background: #fafafa;
	box-sizing: border-box;
}

.note-textarea:focus {
	border-color: #FF5347;
	background: #fff;
}

.progress-input {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 12rpx;
	background: #fafafa;
}

.progress-input slider {
	flex: 1;
	margin-right: 20rpx;
}

.progress-text {
	font-size: 26rpx;
	color: #FF5347;
	font-weight: bold;
	min-width: 60rpx;
}

.form-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 40rpx;
}

.btn-cancel, .btn-submit {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
	font-weight: bold;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
}

.btn-submit {
	color: #fff;
}

.my-notes-content {
	padding: 0 20rpx;
}

.notes-filter {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f8f8f8;
	border-radius: 12rpx;
}

.filter-item {
	display: flex;
	align-items: center;
}

.filter-label {
	font-size: 26rpx;
	color: #666;
	margin-right: 20rpx;
}

.picker-text {
	font-size: 26rpx;
	color: #333;
	padding: 10rpx 20rpx;
	background: #fff;
	border-radius: 8rpx;
	border: 1rpx solid #ddd;
}

.notes-list {
	max-height: 600rpx;
	overflow-y: auto;
}

.note-item {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	border-left: 6rpx solid #FF5347;
}

.note-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.note-time {
	font-size: 24rpx;
	color: #999;
}

.note-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	border: 1rpx solid;
}

.edit-btn {
	color: #FF5347;
	border-color: #FF5347;
	background: rgba(255, 83, 71, 0.1);
}

.delete-btn {
	color: #ff4757;
	border-color: #ff4757;
	background: rgba(255, 71, 87, 0.1);
}

.note-content {
	font-size: 28rpx;
	line-height: 1.6;
	color: #333;
	margin-bottom: 20rpx;
	word-break: break-all;
}

.note-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 24rpx;
	color: #666;
}

.note-progress {
	color: #FF5347;
	font-weight: bold;
}

.note-chapter {
	color: #999;
}

.no-notes {
	text-align: center;
	padding: 80rpx 20rpx;
}

.empty-icon {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}

.empty-text {
	display: block;
	font-size: 28rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.empty-tip {
	display: block;
	font-size: 24rpx;
	color: #ccc;
}

.load-more-notes {
	text-align: center;
	padding: 30rpx;
	font-size: 26rpx;
	color: #666;
	border-top: 1rpx solid #f0f0f0;
	margin-top: 20rpx;
}

.load-more-notes:active {
	background: #f8f8f8;
}
</style>