{"id": "jack-filepicker", "displayName": "jack-filepicker", "version": "1.0.3", "description": "jack-filepicker是一款H5端的图片选择器，与官方uni-file-picker不同的是：本组件支持选择图片后进行直接编辑的操作。", "keywords": ["h5", "vue", "图片选择", "图片编辑"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "仅在本地使用文件数据，不上传到服务器", "permissions": "定位 相机 文件 相册"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uni-popup"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "u"}, "App": {"app-vue": "u", "app-nvue": "u", "app-uvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1"}}