<template>
<view class="container">
	<block v-if="isload">
		<!-- 公众号模式 - 微信授权获取 -->
		<!-- #ifdef H5 -->
		<view v-if="platform === 'mp'" class="auth-container">
			<view class="title">请授权获取头像昵称</view>
			<view class="auth-info">
				<image :src="headimg || default_headimg" class="auth-avatar"></image>
				<view class="auth-nickname">{{nickname || '未设置昵称'}}</view>
			</view>
			<button class="auth-btn" @tap="getWxUserInfo" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
				微信授权获取
			</button>
			<view class="auth-tip">授权后将自动获取您的微信头像和昵称</view>
			<view class="manual-setting" @tap="showManualForm = true">
				<text>手动设置</text>
			</view>
		</view>
		<!-- 手动设置表单 -->
		<form v-if="platform !== 'mp' || showManualForm" @submit="formSubmit" @reset="formReset">
		<view class="avatar-setting">
			<view class="title">请设置头像昵称</view>
			<view class="form">
				<!--  #ifdef MP-WEIXIN -->
				<view class="form-item avatar-item">
					<view class="label">头像</view>
					<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" class="avatar-btn">
						<image :src="headimg || default_headimg" class="avatar-img"></image>
					</button> 
				</view>
				<view class="form-item nickname-item">
					<view class="label">昵称</view>
					<input type="nickname" class="input" placeholder="请输入昵称" name="nickname" v-model="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE"/>
				</view>
				<!-- #endif -->
				<!--  #ifndef MP-WEIXIN -->
				<view class="form-item avatar-item">
					<view class="label">头像</view>
					<image :src="headimg || default_headimg" class="avatar-img" @tap="uploadHeadimg"></image>
				</view>
				<view class="form-item nickname-item">
					<view class="label">昵称</view>
					<input type="text" class="input" placeholder="请输入昵称" name="nickname" v-model="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE"/>
				</view>
				<!-- #endif -->
			</view>
		</view>
		<button class="set-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">保 存</button>
		</form>
		<!-- #endif -->
		
		<!-- 非公众号模式 - 正常表单 -->
		<!-- #ifndef H5 -->
		<form @submit="formSubmit" @reset="formReset">
		<view class="avatar-setting">
			<view class="title">请设置头像昵称</view>
			<view class="form">
				<!--  #ifdef MP-WEIXIN -->
				<view class="form-item avatar-item">
					<view class="label">头像</view>
					<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" class="avatar-btn">
						<image :src="headimg || default_headimg" class="avatar-img"></image>
					</button> 
				</view>
				<view class="form-item nickname-item">
					<view class="label">昵称</view>
					<input type="nickname" class="input" placeholder="请输入昵称" name="nickname" v-model="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE"/>
				</view>
				<!-- #endif -->
				<!--  #ifndef MP-WEIXIN -->
				<view class="form-item avatar-item">
					<view class="label">头像</view>
					<image :src="headimg || default_headimg" class="avatar-img" @tap="uploadHeadimg"></image>
				</view>
				<view class="form-item nickname-item">
					<view class="label">昵称</view>
					<input type="text" class="input" placeholder="请输入昵称" name="nickname" v-model="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE"/>
				</view>
				<!-- #endif -->
			</view>
		</view>
		<button class="set-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">保 存</button>
		</form>
		<!-- #endif -->
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			headimg: '',
			nickname: '',
			default_headimg: app.globalData.pre_url + '/static/img/touxiang.png',
			platform: app.globalData.platform || '',
			showManualForm: false,
			wxUserInfoLoading: false
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata:function(){
			var that = this;
			this.loading = true
			app.get('ApiMy/setavatar', {}, function(res){
				that.loading = false;
				that.headimg = res.headimg || '';
				that.nickname = res.nickname || '';
				that.isload = true;
				
				// 如果是公众号模式且没有头像或昵称，自动尝试获取
				if(that.platform === 'mp' && (!that.headimg || that.headimg === that.default_headimg || !that.nickname)) {
					console.log('公众号模式自动尝试获取头像昵称');
					that.getWxUserInfo();
				}
			})
		},
		
		// 微信公众号授权获取用户信息
		getWxUserInfo: function(){
			if(this.wxUserInfoLoading) return;
			
			var that = this;
			this.wxUserInfoLoading = true;
			app.showLoading('授权中');
			
			// 调用公众号获取用户信息接口
			app.post('ApiIndex/getWxUserInfo', {}, function(res){
				app.showLoading(false);
				that.wxUserInfoLoading = false;
				
				if(res.status == 1){
					// 授权成功，获取头像昵称
					that.headimg = res.headimg || that.headimg;
					that.nickname = res.nickname || that.nickname;
					
					// 自动保存
					that.submitAvatarNickname();
				} else if(res.url) {
					// 需要跳转授权
					console.log('需要跳转授权: ' + res.url);
					location.href = res.url;
				} else {
					// 授权失败，显示手动设置表单
					app.alert(res.msg || '授权失败，请手动设置');
					that.showManualForm = true;
				}
			}, function(err){
				app.showLoading(false);
				that.wxUserInfoLoading = false;
				app.alert('网络请求失败，请手动设置');
				that.showManualForm = true;
			});
		},
		
		// 自动提交头像昵称
		submitAvatarNickname: function(){
			if(!this.headimg || this.headimg == this.default_headimg){
				app.alert('获取头像失败，请手动设置');
				this.showManualForm = true;
				return;
			}
			
			if(!this.nickname){
				app.alert('获取昵称失败，请手动设置');
				this.showManualForm = true;
				return;
			}
			
			var that = this;
			app.showLoading('保存中');
			app.post("ApiMy/setavatarsub", {headimg: this.headimg, nickname: this.nickname}, function(data){
				app.showLoading(false);
				if(data.status == 1){
					app.success(data.msg);
					setTimeout(function(){
						app.goback(true);
					}, 1000);
				} else {
					app.error(data.msg);
					that.showManualForm = true;
				}
			});
		},
    formSubmit: function (e) {
      var formdata = e.detail.value;
			var nickname = formdata.nickname || this.nickname;
			
			if (!this.headimg || this.headimg == this.default_headimg) {
        app.alert('请设置头像'); 
        return;
      }
			if (nickname == '') {
        app.alert('请输入昵称'); 
        return;
      }
			
			app.showLoading('提交中');
      app.post("ApiMy/setavatarsub", {headimg: this.headimg, nickname: nickname}, function (data) {
				app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function () {
            app.goback(true);
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    },
		// 非微信小程序头像上传
		uploadHeadimg:function(){
			var that = this;
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera'],
				success: function(res) {
					var tempFilePaths = res.tempFilePaths;
					var tempFilePath = tempFilePaths[0];
					app.showLoading('上传中');
					uni.uploadFile({
						url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',
						filePath: tempFilePath,
						name: 'file',
						success: function(res) {
							app.showLoading(false);
							var data = JSON.parse(res.data);
							if (data.status == 1) {
								that.headimg = data.url;
							} else {
								app.alert(data.msg);
							}
						},
						fail: function(res) {
							app.showLoading(false);
							app.alert(res.errMsg);
						}
					});
				},
				fail: function(res) {
					app.alert('选择图片失败');
				}
			});
		},
		// 微信小程序头像选择
		onChooseAvatar:function(e){
			var that = this;
			app.showLoading('上传中');
			uni.uploadFile({
				url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id+'/isheadimg/1',
				filePath: e.detail.avatarUrl,
				name: 'file',
				success: function(res) {
					app.showLoading(false);
					var data = JSON.parse(res.data);
					if (data.status == 1) {
						that.headimg = data.url;
					} else {
						app.alert(data.msg);
					}
				},
				fail: function(res) {
					app.showLoading(false);
					app.alert(res.errMsg);
				}
			});
		}
  }
};
</script>

<style>
.container {
	background: #f6f6f6;
	min-height: 100vh;
}

.avatar-setting {
	width: 94%;
	margin: 20rpx 3%;
	border-radius: 10rpx;
	background: #FFF;
	padding: 40rpx 30rpx;
}

.title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 40rpx;
}

.form {
	width: 100%;
}

.form-item {
	display: flex;
	align-items: center;
	width: 100%;
	border-bottom: 1px #ededed solid;
	min-height: 120rpx;
	padding: 20rpx 0;
}

.form-item:last-child {
	border: 0;
}

.form-item .label {
	color: #333;
	width: 120rpx;
	font-size: 30rpx;
}

.form-item .input {
	flex: 1;
	color: #333;
	font-size: 30rpx;
	text-align: right;
}

.avatar-btn {
	background: none;
	border: none;
	padding: 0;
	margin: 0;
	width: 100rpx;
	height: 100rpx;
}

.avatar-img {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	border: 2rpx solid #e0e0e0;
}

.set-btn {
	width: 90%;
	margin: 60rpx 5%;
	height: 96rpx;
	line-height: 96rpx;
	border-radius: 48rpx;
	color: #FFFFFF;
	font-weight: bold;
	font-size: 32rpx;
}

.avatar-item {
	justify-content: space-between;
}

.nickname-item {
	justify-content: space-between;
}

/* 公众号授权样式 */
.auth-container {
	width: 94%;
	margin: 20rpx 3%;
	border-radius: 10rpx;
	background: #FFF;
	padding: 40rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.auth-info {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin: 30rpx 0;
}

.auth-avatar {
	width: 150rpx;
	height: 150rpx;
	border-radius: 50%;
	margin-bottom: 20rpx;
	border: 2rpx solid #e0e0e0;
}

.auth-nickname {
	font-size: 32rpx;
	color: #333;
	margin-top: 10rpx;
}

.auth-btn {
	width: 80%;
	height: 90rpx;
	line-height: 90rpx;
	border-radius: 45rpx;
	color: #FFFFFF;
	font-size: 32rpx;
	margin: 30rpx 0;
}

.auth-tip {
	font-size: 26rpx;
	color: #999;
	margin: 20rpx 0;
}

.manual-setting {
	margin-top: 30rpx;
	padding: 10rpx 20rpx;
	font-size: 28rpx;
	color: #666;
	border-bottom: 1rpx solid #999;
}
</style> 