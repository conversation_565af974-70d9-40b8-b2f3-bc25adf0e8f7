<template>
<view class="container">
	<view class="order-info">
		<view class="product-info">
			<image :src="orderInfo.propic" mode="aspectFill"></image>
			<view class="info">
				<text class="name">{{orderInfo.proname}}</text>
				<text class="price">￥{{orderInfo.totalprice}}</text>
			</view>
		</view>
	</view>

	<view class="comment-form">
		<view class="rating">
			<text class="label">服务评分：</text>
			<view class="stars">
				<view 
					v-for="n in 5" 
					:key="n" 
					class="star" 
					:class="{'active': n <= rating}"
					@tap="setRating(n)"
				>
					<text class="iconfont" :class="n <= rating ? 'iconxingshifill' : 'iconxingshi'"></text>
				</view>
			</view>
		</view>
		
		<view class="textarea-box">
			<textarea 
				v-model="content" 
				placeholder="请输入您的评价内容" 
				maxlength="200"
				class="textarea"
			></textarea>
			<text class="word-count">{{content.length}}/200</text>
		</view>
		
		<view class="upload-box">
			<view class="label">上传图片(最多3张)：</view>
			<view class="image-list">
				<view 
					v-for="(item, index) in imageList" 
					:key="index"
					class="image-item"
				>
					<image :src="item" mode="aspectFill"></image>
					<view class="delete-btn" @tap="deleteImage(index)">
						<text class="iconfont iconclose"></text>
					</view>
				</view>
				<view 
					class="upload-btn" 
					@tap="chooseImage" 
					v-if="imageList.length < 3"
				>
					<text class="iconfont iconjiahao"></text>
				</view>
			</view>
		</view>
	</view>

	<view class="footer">
		<button 
			class="submit-btn" 
			:disabled="!canSubmit"
			@tap="submitComment"
			:style="{backgroundColor: canSubmit ? t('color1') : '#cccccc'}"
		>提交评价</button>
	</view>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			orderId: '',
			orderInfo: {},
			rating: 5,
			content: '',
			imageList: []
		}
	},
	
	computed: {
		canSubmit() {
			return this.rating > 0 && this.content.trim().length > 0;
		}
	},
	
	onLoad(options) {
		this.orderId = options.oid;
		this.getOrderInfo();
	},
	
	methods: {
		// 获取订单信息
		getOrderInfo() {
			var that = this;
			console.log('getOrderInfo called with orderId:', that.orderId);
			app.get('ApiYuyue/orderDetail', {
				id: that.orderId
			}, function(res) {
				console.log('Response from ApiYuyue/orderDetail:', res);
				
				// 检查是否返回了订单详情
				if (res && res.detail) {
					// 直接使用返回的detail对象作为订单信息
					that.orderInfo = res.detail;
					console.log('Order info loaded successfully:', that.orderInfo);
				} else if (res && res.status == 1 && res.data) {
					// 兼容之前的数据结构
					that.orderInfo = res.data;
					console.log('Order info loaded successfully (using res.data):', that.orderInfo);
				} else {
					// 错误处理：如果没有detail也没有data
					let errorMsg = (res && res.msg) ? res.msg : '获取订单信息失败';
					app.error(errorMsg);
					console.error('Failed to get order info:', errorMsg);
				}
			});
		},
		
		// 设置评分
		setRating(n) {
			this.rating = n;
		},
		
		// 选择并上传图片 (调用全局方法)
		chooseImage() {
			var that = this;
			if (that.imageList.length >= 3) {
				app.error('最多上传3张图片');
				return;
			}
			var count = 3 - that.imageList.length;
			app.chooseImage(function(urls) {
				// 返回的是上传成功后的URL数组
				that.imageList = that.imageList.concat(urls);
				if (that.imageList.length > 3) {
					// 以防万一，如果返回的urls过多，截取前3张
					that.imageList = that.imageList.slice(0, 3);
				}
			}, count);
		},
		
		// 删除图片
		deleteImage(index) {
			this.imageList.splice(index, 1);
		},
		
		// 提交评价
		submitComment() {
			var that = this;
			console.log('submitComment function called.');
			console.log('Current rating:', that.rating);
			console.log('Current content:', that.content);
			console.log('canSubmit computed value:', that.canSubmit);

			if (!that.canSubmit) {
				console.log('Submit button is disabled. Submission prevented.');
				app.error('请先评分并填写评价内容'); // 或者其他更合适的提示
				return; // 阻止后续执行
			}

			// 添加日志打印发送的数据
			console.log('Submitting comment with data:', {
				order_id: that.orderId,
				score: that.rating,
				content: that.content,
				pics: that.imageList.join(',')
			});
			app.post('ApiYuyue/comment', {
				oid: that.orderId,
				score: that.rating,
				content: that.content,
				pics: that.imageList.join(',')
			}, function(res) {
				if(res.status == 1) {
					app.alert('评价成功', function() {
						app.goto('/yuyue/orderlist');
					});
				} else {
					app.error(res.msg);
				}
			});
		}
	}
};
</script>

<style>
.container {
	padding: 20rpx;
	min-height: 100vh;
	background: #f5f5f5;
}

.order-info {
	background: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.product-info {
	display: flex;
}

.product-info image {
	width: 140rpx;
	height: 140rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
}

.info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.price {
	font-size: 32rpx;
	color: #ff4d4f;
	font-weight: bold;
}

.comment-form {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx 20rpx;
}

.rating {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.label {
	font-size: 28rpx;
	color: #333;
	margin-right: 20rpx;
}

.stars {
	display: flex;
}

.star {
	font-size: 40rpx;
	color: #ccc;
	margin-right: 10rpx;
}

.star.active {
	color: #ffc107;
}

.textarea-box {
	position: relative;
	margin-bottom: 30rpx;
}

.textarea {
	width: 100%;
	height: 200rpx;
	border: 1px solid #e8e8e8;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.word-count {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	font-size: 24rpx;
	color: #999;
}

.upload-box {
	margin-bottom: 30rpx;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	margin-top: 20rpx;
}

.image-item {
	width: 160rpx;
	height: 160rpx;
	position: relative;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
}

.image-item image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
}

.delete-btn {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-btn {
	width: 160rpx;
	height: 160rpx;
	border: 1px dashed #ccc;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 60rpx;
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx;
	background: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
}

.submit-btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	color: #fff;
	font-size: 30rpx;
	border-radius: 40rpx;
}
</style>