<template>
	<view class="container">
		<block>
			<view class="view-show" style="padding: 30rpx;padding-bottom: 200rpx;">
				<!-- 高考分数线 -->
				<view style="margin-bottom: 30rpx;">

					<view style="display: flex;align-items: center;margin-bottom: 40rpx;">
						<view style="height: 30rpx;width: 8rpx;background-color: #05aaf6;margin-right: 10rpx;"></view>
						<span style="font-weight: bold;font-size: 36rpx;">高考分数线：</span>
					</view>

					<view style="display: flex;margin-bottom: 20rpx;">
						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="areaChange($event,'1')" :value="index" :range="array">
								<view class="uni-input">{{college.areaName}} </view>
							</picker>
						</view>

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="yearChange($event,'1')" :value="index" :range="arrayYear">
								<view class="uni-input">{{college.year}}</view>
							</picker>
						</view>

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="groupChange($event,'1')" :value="index" :range="arrayGroup">
								<view class="uni-input">{{college.group}}</view>
							</picker>
						</view>
					</view>
					<!-- 表格 -->
					<view class="table-box">
						<!-- 表头 -->
						<view class="table-header">
							<view>录取批次</view>
							<view>最低分/位次</view>
							<view>省控线</view>
							<view>招生类型</view>
							<view>选科要求</view>
						</view>
						<view class="table-content" v-for="item in data['高考分数线']">
							<view>{{item.batch}}</view>
							<view>{{item.min_score}}</view>
							<view>{{item.control_line}}</view>
							<view>{{item.admission_type}}</view>
							<view>{{item.requirement}}</view>
						</view>
					</view>
				</view>

				<!-- 专业分数线 -->
				<view style="margin-bottom: 20rpx;">

					<view style="display: flex;align-items: center;margin-bottom: 40rpx;">
						<view style="height: 30rpx;width: 8rpx;background-color: #05aaf6;margin-right: 10rpx;"></view>
						<span style="font-weight: bold;font-size: 36rpx;">专业分数线：</span>
					</view>

					<view style="display: flex;margin-bottom: 20rpx;">

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="areaChange($event,'2')" :value="index" :range="array">
								<view class="uni-input">{{speciality.areaName}} </view>
							</picker>
						</view>

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="yearChange($event,'2')" :value="index" :range="arrayYear">
								<view class="uni-input">{{speciality.year}}</view>
							</picker>
						</view>

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="groupChange($event,'2')" :value="index" :range="arrayGroup">
								<view class="uni-input">{{speciality.group}}</view>
							</picker>
						</view>

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="batchChange($event,'2')" :value="index" :range="arrayBatch">
								<view class="uni-input">{{speciality.batch}}</view>
							</picker>
						</view>
					</view>
					<!-- 表格 -->
					<view class="table-box">
						<!-- 表头 -->
						<view class="table-header">
							<view>专业名称</view>
							<view>选科要求</view>
							<view>最低分/位次</view>
							<view>录取批次</view>
						</view>
						<view class="table-content" v-for="item in data['专业分数线']">
							<view>{{item.zhuanye_name}}</view>
							<view>{{item.requirement}}</view>
							<view>{{item.min_score}}</view>
							<view>{{item.batch}}</view>
						</view>
					</view>
				</view>

				<!-- 分类单招分数线 -->
				<view style="margin-bottom: 20rpx;">

					<view style="display: flex;align-items: center;margin-bottom: 40rpx;">
						<view style="height: 30rpx;width: 8rpx;background-color: #05aaf6;margin-right: 10rpx;"></view>
						<span style="font-weight: bold;font-size: 36rpx;">分类单招分数线：</span>
					</view>

					<view style="display: flex;margin-bottom: 20rpx;">

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="areaChange($event,'3')" :value="index" :range="array">
								<view class="uni-input">{{classify.areaName}} </view>
							</picker>
						</view>

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="yearChange($event,'3')" :value="index" :range="arrayYear">
								<view class="uni-input">{{classify.year}}</view>
							</picker>
						</view>

						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="typeChange($event,'3')" :value="index" :range="arrayType">
								<view class="uni-input">{{classify.type}}</view>
							</picker>
						</view>

					</view>
					<!-- 表格 -->
					<view class="table-box">
						<!-- 表头 -->
						<view class="table-header">
							<view>录取批次</view>
							<view>最低分/位次</view>
							<view>省控线</view>
							<view>招生类型</view>
							<view>选科要求</view>
						</view>
						<view class="table-content" v-for="item in data['分类单招分数线']">
							<view>{{item.batch}}</view>
							<view>{{item.min_score}}</view>
							<view>{{item.control_line}}</view>
							<view>{{item.admission_type}}</view>
							<view>{{item.requirement}}</view>
						</view>
					</view>
				</view>
				
				
				<!-- 专升本分数线 -->
				<view style="margin-bottom: 20rpx;">
				
					<view style="display: flex;align-items: center;margin-bottom: 40rpx;">
						<view style="height: 30rpx;width: 8rpx;background-color: #05aaf6;margin-right: 10rpx;"></view>
						<span style="font-weight: bold;font-size: 36rpx;">专升本分数线：</span>
					</view>
				
					<view style="display: flex;margin-bottom: 20rpx;">
				
						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="areaChange($event,'4')" :value="index" :range="array">
								<view class="uni-input">{{upgradation.areaName}} </view>
							</picker>
						</view>
				
						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="yearChange($event,'4')" :value="index" :range="arrayYear">
								<view class="uni-input">{{upgradation.year}}</view>
							</picker>
						</view>
				
						<view class="tag-item" style="margin-right: 10rpx;">
							<picker @change="subjectChange($event,'4')" :value="index" :range="arraySubject">
								<view class="uni-input">{{upgradation.subject}}</view>
							</picker>
						</view>
				
					</view>
					<!-- 表格 -->
					<view class="table-box">
						<!-- 表头 -->
						<view class="table-header">
							<view>录取批次</view>
							<view>最低分/位次</view>
							<view>省控线</view>
						</view>
						<view class="table-content" v-for="item in data['专升本分数线']">
							<view>{{item.batch}}</view>
							<view>{{item.min_score}}</view>
							<view>{{item.control_line}}</view>
						</view>
					</view>
				</view>

			</view>
		</block>
	</view>
</template>

<script>
	var app = getApp();
	import area from '../common/area.js'
	export default {
		props: {
			daxueid: ''
		},
		data() {
			return {
				loading: false,
				data: [],
				oldData: [],
				array: JSON.parse(JSON.stringify(area.provinces)), // 地区
				arrayYear: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
				arrayGroup: ['物理', '历史'],
				arrayBatch: ['本科提前批', '本科一批', '本科二批', '专科一批', '专科二批'],
				arrayType: ['普高', '中职'],
				arraySubject:['理科','文科'],
				index: 0,
				college: { // 高考分数线筛选条件
					areaName: '全部',
					year: '2023',
					group: '历史'
				},
				speciality: { // 专业分数线筛选条件
					areaName: '全部',
					year: '2023',
					group: '历史',
					batch: '本科一批'
				},
				classify: { // 分类单招分数线筛选条件
					areaName: '全部',
					year: '2023',
					type: '普高'
				},
				upgradation: { // 专升本分数线筛选条件
					areaName: '全部',
					year: '2023',
					subject: '理科'
				}

			};
		},
		created() {
			this.getdata();
			this.array = this.array.map(item => {
				console.log('item', item)
				// 替换掉字符串中的“市”和“省”
				return item.name.replace(/市|省/g, '');
			});
			this.array.unshift('全部')
		},
		onLoad: function(opt) {

		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiDaxue/fenshuxianlist', {
					daxueid: that.daxueid
				}, function(res) {
					that.loading = false;
					that.data = res.list;
					that.oldData = JSON.parse(JSON.stringify(res.list))
				});
			},
			// 地区筛选
			areaChange: function(e, type) {
				const index = e.detail.value
				if (type == '1') { // 高考分数线
					this.college.areaName = this.array[index]
					if (this.college.areaName == '全部') {
						this.data['高考分数线'] = this.oldData['高考分数线']
						return;
					}
					this.data['高考分数线'] = this.oldData['高考分数线'].filter(item => {
						return item.province == this.college.areaName
					})
				}
				
				if (type == '2') { // 专业分数线
					this.speciality.areaName = this.array[index]
					if (this.speciality.areaName == '全部') {
						this.data['专业分数线'] = this.oldData['专业分数线']
						return;
					}
					this.data['专业分数线'] = this.oldData['专业分数线'].filter(item => {
						return item.province == this.speciality.areaName
					})
				}
				
				if (type == '3') { // 分类单招分数线
					this.classify.areaName = this.array[index]
					if (this.classify.areaName == '全部') {
						this.data['分类单招分数线'] = this.oldData['分类单招分数线']
						return;
					}
					this.data['分类单招分数线'] = this.oldData['分类单招分数线'].filter(item => {
						return item.province == this.classify.areaName
					})
				}
				
				if (type == '4') { // 专升本分数线
					this.upgradation.areaName = this.array[index]
					if (this.upgradation.areaName == '全部') {
						this.data['专升本分数线'] = this.oldData['专升本分数线']
						return;
					}
					this.data['专升本分数线'] = this.oldData['专升本分数线'].filter(item => {
						return item.province == this.upgradation.areaName
					})
				}
			},
			// 年份筛选
			yearChange: function(e,type) {
				const year = this.arrayYear[e.detail.value]
				if (type == '1') { // 高考分数线
					this.college.year = year
					this.data['高考分数线'] = this.oldData['高考分数线'].filter(item => {
						return item.year == this.college.year
					})
				}
				if (type == '2') { // 专业分数线
					this.speciality.year = year
					this.data['专业分数线'] = this.oldData['专业分数线'].filter(item => {
						return item.year == this.speciality.year
					})
				}
				if (type == '3') { // 分类单招分数线
					this.classify.year = year
					this.data['分类单招分数线'] = this.oldData['分类单招分数线'].filter(item => {
						return item.year == this.classify.year
					})
				}
				
				if (type == '4') { // 专升本分数线
					this.upgradation.year = year
					this.data['专升本分数线'] = this.oldData['专升本分数线'].filter(item => {
						return item.year == this.upgradation.year
					})
				}
			},
			// 组筛选
			groupChange: function(e,type) {
				const group = this.arrayGroup[e.detail.value]
				if (type == '1') { // 高考分数线
					this.college.group = group
					this.data['高考分数线'] = this.oldData['高考分数线'].filter(item => {
						return item.subject_choice == this.college.group
					})
				}
				if (type == '2') { // 专业分数线
					this.speciality.group = group
					this.data['专业分数线'] = this.oldData['专业分数线'].filter(item => {
						return item.subject_choice == this.speciality.group
					})
				}
			},
			// 批次 筛选
			batchChange: function(e,type) {
				const batch = this.arrayBatch[e.detail.value]
				if (type == '2') { // 专业分数线
					this.speciality.batch = batch
					this.data['专业分数线'] = this.oldData['专业分数线'].filter(item => {
						return item.batch == this.speciality.batch
					})
				}
			},
			// 普高', '中职'筛选
			typeChange: function(e,type) {
				const type1 = this.arrayType[e.detail.value]
				if (type == '3') { // 分类单招分数线
					this.classify.type = type1
					this.data['分类单招分数线'] = this.oldData['分类单招分数线'].filter(item => {
						return item.admission_type == this.classify.type
					})
				}
			},
			// 科目，理科，文科
			subjectChange:function(e,type){
				const type1 = this.arraySubject[e.detail.value]
				if (type == '4') { // 专升本分数线
					this.upgradation.subject = type1
					this.data['专升本分数线'] = this.oldData['专升本分数线'].filter(item => {
						return item.admission_type == this.speciality.type
					})
				}
			}
		}
	};
</script>
<style>
	page {
		position: relative;
		width: 100%;
		height: 100%;
	}

	.tag-item {
		border-radius: 30rpx;
		padding: 2rpx 8rpx;
		border: 2rpx solid #d7d7d7;
		color: #686868;
	}

	.table-box {
		border-radius: 26rpx;
		box-shadow: 1rpx 1rpx 10rpx 0 rgba(0, 0, 0, 0.2);
		padding: 30rpx 0;
	}

	.table-header {
		display: flex;
		align-items: center;
		justify-content: space-around;
		font-size: 26rpx;
		color: #565656;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #d7d7d7;
	}

	.table-content {
		display: flex;
		align-items: center;
		justify-content: space-around;
		color: #565656;
		padding-top: 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #d7d7d7;
	}
</style>