<template>
<view>
	<block v-if="isload">
		<view class="container">
			<view class="header">
				<text class="title" v-if="activityInfo.title">{{activityInfo.title}}</text>
				<view class="activity-meta">
					<text class="meta-item">{{activityInfo.performance_name || (activityInfo.performance_type == 1 ? '团队金额' : '团队数量')}}</text>
					<text class="meta-item">{{activityInfo.reward_name || (activityInfo.reward_type == 1 ? '比例奖励' : '固定金额')}}</text>
					<text class="meta-item algorithm-tag" :class="activityInfo.algorithm_type === 'layered_reduction' ? 'algorithm-layered' : 'algorithm-standard'">
						{{activityInfo.algorithm_name || (activityInfo.algorithm_type === 'layered_reduction' ? '分层递减算法' : '传统算法')}}
					</text>
				</view>
				<view class="activity-description">
					<view class="desc-item" v-if="activityInfo.time_range">
						<text class="desc-label">活动时间：</text>
						<text class="desc-value">{{activityInfo.time_range.start_date}} ~ {{activityInfo.time_range.end_date}}</text>
					</view>
					<view class="desc-item" v-if="activityInfo.min_amount">
						<text class="desc-label">起始业绩：</text>
						<text class="desc-value">{{activityInfo.min_amount}}</text>
					</view>
					<view class="desc-item" v-if="activityInfo.deduct_contribution_text">
						<text class="desc-label">贡献值扣除：</text>
						<text class="desc-value">{{activityInfo.deduct_contribution_text}}</text>
					</view>
					<view class="desc-item" v-if="activityInfo.description">
						<text class="desc-label">活动说明：</text>
						<text class="desc-value">{{activityInfo.description}}</text>
					</view>
					<view class="desc-item">
						<text class="desc-label">创建时间：</text>
						<text class="desc-value">{{activityInfo.createtime}}</text>
					</view>
				</view>
			</view>

			<!-- 奖励规则详情 -->
			<view class="rules-box">
				<view class="rules-title">奖励规则</view>
				<view class="rules-content">
					<block v-for="(rule, index) in rewardRules" :key="index">
						<view class="rule-item">
							<view class="rule-level">等级 {{index + 1}}</view>
							<view class="rule-details">
								<view class="rule-achievement">
									<text class="rule-label">目标业绩：</text>
									<text class="rule-value">{{rule.achievement}}</text>
								</view>
								<view class="rule-reward">
									<text class="rule-label">奖励：</text>
									<text class="rule-value reward-highlight">
										{{activityInfo.reward_type == 1 ? rule.reward_value + '%' : '¥' + rule.reward_value}}
									</text>
								</view>
							</view>
							<view class="rule-calculation" v-if="activityInfo.reward_type == 1">
								按业绩的 {{rule.reward_value}}% 发放奖励
							</view>
							<view class="rule-calculation" v-else>
								固定奖励 ¥{{rule.reward_value}}
							</view>
							<view class="algorithm-note" v-if="activityInfo.algorithm_type === 'layered_reduction'">
								<text class="note-text">注：使用分层递减算法，实际奖励 = 理论奖励 - 直接下级理论奖励</text>
							</view>
						</view>
					</block>
				</view>
			</view>

			<!-- 适用条件 -->
			<view class="conditions-box">
				<view class="conditions-title">适用条件</view>
				<view class="conditions-content">
					<view class="condition-item" v-if="activityInfo.level_names">
						<view class="condition-label">适用等级：</view>
						<view class="condition-value">{{activityInfo.level_names}}</view>
					</view>
					<view class="condition-item" v-if="activityInfo.product_names">
						<view class="condition-label">适用商品：</view>
						<view class="condition-value">{{activityInfo.product_names}}</view>
					</view>
					<view class="condition-item">
						<view class="condition-label">统计类型：</view>
						<view class="condition-value">{{activityInfo.statistics_type == 1 ? '已付款订单' : activityInfo.statistics_type == 2 ? '已收货订单' : '全部订单'}}</view>
					</view>
				</view>
			</view>

			<!-- 用户状态 -->
			<view class="user-status-box" v-if="userRewardInfo && userRewardInfo.user_performance">
				<view class="status-title">我的状态</view>
				<view class="status-content">
					<view class="current-performance">
						<view class="perf-label">当前团队业绩</view>
						<view class="perf-value">{{userRewardInfo.user_performance.team_performance}}{{activityInfo.performance_type == 1 ? '元' : '件'}}</view>
					</view>
					<view class="team-info" v-if="userRewardInfo.team_structure">
						<view class="team-count">团队成员：{{userRewardInfo.team_structure.total_members}}人</view>
						<view class="team-levels">团队层级：{{userRewardInfo.team_structure.max_level}}级</view>
					</view>
					<view class="reward-progress">
						<view class="progress-label">奖励进度</view>
						<view class="progress-details">
							<view class="progress-item">
								<text class="progress-name">理论奖励</text>
								<text class="progress-amount">¥{{userRewardInfo.user_performance.theoretical_reward || 0}}</text>
							</view>
							<view class="progress-item" v-if="activityInfo.algorithm_type === 'layered_reduction'">
								<text class="progress-name">实际奖励</text>
								<text class="progress-amount success">¥{{userRewardInfo.user_performance.actual_reward || 0}}</text>
							</view>
						</view>
						<view class="calculation-explanation" v-if="userRewardInfo.user_performance.calculation_detail">
							<text class="explanation-text">{{userRewardInfo.user_performance.calculation_detail}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 奖励详情 -->
			<view class="reward-details-box" v-if="userRewardInfo && userRewardInfo.reward_details">
				<view class="details-title">奖励详情</view>
				<view class="details-content">
					<block v-for="(item, index) in userRewardInfo.reward_details" :key="index">
						<view class="detail-item">
							<view class="detail-header">
								<view class="achievement-info">
									<text class="achievement-label">等级 {{item.achievement_level}}</text>
									<text class="achievement-target">目标：{{item.achievement_target}}</text>
								</view>
								<view class="reward-info">
									<text class="reward-value">{{item.reward_value}}{{activityInfo.reward_type == 1 ? '%' : '元'}}</text>
								</view>
							</view>
							<view class="detail-amounts">
								<view class="amount-item">
									<text class="amount-label">理论奖励：</text>
									<text class="amount-value theoretical">¥{{item.theoretical_reward_amount}}</text>
								</view>
								<view class="amount-item" v-if="activityInfo.algorithm_type === 'layered_reduction' && item.actual_reward_amount !== item.theoretical_reward_amount">
									<text class="amount-label">实际奖励：</text>
									<text class="amount-value actual">¥{{item.actual_reward_amount}}</text>
								</view>
							</view>
							<view class="detail-status">
								<view class="status-badge" :class="item.is_achieved ? (item.is_paid ? 'status-paid' : (item.is_claimed ? 'status-claimed' : 'status-achieved')) : 'status-not-achieved'">
									{{item.is_achieved ? (item.is_paid ? '已发放' : (item.is_claimed ? '已领取' : '已达成')) : '未达成'}}
								</view>
								<view class="claim-action" v-if="item.is_achieved && !item.is_claimed && !item.is_paid">
									<text class="claim-btn" @tap="claimReward" :data-activity="item.activity_id" :data-level="item.achievement_level">立即领取</text>
								</view>
							</view>
							<view class="calculation-desc" v-if="item.calculation">
								{{item.calculation}}
							</view>
						</view>
					</block>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-box">
				<view class="action-btn primary" @tap="refreshData">
					刷新数据
				</view>
				<view class="action-btn secondary" @tap="gotoRecords">
					查看记录
				</view>
			</view>
		</view>
	</block>

	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			activityId: 0,
			activityInfo: {},
			rewardRules: [],
			userRewardInfo: null
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.activityId = opt.id || 0;
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShow: function() {
		// 页面显示时刷新用户奖励信息
		if (this.isload && this.activityId > 0) {
			this.getUserRewardInfo();
		}
	},
	onShareAppMessage: function() {
		var that = this;
		return this._sharewx({
			title: this.activityInfo.title,
			desc: '团队业绩奖励活动',
			pic: '/static/img/tuandui-share.png',
			callback: function() {
				// 分享回调
			}
		});
	},
	methods: {
		getdata: function() {
			var that = this;
			
			if (!that.activityId) {
				console.log('2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_001] 活动ID不能为空');
				app.alert('活动ID不能为空');
				return;
			}
			
			console.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getdata_002] 获取活动详情, ID:', that.activityId);
			that.loading = true;
			
			// 先获取活动列表，从中找到指定活动的信息
			app.get('ApiTuandui/getActivityList', {}, function(res) {
				console.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getdata_003] 活动列表响应:', res);
				if (res.status == 1) {
					// 从活动列表中找到指定活动
					var targetActivity = null;
					for (var i = 0; i < res.data.length; i++) {
						if (res.data[i].id == that.activityId) {
							targetActivity = res.data[i];
							break;
						}
					}
					
					if (targetActivity) {
						that.activityInfo = targetActivity;
						that.rewardRules = targetActivity.reward_rules || [];
						
						uni.setNavigationBarTitle({
							title: targetActivity.title || '活动详情'
						});
						
						// 获取用户奖励信息
						that.getUserRewardInfo();
					} else {
						that.loading = false;
						console.log('2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_004] 未找到指定活动');
						app.alert('活动不存在');
					}
				} else {
					that.loading = false;
					console.log('2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_005] 获取活动列表失败:', res.msg);
					app.alert(res.msg || '获取活动信息失败');
				}
			});
		},
		
		getUserRewardInfo: function() {
			var that = this;
			
			console.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_001] 获取用户奖励信息, 活动ID:', that.activityId);
			
			app.get('ApiTuandui/getUserRewardInfo', {activity_id: that.activityId}, function(res) {
				that.loading = false;
				that.isload = true;
				
				console.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_002] 用户奖励信息响应:', res);
				
				if (res.status == 1) {
					that.userRewardInfo = res.data;
					console.log('2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_003] 设置用户奖励信息成功');
				} else {
					// 用户信息获取失败不影响页面显示
					console.log('2025-01-03 22:55:53,565-WARN-[detail.vue][getUserRewardInfo_004] 获取用户奖励信息失败：', res.msg);
					that.userRewardInfo = null;
				}
				
				uni.stopPullDownRefresh();
			});
		},
		
		refreshData: function() {
			console.log('2025-01-03 22:55:53,565-INFO-[detail.vue][refreshData_001] 刷新数据');
			this.getActivityInfo();
			this.getUserRewardInfo();
		},
		
		gotoRecords: function() {
			uni.navigateTo({
				url: '/pagesExa/tuandui/records?activity_id=' + this.activityId
			});
		},
		
		claimReward: function(e) {
			var activityId = e.currentTarget.dataset.activity;
			var level = e.currentTarget.dataset.level;
			
			console.log('2025-01-03 22:55:53,565-INFO-[detail.vue][claimReward_001] 领取奖励:', activityId, level);
			
			// TODO: 实现领取奖励逻辑
			this.$refs.popmsg.show('提示', '领取奖励功能待实现');
		}
	}
};
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.header .title {
	font-size: 40rpx;
	color: #333;
	font-weight: bold;
	line-height: 1.4;
	margin-bottom: 20rpx;
}

.activity-meta {
	display: flex;
	gap: 15rpx;
	margin-bottom: 25rpx;
}

.meta-item {
	font-size: 24rpx;
	color: #666;
	background-color: #f8f9fa;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}

.activity-description {
	border-top: 1px solid #f0f0f0;
	padding-top: 20rpx;
}

.desc-item {
	display: flex;
	margin-bottom: 10rpx;
}

.desc-label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
}

.desc-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

/* 奖励规则样式 */
.rules-box, .conditions-box, .user-status-box, .reward-details-box {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.rules-title, .conditions-title, .status-title, .details-title {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.rules-content, .conditions-content, .status-content, .details-content {
	padding: 30rpx;
}

.rule-item {
	border: 1px solid #e9ecef;
	border-radius: 12rpx;
	padding: 25rpx;
	margin-bottom: 20rpx;
	background-color: #fafafa;
}

.rule-level {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.rule-details {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10rpx;
}

.rule-achievement, .rule-reward {
	display: flex;
	align-items: center;
}

.rule-label {
	font-size: 26rpx;
	color: #666;
	margin-right: 10rpx;
}

.rule-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.reward-highlight {
	color: #FA5151 !important;
}

.rule-calculation {
	font-size: 24rpx;
	color: #999;
	font-style: italic;
}

/* 适用条件样式 */
.condition-item {
	margin-bottom: 20rpx;
}

.condition-label {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.condition-value {
	font-size: 26rpx;
	color: #333;
	line-height: 1.5;
}

/* 用户状态样式 */
.current-performance {
	text-align: center;
	margin-bottom: 25rpx;
}

.perf-label {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.perf-value {
	font-size: 48rpx;
	color: #FA5151;
	font-weight: bold;
}

.team-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 25rpx;
	font-size: 24rpx;
	color: #666;
}

.reward-progress {
	border-top: 1px solid #f0f0f0;
	padding-top: 20rpx;
}

.progress-label {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.progress-details {
	display: flex;
	justify-content: space-between;
}

.progress-item {
	text-align: center;
	flex: 1;
}

.progress-name {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.progress-amount {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.progress-amount.success {
	color: #28a745;
}

.progress-amount.pending {
	color: #ffc107;
}

/* 奖励详情样式 */
.detail-item {
	border: 1px solid #e9ecef;
	border-radius: 12rpx;
	padding: 25rpx;
	margin-bottom: 20rpx;
}

.detail-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.achievement-info, .reward-info {
	display: flex;
	flex-direction: column;
}

.achievement-label, .achievement-target {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 5rpx;
}

.reward-value {
	font-size: 24rpx;
	color: #666;
	text-align: right;
}

.reward-amount {
	font-size: 32rpx;
	color: #FA5151;
	font-weight: bold;
	text-align: right;
}

.detail-status {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.status-badge {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.status-unachieved {
	background-color: #f0f0f0;
	color: #999;
}

.status-paid {
	background-color: #d4edda;
	color: #155724;
}

.status-pending {
	background-color: #fff3cd;
	color: #856404;
}

.status-claimable {
	background-color: #d1ecf1;
	color: #0c5460;
}

.claim-btn {
	background-color: #FA5151;
	color: #fff;
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.calculation-desc {
	font-size: 24rpx;
	color: #999;
	font-style: italic;
}

/* 操作按钮样式 */
.action-box {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	margin-bottom: 100rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 30rpx;
}

.action-btn.primary {
	background-color: #FA5151;
	color: #fff;
}

.action-btn.secondary {
	background-color: #f8f9fa;
	color: #333;
	border: 1px solid #e9ecef;
}
</style> 