<template>
  <view class="page flex-col justify-end">
  
    
    <!-- 主要内容区域 -->
    <view class="block_5 flex-col">
      <text class="text_11">询价市场</text>
      <view class="block_7 flex-row justify-between">
        <view class="text-group_6 flex-col justify-between">
          <text class="text_12">{{total_gongying}}</text>
          <text class="text_13">累计供应单总数</text>
        </view>
        <view class="text-group_7 flex-col justify-between">
          <text class="text_14">{{total_qiugou}}</text>
          <text class="text_15">累计求购单总数</text>
        </view>
      </view>
      <view class="group_5 flex-row">
        <view class="text-group_8 flex-col justify-between" @tap="goto" data-url="fatielog?type=2">
          <text class="text_16">{{my_gongying}}</text>
          <text class="text_17">我的供应单</text>
        </view>
        <view class="text-group_9 flex-col justify-between" @tap="goto" data-url="fatielog?type=1">
          <text class="text_18">{{my_qiugou}}</text>
          <text class="text_19">我的求购数</text>
        </view>
        <view class="text-group_10 flex-col justify-between">
          <text class="text_20">{{my_baojia}}</text>
          <text class="text_21">我的报价数</text>
        </view>
      </view>
      <view class="list-container">
        <view
          class="list-item flex-col"
          v-for="(item, index) in loopData0"
          :key="index"
        >
          <view class="item-header flex-row justify-between">
            <text class="title" v-html="item.lanhutext0"></text>
            <view class="more flex-row" @tap="goto" :data-url="'ltlist?type=' + (index === 0 ? '1' : '2')">
              <text class="more-text" v-html="item.lanhutext1"></text>
              <image
                class="more-icon"
                referrerpolicy="no-referrer"
                :src="item.lanhuimage0"
              />
            </view>
          </view>
          <view class="list-content">
            <view 
              class="list-row"
              v-for="(subItem, subIndex) in item.list"
              :key="subIndex"
              :data-url="'detail?id=' + subItem.id + '&type=' + (index === 0 ? '1' : '2')"
              @tap="goto"
            >
              <view class="row-left">
                <text class="row-title">{{subItem.title}}</text>
                <text class="row-type">{{subItem.type}}</text>
              </view>
              <view class="row-right">
                <text class="row-status">{{subItem.status}}</text>
                <text class="row-time">{{subItem.time}}</text>
              </view>
            </view>
          </view>
          <view class="item-content flex-row justify-between">
            <view
              class="tag"
              :style="{ background: item.lanhuBg4 }"
            >
              <text
                class="tag-text"
                :style="{ color: item.lanhufontColor2 }"
                v-html="item.lanhutext2"
              ></text>
            </view>
            <view
              class="tag"
              :style="{ background: item.lanhuBg5 }"
            >
              <text
                class="tag-text"
                :style="{ color: item.lanhufontColor3 }"
                v-html="item.lanhutext3"
              ></text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮和导航栏 -->
    <view class="block_3 flex-row justify-between">
      <view class="text-wrapper_4 flex-col" @tap="goto" data-url="fatie?type=qiugou">
        <text class="text_7">我要求购</text>
      </view>
      <view class="text-wrapper_5 flex-col" @tap="goto" data-url="fatie?type=gongying">
        <text class="text_8">我要供应</text>
      </view>
    </view>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      loading: false,
      isload: false,
      opt: {},
      // 统计数据
      total_gongying: 0,
      total_qiugou: 0,
      my_gongying: 0,
      my_qiugou: 0,
      my_baojia: 0,
      // 列表数据
      loopData0: [
        {
          lanhutext0: '最新采购',
          lanhutext1: '点击进入采购列表页',
          lanhuimage0: 'https://lanhu-oss.lanhuapp.com/FigmaDDSSlicePNG604a5f47b9c178e0a0016a755dc76ed3.png',
          lanhuBg4: 'rgba(255,246,234,1.000000)',
          lanhutext2: '采购需求',
          lanhufontColor2: 'rgba(238,137,44,1.000000)',
          lanhuBg5: 'rgba(255,246,234,1.000000)',
          lanhutext3: '待报价',
          lanhufontColor3: 'rgba(238,137,44,1.000000)',
          list: []
        },
        {
          lanhutext0: '最新供应',
          lanhutext1: '点击进入供应列表页',
          lanhuimage0: 'https://lanhu-oss.lanhuapp.com/FigmaDDSSlicePNG604a5f47b9c178e0a0016a755dc76ed3.png',
          lanhuBg4: 'rgba(255,239,234,1.000000)',
          lanhutext2: '供应信息',
          lanhufontColor2: 'rgba(238,90,44,1.000000)',
          lanhuBg5: 'rgba(255,239,234,1.000000)',
          lanhutext3: '可询价',
          lanhufontColor3: 'rgba(238,90,44,1.000000)',
          list: []
        }
      ]
    };
  },
  onLoad(options) {
    this.opt = app.getopts(options);
    this.getdata();
  },
  onPullDownRefresh() {
    this.getdata();
  },
  methods: {
    // 获取数据
    getdata(loadmore) {
      var that = this;
      if(!loadmore) {
        that.loading = true;
      }
      
      // 获取统计数据
      app.get('ApiCaigou/statistics', {}, function(res) {
        if(res.status == 1) {
          that.my_gongying = res.data.my_gongying;
          that.my_qiugou = res.data.my_caigou;
          that.my_baojia = res.data.my_baojia;
        }
      });

      // 获取最新采购列表
      app.get('ApiCaigou/latest_caigou', {}, function(res) {
        if(res.status == 1) {
          that.loopData0[0].list = res.data.map(item => ({
            id: item.id,
            title: item.title,
            type: item.categoryname,
            status: '待报价',
            time: item.wait_time || that.formatTime(item.addtime)
          }));
        }
      });

      // 获取最新供应列表
      app.get('ApiCaigou/latest_gongying', {}, function(res) {
        if(res.status == 1) {
          that.loopData0[1].list = res.data.map(item => ({
            id: item.id,
            title: item.title,
            type: item.categoryname,
            status: '可询价',
            time: that.formatTime(item.addtime),
            price: item.price
          }));
        }
      });

      // 获取采购总数
      app.get('ApiCaigou/lists', {
        type: 1,
        page: 1,
        pagesize: 1
      }, function(res) {
        if(res.status == 1) {
          that.total_qiugou = res.data.count;
        }
      });

      // 获取供应总数
      app.get('ApiCaigou/lists', {
        type: 2,
        page: 1,
        pagesize: 1
      }, function(res) {
        if(res.status == 1) {
          that.total_gongying = res.data.count;
          that.loading = false;
          that.isload = true;
        }
        // 停止下拉刷新动画
        uni.stopPullDownRefresh();
      });
    },

    // 格式化时间
    formatTime(time) {
      if(!time) return '';
      const now = new Date();
      const date = new Date(time);
      const diff = now - date;
      const hour = 1000 * 60 * 60;
      const day = hour * 24;

      if(diff < hour) {
        return '1小时内';
      } else if(diff < day) {
        return Math.floor(diff/hour) + '小时前';
      } else {
        return Math.floor(diff/day) + '天前';
      }
    },

    // 页面跳转
    goto(e) {
      const url = e.currentTarget.dataset.url;
      if(url) {
        uni.navigateTo({
          url: url
        });
      }
    },

    // 底部导航切换
    getmenuindex(e) {
      app.getmenuindex(e);
    }
  }
};
</script>

<style>
.page {
  background-color: #f6f7f9;
  min-height: 100vh;
  width: 750rpx;
  overflow-x: hidden;
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
}

/* 头部区域 */
.block_5 {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  width: 750rpx;
  position: relative;
  overflow: hidden;
  padding: 88rpx 0 32rpx;
}

.text_11 {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: bold;
  margin: 32rpx;
  letter-spacing: 1rpx;
}

.list-container {
  padding: 0 32rpx;
  margin-top: 32rpx;
}

.list-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 28rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #f0f0f0;
}

.item-header {
  margin-bottom: 24rpx;
}

.title {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 600;
}

.more {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.more-text {
  font-size: 24rpx;
  color: #95a5a6;
}

.more-icon {
  width: 32rpx;
  height: 32rpx;
}

.item-content {
  display: flex;
  gap: 20rpx;
}

.tag {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 数据统计区 */
.block_7 {
  margin: 32rpx;
  padding: 32rpx;
  background: transparent;
  display: flex;
  justify-content: space-between;
  border: none;
}

.text-group_6, .text-group_7 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.text_12, .text_14 {
  font-size: 64rpx;
  color: #ffffff;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8rpx;
  font-family: DINPro;
}

.text_13, .text_15 {
  font-size: 24rpx;
  color: rgba(255,255,255,0.9);
}

/* 个人数据区 */
.group_5 {
  margin: 32rpx;
  padding: 20rpx;
  background: rgba(255,255,255,0.1);
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
}

.text-group_8, .text-group_9, .text-group_10 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 12rpx 0;
}

.text-group_8:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1rpx;
  background: rgba(255,255,255,0.2);
}

.text_16, .text_18, .text_20 {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8rpx;
  font-family: DINPro;
}

.text_17, .text_19, .text_21 {
  font-size: 24rpx;
  color: rgba(255,255,255,0.9);
}

/* 底部按钮区 */
.block_3 {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  padding: 20rpx 32rpx;
  background: #ffffff;
  display: flex;
  gap: 24rpx;
  box-sizing: border-box;
  z-index: 98;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
}

.text-wrapper_4, .text-wrapper_5 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.text-wrapper_4 {
  background: #ff4757;
}

.text-wrapper_5 {
  background: #ff7f50;
}

/* 求购信息区 */
.block_2 {
  margin: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 28rpx;
  border: 1rpx solid #f0f0f0;
}

.text_4 {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 24rpx;
}

.list_2 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.text-wrapper_3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: background-color 0.3s ease;
}

.text-wrapper_3:active {
  background: #f1f2f3;
}

.text_5 {
  font-size: 28rpx;
  color: #2c3e50;
  flex: 1;
}

.text_6 {
  font-size: 24rpx;
  color: #95a5a6;
  margin-left: 16rpx;
}

/* 添加列表内容样式 */
.list-content {
  margin: 16rpx 0 24rpx;
}

.list-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-row:last-child {
  border-bottom: none;
}

.row-left {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.row-title {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

.row-type {
  font-size: 24rpx;
  color: #95a5a6;
}

.row-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.row-status {
  font-size: 26rpx;
  color: #ff4757;
  font-weight: 500;
}

.row-time {
  font-size: 24rpx;
  color: #95a5a6;
}

/* 添加底部导航相关样式 */
.dp-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}

/* 调整页面底部间距，避免被底部导航遮挡 */
.page {
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
}
</style> 