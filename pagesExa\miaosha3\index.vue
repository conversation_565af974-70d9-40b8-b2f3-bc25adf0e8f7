<template>
<view class="container">
	<block v-if="isload">
		<dd-tab :itemdata="['已结束','抢购中','即将开始']" :itemst="['0','1','2']" :st="st" :isfixed="true" @changetab="changetab"></dd-tab>
		<view style="width:100%;height:100rpx"></view>
		<view class="content">
			<view v-for="(item, index) in datalist" :key="index" class="item">
				<image class="f1" style="height:150px;"  :src="item.pic" @tap="goto" :data-url="'product?id=' + item.id"></image>
				<view class="f2">
					<text class="t1">{{item.name}}</text>
					<view class="t2" :style="{color:t('color1')}">
						<text>售卖人:{{item.outname}}</text>
					</view>
					<view class="t2" :style="{color:t('color1')}">
						<text>开始时间:{{item.miaosha_date}}</text>
					</view>
					<view class="t2" :style="{color:t('color1')}">
						<!--  && item.qiang == 1 -->
						<text v-if="st == 2 && item.qiang == 1 ">可提前开始时间:{{item.miaosha_date2}}</text>
					</view>
					<view class="t3">
						<text class="x1" :style="{color:t('color1')}"><text style="font-size:24rpx">￥</text>{{item.sell_price}}</text>
						<!-- <text class="x2">￥{{item.market_price}}</text> -->
						<button @tap="goto" :data-url="'product?id=' + item.id" class="x3" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" v-if="st == 1">立即抢购</button>
						<button @tap="goto" :data-url="'product?id=' + item.id" class="x3" v-else-if="item.starttime2<=nowtime && item.qiang == 1" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">可提前抢购</button>
						<button @tap="goto" :data-url="'product?id=' + item.id" class="x3" v-else-if="st==2 " :style="{background:t('color2')}">抢先看看</button>
						<button class="x3 xx1" v-else :style="{background:t('color4')}">已结束</button>
					</view>
				</view>
			</view>
			<view class="item" style="display:block" v-if="nodata"><nodata></nodata></view>
			<nomore v-if="nomore"></nomore>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			bid:'',
      st: '',
      datalist: [],
      pagenum: 1,
      navlist: "",
      activetime: "",
      activeindex: "",
      selected: "",
      top_bar_scroll: "",
      seckill_duration: "",
      nowtime: "",
      nomore: false,
			nodata:false,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st;
		this.bid = this.opt.bid || '';
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getDataList();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getDataList(true);
    }
  },
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			that.loaded();
			that.getDataList();
		},
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getDataList();
    },
    getDataList: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var st = that.st;
      var navlist = that.navlist;
      var pagenum = that.pagenum;
			that.nomore = false;
			that.nodata = false;
      app.post('ApiMiaosha/getprolist', {bid:that.bid,st:st,pagenum: pagenum}, function (res) {
        uni.stopPullDownRefresh();
        var data = res.data;
		that.nowtime = res.nowtime;
        if (pagenum == 1) {
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    }
  }
};
</script>
<style>
.container{width:100%;overflow:hidden}
.navbg{width: 100%;position:relative}
.navbg:after {background: linear-gradient(90deg,rgba(253, 74, 70, 1) 0%,rgba(253, 74, 70, 0.8) 100%);content: '';width: 160%;height:300rpx;position: absolute;left: -30%;top:0;border-radius: 0 0 50% 50%;z-index:1}
.nav {width: 100%;position:relative;z-index:2}
.nav>scroll-view {overflow: visible !important;padding-top:20rpx;padding-bottom:20rpx}
.nav .f1 {flex-grow: 0;flex-shrink: 0;display:flex;align-items:center;color:#fff;position:relative;z-index:2}
.nav .f1 .item{flex-grow: 0;flex-shrink: 0;width:150rpx;text-align:center;padding:16rpx 0;opacity: 0.6;}
.nav .f1 .item .t1 {font-size:34rpx;font-weight:bold}
.nav .f1 .item .t2 {font-size:24rpx}
.nav .f1 .item .t3 {font-size:30rpx}
.nav .f1 .item.active {position: relative;color:#fff;opacity:1}

.content{width:94%;margin-left:3%;position:relative;z-index:3}
.data-empty{background:#fff;border-radius:16rpx}
.content .item{width:100%;display:flex;padding: 20rpx;background:#fff;border-radius:16rpx;margin-bottom:20rpx}
.item .f1{width:200rpx;height:200rpx;margin-right:20rpx;}
.item .f2{position: relative; padding-right: 20rpx;flex:1;display:flex;flex-direction:column}
.item .f2 .t1{font-size:28rpx;font-weight:bold;color: #222;margin-top: 2px;height:80rpx;overflow:hidden}
.item .f2 .t2{width:100%;margin-top:12rpx;display:flex;align-items:center}
.item .f2 .t2 .x2{padding-left:16rpx;font-size:24rpx;font-weight:bold}
.item .f2 .t3{width:100%;margin-top:20rpx;display:flex;align-items:flex-end}
.item .f2 .t3 .x1{font-size:32rpx;font-weight:bold}
.item .f2 .t3 .x2{color:#999999;font-size:24rpx;text-decoration:line-through;padding-left:8rpx}
.item .f2 .t3 .x3{position:absolute;bottom:0;right:0;border: 0;color: #fff;font-size:28rpx;padding:0 28rpx;height:54rpx;line-height:50rpx;border-radius:54rpx;margin:0}
.item .f2 .t3 .x3.xx1{background:#888}

.progress{width:240rpx;font-size:24rpx}
.nomore-footer-tips{background:#fff!important}
</style>