<template>
<view class="container">
	<block v-if="isload">
		<!-- 商户基本信息 -->
		<view class="merchant-info-card">
			<view class="merchant-header">
				<view class="merchant-avatar">
					<image :src="merchant.logo || '/static/img/default_merchant.png'" class="avatar-img"></image>
				</view>
				<view class="merchant-info">
					<view class="merchant-name">{{merchant.name}}</view>
					<view class="merchant-category">{{merchant.category_name || '未分类'}}</view>
					<view class="merchant-status">
						<text class="status-badge" :style="{background: merchant.status_color}">
							{{merchant.status_text}}
						</text>
					</view>
				</view>
			</view>
			<view class="merchant-details">
				<view class="detail-item" v-if="merchant.address">
					<text class="detail-label">地址：</text>
					<text class="detail-value">{{merchant.address}}</text>
				</view>
				<view class="detail-item" v-if="merchant.phone">
					<text class="detail-label">电话：</text>
					<text class="detail-value">{{merchant.phone}}</text>
				</view>
				<view class="detail-item" v-if="merchant.description">
					<text class="detail-label">简介：</text>
					<text class="detail-value">{{merchant.description}}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">注册时间：</text>
					<text class="detail-value">{{merchant.createtime_format}}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">账户余额：</text>
					<text class="detail-value highlight">¥{{merchant.money}}</text>
				</view>
			</view>
			<!-- 操作按钮 -->
			<view class="action-buttons">
				<view class="action-btn primary" @tap="callMerchant" v-if="merchant.phone">
					<text class="iconfont icondianhua"></text>
					<text>拨打电话</text>
				</view>
				<view class="action-btn" @tap="viewLocation" v-if="merchant.longitude && merchant.latitude">
					<text class="iconfont iconweizhi"></text>
					<text>查看位置</text>
				</view>
			</view>
		</view>

		<!-- 统计数据 -->
		<view class="stats-container">
			<view class="stats-title">经营数据</view>
			<view class="stats-grid">
				<view class="stat-card">
					<text class="stat-number">{{statistics.today_orders}}</text>
					<text class="stat-label">今日订单</text>
				</view>
				<view class="stat-card">
					<text class="stat-number">¥{{statistics.today_amount}}</text>
					<text class="stat-label">今日交易额</text>
				</view>
				<view class="stat-card">
					<text class="stat-number">{{statistics.month_orders}}</text>
					<text class="stat-label">本月订单</text>
				</view>
				<view class="stat-card">
					<text class="stat-number">¥{{statistics.month_amount}}</text>
					<text class="stat-label">本月交易额</text>
				</view>
				<view class="stat-card">
					<text class="stat-number">{{statistics.total_orders}}</text>
					<text class="stat-label">总订单数</text>
				</view>
				<view class="stat-card">
					<text class="stat-number">¥{{statistics.total_amount}}</text>
					<text class="stat-label">总交易额</text>
				</view>
			</view>
		</view>

		<!-- 最近订单 -->
		<view class="recent-orders" v-if="recent_orders.length > 0">
			<view class="section-header">
				<text class="section-title">最近订单</text>
				<text class="section-more" @tap="goto" :data-url="'merchant_orders?merchant_id=' + merchant.id">查看更多</text>
			</view>
			<view class="order-list">
				<view class="order-item" v-for="(order, index) in recent_orders" :key="index">
					<view class="order-header">
						<view class="order-user">
							<image :src="order.headimg || '/static/img/default_avatar.png'" class="user-avatar"></image>
							<text class="user-name">{{order.nickname || '用户'}}</text>
						</view>
						<view class="order-status">
							<text class="status-text">{{order.status_text}}</text>
						</view>
					</view>
					<view class="order-content">
						<view class="order-info">
							<text class="order-sn">订单号：{{order.ordersn}}</text>
							<text class="order-time">{{order.createtime_format}}</text>
						</view>
						<view class="order-amount">
							<text class="amount-text">¥{{order.totalprice}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-orders" v-if="recent_orders.length === 0">
			<text class="empty-text">暂无订单记录</text>
		</view>
	</block>
	
	<!-- 加载状态 -->
	<loading v-if="loading"></loading>
	
	<!-- 底部导航 -->
	<dp-tabbar :opt="opt"></dp-tabbar>
	
	<!-- 消息提示 -->
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			opt: {},
			loading: false,
			isload: false,
			merchant_id: 0,
			merchant: {},
			statistics: {
				today_orders: 0,
				today_amount: '0.00',
				month_orders: 0,
				month_amount: '0.00',
				total_orders: 0,
				total_amount: '0.00'
			},
			recent_orders: []
		};
	},

	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.merchant_id = opt.merchant_id || 0;
		
		if (!this.merchant_id) {
			app.error('参数错误');
			return;
		}
		
		this.getdata();
	},

	onPullDownRefresh: function () {
		this.getdata();
	},

	methods: {
		// 获取商户详情
		getdata: function () {
			var that = this;
			that.loading = true;
			
			app.get('ApiCityAgent/getMerchantDetail', {
				merchant_id: that.merchant_id
			}, function (res) {
				that.loading = false;
				uni.stopPullDownRefresh();
				
				if (res.status == 0) {
					app.error(res.msg);
					return;
				}
				
				// 设置导航标题
				uni.setNavigationBarTitle({
					title: res.merchant.name || '商户详情'
				});
				
				// 更新数据
				that.merchant = res.merchant;
				that.statistics = res.statistics;
				that.recent_orders = res.recent_orders || [];
				
				that.loaded();
			});
		},
		
		// 拨打电话
		callMerchant: function () {
			var phone = this.merchant.phone;
			if (!phone) {
				app.error('该商户未设置联系电话');
				return;
			}
			
			uni.showModal({
				title: '拨打电话',
				content: '确认拨打电话：' + phone + '？',
				success: function (res) {
					if (res.confirm) {
						uni.makePhoneCall({
							phoneNumber: phone,
							fail: function() {
								app.error('拨打电话失败');
							}
						});
					}
				}
			});
		},
		
		// 查看位置
		viewLocation: function () {
			var longitude = parseFloat(this.merchant.longitude);
			var latitude = parseFloat(this.merchant.latitude);
			var name = this.merchant.name;
			
			if (!longitude || !latitude) {
				app.error('该商户未设置位置信息');
				return;
			}
			
			uni.openLocation({
				longitude: longitude,
				latitude: latitude,
				name: name,
				address: this.merchant.address,
				scale: 18,
				fail: function() {
					app.error('打开地图失败');
				}
			});
		},
		
		// 页面跳转
		goto: function (e) {
			var url = e.currentTarget.dataset.url;
			if (url) {
				// 替换模板变量
				url = url.replace('{{merchant.id}}', this.merchant.id);
				app.goto('/pagesExt/cityagent/' + url);
			}
		},
		
		// 数据加载完成
		loaded: function () {
			this.isload = true;
		}
	}
};
</script>

<style>
.container {
	background: #f8f8f8;
	min-height: 100vh;
}

/* 商户信息卡片 */
.merchant-info-card {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.merchant-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 30rpx;
}

.merchant-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 20rpx;
	overflow: hidden;
	margin-right: 30rpx;
}

.avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.merchant-info {
	flex: 1;
}

.merchant-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.merchant-category {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.merchant-status {
	
}

.status-badge {
	font-size: 24rpx;
	color: white;
	padding: 10rpx 20rpx;
	border-radius: 25rpx;
}

.merchant-details {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 30rpx;
	margin-bottom: 30rpx;
}

.detail-item {
	display: flex;
	margin-bottom: 20rpx;
}

.detail-item:last-child {
	margin-bottom: 0;
}

.detail-label {
	width: 140rpx;
	font-size: 28rpx;
	color: #666;
}

.detail-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	word-break: break-all;
}

.detail-value.highlight {
	color: #4CAF50;
	font-weight: bold;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
	padding-top: 30rpx;
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 25rpx 0;
	margin-right: 20rpx;
	background: #f8f8f8;
	border-radius: 15rpx;
	font-size: 26rpx;
	color: #666;
}

.action-btn:last-child {
	margin-right: 0;
}

.action-btn.primary {
	background: #4CAF50;
	color: white;
}

.action-btn .iconfont {
	font-size: 36rpx;
	margin-bottom: 10rpx;
}

.action-btn.primary .iconfont {
	color: white;
}

/* 统计数据 */
.stats-container {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.stats-grid {
	display: flex;
	flex-wrap: wrap;
}

.stat-card {
	width: 33.33%;
	text-align: center;
	padding: 25rpx 0;
	border-right: 1rpx solid #f0f0f0;
	border-bottom: 1rpx solid #f0f0f0;
	box-sizing: border-box;
}

.stat-card:nth-child(3n) {
	border-right: none;
}

.stat-card:nth-child(n+4) {
	border-bottom: none;
}

.stat-number {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #4CAF50;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

/* 最近订单 */
.recent-orders {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.section-more {
	font-size: 26rpx;
	color: #4CAF50;
}

.order-list {
	
}

.order-item {
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.order-item:last-child {
	border-bottom: none;
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.order-user {
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	margin-right: 15rpx;
}

.user-name {
	font-size: 28rpx;
	color: #333;
}

.order-status {
	
}

.status-text {
	font-size: 24rpx;
	color: #4CAF50;
	padding: 5rpx 15rpx;
	background: rgba(76, 175, 80, 0.1);
	border-radius: 15rpx;
}

.order-content {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}

.order-info {
	flex: 1;
}

.order-sn {
	display: block;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.order-time {
	font-size: 24rpx;
	color: #999;
}

.order-amount {
	
}

.amount-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 空状态 */
.empty-orders {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 80rpx 30rpx;
	text-align: center;
	box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}
</style> 