<template>
	<view class="live-msg">
		<scroll-view 
			class="msg-scroll" 
			:scroll-top="scrollTop" 
			:scroll-with-animation="true"
			scroll-y
			@scroll="handleScroll"
			:show-scrollbar="false"
		>
			<view class="msg-list">
				<!-- 系统消息 -->
				<view class="msg-item system">
					<text class="system-text">欢迎来到直播间，请文明发言，理性观看</text>
				</view>
				
				<!-- 用户消息列表 -->
				<view 
					class="msg-item" 
					v-for="(item, index) in messageList" 
					:key="index"
				>
					<template v-if="item.type === 'system'">
						<text class="system-text">{{item.message}}</text>
					</template>
					<template v-else>
						<text class="user-name">{{item.nickname}}：</text>
						<text class="msg-content">{{item.message}}</text>
					</template>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			messageList: [],
			scrollTop: 0,
			autoScroll: true
		}
	},
	
	methods: {
		// 添加新消息
		addMessage(message) {
			this.messageList.push(message)
			// 限制消息数量，保留最新的50条
			if (this.messageList.length > 50) {
				this.messageList = this.messageList.slice(-50)
			}
			// 自动滚动到底部
			this.$nextTick(() => {
				if (this.autoScroll) {
					this.scrollToBottom()
				}
			})
		},
		
		// 添加系统消息
		addSystemMessage(message) {
			this.addMessage({
				type: 'system',
				message
			})
		},
		
		// 滚动到底部
		scrollToBottom() {
			const query = uni.createSelectorQuery().in(this)
			query.select('.msg-list').boundingClientRect(data => {
				if (data) {
					this.scrollTop = data.height
				}
			}).exec()
		},
		
		// 处理滚动事件
		handleScroll(e) {
			const scrollHeight = e.detail.scrollHeight
			const scrollTop = e.detail.scrollTop
			const clientHeight = e.detail.clientHeight
			
			// 如果用户向上滚动，则暂停自动滚动
			this.autoScroll = scrollHeight - scrollTop - clientHeight < 20
		},
		
		// 清空消息
		clearMessages() {
			this.messageList = []
			this.scrollTop = 0
		}
	}
}
</script>

<style lang="scss">
.live-msg {
	position: absolute;
	left: 20rpx;
	right: 120rpx;
	bottom: 120rpx;
	height: 400rpx;
	
	.msg-scroll {
		height: 100%;
		
		.msg-list {
			padding: 20rpx;
		}
		
		.msg-item {
			margin-bottom: 10rpx;
			font-size: 26rpx;
			color: #fff;
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
			word-break: break-all;
			
			&.system {
				.system-text {
					color: rgba(255, 255, 255, 0.8);
					font-size: 24rpx;
				}
			}
			
			.user-name {
				color: #FFD700;
				margin-right: 10rpx;
			}
			
			.msg-content {
				color: #fff;
			}
		}
	}
}
</style> 