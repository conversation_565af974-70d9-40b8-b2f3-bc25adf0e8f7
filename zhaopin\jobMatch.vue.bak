<template>
  <view class="job-match-page">
    <!-- 顶部匹配分析 -->
    <!-- <view class="match-analysis" :style="{ background: `linear-gradient(135deg, ${t('color1')}, ${t('color1')})` }">
      <view class="analysis-header">
        <text class="title">智能匹配分析</text>
        <text class="subtitle">基于您的简历信息，为您推荐最适合的工作</text>
      </view>
      
      <view class="match-stats">
        <view class="stat-item">
          <text class="number">{{ matchStats.totalJobs }}</text>
          <text class="label">匹配职位</text>
        </view>
        <view class="stat-item">
          <text class="number">{{ matchStats.highMatch }}%</text>
          <text class="label">较高匹配度</text>
        </view>
        <view class="stat-item">
          <text class="number">{{ matchStats.salary }}</text>
          <text class="label">平均薪资</text>
        </view>
      </view>
      
      <view class="match-chart" :style="{ background: `rgba(${t('color1rgb')}, 0.1)` }">
        <view class="chart-title">匹配度分布</view>
        <view class="chart-bars">
          <view 
            v-for="(item, index) in matchDistribution" 
            :key="index"
            class="bar-item"
          >
            <view 
              class="bar" 
              :style="{
                height: item.percentage + '%',
                background: t('color1'),
                opacity: item.type === 'very-high' ? 1 :
                         item.type === 'high' ? 0.8 :
                         item.type === 'medium' ? 0.6 : 0.4
              }"
            ></view>
            <view class="bar-info">
              <text class="bar-count">{{ item.count }}个</text>
              <text class="bar-label">{{ item.label }}</text>
              <text class="bar-percent">{{ item.percentage }}%</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->
    
    <!-- 标签筛选 -->
    <view class="filter-section">
      <scroll-view scroll-x class="filter-scroll" show-scrollbar="false">
        <view class="filter-tags">
          <view 
            v-for="(tag, index) in filterTags" 
            :key="index"
            class="tag"
            :style="{
              background: tag.active ? t('color1') : '#f5f5f5',
              color: tag.active ? '#ffffff' : '#666'
            }"
            @tap="toggleFilter(index)"
          >
            {{ tag.name }}
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 推荐职位列表 -->
    <view class="job-list">
      <view 
        v-for="(job, index) in matchedJobsWithClass" 
        :key="index"
        class="job-card"
        :style="{ boxShadow: `0 2rpx 12rpx rgba(${t('color1rgb')}, 0.05)` }"
        @tap="viewJobDetail(job.id)"
      >
        <view class="job-header">
          <view class="job-info">
            <text class="job-title" :style="{ color: t('color1') }">{{ job.title }}</text>
            <text class="company-name">{{ job.company }}</text>
          </view>
          <view 
            class="match-score"
            :style="{
              color: t('color1'),
              opacity: job.scoreClass === 'score-excellent' ? 1 :
                      job.scoreClass === 'score-good' ? 0.8 :
                      job.scoreClass === 'score-medium' ? 0.6 : 0.4
            }"
          >
            {{ job.matchScore }}%
            <text class="match-label">匹配度</text>
          </view>
        </view>
        
        <view class="job-tags">
          <text 
            class="tag" 
            v-for="(tag, tagIndex) in job.tags" 
            :key="tagIndex"
            :style="{ background: `rgba(${t('color1rgb')}, 0.1)` }"
          >
            {{ tag }}
          </text>
        </view>
        
        <!-- 添加任职资格显示 -->
        <!-- <view class="job-requirements" v-if="job.requirements && job.requirements.length > 0">
          <view class="requirements-title">任职要求：</view>
          <view class="requirements-list">
            <view 
              class="requirement-item"
              v-for="(req, reqIndex) in job.requirements" 
              :key="reqIndex"
            >
              <text class="bullet">•</text>
              <text class="content">{{ req }}</text>
            </view>
          </view>
        </view> -->
        
        <view class="match-details" :style="{ background: `rgba(${t('color1rgb')}, 0.05)` }">
          <view class="match-item" v-for="(item, itemIndex) in job.matchDetailsWithClass" :key="itemIndex">
            <text class="match-type">{{ item.type }}</text>
            <view class="progress-bar" :style="{ background: `rgba(${t('color1rgb')}, 0.1)` }">
              <view 
                class="progress" 
                :style="{
                  width: item.score + '%',
                  background: t('color1'),
                  opacity: item.progressClass === 'progress-excellent' ? 1 :
                          item.progressClass === 'progress-good' ? 0.8 :
                          item.progressClass === 'progress-medium' ? 0.6 : 0.4
                }"
              ></view>
            </view>
            <text class="match-percent">{{ item.score }}%</text>
          </view>
        </view>
        
        <view class="job-footer">
          <view class="salary" :style="{ color: t('color1') }">{{ job.salary }}</view>
          <view class="location">{{ job.location }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      matchStats: {
        totalJobs: 0,
        highMatch: 0,
        salary: '0'
      },
      matchDistribution: [
        { type: 'very-high', label: '90%+', percentage: 0 },
        { type: 'high', label: '80-89%', percentage: 0 },
        { type: 'medium', label: '60-79%', percentage: 0 },
        { type: 'low', label: '60%以下', percentage: 0 }
      ],
      filterTags: [
        { name: '全部', active: true },
        { name: '匹配度优先', active: false },
        { name: '薪资优先', active: false },
        { name: '距离优先', active: false },
        { name: '最新发布', active: false }
      ],
      matchedJobs: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      filterData: null,
      matchResult: null
    }
  },
  
  onLoad(options) {
    console.log('页面加载参数:', options)
    
    if (options.from === 'filter') {
      // 从筛选页面来，获取缓存的数据
      const filterData = uni.getStorageSync('jobMatchFilterData')
      const matchResult = uni.getStorageSync('jobMatchResult')
      
      console.log('从缓存获取的筛选数据:', filterData)
      console.log('从缓存获取的匹配结果:', matchResult)
      
      if (filterData) {
        this.filterData = filterData
      }
      
      if (matchResult && matchResult.status === 1) {
        this.processMatchResult(matchResult)
      } else {
        this.fetchMatchedJobs()
      }
      
      // 使用完后清除缓存
      uni.removeStorageSync('jobMatchFilterData')
      uni.removeStorageSync('jobMatchResult')
    } else {
      // 直接进入页面，获取默认数据
      this.fetchMatchedJobs()
    }
  },
  
  computed: {
    matchedJobsWithClass() {
      return this.matchedJobs.map(job => ({
        ...job,
        scoreClass: this.getScoreClass(job.matchScore),
        matchDetailsWithClass: [
          { 
            type: '综合匹配', 
            score: job.matchScore,
            progressClass: this.getProgressClass(job.matchScore)
          }
        ]
      }))
    }
  },
  
  methods: {
    async loadUserProfile() {
      try {
        const userProfile = await this.$api.getUserProfile()
        // 处理用户简历数据
      } catch (error) {
        uni.showToast({
          title: '获取简历信息失败',
          icon: 'none'
        })
      }
    },
    
    async fetchMatchedJobs() {
      try {
        this.loading = true
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
          sort_type: this.getActiveSortType(),
          ...(this.filterData || {}) // 确保filterData为空时不会报错
        }
        
        console.log('请求参数:', params)
        
        app.get('apiZhaopin/jobMatch', params, (result) => {
          console.log('匹配结果:', result)
          if (result.status === 1) {
            this.processMatchResult(result)
          } else {
            uni.showToast({
              title: result.msg || '获取数据失败',
              icon: 'none'
            })
          }
          this.loading = false
        })
      } catch (error) {
        console.error('获取匹配职位失败:', error)
        uni.showToast({
          title: '获取匹配职位失败',
          icon: 'none'
        })
        this.loading = false
      }
    },
    
    getActiveSortType() {
      const activeTag = this.filterTags.find(tag => tag.active)
      switch(activeTag.name) {
        case '匹配度优先':
          return 'match'
        case '薪资优先':
          return 'salary'
        case '距离优先':
          return 'distance'
        case '最新发布':
          return 'time'
        default:
          return ''
      }
    },
    
    toggleFilter(index) {
      this.filterTags.forEach((tag, i) => {
        tag.active = i === index
      })
      this.currentPage = 1
      this.fetchMatchedJobs()
    },
    
    getScoreClass(score) {
      if (score >= 90) return 'score-excellent'
      if (score >= 80) return 'score-good'
      if (score >= 60) return 'score-medium'
      return 'score-low'
    },
    
    getProgressClass(score) {
      if (score >= 90) return 'progress-excellent'
      if (score >= 80) return 'progress-good'
      if (score >= 60) return 'progress-medium'
      return 'progress-low'
    },
    
    viewJobDetail(jobId) {
      uni.navigateTo({
        url: `/zhaopin/partdetails?id=${jobId}`
      })
    },
    
    // 添加HTML标签过滤函数
    stripHtml(html) {
      if (!html) return ''
      try {
        return html.replace(/<[^>]+>/g, '').replace(/&[^;]+;/g, '').trim()
      } catch (e) {
        console.warn('处理HTML标签失败:', e)
        return String(html)
      }
    },
    
    // 在processMatchResult方法中修改职位数据处理部分
    processMatchResult(result) {
      const { data } = result
      
      if (!data || !data.statistics || !data.jobs) {
        console.error('匹配结果数据格式错误:', result)
        uni.showToast({
          title: '数据格式错误',
          icon: 'none'
        })
        return
      }
      
      try {
        // 更新统计数据
        this.matchStats = {
          totalJobs: data.statistics.match_count || 0,
          highMatch: (data.statistics.match_degree || 0).toFixed(1),
          salary: data.statistics.average_salary || '0'
        }
        
        // 更新分布数据
        const distribution = data.statistics.distribution || {}
        const total = (distribution['90up'] || 0) + 
                     (distribution['80_89'] || 0) + 
                     (distribution['60_79'] || 0) + 
                     (distribution['below60'] || 0)
                     
        // 如果总数为0，设置一个默认值避免除以0
        const safeTotal = total || 1
        
        // 计算每个区间的实际数量和百分比
        const ranges = [
          { 
            type: 'very-high', 
            label: '90%+', 
            count: distribution['90up'] || 0,
            percentage: Math.round((distribution['90up'] || 0) / safeTotal * 100)
          },
          { 
            type: 'high', 
            label: '80-89%', 
            count: distribution['80_89'] || 0,
            percentage: Math.round((distribution['80_89'] || 0) / safeTotal * 100)
          },
          { 
            type: 'medium', 
            label: '60-79%', 
            count: distribution['60_79'] || 0,
            percentage: Math.round((distribution['60_79'] || 0) / safeTotal * 100)
          },
          { 
            type: 'low', 
            label: '60%以下', 
            count: distribution['below60'] || 0,
            percentage: Math.round((distribution['below60'] || 0) / safeTotal * 100)
          }
        ]
        
        this.matchDistribution = ranges
        
        // 转换职位数据
        this.matchedJobs = (data.jobs || []).map(job => {
          try {
            // 处理工作时间
            const workTime = job.work_time?.type || ''
            
            // 构建标签数组
            const tags = []
            if (workTime) tags.push(workTime)
            if (job.benefits && Array.isArray(job.benefits)) {
              tags.push(...job.benefits.slice(0, 2)) // 最多显示2个福利标签
            }
            
            return {
              id: job.id || '',
              title: job.title || '职位名称未知',
              company: job.company_name || '未知公司',
              matchScore: Math.round(job.match_degree || 0),
              tags: tags,
              salary: job.salary || '薪资面议',
              location: job.work_address || '地点未知',
              workTime: job.work_time ? `${job.work_time.start}-${job.work_time.end}` : '',
              companyLogo: '/static/img/default-company.png'
            }
          } catch (error) {
            console.error('处理职位数据时出错:', error, job)
            // 返回一个默认的职位对象
            return {
              id: job.id || '',
              title: '数据解析错误',
              company: '未知公司',
              matchScore: 0,
              tags: [],
              salary: '薪资面议',
              location: '地点未知',
              workTime: '',
              companyLogo: '/static/img/default-company.png'
            }
          }
        })
        
        console.log('处理后的职位数据:', this.matchedJobs)
      } catch (error) {
        console.error('处理匹配结果时出错:', error)
        uni.showToast({
          title: '数据处理失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.job-match-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: env(safe-area-inset-bottom);
  
  .match-analysis {
    padding: 40rpx 30rpx 60rpx;
    color: #ffffff;
    border-radius: 0 0 30rpx 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 120rpx;
      background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1));
      pointer-events: none;
    }
    
    .analysis-header {
      margin-bottom: 40rpx;
      
      .title {
        font-size: 40rpx;
        font-weight: bold;
        letter-spacing: 2rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }
      
      .subtitle {
        font-size: 28rpx;
        opacity: 0.9;
        margin-top: 12rpx;
        letter-spacing: 1rpx;
      }
    }
    
    .match-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 50rpx;
      
      .stat-item {
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        padding: 20rpx 30rpx;
        border-radius: 16rpx;
        backdrop-filter: blur(10px);
        transition: transform 0.3s ease;
        
        &:active {
          transform: scale(0.95);
        }
        
        .number {
          font-size: 44rpx;
          font-weight: bold;
          margin-bottom: 8rpx;
          display: block;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }
        
        .label {
          font-size: 24rpx;
          opacity: 0.9;
          letter-spacing: 2rpx;
        }
      }
    }
    
    .match-chart {
      border-radius: 20rpx;
      padding: 30rpx;
      backdrop-filter: blur(10px);
      
      .chart-title {
        font-size: 30rpx;
        margin-bottom: 30rpx;
        font-weight: 500;
        letter-spacing: 1rpx;
      }
      
      .chart-bars {
        display: flex;
        justify-content: space-between;
        height: 240rpx;
        align-items: flex-end;
        padding: 0 20rpx;
        
        .bar-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 0 10rpx;
          
          .bar {
            width: 40rpx;
            border-radius: 20rpx 20rpx 6rpx 6rpx;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-height: 4rpx;
            
            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 30%;
              background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
            }
          }
          
          .bar-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 16rpx;
            opacity: 0.9;
            
            .bar-count {
              font-size: 24rpx;
              font-weight: bold;
              margin-bottom: 4rpx;
              color: #ffffff;
              text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
            }
            
            .bar-label {
              font-size: 22rpx;
              margin-bottom: 4rpx;
              color: rgba(255, 255, 255, 0.9);
            }
            
            .bar-percent {
              font-size: 20rpx;
              color: rgba(255, 255, 255, 0.8);
              background: rgba(255, 255, 255, 0.1);
              padding: 2rpx 8rpx;
              border-radius: 10rpx;
            }
          }
        }
      }
    }
  }
  
  .filter-section {
    background-color: #ffffff;
    padding: 20rpx 0;
    margin: 20rpx 0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    
    .filter-scroll {
      white-space: nowrap;
      
      .filter-tags {
        padding: 0 20rpx;
        display: inline-flex;
        
        .tag {
          display: inline-block;
          padding: 12rpx 30rpx;
          font-size: 28rpx;
          border-radius: 32rpx;
          margin-right: 20rpx;
          transition: all 0.3s ease;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
          
          &:active {
            transform: scale(0.95);
          }
          
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
  
  .job-list {
    padding: 20rpx;
    
    .job-card {
      background-color: #ffffff;
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      }
      
      .job-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 24rpx;
        
        .job-info {
          flex: 1;
          margin-right: 20rpx;
          
          .job-title {
            font-size: 34rpx;
            font-weight: bold;
            margin-bottom: 10rpx;
            display: block;
            line-height: 1.4;
          }
          
          .company-name {
            font-size: 26rpx;
            color: #666;
            line-height: 1.4;
          }
        }
        
        .match-score {
          text-align: center;
          font-size: 40rpx;
          font-weight: bold;
          padding: 10rpx 20rpx;
          border-radius: 12rpx;
          background: rgba(0, 0, 0, 0.03);
          
          .match-label {
            display: block;
            font-size: 22rpx;
            font-weight: normal;
            color: #999;
            margin-top: 4rpx;
          }
        }
      }
      
      .job-tags {
        margin-bottom: 24rpx;
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        
        .tag {
          padding: 8rpx 20rpx;
          font-size: 24rpx;
          color: #666;
          border-radius: 8rpx;
          background: rgba(0, 0, 0, 0.03);
          transition: all 0.3s ease;
          
          &:active {
            transform: scale(0.95);
          }
        }
      }
      
      .job-requirements {
        margin-bottom: 24rpx;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        
        .requirements-title {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 12rpx;
        }
        
        .requirements-list {
          .requirement-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8rpx;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .bullet {
              color: #666;
              margin-right: 8rpx;
              flex-shrink: 0;
            }
            
            .content {
              font-size: 26rpx;
              color: #666;
              line-height: 1.5;
              flex: 1;
            }
          }
        }
      }
      
      .match-details {
        border-radius: 16rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
        
        .match-item {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .match-type {
            width: 140rpx;
            font-size: 26rpx;
            color: #666;
            font-weight: 500;
          }
          
          .progress-bar {
            flex: 1;
            height: 16rpx;
            border-radius: 8rpx;
            margin: 0 20rpx;
            overflow: hidden;
            box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
            
            .progress {
              height: 100%;
              border-radius: 8rpx;
              transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
              position: relative;
              
              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 50%;
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
              }
            }
          }
          
          .match-percent {
            font-size: 26rpx;
            color: #666;
            width: 70rpx;
            text-align: right;
            font-weight: 500;
          }
        }
      }
      
      .job-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .salary {
          font-size: 34rpx;
          font-weight: bold;
        }
        
        .location {
          font-size: 26rpx;
          color: #666;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}

// 添加骨架屏动画
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading {
  .job-card {
    background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
}
</style> 