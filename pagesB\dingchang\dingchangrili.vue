<template>
	<view class="container">
		<!-- <view class="tags_box">
			<view class="tag_item fitness">
				<view class="tag_icon">
					<text class="gift-icon" style="color: #d62828; font-size: 18px;">🎁</text>
				</view>
				<view class="tag_text">全民健身补贴</view>
			</view>
			<view class="tag_item" v-for="(tick, index) in ticketList" :key="index">
				<view class="tag_text">{{tick.text}}</view>
			</view>
		</view> -->
		
		<view class="tags_box">		
				
				<view  v-for="(date, index) in dateList" :key="index" @click="changeDay(index)" v-show="date.show">
					
					<view class="tags_date active" v-if="currentIndex === index" :style="'color:'+primary_color+';background:'+secondary_color+';border:1px solid '+primary_color">
						
						 <view style="margin-bottom: 10rpx;">{{date.weekShow}}</view>
						 <view>{{date.dateShow}}</view>
						
					</view>
					
					<view class="tags_date" v-else>
						
						<view style="margin-bottom: 10rpx;">{{date.weekShow}}</view>
						<view>{{date.dateShow}}</view>
						
					</view>
					

				</view>
				

		</view>
		
		<view  class="tags_box"  v-if="typeList.length > 1">

			<view  v-for="(item, index) in typeList" :key="index" @click="changeType(index)">
					
					<view class="tags_type active" :style="'background:'+primary_color" v-if="typeIndex === index" >
						{{item.title}}
					</view>
					
					<view class="tags_type" v-else>
						{{item.title}}
					</view>
			</view>

		</view>
		
		
		<view class="tags_session">
			
			<view class="session_wrap" >
				
				<view class="s_box">
					
					<view class="session_td session_dt"
						style="position: absolute; top: 0; left: 0;transform: translateX(0%);"></view>
						
					<view class="session_th" :style="{position: fixScrollTop?'fixed' : ''}">
						<view class="session_td" v-for="(ss, index) in sessionList" :key="index">{{ss.sessionName}}
						</view>
					</view>
					
					<view >

						<view class="session_td session_dt" >
							
							 <view style="height: 49px;background: #f5f5f5;" v-for="(ss, index) in timesList" :key="index" >{{ss.text}}</view>	
		
						</view>
						
					</view>	

                     <view >
						 
						 <view class="session_tr" v-for="(ss, index) in timesList" :key="index">
						 	
						 	<view class="session_td"  v-for="(sss, ind) in ss.list" :key="ind">
						 		<view class="s_price p_select" :style="'background:'+primary_color"  v-if="sss.is_book==0&&sss.active" @click="handleSelect(index,ind)">
						 			￥{{sss.price}}
						 		</view>
								
								<view class="s_price" :style="'color:'+primary_color"  v-if="sss.is_book==0&&!sss.active" @click="handleSelect(index,ind)">
									￥{{sss.price}}
								</view>
								
						 	</view>

						 </view>	
	
						 
					 </view>

					
				</view>
				
			</view>
		</view>
		
		
		<view class="footer">
			<view class="footer_wrap">
				<view>已选择场次({{ selectCommit.length }})</view>
				<view class="tags_warp" v-if="selectCommit.length">
					<view class="select_it" v-for="(item, index) in selectCommit" :key="index">
						
						<view>
							<view>{{item.date}}</view>
							<view>{{item.sessionName}}</view>
					 	</view> 
							  
						<image src="../../static/img/close.png" style="width: 10px;height: 10px;margin-left: 10px;" @click="delSelect(index)"></image>	
			
					</view>
					 
				</view>
				<view class="tags_warp" v-else>
					<view class="exmap">
						<view class="s_price"  :style="'width: 50rpx;height: 30rpx;margin-right: 10rpx;color:'+primary_color"></view>
						可预定
					</view>
					<view class="exmap">
						<view class="s_price"
							style="width: 50rpx;height: 30rpx;margin-right: 10rpx;background: #e6e6e6;border: 1px solid #a6a4a4;"></view>
						已出售
					</view>
					<view class="exmap">
						<view class="s_price"
							:style="'width: 50rpx;height: 30rpx;margin-right: 10rpx;background:'+primary_color+';color:'+primary_color"></view>
						我的选择
					</view>
				</view>
				<view class="border"></view>
				<view class="footer_btn">
					<view class="btn_1" :style="'color:'+primary_color">
						<view v-if="selectCommit.length">
							￥{{total}}
							<view class="b_tag">全民健身补贴</view>
						</view>
						<view v-else>
							￥ 0
						</view>
					</view>
					<view><button class="btn_2" type="primary" :style="'background:'+primary_color"  @tap="submit">提交订单</button></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();

	const initWeekShow = (dayOfWeek) => {
		switch (dayOfWeek) {
			case 0:
				return '周日';
			case 1:
				return '周一';
			case 2:
				return '周二';
			case 3:
				return '周三';
			case 4:
				return '周四';
			case 5:
				return '周五';
			default:
				return '周六';
		}
	}
	export default {
		data() {
			let newDate = new Date();
			const getDay = num => {
				let targetDate = new Date(newDate);
				targetDate.setDate(targetDate.getDate() + num);
				return targetDate;
			};
			// const getHour = num => newDate.add(num, 'hour').format('H:00')
			return {
				currentIndex: 0,
				opt: {},
				loading: false,
				fixScrollTop: false,
				isload: false,
				menuindex: -1,
				selectCommit: [],
				sessionList: [],

				sessionDataSub: {},
				shou : {},

				dateList: [{
					dateShow: (newDate.getMonth() + 1).toString().padStart(2, '0') + '-' + newDate.getDate().toString().padStart(2, '0'),
					date: newDate.getFullYear() + '-' + (newDate.getMonth() + 1).toString().padStart(2, '0') + '-' + newDate.getDate().toString().padStart(2, '0'),
					weekShow: '今天',
					show : false      
				}, {
					dateShow: (getDay(1).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(1).getDate().toString().padStart(2, '0'),
					date: getDay(1).getFullYear() + '-' + (getDay(1).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(1).getDate().toString().padStart(2, '0'),
					weekShow: '明天',
					show : false
				}, {
					dateShow: (getDay(2).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(2).getDate().toString().padStart(2, '0'),
					date: getDay(2).getFullYear() + '-' + (getDay(2).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(2).getDate().toString().padStart(2, '0'),
					weekShow: initWeekShow(getDay(2).getDay()),
					show : false
				}, {
					dateShow: (getDay(3).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(3).getDate().toString().padStart(2, '0'),
					date: getDay(3).getFullYear() + '-' + (getDay(3).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(3).getDate().toString().padStart(2, '0'),
					weekShow: initWeekShow(getDay(3).getDay()),
					show : false
				}, {
					dateShow: (getDay(4).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(4).getDate().toString().padStart(2, '0'),
					date: getDay(4).getFullYear() + '-' + (getDay(4).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(4).getDate().toString().padStart(2, '0'),
					weekShow: initWeekShow(getDay(4).getDay()),
					show : false
				}, {
					dateShow: (getDay(5).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(5).getDate().toString().padStart(2, '0'),
					date: getDay(5).getFullYear() + '-' + (getDay(5).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(5).getDate().toString().padStart(2, '0'),
					weekShow: initWeekShow(getDay(5).getDay()),
					show : false
				}, {
					dateShow: (getDay(6).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(6).getDate().toString().padStart(2, '0'),         
					date: getDay(6).getFullYear() + '-' + (getDay(6).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(6).getDate().toString().padStart(2, '0'),
					weekShow: initWeekShow(getDay(6).getDay()),
					show : false
				}, {
					dateShow: (getDay(7).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(7).getDate().toString().padStart(2, '0'),
					date: getDay(7).getFullYear() + '-' + (getDay(7).getMonth() + 1).toString().padStart(2, '0') + '-' + getDay(7).getDate().toString().padStart(2, '0'),
					weekShow: initWeekShow(getDay(7).getDay()),
					show : false
				}],
				
				ticketList: [{
					text: '满130减10'
				}, {
					text: '满80减6'
				}, {
					text: '满50减3'
				}, {
					text: '满40减2'
				}, ],
				
				timesList :[],
				
				typeIndex: 0,
				typeList : [],
				
				total : 0,

				datalist: [],
				type: "",
				keyword: '',
				nodata: false,
				
				info:'',
				primary_color : '',
				secondary_color : '',
				open_appointment : 0
			}
		},
		onLoad: function(opt) {

			this.opt = opt;

			console.log('日历页面 onLoad 接收到的参数:', opt);

			uni.setNavigationBarTitle({
				title: opt.name
			})

			// 优先使用传递的tabIndex参数来设置当前选中的tab
			if(opt.tabIndex !== undefined && opt.tabIndex !== null) {
				console.log('使用 tabIndex 参数:', opt.tabIndex);
				this.currentIndex = parseInt(opt.tabIndex);
				console.log('设置 currentIndex 为:', this.currentIndex);
			} else if(opt.date){
				console.log('使用 date 参数匹配:', opt.date);
				// 如果没有tabIndex参数，则使用原来的日期匹配逻辑
				this.dateList.filter((item,index)=>{
					 console.log('比较 dateShow:', item.dateShow, '与 date:', opt.date);
					 if(item.dateShow == opt.date){
						 console.log('匹配成功，设置 currentIndex 为:', index);
						 this.currentIndex = index;
					 }
				})
			}

			// 设置场地类型索引
			if(opt.typeIndex !== undefined && opt.typeIndex !== null) {
				console.log('使用 typeIndex 参数:', opt.typeIndex);
				this.typeIndex = parseInt(opt.typeIndex);
				console.log('设置 typeIndex 为:', this.typeIndex);
			}

			console.log('最终 currentIndex:', this.currentIndex);

			this.getdata();
			// this.loading = false

			this.primary_color = app.getCache('primary_color')
			this.secondary_color = app.getCache('secondary_color')
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		computed: {

		},
		methods: {
			getdata() {
	
				var that = this;
				
				that.nodata = false;
				
				that.total = 0;
				that.selectCommit = [];
				
				app.showLoading('加载中');

				app.get('ApiVenues/getFieldInfo', {id: that.opt.id}, function (res) {
				  

				    app.showLoading(false);
					
					
			         if(res.status == 1){
						 
						 console.log(res);
						
						 let times = [];
						 res.data.time_list.filter(item=>{
							 let obj = {
								 text :item,
								 list : []
							 }
							 times.push(obj);
						 })
						 
						 
						that.timesList = times;
						
						that.open_appointment =  res.data.open_appointment;
						
						that.shou = {
							duration : res.data.duration,
							fees : res.data.fees,
							fees_type : res.data.fees_type,
							is_percentage : res.data.is_percentage
						}
						 
						that.typeList = res.data.field_list; 

						let ls = [];
						 res.data.region.filter((item,k)=>{
						
							 
							 if(that.dateList[that.currentIndex].dateShow == item.charges_time_list.day){
								  
								that.timesList.filter((o,i)=>{
									
									if(i<that.timesList.length-1){  //处理最后结束时间
										let obj;
										item.charges_time_list.list.filter(m=>{
											if(o.text == m.time){      
												m.sessionName = item.title;
												m.sessionId = item.id;
												obj = m;
											}
										})
										
										if(!obj){
										   obj = {
											   time : o.text,
											   is_book: 1,
											   sessionName : item.title
											   
										   }
										}
										
										that.timesList[i].list.push(obj);	
									}
									
		
								})
							 }
							 
							 
							 that.timesList.filter((o,k)=>{
								 
								 o.list.filter((m,n)=>{
									 
									 m.x =k;
									 m.y =n;
									 
								 })
								 
							 })
							 
 	 
							 let obj = {
								 sessionId: item.id,
								 sessionName: item.title,
							 }
							 
							 let is = false
							 ls.filter(m=>{
								 if(m.sessionId == item.id){
									 is = true;
								 } 
							 })
							 
							 if(!is){
								  ls.push(obj);
							 }
							
							 that.dateList.filter((o,k)=>{
								 if(item.charges_time_list.day == o.dateShow){
									  o.show = true;
								 }
							 })
						
						 })
						 
	
						that.sessionList = ls;  
						  
					 }
					 
				});  
			},
			
			
			dateToStr(date){
				const year = date.getFullYear();
				const month = date.getMonth() + 1;
				const day = date.getDate();
		
		        if(month<10){
					month = '0'+ month
				} 
				
				if(day<10){
					day = '0'+ day
				}
				 
				return year + '-' + month + '-' + day
			},
			
			
			submit(){
				
	
				
				var that = this;
				if(that.selectCommit.length == 0){
					uni.showToast({
						icon: 'none',
						title: '未选择场次'
					})
				   return;
				}
					
				
				
				let list = [];
				that.selectCommit.filter(item=>{
					list.push(item.sessionId);
					
				})
				
				let book = []
				
				let unique = Array.from(new Set(list));
				
				unique.filter(item=>{
					
					let charges = [];
					
					that.selectCommit.filter(m=>{  
				          
						  if(item == m.sessionId){
							  charges.push(m.timeId);
						  }
					})	 
					
					
					let obj = {
						charges : charges.toString(),
						region : item,
						field : that.opt.id,
						day : that.dateList[that.currentIndex].date
					}
					
					book.push(obj);
				})

				uni.navigateTo({
					url:'/pagesB/dingchang/dingchangPayment?book='  + 
						JSON.stringify(book)+'&select='+ JSON.stringify(that.selectCommit) +'&title='+ that.opt.name + '&yue='+that.open_appointment+'&shou='+JSON.stringify(that.shou)
				})
				
			},
			
			
			delSelect(index){
				
				let item = this.selectCommit[index];
					
				//console.log(item);
				
				this.total-= item.price;
				
				this.selectCommit.splice(index,1);
				
				this.timesList[item.x].list[item.y].active =  false;
				
			},
			
			changeDay(index){
			   this.currentIndex = index;
			   this.getdata();	   
			},
			
			changeType(index){
			   this.typeIndex = index;
			   this.opt.id =  this.typeList[index].id;
			   this.getdata();	   
			},
			
	
			handleSelect(index, ind) {
				let that = this
	
			 	let item =that.timesList[index].list[ind];
				
				//console.log(item);
			
				if(item.is_book == 0){
					
					that.timesList[index].list[ind].active = !that.timesList[index].list[ind].active;
					
	                if(that.timesList[index].list[ind].active ){
						that.total+= parseInt(item.price);
						
						that.selectCommit.push({
							price : parseInt(item.price),
							date : item.time_period,
							sessionName : item.sessionName,
							sessionId : item.sessionId,
							timeId : item.id,  
							x : item.x,
							y : item.y
						})
					}else{
						
						that.total-= parseInt(item.price);
						
			            
						that.selectCommit.filter((item,k)=>{
							
							 if(item.x == index && item.y == ind){
								 that.selectCommit.splice(k,1);
							 }
							
						})
						
					}

				}else{
					uni.showToast({
						icon:'none',
						title:'该场次已出售'
					})
					
				}
	
			}
		}
	};
</script>
<style>
	page {
		background: white;
	}
</style>
<style scoped lang="scss">
	.date_box {
		.date_warp {}
	}

	.footer {
		position: fixed;
		bottom: 0;
		padding: 20rpx 0;
		height: 308rpx;
		overflow: hidden;
		width: 100%;
		background: white;

		.footer_wrap {
			padding: 0 24rpx;
			color: #999999;
			font-size: 26rpx;
		}

		.tags_warp {
			display: flex;
			align-items: center;
			margin: 20rpx 0;
			min-height: 80rpx;
			white-space: nowrap;
			overflow: scroll;

			.exmap {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 22rpx;
				margin-right: 40rpx;
			}

			.select_it {
				border-radius: 10rpx;
				background: #e6e6e6;
				padding: 10rpx 20rpx;
				color: #000;
				margin-right: 10rpx;
				display: flex;
				align-items: center;
			}
		}

		.footer_btn {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 20rpx;

			.btn_1 {
				color: rgb(228, 69, 66);
				font-size: 56rpx;
				position: relative;

				.b_tag {
					position: absolute;
					top: -10rpx;
					right: 0;
					color: white;
					background: #d62828;
					border-radius: 10rpx 10rpx 10rpx 0;
					font-size: 20rpx;
					padding: 4rpx 10rpx;
					transform: translateX(100%);
					opacity: 0.8;
					white-space: nowrap;
				}
			}

			.btn_2 {
				border-radius: 50rpx;
				background-color: rgb(91, 115, 242);
				width: 200rpx;
			}
		}

		.border {
			height: 1rpx;
			background: #e6e6e6;
			transform: scaleY(0.5);
		}
	}

	.s_price {
		color: blue;
		border: 1px solid;
		border-radius: 6rpx;
		background: white;
		height: 100%;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;

		&.p_select {
			color: white;
			background: #5c5ccb;
		}
	}

	.tags_session {
		flex: 1;
		background: rgb(245, 245, 245);
		// height: 100%;
		height: calc(100% - 308rpx);

		.session_wrap {
			width: 100%;
			position: relative;
			box-sizing: border-box;
			overflow-y: scroll;
			max-height: calc(100% - 308rpx);

			.s_box {
				padding-left: 110rpx;
				width: 100%;
				overflow: scroll;
				background: #f5f5f7;;
			}

			.session_th {
				// position: absolute;
				background: whitesmoke !important;
			}

			.session_th,
			.session_tr {
				display: flex;
				align-items: center;
			}

			.session_tr {

				.session_td {
					background: rgb(230, 230, 230);
				}
			}

			.session_td {
				text-align: center;  
				min-width: 150rpx;
				height: 45px;
				line-height: 45px;   
				white-space: nowrap;
				// border-radius: 6rpx;
				margin: 4rpx;
				border: 1px solid transparent;
			}

			.session_dt {
				background: whitesmoke !important;
				position: absolute;
				top: auto;
				left: -5rpx;
				padding: 4rpx;
				margin: 0;
				min-width: 110rpx;
				color: #000;
				text-align: center;
				transform: translateY(-50rpx);
				height: 100rpx;
			}
		}
	}

	.tags_box {
		width: 100%;
		overflow-x: auto;
		white-space: nowrap;
		padding: 10rpx 16rpx;
		display: flex;
		align-items: center;

		.tags_date {
			text-align: center;
			background: #f5f5f7;
			margin-right: 10rpx;
			padding: 30rpx;
			border-radius: 8rpx;
			border: 1px solid transparent;


			&.active {
				color: rgb(91, 114, 242);
				background: rgb(240, 242, 255);
				border: 1px solid;
			}
		}
		
		
		.tags_type {
			margin: 30rpx 30rpx 30rpx 0;
			padding: 10rpx 20rpx;
			background: #fff;
			border-radius: 20px;
			text-align: center;
		
			&.active {
				color: #fff;
				background: rgb(91, 114, 242);
				border: 1px solid;
			}
		}
		

		.tag_text {
			padding: 4rpx 10rpx;
		}

		.tag_icon {
			background: #efd599;
			height: 100%;
			width: 46rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 9rpx 0 18rpx 9rpx;
		}

		.tag_item {
			display: flex;
			align-items: center;
			border: 1px solid;
			color: #d62828;
			margin: 6rpx;
			border-radius: 8rpx;
			font-size: 24rpx;

			&.fitness {
				background: #d62828;
				color: white;
				border: none;
			}
		}
	}

	.tags_box::-webkit-scrollbar {
		display: none;
	}

	.tags_box::-webkit-scrollbar-thumb {}

	.topsearch {
		width: 94%;
		margin: 16rpx 3%;
	}

	.topsearch .f1 {
		height: 60rpx;
		border-radius: 30rpx;
		border: 0;
		background-color: #fff;
		flex: 1
	}  

	.topsearch .f1 .img {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10px
	}

	.topsearch .f1 input {
		height: 100%;
		flex: 1;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
	}

	.content {
		width: 94%;
		margin: 20rpx 3%;
		background: #fff;
		border-radius: 5px;
		padding: 20rpx 40rpx;
	}

	.content .f1 {
		height: 96rpx;
		line-height: 96rpx;
		display: flex;
		align-items: center
	}

	.content .f1 .t1 {
		color: #2B2B2B;
		font-weight: bold;
		font-size: 30rpx
	}

	.content .f1 .t2 {
		color: #999999;
		font-size: 28rpx;
		margin-left: 10rpx
	}

	.content .f1 .t3 {
		width: 28rpx;
		height: 28rpx
	}

	.content .f2 {
		color: #2b2b2b;
		font-size: 26rpx;
		line-height: 42rpx;
		padding-bottom: 20rpx;
		border-bottom: 1px solid #F2F2F2
	}

	.content .f3 {
		height: 96rpx;
		display: flex;
		align-items: center
	}

	.content .radio {
		flex-shrink: 0;
		width: 36rpx;
		height: 36rpx;
		background: #FFFFFF;
		border: 3rpx solid #BFBFBF;
		border-radius: 50%;
	}

	.content .radio .radio-img {
		width: 100%;
		height: 100%
	}

	.content .mrtxt {
		color: #2B2B2B;
		font-size: 26rpx;
		margin-left: 10rpx
	}

	.content .del {
		font-size: 24rpx
	}

	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
	}

	.container .btn-add {
		width: 90%;
		max-width: 700px;
		margin: 0 auto;
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: bold;
		border-radius: 40rpx;
		position: fixed;
		left: 0px;
		right: 0;
		bottom: 0;
		margin-bottom: 20rpx;
	}

	.container .btn-add2 {
		width: 43%;
		max-width: 700px;
		margin: 0 auto;
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: bold;
		border-radius: 40rpx;
		position: fixed;
		left: 5%;
		bottom: 0;
		margin-bottom: 20rpx;
	}

	.container .btn-add3 {
		width: 43%;
		max-width: 700px;
		margin: 0 auto;
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: bold;
		border-radius: 40rpx;
		position: fixed;
		right: 5%;
		bottom: 0;
		margin-bottom: 20rpx;
	}
</style>