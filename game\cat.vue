<template>
	<view class="container">
		<view id="catIndex">
			<view class="title">
				<view class="title-text" @tap="onBack">返回</view>
				<view class="title-text" @tap="goToOrder">订单</view>
			</view>
			<view class="box">
				<view class="ads"><xzw-notice speed="fast" :showIcon="false" :showMore="false" :list="adList" /></view>
				<view class="game_box">
					<view class="goods">
						<image class="goods-img" :src="productInfo.pic" />
					</view>
					<view class="goods-content">
						<view>{{ productInfo.name }}</view>
						<view class="goods-price">¥ {{ productInfo.sell_price }}</view>
					</view>
					<view class="chance">中奖概率：{{ getProbability() }}%</view>
				</view>
				<view class="fold">
					<view
						@tap="onChange(item)"
						class="icon-bg"
						:class="{ 'icon-bg_active': item === current }"
						v-for="item in list"
						:key="item"
					>
						x{{ item }}
					</view>
				</view>
			</view>
			<view class="btn_bot">
				<view class="btn_bot_l">
					<image class="img-icon" src="@/game/static/game/cat/reduce.png" @tap="onReduce" />
					<view class="num_text">{{ time }}次</view>
					<image class="img-icon" src="@/game/static/game/cat/add.png" @tap="onAdd" />
				</view>

				<view class="btn_bot_r" @tap="onSubmit">
					<view class="pay_text">支付{{ price }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
var app = getApp();
import xzwNotice from './components/xzw-notice/xzw-notice.vue'
export default {
	data() {
		return {
			adList: [{ id: 1, title: '恭喜用户 大雨 中奖小圆饼120g' }],
			current: 1,
			// 商品信息
			productInfo: {
				name: '',
				pic: '',
				sell_price: 0
			},
			list: [1, 3, 5],
			price: 0,
			time: 1, // 次数
			multiple: 1 // 倍数
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.price = this.opt.amount;
		this.getdata();

		if (this.opt.oid) {
			this.loading = true;
			this.getResult();
		}
	},
	methods: {
		onBack() {
			uni.redirectTo({ url: `/shopPackage/shop/product?id=${this.opt.id}` });
		},
		goToOrder() {
			app.goto('/game/order');
		},
		/**
		 * 商品信息
		 */
		getdata: function () {
			let that = this;
			let id = this.opt.id || 0;
			that.loading = true;
			app.get(
				'ApiShop/product',
				{
					id: id
				},
				function (res) {
					that.loading = false;
					if (res.status == 0) {
						app.alert(res.msg);
						return;
					}
					that.productInfo = res.product;
				}
			);
		},
		getProbability() {
			if (this.opt.amount && this.current && this.productInfo.sell_price) {
				let num = (this.opt.amount * this.current) / this.productInfo.sell_price;
				return ((num && num * 100) || 0).toFixed(2);
			} else {
				return (0).toFixed(2);
			}
		},
		onReduce() {
			if (this.time > 1) {
				this.time--;
				this.price = (this.time * this.opt.amount).toFixed(2);
			}
		},
		onAdd() {
			this.time++;
			this.price = (this.time * this.opt.amount).toFixed(2);
		},
		/**
		 * 切换
		 * @param {Object} index
		 */
		onChange(num) {
			this.current = num;
			this.price = (this.time * this.opt.amount * this.current).toFixed(2);
		},
		onReduce() {
			if (this.time > 1) {
				this.time--;
				this.price = (this.time * this.opt.amount * this.current).toFixed(2);
			}
		},
		onAdd() {
			this.time++;
			this.price = (this.time * this.opt.amount * this.current).toFixed(2);
		},
		onSubmit() {
			let that = this;
			that.loading = true;
			const params = {
				sid: that.opt.gameId,
				pid: that.opt.id,
				amount: that.opt.amount * that.current,
				frequency: that.time,
				tourl: app._fullurl()
			};
			app.post('ApiSweepstakes/postRaffle', params, function (res) {
				switch (res.status) {
					case 1:
						if (res.is_lottery === 1) {
							app.alert('恭喜你，中奖了');
						} else {
							app.alert('未中奖，请再接再厉');
						}
						break;
					case 2:
						app.confirm(res.msg || '出错了', function () {
							app.goto(res.pathUrl);
						});
						break;
					case 3:
						app.goto(res.pathUrl);
						break;
					default:
						app.alert(res.msg || '出错了');
						break;
				}
			});
		},
		getResult() {
			let that = this;
			const params = {
				oid: this.opt.oid
			};
			app.post('ApiSweepstakes/getResult', params, function (res) {
				if (res.status === 1) {
					if (res.is_lottery === 1) {
						app.alert('恭喜你，中奖了', function () {
							app.goto(app._delOrderId());
						});
					} else {
						app.alert('未中奖，请再接再厉', function () {
							app.goto(app._delOrderId());
						});
					}
				} else {
					app.alert(res.msg, function () {
						app.goto(app._delOrderId());
					});
				}
			});
		}
	}
};
</script>

<style scoped>
.container {
	height: 100vh;
	overflow: hidden;
}

#catIndex {
	height: 100%;
	background: url(../game/static/game/cat/cat_bg.png) no-repeat;
	background-size: 100% 100%;
}

#catIndex .title {
	display: flex;
	justify-content: space-between;
	padding: 10px 20px 0;
	box-sizing: border-box;
}

#catIndex .title .title-text {
	width: 45px;
	height: 23px;
	border-radius: 23px;
	background: #ed1f21;
	box-shadow: 0 2px 2px 0 rgba(187, 32, 6, 0.7);
	font-size: 12px;
	color: #fff;
	text-align: center;
	line-height: 23px;
}

#catIndex .ads {
	text-align: center;
	color: #f2f2f2;
	font-size: 14px;
	line-height: 39px;
}

#catIndex .box {
	background: url(../game/static/game/cat/box_bg.png) no-repeat;
	background-size: 100% 100%;
	margin-top: 33px;
	height: 458px;
}

#catIndex .game_box {
	width: 323px;
	height: 300px;
	margin: 10px auto 0;
	background: url(../game/static/game/cat/goods_bg.png) no-repeat;
	background-size: 100% 100%;
	text-align: center;
	padding-top: 34px;
	box-sizing: border-box;
}

#catIndex .game_box .goods {
	width: 100px;
	height: 100px;
	margin: 0 auto;
	text-align: center;
}

#catIndex .goods-img {
	width: 100%;
	height: 100%;
}

#catIndex .goods-content {
	font-size: 15px;
	color: #333;
	margin-top: 65px;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	padding: 0 53px;
}

#catIndex .goods-price {
	color: #f71f16;
}

.chance {
	margin-top: 18px;
	color: #fff;
	font-size: 14px;
}

#catIndex .fold {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	padding: 35px 45px 0;
	box-sizing: border-box;
}

.icon-bg {
	width: 52px;
	height: 49px;
	text-align: center;
	line-height: 49px;
	background: url(../game/static/game/cat/icon.png) no-repeat;
	background-size: 100% 100%;
	color: #fff;
	font-size: 16px;
	font-weight: 700;
}

.icon-bg_active {
	width: 52px;
	height: 49px;
	text-align: center;
	line-height: 49px;
	background: url(../game/static/game/cat/icon_active.png) no-repeat;
	background-size: 100% 100%;
	color: #fff;
	font-size: 16px;
	font-weight: 700;
}

#catIndex .btn_bot {
	display: flex;
	justify-content: space-between;
	padding: 30px 35px 0;
}

.btn_bot .btn_bot_l {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 12px;
	box-sizing: border-box;
	background: url(../game/static/game/cat/pay_num.png) no-repeat;
	background-size: 100% 100%;
	width: 134px;
	height: 49px;
}

.btn_bot .btn_bot_r {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	background: url(../game/static/game/cat/pay_bg.png) no-repeat;
	background-size: 100% 100%;
	width: 134px;
	height: 49px;
}

.img-icon {
	display: inline-block;
	width: 22px;
	height: 22px;
	position: relative;
	margin-top: -3px;
}

.num_text {
	position: relative;
	margin-top: -3px;
	color: #fff;
	font-size: 15px;
	line-height: 100%;
	font-weight: 700;
}

.pay_text {
	position: relative;
	top: -3px;
	color: #fff;
	font-weight: 700;
	font-size: 13px;
	padding-left: 12px;
}
</style>
