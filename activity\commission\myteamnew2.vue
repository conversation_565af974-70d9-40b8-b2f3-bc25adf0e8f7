<template>
<view class="container">
	<block v-if="isload">
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<image class="search-icon" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="搜索" placeholder-style="font-size:30rpx;color:#999" @confirm="searchConfirm" @input="searchChange"></input>
			</view>
		</view>
		
		<!-- 绿色统计区域 -->
		<view class="stats-section" :style="{background:'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)'}">
			<!-- 顶部筛选按钮 -->
			<view class="filter-buttons">
				<view class="filter-btn" :class="{ active: activeFilter === 'all' }" @tap="resetAllFilters">全部</view>
				<view class="filter-btn" :class="{ active: activeFilter === 'date' }" @tap="toggleDateFilter">
					<text>日期筛选</text>
					<text class="arrow" :class="{ up: showDateFilter }">▼</text>
				</view>
			</view>
			
			<!-- 统计数据 -->
			<view class="stats-data">
				<view class="stat-item">
					<text class="stat-value">¥{{statistics.total_amount || '0.00'}}</text>
					<text class="stat-label">总消费金额</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{statistics.total_count || 0}}</text>
					<text class="stat-label">当前客户数</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{statistics.team_total_count || 0}}</text>
					<text class="stat-label">团队总人数</text>
				</view>
			</view>
		</view>

		<!-- 原有的标签页导航 -->
		<dd-tab :itemdata="[t('一级'),t('二级')]" :itemst="['1','2','3']" :st="st" :isfixed="false" @changetab="changetab" v-if="userlevel && userlevel.can_agent==2"></dd-tab>
		<dd-tab :itemdata="[t('一级'),t('二级'),t('三级')]" :itemst="['1','2','3']" :st="st" :isfixed="false" @changetab="changetab" v-if="userlevel && userlevel.can_agent==3"></dd-tab>
		
		<!-- 新的时间筛选区域 -->
		<view class="date-filter-section" v-if="showDateFilter">
			<view class="filter-row">
				<text class="filter-label">开始：</text>
				<picker mode="date" @change="onStartDateChange" :value="start_time">
					<view class="date-picker">{{start_time || '请选择'}}</view>
				</picker>
			</view>
			<view class="filter-row">
				<text class="filter-label">结束：</text>
				<picker mode="date" @change="onEndDateChange" :value="end_time">
					<view class="date-picker">{{end_time || '请选择'}}</view>
				</picker>
			</view>
			<view class="button-row">
				<view class="filter-button" @tap="applyTimeFilter">确定</view>
				<view class="filter-button reset" @tap="resetTimeFilter">重置</view>
			</view>
		</view>
		
		
		
		<!-- 用户列表 -->
		<view class="user-list" v-if="datalist && datalist.length>0">
			<block v-for="(item, index) in datalist" :key="index">
				<view class="user-item">
					<!-- 用户头像 -->
					<view class="user-avatar">
						<image :src="item.headimg" class="avatar-img"></image>
					</view>
					
					<!-- 用户信息 -->
					<view class="user-info">
												<view class="user-name-line">
							<text class="user-name">{{item.nickname || '阿白'}}</text>
							<text class="user-level">{{item.levelname || '会员'}}</text>
						</view>
						<view class="user-time">{{item.createtime || '2025-06-27 22:23:36'}}</view>
						<view class="user-stats">
							<text class="stat-text">订单：{{item.ordercount || '0'}}</text>
						</view>
						<view class="user-stats">
							<text class="stat-text">消费金额：{{item.consume_amount || '0.00'}}CNY</text>
						</view>
						<view class="user-stats">
							<text class="stat-text">销售金额：{{item.tuanduiyeji || '0.00'}}CNY</text>
						</view>
					</view>
					
					<!-- 右侧直推数 -->
					<view class="user-right">
						<view class="direct-count">
							<text class="count-label">直推数：</text>
							<text class="count-value">{{item.direct_count || item.downcount || '0'}}</text>
						</view>
					</view>
				</view>
				
				
			</block>
		</view>
		
		<!-- 原有的内容区域保持不变 -->
		<view class="content" v-if="datalist && datalist.length>0" style="display:none">
			<view class="label">
				<text class="t1">成员信息 </text>
				<text class="t2" v-if="team_settings && team_settings.show_other_commission == 1">来自TA的{{t('佣金')}}</text>
			</view>
			<block v-for="(item, index) in datalist" :key="index">
				<view class="item">
					<view class="f1">
						<image :src="item.headimg"></image>
						<view class="t2">
							<text class="x1">{{item.nickname}}</text>
							<text class="x2">{{item.createtime}}</text>
							<text class="x2">等级：{{item.levelname}}</text>
							<text class="x2" v-if="item.tel">手机号：{{item.tel}}</text>
							<text class="x2" v-if="item.has_original_recommender == 1 && item.original_recommender">原推荐人：{{item.original_recommender.nickname}}</text>
							<text class="x2" v-if="team_settings && team_settings.show_member_count == 1">下级人数：{{item.downcount}}</text>
							<text class="x2" v-if="team_settings && team_settings.show_direct_count == 1">直推人数：{{item.direct_count || item.downcount}}</text>
							<text class="x2" v-if="team_settings && team_settings.show_team_performance == 1" :style="{color:t('color1')}">团队业绩：{{item.tuanduiyeji}}</text>
							<text class="x2" v-if="team_settings && team_settings.show_consume_amount == 1" :style="{color:t('color1')}">消费金额：¥{{item.consume_amount || '0.00'}}</text>
						</view>
					</view>
					<!-- 操作区域 - 独立于佣金显示 -->
					<view class="operation-area" v-if="team_settings && team_settings.show_relation_chart == 1 || userlevel && (userlevel.team_givemoney==1 || userlevel.team_givescore==1 || userlevel.team_levelup==1 || userlevel.team_daikexiadan==1)">
						<view v-if="team_settings && team_settings.show_relation_chart == 1" class="op-btn" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)',color:'#fff'}" @tap="goToRelationChart" :data-id="item.id">关系图</view>
					</view>
					<!-- 佣金显示区域 -->
					<view class="f2" v-if="team_settings && team_settings.show_other_commission == 1">
						<text class="t1" :style="{color:t('color1')}">+{{item.commission}}</text>
					<view class="t3">
					    <view v-if="userlevel && userlevel.team_givemoney==1" class="x1" :style="{borderColor:t('color1'),color:t('color1')}" @tap="givemoneyshow" :data-id="item.id">转{{t('余额')}}</view>
					    <view v-if="userlevel && userlevel.team_givescore==1" class="x1" :style="{borderColor:t('color1'),color:t('color1')}" @tap="givescoreshow" :data-id="item.id" >转{{t('积分')}}</view>
					    <view v-if="userlevel && userlevel.team_levelup==1" class="x1" :style="{borderColor:t('color1'),color:t('color1')}" @tap="showDialog" :data-id="item.id" :data-levelid="item.levelid" :data-levelsort="item.levelsort">升级</view>
					    <!-- 新增"帮他下单"按钮 -->
					    <view v-if="userlevel && userlevel.team_daikexiadan==1"  class="x1" :style="{borderColor:t('color1'),color:t('color1')}" @tap="goDaigoupick" :data-id="item.id">帮他下单</view>
					</view>
					</view>
				</view>
			</block>
		</view>
		
		<!-- 弹窗保持不变 -->
		<uni-popup id="dialogmoneyInput" ref="dialogmoneyInput" type="dialog">
			<uni-popup-dialog mode="input" title="转账金额" value="" placeholder="请输入转账金额" @confirm="givemoney"></uni-popup-dialog>
		</uni-popup>
		<uni-popup id="dialogscoreInput" ref="dialogscoreInput" type="dialog">
			<uni-popup-dialog mode="input" title="转账数量" value="" placeholder="请输入转账数量" @confirm="givescore"></uni-popup-dialog>
		</uni-popup>
		
		<view v-if="dialogShow" class="popup__container">
			<view class="popup__overlay" @tap.stop="showDialog"></view>
			<view class="popup__modal">
				<view class="popup__title">
					<text class="popup__title-text">升级</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="showDialog"/>
				</view>
				<view class="popup__content">
					<view class="sheet-item" v-for="(item, index) in levelList" :key="index">
						<text class="item-text flex-item">{{item.name}}</text>
						<view class="flex1"></view><view @tap="changeLevel" :data-id="item.id" :data-name="item.name" v-if="item.id != tempLevelid && item.sort > tempLevelsort" :style="{'color':t('color1')}">选择</view><view v-else style="color: #ccc;">选择</view>
					</view>
				</view>
			</view>
		</view>
	</block>
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      st: 1,
      datalist: [],
      pagenum: 1,
			userlevel:{},
			userinfo:{},
			textset:{},
			levelList:{},
			team_settings: {},
			keyword:'',
			tomid:'',
			tomoney:0,
			toscore:0,
      nodata: false,
      nomore: false,
			dialogShow: false,
			tempMid: '',
			tempLevelid: '',
			tempLevelsort: '',
			start_time: '', // 开始时间
			end_time: '',   // 结束时间
			statistics: {},
			showDateFilter: false,
			activeFilter: 'all', // 统计数据
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1
      this.getdata(true);
    }
  },
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var st = that.st;
      var pagenum = that.pagenum;
      var keyword = that.keyword;
      var start_time = that.start_time;
      var end_time = that.end_time;
			that.loading = true;
			that.nodata = false;
      that.nomore = false;
                  app.post('ApiAgent/team', {st: st,pagenum: pagenum,keyword:keyword,start_time:start_time,end_time:end_time}, function (res) {
				that.loading = false;
        var data = res.datalist;
        if (pagenum == 1) {
					that.userinfo = res.userinfo;
					that.userlevel = res.userlevel;
					that.textset = app.globalData.textset;
          that.datalist = data;
					that.levelList = res.levelList;
					that.team_settings = res.team_settings || {};
					that.statistics = res.statistics || {}; // 获取统计数据
          if (data.length == 0) {
            that.nodata = true;
          }
					uni.setNavigationBarTitle({
						title: that.t('我的团队')
					});
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
	  goDaigoupick: function(e) {
	        var mid = e.currentTarget.dataset.id;
	        uni.navigateTo({
	            url: '/pagesExa/daike/daigoupick?mid=' + mid
	        });
	    },
    changetab: function (st) {
			this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
		givemoneyshow:function(e){
			var that = this;
			var id = e.currentTarget.dataset.id;
			that.tomid = id;
			that.$refs.dialogmoneyInput.open();
		},
		givescoreshow:function(e){
			var that = this;
			var id = e.currentTarget.dataset.id;
			that.tomid = id;
			that.$refs.dialogscoreInput.open();
		},
		givemoney:function(done, money){
			var that = this;
			var id = that.tomid;
			app.showLoading('提交中');
			app.post('ApiAgent/givemoney', {id:id,money:money}, function (res) {
				app.showLoading(false);
				if (res.status == 0) {
          app.error(res.msg);
        } else {
          app.success(res.msg);
					that.getdata();
					that.$refs.dialogmoneyInput.close();
				}
			})
		},
		givescore:function(done, score){
			var that = this;
			var id = that.tomid;
			app.showLoading('提交中');
			app.post('ApiAgent/givescore', {id:id,score:score}, function (res) {
				app.showLoading(false);
				if (res.status == 0) {
          app.error(res.msg);
        } else {
          app.success(res.msg);
					that.getdata();
					that.$refs.dialogscoreInput.close();
				}
			})
		},
    searchChange: function (e) {
      this.keyword = e.detail.value;
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
      that.getdata();
    },
		showDialog:function(e){
			let that = this;
			that.tempMid = e.currentTarget.dataset.id;
			that.tempLevelid = e.currentTarget.dataset.levelid;
			that.tempLevelsort = e.currentTarget.dataset.levelsort;
			this.dialogShow = !this.dialogShow
		},
		changeLevel: function (e) {
			var that = this;
			var mid = that.tempMid;
			var levelId = e.currentTarget.dataset.id;
			var levelName = e.currentTarget.dataset.name;
			app.confirm('确定要升级为'+levelName+'吗?', function () {
				app.showLoading('提交中');
			  app.post('ApiAgent/levelUp', {mid: mid,levelId:levelId}, function (res) {
					app.showLoading(false);
					if (res.status == 0) {
					  app.error(res.msg);
					} else {
						app.success(res.msg);
						that.dialogShow = false;
						that.getdata();
					}
			  });
			});
    },
		goToRelationChart: function(e) {
			var mid = e.currentTarget.dataset.id;
			uni.navigateTo({
				url: '/activity/commission/teamchart?mid=' + mid
			});
		},
    // 时间筛选相关方法
    onStartDateChange: function(e) {
    	this.start_time = e.detail.value;
    },
    onEndDateChange: function(e) {
    	this.end_time = e.detail.value;
    },
    applyTimeFilter: function() {
    	if (this.start_time && this.end_time) {
    		if (this.start_time > this.end_time) {
    			uni.showToast({
    				title: '开始时间不能大于结束时间',
    				icon: 'none'
    			});
    			return;
    		}
    	}
    	this.getdata();
    },
    resetTimeFilter: function() {
    	this.start_time = '';
    	this.end_time = '';
    	this.getdata();
    },

		toggleDateFilter: function() {
			this.showDateFilter = !this.showDateFilter;
			if (this.showDateFilter) {
				this.activeFilter = 'date';
			} else {
				this.activeFilter = 'all';
			}
		},

		resetAllFilters: function() {
			this.start_time = '';
			this.end_time = '';
			this.keyword = '';
			this.activeFilter = 'all';
			this.showDateFilter = false;
			this.getdata();
		}
  }
};
</script>

<style>
/* 全局样式 */
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

/* 搜索框样式 */
.search-container {
	padding: 20rpx;
	background: #f5f5f5;
}

.search-box {
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	height: 80rpx;
	background: #fff;
	border-radius: 40rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.search-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 20rpx;
	opacity: 0.6;
}

.search-box input {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}

/* 绿色统计区域样式 */
.stats-section {
	margin: 0 20rpx;
	padding: 30rpx 20rpx 40rpx;
	border-radius: 20rpx;
	position: relative;
	overflow: hidden;
}

.stats-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
	z-index: -1;
}

/* 筛选按钮样式 */
.filter-buttons {
	display: flex;
	justify-content: space-between;
	margin-bottom: 40rpx;
}

.filter-btn {
	flex: 1;
	height: 60rpx;
	line-height: 60rpx;
	text-align: center;
	border-radius: 30rpx;
	margin: 0 10rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	color: #fff;
}

.filter-btn.active {
	background: rgba(255,255,255,0.3) !important;
}

.arrow.up {
	transform: rotate(180deg);
}

.filter-btn .arrow {
	margin-left: 10rpx;
	font-size: 24rpx;
}

/* 统计数据样式 */
.stats-data {
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}

.stat-item {
	text-align: center;
	color: #fff;
	flex: 1;
	min-width: 30%;
	margin: 0 5rpx;
}

.stat-value {
	display: block;
	font-size: 60rpx;
	font-weight: bold;
	line-height: 1;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 24rpx;
	opacity: 0.9;
}

/* 用户列表样式 */
.user-list {
	background: #fff;
	margin: 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 20rpx rgba(0,0,0,0.1);
}

.user-item {
	display: flex;
	align-items: flex-start;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;
}

.user-item:last-child {
	border-bottom: none;
}

/* 用户头像样式 */
.user-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	overflow: hidden;
	margin-right: 20rpx;
	background: linear-gradient(135deg, #ff9800 0%, #ff6b35 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-img {
	width: 100%;
	height: 100%;
	border-radius: 60rpx;
}

/* 用户信息样式 */
.user-info {
	flex: 1;
	padding-right: 20rpx;
}

.user-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.user-name-line {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.user-level {
	font-size: 22rpx;
	color: #fff;
	background: #4CAF50;
	padding: 2rpx 10rpx;
	border-radius: 4rpx;
	margin-left: 12rpx;
}

.user-time {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 15rpx;
}

.user-stats {
	margin-bottom: 8rpx;
}

.stat-text {
	font-size: 26rpx;
	color: #666;
}

/* 右侧直推数样式 */
.user-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	justify-content: center;
}

.direct-count {
	display: flex;
	align-items: center;
}

.count-label {
	font-size: 24rpx;
	color: #666;
	margin-right: 10rpx;
}

.count-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

/* 会员标签样式 */
.member-tag {
	position: absolute;
	bottom: 0;
	left: 30rpx;
	right: 30rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transform: translateY(30rpx);
}

.member-text {
	color: #fff;
	font-size: 28rpx;
	font-weight: bold;
}

/* 原有样式保持不变 */
.topsearch{width:94%;margin:16rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.content{width:94%;margin:0 3%;border-radius:16rpx;background: #fff;margin-top: 20rpx;}
.content .label{display:flex;width: 100%;padding: 16rpx;color: #333;}
.content .label .t1{flex:1}
.content .label .t2{ width:300rpx;text-align:right}

.content .item{width: 100%;padding: 32rpx;border-top: 1px #eaeaea solid;min-height: 112rpx;display:flex;align-items:center;}
.content .item image{width: 90rpx;height: 90rpx;border-radius:4px}
.content .item .f1{display:flex;flex:1;align-items:center;}
.content .item .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}
.content .item .f1 .t2 .x1{color: #333;font-size:26rpx;}
.content .item .f1 .t2 .x2{color: #999;font-size:24rpx;}

.content .item .f2{display:flex;flex-direction:column;width:200rpx;text-align:right;border-left:1px solid #eee}
.content .item .f2 .t1{ font-size: 40rpx;height: 40rpx;line-height: 40rpx;}
.content .item .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}
.content .item .f2 .t3{ display:flex;justify-content:space-around;margin-top:10rpx; flex-wrap: wrap;}
.content .item .f2 .t3 .x1{height:40rpx;line-height:40rpx;padding:0 8rpx;border:1px solid #ccc;border-radius:6rpx;font-size:22rpx;margin-top: 10rpx;}

/* 独立操作区域样式 */
.content .item .operation-area {
	display:flex;
	flex-direction:column;
	padding: 0 20rpx;
	align-items: center;
	justify-content: center;
	margin-right: 10rpx;
}
.content .item .operation-area .op-btn {
	height:40rpx;
	line-height:40rpx;
	padding:0 16rpx;
	border-radius:20rpx;
	font-size:22rpx;
	margin-bottom: 10rpx;
}

/* 时间筛选样式 */
.time-filter {
	width: 94%;
	margin: 16rpx 3%;
	padding: 20rpx;
	background: #fff;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: nowrap;
}

.filter-item {
	display: flex;
	align-items: center;
	margin-right: 20rpx;
}

.filter-label {
	font-size: 26rpx;
	color: #333;
	margin-right: 8rpx;
	white-space: nowrap;
}

.date-picker {
	padding: 8rpx 16rpx;
	border: 1px solid #ddd;
	border-radius: 6rpx;
	font-size: 24rpx;
	color: #333;
	min-width: 140rpx;
	text-align: center;
}

.time-filter .filter-btn {
	padding: 8rpx 20rpx;
	border-radius: 6rpx;
	font-size: 24rpx;
	margin-left: 8rpx;
	white-space: nowrap;
}

.reset-btn {
	background: #f0f0f0;
	color: #333;
}

/* 新的时间筛选区域样式 */
.date-filter-section {
	background: #fff;
	padding: 30rpx;
	margin: 0 20rpx 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
	transition: all 0.3s ease;
}

.filter-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

.filter-row:last-child {
	margin-bottom: 0;
}

.date-picker {
	padding: 12rpx 24rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	text-align: center;
	width: 280rpx;
}

.button-row {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	margin-top: 20rpx;
}

.filter-button {
	flex: 1;
	height: 70rpx;
	line-height: 70rpx;
	text-align: center;
	border-radius: 35rpx;
	background: #4CAF50;
	color: #fff;
	font-size: 28rpx;
	font-weight: 500;
	transition: background 0.3s;
}

.filter-button:hover {
	opacity: 0.9;
}

.filter-button.reset {
	background: #f0f0f0;
	color: #333;
	border: 1rpx solid #e0e0e0;
}

/* 统计信息样式 */
.statistics-bar {
	width: 94%;
	margin: 0 3%;
	padding: 20rpx;
	border-radius: 16rpx;
	display: flex;
	justify-content: space-around;
	margin-bottom: 20rpx;
}

.statistics-bar .stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #fff;
}

.statistics-bar .stat-value {
	font-size: 36rpx;
	font-weight: bold;
	line-height: 1.2;
}

.statistics-bar .stat-label {
	font-size: 24rpx;
	margin-top: 8rpx;
	opacity: 0.9;
}

.sheet-item {display: flex;align-items: center;padding:20rpx 30rpx;}
.sheet-item .item-img {width: 44rpx;height: 44rpx;}
.sheet-item .item-text {display: block;color: #333;height: 100%;padding: 20rpx;font-size: 32rpx;position: relative; width: 90%;}
.sheet-item .item-text:after {position: absolute;content: '';height: 1rpx;width: 100%;bottom: 0;left: 0;border-bottom: 1rpx solid #eee;}
.man-btn {
	line-height: 100rpx;
	text-align: center;
	background: #FFFFFF;
	font-size: 30rpx;
	color: #FF4015;
}
</style>