<template>
<view class="container">
	<block v-if="isload">
		<dd-tab :itemdata="['全部','进行中','已暂停','已完成','已取消']" :itemst="['all','1','2','3','4']" :st="st" :isfixed="true" @changetab="changetab"></dd-tab>
		<view style="width:100%;height:100rpx"></view>
		
		<view class="order-content">
			<block v-for="(item, index) in datalist" :key="index">
			<view class="order-box" @tap="goto" :data-url="'/yuyue/cycle/orderDetail?id=' + item.id">
				<view class="head">
					<text class="flex1">{{item.product_name}}</text>
					<text :class="'status-tag st' + item.status">{{item.status_text}}</text>
				</view>

				<view class="content">
					<view class="img-wrapper" @tap.stop="goto" :data-url="'/yuyue/cycle/productDetail?id=' + item.product_id">
						<image :src="item.product_pic" mode="aspectFill"></image>
					</view>
					<view class="detail">
						<view class="f1">总期数：{{item.total_period}}期</view>
						<view class="f2">已完成：{{item.served_period}}期</view>
						<view class="f3">开始于：{{item.start_date}}</view>
						<view class="f3">下次服务：{{item.next_service_date || '暂无'}}</view>
					</view>
				</view>
				
				<view class="op">
					<view class="btn" @tap.stop="goto" :data-url="'/yuyue/cycle/orderDetail?id=' + item.id">查看详情</view>
					<!-- 根据状态显示操作按钮 -->
					<block v-if="item.status == 1 || item.status == 2"> <!-- 进行中或已暂停 -->
						<view class="btn" :class="item.status == 1 ? 'btn-pause' : 'btn-resume'" @tap.stop="togglePause" :data-id="item.id">{{item.status == 1 ? '暂停服务' : '恢复服务'}}</view>
						<view class="btn btn-cancel" @tap.stop="cancelOrder" :data-id="item.id">取消服务</view>
					</block>
				</view>
			</view>
			</block>
		</view>
		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
				opt:{},
				loading:false,
				isload: false,
				menuindex:-1,
				st: 'all', // 默认显示全部状态 all, 1(进行中), 2(暂停), 3(完成), 4(取消)
				datalist: [],
				pagenum: 1,
				nomore: false,
				nodata: false,
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st || 'all';
		this.getdata();
	},
	onShow: function() {
		// 每次进入页面都刷新数据
		this.getdata();
	},
	onPullDownRefresh: function () {
		this.getdata();
	},
	onReachBottom: function () {
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getdata(true);
		}
	},
	methods: {
		changetab: function (st) {
			this.st = st;
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0
			});
			this.getdata();
		},
		getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
			var that = this;
			var pagenum = that.pagenum;
			var st = that.st;
			that.nodata = false;
			that.nomore = false;
			that.loading = true;
			
			app.post('ApiPeriodicService/orderList', {st: st, pagenum: pagenum}, function (res) {
				that.loading = false;
				that.isload = true;
				var data = res.data;
				if (pagenum == 1) {
					that.datalist = data;
					if (data.length == 0) {
						that.nodata = true;
					}
				} else {
					if (data.length == 0) {
						that.nomore = true;
					} else {
						var datalist = that.datalist;
						var newdata = datalist.concat(data);
						that.datalist = newdata;
					}
				}
			});
		},
		togglePause: function (e) {
			var that = this;
			var orderid = e.currentTarget.dataset.id;
			var order = that.datalist.find(item => item.id == orderid);
			var actionText = order.status == 1 ? '暂停' : '恢复';
			app.confirm('确定要'+actionText+'该服务吗?', function () {
				app.showLoading('提交中');
				app.post('ApiPeriodicService/togglePauseOrder', {id: orderid}, function (data) {
					app.showLoading(false);
					if(data.status == 1){
						app.success(actionText+'成功');
						setTimeout(function () {
							that.getdata();
						}, 1000);
					}else{
						app.error(data.msg);
					}
				});
			});
		},
		cancelOrder: function (e) {
			var that = this;
			var orderid = e.currentTarget.dataset.id;
			app.confirm('确定要取消该周期服务吗? 未完成的服务将一并取消。', function () {
				app.showLoading('提交中');
				app.post('ApiPeriodicService/cancelOrder', {id: orderid}, function (data) {
					app.showLoading(false);
					if(data.status == 1){
						app.success('取消成功');
						setTimeout(function () {
							that.getdata();
						}, 1000);
					}else{
						app.error(data.msg);
					}
				});
			});
		}
	}
};
</script>
<style>
.container{ width:100%; background-color: #f8f8f8; min-height: 100vh; }

.order-content{ display: flex; flex-direction: column; padding-bottom: 30rpx; }
.order-box{ 
	width: 94%; 
	margin: 20rpx 3%; 
	padding: 0; 
	background: #fff; 
	border-radius: 16rpx; 
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	overflow: hidden;
}

.order-box .head{ 
	display: flex; 
	width: 100%; 
	padding: 24rpx 30rpx;
	border-bottom: 1px #f4f4f4 solid; 
	height: 80rpx; 
	line-height: 80rpx; 
	overflow: hidden; 
	align-items: center;
}

.order-box .head .flex1 { 
	color: #333; 
	font-weight: bold; 
	font-size: 32rpx;
	overflow: hidden; 
	white-space: nowrap; 
	text-overflow: ellipsis; 
	flex: 1;
}

/* 状态标签样式 */
.status-tag {
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
	font-weight: 500;
	text-align: center;
}
.st0 { background-color: #e6f7ff; color: #1890ff; } /* 待激活 */
.st1 { background-color: #fff7e6; color: #FFB600; } /* 进行中 */
.st2 { background-color: #fff2e8; color: #FF8D00; } /* 已暂停 */
.st3 { background-color: #e6fffb; color: #06A051; } /* 已完成 */
.st4 { background-color: #f5f5f5; color: #999999; } /* 已取消 */

.order-box .content{
	display: flex;
	width: 100%; 
	padding: 24rpx 30rpx;
	position: relative;
}

.order-box .content .img-wrapper {
	width: 140rpx;
	height: 140rpx;
	border-radius: 12rpx;
	overflow: hidden;
	background-color: #f7f7f7;
}

.order-box .content image{ 
	width: 140rpx; 
	height: 140rpx; 
}

.order-box .content .detail{
	display: flex;
	flex-direction: column;
	margin-left: 24rpx;
	flex: 1; 
	font-size: 28rpx; 
	color: #666; 
	line-height: 50rpx;
}

.order-box .content .detail .f1{
	color: #333; 
	font-weight: bold;
}

.order-box .content .detail .f3{
	font-size: 26rpx; 
	color: #999;
}

.order-box .op{ 
	display: flex;
	justify-content: flex-end;
	align-items: center;
	width: 100%; 
	padding: 20rpx 30rpx; 
	border-top: 1px #f4f4f4 solid; 
}

.btn {
	margin-left: 20rpx;
	height: 64rpx;
	line-height: 64rpx;
	padding: 0 30rpx;
	color: #333;
	background: #fff;
	border: 1px solid #e6e6e6;
	border-radius: 32rpx;
	text-align: center; 
	font-size: 28rpx;
}

.btn-pause {
	color: #ff9800;
	border-color: #ff9800;
}

.btn-resume {
	color: #2196f3;
	border-color: #2196f3;
}

.btn-cancel {
	color: #f44336;
	border-color: #f44336;
}
</style> 