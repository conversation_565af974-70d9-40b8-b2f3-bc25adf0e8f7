<template>
	<view class="container">
		<block v-if="isload">
			<view class="background-container">
				<view class="header-bg" :style="'background:'+t('color1')"></view>
				<view class="header-gradient" :style="'background-image: linear-gradient('+t('color1')+', #f1f1f1);'">
				</view>
			</view>
			<view class="content-container">
				<view class="search-container" :style="'background:'+t('color1')">
					<view class="search-bar">
			<!-- 			<image class="back-icon" src="/static/img/arrow-left.png"></image> -->
						<view class="search-box">
							<view class="f1 flex-y-center">
								<image class="img" :src="pre_url+'/static/img/search_ico.png'"></image>
								<input :value="keyword" placeholder="搜索感兴趣的团"
									placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
							</view>
						</view>
					</view>


					<view class="top-nav">
						<view class="nav-item" :style="'background:'+t('color1')" @tap="goto" data-url="/pages/order/orderlist">
							<image class="nav-icon" :src="pre_url+'/static/icon/icon1.png'"></image>
							<text class="nav-label">订单</text>
						</view>
						<view class="nav-item" :style="'background:'+t('color1')"@tap="goto" data-url="/shopPackage/shop/cart">
							<image class="nav-icon" :src="pre_url+'/static/icon/icon2.png'"></image>
							<text class="nav-label">购物车</text>
						</view>
						<view class="nav-item" :style="'background:'+t('color1')" @tap="goto" data-url="/activity/scoreshop/index">
							<image class="nav-icon" :src="pre_url+'/static/icon/icon3.png'"></image>
							<text class="nav-label">积分</text>
						</view>
						<view class="nav-item" :style="'background:'+t('color1')" @tap="goto" data-url="/pagesExt/kefu/index">
							<image class="nav-icon" :src="pre_url+'/static/icon/icon4.png'"></image>
							<text class="nav-label">消息</text>
						</view>
						<view class="nav-item" :style="'background:'+t('color1')" @tap="goto" data-url="/pages/index/index">
							<image class="nav-icon" :src="pre_url+'/static/icon/icon5.png'"></image>
							<text class="nav-label">总平台</text>
						</view>
					</view>
					
				</view>
			</view>


			<!-- 	<view class="topsearch flex-y-center">
			<view class="f1 flex-y-center">
				<image class="img" src="/static/img/search_ico.png"></image>
				<input :value="keyword" placeholder="搜索感兴趣的文章" placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
			</view>
		</view>
		<dd-tab :itemdata="cnamelist" :itemst="cidlist" :st="cid" :isfixed="false" @changetab="changetab" v-if="clist.length>0 && !look_type"></dd-tab>
		 -->

			<view class="article_list">
				<!--横排-->
				<view v-if="listtype=='0'" class="article-itemlist" v-for="(item,index) in datalist" :key="item.id"
					@click="gotoDetail" :data-url="'/daihuobiji/kuaituan/detail?id='+item.id">
					<view class="article-pic">
						<image class="image" :src="item.pic" mode="widthFix" />
					</view>
					<view class="article-info">
						<view class="p1" 
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''">
							{{item.name}}</view>
						<block v-if="item.po_status && item.po_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.po_name">
								{{item.po_name}} {{item.po_content}}
							</view>
						</block>
						<block v-if="item.pt_status && item.pt_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pt_name">
								{{item.pt_name}} {{item.pt_content}}
							</view>
						</block>
						<block v-if="item.pth_status && item.pth_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pth_name">
								{{item.pth_name}} {{item.pth_content}}
							</view>
						</block>
						<block v-if="item.pf_status && item.pf_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pf_name">
								{{item.pf_name}} {{item.pf_content}}
							</view>
						</block>
						<view class="p2">
							<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
							<text style="overflow:hidden">阅读 {{item.readcount}}</text>
						</view>
					</view>
				</view>
				<!--双排-->
				<view v-if="listtype=='1'" class="article-item2" v-for="(item,index) in datalist" :key="item.id"
					:style="{marginRight:index%2==0?'2%':'0'}" @click="gotoDetail"
					:data-url="'/daihuobiji/kuaituan/detail?id='+item.id">
					<view class="article-pic">
						<image class="image" :src="item.pic" mode="widthFix" />
					</view>
					<view class="article-info">
						<view class="p1"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''">
							{{item.name}}</view>
						<block v-if="item.po_status && item.po_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.po_name">
								{{item.po_name}} {{item.po_content}}
							</view>
						</block>
						<block v-if="item.pt_status && item.pt_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pt_name">
								{{item.pt_name}} {{item.pt_content}}
							</view>
						</block>
						<block v-if="item.pth_status && item.pth_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pth_name">
								{{item.pth_name}} {{item.pth_content}}
							</view>
						</block>
						<block v-if="item.pf_status && item.pf_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pf_name">
								{{item.pf_name}} {{item.pf_content}}
							</view>
						</block>
						<view class="p2">
							<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
							<text style="overflow:hidden">阅读 {{item.readcount}}</text>
						</view>
					</view>
				</view>
				<waterfall-article v-if="listtype=='2'" :list="datalist" ref="waterfall"></waterfall-article>
				<!--单排-->
				<view v-if="listtype=='3'" class="article-item1" v-for="(item,index) in datalist" :key="item.id"
					@click="gotoDetail" :data-url="'/daihuobiji/kuaituan/detail?id='+item.id">
					<view class="article-pic">
						<image class="image" :src="item.pic" mode="widthFix" />
					</view>
					<view class="article-info">
						<view class="p1"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''">
							{{item.name}}</view>
						<block v-if="item.po_status && item.po_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.po_name">
								{{item.po_name}} {{item.po_content}}
							</view>
						</block>
						<block v-if="item.pt_status && item.pt_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pt_name">
								{{item.pt_name}} {{item.pt_content}}
							</view>
						</block>
						<block v-if="item.pth_status && item.pth_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pth_name">
								{{item.pth_name}} {{item.pth_content}}
							</view>
						</block>
						<block v-if="item.pf_status && item.pf_status==1">
							<view class="p3"
								:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
								v-if="item.pf_name">
								{{item.pf_name}} {{item.pf_content}}
							</view>
						</block>
						<view class="p2">
							<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
							<text style="overflow:hidden">阅读 {{item.readcount}}</text>
						</view>
					</view>
				</view>
				<!-- 三排显示s -->
				<view v-if="listtype=='4'" class="article-item3" v-for="(item,index) in datalist" :key="item.id"
					:style="{marginRight:(index+1)%3==0?'0':'2%'}" @click="gotoDetail"
					:data-url="'/daihuobiji/kuaituan/detail?id='+item.id">
					<view class="article-info">
						<view class="p1"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''">
							{{item.name}}</view>
					</view>
					<view class="article-pic">
						<image class="image" :src="item.pic" mode="widthFix" />
					</view>
					<view class="article-info" v-if="item.po_status && item.po_status==1" style="padding:0rpx 20rpx;">
						<view class="p3"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
							v-if="item.po_name">
							{{item.po_name}} {{item.po_content}}
						</view>
					</view>
					<view class="article-info" v-if="item.pt_status && item.pt_status==1" style="padding:0rpx 20rpx;">
						<view class="p3"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
							v-if="item.pt_name">
							{{item.pt_name}} {{item.pt_content}}
						</view>
					</view>
					<view class="article-info" v-if="item.pth_status && item.pth_status==1" style="padding:0rpx 20rpx;">
						<view class="p3"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
							v-if="item.pth_name">
							{{item.pth_name}} {{item.pth_content}}
						</view>
					</view>
					<view class="article-info" v-if="item.pf_status && item.pf_status==1" style="padding:0rpx 20rpx;">
						<view class="p3"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
							v-if="item.pf_name">
							{{item.pf_name}} {{item.pf_content}}
						</view>
					</view>
					<view class="article-info">
						<view class="p2">
							<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
						</view>
						<view class="p2">
							<text style="overflow:hidden">阅读 {{item.readcount}}</text>
						</view>
					</view>
				</view>
				<!-- 三排显示e -->

				<!--单排三图s-->
				<view v-if="listtype=='5'" class="article-item1" v-for="(item,index) in datalist" :key="item.id"
					@click="gotoDetail" :data-url="'/daihuobiji/kuaituan/detail?id='+item.id">
					<view class="article-info">
						<view class="p1"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''">
							{{item.name}}</view>
					</view>
					<view class="article-pic">
						<block v-if="item.pic" v-for="(img,index) in item.pic">
							<image class="image" :src="img" style="width: 220rpx;height: 220rpx;margin: 8rpx;" />
						</block>
					</view>
					<view class="article-info" v-if="item.po_status && item.po_status==1" style="padding:0rpx 20rpx;">
						<view class="p3"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
							v-if="item.po_name">
							{{item.po_name}} {{item.po_content}}
						</view>
					</view>
					<view class="article-info" v-if="item.pt_status && item.pt_status==1" style="padding:0rpx 20rpx;">
						<view class="p3"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
							v-if="item.pt_name">
							{{item.pt_name}} {{item.pt_content}}
						</view>
					</view>
					<view class="article-info" v-if="item.pth_status && item.pth_status==1" style="padding:0rpx 20rpx;">
						<view class="p3"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
							v-if="item.pth_name">
							{{item.pth_name}} {{item.pth_content}}
						</view>
					</view>
					<view class="article-info" v-if="item.pf_status && item.pf_status==1" style="padding:0rpx 20rpx;">
						<view class="p3"
							:style="set.title_size&&set.title_size>0?'font-size:'+set.title_size*2+'rpx':''"
							v-if="item.pf_name">
							{{item.pf_name}} {{item.pf_content}}
						</view>
					</view> 
					<view class="article-info">
						<view class="p2">
							<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
							<text style="overflow:hidden">阅读 {{item.readcount}}</text>
						</view>
					</view>
				</view>
				<!--单排三图e-->
				<!-- 新增类型6 -->
				<!-- 新增类型6 -->

				<view style="position: absolute;top:150px;left: 0;width: 100%;">

				<view 
				  v-if="listtype == '6'" 
				  class="item-list" 
				  v-for="(item, index) in datalist" 
				  :key="item.id"
				  @click="gotoDetail(item)"
				>
						<view class="item-header">
							<image class="item-image" :src="item.pic"></image>
							<view class="item-author">{{item.author}}</view>
						</view>
						<view class="item-name">{{item.name}}</view>
						<view class="item-price">
							<text class="currency-symbol">￥</text>
							<text class="price-value">{{item.priceRange}}</text>
						</view>
						<view class="item-pics">
							<image class="item-pic" v-for="(ite, ind) in item.pics" :key="ind" :src="ite" v-if="ind<3"></image>
						</view>
						<view class="item-footer">
							<view class="viewers">
								<image class="viewer-icon"
									src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
								<image class="viewer-icon"
									src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
								<image class="viewer-icon"
									src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image>
								<text class="view-count">{{item.readcount}}人看过</text>
							</view>
							<!-- <view class="share" @tap.stop="handleShare(item)">
								<image class="share-icon" src="../../static/img/share.png"></image>
								<text class="share-text">分享</text>
							</view> -->
						</view>
					</view>

				</view>


			</view>
		</block>
		<nodata v-if="nodata"></nodata>
		<nomore v-if="nomore"></nomore>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				nodata: false,
				nomore: false,
				keyword: '',
				datalist: [],
				pagenum: 1,
				clist: [],
				cnamelist: [],
				cidlist: [],
				datalist: [],
				cid: 0,
				bid: 0,
				listtype: 0,
				set: '',
				 tuanzhang_status: 0,  // 确保 tuanzhang_status 在全局 data 中定义
				look_type: false,
				pre_url: '',
			};
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.cid = this.opt.cid || 0;
			this.bid = this.opt.bid || 0;
			this.look_type = this.opt.look_type || false;
			this.pre_url = this.opt.pre_url || '';
			if (this.opt.keyword) {
				this.keyword = this.opt.keyword;
			}
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onReachBottom: function() {
			if (!this.nomore && !this.nodata) {
				this.pagenum = this.pagenum + 1;
				this.getdata(true);
			}
		},
		methods: {
			
			getdata: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var keyword = that.keyword;
				var cid = that.cid;
				
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
				app.post('Apidaihuoyiuan/getartlist', {
					bid: that.bid,
					cid: cid,
					pagenum: pagenum,
					keyword: keyword
				}, function(res) {
					
					console.log(res)
					that.loading = false;
					var data = res.data;
					that.tuanzhang_status =res.tuanzhang_status;
					 //console.log("找到团长: ", tuanzhang_status);
					// 确保数据存在并处理图片字段
					if (data && data.length > 0) {
						data.forEach((item) => {
							// 检查 pic2 字段是否存在，并将其转换为图片数组
							if (item.pic2) {
								item.pics = item.pic2.split(',');
							} else {
								item.pics = []; // 如果 pic2 不存在，则使用空数组
							}
						});
					}
					
					if (pagenum == 1) {
						that.listtype = res.listtype || 0;
						that.clist = res.clist;
						that.set = res.set;
						if ((res.clist).length > 0) {
							var cnamelist = [];
							var cidlist = [];
							cnamelist.push('全部');
							cidlist.push('0');
							for (var i in that.clist) {
								cnamelist.push(that.clist[i].name);
								cidlist.push(that.clist[i].id);
							}
							that.cnamelist = cnamelist;
							that.cidlist = cidlist;
						}

						uni.setNavigationBarTitle({
							title: res.title
						});
						that.datalist = data;
						if (data.length == 0) {
							that.nodata = true;
						}
						that.loaded();
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}
				});
			},
			 // 动态跳转到不同的页面
			  gotoDetail(item) {
			    // 判断团长状态，选择不同的跳转路径
			    if (this.tuanzhang_status === 1) {
			      // 团长存在，跳转到团长的特定链接
			      const url = `/daihuobiji/kuaituan/tuanzhangdetail?id=${item.id}`;
			      console.log("跳转到团长链接: ", url);
			      uni.navigateTo({ url });
			    } else {
			      // 团长不存在，跳转到默认的带货团链接
			      const url = `/daihuobiji/kuaituan/detail?id=${item.id}`;
			      console.log("跳转到默认链接: ", url);
			      uni.navigateTo({ url });
			    }
			  },
			searchConfirm: function(e) {
				var that = this;
				var keyword = e.detail.value;
				that.keyword = keyword
				that.getdata();
			},
			changetab: function(cid) {
				this.cid = cid;
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				if (this.listtype == 2) {
					this.$refs.waterfall.refresh();
				}
				this.getdata();
			},
			handleShare(item) {
				// 阻止事件冒泡
				uni.showActionSheet({
					itemList: ['分享到微信', '复制链接'],
					success: (res) => {
						switch(res.tapIndex) {
							case 0: // 分享到微信
								uni.share({
									provider: 'weixin',
									scene: 'WXSceneSession',
									type: 0,
									title: item.name,
									imageUrl: item.pic,
									summary: `价格：￥${item.priceRange}`,
									success: () => {
										uni.showToast({
											title: '分享成功',
											icon: 'success'
										});
									}
								});
								break;
							case 1: // 复制链接
								const link = `${window.location.origin}/daihuobiji/kuaituan/detail?id=${item.id}`;
								uni.setClipboardData({
									data: link,
									success: () => {
										uni.showToast({
											title: '链接已复制',
											icon: 'success'
										});
									}
								});
								break;
						}
					}
				});
			}
		}
	};
</script>
<style>
	page {
		background: #f6f6f7
	}

	.topsearch {
		width: 100%;
		padding: 20rpx 20rpx;
		background: #fff
	}

	.topsearch .f1 {
		height: 70rpx;
		border-radius: 35rpx;
		border: 0;
		background-color: #f5f5f5;
		flex: 1;
		overflow: hidden
	}

	.topsearch .f1 image {
		width: 30rpx;
		height: 30rpx;
		margin-left: 10px
	}

	.topsearch .f1 input {
		height: 100%;
		flex: 1;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
		background-color: #f5f5f5;
	}

	.article_list {
		padding: 10rpx 16rpx;
		background: #f6f6f7;
		margin-top: 6rpx;
	}

	.article_list .article-item1 {
		width: 100%;
		display: inline-block;
		position: relative;
		margin-bottom: 16rpx;
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden
	}

	.article_list .article-item1 .article-pic {
		width: 100%;
		height: auto;
		overflow: hidden;
		background: #ffffff;
	}

	.article_list .article-item1 .article-pic .image {
		width: 100%;
		height: auto
	}

	.article_list .article-item1 .article-info {
		padding: 10rpx 20rpx 20rpx 20rpx;
	}

	.article_list .article-item1 .article-info .p1 {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.article_list .article-item1 .article-info .t1 {
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		display: block;
		font-size: 32rpx;
	}

	.article_list .article-item1 .article-info .t2 {
		word-break: break-all;
		text-overflow: ellipsis;
		padding-top: 4rpx;
		overflow: hidden;
	}

	.article_list .article-item1 .article-info .p2 {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		padding: 10rpx 0;
		font-size: 24rpx;
		color: #a88;
		overflow: hidden
	}

	.article_list .article-item2 {
		width: 49%;
		display: inline-block;
		position: relative;
		margin-bottom: 12rpx;
		background: #fff;
		border-radius: 8rpx;
	}

	/*.article-item2:nth-child(even){margin-right:2%}*/
	.article_list .article-item2 .article-pic {
		width: 100%;
		height: 0;
		overflow: hidden;
		background: #ffffff;
		padding-bottom: 70%;
		position: relative;
		border-radius: 8rpx 8rpx 0 0;
	}

	.article_list .article-item2 .article-pic .image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: auto
	}

	.article_list .article-item2 .article-info {
		padding: 10rpx 20rpx 20rpx 20rpx;
		display: flex;
		flex-direction: column;
	}

	.article_list .article-item2 .article-info .p1 {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.article_list .article-item2 .article-info .p2 {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		padding-top: 10rpx;
		font-size: 24rpx;
		color: #a88;
		overflow: hidden
	}

	.article_list .article-itemlist {
		width: 100%;
		display: inline-block;
		position: relative;
		margin-bottom: 12rpx;
		padding: 12rpx;
		background: #fff;
		display: flex;
		border-radius: 8rpx;
	}

	.article_list .article-itemlist .article-pic {
		width: 35%;
		height: 0;
		overflow: hidden;
		background: #ffffff;
		padding-bottom: 25%;
		position: relative;
	}

	.article_list .article-itemlist .article-pic .image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: auto
	}

	.article_list .article-itemlist .article-info {
		width: 65%;
		height: auto;
		overflow: hidden;
		padding: 0 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between
	}

	.article_list .article-itemlist .article-info .p1 {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		height: 92rpx
	}

	.article_list .article-itemlist .article-info .p2 {
		display: flex;
		flex-grow: 0;
		flex-shrink: 0;
		font-size: 24rpx;
		color: #a88;
		overflow: hidden;
		padding-bottom: 6rpx
	}

	.article_list .article-item3 {
		width: 32%;
		display: inline-block;
		position: relative;
		margin-bottom: 12rpx;
		background: #fff;
		border-radius: 8rpx;
	}

	/*.article-item3:nth-child(even){margin-right:2%}*/
	.article_list .article-item3 .article-pic {
		width: 100%;
		height: 0;
		overflow: hidden;
		background: #ffffff;
		padding-bottom: 70%;
		position: relative;
		border-radius: 8rpx 8rpx 0 0;
	}

	.article_list .article-item3 .article-pic .image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: auto
	}

	.article_list .article-item3 .article-info {
		padding: 10rpx 20rpx 20rpx 20rpx;
		display: flex;
		flex-direction: column;
	}

	.article_list .article-item3 .article-info .p1 {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.article_list .article-item3 .article-info .p2 {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		padding-top: 10rpx;
		font-size: 24rpx;
		color: #a88;
		overflow: hidden
	}

	.p3 {
		color: #8c8c8c;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}



	.background-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}

	.header-bg {
		background: #58A27E;
		height: 200px;
	}

	.header-gradient {
		background-image: linear-gradient(#58A27E, #f1f1f1);
		height: 100px;
	}

	.content-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}

	.search-bar {
		display: flex;
		align-items: center;
	}

	.back-icon {
		width: 18px;
		height: 18px;
	}

	.search-container {
		width: 100%;
		padding: 16rpx 23rpx;
		background: #5AA37B;
		position: relative;
		overflow: hidden;
	}

	.search-box {
		display: flex;
		align-items: center;
		height: 60rpx;
		border-radius: 10rpx;
		background-color: #fff;
		flex: 1;
	/* 	margin-left: 15px; */
	}

	.search-box .img {
		width: 24rpx;
		height: 24rpx;
		margin-right: 10rpx;
		margin-left: 30rpx;
	}

	.search-box .search-text {
		font-size: 24rpx;
		color: #C2C2C2;
		width: 100%;
	}

	.top-nav {
		margin-top: 30px;
		margin-bottom: 10px;
		display: flex;
		justify-content: space-between;
		padding: 0;
		background-color: #fff;
		align-items: center;
	}

	.nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		flex: 1;
		background: #5AA37B;
	}

	.nav-icon {
		width: 64rpx;
		height: 64rpx;
		margin-bottom: 6rpx;
	}

	.nav-label {
		font-size: 22rpx;
		color: #fff;
	}

	.item-list {
		background: #fff;
		margin: 10px;
		padding: 15px;
		border-radius: 8px; 
	}

	.item-header {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #eee;
		padding-bottom: 10px;
		margin-bottom: 10px;
	}

	.item-image {
		width: 50px;
		height: 50px;
		border-radius: 5px;
	}

	.item-author {
		margin: 5px;
		font-size: 14px;
	}

	.item-name {
		overflow-wrap: break-word;
		font-size: 16px;
	}

	.item-price {
		margin: 10px 0;
		color: red;
	}

	.currency-symbol {
		font-size: 12px;
		font-weight: bold;
	}

	.price-value {
		font-size: 16px;
		font-weight: bold;
	}

	.item-pics {
		display: flex;
	}

	.item-pic {
		width: 100px;
		height: 100px;
		border-radius: 8px;
		margin-right: 5px;
	}

	.item-footer {
		display: flex;
		background: #F6F6F6;
		padding: 5px;
		border-radius: 5px;
		align-items: center;
		justify-content: space-between;
		margin-top: 10px;
	}

	.viewers {
		display: flex;
		align-items: center;
	}

	.viewer-icon {
		width: 20px;
		height: 20px;
		border-radius: 50px;
		margin-left: -10px;
	}

	.view-count {
		font-size: 12px;
		margin-left: 10px;
		color: #507C85;
	}

	.share {
		display: flex;
		align-items: center;
		padding: 0 10px;
		border-left: 1px solid #ccc;
	}

	.share-icon {
		width: 15px;
		height: 15px;
	}

	.share-text {
		font-size: 12px;
		margin-left: 10px;
		color: #507C85;
	}
</style>