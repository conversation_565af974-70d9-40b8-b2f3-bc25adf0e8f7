<template>
	<view class="container" v-if="!loading && packageInfo.id">
		<!-- 购买的套餐信息 -->
		<view class="package-card">
			<image class="package-pic" :src="packageInfo.pic" mode="aspectFill"></image>
			<view class="package-details">
				<view class="package-name">{{ packageInfo.name }}</view>
				<view class="package-price-buy" :style="{color: t('color1')}">￥{{ packageInfo.sell_price }}</view>
				<!-- 可添加规格/数量等 -->
			</view>
		</view>

		<!-- 联系方式 -->
		<view class="form-section">
			<view class="form-item">
				<text class="label">联系人</text>
				<input class="input" v-model="linkman" placeholder="请填写联系人姓名" />
			</view>
			<view class="form-item">
				<text class="label">联系电话</text>
				<input class="input" type="number" v-model="tel" placeholder="请填写联系电话" />
			</view>
		</view>

		<!-- 优惠与金额 -->
		<view class="form-section">
			<view class="form-item arrow" @tap="chooseCoupon" v-if="couponList.length > 0">
				<text class="label">优惠券</text>
				<view class="value coupon-value" :class="{placeholder: !selectedCoupon}">
					{{ selectedCoupon ? '已选 ' + selectedCoupon.name : couponCount + ' 张可用' }}
				</view>
			</view>
			<view class="form-item" v-if="orderData.userinfo && orderData.userinfo.leveldk_money > 0">
				<text class="label">会员折扣</text>
				<view class="value discount-value" :style="{color: t('color1')}">-￥{{ orderData.userinfo.leveldk_money }}</view>
			</view>
			<view class="form-item" v-if="coupon_money > 0">
				<text class="label">优惠券抵扣</text>
				<view class="value discount-value" :style="{color: t('color1')}">-￥{{ coupon_money }}</view>
			</view>
			<view class="form-item">
				<text class="label">商品金额</text>
				<view class="value">￥{{ packageInfo.product_price || packageInfo.sell_price }}</view>
			</view>
		</view>

		<!-- 备注 -->
		<view class="form-section">
			<view class="form-item remark-item">
				<text class="label">订单备注</text>
				<textarea class="textarea" v-model="remark" placeholder="选填，请输入备注信息"></textarea>
			</view>
		</view>

		<!-- 底部提交栏 -->
		<view class="bottom-bar-buy">
			<view class="total-price-area">
				<text>合计：</text>
				<text class="price-symbol-buy" :style="{color: t('color1')}">￥</text>
				<text class="total-price-value" :style="{color: t('color1')}">{{ actual_price }}</text>
			</view>
			<button class="submit-button" :style="{background: t('color1')}" @tap="submitOrder">提交订单</button>
		</view>

		<!-- 优惠券选择弹窗 (示例，可能需要替换为项目中已有的组件) -->
		<uni-popup ref="couponPopup" type="bottom">
			<view class="coupon-popup-content">
				<view class="popup-header">
					<text>选择优惠券</text>
					<text class="close-icon" @tap="closeCouponPopup">×</text>
				</view>
				<scroll-view scroll-y class="coupon-scroll">
					<view class="coupon-item-popup" v-for="coupon in couponList" :key="coupon.couponrid"
						@tap="selectCoupon(coupon)" :class="{selected: selectedCoupon && selectedCoupon.couponrid === coupon.couponrid}">
						<view class="coupon-info">
							<view class="coupon-name">{{ coupon.name }}</view>
							<view class="coupon-desc">{{ coupon.desc }}</view>
							<view class="coupon-expire">有效期至 {{ coupon.expiredate }}</view>
						</view>
						<view class="coupon-radio" :style="selectedCoupon && selectedCoupon.couponrid === coupon.couponrid ? 'border-color:' + t('color1') + ';background:' + t('color1') : ''">
							<text class="check-icon" v-if="selectedCoupon && selectedCoupon.couponrid === coupon.couponrid">✓</text>
						</view>
					</view>
					<view class="coupon-item-popup no-use" @tap="selectCoupon(null)" :class="{selected: !selectedCoupon}">
						不使用优惠券
						<view class="coupon-radio" :style="!selectedCoupon ? 'border-color:' + t('color1') + ';background:' + t('color1') : ''">
							<text class="check-icon" v-if="!selectedCoupon">✓</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>

	</view>
	<view class="loading-container" v-else-if="loading">
		<text>加载中...</text>
	</view>
	<view class="empty-container" v-else>
		<text>加载订单信息失败</text>
	</view>
</template>

<script>
var app = getApp();
export default {
	components: { // 注册uni-popup组件
		// 如果项目未使用easycom，需要在此显式注册
	},
	data() {
		return {
			packageId: null,
			orderData: {},       // 接口返回的原始数据
			packageInfo: {},     // 提取的套餐信息
			businessInfo: {},    // 提取的商家信息
			couponList: [],      // 可用优惠券列表
			couponCount: 0,      // 可用优惠券数量
			linkman: '',
			tel: '',
			remark: '',
			selectedCoupon: null, // 选中的优惠券对象 {couponrid, name, desc, money, ...}
			loading: true,
			submitting: false // 防止重复提交
		}
	},
	computed: {
		// 优惠券抵扣金额
		coupon_money() {
			return this.selectedCoupon ? parseFloat(this.selectedCoupon.money || 0) : 0;
		},
		// 会员折扣金额
		leveldk_money() {
			return this.orderData.userinfo ? parseFloat(this.orderData.userinfo.leveldk_money || 0) : 0;
		},
		// 套餐原始总价 (可能是product_price或sell_price，根据业务定)
		package_total_price() {
			return parseFloat(this.packageInfo.product_price || this.packageInfo.sell_price || 0);
		},
		// 最终实际支付金额
		actual_price() {
			let price = this.package_total_price - this.leveldk_money - this.coupon_money;
			price = price < 0 ? 0 : price; // 价格不能小于0
			return price.toFixed(2); // 保留两位小数
		}
	},
	onLoad(options) {
		if (options.package_id) {
			this.packageId = options.package_id;
			this.getBuyData();
		} else {
			app.error('缺少套餐ID', function() {
				app.goback();
			});
			this.loading = false;
		}
		uni.setNavigationBarTitle({
			title: '确认订单'
		});
	},
	methods: {
		getBuyData() {
			var that = this;
			that.loading = true;
			app.post('ApiYuyuePackage/buy', { package_id: that.packageId }, function(res) {
				console.log('[getBuyData] ApiYuyuePackage/buy 返回: ', JSON.stringify(res)); // 添加日志
				that.loading = false;
				// 根据新的 API 响应调整数据提取逻辑
				if (res.status == 1 && res.allbuydata && Array.isArray(res.allbuydata) && res.allbuydata.length > 0) {
					that.orderData = res; // 存储完整响应，以防他处需要
					// 提取默认联系方式和地址
					that.linkman = res.linkman || '';
					that.tel = res.tel || '';
					// that.address = res.address || {}; // 如果需要显示地址

					// 处理 allbuydata 数组 (假设只处理第一个元素，即一次只买一个套餐包)
					const buyItem = res.allbuydata[0];
					console.log('[getBuyData] 处理 buyItem: ', JSON.stringify(buyItem));

					that.couponList = buyItem.couponList || [];
					that.couponCount = buyItem.couponCount || 0;

					// 确保 userinfo 对象存在，并存储会员折扣信息
					if (!that.orderData.userinfo) that.orderData.userinfo = {};
					that.orderData.userinfo.leveldk_money = buyItem.leveldk_money || 0;
					console.log('[getBuyData] 更新 userinfo: ', JSON.stringify(that.orderData.userinfo));

					// 从 prodata 中提取套餐信息
					if (buyItem.prodata && Array.isArray(buyItem.prodata) && buyItem.prodata.length > 0) {
						const productInfo = buyItem.prodata[0];
						console.log('[getBuyData] 处理 productInfo: ', JSON.stringify(productInfo));
						if (productInfo.package) {
							const packageFromServer = productInfo.package;
							console.log('[getBuyData] 提取 packageFromServer: ', JSON.stringify(packageFromServer));

							that.packageInfo = {
								id: packageFromServer.id,
								name: packageFromServer.name,
								pic: packageFromServer.pic,
								// 优先使用 buyItem 中的价格，然后是 package 中的
								sell_price: buyItem.sell_price !== undefined ? buyItem.sell_price : packageFromServer.sell_price,
								product_price: buyItem.product_price !== undefined ? buyItem.product_price : (packageFromServer.product_price || packageFromServer.sell_price)
							};

							// 清理图片 URL
							if (that.packageInfo.pic && that.packageInfo.pic.startsWith('https://localhost')) {
								console.log('[getBuyData] 清理套餐图片前缀');
								that.packageInfo.pic = that.packageInfo.pic.substring('https://localhost'.length);
							}
							console.log('[getBuyData] 构建的 packageInfo: ', JSON.stringify(that.packageInfo));
						} else {
							console.error('[getBuyData] buyItem.prodata[0] 中缺少 package 对象');
							app.error('加载购买信息失败[pke]');
						}
					} else {
						console.error('[getBuyData] buyItem.prodata 结构错误或为空');
						app.error('加载购买信息失败[pdi]');
					}

					// 存储商家ID，用于提交订单 (商家名称和logo可能需要从packageInfo或单独接口获取)
					that.businessInfo = { id: buyItem.bid };
					console.log('[getBuyData] 存储 businessInfo: ', JSON.stringify(that.businessInfo));

				} else {
					console.error('[getBuyData] 接口状态错误或 allbuydata 结构不符: ', res);
					app.error(res.msg || '加载订单信息失败', function() {
						// app.goback(); // 暂时不返回，让用户看到错误
					});
				}
			}, function(err) { // 添加错误回调日志
				that.loading = false;
				console.error('[getBuyData] ApiYuyuePackage/buy 请求失败: ', err);
				app.error('请求失败', function() {
					// app.goback();
				});
			});
		},

		// 打开优惠券弹窗
		chooseCoupon() {
			if (this.couponList.length > 0) {
				this.$refs.couponPopup.open();
			} else {
				app.toast('暂无可用优惠券');
			}
		},
		// 关闭优惠券弹窗
		closeCouponPopup() {
			this.$refs.couponPopup.close();
		},
		// 选择优惠券
		selectCoupon(coupon) {
			this.selectedCoupon = coupon;
			this.closeCouponPopup();
		},

		// 提交订单
		submitOrder() {
			var that = this;
			if (!that.linkman) {
				app.error('请填写联系人');
				return;
			}
			if (!that.tel || !/^1[3-9]\d{9}$/.test(that.tel)) {
				app.error('请填写正确的手机号');
				return;
			}

			if (that.submitting) return; // 防止重复提交
			that.submitting = true;
			app.showLoading('提交中...');

			//-- 调试日志 --
			console.log('[submitOrder] 当前 packageInfo:', JSON.stringify(that.packageInfo));
			//--------------

			// 构建buydata数组
			let buydata = [];
			console.log('[submitOrder] 检查条件: businessInfo.id=', that.businessInfo.id, 'packageId=', that.packageId); // 添加日志
			// 修改判断条件，允许 businessInfo.id 为 0
			if (typeof that.businessInfo.id === 'number' && that.packageId) {
				buydata.push({
					bid: that.businessInfo.id,
					prodatastr: that.packageId, // 套餐ID，接口文档用prodatastr
					couponrid: that.selectedCoupon ? that.selectedCoupon.couponrid : 0,
					// remark: that.remark, // 接口文档中 remark 在外层
					// 数量可能需要，默认为1
					num: 1,
					product_price: that.packageInfo.product_price // 添加后端需要的 product_price
				});
			} else {
				app.showLoading(false);
				that.submitting = false;
				app.error('订单信息不完整');
				return;
			}

			// 准备提交参数
			const params = {
				// buydata: JSON.stringify(buydata), // 接口可能需要JSON字符串 - 改为直接传递数组
				buydata: buydata,
				linkman: that.linkman,
				tel: that.tel,
				remark: that.remark
				// 可能还需要 platform, scene 等通用参数，app.post内部处理
			};

			//-- 调试日志 --
			console.log('[submitOrder] 最终提交参数 params:', JSON.stringify(params));
			//--------------

			app.post('ApiYuyuePackage/createOrder', params, function(res) {
				app.showLoading(false);
				that.submitting = false;
				if (res.status == 1) {
					const order_id = res.order_id;
					// 判断是否需要支付
					if (that.actual_price > 0 && res.payorderid) {
						// 修改此处：参考buy.vue的支付方式，直接跳转到支付页面
						console.log('[submitOrder] 跳转支付页面, payorderid=', res.payorderid); // 添加日志
						that.submitting = true; // 设置提交状态，防止重复点击
						app.goto('/pages/pay/pay?id=' + res.payorderid);
						
						// 注释掉原来的支付方式
						/*
						app.payorder({
							payorderid: res.payorderid,
							orderid: order_id, // 支付成功后跳转可能需要
							type: 'yuyue_package', // 定义一个类型用于支付成功后的回调或跳转判断
							success: function(){
								app.success('支付成功', function(){
									app.goto('/pages/my/packageorderlist'); // 跳转到套餐订单列表
								});
							},
							fail: function(){
								app.error('支付失败', function(){
									app.goto('/pages/my/packageorderlist?status=0'); // 跳转到待支付列表
								});
							}
						});
						*/
					} else {
						// 0元订单，直接成功
						app.success('下单成功', function() {
							app.goto('/pages/my/packageorderlist');
						});
					}
				} else {
					app.error(res.msg || '创建订单失败');
				}
			}, function() {
				app.showLoading(false);
				that.submitting = false;
				app.error('请求失败');
			});
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	padding-bottom: 140rpx; /* 留出底部空间 */
}

/* 套餐卡片 */
.package-card {
	display: flex;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.package-pic {
	width: 160rpx;
	height: 160rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.package-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.package-name {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
	/* 最多显示两行 */
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	margin-bottom: 10rpx;
}

.package-price-buy {
	font-size: 32rpx;
	font-weight: bold;
}

/* 表单区域 */
.form-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 0 25rpx;
	margin-bottom: 20rpx;
}

.form-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-height: 100rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
	border-bottom: none;
}

.form-item.arrow {
	position: relative;
	padding-right: 30rpx;
}

.form-item.arrow::after {
	content: '';
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%) rotate(45deg);
	width: 14rpx;
	height: 14rpx;
	border-top: 2rpx solid #ccc;
	border-right: 2rpx solid #ccc;
}

.label {
	font-size: 28rpx;
	color: #333;
	width: 150rpx; /* 固定标签宽度 */
	flex-shrink: 0;
}

.input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	text-align: right;
	height: 100%; /* 确保input高度撑满form-item */
}

.value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	text-align: right;
}

.coupon-value {
	color: #ff9900;
}
.coupon-value.placeholder {
	color: #999;
}

.discount-value {
	font-weight: bold;
}

.remark-item {
	align-items: flex-start; /* 标签和文本域顶部对齐 */
	padding: 20rpx 0;
	min-height: 150rpx;
}
.remark-item .label {
	padding-top: 10rpx; /* 微调标签位置 */
}

.textarea {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	height: 120rpx;
	padding: 10rpx;
	box-sizing: border-box;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	width: auto; /* 覆盖uni-app默认宽度 */
}

/* 底部提交栏 */
.bottom-bar-buy {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx 0 30rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	box-sizing: border-box;
	padding-bottom: env(safe-area-inset-bottom); /* iPhone X 适配 */
	height: calc(100rpx + env(safe-area-inset-bottom));
}

.total-price-area {
	font-size: 26rpx;
	color: #333;
}

.price-symbol-buy {
	font-size: 28rpx;
	margin-left: 8rpx;
	font-weight: bold;
}

.total-price-value {
	font-size: 36rpx;
	font-weight: bold;
}

.submit-button {
	height: 72rpx;
	line-height: 72rpx;
	font-size: 28rpx;
	color: #fff;
	padding: 0 50rpx;
	border-radius: 36rpx;
	text-align: center;
	border: none;
	outline: none;
	margin: 0;
}
button::after {
	border: none;
}

/* 优惠券弹窗 */
.coupon-popup-content {
	background-color: #fff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	padding: 20rpx 0;
	height: 60vh; /* 弹窗高度 */
	display: flex;
	flex-direction: column;
}

.popup-header {
	font-size: 32rpx;
	font-weight: bold;
	text-align: center;
	padding-bottom: 20rpx;
	position: relative;
	border-bottom: 1rpx solid #f0f0f0;
}

.close-icon {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 36rpx;
	color: #999;
}

.coupon-scroll {
	flex: 1;
	overflow-y: auto;
	padding: 20rpx 30rpx;
}

.coupon-item-popup {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx 0;
	border-bottom: 1rpx dashed #eee;
}
.coupon-item-popup:last-of-type {
    border-bottom: none;
}
.coupon-item-popup.no-use {
    font-size: 28rpx;
    color: #666;
}

.coupon-info {
	flex: 1;
	margin-right: 20rpx;
}

.coupon-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
	font-weight: bold;
}

.coupon-desc {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 8rpx;
}

.coupon-expire {
	font-size: 22rpx;
	color: #ccc;
}

.coupon-radio {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	border: 2rpx solid #ccc;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.coupon-radio .check-icon {
	color: #fff;
	font-size: 24rpx;
}

/* 加载和空状态 */
.loading-container, .empty-container {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 80vh;
	color: #999;
	font-size: 28rpx;
}
</style> 