/*
 * PDF.js worker v2.12.313 (simplified version for demo)
 * Build date: 2023-03-15
 * Copyright 2012 Mozilla Foundation
 */

// 简化版的PDF.js worker代码
console.log('PDF.js worker loaded');

// 基本的worker功能模拟
self.onmessage = function(event) {
  console.log('PDF.js worker received message:', event.data);
  
  // 模拟处理完成后的返回
  self.postMessage({
    type: 'success',
    data: { status: 'OK' }
  });
};

// 确保在不同环境下正确导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {};
} 