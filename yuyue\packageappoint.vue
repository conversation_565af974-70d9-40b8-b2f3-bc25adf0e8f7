<template>
  <view class="container">
    <!-- 套餐信息卡片 -->
    <view class="card order-info-card" v-if="packageInfo">
      <view class="product-info">
        <image :src="packageInfo.product_pic || packageInfo.service_pic" mode="aspectFill" class="product-image"></image>
        <view class="info">
          <text class="name">{{ packageInfo.product_name || packageInfo.service_name }}</text>
          <text class="package">来自套餐：{{ packageOrderInfo.package_name }}</text>
        </view>
      </view>
      <view class="order-detail">
        <view class="detail-item">
          <text class="label">套餐订单号：</text>
          <text class="value">{{ packageOrderInfo.ordernum }}</text>
        </view>
        <view class="detail-item">
          <text class="label">剩余次数：</text>
          <text class="value">{{ packageInfo.remain_num || packageInfo.remain_times }}</text>
        </view>
      </view>
    </view>

    <!-- 服务方式与信息卡片 -->
    <view class="card service-info-card">
      <!-- 服务方式选择 -->
      <view class="body_item service-method-item">
        <view class="body_title flex flex-bt">服务方式<text class="body_text">请选择服务方式</text></view>
        <view class="body_content">
          <view class="body_tag" 
            :class="fwtype == 1 ? 'body_active' : ''"
            :style="fwtype == 1 ? 'border-color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1);color:'+t('color1') : ''"
            @tap="selectServiceType(1)">到店服务
          </view>
          <view class="body_tag" 
            :class="fwtype == 2 ? 'body_active' : ''"
            :style="fwtype == 2 ? 'border-color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1);color:'+t('color1') : ''"
            @tap="selectServiceType(2)">上门服务
          </view>
        </view>
      </view>

      <!-- 地址选择 (上门) -->
      <view class="address-section" v-if="fwtype == 2" @tap="goToAddressPage">
        <view class="address-content flex-y-center">
          <image class="location-icon" src="/static/img/address.png" />
          <view class="address-details flex1" v-if="addressInfo && addressInfo.id">
            <view class="user-info">{{addressInfo.name}} {{addressInfo.tel}} <text v-if="addressInfo.company">{{addressInfo.company}}</text></view>
            <view class="address-text">{{addressInfo.area}} {{addressInfo.address}}</view>
          </view>
          <view v-else class="placeholder flex1">请选择服务地址</view>
          <image src="/static/img/arrowright.png" class="arrow-icon"></image>
        </view>
      </view>

      <!-- 联系信息 (到店) -->
      <view class="contact-section" v-if="fwtype==1">
        <view class="linkitem">
          <text class="label">联 系 人</text>
          <input type="text" class="input" :value="linkman" placeholder="请输入您的姓名" @input="inputLinkman" placeholder-class="input-placeholder"/>
        </view>
        <view class="linkitem">
          <text class="label">联系电话</text>
          <input type="text" class="input" :value="tel" placeholder="请输入您的手机号" @input="inputTel" placeholder-class="input-placeholder"/>
        </view>
      </view>
    </view>

    <view class="footer-placeholder"></view> <!-- 底部按钮占位 -->
    <view class="footer">
      <button class="submit-btn" :disabled="!canSubmit" 
        :style="canSubmit ? 'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%); box-shadow: 0 6rpx 12rpx rgba('+t('color1rgb')+',0.3)' : 'background:#ccc'" 
        @tap="submitAppoint">确认使用</button>
    </view>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      // 套餐订单相关
      packageOrderId: '', // 套餐订单ID
      productId: '', // 服务/商品ID
      packageOrderInfo: {}, // 套餐订单信息
      packageInfo: {}, // 服务项信息

      // 服务方式选择
      productFwtype: 1, // 产品支持的服务方式: 1到店 2上门 3两者都支持
      fwtype: 1, // 默认选择到店服务
      
      // 地址选择
      addressInfo: null, // 用户选择的地址信息
      selectedAddressId: 0, // 选中的地址ID

      // 联系人信息
      linkman: '',
      tel: '',

      // UI状态
      loading: true,
      order_flow_mode: 1, // 设置为简化模式
    }
  },
  
  computed: {
    canSubmit() {
      // 验证是否可以提交
      if (this.fwtype == 2) {
        // 如果是上门服务，必须选择地址
        return this.addressInfo && this.selectedAddressId > 0;
      } else {
        // 如果是到店服务，必须填写联系人和电话
        return this.linkman && this.tel;
      }
    }
  },
  
  onLoad(options) {
    console.log('加载套餐使用页面，参数:', options);
    
    if (options && options.package_order_id && options.product_id) {
      this.packageOrderId = options.package_order_id;
      this.productId = options.product_id;
      
      this.getPackageOrderInfo(); // 会触发 getProductInfo
      
      // 注意：默认服务方式的设置移到 getProductInfo 内部，
      // 因为需要先判断商品是否支持上门服务。
      // 如果需要强制默认上门（无论商品是否支持），可以在 getProductInfo 回调后设置
      // this.fwtype = 2;
      // this.loadAddressInfo();
      
    } else {
      app.error('缺少必要参数');
      this.loading = false; // 参数错误也要结束loading
    }
  },
  
  methods: {
    t(name) {
      return app.globalData.config && app.globalData.config.t ? app.globalData.config.t(name) : '';
    },
    
    // 获取套餐订单信息
    getPackageOrderInfo() {
      var that = this;
      app.showLoading('加载套餐信息');
      
      app.post('ApiYuyuePackage/getPackageOrderDetail', { 
        order_id: that.packageOrderId 
      }, function(res) {
        // app.showLoading(false); // loading在getProductInfo后结束
        
        if (res.status == 1 && res.data) {
          console.log('套餐订单信息:', res.data);
          that.packageOrderInfo = res.data;
          
          // 找到对应的服务项
          if (res.data.items && res.data.items.length > 0) {
            const serviceItem = res.data.items.find(item => 
              item.product_id == that.productId || item.service_id == that.productId
            );
            
            if (serviceItem) {
              that.packageInfo = serviceItem;
              console.log('找到的服务项信息:', serviceItem);
              
              // 获取商品详情以获取fwtype 和 默认联系方式
              that.getProductInfo(that.productId);
              
            } else {
              app.error('未找到指定的服务项');
              that.loading = false;
            }
          } else {
            app.error('套餐中没有服务项');
            that.loading = false;
          }
          
        } else {
          app.error(res.msg || '获取套餐订单详情失败');
          that.loading = false;
        }
      }, function() {
        app.showLoading(false);
        app.error('请求失败');
        that.loading = false;
      });
    },
    
    // 获取商品详情，主要是获取fwtype 和 默认联系方式
    getProductInfo(productId) {
      var that = this;
      console.log('正在获取商品信息，ID:', productId);
      // app.showLoading('获取商品信息'); // 减少loading次数
      
      app.post('ApiYuyue/product', { // 调用商品详情接口
        id: productId 
      }, function(res) {
        app.showLoading(false); // 在这里结束loading
        
        if (res.status == 1 && res.data) {
          console.log('商品详情:', res.data);
          
          // 获取商品支持的服务方式
          if (res.data.product && res.data.product.fwtype !== undefined) {
            that.productFwtype = parseInt(res.data.product.fwtype || '0');
            console.log('商品支持的服务方式:', that.productFwtype);
            
            // 设置默认服务方式为上门 (fwtype=2)，如果商品支持的话
            if (that.productFwtype === 2 || that.productFwtype === 3) { // 支持上门或两者都支持
              that.fwtype = 2;
              that.loadAddressInfo(); // 加载地址信息
            } else { // 只支持到店
              that.fwtype = 1;
            }
          } else {
            console.log('商品未设置服务方式，默认设置为上门');
            that.productFwtype = 3; // 假设支持所有
            that.fwtype = 2; // 默认上门
            that.loadAddressInfo(); // 加载地址信息
          }
          
          // 获取联系人和电话 (即使默认上门，也可能需要备用)
          if (res.data.linkman || res.data.tel) {
             that.linkman = res.data.linkman || '';
             that.tel = res.data.tel || '';
             console.log('获取到默认联系方式:', that.linkman, that.tel);
          }
          
        } else {
          console.log('获取商品详情失败，默认设置为上门');
          that.productFwtype = 3; // 假设支持所有
          that.fwtype = 2; // 默认上门
          that.loadAddressInfo(); // 加载地址信息
        }
        // that.loading = false; // loading移到回调开始处结束
      }, function(err) {
        app.showLoading(false); // 错误时也要结束loading
        console.log('请求商品详情出错，默认设置为上门:', err);
        that.productFwtype = 3; // 假设支持所有
        that.fwtype = 2; // 默认上门
        that.loadAddressInfo(); // 加载地址信息
        // that.loading = false;
      });
    },
    
    // 选择服务方式
    selectServiceType(type) {
      console.log('选择服务方式:', type);
      this.fwtype = type;
      
      if (type == 2) {
        // 选择了上门服务，加载地址信息
        this.loadAddressInfo();
      }
    },
    
    // 加载地址信息
    loadAddressInfo() {
      var that = this;
      app.showLoading('获取地址信息');
      
      // 通过地址列表接口获取地址，默认地址排在最前
      app.post('ApiAddress/address', { type: 0 }, function(res) { // type=0 获取所有地址
        app.showLoading(false);
        
        if (res.status == 1 && res.data && res.data.length > 0) {
          console.log('获取到地址列表:', res.data);
          // 查找默认地址 (isdefault=1)
          let defaultAddress = res.data.find(item => item.isdefault == 1);
          if (defaultAddress) {
            console.log('找到默认地址:', defaultAddress);
            that.addressInfo = defaultAddress;
            that.selectedAddressId = defaultAddress.id;
          } else {
            // 如果没有默认地址，选择列表中的第一个地址作为备选
            console.log('未找到默认地址，使用列表第一个地址');
            that.addressInfo = res.data[0];
            that.selectedAddressId = res.data[0].id;
          }
        } else {
          console.log('地址列表为空或获取失败');
          that.addressInfo = null;
          that.selectedAddressId = 0;
        }
      }, function(err) {
        app.showLoading(false);
        console.log('获取地址列表失败:', err);
        that.addressInfo = null;
        that.selectedAddressId = 0;
      });
    },
    
    // 跳转到地址选择页面
    goToAddressPage() {
      var url = '/pages/address/' + (this.addressInfo && this.addressInfo.id ? 'address' : 'addressadd') + '?fromPage=packageappoint&type=1';
      console.log('跳转到地址页面:', url);
      app.goto(url);
    },
    
    // 处理联系人输入
    inputLinkman(e) {
      this.linkman = e.detail.value;
    },
    
    // 处理电话输入
    inputTel(e) {
      this.tel = e.detail.value;
    },
    
    // 提交预约
    submitAppoint() {
      var that = this;
      
      if (!that.canSubmit) {
        if (that.fwtype == 2 && (!that.addressInfo || that.selectedAddressId <= 0)) {
          app.error('请选择服务地址');
          return;
        }
        
        if (that.fwtype == 1 && (!that.linkman || !that.tel)) {
          app.error('请填写联系人和电话');
          return;
        }
        
        return;
      }
      
      app.confirm('确认使用套餐服务吗？', function() {
        app.showLoading('提交中');
        
        // 构建请求参数
        const params = {
          order_id: that.packageOrderId,
          product_id: that.productId,
          fwtype: that.fwtype, // 添加服务方式参数
          order_flow_mode: 1,  // 设置为简化模式
          formdata: {} // 如果需要表单数据，可以在这里添加
        };
        
        // 如果是上门服务，添加地址ID
        if (that.fwtype == 2 && that.addressInfo) {
          params.address_id = that.selectedAddressId;
        } else if (that.fwtype == 1) {
          // 如果是到店服务，添加联系人和电话
          params.linkman = that.linkman;
          params.tel = that.tel;
        }
        
        console.log('提交预约参数:', params);
        
        // 调用使用套餐服务接口
        app.post('ApiYuyuePackage/usePackageService', params, function(res) {
          app.showLoading(false);
          
          if (res.status == 1) {
            app.success(res.msg || '预约成功', function() {
              // 如果返回了预约订单ID，跳转到预约详情页
              if (res.data && res.data.yuyue_order_id) {
                app.goto('/yuyue/orderdetail?id=' + res.data.yuyue_order_id);
              } else {
                // 否则返回套餐详情页
                app.goto('/yuyue/packageorderdetail?order_id=' + that.packageOrderId);
              }
            });
          } else {
            app.error(res.msg || '预约失败');
          }
        }, function() {
          app.showLoading(false);
          app.error('请求失败');
        });
      });
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx 20rpx 140rpx; /* 增加底部padding给按钮留空间 */
  min-height: 100vh;
  background-color: #f7f8fa; /* 更柔和的背景色 */
  box-sizing: border-box;
}

.card {
  background: #fff;
  border-radius: 16rpx; /* 更大的圆角 */
  padding: 30rpx; /* 增加内边距 */
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); /* 更柔和的阴影 */
}

/* 套餐信息卡片 */
.order-info-card .product-info {
  display: flex;
  align-items: center; /* 垂直居中 */
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.order-info-card .product-image {
  width: 100rpx; /* 略微缩小图片 */
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.order-info-card .info {
  flex: 1;
}

.order-info-card .name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.order-info-card .package {
  font-size: 24rpx;
  color: #999;
}

.order-info-card .order-detail {
  font-size: 26rpx; /* 统一字体大小 */
  color: #666;
}

.order-info-card .order-detail .detail-item {
  display: flex;
  justify-content: space-between;
  line-height: 1.6;
}

.order-info-card .order-detail .label {
  color: #666;
}

.order-info-card .order-detail .value {
  color: #333;
}

/* 服务方式与信息卡片 */
.service-info-card {
  /* 可以在这里添加卡片特定样式 */
}

.service-method-item {
  padding-bottom: 30rpx !important;
  border-bottom: 1rpx solid #f0f0f0 !important; /* 加粗分割线 */
}

.body_title {
  font-size: 30rpx; /* 稍大标题 */
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.body_text {
  font-size: 24rpx;
  font-weight: normal;
  color: #999;
}

.body_content {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.body_tag {
  padding: 12rpx 30rpx; /* 调整标签内边距 */
  height: auto;
  min-height: 54rpx;
  line-height: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #f7f8fa;
  border: 2rpx solid #f7f8fa; /* 默认边框与背景同色 */
  border-radius: 30rpx; /* 更圆的标签 */
  margin-right: 20rpx;
  margin-bottom: 20rpx; /* 增加底部间距 */
  font-size: 26rpx; /* 标签字体 */
  color: #666;
  transition: all 0.2s;
}

.body_active {
  background: rgba(252, 67, 67, 0.08) !important; /* 选中背景色，使用主题色 */
  border-color: #FC4343 !important; /* 选中边框色，使用主题色 */
  color: #FC4343 !important; /* 选中文字颜色，使用主题色 */
  font-weight: bold;
}

/* 地址选择 */
.address-section {
  padding: 30rpx 0 10rpx; /* 调整上下边距 */
}

.address-content {
  display: flex;
  align-items: center;
  min-height: 100rpx; /* 保证足够高度 */
}

.location-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.address-details {
  flex: 1;
  overflow: hidden; /* 防止文本溢出 */
}

.user-info {
  font-weight: bold;
  color: #333;
  font-size: 30rpx;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.address-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}

.arrow-icon {
  width: 28rpx;
  height: 28rpx;
  margin-left: 20rpx;
  flex-shrink: 0;
}

/* 联系信息 */
.contact-section {
  padding-top: 30rpx;
}

.contact-section .linkitem {
  display: flex;
  align-items: center;
  padding: 20rpx 0; /* 增加垂直间距 */
  border-bottom: 1rpx solid #f0f0f0;
}
.contact-section .linkitem:last-child {
  border-bottom: none;
}

.contact-section .label {
  width: 140rpx; /* 调整标签宽度 */
  color: #333;
  font-size: 28rpx;
  flex-shrink: 0;
}

.contact-section .input {
  flex: 1;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 28rpx;
  color: #333;
  text-align: right;
}

.input-placeholder {
  color: #ccc; /* 更浅的占位符颜色 */
  font-size: 28rpx;
  text-align: right;
}


/* 底部确认按钮 */
.footer-placeholder {
  height: 130rpx; /* 占位高度，等于footer高度+一些额外间距 */
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom)); /* 适配iPhone X等底部安全区 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.06);
  z-index: 10;
}

.submit-btn {
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
  border: none;
  text-align: center;
}

.submit-btn:after {
  display: none;
}

.submit-btn[disabled] {
  opacity: 0.6;
  box-shadow: none;
}

/* 继承buy.vue的必要样式，去除冗余 */
.flex-bt {
	justify-content: space-between;
}

.flex-y-center {
  display: flex;
  align-items: center;
}

.flex1 {
  flex: 1;
}
</style> 