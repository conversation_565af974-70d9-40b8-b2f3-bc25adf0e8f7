<template>
  <view>
    <block v-if="isload">
      <view class="apply-page">
        <!-- 顶部筛选栏 -->
        <scroll-view 
          scroll-x 
          class="filter-bar" 
          :scroll-with-animation="true"
          :scroll-into-view="'tab-' + currentTab"
        >
          <view 
            v-for="tab in statusList"
            :key="tab"
            class="filter-item"
            :id="'tab-' + tab"
            :class="{ active: currentTab === tab }"
            @tap="switchTab(tab)"
          >
            {{ tab === 'all' ? '全部' : 
               tab === '1' ? '已投递' :
               tab === '2' ? '已查看' :
               tab === '3' ? '初筛通过' :
               tab === '4' ? '待面试' :
               tab === '5' ? '面试通过' :
               tab === '6' ? '已入职' : '已结束'
            }}
          </view>
        </scroll-view>

        <!-- 职位列表 -->
        <swiper 
          class="swiper-content" 
          :current="swiperCurrent"
          @change="swiperChange"
          :style="{ height: 'calc(100vh - 120rpx)' }"
        >
          <swiper-item v-for="(status, index) in ['all', '1', '2', '3', '4', '5', '6', '7']" :key="index">
            <scroll-view 
              scroll-y 
              class="job-list"
              @scrolltolower="loadMore"
              refresher-enabled
              :refresher-triggered="isRefreshing"
              @refresherrefresh="onRefresh"
            >
              <!-- 列表内容 -->
              <block v-if="applyJobs.length > 0">
                <view 
                  v-for="(job, index) in applyJobs" 
                  :key="index"
                  class="job-card"
                  @tap="viewJobDetail(job.id)"
                >
                  <!-- 职位信息 -->
                  <view class="job-info">
                    <view class="job-header">
                      <text class="job-title">{{ job.title }}</text>
                      <text class="job-salary">{{ job.salary }}</text>
                    </view>
                    <view class="company-info">
                      <image class="company-logo" :src="job.companyLogo" mode="aspectFit"></image>
                      <text class="company-name">{{ job.companyName }}</text>
                      <text class="apply-time">{{ job.applyTime }}</text>
                    </view>
                  </view>
                  
                  <!-- 底部标签和状态 -->
                  <view class="job-footer">
                    <view class="job-tags">
                      <text class="tag" v-for="(tag, tagIndex) in job.tags" :key="tagIndex">
                        {{ tag }}
                      </text>
                    </view>
                    <view class="status-tag" :class="job.status">
                      {{ getStatusText(job.status, job.statusText) }}
                    </view>
                  </view>
                </view>
              </block>

              <!-- 空状态 -->
              <view v-else class="empty-state">
                <image class="empty-icon" src="/static/icons/empty-apply.png" mode="aspectFit"></image>
                <text class="empty-text">暂无投递记录</text>
                <button class="browse-btn" @tap="goToJobList">去浏览职位</button>
              </view>

              <!-- 加载状态 -->
              <view v-if="isLoading && applyJobs.length > 0" class="loading-more">
                正在加载更多...
              </view>
              <view v-if="noMoreData && applyJobs.length > 0" class="no-more-data">
                没有更多数据了
              </view>
            </scroll-view>
          </swiper-item>
        </swiper>
      </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      currentTab: 'all',
      swiperCurrent: 0,
      applyJobs: [],
      isLoading: false,
      isRefreshing: false,
      noMoreData: false,
      page: 1,
      pageSize: 10,
      pre_url: app.globalData.pre_url,
      statusList: ['all', '1', '2', '3', '4', '5', '6', '7']
    }
  },

  onLoad(opt) {
    this.opt = app.getopts(opt);
    this.loadApplyJobs();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  onShareAppMessage() {
    return this._sharewx({
      title: '我投递的工作',
      desc: '查看投递记录',
      pic: ''
    });
  },

  onShareTimeline() {
    var sharewxdata = this._sharewx({
      title: '我投递的工作',
      desc: '查看投递记录',
      pic: ''
    });
    var query = (sharewxdata.path).split('?')[1];
    return {
      title: sharewxdata.title,
      imageUrl: sharewxdata.imageUrl,
      query: query
    }
  },

  onReachBottom() {
    if (!this.noMoreData && !this.isLoading) {
      this.loadMore();
    }
  },

  methods: {
    // 切换标签
    switchTab(tab) {
      if (this.currentTab === tab) return;
      this.currentTab = tab;
      this.swiperCurrent = this.statusList.indexOf(tab);
      this.page = 1;
      this.noMoreData = false;
      this.applyJobs = [];
      this.loadApplyJobs();
    },

    // swiper 切换事件
    swiperChange(e) {
      const index = e.detail.current;
      const tab = this.statusList[index];
      if (this.currentTab === tab) return;
      this.currentTab = tab;
      this.page = 1;
      this.noMoreData = false;
      this.applyJobs = [];
      this.loadApplyJobs();
    },

    // 获取状态文本
    getStatusText(status, statusText) {
      return statusText || '已投递';
    },

    // 加载投递的职位
    loadApplyJobs() {
      if (this.isLoading || this.noMoreData) return;
      this.isLoading = true;
      this.loading = true;

      const params = {
        page: this.page,
        limit: this.pageSize
      };
      
      // 根据当前标签筛选状态
      if (this.currentTab !== 'all') {
        params.status = parseInt(this.currentTab);
      }

      app.get('apiZhaopin/getApplyList', params, (res) => {
        this.loading = false;
        this.isLoading = false;
        this.isRefreshing = false;
        uni.stopPullDownRefresh();

        if (res.code === 0) {
          const jobList = res.data.map(item => {
            // 合并所有标签
            const tags = [];
            if (item.options) {
              try {
                const options = JSON.parse(item.options);
                Object.values(options).forEach(values => {
                  if (Array.isArray(values)) {
                    tags.push(...values);
                  }
                });
              } catch (e) {
                console.error('解析options失败:', e);
              }
            }
            // 添加基本信息标签
            if (item.education) tags.push(item.education);
            if (item.experience) tags.push(item.experience);
            if (item.address) tags.push(item.address);

            return {
              id: item.position_id,
              title: item.position_name || item.title,
              salary: item.salary,
              companyName: item.company_name,
              companyLogo: item.company_logo,
              tags: tags,
              applyTime: item.create_time,
              status: item.status,
              statusText: item.status_text
            };
          });

          if (this.page === 1) {
            this.applyJobs = jobList;
          } else {
            this.applyJobs = [...this.applyJobs, ...jobList];
          }

          this.noMoreData = jobList.length < this.pageSize || this.applyJobs.length >= res.count;
          this.page++;
          this.isload = true;
        } else {
          uni.showToast({
            title: res.msg || '加载失败，请重试',
            icon: 'none'
          });
        }
      });
    },

    // 下拉刷新
    async onRefresh() {
      this.isRefreshing = true;
      this.page = 1;
      this.noMoreData = false;
      await this.loadApplyJobs();
    },

    // 加载更多
    loadMore() {
      this.loadApplyJobs();
    },

    // 查看职位详情
    viewJobDetail(jobId) {
      uni.navigateTo({
        url: `/pagesExa/zhaopin/partdetails?id=${jobId}`
      });
    },

    // 跳转到职位列表
    goToJobList() {
      uni.switchTab({
        url: '/pagesExa/zhaopin/index'
      });
    }
  }
}
</script>

<style lang="scss">
.apply-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: env(safe-area-inset-bottom);
  
  .filter-bar {
    display: flex;
    background-color: rgba(255, 255, 255, 0.98);
    padding: 20rpx 24rpx;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(10px);
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    -webkit-overflow-scrolling: touch;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
    
    &::-webkit-scrollbar {
      display: none;
    }
    
    .filter-item {
      padding: 12rpx 28rpx;
      font-size: 26rpx;
      color: #666;
      background-color: #f8f9fa;
      border-radius: 32rpx;
      margin-right: 16rpx;
      transition: all 0.25s ease-in-out;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02);
      flex-shrink: 0;
      display: inline-block;
      border: 1rpx solid rgba(0, 0, 0, 0.03);
      
      &:last-child {
        margin-right: 24rpx;
      }
      
      &:first-child {
        margin-left: 4rpx;
      }
      
      &.active {
        background: linear-gradient(135deg, #1890ff, #096dd9);
        color: #fff;
        font-weight: 500;
        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
        border: 1rpx solid rgba(24, 144, 255, 0.1);
        transform: translateY(-1rpx);
      }

      &:active {
        transform: scale(0.95) translateY(0);
        transition: all 0.15s ease-in-out;
      }
    }
  }
  
  .job-list {
    height: calc(100vh - 120rpx);
    padding: 16rpx;
    
    .job-card {
      margin: 20rpx 12rpx;
      padding: 28rpx;
      background-color: #fff;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1rpx solid rgba(0, 0, 0, 0.02);
      
      &:active {
        transform: scale(0.985);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
      }
      
      .job-info {
        .job-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20rpx;
          
          .job-title {
            font-size: 32rpx;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.4;
          }
          
          .job-salary {
            font-size: 30rpx;
            font-weight: 600;
            color: #ff4d4f;
            background: rgba(255, 77, 79, 0.08);
            padding: 4rpx 16rpx;
            border-radius: 6rpx;
          }
        }
        
        .company-info {
          display: flex;
          align-items: center;
          margin-bottom: 24rpx;
          
          .company-logo {
            width: 56rpx;
            height: 56rpx;
            border-radius: 12rpx;
            margin-right: 16rpx;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
            border: 1rpx solid rgba(0, 0, 0, 0.04);
          }
          
          .company-name {
            font-size: 26rpx;
            color: #666;
            margin-right: 16rpx;
          }
          
          .apply-time {
            font-size: 24rpx;
            color: #999;
            background: #f8f9fa;
            padding: 2rpx 12rpx;
            border-radius: 4rpx;
          }
        }
      }
      
      .job-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20rpx;
        
        .job-tags {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;
          margin-right: 16rpx;
          
          .tag {
            padding: 4rpx 16rpx;
            background-color: #f8f9fa;
            border-radius: 4rpx;
            font-size: 24rpx;
            color: #666;
            border: 1rpx solid #eee;
          }
        }
        
        .status-tag {
          padding: 6rpx 16rpx;
          font-size: 24rpx;
          border-radius: 4rpx;
          font-weight: 500;
          
          // 已投递
          &[class="1"] {
            color: #faad14;
            background: rgba(250, 173, 20, 0.1);
            border: 1rpx solid rgba(250, 173, 20, 0.3);
          }
          
          // 已查看
          &[class="2"] {
            color: #1890ff;
            background: rgba(24, 144, 255, 0.1);
            border: 1rpx solid rgba(24, 144, 255, 0.3);
          }
          
          // 初筛通过
          &[class="3"] {
            color: #52c41a;
            background: rgba(82, 196, 26, 0.1);
            border: 1rpx solid rgba(82, 196, 26, 0.3);
          }
          
          // 待面试
          &[class="4"] {
            color: #722ed1;
            background: rgba(114, 46, 209, 0.1);
            border: 1rpx solid rgba(114, 46, 209, 0.3);
          }
          
          // 面试通过
          &[class="5"] {
            color: #13c2c2;
            background: rgba(19, 194, 194, 0.1);
            border: 1rpx solid rgba(19, 194, 194, 0.3);
          }
          
          // 已入职
          &[class="6"] {
            color: #389e0d;
            background: rgba(56, 158, 13, 0.1);
            border: 1rpx solid rgba(56, 158, 13, 0.3);
          }
          
          // 已结束
          &[class="7"] {
            color: #999;
            background: rgba(0, 0, 0, 0.05);
            border: 1rpx solid rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 240rpx;
    
    .empty-icon {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 40rpx;
      opacity: 0.8;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
      margin-bottom: 48rpx;
    }
    
    .browse-btn {
      padding: 24rpx 64rpx;
      background: linear-gradient(135deg, #40a9ff, #1890ff);
      color: #fff;
      font-size: 28rpx;
      border-radius: 44rpx;
      box-shadow: 0 8rpx 20rpx rgba(24, 144, 255, 0.25);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: none;
      font-weight: 500;
      letter-spacing: 2rpx;
      
      &:active {
        transform: translateY(2rpx) scale(0.98);
        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
        background: linear-gradient(135deg, #1890ff, #096dd9);
      }
    }
  }
  
  .loading-more,
  .no-more-data {
    text-align: center;
    padding: 30rpx 0;
    font-size: 26rpx;
    color: #999;
    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.02));
  }
}
</style> 