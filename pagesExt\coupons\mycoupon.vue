<template>
<view class="container">
	<block v-if="isload">
		<dd-tab :itemdata="['未使用','已使用','已过期']" :itemst="['0','1','2']" :st="st" :isfixed="false" @changetab="changetab"></dd-tab>

		<view class="coupon-list">
			<view v-for="(item, index) in datalist" :key="index" class="coupon" @tap.stop="goto" :data-url="'coupondetail?rid=' + item.id" :style=" (item.isgive == 1 || item.isgive == 2)?'padding-left:40rpx':''">
				<view class="pt_left">
					<view class="pt_left-content">
						<view class="f1" :style="{color:t('color1')}" v-if="item.type==0"><text class="t0">￥</text><text class="t1">{{item.money}}</text></view>
						<view class="f1" :style="{color:t('color1')}" v-if="item.type==1">商品券</view>
						<view class="f1" :style="{color:t('color1')}" v-if="item.type==2"><text class="t1">{{item.score}}</text><text class="t2">积分</text></view>
						<view class="f1" :style="{color:t('color1')}" v-if="item.type==3">礼品卡</view>
						<view class="f2" :style="{color:t('color1')}" >
							<text >兑换码：{{item.code}}</text>
						</view>
					</view>
				</view>
				<view class="pt_right">
					<view class="f1">
						<view class="t1">{{item.name}}</view>
						<!-- <view class="t4" style="color:red;" >x {{item.count}} 次</view> -->
						<text class="t2" v-if=" (item.isgive == 1 || item.isgive == 2)" :style="{background:'rgba('+t('color2rgb')+',0.1)',color:t('color2')}">可赠送</text>
						<view class="t3" style="margin-top:10rpx">有效期至 {{item.endtime}}</view>
					</view>
					<view class="f1_top">
						<block>
							<button class="btn" :style="{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap.stop="goto" :data-url="'coupondetail?rid=' + item.id">详情</button>
						</block>
					</view>
					<!-- <view class="f1_right">
						<block>
							<button class="btn" :style="{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap.stop="goto2" :data-url="'coupondetail?rid=' + item.id">去兑换</button>
						</block>
					</view> -->
					<image class="sygq" v-if="st==1" :src="pre_url+'/static/img/ysy.png'"></image>
					<image class="sygq" v-if="st==2" :src="pre_url+'/static/img/ygq.png'"></image>
				</view>
			</view>
		</view>
		<nomore v-if="nomore"></nomore>
		<nodata v-if="nodata"></nodata>
		<view class="giveopbox" v-if="checkednum > 0" :class="menuindex>-1?'tabbarbot':'notabbarbot3'">
			<view class="btn-give" @tap="shareapp" v-if="getplatform() == 'app'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}">转赠好友({{checkednum}}张)</view>
			<view class="btn-give" @tap="sharemp" v-else-if="getplatform() == 'mp'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}">转赠好友({{checkednum}}张)</view>
			<view class="btn-give" @tap="sharemp" v-else-if="getplatform() == 'h5'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}">转赠好友({{checkednum}}张)</view>
			<button class="btn-give" open-type="share" v-else :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" >转赠好友({{checkednum}}张)</button>
		</view>
		<view style="display:none">{{test}}</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			
      st: 0,
      datalist: [],
      pagenum: 1,
      nomore: false,
			nodata:false,
			givecheckbox:false,
			checkednum:0,
			test:'',
			shareTitle:'',
			sharePic:'',
			shareDesc:'',
			shareLink:'',
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st || 0;
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
	onShareAppMessage:function(){
		console.log('this.shareLink');
		console.log(this.shareLink);
		return this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});
		var query = (sharewxdata.path).split('?')[1]+'&seetype=circle';
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  methods: {
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var st = that.st;
      var bid = that.opt && (that.opt.bid || that.opt.bid === '0') ? that.opt.bid : '';
			that.loading = true;
			that.nomore = false;
			that.nodata = false;
        app.post('ApiLipin2/mycoupon', {st: st,pagenum: pagenum,bid: bid}, function (res) {
				that.loading = false;
				uni.setNavigationBarTitle({
					title: '我的' + that.t('礼品卡')
				});
        var data = res.data;
		
        if (pagenum == 1) {
					that.checkednum = 0;
					that.pics = res.pics;
					that.clist = res.clist;
					that.givecheckbox = res.givecheckbox;
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    changeradio: function (e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
			var datalist = that.datalist;
      var checked = datalist[index].checked;
			if(checked){
				datalist[index].checked = false;
				that.checkednum--;
			}else{
				datalist[index].checked = true;
				that.checkednum++;
			}
			that.datalist = datalist;
			that.test = Math.random();
			console.log(that.checkednum);
			var ids = [];
			for(var i in datalist){
				if(datalist[i].checked){
					ids.push(datalist[i].id);
				}
			}
			ids = ids.join(',');
			that.shareTitle = '送你'+that.checkednum+'张'+that.t('礼品卡');
			that.shareDesc = '点击前往查看领取';
			that.sharePic = app.globalData.initdata.logo;
			if(app.globalData.platform == 'h5' || app.globalData.platform == 'mp' || app.globalData.platform == 'app'){
				that.shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pagesExt/coupons/coupongive?scene=rids_'+ids+'-pid_' + app.globalData.mid;
			}else{
				that.shareLink = '/pagesExt/coupons/coupongive?scene=rids_'+ids+'-pid_' + app.globalData.mid;
			}
			console.log(that.shareLink);
			that.loaded({title:that.shareTitle,pic:that.sharePic,desc:that.shareDesc,link:that.shareLink});
    },
	
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			that.sharetypevisible = false;
			uni.showActionSheet({
		    itemList: ['发送给微信好友', '分享到微信朋友圈'],
		    success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.shareTitle;
						sharedata.summary = that.shareDesc;
						sharedata.href = that.shareLink;
						sharedata.imageUrl = that.sharePic;
						
						uni.share(sharedata);
					}
		    }
		  });
		},
  }
};
</script>
<style>

.coupon-list{width:100%;padding:20rpx}
.coupon{width:100%;display:flex;margin-bottom:20rpx;border-radius:10rpx;overflow:hidden;align-items:center;position:relative;background: #fff;}
.coupon .pt_left{background: #fff;min-height:200rpx;color: #FFF;width:30%;display:flex;flex-direction:column;align-items:center;justify-content:center}
.coupon .pt_left-content{width:100%;height:100%;margin:30rpx 0;border-right:1px solid #EEEEEE;display:flex;flex-direction:column;align-items:center;justify-content:center}
.coupon .pt_left .f1{font-size:40rpx;font-weight:bold;text-align:center;}
.coupon .pt_left .t0{padding-right:0;}
.coupon .pt_left .t1{font-size:60rpx;}
.coupon .pt_left .t2{padding-left:10rpx;}
.coupon .pt_left .f2{font-size:20rpx;color:#4E535B;text-align:center;}
.coupon .pt_right{background: #fff;width:70%;display:flex;min-height:200rpx;text-align: left;padding:20rpx 20rpx;position:relative}
.coupon .pt_right .f1{
	flex-grow: 1;flex-shrink: 1;
	position: relative;
}
.coupon .pt_right .f1_right{
	flex-grow: 1;flex-shrink: 1;
	position: relative;
	top:5px;
	left:20px;
}
.coupon .pt_right .f1_top{
	flex-grow: 1;flex-shrink: 1;
	position: relative;
	top:-20rpx;
	left:35rpx;
}
.coupon .pt_right .f1 .f1_action {
	position: absolute;
	right: 20rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 100rpx;
	height: 40rpx;
	border-radius: 25rpx;
	background: linear-gradient(130deg, #F21818 0%, #FC9191 100%);
	color: #ffffff;
	font-size: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.coupon .pt_right .f1 .t1{font-size:28rpx;color:#2B2B2B;font-weight:bold;height:60rpx;line-height:60rpx;overflow:hidden}
.coupon .pt_right .f1 .t2{height:36rpx;line-height:36rpx;font-size:20rpx;font-weight:bold;padding:0 16rpx;border-radius:4rpx; margin-right: 16rpx;}
.coupon .pt_right .f1 .t2:last-child {margin-right: 0;}
.coupon .pt_right .f1 .t3{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}
.coupon .pt_right .f1 .t4{font-size:20rpx;color:#999999;height:46rpx;line-height:46rpx;}
.coupon .pt_right .btn{position:absolute;right:30rpx;top:50%;margin-top:-28rpx;border-radius:28rpx;width:160rpx;height:56rpx;line-height:56rpx;color:#fff}
.coupon .pt_right .sygq{position:absolute;right:30rpx;top:50%;margin-top:-50rpx;width:100rpx;height:100rpx;}

.coupon .pt_left.bg3{background:#ffffff;color:#b9b9b9!important}
.coupon .pt_right.bg3 .t1{color:#b9b9b9!important}
.coupon .pt_right.bg3 .t3{color:#b9b9b9!important}
.coupon .pt_right.bg3 .t4{color:#999999!important}

.coupon .radiobox{position:absolute;left:0;padding:20rpx}
.coupon .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;}
.coupon .radio .radio-img{width:100%;height:100%}
.giveopbox{position:fixed;bottom:0;left:0;width:100%;}
.btn-give{width:90%;margin:30rpx 5%;height:96rpx; line-height:96rpx; text-align:center;color: #fff;font-size:30rpx;font-weight:bold;border-radius:48rpx;}
</style>