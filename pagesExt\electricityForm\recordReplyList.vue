<template>
	<view class="container">
		<block v-if="isload">

			<view class="order-content">
				<block v-for="(item, index) in datalist" :key="item.id">
					<view class="order-box">
						<view class="head">
							<text class="flex1"></text>
							<text v-if="item.userInfo.realname != null" class="st2">服务人员</text>
							<text v-if="item.userInfo.realname == null" class="st3">评论</text>
							<!-- <block v-if="item.status==1">
								<block v-if="item.buytype!=1">
									<text v-if="item.team.status==1" class="st1">拼团中</text>
									<text v-if="item.team.status==2 && item.freight_type!=1" class="st1">拼团成功,待发货</text>
									<text v-if="item.team.status==2 && item.freight_type==1" class="st1">拼团成功,待提货</text>
									<text v-if="item.team.status==3" class="st4">拼团失败,已退款</text>
								</block>
								<block v-else>
									<text v-if="item.freight_type!=1" class="st1">待发货</text>
									<text v-if="item.freight_type==1" class="st1">待提货</text>
								</block>
							</block> -->

							<!-- <text v-if="item.status==2" class="st2">待收货</text>
							<text v-if="item.status==3" class="st3">已完成</text>
							<text v-if="item.status==4" class="st4">已关闭</text> -->
						</view>

						<view class="content" style="border-bottom:none">
							<!-- <view @tap.stop="goto" :data-url="'product?id=' + item.proid">
								<image :src="item.propic"></image>
							</view> -->
							<view class="detail">
								<text class="t1" style="display: block;">{{item.content}}</text>
								<text class="t2" v-if="item.userInfo.realname == null">{{item.userInfo.nickname}}</text>
								<text class="t2" v-if="item.userInfo.realname != null">{{item.userInfo.realname}}</text>
								<!-- <view class="t3"><text class="x1 flex1">￥{{item.sell_price}}</text><text
										class="x2">×{{item.num}}</text></view> -->
							</view>
						</view>
						<view class="bottom" style="text-align: right;">
							<text>{{item.createtime}}</text>
						</view>
						<!-- <view class="bottom">
							<text>共计{{item.num}}件商品 实付:￥{{item.totalprice}}</text>
							<text v-if="item.refund_status==1" style="color:red"> 退款中￥{{item.refund_money}}</text>
							<text v-if="item.refund_status==2" style="color:red"> 已退款￥{{item.refund_money}}</text>
							<text v-if="item.refund_status==3" style="color:red"> 退款申请已驳回</text>
						</view> -->
						<view class="op">
							<!-- <block v-if="([1,2,3]).includes(item.status) && item.invoice && item.team.status==2">
								<view class="btn2" @tap="goto"
									:data-url="'/pagesExt/order/invoice?type=lucky_collage&orderid=' + item.id">发票
								</view>
							</block> -->
							<!-- <view v-if="is_more==1&&item.status!=0" @tap.stop="more_one" :data-id="item.proid"
								class="btn2" :data-num="item.num" :data-buytype="item.buytype" :data-ggid="item.ggid">
								再来一单</view> -->
							<!-- <view @tap.stop="goto" :data-url="'recordDetail?id=' + item.id" class="btn2">详情</view> -->
							<!-- <view @tap.stop="goto" :data-url="'team?teamid=' + item.teamid" class="btn2"
								v-if="item.buytype!=1">查看团</view> -->
							<!-- <block v-if="item.status==0">
								<view class="btn2" @tap.stop="toclose" :data-id="item.id">关闭订单</view>
								<view class="btn1" :style="{background:t('color1')}" @tap.stop="goto"
									:data-url="'/pages/pay/pay?id=' + item.payorderid">去付款</view>
							</block> -->

							<!-- <block v-if="item.status==2">
								<view class="btn2" @tap.stop="goto"
									:data-url="'/pagesExt/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no"
									v-if="item.freight_type!=3 && item.freight_type!=4">查看物流</view>
								<view class="btn1" :style="{background:t('color1')}" @tap.stop="orderCollect"
									:data-id="item.id">确认收货</view>
							</block>
							<block v-if="item.status==4">
								<view class="btn2" @tap.stop="todel" :data-id="item.id">删除订单</view>
							</block> -->
						</view>
					</view>
				</block>
			</view>
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>

		<uni-popup ref="more_one" type="center">
			<uni-popup-dialog mode="input" message="成功消息" :duration="2000" valueType="number" :before-close="true"
				@close="close" @confirm="confirm"></uni-popup-dialog>
			<!-- <view class="uni-popup-dialog">
			<view class="uni-dialog-title">
				<text>test</text>
			</view>
			<view class="uni-dialog-content">
				<input type="number"/>
			</view>
			<view class="uni-dialog-button-group">
				 <view></view>
			</view>
		</view> -->
		</uni-popup>
	</view>

</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				datalist: [],
				pagenum: 1,
				nomore: false,
				is_more: 0,
				nodata: false,
				keyword: '',
				more_data: {}
			};
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onReachBottom: function() {
			if (!this.nodata && !this.nomore) {
				this.pagenum = this.pagenum + 1;
				this.getdata(true);
			}
		},
		onNavigationBarSearchInputConfirmed: function(e) {
			this.searchConfirm({
				detail: {
					value: e.text
				}
			});
		},
		methods: {
			more_one: function(e) {
				// console.log(e);
				let data = e.target.dataset;
				// let url = "/activity/luckycollage/buy"+"?proid="+data['id']+"&num="+data['num']+"&buytype="+data['buytype']+"&ggid="+data['ggid'];
				// uni.redirectTo({
				// 	url:url,
				// })
				this.more_data = data;
				this.$refs.more_one.open();

			},
			confirm(value1, value2) {
				return 
				let data = this.more_data;
				//console.log(value2);
				let num = value2;
				let that = this;
				if (num == "" || num == 0) {
					uni.showModal({
						title: "提示",
						content: "请输入数量",
						success() {
							that.$refs.more_one.close();
						}
					})
				}
				let url = "/activity/luckycollage/buy" + "?proid=" + data['id'] + "&num=" + num + "&buytype=" + data[
					'buytype'] + "&ggid=" + data['ggid'];
				uni.redirectTo({
					url: url,
				})
				this.$refs.more_one.close();
			},
			close() {
				this.$refs.more_one.close();
			},
			changetab: function(st) {
				this.st = st;
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				this.getdata();
			},
			getdata: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var id = that.opt.id;
				that.nodata = false;
				that.nomore = false;
				that.loading = true;
				app.post('ApiElectricityForm/getRecordDetail', {
					id: id,
					pagenum: pagenum,
					// keyword: that.keyword
				}, function(res) {
					that.loading = false;
					var data = res.list;
					if (pagenum == 1) {
						that.datalist = data;
						if (data.length == 0) {
							that.nodata = true;
						}
						that.loaded();
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}
					// that.is_more = res['is_more'];
				});
			},
			searchConfirm: function(e) {
				this.keyword = e.detail.value;
				this.getdata(false);
			}
		}
	};
</script>
<style>
	.container {
		width: 100%;
	}

	.topsearch {
		width: 94%;
		margin: 10rpx 3%;
	}

	.topsearch .f1 {
		height: 60rpx;
		border-radius: 30rpx;
		border: 0;
		background-color: #fff;
		flex: 1
	}

	.topsearch .f1 .img {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10px
	}

	.topsearch .f1 input {
		height: 100%;
		flex: 1;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
	}

	.order-content {
		display: flex;
		flex-direction: column
	}

	.order-box {
		width: 94%;
		margin: 10rpx 3%;
		padding: 6rpx 3%;
		background: #fff;
		border-radius: 8px
	}

	.order-box .head {
		display: flex;
		width: 100%;
		border-bottom: 1px #f4f4f4 solid;
		height: 70rpx;
		line-height: 70rpx;
		overflow: hidden;
		color: #999;
	}

	.order-box .head .f1 {
		display: flex;
		align-items: center;
		color: #333
	}

	.order-box .head .f1 image {
		width: 34rpx;
		height: 34rpx;
		margin-right: 4px
	}

	.order-box .head .st0 {
		width: 140rpx;
		color: #ff8758;
		text-align: right;
	}

	.order-box .head .st1 {
		width: 204rpx;
		color: #ffc702;
		text-align: right;
	}

	.order-box .head .st2 {
		width: 204rpx;
		color: #ff4246;
		text-align: right;
	}

	.order-box .head .st3 {
		width: 140rpx;
		color: #999;
		text-align: right;
	}

	.order-box .head .st4 {
		width: 140rpx;
		color: #bbb;
		text-align: right;
	}

	.order-box .content {
		display: flex;
		width: 100%;
		padding: 16rpx 0px;
		border-bottom: 1px #f4f4f4 dashed;
		position: relative
	}

	.order-box .content:last-child {
		border-bottom: 0;
	}

	.order-box .content image {
		width: 140rpx;
		height: 140rpx;
	}

	.order-box .content .detail {
		display: flex;
		flex-direction: column;
		margin-left: 14rpx;
		flex: 1
	}

	.order-box .content .detail .t1 {
		font-size: 26rpx;
		line-height: 36rpx;
		margin-bottom: 10rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.order-box .content .detail .t2 {
		height: 46rpx;
		line-height: 46rpx;
		color: #999;
		overflow: hidden;
		font-size: 26rpx;
	}

	.order-box .content .detail .t3 {
		display: flex;
		height: 40rpx;
		line-height: 40rpx;
		color: #ff4246;
	}

	.order-box .content .detail .x1 {
		flex: 1
	}

	.order-box .content .detail .x2 {
		width: 100rpx;
		font-size: 32rpx;
		text-align: right;
		margin-right: 8rpx
	}

	.order-box .bottom {
		width: 100%;
		padding: 10rpx 0px;
		border-top: 1px #f4f4f4 solid;
		color: #555;
	}

	.order-box .op {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
		align-items: center;
		width: 100%;
		padding: 10rpx 0px;
		border-top: 1px #f4f4f4 solid;
		color: #555;
	}

	.btn1 {
		margin-left: 20rpx;
		margin-top: 10rpx;
		width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		color: #fff;
		border-radius: 3px;
		text-align: center
	}

	.btn2 {
		margin-left: 20rpx;
		margin-top: 10rpx;
		width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		color: #333;
		background: #fff;
		border: 1px solid #cdcdcd;
		border-radius: 3px;
		text-align: center
	}

	/*.order-pin{ border: 1px #ffc702 solid; border-radius: 5px; color: #ffc702; float: right; padding: 0 5px; height: 23px; line-height: 23px; margin-left: 5px; font-size: 14px; position: absolute; bottom: 10px; right: 10px; background: #fff; }*/
	.order-pin {
		border: 1px #ffc702 solid;
		border-radius: 5px;
		color: #ffc702;
		float: right;
		padding: 0 5px;
		height: 23px;
		line-height: 23px;
		margin-left: 5px;
	}

	.zan-tex {
		clear: both;
		display: block;
		width: 100%;
		color: #565656;
		font-size: 12px;
		height: 30px;
		line-height: 30px;
		text-align: center;
	}

	.ind-bot {
		width: 100%;
		float: left;
		text-align: center;
		height: 50px;
		line-height: 50px;
		font-size: 13px;
		color: #ccc;
		background: #F2F2F2
	}
</style>