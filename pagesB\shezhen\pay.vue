<template>
	<view>
		<block v-if="isload">
			<view class="container">
				<view class="header">
					<view class="flex-y-center">
						<image class="header_icon" src="/static/shezhen/tongue-icon.png"></image>
						<view class="flex1">
							<view class="header_name">AI智能舌诊</view>
							<view class="header_shop">
								<text>专业舌诊分析服务</text>
							</view>
						</view>
					</view>
				</view>
				<view class="page">
					<view class="service-info">
						<view class="service-title">舌诊分析服务</view>
						<view class="service-desc">通过AI智能分析，为您提供专业的舌诊报告</view>
						<view class="service-price">
							<text class="price-label">服务价格：</text>
							<text class="price-value">￥{{servicePrice}}</text>
						</view>
					</view>
					
					<view class="photo-preview" v-if="imageUrl">
						<view class="preview-title">待分析图片</view>
						<image class="preview-image" :src="imageUrl" mode="aspectFit"></image>
					</view>
					
					<view class="info-box">
						<view class="dkdiv-item flex" v-if="userinfo.discount>0 && userinfo.discount<10">
							<text class="f1">{{t('会员')}}折扣({{userinfo.discount*100/100}}折)</text>
							<text class="f2" style="color: #e94745;">-￥{{disprice}}</text>
						</view>
						<view class="dkdiv-item flex-y-center">
							<text class="f1">{{t('优惠券')}}</text>
							<text class="f2" v-if="couponList.length>0" @tap="showCouponList" style="color:#e94745">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+t('优惠券')}}</text>
							<text class="f2" v-else style="color:#999">无可用{{t('优惠券')}}</text>
							<image class="f3" src="/static/img/arrowright.png"></image>
						</view>
						<view class="dkdiv-item flex" v-if="userinfo.scoredkmaxpercent > 0">
							<checkbox-group @change="scoredk" class="flex" style="width:100%">
								<view class="f1">
									<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text style="color:#e94745">{{userinfo.dkmoney*1}}</text> 元</view>
									<view style="font-size:22rpx;color:#999" v-if="userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100">
										最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>
								</view>
								<view class="f2">使用{{t('积分')}}抵扣
									<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
								</view>
							</checkbox-group>
						</view>
						<view class="dkdiv-item flex flex-bt">
							<text class="t1">实付金额:</text>
							<text class="t2">￥{{paymoney}}</text>
						</view>
						
						<view class="op">
							<view class="btn" @tap="topay" :style="{background:t('color1')}">确认支付</view>
						</view>
					</view>
				</view>

				<view v-if="couponvisible" class="popup__container">
					<view class="popup__overlay" @tap.stop="handleClickMask"></view>
					<view class="popup__modal">
						<view class="popup__title">
							<text class="popup__title-text">请选择{{t('优惠券')}}</text>
							<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="handleClickMask" />
						</view>
						<view class="popup__content">
							<couponlist :couponlist="couponList" :choosecoupon="true" :selectedrid="couponrid" @chooseCoupon="chooseCoupon"></couponlist>
						</view>
					</view>
				</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				// 舌诊相关数据
				imageUrl: '', // 舌头图片URL
				servicePrice: 0, // 服务价格
				useFree: 0, // 是否使用免费次数
				
				// 支付相关数据
				userinfo: {},
				couponList: [],
				couponrid: 0,
				couponkey: 0,
				coupontype: 1,
				usescore: 0,
				disprice: 0,
				couponmoney: 0,
				paymoney: 0,
				couponvisible: false
			};
		},

		onLoad: function (opt) {
			this.opt = app.getopts(opt);
			this.imageUrl = opt.image_url || '';
			this.useFree = opt.use_free || 0;
			this.getdata();
		},

		onPullDownRefresh: function () {
			this.getdata();
		},

		methods: {
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiSheZhen/getPayInfo', {
					image_url: that.imageUrl,
					use_free: that.useFree
				}, function(res) {
					that.loading = false;
					if (res.code == 0) {
						app.alert(res.msg, function() {
							app.goback();
						});
						return;
					}
					
					that.servicePrice = res.data.price;
					that.userinfo = res.data.userinfo;
					that.couponList = res.data.couponList || [];
					that.calculatePrice();
					that.loaded();
				});
			},

			// 计算价格
			calculatePrice: function() {
				var that = this;
				var money = that.servicePrice;
				
				// 会员折扣
				if (that.userinfo.discount > 0 && that.userinfo.discount < 10) {
					var disprice = Math.round(money * (1 - 0.1 * that.userinfo.discount) * 100) / 100;
				} else {
					var disprice = 0;
				}
				
				// 优惠券抵扣
				var couponmoney = parseFloat(that.couponmoney);
				
				// 积分抵扣
				if (that.usescore) {
					var dkmoney = parseFloat(that.userinfo.dkmoney);
				} else {
					var dkmoney = 0;
				}
				
				var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent);
				if (dkmoney > 0 && scoredkmaxpercent >= 0 && scoredkmaxpercent < 100 &&
					dkmoney > (money - disprice - couponmoney) * scoredkmaxpercent * 0.01) {
					dkmoney = (money - disprice - couponmoney) * scoredkmaxpercent * 0.01;
				}

				var paymoney = money - disprice - couponmoney - dkmoney;
				if (paymoney < 0) paymoney = 0;
				paymoney = paymoney.toFixed(2);
				
				that.paymoney = paymoney;
				that.disprice = disprice;
			},

			// 积分抵扣
			scoredk: function(e) {
				var usescore = e.detail.value[0];
				if (!usescore) usescore = 0;
				this.usescore = usescore;
				this.calculatePrice();
			},

			// 选择优惠券
			chooseCoupon: function(e) {
				var couponrid = e.rid;
				var couponkey = e.key;

				if (couponrid == this.couponrid) {
					this.couponkey = 0;
					this.couponrid = 0;
					this.coupontype = 1;
					this.couponmoney = 0;
					this.couponvisible = false;
				} else {
					var couponList = this.couponList;
					var couponmoney = couponList[couponkey]['money'];
					var coupontype = couponList[couponkey]['type'];
					
					this.couponkey = couponkey;
					this.couponrid = couponrid;
					this.coupontype = coupontype;
					this.couponmoney = couponmoney;
					this.couponvisible = false;
				}
				this.calculatePrice();
			},

			// 去支付
			topay: function(e) {
				var that = this;
				var imageUrl = that.imageUrl;
				var couponrid = that.couponrid;
				var usescore = that.usescore;
				var useFree = that.useFree;
				
				if (!imageUrl) {
					app.error('请先上传舌头图片');
					return;
				}

				app.post('ApiSheZhen/createPayOrder', {
					image_url: imageUrl,
					use_free: useFree,
					couponrid: couponrid,
					usescore: usescore
				}, function(res) {
					if (res.code == 0) {
						app.error(res.msg);
						return;
					}
					// 跳转到支付页面
					app.goto('/pages/pay/pay?id=' + res.data.payorderid);
				});
			},

			showCouponList: function() {
				this.couponvisible = true;
			},

			handleClickMask: function() {
				this.couponvisible = false;
			},

			getmenuindex: function(e) {
				this.menuindex = e;
			}
		}
	};
</script>

<style>
	page {
		background: #f0f0f0;
	}

	.container {
		position: fixed;
		height: 100%;
		width: 100%;
		overflow: hidden;
		z-index: 5;
	}

	.header {
		position: relative;
		padding: 30rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.header_name {
		font-size: 36rpx;
		color: #fff;
		font-weight: bold;
	}

	.header_icon {
		position: relative;
		height: 85rpx;
		width: 85rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
	}

	.header_shop {
		font-size: 28rpx;
		color: rgba(255,255,255,0.8);
		margin-top: 10rpx;
	}

	.page {
		position: relative;
		padding: 20rpx 30rpx;
		border-radius: 30rpx 30rpx 0 0;
		background: #fff;
		box-sizing: border-box;
		width: 100%;
		height: calc(100% - 185rpx);
		overflow-y: auto;
	}

	.service-info {
		padding: 30rpx 0;
		border-bottom: 1px solid #f0f0f0;
		margin-bottom: 20rpx;
	}

	.service-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.service-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 20rpx;
	}

	.service-price {
		display: flex;
		align-items: center;
	}

	.price-label {
		font-size: 28rpx;
		color: #333;
	}

	.price-value {
		font-size: 36rpx;
		font-weight: bold;
		color: #e94745;
		margin-left: 10rpx;
	}

	.photo-preview {
		margin-bottom: 20rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
	}

	.preview-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 15rpx;
	}

	.preview-image {
		width: 200rpx;
		height: 200rpx;
		border-radius: 10rpx;
	}

	.info-box {
		position: relative;
		background: #fff;
	}

	.dkdiv-item {
		width: 100%;
		padding: 30rpx 0;
		background: #fff;
		border-bottom: 1px #ededed solid;
	}

	.dkdiv-item:last-child {
		border: none;
	}

	.dkdiv-item .f1 {}

	.dkdiv-item .f2 {
		text-align: right;
		flex: 1
	}

	.dkdiv-item .f3 {
		width: 30rpx;
		height: 30rpx;
	}

	.dkdiv-item .t1 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.dkdiv-item .t2 {
		font-size: 36rpx;
		font-weight: bold;
		color: #e94745;
	}

	.op {
		margin-top: 40rpx;
		padding: 0 20rpx;
	}

	.btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
	}

	.popup__container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.popup__overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
	}

	.popup__modal {
		position: relative;
		width: 90%;
		max-height: 70%;
		background: #fff;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.popup__title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.popup__title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.popup__content {
		max-height: 500rpx;
		overflow-y: auto;
		padding: 20rpx;
	}
</style> 