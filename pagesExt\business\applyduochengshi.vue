<template>
  <view>
    <block v-if="isload">
    
  
      <form @submit="subform">
        <view class="apply_box">
          
          <view class="apply_item">
            <view>联系人姓名<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="linkman" :value="info.linkman" placeholder="请填写姓名"></input></view>
          </view>
          <view class="apply_item">
            <view>联系人电话<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="linktel" :value="info.linktel" placeholder="请填写手机号码"></input></view>
          </view>
          
        </view>
        
        <view class="apply_box">
          <view class="apply_item">
            <view>商家名称<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="name" :value="info.name" placeholder="请输入商家名称"></input></view>
          </view>
          <view class="apply_item">
            <view>商家描述<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="desc" :value="info.desc" placeholder="请输入商家描述"></input></view>
          </view>
          <view class="apply_item">
            <view>所在学校<text style="color:red"> *</text></view>
            <view>
              <view class="search-picker" @tap="showCateSearch">
                <view class="picker">{{cateArr[cindex] || '请选择所在学校'}}</view>
              </view>
            </view>
          </view>
          
          <!-- 学校搜索弹窗 -->
          <view v-if="showCateSearchPopup" class="cate-search-mask" @tap="hideCateSearch">
            <view class="cate-search-popup" @tap.stop>
              <view class="cate-search-header">
                <view class="cate-search-input">
                  <input type="text" v-model="cateSearchKeyword" placeholder="搜索学校" @input="searchCate"/>
                </view>
                <view class="cate-search-close" @tap="hideCateSearch">关闭</view>
              </view>
              <view class="cate-search-list">
                <view 
                  v-for="(item, index) in filteredCateList" 
                  :key="index" 
                  class="cate-search-item"
                  @tap="selectCate(index)"
                >
                  {{item.name}}
                </view>
                <view v-if="filteredCateList.length === 0" class="cate-no-result">
                  无匹配结果
                </view>
              </view>
            </view>
          </view>
          
          <view class="apply_item">
            <view>店铺坐标<text style="color:red"> </text></view>
            <view class="flex-y-center"><input type="text" disabled placeholder="请选择坐标" name="zuobiao" :value="latitude ? latitude+','+longitude:''" @tap="locationSelect"></input></view>
          </view>
          <view class="apply_item">
            <view>店铺地址<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="address" :value="address" placeholder="请输入商家详细地址"></input></view>
          </view>
          <input type="text" hidden="true" name="latitude" :value="latitude"></input>
          <input type="text" hidden="true" name="longitude" :value="longitude"></input>
          <view class="apply_item">
            <view>客服电话<text style="color:red"> *</text></view>
            <view class="flex-y-center"><input type="text" name="tel" :value="info.tel" placeholder="请填写客服电话"></input></view>
          </view>
          <view class="apply_item" style="line-height:50rpx"><textarea name="content" placeholder="请输入商家简介" :value="info.content"></textarea></view>
        </view>
        <view class="apply_box">
          <view class="apply_item" style="border-bottom:0"><text>商家头像<text style="color:red"> *</text></text></view>
          <view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
            <view v-for="(item, index) in pic" :key="index" class="layui-imgbox">
              <view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pic"><image src="/static/img/ico-del.png"></image></view>
              <view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
            </view>
            <view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pic" v-if="pic.length==0"></view>
          </view>
          <input type="text" hidden="true" name="pic" :value="pic.join(',')" maxlength="-1"></input>
        </view>
      
      
        
          
        
        <view class="apply_box">
          <view class="apply_item">
            <text>登录账号<text style="color:red"> *</text></text>
            <view class="flex-y-center"><input type="text" name="un" :value="info.un" placeholder="请填写登录账号" autocomplete="off"></input></view>
          </view>
          <view class="apply_item">
            <text>登录密码<text style="color:red"> *</text></text>
            <view class="flex-y-center"><input type="password" name="pwd" :value="info.pwd" placeholder="请填写登录密码" autocomplete="off"></input></view>
          </view>
          <view class="apply_item">
            <text>确认密码<text style="color:red"> *</text></text>
            <view class="flex-y-center"><input type="password" name="repwd" :value="info.repwd" placeholder="请再次填写密码"></input></view>
          </view>
        </view>
        <block v-if="bset.xieyi_show==1">
        <view class="flex-y-center" style="margin-left:20rpx;color:#999" v-if="!info.id || info.status==2">
          <checkbox-group @change="isagreeChange"><label class="flex-y-center"><checkbox value="1" :checked="isagree"></checkbox>阅读并同意</label></checkbox-group>
          <text style="color:#666" @tap="showxieyiFun">《商户入驻协议》</text>
        </view>
        </block>
        <view style="padding:30rpx 0"><button v-if="!info.id || info.status==2" form-type="submit" class="set-btn" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">提交申请</button>
  </view>
      </form>
      <!-- 底部返回按钮 -->
         <view class="content">
           <view class="back-button" @tap="goBack">
             <text class="t1">返回</text>
           </view>
         </view>
      <view id="xieyi" :hidden="!showxieyi" style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)">
        <view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px">
          <view style="overflow:scroll;height:100%;">
            <parse :content="bset.xieyi"/>
          </view>
          <view style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center" @tap="hidexieyi">已阅读并同意</view>
        </view>
      </view>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
  </template>
  <script>
  var app = getApp();
  
  export default {
    data() {
      return {
        opt: {},
        loading: false,
        isload: false,
        menuindex: -1,
        pre_url: app.globalData.pre_url,
        datalist: [],
        pagenum: 1,
        cateArr: [],
        cindex: 0,
        rateArr: [], // 商户费率名称数组
        rateIndex: 0, // 当前选中的费率索引
        selectedRate: null, // 当前选中的费率对象
        isagree: false,
        showxieyi: false,
        pic: [],
        pics: [],
        zhengming: [],
        info: {},
        bset: {},
        latitude: '',
        longitude: '',
        address: '',
        withdrawMethods: ['支付宝', '银行卡', '三方支付'],
        withdrawMethodIndex: 0,
        selectedWithdrawMethod: '支付宝',
        showCateSearchPopup: false,
        cateSearchKeyword: '',
        filteredCateList: []
      };
    },
  
    onLoad: function (opt) {
      this.opt = app.getopts(opt);
      this.getdata();
    },
    onPullDownRefresh: function () {
      this.getdata();
    },
    methods: {
      getdata: function () {
        var that = this;
        that.loading = true;
        app.get('ApiBusiness/apply', {}, function (res) {
          that.loading = false;
          if (res.status == 2) {
            app.alert(res.msg, function () {
              app.goto('/admin/index/index', 'redirect');
            });
            return;
          }
          uni.setNavigationBarTitle({
            title: res.title
          });
          var clist = res.clist;
          var cateArr = [];
          for (var i in clist) {
            cateArr.push(clist[i].name);
          }
       // 处理费率数据，模仿分类的处理方式
              var feilv = res.feilv || [];
              var rateArr = [];
              for (var i in feilv) {
                rateArr.push(feilv[i].name);
              }
              that.feilv = feilv;
              that.rateArr = rateArr;
          
          var pics = res.info ? res.info.pics : '';
          if (pics) {
            pics = pics.split(',');
          } else {
            pics = [];
          }
          var zhengming = res.info ? res.info.zhengming : '';
          if (zhengming) {
            zhengming = zhengming.split(',');
          } else {
            zhengming = [];
          }
          that.clist = res.clist;
          that.bset = res.bset;
          that.info = res.info;
          that.address = res.info.address;
          that.latitude = res.info.latitude;
          that.longitude = res.info.longitude;
          that.cateArr = cateArr;
          that.pic = res.info.logo ? [res.info.logo] : [];
          that.pics = pics;
          that.zhengming = zhengming;
          that.loaded();
        });
      },
      cateChange: function (e) {
        this.cindex = e.detail.value;
      },
      // 费率选择事件处理
        rateChange: function (e) {
          this.rateIndex = e.detail.value;
          this.selectedRate = this.feilv[this.rateIndex]; // 更新选中的费率对象
        },
      withdrawMethodChange: function (e) {
        this.withdrawMethodIndex = e.detail.value;
        this.selectedWithdrawMethod = this.withdrawMethods[this.withdrawMethodIndex];
      },
      locationSelect: function () {
        var that = this;
        uni.chooseLocation({
          success: function (res) {
            that.info.address = res.name;
            that.info.latitude = res.latitude;
            that.info.longitude = res.longitude;
            that.address = res.name;
            that.latitude = res.latitude;
            that.longitude = res.longitude;
          }
        });
      },
      subform: function (e) {
        var that = this;
        var info = e.detail.value;
      
        // 添加 id 到表单数据中
        if (that.info && that.info.id) {
          info.id = that.info.id;  // 将已有的 id 添加到表单数据中
        }
      
        // 验证所有必填项
        if (info.linkman == '') {
          app.error('请填写联系人姓名');
          return false;
        }
        if (info.linktel == '') {
          app.error('请填写联系人电话');
          return false;
        }
        if (info.tel == '') {
          app.error('请填写客服电话');
          return false;
        }
        if (info.name == '') {
          app.error('请填写商家名称');
          return false;
        }
        if (info.address == '') {
          app.error('请填写店铺地址');
          return false;
        }
        if (info.pic == '') {
          app.error('请上传商家头像');
          return false;
        }
       
        if (info.un == '') {
          app.error('请填写登录账号');
          return false;
        }
        if (info.pwd == '') {
          app.error('请填写登录密码');
          return false;
        }
        if (info.pwd.length < 6) {
          app.error('密码不能小于6位');
          return false;
        }
        if (info.repwd != info.pwd) {
          app.error('两次输入的密码不一致');
          return false;
        }
      
       
      
        // 赋值地址、纬度和经度
        info.address = that.address;
        info.latitude = that.latitude;
        info.longitude = that.longitude;
      
      
      
        // 将提现方式和相关字段添加到 info 对象
        info.withdrawMethod = that.selectedWithdrawMethod;
        if (that.selectedWithdrawMethod === '支付宝') {
          info.alipayName = that.info.alipayName;
          info.alipayAccount = that.info.alipayAccount;
        } else if (that.selectedWithdrawMethod === '银行卡') {
          info.bankcarduser = that.info.bankcarduser;
          info.bankCardAccount = that.info.bankCardAccount;
          info.bankName = that.info.bankName;
        } else if (that.selectedWithdrawMethod === '三方支付') {
          info.jialianMerchantNumber = that.info.jialianMerchantNumber;
          info.jialianTerminalNumber = that.info.jialianTerminalNumber;
        }
      
        // 检查是否同意协议（如果需要）
        if (that.bset.xieyi_show == 1 && !that.isagree) {
          app.error('请先阅读并同意商户入驻协议');
          return false;
        }
      
        // 提交表单数据到服务器
        app.showLoading('提交中');
        app.post("ApiBusiness/apply", { info: info }, function (res) {
          app.showLoading(false);
          if (res.status == 1) {
            app.success(res.msg);
            setTimeout(function () {
              if (res.after_register_url) {
                app.goto(res.after_register_url);
              } else {
                app.goto(app.globalData.indexurl);
              }
            }, 1500);
          } else {
            app.error(res.msg);
          }
        });
      },
  
      isagreeChange: function (e) {
        var val = e.detail.value;
        this.isagree = val.length > 0;
      },
      showxieyiFun: function () {
        this.showxieyi = true;
      },
      hidexieyi: function () {
        this.showxieyi = false;
        this.isagree = true;
      },
      uploadimg: function (e) {
        var that = this;
        var field = e.currentTarget.dataset.field;
        var pics = that[field];
        if (!pics) pics = [];
        app.chooseImage(function (urls) {
          for (var i = 0; i < urls.length; i++) {
            pics.push(urls[i]);
          }
          if (field == 'pic') that.pic = pics;
          if (field == 'pics') that.pics = pics;
          if (field == 'zhengming') that.zhengming = pics;
        }, 1);
      },
    // 返回功能
      goBack: function () {
        uni.navigateBack({
          delta: 1
        });
      },
      removeimg: function (e) {
        var that = this;
        var index = e.currentTarget.dataset.index;
        var field = e.currentTarget.dataset.field;
        if (field == 'pic') {
          var pics = that.pic;
          pics.splice(index, 1);
          that.pic = pics;
        } else if (field == 'pics') {
          var pics = that.pics;
          pics.splice(index, 1);
          that.pics = pics;
        } else if (field == 'zhengming') {
          var pics = that.zhengming;
          pics.splice(index, 1);
          that.zhengming = pics;
        }
      },
      showCateSearch: function () {
        this.filteredCateList = this.clist || [];
        this.cateSearchKeyword = '';
        this.showCateSearchPopup = true;
      },
      hideCateSearch: function () {
        this.showCateSearchPopup = false;
      },
      searchCate: function () {
        var that = this;
        var keyword = that.cateSearchKeyword.toLowerCase();
        that.filteredCateList = (that.clist || []).filter(function (item) {
          return item.name.toLowerCase().indexOf(keyword) !== -1;
        });
      },
      selectCate: function (index) {
        var item = this.filteredCateList[index];
        var originalIndex = this.clist.findIndex(function(cateItem) {
          return cateItem.id === item.id;
        });
        if (originalIndex !== -1) {
          this.cindex = originalIndex;
        }
        this.hideCateSearch();
      }
    }
  };
  </script>
  
  <style>
  radio{transform: scale(0.6);}
  checkbox{transform: scale(0.6);}
  .apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
  .apply_title { background: #fff}
  .apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}
  .apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}
  
  .apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }
  .apply_box .apply_item:last-child{ border:none}
  .apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}
  .apply_item input::placeholder{ color:#999999}
  .apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}
  .apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }
  .apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }
  .set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
  
  .layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
  .layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}
  .layui-imgbox-close image{width:100%;height:100%}
  .layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
  .layui-imgbox-img>image{max-width:100%;}
  .layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
  .uploadbtn{position:relative;height:200rpx;width:200rpx}
  
  /* 返回按钮样式 */
  .back-button {
    width: 90%;
    background: #b60000;
    color: #fff;
    text-align: center;
    height: 96rpx;
    line-height: 96rpx;
    border-radius: 50px;
    margin-top: 0rpx;
    margin: auto;
  }
  
  .back-button .t1 {
    font-size: 30rpx;
    color: #fff;
  }

  /* 学校搜索样式 */
  .search-picker {
    text-align: right;
  }

  .cate-search-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cate-search-popup {
    position: relative;
    width: 80%;
    max-height: 70%;
    background-color: #fff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    animation: popup 0.3s ease;
  }

  @keyframes popup {
    from {
      transform: scale(0.8);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  .cate-search-header {
    display: flex;
    padding: 20rpx;
    border-bottom: 1rpx solid #eee;
    background-color: #fff;
  }
  .cate-search-input {
    flex: 1;
    background: #f5f5f5;
    border-radius: 36rpx;
    padding: 10rpx 20rpx;
    margin-right: 20rpx;
  }
  .cate-search-input input {
    height: 60rpx;
    width: 100%;
  }
  .cate-search-close {
    width: 100rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    color: #666;
  }
  .cate-search-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 30rpx;
    max-height: 600rpx;
  }
  .cate-search-item {
    padding: 30rpx 0;
    border-bottom: 1rpx solid #eee;
    font-size: 30rpx;
  }
  .cate-no-result {
    padding: 50rpx 0;
    text-align: center;
    color: #999;
  }
  </style>
  