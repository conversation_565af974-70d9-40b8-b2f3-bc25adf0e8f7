<template>
	<view class="dp-kecheng-boutique">
		<!-- 标题栏 -->
		<view class="header" :style="dynamicHeaderStyle">
			<view class="title" :style="dynamicTitleTextStyle">
				<view class="title-content-area">
					<view style="display: flex; align-items: center;">
						<view class="configurable-title-left-border"
							  v-if="params.showTitleLeftBorder === '1'"
							  :style="dynamicConfigurableTitleLeftBorderStyle">
						</view>
						<text>{{ headerTitleText }}</text>
					</view>
					<view class="configurable-title-bottom-border"
						  v-if="params.showTitleBottomBorder === '1'"
						  :style="dynamicConfigurableTitleBottomBorderStyle">
					</view>
				</view>
			</view>
			<view class="refresh" @click="refreshData" :style="dynamicRefreshTextStyle">
				<view class="refresh-container">
					<text>{{ headerRefreshText }}</text>
					<text class="iconfont icon-refresh">⟳</text>
				</view>
			</view>
		</view>
	
		<!-- Grid Items -->
		<view class="grid-container">
			<view v-for="(item, index) in boutiqueItemsData" :key="item[idfield] || index" class="grid-item" @click="handleBoutiqueItemClick(index)">
				<view class="product-pic-boutique" style="padding-bottom: 60%;">
					<image class="image-boutique" :src="item.pic" mode="aspectFill" style="border-radius: 12px;"/>
				</view>
				<view class="product-info-boutique">
					<view class="name-boutique" v-if="params.showname == '1'">{{item.name}}</view>
				</view>
			</view>
		</view>
	</view>
</template>
	
<script>
export default {
	props: {
		params: {
			type: Object,
			default: () => ({})
		},
		data: {
			type: Array,
			default: () => []
		},
		idfield: {
			type: String,
			default: 'proid'
		},
		menuindex: {
			default: -1
		},
		sysset: {
			type: Object,
			default: () => ({})
		},
		saleimg: {
			type: String,
			default: ''
		},
	},
	computed: {
		headerTitleText() {
			return this.params.titleText || '精品课程';
		},
		headerRefreshText() {
			return this.params.refreshText || '换一换';
		},
		dynamicHeaderStyle() {
			const styles = {};
			const params = this.params;
			if (params.titleBgType === 'solid' && params.titleBgSolid) {
				styles.background = params.titleBgSolid;
				styles.padding = '16rpx 20rpx'; 
				styles.borderRadius = '12rpx 12rpx 0 0';
			} else if (params.titleBgType === 'gradient' && params.titleBgGradientStart && params.titleBgGradientEnd) {
				const direction = params.titleBgGradientDirection || 'to right';
				styles.background = `linear-gradient(${direction}, ${params.titleBgGradientStart}, ${params.titleBgGradientEnd})`;
				styles.padding = '16rpx 20rpx';
				styles.borderRadius = '12rpx 12rpx 0 0';
			} else if (params.titleBgType === 'none') {
				styles.background = 'transparent';
				styles.padding = '0';
				styles.borderRadius = '0';
			}
			return styles;
		},
		dynamicTitleTextStyle() {
			const styles = {};
			if (this.params.titleTextColor) {
				styles.color = this.params.titleTextColor;
			}
			return styles;
		},
		dynamicRefreshTextStyle() {
			const styles = {};
			if (this.params.refreshTextColor) {
				styles.color = this.params.refreshTextColor;
			}
			return styles;
		},
		dynamicConfigurableTitleLeftBorderStyle() {
			return {
				backgroundColor: this.params.titleLeftBorderColor || '#FF8C00',
			};
		},
		dynamicConfigurableTitleBottomBorderStyle() {
			return {
				backgroundColor: this.params.titleBottomBorderColor || '#FF8C00',
			};
		},
		boutiqueItemsData() {
			const numToShow = this.params.proshownum || 4;
			return this.data && this.data.length > 0 ? this.data.slice(0, numToShow) : [];
		}
	},
	methods: {
		handleBoutiqueItemClick(index) {
			const item = this.boutiqueItemsData[index];
			console.log('[DEBUG] dp-kecheng-boutique: handleBoutiqueItemClick - Index:', index, 'Resolved Item:', JSON.stringify(item));
			if (item) {
				this.gotoProduct(item);
			} else {
				console.error('[ERROR] dp-kecheng-boutique: handleBoutiqueItemClick - Item not found at index:', index, 'Current boutiqueItemsData:', JSON.stringify(this.boutiqueItemsData));
			}
		},
		gotoProduct(item) {
			if (item && item[this.idfield]) {
				const itemId = item[this.idfield];
				const targetUrl = '/activity/kecheng/product?id=' + itemId;
				console.log('[DEBUG] dp-kecheng-boutique: Navigating. Item ID:', itemId, 'URL:', targetUrl, 'Item:', JSON.stringify(item));
				uni.navigateTo({
					url: targetUrl,
					success: function(res) {
						console.log('[DEBUG] dp-kecheng-boutique: Navigation success. URL:', targetUrl, res);
					},
					fail: function(err) {
						console.error('[ERROR] dp-kecheng-boutique: Navigation failed. URL:', targetUrl, 'Error:', err);
						// uni.showToast({ title: '页面跳转失败: ' + (err.errMsg || '未知错误'), icon: 'none', duration: 3000 });
					}
				});
			} else {
				console.warn('[WARN] dp-kecheng-boutique: gotoProduct - invalid item or idfield. Item keys:', item ? Object.keys(item) : 'null item', 'idfield:', this.idfield, 'ID Value:', item ? item[this.idfield] : 'N/A', 'Full item:', JSON.stringify(item));
			}
		},
		refreshData() {
				// 通知父组件刷新数据
				console.log('dp-kecheng-boutique: 触发换一换事件');
				
				// 在不同环境下使用不同的刷新策略
				// #ifdef MP-WEIXIN
				// 小程序环境，使用更简单的事件参数格式
				console.log('小程序环境：触发刷新');
				this.$emit('getdata', {refreshType: 'boutique'});
				// #endif
				
				// #ifndef MP-WEIXIN
				// 其他环境
				this.$emit('getdata');
				// #endif
			},
		t(key) { 
			if (key === 'color1') return this.params.color1 || '#FF5000';
			return '';
		}
	}
}
</script>
	
<style scoped>
.dp-kecheng-boutique {
	display: flex;
	flex-direction: column;
	width: 100%;
	border-radius: 24rpx;
	padding-bottom: 16rpx; /* 底部内边距，顶部由header控制 */
	/* box-shadow: 0 8rpx 16rpx rgba(100, 100, 100, 0.08); */
	overflow: hidden; /* 确保圆角和阴影正确应用 */
	background-image: linear-gradient(to bottom, #D7EEFE, #fff);
}

/* 标题栏样式 - 应用推荐样式的特性 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 24rpx 16rpx;
	position: relative; 
}

.title {
	font-size: 32rpx;
	font-weight: bold;
	color: #267DFF; 
	display: flex;
	align-items: center; 
}

.title-content-area {
	display: flex;
	flex-direction: column;
	align-items: flex-start; /* Align bottom border to the left */
}

.configurable-title-left-border {
	width: 4px; 
	height: 1em; 
	margin-right: 8px; 
	display: inline-block;
}

.configurable-title-bottom-border {
	height: 3px; 
	width: 25px; 
	margin-top: 4px; 
}

.refresh {
	display: flex;
	align-items: center;
	font-size: 26rpx;
}

.refresh-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	background-color: transparent;
	border: 1px solid rgba(38, 125, 255, 0.3);
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	transition: all 0.2s ease;
}

.refresh-container:active {
	transform: scale(0.95);
	box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.refresh-container text {
	color: #267DFF;
	font-size: 24rpx;
	font-weight: 500;
}

.refresh-container .icon-refresh {
	margin-left: 8rpx;
	font-size: 24rpx;
	font-weight: bold;
}

/* Grid Container Styles */
.grid-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 20rpx; /* 标题栏和网格之间的间距 */
	padding: 0 16rpx; /* 网格区域的左右内边距 */
}

.grid-item { /* .item-card 类名似乎已在模板中移除，统一用 .grid-item */
	width: calc(50% - 10rpx); /* 两列布局，减去间距 */
	background-color: #fff; /* 卡片背景色 */
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
/* 	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06); */
	display: flex;
	flex-direction: column;
}

.product-pic-boutique {
	width: 100%;
	height: 0;
	padding-bottom: 66.66%; /* 图片宽高比 3:2 左右 */
	position: relative;
	overflow: hidden;
	/* border-radius: 16rpx 16rpx 0 0; 若grid-item已圆角，此处可省略或按需保留 */
}

.image-boutique {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.product-info-boutique {
	padding: 16rpx;
	text-align: left; /* 课程名称左对齐 */
}

.name-boutique {
	color: #333;
	font-size: 28rpx;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2; /* 最多显示两行 */
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>