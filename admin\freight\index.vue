<template>
  <view class="container">
    <dd-tab :itemdata="[
      '全部('+(countall || 0)+')',
      '已启用('+(count1 || 0)+')',
      '已停用('+(count0 || 0)+')'
    ]" 
            :itemst="['all','1','0']" 
            :st="st" 
            :isfixed="true" 
            @changetab="changetab">
    </dd-tab>
    <view style="width:100%;height:100rpx"></view>
    
    <!-- 搜索框和添加按钮区域 -->
    <view class="search-header">
      <view class="topsearch flex-y-center">
        <view class="f1 flex-y-center">
          <image class="img" src="/static/img/search_ico.png"></image>
          <input :value="keyword" 
                 placeholder="输入模板名称搜索" 
                 placeholder-style="font-size:24rpx;color:#C2C2C2" 
                 @confirm="searchConfirm">
          </input>
        </view>
      </view>
      <view class="add-btn" 
            :style="{background:t('color1')}" 
            @tap="goto" 
            data-url="edit">
        <text class="icon">+</text>
        <text>新增模板</text>
      </view>
    </view>

    <!-- 模板列表 -->
    <view class="freight-content">
      <block v-for="(item, index) in datalist" :key="index">
        <view class="freight-box">
          <view class="header">
            <view class="name">{{item.name}}</view>
            <view class="tag" :class="item.status ? 'active' : ''">
              {{item.status ? '已启用' : '已停用'}}
            </view>
          </view>
          
          <view class="content">
            <view class="info-row">
              <text class="label">计费方式：</text>
              <text class="value">{{item.type === 1 ? '按重量' : '按件数'}}</text>
            </view>
            <view class="info-row">
              <text class="label">配送区域：</text>
              <text class="value">{{item.areas || '全国'}}</text>
            </view>
            <view class="info-row" v-if="item.type === 1">
              <text class="label">首重：</text>
              <text class="value">{{getPriceData(item.pricedata, 'first')}}kg，¥{{getPriceData(item.pricedata, 'first_fee')}}</text>
            </view>
            <view class="info-row" v-if="item.type === 1">
              <text class="label">续重：</text>
              <text class="value">{{getPriceData(item.pricedata, 'next')}}kg，¥{{getPriceData(item.pricedata, 'next_fee')}}</text>
            </view>
            <view class="info-row" v-if="item.type === 2">
              <text class="label">首件：</text>
              <text class="value">{{getPriceData(item.pricedata, 'first')}}件，¥{{getPriceData(item.pricedata, 'first_fee')}}</text>
            </view>
            <view class="info-row" v-if="item.type === 2">
              <text class="label">续件：</text>
              <text class="value">{{getPriceData(item.pricedata, 'next')}}件，¥{{getPriceData(item.pricedata, 'next_fee')}}</text>
            </view>
          </view>

          <view class="op">
            <text style="color:red" class="flex1" v-if="!item.status">已停用</text>
            <text style="color:green" class="flex1" v-if="item.status">已启用</text>
            <view class="btn1" 
                  :style="{background:t('color1')}" 
                  @tap="setStatus" 
                  data-status="1" 
                  :data-id="item.id" 
                  v-if="!item.status">启用</view>
            <view class="btn1" 
                  :style="{background:t('color2')}" 
                  @tap="setStatus" 
                  data-status="0" 
                  :data-id="item.id" 
                  v-else>停用</view>
            <view @tap="goto" 
                  :data-url="'edit?id='+item.id" 
                  class="btn2">编辑</view>
            <view class="btn2" 
                  @tap="deleteTemplate" 
                  :data-id="item.id">删除</view>
          </view>
        </view>
      </block>
    </view>

    <loading v-if="loading"></loading>
    <nomore v-if="nomore"></nomore>
    <nodata v-if="nodata"></nodata>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      st: 'all',
      datalist: [],
      pagenum: 1,
      nodata: false,
      nomore: false,
      loading: false,
      count0: 0,
      count1: 0, 
      countall: 0,
      keyword: ''
    }
  },

  onLoad(opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },

  onPullDownRefresh() {
    this.getdata();
  },

  onReachBottom() {
    if(!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },

  onNavigationBarSearchInputConfirmed(e) {
    this.searchConfirm({detail:{value:e.text}});
  },

  methods: {
    changetab(st) {
      this.st = st;
      this.getdata();
    },

    getdata(loadmore) {
      if(!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
      }

      let that = this;
      let pagenum = that.pagenum;
      that.nodata = false;
      that.nomore = false;
      that.loading = true;

      app.post('ApiAdminProduct/freightList', {
        keyword: that.keyword,
        pagenum: pagenum,
        st: that.st
      }, function(res) {
        that.loading = false;
        let data = res.datalist;
        
        if(pagenum == 1) {
          that.countall = res.countall;
          that.count0 = res.count0;
          that.count1 = res.count1;
          that.datalist = data;
          if(data.length == 0) {
            that.nodata = true;
          }
          that.loaded();
        } else {
          if(data.length == 0) {
            that.nomore = true;
          } else {
            let datalist = that.datalist;
            let newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },

    deleteTemplate(e) {
      let that = this;
      let id = e.currentTarget.dataset.id;
      app.confirm('确定要删除该运费模板吗?', function() {
        app.post('ApiAdminProduct/freightDel', {
          id: id
        }, function(res) {
          if(res.status == 1) {
            app.success(res.msg);
            that.getdata();
          } else {
            app.error(res.msg);
          }
        });
      });
    },

    setStatus(e) {
      let that = this;
      let id = e.currentTarget.dataset.id;
      let status = e.currentTarget.dataset.status;
      app.confirm('确定要'+(status == 0 ? '停用' : '启用')+'该模板吗?', function() {
        app.post('ApiAdminProduct/freightStatus', {
          status: status,
          id: id
        }, function(res) {
          if(res.status == 1) {
            app.success(res.msg);
            that.getdata();
          } else {
            app.error(res.msg);
          }
        });
      });
    },

    searchConfirm(e) {
      this.keyword = e.detail.value;
      this.getdata(false);
    },

    getPriceData(pricedata, type) {
      try {
        const data = typeof pricedata === 'string' ? JSON.parse(pricedata) : pricedata;
        const defaultRule = data.find(item => item.region === '全国(默认运费)') || data[0];
        
        if (!defaultRule) return '0';

        // 获取值
        let value = '0';
        switch(type) {
          case 'first':
            value = defaultRule.fristweight || '0';
            break;
          case 'first_fee':
            value = defaultRule.fristprice || '0';
            break;
          case 'next':
            value = defaultRule.secondweight || '0';
            break;
          case 'next_fee':
            value = defaultRule.secondprice || '0';
            break;
        }

        // 如果是重量，需要转换为kg
        if (type === 'first' || type === 'next') {
          value = (parseFloat(value) / 1000).toFixed(2);
        }

        return value;
      } catch (e) {
        console.error('解析运费数据失败:', e);
        return '0';
      }
    },

  
  }
}
</script>

<style>
.container {
  width: 100%;
}

.search-header {
  width: 94%;
  margin: 0 auto 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.topsearch {
  flex: 1;
  background: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  color: #fff;
  font-size: 28rpx;
}

.add-btn .icon {
  font-size: 32rpx;
  margin-right: 8rpx;
  font-weight: bold;
}

.topsearch .f1 {
  background: #f7f7f7;
  border-radius: 30rpx;
  padding: 16rpx 24rpx;
}

.topsearch input {
  flex: 1;
  font-size: 26rpx;
}

.topsearch .img {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

/* 配送模板列表样式 */
.freight-content {
  padding: 20rpx;
}

.freight-box {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.freight-box .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #eee;
}

.freight-box .name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.freight-box .tag {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  color: #999;
}

.freight-box .tag.active {
  background: #e1f3d8;
  color: #67c23a;
}

.freight-box .content {
  padding: 20rpx 0;
}

.freight-box .info-row {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 40rpx;
}

.freight-box .info-row .label {
  color: #666;
  width: 160rpx;
}

.freight-box .info-row .value {
  color: #333;
  flex: 1;
}

.freight-box .op {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid #eee;
}

.freight-box .op .flex1 {
  flex: 1;
  font-size: 28rpx;
}

.freight-box .btn1,
.freight-box .btn2 {
  margin-left: 20rpx;
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.freight-box .btn1 {
  color: #fff;
}

.freight-box .btn2 {
  background: #f5f5f5;
  color: #666;
}

/* 加载状态样式 */
.loading,
.nomore,
.nodata {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 28rpx;
}
</style>