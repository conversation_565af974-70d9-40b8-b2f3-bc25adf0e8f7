<template>
	<view v-if="isload">
		<view class="banner"></view>
		<view class="page">
			<view class="data" :style="{'background':info.bgpic ? 'url('+info.bgpic+')' : '#fff','background-size':'100%'}">
				<view class="data_info">
					<img class="data_head" :src="info.headimg" alt=""/>
					<view>
						<view class="data_name">{{info.realname}}</view>
						<view class="data_text" v-if="info.touxian1">{{info.touxian1}}</view>
						<view class="data_text" v-if="info.touxian2">{{info.touxian2}}</view>
						<view class="data_text" v-if="info.touxian3">{{info.touxian3}}</view>
					</view>
				</view>
				<img class="data_tag" src="../static/images/card_write.png" alt="" @tap="goto" data-url="edit" v-if="info.mid == mid"/>

				<view class="data_list" v-for="(item,index) in field_list2">
					<img v-if="index == 'tel'" src="../static/images/tel.png" alt=""/>
					<img v-else-if="index == 'weixin'" src="../static/images/weixin.png" alt=""/>
					<img v-else-if="index == 'address'" src="../static/images/address.png" alt=""/>
					<img v-else :src="item.icon" alt=""/>
					{{info[index]}}
				</view>
			</view>
			
			<view class="module">
				<view class="module_item" @tap="addfavorite" v-if="mid != info.mid">
					<img class="module_img" src="../static/images/card_m0.png" alt=""/>
					<view class="module_text">存名片夹</view>
				</view>
				<view class="module_item" @tap="goto" data-url="favorite">
					<img class="module_img" src="../static/images/card_m1.png" alt=""/>
					<view class="module_text">{{mid != info.mid ? '我的名片夹' : '名片夹'}}</view>
				</view>
				<view class="module_item" @tap="shareapp" v-if="getplatform() == 'app'">
					<image class="module_img" src="../static/images/card_m2.png"/>
					<view class="module_text">分享名片</view>
				</view>
				<view class="module_item" @tap="sharemp" v-else-if="getplatform() == 'mp'">
					<image class="module_img" src="../static/images/card_m2.png"/>
					<view class="module_text">分享名片</view>
				</view>
				<view class="module_item" @tap="sharemp" v-else-if="getplatform() == 'h5'">
					<image class="module_img" src="../static/images/card_m2.png"/>
					<view class="module_text">分享名片</view>
				</view>
				<button class="module_item" open-type="share" v-else>
					<image class="module_img" src="../static/images/card_m2.png"/>
					<view class="module_text">分享名片</view>
				</button>

				<view class="module_item" @tap="goto" :data-url="'readlog?id='+info.id" v-if="mid == info.mid">
					<img class="module_img" src="../static/images/card_m3.png" alt=""/>
					<view class="module_text">谁看过</view>
				</view>
				<view class="module_item" @tap="goto" :data-url="'favoritelog?id='+info.id" v-if="mid == info.mid">
					<img class="module_img" src="../static/images/card_m4.png" alt=""/>
					<view class="module_text">谁收藏了</view>
				</view>
				<view class="module_item" @tap="addPhoneContact" v-if="getplatform() != 'h5' && getplatform() != 'mp'">
					<img class="module_img" src="../static/images/card_m5.png" alt=""/>
					<view class="module_text">存通讯录</view>
				</view>
			</view>
			
			<view class="list">
				<view class="list_title">
					联系方式
				</view>
				<view class="list_item" v-for="(item,index,idx) in field_list">
					<img class="list_img" :src="item.icon" alt=""/>
					<view class="list_lable">{{item.name}}</view>
					<view class="list_value" v-if="index == 'tel'" @tap="goto" :data-url="'tel:'+info[index]">{{info[index]}}</view>
					<view class="list_value" v-else-if="index == 'address' && info.longitude" @tap="openLocation" :data-latitude="info.latitude" :data-longitude="info.longitude" :data-address="info.address">{{info[index]}}</view>
					<view class="list_value" v-else @tap="fuzhi" :data-content="info[index]"><text user-select="true" selectable="true">{{info[index]}}</text></view>
				</view>
			</view>
			<view class="person">
				<view class="person_title">
					<view></view>
					<text>个人简介</text>
					<view></view>
				</view>
				<dp :pagecontent="pagecontent"></dp>
			</view>
			
			<view class="opt">
				<view class="opt_module">
					<view class="opt_btn" @tap="goto" data-url="/pages/index/index" data-opentype="reLaunch">回首页</view>
					<view class="opt_btn" @tap="goto" data-url="index" v-if="viewmymp">查看我的名片</view>
					<view class="opt_btn" @tap="goto" data-url="edit" v-else>{{mid == info.mid ? '编辑名片' : '创建自己的名片'}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
			isload:false,
			loading:false,
			pre_url:app.globalData.pre_url,
      info:{},
			pagecontent:[],
			field_list:[],
			field_list2:[],
			test:'',
			sharetitle:'',
			sharedesc:'',
			mid:'',
			viewmymp:false,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onShareAppMessage:function(){
		return this._sharewx({title:this.sharetitle,link:'/pagesExt/mingpian/index?scene=id_'+this.info.id+'-pid_'+(this.info.mid || app.globalData.mid)});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.sharetitle,link:'/pagesExt/mingpian/index?scene=id_'+this.info.id+'-pid_'+(this.info.mid || app.globalData.mid)});
		var query = (sharewxdata.path).split('?')[1];
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  methods: {
		getdata:function(){
			var that = this;
			var id = that.opt.id ? that.opt.id : '';
			that.loading = true;
			app.get('ApiMingpian/index',{id:id,scene:app.globalData.scene}, function (res) {
				that.loading = false;
				that.info = res.info;
				that.mid = res.mid;
				that.viewmymp = res.viewmymp;
				that.field_list = res.field_list;
				that.field_list2 = res.field_list2;
				that.pagecontent = res.pagecontent;
				var sharedesc = that.info.realname;
				if(that.info.touxian1) sharedesc += ' '+that.info.touxian1;
				if(that.info.touxian2) sharedesc += ','+that.info.touxian2;
				if(that.info.touxian3) sharedesc += ','+that.info.touxian3;
				that.sharedesc = sharedesc;
				that.sharetitle = that.info.sharetitle || '您好，这是我的名片，望惠存！';
				var sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pagesExt/mingpian/index?scene=id_'+that.info.id+'-pid_' + (that.info.mid || app.globalData.mid);
				that.loaded({title:that.sharetitle,pic:that.info.headimg,desc:sharedesc,link:sharelink});
			});
		},
		favorite:function(){
			var that = this;
			if(app.globalData.mid == that.info.mid){
				app.goto('favorite');
			}else{
				uni.showActionSheet({
					itemList: ['存入名片夹','查看名片夹'],
					success: function(res) {
						console.log(res.tapIndex)
						if (res.tapIndex == 0) {
							that.addfavorite();
						}else{
							app.goto('favorite');
						}
					}
				});
			}
		},
		addfavorite:function(){
			var that = this;
			app.showLoading('保存中');
			app.post('ApiMingpian/addfavorite',{id:that.info.id},function(res){
				if(res.status== 1){
					app.success(res.msg);
				}else{
					app.error(res.msg);
				}
			})
		},
		addPhoneContact:function(){
			var that = this;
			uni.addPhoneContact({
				firstName: that.info.realname,
				remark: that.info.touxian1,
				mobilePhoneNumber: that.info.tel,
				success: function () {
					app.success('添加成功');
				},
				fail: function (res) {
					console.log(res)
					app.error('添加失败,请手动添加');
				}
			});
		},
		fuzhi:function(e){
			var content = e.currentTarget.dataset.content;
			var that = this;
			uni.setClipboardData({
				data: content,
				success: function () {
					app.success('已复制到剪贴板');
				},
				fail:function(err){
					console.log(err)
					app.error('请长按文本内容复制');
				}
			});
		},	
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			that.sharetypevisible = false;
			uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = that.info.sharetitle || '您好，这是我的名片，望惠存！';
						sharedata.summary = that.sharedesc;
						sharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pagesExt/mingpian/index?scene=id_'+that.info.id+'-pid_' + (that.info.mid || app.globalData.mid);
						sharedata.imageUrl = that.info.headimg;
						var sharelist = app.globalData.initdata.sharelist;
						if(sharelist){
							for(var i=0;i<sharelist.length;i++){
								if(sharelist[i]['indexurl'] == '/pagesExt/mingpian/index'){
									sharedata.title = sharelist[i].title;
									sharedata.summary = sharelist[i].desc;
									sharedata.imageUrl = sharelist[i].pic;
									if(sharelist[i].url){
										var sharelink = sharelist[i].url;
										if(sharelink.indexOf('/') === 0){
											sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;
										}
										if(app.globalData.mid>0){
											 sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;
										}
										sharedata.href = sharelink;
									}
								}
							}
						}
						uni.share(sharedata);
					}
        }
      });
		},
		openLocation:function(e){
			//console.log(e)
			var latitude = parseFloat(e.currentTarget.dataset.latitude)
			var longitude = parseFloat(e.currentTarget.dataset.longitude)
			var address = e.currentTarget.dataset.address
			uni.openLocation({
			 latitude:latitude,
			 longitude:longitude,
			 name:address,
			 scale: 13
		 })		
		},
	}
}
</script>

<style scoped>
	page{
		background: #F4F5F7;
	}
</style>
<style>
	.banner{
		position: absolute;
		width: 100%;
		height: 300rpx;
		background: #4a8aff;
		/*border-radius: 0 0 20% 20%;*/
	}
	.page{
		padding: 70rpx 30rpx 0 30rpx;
	}
	.data{
		position: relative;
		background: #FFFFFF;
		padding: 40rpx 0 0 40rpx;
		border-radius: 12rpx;
		box-shadow:2px 0px 10px rgba(0,0,0,0.5);
		height:520rpx;
	}
	.data_info{
		display: flex;
		align-items: center;
		padding: 0 0 45rpx 0;
		border-bottom: 1rpx solid #eee;
		margin-bottom: 20rpx;
	}
	.data_head{
		width: 172rpx;
		height: 172rpx;
		border-radius: 50%;
		margin-right: 40rpx;
	}
	.data_name{
		font-size: 36rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #121212;
		padding-bottom: 10rpx;
	}
	.data_text{
		font-size: 24rpx;
		font-family: Alibaba PuHuiTi;
		font-weight: 400;
		color: #545556;
		padding-top: 15rpx;
	}
	.data_list{
		padding: 9rpx 0;
		font-size: 28rpx;
		font-family: Alibaba PuHuiTi;
		font-weight: 400;
		color: #8B9198;
		display: flex;
	}
	.data_list img{
		height: 30rpx;
		width: 30rpx;
		margin: 5rpx 30rpx 0 0;
		flex-shrink:0;
	}
	.data_tag{
		position: absolute;
		top: 50rpx;
		right: 50rpx;
		height: 60rpx;
		width: 60rpx;
	}
	
	.module{
		position: relative;
		padding: 30rpx 10rpx;
		margin: 25rpx 0 0 0;
		background: #fff;
		display: flex;
		border-radius: 12rpx;
		box-shadow:2px 0px 10px #ccc;
	}
	.module_item{
		flex: 1;
	}
	.module_img{
		height: 72rpx;
		width: 72rpx;
		display: block;
		margin: 0 auto;
	}
	.module_text{
		font-size: 24rpx;
		text-align: center;
		font-family: Alibaba PuHuiTi;
		font-weight: 400;
		color: #8B9198;
		margin-top: 20rpx;
		line-height:30rpx;
	}
	
	.list{
		position: relative;
		padding: 40rpx;
		margin: 25rpx 0 0 0;
		background: #fff;
		border-radius: 12rpx;
		box-shadow:2px 0px 10px #ccc;
	}
	.list_title{
		font-size: 32rpx;
		font-family: Source Han Sans CN;
		font-weight: 500;
		color: #121212;
		padding-bottom:20rpx;
	}
	.list_item{
		display: flex;
		align-items: center;
		padding:22rpx 0;
	}
	.list_item:active{background:#f5f5f5}
	.list_img{
		height: 48rpx;
		width: 48rpx;
		flex-shrink: 0;
	}
	.list_lable{
		font-size: 30rpx;
		font-family: Alibaba PuHuiTi;
		font-weight: 500;
		color: #353535;
		flex-shrink: 0;
		padding: 0 50rpx 0 25rpx;
		width:180rpx;
	}
	.list_value{
		font-size: 28rpx;
		font-family: Alibaba PuHuiTi;
		font-weight: 400;
		color: #232323;
		min-width: 0;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.person{
		position: relative;
		padding: 40rpx;
		margin: 25rpx 0 0 0;
		background: #fff;
		border-radius: 12rpx;
		box-shadow:2px 0px 10px #ccc;
	}
	.person_title{
		font-size: 32rpx;
		font-family: Source Han Sans CN;
		font-weight: 500;
		color: #121212;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.person_title view{
		width: 200rpx;
		height: 1rpx;
		background: #EEEEEE;
	}
	.person_data{
		margin-top: 30rpx;
	}
	.opt{
		position: relative;
		width: 100%;
		height: 190rpx;
	}
	.opt_module{
		width: 100%;
		height: 190rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
	}
	.opt_btn{
		width: 200rpx;
		height: 108rpx;
		background: #F2F6FF;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #4A84FF;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 54rpx;
		box-shadow: 0 0 10rpx #e0e0e0;
	}
	.opt_btn:last-child{
		width: 450rpx;
		height: 108rpx;
		background: #4A84FF;
		border-radius: 54rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
	}
</style>
