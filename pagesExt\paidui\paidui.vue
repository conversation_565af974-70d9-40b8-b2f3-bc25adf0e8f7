<template>
<view class="container">
	<block v-if="isload">
		<view class="banner" :style="{background:t('color1')}">
			<image :src="userinfo.headimg"/>
			<view class="info" style="line-height: 120rpx;padding-top: 0;">
				 <text class="nickname" style="color: azure;">{{userinfo.nickname}}</text>
				
			</view>
		</view>
			<view class="contentdata">
				<view class="order">
					<view class="head">
						<text class="f1">排队数据看板</text>
						<view class="f2" @tap="topay">
							<text>红包转至余额</text>
							<image src="/static/img/arrowright.png"></image>
						</view>
						<view class="f3" @tap="showDataFixMenu">
							<text>数据修复</text>
						</view>
					</view>
					<view class="content2">
					
						<view class="item"  style="border: none;">
							<text class="t1">￥{{userinfo.yingde}}</text>
							<text class="t3">应补贴红包</text>
						</view>
						<view class="item"  style="border: none;">
							<text class="t1">￥{{userinfo.leiji}}</text>
							<text class="t3">合计补贴红包</text>
						</view>
					<!-- 	<view class="item"  style="border: none;">
							<text class="t1">￥{{userinfo.daijiang}}</text>
							<text class="t3">待补贴金额</text>
							
						</view>
						<view class="item"  style="border: none;">
							<text class="t1">￥{{userinfo.zailushang}}</text>
							<text class="t3">在路上</text>
						</view> -->
					</view>
					<view class="content2">
					<!-- 	<view class="item"  style="border: none;">
							<text class="t1">￥{{userinfo.zailushang}}</text>
							<text class="t3">在路上</text>
						</view> -->
						<view class="item"  style="border: none;">
							<text class="t1">￥{{userinfo.daizhuan}}</text>
							<text class="t3">可转至余额</text>
						</view>
						<view class="item"  style="border: none;">
							<text class="t1">{{userinfo.zhuanhua}}</text>
							<text class="t3">已转至余额</text>
						</view>
						<!-- <view class="item"  style="border: none;">
							<text class="t1">￥{{userinfo.quandaijiang}}</text>
							<text class="t3">平台排队金额</text>
						</view>
						<view class="item"  style="border: none;">
							<text class="t1">{{userinfo.quanorder}}</text>
							<text class="t3">排队订单数</text>
						</view> -->
					</view>
				<!-- 	<view class="content">
					
					</view> -->
				
				</view>
			</view>
			<view class="container3" v-if="set.endtime.remark != 0">
			  <!--  <view class="header">买单</view> -->
			  <view class="card">
			    <view class="content23">
			      <text class="discount-text">
			        活动结束时间:
			        <text class="highlight">{{set.endtime.endtime}}</text>
			        <text class="question-icon" @tap="showExplanation">?</text>
			      </text>
			      <!--  <text class="sub-text">先付后返</text> -->
			    </view>
			    <!-- <button class="pay-button"  :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="goto" :data-url="'/pagesExt/maidan/pay?bid='">点击买单</button> -->
			  </view>
			
			  <!-- Explanation Modal -->
			  <view v-if="showModal" class="modal" @tap="closeExplanation">
			    <view class="modal-content" @tap.stop>
			      <text class="close" @tap="closeExplanation">&times;</text>
			      <text>{{set.endtime.remark}}</text>
			    </view>
			  </view>
			</view>
		<dd-tab :itemdata="[t('排队中'),t('已完成')]" :itemst="['1','2']" :st="st" :isfixed="false" @changetab="changetab" ></dd-tab>
		<dd-tab :itemdata="[t('商品'),t('门店'),t('买单'),t('独立排队')]" :itemst="['1','2','3','4']" :st="st1" :isfixed="false" @changetab="changetab2"></dd-tab>
		<view class="order-content">
			
			<block  v-if="datalist && datalist.length>0">
			<view v-for="(item, index) in datalist" :key="index" class="content">
				<view class="order-box">
					<!-- <text class="t1" style="font-weight:bold;">奖励队列：{{item.id}}</text>
					<text class="t1" style="font-weight:bold;" v-if="st1==4">商家排队队列数量：{{item.duilicount}}</text> -->
				<view class="head">
					<view class="f1" ><image src="/static/img/ico-shop.png"></image>{{item.bunessname}}</view>
					
					<view class="flex1"></view>
					
					<text  class="st4">
						<block  v-if="item.status==0"><text class="t1"  :style="{color:t('color1')}">{{item.statusname}}</text></block>
						<block  v-if="item.status==1"><text class="t1" :style="{color:t('color1')}">{{item.statusname}}</text></block>
						<block  v-if="item.status==2"><text class="t1" :style="{color:t('color1')}">{{item.statusname}}</text></block>
					</text>
				</view>
				<block >
					<view class="content">
						<!-- :data-url="'/shopPackage/shop/product?id=' + item.product.id" -->
						<view @tap.stop="goto"  :data-url="'/shopPackage/shop/product?id='+item.proid"  v-if="item.qu==0">
							<image  :src="item.pic"></image>
						</view>
						<view v-if="item.qu==1">
							<image  :src="item.pic"></image>
						</view>
						<view v-if="item.name" class="detail">
							<text class="t1">{{item.name}}</text>
							<text class="t2"  @tap.stop="goto"  :data-url="'/pagesExt/order/detail?id='+item.orderid">排队时间：{{timestampToDate(item.createtime)}}</text>
							<view class="t3">
								<text class="x1 flex1">￥{{item.totalprice}}</text>
								<text class="x2">×{{item.num}}</text>
							</view>
						</view>
					</view>
				</block>
				<!-- <view class="bottom">
					<view class="l">
						<text class="t1">分红进度：{{item.progress_percent}}%</text>
						<text class="t2">目标金额：￥{{item.paidui_jiang}}</text>
						<text class="t2">已获得：￥{{item.shiji_dedao}}</text>
						<text class="t2">剩余：￥{{item.remaining_amount}}</text>
					</view>
					<view class="r">
						<text v-if="item.status==0" class="t1">前面还有{{item.qiancount}}人</text>
						<text v-if="item.status==1"  class="t1">已免单￥{{item.shiji_dedao}}</text>
						<text v-if="item.status==2"  class="t1">已完成</text>
						<text v-if="item.status==3"  class="t1">已取消</text>
					</view>
				</view> -->
				
				<!-- 分红流向信息 -->
				<!-- <view class="distribution-info" v-if="item.distribution_info && item.distribution_info.length > 0">
					<view class="distribution-header">
						<text class="distribution-title">💰 分红流向</text>
						<text class="distribution-count">共{{item.distribution_info.length}}笔收益</text>
					</view>
					<view class="distribution-list">
						<view v-for="(dist, distIndex) in item.distribution_info" :key="distIndex" class="distribution-item">
							<view class="distribution-amount">+￥{{dist.amount}}</view>
							<view class="distribution-details">
								<text class="distribution-time">{{dist.time}}</text>
								<text class="distribution-type">{{dist.type}}</text>
							</view>
						</view>
					</view>
					<view class="distribution-more" @tap="showDistributionFlow(item)">
						<text>查看详细流向 →</text>
					</view>
				</view> -->
				
				<!-- 分红来源信息 -->
			<!-- 	<view class="distribution-source" v-if="item.distribution_source && item.distribution_source.length > 0">
					<view class="source-header">
						<text class="source-title">📈 分红来源</text>
					</view>
					<view class="source-list">
						<view v-for="(source, sourceIndex) in item.distribution_source" :key="sourceIndex" class="source-item">
							<view class="source-amount">+￥{{source.amount}}</view>
							<view class="source-details">
								<text class="source-time">{{source.time}}</text>
								<text class="source-type">{{source.source_type}}</text>
							</view>
						</view>
					</view>
				</view> -->
				
				<view class="info-row">
					<view class="info-item"  style="border: none;">
						<text class="amount">{{Number(item.bili).toFixed(0)}}%</text>
						<text class="label">补贴比例</text>
					</view>
					<view class="info-item"  style="border: none;">
						<text class="amount">￥{{item.paidui_jiang}}</text>
						<text class="label">应补贴红包</text>
					</view>
					<view class="info-item"  style="border: none;">
						<text class="amount">￥{{item.shiji_dedao}}</text>
						<text class="label">已补贴红包</text>
					</view>
				</view>
				
				<view class="footer" v-if="item.status==0">
				  <text class="date pw">目前排位：{{item.qiancount}}</text>
				  <text class="action" @click="showModal2" v-if=" userinfo.duijifen == 1 || userinfo.duiyue == 1 || userinfo.duichoujiang == 1">
					  结束排队，领取补贴 &gt;</text>
				</view>
				
				<view v-if="modalVisible" class="modal-overlay">
				  <view class="modal-content">
				    <view v-if="!selectedOption">
				      <text class="modal-title">选择结束排队的原因</text>
				      <view class="option" v-for="option in options" :key="option.value" @click="selectOption2(option,item.id,option.value,item.butieyue,item.butiejifen,item.butieduijiang)">
				        <text>{{ option.text }}</text>
				      </view>
				    </view>
				    <view v-if="selectedOption" class="modal-info">
						  <text class="modal-title">结束排队，领取补贴</text>
					    <text class="modal-amount">{{ selectedOption.amount }}</text>
					    <view class="modal-text">
					      <text>{{ selectedOption.description }}</text>
					    </view>
					    <button class="modal-button confirm" @click="confirmEndQueue(item.id)">确认结束领取补贴</button>
					    <button class="modal-button back" @click="backToOptions">返回选择</button>
				    </view>
				    <button class="modal-button continue" @click="hideModal">继续排队等待补贴 &gt;</button>
				  </view>
				</view>
				
				<view class="op" v-if="st1==4">
					<text class="t1" style="font-weight:bold;" >商家排队队列数量：{{item.duilicount}}</text>
				</view>
				</view>
			</view>
			</block>
		</view>
	</block>
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			pageinfo: [],
			loading:false,
      isload: false,
	   modalVisible: false,
	      selectedOption: null,
			menuindex:-1,
			 showModal: false,
      nodata: false,
      nomore: false,
      datalist: [],
			textset:{},
      pagenum: 1,
	  userinfo: [],
	  set:[],
	  st: 1,
	  st1: 1,
	  primary_color : '',
	   options: [],
	  secondary_color : '',
	  xuanze:'',
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st || 1;
		var that = this;
		this.getdata();
		this.primary_color = app.getCache('primary_color')
		this.secondary_color = app.getCache('secondary_color')
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
	  numFilter (value) {
	  // 截取当前数据到小数点后两位
	  return parseFloat(value).toFixed(2);
	  },
	timestampToDate(timestamp) {
	    const date = new Date(timestamp*1000); // 如果timestamp是数值，直接使用；如果是字符串，确保是数值字符串
	    const year = date.getFullYear();
	    const month = (date.getMonth() + 1).toString().padStart(2, '0');
	    const day = date.getDate().toString().padStart(2, '0');
	    const hours = date.getHours().toString().padStart(2, '0');
	    const minutes = date.getMinutes().toString().padStart(2, '0');
	    const seconds = date.getSeconds().toString().padStart(2, '0');
	    return `${year}-${month}-${day} ${hours}:${minutes}`;
	  },
    getdata: function (loadmore) {
		
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var st = that.st;
	  var st1 = that.st1;
      var pagenum = that.pagenum;
			that.loading = true;
			that.nodata = false;
      that.nomore = false;
      app.post('ApiPaidui/paidui', {st: st,st1: st1,pagenum: pagenum}, function (res) {
				that.loading = false;
        var data = res.data;
		that.userinfo = res.userinfo;
		that.set = res.set;
		var options2=[];
		if(that.set.paidui_duihuan_money >0)
		{	
			var list= {value: 'reason1', text: '余额', amount: '¥ 0.05', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。'};
			options2.push(list)
		}
		if(that.set.paidui_duihuan >0)
		{
			var list={value: 'reason2', text: '积分', amount: '100积分', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。'};
			options2.push(list)
		}	
		if(that.set.paidui_duihuan_choujiang != '')
		{
			var list= {value: 'reason3', text: '抽奖', amount: '1次', description: '当放弃排队时，可结束排队并领取补贴；补贴可提现或再次消费；结束排队后，默认自动放弃该订单补贴权益。'};
			options2.push(list)
		}
		that.options  = options2;
        if (pagenum == 1) {
					that.textset = app.globalData.textset;
					uni.setNavigationBarTitle({
						title: that.t('排队补贴')
					});
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
		console.log('tg',that.datalist);
      });
    },
	showExplanation() {
	      this.showModal = true;
	    },
	    closeExplanation() {
	      this.showModal = false;
	    },
	duihuantixian: function (id) {
	  var that = this;
	  that.loading = true;
	  app.post('ApiPaidui/tixian', {id:id}, function(res) {
	  	that.loading = false;
	  	that.getdata();
	  	if (res.status == 0) {
	  		app.error(res.msg);
	  		return;
	  	}else{
	  		app.error('兑换成功');
	  	}
	  });
	},
	showModal2() {
	  this.modalVisible = true;
	},
	hideModal() {
	  this.modalVisible = false;
	  this.selectedOption = null;
	},
	selectOption2(option,orderid,xuanze,butie1,butie2,butie3) {
	   var that = this;
	   that.xuanze = xuanze;
	   if( xuanze == 'reason1')
	   {
		   option.amount = '¥'+butie1;
	   }else if(xuanze == 'reason2')
	   {
		   option.amount = butie2+'积分'; 
	   }else if(xuanze == 'reason3')
	   {
		   option.amount = butie3+'次';
	   }
	   this.selectedOption = option;
	},
	backToOptions() {
	  this.selectedOption = null;
	},
	confirmEndQueue(orderid) {
	  this.hideModal();
	  var that = this;
	  if( that.xuanze == 'reason1')
	  {
			that.duihuantixian2(orderid)
	  }else if(that.xuanze == 'reason2')
	  {
			that.duihuantixian(orderid)
	  }else if(that.xuanze == 'reason3')
	  {
			that.duihuantixian3(orderid)
	  }
	  // 处理结束排队逻辑
	  
	},
	duihuantixian2: function (id) {
	  var that = this;
	  that.loading = true;
	  app.post('ApiPaidui/tixian2', {id:id}, function(res) {
	  	that.loading = false;
	  	that.getdata();
	  	if (res.status == 0) {
	  		app.error(res.msg);
	  		return;
	  	}else{
	  		app.error('兑换成功');
	  	}
	  });
	},
	duihuantixian3: function (id) {
	  var that = this;
	  that.loading = true;
	  app.post('ApiPaidui/tixian3', {id:id}, function(res) {
	  	that.loading = false;
	  	that.getdata();
	  	if (res.status == 0) {
	  		app.error(res.msg);
	  		return;
	  	}else{
			app.success(res.msg);
			setTimeout(function () {
			  app.goto(res.data);
			}, 1000);
	  		// app.error('兑换成功');
	  	}
	  });
	},
	topay: function(e) {
		var that = this;
		that.loading = true;
		app.post('ApiPaidui/zhuanhua', {}, function(res) {
			that.loading = false;
			that.getdata();
			if (res.status == 0) {
				app.error(res.msg);
				return;
			}else{
				app.error('转化成功');
			}
		});
	},
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
	changetab2: function (st1) {
		this.st1 = st1;
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 0
		});
		this.getdata();
	},
	showDistributionFlow: function(item) {
		var that = this;
		app.post('ApiPaidui/paiduiDistributionFlow', {paidui_id: item.id}, function(res) {
			if (res.status == 1) {
				that.showDistributionModal(res.data);
			} else {
				app.error(res.msg || '获取分红流向失败');
			}
		});
	},
	showDistributionModal: function(data) {
		var that = this;
		var content = '【分红流向详情】\n\n';
		content += '排队进度：' + data.progress_info.progress_percent + '%\n';
		content += '目标金额：￥' + data.progress_info.target_amount + '\n';
		content += '已获得：￥' + data.progress_info.received_amount + '\n';
		content += '剩余：￥' + data.progress_info.remaining_amount + '\n\n';
		
		content += '分红记录：\n';
		if (data.flow_records && data.flow_records.length > 0) {
			data.flow_records.forEach(function(record) {
				content += '• ' + record.time_text + ' +￥' + record.money + '\n';
				content += '  来源：' + record.source_type + '\n';
			});
		} else {
			content += '暂无分红记录\n';
		}
		
		uni.showModal({
			title: '分红流向详情',
			content: content,
			showCancel: false,
			confirmText: '知道了'
		});
	},
	
	// 检查数据一致性
	checkDataConsistency: function() {
		var that = this;
		that.loading = true;
		app.post('ApiPaidui/checkDataConsistency', {}, function(res) {
			that.loading = false;
			if (res.status == 1) {
				if (res.need_fix) {
					// 发现数据不一致，询问是否修复
					uni.showModal({
						title: '数据不一致',
						content: res.msg + '\n\n是否立即修复？',
						showCancel: true,
						confirmText: '立即修复',
						cancelText: '稍后处理',
						success: function(modalRes) {
							if (modalRes.confirm) {
								that.syncPaiduiData();
							}
						}
					});
				} else {
					uni.showToast({
						title: '数据一致性正常',
						icon: 'success'
					});
				}
			} else {
				app.error(res.msg || '检查失败');
			}
		}, function() {
			that.loading = false;
			app.error('网络请求失败');
		});
	},
	
	// 数据修复
	syncPaiduiData: function() {
		var that = this;
		that.loading = true;
		app.post('ApiPaidui/syncPaiduiData', {}, function(res) {
			that.loading = false;
			if (res.status == 1) {
				uni.showModal({
					title: '修复完成',
					content: res.msg,
					showCancel: false,
					confirmText: '确定',
					success: function() {
						// 刷新页面数据
						that.getdata();
					}
				});
			} else {
				app.error(res.msg || '修复失败');
			}
		}, function() {
			that.loading = false;
			app.error('网络请求失败');
		});
	},
	
	// 显示数据修复菜单
	showDataFixMenu: function() {
		var that = this;
		uni.showActionSheet({
			itemList: ['检查数据一致性', '修复数据', '取消'],
			success: function(res) {
				if (res.tapIndex == 0) {
					that.checkDataConsistency();
				} else if (res.tapIndex == 1) {
					uni.showModal({
						title: '确认修复',
						content: '此操作将修复分红记录与排队记录的数据不一致问题，是否继续？',
						showCancel: true,
						confirmText: '确认修复',
						cancelText: '取消',
						success: function(modalRes) {
							if (modalRes.confirm) {
								that.syncPaiduiData();
							}
						}
					});
				}
			}
		});
	}
  }
};
</script>
<style>
.container3 {
  padding: 16px;
  width: 95%;
  background-color: #f5f5f5;
  
}

.container3 {
  padding: 10px;
  width: 100%;
  background-color: #f5f5f5;
  margin-top: -10px;
}

.header {
  font-size: 18px;
  font-weight: bold;
  padding-bottom: 16px;
}

.card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.content23 {
  display: flex;
  flex-direction: column;
}

.discount-text {
  font-size: 16px;
  font-weight: bold;
}

.highlight {
  color: #ff0000;
}

.sub-text {
  font-size: 14px;
  color: #666666;
  margin-top: 8px;
}

.pay-button {
  background-color: #ff4d4f;
  color: #ffffff;
  border: none;
  border-radius: 10px;
  padding: 4px 16px;
  font-size: 14px;
  cursor: pointer;
  margin-left: auto;
  margin-right: -10px;
}
.content2{ width:94%;margin:20rpx 0%;}
.content .item{width:100%;background:#fff;margin:20rpx 0;padding:40rpx 30rpx;border-radius:8px;display:flex;align-items:center}
.content .item:last-child{border:0}
.content .item .f1{width:500rpx;display:flex;flex-direction:column}
.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}
.content .item .f1 .t2{color:#666666;font-size:24rpx;margin-top:10rpx}
.content .item .f1 .t3{color:#666666;font-size:24rpx;margin-top:10rpx}
.content .item .f2{ flex:1;width:200rpx;font-size:36rpx;text-align:right}
.content .item .f2 .t1{color:#03bc01}
.content .item .f2 .t2{color:#000000}
.content .item .f3{ flex:1;width:200rpx;font-size:32rpx;text-align:right}
.content .item .f3 .t1{color:#03bc01}
.content .item .f3 .t2{color:#000000}
.banner{ display:flex;width:100%;height:560rpx;padding:40rpx 32rpx;color:#fff;position:relative}
.banner image{ width:120rpx;height:120rpx;border-radius:50%;margin-right:20rpx}
.banner .info{display:flex;flex:auto;flex-direction:column;padding-top:10rpx}
.banner .info .nickname{font-size:32rpx;padding-bottom:12rpx;color:black;}
.banner .set{ width:70rpx;height:100rpx;line-height:100rpx;font-size:40rpx;text-align:center}
.banner .set image{width:50rpx;height:50rpx;border-radius:0}

.contentdata{display:flex;flex-direction:column;width:100%;padding:0 30rpx;margin-top:-380rpx;position:relative;margin-bottom:20rpx}


.order3{width:100%;background:#fff;padding:0 20rpx;margin-top:-60rpx;border-radius:16rpx }
.order3 .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:0px solid #eee}
.order3 .head .f1{flex:auto;color:#333}
.order3 .head .f2{ display:flex;align-items:center;color:#FE2B2E;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}
.order3 .head .f2 image{ width:30rpx;height:30rpx;}
.order3 .head .t3{ width: 40rpx; height: 40rpx;}
.order3 .content{ display:flex;width:100%;padding:0rpx 0;align-items:center;font-size:24rpx}
.order3 .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}
.order3 .content .item image{ width:50rpx;height:50rpx}
.order3 .content .item .t1{color:#FE2B2E;font-size:36rpx;font-weight:bold;}
.order3 .content .item .t3{ padding-top:3px;color:#666}
.order3 .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}



.order{width:100%;background:#fff;padding:0 20rpx;margin-top:20rpx;border-radius:16rpx }
.order .head{ display:flex;align-items:center;width:100%;padding:10rpx 0;border-bottom:0px solid #eee}
.order .head .f1{flex:auto;color:#333}
.order .head .f2{ display:flex;align-items:center;color:#FE2B2E;width:200rpx;padding:10rpx 0;text-align:right;justify-content:flex-end}
.order .head .f2 image{ width:30rpx;height:30rpx;}
.order .head .f3{ display:flex;align-items:center;color:#666;width:120rpx;padding:10rpx 0;text-align:right;justify-content:flex-end;font-size:24rpx}
.order .head .t3{ width: 40rpx; height: 40rpx;}
.order .content{ display:flex;width:100%;padding:0rpx 0;align-items:center;font-size:24rpx}
.order .content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}
.order .content .item image{ width:50rpx;height:50rpx}
.order .content .item .t1{color:#FE2B2E; font-size:36rpx;font-weight:bold;}
.order .content .item .t3{ padding-top:3px;color:#666}
.order .content .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}


.order .content2{ display:flex;width:100%;padding:0rpx 0;align-items:center;font-size:24rpx}
.order .content2 .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}
.order .content2 .item image{ width:50rpx;height:50rpx}
.order .content2 .item .t1{color:#FE2B2E;font-size:36rpx;font-weight:bold;}
.order .content2 .item .t3{ padding-top:3px;color:#666}
.order .content2 .item .t2{background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:40rpx;width:34rpx;height:34rpx;text-align:center;}

.list{ width: 100%;background: #fff;margin-top:20rpx;padding:0 20rpx;font-size:30rpx;border-radius:16rpx}
.list .item{ height:100rpx;display:flex;align-items:center;border-bottom:0px solid #eee}
.list .item:last-child{border-bottom:0;}
.list .f1{width:50rpx;height:50rpx;line-height:50rpx;display:flex;align-items:center;}
.list .f1 image{ width:40rpx;height:40rpx;}
.list .f1 span{ width:40rpx;height:40rpx;font-size:40rpx}
.list .f2{color:#222}
.list .f3{ color: #FC5648;text-align:right;flex:1;}
.list .f4{ width: 24rpx; height: 24rpx;}
.topsearch{width:94%;margin:16rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}

.content{width:94%;margin:0 3%;border-radius:16rpx;background: #fff;margin-top: 20rpx;}
.content .label{display:flex;width: 100%;padding: 16rpx;color: #333;}
.content .label .t1{flex:1}
.content .label .t2{ width:300rpx;text-align:right}

.content .item{width: 100%;padding: 32rpx;border-top: 1px #eaeaea solid;min-height: 112rpx;display:flex;align-items:center;}
.content .item image{width: 90rpx;height: 90rpx;border-radius:4px}
.content .item .f1{display:flex;flex:1;align-items:center;}
.content .item .f1 .t2{display:flex;flex-direction:column;padding-left:20rpx}
.content .item .f1 .t2 .x1{color: #333;font-size:26rpx;}
.content .item .f1 .t2 .x2{color: #999;font-size:24rpx;}

.content .item .f2{display:flex;flex-direction:column;width:400rpx;text-align:right;border-left:1px solid #eee}
.content .item .f2 .t1{ font-size: 40rpx;color: #666;height: 40rpx;line-height: 40rpx;}
.content .item .f2 .t2{ font-size: 28rpx;color: #999;height: 50rpx;line-height: 50rpx;}
.content .item .f2 .t3{ display:flex;justify-content:space-around;margin-top:10rpx; flex-wrap: wrap;}
.content .item .f2 .t3 .x1{height:40rpx;line-height:40rpx;padding:0 8rpx;border:1px solid #ccc;border-radius:6rpx;font-size:22rpx;color:#666;margin-top: 10rpx;}

	.sheet-item {display: flex;align-items: center;padding:20rpx 30rpx;}
	.sheet-item .item-img {width: 44rpx;height: 44rpx;}
	.sheet-item .item-text {display: block;color: #333;height: 100%;padding: 20rpx;font-size: 32rpx;position: relative; width: 90%;}
	.sheet-item .item-text:after {position: absolute;content: '';height: 1rpx;width: 100%;bottom: 0;left: 0;border-bottom: 1rpx solid #eee;}
	.man-btn {
		line-height: 100rpx;
		text-align: center;
		background: #FFFFFF;
		font-size: 30rpx;
		color: #FF4015;
	}
.data-empty{background:#fff}
.container{ width:100%;}
.topsearch{width:94%;margin:10rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.order-content{display:flex;flex-direction:column}
.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #f4f4f4 solid; height: 70rpx; line-height: 70rpx; overflow: hidden; color: #999;}
.order-box .head .f1{display:flex;align-items:center;color:#333}
.order-box .head image{width:34rpx;height:34rpx;margin-right:4px}
.order-box .head .st0{ width: 140rpx; color: #ff8758; text-align: right; }
.order-box .head .st1{ width: 140rpx; color: #ffc702; text-align: right; }
.order-box .head .st2{ width: 140rpx; color: #ff4246; text-align: right; }
.order-box .head .st3{ width: 140rpx; color: #999; text-align: right; }
.order-box .head .st4{ width: 140rpx; color: #bbb; text-align: right; }

.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #f4f4f4 dashed;position:relative}
.order-box .content:last-child{ border-bottom: 0; }
.order-box .content image{ width: 140rpx; height: 140rpx;}
.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}
.order-box .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.order-box .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}
.order-box .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}
.order-box .content .detail .x1{ flex:1}
.order-box .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}

.order-box .bottom{ width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
.order-box .op{ display:flex;flex-wrap: wrap;justify-content:flex-end;align-items:center;width:100%; padding: 10rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}

.btn1{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}
.btn2{margin-left:20rpx; margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}

.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.hxqrbox .img{width:400rpx;height:400rpx}
.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}
.question-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 50%;
  background-color: #d3d3d3; /* 浅灰色背景 */
  color: #000; /* 问号颜色 */
  margin-left: 5px;
  font-weight: bold;
  cursor: pointer;
  position: relative;
  top: -2px; /* 调整问号图标位置 */
}
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}
.modal-content {
  background-color: #fefefe;
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
}
.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}
.close:hover,
.close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}  /* 其他样式省略 */
  
  .footer {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    background-color: white;
    margin-top: 10px;
    border-top: 1px solid #e5e5e5;
  }
  
  .date {
    font-size: 14px;
    color: #999;
  }
  
  .action {
    font-size: 14px;
    color: #d92c1e;
    cursor: pointer;
  }
  
  .floating-button {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background-color: #666;
    color: white;
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 14px;
  }
  
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .modal-content {
    width: 80%;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
  }
  
  .modal-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .modal-info {
    margin-top: 20px;
  }
  
  .modal-amount {
    font-size: 24px;
    font-weight: bold;
    color: #d92c1e;
    margin-bottom: 20px;
  }
  
  .modal-text {
    font-size: 14px;
    color: #666;
    text-align: left;
    margin-bottom: 20px;
  }
  
  .modal-button {
    display: block;
    width: 100%;
    padding: 10px;
    margin-top: 10px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
  }
  
  .confirm {
    background-color: #d92c1e;
    color: white;
  }
  
  .back {
    background-color: #f5f5f5;
    color: #333;
  }
  
  .option {
    padding: 10px;
    background-color: #f5f5f5;
    margin-bottom: 10px;
    cursor: pointer;
  }
  
  .option:hover {
    background-color: #e5e5e5;
  }

	
	.info-row {
	  display: flex;
	  justify-content: space-around;
	}
	
	.info-item {
	  display: flex;
	  flex-direction: column;
	  align-items: center;
	  text-align: center;
	}
	
	.amount {
	  font-size: 40rpx;
	  color: #d92c1e;
	  font-weight: bold;
	}
	
	.pw {
	  color: #d92c1e;
	  font-weight: bold;
	}

/* 分红流向信息样式 */
.distribution-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.distribution-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.distribution-count {
  font-size: 12px;
  color: #666;
}

.distribution-list {
  margin-bottom: 12px;
}

.distribution-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.distribution-item:last-child {
  border-bottom: none;
}

.distribution-amount {
  font-size: 16px;
  font-weight: bold;
  color: #52c41a;
}

.distribution-details {
  text-align: right;
}

.distribution-time {
  font-size: 12px;
  color: #666;
  display: block;
}

.distribution-type {
  font-size: 12px;
  color: #1890ff;
  display: block;
}

.distribution-more {
  text-align: center;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  color: #1890ff;
  font-size: 14px;
}

/* 分红来源信息样式 */
.distribution-source {
  background: #fff7e6;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.source-header {
  margin-bottom: 12px;
}

.source-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.source-list {
  margin-bottom: 8px;
}

.source-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #ffe7ba;
}

.source-item:last-child {
  border-bottom: none;
}

.source-amount {
  font-size: 14px;
  font-weight: bold;
  color: #fa8c16;
}

.source-details {
  text-align: right;
}

.source-time {
  font-size: 12px;
  color: #666;
  display: block;
}

.source-type {
  font-size: 12px;
  color: #fa8c16;
  display: block;
}
</style>