<template>
  <view class="face-camera">
    <!-- 顶部导航 -->
    <view class="nav-header">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="nav-title">面诊拍摄</view>
      <view class="nav-right"></view>
    </view>

    <!-- 相机预览区域 -->
    <view class="camera-container">
      <camera 
        class="camera-preview"
        device-position="front"
        flash="off"
        @error="onCameraError"
        @initdone="onCameraReady"
        ref="camera"
      >
        <!-- 拍摄框指引 -->
        <view class="camera-guide">
          <view class="face-frame">
            <view class="frame-corner top-left"></view>
            <view class="frame-corner top-right"></view>
            <view class="frame-corner bottom-left"></view>
            <view class="frame-corner bottom-right"></view>
            <text class="frame-text">请将面部置于框内</text>
          </view>
        </view>
        
        <!-- 拍摄提示 -->
        <view class="camera-tips">
          <text class="tip-text">{{ currentTip }}</text>
        </view>
      </camera>
      
      <!-- 图片预览 -->
      <view class="image-preview" v-if="capturedImage">
        <image :src="capturedImage" mode="aspectFit" class="preview-image"></image>
        <view class="preview-actions">
          <button class="btn-retake" @click="retakePhoto">重新拍摄</button>
          <button class="btn-confirm" @click="confirmPhoto">确认使用</button>
        </view>
      </view>
    </view>

    <!-- 底部控制栏 -->
    <view class="bottom-controls">
      <!-- 拍摄按钮 -->
      <view class="capture-section" v-if="!capturedImage">
        <view class="capture-button-wrapper">
          <button 
            class="capture-button" 
            @click="takePhoto"
            :disabled="!cameraReady || capturing"
          >
            <view class="capture-inner" :class="{ capturing: capturing }">
              <text class="capture-icon" v-if="!capturing">📷</text>
              <view class="capture-loading" v-else></view>
            </view>
          </button>
        </view>
        
        <view class="control-actions">
          <button class="control-btn" @click="switchCamera">
            <text class="control-icon">🔄</text>
            <text class="control-text">切换</text>
          </button>
          
          <button class="control-btn" @click="chooseFromAlbum">
            <text class="control-icon">🖼️</text>
            <text class="control-text">相册</text>
          </button>
        </view>
      </view>
      
      <!-- 分析按钮 -->
      <view class="analyze-section" v-if="capturedImage">
        <view class="price-info" v-if="needPayment">
          <text class="price-label">分析费用：</text>
          <text class="price-value">¥{{ config.price }}</text>
          <text class="free-badge" v-if="config.can_use_free">VIP免费</text>
        </view>
        
        <button 
          class="analyze-button" 
          @click="startAnalysis"
          :disabled="analyzing"
        >
          <text v-if="!analyzing">开始分析</text>
          <text v-else>分析中...</text>
        </button>
      </view>
    </div>

    <!-- 加载提示 -->
    <view class="loading-overlay" v-if="analyzing">
      <view class="loading-content">
        <view class="loading-animation">
          <view class="scanning-line"></view>
          <text class="scanning-text">AI正在分析面部特征...</text>
        </view>
        <view class="progress-info">
          <text class="progress-text">{{ progressText }}</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progress + '%' }"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();

export default {
  data() {
    return {
      cameraReady: false,
      capturing: false,
      analyzing: false,
      capturedImage: '',
      devicePosition: 'front',
      
      // 配置信息
      config: {
        price: 0,
        can_use_free: false,
        is_vip: false
      },
      
      // 拍摄提示
      tips: [
        '请保持面部清洁',
        '确保光线充足',
        '表情保持自然',
        '眼神平视镜头'
      ],
      currentTipIndex: 0,
      tipTimer: null,
      
      // 分析进度
      progress: 0,
      progressText: '准备分析...',
      progressTimer: null
    }
  },

  computed: {
    currentTip() {
      return this.tips[this.currentTipIndex];
    },
    
    needPayment() {
      return this.config.price > 0 && !this.config.can_use_free;
    }
  },

  onLoad(options) {
    this.getConfig();
    this.startTipRotation();
  },

  onUnload() {
    this.clearTimers();
  },

  methods: {
    // 获取配置
    async getConfig() {
      try {
        const res = await app.get('ApiDiagnosis/getConfig', {
          diagnosis_type: 'face'
        });
        
        if (res.code === 1) {
          this.config = res.data;
        }
      } catch (error) {
        console.error('获取配置失败:', error);
      }
    },

    // 开始提示轮播
    startTipRotation() {
      this.tipTimer = setInterval(() => {
        this.currentTipIndex = (this.currentTipIndex + 1) % this.tips.length;
      }, 3000);
    },

    // 清除定时器
    clearTimers() {
      if (this.tipTimer) {
        clearInterval(this.tipTimer);
        this.tipTimer = null;
      }
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
      }
    },

    // 相机就绪
    onCameraReady() {
      this.cameraReady = true;
      console.log('相机已就绪');
    },

    // 相机错误
    onCameraError(error) {
      console.error('相机错误:', error);
      uni.showToast({
        title: '相机启动失败',
        icon: 'none'
      });
    },

    // 拍摄照片
    takePhoto() {
      if (!this.cameraReady || this.capturing) {
        return;
      }

      this.capturing = true;
      
      const cameraContext = uni.createCameraContext();
      cameraContext.takePhoto({
        quality: 'high',
        success: (res) => {
          this.capturedImage = res.tempImagePath;
          this.capturing = false;
          
          // 震动反馈
          uni.vibrateShort();
          
          // 检查图片质量
          this.checkImageQuality();
        },
        fail: (error) => {
          console.error('拍摄失败:', error);
          uni.showToast({
            title: '拍摄失败，请重试',
            icon: 'none'
          });
          this.capturing = false;
        }
      });
    },

    // 检查图片质量
    checkImageQuality() {
      // 这里可以添加图片质量检测逻辑
      // 比如检查图片大小、清晰度等
      uni.getImageInfo({
        src: this.capturedImage,
        success: (info) => {
          console.log('图片信息:', info);
          
          // 检查图片尺寸
          if (info.width < 300 || info.height < 300) {
            uni.showModal({
              title: '图片质量提醒',
              content: '图片分辨率较低，可能影响分析准确性，是否重新拍摄？',
              success: (res) => {
                if (res.confirm) {
                  this.retakePhoto();
                }
              }
            });
          }
        }
      });
    },

    // 重新拍摄
    retakePhoto() {
      this.capturedImage = '';
      this.progress = 0;
      this.progressText = '准备分析...';
    },

    // 确认照片
    confirmPhoto() {
      // 直接进入分析流程
      this.startAnalysis();
    },

    // 切换摄像头
    switchCamera() {
      this.devicePosition = this.devicePosition === 'front' ? 'back' : 'front';
    },

    // 从相册选择
    chooseFromAlbum() {
      uni.chooseImage({
        count: 1,
        sizeType: ['original'],
        sourceType: ['album'],
        success: (res) => {
          this.capturedImage = res.tempFilePaths[0];
          this.checkImageQuality();
        }
      });
    },

    // 开始分析
    async startAnalysis() {
      if (!this.capturedImage) {
        uni.showToast({
          title: '请先拍摄面部照片',
          icon: 'none'
        });
        return;
      }

      try {
        this.analyzing = true;
        this.startProgressAnimation();
        
        // 1. 上传图片
        this.updateProgress(20, '正在上传图片...');
        const uploadResult = await this.uploadImage();
        
        if (!uploadResult.success) {
          throw new Error(uploadResult.message);
        }
        
        // 2. 调用分析接口
        this.updateProgress(50, '正在进行AI分析...');
        const analysisResult = await this.callAnalysisApi(uploadResult.url);
        
        if (analysisResult.code === 1) {
          this.updateProgress(100, '分析完成！');
          
          // 延迟跳转以显示完成状态
          setTimeout(() => {
            this.navigateToResult(analysisResult.data);
          }, 1000);
        } else {
          throw new Error(analysisResult.msg || '分析失败');
        }
        
      } catch (error) {
        console.error('分析失败:', error);
        uni.showToast({
          title: error.message || '分析失败，请重试',
          icon: 'none'
        });
        this.analyzing = false;
        this.clearTimers();
      }
    },

    // 上传图片
    uploadImage() {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: app.globalData.baseurl + 'ApiImageupload/uploadImg',
          filePath: this.capturedImage,
          name: 'file',
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.status === 1) {
                resolve({
                  success: true,
                  url: data.url
                });
              } else {
                resolve({
                  success: false,
                  message: data.msg || '上传失败'
                });
              }
            } catch (error) {
              resolve({
                success: false,
                message: '上传响应解析失败'
              });
            }
          },
          fail: (error) => {
            resolve({
              success: false,
              message: '网络错误，上传失败'
            });
          }
        });
      });
    },

    // 调用分析API
    async callAnalysisApi(imageUrl) {
      return new Promise((resolve, reject) => {
        app.post('ApiDiagnosis/analyze', {
          diagnosis_type: 'face',
          image_url: imageUrl,
          use_free: this.config.can_use_free ? 1 : 0
        }, (res) => {
          resolve(res);
        }, (error) => {
          reject(error);
        });
      });
    },

    // 开始进度动画
    startProgressAnimation() {
      this.progress = 0;
      this.progressTimer = setInterval(() => {
        if (this.progress < 90) {
          this.progress += Math.random() * 3;
        }
      }, 200);
    },

    // 更新进度
    updateProgress(progress, text) {
      this.progress = progress;
      this.progressText = text;
    },

    // 跳转到结果页面
    navigateToResult(analysisData) {
      // 保存分析结果到本地存储
      uni.setStorageSync('face_analysis_result', analysisData);
      
      uni.redirectTo({
        url: '/pagesB/diagnosis/face/result'
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss" scoped>
.face-camera {
  height: 100vh;
  background: #000;
  position: relative;
  overflow: hidden;
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44rpx 32rpx 20rpx;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background: linear-gradient(to bottom, rgba(0,0,0,0.6), transparent);
}

.nav-back {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  
  .icon-back {
    font-size: 32rpx;
    color: #fff;
  }
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.nav-right {
  width: 64rpx;
}

/* 相机容器 */
.camera-container {
  flex: 1;
  position: relative;
}

.camera-preview {
  width: 100%;
  height: 100vh;
}

/* 拍摄指引 */
.camera-guide {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}

.face-frame {
  width: 400rpx;
  height: 500rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.frame-corner {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #4facfe;
  
  &.top-left {
    top: 0;
    left: 0;
    border-right: none;
    border-bottom: none;
  }
  
  &.top-right {
    top: 0;
    right: 0;
    border-left: none;
    border-bottom: none;
  }
  
  &.bottom-left {
    bottom: 0;
    left: 0;
    border-right: none;
    border-top: none;
  }
  
  &.bottom-right {
    bottom: 0;
    right: 0;
    border-left: none;
    border-top: none;
  }
}

.frame-text {
  color: #fff;
  font-size: 24rpx;
  background: rgba(0, 0, 0, 0.6);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  position: absolute;
  bottom: -60rpx;
  white-space: nowrap;
}

/* 拍摄提示 */
.camera-tips {
  position: absolute;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
}

.tip-text {
  color: #fff;
  font-size: 28rpx;
  background: rgba(0, 0, 0, 0.6);
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* 图片预览 */
.image-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  display: flex;
  flex-direction: column;
}

.preview-image {
  flex: 1;
  width: 100%;
}

.preview-actions {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background: rgba(0, 0, 0, 0.8);
}

.btn-retake,
.btn-confirm {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.btn-retake {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.btn-confirm {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: #fff;
}

/* 底部控制栏 */
.bottom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  padding: 40rpx 32rpx 60rpx;
}

/* 拍摄区域 */
.capture-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.capture-button-wrapper {
  display: flex;
  justify-content: center;
}

.capture-button {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #fff;
  border: 8rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:disabled {
    opacity: 0.5;
  }
}

.capture-inner {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
  
  &.capturing {
    transform: scale(0.9);
  }
}

.capture-icon {
  font-size: 36rpx;
  color: #fff;
}

.capture-loading {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.control-actions {
  display: flex;
  justify-content: space-around;
  width: 100%;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  color: #fff;
}

.control-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.control-text {
  font-size: 22rpx;
}

/* 分析区域 */
.analyze-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.price-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.price-label {
  color: #fff;
  font-size: 26rpx;
}

.price-value {
  color: #4facfe;
  font-size: 32rpx;
  font-weight: 600;
}

.free-badge {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
}

.analyze-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  
  &:disabled {
    background: #666;
    opacity: 0.8;
  }
}

/* 加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: rgba(255, 255, 255, 0.1);
  padding: 48rpx;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  backdrop-filter: blur(10rpx);
}

.loading-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.scanning-line {
  width: 300rpx;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, #4facfe, transparent);
  animation: scanning 2s linear infinite;
}

@keyframes scanning {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.scanning-text {
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}

.progress-info {
  width: 300rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.progress-text {
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe, #00f2fe);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>