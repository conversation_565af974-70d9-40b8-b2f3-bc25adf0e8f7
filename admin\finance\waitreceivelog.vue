<template>
	<view class="container">
		<view class="search-box">
			<view class="search-form">
				<view class="search-item">
					<text class="icon-search">🔍</text>
					<input type="text" v-model="keyword" placeholder="搜索订单号/买家信息" class="input" confirm-type="search" @confirm="doSearch"/>
				</view>
				<view class="date-range">
					<picker mode="date" :value="start_time" @change="bindStartChange">
						<view class="picker-item">
							<text class="icon-calendar">📅</text>
							<text class="picker-text">{{start_time || '开始时间'}}</text>
						</view>
					</picker>
					<text class="split-line">-</text>
					<picker mode="date" :value="end_time" @change="bindEndChange">
						<view class="picker-item">
							<text class="icon-calendar">📅</text>
							<text class="picker-text">{{end_time || '结束时间'}}</text>
						</view>
					</picker>
				</view>
				<view class="search-btn" @tap="doSearch">搜索</view>
			</view>
		</view>
		
		<view class="total-card">
			<view class="total-item">
				<text class="label">订单总数</text>
				<text class="num">{{count}}</text>
				<text class="unit">笔</text>
			</view>
			<view class="divider"></view>
			<view class="total-item">
				<text class="label">待收总额</text>
				<text class="num highlight">￥{{total_money}}</text>
			</view>
		</view>

		<view class="list-content">
			<view class="order-card" v-for="(item,index) in list" :key="index">
				<view class="order-header">
					<view class="left">
						<text class="order-label">订单号</text>
						<text class="order-num">{{item.ordernum}}</text>
					</view>
					<text class="order-status">{{item.status_text}}</text>
				</view>
				
				<view class="buyer-info">
					<image class="avatar" :src="item.headimg" mode="aspectFill"></image>
					<view class="buyer-detail">
						<view class="nickname-box">
							<text class="nickname">{{item.nickname}}</text>
							<text class="tel">{{item.member_tel}}</text>
						</view>
					</view>
				</view>
				
				<view class="goods-list">
					<view class="goods-item" v-for="(goods,idx) in item.goods" :key="idx">
						<image class="goods-img" :src="goods.pic" mode="aspectFill"></image>
						<view class="goods-info">
							<text class="goods-name text-cut-2">{{goods.name}}</text>
							<text class="goods-spec">{{goods.ggname}}</text>
							<view class="goods-price">
								<text class="price">￥{{goods.sell_price}}</text>
								<text class="num">x{{goods.num}}</text>
							</view>
						</view>
					</view>
				</view>
				
				<view class="order-footer">
					<view class="pay-info">
						<view class="pay-item">
							<text class="label">支付方式：</text>
							<text class="value">{{item.paytype}}</text>
						</view>
						<view class="pay-item">
							<text class="label">支付时间：</text>
							<text class="value">{{item.paytime_text}}</text>
						</view>
					</view>
					<view class="total-price">
						<text class="label">待收金额</text>
						<text class="amount">￥{{item.totalprice}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="nodata" v-if="list.length==0">
			<image src="/static/img/nodata.png" mode="aspectFit" class="nodata-img"></image>
			<text class="nodata-text">暂无待收款订单</text>
		</view>
		
		<view class="loadmore" v-if="loadmore">
			<view class="loading-icon"></view>
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
var app = getApp();
export default {
	data() {
		return {
			isload: false,
			loadmore: true,
			pernum: 20,
			list: [],
			pagenum: 1,
			keyword: '',
			start_time: '',
			end_time: '',
			count: 0,
			total_money: 0
		}
	},
	onLoad: function(option) {
		this.getList();
	},
	onReachBottom: function() {
		if(this.loadmore) {
			this.getList();
		}
	},
	onPullDownRefresh: function() {
		this.pagenum = 1;
		this.getList();
	},
	methods: {
		bindStartChange(e) {
			this.start_time = e.detail.value;
		},
		bindEndChange(e) {
			this.end_time = e.detail.value;
		},
		doSearch() {
			this.pagenum = 1;
			this.getList();
		},
		getList: function() {
			var that = this;
			if(!that.loadmore) {
				return false;
			}
			app.post("ApiAdminFinance/waitReceiveList", {
				pagenum: that.pagenum,
				pernum: that.pernum,
				keyword: that.keyword,
				start_time: that.start_time,
				end_time: that.end_time
			}, function(res) {
				if(res.status == 0) {
					app.alert(res.msg);
					return false;
				}
				if(that.pagenum == 1) {
					that.list = [];
				}
				that.count = res.count;
				that.total_money = res.total_money;
				var list = res.data;
				if(list.length > 0) {
					that.list = that.list.concat(list);
					that.loadmore = true;
					that.pagenum++;
				} else {
					that.loadmore = false;
				}
				that.isload = true;
				uni.stopPullDownRefresh();
			})
		}
	}
}
</script>

<style>
page{ background: #f5f6f7;}
.container{ min-height: 100vh;}

/* 搜索区域 */
.search-box{ background: #fff; padding: 20rpx 30rpx; position: sticky; top: 0; z-index: 100;}
.search-form{ background: #fff; border-radius: 16rpx;}
.search-item{ display: flex; align-items: center; background: #f5f6f7; border-radius: 12rpx; padding: 0 20rpx; margin-bottom: 20rpx;}
.icon-search{ font-size: 32rpx; color: #999; margin-right: 10rpx;}
.input{ flex: 1; height: 80rpx; font-size: 28rpx;}

.date-range{ display: flex; align-items: center; margin-bottom: 20rpx;}
.picker-item{ flex: 1; display: flex; align-items: center; background: #f5f6f7; height: 80rpx; border-radius: 12rpx; padding: 0 20rpx;}
.icon-calendar{ font-size: 32rpx; color: #999; margin-right: 10rpx;}
.picker-text{ font-size: 28rpx; color: #333;}
.split-line{ padding: 0 20rpx; color: #999;}

.search-btn{ height: 80rpx; line-height: 80rpx; text-align: center; background: linear-gradient(90deg, #007AFF, #0056b3); color: #fff; border-radius: 12rpx; font-size: 28rpx;}

/* 统计卡片 */
.total-card{ margin: 20rpx 30rpx; background: #fff; border-radius: 16rpx; padding: 30rpx; display: flex; align-items: center;}
.total-item{ flex: 1; text-align: center;}
.divider{ width: 2rpx; height: 60rpx; background: #eee;}
.total-item .label{ font-size: 28rpx; color: #666; margin-bottom: 10rpx; display: block;}
.total-item .num{ font-size: 40rpx; font-weight: bold; color: #333;}
.total-item .num.highlight{ color: #f60;}
.total-item .unit{ font-size: 24rpx; color: #999; margin-left: 4rpx;}

/* 订单列表 */
.list-content{ padding: 0 30rpx;}
.order-card{ background: #fff; border-radius: 16rpx; margin-bottom: 20rpx; padding: 30rpx;}

.order-header{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 20rpx;}
.order-header .left{ display: flex; align-items: center;}
.order-label{ font-size: 26rpx; color: #999; margin-right: 10rpx;}
.order-num{ font-size: 28rpx; color: #333;}
.order-status{ font-size: 28rpx; color: #f60; font-weight: 500;}

.buyer-info{ display: flex; padding: 20rpx 0; border-bottom: 1px solid #f5f6f7;}
.avatar{ width: 80rpx; height: 80rpx; border-radius: 50%; background: #f5f6f7;}
.buyer-detail{ flex: 1; margin-left: 20rpx;}
.nickname-box{ display: flex; align-items: center;}
.nickname{ font-size: 28rpx; color: #333; margin-right: 20rpx;}
.tel{ font-size: 26rpx; color: #999;}

.goods-list{ padding: 20rpx 0;}
.goods-item{ display: flex; margin-bottom: 20rpx;}
.goods-item:last-child{ margin-bottom: 0;}
.goods-img{ width: 140rpx; height: 140rpx; border-radius: 12rpx; background: #f5f6f7;}
.goods-info{ flex: 1; margin-left: 20rpx; display: flex; flex-direction: column; justify-content: space-between;}
.goods-name{ font-size: 28rpx; color: #333; line-height: 1.4;}
.text-cut-2{ display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden;}
.goods-spec{ font-size: 24rpx; color: #999; margin-top: 10rpx;}
.goods-price{ display: flex; justify-content: space-between; align-items: center;}
.price{ color: #f60; font-size: 32rpx; font-weight: bold;}
.num{ color: #999; font-size: 26rpx;}

.order-footer{ padding-top: 20rpx; border-top: 1px solid #f5f6f7;}
.pay-info{ margin-bottom: 20rpx;}
.pay-item{ display: flex; line-height: 1.6;}
.pay-item .label{ font-size: 26rpx; color: #999;}
.pay-item .value{ font-size: 26rpx; color: #666;}
.total-price{ display: flex; justify-content: flex-end; align-items: center;}
.total-price .label{ font-size: 28rpx; color: #999; margin-right: 20rpx;}
.total-price .amount{ font-size: 36rpx; color: #f60; font-weight: bold;}

/* 空数据 */
.nodata{ padding: 100rpx 0; text-align: center;}
.nodata-img{ width: 200rpx; height: 200rpx; margin-bottom: 20rpx;}
.nodata-text{ font-size: 28rpx; color: #999;}

/* 加载更多 */
.loadmore{ display: flex; align-items: center; justify-content: center; padding: 30rpx 0;}
.loading-icon{ width: 40rpx; height: 40rpx; margin-right: 10rpx; animation: rotate 1s linear infinite;}
.loading-text{ font-size: 24rpx; color: #999;}

@keyframes rotate {
  from { transform: rotate(0deg);}
  to { transform: rotate(360deg);}
}
</style> 
.loadmore{ text-align: center; padding: 30rpx; color: #999;}
</style> 